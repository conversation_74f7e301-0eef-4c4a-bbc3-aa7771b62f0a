package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.*

/**
 * 今日待办小组件 - 显示当日重要任务
 * 支持多种尺寸：小(2x1)、中(4x2)、大(4x3)
 * 支持白天/夜间模式自动切换
 */
class TodayTasksWidget : AppWidgetProvider() {

    companion object {
        private const val TAG = "TodayTasksWidget"

        // 性能优化：防抖和缓存
        private const val UPDATE_DEBOUNCE_DELAY = 300L // 300ms防抖延迟
        private val updateJobs = ConcurrentHashMap<Int, Job>()
        private val widgetCache = ConcurrentHashMap<String, RemoteViews>()
        private var lastUpdateTime = 0L

        // 内存优化：复用对象
        private val dateFormat = SimpleDateFormat("MM月dd日", Locale.getDefault())
        private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        Log.d(TAG, "🔄 开始更新今日待办小组件")

        for (appWidgetId in appWidgetIds) {
            updateAppWidgetWithDebounce(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle?
    ) {
        Log.d(TAG, "📐 小组件尺寸变化: appWidgetId=$appWidgetId")
        updateAppWidgetWithDebounce(context, appWidgetManager, appWidgetId)
    }

    /**
     * 带防抖的小组件更新方法
     * 避免频繁更新导致的性能问题
     */
    private fun updateAppWidgetWithDebounce(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        // 取消之前的更新任务
        updateJobs[appWidgetId]?.cancel()

        // 创建新的防抖更新任务
        updateJobs[appWidgetId] = scope.launch {
            delay(UPDATE_DEBOUNCE_DELAY)
            try {
                updateAppWidget(context, appWidgetManager, appWidgetId)
            } catch (e: Exception) {
                Log.e(TAG, "更新小组件失败: appWidgetId=$appWidgetId", e)
            } finally {
                updateJobs.remove(appWidgetId)
            }
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        try {
            val currentTime = System.currentTimeMillis()

            // 性能优化：避免过于频繁的更新
            if (currentTime - lastUpdateTime < 100) {
                Log.d(TAG, "⏭️ 跳过过于频繁的更新")
                return
            }
            lastUpdateTime = currentTime

            Log.d(TAG, "🔄 开始更新今日待办小组件")

            val options = appWidgetManager.getAppWidgetOptions(appWidgetId)
            val minWidth = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH, 250)
            val minHeight = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT, 150)

            // 检测当前主题模式
            val isDarkMode = isSystemInDarkMode(context)
            Log.d(TAG, "当前主题模式: ${if (isDarkMode) "夜间" else "白天"}")

            // 生成缓存键
            val cacheKey = "${appWidgetId}_${minWidth}x${minHeight}_${isDarkMode}"

            // 检查缓存
            widgetCache[cacheKey]?.let { cachedViews ->
                Log.d(TAG, "📦 使用缓存的小组件视图")
                appWidgetManager.updateAppWidget(appWidgetId, cachedViews)
                return
            }

            // 根据尺寸选择布局 - 支持更多尺寸选项
            val layoutId = when {
                // 小尺寸 (1x1 或 2x1)
                minWidth < 250 || minHeight < 150 -> R.layout.widget_today_tasks_small
                // 中等尺寸 (2x2)
                minWidth < 350 || minHeight < 250 -> R.layout.widget_today_tasks_medium
                // 大尺寸 (4x4)
                else -> R.layout.widget_today_tasks_large
            }

            Log.d(TAG, "选择布局: ${getLayoutName(layoutId)}, 尺寸: ${minWidth}x${minHeight}")

            val views = RemoteViews(context.packageName, layoutId)

            // 获取今日任务数据（复用对象）
            val tasksData = getTodayTasksDataOptimized(context)

            // 设置日期信息
            setupDateInfo(views)

            // 根据布局类型设置内容
            when (layoutId) {
                R.layout.widget_today_tasks_small -> setupSmallWidget(context, views, tasksData, isDarkMode)
                R.layout.widget_today_tasks_medium -> setupMediumWidget(context, views, tasksData, isDarkMode)
                else -> setupLargeWidget(context, views, tasksData, isDarkMode)
            }

            // 设置点击事件
            setupClickEvents(context, views)

            // 缓存视图（限制缓存大小）
            if (widgetCache.size < 10) {
                widgetCache[cacheKey] = views
                Log.d(TAG, "💾 缓存小组件视图: $cacheKey")
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
        } catch (e: Exception) {
            Log.e(TAG, "小组件更新失败，尝试使用备用方案", e)

            // 如果出现错误，使用默认的中等尺寸布局
            try {
                Log.d(TAG, "尝试使用中等尺寸备用布局")
                val views = RemoteViews(context.packageName, R.layout.widget_today_tasks_medium)
                val defaultTasksData = getDefaultTasksData()
                setupDateInfo(views)
                setupMediumWidgetSafe(context, views, defaultTasksData, false)
                setupClickEvents(context, views)
                appWidgetManager.updateAppWidget(appWidgetId, views)
                Log.d(TAG, "中等尺寸备用布局设置成功")
            } catch (e2: Exception) {
                Log.e(TAG, "中等尺寸备用布局也失败，使用最简单布局", e2)

                // 最后的备用方案：使用最简单的布局
                try {
                    val views = RemoteViews(context.packageName, R.layout.widget_today_tasks_small)
                    val defaultTasksData = getDefaultTasksData()
                    views.setTextViewText(R.id.widget_date, Calendar.getInstance().get(Calendar.DAY_OF_MONTH).toString())
                    views.setTextViewText(R.id.widget_weekday, "今日")
                    setupSmallWidget(context, views, defaultTasksData, false)
                    appWidgetManager.updateAppWidget(appWidgetId, views)
                    Log.d(TAG, "最简单布局设置成功")
                } catch (e3: Exception) {
                    Log.e(TAG, "所有备用方案都失败", e3)
                }
            }
        }
    }

    /**
     * 优化的日期信息设置（减少对象创建）
     */
    private fun setupDateInfo(views: RemoteViews) {
        try {
            val calendar = Calendar.getInstance()
            val dayOfWeek = when (calendar.get(Calendar.DAY_OF_WEEK)) {
                Calendar.SUNDAY -> "周日"
                Calendar.MONDAY -> "周一"
                Calendar.TUESDAY -> "周二"
                Calendar.WEDNESDAY -> "周三"
                Calendar.THURSDAY -> "周四"
                Calendar.FRIDAY -> "周五"
                Calendar.SATURDAY -> "周六"
                else -> "周日"
            }
            val dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH).toString()

            // 安全设置文本
            safeSetTextViewText(views, R.id.widget_weekday, dayOfWeek)
            safeSetTextViewText(views, R.id.widget_date, dayOfMonth)
        } catch (e: Exception) {
            Log.w(TAG, "设置日期信息失败", e)
        }
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        // 清理缓存和取消任务
        appWidgetIds.forEach { appWidgetId ->
            updateJobs[appWidgetId]?.cancel()
            updateJobs.remove(appWidgetId)

            // 清理相关缓存
            widgetCache.keys.removeAll { key -> key.startsWith("${appWidgetId}_") }
        }
        Log.d(TAG, "🗑️ 清理小组件缓存: ${appWidgetIds.contentToString()}")
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        // 清理所有缓存和任务
        updateJobs.values.forEach { it.cancel() }
        updateJobs.clear()
        widgetCache.clear()
        Log.d(TAG, "🗑️ 清理所有小组件缓存")
    }

    /**
     * 优化的数据获取方法
     * 减少对象创建和内存分配
     */
    private fun getTodayTasksDataOptimized(context: Context): TodayTasksData {
        return try {
            // 复用DataProvider实例
            val dataProvider = TodayTasksDataProvider(context)
            dataProvider.getTodayTasksData()
        } catch (e: Exception) {
            Log.w(TAG, "获取任务数据失败，使用默认数据", e)
            // 返回轻量级的默认数据
            getDefaultTasksData()
        }
    }

    /**
     * 获取默认任务数据（内存优化）
     */
    private fun getDefaultTasksData(): TodayTasksData {
        return TodayTasksData(
            totalIncomplete = 0,
            totalCompleted = 0,
            totalTasks = 0
        )
    }

    private fun setupSmallWidget(
        context: Context,
        views: RemoteViews,
        tasksData: TodayTasksData,
        isDarkMode: Boolean
    ) {
        // 小尺寸显示未完成任务计数
        views.setTextViewText(R.id.widget_task_count, tasksData.totalIncomplete.toString())
        Log.d(TAG, "小尺寸小组件: 未完成任务 ${tasksData.totalIncomplete} 个")
    }

    private fun setupMediumWidget(
        context: Context,
        views: RemoteViews,
        tasksData: TodayTasksData,
        isDarkMode: Boolean
    ) {
        try {
            Log.d(TAG, "开始设置中尺寸小组件")

            // 中等尺寸显示未完成任务列表（新设计）
            val displayTasks = tasksData.getDisplayTasks()
            Log.d(TAG, "中尺寸小组件: 显示 ${displayTasks.size} 个未完成任务")

            // 验证任务数据
            displayTasks.forEachIndexed { index, task ->
                Log.d(TAG, "任务${index + 1}: ${task.title}, 完成状态: ${task.isCompleted}, 优先级: ${task.priority}")
            }

            // 使用安全的设置方法
            setupMediumWidgetSafe(context, views, tasksData, isDarkMode)

        } catch (e: Exception) {
            Log.e(TAG, "设置中尺寸小组件失败", e)
            // 使用安全的降级方法
            setupMediumWidgetSafe(context, views, tasksData, isDarkMode)
        }
    }

    /**
     * 安全的中等尺寸小组件设置方法
     */
    private fun setupMediumWidgetSafe(
        context: Context,
        views: RemoteViews,
        tasksData: TodayTasksData,
        isDarkMode: Boolean
    ) {
        try {
            val displayTasks = tasksData.getDisplayTasks()
            Log.d(TAG, "安全设置中尺寸小组件: ${displayTasks.size} 个任务")

            // 安全设置任务容器
            for (i in 1..3) { // 中等尺寸显示3个任务
                setupTaskItemSafe(context, views, i, displayTasks.getOrNull(i - 1))
            }

            // 设置心情emoji
            try {
                val emoji = when (tasksData.getCompletionPercentage()) {
                    in 0..25 -> "😴"
                    in 26..50 -> "🙂"
                    in 51..75 -> "😊"
                    in 76..100 -> "🎉"
                    else -> "😊"
                }
                views.setTextViewText(R.id.widget_mood_emoji, emoji)
                Log.d(TAG, "心情emoji设置成功: $emoji")
            } catch (e: Exception) {
                Log.w(TAG, "设置心情emoji失败", e)
            }

        } catch (e: Exception) {
            Log.e(TAG, "安全设置中尺寸小组件失败", e)
        }
    }

    /**
     * 安全设置单个任务项
     */
    private fun setupTaskItemSafe(
        context: Context,
        views: RemoteViews,
        taskIndex: Int,
        task: DisplayTask?
    ) {
        try {
            val containerId = getTaskContainerId(taskIndex)
            val taskId = getTaskTextId(taskIndex)
            val checkboxId = getTaskCheckboxId(taskIndex)
            val priorityBarId = getTaskPriorityBarId(taskIndex)

            if (task != null) {
                // 显示任务
                safeSetViewVisibility(views, containerId, android.view.View.VISIBLE)
                safeSetTextViewText(views, taskId, task.title)
                safeSetCheckboxState(views, checkboxId, task.isCompleted)
                safeSetPriorityColor(context, views, priorityBarId, task.priority)

                Log.d(TAG, "任务${taskIndex}设置成功: ${task.title}")
            } else {
                // 隐藏任务容器
                safeSetViewVisibility(views, containerId, android.view.View.GONE)
                Log.d(TAG, "任务${taskIndex}容器隐藏")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置任务${taskIndex}失败", e)
        }
    }

    /**
     * 获取任务容器ID
     */
    private fun getTaskContainerId(index: Int): Int {
        return when (index) {
            1 -> R.id.widget_task_container_1
            2 -> R.id.widget_task_container_2
            3 -> R.id.widget_task_container_3
            4 -> R.id.widget_task_container_4
            5 -> R.id.widget_task_container_5
            else -> R.id.widget_task_container_1
        }
    }

    /**
     * 获取任务文本ID
     */
    private fun getTaskTextId(index: Int): Int {
        return when (index) {
            1 -> R.id.widget_task_1
            2 -> R.id.widget_task_2
            3 -> R.id.widget_task_3
            4 -> R.id.widget_task_4
            5 -> R.id.widget_task_5
            else -> R.id.widget_task_1
        }
    }

    /**
     * 获取任务复选框ID
     */
    private fun getTaskCheckboxId(index: Int): Int {
        return when (index) {
            1 -> R.id.widget_checkbox_1
            2 -> R.id.widget_checkbox_2
            3 -> R.id.widget_checkbox_3
            4 -> R.id.widget_checkbox_4
            5 -> R.id.widget_checkbox_5
            else -> R.id.widget_checkbox_1
        }
    }

    /**
     * 获取任务优先级条ID
     */
    private fun getTaskPriorityBarId(index: Int): Int {
        return when (index) {
            1 -> R.id.widget_priority_bar_1
            2 -> R.id.widget_priority_bar_2
            3 -> R.id.widget_priority_bar_3
            4 -> R.id.widget_priority_bar_4
            5 -> R.id.widget_priority_bar_5
            else -> R.id.widget_priority_bar_1
        }
    }

    /**
     * 安全设置View可见性
     */
    private fun safeSetViewVisibility(views: RemoteViews, viewId: Int, visibility: Int) {
        try {
            views.setViewVisibility(viewId, visibility)
        } catch (e: Exception) {
            Log.w(TAG, "设置View可见性失败: viewId=$viewId", e)
        }
    }

    /**
     * 安全设置TextView文本
     */
    private fun safeSetTextViewText(views: RemoteViews, viewId: Int, text: String) {
        try {
            views.setTextViewText(viewId, text)
        } catch (e: Exception) {
            Log.w(TAG, "设置TextView文本失败: viewId=$viewId, text=$text", e)
        }
    }

    /**
     * 安全设置复选框状态
     */
    private fun safeSetCheckboxState(views: RemoteViews, viewId: Int, isCompleted: Boolean) {
        try {
            val checkboxDrawable = if (isCompleted) {
                R.drawable.widget_checkbox_checked
            } else {
                R.drawable.widget_checkbox_unchecked
            }
            views.setImageViewResource(viewId, checkboxDrawable)
        } catch (e: Exception) {
            Log.w(TAG, "设置复选框状态失败: viewId=$viewId, isCompleted=$isCompleted", e)
        }
    }

    /**
     * 安全设置优先级颜色
     */
    private fun safeSetPriorityColor(context: Context, views: RemoteViews, viewId: Int, priority: String) {
        try {
            val colorRes = when (priority.uppercase()) {
                "HIGH", "URGENT" -> R.color.widget_priority_high
                "MEDIUM", "NORMAL" -> R.color.widget_priority_medium
                "LOW" -> R.color.widget_priority_low
                else -> R.color.widget_accent_blue
            }

            val colorValue = androidx.core.content.ContextCompat.getColor(context, colorRes)
            views.setInt(viewId, "setBackgroundColor", colorValue)
        } catch (e: Exception) {
            Log.w(TAG, "设置优先级颜色失败: viewId=$viewId, priority=$priority", e)
            // 使用默认绿色
            try {
                views.setInt(viewId, "setBackgroundColor", 0xFF4CAF50.toInt())
            } catch (e2: Exception) {
                Log.e(TAG, "设置默认颜色也失败", e2)
            }
        }
    }

    private fun setupLargeWidget(
        context: Context,
        views: RemoteViews,
        tasksData: TodayTasksData,
        isDarkMode: Boolean
    ) {
        try {
            // 大尺寸显示最多5个任务的详细信息
            val displayTasks = tasksData.getDisplayTasks()
            Log.d(TAG, "大尺寸小组件: 显示 ${displayTasks.size} 个任务")

            // 设置任务计数
            views.setTextViewText(R.id.widget_task_count, displayTasks.size.toString())

            // 安全设置任务容器
            for (i in 1..5) { // 大尺寸支持5个任务
                setupTaskItemSafe(context, views, i, displayTasks.getOrNull(i - 1))
            }

            // 设置心情emoji（根据完成进度）
            try {
                val emoji = when (tasksData.getCompletionPercentage()) {
                    in 0..25 -> "😴"
                    in 26..50 -> "🙂"
                    in 51..75 -> "😊"
                    in 76..100 -> "🎉"
                    else -> "😊"
                }
                safeSetTextViewText(views, R.id.widget_mood_emoji, emoji)
                Log.d(TAG, "大尺寸心情emoji设置成功: $emoji")
            } catch (e: Exception) {
                Log.w(TAG, "设置心情emoji失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置大尺寸小组件失败", e)
        }
    }

    private fun setupClickEvents(context: Context, views: RemoteViews) {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "tasks")
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 尝试设置点击事件到不同的容器ID
        try {
            views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)
        } catch (e: Exception) {
            // 如果widget_container不存在，尝试设置到根布局
            try {
                views.setOnClickPendingIntent(android.R.id.background, pendingIntent)
            } catch (e2: Exception) {
                // 忽略错误
            }
        }
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    /**
     * 检测系统是否处于夜间模式
     */
    private fun isSystemInDarkMode(context: Context): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 获取布局名称（用于日志）
     */
    private fun getLayoutName(layoutId: Int): String {
        return when (layoutId) {
            R.layout.widget_today_tasks_small -> "小尺寸(2x1)"
            R.layout.widget_today_tasks_medium -> "中尺寸(4x2)"
            R.layout.widget_today_tasks -> "大尺寸(4x3)"
            else -> "未知布局"
        }
    }


}
