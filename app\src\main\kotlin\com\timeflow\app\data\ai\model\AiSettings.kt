package com.timeflow.app.data.ai.model

import java.util.UUID

/**
 * AI设置数据类，用于存储AI助手的个性化设置
 */
data class AiSettings(
    val id: String = UUID.randomUUID().toString(),
    val assistantName: String = "小晏",  // AI助手名称，默认为"小晏"
    val assistantAvatar: String = "🤖", // AI助手头像图标，默认为机器人
    val customAvatarUri: String? = null, // 自定义头像图片的URI
    val promptEmoji: String = "✨", // 提示消息前的emoji表情，默认为星星
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val selectedConfigId: String = "" // 当前选中的AI配置ID
) 