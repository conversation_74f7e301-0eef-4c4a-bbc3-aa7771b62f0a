package com.timeflow.app.utils

import com.timeflow.app.data.model.GoalTemplate
import android.util.LruCache
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * 模板数据二级缓存管理器
 * 使用LruCache实现内存缓存，减少数据库访问次数
 */
@Singleton
class TemplateCache @Inject constructor() {
    // 缓存锁，确保线程安全
    private val mutex = Mutex()
    
    // 模板列表缓存
    private val templatesCache = LruCache<String, List<GoalTemplate>>(20)
    
    // 单个模板缓存
    private val singleTemplateCache = LruCache<String, GoalTemplate>(50)
    
    // 分页缓存 - 按页码和分类缓存
    private val pagedTemplatesCache = LruCache<String, PagedTemplateResult>(10)
    
    /**
     * 从缓存获取所有模板
     * @param cacheKey 缓存键，默认为"all_templates"
     * @return 缓存中的模板列表，如果不存在则返回null
     */
    suspend fun getAllTemplates(cacheKey: String = "all_templates"): List<GoalTemplate>? {
        return mutex.withLock {
            templatesCache.get(cacheKey)
        }
    }
    
    /**
     * 缓存所有模板
     * @param templates 要缓存的模板列表
     * @param cacheKey 缓存键，默认为"all_templates"
     */
    suspend fun cacheAllTemplates(templates: List<GoalTemplate>, cacheKey: String = "all_templates") {
        mutex.withLock {
            templatesCache.put(cacheKey, templates)
            
            // 同时更新单个模板缓存
            templates.forEach { template ->
                singleTemplateCache.put(template.id, template)
            }
        }
    }
    
    /**
     * 从缓存获取单个模板
     * @param templateId 模板ID
     * @return 缓存中的模板，如果不存在则返回null
     */
    suspend fun getTemplate(templateId: String): GoalTemplate? {
        return mutex.withLock {
            singleTemplateCache.get(templateId)
        }
    }
    
    /**
     * 缓存单个模板
     * @param template 要缓存的模板
     */
    suspend fun cacheTemplate(template: GoalTemplate) {
        mutex.withLock {
            singleTemplateCache.put(template.id, template)
        }
    }
    
    /**
     * 获取分页模板结果
     * @param pageKey 分页缓存键
     * @return 分页结果，如果不存在则返回null
     */
    suspend fun getPagedTemplates(pageKey: String): PagedTemplateResult? {
        return mutex.withLock {
            pagedTemplatesCache.get(pageKey)
        }
    }
    
    /**
     * 缓存分页模板结果
     * @param pageKey 分页缓存键
     * @param pagedResult 分页结果
     */
    suspend fun cachePagedTemplates(pageKey: String, pagedResult: PagedTemplateResult) {
        mutex.withLock {
            pagedTemplatesCache.put(pageKey, pagedResult)
        }
    }
    
    /**
     * 生成分页缓存键
     * @param page 页码
     * @param pageSize 每页大小
     * @param category 分类，可选
     * @return 缓存键
     */
    fun generatePageKey(page: Int, pageSize: Int, category: String? = null): String {
        return if (category != null) {
            "page_${page}_size_${pageSize}_cat_${category}"
        } else {
            "page_${page}_size_${pageSize}_all"
        }
    }
    
    /**
     * 清除所有缓存
     */
    suspend fun clearAllCaches() {
        mutex.withLock {
            templatesCache.evictAll()
            singleTemplateCache.evictAll()
            pagedTemplatesCache.evictAll()
        }
    }
    
    /**
     * 从缓存中移除模板
     * @param templateId 要移除的模板ID
     */
    suspend fun removeTemplate(templateId: String) {
        mutex.withLock {
            singleTemplateCache.remove(templateId)
            // 移除后整个列表缓存也需要清除，因为已经不准确了
            templatesCache.evictAll()
            pagedTemplatesCache.evictAll()
        }
    }
}

/**
 * 分页模板结果数据类
 */
data class PagedTemplateResult(
    val templates: List<GoalTemplate>,
    val totalCount: Int,
    val hasNextPage: Boolean,
    val page: Int,
    val pageSize: Int
) 