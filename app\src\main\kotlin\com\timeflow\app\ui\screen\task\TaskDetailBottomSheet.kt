package com.timeflow.app.ui.screen.task

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.ui.theme.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.unit.DpOffset
import java.util.*

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.rememberTimePickerState
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.*
import androidx.compose.animation.*
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.ui.text.style.TextDecoration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.input.ImeAction
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent
import com.timeflow.app.util.AIAssistantTaskCreatedEvent
import androidx.compose.runtime.rememberCoroutineScope
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import android.util.Log
import androidx.compose.ui.platform.LocalContext
import android.widget.Toast

// 导入TaskModel.kt中的类和函数
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.UrgencyColors
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.screen.task.getUrgencyText
import com.timeflow.app.ui.screen.task.getUrgencyColor
import com.timeflow.app.ui.screen.task.model.convertDaysLeftToDateTime
import com.timeflow.app.ui.screen.task.TaskDetailViewModel
import com.timeflow.app.ui.screen.task.TaskTimeManager
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel
import com.timeflow.app.ui.screen.task.convertTaskToModelTaskData
import com.timeflow.app.ui.screen.task.convertModelTaskDataToUITaskData
import com.timeflow.app.util.DataConsistencyHelper
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.repository.TaskRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
// 🔧 循环任务相关导入
import com.timeflow.app.data.model.RecurrenceSettings
import com.timeflow.app.data.model.RecurrenceType
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import androidx.compose.material.icons.filled.Build

// 新增：导入目标选择组件
import com.timeflow.app.ui.screen.task.components.GoalSelectionComponent

/**
 * 格式化剩余时间为详细格式，包含具体时间段
 * 例如: "后天，3月31日, 17:25 - 18:25"
 */
private fun formatDetailedTimeRange(dueDate: LocalDateTime?): String {
    if (dueDate == null) return ""
    
    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val inputDate = dueDate.toLocalDate()
    val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endTime = dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))
    
    // 添加日志，帮助调试时间不一致问题
    Log.d("TimeFormatting", "TaskDetailBottomSheet格式化日期: $dueDate, 今天: $now")
    
    return when {
        // 今天
        inputDate.isEqual(today) -> {
            "今天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 明天
        inputDate.isEqual(today.plusDays(1)) -> {
            "明天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 后天
        inputDate.isEqual(today.plusDays(2)) -> {
            "后天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 本周内 (显示星期几)
        ChronoUnit.DAYS.between(today, inputDate) < 7 -> {
            val dayOfWeek = when (inputDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            "$dayOfWeek，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 本年内 (显示月日)
        inputDate.year == today.year -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 其他年份 (显示年月日)
        else -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - $endTime"
        }
    }
}

/**
 * 格式化任务的时间范围为详细格式，包含具体时间段
 * 例如: "后天，3月31日, 17:25 - 18:25"
 */
private fun formatDetailedTimeRange(startDateTime: LocalDateTime, endDateTime: LocalDateTime): String {
    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val inputDate = startDateTime.toLocalDate()
    val startTime = startDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endTime = endDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
    
    // 添加日志，帮助调试时间不一致问题
    Log.d("TimeFormatting", "TaskDetailBottomSheet格式化时间段: 开始=$startDateTime, 结束=$endDateTime, 今天: $now")
    
    val datePrefix = when {
        // 今天
        inputDate.isEqual(today) -> {
            "今天"
        }
        // 明天
        inputDate.isEqual(today.plusDays(1)) -> {
            "明天"
        }
        // 后天
        inputDate.isEqual(today.plusDays(2)) -> {
            "后天"
        }
        // 本周内 (显示星期几)
        ChronoUnit.DAYS.between(today, inputDate) < 7 -> {
            val dayOfWeek = when (inputDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            dayOfWeek
        }
        // 本年内 (显示月日)
        inputDate.year == today.year -> {
            ""
        }
        // 其他年份 (显示年月日)
        else -> {
            ""
        }
    }
    
    val dateStr = if (datePrefix.isNotEmpty()) {
        "$datePrefix，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}"
    } else if (inputDate.year == today.year) {
        inputDate.format(DateTimeFormatter.ofPattern("M月d日"))
    } else {
        inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))
    }
    
    return "$dateStr, $startTime - $endTime"
}

/**
 * 解析任务描述字段中可能存在的颜色JSON信息
 * 返回Pair<String, Color?>，第一个为过滤后的描述文本，第二个为解析出的颜色（可能为null）
 */
private fun parseColorFromDescription(description: String): Pair<String, Color?> {
    // 正则表达式匹配颜色JSON格式
    val colorPattern = """\{"color":(\d+)\}""".toRegex()
    val matchResult = colorPattern.find(description)
    
    return if (matchResult != null) {
        // 从描述中提取颜色值
        val colorValue = matchResult.groupValues[1].toLongOrNull()
        val color = colorValue?.let { Color(it.toInt()) }
        
        // 移除颜色JSON字符串，返回纯文本描述
        val cleanDescription = description.replace(colorPattern, "").trim()
        
        android.util.Log.d("TaskDetailBottomSheet", "解析到颜色: $colorValue, 净化后描述: $cleanDescription")
        Pair(cleanDescription, color)
    } else {
        // 没有找到颜色信息，返回原始描述
        Pair(description, null)
    }
}

/**
 * 任务详情底部弹出层组件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TaskDetailBottomSheet(
    task: TaskData,
    onDismiss: () -> Unit,
    onEditClick: () -> Unit,
    onTagsUpdated: (String, List<String>) -> Unit,
    onPriorityUpdated: (String, Priority) -> Unit,
    onSubTaskAdded: (String, SubTask) -> Unit,
    onSubTaskUpdated: (String, SubTask) -> Unit,
    onSubTaskDeleted: (String, String) -> Unit,
    onTaskCompleted: (String) -> Unit,
    onTaskTimeUpdated: (String, LocalDateTime?, LocalDateTime?) -> Unit,
    navController: NavController? = null, // 可选的导航控制器
    // 修改ViewModel类型
    viewModel: TaskDetailViewModel = hiltViewModel(),
    taskTimeViewModel: TaskTimeViewModel = hiltViewModel(), // 使用新的TaskTimeViewModel
    onTaskUpdated: ((TaskData) -> Unit)? = null, // 添加任务更新回调
    // 新增：目标关联相关回调
    onGoalAssociated: ((String, String?, String?) -> Unit)? = null // taskId, goalId, goalTitle
) {
    // 创建TaskTimeManager实例
    val taskTimeManager = remember { TaskTimeManager() }
    
    // 导入SystemBarManager - 进一步减少状态栏间距
    val statusBarPadding = com.timeflow.app.utils.SystemBarManager.getFixedStatusBarHeight()
    
    // **关键修改**: 使用实时数据状态，但避免不必要的重置
    var currentTask by remember(task.id) { 
        mutableStateOf(task.copy(
            // 保留原有的所有数据，确保完整性
            goalId = task.goalId,
            goalTitle = task.goalTitle
        )) 
    }
    var dataLoadingKey by remember { mutableStateOf(0) }
    
    // 添加最后更新时间跟踪
    var lastUpdateTime by remember { mutableStateOf(System.currentTimeMillis()) }
    
    // 使用rememberCoroutineScope替代viewModelScope - 提前定义
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 本地子任务列表状态，用于即时更新UI - 提前定义
    var localSubTasks by remember(currentTask.id) { mutableStateOf(currentTask.subTasks) }
    
    // 使用StateFlow存储和管理最新的子任务状态 - 提前定义
    val subTasksFlow = remember(currentTask.id) { 
        MutableStateFlow(currentTask.subTasks) 
    }
    
    // **关键修复**: 强制刷新数据的函数，绕过缓存获取最新数据
    fun forceRefreshTaskData() {
        coroutineScope.launch {
            try {
                Log.d("TaskDetailBottomSheet", "强制刷新任务数据，绕过缓存: taskId=${currentTask.id}")
                
                // **数据清理**: 首先清理被污染的标签数据
                Log.d("TaskDetailBottomSheet", "开始清理被污染的标签数据")
                withContext(Dispatchers.IO) {
                    // 调用ViewModel的数据清理方法
                    try {
                        viewModel.cleanupCorruptedTagData()
                    } catch (e: Exception) {
                        Log.e("TaskDetailBottomSheet", "数据清理失败: ${e.message}", e)
                    }
                }
                
                // 1. 强制清除缓存
                withContext(Dispatchers.IO) {
                    // 这里需要调用Repository的清除缓存方法
                    viewModel.clearTaskCache(currentTask.id)
                }
                
                // 2. 重新获取最新数据
                val latestTask = withContext(Dispatchers.IO) {
                    viewModel.getTaskByIdSync(currentTask.id)
                }
                
                if (latestTask != null) {
                    Log.d("TaskDetailBottomSheet", "强制刷新获取到最新数据: ${latestTask.title}, 标签数量: ${latestTask.tags.size}")
                    
                    // 3. 获取子任务数据
                    val latestSubTasks = withContext(Dispatchers.IO) {
                        viewModel.getSubTasksForTask(currentTask.id)
                    }
                    
                    // 4. 计算daysLeft
                    val daysLeft = latestTask.dueDate?.let { dueDate ->
                        val now = java.time.LocalDateTime.now()
                        java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), dueDate.toLocalDate()).toInt()
                    } ?: 0
                    
                    // 5. 转换为UI层的TaskData，保留用户当前的目标选择
                    val convertedTask = TaskData(
                        id = latestTask.id,
                        title = latestTask.title,
                        description = latestTask.description,
                        dueDate = latestTask.dueDate,
                        priority = latestTask.priority ?: Priority.MEDIUM,
                        status = if (latestTask.isCompleted) "已完成" else "待办",
                        tags = latestTask.tags.map { it.name }, // 🔧 修复：使用it.name而不是it.toString()
                        daysLeft = daysLeft,
                        subTasks = latestSubTasks,
                        // 🎯 关键修复：智能保留用户当前的目标选择
                        goalId = if (currentTask.goalId != null) {
                            // 如果用户已设置目标，保留用户选择
                            currentTask.goalId
                        } else {
                            // 如果用户未设置，使用数据库中的值（可能来自其他地方的设置）
                            latestTask.goalId
                        },
                        goalTitle = if (currentTask.goalId != null) {
                            // 如果用户已设置目标，保留用户设置的标题
                            currentTask.goalTitle
                        } else {
                            // 如果用户未设置，尝试获取目标标题（这里暂时为null，需要额外查询）
                            null
                        }
                    )
                    
                    // 6. 立即更新UI状态，保护用户的目标选择
                    Log.d("GoalAssociation", "[forceRefreshTaskData] 刷新前: currentTask.goalId=${currentTask.goalId}, currentTask.goalTitle=${currentTask.goalTitle}")
                    Log.d("GoalAssociation", "[forceRefreshTaskData] 数据库值: latestTask.goalId=${latestTask.goalId}")
                    Log.d("GoalAssociation", "[forceRefreshTaskData] 转换后: convertedTask.goalId=${convertedTask.goalId}, convertedTask.goalTitle=${convertedTask.goalTitle}")
                    
                    currentTask = convertedTask
                    localSubTasks = latestSubTasks
                    subTasksFlow.value = latestSubTasks
                    lastUpdateTime = System.currentTimeMillis()
                    
                    Log.d("TaskDetailBottomSheet", "强制刷新完成: 标签=${convertedTask.tags.size}个, 子任务=${latestSubTasks.size}个")
                    Log.d("GoalAssociation", "[forceRefreshTaskData] 刷新后: currentTask.goalId=${currentTask.goalId}, currentTask.goalTitle=${currentTask.goalTitle}")
                } else {
                    Log.w("TaskDetailBottomSheet", "强制刷新未找到任务 ID: ${currentTask.id}")
                }
                
            } catch (e: Exception) {
                Log.e("TaskDetailBottomSheet", "强制刷新任务数据失败", e)
            }
        }
    }
    
    // 页面首次打开时刷新数据
    LaunchedEffect(task.id) {
        delay(100) // 短暂延迟确保页面渲染完成
        forceRefreshTaskData()
        Log.d("TaskDetailBottomSheet", "页面打开，开始刷新任务数据: ${task.id}")
    }
    
    // 统一的事件监听 - 只监听TaskRefreshEvent，但添加智能防抖
    LaunchedEffect(Unit) {
        NotificationCenter.events.collect { event ->
            when (event) {
                is TaskRefreshEvent -> {
                    if (event.taskId == currentTask.id) {
                        Log.d("GoalAssociation", "[TaskDetailBottomSheet] 收到任务刷新事件: ${event.taskId}")
                        
                        // 🔧 智能防抖：如果用户刚刚选择了目标，延长防抖时间
                        val debounceTime = if (currentTask.goalId != null) {
                            500L // 如果有目标关联，延长防抖时间保护用户选择
                        } else {
                            100L // 正常防抖时间
                        }
                        
                        Log.d("GoalAssociation", "[TaskDetailBottomSheet] 防抖延迟: ${debounceTime}ms")
                        delay(debounceTime)
                        
                        forceRefreshTaskData()
                    }
                }
            }
        }
    }
    
    // 任务完成状态 - 基于currentTask而不是task
    var isTaskCompleted by remember { mutableStateOf(currentTask.status == "已完成") }
    
    // 监听currentTask变化，更新完成状态
    LaunchedEffect(currentTask.status) {
        isTaskCompleted = currentTask.status == "已完成"
    }
    
    // 任务时间状态 - 使用Key进行强制重组
    var timeUpdateKey by remember { mutableStateOf(0) }
    var showTaskTimeDialog by remember { mutableStateOf(false) }
    
    // ⭐ 删除确认对话框状态
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }

    // 🔧 延期功能状态
    var showPostponeDialog by remember { mutableStateOf(false) }
    
    // 创建一个可观察的task状态，使其随timeUpdateKey变化而更新 - 基于currentTask
    val observableTask by remember(currentTask.id, currentTask.dueDate, timeUpdateKey) {
        mutableStateOf(currentTask)
    }
    
    // 修复时间状态管理 - 使用单一的时间状态源，避免多个状态导致的不一致
    var currentTaskTime by remember(currentTask.id) { 
        mutableStateOf<LocalDateTime?>(currentTask.dueDate) 
    }
    
    // 将selectedStartDateTime和selectedEndDateTime基于currentTaskTime计算
    val selectedStartDateTime by remember(currentTaskTime) { 
        derivedStateOf { currentTaskTime }
    }
    
    val selectedEndDateTime by remember(currentTaskTime) { 
        derivedStateOf { currentTaskTime?.plusHours(1) }
    }
    
    // 添加上次更新时间记录，用于防止意外丢失子任务
    var lastSubTasksCount by remember(currentTask.id) { mutableStateOf(currentTask.subTasks.size) }
    
    // === 自动保存功能 ===
    // 自动保存状态管理
    var autoSaveEnabled by remember { mutableStateOf(true) }
    var lastSaveTime by remember { mutableStateOf(System.currentTimeMillis()) }
    var pendingChanges by remember { mutableStateOf(false) }
    var saveInProgress by remember { mutableStateOf(false) }
    
    // 🔧 防重复刷新机制
    var lastRefreshTime by remember { mutableStateOf(0L) }
    val minimumRefreshInterval = 1000L // 1秒内最多发送一次刷新事件
    
    // 智能刷新发送函数
    val sendRefreshEvent = remember {
        { reason: String ->
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastRefreshTime >= minimumRefreshInterval) {
                Log.d("RefreshEvent", "[TaskDetailBottomSheet] 发送刷新事件: $reason")
                NotificationCenter.post(TaskRefreshEvent(currentTask.id))
                lastRefreshTime = currentTime
            } else {
                Log.d("RefreshEvent", "[TaskDetailBottomSheet] 跳过重复刷新事件: $reason")
            }
        }
    }
    
    // 保存队列 - 存储待保存的更改
    val saveQueue = remember { mutableListOf<suspend () -> Unit>() }
    
    // 添加页面初始化状态标记
    var isInitialLoading by remember { mutableStateOf(true) }
    
    // 页面初始化完成后设置标记
    LaunchedEffect(currentTask.id) {
        delay(2000) // 2秒后认为页面初始化完成
        isInitialLoading = false
        Log.d("AutoSave", "[TaskDetailBottomSheet] 页面初始化完成，启用自动保存刷新")
    }
    
    // 立即保存函数 - 改为立即执行而不是延迟执行
    val performImmediateSave = remember {
        { saveAction: suspend () -> Unit ->
            if (autoSaveEnabled && !saveInProgress) {
                saveInProgress = true
                coroutineScope.launch {
                    try {
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 立即执行保存操作")
                        
                        // 立即执行保存操作
                        saveAction()
                        
                        pendingChanges = false
                        lastSaveTime = System.currentTimeMillis()
                        
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 立即保存完成")
                        
                        // 🔧 只有在页面初始化完成后才发送刷新事件，避免双重刷新
                        if (!isInitialLoading) {
                            sendRefreshEvent("立即保存")
                            Log.d("AutoSave", "[TaskDetailBottomSheet] 发送自动保存刷新事件")
                        } else {
                            Log.d("AutoSave", "[TaskDetailBottomSheet] 页面初始化中，跳过自动保存刷新事件")
                        }
                        
                        // 强制刷新子任务数据，确保最新状态
                        delay(100) // 短暂延迟确保数据库写入完成
                        fetchLatestSubTasks(currentTask.id, viewModel, coroutineScope, subTasksFlow)
                        
                    } catch (e: Exception) {
                        Log.e("AutoSave", "[TaskDetailBottomSheet] 立即保存失败", e)
                    } finally {
                        saveInProgress = false
                    }
                }
            }
        }
    }
    
    // 自动保存函数 - 保留原有的批量保存逻辑作为备用
    val performAutoSave = remember {
        {
            if (autoSaveEnabled && pendingChanges && !saveInProgress && saveQueue.isNotEmpty()) {
                saveInProgress = true
                coroutineScope.launch {
                    try {
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 开始批量保存，队列中有${saveQueue.size}个待保存项")
                        
                        // 执行所有待保存的操作
                        saveQueue.forEach { saveAction ->
                            try {
                                saveAction()
                                delay(50) // 短暂延迟避免过于频繁的保存
                            } catch (e: Exception) {
                                Log.e("AutoSave", "执行保存操作失败", e)
                            }
                        }
                        
                        // 清空保存队列
                        saveQueue.clear()
                        pendingChanges = false
                        lastSaveTime = System.currentTimeMillis()
                        
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 批量保存完成")
                        
                        // 🔧 只有在页面初始化完成后才发送刷新事件，避免双重刷新
                        if (!isInitialLoading) {
                            sendRefreshEvent("批量保存")
                            Log.d("AutoSave", "[TaskDetailBottomSheet] 发送批量保存刷新事件")
                        } else {
                            Log.d("AutoSave", "[TaskDetailBottomSheet] 页面初始化中，跳过批量保存刷新事件")
                        }
                        
                        // 强制刷新子任务数据，绕过缓存
                        delay(100)
                        fetchLatestSubTasks(currentTask.id, viewModel, coroutineScope, subTasksFlow)
                        
                    } catch (e: Exception) {
                        Log.e("AutoSave", "[TaskDetailBottomSheet] 批量保存失败", e)
                    } finally {
                        saveInProgress = false
                    }
                }
            }
        }
    }
    
    // 添加到保存队列的函数 - 改为立即保存模式
    val addToSaveQueue = remember {
        { saveAction: () -> Unit ->
            Log.d("AutoSave", "[TaskDetailBottomSheet] 触发立即保存模式")
            
            // 立即执行保存操作
            performImmediateSave {
                saveAction()
            }
            Unit // 明确返回 Unit
        }
    }
    
    // 页面关闭时的处理 - 简化，因为现在都是立即保存
    DisposableEffect(Unit) {
        onDispose {
            Log.d("AutoSave", "[TaskDetailBottomSheet] 页面关闭，由于采用立即保存模式，无需额外处理")
        }
    }
    
    // 监听StateFlow更新
    LaunchedEffect(subTasksFlow) {
        subTasksFlow.collect { latestSubTasks ->
            android.util.Log.d("TaskViewModel_Trace", "[TaskDetailBottomSheet] StateFlow更新: 收到${latestSubTasks.size}个子任务")
            
            // 仅当有值时更新本地状态，防止空列表覆盖
            if (latestSubTasks.isNotEmpty() || localSubTasks.isEmpty()) {
                localSubTasks = latestSubTasks
                lastUpdateTime = System.currentTimeMillis()
                lastSubTasksCount = latestSubTasks.size
                
                android.util.Log.d("TaskViewModel_Trace", "[TaskDetailBottomSheet] StateFlow更新了localSubTasks，新大小: ${latestSubTasks.size}")
            }
        }
    }
    
    // 监听任务时间变化，确保UI更新
    LaunchedEffect(currentTask.dueDate) {
        android.util.Log.d("TimeFormatting", "[TaskDetailBottomSheet] 检测到currentTask.dueDate变化: ${currentTask.dueDate}")
        // 当currentTask.dueDate发生变化时，更新本地状态
        if (currentTaskTime != currentTask.dueDate) {
            currentTaskTime = currentTask.dueDate
            // 增加timeUpdateKey触发UI重组
            timeUpdateKey += 1
            android.util.Log.d("TimeFormatting", "[TaskDetailBottomSheet] 任务dueDate变化，更新currentTaskTime=$currentTaskTime, timeUpdateKey=$timeUpdateKey")
        }
    }
    
    // 监听子任务列表变化，确保UI与数据同步
    LaunchedEffect(currentTask.id, currentTask.subTasks) {
        // >>> 添加日志追踪 (开始)
        android.util.Log.d("TaskViewModel_Trace", "[TaskDetailBottomSheet Effect] Triggered. Incoming currentTask.subTasks.size = ${currentTask.subTasks.size}")
        
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - lastUpdateTime
        
        // 防御性逻辑：如果短时间内(3秒内)子任务数量从有变为无，可能是数据加载错误，保留之前的子任务
        if (currentTask.subTasks.isEmpty() && localSubTasks.isNotEmpty() && timeSinceLastUpdate < 3000) {
            android.util.Log.w("TaskDetailBottomSheet", "检测到子任务在短时间内(${timeSinceLastUpdate}ms)从${localSubTasks.size}个变为0个，可能是临时加载错误，保留现有子任务")
            // 不更新localSubTasks，保留当前值
        }
        // 正常更新逻辑 - 只有当任务子任务为空但在很久以前有值，或者有新的子任务数据时才更新
        else if (currentTask.subTasks.isNotEmpty() || timeSinceLastUpdate > 3000) {
            android.util.Log.d("TaskDetailBottomSheet", "更新本地子任务列表: 旧=${localSubTasks.size}, 新=${currentTask.subTasks.size}")
            
            // 更新本地状态
            localSubTasks = currentTask.subTasks
            
            // 更新时间戳和计数器
            lastUpdateTime = currentTime
            lastSubTasksCount = currentTask.subTasks.size
            
            // 更新StateFlow
            subTasksFlow.value = currentTask.subTasks
            
            // >>> 添加日志追踪 (结束)
            android.util.Log.d("TaskViewModel_Trace", "[TaskDetailBottomSheet Effect] Updated localSubTasks. localSubTasks.size = ${localSubTasks.size}")
    
            // 打印更新后的本地子任务列表信息
            android.util.Log.d("TaskDetailBottomSheet", "本地子任务列表更新后状态:")
            localSubTasks.forEachIndexed { index, subTask ->
                android.util.Log.d("TaskDetailBottomSheet", "本地子任务[$index]: id=${subTask.id}, title=${subTask.title}, 完成状态=${subTask.isCompleted}")
            }
        } else {
            android.util.Log.d("TaskDetailBottomSheet", "保持本地子任务列表不变 (当前=${localSubTasks.size}个)")
        }
    }
    
    // 每次底部弹窗打开时，强制刷新数据
    LaunchedEffect(currentTask.id) {
        android.util.Log.d("TaskDetailBottomSheet", "强制刷新任务详情: 任务ID=${currentTask.id}")
        
        // 主动获取最新子任务数据
        fetchLatestSubTasks(currentTask.id, viewModel, coroutineScope, subTasksFlow)
    }
    
    // 使用LaunchedEffect监听子任务完成状态 (现在基于 localSubTasks)
    LaunchedEffect(localSubTasks) {
        // 如果有子任务且全部完成，触发任务完成回调
        if (localSubTasks.isNotEmpty() && localSubTasks.all { it.isCompleted }) {
            // 自动触发任务完成回调
            onTaskCompleted(currentTask.id)
            isTaskCompleted = true
        }
    }
    
    // 添加状态一致性检查，如果发现不一致立即更正
    SideEffect {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - lastUpdateTime

        // 当且仅当以下条件满足时才重新更正localSubTasks:
        // 1. localSubTasks为空但task.subTasks不为空 (可能是错误加载)
        // 2. 或者已经很长时间没有更新了(>5秒)且任务子任务有变化
        if ((localSubTasks.isEmpty() && currentTask.subTasks.isNotEmpty()) ||
            (timeSinceLastUpdate > 5000 && currentTask.subTasks.size != localSubTasks.size && currentTask.subTasks.isNotEmpty())) {
            android.util.Log.d("TaskViewModel_Trace", "[TaskDetailBottomSheet] 检测到状态不一致, 立即修复: localSubTasks=${localSubTasks.size}, task.subTasks=${currentTask.subTasks.size}, 时间=${timeSinceLastUpdate}ms")
            localSubTasks = currentTask.subTasks
            lastUpdateTime = currentTime
            lastSubTasksCount = currentTask.subTasks.size
        }
    }
    
    // 使用Box作为根布局容器，将底部按钮固定在底部，内容区域设置为可滚动
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = statusBarPadding * 0.1f) // 进一步减少顶部状态栏间距到0.1倍
    ) {
        // 内容区域（可滚动）
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 80.dp) // 从100.dp减少到80.dp
                .verticalScroll(rememberScrollState())
        ) {
            // 顶部操作栏 - 优化顶部操作栏
            TopBar(
                onDismiss = onDismiss,
                onTimeIconClick = { showTaskTimeDialog = true },
                selectedStartDateTime = selectedStartDateTime,
                selectedEndDateTime = selectedEndDateTime,
                // 传递自动保存状态
                saveInProgress = saveInProgress,
                pendingChanges = pendingChanges,
                performAutoSave = performAutoSave,
                // ⭐ 添加删除确认对话框状态参数
                showDeleteConfirmDialog = showDeleteConfirmDialog,
                onShowDeleteConfirmDialog = { showDeleteConfirmDialog = it }
            )
            
            // 任务标题 - 减少间距
            TaskTitle(
                task = currentTask,
                onTimeClick = { showTaskTimeDialog = true }, // 添加打开任务时间编辑器的回调
                timeUpdateKey = timeUpdateKey,
                currentTaskTime = currentTaskTime
            )
            
            Spacer(modifier = Modifier.height(2.dp)) // 从4.dp减少到2.dp
            
            // 🎯 新增：目标关联选择组件
            GoalSelectionComponent(
                selectedGoalId = currentTask.goalId,
                onGoalSelected = { goalId, goalTitle ->
                    Log.d("GoalAssociation", "[TaskDetailBottomSheet] 用户选择目标: goalId=$goalId, goalTitle=$goalTitle")
                    
                    // 立即更新本地UI状态，确保用户看到即时反馈
                    currentTask = currentTask.copy(goalId = goalId, goalTitle = goalTitle)
                    
                    // 立即触发UI重组，提供即时视觉反馈
                    timeUpdateKey += 1
                    
                    // 异步保存到数据库，不阻塞UI
                    coroutineScope.launch {
                        try {
                            Log.d("GoalAssociation", "[TaskDetailBottomSheet] 开始保存目标关联到数据库...")
                            
                            // 调用目标关联回调进行数据库保存
                            onGoalAssociated?.invoke(currentTask.id, goalId, goalTitle)
                            
                            Log.d("GoalAssociation", "[TaskDetailBottomSheet] ✓ 目标关联保存请求已发送")
                            
                            // 等待数据库操作完成
                            kotlinx.coroutines.delay(300)
                            
                            // 🔧 延迟发送刷新事件，让其他组件知道数据已更新
                            // 但不会立即触发forceRefreshTaskData，避免覆盖用户选择
                            Log.d("GoalAssociation", "[TaskDetailBottomSheet] 准备发送刷新事件...")
                            
                            // 发送刷新事件给其他组件，但当前组件会通过智能判断保护用户选择
                            NotificationCenter.post(TaskRefreshEvent(currentTask.id))
                            
                            Log.d("GoalAssociation", "[TaskDetailBottomSheet] ✓ 目标关联完成，刷新事件已发送")
                            
                        } catch (e: Exception) {
                            Log.e("GoalAssociation", "[TaskDetailBottomSheet] ✗ 目标关联保存失败: ${e.message}", e)
                            
                            // 如果保存失败，可以选择回滚或显示错误消息
                            // 这里先保持用户的选择，因为可能只是网络问题
                        }
                    }
                },
                modifier = Modifier.padding(horizontal = 12.dp) // 从16.dp减少到12.dp
                    )
            
            Spacer(modifier = Modifier.height(4.dp)) // 从8.dp减少到4.dp
            
            // 添加数据同步状态指示器 - 优化布局，隐藏最后更新时间以节省空间
            
            // 任务内容（包含子任务和标签）
            TaskDetailContent(
                // 直接传递更新后的 localSubTasks
                task = currentTask.copy(subTasks = localSubTasks),
                onTagsUpdated = { taskId, updatedTags ->
                    // 添加到自动保存队列
                    addToSaveQueue {
                        onTagsUpdated(taskId, updatedTags)
                        Unit // 明确返回 Unit
                    }
                    Log.d("AutoSave", "[TaskDetailBottomSheet] 标签更新立即保存")
                },
                onPriorityUpdated = { taskId, priority ->
                    // 添加到自动保存队列
                    addToSaveQueue {
                        onPriorityUpdated(taskId, priority)
                        Unit // 明确返回 Unit
                    }
                    Log.d("AutoSave", "[TaskDetailBottomSheet] 优先级更新立即保存")
                },
                onSubTaskAdded = { taskId, subTask ->
                    // 立即更新本地状态，提供即时反馈
                    localSubTasks = localSubTasks + subTask
                    
                    // 添加到自动保存队列
                    addToSaveQueue {
                    onSubTaskAdded(taskId, subTask)
                        Unit // 明确返回 Unit
                    }
                    Log.d("AutoSave", "[TaskDetailBottomSheet] 子任务添加立即保存")
                },
                onSubTaskUpdated = { taskId, subTask ->
                    // 立即更新本地状态
                    localSubTasks = localSubTasks.map { 
                        if (it.id == subTask.id) subTask else it 
                    }
                    
                    // 添加到自动保存队列
                    addToSaveQueue {
                    onSubTaskUpdated(taskId, subTask)
                        Unit // 明确返回 Unit
                    }
                    
                    // 检查是否所有子任务都已完成
                    if (subTask.isCompleted) {
                        val allCompleted = localSubTasks.all { 
                            it.id == subTask.id || it.isCompleted 
                        }
                        if (allCompleted) {
                            // 所有子任务完成，更新父任务状态
                            addToSaveQueue {
                            onTaskCompleted(currentTask.id)
                                Unit // 明确返回 Unit
                        }
                    }
                    }
                    Log.d("AutoSave", "[TaskDetailBottomSheet] 子任务更新立即保存")
                },
                onSubTaskDeleted = { taskId, subTaskId ->
                    // 立即更新本地状态
                    localSubTasks = localSubTasks.filter { it.id != subTaskId }
                    
                    // 添加到自动保存队列
                    addToSaveQueue {
                    onSubTaskDeleted(taskId, subTaskId)
                        Unit // 明确返回 Unit
                    }
                    Log.d("AutoSave", "[TaskDetailBottomSheet] 子任务删除立即保存")
                },
                onAllSubTasksCompleted = {
                    // 所有子任务完成时，将任务标记为完成
                    if (!isTaskCompleted) {
                        isTaskCompleted = true
                        addToSaveQueue {
                        onTaskCompleted(currentTask.id)
                            Unit // 明确返回 Unit
                        }
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 任务完成立即保存")
                    }
                },
                // 传递自动保存函数
                addToSaveQueue = addToSaveQueue,
                // 传递viewModel参数
                viewModel = viewModel,
                modifier = Modifier.padding(horizontal = 12.dp) // 从16.dp减少到12.dp
            )
            
            // 添加AI优化此任务按钮 - 减少间距
            AIOptimizeTaskButton(
                onClick = {
                    // 处理AI优化任务的点击事件
                    navController?.navigate(AppDestinations.aiAssistantWithTaskRoute(currentTask.id))
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp, vertical = 4.dp) // 从horizontal = 16.dp, vertical = 8.dp减少
            )
            
            Spacer(modifier = Modifier.height(2.dp)) // 从4.dp减少到2.dp
        }
        
        // 底部按钮区域固定在底部
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(Color.White)
                .navigationBarsPadding() // 为系统导航栏添加内边距
                .padding(bottom = 4.dp) // 从6.dp减少到4.dp
        ) {
            // 底部操作按钮 - 增强视觉吸引力
            BottomActions(
                isCompleted = isTaskCompleted,
                onCompleteClick = {
                    isTaskCompleted = true
                    onTaskCompleted(currentTask.id)
                    Unit // 明确返回 Unit
                },
                onPostponeClick = {
                    showPostponeDialog = true
                }
            )
        }
    }
    
    // 显示任务时间设置对话框
    if (showTaskTimeDialog) {
        DateTimePickerDialog(
            initialDateTime = selectedStartDateTime ?: LocalDateTime.now(),
            onDateTimeSelected = { startDateTime, endDateTime ->
                Log.d("TimeFormatting", "[TaskDetailBottomSheet] 时间选择完成 - 开始时间: $startDateTime, 结束时间: $endDateTime")
                
                // 先关闭对话框
                showTaskTimeDialog = false
                
                // 立即更新本地状态，确保UI立即反映变化
                currentTaskTime = startDateTime
                
                // 更新timeUpdateKey触发UI强制重组
                timeUpdateKey += 1
                Log.d("TimeFormatting", "[TaskDetailBottomSheet] 更新后 - currentTaskTime=$currentTaskTime, timeUpdateKey=$timeUpdateKey")
                
                // 立即强制UI重绘，不等待下一个组合周期
                coroutineScope.launch {
                    try {
                        // 使用TaskTimeViewModel统一管理时间更新
                        taskTimeViewModel.updateTaskTime(
                            taskId = currentTask.id,
                            startTime = startDateTime,
                            endTime = endDateTime,
                            dueDate = startDateTime, // 将开始时间作为截止时间
                            source = "TaskDetailBottomSheet"
                        )
                        
                        // 🔧 新增：标记为手动修改日期，确保不会被自动重新调度
                        Log.d("TaskDetailBottomSheet", "标记任务为手动修改日期: taskId=${currentTask.id}")
                        try {
                            viewModel.markTaskDateAsManuallyModified(currentTask.id)
                        } catch (e: Exception) {
                            Log.e("TaskDetailBottomSheet", "标记任务日期手动修改失败", e)
                        }
                        
                        // 添加时间更新到自动保存队列
                        addToSaveQueue {
                            onTaskTimeUpdated(currentTask.id, startDateTime, endDateTime)
                            Unit // 明确返回 Unit
                        }
                        
                        Log.d("TimeFormatting", "[TaskDetailBottomSheet] 时间更新完成")
                        Log.d("AutoSave", "[TaskDetailBottomSheet] 时间更新立即保存")
                        
                    } catch (e: Exception) {
                        Log.e("TimeFormatting", "[TaskDetailBottomSheet] 时间更新失败", e)
                    }
                }
            },
            onDismiss = { showTaskTimeDialog = false }
        )
    }

    // 添加一个新的LaunchedEffect:

    // 添加全局任务刷新事件监听器，确保能捕获来自AI助手的更新
    LaunchedEffect(currentTask.id) {
        var isProcessingRefresh = false
        
        NotificationCenter.events.collect { event ->
            if (event is TaskRefreshEvent && event.taskId == currentTask.id && !isProcessingRefresh) {
                isProcessingRefresh = true
                android.util.Log.d("TaskDetailBottomSheet", "收到全局任务刷新事件: ${event.taskId}")
                
                // 防抖处理：延迟执行，避免短时间内重复刷新
                delay(200)
                
                try {
                    // 当收到全局刷新事件时，主动获取最新数据
                    fetchLatestSubTasks(currentTask.id, viewModel, coroutineScope, subTasksFlow)
                } finally {
                    isProcessingRefresh = false
                }
            }
        }
    }
    
    // 监听时间更新，强制刷新任务数据
    LaunchedEffect(timeUpdateKey) {
        if (timeUpdateKey > 0) { // 只在timeUpdateKey变化后执行
            Log.d("TimeFormatting", "检测到timeUpdateKey变化，尝试获取最新任务数据: key=$timeUpdateKey")
            
            // 先延迟一小段时间确保数据库更新完成
            kotlinx.coroutines.delay(100)
            
            try {
                // 从ViewModel获取最新任务数据
                viewModel.getTaskById(currentTask.id) { freshTask ->
                    freshTask?.let { latestTask ->
                        // 更新任务时间状态
                        if (latestTask.dueDate != null && latestTask.dueDate != currentTaskTime) {
                            Log.d("TimeFormatting", "任务时间已更新: 旧=${currentTaskTime}, 新=${latestTask.dueDate}")
                            currentTaskTime = latestTask.dueDate
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("TimeFormatting", "获取最新任务数据失败", e)
            }
        }
    }

    // 监听TaskTimeViewModel的时间更新事件
    LaunchedEffect(Unit) {
        taskTimeViewModel.timeUpdateEvents.collect { event ->
            if (event.taskId == currentTask.id) {
                Log.d("TimeFormatting", "[TaskDetailBottomSheet] 收到时间更新事件: taskId=${event.taskId}, 新时间=${event.newTime.effectiveTime}")
                
                // 更新本地时间状态
                currentTaskTime = event.newTime.effectiveTime
                timeUpdateKey += 1
                
                Log.d("TimeFormatting", "[TaskDetailBottomSheet] 已更新本地时间状态: currentTaskTime=$currentTaskTime")
            }
        }
    }

    // 添加任务更新回调
    LaunchedEffect(currentTask) {
        onTaskUpdated?.invoke(currentTask)
    }
    
    // ⭐ 删除确认对话框
    if (showDeleteConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmDialog = false },
            title = {
                Text(
                    text = "删除任务",
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )
            },
            text = {
                Text(
                    text = "确定要删除任务 \"${currentTask.title}\" 吗？\n\n此操作无法撤销，任务的所有信息和子任务都将被永久删除。",
                    color = TextSecondary,
                    lineHeight = 20.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        // 执行删除操作
                        coroutineScope.launch {
                            try {
                                Log.d("TaskDelete", "开始删除任务: ${currentTask.id}")
                                
                                // 通过ViewModel删除任务
                                withContext(Dispatchers.IO) {
                                    viewModel.deleteTask(currentTask.id)
                                }
                                
                                // 发送删除通知事件
                                NotificationCenter.post(TaskRefreshEvent(currentTask.id))
                                Log.d("TaskDelete", "[TaskDetailBottomSheet] ✓ 刷新事件已发送")
                                
                                // 关闭对话框
                                showDeleteConfirmDialog = false
                                
                                // 关闭详情页面
                                onDismiss()
                                
                                // 显示删除成功提示
                                withContext(Dispatchers.Main) {
                                    Toast.makeText(context, "任务已删除", Toast.LENGTH_SHORT).show()
                                }
                                
                                Log.d("TaskDelete", "✓ 任务删除成功")
                                
                            } catch (e: Exception) {
                                Log.e("TaskDelete", "✗ 删除任务失败: ${e.message}")
                                
                                // 显示错误提示
                                withContext(Dispatchers.Main) {
                                    Toast.makeText(context, "删除失败: ${e.message}", Toast.LENGTH_LONG).show()
                                }
                            }
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color(0xFFE53E3E)
                    )
                ) {
                    Text(
                        text = "删除",
                        fontWeight = FontWeight.Medium
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmDialog = false }
                ) {
                    Text(
                        text = "取消",
                        color = TextSecondary
                    )
                }
            },
            containerColor = Color.White,
            shape = RoundedCornerShape(16.dp)
        )
    }

    // 🔧 延期选择对话框
    if (showPostponeDialog) {
        PostponeDialog(
            currentTask = currentTask,
            onDismiss = { showPostponeDialog = false },
            onPostpone = { postponeOption ->
                showPostponeDialog = false

                // 计算新的时间
                val newDateTime = when (postponeOption) {
                    PostponeOption.ONE_HOUR -> currentTaskTime?.plusHours(1) ?: LocalDateTime.now().plusHours(1)
                    PostponeOption.THREE_HOURS -> currentTaskTime?.plusHours(3) ?: LocalDateTime.now().plusHours(3)
                    PostponeOption.TOMORROW -> {
                        val tomorrow = LocalDate.now().plusDays(1)
                        val currentTime = currentTaskTime?.toLocalTime() ?: LocalTime.of(9, 0)
                        LocalDateTime.of(tomorrow, currentTime)
                    }
                    PostponeOption.NEXT_WEEK -> {
                        val nextWeek = LocalDate.now().plusWeeks(1)
                        val currentTime = currentTaskTime?.toLocalTime() ?: LocalTime.of(9, 0)
                        LocalDateTime.of(nextWeek, currentTime)
                    }
                    PostponeOption.CUSTOM -> {
                        // 自定义时间，打开时间选择器
                        showTaskTimeDialog = true
                        return@PostponeDialog
                    }
                }

                // 立即更新本地状态
                currentTaskTime = newDateTime
                timeUpdateKey += 1

                // 异步保存到数据库
                coroutineScope.launch {
                    try {
                        Log.d("TaskPostpone", "延期任务到: $newDateTime")

                        // 使用TaskTimeViewModel统一管理时间更新
                        taskTimeViewModel.updateTaskTime(
                            taskId = currentTask.id,
                            startTime = newDateTime,
                            endTime = newDateTime.plusHours(1),
                            dueDate = newDateTime,
                            source = "TaskDetailBottomSheet_Postpone"
                        )

                        // 标记为手动修改日期
                        viewModel.markTaskDateAsManuallyModified(currentTask.id)

                        // 添加到自动保存队列
                        addToSaveQueue {
                            onTaskTimeUpdated(currentTask.id, newDateTime, newDateTime.plusHours(1))
                            Unit
                        }

                        // 显示成功提示
                        withContext(Dispatchers.Main) {
                            Toast.makeText(context, "任务已延期到 ${newDateTime.format(DateTimeFormatter.ofPattern("M月d日 HH:mm"))}", Toast.LENGTH_SHORT).show()
                        }

                        Log.d("TaskPostpone", "任务延期成功")

                    } catch (e: Exception) {
                        Log.e("TaskPostpone", "任务延期失败: ${e.message}", e)

                        // 显示错误提示
                        withContext(Dispatchers.Main) {
                            Toast.makeText(context, "延期失败: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                }
            }
        )
    }

    // 🔧 优化的目标关联状态管理器 - 只在真正需要时进行初始化同步
    LaunchedEffect(currentTask.id) {
        // 监听任务刷新事件，当收到刷新事件时从数据库获取最新状态
        try {
            Log.d("GoalAssociation", "[TaskDetailBottomSheet] 初始化目标关联状态监听: taskId=${currentTask.id}")
            
            // 只有当currentTask没有目标关联信息时，才从数据库初始化
            if (currentTask.goalId == null) {
                Log.d("GoalAssociation", "[TaskDetailBottomSheet] 当前任务无目标关联，尝试从数据库初始化...")
                
                // 初始化时尝试从ViewModel获取最新的任务状态
                viewModel.getTaskById(currentTask.id) { latestTask ->
                    if (latestTask != null && latestTask.goalId != null) {
                        Log.d("GoalAssociation", "[TaskDetailBottomSheet] 🔄 初始化时发现数据库中的目标关联: " +
                            "goalId=${latestTask.goalId}")
                        
                        // 只有当前任务确实没有目标关联时才更新
                        if (currentTask.goalId == null) {
                            currentTask = currentTask.copy(
                                goalId = latestTask.goalId,
                                goalTitle = null // Task模型不包含goalTitle，需要额外查询
                            )
                            
                            timeUpdateKey += 1
                            
                            Log.d("GoalAssociation", "[TaskDetailBottomSheet] ✅ 初始化时的目标关联状态已同步")
                        }
                    }
                }
            } else {
                Log.d("GoalAssociation", "[TaskDetailBottomSheet] 当前任务已有目标关联，跳过初始化: goalId=${currentTask.goalId}")
            }
        } catch (e: Exception) {
            Log.e("GoalAssociation", "[TaskDetailBottomSheet] 初始化目标关联状态失败", e)
        }
    }
    
    // 🔧 修复：只在真正需要的时候同步外部状态，避免覆盖用户选择
    LaunchedEffect(task.goalId, task.goalTitle) {
        // 只有在以下情况才同步外部状态：
        // 1. 外部goalId不为null且与当前不同
        // 2. 当前currentTask的goalId为null（即首次设置）
        // 3. 或者外部goalTitle不为null且与当前不同（确保是有效的更新）
        val shouldSync = task.goalId != null && (
            currentTask.goalId == null || // 首次设置
            (task.goalId != currentTask.goalId && task.goalTitle != null) // 有效的外部更新
        )
        
        if (shouldSync) {
            Log.d("GoalAssociation", "[TaskDetailBottomSheet] 📥 外部状态更新: " +
                "goalId=${task.goalId}, goalTitle=${task.goalTitle}")
            
            currentTask = currentTask.copy(
                goalId = task.goalId,
                goalTitle = task.goalTitle
            )
            
            timeUpdateKey += 1
            
            Log.d("GoalAssociation", "[TaskDetailBottomSheet] ✅ 外部状态已同步")
        } else {
            Log.d("GoalAssociation", "[TaskDetailBottomSheet] 跳过外部状态同步: " +
                "task.goalId=${task.goalId}, task.goalTitle=${task.goalTitle}, " +
                "currentTask.goalId=${currentTask.goalId}, shouldSync=$shouldSync")
        }
    }
}

@Composable
private fun TopBar(
    onDismiss: () -> Unit,
    onTimeIconClick: () -> Unit,
    selectedStartDateTime: LocalDateTime? = null,
    selectedEndDateTime: LocalDateTime? = null,
    // 传递自动保存状态
    saveInProgress: Boolean,
    pendingChanges: Boolean,
    performAutoSave: () -> Unit,
    // ⭐ 添加删除确认对话框状态参数
    showDeleteConfirmDialog: Boolean,
    onShowDeleteConfirmDialog: (Boolean) -> Unit
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 2.dp), // 从horizontal = 16.dp, vertical = 4.dp减少
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(28.dp) // 从32.dp减少到28.dp
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp) // 添加图标尺寸限制
                )
            }
            
            // 操作按钮组
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp), // 从6.dp减少到4.dp
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 自动保存状态指示器
                if (saveInProgress) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(end = 4.dp) // 从6.dp减少到4.dp
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(14.dp), // 从16.dp减少到14.dp
                            strokeWidth = 1.5.dp, // 从2.dp减少到1.5.dp
                            color = DustyLavender
                        )
                        Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
                        Text(
                            text = "保存中...",
                            fontSize = 10.sp, // 从12.sp减少到10.sp
                            color = DustyLavender
                        )
                    }
                } else if (pendingChanges) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(end = 4.dp) // 从6.dp减少到4.dp
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = "待保存",
                            tint = Color(0xFFFF9800),
                            modifier = Modifier.size(14.dp) // 从16.dp减少到14.dp
                        )
                        Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
                        Text(
                            text = "待保存",
                            fontSize = 10.sp, // 从12.sp减少到10.sp
                            color = Color(0xFFFF9800)
                        )
                    }
                }
                
                // 时间按钮 (原提醒按钮)
                IconButton(
                    onClick = onTimeIconClick,
                    modifier = Modifier.size(28.dp) // 从32.dp减少到28.dp
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "设置任务时间",
                        tint = if (selectedStartDateTime != null) DustyLavender else TextSecondary,
                        modifier = Modifier.size(18.dp) // 添加图标尺寸限制
                    )
                }
                
                // 手动保存按钮（仅在有待保存更改时显示）
                if (pendingChanges && !saveInProgress) {
                    IconButton(
                        onClick = { performAutoSave() },
                        modifier = Modifier
                            .size(28.dp) // 从32.dp减少到28.dp
                            .background(
                                Color(0xFF4CAF50).copy(alpha = 0.1f),
                                CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = "立即保存",
                            tint = Color(0xFF4CAF50),
                            modifier = Modifier.size(18.dp) // 添加图标尺寸限制
                        )
                    }
                }
                
                // 删除按钮
                IconButton(
                    onClick = { 
                        // ⭐ 完善删除功能：显示删除确认对话框
                        onShowDeleteConfirmDialog(true)
                    },
                    modifier = Modifier.size(28.dp) // 从32.dp减少到28.dp
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除任务",
                        tint = Color(0xFFE53E3E), // 使用红色突出删除操作
                        modifier = Modifier.size(18.dp) // 添加图标尺寸限制
                    )
                }
            }
        }
        
        // 显示已设置的任务时间（如果有）
        // 删除重复显示的时间组件，只保留TaskTitle中的时间显示
        
        // 分隔线 - 减少空白
        Divider(color = Color.LightGray.copy(alpha = 0.2f), thickness = 0.5.dp)
    }
}

@Composable
private fun TaskTimeDisplay(
    startDateTime: LocalDateTime,
    endDateTime: LocalDateTime,
    onClearTaskTime: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .background(
                DustyLavender.copy(alpha = 0.08f),
                RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Schedule,
            contentDescription = null,
            tint = DustyLavender,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 显示日期
            Text(
                text = "任务时间",
                style = MaterialTheme.typography.labelMedium,
                color = DustyLavender,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 显示时间段
            Text(
                text = formatDetailedTimeRange(startDateTime, endDateTime),
                style = MaterialTheme.typography.bodySmall,
                color = TextPrimary
            )
        }
        
        // 清除按钮
        IconButton(
            onClick = onClearTaskTime,
            modifier = Modifier.size(28.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "清除任务时间",
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@Composable
private fun TaskTitle(
    task: TaskData,
    onTimeClick: () -> Unit, // 添加点击回调参数
    timeUpdateKey: Int = 0, // 添加时间更新键，用于强制重组
    currentTaskTime: LocalDateTime? // 添加最新选择的时间作为参数
) {
    // 解析任务描述中可能存在的颜色信息
    val (cleanDescription, taskColor) = parseColorFromDescription(task.description)
    
    // 添加对timeUpdateKey的日志，帮助验证重组是否被触发
    LaunchedEffect(timeUpdateKey) {
        Log.d("TimeFormatting", "TaskTitle组件重组，timeUpdateKey=$timeUpdateKey")
    }
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp) // 从horizontal = 16.dp, vertical = 8.dp减少
    ) {
        // 标题行，如果有颜色则显示颜色标记
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 如果有颜色，则显示颜色指示点
            if (taskColor != null) {
                Box(
                    modifier = Modifier
                        .size(8.dp) // 从12.dp减少到8.dp
                        .clip(CircleShape)
                        .background(taskColor)
                        .border(0.5.dp, taskColor.copy(alpha = 0.5f), CircleShape)
                )
                Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
            }
            
            // 缩小标题字体
            Text(
                text = task.title,
                fontSize = 18.sp, // 从22.sp减少到18.sp
                fontWeight = FontWeight.Bold,
                color = TextPrimary,
                lineHeight = 22.sp, // 从28.sp减少到22.sp
                modifier = Modifier.weight(1f)
            )
        }
        
        // 显示时间信息 - 减少垂直间距
        Row(
            modifier = Modifier
                .padding(top = 4.dp) // 从6.dp减少到4.dp
                .fillMaxWidth()
                .clickable { onTimeClick() }, // 使用传入的回调函数
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Schedule,
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(14.dp) // 从18.dp减少到14.dp
            )
            Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
            
            // 使用详细的时间范围格式 - 使用remember结合timeUpdateKey，确保时间更新时重新计算
            val timeText = remember(timeUpdateKey, currentTaskTime) {
                // 直接使用currentTaskTime，它现在是基于currentTaskTime的derivedStateOf
                val effectiveDateTime = currentTaskTime
                
                if (effectiveDateTime != null) {
                    Log.d("TimeFormatting", "TaskTitle组件中格式化时间: timeUpdateKey=$timeUpdateKey, 时间值=$effectiveDateTime")
                    formatDetailedTimeRange(effectiveDateTime)
                } else {
                    // 如果没有直接的时间，使用daysLeft计算时间
                    val calculatedDate = convertDaysLeftToDateTime(task.daysLeft)
                    Log.d("TimeFormatting", "TaskTitle组件中从daysLeft计算时间: timeUpdateKey=$timeUpdateKey, task.daysLeft=${task.daysLeft}, 计算结果=${calculatedDate}")
                    formatDetailedTimeRange(calculatedDate)
                }
            }
            
            Column {
                Text(
                    text = "任务时间",
                    style = TextStyle(
                        color = DustyLavender,
                        fontSize = 11.sp, // 从14.sp减少到11.sp
                        fontWeight = FontWeight.Medium
                    )
                )
                
                Spacer(modifier = Modifier.height(1.dp)) // 从2.dp减少到1.dp
                
                Text(
                    text = timeText,
                    style = TextStyle(
                        color = TextPrimary,
                        fontSize = 12.sp // 从14.sp减少到12.sp
                    )
                )
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // 添加一个小编辑图标，仅作为视觉提示，点击事件已经在Row上设置
            Icon(
                imageVector = Icons.Default.Edit,
                contentDescription = "编辑时间",
                tint = DustyLavender,
                modifier = Modifier
                    .size(14.dp) // 从18.dp减少到14.dp
                    .alpha(0.7f)
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class, ExperimentalAnimationApi::class)
@Composable
private fun TaskDetailContent(
    task: TaskData, // task 对象现在包含了从 localSubTasks 更新的 subTasks
    onTagsUpdated: (String, List<String>) -> Unit,
    onPriorityUpdated: (String, Priority) -> Unit,
    onSubTaskAdded: (String, SubTask) -> Unit,
    onSubTaskUpdated: (String, SubTask) -> Unit,
    onSubTaskDeleted: (String, String) -> Unit,
    onAllSubTasksCompleted: () -> Unit,
    addToSaveQueue: (() -> Unit) -> Unit,
    viewModel: TaskDetailViewModel, // 添加viewModel参数
    modifier: Modifier = Modifier
) {
    // 获取Context和CoroutineScope
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // 本地子任务状态管理 - 用于即时UI更新
    var localSubTasks by remember(task.id) { mutableStateOf(task.subTasks) }
    
    // 用户操作保护机制 - 防止外部同步覆盖用户操作
    var userOperationTimestamp by remember { mutableStateOf(0L) }
    var protectedSubTaskIds by remember { mutableStateOf(setOf<String>()) }
    
    // 🔧 智能同步机制：保护用户操作不被外部数据覆盖
    LaunchedEffect(task.subTasks) {
        val currentTime = System.currentTimeMillis()
        val timeSinceUserOp = currentTime - userOperationTimestamp
        
        Log.d("SubTaskSync", "[TaskDetailBottomSheet] 外部子任务数据变化检测")
        Log.d("SubTaskSync", "[TaskDetailBottomSheet] 距离用户操作时间: ${timeSinceUserOp}ms")
        Log.d("SubTaskSync", "[TaskDetailBottomSheet] 保护的子任务IDs: $protectedSubTaskIds")
        
        // 如果用户刚操作过（3秒内），则智能合并而非完全覆盖
        if (timeSinceUserOp < 3000 && protectedSubTaskIds.isNotEmpty()) {
            Log.d("SubTaskSync", "[TaskDetailBottomSheet] 用户近期有操作，启动智能合并模式")
            
            // 智能合并：保留用户修改的子任务状态，同步其他子任务
            val mergedSubTasks = task.subTasks.map { externalSubTask ->
                if (protectedSubTaskIds.contains(externalSubTask.id)) {
                    // 查找本地对应的子任务，保留用户状态
                    localSubTasks.find { it.id == externalSubTask.id } ?: externalSubTask
                } else {
                    // 非保护项目，使用外部状态
                    externalSubTask
                }
            }
            
            Log.d("SubTaskSync", "[TaskDetailBottomSheet] 智能合并完成，保护了 ${protectedSubTaskIds.size} 个子任务")
            localSubTasks = mergedSubTasks
        } else {
            // 用户没有近期操作，直接同步外部数据
            Log.d("SubTaskSync", "[TaskDetailBottomSheet] 直接同步外部子任务数据")
            localSubTasks = task.subTasks
            
            // 清除过期的保护状态
            if (timeSinceUserOp >= 3000) {
                protectedSubTaskIds = setOf()
            }
        }
    }
    
    // 子任务删除状态
    var taskDeleting by remember { mutableStateOf<String?>(null) }
    
    // 子任务输入面板状态控制
    var showSubTaskInput by remember { mutableStateOf(false) }
    var showAdvancedSettings by remember { mutableStateOf(false) }
    var isEditingDescription by remember { mutableStateOf(false) }
    var descriptionText by remember { 
        val (cleanDesc, _) = parseColorFromDescription(task.description)
        mutableStateOf(cleanDesc) 
    }
    var showDescriptionToolbar by remember { mutableStateOf(false) }
    
    // 标签相关状态 - 添加数据清理
    var tags by remember { 
        mutableStateOf(
            task.tags?.mapNotNull { tag ->
                when {
                    // 清理被污染的标签数据
                    tag.contains("TaskTag(") -> {
                        Log.w("TagCleanup", "发现被污染的标签，已清理: $tag")
                        null
                    }
                    tag.isNotBlank() && !tag.contains("(") -> tag
                    else -> null
                }
            } ?: emptyList()
        ) 
    }
    var isEditingTags by remember { mutableStateOf(false) }

    // 高级设置状态
    var selectedDateTime by remember { mutableStateOf<LocalDateTime?>(null) }
    var selectedPriority by remember { mutableStateOf<Priority>(Priority.MEDIUM) }
    
    // 更新标签列表当task.tags变化时 - 添加数据清理
    LaunchedEffect(task.tags) {
        val cleanedTags = task.tags?.mapNotNull { tag ->
            when {
                // 清理被污染的标签数据
                tag.contains("TaskTag(") -> {
                    Log.w("TagCleanup", "发现被污染的标签，已清理: $tag")
                    null
                }
                tag.isNotBlank() && !tag.contains("(") -> tag
                else -> null
            }
        } ?: emptyList()
        
        if (cleanedTags != tags) {
            Log.d("TagCleanup", "更新清理后的标签: ${tags.size} -> ${cleanedTags.size}")
            tags = cleanedTags
        }
    }
    
    // 监控子任务完成状态 (直接使用传入的 task.subTasks)
    LaunchedEffect(task.subTasks) {
        android.util.Log.d("TaskContent", "监控子任务完成状态: 任务ID=${task.id}, 子任务数量=${task.subTasks.size}")
        // 如果有子任务且全部完成，触发任务完成回调
        if (task.subTasks.isNotEmpty() && task.subTasks.all { it.isCompleted }) {
            android.util.Log.d("TaskContent", "所有子任务已完成，触发 onAllSubTasksCompleted 回调")
            onAllSubTasksCompleted()
        }
    }
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 2.dp) // 从horizontal = 16.dp, vertical = 4.dp减少
            .clip(RoundedCornerShape(12.dp)), // 从16.dp减少到12.dp
        color = Color.White,
        shape = RoundedCornerShape(12.dp), // 从16.dp减少到12.dp
        shadowElevation = 0.dp // 禁用Surface自带阴影
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp) // 从horizontal = 16.dp, vertical = 12.dp减少
        ) {
            // 🎯 简化：直接显示用户设置的优先级，移除逾期检测
            var currentPriority by remember(task.id) { 
                mutableStateOf(task.priority ?: Priority.MEDIUM) 
            }
            var showPriorityMenu by remember { mutableStateOf(false) }
            var priorityUpdateInProgress by remember { mutableStateOf(false) }
            var lastKnownPriority by remember(task.id) { 
                mutableStateOf(task.priority ?: Priority.MEDIUM) 
            }
            
            // 🔧 优化：简化的优先级更新处理，始终使用task.priority原始字段
            LaunchedEffect(task.priority) {
                // 只有在不是用户主动更新时才同步外部变化
                if (!priorityUpdateInProgress && task.priority != null && currentPriority != task.priority) {
                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] 外部task原始优先级变化: $currentPriority -> ${task.priority}")
                    currentPriority = task.priority!!
                    lastKnownPriority = task.priority!!
                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ✓ 外部优先级变化已同步")
                }
            }
            
            // 优先级选择器 - 🎯 简化版本，直接显示用户设置的优先级
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(6.dp)) // 从8.dp减少到6.dp
                    .clickable { 
                        if (!priorityUpdateInProgress) {
                            showPriorityMenu = true 
                        }
                    }
                    .padding(vertical = 8.dp) // 从12.dp减少到8.dp
                    // 💡 添加更新状态的视觉反馈
                    .alpha(if (priorityUpdateInProgress) 0.6f else 1.0f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp) // 从10.dp减少到8.dp
                        .clip(CircleShape)
                        .background(getPriorityColor(currentPriority))
                )
                Spacer(modifier = Modifier.width(8.dp)) // 从10.dp减少到8.dp
                
                // 🎯 简化：直接显示用户设置的优先级，无逾期提示
                Text(
                    text = if (priorityUpdateInProgress) {
                        "${getPriorityText(currentPriority)} (更新中...)"
                    } else {
                        getPriorityText(currentPriority)
                    },
                    color = getPriorityColor(currentPriority),
                    fontSize = 13.sp, // 从15.sp减少到13.sp
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.width(4.dp)) // 从6.dp减少到4.dp
                
                // 💡 更新状态指示器
                if (priorityUpdateInProgress) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(12.dp), // 从16.dp减少到12.dp
                        strokeWidth = 1.5.dp, // 从2.dp减少到1.5.dp
                        color = getPriorityColor(currentPriority)
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.ArrowDropDown,
                        contentDescription = "切换优先级",
                        tint = getPriorityColor(currentPriority).copy(alpha = 0.7f),
                        modifier = Modifier.size(16.dp) // 从20.dp减少到16.dp
                    )
                }
            }
            
            // 使用Dialog代替DropdownMenu实现纯净的下拉菜单
            if (showPriorityMenu) {
                Dialog(
                    onDismissRequest = { showPriorityMenu = false },
                    properties = DialogProperties(
                        dismissOnBackPress = true,
                        dismissOnClickOutside = true,
                        usePlatformDefaultWidth = false
                    )
                ) {
                    // 在Dialog内部创建局部的coroutineScope
                    val localCoroutineScope = rememberCoroutineScope()
                    
                    Surface(
                        modifier = Modifier
                            .width(240.dp) // 从280.dp减少到240.dp
                            .wrapContentHeight()
                            .clip(RoundedCornerShape(12.dp)), // 从16.dp减少到12.dp
                        color = Color.White,
                        shape = RoundedCornerShape(12.dp), // 从16.dp减少到12.dp
                        shadowElevation = 3.dp // 从4.dp减少到3.dp
                    ) {
                        Column(
                            modifier = Modifier.padding(vertical = 6.dp) // 从8.dp减少到6.dp
                        ) {
                            // 使用固定的优先级顺序，与图片一致
                            listOf(Priority.URGENT, Priority.HIGH, Priority.MEDIUM, Priority.LOW).forEach { priority ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { 
                                            if (priorityUpdateInProgress) {
                                                Log.w("PriorityUpdate", "[TaskDetailBottomSheet] 优先级更新正在进行中，忽略重复请求")
                                                return@clickable
                                            }
                                            
                                            Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ===== 用户选择优先级 =====")
                                            Log.d("PriorityUpdate", "[TaskDetailBottomSheet] 用户选择新优先级: $priority")
                                            Log.d("PriorityUpdate", "[TaskDetailBottomSheet] 任务ID: ${task.id}")
                                            
                                            // 保存原始状态用于回滚
                                            val originalPriority = currentPriority
                                            
                                            // 立即更新UI状态提供即时反馈
                                            currentPriority = priority
                                            priorityUpdateInProgress = true
                                            showPriorityMenu = false
                                            
                                            // 使用协程处理保存
                                            localCoroutineScope.launch {
                                                try {
                                                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] 开始保存到数据库...")
                                                    
                                                    // 调用回调保存到数据库
                                                    onPriorityUpdated(task.id, priority)
                                                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ✓ 数据库保存调用完成")
                                                    
                                                    // 等待数据库操作完成
                                                    kotlinx.coroutines.delay(300)
                                                    
                                                    // 发送刷新事件
                                                    NotificationCenter.post(TaskRefreshEvent(task.id))
                                                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ✓ 刷新事件已发送")
                                                    
                                                    // 验证同步成功
                                                    kotlinx.coroutines.delay(500)
                                                    
                                                    lastKnownPriority = priority
                                                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ✓ 优先级同步完成: $priority")
                                                    
                                                } catch (e: Exception) {
                                                    Log.e("PriorityUpdate", "[TaskDetailBottomSheet] ✗ 优先级更新失败: ${e.message}")
                                                    
                                                    // 回滚到原始状态
                                                    currentPriority = originalPriority
                                                    Log.d("PriorityUpdate", "[TaskDetailBottomSheet] 已回滚到原始优先级: $originalPriority")
                                                    
                                                } finally {
                                                    priorityUpdateInProgress = false
                                                }
                                            }
                                            
                                            Log.d("PriorityUpdate", "[TaskDetailBottomSheet] ✓ 优先级选择处理完成")
                                        }
                                        .padding(horizontal = 12.dp, vertical = 8.dp), // 从horizontal = 16.dp, vertical = 12.dp减少
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 优先级颜色点
                                    Box(
                                        modifier = Modifier
                                            .size(8.dp) // 从10.dp减少到8.dp
                                            .clip(CircleShape)
                                            .background(getPriorityColor(priority))
                                    )
                                    Spacer(modifier = Modifier.width(12.dp)) // 从16.dp减少到12.dp
                                    // 优先级文本
                                    Text(
                                        text = getPriorityText(priority),
                                        color = TextPrimary,
                                        fontSize = 12.sp, // 从14.sp减少到12.sp
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
            }

            Divider(
                color = Color.LightGray.copy(alpha = 0.3f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(vertical = 4.dp) // 从8.dp减少到4.dp
            )

            // 🔧 循环设置区域
            var isRecurring by remember { mutableStateOf(false) }
            var recurrenceSettings by remember { mutableStateOf(RecurrenceSettings.none()) }
            var selectedRecurrenceType by remember { mutableStateOf(RecurrenceType.NONE) }
            var showRecurrenceDialog by remember { mutableStateOf(false) }

            // 🔧 从任务数据中初始化循环设置
            LaunchedEffect(task.id) {
                try {
                    // 从task中读取循环设置
                    isRecurring = task.isRecurring

                    if (task.recurringPattern != null) {
                        val settings = Json.decodeFromString<RecurrenceSettings>(task.recurringPattern)
                        recurrenceSettings = settings
                        selectedRecurrenceType = RecurrenceType.values().find { it.name == settings.type } ?: RecurrenceType.NONE
                    } else {
                        recurrenceSettings = RecurrenceSettings.none()
                        selectedRecurrenceType = RecurrenceType.NONE
                    }
                } catch (e: Exception) {
                    Log.e("TaskDetailBottomSheet", "解析循环设置失败", e)
                    isRecurring = false
                    recurrenceSettings = RecurrenceSettings.none()
                    selectedRecurrenceType = RecurrenceType.NONE
                }
            }

            // 循环设置行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(6.dp))
                    .clickable { showRecurrenceDialog = true }
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Repeat,
                    contentDescription = "循环设置",
                    tint = if (isRecurring) DustyLavender else TextSecondary,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = if (isRecurring) selectedRecurrenceType.displayName else "不循环",
                    color = if (isRecurring) DustyLavender else TextSecondary,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "设置循环",
                    tint = TextSecondary.copy(alpha = 0.7f),
                    modifier = Modifier.size(16.dp)
                )
            }

            // 🔧 循环设置对话框
            if (showRecurrenceDialog) {
                RecurrenceSettingsDialog(
                    isRecurring = isRecurring,
                    recurrenceSettings = recurrenceSettings,
                    selectedRecurrenceType = selectedRecurrenceType,
                    onDismiss = { showRecurrenceDialog = false },
                    onConfirm = { recurring, settings, type ->
                        isRecurring = recurring
                        recurrenceSettings = settings
                        selectedRecurrenceType = type
                        showRecurrenceDialog = false

                        // 保存循环设置到数据库
                        try {
                            val recurringPatternJson = if (recurring) {
                                Json.encodeToString(settings)
                            } else {
                                null
                            }

                            // 更新任务的循环设置
                            // TODO: 实现循环设置的保存逻辑
                            Log.d("TaskDetailBottomSheet", "循环设置已更新: recurring=$recurring, pattern=$recurringPatternJson")
                        } catch (e: Exception) {
                            Log.e("TaskDetailBottomSheet", "保存循环设置失败", e)
                        }
                    }
                )
            }

            Divider(
                color = Color.LightGray.copy(alpha = 0.3f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(vertical = 4.dp)
            )
            
            // 描述内容编辑区域
            if (isEditingDescription) {
                Column {
                    var focusState by remember { mutableStateOf(false) }
                    val focusRequester = remember { FocusRequester() }
                    
                    // ⭐ 自动编号功能状态
                    var autoNumberingEnabled by remember { mutableStateOf(false) }
                    var lastTextLength by remember { mutableStateOf(0) }
                    
                    // 自动聚焦到输入框
                    LaunchedEffect(isEditingDescription) {
                        if (isEditingDescription) {
                            delay(100) // 短暂延迟确保UI渲染完成
                            focusRequester.requestFocus()
                        }
                    }
                    
                    /**
                     * 智能换行自动编号处理函数
                     * 检测用户换行输入，自动添加编号
                     */
                    fun handleAutoNumbering(newText: String): String {
                        if (!autoNumberingEnabled) return newText
                        
                        // 检测是否是换行输入（新文本比旧文本长且以换行符结尾）
                        if (newText.length > lastTextLength && newText.endsWith("\n")) {
                            val lines = newText.split("\n")
                            if (lines.size >= 2) {
                                val lastCompleteLine = lines[lines.size - 2].trim()
                                
                                // ⭐ 优化编号检测：支持多种编号格式
                                val numberRegex = Regex("^(\\d+)[.)]\\.?\\s.*")
                                val match = numberRegex.find(lastCompleteLine)
                                
                                if (match != null) {
                                    // 提取编号并递增
                                    val currentNumber = match.groupValues[1].toIntOrNull() ?: 0
                                    val nextNumber = currentNumber + 1
                                    
                                    Log.d("AutoNumbering", "检测到编号格式，当前: $currentNumber, 下一个: $nextNumber")
                                    
                                    // 自动添加下一个编号
                                    return newText + "$nextNumber. "
                                } else if (lastCompleteLine.isNotBlank() && !lastCompleteLine.matches(Regex("^\\d+[.)]\\.?\\s.*"))) {
                                    // ⭐ 如果上一行有内容但没有编号，检查是否在编号序列中
                                    val allLines = newText.trimEnd('\n').split("\n")
                                    val numberedLines = allLines.filter { it.trim().matches(Regex("^\\d+[.)]\\.?\\s.*")) }
                                    
                                    if (numberedLines.isNotEmpty()) {
                                        // 如果之前有编号行，继续编号序列
                                        val nextNumber = numberedLines.size + 1
                                        Log.d("AutoNumbering", "继续编号序列，下一个编号: $nextNumber")
                                        return newText + "$nextNumber. "
                                    }
                                }
                            }
                        }
                        
                        return newText
                    }
                    
                    OutlinedTextField(
                        value = descriptionText,
                        onValueChange = { newText ->
                            // ⭐ 应用智能自动编号
                            val processedText = handleAutoNumbering(newText)
                            lastTextLength = descriptionText.length
                            descriptionText = processedText
                            
                            // 添加描述更新到自动保存队列
                            addToSaveQueue {
                                // 这里需要调用更新任务描述的回调
                                // 由于当前没有onDescriptionUpdated回调，我们可以通过其他方式处理
                                Log.d("AutoSave", "[TaskDetailBottomSheet] 描述更新: $processedText")
                                Unit // 明确返回 Unit
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .focusRequester(focusRequester) // 添加焦点请求器
                            .onFocusChanged { focusStateChange: FocusState ->
                                focusState = focusStateChange.isFocused
                                if (focusStateChange.isFocused) {
                                    showDescriptionToolbar = true
                                }
                            },
                        colors = TextFieldDefaults.outlinedTextFieldColors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color.LightGray.copy(alpha = 0.5f)
                        ),
                        placeholder = { 
                            Text(
                                "添加描述...",
                                style = TextStyle(textAlign = TextAlign.Start) // 占位符左对齐
                            ) 
                        },
                        textStyle = TextStyle(
                            textAlign = TextAlign.Start // 文本始终左对齐
                        ),
                        shape = RoundedCornerShape(8.dp),
                        minLines = 3,
                        maxLines = 6
                    )
                    
                    // 描述编辑工具栏 - 简洁版本，删除不必要的图标
                    if (showDescriptionToolbar) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 添加项目编号列表（主要功能）
                            IconButton(
                                onClick = { 
                                    // ⭐ 智能列表功能：切换自动编号模式 + 手动编号
                                    if (autoNumberingEnabled) {
                                        // 如果自动编号已启用，执行手动编号
                                        val currentText = descriptionText.trim()
                                        if (currentText.isNotBlank()) {
                                            val lines = currentText.split("\n")
                                            val filteredLines = lines.filter { it.trim().isNotBlank() }
                                            
                                            if (filteredLines.isNotEmpty()) {
                                                val numberedLines = filteredLines.mapIndexed { index, line ->
                                                    val trimmedLine = line.trim()
                                                    // 检查是否已经有编号
                                                    if (!trimmedLine.matches(Regex("^\\d+[.)]\\s.*")) && 
                                                        !trimmedLine.matches(Regex("^[-*]\\s.*"))) {
                                                        "${index + 1}. $trimmedLine"
                                                    } else {
                                                        trimmedLine
                                                    }
                                                }
                                                descriptionText = numberedLines.joinToString("\n")
                                            }
                                        }
                                    } else {
                                        // 启用自动编号模式
                                        autoNumberingEnabled = true
                                        
                                        // 首次启用时，如果当前文本为空或只有一行，添加第一个编号
                                        if (descriptionText.isBlank()) {
                                            descriptionText = "1. "
                                        } else if (!descriptionText.contains("\n") && !descriptionText.trim().matches(Regex("^\\d+[.)]\\s.*"))) {
                                            // 如果只有一行且没有编号，添加编号
                                            descriptionText = "1. ${descriptionText.trim()}"
                                        }
                                        
                                        // 自动聚焦到输入框末尾
                                        focusRequester.requestFocus()
                                    }
                                },
                                modifier = Modifier.size(40.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.List,
                                    contentDescription = if (autoNumberingEnabled) "手动重新编号" else "启用自动编号",
                                    tint = if (autoNumberingEnabled) Color(0xFF8B5CF6) else DustyLavender // 启用时高亮显示
                                )
                            }
                            
                            // ⭐ 自动编号状态指示器和关闭按钮
                            if (autoNumberingEnabled) {
                                // 状态指示文本
                                Text(
                                    text = "自动编号",
                                    fontSize = 12.sp,
                                    color = Color(0xFF8B5CF6),
                                    fontWeight = FontWeight.Medium,
                                    modifier = Modifier.padding(horizontal = 4.dp)
                                )
                                
                                // 关闭自动编号按钮
                            IconButton(
                                    onClick = { 
                                        autoNumberingEnabled = false
                                        Log.d("AutoNumbering", "自动编号模式已关闭")
                                    },
                                    modifier = Modifier.size(24.dp)
                            ) {
                                Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "关闭自动编号",
                                        tint = Color(0xFF8B5CF6),
                                        modifier = Modifier.size(16.dp)
                                )
                                }
                            }
                            
                            // 撤销功能
                            IconButton(
                                onClick = { 
                                    // 撤销到原始状态
                                    val (cleanDesc, _) = parseColorFromDescription(task.description)
                                    descriptionText = cleanDesc
                                },
                                modifier = Modifier.size(40.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Undo,
                                    contentDescription = "撤销",
                                    tint = TextSecondary
                                )
                            }
                            
                            // 重做/清空功能
                            IconButton(
                                onClick = { 
                                    // 清空所有内容
                                    descriptionText = ""
                                },
                                modifier = Modifier.size(40.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Redo,
                                    contentDescription = "清空内容",
                                    tint = TextSecondary
                                )
                            }
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            // 保存按钮
                            TextButton(
                                onClick = { 
                                    // 保存更改
                                    isEditingDescription = false
                                    showDescriptionToolbar = false
                                    
                                    // 直接执行保存操作
                                    coroutineScope.launch {
                                        try {
                                            // 实现保存描述功能
                                            Log.d("TaskDetailBottomSheet", "保存描述: $descriptionText")
                                            
                                            // 更新任务描述
                                            withContext(Dispatchers.IO) {
                                                viewModel.updateTaskDescription(task.id, descriptionText)
                                            }
                                            Log.d("TaskDetailBottomSheet", "✓ 描述保存成功")
                                            
                                            // 发送刷新事件
                                            NotificationCenter.post(TaskRefreshEvent(task.id))
                                        } catch (e: Exception) {
                                            Log.e("TaskDetailBottomSheet", "✗ 描述保存失败: ${e.message}")
                                        }
                                    }
                                }
                            ) {
                                Text(
                                    text = "保存",
                                    color = DustyLavender,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            } else {
                // 显示描述或"添加描述"的文本
                if (descriptionText.isNotEmpty()) {
                    Text(
                        text = descriptionText,
                        color = TextPrimary,
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .clickable { isEditingDescription = true }
                    )
                } else {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(8.dp))
                            .clickable { isEditingDescription = true }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = TextSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "添加描述...",
                            color = TextSecondary.copy(alpha = 0.6f),
                            fontSize = 16.sp
                        )
                    }
                }
            }
            
            // 标签区域
            if (tags.isNotEmpty() || isEditingTags) {
                Column(
                    modifier = Modifier
                        .padding(vertical = 8.dp)
                        .animateContentSize()
                ) {
                    // 标签标题行
                    if (!isEditingTags) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Label,
                                contentDescription = null,
                                tint = TextSecondary.copy(alpha = 0.7f),
                                modifier = Modifier.size(16.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "标签",
                                color = TextSecondary.copy(alpha = 0.7f),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            // 编辑按钮
                            IconButton(
                                onClick = { isEditingTags = true },
                                modifier = Modifier.size(24.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "编辑标签",
                                    tint = TextSecondary.copy(alpha = 0.6f),
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                    
                    // 显示现有标签
                    if (tags.isNotEmpty() && !isEditingTags) {
                        TagDisplayArea(tags = tags)
                    }
                    
                    // 标签编辑区域
                    if (isEditingTags) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            // 标签编辑标题与关闭按钮
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "编辑标签",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = TextPrimary
                                )
                                
                                IconButton(
                                    onClick = { isEditingTags = false },
                                    modifier = Modifier.size(24.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "关闭",
                                        tint = TextSecondary
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp)) // 从12.dp减少到8.dp
                            
                            // 显示可编辑的标签（可删除）
                            if (tags.isNotEmpty()) {
            TagDisplayArea(
                                    tags = tags, 
                                    onRemoveTag = { tagToRemove ->
                                        val updatedTags = tags.filter { it != tagToRemove }
                                        tags = updatedTags
                                        // 添加标签删除到自动保存队列
                                        addToSaveQueue {
                                        onTagsUpdated(task.id, updatedTags)
                                            Unit // 明确返回 Unit
                                        }
                                        Log.d("AutoSave", "[TaskDetailBottomSheet] 标签删除立即保存")
                                    }
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp)) // 从16.dp减少到8.dp
                            
                            // 添加新标签
            EnhancedTagInputField(
                onTagAdded = { newTag ->
                                    Log.d("TagSave", "[onTagAdded] 开始添加标签: '$newTag'")
                                    Log.d("TagSave", "[onTagAdded] 当前标签列表: $tags")
                                    
                                    if (!tags.contains(newTag)) {
                                        val updatedTags = tags + newTag
                                        tags = updatedTags
                                        
                                        Log.d("TagSave", "[onTagAdded] 更新后的标签列表: $updatedTags")
                                        Log.d("TagSave", "[onTagAdded] 准备调用onTagsUpdated: taskId=${task.id}")
                                        
                                        // 🔧 关键修复：确保传递的是纯字符串列表
                                        val stringTags = updatedTags.mapNotNull { tag ->
                                            when (tag) {
                                                is String -> tag
                                                else -> {
                                                    Log.w("TagSave", "[onTagAdded] 检测到非字符串标签，已过滤: $tag")
                                                    null
                                                }
                                            }
                                        }
                                        
                                        Log.d("TagSave", "[onTagAdded] 正在执行onTagsUpdated: taskId=${task.id}, tags=$stringTags")
                                        
                                        onTagsUpdated(task.id, stringTags)
                                        
                                        Log.d("TagSave", "[onTagAdded] onTagsUpdated执行完成")
                                    } else {
                                        Log.d("TagSave", "[onTagAdded] 标签已存在，跳过添加: '$newTag'")
                                    }
                                },
                                existingTagOptions = listOf("工作", "生活", "学习", "重要", "紧急"),
                onTagsUpdated = onTagsUpdated
            )
}

                Divider(
                    color = Color.LightGray.copy(alpha = 0.3f), 
                    thickness = 0.5.dp,
                            modifier = Modifier.padding(vertical = 6.dp) // 从8.dp减少到6.dp
                )
                    }
                }
            } else {
                // 添加标签按钮（当没有标签时显示）
                Row(
        modifier = Modifier
            .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .clickable { isEditingTags = true }
                        .padding(vertical = 8.dp), // 从12.dp减少到8.dp
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Label,
                        contentDescription = null,
                        tint = TextSecondary.copy(alpha = 0.6f),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "添加标签...",
                        color = TextSecondary.copy(alpha = 0.6f),
                        fontSize = 16.sp
                    )
                }
                
                Divider(
                    color = Color.LightGray.copy(alpha = 0.3f), 
                    thickness = 0.5.dp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            // 子任务部分
            Column(modifier = Modifier.animateContentSize()) {
                // 子任务标题
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 4.dp), // 从8.dp减少到4.dp
                    verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "子任务",
                fontWeight = FontWeight.Bold,
                        fontSize = 14.sp, // 从17.sp减少到14.sp
                        color = TextPrimary
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 添加进度指示 (使用本地状态 localSubTasks)
                    if (localSubTasks.isNotEmpty()) {
                        val completedCount = localSubTasks.count { it.isCompleted }
                        val totalCount = localSubTasks.size
                        
                        Text(
                            text = "$completedCount/$totalCount",
                            fontSize = 11.sp, // 从14.sp减少到11.sp
                            color = if (completedCount == totalCount)
                                DustyLavender
                            else
                                DustyLavender.copy(alpha = 0.8f),
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
                    
                    // 添加子任务按钮（直接显示在标题栏中）
                    IconButton(
                        onClick = { showSubTaskInput = !showSubTaskInput },
                        modifier = Modifier
                            .size(28.dp) // 从32.dp减少到28.dp
                            .background(
                                color = if (showSubTaskInput) DustyLavender.copy(alpha = 0.1f) else Color.Transparent,
                                shape = CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = if (showSubTaskInput) Icons.Default.Remove else Icons.Default.Add,
                            contentDescription = if (showSubTaskInput) "隐藏输入框" else "显示输入框",
                            tint = DustyLavender,
                            modifier = Modifier.size(16.dp) // 添加图标尺寸限制
                        )
                    }
                }
                
                // 内联子任务输入框
                AnimatedVisibility(
                    visible = showSubTaskInput,
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    InlineSubTaskInput(
                        onAddSubTask = { newSubTask ->
                            // 添加震动反馈
                            // HapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            
                            // 通过回调添加子任务
                            onSubTaskAdded(task.id, newSubTask)
                            
                            // 立即更新本地列表以提供即时反馈
                            // 在LaunchedEffect中会通过task.subTasks更新，这里主要是为了UI即时响应
                            // subTasks = subTasks + newSubTask  <- 移除对内部状态的修改
                            // 注意：添加子任务的回调 onSubTaskAdded 应该触发数据源更新，
                            // 然后父组件传递新的 TaskData，最终更新这里的 task.subTasks
                        },
                                        modifier = Modifier.padding(bottom = 4.dp) // 从8.dp减少到4.dp
            )
                }
                
                // 显示现有子任务 - 使用动画增强UI体验 (使用本地状态的子任务)
                AnimatedSubTaskList(
                    subTasks = localSubTasks, // 使用本地状态的 subTasks，确保UI即时更新
                    onTaskClick = { /* TODO: Implement task click */ },
                    onCheckChange = { subTask, isChecked ->
                        Log.d("SubTaskUpdate", "🔄 开始更新子任务状态: ${subTask.id}, 完成状态: $isChecked")
                        
                        // 🔧 记录用户操作时间和保护的子任务ID
                        userOperationTimestamp = System.currentTimeMillis()
                        protectedSubTaskIds = protectedSubTaskIds + subTask.id
                        Log.d("SubTaskSync", "[TaskDetailBottomSheet] 记录用户操作: ${subTask.id}, 保护时间戳: $userOperationTimestamp")
                        
                        // 创建更新后的子任务对象
                        val updatedSubTask = subTask.copy(isCompleted = isChecked)
                        
                        // 立即更新本地子任务状态以提供即时UI反馈
                        localSubTasks = localSubTasks.map { 
                            if (it.id == subTask.id) it.copy(isCompleted = isChecked) else it 
                        }
                        
                        // 异步保存到数据库
                        coroutineScope.launch {
                            try {
                                Log.d("SubTaskUpdate", "💾 开始保存子任务状态到数据库...")
                                
                                // 调用外部回调保存子任务状态
                                onSubTaskUpdated(task.id, updatedSubTask)
                                Log.d("SubTaskUpdate", "✅ 子任务状态保存成功: ${subTask.id}")
                                
                                // 发送刷新事件确保UI数据同步
                                NotificationCenter.post(TaskRefreshEvent(task.id))
                                
                                // 等待数据库操作完成
                                delay(200)
                                
                                // 检查是否所有子任务都已完成（使用最新的本地状态）
                                val allSubTasksCompleted = localSubTasks.all { it.isCompleted }
                                
                                // 详细日志：显示每个子任务的完成状态
                                Log.d("SubTaskUpdate", "🔍 检查所有子任务完成状态: ${localSubTasks.size}个子任务, 全部完成: $allSubTasksCompleted")
                                localSubTasks.forEachIndexed { index, subTaskItem ->
                                    Log.d("SubTaskUpdate", "   子任务${index + 1}: ${subTaskItem.title} - 完成状态: ${subTaskItem.isCompleted}")
                                }
                                
                                if (allSubTasksCompleted && localSubTasks.isNotEmpty()) {
                                    Log.d("SubTaskUpdate", "🎉 所有子任务已完成，触发父任务完成")
                                    // 延迟一点时间确保子任务状态保存完成
                                    delay(300)
                                    
                                                                    // 触发父任务完成
                                onAllSubTasksCompleted()
                                
                                // 显示成功提示
                                Log.d("SubTaskUpdate", "✅ 父任务已自动完成")
                                
                                // 发送任务刷新事件确保数据同步
                                NotificationCenter.post(TaskRefreshEvent(task.id))
                                } else {
                                    Log.d("SubTaskUpdate", "⏳ 还有子任务未完成，等待用户继续操作")
                                }
                                
                            } catch (e: Exception) {
                                Log.e("SubTaskUpdate", "❌ 子任务状态保存失败: ${subTask.id}", e)
                                
                                // 保存失败时回滚本地状态
                                localSubTasks = localSubTasks.map { 
                                    if (it.id == subTask.id) it.copy(isCompleted = !isChecked) else it 
                                }
                                
                                // 显示错误提示（可以添加Toast）
                                Log.e("SubTaskUpdate", "状态已回滚，请重试")
                                
                                // 可以在这里添加Toast通知用户
                                try {
                                    Toast.makeText(context, "子任务状态保存失败，请重试", Toast.LENGTH_SHORT).show()
                                } catch (ex: Exception) {
                                    Log.e("SubTaskUpdate", "显示错误提示失败", ex)
                                }
                            }
                        }
                    },
                    onDelete = { subTask ->
                        taskDeleting = subTask.id
                        // 使用协程和延迟创建删除动画效果
                        CoroutineScope(Dispatchers.Main).launch {
                            kotlinx.coroutines.delay(300) // 等待动画完成
                            onSubTaskDeleted(task.id, subTask.id)
                            // 不再需要更新本地状态
                            // subTasks = subTasks.filter { it.id != subTask.id }
                            taskDeleting = null

                            // 删除后检查剩余子任务是否全部完成
                            // 同样依赖外部数据更新后的检查
                        }
                    },
                    taskDeleting = taskDeleting
                )

                // 子任务空状态 (检查本地子任务状态)
                if (localSubTasks.isEmpty() && !showSubTaskInput) {
                    android.util.Log.d("TaskDetailBottomSheet", "显示空子任务状态UI, 本地子任务列表: empty=${localSubTasks.isEmpty()}, showInput=${showSubTaskInput}")
                    EmptySubTaskState(
                        onAddClick = {
                            android.util.Log.d("TaskDetailBottomSheet", "点击添加子任务按钮")
                            showSubTaskInput = true
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun AnimatedSubTaskList(
    subTasks: List<SubTask>,
    onTaskClick: () -> Unit,
    onCheckChange: (SubTask, Boolean) -> Unit,
    onDelete: (SubTask) -> Unit,
    taskDeleting: String?
) {
    android.util.Log.d("TaskDetailBottomSheet", "AnimatedSubTaskList: 子任务数量=${subTasks.size}")
    
    if (subTasks.isEmpty()) {
        android.util.Log.d("TaskDetailBottomSheet", "AnimatedSubTaskList: 子任务列表为空，返回空UI")
        return
    }

    // 使用key确保列表项在重组时能保持其状态
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
    ) {
        android.util.Log.d("TaskDetailBottomSheet", "AnimatedSubTaskList: 开始渲染${subTasks.size}个子任务")
        
        // 不再缓存子任务列表，确保localSubTasks的更新能立即反映到UI
        val rememberedSubTasks = subTasks
        
        rememberedSubTasks.forEachIndexed { index, subTask ->
            key(subTask.id) { // 使用key包装，确保每个项目能正确追踪
                // 为新加入的子任务添加特殊动画
                val isNewlyAdded = remember { mutableStateOf(true) }
                val backgroundColor = animateColorAsState(
                    targetValue = if (isNewlyAdded.value) 
                                    DustyLavender.copy(alpha = 0.05f) 
                                  else 
                                    Color.Transparent,
                    animationSpec = tween(1000),
                    label = "Background Color Animation"
                )
                
                // 初始动画完成后重置状态
                LaunchedEffect(isNewlyAdded.value) {
                    if (isNewlyAdded.value) {
                        kotlinx.coroutines.delay(1000)
                        isNewlyAdded.value = false
                    }
                }
                
                // 子任务的显示动画
                AnimatedVisibility(
                    visible = taskDeleting != subTask.id,
                    enter = expandVertically(
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    ) + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    SubTaskItem(
                        subTask = subTask,
                        showDeleteButton = taskDeleting == subTask.id,
                        onTaskClick = onTaskClick,
                        onCheckChange = { isChecked -> onCheckChange(subTask, isChecked) },
                        onDelete = { onDelete(subTask) }
                    )
                }
                
                // 最后一个子任务不显示分隔线
                if (index < subTasks.size - 1 && taskDeleting != subTask.id) {
                    Divider(
                        color = Color.LightGray.copy(alpha = 0.2f),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun InlineSubTaskInput(
    onAddSubTask: (SubTask) -> Unit,
    modifier: Modifier = Modifier
) {
    var inputState by remember { mutableStateOf(SubTaskInputState()) }
    var showQuickDatePicker by remember { mutableStateOf(false) }
    var showAddAnimation by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .border(
                width = 1.dp,
                color = if (errorMessage != null) Color.Red.copy(alpha = 0.3f) else Color.LightGray.copy(alpha = 0.3f),
                shape = RoundedCornerShape(12.dp)
            )
            .background(Color.White)
            .padding(12.dp)
    ) {
        // 子任务输入行
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            // 复选框（未选中状态）
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .clip(CircleShape)
                    .border(1.dp, LavenderAsh.copy(alpha = 0.6f), CircleShape)
                    .padding(4.dp)
            )
            
            // 输入框
            BasicTextField(
                value = inputState.text,
                onValueChange = { 
                    inputState = inputState.copy(text = it)
                    if (errorMessage != null && it.isNotBlank()) {
                        errorMessage = null
                    }
                },
                decorationBox = { innerTextField ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 8.dp)
                    ) {
                        if (inputState.text.isEmpty()) {
                            Text(
                                text = "添加新子任务...",
                                color = TextSecondary.copy(alpha = 0.5f),
                                fontSize = 15.sp
                            )
                        }
                        innerTextField()
                    }
                },
                textStyle = TextStyle(
                    color = TextPrimary,
                    fontSize = 15.sp
                ),
                singleLine = true,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 8.dp)
            )
            
            // 添加按钮 - 增加视觉反馈效果
            IconButton(
                onClick = {
                    if (inputState.text.isNotBlank()) {
                        val newSubTask = SubTask(
                            id = UUID.randomUUID().toString(),
                            title = inputState.text.trim(),
                            priority = inputState.priority,
                            reminderTime = inputState.datetime,
                            // 不再使用note字段
                            note = ""
                        )
                        onAddSubTask(newSubTask)
                        showAddAnimation = true
                        
                        // 重置输入框但保留之前选择的优先级和时间设置
                        val currentPriority = inputState.priority
                        val currentDateTime = inputState.datetime
                        inputState = SubTaskInputState(
                            text = "",
                            priority = currentPriority,
                            datetime = currentDateTime
                        )
                        
                        errorMessage = null
                    } else {
                        errorMessage = "请输入任务内容"
                    }
                },
                modifier = Modifier
                    .size(36.dp)
                    .background(
                        color = if (inputState.text.isNotBlank()) DustyLavender else Color.Transparent,
                        shape = CircleShape
                    ),
                enabled = inputState.text.isNotBlank()
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加子任务",
                    tint = if (inputState.text.isNotBlank()) Color.White else TextSecondary.copy(alpha = 0.3f)
                )
            }
        }
        
        // 错误信息显示
        AnimatedVisibility(
            visible = errorMessage != null,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Text(
                text = errorMessage ?: "",
                color = Color.Red,
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 28.dp, top = 4.dp)
            )
        }
        
        // 添加成功的动画效果
        LaunchedEffect(showAddAnimation) {
            if (showAddAnimation) {
                delay(300)
                showAddAnimation = false
            }
        }
        
        // 快捷工具栏 - 只保留优先级和时间选择
        AnimatedVisibility(
            visible = inputState.text.isNotBlank(),
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                    .padding(top = 8.dp, start = 28.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                // 优先级选择
                QuickPrioritySelector(
                    selectedPriority = inputState.priority,
                    onPrioritySelected = { 
                        inputState = inputState.copy(priority = it) 
                    }
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 快速日期选择
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .clickable { showQuickDatePicker = true }
                        .background(
                            if (inputState.datetime != null) 
                                DustyLavender.copy(alpha = 0.1f) 
                            else 
                                Color.Transparent
                        )
                        .border(
                            width = 1.dp,
                            color = DustyLavender.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(16.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "设置时间",
                        tint = if (inputState.datetime != null) DustyLavender else TextSecondary.copy(alpha = 0.6f),
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = inputState.datetime?.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")) ?: "设置时间",
                        color = if (inputState.datetime != null) DustyLavender else TextSecondary.copy(alpha = 0.6f),
                        fontSize = 12.sp
                    )
                }
                
                // 笔记按钮已移除
                
                // 添加清除时间的按钮
                if (inputState.datetime != null) {
                    Spacer(modifier = Modifier.width(12.dp))
                    IconButton(
                        onClick = { inputState = inputState.copy(datetime = null) },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "清除时间",
                            tint = TextSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
            }
        }
    }
    
    // 快速日期选择弹出框
    if (showQuickDatePicker) {
        QuickDateTimePickerDialog(
            onDismiss = { showQuickDatePicker = false },
            onDateTimeSelected = { startDateTime, endDateTime -> 
                // 只使用开始时间作为子任务的提醒时间
                inputState = inputState.copy(datetime = startDateTime)
                showQuickDatePicker = false
            },
            initialDateTime = inputState.datetime
        )
    }
}

@Composable
private fun QuickPrioritySelector(
    selectedPriority: Priority,
    onPrioritySelected: (Priority) -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(getPriorityColor(selectedPriority).copy(alpha = 0.1f))
            .border(
                width = 1.dp,
                color = getPriorityColor(selectedPriority).copy(alpha = 0.3f),
                shape = RoundedCornerShape(16.dp)
            )
            .clickable {
                // 循环切换优先级
                val priorities = listOf(Priority.URGENT, Priority.HIGH, Priority.MEDIUM, Priority.LOW)
                val currentIndex = priorities.indexOf(selectedPriority)
                val nextIndex = (currentIndex + 1) % priorities.size
                onPrioritySelected(priorities[nextIndex])
            }
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(getPriorityColor(selectedPriority))
        )
        Spacer(modifier = Modifier.width(4.dp))
                    Text(
            text = when (selectedPriority) {
                Priority.URGENT -> "紧急"
                Priority.HIGH -> "高"
                Priority.MEDIUM -> "中"
                Priority.LOW -> "低"
            },
            color = getPriorityColor(selectedPriority),
            fontSize = 12.sp
        )
    }
}

@Composable
private fun QuickDateTimePickerDialog(
    onDismiss: () -> Unit,
    onDateTimeSelected: (LocalDateTime, LocalDateTime) -> Unit,
    initialDateTime: LocalDateTime? = null
) {
    val today = LocalDate.now()
    val tomorrow = today.plusDays(1)
    val nextWeek = today.plusDays(7)
    
    // 添加当前时间作为选项
    val now = LocalDateTime.now()
    val roundedNow = LocalDateTime.of(
        now.year, now.month, now.dayOfMonth,
        now.hour, (now.minute / 15) * 15
    )
    
    // 是否显示自定义日期时间选择器
    var showCustomDateTimePicker by remember { mutableStateOf(false) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .width(280.dp)
                .wrapContentHeight()
                .clip(RoundedCornerShape(16.dp)),
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            shadowElevation = 4.dp
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 显示设置时间标题和当前选中时间
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "设置时间",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    if (initialDateTime != null) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .clip(RoundedCornerShape(16.dp))
                                .background(DustyLavender.copy(alpha = 0.1f))
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = initialDateTime.format(DateTimeFormatter.ofPattern("MM/dd HH:mm")),
                                color = DustyLavender,
                                fontSize = 12.sp
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "清除时间",
                                tint = DustyLavender,
                                modifier = Modifier
                                    .size(14.dp)
                                    .clickable { 
                                        // 使用LocalDateTime.MIN来表示清除时间
                                        onDateTimeSelected(LocalDateTime.MIN, LocalDateTime.MIN) 
                                    }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp)) // 从24.dp减少到16.dp
                
                // 当前时间选项
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .clickable { 
                            onDateTimeSelected(roundedNow, roundedNow.plusHours(1)) 
                        },
                    shape = RoundedCornerShape(8.dp),
                    color = DustyLavender.copy(alpha = 0.1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 10.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = null,
                            tint = DustyLavender,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "现在",
                            color = DustyLavender,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        Text(
                            text = "${roundedNow.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${roundedNow.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}",
                            color = DustyLavender,
                            fontSize = 14.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp)) // 从24.dp减少到16.dp
                
                // 快速选项
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 今天
                    QuickTimeOption(
                        label = "今天",
                        times = listOf(
                            Triple("9:00-10:00", 
                                LocalDateTime.of(today, LocalTime.of(9, 0)),
                                LocalDateTime.of(today, LocalTime.of(10, 0))),
                            Triple("12:00-13:00", 
                                LocalDateTime.of(today, LocalTime.of(12, 0)),
                                LocalDateTime.of(today, LocalTime.of(13, 0))),
                            Triple("18:00-19:00", 
                                LocalDateTime.of(today, LocalTime.of(18, 0)),
                                LocalDateTime.of(today, LocalTime.of(19, 0)))
                        ),
                        onTimeSelected = onDateTimeSelected
                    )
                    
                    // 明天
                    QuickTimeOption(
                        label = "明天",
                        times = listOf(
                            Triple("9:00-10:00", 
                                LocalDateTime.of(tomorrow, LocalTime.of(9, 0)),
                                LocalDateTime.of(tomorrow, LocalTime.of(10, 0))),
                            Triple("12:00-13:00", 
                                LocalDateTime.of(tomorrow, LocalTime.of(12, 0)),
                                LocalDateTime.of(tomorrow, LocalTime.of(13, 0))),
                            Triple("18:00-19:00", 
                                LocalDateTime.of(tomorrow, LocalTime.of(18, 0)),
                                LocalDateTime.of(tomorrow, LocalTime.of(19, 0)))
                        ),
                        onTimeSelected = onDateTimeSelected
                    )
                    
                    // 下周
                    QuickTimeOption(
                        label = "下周",
                        times = listOf(
                            Triple("一 9:00-10:00", 
                                LocalDateTime.of(nextWeek, LocalTime.of(9, 0)),
                                LocalDateTime.of(nextWeek, LocalTime.of(10, 0))),
                            Triple("一 12:00-13:00", 
                                LocalDateTime.of(nextWeek, LocalTime.of(12, 0)),
                                LocalDateTime.of(nextWeek, LocalTime.of(13, 0))),
                            Triple("一 18:00-19:00", 
                                LocalDateTime.of(nextWeek, LocalTime.of(18, 0)),
                                LocalDateTime.of(nextWeek, LocalTime.of(19, 0)))
                        ),
                        onTimeSelected = onDateTimeSelected
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 自定义按钮
                Button(
                    onClick = {
                        // 显示自定义日期时间选择器
                        showCustomDateTimePicker = true
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = DustyLavender
                    ),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Text("自定义时间选择")
                }
            }
        }
    }
    
    // 自定义日期时间选择器对话框
    if (showCustomDateTimePicker) {
        DateTimePickerDialog(
            initialDateTime = initialDateTime ?: LocalDateTime.now(),
            onDateTimeSelected = { startDateTime, endDateTime ->
                onDateTimeSelected(startDateTime, endDateTime)
                showCustomDateTimePicker = false
                onDismiss()
            },
            onDismiss = { 
                showCustomDateTimePicker = false 
            }
        )
    }
}

@Composable
private fun QuickTimeOption(
    label: String,
    times: List<Triple<String, LocalDateTime, LocalDateTime>>,
    onTimeSelected: (LocalDateTime, LocalDateTime) -> Unit
) {
    Column {
        Text(
            text = label,
            fontWeight = FontWeight.Medium,
            fontSize = 15.sp,
            color = TextPrimary,
            modifier = Modifier.padding(vertical = 4.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            times.forEach { (timeLabel, startDateTime, endDateTime) ->
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .height(36.dp)
                        .clip(RoundedCornerShape(18.dp))
                        .clickable {
                            onTimeSelected(startDateTime, endDateTime)
                        },
                    shape = RoundedCornerShape(18.dp),
                    color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = timeLabel,
                            fontSize = 12.sp,
                            color = TextSecondary
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DateTimePickerDialog(
    initialDateTime: LocalDateTime,
    onDateTimeSelected: (LocalDateTime, LocalDateTime) -> Unit,
    onDismiss: () -> Unit
) {
    val initialDate = initialDateTime.toLocalDate()
    val initialTime = initialDateTime.toLocalTime()
    
    // 记录当前选中的日期和时间范围
    var selectedDate by remember { mutableStateOf(initialDate) }
    var selectedStartTime by remember { mutableStateOf(initialTime) }
    var selectedEndTime by remember { mutableStateOf(initialTime.plusHours(1)) }
    
    // 选项卡状态
    var selectedTab by remember { mutableStateOf(0) } // 0表示日期, 1表示时间
    
    // 时间范围选择状态
    var isSelectingStartTime by remember { mutableStateOf(true) }
    
    // 移除不需要的DatePickerState，直接使用自定义日历组件
    
    // 分别为开始和结束时间创建独立的状态
    val startTimePickerState = rememberTimePickerState(
        initialHour = selectedStartTime.hour,
        initialMinute = selectedStartTime.minute
    )
    
    val endTimePickerState = rememberTimePickerState(
        initialHour = selectedEndTime.hour,
        initialMinute = selectedEndTime.minute
    )
    
    // 当前使用的时间选择器状态
    val currentTimePickerState = if (isSelectingStartTime) startTimePickerState else endTimePickerState
    
    // 日期状态直接通过自定义日历组件管理
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .width(340.dp)
                .height(600.dp) // 固定高度确保按钮可见
                .clip(RoundedCornerShape(20.dp)),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 4.dp
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 标题栏 - 固定在顶部
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "设置任务时间",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = DustyLavender,
                        letterSpacing = 0.sp
                    )
                    
                    // 关闭按钮
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = TextSecondary,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                // 可滚动内容区域
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp)
                ) {
                
                // 当前已选择的日期时间显示
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = DustyLavender.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(6.dp)
                        )
                            .padding(horizontal = 12.dp, vertical = 8.dp)
                ) {
                    // 日期显示
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = null,
                            tint = DustyLavender,
                                modifier = Modifier.size(14.dp)
                        )
                            Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            color = TextPrimary,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                letterSpacing = 0.sp
                        )
                    }
                    
                        Spacer(modifier = Modifier.height(4.dp))
                    
                    // 时间段显示
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = null,
                            tint = DustyLavender,
                                modifier = Modifier.size(14.dp)
                        )
                            Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "${selectedStartTime.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${selectedEndTime.format(DateTimeFormatter.ofPattern("HH:mm"))}",
                            color = TextPrimary,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                letterSpacing = 0.sp
                        )
                    }
                }
                
                    Spacer(modifier = Modifier.height(12.dp))
                
                // 选项卡切换
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                            .padding(bottom = 8.dp)
                ) {
                    // 日期选项卡
                    TabButton(
                        text = "选择日期",
                        isSelected = selectedTab == 0,
                        onClick = { selectedTab = 0 },
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 时间选项卡
                    TabButton(
                        text = "选择时间",
                        isSelected = selectedTab == 1,
                        onClick = { selectedTab = 1 },
                        modifier = Modifier.weight(1f)
                    )
                }
                
                // 选项卡内容
                when (selectedTab) {
                    // 日期选择器 - 使用自定义组件确保显示周日
                    0 -> {
                        CompactCalendarForTaskTime(
                            selectedDate = selectedDate,
                            onDateSelected = { newDate ->
                                // 直接更新selectedDate状态
                                selectedDate = newDate
                            },
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                    }
                    // 时间选择器
                    1 -> {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                    .padding(bottom = 4.dp) // 减少时间选择器整体底部间距从8dp到4dp
                        ) {
                            // 时间范围选择切换
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                        .padding(bottom = 2.dp), // 进一步减少按钮底部间距从4dp到2dp
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                TimeRangeButton(
                                    text = "开始时间",
                                    isSelected = isSelectingStartTime,
                                    onClick = { isSelectingStartTime = true }
                                )
                                
                                TimeRangeButton(
                                    text = "结束时间",
                                    isSelected = !isSelectingStartTime,
                                    onClick = { isSelectingStartTime = false }
                                )
                            }
                            
                            // 更新开始时间
                            LaunchedEffect(startTimePickerState.hour, startTimePickerState.minute) {
                                val newStartTime = LocalTime.of(startTimePickerState.hour, startTimePickerState.minute)
                                selectedStartTime = newStartTime
                                // 确保结束时间在开始时间之后
                                if (selectedEndTime.isBefore(newStartTime) || selectedEndTime == newStartTime) {
                                    selectedEndTime = newStartTime.plusHours(1)
                                }
                            }
                            
                            // 更新结束时间
                            LaunchedEffect(endTimePickerState.hour, endTimePickerState.minute) {
                                val newEndTime = LocalTime.of(endTimePickerState.hour, endTimePickerState.minute)
                                // 确保结束时间在开始时间之后
                                if (newEndTime.isAfter(selectedStartTime)) {
                                    selectedEndTime = newEndTime
                                }
                            }
                            
                                // 时间选择器UI - 向上移动，减少空白距离
                            Box(
                                modifier = Modifier
                                        .fillMaxWidth()
                                        .offset(y = (-12).dp), // 增加向上偏移从8dp到12dp，进一步减少空白
                                contentAlignment = Alignment.Center
                            ) {
                                TimePicker(
                                    state = currentTimePickerState,
                                        modifier = Modifier
                                            .scale(0.75f) // 缩小表盘大小到75%，保持圆形形状
                                            .padding(0.dp), // 完全移除内边距，最大化利用空间
                                    colors = TimePickerDefaults.colors(
                                        clockDialColor = DustyLavender.copy(alpha = 0.1f),
                                        clockDialSelectedContentColor = Color.White,
                                        clockDialUnselectedContentColor = TextPrimary,
                                        selectorColor = DustyLavender,
                                        containerColor = Color.White,
                                        periodSelectorBorderColor = DustyLavender.copy(alpha = 0.5f),
                                        periodSelectorSelectedContainerColor = DustyLavender,
                                        periodSelectorUnselectedContainerColor = Color.Transparent,
                                        periodSelectorSelectedContentColor = Color.White,
                                        periodSelectorUnselectedContentColor = TextPrimary,
                                        timeSelectorSelectedContainerColor = DustyLavender,
                                        timeSelectorUnselectedContainerColor = Color.Transparent,
                                        timeSelectorSelectedContentColor = Color.White,
                                        timeSelectorUnselectedContentColor = TextPrimary
                                    )
                                )
                                }
                            }
                        }
                    }
                }
                
                // 底部按钮 - 固定在底部，减少上方间距
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp), // 减少垂直间距从12dp到8dp
                    horizontalArrangement = Arrangement.End
                ) {
                    // 取消按钮
                    TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = TextSecondary
                        ),
                        modifier = Modifier.padding(end = 8.dp)
                    ) {
                        Text(
                            "取消",
                            fontSize = 13.sp,
                            letterSpacing = 0.sp
                        )
                    }
                    
                    // 确认按钮
                    TextButton(
                        onClick = {
                            // 创建包含所选日期和时间的LocalDateTime对象
                            val startDateTime = LocalDateTime.of(selectedDate, selectedStartTime)
                            val endDateTime = LocalDateTime.of(selectedDate, selectedEndTime)
                            
                            // 传递开始和结束时间
                            onDateTimeSelected(startDateTime, endDateTime)
                        },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = DustyLavender
                        )
                    ) {
                        Text(
                            "确定",
                            fontSize = 13.sp,
                            letterSpacing = 0.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TimeRangeButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(
                if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (isSelected) DustyLavender else TextSecondary.copy(alpha = 0.3f),
                shape = RoundedCornerShape(16.dp)
            )
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp, vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isSelected) DustyLavender else TextSecondary,
            fontSize = 12.sp,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            letterSpacing = 0.sp
        )
    }
}

/**
 * 紧凑型日历组件 - 专为任务时间设置设计，确保显示周日列
 */
@Composable
private fun CompactCalendarForTaskTime(
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    var currentMonth by remember(selectedDate) { 
        mutableStateOf(selectedDate.withDayOfMonth(1))
    }
    
    val today = LocalDate.now()
    
    Column(modifier = modifier) {
        // 月份导航
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { currentMonth = currentMonth.minusMonths(1) },
                modifier = Modifier.size(28.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ChevronLeft,
                    contentDescription = "上个月",
                    tint = DustyLavender,
                    modifier = Modifier.size(18.dp)
                )
            }
            
            Text(
                text = "${currentMonth.year}年${currentMonth.monthValue}月",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = DustyLavender,
                letterSpacing = 0.sp
            )
            
            IconButton(
                onClick = { currentMonth = currentMonth.plusMonths(1) },
                modifier = Modifier.size(28.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "下个月",
                    tint = DustyLavender,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp))
        
        // 星期标题 - 确保显示周日
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                Text(
                    text = day,
                    fontSize = 11.sp,
                    color = TextSecondary,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    letterSpacing = 0.sp,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 日期网格
        val firstDayOfMonth = currentMonth.withDayOfMonth(1)
        val lastDayOfMonth = currentMonth.withDayOfMonth(currentMonth.lengthOfMonth())
        
        // 计算第一天是星期几（周日=0，周一=1...周六=6）
        val firstDayOfWeek = when (firstDayOfMonth.dayOfWeek.value) {
            7 -> 0 // 周日
            else -> firstDayOfMonth.dayOfWeek.value // 周一到周六
        }
        
        // 计算需要显示的所有日期
        val startDate = firstDayOfMonth.minusDays(firstDayOfWeek.toLong())
        val endDate = lastDayOfMonth.plusDays((6 - when (lastDayOfMonth.dayOfWeek.value) {
            7 -> 0
            else -> lastDayOfMonth.dayOfWeek.value
        }).toLong())
        
        val weeks = mutableListOf<List<LocalDate>>()
        var currentDate = startDate
        
        while (currentDate <= endDate) {
            val week = mutableListOf<LocalDate>()
            repeat(7) {
                week.add(currentDate)
                currentDate = currentDate.plusDays(1)
            }
            weeks.add(week)
        }
        
        weeks.forEach { week ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                week.forEach { date ->
                    val isSelected = date == selectedDate
                    val isToday = date == today
                    val isCurrentMonth = date.month == currentMonth.month
                    val isSelectable = !date.isBefore(today) // 只允许选择今天及以后的日期
                    
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                            .padding(1.dp)
                            .clip(CircleShape)
                            .background(
                                when {
                                    isSelected -> DustyLavender
                                    isToday -> DustyLavender.copy(alpha = 0.2f)
                                    else -> Color.Transparent
                                }
                            )
                            .clickable(enabled = isCurrentMonth && isSelectable) { 
                                onDateSelected(date)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            fontSize = 11.sp,
                            fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                            color = when {
                                isSelected -> Color.White
                                isToday -> DustyLavender
                                isCurrentMonth && isSelectable -> TextPrimary
                                isCurrentMonth && !isSelectable -> TextSecondary.copy(alpha = 0.4f)
                                else -> TextSecondary.copy(alpha = 0.3f)
                            },
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.sp
                        )
                    }
                }
            }
            
            if (week != weeks.last()) {
                Spacer(modifier = Modifier.height(2.dp))
            }
        }
    }
}

@Composable
private fun TabButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .padding(horizontal = 4.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(
                if (isSelected) DustyLavender else Color.Transparent
            )
            .clickable(onClick = onClick)
            .padding(vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isSelected) Color.White else TextSecondary,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            letterSpacing = 0.sp
        )
    }
}

@Composable
private fun EmptySubTaskState(
    onAddClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp)
            .background(Color(0xFFFAFAFA)) // 添加背景色以便于调试
            .padding(vertical = 24.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(LavenderAsh.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = LavenderAsh.copy(alpha = 0.5f),
                    modifier = Modifier.size(28.dp)
                )
            }
            
            Text(
                text = "暂无子任务",
                color = TextSecondary.copy(alpha = 0.7f),
                fontSize = 15.sp
            )
            
            // 添加按钮
            OutlinedButton(
                onClick = onAddClick,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = DustyLavender
                ),
                border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.5f)),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("添加子任务")
                }
            }
        }
    }
}

@Composable
private fun BottomActions(
    isCompleted: Boolean,
    onCompleteClick: () -> Unit,
    onPostponeClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp) // 从horizontal = 16.dp, vertical = 8.dp减少
            .clip(RoundedCornerShape(12.dp)), // 从16.dp减少到12.dp
        color = Color.White,
        shape = RoundedCornerShape(12.dp), // 从16.dp减少到12.dp
        shadowElevation = 0.dp // 禁用Surface自带阴影
    ) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 10.dp), // 从horizontal = 16.dp, vertical = 16.dp减少
            horizontalArrangement = Arrangement.spacedBy(8.dp) // 从12.dp减少到8.dp
    ) {
            // 完成按钮 - 更新UI以反映任务完成状态
        Button(
                onClick = { onCompleteClick() },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.buttonColors(
                    containerColor = if (isCompleted) Color(0xFF194812) else DustyLavender,
                    disabledContainerColor = Color(0xFF194812).copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(8.dp), // 从12.dp减少到8.dp
                contentPadding = PaddingValues(vertical = 8.dp), // 从12.dp减少到8.dp
                enabled = !isCompleted // 任务完成后禁用按钮
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = if (isCompleted) Icons.Default.Check else Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp), // 从18.dp减少到16.dp
                        tint = if (isCompleted) Color(0xFFF5F7F4) else Color.White
                    )
                    Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
                    Text(
                        text = if (isCompleted) "已完成" else "完成",
                        fontSize = 14.sp, // 从16.sp减少到14.sp
                        fontWeight = FontWeight.Medium,
                        color = if (isCompleted) Color(0xFFF5F7F4) else Color.White
                    )
                }
            }
            
            // 延期按钮 - 在任务完成时禁用
        OutlinedButton(
            onClick = { onPostponeClick() },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = if (isCompleted) Color.Gray else DustyLavender
                ),
                border = BorderStroke(
                    width = 1.dp, // 从1.5.dp减少到1.dp
                    color = if (isCompleted) Color.Gray.copy(alpha = 0.5f) else DustyLavender
                ),
                shape = RoundedCornerShape(8.dp), // 从12.dp减少到8.dp
                contentPadding = PaddingValues(vertical = 8.dp), // 从12.dp减少到8.dp
                enabled = !isCompleted // 任务完成后禁用按钮
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp) // 从18.dp减少到16.dp
                    )
                    Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
                    Text(
                        text = "延期",
                        fontSize = 14.sp, // 从16.sp减少到14.sp
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun TagChip(tag: String, modifier: Modifier = Modifier) {
    Surface(
        color = LavenderAsh.copy(alpha = 0.2f),
        shape = MaterialTheme.shapes.small,
        modifier = modifier
    ) {
        Text(
            text = tag,
            fontSize = 10.sp, // 从12.sp减少到10.sp
            color = TextSecondary,
            modifier = Modifier.padding(horizontal = 6.dp, vertical = 3.dp) // 从horizontal = 8.dp, vertical = 4.dp减少
        )
    }
}

@Composable
private fun ColoredTagChip(
    tag: String, 
    color: Color,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        color = color.copy(alpha = 0.2f),
        shape = MaterialTheme.shapes.small,
        modifier = modifier
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 6.dp, vertical = 3.dp) // 从horizontal = 8.dp, vertical = 4.dp减少
        ) {
            Text(
                text = tag,
                fontSize = 10.sp, // 从12.sp减少到10.sp
                color = color.copy(alpha = 0.8f)
            )
            Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "删除标签",
                tint = color.copy(alpha = 0.6f),
                modifier = Modifier
                    .size(12.dp) // 从14.dp减少到12.dp
                    .clickable(onClick = onDelete)
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)
@Composable
private fun TagDisplayArea(tags: List<String>, onRemoveTag: ((String) -> Unit)? = null) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp) // 从8.dp减少到4.dp
    ) {
        if (tags.isNotEmpty()) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(6.dp), // 从10.dp减少到6.dp
                verticalArrangement = Arrangement.spacedBy(4.dp), // 从10.dp减少到4.dp
                maxItemsInEachRow = 4 // 从3增加到4，让更多标签在一行显示
            ) {
                tags.forEach { tag ->
                    if (onRemoveTag != null) {
                        TagChipWithDelete(
                            tag = tag, 
                            onDelete = { onRemoveTag(tag) },
                            modifier = Modifier
                        )
                    } else {
                        SimpleTagChip(
                            tag = tag, 
                            modifier = Modifier
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TagChipWithDelete(
    tag: String,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        color = Color(0xFFF7F3DE), // 保持与SimpleTagChip相同的颜色
        shape = RoundedCornerShape(12.dp), // 从16.dp减少到12.dp
        shadowElevation = 0.dp, // 禁用Surface自带阴影
        modifier = modifier.clip(RoundedCornerShape(12.dp)) // 从16.dp减少到12.dp
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 3.dp) // 从horizontal = 10.dp, vertical = 5.dp减少
        ) {
            Text(
                text = tag,
                fontSize = 12.sp, // 从15.sp减少到12.sp
                color = Color.Black.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
            IconButton(
                onClick = onDelete,
                modifier = Modifier.size(14.dp) // 从16.dp减少到14.dp
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除标签",
                    tint = Color.DarkGray,
                    modifier = Modifier.size(10.dp) // 从12.dp减少到10.dp
                )
            }
        }
    }
}

@Composable
private fun SimpleTagChip(tag: String, modifier: Modifier = Modifier) {
    Surface(
        color = Color(0xFFF7F3DE), // 浅黄色背景，更接近图片中的样式
        shape = RoundedCornerShape(12.dp), // 从16.dp减少到12.dp
        shadowElevation = 0.dp, // 禁用Surface自带阴影
        modifier = modifier.clip(RoundedCornerShape(12.dp)) // 从16.dp减少到12.dp
    ) {
        Text(
            text = tag,
            fontSize = 12.sp, // 从15.sp减少到12.sp
            color = Color.Black.copy(alpha = 0.7f),
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp) // 从horizontal = 14.dp, vertical = 6.dp减少
        )
    }
}

@OptIn(ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)
@Composable
private fun EnhancedTagInputField(
    onTagAdded: (String) -> Unit,
    existingTagOptions: List<String> = listOf("工作", "生活"), // 默认提供常用标签
    onTagsUpdated: (String, List<String>) -> Unit
) {
    var showTagDialog by remember { mutableStateOf(false) }
    var inputText by remember { mutableStateOf("") }
    var recentlyUsedTags by remember { mutableStateOf(listOf("工作", "生活", "学习", "重要", "紧急")) }
    
    // 标签输入字段 - 使用Material 3设计语言
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clip(RoundedCornerShape(16.dp))
            .clickable { showTagDialog = true },
        color = Color(0xFFF9F9F9),
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 0.dp, // 禁用Surface自带阴影
        border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.4f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "添加标签",
                tint = DustyLavender.copy(alpha = 0.8f),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = "添加新标签",
                color = DustyLavender.copy(alpha = 0.8f),
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            // 快速添加标签按钮
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(horizontal = 4.dp)
            ) {
                // 显示3个常用标签快速添加按钮
                items(existingTagOptions.take(3)) { tag ->
                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(12.dp))
                            .background(DustyLavender.copy(alpha = 0.1f))
                            .border(
                                width = 1.dp,
                                color = DustyLavender.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(12.dp)
                            )
                            .clickable { onTagAdded(tag) }
                            .padding(horizontal = 8.dp, vertical = 4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = tag,
                            color = DustyLavender,
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
    
    // 弹出标签添加对话框 - 完全重新设计的标签添加对话框
    if (showTagDialog) {
        Dialog(onDismissRequest = { 
            showTagDialog = false
            inputText = ""
        }) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(24.dp)),
                color = Color.White,
                shape = RoundedCornerShape(24.dp),
                shadowElevation = 4.dp
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                    // 对话框标题
                        Text(
                            text = "添加标签",
                            fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = TextPrimary,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // 搜索/输入框
                    OutlinedTextField(
                        value = inputText,
                        onValueChange = { inputText = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        colors = TextFieldDefaults.outlinedTextFieldColors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = LavenderAsh.copy(alpha = 0.5f),
                            cursorColor = DustyLavender
                        ),
                        singleLine = true,
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = "搜索",
                                tint = TextSecondary.copy(alpha = 0.6f)
                            )
                        },
                        trailingIcon = if (inputText.isNotEmpty()) {
                            {
                                IconButton(onClick = { inputText = "" }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                        contentDescription = "清除",
                                        tint = TextSecondary.copy(alpha = 0.6f)
                                    )
                                }
                            }
                        } else null,
                        placeholder = { Text("输入或搜索标签") },
                        shape = RoundedCornerShape(16.dp)
                    )
                    
                    // 最近使用的标签标题
                    Text(
                        text = "常用标签",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = TextSecondary,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                    
                    // 常用标签列表
                        FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                            maxItemsInEachRow = 3
                        ) {
                        recentlyUsedTags.forEach { tag ->
                            Surface(
                                modifier = Modifier
                                    .clip(RoundedCornerShape(16.dp))
                                    .clickable {
                                        onTagAdded(tag)
                                        showTagDialog = false
                                        inputText = ""
                                    },
                                color = LavenderAsh.copy(alpha = 0.15f),
                                shape = RoundedCornerShape(16.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Label,
                                        contentDescription = null,
                                        tint = DustyLavender.copy(alpha = 0.7f),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Text(
                                        text = tag,
                                        color = TextPrimary,
                                        fontSize = 14.sp
                                    )
                                }
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 创建新标签按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 取消按钮
                        OutlinedButton(
                            onClick = { 
                                showTagDialog = false
                                inputText = ""
                            },
                            border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.5f)),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = TextSecondary
                            ),
                            shape = RoundedCornerShape(16.dp),
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        // 创建按钮
                    Button(
                        onClick = { 
                            if (inputText.isNotBlank()) {
                                    onTagAdded(inputText.trim())
                                inputText = ""
                                showTagDialog = false
                            }
                        },
                            enabled = inputText.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                                containerColor = DustyLavender,
                                disabledContainerColor = DustyLavender.copy(alpha = 0.3f)
                            ),
                            shape = RoundedCornerShape(16.dp),
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("创建标签")
                        }
                    }
                }
            }
        }
    }
}

// 在文件底部添加优先级相关的枚举和工具函数
@Composable
fun getPriorityColor(priority: Priority): Color {
    return when (priority) {
        Priority.HIGH -> UrgencyColors.High
        Priority.MEDIUM -> UrgencyColors.Medium
        Priority.LOW -> UrgencyColors.Low
        Priority.URGENT -> UrgencyColors.Critical
    }
}

@Composable
fun getPriorityText(priority: Priority): String {
    return when (priority) {
        Priority.HIGH -> "高优先级"
        Priority.MEDIUM -> "普通优先级"
        Priority.LOW -> "低优先级"
        Priority.URGENT -> "紧急"
    }
}

/**
 * 子任务条目组件
 */
@Composable
private fun SubTaskItem(
    subTask: SubTask,
    showDeleteButton: Boolean,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDelete: () -> Unit
) {
    // 任务完成状态 - 优先使用本地状态，避免外部更新干扰用户操作
    var isChecked by remember(subTask.id) { 
        mutableStateOf(subTask.isCompleted) 
    }
    
    // 跟踪用户操作状态，避免外部数据覆盖用户操作
    var userJustClicked by remember { mutableStateOf(false) }
    var lastUserOperationTime by remember { mutableStateOf(0L) }
    
    // 只在特定条件下同步外部状态，避免覆盖用户的即时操作
    LaunchedEffect(subTask.id, subTask.isCompleted) {
        // 添加日志帮助调试
        Log.d("SubTaskUpdate", "[SubTaskItem] 子任务${subTask.id}状态检查: UI状态=$isChecked, 数据状态=${subTask.isCompleted}, 用户刚点击=$userJustClicked")
        
        // 如果用户刚刚点击过，完全不允许外部状态覆盖，直到确认数据已同步
        if (userJustClicked) {
            Log.d("SubTaskUpdate", "[SubTaskItem] 用户刚操作过，延迟同步外部状态")
            delay(2000) // 延长等待时间确保数据同步
            
            // 再次检查，只有当外部状态确实与用户操作一致时才清除保护
            if (isChecked == subTask.isCompleted) {
                Log.d("SubTaskUpdate", "[SubTaskItem] 外部状态已同步，解除操作保护")
                userJustClicked = false
            } else {
                Log.d("SubTaskUpdate", "[SubTaskItem] 外部状态仍未同步，继续保护用户操作")
                // 保持用户操作状态，不清除保护标记
                return@LaunchedEffect
            }
        }
        
        // 增强保护：检查时间距离用户最后操作是否足够久
        val timeSinceLastOperation = System.currentTimeMillis() - lastUserOperationTime
        val safeToSync = timeSinceLastOperation > 3000 // 3秒保护期
        
        // 只在没有用户操作保护且时间足够的情况下才允许外部状态同步
        if (isChecked != subTask.isCompleted && !userJustClicked && safeToSync) {
            Log.d("SubTaskUpdate", "[SubTaskItem] 同步外部状态变化: $isChecked -> ${subTask.isCompleted}")
            isChecked = subTask.isCompleted
        } else if (isChecked != subTask.isCompleted && !safeToSync) {
            Log.d("SubTaskUpdate", "[SubTaskItem] 距离用户操作时间过短，保护用户状态 (${timeSinceLastOperation}ms)")
        }
    }
    
    // 复选框动画状态
    val checkboxScale by animateFloatAsState(
        targetValue = if (isChecked) 1.1f else 1.0f,
        animationSpec = tween(durationMillis = 200),
        label = "checkbox_scale"
    )
    
    // 文本透明度和装饰
    val textAlpha = if (isChecked) 0.7f else 1.0f
    val textDecoration = if (isChecked) TextDecoration.LineThrough else TextDecoration.None
    
    // 背景颜色
    val backgroundColor = if (isChecked) {
        BackgroundCompleted.copy(alpha = 0.3f)
    } else {
        BackgroundDefault.copy(alpha = 0.2f)
    }
    
    // 长按状态
    var isLongPressed by remember { mutableStateOf(false) }
    
    // 创建基本布局
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp)) // 从12.dp减少到8.dp
            .background(backgroundColor)
            .clickable(onClick = onTaskClick)
            .padding(vertical = 6.dp, horizontal = 4.dp) // 从vertical = 10.dp, horizontal = 6.dp减少
    ) {
        // 主要内容行
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 增强复选框设计和交互
            Box(
                modifier = Modifier
                    .size(20.dp) // 从24.dp减少到20.dp
                    .scale(checkboxScale) // 应用动画比例
                    .clip(CircleShape)
                    .background(
                        if (isChecked) DustyLavender.copy(alpha = 0.2f) else Color.Transparent
                    )
                    .border(
                        width = 1.dp, // 从1.5.dp减少到1.dp
                        color = if (isChecked) DustyLavender else LavenderAsh.copy(alpha = 0.6f),
                        shape = CircleShape
                    )
                    .clickable { 
                        // 立即更新本地UI状态
                        val newState = !isChecked
                        Log.d("SubTaskUpdate", "[SubTaskItem] 用户点击复选框: ${subTask.id}, $isChecked -> $newState")
                        isChecked = newState
                        userJustClicked = true // 标记用户刚刚操作过
                        lastUserOperationTime = System.currentTimeMillis() // 记录操作时间
                        // 调用回调通知父组件
                        onCheckChange(isChecked) 
                    }
                    .padding(3.dp), // 从4.dp减少到3.dp
                contentAlignment = Alignment.Center
            ) {
                // 在选中状态显示对勾图标
                if (isChecked) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "任务已完成",
                        tint = DustyLavender,
                        modifier = Modifier.size(12.dp) // 从14.dp减少到12.dp
                    )
                }
            }
            
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 6.dp, end = 3.dp) // 从start = 8.dp, end = 4.dp减少
            ) {
                Text(
                    text = subTask.title,
                    color = TextPrimary.copy(alpha = textAlpha),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 13.sp, // 从15.sp减少到13.sp
                    textDecoration = textDecoration,
                    fontWeight = if (!isChecked) FontWeight.Medium else FontWeight.Normal
                )
            }
            
            // 优先级指示点
            Box(
                modifier = Modifier
                    .size(6.dp) // 从8.dp减少到6.dp
                    .clip(CircleShape)
                    .background(getPriorityColor(subTask.priority).copy(
                        alpha = if (isChecked) 0.4f else 0.8f
                    ))
            )
            
            // 使用条件渲染替代AnimatedVisibility
            if (showDeleteButton || isLongPressed) {
                IconButton(
                    onClick = onDelete,
                    modifier = Modifier.size(28.dp) // 从32.dp减少到28.dp
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "删除子任务",
                        tint = TextSecondary.copy(alpha = 0.6f),
                        modifier = Modifier.size(14.dp) // 从16.dp减少到14.dp
                    )
                }
            } else {
                // 只有当删除按钮不可见时显示的点击区域
                Box(
                    modifier = Modifier
                        .size(28.dp) // 从32.dp减少到28.dp
                        .clickable { isLongPressed = true }
                        .padding(6.dp) // 从8.dp减少到6.dp
                ) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "更多选项",
                        tint = TextSecondary.copy(alpha = 0.4f),
                        modifier = Modifier.size(14.dp) // 从16.dp减少到14.dp
                    )
                }
            }
        }
        
        // 如果有提醒时间或笔记，则显示这些信息
        if (subTask.reminderTime != null || subTask.note.isNotBlank()) {
            Spacer(modifier = Modifier.height(2.dp)) // 从4.dp减少到2.dp
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(start = 26.dp) // 从36.dp减少到26.dp与缩小的复选框对齐
            ) {
                if (subTask.reminderTime != null) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = TextSecondary.copy(alpha = if (isChecked) 0.4f else 0.6f),
                        modifier = Modifier.size(12.dp) // 从14.dp减少到12.dp
                    )
                    Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
                    Text(
                        text = subTask.reminderTime.format(
                            DateTimeFormatter.ofPattern("MM月dd日 HH:mm")
                        ),
                        color = TextSecondary.copy(alpha = if (isChecked) 0.4f else 0.6f),
                        fontSize = 10.sp // 从12.sp减少到10.sp
                    )
                    Spacer(modifier = Modifier.width(6.dp)) // 从8.dp减少到6.dp
                }
                
                if (subTask.note.isNotBlank()) {
                    Icon(
                        imageVector = Icons.Default.Notes,
                        contentDescription = null,
                        tint = TextSecondary.copy(alpha = if (isChecked) 0.4f else 0.6f),
                        modifier = Modifier.size(12.dp) // 从14.dp减少到12.dp
                    )
                    Spacer(modifier = Modifier.width(3.dp)) // 从4.dp减少到3.dp
                    Text(
                        text = subTask.note,
                        color = TextSecondary.copy(alpha = if (isChecked) 0.4f else 0.6f),
                        fontSize = 10.sp, // 从12.sp减少到10.sp
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * AI优化任务按钮
 */
@Composable
private fun AIOptimizeTaskButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isPressed by remember { mutableStateOf(false) }
    val buttonColor = DustyLavender
    
    OutlinedButton(
        onClick = {
            isPressed = true
            try {
                // 显示处理中提示
                Toast.makeText(context, "正在连接AI助手...", Toast.LENGTH_SHORT).show()
                
                // 调用导航回调
                onClick()
            } catch (e: Exception) {
                // 错误处理
                Log.e("TaskDetailBottomSheet", "AI优化导航失败", e)
                Toast.makeText(context, "无法打开AI助手: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
            } finally {
                // 恢复按钮状态
                isPressed = false
            }
        },
        modifier = modifier
            .scale(animateFloatAsState(
                targetValue = if (isPressed) 0.97f else 1f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            ).value),
        border = BorderStroke(1.dp, buttonColor),
        shape = RoundedCornerShape(8.dp), // 从12.dp减少到8.dp
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = buttonColor,
            containerColor = buttonColor.copy(alpha = 0.05f)
        ),
        contentPadding = PaddingValues(vertical = 10.dp) // 从16.dp减少到10.dp
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = Icons.Default.SmartToy,
                contentDescription = "AI助手",
                tint = buttonColor,
                modifier = Modifier.size(16.dp) // 从20.dp减少到16.dp
            )
            
            Spacer(modifier = Modifier.width(8.dp)) // 从12.dp减少到8.dp
            
            Text(
                text = "AI优化此任务",
                fontSize = 14.sp, // 从16.sp减少到14.sp
                fontWeight = FontWeight.Medium,
                color = buttonColor
            )
        }
    }
}

/**
 * 获取最新子任务的辅助函数 - 优化版本，添加防抖和并发控制
 */
private suspend fun fetchLatestSubTasks(
    taskId: String,
    viewModel: TaskDetailViewModel,
    coroutineScope: CoroutineScope,
    subTasksFlow: MutableStateFlow<List<SubTask>>
) {
    Log.d("TaskDetailBottomSheet", "开始主动获取最新子任务数据: 任务ID=$taskId")
    
    try {
        // 在 IO 调度器中执行数据库操作，避免阻塞 UI 线程
        withContext(Dispatchers.IO) {
            // 短暂延迟，确保数据库写入完成
            delay(100)
            
            // 先尝试从缓存获取，如果有效则直接使用
            val cachedSubTasks = viewModel.getCachedSubTasks(taskId)
            if (cachedSubTasks.isNotEmpty()) {
                Log.d("TaskDetailBottomSheet", "从缓存获取到${cachedSubTasks.size}个子任务")
                withContext(Dispatchers.Main) {
                    subTasksFlow.value = cachedSubTasks
                }
                return@withContext
            }
            
            // 缓存无效时才从数据库获取
            val latestSubTasks = viewModel.getSubTasksDirectFromDatabase(taskId)
            
            // 更新StateFlow
            withContext(Dispatchers.Main) {
                if (latestSubTasks.isNotEmpty()) {
                    Log.d("TaskDetailBottomSheet", "成功直接从数据库获取${latestSubTasks.size}个子任务，更新StateFlow")
                    subTasksFlow.value = latestSubTasks
                } else {
                    Log.d("TaskDetailBottomSheet", "数据库中暂无子任务数据")
                }
            }
        }
    } catch (e: Exception) {
        Log.e("TaskDetailBottomSheet", "获取子任务时出错", e)
    }
}

/**
 * 循环设置对话框
 */
@Composable
private fun RecurrenceSettingsDialog(
    isRecurring: Boolean,
    recurrenceSettings: RecurrenceSettings,
    selectedRecurrenceType: RecurrenceType,
    onDismiss: () -> Unit,
    onConfirm: (Boolean, RecurrenceSettings, RecurrenceType) -> Unit
) {
    var tempIsRecurring by remember { mutableStateOf(isRecurring) }
    var tempRecurrenceType by remember { mutableStateOf(selectedRecurrenceType) }
    var tempSettings by remember { mutableStateOf(recurrenceSettings) }

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "循环设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 是否启用循环
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "启用循环",
                        fontSize = 16.sp,
                        color = TextPrimary
                    )
                    Switch(
                        checked = tempIsRecurring,
                        onCheckedChange = { tempIsRecurring = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = DustyLavender,
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = LavenderAsh
                        )
                    )
                }

                // 循环类型选择
                if (tempIsRecurring) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "循环类型",
                        fontSize = 14.sp,
                        color = TextSecondary,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // 循环类型选项
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        RecurrenceType.values().filter { it != RecurrenceType.NONE }.forEach { type ->
                            RecurrenceTypeItem(
                                type = type,
                                isSelected = tempRecurrenceType == type,
                                onClick = {
                                    tempRecurrenceType = type
                                    tempSettings = when (type) {
                                        RecurrenceType.DAILY -> RecurrenceSettings.daily()
                                        RecurrenceType.WEEKLY -> RecurrenceSettings.weekly()
                                        RecurrenceType.MONTHLY -> RecurrenceSettings.monthly()
                                        RecurrenceType.YEARLY -> RecurrenceSettings.yearly()
                                        else -> RecurrenceSettings.none()
                                    }
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = TextSecondary
                        ),
                        border = BorderStroke(1.dp, LavenderAsh)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            val finalType = if (tempIsRecurring) tempRecurrenceType else RecurrenceType.NONE
                            val finalSettings = if (tempIsRecurring) tempSettings else RecurrenceSettings.none()
                            onConfirm(tempIsRecurring, finalSettings, finalType)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = DustyLavender
                        )
                    ) {
                        Text("确定", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 循环类型选项组件
 */
@Composable
private fun RecurrenceTypeItem(
    type: RecurrenceType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent,
        border = BorderStroke(
            1.dp,
            if (isSelected) DustyLavender else LavenderAsh.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = DustyLavender,
                    unselectedColor = LavenderAsh
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = type.displayName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                Text(
                    text = type.description,
                    fontSize = 12.sp,
                    color = TextSecondary
                )
            }
        }
    }
}

/**
 * 延期选项枚举
 */
enum class PostponeOption(val displayName: String, val description: String) {
    ONE_HOUR("1小时后", "延期1小时"),
    THREE_HOURS("3小时后", "延期3小时"),
    TOMORROW("明天", "延期到明天同一时间"),
    NEXT_WEEK("下周", "延期到下周同一时间"),
    CUSTOM("自定义", "选择具体时间")
}

/**
 * 延期选择对话框
 */
@Composable
private fun PostponeDialog(
    currentTask: TaskData,
    onDismiss: () -> Unit,
    onPostpone: (PostponeOption) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "延期任务",
                fontWeight = FontWeight.Bold,
                color = TextPrimary
            )
        },
        text = {
            Column {
                Text(
                    text = "选择延期时间：",
                    color = TextSecondary,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 延期选项列表
                PostponeOption.values().forEach { option ->
                    PostponeOptionItem(
                        option = option,
                        onClick = { onPostpone(option) }
                    )

                    if (option != PostponeOption.values().last()) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "取消",
                    color = TextSecondary
                )
            }
        },
        containerColor = Color.White,
        shape = RoundedCornerShape(16.dp)
    )
}

/**
 * 延期选项项目组件
 */
@Composable
private fun PostponeOptionItem(
    option: PostponeOption,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = Color.Transparent,
        border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.3f)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (option) {
                    PostponeOption.ONE_HOUR, PostponeOption.THREE_HOURS -> Icons.Default.Schedule
                    PostponeOption.TOMORROW -> Icons.Default.Today
                    PostponeOption.NEXT_WEEK -> Icons.Default.DateRange
                    PostponeOption.CUSTOM -> Icons.Default.Edit
                },
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column {
                Text(
                    text = option.displayName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                Text(
                    text = option.description,
                    fontSize = 12.sp,
                    color = TextSecondary
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}
