package com.timeflow.app.data.converter

import com.timeflow.app.data.entity.Task as EntityTask
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.data.model.TaskType
import androidx.compose.ui.graphics.Color
import timber.log.Timber
import java.time.LocalDateTime

/**
 * 任务转换器 - 在实体和模型之间进行转换
 */
object TaskConverter {

    /**
     * 将Task实体转换为模型
     * @param entity 任务实体
     * @return 任务模型
     */
    fun toModelTask(entity: EntityTask): ModelTask {
        return try {
            // 🔧 修复完成状态判断：使用多重判断确保数据一致性
            val isCompleted = when {
                // 优先级1：completedAt字段不为空则认为已完成
                entity.completedAt != null -> true
                // 优先级2：status字段为"已完成"则认为已完成
                entity.status == "已完成" -> true
                // 其他情况均认为未完成
                else -> false
            }
            
            // 如果存在状态不一致，记录警告日志
            if ((entity.completedAt != null) != isCompleted) {
                Timber.w("发现任务实体 ${entity.id} 完成状态不一致: completedAt=${entity.completedAt}, status=${entity.status} -> 最终结果=$isCompleted")
            }
            
            ModelTask(
                id = entity.id,
                title = entity.title,
                description = entity.description,
                dueDate = entity.dueDate,
                startDate = entity.startDate,
                createdAt = entity.createdAt,
                updatedAt = entity.updatedAt,
                completedAt = entity.completedAt,
                isCompleted = isCompleted,
                priority = priorityFromInt(entity.priority),
                type = TaskType.NORMAL,
                parentTaskId = entity.parentTaskId,
                hasSubtasks = entity.hasSubtasks,
                childTasksCount = null,
                completedChildTasksCount = null,
                depth = entity.depth,
                orderIndex = entity.orderIndex,
                groupId = null,
                groupType = null,
                columnId = entity.columnId ?: "",
                tags = entity.tagIds.map { tagName -> 
                    TaskTag(name = tagName, color = Color.Gray)
                },
                estimatedTimeMinutes = entity.estimatedTime ?: 0,
                actualTimeMinutes = entity.actualTime ?: 0,
                progress = entity.progress,
                aiGenerated = false,
                emotionState = null,
                status = entity.status,
                reminderTime = entity.reminderTime,
                goalId = entity.goalId,
                dateManuallyModified = entity.dateManuallyModified,
                isFloatingTask = entity.isFloatingTask,
                floatingWeekStart = entity.floatingWeekStart,
                floatingWeekEnd = entity.floatingWeekEnd,
                scheduledDate = entity.scheduledDate,
                floatingTaskOrder = entity.floatingTaskOrder
            )
        } catch (e: Exception) {
            Timber.e(e, "转换任务实体到模型失败: ${entity.id}")
            throw e
        }
    }

    /**
     * 将Task模型转换为实体
     * @param model 任务模型
     * @return 任务实体
     */
    fun toEntityTask(model: ModelTask): EntityTask {
        return try {
            EntityTask(
                id = model.id,
                title = model.title,
                description = model.description,
                dueDate = model.dueDate,
                startDate = model.startDate,
                priority = priorityToInt(model.priority),
                createdAt = model.createdAt,
                updatedAt = LocalDateTime.now(),
                completedAt = model.completedAt,
                parentTaskId = model.parentTaskId,
                depth = model.depth ?: 0,
                status = model.status,
                hasSubtasks = model.hasSubtasks,
                progress = model.progress,
                orderIndex = model.orderIndex,
                projectId = null,
                parentId = null,
                reminderTime = model.reminderTime,
                isRecurring = false,
                recurringPattern = null,
                attachmentUrls = emptyList(),
                tagIds = model.tags.map { it.name },
                assignedTo = null,
                estimatedTime = if (model.estimatedTimeMinutes > 0) model.estimatedTimeMinutes else null,
                actualTime = if (model.actualTimeMinutes > 0) model.actualTimeMinutes else null,
                sortOrder = 0,
                isStarred = false,
                childTasksCount = 0,
                completedChildTasksCount = 0,
                columnId = model.columnId?.takeIf { it.isNotEmpty() },
                aiGenerated = model.aiGenerated,
                goalId = model.goalId,
                dateManuallyModified = model.dateManuallyModified,
                isFloatingTask = model.isFloatingTask,
                floatingWeekStart = model.floatingWeekStart,
                floatingWeekEnd = model.floatingWeekEnd,
                scheduledDate = model.scheduledDate,
                floatingTaskOrder = model.floatingTaskOrder
            )
        } catch (e: Exception) {
            Timber.e(e, "转换任务模型到实体失败: ${model.id}")
            throw e
        }
    }

    /**
     * 将Task实体列表转换为模型列表
     * @param entities 任务实体列表
     * @return 任务模型列表
     */
    fun toModelTasks(entities: List<EntityTask>): List<ModelTask> {
        return entities.map { toModelTask(it) }
    }

    /**
     * 将Task模型列表转换为实体列表
     * @param models 任务模型列表
     * @return 任务实体列表
     */
    fun toEntityTasks(models: List<ModelTask>): List<EntityTask> {
        return models.map { toEntityTask(it) }
    }

    /**
     * 优先级整数转枚举
     */
    private fun priorityFromInt(priority: Int): Priority? {
        return when (priority) {
            0 -> Priority.URGENT
            1 -> Priority.HIGH
            2 -> Priority.MEDIUM
            3 -> Priority.LOW
            else -> null
        }
    }

    /**
     * 优先级枚举转整数
     */
    private fun priorityToInt(priority: Priority?): Int {
        return priority?.ordinal ?: 1 // 默认为HIGH(1)
    }
} 