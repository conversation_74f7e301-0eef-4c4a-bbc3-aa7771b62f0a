package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.CycleDao
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.entity.SymptomRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Inject

/**
 * 生理周期数据仓库
 */
class CycleRepository @Inject constructor(
    private val cycleDao: CycleDao
) {
    // 周期记录相关方法
    fun getCycles(): Flow<List<CycleRecord>> {
        return cycleDao.getAllCycles()
    }
    
    suspend fun getCycleById(id: Long): CycleRecord? {
        return cycleDao.getCycleById(id)
    }
    
    suspend fun getCycleByDate(date: LocalDate): CycleRecord? {
        return cycleDao.getCycleByDate(date)
    }
    
    suspend fun getLatestCycle(): CycleRecord? {
        return cycleDao.getLatestCycle()
    }
    
    suspend fun insertCycle(cycle: CycleRecord): Long {
        return cycleDao.insertCycle(cycle)
    }
    
    suspend fun updateCycle(cycle: CycleRecord) {
        cycleDao.updateCycle(cycle)
    }
    
    /**
     * 删除指定的周期记录
     */
    suspend fun deleteCycle(cycle: CycleRecord) {
        cycleDao.deleteCycle(cycle)
    }
    
    // 自动计算周期长度并更新
    suspend fun endCurrentCycle(endDate: LocalDate) {
        val latestCycle = cycleDao.getLatestCycle() ?: return
        if (latestCycle.endDate != null) return // 已经结束
        
        // 计算经期长度
        val periodLength = ChronoUnit.DAYS.between(latestCycle.startDate, endDate).toInt() + 1
        
        // 更新周期记录
        cycleDao.updateCycle(latestCycle.copy(
            endDate = endDate,
            periodLength = periodLength
        ))
    }
    
    // 计算两次经期之间的周期长度
    suspend fun calculateCycleLength(cycleId: Long) {
        val currentCycle = cycleDao.getCycleById(cycleId) ?: return
        val cycles = cycleDao.getAllCycles().first() // 使用first()挂起函数获取Flow中的值
        
        // 找到下一个周期
        val nextCycle = cycles.find { cycle -> cycle.startDate > currentCycle.startDate }
            ?: return // 没有下一个周期
            
        // 计算周期长度
        val cycleLength = ChronoUnit.DAYS.between(currentCycle.startDate, nextCycle.startDate).toInt()
        
        // 更新记录
        cycleDao.updateCycle(currentCycle.copy(cycleLength = cycleLength))
    }
    
    // 症状记录相关方法
    fun getSymptomsByDate(date: LocalDate): Flow<List<SymptomRecord>> {
        return cycleDao.getSymptomsByDate(date)
    }
    
    fun getAllSymptoms(): Flow<List<SymptomRecord>> {
        return cycleDao.getAllSymptoms()
    }
    
    suspend fun insertSymptom(symptom: SymptomRecord): Long {
        return cycleDao.insertSymptom(symptom)
    }
    
    suspend fun updateSymptom(symptom: SymptomRecord) {
        cycleDao.updateSymptom(symptom)
    }
    
    suspend fun deleteSymptom(symptom: SymptomRecord) {
        cycleDao.deleteSymptom(symptom)
    }
    
    suspend fun deleteSymptomsByDate(date: LocalDate) {
        cycleDao.deleteSymptomsByDate(date)
    }
    
    /**
     * 检查是否存在周期记录
     * @return 如果存在记录返回true，否则返回false
     */
    suspend fun hasCycleRecords(): Boolean {
        // 获取所有周期记录
        val cycles = cycleDao.getAllCycles().first()
        // 检查列表是否为空
        return cycles.isNotEmpty()
    }
} 