package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.DateTimeConverter
import java.time.LocalDateTime

/**
 * 目标模板实体类
 */
@Entity(tableName = "goal_templates")
@TypeConverters(DateTimeConverter::class)
data class GoalTemplate(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String = "",
    val category: String = "", // 保留旧字段以兼容
    val categoryId: String = "personal_development", // 新的分类ID字段
    val iconName: String = "target",
    val colorHex: String = "#9370DB",
    val usageCount: Int = 0,
    val lastUsed: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val defaultTitle: String = "",
    val defaultDescription: String = "",
    val defaultPriority: String = "MEDIUM",
    val defaultTags: String = "", // 逗号分隔的标签列表
    val defaultDurationDays: Int? = null,
    val goalType: String = "BOOLEAN",
    val defaultTargetValue: Double? = null,
    val defaultUnit: String? = null,
    val isRecurring: Boolean = false,
    val recurringSettingsJson: String = "{}", // JSON格式的RecurringSettings
    val reminderSettingsJson: String = "[]" // JSON格式的ReminderSettings列表
)

/**
 * 目标子任务模板实体类
 */
@Entity(
    tableName = "goal_subtask_templates",
    foreignKeys = [
        ForeignKey(
            entity = GoalTemplate::class,
            parentColumns = ["id"],
            childColumns = ["templateId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("templateId")]
)
data class GoalSubTaskTemplate(
    @PrimaryKey
    val id: String,
    val templateId: String, // 外键，关联到goal_templates表
    val title: String,
    val description: String = "",
    val estimatedDurationDays: Int = 0,
    val orderIndex: Int = 0
) 