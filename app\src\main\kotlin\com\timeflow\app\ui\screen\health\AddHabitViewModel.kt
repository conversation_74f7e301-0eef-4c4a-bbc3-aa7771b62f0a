package com.timeflow.app.ui.screen.health

import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.*
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.repository.GoalRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalTime
import javax.inject.Inject

/**
 * 添加习惯页面的ViewModel
 */
@HiltViewModel
class AddHabitViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val goalRepository: GoalRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(AddHabitUiState())
    val uiState: StateFlow<AddHabitUiState> = _uiState.asStateFlow()
    
    // 目标列表
    private val _goals = MutableStateFlow<List<Goal>>(emptyList())
    val goals: StateFlow<List<Goal>> = _goals.asStateFlow()
    
    /**
     * 加载目标列表
     */
    fun loadGoals() {
        viewModelScope.launch {
            try {
                goalRepository.getAllGoals().collect { goalList ->
                    _goals.value = goalList
                }
            } catch (e: Exception) {
                // 处理错误
            }
        }
    }
    
    /**
     * 更新习惯名称
     */
    fun updateName(name: String) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(name = name)
        )
    }
    
    /**
     * 更新描述
     */
    fun updateDescription(description: String) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(description = description)
        )
    }
    
    /**
     * 更新类别
     */
    fun updateCategory(category: HabitCategory) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(
                category = category,
                icon = category.icon,
                color = category.color
            )
        )
    }
    
    /**
     * 更新图标
     */
    fun updateIcon(icon: String) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(icon = icon)
        )
    }
    
    /**
     * 更新颜色
     */
    fun updateColor(color: Color) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(color = color)
        )
    }
    
    /**
     * 更新频率类型
     */
    fun updateFrequencyType(frequencyType: HabitFrequencyType) {
        val selectedDays = when (frequencyType) {
            HabitFrequencyType.DAILY -> DayOfWeek.values().toSet()
            HabitFrequencyType.WEEKLY -> setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY)
            HabitFrequencyType.CUSTOM -> emptySet()
        }
        
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(
                frequencyType = frequencyType,
                selectedDays = selectedDays
            )
        )
    }
    
    /**
     * 更新选中的天数
     */
    fun updateSelectedDays(days: Set<DayOfWeek>) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(selectedDays = days)
        )
    }
    
    /**
     * 更新目标次数
     */
    fun updateTargetCount(count: Int) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(targetCount = count)
        )
    }
    
    /**
     * 切换提醒开关
     */
    fun toggleReminder(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(enableReminder = enabled)
        )
    }
    
    /**
     * 更新提醒时间
     */
    fun updateReminderTime(time: LocalTime) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(reminderTime = time)
        )
    }
    
    /**
     * 更新固定时间
     */
    fun updateFixedTime(time: LocalTime?) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(fixedTime = time)
        )
    }
    
    /**
     * 更新难度
     */
    fun updateDifficulty(difficulty: HabitDifficulty) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(difficulty = difficulty)
        )
    }
    
    /**
     * 更新关联目标
     */
    fun updateRelatedGoal(goalId: String?, goalTitle: String?) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(
                relatedGoalId = goalId,
                relatedGoalTitle = goalTitle
            )
        )
    }
    
    /**
     * 更新备注
     */
    fun updateNotes(notes: String) {
        _uiState.value = _uiState.value.copy(
            formState = _uiState.value.formState.copy(notes = notes)
        )
    }
    
    /**
     * 保存习惯
     */
    fun saveHabit() {
        val formState = _uiState.value.formState
        
        if (formState.name.isBlank()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "习惯名称不能为空"
            )
            return
        }
        
        if (formState.frequencyType != HabitFrequencyType.DAILY && formState.selectedDays.isEmpty()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "请选择至少一天"
            )
            return
        }
        
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
        
        viewModelScope.launch {
            try {
                val habit = HabitModel(
                    name = formState.name,
                    description = formState.description,
                    icon = formState.icon,
                    color = formState.color,
                    category = formState.category,
                    frequencyType = formState.frequencyType,
                    frequencyDays = formState.selectedDays.toList(),
                    targetCount = formState.targetCount,
                    reminderEnabled = formState.enableReminder,
                    reminderTime = if (formState.enableReminder) formState.reminderTime else null,
                    fixedTime = formState.fixedTime,
                    difficulty = formState.difficulty,
                    relatedGoalId = formState.relatedGoalId,
                    relatedGoalTitle = formState.relatedGoalTitle,
                    notes = formState.notes
                )
                
                val success = habitRepository.addHabit(habit)
                
                if (success) {
                    // 如果启用了提醒，设置提醒
                    if (formState.enableReminder && formState.reminderTime != null) {
                        habitRepository.scheduleHabitReminder(
                            habit.id,
                            formState.reminderTime.toString()
                        )
                    }
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isSaved = true
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "保存失败，请重试"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "保存失败：${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
}

/**
 * 添加习惯页面的UI状态
 */
data class AddHabitUiState(
    val formState: HabitFormState = HabitFormState(),
    val isLoading: Boolean = false,
    val isSaved: Boolean = false,
    val errorMessage: String? = null
) 