package com.timeflow.app.ui.screen.analytics

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt
import com.timeflow.app.ui.theme.AnalyticsThemeData
import com.timeflow.app.ui.theme.LocalAnalyticsTheme

/**
 * 活动数据类
 */
data class ActivityData(
    val category: String,
    val percentage: Float,
    val duration: Long, // 毫秒
    val pomodoroCount: Float,
    val formattedDuration: String
)

/**
 * 格式化时长（秒）为可读格式
 */
fun formatDuration(seconds: Long): String {
    val hours = seconds / 3600
    val minutes = (seconds % 3600) / 60
    return when {
        hours > 0 -> "${hours}时${minutes}分"
        minutes > 0 -> "${minutes}分钟"
        else -> "${seconds}秒"
    }
}

/**
 * 时间范围选择器组件
 */
@Composable
fun ChipGroup(
    items: List<String>,
    selectedItem: String,
    onItemSelected: (String) -> Unit,
    theme: AnalyticsThemeData
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(theme.dimensions.spacingMedium)
    ) {
        items(items) { item ->
            val isSelected = item == selectedItem
            
            ElevatedFilterChip(
                selected = isSelected,
                onClick = { onItemSelected(item) },
                label = { 
                    Text(
                        text = item, 
                        style = theme.typography.labelLarge
                    ) 
                },
                enabled = true,
                colors = FilterChipDefaults.elevatedFilterChipColors(
                    containerColor = theme.colors.monet.surface,
                    selectedContainerColor = theme.colors.monet.primary,
                    labelColor = if (isSelected) theme.colors.monet.surface else theme.colors.text.secondary,
                    selectedLabelColor = theme.colors.monet.surface
                ),
                elevation = FilterChipDefaults.elevatedFilterChipElevation(
                    elevation = theme.dimensions.cardElevation
                )
            )
        }
    }
}

/**
 * 概览卡片组件
 */
@Composable
fun OverviewCard(
    selectedTimeRange: String,
    efficiencyData: List<Pair<String, Float>>,
    goalStatistics: GoalStatistics?,
    habitStatistics: HabitStatistics?,
    timeDistributionData: TimeDistributionData?,
    theme: AnalyticsThemeData,
    dailyInsights: List<String> = emptyList()
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = theme.dimensions.cardElevation)
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            // 标题区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when(selectedTimeRange) {
                        "日" -> Icons.Default.Today
                        "周" -> Icons.Default.DateRange
                        "月" -> Icons.Default.CalendarViewMonth
                        "年" -> Icons.Default.CalendarToday
                        else -> Icons.Default.Today
                    },
                    contentDescription = null,
                    tint = theme.colors.monet.primary,
                    modifier = Modifier.size(theme.dimensions.iconSizeMedium)
                )
                
                Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                
                Text(
                    text = when(selectedTimeRange) {
                        "日" -> "回顾今天以改善明天"
                        "周" -> "本周表现总结"
                        "月" -> "本月成果概览"
                        "年" -> "年度回顾与展望"
                        else -> "回顾今天以改善明天"
                    },
                    style = theme.typography.titleLarge,
                    color = theme.colors.text.primary
                )
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 根据时间范围显示不同内容
            when(selectedTimeRange) {
                "日" -> DayOverview(goalStatistics, habitStatistics, timeDistributionData, theme)
                "周" -> WeekOverview(efficiencyData, goalStatistics, habitStatistics, theme)
                "月" -> MonthOverview(goalStatistics, habitStatistics, timeDistributionData, theme)
                "年" -> YearOverview(goalStatistics, habitStatistics, timeDistributionData, theme)
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))

            // 洞察卡片
            if (dailyInsights.isNotEmpty()) {
                InsightCard(
                    title = "今日洞察",
                    insights = dailyInsights,
                    theme = theme
                )

                Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
            }

            // 底部视图切换按钮
            ViewSwitchButtons(theme)
        }
    }
}

/**
 * 日概览内容
 */
@Composable
private fun DayOverview(
    goalStatistics: GoalStatistics?,
    habitStatistics: HabitStatistics?,
    timeDistributionData: TimeDistributionData?,
    theme: AnalyticsThemeData
) {
    // 日期显示
    Text(
        text = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年M月d日")),
        style = theme.typography.bodyMedium,
        color = theme.colors.text.tertiary,
        modifier = Modifier.padding(bottom = theme.dimensions.spacingMedium)
    )
    
    // 关键数据卡片
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(theme.dimensions.spacingSmall)
    ) {
        DataHighlightCard(
            icon = Icons.Default.CheckCircle,
            label = "完成目标",
            value = "${goalStatistics?.completedGoals ?: 0}个",
            modifier = Modifier.weight(1f),
            color = theme.colors.functional.success,
            theme = theme
        )
        
        DataHighlightCard(
            icon = Icons.Default.Timer,
            label = "专注时长",
            value = formatDuration(timeDistributionData?.todayTotal ?: 0L),
            modifier = Modifier.weight(1f),
            color = theme.colors.monet.primary,
            theme = theme
        )
        
        DataHighlightCard(
            icon = Icons.Default.AvTimer,
            label = "习惯完成",
            value = "${habitStatistics?.activeHabits ?: 0}个",
            modifier = Modifier.weight(1f),
            color = theme.colors.functional.warning,
            theme = theme
        )
    }
}


/**
 * 周概览内容
 */
@Composable
private fun WeekOverview(
    efficiencyData: List<Pair<String, Float>>,
    goalStatistics: GoalStatistics?,
    habitStatistics: HabitStatistics?,
    theme: AnalyticsThemeData
) {
    // 周统计摘要
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        StatSummaryText("完成${goalStatistics?.completedGoals ?: 0}个目标", theme)
        StatSeparator(theme)
        StatSummaryText("习惯坚持${habitStatistics?.averageStreak?.toInt() ?: 0}天", theme)
        StatSeparator(theme)
        StatSummaryText("目标完成率${goalStatistics?.completionRate?.toInt() ?: 0}%", theme.colors.functional.success)
    }
    
    Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
    
    // 效率趋势图
    Column {
        Text(
            text = "每日效率趋势",
            style = theme.typography.titleMedium,
            color = theme.colors.text.primary,
            modifier = Modifier.padding(bottom = theme.dimensions.spacingMedium)
        )
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(160.dp)
        ) {
            // 使用真实效率数据的柱状图
            val chartData = efficiencyData
            BarChart(
                data = chartData,
                primaryColor = theme.colors.monet.primary,
                secondaryColor = theme.colors.monet.secondary
            )
        }
    }
    
    Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
    
    // 周洞察 - 暂时隐藏，等待真实数据
    // TODO: 添加周度洞察参数
}

/**
 * 月概览内容
 */
@Composable
private fun MonthOverview(
    goalStatistics: GoalStatistics?,
    habitStatistics: HabitStatistics?,
    timeDistributionData: TimeDistributionData?,
    theme: AnalyticsThemeData
) {
    // 月度统计
    Column(verticalArrangement = Arrangement.spacedBy(theme.dimensions.spacingSmall)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            StatSummaryText("完成任务数: 215", theme)
            StatSeparator(theme)
            StatSummaryText("专注时长: 120小时", theme)
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            StatSummaryText("目标完成率: 85%", theme)
            StatSeparator(theme)
            StatSummaryText("生产力提升: 12%", theme.colors.functional.success)
        }
    }
    
    Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
    
    // 月度进度条
    MonthProgressIndicator(theme)
    
    Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
    
    // 行动建议 - 暂时隐藏，等待真实数据
    // TODO: 添加行动建议参数
}

/**
 * 年概览内容
 */
@Composable
private fun YearOverview(
    goalStatistics: GoalStatistics?,
    habitStatistics: HabitStatistics?,
    timeDistributionData: TimeDistributionData?,
    theme: AnalyticsThemeData
) {
    Column {
        Text(
            text = "年度成就",
            style = theme.typography.titleMedium,
            color = theme.colors.text.primary
        )
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingSmall))
        
        // 年度成就列表
        Column(verticalArrangement = Arrangement.spacedBy(theme.dimensions.spacingSmall)) {
            AchievementItem("专注总时长: 1,245小时", theme.colors.monet.primary, theme)
            AchievementItem("完成项目: 32个", theme.colors.morandi.dustyRose, theme)
            AchievementItem("学习技能: 8项", theme.colors.morandi.softLavender, theme)
        }
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
        
        // 季度表现
        QuarterPerformance(theme)
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
        
        // 年度成就卡片 - 暂时隐藏，等待真实数据
        // TODO: 添加年度成就参数
    }
}

/**
 * 活动分布卡片
 */
@Composable
fun ActivityDistributionCard(
    selectedTimeRange: String,
    timeDistribution: Map<String, Float>,
    viewModel: AnalyticsViewModel,
    theme: AnalyticsThemeData,
    realActivityData: Map<String, ActivityData> = emptyMap()
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = theme.dimensions.cardElevation)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(theme.dimensions.cardPadding),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 图表标题
            Text(
                text = when(selectedTimeRange) {
                    "日" -> "今日活动分布"
                    "周" -> "本周活动分布"
                    "月" -> "本月活动分布"
                    "年" -> "年度活动分布"
                    else -> "活动分布"
                },
                style = theme.typography.titleLarge,
                color = theme.colors.text.primary,
                modifier = Modifier.align(Alignment.Start)
            )
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 从ViewModel获取数据
            val timeDistributionData = viewModel.timeDistribution.collectAsState().value
            
            // 环形图
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                if (timeDistributionData.isNotEmpty()) {
                    DonutChart(
                        data = timeDistributionData,
                        colors = theme.colors.chart.palette,
                        theme = theme
                    )
                    
                    // 中心显示总时间
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        val totalHours = calculateTotalHours(selectedTimeRange, timeDistributionData)
                        Text(
                            text = totalHours.first,
                            style = theme.typography.titleLarge,
                            color = theme.colors.text.primary
                        )
                        if (totalHours.second.isNotEmpty()) {
                            Text(
                                text = totalHours.second,
                                style = theme.typography.bodyMedium,
                                color = theme.colors.text.tertiary
                            )
                        }
                    }
                } else {
                    // 无数据时的占位符
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.Analytics,
                                contentDescription = null,
                                modifier = Modifier.size(48.dp),
                                tint = theme.colors.text.tertiary
                            )
                            Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
                            Text(
                                text = "暂无数据",
                                style = theme.typography.bodyMedium,
                                color = theme.colors.text.tertiary
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingXLarge))
            
            // 活动详情
            if (timeDistributionData.isNotEmpty()) {
                ActivityDetailsList(
                    selectedTimeRange = selectedTimeRange,
                    timeDistributionData = timeDistributionData,
                    viewModel = viewModel,
                    theme = theme,
                    realActivityData = realActivityData
                )
            }
        }
    }
}

/**
 * 生产力得分卡片
 */
@Composable
fun ProductivityScoreCard(
    productivityScore: Int,
    completionRate: Float,
    interruptionCount: Int,
    comparisonData: String,
    theme: AnalyticsThemeData
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = theme.dimensions.cardElevation)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(theme.dimensions.cardPadding),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "生产力得分",
                style = theme.typography.titleMedium,
                color = theme.colors.text.secondary
            )
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 环形进度指示器
            Box(
                modifier = Modifier.size(100.dp),
                contentAlignment = Alignment.Center
            ) {
                ProductivityCircularIndicator(
                    score = productivityScore,
                    theme = theme
                )
                
                // 中心文字
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "$productivityScore",
                        style = theme.typography.headlineLarge,
                        color = theme.colors.text.primary
                    )
                    Text(
                        text = getProductivityLevel(productivityScore),
                        style = theme.typography.bodySmall,
                        color = theme.colors.text.tertiary
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 统计项
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    label = "完成率",
                    value = "${completionRate.toInt()}%",
                    icon = Icons.Default.CheckCircle,
                    color = theme.colors.functional.success
                )
                
                StatItem(
                    label = "中断次数",
                    value = "$interruptionCount",
                    icon = Icons.Default.Pause,
                    color = theme.colors.functional.warning
                )
                
                StatItem(
                    label = "同比",
                    value = comparisonData,
                    icon = Icons.Default.TrendingUp,
                    color = theme.colors.functional.info
                )
            }
        }
    }
}

/**
 * 高效时段分析卡片
 */
@Composable
fun EfficiencyAnalysisCard(theme: AnalyticsThemeData) {
    Text(
        text = "高效时段分析",
        style = theme.typography.titleLarge,
        color = theme.colors.text.primary,
        modifier = Modifier.padding(vertical = theme.dimensions.spacingMedium)
    )
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            Text(
                text = "一天中最高效的时段",
                style = theme.typography.titleMedium,
                color = theme.colors.text.primary
            )
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 时段效率图
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(theme.dimensions.chartHeight)
            ) {
                DayTimeEfficiencyChart()
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                BestTimeItem(
                    title = "最高效时段",
                    time = "14:00-16:00",
                    description = "专注度最高"
                )
                
                BestTimeItem(
                    title = "创意高峰",
                    time = "9:00-11:00",
                    description = "灵感最多"
                )
            }
        }
    }
}

/**
 * 专注质量分析卡片
 */
@Composable
fun FocusQualityCard(theme: AnalyticsThemeData) {
    Text(
        text = "专注质量分析",
        style = theme.typography.titleLarge,
        color = theme.colors.text.primary,
        modifier = Modifier.padding(vertical = theme.dimensions.spacingMedium)
    )
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                FocusQualityItem(
                    title = "专注指数",
                    value = "92",
                    description = "高于上周15%",
                    icon = Icons.Default.CheckCircle,
                    color = theme.colors.functional.success
                )
                
                FocusQualityItem(
                    title = "平均专注时长",
                    value = "32分钟",
                    description = "每次专注",
                    icon = Icons.Default.Timer,
                    color = theme.colors.monet.primary
                )
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                FocusQualityItem(
                    title = "完成番茄数",
                    value = "13.6",
                    description = "共计25个",
                    icon = Icons.Default.Schedule,
                    color = theme.colors.functional.warning
                )
                
                FocusQualityItem(
                    title = "总中断次数",
                    value = "8",
                    description = "少于上周3次",
                    icon = Icons.Default.NotificationsOff,
                    color = theme.colors.functional.error
                )
            }
        }
    }
}

/**
 * AI智能复盘卡片
 */
@Composable
fun AIReviewCard(
    navController: NavController,
    theme: AnalyticsThemeData,
    dailyInsights: List<String> = emptyList()
) {
    Text(
        text = "AI智能复盘",
        style = theme.typography.titleLarge,
        color = theme.colors.text.primary,
        modifier = Modifier.padding(vertical = theme.dimensions.spacingMedium)
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { navController.navigate("ai_review") },
        shape = RoundedCornerShape(theme.dimensions.cardRadius),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = theme.dimensions.cardElevation)
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            // 顶部标题与图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    theme.colors.chart.gradientStart,
                                    theme.colors.chart.gradientEnd
                                )
                            ),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = null,
                        tint = theme.colors.monet.surface,
                        modifier = Modifier.size(theme.dimensions.iconSizeMedium)
                    )
                }
                
                Spacer(modifier = Modifier.width(theme.dimensions.spacingLarge))
                
                Column {
                    Text(
                        text = "AI智能复盘助手",
                        style = theme.typography.titleLarge,
                        color = theme.colors.text.primary
                    )
                    
                    Text(
                        text = "让数据驱动你的成长",
                        style = theme.typography.bodyMedium,
                        color = theme.colors.text.secondary
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 复盘模型说明
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ReviewModelChip("日", "KISS", theme.colors.functional.success)
                ReviewModelChip("周", "PDCA", theme.colors.functional.info)
                ReviewModelChip("月", "GRAI", theme.colors.functional.warning)
                ReviewModelChip("年", "KPT", theme.colors.morandi.softLavender)
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 最近复盘洞察
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = theme.colors.monet.surfaceVariant,
                        shape = RoundedCornerShape(theme.dimensions.spacingMedium)
                    )
                    .padding(theme.dimensions.cardPadding)
            ) {
                Text(
                    text = "今日复盘洞察",
                    style = theme.typography.titleMedium,
                    color = theme.colors.text.primary
                )
                
                Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
                
                // 使用真实洞察数据或显示占位文本
                val insights = if (dailyInsights.isNotEmpty()) dailyInsights else listOf("暂无洞察数据")
                insights.forEach { insight ->
                    Text(
                        text = "· $insight",
                        style = theme.typography.bodySmall,
                        color = theme.colors.text.secondary
                    )
                    
                    Spacer(modifier = Modifier.height(theme.dimensions.spacingXSmall))
                }
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingLarge))
            
            // 开始复盘按钮
            Button(
                onClick = { navController.navigate("ai_review") },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(theme.dimensions.spacingMedium),
                colors = ButtonDefaults.buttonColors(
                    containerColor = theme.colors.monet.primary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.InsertChart,
                    contentDescription = null,
                    modifier = Modifier.size(theme.dimensions.iconSizeSmall)
                )
                
                Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                
                Text(
                    text = "开始智能复盘",
                    style = theme.typography.titleMedium
                )
            }
        }
    }
}

/**
 * 数据高亮卡片
 */
@Composable
fun DataHighlightCard(
    icon: ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    color: Color,
    theme: AnalyticsThemeData
) {
    Card(
        modifier = modifier.height(80.dp),
        shape = RoundedCornerShape(theme.dimensions.spacingMedium),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(theme.dimensions.spacingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(theme.dimensions.iconSizeMedium)
            )
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingXSmall))
            
            Text(
                text = value,
                style = theme.typography.titleMedium,
                color = theme.colors.text.primary,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
            
            Text(
                text = label,
                style = theme.typography.labelSmall,
                color = theme.colors.text.tertiary,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/**
 * 洞察卡片
 */
@Composable
fun InsightCard(
    title: String,
    insights: List<String>,
    theme: AnalyticsThemeData
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.spacingMedium),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.monet.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            Text(
                text = title,
                style = theme.typography.titleMedium,
                color = theme.colors.text.primary
            )
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
            
            insights.forEach { insight ->
                Row(
                    modifier = Modifier.padding(vertical = theme.dimensions.spacingXSmall),
                    verticalAlignment = Alignment.Top
                ) {
                    Box(
                        modifier = Modifier
                            .size(4.dp)
                            .padding(top = theme.dimensions.spacingSmall)
                            .background(theme.colors.monet.primary, CircleShape)
                    )
                    
                    Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                    
                    Text(
                        text = insight,
                        style = theme.typography.bodySmall,
                        color = theme.colors.text.secondary,
                        lineHeight = theme.typography.bodySmall.lineHeight
                    )
                }
            }
        }
    }
}

/**
 * 统计摘要文本
 */
@Composable
fun StatSummaryText(text: String, theme: AnalyticsThemeData) {
    Text(
        text = text,
        style = theme.typography.bodyMedium,
        color = theme.colors.text.secondary
    )
}

@Composable
fun StatSummaryText(text: String, color: Color) {
    Text(
        text = text,
        style = LocalAnalyticsTheme.current.typography.bodyMedium,
        color = color
    )
}

/**
 * 统计分隔符
 */
@Composable
fun StatSeparator(theme: AnalyticsThemeData) {
    Text(
        text = " • ",
        style = theme.typography.bodyMedium,
        color = theme.colors.text.disabled
    )
}

/**
 * 月度进度指示器
 */
@Composable
fun MonthProgressIndicator(theme: AnalyticsThemeData) {
    Column {
        Text(
            text = "月度目标进度",
            style = theme.typography.titleMedium,
            color = theme.colors.text.primary,
            modifier = Modifier.padding(bottom = theme.dimensions.spacingMedium)
        )
        
        LinearProgressIndicator(
            progress = 0.85f,
            modifier = Modifier
                .fillMaxWidth()
                .height(10.dp)
                .clip(RoundedCornerShape(5.dp)),
            color = theme.colors.monet.primary,
            trackColor = theme.colors.monet.primary.copy(alpha = 0.2f)
        )
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "已完成",
                style = theme.typography.labelMedium,
                color = theme.colors.text.tertiary
            )
            
            Text(
                text = "85%",
                style = theme.typography.labelMedium.copy(fontWeight = androidx.compose.ui.text.font.FontWeight.Medium),
                color = theme.colors.monet.primary
            )
        }
    }
}

/**
 * 行动建议卡片
 */
@Composable
fun ActionSuggestionsCard(
    suggestions: List<String>,
    theme: AnalyticsThemeData
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.spacingMedium),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.functional.success.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = theme.colors.functional.success,
                    modifier = Modifier.size(theme.dimensions.iconSizeSmall)
                )
                
                Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                
                Text(
                    text = "行动建议",
                    style = theme.typography.titleMedium,
                    color = theme.colors.functional.success
                )
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
            
            suggestions.forEach { suggestion ->
                Row(
                    modifier = Modifier.padding(vertical = theme.dimensions.spacingXSmall),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowRight,
                        contentDescription = null,
                        tint = theme.colors.functional.success,
                        modifier = Modifier.size(theme.dimensions.iconSizeSmall)
                    )
                    
                    Spacer(modifier = Modifier.width(theme.dimensions.spacingXSmall))
                    
                    Text(
                        text = suggestion,
                        style = theme.typography.bodySmall,
                        color = theme.colors.text.secondary,
                        lineHeight = theme.typography.bodySmall.lineHeight
                    )
                }
            }
        }
    }
}

/**
 * 成就项
 */
@Composable
fun AchievementItem(text: String, color: Color, theme: AnalyticsThemeData) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(theme.dimensions.spacingMedium)
                .background(color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
        
        Text(
            text = text,
            style = theme.typography.bodyMedium,
            color = theme.colors.text.secondary
        )
    }
}

/**
 * 季度表现
 */
@Composable
fun QuarterPerformance(theme: AnalyticsThemeData) {
    Column {
        Text(
            text = "季度表现",
            style = theme.typography.titleMedium,
            color = theme.colors.text.primary,
            modifier = Modifier.padding(bottom = theme.dimensions.spacingMedium)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            QuarterBox("Q1", 75, theme.colors.functional.warning, theme)
            QuarterBox("Q2", 82, theme.colors.functional.info, theme)
            QuarterBox("Q3", 78, theme.colors.functional.error, theme)
            QuarterBox("Q4", 88, theme.colors.functional.success, theme)
        }
    }
}

/**
 * 季度盒子
 */
@Composable
fun QuarterBox(
    title: String,
    score: Int,
    color: Color,
    theme: AnalyticsThemeData
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .padding(theme.dimensions.spacingXSmall)
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(theme.dimensions.spacingMedium)
            )
            .padding(horizontal = theme.dimensions.spacingMedium, vertical = theme.dimensions.cardPadding)
            .width(50.dp)
    ) {
        Text(
            text = title,
            style = theme.typography.titleMedium,
            color = color
        )
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
        
        Text(
            text = "$score",
            style = theme.typography.headlineMedium,
            color = color
        )
        
        Spacer(modifier = Modifier.height(theme.dimensions.spacingXSmall))
        
        Text(
            text = "分",
            style = theme.typography.labelSmall,
            color = color.copy(alpha = 0.8f)
        )
    }
}

/**
 * 成就卡片
 */
@Composable
fun AchievementCard(
    achievements: List<String>,
    theme: AnalyticsThemeData
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(theme.dimensions.spacingMedium),
        colors = CardDefaults.cardColors(
            containerColor = theme.colors.functional.info.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(theme.dimensions.cardPadding)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.EmojiEvents,
                    contentDescription = null,
                    tint = theme.colors.functional.info,
                    modifier = Modifier.size(theme.dimensions.iconSizeSmall)
                )
                
                Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                
                Text(
                    text = "年度成就",
                    style = theme.typography.titleMedium,
                    color = theme.colors.functional.info
                )
            }
            
            Spacer(modifier = Modifier.height(theme.dimensions.spacingMedium))
            
            achievements.forEach { achievement ->
                Row(
                    modifier = Modifier.padding(vertical = theme.dimensions.spacingXSmall),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = theme.colors.functional.warning,
                        modifier = Modifier.size(theme.dimensions.iconSizeSmall)
                    )
                    
                    Spacer(modifier = Modifier.width(theme.dimensions.spacingXSmall))
                    
                    Text(
                        text = achievement,
                        style = theme.typography.bodySmall,
                        color = theme.colors.text.secondary,
                        lineHeight = theme.typography.bodySmall.lineHeight
                    )
                }
            }
        }
    }
}

/**
 * 视图切换按钮
 */
@Composable
fun ViewSwitchButtons(theme: AnalyticsThemeData) {
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        ViewSwitchButton(
            icon = Icons.Default.List,
            text = "具象",
            theme = theme,
            modifier = Modifier.weight(1f)
        )
        
        Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
        
        ViewSwitchButton(
            icon = Icons.Default.Insights,
            text = "宏观",
            theme = theme,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun ViewSwitchButton(
    icon: ImageVector,
    text: String,
    theme: AnalyticsThemeData,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(theme.dimensions.spacingSmall))
            .clickable { /* 点击切换视图 */ }
            .background(
                color = theme.colors.monet.surfaceVariant,
                shape = RoundedCornerShape(theme.dimensions.spacingSmall)
            )
            .padding(vertical = theme.dimensions.spacingLarge),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = theme.colors.text.secondary,
                modifier = Modifier.size(theme.dimensions.iconSizeSmall)
            )
            Spacer(modifier = Modifier.width(theme.dimensions.spacingXSmall))
            Text(
                text = text,
                style = theme.typography.bodyMedium,
                color = theme.colors.text.secondary
            )
        }
    }
}

/**
 * 活动详情列表
 */
@Composable
fun ActivityDetailsList(
    selectedTimeRange: String,
    timeDistributionData: Map<String, Float>,
    viewModel: AnalyticsViewModel,
    theme: AnalyticsThemeData,
    realActivityData: Map<String, ActivityData> = emptyMap()
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        when (selectedTimeRange) {
            "日" -> {
                val completedTasksData = viewModel.completedTasks.collectAsState().value
                completedTasksData.forEachIndexed { index, taskData ->
                    val percentage = timeDistributionData[taskData.title] ?: 0f
                    ActivityDetailItem(
                        name = taskData.title,
                        percentage = "${percentage.roundToInt()}%",
                        time = formatMinutesToHoursAndMinutes(taskData.duration.toInt()),
                        pomos = "${taskData.pomos}",
                        color = theme.colors.chart.palette[index % theme.colors.chart.palette.size],
                        theme = theme
                    )
                }
            }
            "周" -> {
                Column {
                    if (realActivityData.isNotEmpty()) {
                        realActivityData.entries.take(3).forEachIndexed { index, entry ->
                            val (category, data) = entry
                            ActivityDetailItem(
                                name = category,
                                percentage = "${String.format("%.1f", data.percentage)}%",
                                time = data.formattedDuration,
                                pomos = String.format("%.1f", data.pomodoroCount),
                                color = theme.colors.chart.palette[index % theme.colors.chart.palette.size],
                                theme = theme
                            )
                        }
                    } else {
                        Text(
                            text = "暂无周度活动数据",
                            style = theme.typography.bodyMedium,
                            color = theme.colors.text.secondary,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
            "月" -> {
                Column {
                    if (realActivityData.isNotEmpty()) {
                        realActivityData.entries.take(3).forEachIndexed { index, entry ->
                            val (category, data) = entry
                            ActivityDetailItem(
                                name = category,
                                percentage = "${String.format("%.1f", data.percentage)}%",
                                time = data.formattedDuration,
                                pomos = String.format("%.1f", data.pomodoroCount),
                                color = theme.colors.chart.palette[index % theme.colors.chart.palette.size],
                                theme = theme
                            )
                        }
                    } else {
                        Text(
                            text = "暂无月度活动数据",
                            style = theme.typography.bodyMedium,
                            color = theme.colors.text.secondary,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
            else -> {
                Column {
                    if (realActivityData.isNotEmpty()) {
                        realActivityData.entries.take(3).forEachIndexed { index, entry ->
                            val (category, data) = entry
                            ActivityDetailItem(
                                name = category,
                                percentage = "${String.format("%.1f", data.percentage)}%",
                                time = data.formattedDuration,
                                pomos = String.format("%.1f", data.pomodoroCount),
                                color = theme.colors.chart.palette[index % theme.colors.chart.palette.size],
                                theme = theme
                            )
                        }
                    } else {
                        Text(
                            text = "暂无年度活动数据",
                            style = theme.typography.bodyMedium,
                            color = theme.colors.text.secondary,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 活动详情项
 */
@Composable
fun ActivityDetailItem(
    name: String,
    percentage: String,
    time: String,
    pomos: String,
    color: Color,
    theme: AnalyticsThemeData
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = theme.dimensions.spacingXSmall),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 颜色标记
        Box(
            modifier = Modifier
                .size(theme.dimensions.spacingLarge)
                .background(color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(theme.dimensions.spacingLarge))
        
        // 活动名称
        Text(
            text = name,
            style = theme.typography.bodyMedium,
            color = theme.colors.text.primary,
            modifier = Modifier.weight(1f)
        )
        
        // 百分比
        Text(
            text = percentage,
            style = theme.typography.bodyMedium,
            color = theme.colors.text.tertiary,
            modifier = Modifier.width(50.dp)
        )
        
        // 时间
        Text(
            text = time,
            style = theme.typography.bodyMedium,
            color = theme.colors.text.tertiary,
            modifier = Modifier.width(80.dp),
            textAlign = TextAlign.End
        )
        
        // Pomos
        Text(
            text = "$pomos Pomos",
            style = theme.typography.bodyMedium,
            color = theme.colors.text.tertiary,
            modifier = Modifier.width(70.dp),
            textAlign = TextAlign.End
        )
    }
}

/**
 * 图表组件
 */
@Composable
fun DonutChart(
    data: Map<String, Float>,
    colors: List<Color>,
    theme: AnalyticsThemeData
) {
    val total = data.values.sum()
    
    Canvas(
        modifier = Modifier.size(160.dp)
    ) {
        val innerRadius = size.minDimension * 0.3f
        val outerRadius = size.minDimension * 0.5f
        val center = Offset(size.width / 2, size.height / 2)
        
        var startAngle = 0f
        
        data.entries.forEachIndexed { index, entry ->
            val sweepAngle = 360f * (entry.value / total)
            val color = colors[index % colors.size]
            
            val padding = 1f
            drawArc(
                color = color,
                startAngle = startAngle + padding / 2,
                sweepAngle = sweepAngle - padding,
                useCenter = false,
                topLeft = Offset(
                    center.x - outerRadius,
                    center.y - outerRadius
                ),
                size = Size(outerRadius * 2, outerRadius * 2),
                style = Stroke(width = (outerRadius - innerRadius), cap = StrokeCap.Round)
            )
            
            startAngle += sweepAngle
        }
    }
}

/**
 * 生产力环形指示器
 */
@Composable
fun ProductivityCircularIndicator(
    score: Int,
    theme: AnalyticsThemeData
) {
    Canvas(modifier = Modifier.size(100.dp)) {
        val strokeWidth = 8.dp.toPx()
        val radius = (size.minDimension - strokeWidth) / 2
        val center = Offset(size.width / 2, size.height / 2)
        
        // 背景圆环
        drawCircle(
            color = theme.colors.monet.primary.copy(alpha = 0.2f),
            radius = radius,
            center = center,
            style = Stroke(width = strokeWidth)
        )
        
        // 进度圆环
        val sweepAngle = 360f * (score / 100f)
        drawArc(
            color = theme.colors.monet.primary,
            startAngle = -90f,
            sweepAngle = sweepAngle,
            useCenter = false,
            topLeft = Offset(center.x - radius, center.y - radius),
            size = Size(radius * 2, radius * 2),
            style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
        )
    }
}

/**
 * 最佳时间项数据类
 */
data class BestTimeItem(
    val period: String,
    val timeRange: String,
    val efficiency: Int,
    val color: Color
)

/**
 * 最佳时间行
 */
@Composable
fun BestTimeRow(
    timeItem: BestTimeItem,
    theme: AnalyticsThemeData
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = timeItem.period,
                style = theme.typography.bodyMedium,
                color = theme.colors.text.primary
            )
            Text(
                text = timeItem.timeRange,
                style = theme.typography.bodySmall,
                color = theme.colors.text.tertiary
            )
        }
        
        Text(
            text = "${timeItem.efficiency}%",
            style = theme.typography.titleMedium.copy(fontWeight = androidx.compose.ui.text.font.FontWeight.Medium),
            color = timeItem.color
        )
    }
}

/**
 * 专注质量项数据类
 */
data class FocusQualityItem(
    val type: String,
    val percentage: Int,
    val color: Color
)

/**
 * 专注质量行
 */
@Composable
fun FocusQualityRow(
    quality: FocusQualityItem,
    theme: AnalyticsThemeData
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(quality.color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
        
        Text(
            text = quality.type,
            style = theme.typography.bodyMedium,
            color = theme.colors.text.primary,
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = "${quality.percentage}%",
            style = theme.typography.bodyMedium,
            color = theme.colors.text.tertiary
        )
    }
}

/**
 * 计算总时间显示
 */
private fun calculateTotalHours(selectedTimeRange: String, data: Map<String, Float>): Pair<String, String> {
    // 根据时间范围计算基础时间
    val baseHours = when(selectedTimeRange) {
        "日" -> 5
        "周" -> 28  
        "月" -> 120
        "年" -> 1245
        else -> 8
    }
    
    val totalMinutes = (baseHours * 60 * (data.values.sum() / 100f)).toInt()
    val hours = totalMinutes / 60
    val minutes = totalMinutes % 60
    
    return when(selectedTimeRange) {
        "日" -> "${hours}小时" to if (minutes > 0) "${minutes}分钟" else ""
        "周" -> "${hours}小时" to if (minutes > 0) "${minutes}分钟" else ""
        "月" -> "${hours}小时" to if (minutes > 0) "${minutes}分钟" else ""
        "年" -> "${hours}小时" to ""
        else -> "${hours}小时" to ""
    }
}

/**
 * 活动详情列表
 */
@Composable
private fun ActivityDetailsList(
    selectedTimeRange: String,
    timeDistributionData: Map<String, Float>,
    theme: AnalyticsThemeData
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(theme.dimensions.spacingMedium)
    ) {
        Text(
            text = "活动详情",
            style = theme.typography.titleMedium,
            color = theme.colors.text.primary
        )
        
        timeDistributionData.entries.sortedByDescending { it.value }.forEach { (category, percentage) ->
            val baseTime = when(selectedTimeRange) {
                "日" -> 5 * 60 // 5小时转分钟
                "周" -> 28 * 60
                "月" -> 120 * 60
                "年" -> 1245 * 60
                else -> 8 * 60
            }
            
            val categoryMinutes = (baseTime * percentage / 100).toInt()
            val timeString = formatMinutesToHoursAndMinutes(categoryMinutes)
            val pomosCount = (categoryMinutes / 25).coerceAtLeast(1)
            
            ActivityDetailItem(
                name = category,
                percentage = "${percentage.toInt()}%",
                time = timeString,
                pomos = pomosCount.toString(),
                color = getCategoryColor(category, theme),
                theme = theme
            )
        }
    }
}

/**
 * 获取类别颜色
 */
private fun getCategoryColor(category: String, theme: AnalyticsThemeData): Color {
    return when(category) {
        "工作" -> theme.colors.chart.palette[0]
        "学习" -> theme.colors.chart.palette[1]
        "娱乐" -> theme.colors.chart.palette[2]
        "健康" -> theme.colors.chart.palette[3]
        "生活" -> theme.colors.chart.palette[4]
        "其他" -> theme.colors.chart.palette[5]
        else -> theme.colors.chart.palette[0]
    }
}

/**
 * 获取生产力等级
 */
private fun getProductivityLevel(score: Int): String {
    return when {
        score >= 90 -> "卓越"
        score >= 80 -> "优秀"
        score >= 70 -> "良好"
        score >= 60 -> "及格"
        score >= 40 -> "待改进"
        else -> "需努力"
    }
}

/**
 * 格式化分钟为小时和分钟
 */
fun formatMinutesToHoursAndMinutes(minutes: Int): String {
    val hours = minutes / 60
    val mins = minutes % 60
    
    return if (hours > 0) {
        "${hours}小时${if (mins > 0) "${mins}分钟" else ""}"
    } else {
        "${mins}分钟"
    }
} 