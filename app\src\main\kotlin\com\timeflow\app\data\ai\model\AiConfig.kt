package com.timeflow.app.data.ai.model

import java.util.UUID

/**
 * AI模型配置数据类
 */
data class AiConfig(
    val id: String = UUID.randomUUID().toString(),
    val name: String = "",  // 配置名称
    val provider: String = "DeepSeek", // 服务商名称
    val apiKey: String = "", // API密钥
    val modelName: String = "", // 具体模型名称
    val serverUrl: String = "", // 服务器地址
    val maxTokens: Int = 2048, // 最大token数
    val temperature: Float = 0.7f, // 创造性参数
    val isDefault: Boolean = false, // 是否为默认配置
    val systemPrompt: String = "" // 系统提示词
) 