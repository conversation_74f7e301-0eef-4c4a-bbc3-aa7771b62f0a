package com.timeflow.app.ui.screen.goal.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.timeflow.app.data.model.*
import com.timeflow.app.ui.theme.DustyLavender
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

/**
 * 提醒设置面板
 * 支持添加、编辑、删除各种类型的提醒
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReminderSettingsPanel(
    reminderSettings: List<ReminderSetting>,
    onReminderSettingsChanged: (List<ReminderSetting>) -> Unit,
    modifier: Modifier = Modifier
) {
    var showAddReminderDialog by remember { mutableStateOf(false) }
    var editingReminder by remember { mutableStateOf<ReminderSetting?>(null) }
    
    // 显示添加提醒对话框
    if (showAddReminderDialog) {
        ReminderTypeSelectionDialog(
            onDismiss = { showAddReminderDialog = false },
            onReminderTypeSelected = { type ->
                // 创建新的提醒设置并添加到列表
                val newReminder = ReminderSetting.createDefault(type)
                onReminderSettingsChanged(reminderSettings + newReminder)
                showAddReminderDialog = false
                editingReminder = newReminder // 自动进入编辑模式
            }
        )
    }
    
    // 显示编辑提醒对话框
    editingReminder?.let { reminder ->
        ReminderEditDialog(
            reminder = reminder,
            onDismiss = { editingReminder = null },
            onSave = { updatedReminder ->
                val updatedSettings = reminderSettings.map { 
                    if (it.type == reminder.type) updatedReminder else it 
                }
                onReminderSettingsChanged(updatedSettings)
                editingReminder = null
            },
            onDelete = {
                val updatedSettings = reminderSettings.filter { it.type != reminder.type }
                onReminderSettingsChanged(updatedSettings)
                editingReminder = null
            }
        )
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "提醒设置",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333)
            )
            
            // 添加按钮
            IconButton(
                onClick = { showAddReminderDialog = true },
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(DustyLavender.copy(alpha = 0.1f))
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加提醒",
                    tint = DustyLavender
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 提醒列表
        if (reminderSettings.isEmpty()) {
            // 空状态
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color(0xFFF8FAFC))
                    .clickable { showAddReminderDialog = true },
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Notifications,
                        contentDescription = null,
                        tint = Color(0xFF888888),
                        modifier = Modifier.size(32.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "点击添加提醒",
                        fontSize = 14.sp,
                        color = Color(0xFF888888)
                    )
                }
            }
        } else {
            // 提醒列表
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                reminderSettings.forEach { reminder ->
                    ReminderItem(
                        reminder = reminder,
                        onClick = { editingReminder = reminder }
                    )
                }
            }
        }
    }
}

/**
 * 提醒类型选择对话框
 */
@Composable
private fun ReminderTypeSelectionDialog(
    onDismiss: () -> Unit,
    onReminderTypeSelected: (ReminderType) -> Unit
) {
    val reminderTypes = listOf(
        Pair(ReminderType.DAILY, "每日提醒"),
        Pair(ReminderType.WEEKLY, "每周提醒"),
        Pair(ReminderType.MONTHLY, "每月提醒"),
        Pair(ReminderType.BEFORE_DUE, "截止日期前提醒"),
        Pair(ReminderType.PROGRESS, "进度提醒"),
        Pair(ReminderType.COMPLETION, "完成提醒"),
        Pair(ReminderType.CUSTOM, "自定义提醒")
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                text = "选择提醒类型",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            ) 
        },
        text = {
            LazyColumn {
                items(reminderTypes) { (type, text) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onReminderTypeSelected(type) }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = getReminderTypeIcon(type),
                            contentDescription = null,
                            tint = DustyLavender,
                            modifier = Modifier.size(24.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Text(
                            text = text,
                            fontSize = 16.sp,
                            color = Color(0xFF333333)
                        )
                    }
                    
                    if (reminderTypes.indexOf(Pair(type, text)) < reminderTypes.size - 1) {
                        Divider(
                            color = Color(0xFFEEEEEE),
                            thickness = 1.dp
                        )
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        containerColor = Color.White,
        tonalElevation = 0.dp
    )
}

/**
 * 提醒编辑对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ReminderEditDialog(
    reminder: ReminderSetting,
    onDismiss: () -> Unit,
    onSave: (ReminderSetting) -> Unit,
    onDelete: () -> Unit
) {
    var updatedReminder by remember { mutableStateOf(reminder) }
    var showTimePicker by remember { mutableStateOf(false) }
    var showDatePicker by remember { mutableStateOf(false) }
    
    // 时间选择器
    if (showTimePicker) {
        val timePickerState = rememberTimePickerState(
            initialHour = updatedReminder.time?.hour ?: 9,
            initialMinute = updatedReminder.time?.minute ?: 0
        )
        
        AlertDialog(
            onDismissRequest = { showTimePicker = false },
            title = { Text("选择提醒时间") },
            text = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    TimePicker(
                        state = timePickerState,
                        colors = TimePickerDefaults.colors(
                            timeSelectorSelectedContainerColor = DustyLavender,
                            timeSelectorSelectedContentColor = Color.White,
                            timeSelectorUnselectedContainerColor = DustyLavender.copy(alpha = 0.1f),
                            timeSelectorUnselectedContentColor = DustyLavender,
                            periodSelectorSelectedContainerColor = DustyLavender,
                            periodSelectorSelectedContentColor = Color.White,
                            periodSelectorUnselectedContainerColor = DustyLavender.copy(alpha = 0.1f),
                            periodSelectorUnselectedContentColor = DustyLavender
                        )
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        val selectedTime = LocalTime.of(
                            timePickerState.hour,
                            timePickerState.minute
                        )
                        updatedReminder = updatedReminder.copy(time = selectedTime)
                        showTimePicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showTimePicker = false }) {
                    Text("取消")
                }
            },
            containerColor = Color.White,
            tonalElevation = 0.dp
        )
    }
    
    // 日期选择器
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = updatedReminder.customDateTime?.atZone(java.time.ZoneId.systemDefault())
                ?.toInstant()?.toEpochMilli()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(onClick = {
                    datePickerState.selectedDateMillis?.let { millis ->
                        val selectedDate = java.time.Instant.ofEpochMilli(millis)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDate()
                        
                        // 保留时间部分
                        val currentTime = updatedReminder.customDateTime?.toLocalTime() 
                            ?: LocalTime.now()
                        
                        // 合并日期和时间
                        val newDateTime = LocalDateTime.of(
                            selectedDate,
                            currentTime
                        )
                        
                        updatedReminder = updatedReminder.copy(customDateTime = newDateTime)
                    }
                    showDatePicker = false
                }) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("取消")
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                usePlatformDefaultWidth = false
            ),
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .padding(16.dp),
            colors = DatePickerDefaults.colors(
                containerColor = Color.White,
                titleContentColor = DustyLavender,
                selectedDayContainerColor = DustyLavender
            )
        ) {
            DatePicker(
                state = datePickerState,
                showModeToggle = false,
                colors = DatePickerDefaults.colors(
                    containerColor = Color.White,
                    titleContentColor = DustyLavender,
                    selectedDayContainerColor = DustyLavender
                )
            )
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                text = "编辑提醒",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            ) 
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 提醒类型
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = getReminderTypeIcon(updatedReminder.type),
                        contentDescription = null,
                        tint = DustyLavender,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Text(
                        text = getReminderTypeText(updatedReminder.type),
                        fontSize = 16.sp,
                        color = Color(0xFF333333)
                    )
                }
                
                Divider(color = Color(0xFFEEEEEE))
                
                // 根据提醒类型显示不同的设置选项
                when (updatedReminder.type) {
                    ReminderType.DAILY, ReminderType.WEEKLY, ReminderType.MONTHLY -> {
                        // 时间设置
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTimePicker = true },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.AccessTime,
                                contentDescription = null,
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Column {
                                Text(
                                    text = "提醒时间",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666)
                                )
                                
                                Text(
                                    text = updatedReminder.time?.format(
                                        DateTimeFormatter.ofPattern("HH:mm")
                                    ) ?: "点击设置时间",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF333333)
                                )
                            }
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            Icon(
                                imageVector = Icons.Default.KeyboardArrowRight,
                                contentDescription = null,
                                tint = Color(0xFFCCCCCC)
                            )
                        }
                    }
                    
                    ReminderType.BEFORE_DUE -> {
                        // 截止日期前天数设置
                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = "提前天数",
                                fontSize = 14.sp,
                                color = Color(0xFF666666)
                            )
                            
                            Spacer(modifier = Modifier.height(4.dp))
                            
                            OutlinedTextField(
                                value = (updatedReminder.daysBeforeDue ?: 1).toString(),
                                onValueChange = { value ->
                                    value.toIntOrNull()?.let { days ->
                                        if (days > 0) {
                                            updatedReminder = updatedReminder.copy(daysBeforeDue = days)
                                        }
                                    }
                                },
                                modifier = Modifier.fillMaxWidth(),
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true,
                                maxLines = 1,
                                label = { Text("天") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = DustyLavender,
                                    unfocusedBorderColor = Color(0xFFE2E8F0),
                                    focusedLabelColor = DustyLavender,
                                    unfocusedLabelColor = Color(0xFF666666)
                                )
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 时间设置
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTimePicker = true },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.AccessTime,
                                contentDescription = null,
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Column {
                                Text(
                                    text = "提醒时间",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666)
                                )
                                
                                Text(
                                    text = updatedReminder.time?.format(
                                        DateTimeFormatter.ofPattern("HH:mm")
                                    ) ?: "点击设置时间",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF333333)
                                )
                            }
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            Icon(
                                imageVector = Icons.Default.KeyboardArrowRight,
                                contentDescription = null,
                                tint = Color(0xFFCCCCCC)
                            )
                        }
                    }
                    
                    ReminderType.PROGRESS -> {
                        // 进度阈值设置
                        Column(modifier = Modifier.fillMaxWidth()) {
                            Text(
                                text = "进度阈值",
                                fontSize = 14.sp,
                                color = Color(0xFF666666)
                            )
                            
                            Spacer(modifier = Modifier.height(4.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                OutlinedTextField(
                                    value = (updatedReminder.progressThreshold ?: 50f).toString(),
                                    onValueChange = { value ->
                                        value.toFloatOrNull()?.let { threshold ->
                                            if (threshold in 0f..100f) {
                                                updatedReminder = updatedReminder.copy(
                                                    progressThreshold = threshold
                                                )
                                            }
                                        }
                                    },
                                    modifier = Modifier.weight(1f),
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    singleLine = true,
                                    maxLines = 1,
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = DustyLavender,
                                        unfocusedBorderColor = Color(0xFFE2E8F0)
                                    )
                                )
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Text(
                                    text = "%",
                                    fontSize = 16.sp,
                                    color = Color(0xFF666666)
                                )
                            }
                        }
                    }
                    
                    ReminderType.CUSTOM -> {
                        // 自定义日期时间设置
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showDatePicker = true },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.CalendarMonth,
                                contentDescription = null,
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Column {
                                Text(
                                    text = "提醒日期",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666)
                                )
                                
                                Text(
                                    text = updatedReminder.customDateTime?.format(
                                        DateTimeFormatter.ofPattern("yyyy年MM月dd日")
                                    ) ?: "点击设置日期",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF333333)
                                )
                            }
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            Icon(
                                imageVector = Icons.Default.KeyboardArrowRight,
                                contentDescription = null,
                                tint = Color(0xFFCCCCCC)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 时间设置
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTimePicker = true },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.AccessTime,
                                contentDescription = null,
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Column {
                                Text(
                                    text = "提醒时间",
                                    fontSize = 14.sp,
                                    color = Color(0xFF666666)
                                )
                                
                                Text(
                                    text = updatedReminder.customDateTime?.format(
                                        DateTimeFormatter.ofPattern("HH:mm")
                                    ) ?: "点击设置时间",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF333333)
                                )
                            }
                            
                            Spacer(modifier = Modifier.weight(1f))
                            
                            Icon(
                                imageVector = Icons.Default.KeyboardArrowRight,
                                contentDescription = null,
                                tint = Color(0xFFCCCCCC)
                            )
                        }
                    }
                    
                    else -> {} // 完成提醒不需要额外设置
                }
                
                // 提醒描述设置
                Column(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = "提醒描述（选填）",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    OutlinedTextField(
                        value = updatedReminder.description ?: "",
                        onValueChange = { 
                            updatedReminder = updatedReminder.copy(
                                description = it.takeIf { it.isNotBlank() }
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        placeholder = { Text("添加描述信息") },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color(0xFFE2E8F0)
                        )
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = { onSave(updatedReminder) }) {
                Text("保存")
            }
        },
        dismissButton = {
            Row {
                TextButton(
                    onClick = onDelete,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Red
                    )
                ) {
                    Text("删除")
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            }
        },
        containerColor = Color.White,
        tonalElevation = 0.dp
    )
}

/**
 * 提醒项组件
 */
@Composable
private fun ReminderItem(
    reminder: ReminderSetting,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = Color(0xFFF8FAFC),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标容器
            Box(
                modifier = Modifier
                    .size(44.dp)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                DustyLavender.copy(alpha = 0.7f),
                                DustyLavender.copy(alpha = 0.3f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getReminderTypeIcon(reminder.type),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 提醒内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = getReminderTypeText(reminder.type),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                val detailText = when (reminder.type) {
                    ReminderType.ONCE -> reminder.customDateTime?.let { 
                        "单次提醒: ${it.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))}" 
                    } ?: "单次提醒"
                    ReminderType.DAILY -> reminder.time?.let { "每天 ${it.format(DateTimeFormatter.ofPattern("HH:mm"))}" } ?: "每天"
                    ReminderType.WEEKLY -> reminder.time?.let { "每周 ${it.format(DateTimeFormatter.ofPattern("HH:mm"))}" } ?: "每周"
                    ReminderType.MONTHLY -> reminder.time?.let { "每月 ${it.format(DateTimeFormatter.ofPattern("HH:mm"))}" } ?: "每月"
                    ReminderType.BEFORE_DUE -> "截止日期前 ${reminder.daysBeforeDue ?: 1} 天"
                    ReminderType.COMPLETION -> "目标完成时提醒"
                    ReminderType.PROGRESS -> "进度达到 ${reminder.progressThreshold?.toInt() ?: 50}% 时提醒"
                    ReminderType.CUSTOM -> reminder.customDateTime?.let { 
                        "单次提醒: ${it.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))}" 
                    } ?: "自定义提醒"
                }
                
                Text(
                    text = detailText,
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 显示描述信息（如果有）
                reminder.description?.let { desc ->
                    Text(
                        text = desc,
                        fontSize = 12.sp,
                        color = Color(0xFF888888),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 状态开关
            Switch(
                checked = reminder.enabled,
                onCheckedChange = { /* 状态切换需要在编辑对话框中处理 */ onClick() },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = DustyLavender,
                    checkedTrackColor = DustyLavender.copy(alpha = 0.5f),
                    uncheckedThumbColor = Color.Gray,
                    uncheckedTrackColor = Color.Gray.copy(alpha = 0.3f)
                )
            )
        }
    }
}

/**
 * 获取提醒类型对应的图标
 */
@Composable
private fun getReminderTypeIcon(type: ReminderType): ImageVector {
    return when (type) {
        ReminderType.ONCE -> Icons.Outlined.AlarmOn
        ReminderType.DAILY -> Icons.Default.Today
        ReminderType.WEEKLY -> Icons.Default.DateRange
        ReminderType.MONTHLY -> Icons.Outlined.CalendarMonth
        ReminderType.BEFORE_DUE -> Icons.Outlined.Timer
        ReminderType.COMPLETION -> Icons.Default.CheckCircle
        ReminderType.PROGRESS -> Icons.Default.Timelapse
        ReminderType.CUSTOM -> Icons.Outlined.Notifications
    }
}

/**
 * 获取提醒类型对应的文本
 */
@Composable
private fun getReminderTypeText(type: ReminderType): String {
    return when (type) {
        ReminderType.ONCE -> "单次提醒"
        ReminderType.DAILY -> "每日提醒"
        ReminderType.WEEKLY -> "每周提醒"
        ReminderType.MONTHLY -> "每月提醒"
        ReminderType.BEFORE_DUE -> "截止日期前提醒"
        ReminderType.COMPLETION -> "完成提醒"
        ReminderType.PROGRESS -> "进度提醒"
        ReminderType.CUSTOM -> "自定义提醒"
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
fun ReminderSettingsPanelPreview() {
    val reminders = remember {
        mutableStateOf(
            listOf(
                ReminderSetting.createDefault(ReminderType.DAILY),
                ReminderSetting.createDefault(ReminderType.BEFORE_DUE)
            )
        )
    }
    
    ReminderSettingsPanel(
        reminderSettings = reminders.value,
        onReminderSettingsChanged = { reminders.value = it }
    )
} 