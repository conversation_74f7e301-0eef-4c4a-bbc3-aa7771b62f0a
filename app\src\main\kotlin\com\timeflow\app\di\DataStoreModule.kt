package com.timeflow.app.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 为应用程序提供单例DataStore的Module
 */
@Module
@InstallIn(SingletonComponent::class)
object DataStoreModule {
    
    // 为Context扩展themeDataStore属性，但仅在这里定义一次
    private val Context.themeDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "theme_settings"
    )
    
    // 为Context扩展medicationDataStore属性，用于存储药物相关数据
    private val Context.medicationDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "medication_settings"
    )

    // 为Context扩展notificationDataStore属性，用于存储通知相关数据
    private val Context.notificationDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "notification_settings"
    )
    
    /**
     * 主题设置DataStore限定符
     */
    @Qualifier
    @Retention(AnnotationRetention.BINARY)
    annotation class ThemeDataStore
    
    /**
     * 药物设置DataStore限定符
     */
    @Qualifier
    @Retention(AnnotationRetention.BINARY)
    annotation class MedicationDataStore

    /**
     * 通知设置DataStore限定符
     */
    @Qualifier
    @Retention(AnnotationRetention.BINARY)
    annotation class NotificationDataStore
    
    /**
     * 提供主题设置DataStore实例
     * 
     * @param context 应用上下文
     * @return DataStore<Preferences> 主题设置的数据存储实例
     */
    @Provides
    @Singleton
    @ThemeDataStore
    fun provideThemeDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
        return context.themeDataStore
    }
    
    /**
     * 提供药物设置DataStore实例
     *
     * @param context 应用上下文
     * @return DataStore<Preferences> 药物设置的数据存储实例
     */
    @Provides
    @Singleton
    @MedicationDataStore
    fun provideMedicationDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
        return context.medicationDataStore
    }

    /**
     * 提供通知设置DataStore实例
     *
     * @param context 应用上下文
     * @return DataStore<Preferences> 通知设置的数据存储实例
     */
    @Provides
    @Singleton
    @NotificationDataStore
    fun provideNotificationDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
        return context.notificationDataStore
    }
}