package com.timeflow.app.ui.theme

import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 莫奈和莫兰迪风格的数据分析页面配色主题
 * 
 * 莫奈配色特点：柔和、自然、渐变感强
 * 莫兰迪配色特点：低饱和度、温暖、舒缓
 */
object AnalyticsTheme {
    
    // 莫奈风格主色调 - 柔和的自然色彩
    object MonetColors {
        val primary = Color(0xFFB8C5D1)      // 雾蓝
        val secondary = Color(0xFFD4C5B9)    // 暖米
        val tertiary = Color(0xFFC9D4C5)     // 薄荷绿
        val surface = Color(0xFFF4F1ED)      // 奶白
        val surfaceVariant = Color(0xFFEAE6E0) // 浅灰米
        val background = Color(0xFFFAF8F5)   // 温白
    }
    
    // 莫兰迪风格辅助色调 - 低饱和度优雅色彩
    object MorandiColors {
        val dustyRose = Color(0xFFD4B5B0)    // 尘玫瑰
        val sageGreen = Color(0xFFB5C4B1)    // 鼠尾草绿
        val warmGray = Color(0xFFC8BDB1)     // 暖灰
        val softLavender = Color(0xFFBFB5C4) // 柔紫
        val mauveGray = Color(0xFFB8B5C1)    // 淡紫灰
        val peachCream = Color(0xFFE8D5C4)   // 桃奶
    }
    
    // 功能性色彩
    object FunctionalColors {
        val success = Color(0xFFA8C4A2)      // 柔和绿
        val warning = Color(0xFFE6C068)      // 温黄
        val error = Color(0xFFD4A5A5)        // 柔红
        val info = Color(0xFFA5BDD4)         // 柔蓝
        val neutral = Color(0xFFBBB8B5)      // 中性灰
    }
    
    // 文本颜色层级
    object TextColors {
        val primary = Color(0xFF2F2F2F)      // 主要文本
        val secondary = Color(0xFF6B6B6B)    // 次要文本
        val tertiary = Color(0xFF9B9B9B)     // 辅助文本
        val disabled = Color(0xFFB8B8B8)     // 禁用文本
        val onSurface = Color(0xFF1C1C1C)    // 表面文本
    }
    
    // 图表专用配色 - 莫奈渐变色盘
    object ChartColors {
        val palette = listOf(
            MonetColors.primary,
            MorandiColors.dustyRose,
            MorandiColors.sageGreen,
            MorandiColors.softLavender,
            MonetColors.tertiary,
            MorandiColors.peachCream,
            MorandiColors.warmGray,
            MorandiColors.mauveGray
        )
        
        val gradientStart = MonetColors.primary
        val gradientEnd = MorandiColors.dustyRose
    }
    
    // 字体尺寸定义 - 缩小版本
    object Typography {
        val headlineLarge = TextStyle(
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            lineHeight = 20.sp
        )
        
        val headlineMedium = TextStyle(
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            lineHeight = 18.sp
        )
        
        val headlineSmall = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 16.sp
        )
        
        val titleLarge = TextStyle(
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 18.sp
        )
        
        val titleMedium = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 16.sp
        )
        
        val titleSmall = TextStyle(
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 14.sp
        )
        
        val bodyLarge = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            lineHeight = 16.sp
        )
        
        val bodyMedium = TextStyle(
            fontSize = 11.sp,
            fontWeight = FontWeight.Normal,
            lineHeight = 14.sp
        )
        
        val bodySmall = TextStyle(
            fontSize = 10.sp,
            fontWeight = FontWeight.Normal,
            lineHeight = 12.sp
        )
        
        val labelLarge = TextStyle(
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 14.sp
        )
        
        val labelMedium = TextStyle(
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 12.sp
        )
        
        val labelSmall = TextStyle(
            fontSize = 9.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 11.sp
        )
    }
    
    // 尺寸定义
    object Dimensions {
        val cardPadding = 12.dp
        val cardRadius = 10.dp
        val spacingXSmall = 4.dp
        val spacingSmall = 6.dp
        val spacingMedium = 8.dp
        val spacingLarge = 12.dp
        val spacingXLarge = 16.dp
        val iconSizeSmall = 16.dp
        val iconSizeMedium = 20.dp
        val iconSizeLarge = 24.dp
        val chartHeight = 100.dp
        val cardElevation = 1.dp
    }
}

/**
 * 主题数据类
 */
data class AnalyticsThemeData(
    val colors: AnalyticsColorScheme,
    val typography: AnalyticsTypography,
    val dimensions: AnalyticsDimensions
)

data class AnalyticsColorScheme(
    val monet: MonetColorScheme,
    val morandi: MorandiColorScheme,
    val functional: FunctionalColorScheme,
    val text: TextColorScheme,
    val chart: ChartColorScheme
)

data class MonetColorScheme(
    val primary: Color,
    val secondary: Color,
    val tertiary: Color,
    val surface: Color,
    val surfaceVariant: Color,
    val background: Color
)

data class MorandiColorScheme(
    val dustyRose: Color,
    val sageGreen: Color,
    val warmGray: Color,
    val softLavender: Color,
    val mauveGray: Color,
    val peachCream: Color
)

data class FunctionalColorScheme(
    val success: Color,
    val warning: Color,
    val error: Color,
    val info: Color,
    val neutral: Color
)

data class TextColorScheme(
    val primary: Color,
    val secondary: Color,
    val tertiary: Color,
    val disabled: Color,
    val onSurface: Color
)

data class ChartColorScheme(
    val palette: List<Color>,
    val gradientStart: Color,
    val gradientEnd: Color
)

data class AnalyticsTypography(
    val headlineLarge: TextStyle,
    val headlineMedium: TextStyle,
    val headlineSmall: TextStyle,
    val titleLarge: TextStyle,
    val titleMedium: TextStyle,
    val titleSmall: TextStyle,
    val bodyLarge: TextStyle,
    val bodyMedium: TextStyle,
    val bodySmall: TextStyle,
    val labelLarge: TextStyle,
    val labelMedium: TextStyle,
    val labelSmall: TextStyle
)

data class AnalyticsDimensions(
    val cardPadding: androidx.compose.ui.unit.Dp,
    val cardRadius: androidx.compose.ui.unit.Dp,
    val spacingXSmall: androidx.compose.ui.unit.Dp,
    val spacingSmall: androidx.compose.ui.unit.Dp,
    val spacingMedium: androidx.compose.ui.unit.Dp,
    val spacingLarge: androidx.compose.ui.unit.Dp,
    val spacingXLarge: androidx.compose.ui.unit.Dp,
    val iconSizeSmall: androidx.compose.ui.unit.Dp,
    val iconSizeMedium: androidx.compose.ui.unit.Dp,
    val iconSizeLarge: androidx.compose.ui.unit.Dp,
    val chartHeight: androidx.compose.ui.unit.Dp,
    val cardElevation: androidx.compose.ui.unit.Dp
)

// 默认主题实例
val defaultAnalyticsTheme = AnalyticsThemeData(
    colors = AnalyticsColorScheme(
        monet = MonetColorScheme(
            primary = AnalyticsTheme.MonetColors.primary,
            secondary = AnalyticsTheme.MonetColors.secondary,
            tertiary = AnalyticsTheme.MonetColors.tertiary,
            surface = AnalyticsTheme.MonetColors.surface,
            surfaceVariant = AnalyticsTheme.MonetColors.surfaceVariant,
            background = AnalyticsTheme.MonetColors.background
        ),
        morandi = MorandiColorScheme(
            dustyRose = AnalyticsTheme.MorandiColors.dustyRose,
            sageGreen = AnalyticsTheme.MorandiColors.sageGreen,
            warmGray = AnalyticsTheme.MorandiColors.warmGray,
            softLavender = AnalyticsTheme.MorandiColors.softLavender,
            mauveGray = AnalyticsTheme.MorandiColors.mauveGray,
            peachCream = AnalyticsTheme.MorandiColors.peachCream
        ),
        functional = FunctionalColorScheme(
            success = AnalyticsTheme.FunctionalColors.success,
            warning = AnalyticsTheme.FunctionalColors.warning,
            error = AnalyticsTheme.FunctionalColors.error,
            info = AnalyticsTheme.FunctionalColors.info,
            neutral = AnalyticsTheme.FunctionalColors.neutral
        ),
        text = TextColorScheme(
            primary = AnalyticsTheme.TextColors.primary,
            secondary = AnalyticsTheme.TextColors.secondary,
            tertiary = AnalyticsTheme.TextColors.tertiary,
            disabled = AnalyticsTheme.TextColors.disabled,
            onSurface = AnalyticsTheme.TextColors.onSurface
        ),
        chart = ChartColorScheme(
            palette = AnalyticsTheme.ChartColors.palette,
            gradientStart = AnalyticsTheme.ChartColors.gradientStart,
            gradientEnd = AnalyticsTheme.ChartColors.gradientEnd
        )
    ),
    typography = AnalyticsTypography(
        headlineLarge = AnalyticsTheme.Typography.headlineLarge,
        headlineMedium = AnalyticsTheme.Typography.headlineMedium,
        headlineSmall = AnalyticsTheme.Typography.headlineSmall,
        titleLarge = AnalyticsTheme.Typography.titleLarge,
        titleMedium = AnalyticsTheme.Typography.titleMedium,
        titleSmall = AnalyticsTheme.Typography.titleSmall,
        bodyLarge = AnalyticsTheme.Typography.bodyLarge,
        bodyMedium = AnalyticsTheme.Typography.bodyMedium,
        bodySmall = AnalyticsTheme.Typography.bodySmall,
        labelLarge = AnalyticsTheme.Typography.labelLarge,
        labelMedium = AnalyticsTheme.Typography.labelMedium,
        labelSmall = AnalyticsTheme.Typography.labelSmall
    ),
    dimensions = AnalyticsDimensions(
        cardPadding = AnalyticsTheme.Dimensions.cardPadding,
        cardRadius = AnalyticsTheme.Dimensions.cardRadius,
        spacingXSmall = AnalyticsTheme.Dimensions.spacingXSmall,
        spacingSmall = AnalyticsTheme.Dimensions.spacingSmall,
        spacingMedium = AnalyticsTheme.Dimensions.spacingMedium,
        spacingLarge = AnalyticsTheme.Dimensions.spacingLarge,
        spacingXLarge = AnalyticsTheme.Dimensions.spacingXLarge,
        iconSizeSmall = AnalyticsTheme.Dimensions.iconSizeSmall,
        iconSizeMedium = AnalyticsTheme.Dimensions.iconSizeMedium,
        iconSizeLarge = AnalyticsTheme.Dimensions.iconSizeLarge,
        chartHeight = AnalyticsTheme.Dimensions.chartHeight,
        cardElevation = AnalyticsTheme.Dimensions.cardElevation
    )
)

// CompositionLocal 提供者
val LocalAnalyticsTheme = staticCompositionLocalOf { defaultAnalyticsTheme }

/**
 * 主题提供者组件
 */
@androidx.compose.runtime.Composable
fun AnalyticsThemeProvider(
    theme: AnalyticsThemeData = defaultAnalyticsTheme,
    content: @androidx.compose.runtime.Composable () -> Unit
) {
    androidx.compose.runtime.CompositionLocalProvider(LocalAnalyticsTheme provides theme) {
        content()
    }
} 