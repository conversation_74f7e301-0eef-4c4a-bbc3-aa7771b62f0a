package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.AppUsageEntity
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * 应用使用数据DAO接口
 */
@Dao
interface AppUsageDao {
    /**
     * 插入单条记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(appUsage: AppUsageEntity): Long
    
    /**
     * 批量插入记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(appUsages: List<AppUsageEntity>): List<Long>
    
    /**
     * 更新记录
     */
    @Update
    suspend fun update(appUsage: AppUsageEntity)
    
    /**
     * 批量更新记录
     */
    @Update
    suspend fun updateAll(appUsages: List<AppUsageEntity>)
    
    /**
     * 删除记录
     */
    @Delete
    suspend fun delete(appUsage: AppUsageEntity)
    
    /**
     * 获取指定日期的所有应用使用数据
     */
    @Query("SELECT * FROM app_usage WHERE date = :date")
    fun getAppUsageByDate(date: LocalDate): Flow<List<AppUsageEntity>>
    
    /**
     * 获取指定日期的所有应用使用数据（同步方法）
     */
    @Query("SELECT * FROM app_usage WHERE date = :date")
    suspend fun getAppUsageByDateSync(date: LocalDate): List<AppUsageEntity>
    
    /**
     * 获取指定日期范围的所有应用使用数据
     */
    @Query("SELECT * FROM app_usage WHERE date BETWEEN :startDate AND :endDate")
    fun getAppUsageByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<AppUsageEntity>>
    
    /**
     * 获取指定应用和日期的使用数据
     */
    @Query("SELECT * FROM app_usage WHERE packageName = :packageName AND date = :date LIMIT 1")
    suspend fun getAppUsageByPackageAndDate(packageName: String, date: LocalDate): AppUsageEntity?
    
    /**
     * 获取指定应用的所有使用数据
     */
    @Query("SELECT * FROM app_usage WHERE packageName = :packageName ORDER BY date DESC")
    fun getAppUsageByPackage(packageName: String): Flow<List<AppUsageEntity>>
    
    /**
     * 获取指定分类的所有使用数据
     */
    @Query("SELECT * FROM app_usage WHERE category = :category ORDER BY date DESC")
    fun getAppUsageByCategory(category: String): Flow<List<AppUsageEntity>>
    
    /**
     * 获取生产力应用的使用数据
     */
    @Query("SELECT * FROM app_usage WHERE isProductivity = 1 AND date = :date")
    fun getProductiveAppUsage(date: LocalDate): Flow<List<AppUsageEntity>>
    
    /**
     * 获取非生产力应用的使用数据
     */
    @Query("SELECT * FROM app_usage WHERE isProductivity = 0 AND date = :date")
    fun getNonProductiveAppUsage(date: LocalDate): Flow<List<AppUsageEntity>>
    
    /**
     * 获取所有应用类别
     */
    @Query("SELECT DISTINCT category FROM app_usage")
    fun getAllCategories(): Flow<List<String>>
} 