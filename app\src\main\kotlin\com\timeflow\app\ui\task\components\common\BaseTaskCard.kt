package com.timeflow.app.ui.task.components.common

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.entity.Task
import java.time.format.DateTimeFormatter

/**
 * 基础任务卡片组件
 *
 * 这是一个统一的基础任务卡片组件，所有其他任务卡片组件都应该基于此组件构建。
 * 它接受entity.Task类型的参数，并提供基础的任务信息展示。
 *
 * @param task 任务实体
 * @param onClick 点击任务的回调
 * @param onCompletionToggle 切换任务完成状态的回调
 * @param showPriority 是否显示优先级指示器
 * @param cardTheme 卡片主题
 * @param modifier 修饰符
 */
@Composable
fun BaseTaskCard(
    task: Task,
    onClick: (String) -> Unit,
    onCompletionToggle: ((String, Boolean) -> Unit)? = null,
    showPriority: Boolean = true,
    cardTheme: TaskCardTheme = TaskCardThemes.Default(),
    modifier: Modifier = Modifier
) {
    val haptics = LocalHapticFeedback.current
    val isCompleted = task.completedAt != null
    
    // 动画和状态
    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "Scale Animation"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isCompleted) 
            cardTheme.completedBackgroundColor
        else
            cardTheme.backgroundColor,
        label = "Background Color Animation"
    )
    
    // 卡片主体
    Card(
        modifier = modifier
            .fillMaxWidth()
            .scale(scale)
            .clickable { 
                haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                onClick(task.id) 
            },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = cardTheme.shape,
        elevation = CardDefaults.cardElevation(
            defaultElevation = (if (isCompleted) cardTheme.elevationCompleted else cardTheme.elevationActive).dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 优先级指示器
            if (showPriority) {
                // 根据任务优先级选择更适合的变体
                val variant = when {
                    task.priority >= 3 -> PriorityVariant.ALERT
                    task.priority == 2 -> if (cardTheme.priorityIndicatorVariant == PriorityVariant.DOT) 
                        PriorityVariant.ALERT 
                    else 
                        cardTheme.priorityIndicatorVariant
                    else -> cardTheme.priorityIndicatorVariant
                }
                
                // 优先级标签需要特殊处理
                if (variant == PriorityVariant.TAG) {
                    PriorityIndicator(
                        priority = task.priority,
                        variant = variant,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                } else {
                    PriorityIndicator(
                        priority = task.priority,
                        size = 12.dp,
                        variant = variant,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }
            }
            
            // 任务内容
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 4.dp)
            ) {
                // 任务标题
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        textDecoration = if (isCompleted) TextDecoration.LineThrough else TextDecoration.None
                    ),
                    color = if (isCompleted) 
                        Color(0xFF949494)
                    else 
                        MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 如果有描述则显示一行
                if (!task.description.isNullOrEmpty()) {
                    // 🔧 清理描述中的JSON颜色信息
                    val cleanDescription = remember(task.description!!) {
                        val colorPattern = """\{"color":(\d+)\}""".toRegex()
                        task.description.replace(colorPattern, "").trim()
                    }
                    
                    if (cleanDescription.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = cleanDescription,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
                
                // 到期日期（如果有的话）
                if (task.dueDate != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "到期: ${task.dueDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 完成状态切换按钮（如果回调不为空）
            if (onCompletionToggle != null) {
                Checkbox(
                    checked = isCompleted,
                    onCheckedChange = { 
                        haptics.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        onCompletionToggle(task.id, it) 
                    },
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    }
}

/**
 * 基础任务卡片的轻量版本
 * 
 * 适用于列表中的简洁显示，只显示最必要的信息
 * 
 * @param task 任务实体
 * @param onClick 点击任务的回调
 * @param onCompletionToggle 切换任务完成状态的回调
 * @param cardTheme 卡片主题
 * @param modifier 修饰符
 */
@Composable
fun CompactTaskCard(
    task: Task,
    onClick: (String) -> Unit,
    onCompletionToggle: ((String, Boolean) -> Unit)? = null,
    cardTheme: TaskCardTheme = TaskCardThemes.Default(),
    modifier: Modifier = Modifier
) {
    val isCompleted = task.completedAt != null
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 2.dp)
            .clip(cardTheme.shape)
            .background(
                if (isCompleted) 
                    cardTheme.completedBackgroundColor
                else
                    cardTheme.backgroundColor
            )
            .clickable { onClick(task.id) }
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 优先级指示器 - 使用小尺寸的点
        if (task.priority > 0) {
            PriorityIndicator(
                priority = task.priority,
                size = 8.dp,
                modifier = Modifier.padding(end = 8.dp)
            )
        }
        
        // 任务标题和循环标识 - 采用紧凑布局
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium,
                    textDecoration = if (isCompleted) TextDecoration.LineThrough else TextDecoration.None
                ),
                color = if (isCompleted)
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                else
                    MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            // 🔧 循环任务标识
            if (task.isRecurring) {
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Default.Repeat,
                    contentDescription = "循环任务",
                    tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f),
                    modifier = Modifier.size(14.dp)
                )
            }
        }
        
        // 完成状态切换
        if (onCompletionToggle != null) {
            Checkbox(
                checked = isCompleted,
                onCheckedChange = { onCompletionToggle(task.id, it) },
                modifier = Modifier
                    .padding(start = 4.dp)
                    .size(20.dp)
            )
        }
    }
} 