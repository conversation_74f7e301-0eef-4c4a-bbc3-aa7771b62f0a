package com.timeflow.app.worker

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.receiver.TaskAlarmReceiver
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.Calendar

/**
 * 任务提醒工作器
 * 用于管理任务的定时提醒
 */
class TaskReminderWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "TaskReminderWorker"
        const val KEY_TASK_ID = "task_id"
        const val KEY_TASK_TITLE = "task_title"
        const val KEY_REMINDER_TIME = "reminder_time" // ISO格式的DateTime字符串
        const val KEY_PRIORITY = "priority"
        const val KEY_REMINDER_TYPE = "reminder_type" // task_start_reminder, task_deadline_reminder, overdue_reminder, task_reminder(兼容), deadline_reminder(兼容)
        
        /**
         * 生成唯一的请求代码
         * 结合任务ID和提醒类型，确保不同类型的提醒有不同的ID
         */
        fun generateUniqueRequestCode(taskId: String, reminderType: String): Int {
            return (taskId + reminderType).hashCode()
        }
        
        /**
         * 取消任务提醒
         */
        fun cancelReminder(context: Context, taskId: String, reminderType: String = "task_reminder") {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(context, TaskAlarmReceiver::class.java)
            
            val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                generateUniqueRequestCode(taskId, reminderType),
                intent,
                pendingIntentFlags
            )
            
            // 取消闹钟
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()
            
            Log.d(TAG, "任务[$taskId]的${reminderType}提醒已取消")
        }
        
        /**
         * 取消任务的所有提醒
         */
        fun cancelAllReminders(context: Context, taskId: String) {
            val reminderTypes = listOf("task_reminder", "deadline_reminder", "overdue_reminder")
            reminderTypes.forEach { reminderType ->
                cancelReminder(context, taskId, reminderType)
            }
            Log.d(TAG, "任务[$taskId]的所有提醒已取消")
        }
    }
    
    override suspend fun doWork(): Result {
        try {
            val taskId = inputData.getString(KEY_TASK_ID) ?: return Result.failure()
            val taskTitle = inputData.getString(KEY_TASK_TITLE) ?: "任务"
            val reminderTimeString = inputData.getString(KEY_REMINDER_TIME) ?: return Result.failure()
            val priority = inputData.getString(KEY_PRIORITY) ?: "中等"
            val reminderType = inputData.getString(KEY_REMINDER_TYPE) ?: "task_reminder"
            
            // 解析提醒时间
            val reminderTime = LocalDateTime.parse(reminderTimeString)
            
            // 设置提醒的闹钟
            scheduleAlarm(taskId, taskTitle, reminderTime, priority, reminderType)
            
            return Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "设置任务提醒失败: ${e.message}", e)
            return Result.failure()
        }
    }
    
    /**
     * 设置任务提醒闹钟
     */
    private fun scheduleAlarm(
        taskId: String,
        taskTitle: String,
        reminderTime: LocalDateTime,
        priority: String,
        reminderType: String
    ) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        // 创建广播意图
        val intent = Intent(context, TaskAlarmReceiver::class.java).apply {
            putExtra(TaskAlarmReceiver.EXTRA_TASK_ID, taskId)
            putExtra(TaskAlarmReceiver.EXTRA_TASK_TITLE, taskTitle)
            putExtra(TaskAlarmReceiver.EXTRA_PRIORITY, priority)
            putExtra(TaskAlarmReceiver.EXTRA_REMINDER_TYPE, reminderType)
        }
        
        // 创建PendingIntent
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            Companion.generateUniqueRequestCode(taskId, reminderType),
            intent,
            pendingIntentFlags
        )
        
        // 转换LocalDateTime为毫秒时间戳
        val triggerTimeMillis = reminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        
        // 检查提醒时间是否在未来
        if (triggerTimeMillis <= System.currentTimeMillis()) {
            Log.w(TAG, "任务[$taskTitle]的提醒时间已过期: $reminderTime")
            return
        }
        
        // 设置闹钟
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0及以上版本，考虑Doze模式
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                triggerTimeMillis,
                pendingIntent
            )
            Log.d(TAG, "任务[$taskTitle]的${reminderType}提醒已设置: $reminderTime")
        } else {
            // 低版本Android
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                triggerTimeMillis,
                pendingIntent
            )
            Log.d(TAG, "任务[$taskTitle]的${reminderType}提醒已设置: $reminderTime")
        }
    }
    

} 