package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.viewmodel.GoalViewModel
import com.timeflow.app.data.model.*
import java.time.LocalDateTime

/**
 * 目标设定向导界面 - 参照Project50设计
 * 提供步骤式的目标创建流程，包括智能推荐和模板选择
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalSetupWizardScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel()
) {
    var currentStep by remember { mutableIntStateOf(0) }
    var selectedTemplate by remember { mutableStateOf<WizardTemplate?>(null) }
    var goalTitle by remember { mutableStateOf("") }
    var goalDescription by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf<GoalCategory?>(null) }
    var selectedTimeframe by remember { mutableStateOf<TimeFrame?>(null) }
    var selectedDifficulty by remember { mutableStateOf<Difficulty?>(null) }
    
    val steps = listOf("选择类型", "设置目标", "时间规划", "确认创建")
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "创建新目标",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        },
        containerColor = Color(0xFFF8F9FA)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 进度指示器
            WizardProgressIndicator(
                currentStep = currentStep,
                totalSteps = steps.size,
                stepLabels = steps
            )
            
            // 步骤内容
            AnimatedContent(
                targetState = currentStep,
                transitionSpec = {
                    slideInHorizontally(
                        initialOffsetX = { if (targetState > initialState) it else -it }
                    ) + fadeIn() togetherWith
                    slideOutHorizontally(
                        targetOffsetX = { if (targetState > initialState) -it else it }
                    ) + fadeOut()
                },
                modifier = Modifier.weight(1f)
            ) { step ->
                when (step) {
                    0 -> TemplateSelectionStep(
                        selectedTemplate = selectedTemplate,
                        onTemplateSelected = { selectedTemplate = it }
                    )
                    1 -> GoalDetailsStep(
                        template = selectedTemplate,
                        title = goalTitle,
                        description = goalDescription,
                        selectedCategory = selectedCategory,
                        onTitleChange = { goalTitle = it },
                        onDescriptionChange = { goalDescription = it },
                        onCategorySelected = { selectedCategory = it }
                    )
                    2 -> TimeframePlanningStep(
                        selectedTimeframe = selectedTimeframe,
                        selectedDifficulty = selectedDifficulty,
                        onTimeframeSelected = { selectedTimeframe = it },
                        onDifficultySelected = { selectedDifficulty = it }
                    )
                    3 -> ConfirmationStep(
                        template = selectedTemplate,
                        title = goalTitle,
                        description = goalDescription,
                        category = selectedCategory,
                        timeframe = selectedTimeframe,
                        difficulty = selectedDifficulty
                    )
                }
            }
            
            // 底部导航按钮
            WizardNavigationButtons(
                currentStep = currentStep,
                totalSteps = steps.size,
                canProceed = canProceedToNextStep(currentStep, selectedTemplate, goalTitle, selectedCategory, selectedTimeframe),
                onPrevious = { if (currentStep > 0) currentStep-- },
                onNext = { if (currentStep < steps.size - 1) currentStep++ },
                onFinish = {
                    // 创建目标
                    createGoalFromWizard(
                        viewModel = viewModel,
                        template = selectedTemplate,
                        title = goalTitle,
                        description = goalDescription,
                        category = selectedCategory,
                        timeframe = selectedTimeframe,
                        difficulty = selectedDifficulty
                    )
                    navController.popBackStack()
                }
            )
        }
    }
}

/**
 * 向导进度指示器
 */
@Composable
fun WizardProgressIndicator(
    currentStep: Int,
    totalSteps: Int,
    stepLabels: List<String>
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 进度条
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(totalSteps) { index ->
                    // 步骤圆圈
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .background(
                                color = if (index <= currentStep) Color(0xFF6366F1) else Color(0xFFE5E7EB),
                                shape = RoundedCornerShape(16.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        if (index < currentStep) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null,
                                tint = Color.White,
                                modifier = Modifier.size(16.dp)
                            )
                        } else {
                            Text(
                                text = "${index + 1}",
                                color = if (index <= currentStep) Color.White else Color.Gray,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                    
                    // 连接线
                    if (index < totalSteps - 1) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(2.dp)
                                .background(
                                    color = if (index < currentStep) Color(0xFF6366F1) else Color(0xFFE5E7EB)
                                )
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 当前步骤标题
            Text(
                text = stepLabels[currentStep],
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937)
            )
            
            Text(
                text = "第 ${currentStep + 1} 步，共 $totalSteps 步",
                fontSize = 14.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 模板选择步骤
 */
@Composable
fun TemplateSelectionStep(
    selectedTemplate: WizardTemplate?,
    onTemplateSelected: (WizardTemplate?) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            Text(
                text = "选择目标类型",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            Text(
                text = "从预设模板开始，或创建自定义目标",
                fontSize = 16.sp,
                color = Color.Gray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        // 自定义目标选项
        item {
            GoalTemplateCard(
                template = null,
                isSelected = selectedTemplate == null,
                onClick = { onTemplateSelected(null) }
            )
        }
        
        // 预设模板
        items(getGoalTemplates()) { template ->
            GoalTemplateCard(
                template = template,
                isSelected = selectedTemplate == template,
                onClick = { onTemplateSelected(template) }
            )
        }
    }
}

/**
 * 目标模板卡片
 */
@Composable
fun GoalTemplateCard(
    template: WizardTemplate?,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFFF0F9FF) else Color.White
        ),
        border = if (isSelected) BorderStroke(2.dp, Color(0xFF6366F1)) else null,
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = template?.color ?: Color(0xFF6366F1),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = template?.icon ?: Icons.Default.Add,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = template?.title ?: "自定义目标",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1F2937)
                )
                
                Text(
                    text = template?.description ?: "从零开始创建您的专属目标",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                template?.let {
                    Text(
                        text = "预计 ${it.estimatedDays} 天完成",
                        fontSize = 12.sp,
                        color = Color(0xFF6366F1),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
            
            // 选中指示器
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF6366F1),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 目标详情设置步骤
 */
@Composable
fun GoalDetailsStep(
    template: WizardTemplate?,
    title: String,
    description: String,
    selectedCategory: GoalCategory?,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onCategorySelected: (GoalCategory) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "设置目标详情",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }

        // 目标标题
        item {
            OutlinedTextField(
                value = title,
                onValueChange = onTitleChange,
                label = { Text("目标标题") },
                placeholder = { Text(template?.title ?: "输入您的目标") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF6366F1),
                    focusedLabelColor = Color(0xFF6366F1)
                )
            )
        }

        // 目标描述
        item {
            OutlinedTextField(
                value = description,
                onValueChange = onDescriptionChange,
                label = { Text("目标描述（可选）") },
                placeholder = { Text("详细描述您想要达成的目标") },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 3,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF6366F1),
                    focusedLabelColor = Color(0xFF6366F1)
                )
            )
        }

        // 目标分类
        item {
            Text(
                text = "选择分类",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(top = 8.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(getGoalCategories()) { category ->
                    CategoryChip(
                        category = category,
                        isSelected = selectedCategory == category,
                        onClick = { onCategorySelected(category) }
                    )
                }
            }
        }

        // 智能建议
        template?.let {
            item {
                SmartSuggestionCard(template = it)
            }
        }
    }
}

/**
 * 分类选择芯片
 */
@Composable
fun CategoryChip(
    category: GoalCategory,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) category.color else Color.White
        ),
        border = BorderStroke(1.dp, category.color),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 0.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = category.icon,
                contentDescription = null,
                tint = if (isSelected) Color.White else category.color,
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = category.name,
                color = if (isSelected) Color.White else category.color,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 智能建议卡片
 */
@Composable
fun SmartSuggestionCard(template: WizardTemplate) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFEF3C7)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = Icons.Default.Lightbulb,
                contentDescription = null,
                tint = Color(0xFFD97706),
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column {
                Text(
                    text = "智能建议",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF92400E)
                )

                Text(
                    text = template.suggestions.joinToString("\n• ", "• "),
                    fontSize = 14.sp,
                    color = Color(0xFF92400E),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

/**
 * 时间规划步骤
 */
@Composable
fun TimeframePlanningStep(
    selectedTimeframe: TimeFrame?,
    selectedDifficulty: Difficulty?,
    onTimeframeSelected: (TimeFrame) -> Unit,
    onDifficultySelected: (Difficulty) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "时间规划",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }

        // 时间框架选择
        item {
            Text(
                text = "目标时间框架",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                getTimeFrames().forEach { timeframe ->
                    TimeFrameCard(
                        timeframe = timeframe,
                        isSelected = selectedTimeframe == timeframe,
                        onClick = { onTimeframeSelected(timeframe) }
                    )
                }
            }
        }

        // 难度选择
        item {
            Text(
                text = "目标难度",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(top = 16.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                getDifficulties().forEach { difficulty ->
                    DifficultyChip(
                        difficulty = difficulty,
                        isSelected = selectedDifficulty == difficulty,
                        onClick = { onDifficultySelected(difficulty) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 时间框架卡片
 */
@Composable
fun TimeFrameCard(
    timeframe: TimeFrame,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) timeframe.color.copy(alpha = 0.1f) else Color.White
        ),
        border = if (isSelected) BorderStroke(2.dp, timeframe.color) else BorderStroke(1.dp, Color(0xFFE5E7EB)),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = timeframe.icon,
                contentDescription = null,
                tint = timeframe.color,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = timeframe.name,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937)
                )

                Text(
                    text = timeframe.description,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = timeframe.color,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 难度选择芯片
 */
@Composable
fun DifficultyChip(
    difficulty: Difficulty,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) difficulty.color else Color.White
        ),
        border = BorderStroke(1.dp, difficulty.color),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = difficulty.icon,
                contentDescription = null,
                tint = if (isSelected) Color.White else difficulty.color,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = difficulty.displayName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = if (isSelected) Color.White else difficulty.color,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 确认步骤
 */
@Composable
fun ConfirmationStep(
    template: WizardTemplate?,
    title: String,
    description: String,
    category: GoalCategory?,
    timeframe: TimeFrame?,
    difficulty: Difficulty?
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "确认创建",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937),
                modifier = Modifier.padding(vertical = 8.dp)
            )

            Text(
                text = "请确认您的目标设置",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }

        // 目标预览卡片
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    // 标题和分类
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        category?.let {
                            Icon(
                                imageVector = it.icon,
                                contentDescription = null,
                                tint = it.color,
                                modifier = Modifier.size(24.dp)
                            )

                            Spacer(modifier = Modifier.width(8.dp))
                        }

                        Text(
                            text = title.ifBlank { template?.title ?: "未命名目标" },
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1F2937)
                        )
                    }

                    if (description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = description,
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 目标详情
                    GoalDetailRow(
                        label = "分类",
                        value = category?.name ?: "未选择",
                        icon = Icons.Default.Category
                    )

                    timeframe?.let {
                        GoalDetailRow(
                            label = "时间框架",
                            value = it.name,
                            icon = Icons.Default.Schedule
                        )
                    }

                    difficulty?.let {
                        GoalDetailRow(
                            label = "难度",
                            value = it.displayName,
                            icon = Icons.Default.TrendingUp
                        )
                    }

                    template?.let {
                        GoalDetailRow(
                            label = "模板",
                            value = it.title,
                            icon = Icons.Default.Description
                        )
                    }
                }
            }
        }

        // 预期成果
        timeframe?.let { tf ->
            difficulty?.let { diff ->
                item {
                    ExpectedOutcomeCard(timeframe = tf, difficulty = diff)
                }
            }
        }
    }
}

/**
 * 目标详情行
 */
@Composable
fun GoalDetailRow(
    label: String,
    value: String,
    icon: ImageVector
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF6B7280),
            modifier = Modifier.size(16.dp)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF6B7280),
            modifier = Modifier.width(80.dp)
        )

        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF1F2937)
        )
    }
}

/**
 * 预期成果卡片
 */
@Composable
fun ExpectedOutcomeCard(
    timeframe: TimeFrame,
    difficulty: Difficulty
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF0FDF4)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.EmojiEvents,
                    contentDescription = null,
                    tint = Color(0xFF16A34A),
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "预期成果",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF16A34A)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            val successRate = when (difficulty) {
                Difficulty.EASY -> "85%"
                Difficulty.MEDIUM -> "70%"
                Difficulty.HARD -> "55%"
            }

            Text(
                text = "基于${timeframe.name}的时间框架和${difficulty.displayName}难度，预计成功率约为 $successRate",
                fontSize = 14.sp,
                color = Color(0xFF16A34A)
            )
        }
    }
}

/**
 * 向导导航按钮
 */
@Composable
fun WizardNavigationButtons(
    currentStep: Int,
    totalSteps: Int,
    canProceed: Boolean,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onFinish: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 上一步按钮
            if (currentStep > 0) {
                OutlinedButton(
                    onClick = onPrevious,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF6B7280)
                    ),
                    border = BorderStroke(1.dp, Color(0xFFE5E7EB))
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("上一步")
                }

                Spacer(modifier = Modifier.width(12.dp))
            }

            // 下一步/完成按钮
            Button(
                onClick = if (currentStep == totalSteps - 1) onFinish else onNext,
                enabled = canProceed,
                modifier = Modifier.weight(if (currentStep > 0) 1f else 1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF6366F1),
                    disabledContainerColor = Color(0xFFE5E7EB)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = if (currentStep == totalSteps - 1) "创建目标" else "下一步",
                    fontWeight = FontWeight.Medium
                )

                if (currentStep < totalSteps - 1) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 检查是否可以进入下一步
 */
fun canProceedToNextStep(
    currentStep: Int,
    selectedTemplate: WizardTemplate?,
    goalTitle: String,
    selectedCategory: GoalCategory?,
    selectedTimeframe: TimeFrame?
): Boolean {
    return when (currentStep) {
        0 -> true // 模板选择步骤总是可以继续
        1 -> goalTitle.isNotBlank() && selectedCategory != null // 需要标题和分类
        2 -> selectedTimeframe != null // 需要时间框架
        3 -> true // 确认步骤
        else -> false
    }
}

/**
 * 从向导创建目标
 */
fun createGoalFromWizard(
    viewModel: GoalViewModel,
    template: WizardTemplate?,
    title: String,
    description: String,
    category: GoalCategory?,
    timeframe: TimeFrame?,
    difficulty: Difficulty?
) {
    val goalTitle = title.ifBlank { template?.title ?: "新目标" }
    val goalDescription = description.ifBlank { template?.description ?: "" }
    val dueDate = timeframe?.let { LocalDateTime.now().plusDays(it.days.toLong()) }

    // 这里应该调用ViewModel的创建目标方法
    // 由于当前ViewModel可能没有这个方法，这里只是示例
    /*
    viewModel.createGoalFromWizard(
        title = goalTitle,
        description = goalDescription,
        category = category?.name ?: "其他",
        dueDate = dueDate,
        difficulty = difficulty?.displayName ?: "中等",
        template = template
    )
    */
}
