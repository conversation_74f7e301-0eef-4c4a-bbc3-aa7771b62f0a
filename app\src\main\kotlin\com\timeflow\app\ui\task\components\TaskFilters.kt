package com.timeflow.app.ui.task.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.automirrored.filled.Label
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.data.model.TaskType
import java.time.LocalDateTime

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun TaskFilters(
    viewModel: TaskListViewModel,
    onFilterChange: (Priority?, TaskType?) -> Unit
) {
    var showPriorityMenu by remember { mutableStateOf(false) }
    var showTagMenu by remember { mutableStateOf(false) }
    var showDateMenu by remember { mutableStateOf(false) }
    var showStatusMenu by remember { mutableStateOf(false) }
    
    val selectedTags = viewModel.selectedTags.collectAsState().value
    val priorityFilter = viewModel.priorityFilter.collectAsState().value
    val showCompleted = viewModel.showCompleted.collectAsState().value
    
    // 将Int?转换为Priority?的函数
    fun intToPriority(value: Int?): Priority? {
        return when(value) {
            1 -> Priority.LOW
            2 -> Priority.MEDIUM
            3 -> Priority.HIGH
            4 -> Priority.URGENT
            else -> null
        }
    }
    
    // 将Priority转换为Int的函数
    fun priorityToInt(priority: Priority?): Int? {
        return when(priority) {
            Priority.LOW -> 1
            Priority.MEDIUM -> 2
            Priority.HIGH -> 3
            Priority.URGENT -> 4
            null -> null
        }
    }
    
    // 获取当前的优先级过滤条件
    val currentPriorityFilter = intToPriority(priorityFilter)
    
    Surface(
        modifier = Modifier,
        shape = MaterialTheme.shapes.medium,
        tonalElevation = 1.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "筛选条件",
                style = MaterialTheme.typography.titleMedium
            )
            
            // 使用 FlowRow 实现横向自动换行布局
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                maxItemsInEachRow = Int.MAX_VALUE
            ) {
                // 优先级筛选
                FilterChip(
                    selected = currentPriorityFilter != null,
                    onClick = { showPriorityMenu = true },
                    label = { 
                        val priorityLabel = when (currentPriorityFilter) {
                            Priority.LOW -> "低优先级"
                            Priority.MEDIUM -> "中优先级"
                            Priority.HIGH -> "高优先级"
                            Priority.URGENT -> "紧急"
                            else -> "优先级"
                        }
                        Text(priorityLabel) 
                    },
                    leadingIcon = {
                        when (currentPriorityFilter) {
                            Priority.URGENT -> Text("⚡") // 紧急
                            Priority.HIGH -> Text("🔴") // 高
                            Priority.MEDIUM -> Text("🟡") // 中
                            Priority.LOW -> Text("🟢") // 低
                            else -> Text("⚪") // 无
                        }
                    }
                )
                
                // 标签筛选
                FilterChip(
                    selected = selectedTags.isNotEmpty(),
                    onClick = { showTagMenu = true },
                    label = { Text(if (selectedTags.isEmpty()) "标签" else "${selectedTags.size}个标签") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.Label,
                            contentDescription = "标签"
                        )
                    }
                )
                
                // 日期筛选
                FilterChip(
                    selected = false,
                    onClick = { showDateMenu = true },
                    label = { Text("日期") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = null
                        )
                    }
                )
                
                // 状态筛选
                FilterChip(
                    selected = false,
                    onClick = { showStatusMenu = true },
                    label = { Text("状态") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null
                        )
                    }
                )
                
                // 显示已完成开关
                Row(
                    modifier = Modifier
                        .height(32.dp) // 与FilterChip高度保持一致
                        .padding(start = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Switch(
                        checked = showCompleted,
                        onCheckedChange = { viewModel.setShowCompleted(it) },
                        thumbContent = {
                            Icon(
                                imageVector = if (showCompleted) 
                                    Icons.Default.Visibility 
                                else Icons.Default.VisibilityOff,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    )
                }
            }
        }
    }
    
    // 优先级菜单
    DropdownMenu(
        expanded = showPriorityMenu,
        onDismissRequest = { showPriorityMenu = false }
    ) {
        // 使用Priority枚举，并在内部转换成Int
        listOf(
            Triple(Priority.LOW, "低优先级", "🟢"),
            Triple(Priority.MEDIUM, "中优先级", "🟡"),
            Triple(Priority.HIGH, "高优先级", "🔴"),
            Triple(Priority.URGENT, "紧急", "⚡")
        ).forEach { (value, label, icon) ->
            DropdownMenuItem(
                text = { Text(label) },
                leadingIcon = { Text(icon) },
                onClick = {
                    viewModel.setPriorityFilter(priorityToInt(value))
                    onFilterChange(value, null)
                    showPriorityMenu = false
                }
            )
        }
        DropdownMenuItem(
            text = { Text("清除过滤器") },
            onClick = {
                viewModel.setPriorityFilter(null)
                onFilterChange(null, null)
                showPriorityMenu = false
            }
        )
    }
    
    // 标签菜单
    DropdownMenu(
        expanded = showTagMenu,
        onDismissRequest = { showTagMenu = false }
    ) {
        // TODO: 从数据库加载所有可用标签
        listOf(
            "工作", "生活", "学习", "健康", "家庭"
        ).forEach { tag ->
            DropdownMenuItem(
                text = { Text(tag) },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Label,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                },
                onClick = {
                    viewModel.setTagFilter(tag)
                    showTagMenu = false
                }
            )
        }
    }
    
    // 状态菜单
    DropdownMenu(
        expanded = showStatusMenu,
        onDismissRequest = { showStatusMenu = false }
    ) {
        listOf("待办", "进行中", "已完成").forEach { status ->
            DropdownMenuItem(
                text = { Text(status) },
                onClick = {
                    // TODO: 实现状态筛选
                    showStatusMenu = false
                }
            )
        }
    }
}
