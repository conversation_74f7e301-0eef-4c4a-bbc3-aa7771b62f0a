package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.ReflectionEntity
import kotlinx.coroutines.flow.Flow
import java.time.Instant

/**
 * 感想数据访问对象
 */
@Dao
interface ReflectionDao {
    
    /**
     * 获取所有感想，按日期倒序
     */
    @Query("SELECT * FROM reflections ORDER BY date DESC")
    suspend fun getAllReflections(): List<ReflectionEntity>
    
    /**
     * 获取所有感想的Flow，按日期倒序
     */
    @Query("SELECT * FROM reflections ORDER BY date DESC")
    fun getAllReflectionsFlow(): Flow<List<ReflectionEntity>>
    
    /**
     * 根据ID获取感想
     */
    @Query("SELECT * FROM reflections WHERE id = :reflectionId")
    suspend fun getReflectionById(reflectionId: String): ReflectionEntity?
    
    /**
     * 插入感想
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReflection(reflection: ReflectionEntity): Long
    
    /**
     * 更新感想
     */
    @Update
    suspend fun updateReflection(reflection: ReflectionEntity)
    
    /**
     * 删除感想
     */
    @Delete
    suspend fun deleteReflection(reflection: ReflectionEntity)
    
    /**
     * 根据ID删除感想
     */
    @Query("DELETE FROM reflections WHERE id = :reflectionId")
    suspend fun deleteReflectionById(reflectionId: String)
    
    /**
     * 按日期范围查询感想
     */
    @Query("SELECT * FROM reflections WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    suspend fun getReflectionsByDateRange(startDate: Long, endDate: Long): List<ReflectionEntity>
    
    /**
     * 根据类型获取感想
     */
    @Query("SELECT * FROM reflections WHERE type = :type ORDER BY date DESC")
    suspend fun getReflectionsByType(type: String): List<ReflectionEntity>
    
    /**
     * 根据心情获取感想
     */
    @Query("SELECT * FROM reflections WHERE mood = :mood ORDER BY date DESC")
    suspend fun getReflectionsByMood(mood: String): List<ReflectionEntity>
    
    /**
     * 搜索感想（标题或内容包含关键词）
     */
    @Query("SELECT * FROM reflections WHERE title LIKE '%' || :query || '%' OR content LIKE '%' || :query || '%' ORDER BY date DESC")
    suspend fun searchReflections(query: String): List<ReflectionEntity>
    
    /**
     * 获取最近的感想（限制数量）
     */
    @Query("SELECT * FROM reflections ORDER BY date DESC LIMIT :limit")
    suspend fun getRecentReflections(limit: Int): List<ReflectionEntity>
    
    /**
     * 获取任务完成相关的感想
     */
    @Query("SELECT * FROM reflections WHERE is_from_task_completion = 1 ORDER BY date DESC")
    suspend fun getTaskCompletionReflections(): List<ReflectionEntity>
    
    /**
     * 按任务ID查询感想
     */
    @Query("SELECT * FROM reflections WHERE task_id = :taskId ORDER BY date DESC")
    suspend fun getReflectionsByTaskId(taskId: String): List<ReflectionEntity>
    
    /**
     * 获取感想总数
     */
    @Query("SELECT COUNT(*) FROM reflections")
    suspend fun getReflectionCount(): Int
    
    /**
     * 根据标签搜索感想
     */
    @Query("SELECT * FROM reflections WHERE tags_json LIKE '%' || :tag || '%' ORDER BY date DESC")
    suspend fun getReflectionsByTag(tag: String): List<ReflectionEntity>
    
    /**
     * 获取所有使用过的标签
     */
    @Query("SELECT DISTINCT tags_json FROM reflections WHERE tags_json != '[]'")
    suspend fun getAllUsedTags(): List<String>
    
    /**
     * 清空所有感想数据
     */
    @Query("DELETE FROM reflections")
    suspend fun clearAllReflections()
} 