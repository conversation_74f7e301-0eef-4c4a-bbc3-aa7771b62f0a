package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity

/**
 * 专注计时小组件 - 显示正在专注的任务和计时状态
 * 使用时间追踪页面的颜色方案，支持正计时和番茄钟模式
 */
class QuickTimerWidget : AppWidgetProvider() {

    companion object {
        const val ACTION_START_TIMER = "com.timeflow.app.widget.START_TIMER"
        const val ACTION_STOP_TIMER = "com.timeflow.app.widget.STOP_TIMER"
        const val ACTION_TOGGLE_TIMER = "com.timeflow.app.widget.TOGGLE_TIMER"
        const val EXTRA_DURATION = "duration"

        // SharedPreferences名称和键值（与TimeTrackingViewModel保持一致）
        const val SHARED_PREFS_NAME = "time_tracking_prefs"
        const val PREF_TIMER_STATE = "timer_state"
        const val PREF_CURRENT_TASK_ID = "current_task_id"
        const val PREF_CURRENT_TASK_NAME = "current_task_name"
        const val PREF_ELAPSED_TIME = "elapsed_time"
        const val PREF_TIMER_START_TIME = "timer_start_time"
        const val PREF_TIMER_TYPE = "timer_type"
        const val PREF_POMODORO_COUNT = "pomodoro_count"
        const val PREF_POMODORO_TIME_REMAINING = "pomodoro_time_remaining"
        const val PREF_IS_BREAK_TIME = "is_break_time"
        const val PREF_CURRENT_SESSION_ID = "current_session_id"
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: android.os.Bundle?
    ) {
        updateAppWidget(context, appWidgetManager, appWidgetId)
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val options = appWidgetManager.getAppWidgetOptions(appWidgetId)
        val minWidth = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH)
        val minHeight = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT)

        // 根据尺寸选择布局
        val layoutId = when {
            minWidth < 200 || minHeight < 200 -> R.layout.widget_quick_timer_small
            else -> R.layout.widget_quick_timer
        }

        val views = RemoteViews(context.packageName, layoutId)

        // 根据布局设置内容
        if (layoutId == R.layout.widget_quick_timer_small) {
            setupSmallTimerWidget(context, views)
        } else {
            setupLargeTimerWidget(context, views)
        }

        // 设置主应用点击事件
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
        }
        val mainPendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_main_area, mainPendingIntent)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }

    private fun setupSmallTimerWidget(context: Context, views: RemoteViews) {
        // 获取当前计时器状态
        val timerState = getTimerState(context)

        // 设置任务名称
        views.setTextViewText(R.id.widget_task_name, timerState.taskName)

        // 设置计时器显示
        val timeText = formatTime(timerState.displayTime)
        views.setTextViewText(R.id.widget_timer_display, timeText)

        // 设置状态文本
        val statusText = when {
            timerState.isRunning && timerState.timerType == "POMODORO" -> {
                if (timerState.isBreakTime) "休息中" else "番茄钟"
            }
            timerState.isRunning -> "正计时"
            timerState.displayTime > 0 -> "已暂停"
            else -> "未开始"
        }
        views.setTextViewText(R.id.widget_timer_status, statusText)

        // 设置播放/暂停按钮图标
        val playPauseIcon = if (timerState.isRunning) R.drawable.ic_pause else R.drawable.ic_play_arrow
        views.setImageViewResource(R.id.widget_play_pause_button, playPauseIcon)

        // 设置播放/暂停按钮点击事件
        val playPauseIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            putExtra("action", if (timerState.isRunning) "pause_timer" else "resume_timer")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val playPausePendingIntent = PendingIntent.getActivity(
            context, 1, playPauseIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_play_pause_button, playPausePendingIntent)

        // 设置停止按钮点击事件
        try {
            val stopIntent = Intent(context, MainActivity::class.java).apply {
                putExtra("navigate_to", "timer")
                putExtra("action", "stop_timer")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            val stopPendingIntent = PendingIntent.getActivity(
                context, 2, stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_stop_button, stopPendingIntent)
        } catch (e: Exception) {
            // 忽略，某些布局可能没有停止按钮
        }

        // 设置整个小组件点击跳转到时间追踪页面
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val mainPendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, mainPendingIntent)
    }

    private fun getTimerState(context: Context): TimerWidgetState {
        // 使用与TimeTrackingViewModel相同的SharedPreferences
        val prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE)

        // 读取计时器状态
        val timerStateStr = prefs.getString(PREF_TIMER_STATE, "IDLE") ?: "IDLE"
        val isRunning = timerStateStr == "RUNNING"
        val taskId = prefs.getString(PREF_CURRENT_TASK_ID, null)
        val startTime = prefs.getLong(PREF_TIMER_START_TIME, 0L)
        val elapsedTime = prefs.getLong(PREF_ELAPSED_TIME, 0L)
        val timerType = prefs.getString(PREF_TIMER_TYPE, "NORMAL") ?: "NORMAL"
        val pomodoroCount = prefs.getInt(PREF_POMODORO_COUNT, 0)
        val pomodoroTimeRemaining = prefs.getLong(PREF_POMODORO_TIME_REMAINING, 25 * 60L)
        val isBreakTime = prefs.getBoolean(PREF_IS_BREAK_TIME, false)

        // 获取任务名称（从SharedPreferences直接读取）
        val taskName = prefs.getString(PREF_CURRENT_TASK_NAME, "专注任务") ?: "专注任务"

        // 计算显示时间 - 🔧 修复：优化时间计算逻辑，与主应用保持同步
        val displayTime = when (timerType) {
            "POMODORO" -> {
                if (isBreakTime) {
                    // 休息时间显示剩余休息时间
                    pomodoroTimeRemaining
                } else {
                    // 工作时间显示剩余工作时间
                    pomodoroTimeRemaining
                }
            }
            else -> {
                // 正计时模式显示已用时间
                if (isRunning && startTime > 0) {
                    // 🔧 修复：使用更精确的时间计算，考虑到主应用的更新频率
                    val currentTime = System.currentTimeMillis()
                    val timeSinceStart = (currentTime - startTime) / 1000

                    // 使用保存的elapsedTime作为基准，加上从最后一次保存到现在的时间差
                    // 但限制额外时间不超过5秒（主应用的保存间隔）
                    val additionalTime = minOf(timeSinceStart - elapsedTime, 5L)
                    elapsedTime + maxOf(additionalTime, 0L)
                } else {
                    elapsedTime
                }
            }
        }

        return TimerWidgetState(
            isRunning = isRunning,
            taskName = taskName,
            taskId = taskId,
            elapsedTime = elapsedTime,
            displayTime = displayTime,
            timerType = timerType,
            pomodoroCount = pomodoroCount,
            pomodoroGoal = 4,
            isBreakTime = isBreakTime
        )
    }

    private fun formatTime(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60

        return if (hours > 0) {
            String.format("%d:%02d:%02d", hours, minutes, secs)
        } else {
            String.format("%02d:%02d", minutes, secs)
        }
    }

    private fun setupLargeTimerWidget(context: Context, views: RemoteViews) {
        // 大尺寸使用原有逻辑
        views.setTextViewText(R.id.widget_title, "快速计时")
        setupTimerButtons(context, views)
    }

    private fun setupTimerButtons(context: Context, views: RemoteViews) {
        // 25分钟专注
        val intent25 = Intent(context, QuickTimerWidget::class.java).apply {
            action = ACTION_START_TIMER
            putExtra(EXTRA_DURATION, 25)
        }
        val pendingIntent25 = PendingIntent.getBroadcast(
            context, 1, intent25,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_timer_25, pendingIntent25)
        
        // 45分钟专注
        val intent45 = Intent(context, QuickTimerWidget::class.java).apply {
            action = ACTION_START_TIMER
            putExtra(EXTRA_DURATION, 45)
        }
        val pendingIntent45 = PendingIntent.getBroadcast(
            context, 2, intent45,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_timer_45, pendingIntent45)
        
        // 60分钟专注
        val intent60 = Intent(context, QuickTimerWidget::class.java).apply {
            action = ACTION_START_TIMER
            putExtra(EXTRA_DURATION, 60)
        }
        val pendingIntent60 = PendingIntent.getBroadcast(
            context, 3, intent60,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_timer_60, pendingIntent60)
        
        // 自定义时间
        val intentCustom = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            putExtra("action", "custom_timer")
        }
        val pendingIntentCustom = PendingIntent.getActivity(
            context, 4, intentCustom,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_timer_custom, pendingIntentCustom)
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_START_TIMER -> {
                val duration = intent.getIntExtra(EXTRA_DURATION, 25)
                startTimer(context, duration)
            }
            ACTION_STOP_TIMER -> {
                stopTimer(context)
            }
        }
    }
    
    private fun startTimer(context: Context, durationMinutes: Int) {
        // 打开主应用到计时页面并启动计时
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            putExtra("auto_start_timer", true)
            putExtra("timer_duration", durationMinutes)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(mainIntent)
    }

    private fun stopTimer(context: Context) {
        // 打开主应用到计时页面并停止计时
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            putExtra("stop_timer", true)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(mainIntent)
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
    }
}

/**
 * 计时器小组件状态数据类
 */
data class TimerWidgetState(
    val isRunning: Boolean,
    val taskName: String,
    val taskId: String?,
    val elapsedTime: Long,
    val displayTime: Long,
    val timerType: String,
    val pomodoroCount: Int,
    val pomodoroGoal: Int,
    val isBreakTime: Boolean
)
