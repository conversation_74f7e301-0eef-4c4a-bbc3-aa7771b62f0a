package com.timeflow.app.ui.theme

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow

// Primary Purple Colors
val Purple10 = Color(0xFF330033)
val Purple20 = Color(0xFF440044)
val Purple30 = Color(0xFF5C1E69)
val Purple40 = Color(0xFF6650a4)
val Purple50 = Color(0xFF8F579F)
val Purple60 = Color(0xFFA370B5)
val Purple70 = Color(0xFFB98ACA)
val Purple80 = Color(0xFFD0BCFF)
val Purple90 = Color(0xFFE8D1F2)
val Purple95 = Color(0xFFF3E8F8)

// Secondary Lavender Colors
val Lavender10 = Color(0xFF2D293E)
val Lavender20 = Color(0xFF3F3A54)
val Lavender30 = Color(0xFF514B6B)
val Lavender40 = Color(0xFF625b71)
val Lavender50 = Color(0xFF7C7399)
val Lavender60 = Color(0xFF948CB0)
val Lavender70 = Color(0xFFADA6C7)
val Lavender80 = Color(0xFFCCC2DC)
val Lavender90 = Color(0xFFE0DCF0)
val Lavender95 = Color(0xFFEFEDF8)

// Tertiary Mint Colors
val Mint10 = Color(0xFF002022)
val Mint20 = Color(0xFF003739)
val Mint30 = Color(0xFF004F52)
val Mint40 = Color(0xFF00676B)
val Mint50 = Color(0xFF008085)
val Mint60 = Color(0xFF2C999E)
val Mint70 = Color(0xFF58B2B7)
val Mint80 = Color(0xFF84CCD0)
val Mint90 = Color(0xFFB0E5E8)
val Mint95 = Color(0xFFD8F2F4)

// Error Red Colors
val Red10 = Color(0xFF410001)
val Red20 = Color(0xFF680003)
val Red30 = Color(0xFF930006)
val Red40 = Color(0xFFBA1B1B)
val Red80 = Color(0xFFFFB4A9)
val Red90 = Color(0xFFFFDAD4)
val Red95 = Color(0xFFFFEDEA)

// Neutral Colors
val Grey10 = Color(0xFF1A1A1A)
val Grey20 = Color(0xFF333333)
val Grey30 = Color(0xFF4D4D4D)
val Grey40 = Color(0xFF666666)
val Grey50 = Color(0xFF808080)
val Grey60 = Color(0xFF999999)
val Grey70 = Color(0xFFB3B3B3)
val Grey80 = Color(0xFFCCCCCC)
val Grey90 = Color(0xFFE6E6E6)
val Grey95 = Color(0xFFF2F2F2)

// Additional Colors for indicators
val GreenSuccess = Color(0xFF36B37E)
val YellowWarning = Color(0xFFFFC400)
val BlueInfo = Color(0xFF2684FF)

// 主色 - 晨雾紫（温柔核心）
val MistyPurple = Color(0xFFB49EC9).copy(alpha = 0.92f)  // 带灰调的粉紫
val MistyPurpleLight = MistyPurple.copy(alpha = 0.7f)  // 浅色状态
val MistyPurpleDark = Color(0xFF9E8AAE)               // 深色模式对应色

// 中性色 - 米白体系（降低对比度）
val MilkyWhite = Color(0xFFFFFFFF).copy(alpha = 0.95f) // 卡片背景
val MistyGray = Color(0xFFF8F6F2)                     // 全局背景（原保留）
val SoftGray = Color(0xFFEDE7E0)                      // 分割线/弱元素
val TextPrimary = Color(0xFF1A1A1A)                   // 主文本颜色 - 从0xFF222222改为0xFF1A1A1A
val TextSecondary = Color(0xFF444444)                 // 次要文本颜色 - 新增

// 强调色 - 晨露黄（点睛之笔）
val DewYellow = Color(0xFFFFD7A4)                     // 重要提醒色

// 原Material 3调色板（保留但不作为主要使用）
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)
val Pink40 = Color(0xFF7D5260)
val PurpleGrey40 = Color(0xFF625b71)

// 莫兰迪色系 - 温柔知性配色
val MistyRose = Color(0xFFF3E9E5)      // 主背景-玫瑰米白
val LavenderAsh = Color(0xFFB0A8B9).copy(alpha = 0.9f)  // 未选中文字-灰紫，增加透明度
val DustyLavender = Color(0xFF8F88AA)  // 选中状态-粉灰紫
val VintageWhite = Color(0xFFFDF6F0)   // 卡片背景-古董白

// 背景和卡片颜色
val LightSand = Color(0xFFF9F5F2)          // 浅沙白 - 全局背景色
val VeryLightSand = Color(0xFFFCF9F5)      // 极浅沙色 - 更浅淡的全局背景色
val AppBackground = Color(0xFFFFFFFF)      // 改为纯白色背景，去除淡绿色
val TabTextColor = Color(0xFF3C2E3D)       // 标签文字色rgb(59, 85, 64)
val TranslucentWhite = Color.White.copy(alpha = 0.95f)  // 半透明白色 - 卡片背景色

// 新增清新主题色系 - 参考图2-5
val FreshBackground = Color(0xFFF8F8F5)    // 主背景色 - 米白色调
val FreshCardBg = Color(0xFFFFFFFF)        // 卡片背景 - 白色
val FreshPrimary = Color(0xFFCAD3C1)       // 主色调 - 浅绿色
val FreshSecondary = Color(0xFFE8EBE4)     // 次要色 - 更浅的绿灰色
val FreshTextPrimary = Color(0xFF333333)   // 主要文本 - 深灰色
val FreshTextSecondary = Color(0xFF666666) // 次要文本 - 中灰色
val FreshTextTertiary = Color(0xFF999999)  // 第三级文本 - 浅灰色
val FreshDivider = Color(0xFFEEEEEE)       // 分割线 - 极浅灰色

// 优先级指示色 - 更清新的色调
val PriorityLow = Color(0xFFE0F2F1)        // 低优先级 - 薄荷绿背景
val PriorityLowText = Color(0xFF26A69A)    // 低优先级文本 - 深薄荷绿
val PriorityMedium = Color(0xFFFFF8E1)     // 中优先级 - 浅橙色背景
val PriorityMediumText = Color(0xFFFFA000) // 中优先级文本 - 橙色
val PriorityHigh = Color(0xFFFFEBEE)       // 高优先级 - 珊瑚色背景
val PriorityHighText = Color(0xFFE57373)   // 高优先级文本 - 珊瑚红
val PriorityUrgent = Color(0xFFFFDDDD)     // 紧急优先级 - 浅红色背景
val PriorityUrgentText = Color(0xFFE53935) // 紧急优先级文本 - 红色

// 添加缺少的颜色常量
val BackgroundCompleted = Color(0xFFF0F0F5)
val BackgroundDefault = Color(0xFFFAFAFA)

/**
 * 计算两个颜色之间的对比度比率
 * 符合WCAG 2.0标准的对比度计算公式
 * 
 * @param foreground 前景色（通常是文本颜色）
 * @param background 背景色
 * @return 对比度比率，WCAG要求：AA级需要4.5:1，大文本AA级需要3:1
 */
fun calculateContrastRatio(foreground: Color, background: Color): Float {
    val lum1 = foreground.luminance() + 0.05f
    val lum2 = background.luminance() + 0.05f
    return max(lum1, lum2) / min(lum1, lum2)
}

/**
 * 使颜色变亮一定程度
 * 
 * @param factor 增亮因子，范围在0~1之间
 * @return 变亮后的颜色
 */
fun Color.lighten(factor: Float): Color {
    val hsv = FloatArray(3)
    android.graphics.Color.colorToHSV(
        android.graphics.Color.rgb(
            (red * 255).toInt(),
            (green * 255).toInt(),
            (blue * 255).toInt()
        ),
        hsv
    )
    hsv[2] = min(hsv[2] * (1f + factor), 1f)
    val lightened = android.graphics.Color.HSVToColor(hsv)
    return Color(lightened)
} 