package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.entity.SymptomRecord
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * 生理周期相关数据访问接口
 */
@Dao
interface CycleDao {
    // 周期记录相关查询
    @Query("SELECT * FROM cycle_records ORDER BY startDate DESC")
    fun getAllCycles(): Flow<List<CycleRecord>>
    
    @Query("SELECT * FROM cycle_records WHERE id = :id")
    suspend fun getCycleById(id: Long): CycleRecord?
    
    @Query("SELECT * FROM cycle_records WHERE startDate <= :date AND (endDate IS NULL OR endDate >= :date) LIMIT 1")
    suspend fun getCycleByDate(date: LocalDate): CycleRecord?
    
    @Query("SELECT * FROM cycle_records ORDER BY startDate DESC LIMIT 1")
    suspend fun getLatestCycle(): CycleRecord?
    
    @Insert
    suspend fun insertCycle(cycle: CycleRecord): Long
    
    @Update
    suspend fun updateCycle(cycle: CycleRecord)
    
    @Delete
    suspend fun deleteCycle(cycle: CycleRecord)
    
    // 症状记录相关查询
    @Query("SELECT * FROM symptoms WHERE date = :date")
    fun getSymptomsByDate(date: LocalDate): Flow<List<SymptomRecord>>
    
    @Query("SELECT * FROM symptoms ORDER BY date DESC")
    fun getAllSymptoms(): Flow<List<SymptomRecord>>
    
    @Insert
    suspend fun insertSymptom(symptom: SymptomRecord): Long
    
    @Update
    suspend fun updateSymptom(symptom: SymptomRecord)
    
    @Delete
    suspend fun deleteSymptom(symptom: SymptomRecord)
    
    @Query("DELETE FROM symptoms WHERE date = :date")
    suspend fun deleteSymptomsByDate(date: LocalDate)
} 