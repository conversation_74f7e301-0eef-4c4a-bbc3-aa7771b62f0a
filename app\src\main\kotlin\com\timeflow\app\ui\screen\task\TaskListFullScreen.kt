package com.timeflow.app.ui.screen.task

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.core.tween
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.regex.Pattern
import kotlin.random.Random
import kotlin.math.max
import kotlin.math.min
import kotlin.math.abs
import kotlinx.coroutines.*
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.flow.launchIn
import androidx.compose.ui.graphics.graphicsLayer

// 添加导入ThemeManager以访问主题颜色
import com.timeflow.app.ui.theme.ThemeManager
import com.timeflow.app.ui.theme.getPageBackgroundColor

// 添加SwipeToDismiss相关导入
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FractionalThreshold
import androidx.compose.material.SwipeToDismiss
import androidx.compose.material.rememberDismissState
import androidx.compose.material.Scaffold
import androidx.compose.material.rememberScaffoldState
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarResult
import androidx.compose.material.SnackbarDuration

// 添加编辑任务标题相关导入
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable

// 添加TaskTimeManager导入
import com.timeflow.app.ui.screen.task.TaskTimeManager
import com.timeflow.app.ui.screen.task.TaskTimeUpdateEvent

// 添加新的TaskTimeViewModel导入
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel
import com.timeflow.app.ui.screen.task.convertModelTaskDataToUITaskData
import com.timeflow.app.ui.screen.task.convertUITaskDataToModelTaskData
import com.timeflow.app.ui.screen.task.transformUIToModelSubTask

// 添加NotificationCenter和事件相关导入
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.ui.screen.task.SubTask as UISubTask
import com.timeflow.app.util.TaskRefreshEvent
import com.timeflow.app.util.NewTaskAddedEvent

// 添加手势检测和动画相关导入
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.graphics.Canvas

// Add NoRippleInteractionSource implementation
private object NoRippleInteractionSource : MutableInteractionSource {
    override val interactions: Flow<Interaction> = emptyFlow()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction): Boolean = false
}

// 在导入部分添加下拉刷新相关导入

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class, ExperimentalComposeApi::class, ExperimentalFoundationApi::class, ExperimentalTypeConversion::class, ExperimentalMaterialApi::class)
@Composable
fun TaskListFullScreen(
    navController: NavController,
    viewModel: TaskListViewModel = hiltViewModel(),
    taskTimeViewModel: TaskTimeViewModel = hiltViewModel() // 使用新的TaskTimeViewModel
) {
    // 获取View和Activity，用于设置窗口属性
    val view = LocalView.current
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 创建TaskTimeManager实例
    val taskTimeManager = remember { TaskTimeManager() }
    
    // 添加缺失的状态变量
    val scaffoldState = rememberScaffoldState()
    var isShowingLoadingDialog by remember { mutableStateOf(false) }
    var loadingMessage by remember { mutableStateOf<String?>(null) }
    var snackbarMessage by remember { mutableStateOf<String?>(null) }
    
    // 🎨 添加左右滑动手势状态
    var horizontalDragOffset by remember { mutableStateOf(0f) }
    val swipeThreshold = 100f // 滑动阈值（像素）
    var showSwipeHint by remember { mutableStateOf(false) }
    var swipeDirection by remember { mutableStateOf("") }
    
    // 🎨 获取触觉反馈实例
    val hapticFeedback = LocalHapticFeedback.current
    
    // 使用更安全的状态栏实现 - 移除 SideEffect
    /*
    SideEffect {
        activity?.let { 
            SystemBarManager.setupTaskPageSystemBars(it)
        }
    }
    */
    
    // activity?.let { act -> // 移除 DisposableEffect
    //     DisposableEffect(key1 = Unit) {
    //         val window = act.window
    //         
    //         // 不再修改decorFitsSystemWindows，使用SystemBarManager的标准设置
    //         
    //         // 保存原始值以在dispose时恢复
    //         val originalStatusBarColor = window.statusBarColor
    //         
    //         // 应用任务页面系统栏设置
    //         SystemBarManager.setupTaskPageSystemBars(act)
    //         
    //         onDispose {
    //             // 恢复原始状态栏颜色
    //             window.statusBarColor = originalStatusBarColor
    //             Log.d("TaskListFullScreen", "TaskListFullScreen disposed")
    //         }
    //     }
    // }

    var selectedFilter by remember { mutableStateOf("今天") }
    val filters = listOf("今天", "明天", "本周", "未定期", "全部")
    
    // 🎨 标签切换函数 - 在变量声明之后
    fun switchToNextTab() {
        val currentIndex = filters.indexOf(selectedFilter)
        if (currentIndex < filters.size - 1) {
            val newFilter = filters[currentIndex + 1]
            selectedFilter = newFilter
            viewModel.updateSharedFilterState(newFilter)
            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress) // 触觉反馈
            Log.d("TabSwipe", "向右滑动切换到: $newFilter")
        }
    }
    
    fun switchToPreviousTab() {
        val currentIndex = filters.indexOf(selectedFilter)
        if (currentIndex > 0) {
            val newFilter = filters[currentIndex - 1]
            selectedFilter = newFilter
            viewModel.updateSharedFilterState(newFilter)
            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress) // 触觉反馈
            Log.d("TabSwipe", "向左滑动切换到: $newFilter")
        }
    }
    
    // 添加任务完成状态筛选模式 - 默认隐藏已完成任务
    var completionFilterMode by remember { mutableStateOf(CompletionFilterMode.HIDE_COMPLETED) }
    // 🎯 修改默认排序方式为紧急程度降序
    var sortMode by remember { mutableStateOf(TaskSortMode.PRIORITY_HIGH_TO_LOW) }
    // 添加下拉菜单显示状态
    var showMenu by remember { mutableStateOf(false) }
    
    // 🔄 添加完成撤销功能状态管理
    var completedTasks by remember { mutableStateOf<Map<String, UndoTaskInfo>>(emptyMap()) }
    var showUndoSnackbar by remember { mutableStateOf(false) }
    var currentUndoTask by remember { mutableStateOf<UndoTaskInfo?>(null) }
    
    // 🎨 Done List 模式切换状态
    var isDoneListMode by remember { mutableStateOf(false) }
    var doneListAnimationTrigger by remember { mutableStateOf(0) }
    
    // 🔄 撤销计时器管理器
    val undoTimer = remember {
        UndoTimer { taskId ->
            // 30秒后自动移除撤销选项
            Log.d("UndoTimer", "撤销时间已过期，移除任务: $taskId")
            completedTasks = completedTasks.toMutableMap().apply { remove(taskId) }
            if (currentUndoTask?.taskId == taskId) {
                currentUndoTask = null
                showUndoSnackbar = false
            }
        }
    }
    
    // 🔄 确保组件销毁时取消所有计时器
    DisposableEffect(Unit) {
        onDispose {
            undoTimer.cancelAllTimers()
            Log.d("UndoTimer", "TaskListFullScreen销毁，已取消所有撤销计时器")
        }
    }
    
    // 底部弹出层状态
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false)
    var showBottomSheet by remember { mutableStateOf(false) }
    var selectedTask by remember { mutableStateOf<ModelTaskData?>(null) }

    // 直接从ViewModel获取任务列表状态
    val taskListState by viewModel.taskListState.collectAsState()
    // 使用MutableStateList以便能够修改列表内容 - REMOVED
    // val taskList = remember { mutableStateListOf<ModelTaskData>() }
    
    // 添加加载状态 - 从ViewModel状态获取
    // var isLoading by remember { mutableStateOf(true) } - REMOVED, use taskListState.isLoading
    val coroutineScope = rememberCoroutineScope()
    
    // 从ThemeManager获取背景色 - 修复不正确的单例获取方法
    // 直接使用ThemeManager对象，无需调用getInstance()
    // 监听主题偏好变化，确保主题更改时界面会重组
    val themePreference = ThemeManager.userThemePreference.collectAsState()
    // 使用getPageBackgroundColor获取页面专属背景色，确保与主页保持一致
    val backgroundColor = getPageBackgroundColor("home")
    
    // 添加页面进入动画状态
    var pageAnimationStarted by remember { mutableStateOf(false) }
    var showContent by remember { mutableStateOf(false) }
    
    // 🎯 最大化简化页面进入动画，避免抖动
    LaunchedEffect(Unit) {
        pageAnimationStarted = true
        delay(10) // 🎯 最小延迟，几乎立即显示
        showContent = true
    }
    
    // 添加内容透明度动画
    // 🎯 简化内容透明度动画
    val contentAlpha by animateFloatAsState(
        targetValue = if (showContent) 1f else 0f,
        animationSpec = tween(durationMillis = 200, easing = LinearEasing), // 🎯 减少动画时长，使用线性缓动
        label = "contentAlpha"
    )
        
    // 添加列表项进入动画的延迟状态
    var itemAnimationTrigger by remember { mutableStateOf(0) }
    
    // 💡 优化刷新机制 - 添加防抖和微动画
    // 刷新防抖控制
    var lastRefreshTime by remember { mutableStateOf(0L) }
    var refreshDebounceJob: Job? by remember { mutableStateOf(null) }
    val minimumRefreshInterval = 1000L // 最小刷新间隔 1秒
    
    // 🎯 进一步简化列表透明度动画，避免抖动
    val listAlpha = animateFloatAsState(
        targetValue = 1f, // 🎯 直接设为1f，移除加载状态的透明度变化
        animationSpec = tween(100, easing = LinearEasing), // 🎯 最小化动画时长
        label = "listAlpha"
    )
    
    // 💡 自定义下拉刷新状态管理 - 参考知名应用设计
    var pullOffset by remember { mutableStateOf(0f) }
    var isRefreshing by remember { mutableStateOf(false) }
    val maxPullDistance = 120.dp
    val refreshTriggerDistance = 80.dp
    
    // 优雅的刷新动画状态
    val refreshIndicatorAlpha by animateFloatAsState(
        targetValue = if (isRefreshing || pullOffset > 0f) 1f else 0f,
        animationSpec = tween(300, easing = FastOutSlowInEasing),
        label = "refreshIndicatorAlpha"
    )
    
    // 刷新指示器偏移动画
    val indicatorOffset by animateFloatAsState(
        targetValue = if (isRefreshing) refreshTriggerDistance.value else pullOffset.coerceAtMost(maxPullDistance.value),
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "indicatorOffset"
    )
    
    // 🎯 移除复杂的缩放动画，保持界面稳定
    // val contentScale by animateFloatAsState(...)  - 已移除
    
    // 🎯 简化背景透明度动画
    val backgroundAlpha by animateFloatAsState(
        targetValue = 1f, // 🎯 直接设为1f，移除不必要的动画
        animationSpec = tween(100, easing = LinearEasing),
        label = "backgroundAlpha"
    )
    
    // 💡 增强智能防抖刷新机制
    val smartRefresh: (String) -> Unit = { reason ->
        val currentTime = System.currentTimeMillis()
        
        // 🔧 增强防抖逻辑 - 增加到3秒间隔，减少刷新频次
        if (currentTime - lastRefreshTime >= 3000L) { // 从1.5秒增加到3秒
            lastRefreshTime = currentTime
            
            Log.d("SmartRefresh", "[TaskListFullScreen] 🚀 执行智能刷新: $reason")
            Log.d("SmartRefresh", "[TaskListFullScreen] 距离上次刷新间隔: ${currentTime - (lastRefreshTime - 3000L)}ms")
            
            // 🔧 使用协程避免阻塞UI
            coroutineScope.launch {
                try {
                    // 增加延迟，避免与其他操作冲突
                    delay(200) // 从100ms增加到200ms
                    viewModel.refreshTasks()
                    Log.d("SmartRefresh", "[TaskListFullScreen] ✓ 智能刷新完成")
                } catch (e: Exception) {
                    Log.e("SmartRefresh", "[TaskListFullScreen] 智能刷新失败", e)
                }
            }
        } else {
            val remainingTime = 3000L - (currentTime - lastRefreshTime) // 更新计算
            Log.d("SmartRefresh", "[TaskListFullScreen] ⏳ 防抖中，还需等待 ${remainingTime}ms")
            Log.d("SmartRefresh", "[TaskListFullScreen] 忽略刷新请求: $reason")
        }
    }
    
    // 监听刷新完成
    LaunchedEffect(taskListState.isLoading) {
        if (!taskListState.isLoading && isRefreshing) {
            delay(300) // 让用户看到刷新完成的反馈
            isRefreshing = false
            pullOffset = 0f
        }
    }

    // 💡 优雅的下拉刷新处理函数
    fun onRefresh() {
        smartRefresh("用户下拉刷新")
    }

    // 🎯 简化页面初始化，减少抖动
    LaunchedEffect(Unit) {
        Log.d("TaskListFullScreen", "===== TaskListFullScreen初始化 =====")
        
        // 🎯 简化初始化流程，避免多次刷新导致抖动
        viewModel.fixTaskCompletionStatus()
        
        // 🎯 减少延迟，直接同步过滤状态
        Log.d("TaskListFullScreen", "同步共享过滤状态...")
        viewModel.updateSharedFilterState(selectedFilter)
        
        Log.d("TaskListFullScreen", "✓ 初始化完成")
    }
    
    // 🔧 新增：页面重新获得焦点时刷新数据
    LaunchedEffect(navController.currentBackStackEntry) {
        // 当页面重新成为当前页面时，强制刷新数据
        Log.d("TaskListFullScreen", "页面重新获得焦点，强制刷新数据...")
        delay(100) // 短暂延迟确保页面完全加载
        
        // 🔧 清理过期的待删除任务
        viewModel.cleanupExpiredPendingDeletions()
        
        // 刷新任务列表
        viewModel.refreshTasks()
    }
    
    // 🔧 新增：监听待删除任务状态变化，确保UI实时更新
    val pendingDeletions by viewModel.pendingDeletions.collectAsState()
    LaunchedEffect(pendingDeletions.size) {
        if (pendingDeletions.isNotEmpty()) {
            Log.d("TaskListFullScreen", "检测到待删除任务变化，当前待删除任务数: ${pendingDeletions.size}")
            // 当有任务被标记为删除时，立即触发UI更新
            delay(50) // 短暂延迟确保状态已更新
        }
        
        // 当任务加载完成后触发列表项动画
        launch {
            viewModel.taskListState.collect { state ->
                Log.d("TaskListFullScreen", "收到任务列表状态更新: isLoading=${state.isLoading}, 任务数量=${state.tasks.size}")
                if (!state.isLoading && state.tasks.isNotEmpty()) {
                    // 🎯 大幅减少延迟，避免抖动
                    delay(20) // 🎯 最小化延迟，快速响应
                    itemAnimationTrigger = state.tasks.size
                }
            }
        }
        
        // 🎯 关键修复：监听共享过滤状态变化
        launch {
            viewModel.filterState.collect { sharedFilter ->
                Log.d("TaskListFullScreen", "收到共享过滤状态变化: $sharedFilter")
                // 如果共享状态与本地状态不一致，同步本地状态
                if (sharedFilter != selectedFilter && sharedFilter in filters) {
                    Log.d("TaskListFullScreen", "同步本地过滤状态: $selectedFilter -> $sharedFilter")
                    selectedFilter = sharedFilter
                }
            }
        }

        // 🎯 进一步优化定时刷新，避免抖动
        while(true) {
            delay(120000) // 🎯 改为2分钟，大幅减少刷新频率
            Log.d("TaskListFullScreen", "执行定时刷新")
            smartRefresh("定时刷新")
        }
    }

    // 优化列表渲染性能
    val listState = rememberLazyListState()
    
    // 🎨 获取已完成任务列表 - 用于Done List模式
    val completedTasksList by remember(taskListState.tasks, isDoneListMode) {
        derivedStateOf {
            if (!isDoneListMode) return@derivedStateOf emptyList<ModelTaskData>()
            
            val completedTasks = taskListState.tasks.filter { task -> 
                task.isCompleted && task.displayInTaskList
            }.sortedByDescending { task ->
                // 按完成时间降序排列，如果没有completedAt则使用当前时间
                task.completedAt ?: System.currentTimeMillis()
            }
            
            Log.d("DoneList", "已完成任务数量: ${completedTasks.size}")
            completedTasks
        }
    }
    
    // 使用 derivedStateOf 优化过滤和排序计算 - 直接使用 taskListState.tasks
    // 🔧 确保pendingDeletions状态被正确监听
    val filteredTasks by remember(selectedFilter, taskListState.tasks, completionFilterMode, sortMode, isDoneListMode, pendingDeletions) {
        derivedStateOf {
            // 🎨 Done List模式下返回空列表，使用completedTasksList
            if (isDoneListMode) return@derivedStateOf emptyList<ModelTaskData>()
            
            // 添加日志记录初始任务数量
            val initialTaskCount = taskListState.tasks.size
            Log.d("TaskListFullScreen", "过滤前任务总数: $initialTaskCount")
            
            // 🔧 第一步过滤：排除待删除的任务
            val tasksAfterDeletionFilter = taskListState.tasks.filter { task ->
                val isPendingDeletion = pendingDeletions.containsKey(task.id)
                if (isPendingDeletion) {
                    Log.d("TaskListFullScreen", "✂️ 任务 ${task.id} (${task.title}) 被待删除状态过滤掉")
                }
                !isPendingDeletion
            }
            
            Log.d("TaskListFullScreen", "🔍 待删除过滤: 原始${taskListState.tasks.size}个 -> 过滤后${tasksAfterDeletionFilter.size}个任务")
            Log.d("TaskListFullScreen", "🗑️ 当前待删除任务数: ${pendingDeletions.size}")
            if (pendingDeletions.isNotEmpty()) {
                pendingDeletions.keys.forEach { taskId ->
                    Log.d("TaskListFullScreen", "   - 待删除任务ID: $taskId")
                }
            }
            
            // 检查是否有AI生成的任务
            val aiTasks = tasksAfterDeletionFilter.filter { task -> 
                task.aiGenerated == true 
            }
            if (aiTasks.isNotEmpty()) {
                Log.d("TaskListFullScreen", "检测到 ${aiTasks.size} 个AI生成的任务")
                aiTasks.forEach { task ->
                    Log.d("TaskListFullScreen", "AI任务: id=${task.id}, 标题=${task.title}, 父任务ID=${task.parentTaskId}, 显示=${task.displayInTaskList}")
                }
            }
            
            // 第二步过滤：基于displayInTaskList属性，但添加例外情况
            val tasksAfterDisplayFilter = tasksAfterDeletionFilter.filter { task -> 
                // 基本过滤条件
                val shouldDisplay = task.displayInTaskList
                
                // 对于AI生成的任务，如果是主任务（没有父任务），总是显示
                val isAiTask = task.aiGenerated == true
                val isParentTask = task.parentTaskId == null
                
                // 最终决定是否显示该任务
                val finalDecision = shouldDisplay || (isAiTask && isParentTask)
                
                // 记录被过滤掉的AI任务
                if (!finalDecision && isAiTask) {
                    Log.d("TaskListFullScreen", "AI任务 ${task.id} (${task.title}) 被过滤掉，显示设置=${task.displayInTaskList}")
                }
                
                finalDecision
            }
            
            Log.d("TaskListFullScreen", "displayInTaskList过滤后任务数: ${tasksAfterDisplayFilter.size}")
            
            val tasksAfterCompletionFilter = tasksAfterDisplayFilter.filter { task ->
                val passesCompletionFilter = when (completionFilterMode) {
                    CompletionFilterMode.HIDE_COMPLETED -> !task.isCompleted
                    CompletionFilterMode.SHOW_ONLY_COMPLETED -> task.isCompleted
                    else -> true // SHOW_ALL
                }
                if (!passesCompletionFilter) {
                    Log.d("TaskListFullScreen", "任务 ${task.id} (${task.title}) 被完成状态过滤掉, 当前过滤模式: $completionFilterMode, 任务完成状态: ${task.isCompleted}")
                }
                passesCompletionFilter
            }
            
            Log.d("TaskListFullScreen", "完成状态过滤后任务数: ${tasksAfterCompletionFilter.size}")
            
            val tasksAfterDateFilter = tasksAfterCompletionFilter.filter { task ->
                val passesDateFilter = when (selectedFilter) {
                    "今天" -> isTaskForToday(task)
                    "明天" -> isTaskForTomorrow(task)
                    "本周" -> isTaskForThisWeek(task)
                    "未定期" -> isTaskUpcoming(task)
                    else -> true
                }
                if (!passesDateFilter) {
                    Log.d("TaskListFullScreen", "任务 ${task.id} (${task.title}) 被日期过滤掉, 当前过滤: $selectedFilter, 任务天数: ${task.daysLeft}")
                }
                passesDateFilter
            }
            
            Log.d("TaskListFullScreen", "日期过滤后任务数: ${tasksAfterDateFilter.size}, 当前日期过滤: $selectedFilter")
            
            // 确保AI生成的任务在过滤后仍然存在
            val aiTasksAfterFilter = tasksAfterDateFilter.filter { it.aiGenerated == true }
            if (aiTasks.size != aiTasksAfterFilter.size) {
                Log.w("TaskListFullScreen", "警告: 过滤前有 ${aiTasks.size} 个AI任务，过滤后只剩 ${aiTasksAfterFilter.size} 个")
                val missingTasks = aiTasks.filter { aiTask -> !tasksAfterDateFilter.any { it.id == aiTask.id } }
                missingTasks.forEach { task ->
                    Log.w("TaskListFullScreen", "被过滤掉的AI任务: id=${task.id}, 标题=${task.title}, 完成状态=${task.isCompleted}")
                }
            }
            
            // 🎯 默认按紧急程度降序排序，优化排序逻辑
            val sortedTasks = when (sortMode) {
                TaskSortMode.PRIORITY_HIGH_TO_LOW -> tasksAfterDateFilter.sortedByDescending { 
                    when(it.urgency) {
                        TaskUrgency.CRITICAL -> 4
                        TaskUrgency.HIGH -> 3
                        TaskUrgency.MEDIUM -> 2
                        TaskUrgency.LOW -> 1
                    }
                }
                TaskSortMode.TIME_RECENT_TO_EARLIEST -> tasksAfterDateFilter.sortedBy { it.daysLeft }
                TaskSortMode.NONE -> tasksAfterDateFilter
                else -> {
                    // 默认情况下也按紧急程度降序排序
                    tasksAfterDateFilter.sortedByDescending { 
                        when(it.urgency) {
                            TaskUrgency.CRITICAL -> 4
                            TaskUrgency.HIGH -> 3
                            TaskUrgency.MEDIUM -> 2
                            TaskUrgency.LOW -> 1
                        }
                    }
                }
            }
            
            Log.d("TaskListFullScreen", "最终显示的任务数: ${sortedTasks.size}")
            sortedTasks
        }
    }
    
    // 处理标签更新 - 💡 优化版
    val handleTagsUpdated: (String, List<String>) -> Unit = { taskId, updatedTags ->
        Log.d("TaskListFullScreen", "开始更新任务标签: taskId=$taskId, tags=$updatedTags")
        
        coroutineScope.launch {
            try {
                // 调用TaskListViewModel的updateTaskTags方法保存标签
                viewModel.updateTaskTags(taskId, updatedTags)
                
                Log.d("TaskListFullScreen", "任务标签更新成功: $taskId")
                
                // **关键修复**: 立即更新selectedTask的标签，确保UI即时显示
                if (selectedTask?.id == taskId) {
                    selectedTask = selectedTask?.copy(customTags = updatedTags)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask的标签显示")
                }
                
                // **新增**: 强制清除缓存，确保下次获取最新数据
                try {
                    // 清除TaskRepository缓存
                    viewModel.clearTaskCache(taskId)
                    Log.d("TaskListFullScreen", "已清除任务缓存")
                } catch (e: Exception) {
                    Log.w("TaskListFullScreen", "清除缓存失败，但继续执行", e)
                }
                
                // 💡 使用智能防抖刷新，延迟降低
                delay(150) // 减少延迟时间
                smartRefresh("标签更新-$taskId")
                
                // 发送全局刷新事件
                NotificationCenter.post(TaskRefreshEvent(taskId))
                
                Log.d("TaskListFullScreen", "标签更新完成，已请求智能刷新")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新任务标签失败: taskId=$taskId", e)
                snackbarMessage = "更新任务标签失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理紧急程度更新 - 强化数据库保存机制
    val handleUrgencyUpdated: (String, TaskUrgency) -> Unit = { taskId: String, updatedUrgency: TaskUrgency ->
        Log.d("PriorityUpdate", "[TaskListFullScreen] ===== 开始处理紧急程度更新 =====")
        Log.d("PriorityUpdate", "[TaskListFullScreen] 任务ID: $taskId")
        Log.d("PriorityUpdate", "[TaskListFullScreen] 新紧急程度: $updatedUrgency")
        
        coroutineScope.launch {
            try {
                // 将紧急程度转换为优先级
                val priority = when(updatedUrgency) {
                    TaskUrgency.CRITICAL -> Priority.URGENT
                    TaskUrgency.HIGH -> Priority.HIGH
                    TaskUrgency.MEDIUM -> Priority.MEDIUM
                    TaskUrgency.LOW -> Priority.LOW
                }
                Log.d("PriorityUpdate", "[TaskListFullScreen] 转换后的优先级: $priority")
                
                // 立即更新selectedTask的显示
                val taskIndex = taskListState.tasks.indexOfFirst { it.id == taskId }
                if (taskIndex >= 0) {
                    if (selectedTask?.id == taskId) {
                        val task = taskListState.tasks[taskIndex]
                        selectedTask = task.copy(urgency = updatedUrgency)
                        Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ selectedTask显示已立即更新")
                    }
                }
                
                // 调用TaskListViewModel保存到数据库
                Log.d("PriorityUpdate", "[TaskListFullScreen] 开始调用ViewModel保存到数据库...")
                try {
                    viewModel.updateTaskPriority(taskId, priority)
                    Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ ViewModel.updateTaskPriority调用成功")
                } catch (e: Exception) {
                    Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ ViewModel.updateTaskPriority调用失败", e)
                    throw e
                }
                
                // 延迟刷新确保数据库更新完成
                Log.d("PriorityUpdate", "[TaskListFullScreen] 等待数据库更新完成...")
                delay(250)
                
                // 强制刷新任务列表
                Log.d("PriorityUpdate", "[TaskListFullScreen] 开始刷新任务列表...")
                try {
                    viewModel.refreshTasks()
                    Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ 任务列表刷新完成")
                } catch (e: Exception) {
                    Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ 任务列表刷新失败", e)
                }
                
                // 发送刷新事件通知其他组件
                Log.d("PriorityUpdate", "[TaskListFullScreen] 发送全局刷新事件...")
                NotificationCenter.post(TaskRefreshEvent(taskId))
                Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ 全局刷新事件已发送")
                
                Log.d("PriorityUpdate", "[TaskListFullScreen] ===== 优先级更新流程完成 =====")
                
            } catch (e: Exception) {
                Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ 优先级更新流程失败", e)
                snackbarMessage = "更新优先级失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 创建优先级更新处理函数 - 增强处理逻辑
    val handlePriorityUpdate: (String, Priority) -> Unit = { id, priority ->
        Log.d("PriorityUpdate", "接收到优先级更新请求: taskId=$id, priority=$priority")
        
        val urgency = when(priority) {
            Priority.HIGH -> TaskUrgency.HIGH
            Priority.MEDIUM -> TaskUrgency.MEDIUM
            Priority.LOW -> TaskUrgency.LOW
            Priority.URGENT -> TaskUrgency.CRITICAL
            else -> TaskUrgency.MEDIUM
        }
        
        // 直接调用紧急程度更新处理
        handleUrgencyUpdated(id, urgency)
    }
    
    // 创建子任务添加处理函数
    val handleSubTaskAdd: (String, com.timeflow.app.ui.screen.task.SubTask) -> Unit = { parentId, uiSubTask ->
        val modelSubTask = transformUIToModelSubTask(parentId, uiSubTask)
        Log.d("TaskListFullScreen", "开始添加子任务: parentId=$parentId, title=${modelSubTask.title}")
        
        coroutineScope.launch {
            try {
                viewModel.addSubTask(parentId, modelSubTask.title)
                Log.d("TaskListFullScreen", "子任务添加成功: ${modelSubTask.title}")
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                Log.d("TaskListFullScreen", "子任务添加完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "添加子任务失败: parentId=$parentId", e)
                snackbarMessage = "添加子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理子任务更新 - 修复实现
    val handleSubTaskUpdated: (String, com.timeflow.app.ui.screen.task.SubTask) -> Unit = { parentId, updatedSubTask ->
        Log.d("TaskListFullScreen", "开始更新子任务: parentId=$parentId, subTaskId=${updatedSubTask.id}, isCompleted=${updatedSubTask.isCompleted}")
        
        coroutineScope.launch {
            try {
                viewModel.updateTaskStatus(updatedSubTask.id, updatedSubTask.isCompleted)
                Log.d("TaskListFullScreen", "子任务状态更新成功: ${updatedSubTask.id}")
                
                // **关键修复**: 立即更新selectedTask中的对应子任务
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val updatedSubTasks = currentSubTasks.map { subTask ->
                        if (subTask.id == updatedSubTask.id) {
                            subTask.copy(
                                title = updatedSubTask.title,
                                isCompleted = updatedSubTask.isCompleted,
                                priority = updatedSubTask.priority ?: Priority.MEDIUM
                            )
                        } else {
                            subTask
                        }
                    }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask中的子任务状态")
                }
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务更新已同步")
                }
                
                Log.d("TaskListFullScreen", "子任务更新完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新子任务失败: parentId=$parentId, subTaskId=${updatedSubTask.id}", e)
                snackbarMessage = "更新子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 🔄 创建任务完成处理函数（支持撤销）
    val handleTaskComplete: (String) -> Unit = { taskId ->
        Log.d("UndoComplete", "开始处理任务完成: $taskId")
        
        // 首先找到要完成的任务
        val taskToComplete = filteredTasks.find { it.id == taskId }
        if (taskToComplete != null) {
            // 创建撤销信息
            val undoInfo = UndoTaskInfo(
                taskId = taskId,
                taskTitle = taskToComplete.title,
                originalTask = taskToComplete
            )
            
            // 保存到撤销列表
            completedTasks = completedTasks.toMutableMap().apply { 
                put(taskId, undoInfo) 
            }
            
            // 显示撤销Snackbar
            currentUndoTask = undoInfo
            showUndoSnackbar = true
            
            // 启动30秒倒计时
            undoTimer.startTimer(taskId)
            
            Log.d("UndoComplete", "任务已标记为完成，撤销选项可用30秒: ${taskToComplete.title}")
            
            // 异步更新数据库
            coroutineScope.launch {
                try {
                    viewModel.updateTaskStatus(taskId, true)
                    Log.d("UndoComplete", "数据库更新成功: taskId=$taskId")
                } catch (e: Exception) {
                    Log.e("UndoComplete", "数据库更新失败: taskId=$taskId", e)
                    // 如果数据库更新失败，移除撤销选项
                    completedTasks = completedTasks.toMutableMap().apply { remove(taskId) }
                    undoTimer.cancelTimer(taskId)
                    snackbarMessage = "任务完成失败，请重试"
                }
            }
        }
    }
    
    // 🔄 撤销任务完成处理函数
    val handleUndoTaskComplete: (String) -> Unit = { taskId ->
        Log.d("UndoComplete", "开始撤销任务完成: $taskId")
        
        val undoInfo = completedTasks[taskId]
        if (undoInfo != null) {
            // 取消计时器
            undoTimer.cancelTimer(taskId)
            
            // 移除撤销信息
            completedTasks = completedTasks.toMutableMap().apply { remove(taskId) }
            currentUndoTask = null
            showUndoSnackbar = false
            
            // 异步恢复任务状态
            coroutineScope.launch {
                try {
                    viewModel.updateTaskStatus(taskId, false)
                    Log.d("UndoComplete", "任务撤销成功: ${undoInfo.taskTitle}")
                    snackbarMessage = "已撤销「${undoInfo.taskTitle}」的完成状态"
                } catch (e: Exception) {
                    Log.e("UndoComplete", "撤销失败: taskId=$taskId", e)
                    snackbarMessage = "撤销失败，请重试"
                }
            }
        }
    }
    
    // 创建任务时间更新处理函数
    val handleTaskTimeUpdate: (String, LocalDateTime?, LocalDateTime?) -> Unit = { taskId, startDateTime, endDateTime ->
        Log.d("TimeSync", "=== 开始处理时间更新 ===")
        Log.d("TimeSync", "任务ID: $taskId")
        Log.d("TimeSync", "开始时间: $startDateTime")
        Log.d("TimeSync", "结束时间: $endDateTime")
        
        coroutineScope.launch {
            try {
                if (selectedTask?.id == taskId && startDateTime != null) {
                    selectedTask = selectedTask?.copy(
                        dueDate = startDateTime,
                        daysLeft = calculateDaysLeft(startDateTime)
                    )
                    Log.d("TimeSync", "✓ 已更新选中任务显示")
                }
                
                if (startDateTime != null) {
                    viewModel.updateTaskTime(taskId, startDateTime)
                    Log.d("TimeSync", "✓ 已调用TaskListViewModel更新")
                    
                    delay(100)
                    
                    taskTimeViewModel.updateTaskTime(
                        taskId = taskId,
                        startTime = startDateTime,
                        endTime = endDateTime,
                        dueDate = startDateTime,
                        source = "TaskListFullScreen_Sync"
                    )
                    Log.d("TimeSync", "✓ 已调用TaskTimeViewModel同步")
                }
                
                delay(150)
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TimeSync", "时间更新处理失败", e)
            }
        }
    }
    
    // 添加页面交互状态控制，避免在TaskDetailBottomSheet初始化期间的不必要刷新
    var isTaskDetailInitializing by remember { mutableStateOf(false) }
    
    // 创建任务更新处理函数 - 💡 优化版
    val handleTaskUpdate: (TaskData) -> Unit = { updatedTaskData ->
        val updatedModelTask = convertUITaskDataToModelTaskData(updatedTaskData)
        selectedTask = updatedModelTask
        Log.d("TaskSync", "[TaskListFullScreen] 已更新selectedTask: ${updatedModelTask.title}")
        
        // 🔧 避免在TaskDetailBottomSheet初始化期间触发刷新
        if (!isTaskDetailInitializing) {
            smartRefresh("任务更新-${updatedModelTask.id}")
            Log.d("TaskSync", "[TaskListFullScreen] 执行smartRefresh")
        } else {
            Log.d("TaskSync", "[TaskListFullScreen] 跳过smartRefresh，TaskDetail初始化中")
        }
    }
    
    // 监听TaskDetailBottomSheet的初始化状态
    LaunchedEffect(selectedTask?.id) {
        if (selectedTask != null) {
            isTaskDetailInitializing = true
            Log.d("TaskSync", "[TaskListFullScreen] 开始TaskDetail初始化延迟")
            delay(3000) // 3秒延迟，确保TaskDetailBottomSheet完全初始化
            isTaskDetailInitializing = false
            Log.d("TaskSync", "[TaskListFullScreen] TaskDetail初始化延迟结束")
        }
    }
    
    // 创建任务反馈提交处理函数
    val handleFeedbackSubmitted: (String, com.timeflow.app.ui.screen.task.model.FeedbackData) -> Unit = { taskId, feedback ->
        android.util.Log.d("TaskListFullScreen", "===== 收到任务反馈提交 =====")
        android.util.Log.d("TaskListFullScreen", "任务ID: $taskId")
        android.util.Log.d("TaskListFullScreen", "反馈心情: ${feedback.emotion}")
        android.util.Log.d("TaskListFullScreen", "反馈感想: ${feedback.comment}")
        android.util.Log.d("TaskListFullScreen", "调用ViewModel.submitTaskFeedback...")
        
        viewModel.submitTaskFeedback(taskId, feedback)
        
        android.util.Log.d("TaskListFullScreen", "✓ 已调用ViewModel.submitTaskFeedback")
        android.util.Log.d("TaskListFullScreen", "任务反馈已提交: taskId=$taskId, comment=${feedback.comment?.take(20)}...")
    }
    
    // 显示反馈对话框的状态
    var showFeedbackDialog by remember { mutableStateOf(false) }
    var feedbackTaskId by remember { mutableStateOf<String?>(null) }
    var feedbackTaskTitle by remember { mutableStateOf("") }
    
    // 处理子任务添加 - 修复实现
    val handleSubTaskAdded: (String, SubTask) -> Unit = { parentId, subTask ->
        Log.d("TaskListFullScreen", "开始添加子任务: parentId=$parentId, title=${subTask.title}")
        
        coroutineScope.launch {
            try {
                // 调用TaskListViewModel的addSubTask方法保存子任务
                viewModel.addSubTask(parentId, subTask.title)
                
                Log.d("TaskListFullScreen", "子任务添加成功: ${subTask.title}")
                
                // **关键修复**: 立即更新selectedTask的子任务列表，确保UI即时显示
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val newSubTask = SubTask(
                        id = subTask.id,
                        title = subTask.title,
                        isCompleted = subTask.isCompleted,
                        priority = subTask.priority ?: Priority.MEDIUM
                    )
                    val updatedSubTasks = currentSubTasks.toMutableList().apply { add(newSubTask) }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask的子任务列表")
                }
                
                // 延迟一小段时间确保数据库写入完成
                delay(100)
                
                // 强制刷新任务列表以显示新的子任务
                viewModel.refreshTasks()
                
                // 发送全局刷新事件
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 等待任务列表刷新完成后，再次确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务已更新为最新数据")
                }
                
                Log.d("TaskListFullScreen", "子任务添加完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "添加子任务失败: parentId=$parentId", e)
                snackbarMessage = "添加子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理子任务删除 - 修复实现
    val handleSubTaskDeleted: (String, String) -> Unit = { parentId, subTaskId ->
        Log.d("TaskListFullScreen", "开始删除子任务: parentId=$parentId, subTaskId=$subTaskId")
        
        coroutineScope.launch {
            try {
                // 调用ViewModel删除子任务
                viewModel.deleteSubTask(parentId, subTaskId)
                Log.d("TaskListFullScreen", "子任务删除成功: $subTaskId")
                
                // **关键修复**: 立即更新selectedTask中的子任务列表
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val updatedSubTasks = currentSubTasks.filter { it.id != subTaskId }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask，移除已删除的子任务")
                }
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务删除已同步")
                }
                
                Log.d("TaskListFullScreen", "子任务删除完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "删除子任务失败: parentId=$parentId, subTaskId=$subTaskId", e)
                snackbarMessage = "删除子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 🔄 处理任务状态更新（修复复选框立即响应问题）
    val handleTaskStatusChanged: (String, Boolean) -> Unit = { taskId, isCompleted ->
        Log.d("TaskStatus", "[TaskListFullScreen] ===== 开始处理任务状态更新 =====")
        Log.d("TaskStatus", "[TaskListFullScreen] 任务ID: $taskId")
        Log.d("TaskStatus", "[TaskListFullScreen] 新状态: ${if (isCompleted) "已完成" else "未完成"}")
        
        // 🚀 修复关键问题：立即更新本地状态，使用ViewModel的协程作用域避免组件销毁时的取消问题
        // 🔧 步骤1：立即更新selectedTask状态（确保详情页复选框立即响应）
        if (selectedTask?.id == taskId) {
            selectedTask = selectedTask?.copy(isCompleted = isCompleted)
            Log.d("TaskStatus", "[TaskListFullScreen] ✓ selectedTask状态已立即更新")
        }
        
        // 🔧 步骤2：立即更新任务列表状态（确保列表页复选框立即响应）
        val currentState = viewModel.taskListState.value
        val updatedTasks = currentState.tasks.map { task ->
            if (task.id == taskId) {
                task.copy(isCompleted = isCompleted)
            } else {
                task
            }
        }
        
        // 立即触发UI更新
        viewModel.updateTaskListStateImmediate(updatedTasks)
        Log.d("TaskStatus", "[TaskListFullScreen] ✓ 任务列表状态已立即更新")
        
        // 🔧 步骤3：使用ViewModel的协程作用域进行异步数据库更新（避免组件销毁时的取消问题）
        if (isCompleted) {
            // 如果是完成任务，使用支持撤销的处理函数
            Log.d("TaskStatus", "[TaskListFullScreen] 🔄 调用handleTaskComplete处理完成操作...")
            handleTaskComplete(taskId)
        } else {
            // 如果是取消完成，使用ViewModel的协程作用域直接更新数据库
            Log.d("TaskStatus", "[TaskListFullScreen] 🚀 使用ViewModel协程作用域异步更新数据库状态...")
            try {
                // 使用ViewModel的updateTaskStatusOptimistic方法，它有更好的错误处理
                viewModel.updateTaskStatusOptimistic(taskId, isCompleted)
                
                Log.d("TaskStatus", "[TaskListFullScreen] ✓ 数据库更新已提交")
            } catch (e: Exception) {
                Log.e("TaskStatus", "[TaskListFullScreen] ✗ 数据库更新失败，回滚UI状态", e)
                
                // 回滚UI状态
                if (selectedTask?.id == taskId) {
                    selectedTask = selectedTask?.copy(isCompleted = !isCompleted)
                }
                
                val revertedTasks = currentState.tasks.map { task ->
                    if (task.id == taskId) {
                        task.copy(isCompleted = !isCompleted)
                    } else {
                        task
                    }
                }
                viewModel.updateTaskListStateImmediate(revertedTasks)
                
                // 使用协程作用域显示错误消息，避免阻塞UI
                coroutineScope.launch {
                    snackbarMessage = "更新失败: ${e.localizedMessage}"
                }
            }
        }
        
        Log.d("TaskStatus", "[TaskListFullScreen] ===== 任务状态更新处理完成 =====")
    }
    
    // 处理任务反馈提交 - 同上
    val handleFeedbackSubmitted_duplicate: (String, com.timeflow.app.ui.screen.task.model.FeedbackData) -> Unit = { taskId, feedback ->
        // 调用ViewModel的submitTaskFeedback方法
        viewModel.submitTaskFeedback(taskId, feedback)
        android.util.Log.d("TaskListFullScreen", "任务反馈已提交: taskId=$taskId, comment=${feedback.comment?.take(20)}...")
    }
    
    // 处理任务时间更新 - 彻底修复的时间更新处理机制
    val handleTaskTimeUpdated: (String, LocalDateTime?, LocalDateTime?) -> Unit = { taskId, startDateTime, endDateTime ->
        Log.d("TimeSync", "=== 开始处理时间更新 ===")
        Log.d("TimeSync", "任务ID: $taskId")
        Log.d("TimeSync", "开始时间: $startDateTime")
        Log.d("TimeSync", "结束时间: $endDateTime")
        
        coroutineScope.launch {
            try {
                // 1. 立即更新选中任务的显示（UI层面的即时反馈）
                if (selectedTask?.id == taskId && startDateTime != null) {
                    selectedTask = selectedTask?.copy(
                        dueDate = startDateTime,
                        daysLeft = calculateDaysLeft(startDateTime)
                    )
                    Log.d("TimeSync", "✓ 已更新选中任务显示")
                }
                
                // 2. 关键修复：确保TaskRepository也被更新
                if (startDateTime != null) {
                    // 先调用TaskListViewModel更新主数据源
                    viewModel.updateTaskTime(taskId, startDateTime)
                    Log.d("TimeSync", "✓ 已调用TaskListViewModel更新")
                    
                    // 等待数据库更新完成
                    delay(100)
                    
                    // 然后调用TaskTimeViewModel确保时间管理系统同步
                    taskTimeViewModel.updateTaskTime(
                        taskId = taskId,
                        startTime = startDateTime,
                        endTime = endDateTime,
                        dueDate = startDateTime,
                        source = "TaskListFullScreen_Sync"
                    )
                    Log.d("TimeSync", "✓ 已调用TaskTimeViewModel同步")
                }
                
                // 3. 强制刷新任务列表（确保UI同步）
                delay(150) // 增加延迟确保数据库更新完成
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已刷新任务列表")
                
                // 4. 额外的强制刷新机制
                delay(200)
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已进行第二次刷新确保同步")
                
                // 5. 发送全局事件确保其他组件也能收到更新
                try {
                    com.timeflow.app.util.NotificationCenter.post(
                        com.timeflow.app.util.TaskRefreshEvent(taskId)
                    )
                    Log.d("TimeSync", "✓ 已发送全局刷新事件")
                } catch (e: Exception) {
                    Log.e("TimeSync", "发送全局事件失败", e)
                }
                
                Log.d("TimeSync", "=== 时间更新处理完成 ===")
                
            } catch (e: Exception) {
                Log.e("TimeSync", "时间更新处理失败", e)
            }
        }
    }
    
    // 监听TaskListViewModel的任务列表变化 - 主要的数据同步机制
    LaunchedEffect(Unit) {
        viewModel.taskListState.collect { state ->
            if (!state.isLoading && state.tasks.isNotEmpty()) {
                // 检查选中任务是否需要更新
                selectedTask?.let { currentSelected ->
                    val updatedTask = state.tasks.find { it.id == currentSelected.id }
                    if (updatedTask != null && updatedTask.dueDate != currentSelected.dueDate) {
                        selectedTask = updatedTask
                        Log.d("TimeSync", "从任务列表同步更新了选中任务时间: ${updatedTask.dueDate}")
                    }
                }
            }
        }
    }
    
    // 监听TaskTimeViewModel的时间更新事件 - 立即响应机制
    LaunchedEffect(Unit) {
        taskTimeViewModel.timeUpdateEvents.collect { event ->
            Log.d("TimeSync", "收到TaskTimeViewModel时间更新事件: taskId=${event.taskId}, 新时间=${event.newTime.effectiveTime}")
            
            // 立即更新选中任务的时间显示
            if (selectedTask?.id == event.taskId) {
                selectedTask = selectedTask?.copy(
                    dueDate = event.newTime.effectiveTime,
                    daysLeft = event.newTime.daysLeft
                )
                Log.d("TimeSync", "已更新选中任务时间显示: ${event.newTime.effectiveTime}")
            }
            
            // 立即强制刷新任务列表以显示最新时间
            viewModel.refreshTasks()
            Log.d("TimeSync", "已强制刷新任务列表响应时间更新")
            
            // 额外的延迟刷新确保数据同步
            delay(300)
            viewModel.refreshTasks()
            Log.d("TimeSync", "已进行延迟刷新确保数据完全同步")
        }
    }
    
    // 监听TaskListViewModel的状态变化 - 确保UI及时更新
    LaunchedEffect(Unit) {
        viewModel.taskListState.collect { state ->
            Log.d("TaskListFullScreen", "收到任务列表状态更新: isLoading=${state.isLoading}, 任务数量=${state.tasks.size}")
            
            // 如果有选中的任务，检查是否需要更新其时间显示
            selectedTask?.let { currentSelected ->
                val updatedTask = state.tasks.find { it.id == currentSelected.id }
                if (updatedTask != null && updatedTask.dueDate != currentSelected.dueDate) {
                    selectedTask = updatedTask
                    Log.d("TimeSync", "从任务列表状态更新中同步了选中任务时间: 旧时间=${currentSelected.dueDate}, 新时间=${updatedTask.dueDate}")
                }
            }
        }
    }
    
    // 💡 优化全局任务刷新事件处理 - 使用防抖和平滑动画
    LaunchedEffect(Unit) {
        Log.d("TaskSync", "[TaskListFullScreen] ===== 开始监听NotificationCenter事件 =====")
        com.timeflow.app.util.NotificationCenter.events.collect { event ->
            Log.d("TaskSync", "[TaskListFullScreen] 📨 收到事件: ${event.javaClass.simpleName}")
            when (event) {
                // 🎯 高优先级：新任务添加事件 - 绕过防抖机制
                is com.timeflow.app.util.NewTaskAddedEvent -> {
                    Log.d("TaskSync", "[TaskListFullScreen] 🎯 收到新任务添加事件: ${event.taskId} - ${event.taskTitle}")
                    
                    try {
                        // 立即刷新，不受防抖限制
                        Log.d("TaskSync", "[TaskListFullScreen] 🚀 执行新任务即时刷新（绕过防抖）")
                        
                        coroutineScope.launch {
                            Log.d("TaskSync", "[TaskListFullScreen] 开始处理新任务添加事件...")
                            
                            // 🔧 强制清除缓存
                            try {
                                viewModel.clearTaskCache(event.taskId)
                                Log.d("TaskSync", "[TaskListFullScreen] ✓ 缓存已清除")
                            } catch (e: Exception) {
                                Log.w("TaskSync", "[TaskListFullScreen] 清除缓存失败，继续执行", e)
                            }
                            
                            // 🔧 多次刷新确保数据同步
                            for (i in 1..3) {
                                Log.d("TaskSync", "[TaskListFullScreen] 执行第 $i 次刷新...")
                                viewModel.refreshTasks()
                                delay(200) // 等待刷新完成
                            }
                            
                            Log.d("TaskSync", "[TaskListFullScreen] ✓ 新任务刷新完成")
                            
                            // 重置防抖计时器，确保后续正常刷新不受影响
                            lastRefreshTime = System.currentTimeMillis()
                            
                            // 显示用户反馈
                            snackbarMessage = "新任务「${event.taskTitle}」已添加"
                        }
                        
                    } catch (e: Exception) {
                        Log.e("TaskSync", "[TaskListFullScreen] 处理新任务添加事件失败", e)
                    }
                }
                
                // 🔄 普通优先级：任务刷新事件 - 使用防抖机制
                is com.timeflow.app.util.TaskRefreshEvent -> {
                    Log.d("TaskSync", "[TaskListFullScreen] 收到普通任务刷新事件: ${event.taskId}")
                    
                    try {
                        // 💡 增强防抖机制：检查是否是重复事件
                        val currentTime = System.currentTimeMillis()
                        
                        // 🔧 增强重复事件过滤 - 避免短时间内重复处理同一任务的刷新事件
                        if (currentTime - lastRefreshTime < 2000L) { // 从800ms增加到2秒
                            Log.d("TaskSync", "[TaskListFullScreen] ⏳ 防抖忽略重复普通刷新事件，还需等待 ${2000L - (currentTime - lastRefreshTime)}ms")
                            return@collect
                        }
                        
                        // 💡 使用智能防抖刷新，而不是立即刷新
                        Log.d("TaskSync", "[TaskListFullScreen] 开始智能刷新任务列表...")
                        smartRefresh("普通事件-${event.taskId}")
                        Log.d("TaskSync", "[TaskListFullScreen] ✓ 任务列表刷新请求已提交")
                        
                        // 如果是当前选中的任务，使用更温和的更新方式
                        if (selectedTask?.id == event.taskId) {
                            Log.d("TaskSync", "[TaskListFullScreen] 检测到当前选中任务的更新事件")
                            
                            // 💡 添加微动画过渡效果
                            coroutineScope.launch {
                                // 延迟获取更新，减少抖动
                                delay(500) // 给刷新时间
                                
                                val updatedTask = viewModel.taskListState.value.tasks.find { it.id == event.taskId }
                                if (updatedTask != null) {
                                    selectedTask = updatedTask
                                    Log.d("TaskSync", "[TaskListFullScreen] ✓ 平滑更新selectedTask: ${updatedTask.title}")
                                    Log.d("TaskSync", "[TaskListFullScreen] 新的紧急程度: ${updatedTask.urgency}")
                                } else {
                                    Log.w("TaskSync", "[TaskListFullScreen] ⚠ 未找到更新后的任务")
                                }
                            }
                        }
                        
                        Log.d("TaskSync", "[TaskListFullScreen] 普通刷新事件处理完成")
                    } catch (e: Exception) {
                        Log.e("TaskSync", "[TaskListFullScreen] 处理普通刷新事件失败", e)
                    }
                }
            }
        }
    }
    
    // 💡 优化任务状态监听 - 减少不必要的重组
    LaunchedEffect(viewModel.taskListState) {
        viewModel.taskListState.collect { state ->
            // 💡 只在关键状态变化时记录日志，减少日志噪音
            if (state.isLoading) {
                Log.d("TaskListFullScreen", "任务列表开始加载...")
            } else {
                Log.d("TaskListFullScreen", "任务列表加载完成: ${state.tasks.size}个任务")
            }
            
            // 如果有选中的任务，确保它是最新的
            selectedTask?.let { currentSelected ->
                val updatedTask = state.tasks.find { it.id == currentSelected.id }
                if (updatedTask != null && updatedTask != currentSelected) {
                    selectedTask = updatedTask
                    Log.d("TaskSync", "[TaskListFullScreen] selectedTask已静默更新")
                }
            }
        }
    }
    
    // 🔧 监听删除消息并显示Snackbar
    LaunchedEffect(Unit) {
        viewModel.deleteMessage.collect { deleteMessage ->
            try {
                Log.d("TaskDelete", "[TaskListFullScreen] 收到删除消息: ${deleteMessage.taskTitle}")
                
                // 显示撤销删除的Snackbar
                launch {
                    val snackbarResult = scaffoldState.snackbarHostState.showSnackbar(
                        message = deleteMessage.message,
                        actionLabel = deleteMessage.actionLabel,
                        duration = SnackbarDuration.Long
                    )
                    
                    // 如果用户点击了撤销按钮
                    if (snackbarResult == SnackbarResult.ActionPerformed) {
                        viewModel.undoDeleteTask(deleteMessage.taskId)
                        Log.d("TaskDelete", "[TaskListFullScreen] 用户撤销删除: ${deleteMessage.taskId}")
                    }
                }
            } catch (e: Exception) {
                Log.e("TaskDelete", "[TaskListFullScreen] Snackbar显示失败", e)
                // Snackbar失败不影响删除操作
            }
        }
    }
    
    // 显示任务详情底部弹出层
    if (showBottomSheet && selectedTask != null) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissBottomSheet() {
            showBottomSheet = false
        }
        
        fun navigateToEdit() {
            showBottomSheet = false
            navController.navigate(AppDestinations.taskEditRoute(selectedTask!!.id))
        }
        
        ModalBottomSheet(
            onDismissRequest = ::dismissBottomSheet,
            sheetState = sheetState,
            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
            containerColor = Color.White
        ) {
            TaskDetailBottomSheet(
                task = convertModelTaskDataToUITaskData(selectedTask!!),
                onEditClick = ::navigateToEdit,
                onDismiss = ::dismissBottomSheet,
                onTagsUpdated = handleTagsUpdated, // 处理标签更新
                onPriorityUpdated = handlePriorityUpdate,
                onSubTaskAdded = handleSubTaskAdd,
                onSubTaskUpdated = handleSubTaskUpdated,
                onSubTaskDeleted = handleSubTaskDeleted, // 处理子任务删除
                onTaskCompleted = handleTaskComplete,
                onTaskTimeUpdated = handleTaskTimeUpdate,
                navController = navController, // 添加导航控制器参数
                onTaskUpdated = handleTaskUpdate, // 使用函数引用避免实验性功能问题
                onGoalAssociated = { taskId, goalId, goalTitle ->
                    Log.d("GoalAssociation", "[TaskListFullScreen] 目标关联请求: taskId=$taskId, goalId=$goalId, goalTitle=$goalTitle")
                    
                    coroutineScope.launch {
                        try {
                            // 🔧 立即更新selectedTask的显示，提供即时反馈
                            if (selectedTask?.id == taskId) {
                                selectedTask = selectedTask?.copy(goalId = goalId, goalTitle = goalTitle)
                                Log.d("GoalAssociation", "[TaskListFullScreen] ✓ selectedTask显示已立即更新")
                            }
                            
                            // 使用ViewModel保存目标关联到数据库
                            Log.d("GoalAssociation", "[TaskListFullScreen] 即将调用viewModel.updateTaskGoalAssociation")
                            viewModel.updateTaskGoalAssociation(taskId, goalId, goalTitle)
                            
                            Log.d("GoalAssociation", "[TaskListFullScreen] ✓ 目标关联保存成功")
                            
                            // 延迟刷新，确保数据库更新完成
                            delay(200)
                            viewModel.refreshTasks()
                            
                            // 🔧 重新从数据库获取最新的任务状态
                            delay(100)
                            taskListState.tasks.find { task -> task.id == taskId }?.let { updatedTask ->
                                if (selectedTask?.id == taskId) {
                                    selectedTask = updatedTask
                                    Log.d("GoalAssociation", "[TaskListFullScreen] ✓ selectedTask已从数据库重新同步")
                                }
                            }
                            
                            // 发送全局刷新事件
                            NotificationCenter.post(TaskRefreshEvent(taskId))
                            
                            Log.d("GoalAssociation", "[TaskListFullScreen] ✓ 目标关联流程完成")
                            
                        } catch (e: Exception) {
                            Log.e("GoalAssociation", "[TaskListFullScreen] ✗ 目标关联保存失败", e)
                            snackbarMessage = "目标关联失败: ${e.localizedMessage ?: "未知错误"}"
                        }
                    }
                }
            )
        }
    }
    
    // 显示反馈对话框
    if (showFeedbackDialog && feedbackTaskId != null) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissFeedbackDialog() {
            showFeedbackDialog = false 
            feedbackTaskId = null
        }
        
        fun submitFeedback(feedback: com.timeflow.app.ui.screen.task.model.FeedbackData) {
            feedbackTaskId?.let { taskId ->
                handleFeedbackSubmitted(taskId, feedback)
            }
            showFeedbackDialog = false
            feedbackTaskId = null
        }
        
        TaskFeedbackDialog(
            onDismiss = ::dismissFeedbackDialog,
            onSubmit = ::submitFeedback,
            taskTitle = feedbackTaskTitle
        )
    }

    // 添加语音输入底部弹窗状态
    var showVoiceInputSheet by remember { mutableStateOf(false) }

    // 将语音识别相关变量提升到父作用域
    var isListening by remember { mutableStateOf(false) }
    var speechRecognizer by remember { mutableStateOf<SpeechRecognizer?>(null) }
    var recognizedText by remember { mutableStateOf("") }
    var audioLevel by remember { mutableStateOf(0f) }
    var speechListenerJob: Job? by remember { mutableStateOf(null) }
    var isHolding by remember { mutableStateOf(false) }
    var pendingVoiceText by remember { mutableStateOf<String?>(null) }
    
    // 模拟音量波动的动画值
    val waveAmplitudes = remember { List(50) { mutableStateOf(0.1f) } }
    
    // 语音图标状态动画
    val micScale = animateFloatAsState(
        targetValue = if (isListening) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "micScale"
    )
    
    // 处理待处理的语音文本
    LaunchedEffect(pendingVoiceText) {
        pendingVoiceText?.let { text ->
            if (text.isNotBlank()) {
                // 延迟一帧，确保UI已更新
                delay(16) // 等待约一帧的时间
                
                // 设置标志表示有文本需要处理
                showVoiceInputSheet = false // 先关闭语音输入界面
                
                // 不直接调用Composable函数，而是设置状态让组合函数处理
                // 处理过程仍然继续，但不直接从这里调用Composable
            }
        }
    }

    // 底部弹出层显示语音输入界面
    if (showVoiceInputSheet) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissVoiceInputSheet() {
            showVoiceInputSheet = false
            speechRecognizer?.destroy()
            speechRecognizer = null
            speechListenerJob?.cancel()
            recognizedText = ""
        }
        
        fun closeVoiceInput() {
            showVoiceInputSheet = false
            speechRecognizer?.destroy()
            speechRecognizer = null
            speechListenerJob?.cancel()
            recognizedText = ""
        }
        
        ModalBottomSheet(
            onDismissRequest = ::dismissVoiceInputSheet,
            sheetState = rememberModalBottomSheetState(),
            shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
            containerColor = Color.White,
            dragHandle = {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .width(40.dp)
                            .height(4.dp)
                            .background(
                                color = Color(0xFFE0E0E0),
                                shape = RoundedCornerShape(2.dp)
                            )
                    )
                }
            }
        ) {
            // 创建所有setter的显式函数
            fun setIsListening(value: Boolean) { isListening = value }
            fun setRecognizedText(value: String) { recognizedText = value }
            fun setAudioLevel(value: Float) { audioLevel = value }
            fun setSpeechRecognizer(value: SpeechRecognizer?) { speechRecognizer = value }
            fun setSpeechListenerJob(value: Job?) { speechListenerJob = value }
            fun setIsHolding(value: Boolean) { isHolding = value }
            fun setPendingVoiceText(value: String?) { pendingVoiceText = value }
            
            VoiceInputContent(
                onClose = ::closeVoiceInput,
                isListening = isListening,
                setIsListening = ::setIsListening,
                recognizedText = recognizedText,
                setRecognizedText = ::setRecognizedText,
                audioLevel = audioLevel,
                setAudioLevel = ::setAudioLevel,
                speechRecognizer = speechRecognizer,
                setSpeechRecognizer = ::setSpeechRecognizer,
                speechListenerJob = speechListenerJob,
                setSpeechListenerJob = ::setSpeechListenerJob,
                isHolding = isHolding,
                setIsHolding = ::setIsHolding,
                pendingVoiceText = pendingVoiceText,
                setPendingVoiceText = ::setPendingVoiceText,
                waveAmplitudes = waveAmplitudes,
                navController = navController,
                viewModel = viewModel
            )
        }
    }
    
    // 当有待处理的语音文本时显示解析UI
    pendingVoiceText?.let { text ->
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissParseVoiceInput() {
            pendingVoiceText = null
        }
        
        fun dismissLoadingDialog() {
            isShowingLoadingDialog = false
        }
        
        ParseVoiceInputUI(
            text = text,
            navController = navController,
            viewModel = viewModel,
            onDismiss = ::dismissParseVoiceInput
        )
    }
    
    // 显示正在加载对话框
    if (isShowingLoadingDialog) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissLoadingDialog() {
            isShowingLoadingDialog = false
        }
        
        LoadingDialog(
            message = loadingMessage ?: "处理中...",
            onDismiss = ::dismissLoadingDialog
        )
    }

    // 使用Scaffold包装整个界面以支持Snackbar
    Scaffold(
        scaffoldState = scaffoldState,
        snackbarHost = { SnackbarHost(scaffoldState.snackbarHostState) }
    ) { paddingValues ->

    // 处理snackbarMessage显示
    LaunchedEffect(snackbarMessage) {
        snackbarMessage?.let { message ->
            scaffoldState.snackbarHostState.showSnackbar(
                message = message,
                duration = SnackbarDuration.Short
            )
            snackbarMessage = null
        }
    }
    
    // 🔄 处理撤销Snackbar显示
    LaunchedEffect(showUndoSnackbar, currentUndoTask) {
        if (showUndoSnackbar && currentUndoTask != null) {
            val result = scaffoldState.snackbarHostState.showSnackbar(
                message = "「${currentUndoTask!!.taskTitle}」已完成",
                actionLabel = "撤销",
                duration = SnackbarDuration.Long
            )
            
            if (result == SnackbarResult.ActionPerformed) {
                // 用户点击了撤销按钮
                handleUndoTaskComplete(currentUndoTask!!.taskId)
            } else {
                // Snackbar被其他方式关闭
                showUndoSnackbar = false
            }
        }
    }

    // 🎨 根据模式选择背景
    val currentBackground = if (isDoneListMode) {
        // Done List 模式：使用柔和的紫灰色渐变背景
        Brush.verticalGradient(
            colors = listOf(
                Color(0xFFbcb8c2), // 柔和紫灰色
                Color(0xFFE4E3E6)  // 浅紫灰色
            )
        )
    } else {
        // 普通模式：使用原有背景
        Brush.verticalGradient(
            colors = listOf(
                backgroundColor.copy(alpha = backgroundAlpha),
                backgroundColor.copy(alpha = backgroundAlpha)
            )
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(currentBackground)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
            .padding(paddingValues) // 应用Scaffold的padding
    ) {
        // 🎨 优化的顶部栏 - 支持Done List模式切换
        CenterAlignedTopAppBar(
            title = { 
                // 🎨 根据模式显示不同标题
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = if (isDoneListMode) "完成列表" else "所有",
                        style = MaterialTheme.typography.titleLarge,
                        fontSize = SystemBarManager.getStandardTitleTextSize(),
                        fontWeight = FontWeight.SemiBold,
                        color = if (isDoneListMode) Color(0xFF374151) else Color(0xFF474947),
                        modifier = Modifier
                    )
                    
                    // 🎨 Done List模式下显示完成数量
                    if (isDoneListMode) {
                        Spacer(modifier = Modifier.width(12.dp))
                        Box(
                            modifier = Modifier
                                .background(
                                    Color(0xFF8B5CF6).copy(alpha = 0.2f),
                                    RoundedCornerShape(12.dp)
                                )
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "${completedTasksList.size}",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF374151),
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            },
            navigationIcon = {
                IconButton(
                    onClick = { 
                        // 简单地返回上一页
                        navController.popBackStack()
                    },
                    modifier = Modifier.padding(8.dp)
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                // 🎨 模式切换按钮
                IconButton(
                    onClick = { 
                        isDoneListMode = !isDoneListMode
                        doneListAnimationTrigger++
                        Log.d("DoneList", "切换到${if (isDoneListMode) "完成" else "任务"}列表模式")
                    }
                ) {
                    Icon(
                        imageVector = if (isDoneListMode) Icons.Default.List else Icons.Default.Done,
                        contentDescription = if (isDoneListMode) "切换到任务列表" else "切换到完成列表",
                        tint = if (isDoneListMode) Color(0xFF374151) else TextPrimary
                    )
                }
                
                // 🔧 修复：只在非Done List模式下显示菜单按钮
                if (!isDoneListMode) {
                    Box {
                        // 创建显式函数避免lambda类型推断问题
                        fun toggleMenu() { showMenu = !showMenu }
                        fun hideMenu() { showMenu = false }
                        fun showAllTasks() {
                            completionFilterMode = CompletionFilterMode.SHOW_ALL
                            sortMode = TaskSortMode.NONE
                            showMenu = false
                        }
                        fun hideCompleted() {
                            completionFilterMode = CompletionFilterMode.HIDE_COMPLETED
                            sortMode = TaskSortMode.NONE
                            showMenu = false
                        }
                        fun showOnlyCompleted() {
                            completionFilterMode = CompletionFilterMode.SHOW_ONLY_COMPLETED
                            sortMode = TaskSortMode.NONE
                            showMenu = false
                        }
                        fun sortByPriority() {
                            sortMode = TaskSortMode.PRIORITY_HIGH_TO_LOW
                            showMenu = false
                        }
                        fun sortByTime() {
                            sortMode = TaskSortMode.TIME_RECENT_TO_EARLIEST
                            showMenu = false
                        }
                        
                        IconButton(onClick = ::toggleMenu) {
                            Icon(
                                Icons.Default.MoreVert,
                                contentDescription = "更多",
                                tint = TextPrimary
                            )
                        }
                    
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = ::hideMenu,
                        offset = DpOffset(0.dp, 10.dp),
                        modifier = Modifier.background(
                            color = Color.White,
                            shape = RoundedCornerShape(8.dp)
                        )
                    ) {
                        // 自定义菜单项样式
                        Column(
                            modifier = Modifier
                                .width(150.dp)
                                .padding(vertical = 6.dp)
                        ) {
                            // 省略菜单标题，直接进入选项
                            
                            // 筛选选项分组
                            Divider(
                                color = LavenderAsh.copy(alpha = 0.2f),
                                thickness = 1.dp,
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 3.dp)
                            )
                            
                            // 显示所有任务选项
                            FilterMenuItem(
                                title = "显示所有任务",
                                isSelected = completionFilterMode == CompletionFilterMode.SHOW_ALL && sortMode == TaskSortMode.NONE,
                                onClick = ::showAllTasks
                            )
                            
                            // 隐藏已完成任务选项
                            FilterMenuItem(
                                title = "隐藏已完成",
                                isSelected = completionFilterMode == CompletionFilterMode.HIDE_COMPLETED && sortMode == TaskSortMode.NONE,
                                onClick = ::hideCompleted
                            )
                            
                            // 只显示已完成任务选项
                            FilterMenuItem(
                                title = "显示已完成",
                                isSelected = completionFilterMode == CompletionFilterMode.SHOW_ONLY_COMPLETED && sortMode == TaskSortMode.NONE,
                                onClick = ::showOnlyCompleted
                            )
                            
                            // 排序选项分组
                            Divider(
                                color = LavenderAsh.copy(alpha = 0.2f),
                                thickness = 1.dp,
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                            )
                            
                            // 按优先级排序
                            FilterMenuItem(
                                title = "优先级排序",
                                isSelected = sortMode == TaskSortMode.PRIORITY_HIGH_TO_LOW,
                                onClick = ::sortByPriority
                            )
                            
                            // 按时间排序
                            FilterMenuItem(
                                title = "按时间排序",
                                isSelected = sortMode == TaskSortMode.TIME_RECENT_TO_EARLIEST,
                                onClick = ::sortByTime
                            )
                        }
                    }
                }
                } // 关闭 if (!isDoneListMode) 的大括号
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent,
                titleContentColor = TextPrimary
            ),
            modifier = Modifier, // 移除固定高度设置
            windowInsets = WindowInsets(0) // 保持windowInsets为0以避免额外边距
        )

        // 🎨 只在任务列表模式显示过滤器标签
        if (!isDoneListMode) {
        // 过滤器标签
        ScrollableTabRow(
            selectedTabIndex = filters.indexOf(selectedFilter),
            edgePadding = 16.dp,
            containerColor = Color.Transparent,
            divider = {},
            indicator = { }, // 设置空指示器移除默认的蓝色下划线
            modifier = Modifier
                .padding(top = 8.dp, bottom = 4.dp)
                .height(48.dp) // 设置固定高度
        ) {
            filters.forEachIndexed { index, filter ->
                
                // 创建显式函数避免lambda类型推断问题
                fun selectFilter() { 
                    selectedFilter = filter
                    // 🎯 关键修复：同步过滤状态到SharedFilterState
                    viewModel.updateSharedFilterState(filter)
                    Log.d("TaskListFullScreen", "选择过滤器: $filter, 已同步到SharedFilterState")
                }
                
                Tab(
                    selected = selectedFilter == filter,
                    onClick = ::selectFilter,
                    modifier = Modifier.padding(horizontal = 3.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(vertical = 4.dp)
                    ) {
                        Text(
                            text = filter,
                            fontSize = 14.sp,
                            fontWeight = if (selectedFilter == filter) FontWeight.Bold else FontWeight.Normal,
                            color = if (selectedFilter == filter) DustyLavender else TextSecondary,
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                        if (selectedFilter == filter) {
                            Box(
                                Modifier
                                    .width(24.dp)
                                    .height(2.dp)
                                    .background(
                                        color = DustyLavender,
                                        shape = MaterialTheme.shapes.small
                                    )
                            )
                        }
                    }
                }
            }
        }
        } // 关闭过滤器标签的if语句

        // 💡 使用自定义下拉刷新包装主内容 - 参考知名应用的交互设计，并支持左右滑动切换标签
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragEnd = {
                            // 处理下拉刷新
                            if (pullOffset > refreshTriggerDistance.value && !isRefreshing) {
                                smartRefresh("用户下拉刷新")
                            } else if (!isRefreshing) {
                                pullOffset = 0f
                            }
                            
                            // 🎨 处理左右滑动切换标签
                            if (abs(horizontalDragOffset) > swipeThreshold) {
                                if (horizontalDragOffset > 0) {
                                    // 向右滑动 -> 切换到上一个标签
                                    switchToPreviousTab()
                                } else {
                                    // 向左滑动 -> 切换到下一个标签
                                    switchToNextTab()
                                }
                            }
                            
                            // 重置滑动偏移和提示
                            horizontalDragOffset = 0f
                            showSwipeHint = false
                        }
                    ) { change, dragAmount ->
                        // 处理垂直拖拽（下拉刷新）
                        if (listState.firstVisibleItemIndex == 0 && 
                            listState.firstVisibleItemScrollOffset == 0 && 
                            dragAmount.y > 0 && !isRefreshing) {
                            pullOffset = (pullOffset + dragAmount.y).coerceAtMost(maxPullDistance.value)
                        }
                        
                        // 🎨 处理水平拖拽（标签切换）
                        // 只有在非完成列表模式下才支持标签切换
                        if (!isDoneListMode) {
                            horizontalDragOffset += dragAmount.x
                            
                            // 显示滑动提示
                            if (abs(horizontalDragOffset) > 50f) {
                                showSwipeHint = true
                                swipeDirection = if (horizontalDragOffset > 0) "上一个" else "下一个"
                            } else {
                                showSwipeHint = false
                            }
                        }
                    }
                }
        ) {
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 8.dp)
                    .graphicsLayer(
                        alpha = contentAlpha,
                        scaleX = 1f, // 🎯 移除缩放动画，保持稳定
                        scaleY = 1f, // 🎯 移除缩放动画，保持稳定
                        translationY = if (isRefreshing) refreshTriggerDistance.value / 2 else pullOffset / 2
                    ), // 🎯 简化动画，只保留必要的位移
                contentAlignment = Alignment.Center
            ) {
                // 💡 主内容区域 - 优化加载状态和过渡动画
                if (taskListState.isLoading && taskListState.tasks.isEmpty() && !isRefreshing) {
                    // 💡 高级骨架屏 - 参考Notion的设计
                    EnhancedSkeletonTaskList()
                } else if (isDoneListMode) {
                    // 🎨 Done List 模式的特殊UI
                    DoneListContent(
                        completedTasks = completedTasksList,
                        onTaskClick = { task ->
                            selectedTask = task
                            showBottomSheet = true
                        },
                        viewModel = viewModel // 传递ViewModel
                    )
                } else {
                    // 原始的任务列表显示逻辑保持不变
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 4.dp) // 进一步减少水平padding，最大化卡片宽度
                            // 💡 优化：更平滑的透明度过渡，避免突兀
                            .alpha(listAlpha.value),
                        verticalArrangement = Arrangement.spacedBy(6.dp),
                        contentPadding = PaddingValues(vertical = 8.dp)
                    ) {
                        itemsIndexed(
                            items = filteredTasks,
                            key = { _, task -> task.id },
                            contentType = { _, task -> task.isCompleted }
                        ) { index, task ->
                            // 创建显式函数避免lambda类型推断问题
                            fun onTaskClick() {
                                selectedTask = task
                                showBottomSheet = true
                            }
                            
                            fun onCheckChange(isChecked: Boolean) {
                                // 📝 修复复选框状态切换逻辑：
                                // - 未完成任务点击 -> 完成（显示反馈对话框）
                                // - 已完成任务点击 -> 撤销完成（不显示反馈对话框）
                                
                                Log.d("TaskStatus", "[TaskListFullScreen] 任务状态切换: ${task.title}")
                                Log.d("TaskStatus", "[TaskListFullScreen] 当前状态: ${task.isCompleted} -> 目标状态: $isChecked")
                                
                                if (!task.isCompleted && isChecked) {
                                    // 未完成 -> 完成：显示反馈对话框
                                    feedbackTaskId = task.id
                                    feedbackTaskTitle = task.title
                                    showFeedbackDialog = true
                                    Log.d("TaskStatus", "[TaskListFullScreen] 任务完成，显示反馈对话框")
                                } else if (task.isCompleted && !isChecked) {
                                    // 已完成 -> 撤销完成：直接切换状态
                                    Log.d("TaskStatus", "[TaskListFullScreen] 撤销任务完成")
                                }
                                
                                // 🔧 调用状态更新方法
                                handleTaskStatusChanged(task.id, isChecked)
                            }
                            
                            fun onDeleteTask(taskId: String) {
                                // 🔧 修复删除操作：完全避免组件协程作用域依赖
                                try {
                                    Log.d("TaskDelete", "[TaskListFullScreen] 开始删除任务: $taskId")
                                    
                                    // 立即调用ViewModel的删除方法，ViewModel内部会处理所有逻辑
                                    // 包括删除操作、Snackbar显示和撤销功能
                                    viewModel.deleteTaskWithUndo(taskId)
                                    
                                    Log.d("TaskDelete", "[TaskListFullScreen] 删除操作已提交: $taskId")
                                    
                                } catch (e: Exception) {
                                    Log.e("TaskDelete", "[TaskListFullScreen] 删除任务失败: $taskId", e)
                                }
                            }
                            
                            // 🎨 简化微动画 - 只保留基础淡入，移除复杂的滑动动画
                            androidx.compose.animation.AnimatedVisibility(
                                visible = itemAnimationTrigger > 0,
                                enter = fadeIn(
                                    animationSpec = tween(
                                        durationMillis = 150, // 🎯 进一步减少动画时长
                                        delayMillis = (index * 15).coerceAtMost(150), // 🎯 减少延迟，提升响应性
                                        easing = LinearEasing // 🎯 使用线性缓动，更简洁
                                    )
                                )
                            ) {
                            NewEnhancedTaskItem(
                                task = task,
                                onTaskClick = ::onTaskClick,
                                onCheckChange = ::onCheckChange,
                                onDeleteTask = ::onDeleteTask,
                                elevation = 0f, // 取消阴影
                                navController = navController
                            )
                            }
                        }

                        // 显示空状态 - 简化动画效果
                        if (filteredTasks.isEmpty()) {
                            item {
                                androidx.compose.animation.AnimatedVisibility(
                                    visible = !taskListState.isLoading,
                                    enter = fadeIn(
                                        animationSpec = tween(durationMillis = 400, easing = FastOutSlowInEasing)
                                    ) + scaleIn(
                                        initialScale = 0.9f, // 减少缩放幅度，更自然
                                        animationSpec = tween(durationMillis = 400, easing = FastOutSlowInEasing)
                                    )
                                ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Task,
                                            contentDescription = null,
                                            tint = TextSecondary.copy(alpha = 0.5f),
                                            modifier = Modifier.size(48.dp)
                                        )
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Text(
                                            text = "暂无任务",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = TextSecondary.copy(alpha = 0.7f)
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Text(
                                            text = "点击右下角按钮添加新任务",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = TextSecondary.copy(alpha = 0.7f),
                                            fontSize = 14.sp
                                        )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 💡 自定义刷新指示器 - 参考TickTick的设计
            if (refreshIndicatorAlpha > 0f) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp)
                        .alpha(refreshIndicatorAlpha)
                        .offset(y = (indicatorOffset - 40).dp),
                    contentAlignment = Alignment.Center
                ) {
                    // 背景圆形
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .background(
                                color = Color.White,
                                shape = CircleShape
                            )
                            .clip(CircleShape)
                    ) {
                        // 阴影效果
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    color = Color(0xFFF8F9FA),
                                    shape = CircleShape
                                )
                        )
                    }
                    
                    // 刷新图标或进度
                    if (isRefreshing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp,
                            color = DustyLavender
                        )
                    } else {
                        // 下拉进度指示器
                        val progress = (pullOffset / refreshTriggerDistance.value).coerceIn(0f, 1f)
                        Canvas(modifier = Modifier.size(24.dp)) {
                            val strokeWidth = 3.dp.toPx()
                            
                            // 背景圆弧
                            drawArc(
                                color = Color(0xFFE9ECEF),
                                startAngle = -90f,
                                sweepAngle = 360f,
                                useCenter = false,
                                style = androidx.compose.ui.graphics.drawscope.Stroke(strokeWidth)
                            )
                            
                            // 进度圆弧
                            if (progress > 0f) {
                                drawArc(
                                    color = DustyLavender,
                                    startAngle = -90f,
                                    sweepAngle = 360f * progress,
                                    useCenter = false,
                                    style = androidx.compose.ui.graphics.drawscope.Stroke(strokeWidth)
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // 🎨 滑动提示覆盖层
    if (showSwipeHint) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Text(
                    text = "切换到${swipeDirection}标签",
                    modifier = Modifier.padding(16.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }

    // 悬浮添加按钮 - 添加长按语音识别功能
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomEnd
    ) {
        // 使用ViewModel的状态判断是否加载完成
        if (!taskListState.isLoading) {
            // 检查麦克风权限 - 暂不请求
            val hasRecordPermission = true  // 暂时直接假设有权限
            
            // 初始化语音识别器
            LaunchedEffect(context) {
                if (hasRecordPermission && speechRecognizer == null) {
                    speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
                    
                    speechRecognizer?.setRecognitionListener(object : RecognitionListener {
                        override fun onReadyForSpeech(params: Bundle?) {}
                        override fun onBeginningOfSpeech() {}
                        override fun onRmsChanged(rmsdB: Float) {
                            // 将RMS值映射到0-1范围用于波形动画
                            audioLevel = (rmsdB + 20) / 40f // 通常RMS值在-20到20之间
                            audioLevel = max(0f, min(1f, audioLevel))
                            
                            // 更新波形振幅
                            waveAmplitudes.forEachIndexed { index, state ->
                                state.value = audioLevel * (0.5f + Random.nextFloat() * 0.5f)
                            }
                        }
                        override fun onBufferReceived(buffer: ByteArray?) {}
                        override fun onEndOfSpeech() {
                            isListening = false
                        }
                        override fun onError(error: Int) {
                            isListening = false
                            if (error != SpeechRecognizer.ERROR_NO_MATCH) {
                                recognizedText = "无法识别，请重试"
                            }
                        }
                        override fun onResults(results: Bundle?) {
                            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                            val text = matches?.get(0) ?: ""
                            recognizedText = text
                            
                            // 设置待处理文本
                            pendingVoiceText = text
                            
                            // 延迟关闭语音界面，给用户看结果的时间
                            coroutineScope.launch {
                                delay(2000)
                                showVoiceInputSheet = false
                            }
                        }
                        override fun onPartialResults(partialResults: Bundle?) {
                            val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                            if (matches != null && matches.isNotEmpty()) {
                                recognizedText = matches[0]
                            }
                        }
                        override fun onEvent(eventType: Int, params: Bundle?) {}
                    })
                }
            }
            
            // 长按悬浮按钮开始语音输入
            val interactionSource = remember { MutableInteractionSource() }
            val isPressed by interactionSource.collectIsPressedAsState()
            val haptic = LocalHapticFeedback.current
            
            // 根据按压状态更新语音识别
            LaunchedEffect(isPressed) {
                if (isPressed && hasRecordPermission) {
                    isHolding = true
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    delay(300) // 长按0.3秒后开始语音识别
                    
                    if (isHolding) {
                        // 开始语音识别流程
                        isListening = true
                        showVoiceInputSheet = true // 显示底部弹出框
                        recognizedText = "说出你要做的事..."
                        
                        // 再次触发轻微触觉反馈，指示识别开始
                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        
                        // 开始语音听写
                        val recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
                            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN") // 设置为中文
                        }
                        
                        // 启动语音监听并安排波形动画
                        speechRecognizer?.startListening(recognizerIntent)
                        
                        // 为波形条启动异步动画
                        speechListenerJob = launch {
                            while (isListening) {
                                waveAmplitudes.forEachIndexed { index, state ->
                                    launch {
                                        // 根据当前音量和随机因子生成目标值
                                        val targetHeight = state.value * Random.nextFloat() * 0.5f + 0.5f
                                        
                                        // 使用不同持续时间为每个条创建交错动画
                                        state.value = targetHeight
                                    }
                                }
                                delay(150) // 控制动画更新频率
                            }
                        }
                    }
                } else if (!isPressed && isHolding) {
                    isHolding = false
                    
                    // 如果正在识别，停止识别
                    if (isListening) {
                        isListening = false
                        speechRecognizer?.stopListening()
                        speechListenerJob?.cancel()
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    }
                }
            }
            
            // 🎨 优化：只在非Done List模式下显示浮动添加按钮，添加简洁的进入动画
            if (!isDoneListMode) {
                AnimatedVisibility(
                    visible = !taskListState.isLoading,
                    enter = fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing)
                    ) + scaleIn(
                        initialScale = 0.8f,
                        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing)
                    ),
                    exit = fadeOut(
                        animationSpec = tween(durationMillis = 200)
                    ) + scaleOut(
                        targetScale = 0.8f,
                        animationSpec = tween(durationMillis = 200)
                    )
                ) {
                    FloatingActionButton(
                        onClick = {
                            // 短按直接导航到任务编辑页面
                            if (!isListening) {
                                navController.navigate(AppDestinations.ADD_TASK_ROUTE)
                            }
                        },
                        modifier = Modifier
                            .padding(end = 16.dp, bottom = 100.dp)
                            .size(56.dp)
                            .scale(micScale.value),
                        interactionSource = interactionSource,
                        containerColor = Color(0xFF8BAA80),
                        contentColor = Color.White,
                        shape = CircleShape,
                        elevation = FloatingActionButtonDefaults.elevation(
                            defaultElevation = 0.dp, // 取消阴影
                            pressedElevation = 0.dp   // 取消按压阴影
                        )
                    ) {
                        // 🎨 图标切换动画
                        AnimatedContent(
                            targetState = isListening,
                            transitionSpec = {
                                fadeIn(animationSpec = tween(150)) with
                                fadeOut(animationSpec = tween(150))
                            },
                            label = "iconAnimation"
                        ) { listening ->
                            Icon(
                                imageVector = if (listening) Icons.Default.Mic else Icons.Default.Add, 
                                contentDescription = if (listening) "正在录音" else "添加任务",
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }
            }
        }
    }

    // 注释掉旧的TaskTimeManager监听，使用新的TaskTimeViewModel
    /*
    // 监听TaskTimeManager的时间更新事件
    DisposableEffect(Unit) {
        val timeUpdateJob = coroutineScope.launch {
            taskTimeManager.timeUpdateEvents.collect { event ->
                Log.d("TaskListFullScreen", "收到时间更新事件: taskId=${event.taskId}, 新时间=${event.newDateTime}")
                
                // 更新选中任务的时间显示
                if (selectedTask?.id == event.taskId) {
                    selectedTask = selectedTask?.copy(
                        dueDate = event.newDateTime,
                        daysLeft = event.newDateTime?.let { calculateDaysLeft(it) } ?: Int.MAX_VALUE
                    )
                    Log.d("TaskListFullScreen", "已更新选中任务时间显示")
                }
                
                // 强制刷新任务列表以显示最新时间
                delay(100) // 短暂延迟确保数据同步
                viewModel.refreshTasks()
                Log.d("TaskListFullScreen", "已刷新任务列表")
            }
        }
        
        onDispose {
            timeUpdateJob.cancel()
            Log.d("TaskListFullScreen", "已取消时间更新事件监听")
        }
    }
    */

    // 额外监听TaskTimeViewModel的UI状态变化
    LaunchedEffect(Unit) {
        taskTimeViewModel.uiState.collect { state ->
            if (state.lastUpdatedTaskId != null) {
                Log.d("TaskListFullScreen", "检测到任务时间状态变化: ${state.lastUpdatedTaskId}")
                
                // 强制刷新任务列表
                delay(50) // 短暂延迟确保数据同步
                viewModel.refreshTasks()
            }
        }
    }

    // 显式函数，避免实验性功能问题
    fun handleTaskUpdateFunction(updatedTaskData: TaskData) {
        handleTaskUpdate(updatedTaskData)
    }
    } // 关闭Scaffold
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun AnimatedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDeleteTask: (String) -> Unit = {},
    navController: NavController
) {
    // 添加悬停状态
    var isPressed by remember { mutableStateOf(false) }
    
    // 悬停动画
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "itemScale"
    )
    
    val elevation by animateFloatAsState(
        targetValue = if (isPressed) 0.5f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "itemElevation"
    )
    
    Box(
        modifier = Modifier
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale
            )
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onTaskClick() }
                )
            }
    ) {
    NewEnhancedTaskItem(
        task = task,
        onTaskClick = { onTaskClick() },
        onCheckChange = { isChecked -> onCheckChange(isChecked) },
        onDeleteTask = { taskId -> onDeleteTask(taskId) },
        elevation = elevation,
        navController = navController
    )
    }
}

@Composable
private fun EnhancedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDeleteTask: (String) -> Unit = {}, // 添加删除任务的回调函数
    elevation: Float
) {
    // 🚀 优化状态管理：使用derivedStateOf确保状态变化立即反映
    val isChecked by remember(task.id, task.isCompleted) { 
        derivedStateOf { task.isCompleted }
    }
    
    // 🔧 添加强制重组机制：当任务状态发生变化时，强制UI重新渲染
    val forceRecompose by remember(task.id) { mutableStateOf(0) }
    LaunchedEffect(task.isCompleted) {
        // 当完成状态变化时，触发重组
        Log.d("UIUpdate", "[EnhancedTaskItem] 任务 ${task.id} 完成状态变化: ${task.isCompleted}")
    }
    
    val textDecoration = remember(isChecked) { 
        if (isChecked) TextDecoration.LineThrough else TextDecoration.None 
    }
    val textColor = remember(isChecked) { 
        if (isChecked) TextSecondary.copy(alpha = 0.6f) else TextPrimary 
    }
    val urgencyColor = remember(task.urgency) { getUrgencyColor(task.urgency) }
    val timeText = remember(task.dueDate, task.daysLeft) {
        // 优先使用dueDate，如果有的话
        if (task.dueDate != null) {
            formatDetailedTimeRangeFromDate(task.dueDate)
        } else if (task.daysLeft != Int.MAX_VALUE) {
            // 只有当daysLeft有效时才使用它
            formatDetailedTimeRangeFromDaysLeft(task.daysLeft)
        } else {
            "未设置时间" // 添加默认显示
        }
    }
    
    // 添加标题编辑状态
    var isEditing by remember { mutableStateOf(false) }
    var editedTitle by remember(task.title) { mutableStateOf(task.title) }
    
    // 触觉反馈
    val haptic = LocalHapticFeedback.current
    val viewModel: TaskListViewModel = hiltViewModel()
    val coroutineScope = rememberCoroutineScope()
    
    // 保存标题变更的函数
    val saveTitle = {
        if (isEditing && editedTitle.isNotBlank() && editedTitle != task.title) {
            viewModel.updateTaskTitle(task.id, editedTitle)
        }
        isEditing = false
    }
    
    // 添加滑动状态
    var isDeleting by remember { mutableStateOf(false) }
    var isCompleting by remember { mutableStateOf(false) }
    
    val dismissState = rememberDismissState(
        initialValue = DismissValue.Default,
        confirmStateChange = { value ->
            when (value) {
                DismissValue.DismissedToStart -> {
                    // 左滑删除
                    isDeleting = true
                    onDeleteTask(task.id)
                    true
                }
                DismissValue.DismissedToEnd -> {
                    // 右滑完成 - 修复逻辑：只有未完成的任务才能通过右滑完成
                    if (!isChecked) {
                        // 任务未完成 -> 右滑完成任务
                        isCompleting = true
                        Log.d("SwipeToComplete", "右滑完成任务: ${task.title}")
                        onCheckChange(true)
                    } else {
                        // 任务已完成 -> 右滑无效: ${task.title}")
                    }
                    false
                }
                else -> false
            }
        }
    )
    
    // 🔧 添加状态监听，确保dismissState正确重置
    LaunchedEffect(dismissState.currentValue) {
        if (dismissState.currentValue != DismissValue.Default) {
            Log.d("SwipeToComplete", "检测到dismissState异常状态: ${dismissState.currentValue}")
            delay(500) // 等待动画完成
            try {
                dismissState.snapTo(DismissValue.Default)
                Log.d("SwipeToComplete", "dismissState已重置为Default")
            } catch (e: Exception) {
                Log.e("SwipeToComplete", "重置dismissState失败", e)
            }
        }
    }
    
    // 移除复杂的状态监听，简化为基本功能
    val isDismissed = dismissState.isDismissed(DismissDirection.EndToStart) || 
                    dismissState.isDismissed(DismissDirection.StartToEnd)
    
    // 计算滑动进度 (0.0-1.0)
    val dismissProgressLeft = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToStart -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    val dismissProgressRight = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToEnd -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    // 图标旋转动画
    val deleteIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToStart) -45f else 0f,
        label = "deleteIconRotation"
    )
    
    val completeIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToEnd) 45f else 0f,
        label = "completeIconRotation" 
    )

    // 当滑动超过阈值时触发触觉反馈
    LaunchedEffect(dismissState.targetValue) {
        if (dismissState.targetValue == DismissValue.DismissedToStart || 
            dismissState.targetValue == DismissValue.DismissedToEnd) {
            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
        }
    }
    
    // 使用外层Box包裹，控制整体布局
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp) // 减小水平内边距，增加卡片宽度
    ) {
        // 滑动删除组件
        SwipeToDismiss(
            state = dismissState,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 2.dp),
            directions = setOf(DismissDirection.EndToStart, DismissDirection.StartToEnd), // 添加右滑方向
            dismissThresholds = { direction -> FractionalThreshold(0.15f) }, // 降低滑动阈值，轻微滑动就触发
            background = {
                Row(
                    modifier = Modifier.fillMaxSize(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 右滑完成背景 (左侧)
                    if (dismissProgressRight > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(start = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF4CAF50)), // 绿色完成按钮
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "完成",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(completeIconRotation)
                                )
                            }
                        }
                    }
                    
                    // 占位Spacer
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 左滑删除背景 (右侧)
                    if (dismissProgressLeft > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(end = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFFEF5350)), // 红色删除按钮
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(deleteIconRotation)
                                )
                            }
                        }
                    }
                }
            },
            dismissContent = {
                // 任务项内容 - 使用Card替代Box增强视觉效果
                val cardScale by animateFloatAsState(
                    targetValue = 1f - (max(dismissProgressLeft, dismissProgressRight) * 0.03f), // 滑动时卡片轻微缩小
                    label = "cardScale"
                )
                val cardElevation by animateFloatAsState(
                    targetValue = 1f + (max(dismissProgressLeft, dismissProgressRight) * 2f), // 滑动时阴影增大
                    label = "cardElevation"
                )
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .scale(cardScale),
                    shape = RoundedCornerShape(12.dp), // 减小圆角
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFfcfcfc) // 确保背景色为rgb(247, 247, 246)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 0.dp
                    ),
                    onClick = { 
                        // 如果处于编辑状态，点击卡片其他区域时保存更改并退出编辑模式
                        val isEditingTitle = isEditing
                        if (isEditingTitle) {
                            saveTitle()
                        } else {
                            onTaskClick()
                        }
                    }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 使用静态颜色替代动态计算
                        Box(
                            modifier = Modifier
                                .size(width = 4.dp, height = 36.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(urgencyColor)
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        // 优化复选框，移除动画效果
                        Checkbox(
                            checked = isChecked,
                            onCheckedChange = onCheckChange,
                            colors = CheckboxDefaults.colors(
                                checkedColor = DustyLavender,
                                uncheckedColor = LavenderAsh.copy(alpha = 0.7f)
                            ),
                            modifier = Modifier
                                .size(20.dp)
                                .padding(end = 4.dp),
                            interactionSource = NoRippleInteractionSource
                        )

                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 8.dp)
                        ) {
                            // 替换原有的Text组件为可编辑的标题组件
                            
                            if (isEditing) {
                                // 编辑模式 - 显示TextField
                                val focusRequester = remember { FocusRequester() }
                                TextField(
                                    value = editedTitle,
                                    onValueChange = { editedTitle = it },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 0.dp)
                                        .focusRequester(focusRequester),
                                    textStyle = MaterialTheme.typography.bodyLarge.copy(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    ),
                                    colors = TextFieldDefaults.colors(
                                        focusedContainerColor = Color.Transparent,
                                        unfocusedContainerColor = Color.Transparent,
                                        disabledContainerColor = Color.Transparent,
                                        focusedIndicatorColor = DustyLavender,
                                        unfocusedIndicatorColor = Color.Transparent
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(
                                        imeAction = ImeAction.Done
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = { 
                                            if (editedTitle.isNotBlank()) {
                                                saveTitle()
                                            }
                                        }
                                    )
                                )
                                
                                // 自动获取焦点
                                LaunchedEffect(Unit) {
                                    delay(100)
                                    try {
                                        focusRequester.requestFocus()
                                    } catch (e: Exception) {
                                        Log.e("TaskListFullScreen", "请求焦点失败", e)
                                    }
                                }
                                
                                // 监听点击外部事件，保存更改并退出编辑模式
                                DisposableEffect(Unit) {
                                    onDispose {
                                        saveTitle()
                                    }
                                }
                            } else {
                                // 非编辑模式 - 显示普通文本，支持长按编辑
                                Text(
                                    text = task.title,
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontSize = 16.sp,
                                    fontWeight = if (!isChecked) FontWeight.Medium else FontWeight.Normal,
                                    textDecoration = textDecoration,
                                    color = textColor,
                                    maxLines = 2,
                                    overflow = TextOverflow.Ellipsis,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .combinedClickable(
                                            onClick = { /* 短按由外层Card处理 */ },
                                            onLongClick = { 
                                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                                isEditing = true 
                                            }
                                        )
                                )
                            }

                            Spacer(modifier = Modifier.height(6.dp))

                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 使用Box替代Surface以提高性能
                                Box(
                                    modifier = Modifier
                                        .clip(RoundedCornerShape(4.dp))
                                        .background(LavenderAsh.copy(alpha = 0.2f))
                                        .padding(horizontal = 6.dp, vertical = 2.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Schedule,
                                            contentDescription = null,
                                            tint = DustyLavender.copy(alpha = 0.8f),
                                            modifier = Modifier.size(12.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text(
                                            text = timeText,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontSize = 12.sp,
                                            color = DustyLavender.copy(alpha = 0.8f)
                                        )
                                    }
                                }

                                task.tag?.let {
                                    Spacer(modifier = Modifier.width(8.dp))
                                    OptimizedTagChip(it, isPrimary = true)
                                }
                            }
                        }
                    }
                }
            }
        )
    }
}

@Composable
private fun OptimizedTagChip(tag: String, isPrimary: Boolean = false) {
    // 使用remember缓存颜色值和样式
    val backgroundColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFFF7F3DE) // 主标签使用淡黄色背景
        else 
            LavenderAsh.copy(alpha = 0.15f) // 次要标签使用淡紫色背景
    }
        
    val textColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFF8B7E4B) // 主标签文字颜色
        else 
            TextSecondary // 次要标签文字颜色
    }

    val fontWeight = remember(isPrimary) {
        if (isPrimary) FontWeight.Medium else FontWeight.Normal
    }

    // 使用Box替代Surface以提高性能
    Box(
        modifier = Modifier
            .height(20.dp) // 与时间显示区域保持相同高度
            .clip(RoundedCornerShape(4.dp)) // 保持原有的圆角大小
            .background(backgroundColor)
            .padding(horizontal = 8.dp, vertical = 2.dp),
        contentAlignment = Alignment.Center // 确保文字垂直居中
    ) {
        Text(
            text = tag,
            fontSize = 10.sp, // 与时间显示保持一致的字体大小
            color = textColor,
            fontWeight = fontWeight,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.bodySmall // 确保使用与时间显示相同的文字样式
        )
    }
}

// 🎯 优化"今天"过滤逻辑，让未完成的未定期任务也能显示
private fun isTaskForToday(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    
    return when {
        // 如果有具体的dueDate日期，则直接与今天比较
        task.dueDate != null -> {
            task.dueDate.toLocalDate().isEqual(today)
        }
        // 通过daysLeft计算，如果是0天，则表示今天
        task.daysLeft == 0 -> true
        // 🎯 关键修复：对于未定期且未完成的任务，也在"今天"显示，让用户可以处理
        task.daysLeft == Int.MAX_VALUE && !task.isCompleted -> {
            Log.d("TaskListFullScreen", "未定期任务 ${task.id} (${task.title}) 将在今天显示")
            true
        }
        else -> false
    }
}

// 🎯 修复明天日期计算问题
private fun isTaskForTomorrow(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    val tomorrow = today.plusDays(1)
    
    return when {
        // 🎯 先排除未定期任务（daysLeft为Int.MAX_VALUE）
        task.daysLeft == Int.MAX_VALUE -> false
        
        // 如果有具体的dueDate日期，则直接与明天比较
        task.dueDate != null -> {
            task.dueDate.toLocalDate().isEqual(tomorrow)
        }
        // 通过daysLeft计算，如果正好是1天，则表示明天
        task.daysLeft == 1 -> true
        // 或者通过daysLeft计算出来的日期与明天相等
        task.daysLeft > 0 && task.daysLeft < Int.MAX_VALUE -> {
            val dueDate = today.plusDays(task.daysLeft.toLong())
            dueDate.isEqual(tomorrow)
        }
        else -> false
    }
}

// 判断任务是否为本周的任务
private fun isTaskForThisWeek(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    val endOfWeek = today.plusDays(7 - today.dayOfWeek.value.toLong())
    
    return when {
        // 如果有具体的dueDate日期，则判断是否在本周内
        task.dueDate != null -> {
            val dueDate = task.dueDate.toLocalDate()
            !dueDate.isAfter(endOfWeek) && !dueDate.isBefore(today)
        }
        // 通过daysLeft计算，判断是否在本周内（0-6天）
        task.daysLeft >= 0 && task.daysLeft <= 6 -> true
        // 或者通过daysLeft计算出来的日期在本周内
        task.daysLeft > 0 -> {
            val dueDate = today.plusDays(task.daysLeft.toLong())
            !dueDate.isAfter(endOfWeek) && !dueDate.isBefore(today)
        }
        else -> false
    }
}

// 判断任务是否为未定期任务（没有设置任务时间）
private fun isTaskUpcoming(task: ModelTaskData): Boolean {
    return when {
        // 如果没有设置具体的dueDate日期，则属于未定期任务
        task.dueDate == null -> true
        // 或者daysLeft设置为Int.MAX_VALUE，表示未定期任务
        task.daysLeft == Int.MAX_VALUE -> true
        else -> false
    }
}

// 筛选模式枚举
enum class CompletionFilterMode {
    SHOW_ALL,          // 显示所有任务
    HIDE_COMPLETED,    // 隐藏已完成任务
    SHOW_ONLY_COMPLETED // 只显示已完成任务
}

// 排序模式枚举
enum class TaskSortMode {
    NONE,                    // 不排序
    PRIORITY_HIGH_TO_LOW,    // 优先级从高到低
    TIME_RECENT_TO_EARLIEST  // 时间从最近到最早
}

// 🔄 撤销任务完成的信息数据类
data class UndoTaskInfo(
    val taskId: String,
    val taskTitle: String,
    val completedAt: Long = System.currentTimeMillis(),
    val originalTask: ModelTaskData
)

// 🔄 30秒撤销计时器管理
class UndoTimer(
    private val onExpired: (String) -> Unit
) {
    private val timers = mutableMapOf<String, Job>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    fun startTimer(taskId: String, durationMs: Long = 30000L) {
        // 取消之前的计时器
        timers[taskId]?.cancel()
        
        // 启动新的计时器
        timers[taskId] = coroutineScope.launch {
            delay(durationMs)
            onExpired(taskId)
            timers.remove(taskId)
        }
    }
    
    fun cancelTimer(taskId: String) {
        timers[taskId]?.cancel()
        timers.remove(taskId)
    }
    
    fun cancelAllTimers() {
        timers.values.forEach { it.cancel() }
        timers.clear()
    }
}

// 自定义菜单项组件
@Composable
private fun FilterMenuItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            // 使用 NoRippleInteractionSource 来避免菜单项的 RippleDrawable 动画
            .clickable(
                interactionSource = NoRippleInteractionSource,
                indication = null,
                onClick = onClick
            )
            .background(if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent)
            .padding(horizontal = 16.dp, vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isSelected) DustyLavender else TextPrimary,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            fontSize = 14.sp
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// 语音输入内容组件
@Composable
private fun VoiceInputContent(
    onClose: () -> Unit,
    isListening: Boolean,
    setIsListening: (Boolean) -> Unit,
    recognizedText: String,
    setRecognizedText: (String) -> Unit,
    audioLevel: Float,
    setAudioLevel: (Float) -> Unit,
    speechRecognizer: SpeechRecognizer?,
    setSpeechRecognizer: (SpeechRecognizer?) -> Unit,
    speechListenerJob: Job?,
    setSpeechListenerJob: (Job?) -> Unit,
    isHolding: Boolean,
    setIsHolding: (Boolean) -> Unit,
    pendingVoiceText: String?,
    setPendingVoiceText: (String?) -> Unit,
    waveAmplitudes: List<MutableState<Float>>,
    navController: NavController,
    viewModel: TaskListViewModel
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val hapticFeedback = LocalHapticFeedback.current
    val primaryColor = Color(0xFFB6AFC7) // 使用指定的按钮颜色
    
    // 初始化语音识别器
    LaunchedEffect(context) {
        if (speechRecognizer == null) {
            setSpeechRecognizer(SpeechRecognizer.createSpeechRecognizer(context))
        }
    }
    
    // 处理语音识别回调
    LaunchedEffect(speechRecognizer) {
        speechRecognizer?.setRecognitionListener(object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                setIsListening(true)
            }
            
            override fun onBeginningOfSpeech() {}
            
            override fun onRmsChanged(rmsdB: Float) {
                // 将RMS转换为0-1之间的值
                val normalizedLevel = (rmsdB + 100) / 100
                setAudioLevel(normalizedLevel.coerceIn(0f, 1f))
                
                // 使波形产生随机波动
                coroutineScope.launch {
                    waveAmplitudes.forEach { amplitude ->
                        val currentValue = amplitude.value
                        val randomFactor = Random.nextFloat() * 0.4f - 0.2f
                        val newValue = (normalizedLevel + randomFactor).coerceIn(0.05f, 1f)
                        amplitude.value = newValue
                    }
                }
            }
            
            override fun onBufferReceived(buffer: ByteArray?) {}
            
            override fun onEndOfSpeech() {
                setIsListening(false)
            }
            
            override fun onError(error: Int) {
                setIsListening(false)
                Log.e("VoiceInput", "Speech recognition error: $error")
            }
            
            override fun onResults(results: Bundle?) {
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    val recognizedText = matches[0]
                    setRecognizedText(recognizedText)
                    
                    // 设置待处理文本
                    setPendingVoiceText(recognizedText)
                    
                    // 关闭语音输入
                    onClose()
                }
            }
            
            override fun onPartialResults(partialResults: Bundle?) {
                val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    setRecognizedText(matches[0])
                }
            }
            
            override fun onEvent(eventType: Int, params: Bundle?) {}
        })
    }
    
    // 启动语音识别
    LaunchedEffect(Unit) {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN")
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }
        
        speechRecognizer?.startListening(intent)
    }
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题
        Text(
            text = "说出你要做的事",
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            ),
            color = Color.Black,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 识别结果显示区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color(0xFFF6F5F9),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(vertical = 24.dp, horizontal = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            if (recognizedText.isNotEmpty()) {
                Text(
                    text = recognizedText,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.DarkGray,
                    textAlign = TextAlign.Center
                )
            } else {
            Text(
                    text = "无法识别，请重试",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 音量波形可视化
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                waveAmplitudes.forEach { amplitude ->
                    Box(
                        modifier = Modifier
                            .width(3.dp)
                            .height((50 * amplitude.value).dp + 5.dp)
                            .background(
                                primaryColor.copy(alpha = 0.7f + 0.3f * amplitude.value),
                                RoundedCornerShape(1.5.dp)
                            )
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 确认按钮
        Box(
            modifier = Modifier
                .size(72.dp)
                .background(primaryColor, CircleShape)
                .clickable {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    if (recognizedText.isNotEmpty()) {
                        setPendingVoiceText(recognizedText)
                        onClose()
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "确认",
                tint = Color.White,
                modifier = Modifier.size(36.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 底部提示
            Text(
                text = "上滑可取消输入",
                style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray,
            textAlign = TextAlign.Center
            )
    }
}

/**
 * 格式化剩余时间为详细格式，使用实际的日期而不是计算，确保和详情页面一致
 * 例如: "6月5日, 06:06 - 07:06" 或跨天时 "6月5日, 23:00 - 6月6日, 00:00"
 */
private fun formatDetailedTimeRangeFromDate(dueDate: LocalDateTime): String {
    val inputDate = dueDate.toLocalDate()
    val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endDateTime = dueDate.plusHours(1)
    val endTime = endDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endDate = endDateTime.toLocalDate()

    // 添加日志辅助调试
    Log.d("TimeFormatting", "格式化日期: $dueDate, 结束时间: $endDateTime")

    // 检查是否跨天
    val isCrossDay = !inputDate.isEqual(endDate)

    return when {
        // 本年内 (显示月日)
        inputDate.year == LocalDate.now().year -> {
            if (isCrossDay) {
                // 跨天显示：显示开始日期和结束日期
                "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - ${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                // 同一天：只显示一个日期
                "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 其他年份 (显示年月日)
        else -> {
            if (isCrossDay) {
                // 跨天显示：显示开始日期和结束日期
                "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - ${endDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $endTime"
            } else {
                // 同一天：只显示一个日期
                "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - $endTime"
            }
        }
    }
}

/**
 * 从daysLeft计算并格式化时间
 */
private fun formatDetailedTimeRangeFromDaysLeft(daysLeft: Int): String {
    val today = LocalDateTime.now()
    val dueDate = today.plusDays(daysLeft.toLong())
    
    // 添加日志辅助调试
    Log.d("TimeFormatting", "从天数($daysLeft)计算日期: $dueDate")
    
    // 复用formatDetailedTimeRangeFromDate函数保持一致性
    return formatDetailedTimeRangeFromDate(dueDate)
}

// 将 SubTask 转换为 TaskModel
private fun convertModelSubTaskToModelTaskData(subTask: SubTask): ModelTaskData {
    return ModelTaskData(
        id = subTask.id,
        title = subTask.title,
        description = subTask.note, // 使用note字段代替description
        isCompleted = subTask.isCompleted,
        daysLeft = 0,  // 子任务通常没有独立的截止日期
        urgency = TaskUrgency.MEDIUM,  // 默认为中等优先级
        displayInTaskList = false,  // 子任务通常不在主列表显示
        parentTaskId = subTask.parentTaskId
    )
}

// 检查所有子任务是否完成
private fun areAllModelSubTasksCompleted(subTasks: List<SubTask>): Boolean {
    return subTasks.isNotEmpty() && subTasks.all { it.isCompleted }
}

// 修改 transformSubTaskToLocalModel 函数，处理正确的 SubTask 类型
private fun transformSubTaskToLocalModel(subTask: SubTask): SubTask {
    // 我们现在直接使用来自 model 包的 SubTask
    return subTask
}

// 添加功能层转换工具函数
private fun addSubTaskToParentTask(parentTask: ModelTaskData, subTask: SubTask): ModelTaskData {
    val updatedSubTasks = parentTask.subTasks.toMutableList()
    updatedSubTasks.add(subTask)
    return parentTask.copy(subTasks = updatedSubTasks)
}

// 修改实验特性警告
@OptIn(ExperimentalTypeConversion::class, ExperimentalMaterial3Api::class)
@Composable
private fun ParseVoiceInputUI(
    text: String,
    navController: NavController,
    viewModel: TaskListViewModel,
    onDismiss: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val primaryColor = Color(0xFFB6AFC7) // 使用指定的按钮颜色
    val backgroundColor = Color(0xFFF6F5F9) // 使用相同的背景色
    val hapticFeedback = LocalHapticFeedback.current
    
    // 解析任务标题、描述、时间等属性
    val taskTitle = remember { mutableStateOf(text) }
    val taskDescription = remember { mutableStateOf("") }
    val taskPriority = remember { mutableStateOf(TaskUrgency.MEDIUM) }
    val taskTag = remember { mutableStateOf<String?>(null) }
    val taskDate = remember { mutableStateOf<LocalDate?>(null) }
    
    // 解析文本中的时间信息
    LaunchedEffect(text) {
        // 这里可以添加更复杂的NLP解析逻辑
        parseVoiceInput(
            text = text,
            taskTitle = taskTitle,
            taskDescription = taskDescription,
            taskPriority = taskPriority,
            taskTag = taskTag,
            taskDate = taskDate
        )
    }
    
    // 创建任务的函数
    @OptIn(ExperimentalTypeConversion::class)
    val createTask: () -> Unit = {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
        
        // 创建任务对象
        val newTask = ModelTaskData(
            id = UUID.randomUUID().toString(),
            title = taskTitle.value,
            description = taskDescription.value,
            daysLeft = calculateDaysLeft(taskDate.value),
            isCompleted = false,
            urgency = taskPriority.value,
            tag = taskTag.value,
            displayInTaskList = true
        )
        
        // 保存任务
        coroutineScope.launch {
            viewModel.saveTask(newTask)
            onDismiss()
        }
    }
    
    // 使用ModalBottomSheet显示界面
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = rememberModalBottomSheetState(),
        shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
        containerColor = Color.White,
        dragHandle = {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Box(
            modifier = Modifier
                        .width(40.dp)
                        .height(4.dp)
                        .background(
                            color = Color(0xFFE0E0E0),
                            shape = RoundedCornerShape(2.dp)
                        )
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Text(
                text = "创建任务",
                style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.primary,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 解析结果区域
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = backgroundColor,
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(16.dp)
            ) {
                // 任务标题
                Text(
                    text = "标题",
                    style = MaterialTheme.typography.labelLarge,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                OutlinedTextField(
                    value = taskTitle.value,
                    onValueChange = { taskTitle.value = it },
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = primaryColor,
                        unfocusedBorderColor = Color.LightGray,
                        cursorColor = primaryColor
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 任务描述
                Text(
                    text = "描述",
                    style = MaterialTheme.typography.labelLarge,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                OutlinedTextField(
                    value = taskDescription.value,
                    onValueChange = { taskDescription.value = it },
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = primaryColor,
                        unfocusedBorderColor = Color.LightGray,
                        cursorColor = primaryColor
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 优先级和标签
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 优先级选择
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "优先级",
                            style = MaterialTheme.typography.labelLarge,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 8.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color.LightGray,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        ) {
                            Text(
                                text = when(taskPriority.value) {
                                    TaskUrgency.LOW -> "低"
                                    TaskUrgency.MEDIUM -> "中"
                                    TaskUrgency.HIGH -> "高"
                                    TaskUrgency.CRITICAL -> "紧急"
                                    else -> "中"
                                },
                                color = Color.DarkGray
                            )
                        }
                    }
                    
                    // 标签选择
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "标签",
                            style = MaterialTheme.typography.labelLarge,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 8.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color.LightGray,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        ) {
                            Text(
                                text = taskTag.value ?: "未分类",
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 取消按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.LightGray.copy(alpha = 0.5f),
                        contentColor = Color.DarkGray
                    )
                ) {
                    Text("取消")
                }
                
                // 创建按钮
                Button(
                    onClick = createTask,
            modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = primaryColor,
                        contentColor = Color.White
                    )
                ) {
                    Text("创建任务")
                }
            }
        }
    }
}

// 辅助函数：解析语音输入
@OptIn(ExperimentalTypeConversion::class)
private fun parseVoiceInput(
    text: String,
    taskTitle: MutableState<String>,
    taskDescription: MutableState<String>,
    taskPriority: MutableState<TaskUrgency>,
    taskTag: MutableState<String?>,
    taskDate: MutableState<LocalDate?>
) {
    // 简单的语音解析逻辑，可以替换为更复杂的NLP处理
    
    // 如果包含"明天"，设置为明天
    if (text.contains("明天")) {
        taskDate.value = LocalDate.now().plusDays(1)
    } else if (text.contains("今天")) {
        taskDate.value = LocalDate.now()
    } else if (text.contains("后天")) {
        taskDate.value = LocalDate.now().plusDays(2)
    } else if (text.contains("下周")) {
        taskDate.value = LocalDate.now().plusDays(7)
    }
    
    // 检测优先级
    if (text.contains("紧急") || text.contains("重要")) {
        taskPriority.value = TaskUrgency.HIGH
    } else if (text.contains("非常紧急") || text.contains("极其重要")) {
        taskPriority.value = TaskUrgency.CRITICAL
    } else if (text.contains("不紧急") || text.contains("低优先级")) {
        taskPriority.value = TaskUrgency.LOW
    }
    
    // 检测标签
    val tagPattern = Pattern.compile("标签(:|：|是)?\\s*([^,，。；;]+)")
    val tagMatcher = tagPattern.matcher(text)
    if (tagMatcher.find()) {
        taskTag.value = tagMatcher.group(2)?.trim()
    } else {
        // 常见类别检测
        when {
            text.contains("工作") -> taskTag.value = "工作"
            text.contains("学习") -> taskTag.value = "学习"
            text.contains("个人") -> taskTag.value = "个人"
            text.contains("家庭") -> taskTag.value = "家庭"
            text.contains("健康") -> taskTag.value = "健康"
        }
    }
    
    // 检测描述
    val descPattern = Pattern.compile("描述(:|：|是)?\\s*([^。；;]+)")
    val descMatcher = descPattern.matcher(text)
    if (descMatcher.find()) {
        taskDescription.value = descMatcher.group(2)?.trim() ?: ""
    }
    
    // 处理标题，尝试提取主要任务内容
    // 如果找不到明确的标题模式，就使用整个文本作为标题
    val titlePattern = Pattern.compile("^([^,，。；;]+)")
    val titleMatcher = titlePattern.matcher(text)
    if (titleMatcher.find()) {
        var title = titleMatcher.group(1)?.trim() ?: text
        // 移除可能包含的时间和优先级词
        val timeWords = listOf("今天", "明天", "后天", "下周")
        val priorityWords = listOf("紧急", "重要", "非常紧急", "极其重要", "不紧急", "低优先级")
        
        for (word in timeWords + priorityWords) {
            title = title.replace(word, "").trim()
        }
        
        if (title.isNotEmpty()) {
            taskTitle.value = title
        } else {
            taskTitle.value = text
        }
    } else {
        taskTitle.value = text
    }
}

// 辅助函数：计算离deadline还有多少天
private fun calculateDaysLeft(date: LocalDate?): Int {
    if (date == null) return 1 // 默认为明天
    
    val today = LocalDate.now()
    return ChronoUnit.DAYS.between(today, date).toInt()
}

// 辅助函数：将UI层的SubTask转换为model.SubTask
private fun transformUIToModelSubTask(parentId: String, uiSubTask: com.timeflow.app.ui.screen.task.SubTask): SubTask {
    return SubTask(
        id = uiSubTask.id,
        title = uiSubTask.title,
        isCompleted = uiSubTask.isCompleted,
        parentTaskId = parentId,
        note = uiSubTask.note // 使用note字段
    )
}

@OptIn(ExperimentalMaterial3Api::class)
annotation class ExperimentalComposeApi

@OptIn(ExperimentalMaterial3Api::class)
annotation class ExperimentalTypeConversion

@OptIn(ExperimentalTypeConversion::class)
private fun getUrgencyColor(urgency: TaskUrgency): Color {
    return when (urgency) {
        TaskUrgency.CRITICAL -> Color(0xFFE57373)
        TaskUrgency.HIGH -> Color(0xFFFFB74D)
        TaskUrgency.MEDIUM -> Color(0xFFFFF176)
        TaskUrgency.LOW -> Color(0xFF81C784)
    }
}

@OptIn(ExperimentalTypeConversion::class)
private fun createDismissHandler(): () -> Unit {
    return { /* 处理关闭事件 */ }
}

// 添加 LoadingDialog 组件
@Composable
private fun LoadingDialog(message: String, onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("处理中") },
        text = { Text(message) },
        confirmButton = { }
    )
}

// 修复 rememberScaffoldState 函数
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun rememberScaffoldState(): ScaffoldState {
    return remember { 
        ScaffoldState(
            snackbarHostState = SnackbarHostState(),
            drawerState = DrawerState(DrawerValue.Closed)
        ) 
    }
}

// 添加类型别名以处理命名冲突
private typealias ComposeRoundedCornerShape = RoundedCornerShape

// 添加所需的状态类
@OptIn(ExperimentalMaterial3Api::class)
private class ScaffoldState(
    val snackbarHostState: SnackbarHostState,
    val drawerState: DrawerState
)

private enum class DrawerValue {
    Closed,
    Open
}

@OptIn(ExperimentalMaterial3Api::class)
private class DrawerState(initialValue: DrawerValue) {
    var currentValue = initialValue
        private set

    fun open() {
        currentValue = DrawerValue.Open
    }

    fun close() {
        currentValue = DrawerValue.Closed
    }
} 

/**
 * 计算给定日期时间与今天的天数差（重载版本）
 * 支持LocalDateTime类型的参数
 */
private fun calculateDaysLeft(dateTime: LocalDateTime?): Int {
    // 如果日期时间为null，返回Int.MAX_VALUE表示无期限
    if (dateTime == null) return Int.MAX_VALUE
    
    // 转换为LocalDate并调用原有的calculateDaysLeft函数
    return calculateDaysLeft(dateTime.toLocalDate())
}

// 💡 骨架屏组件 - 提供平滑的加载体验
@Composable
private fun SkeletonTaskList() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(vertical = 12.dp)
    ) {
        items(5) { index ->
            SkeletonTaskItem()
        }
    }
}

@Composable
private fun SkeletonTaskItem() {
    // 闪烁动画
    val shimmerAlpha by animateFloatAsState(
        targetValue = 0.3f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "shimmerAlpha"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧圆点
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .clip(CircleShape)
                    .background(Color.Gray.copy(alpha = shimmerAlpha))
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 标题骨架
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .height(16.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.Gray.copy(alpha = shimmerAlpha))
                )
                
                // 描述骨架
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.5f)
                        .height(12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.Gray.copy(alpha = shimmerAlpha * 0.6f))
                )
            }
            
            // 右侧优先级骨架
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .clip(CircleShape)
                    .background(Color.Gray.copy(alpha = shimmerAlpha))
            )
        }
    }
}

// 💡 增强版骨架屏 - 参考Notion的优雅设计
@Composable
private fun EnhancedSkeletonTaskList() {
    val shimmerColors = listOf(
        Color(0xFFE9ECEF),
        Color(0xFFF8F9FA),
        Color(0xFFE9ECEF)
    )
    
    val transition = rememberInfiniteTransition(label = "shimmer")
    val translateAnim by transition.animateFloat(
        initialValue = 0f,
        targetValue = 1000f,
        animationSpec = infiniteRepeatable(
            animation = tween(1200, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmerTranslate"
    )
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 24.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(vertical = 20.dp)
    ) {
        items(6) { index ->
            // 错开动画时间，创建波浪效果
            val animationDelay = index * 100
            val adjustedTranslate = translateAnim - animationDelay
            
            SkeletonTaskItem(
                shimmerColors = shimmerColors,
                translateAnim = adjustedTranslate
            )
        }
    }
}

// 💡 单个骨架屏任务项 - 精心设计的占位符
@Composable
private fun SkeletonTaskItem(
    shimmerColors: List<Color>,
    translateAnim: Float
) {
    val brush = Brush.linearGradient(
        colors = shimmerColors,
        start = Offset(translateAnim - 200f, translateAnim - 200f),
        end = Offset(translateAnim, translateAnim)
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧圆形占位符（复选框）
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .background(brush, CircleShape)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 标题占位符
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .height(16.dp)
                        .background(brush, RoundedCornerShape(8.dp))
                )
                
                // 副标题占位符
                Box(
                    modifier = Modifier
                        .fillMaxWidth(0.5f)
                        .height(12.dp)
                        .background(brush, RoundedCornerShape(6.dp))
                )
            }
            
            // 右侧优先级占位符
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(brush, CircleShape)
            )
        }
    }
}

// 🎨 新增强版任务卡片组件 - 包含子任务数量、多标签支持和计时按钮
@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun NewEnhancedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDeleteTask: (String) -> Unit = {},
    elevation: Float,
    navController: NavController
) {
    // 🚀 优化状态管理：使用derivedStateOf确保状态变化立即反映
    val isChecked by remember(task.id, task.isCompleted) { 
        derivedStateOf { task.isCompleted }
    }
    
    // 🎯 目标状态强制重组机制 - 修复显示不稳定问题
    LaunchedEffect(task.goalId, task.goalTitle) {
        Log.d("GoalDisplay", "[NewEnhancedTaskItem] 目标状态变化: taskId=${task.id}, goalId=${task.goalId}, goalTitle=${task.goalTitle}")
    }
    
    // 📊 计算子任务统计
    val completedSubTasks = remember(task.subTasks) { task.subTasks.count { it.isCompleted } }
    val totalSubTasks = remember(task.subTasks) { task.subTasks.size }
    
    // 🏷️ 标签处理逻辑
    val allTags = remember(task.tag, task.customTags) {
        buildList {
            task.tag?.let { add(it) } // 主标签
            addAll(task.customTags) // 自定义标签
        }.distinct()
    }
    val displayTags = remember(allTags) { allTags.take(4) } // 进一步增加显示标签数量
    val overflowCount = remember(allTags) { (allTags.size - 4).coerceAtLeast(0) }
    
    val textDecoration = remember(isChecked) { 
        if (isChecked) TextDecoration.LineThrough else TextDecoration.None 
    }
    val textColor = remember(isChecked) { 
        if (isChecked) TextSecondary.copy(alpha = 0.6f) else TextPrimary 
    }
    val urgencyColor = remember(task.urgency) { getUrgencyColor(task.urgency) }
    val timeText = remember(task.dueDate, task.daysLeft) {
        // 优先使用dueDate，如果有的话
        if (task.dueDate != null) {
            formatDetailedTimeRangeFromDate(task.dueDate)
        } else if (task.daysLeft != Int.MAX_VALUE) {
            // 只有当daysLeft有效时才使用它
            formatDetailedTimeRangeFromDaysLeft(task.daysLeft)
        } else {
            "未设置时间" // 添加默认显示
        }
    }
    
    // 添加标题编辑状态
    var isEditing by remember { mutableStateOf(false) }
    var editedTitle by remember(task.title) { mutableStateOf(task.title) }
    
    // 触觉反馈
    val haptic = LocalHapticFeedback.current
    val viewModel: TaskListViewModel = hiltViewModel()
    val coroutineScope = rememberCoroutineScope()
    
    // 保存标题变更的函数
    val saveTitle = {
        if (isEditing && editedTitle.isNotBlank() && editedTitle != task.title) {
            viewModel.updateTaskTitle(task.id, editedTitle)
        }
        isEditing = false
    }
    
    // 添加滑动状态
    var isDeleting by remember { mutableStateOf(false) }
    var isCompleting by remember { mutableStateOf(false) }
    
    val dismissState = rememberDismissState(
        initialValue = DismissValue.Default,
        confirmStateChange = { value ->
            when (value) {
                DismissValue.DismissedToStart -> {
                    // 左滑删除
                    isDeleting = true
                    onDeleteTask(task.id)
                    true
                }
                DismissValue.DismissedToEnd -> {
                    // 右滑完成 - 修复逻辑：只有未完成的任务才能通过右滑完成
                    if (!isChecked) {
                        // 任务未完成 -> 右滑完成任务
                        isCompleting = true
                        Log.d("SwipeToComplete", "右滑完成任务: ${task.title}")
                        onCheckChange(true)
                    } else {
                        // 任务已完成 -> 右滑无效: ${task.title}")
                    }
                    false
                }
                else -> false
            }
        }
    )
    
    // 🔧 状态监听，确保dismissState正确重置
    LaunchedEffect(dismissState.currentValue) {
        if (dismissState.currentValue != DismissValue.Default) {
            delay(500)
            try {
                dismissState.snapTo(DismissValue.Default)
            } catch (e: Exception) {
                Log.e("SwipeToComplete", "重置dismissState失败", e)
            }
        }
    }
    
    // 计算滑动进度
    val dismissProgressLeft = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToStart -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    val dismissProgressRight = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToEnd -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    // 图标旋转动画
    val deleteIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToStart) -45f else 0f,
        label = "deleteIconRotation"
    )
    
    val completeIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToEnd) 45f else 0f,
        label = "completeIconRotation" 
    )

    // 当滑动超过阈值时触发触觉反馈
    LaunchedEffect(dismissState.targetValue) {
        if (dismissState.targetValue == DismissValue.DismissedToStart || 
            dismissState.targetValue == DismissValue.DismissedToEnd) {
            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
        }
    }
    
    // 使用外层Box包裹，控制整体布局
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp)
    ) {
        // 滑动删除组件
        SwipeToDismiss(
            state = dismissState,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 2.dp),
            directions = setOf(DismissDirection.EndToStart, DismissDirection.StartToEnd),
            dismissThresholds = { direction -> FractionalThreshold(0.15f) },
            background = {
                Row(
                    modifier = Modifier.fillMaxSize(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 右滑完成背景 (左侧)
                    if (dismissProgressRight > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(start = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF4CAF50)),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "完成",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(completeIconRotation)
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 左滑删除背景 (右侧)
                    if (dismissProgressLeft > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(end = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFFEF5350)),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(deleteIconRotation)
                                )
                            }
                        }
                    }
                }
            },
            dismissContent = {
                // 🎨 优化：任务项内容 - 使用Card，更温和的缩放动画
                val cardScale by animateFloatAsState(
                    targetValue = 1f - (max(dismissProgressLeft, dismissProgressRight) * 0.015f), // 减少缩放幅度，更温和
                    animationSpec = spring(dampingRatio = 0.9f, stiffness = 400f), // 使用弹簧动画，更自然
                    label = "cardScale"
                )
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .scale(cardScale),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFfcfcfc) // 确保背景色为rgb(247, 247, 246)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = elevation.dp
                    ),
                    onClick = { 
                        // 如果处于编辑状态，点击卡片其他区域时保存更改并退出编辑模式
                        val isEditingTitle = isEditing
                        if (isEditingTitle) {
                            saveTitle()
                        } else {
                            onTaskClick()
                        }
                    }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp, vertical = 10.dp), // 优化padding，为标签腾出更多空间
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 🎨 左侧优先级颜色指示器
                        Box(
                            modifier = Modifier
                                .size(width = 4.dp, height = 48.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(urgencyColor)
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        // ✅ 复选框
                        Checkbox(
                            checked = isChecked,
                            onCheckedChange = onCheckChange,
                            colors = CheckboxDefaults.colors(
                                checkedColor = DustyLavender,
                                uncheckedColor = LavenderAsh.copy(alpha = 0.7f)
                            ),
                            modifier = Modifier
                                .size(20.dp)
                                .padding(end = 4.dp),
                            interactionSource = NoRippleInteractionSource
                        )

                        // 📝 主要内容区域
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 4.dp) // 进一步减少padding，最大化标签空间
                        ) {
                            // 🔤 任务标题
                            if (isEditing) {
                                // 编辑模式
                                val focusRequester = remember { FocusRequester() }
                                TextField(
                                    value = editedTitle,
                                    onValueChange = { editedTitle = it },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .focusRequester(focusRequester),
                                    textStyle = MaterialTheme.typography.bodyLarge.copy(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    ),
                                    colors = TextFieldDefaults.colors(
                                        focusedContainerColor = Color.Transparent,
                                        unfocusedContainerColor = Color.Transparent,
                                        disabledContainerColor = Color.Transparent,
                                        focusedIndicatorColor = DustyLavender,
                                        unfocusedIndicatorColor = Color.Transparent
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                    keyboardActions = KeyboardActions(
                                        onDone = { 
                                            if (editedTitle.isNotBlank()) {
                                                saveTitle()
                                            }
                                        }
                                    )
                                )
                                
                                LaunchedEffect(Unit) {
                                    delay(100)
                                    try {
                                        focusRequester.requestFocus()
                                    } catch (e: Exception) {
                                        Log.e("TaskListFullScreen", "请求焦点失败", e)
                                    }
                                }
                                
                                DisposableEffect(Unit) {
                                    onDispose { saveTitle() }
                                }
                            } else {
                                // 非编辑模式 - 显示普通文本
                                Text(
                                    text = task.title,
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontSize = 16.sp,
                                    fontWeight = if (!isChecked) FontWeight.Medium else FontWeight.Normal,
                                    textDecoration = textDecoration,
                                    color = textColor,
                                    maxLines = 2,
                                    overflow = TextOverflow.Ellipsis,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .combinedClickable(
                                            onClick = { /* 短按由外层Card处理 */ },
                                            onLongClick = { 
                                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                                isEditing = true 
                                            }
                                        )
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // 🏷️ 底部信息栏：时间 + 标签 - 优化空间分配
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(3.dp) // 进一步减少间距
                            ) {
                                // ⏰ 时间信息 - 紧凑显示
                                Box(
                                    modifier = Modifier
                                        .height(18.dp) // 减少高度
                                        .clip(RoundedCornerShape(5.dp))
                                        .background(LavenderAsh.copy(alpha = 0.15f))
                                        .padding(horizontal = 5.dp, vertical = 2.dp) // 减少内边距
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Schedule,
                                            contentDescription = null,
                                            tint = DustyLavender.copy(alpha = 0.8f),
                                            modifier = Modifier.size(8.dp) // 缩小图标
                                        )
                                        Spacer(modifier = Modifier.width(2.dp))
                                        Text(
                                            text = timeText,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontSize = 9.sp, // 减小字体
                                            color = DustyLavender.copy(alpha = 0.8f),
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                    }
                                }
                                
                                // 🏷️ 标签显示区域 - 最大化空间使用
                                Row(
                                    modifier = Modifier
                                        .weight(1f) // 使用剩余空间
                                        .fillMaxWidth(), // 确保占用全部可用宽度
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(3.dp)
                                ) {
                                    // 显示标签
                                    displayTags.forEachIndexed { index, tag ->
                                        EnhancedTagChip(
                                            tag = tag,
                                            isPrimary = index == 0 // 第一个标签作为主标签
                                        )
                                    }
                                    
                                    // 显示溢出标签数量
                                    if (overflowCount > 0) {
                                        Box(
                                            modifier = Modifier
                                                .height(18.dp) // 与其他元素保持一致
                                                .clip(RoundedCornerShape(5.dp))
                                                .background(Color(0xFFE8E8E8)) // 灰色背景
                                                .padding(horizontal = 5.dp, vertical = 2.dp)
                                        ) {
                                            Text(
                                                text = "+$overflowCount",
                                                style = MaterialTheme.typography.bodySmall,
                                                fontSize = 9.sp, // 减小字体
                                                color = Color(0xFF757575), // 灰色文字
                                                textAlign = TextAlign.Center,
                                                modifier = Modifier.align(Alignment.Center)
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 🎯 右上角区域：紧凑的目标标记 + 计时按钮
                        Column(
                            horizontalAlignment = Alignment.End,
                            verticalArrangement = Arrangement.spacedBy(2.dp) // 减少间距使布局更紧凑
                        ) {
                            // 🎯 目标标记（右上角）- 修复显示不稳定问题
                            val hasGoal = remember(task.goalId, task.goalTitle) { 
                                task.goalId != null 
                            }
                            val displayGoalTitle = remember(task.goalTitle, task.goalId) {
                                val result = when {
                                    !task.goalTitle.isNullOrBlank() -> task.goalTitle
                                    task.goalId != null -> "目标" // 当有goalId但goalTitle还在加载时显示占位符
                                    else -> null
                                }
                                // 添加调试日志
                                Log.d("GoalDisplay", "[TaskCard] 目标显示逻辑: taskId=${task.id}, goalId=${task.goalId}, goalTitle=${task.goalTitle}, result=$result")
                                result
                            }
                            
                            // 监控目标状态变化，确保UI及时更新
                            LaunchedEffect(task.goalId, task.goalTitle) {
                                Log.d("GoalDisplay", "[TaskCard] 目标状态变化监控: taskId=${task.id}, goalId=${task.goalId}, goalTitle=${task.goalTitle}")
                            }
                            
                            if (hasGoal && displayGoalTitle != null) {
                                Box(
                                    modifier = Modifier
                                        .height(16.dp) // 减少高度使其更紧凑
                                        .widthIn(max = 120.dp) // 限制最大宽度，为标签腾出空间
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(Color(0xFFF3E5F5)) // 紫色背景
                                        .padding(horizontal = 5.dp, vertical = 1.dp) // 减少内边距
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Outlined.TrackChanges,
                                            contentDescription = null,
                                            tint = Color(0xFF9C27B0), // 紫色图标
                                            modifier = Modifier.size(7.dp) // 缩小图标
                                        )
                                        Spacer(modifier = Modifier.width(2.dp))
                                        Text(
                                            text = displayGoalTitle,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontSize = 8.sp, // 减小字体
                                            fontWeight = FontWeight.Medium,
                                            color = Color(0xFF9C27B0), // 紫色文字
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                    }
                                }
                            }
                            
                            // ⏱️ 计时按钮 - 优化尺寸
                            Box(
                                modifier = Modifier
                                    .size(24.dp) // 缩小计时按钮尺寸
                                    .clip(CircleShape)
                                    .background(Color(0xFFE5DEE4))
                                    .clickable {
                                        // 跳转到计时页面并开始计时这个任务
                                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        try {
                                            navController.navigate("${AppDestinations.TIME_TRACKING_ROUTE}?taskId=${task.id}&autoStart=true")
                                        } catch (e: Exception) {
                                            Log.e("TaskListFullScreen", "跳转计时页面失败", e)
                                        }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PlayArrow,
                                    contentDescription = "开始计时",
                                    tint = Color.White,
                                    modifier = Modifier.size(12.dp) // 缩小图标尺寸
                                )
                            }
                        }
                    }
                }
            }
        )
    }
}

// 📦 增强版标签组件
@Composable
private fun EnhancedTagChip(tag: String, isPrimary: Boolean = false) {
    val backgroundColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFFFFF3E0) // 主标签：温暖的浅橙色
        else 
            Color(0xFFE8F5E8) // 次要标签：清新的浅绿色
    }
        
    val textColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFFE65100) // 主标签：深橙色文字
        else 
            Color(0xFF2E7D32) // 次要标签：深绿色文字
    }

    val fontWeight = remember(isPrimary) {
        if (isPrimary) FontWeight.Medium else FontWeight.Normal
    }

    Box(
        modifier = Modifier
            .height(18.dp) // 与时间显示区域保持相同高度
            .clip(RoundedCornerShape(5.dp))
            .background(backgroundColor)
            .padding(horizontal = 4.dp, vertical = 1.dp), // 紧凑的内边距
        contentAlignment = Alignment.Center // 确保文字垂直居中
    ) {
        Text(
            text = tag,
            fontSize = 9.sp, // 与时间显示保持一致的字体大小
            color = textColor,
            fontWeight = fontWeight,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.bodySmall // 确保使用与时间显示相同的文字样式
        )
    }
}

// 🎨 Done List 内容组件 - 按时间分组显示已完成任务
@Composable
private fun DoneListContent(
    completedTasks: List<ModelTaskData>,
    onTaskClick: (ModelTaskData) -> Unit,
    viewModel: TaskListViewModel = hiltViewModel() // 添加ViewModel参数
) {
    // 🔧 按时间分组任务数据
    val groupedTasks = remember(completedTasks) {
        groupTasksByCompletionTime(completedTasks)
    }

    // 🔧 添加状态变量来控制各时间段已完成任务的显示/隐藏
    // 有任务的分组默认展开，没有任务的分组不显示
    var showTodayCompleted by remember(groupedTasks.today.size) {
        mutableStateOf(groupedTasks.today.isNotEmpty())
    }
    var showThisWeekCompleted by remember(groupedTasks.thisWeek.size) {
        mutableStateOf(groupedTasks.thisWeek.isNotEmpty())
    }
    var showThisMonthCompleted by remember(groupedTasks.thisMonth.size) {
        mutableStateOf(groupedTasks.thisMonth.isNotEmpty())
    }
    var showEarlierCompleted by remember(groupedTasks.earlier.size) {
        mutableStateOf(groupedTasks.earlier.isNotEmpty())
    }
    if (completedTasks.isEmpty()) {
        // 空状态
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Done,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = Color(0xFF6B7280)
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "还没有完成的任务",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF374151),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = "完成一些任务后这里就会显示",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF6B7280),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    } else {
        // 🎯 按时间分组显示已完成任务
        
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(vertical = 20.dp)
        ) {
            // 🔧 今天分组 - 只有有任务时才显示
            if (groupedTasks.today.isNotEmpty()) {
                item {
                    DoneTaskGroupHeader(
                        title = "今天",
                        count = groupedTasks.today.size,
                        icon = Icons.Default.Today,
                        showToggle = true,
                        isExpanded = showTodayCompleted,
                        onToggleClick = { showTodayCompleted = !showTodayCompleted }
                    )
                }
            }
            // 🔧 今天分组内容 - 只有有任务时才显示
            if (groupedTasks.today.isNotEmpty()) {
                item {
                    AnimatedVisibility(
                        visible = showTodayCompleted,
                        enter = expandVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeIn(
                            animationSpec = tween(300)
                        ),
                        exit = shrinkVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeOut(
                            animationSpec = tween(200)
                        )
                    ) {
                        Column {
                            groupedTasks.today.forEachIndexed { index, task ->
                                DoneTaskCard(
                                    task = task,
                                    onTaskClick = { onTaskClick(task) },
                                    animationDelay = if (showTodayCompleted) index * 30 else 0,
                                    viewModel = viewModel
                                )
                                if (index < groupedTasks.today.size - 1) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                }
                            }
                        }
                    }
                }
            }
            // 🔧 分组间距 - 只有前面有内容时才显示
            if (groupedTasks.today.isNotEmpty()) {
                item { Spacer(modifier = Modifier.height(8.dp)) }
            }

            // 🔧 本周分组 - 只有有任务时才显示
            if (groupedTasks.thisWeek.isNotEmpty()) {
                item {
                    DoneTaskGroupHeader(
                        title = "本周",
                        count = groupedTasks.thisWeek.size,
                        icon = Icons.Default.DateRange,
                        showToggle = true,
                        isExpanded = showThisWeekCompleted,
                        onToggleClick = { showThisWeekCompleted = !showThisWeekCompleted }
                    )
                }
            }
            // 🔧 本周分组内容 - 只有有任务时才显示
            if (groupedTasks.thisWeek.isNotEmpty()) {
                item {
                    AnimatedVisibility(
                        visible = showThisWeekCompleted,
                        enter = expandVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeIn(
                            animationSpec = tween(300)
                        ),
                        exit = shrinkVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeOut(
                            animationSpec = tween(200)
                        )
                    ) {
                        Column {
                            groupedTasks.thisWeek.forEachIndexed { index, task ->
                                DoneTaskCard(
                                    task = task,
                                    onTaskClick = { onTaskClick(task) },
                                    animationDelay = if (showThisWeekCompleted) index * 30 else 0,
                                    viewModel = viewModel
                                )
                                if (index < groupedTasks.thisWeek.size - 1) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                }
                            }
                        }
                    }
                }
            }
            // 🔧 分组间距 - 只有前面有内容时才显示
            if (groupedTasks.today.isNotEmpty() || groupedTasks.thisWeek.isNotEmpty()) {
                item { Spacer(modifier = Modifier.height(8.dp)) }
            }

            // 🔧 本月分组 - 只有有任务时才显示
            if (groupedTasks.thisMonth.isNotEmpty()) {
                item {
                    DoneTaskGroupHeader(
                        title = "本月",
                        count = groupedTasks.thisMonth.size,
                        icon = Icons.Default.CalendarToday,
                        showToggle = true,
                        isExpanded = showThisMonthCompleted,
                        onToggleClick = { showThisMonthCompleted = !showThisMonthCompleted }
                    )
                }
            }
            // 🔧 本月分组内容 - 只有有任务时才显示
            if (groupedTasks.thisMonth.isNotEmpty()) {
                item {
                    AnimatedVisibility(
                        visible = showThisMonthCompleted,
                        enter = expandVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeIn(
                            animationSpec = tween(300)
                        ),
                        exit = shrinkVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeOut(
                            animationSpec = tween(200)
                        )
                    ) {
                        Column {
                            groupedTasks.thisMonth.forEachIndexed { index, task ->
                                DoneTaskCard(
                                    task = task,
                                    onTaskClick = { onTaskClick(task) },
                                    animationDelay = if (showThisMonthCompleted) index * 30 else 0,
                                    viewModel = viewModel
                                )
                                if (index < groupedTasks.thisMonth.size - 1) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                }
                            }
                        }
                    }
                }
            }
            // 🔧 分组间距 - 只有前面有内容时才显示
            if (groupedTasks.today.isNotEmpty() || groupedTasks.thisWeek.isNotEmpty() || groupedTasks.thisMonth.isNotEmpty()) {
                item { Spacer(modifier = Modifier.height(8.dp)) }
            }

            // 🔧 更早的任务分组 - 只有有任务时才显示
            if (groupedTasks.earlier.isNotEmpty()) {
                item {
                    DoneTaskGroupHeader(
                        title = "更早",
                        count = groupedTasks.earlier.size,
                        icon = Icons.Default.History,
                        showToggle = true,
                        isExpanded = showEarlierCompleted,
                        onToggleClick = { showEarlierCompleted = !showEarlierCompleted }
                    )
                }
            }
            // 🔧 更早分组内容 - 只有有任务时才显示
            if (groupedTasks.earlier.isNotEmpty()) {
                item {
                    AnimatedVisibility(
                        visible = showEarlierCompleted,
                        enter = expandVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeIn(
                            animationSpec = tween(300)
                        ),
                        exit = shrinkVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeOut(
                            animationSpec = tween(200)
                        )
                    ) {
                        Column {
                            groupedTasks.earlier.forEachIndexed { index, task ->
                                DoneTaskCard(
                                    task = task,
                                    onTaskClick = { onTaskClick(task) },
                                    animationDelay = if (showEarlierCompleted) index * 30 else 0,
                                    viewModel = viewModel
                                )
                                if (index < groupedTasks.earlier.size - 1) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 🎨 已完成任务卡片 - 新布局：显示子任务列表
@Composable
private fun DoneTaskCard(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    animationDelay: Int = 0,
    viewModel: TaskListViewModel = hiltViewModel() // 添加ViewModel以获取子任务数据
) {
    var cardVisible by remember { mutableStateOf(false) }
    var isExpanded by remember { mutableStateOf(false) }
    var completedSubTasks by remember { mutableStateOf<List<UISubTask>>(emptyList()) }
    var isLoadingSubTasks by remember { mutableStateOf(false) }
    var hasSubTasks by remember { mutableStateOf(false) } // 🔧 新增：标记是否有子任务

    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        cardVisible = true
    }

    // 🔧 修复：首先检查是否有子任务，决定是否显示展开图标
    LaunchedEffect(task.id) {
        Log.d("DoneTaskCard", "检查任务${task.id}是否有子任务")

        try {
            // 获取该任务的所有子任务数量
            viewModel.loadSubTasksForTask(task.id) { allSubTasks ->
                hasSubTasks = allSubTasks.isNotEmpty()
                Log.d("DoneTaskCard", "任务${task.id}有${allSubTasks.size}个子任务，hasSubTasks=$hasSubTasks")
            }
        } catch (e: Exception) {
            Log.e("DoneTaskCard", "检查子任务失败", e)
            hasSubTasks = false
        }
    }

    // 🔧 修复：只有在展开且有子任务时才获取详细的子任务数据
    LaunchedEffect(task.id, isExpanded) {
        if (isExpanded && hasSubTasks) {
            isLoadingSubTasks = true
            Log.d("DoneTaskCard", "开始获取任务${task.id}的子任务详细数据")

            try {
                // 从数据库获取该任务的所有子任务 - 使用回调方式
                viewModel.loadSubTasksForTask(task.id) { allSubTasks ->
                    // 筛选出已完成的子任务
                    val filteredSubTasks = allSubTasks.filter { it.completedAt != null }.map { modelTask ->
                        UISubTask(
                            id = modelTask.id,
                            title = modelTask.title,
                            isCompleted = true,
                            parentTaskId = task.id,
                            note = modelTask.description,
                            priority = modelTask.priority ?: Priority.MEDIUM,
                            dueDate = modelTask.dueDate
                        )
                    }

                    // 在主线程更新UI状态
                    completedSubTasks = filteredSubTasks
                    isLoadingSubTasks = false

                    Log.d("DoneTaskCard", "获取到${allSubTasks.size}个子任务，其中${filteredSubTasks.size}个已完成")
                }
            } catch (e: Exception) {
                Log.e("DoneTaskCard", "获取子任务失败", e)
                completedSubTasks = emptyList()
                isLoadingSubTasks = false
            }
        }
    }
    
    AnimatedVisibility(
        visible = cardVisible,
        enter = fadeIn(
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ) + slideInVertically(
            initialOffsetY = { it / 3 },
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        )
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF8FAFC) // 🎨 统一卡片背景色 - 浅灰蓝色
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp // 取消阴影
            ),
            shape = RoundedCornerShape(8.dp) // 稍小的圆角，更紧凑
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 主任务行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onTaskClick() }, // 🔧 修复：将点击事件移到Row上，避免与展开按钮冲突
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 🔵 左侧彩色圆点
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = getTaskIconColor(task),
                                shape = CircleShape
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    // 任务标题
                    Text(
                        text = task.title,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1F2937),
                        modifier = Modifier.weight(1f),
                        fontSize = 14.sp
                    )
                    
                    // 🔧 修复：只有当任务有子任务时才显示展开图标
                    if (hasSubTasks) {
                        Box(
                            modifier = Modifier
                                .size(32.dp) // 增大点击区域
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = null // 移除点击涟漪效果，避免视觉冲突
                                ) {
                                    isExpanded = !isExpanded
                                    Log.d("DoneTaskCard", "展开状态切换: ${task.title}, isExpanded=$isExpanded")
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                contentDescription = if (isExpanded) "收起" else "展开",
                                tint = Color(0xFF6B7280),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
                
                // 🔧 修复：只有当任务有子任务时才显示子任务统计信息
                if (hasSubTasks && (completedSubTasks.isNotEmpty() || isLoadingSubTasks)) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = if (isLoadingSubTasks) "正在加载子任务..."
                               else "${completedSubTasks.size}个子任务已完成",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF6B7280),
                        fontSize = 12.sp,
                        modifier = Modifier.padding(start = 24.dp)
                    )
                }
                
                // 🔧 修复：只有当任务有子任务且展开时才显示子任务列表
                AnimatedVisibility(
                    visible = hasSubTasks && isExpanded && (completedSubTasks.isNotEmpty() || isLoadingSubTasks),
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    Column(
                        modifier = Modifier.padding(start = 24.dp, top = 12.dp)
                    ) {
                        if (isLoadingSubTasks) {
                            // 显示加载状态
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(vertical = 8.dp)
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp,
                                    color = Color(0xFF8B5CF6)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "正在加载子任务...",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF6B7280),
                                    fontSize = 12.sp
                                )
                            }
                        } else if (completedSubTasks.isEmpty()) {
                            // 无子任务时的显示
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(vertical = 8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = "信息",
                                    tint = Color(0xFF6B7280),
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "该任务没有子任务",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF6B7280),
                                    fontSize = 12.sp
                                )
                            }
                        } else {
                            // 显示前3个子任务
                            completedSubTasks.take(3).forEach { subTask ->
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(vertical = 4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.CheckCircle,
                                        contentDescription = "已完成",
                                        tint = Color(0xFF22C55E),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = subTask.title,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFF374151),
                                        fontSize = 12.sp
                                    )
                                }
                            }
                            
                            // 如果有更多子任务，显示"查看全部"按钮
                            if (completedSubTasks.size > 3) {
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "查看全部 ${completedSubTasks.size} 个子任务",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF8B5CF6),
                                    fontSize = 12.sp,
                                    modifier = Modifier
                                        .clickable { onTaskClick() }
                                        .padding(vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

// 🎨 获取任务图标颜色
private fun getTaskIconColor(task: ModelTaskData): Color {
    return when (task.urgency) {
        com.timeflow.app.ui.screen.task.model.TaskUrgency.CRITICAL -> Color(0xFFEF4444)
        com.timeflow.app.ui.screen.task.model.TaskUrgency.HIGH -> Color(0xFFF97316)
        com.timeflow.app.ui.screen.task.model.TaskUrgency.MEDIUM -> Color(0xFFEAB308)
        com.timeflow.app.ui.screen.task.model.TaskUrgency.LOW -> Color(0xFF22C55E)
    }
}

// 🎨 获取任务图标
private fun getTaskIcon(task: ModelTaskData): androidx.compose.ui.graphics.vector.ImageVector {
    // 根据任务内容或类型返回不同图标
    return when {
        task.title.contains("健康") || task.title.contains("运动") || task.title.contains("Zumba") -> Icons.Default.FitnessCenter
        task.title.contains("旅行") || task.title.contains("行程") -> Icons.Default.Flight
        task.title.contains("学习") || task.title.contains("读书") -> Icons.Default.MenuBook
        task.title.contains("工作") || task.title.contains("会议") -> Icons.Default.Work
        task.title.contains("购物") || task.title.contains("买") -> Icons.Default.ShoppingCart
        task.title.contains("吃") || task.title.contains("饭") -> Icons.Default.Restaurant
        else -> Icons.Default.CheckCircle
    }
}

// 🎨 格式化完成时间
private fun formatCompletedTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp
    
    return when {
        diff < 60 * 1000 -> "刚刚完成"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
        else -> {
            val date = java.util.Date(timestamp)
            val formatter = java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault())
            formatter.format(date)
        }
    }
}

// 🎯 时间分组数据类
private data class GroupedTasks(
    val today: List<ModelTaskData>,
    val thisWeek: List<ModelTaskData>,
    val thisMonth: List<ModelTaskData>,
    val earlier: List<ModelTaskData>
)

// 🎯 按完成时间分组任务
private fun groupTasksByCompletionTime(tasks: List<ModelTaskData>): GroupedTasks {
    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    
    // 计算本周的开始和结束日期（周一到周日）
    val startOfWeek = today.minusDays(today.dayOfWeek.value.toLong() - 1)
    val endOfWeek = startOfWeek.plusDays(6)
    
    // 计算本月的开始和结束日期
    val startOfMonth = today.withDayOfMonth(1)
    val endOfMonth = today.withDayOfMonth(today.lengthOfMonth())
    
    val todayTasks = mutableListOf<ModelTaskData>()
    val thisWeekTasks = mutableListOf<ModelTaskData>()
    val thisMonthTasks = mutableListOf<ModelTaskData>()
    val earlierTasks = mutableListOf<ModelTaskData>()
    
    Log.d("TaskGrouping", "===== 开始任务分组 =====")
    Log.d("TaskGrouping", "今天: $today")
    Log.d("TaskGrouping", "本周范围: $startOfWeek 到 $endOfWeek")
    Log.d("TaskGrouping", "本月范围: $startOfMonth 到 $endOfMonth")
    Log.d("TaskGrouping", "待分组的已完成任务数量: ${tasks.size}")
    
    tasks.forEach { task ->
        // 使用任务的完成时间，如果没有就使用当前时间
        val completionTime = task.completedAt?.let { 
            java.time.Instant.ofEpochMilli(it).atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
        } ?: now
        
        val completionDate = completionTime.toLocalDate()
        
        Log.d("TaskGrouping", "分析任务: ${task.title}, 完成日期: $completionDate")
        
        when {
            // 今天完成的任务
            completionDate.isEqual(today) -> {
                todayTasks.add(task)
                Log.d("TaskGrouping", "  -> 分类到: 今天")
            }
            
            // 本周完成的任务（本周内但不是今天）
            !completionDate.isBefore(startOfWeek) && !completionDate.isAfter(endOfWeek) && !completionDate.isEqual(today) -> {
                thisWeekTasks.add(task)
                Log.d("TaskGrouping", "  -> 分类到: 本周")
            }
            
            // 本月完成的任务（本月内但不在本周）
            !completionDate.isBefore(startOfMonth) && !completionDate.isAfter(endOfMonth) && 
            (completionDate.isBefore(startOfWeek) || completionDate.isAfter(endOfWeek)) -> {
                thisMonthTasks.add(task)
                Log.d("TaskGrouping", "  -> 分类到: 本月")
            }
            
            // 更早的任务（本月之前）
            else -> {
                earlierTasks.add(task)
                Log.d("TaskGrouping", "  -> 分类到: 更早")
        }
    }
    }
    
    Log.d("TaskGrouping", "===== 分组结果 =====")
    Log.d("TaskGrouping", "今天: ${todayTasks.size} 个任务")
    Log.d("TaskGrouping", "本周: ${thisWeekTasks.size} 个任务")
    Log.d("TaskGrouping", "本月: ${thisMonthTasks.size} 个任务")
    Log.d("TaskGrouping", "更早: ${earlierTasks.size} 个任务")
    
    return GroupedTasks(
        today = todayTasks.sortedByDescending { it.completedAt ?: System.currentTimeMillis() },
        thisWeek = thisWeekTasks.sortedByDescending { it.completedAt ?: System.currentTimeMillis() },
        thisMonth = thisMonthTasks.sortedByDescending { it.completedAt ?: System.currentTimeMillis() },
        earlier = earlierTasks.sortedByDescending { it.completedAt ?: System.currentTimeMillis() }
    )
}

// 🎨 分组头部组件
@Composable
private fun DoneTaskGroupHeader(
    title: String,
    count: Int,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    showToggle: Boolean = false,
    isExpanded: Boolean = true,
    onToggleClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp, horizontal = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF6366F1),
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF374151),
            fontSize = 16.sp
        )
        Spacer(modifier = Modifier.width(8.dp))
        Box(
            modifier = Modifier
                .background(
                    color = Color(0xFF6366F1).copy(alpha = 0.1f),
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 8.dp, vertical = 2.dp)
        ) {
            Text(
                text = "$count",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6366F1),
                fontSize = 12.sp
            )
        }
        
        // 添加切换按钮（仅在showToggle为true时显示）
        if (showToggle) {
            Spacer(modifier = Modifier.weight(1f)) // 推到右侧
            
            // 切换按钮
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(8.dp))
                    .clickable { onToggleClick() }
                    .padding(4.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (isExpanded) "隐藏" else "显示",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF6366F1),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "隐藏任务" else "显示任务",
                        tint = Color(0xFF6366F1),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

