package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.GoalDao
import com.timeflow.app.data.entity.Goal as GoalEntity
import com.timeflow.app.data.entity.GoalSubTask as GoalSubTaskEntity
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.model.ReviewFrequency
import com.timeflow.app.data.model.TimeSlotInfo
import com.timeflow.app.data.model.AiConfig
import com.timeflow.app.utils.PreferenceKeys
import com.timeflow.app.utils.SettingsManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.json.JSONArray
import org.json.JSONObject
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import android.util.Log
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit

// 添加TAG常量
private const val TAG = "GoalRepository"

interface GoalRepository {
    suspend fun saveGoal(goal: Goal): String
    suspend fun updateGoal(goal: Goal)
    suspend fun deleteGoal(goalId: String)
    suspend fun getGoalById(goalId: String): Goal?
    fun getAllGoals(): Flow<List<Goal>>
    suspend fun getAllGoalsList(): List<Goal>
    fun getGoalsByStatus(status: String): Flow<List<Goal>>
    fun getActiveGoals(): Flow<List<Goal>>
    fun getCompletedGoals(): Flow<List<Goal>>
    fun getGoalsWithoutAiBreakdown(): Flow<List<Goal>>
    fun getGoalsNeedingReview(): Flow<List<Goal>>
    fun getGoalsByCategory(categoryId: String): Flow<List<Goal>>
    fun getGoalsByCategoryAndStatus(categoryId: String, status: String): Flow<List<Goal>>
    suspend fun getAllUsedCategories(): List<String>
    
    suspend fun saveSubTask(subTask: GoalSubTask)
    suspend fun updateSubTask(subTask: GoalSubTask)
    suspend fun deleteSubTask(subTaskId: String)
    suspend fun getSubTasksForGoal(goalId: String): List<GoalSubTask>
    
    suspend fun getGoalStats(): GoalStats
    
    /**
     * 获取最近完成的目标列表
     * @param limit 最大返回数量
     * @return 按完成时间降序排列的目标列表
     */
    suspend fun getRecentCompletedGoals(limit: Int): List<Goal>
    
    /**
     * 获取具有AI分析结果的目标列表
     * @param limit 最大返回数量
     * @return 具有AI分析的目标列表
     */
    suspend fun getGoalsWithAiAnalysis(limit: Int): List<Goal>

    /**
     * 获取所有已完成的目标列表(非Flow版本)
     * @return 已完成目标列表
     */
    suspend fun getCompletedGoalsList(): List<Goal>
    
    /**
     * 获取正在进行中的目标数量
     * @return 进行中目标数量
     */
    suspend fun getInProgressGoalsCount(): Int
    
    /**
     * 根据时间范围获取已完成目标的分布
     * @return 按时间范围分组的目标数量Map
     */
    suspend fun getCompletedGoalsByTimeRange(): Map<String, Int>
    
    /**
     * 获取按年度分组的已完成目标分布
     * @return 按季度分组的目标数量Map (Q1, Q2, Q3, Q4)
     */
    suspend fun getCompletedGoalsByYear(): Map<String, Int>
    
    /**
     * 获取按季度分组的已完成目标分布
     * @return 按月份分组的目标数量Map (一月, 二月, 三月等)
     */
    suspend fun getCompletedGoalsByQuarter(): Map<String, Int>
    
    /**
     * 获取按月份分组的已完成目标分布
     * @return 按周分组的目标数量Map (第1周, 第2周等)
     */
    suspend fun getCompletedGoalsByMonth(): Map<String, Int>
    
    /**
     * 根据领域分类获取已完成目标的分布
     * @return 按领域分组的目标数量Map
     */
    suspend fun getCompletedGoalsByDomain(): Map<String, Int>
    
    /**
     * 根据难度分类获取已完成目标的分布
     * @return 按难度分组的目标数量Map
     */
    suspend fun getCompletedGoalsByDifficulty(): Map<String, Int>

    /**
     * 获取目标及其子任务的详细信息
     * @param goalId 目标ID
     * @return 目标与子任务信息
     */
    suspend fun getGoalWithSubtasksById(goalId: String): GoalWithSubtasks?

    suspend fun getGoalSubTasks(goalId: String): List<GoalSubTask>
    
    /**
     * 调用AI API服务
     * @param endpoint API端点
     * @param requestBody 请求体
     * @return 响应字符串
     */
    suspend fun callAiApi(endpoint: String, requestBody: String): String
    
    /**
     * 增强的AI API调用，支持重试和备选模型
     * @param endpoint API端点
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @param useBackupModel 是否在失败时使用备选模型
     * @return 响应结果对象，包含成功状态和响应数据
     */
    suspend fun callEnhancedAiApi(
        endpoint: String, 
        requestBody: String,
        retryCount: Int,
        useBackupModel: Boolean
    ): AiApiResult
}

data class GoalStats(
    val totalGoals: Int,
    val aiBreakdownGoals: Int,
    val totalSubTasks: Int,
    val reviewReportsCount: Int
)

data class GoalWithSubtasks(
    val goal: Goal,
    val subTasks: List<GoalSubTask>
)

/**
 * AI API调用结果
 */
data class AiApiResult(
    val isSuccess: Boolean,
    val data: String = "",
    val error: String = "",
    val usedBackupModel: Boolean = false
)

@Singleton
class GoalRepositoryImpl @Inject constructor(
    private val goalDao: GoalDao
) : GoalRepository {

    // 添加HTTP客户端实例
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .build()

    override suspend fun saveGoal(goal: Goal): String {
        val id = goal.id.ifEmpty { UUID.randomUUID().toString() }
        val goalEntity = mapModelToEntity(goal.copy(id = id))
        goalDao.insertGoal(goalEntity)
        return id
    }

    override suspend fun updateGoal(goal: Goal) {
        val goalEntity = mapModelToEntity(goal)
        goalDao.updateGoal(goalEntity)
    }

    override suspend fun deleteGoal(goalId: String) {
        goalDao.deleteGoalById(goalId)
        goalDao.deleteAllSubTasksForGoal(goalId)
    }

    override suspend fun getGoalById(goalId: String): Goal? {
        val goalEntity = goalDao.getGoalById(goalId) ?: return null
        return mapEntityToModel(goalEntity)
    }

    override fun getAllGoals(): Flow<List<Goal>> {
        return goalDao.getAllGoals().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override suspend fun getAllGoalsList(): List<Goal> {
        val entities = goalDao.getAllGoalsList()
        return entities.map { mapEntityToModel(it) }
    }

    override fun getGoalsByStatus(status: String): Flow<List<Goal>> {
        return goalDao.getGoalsByStatus(status).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override fun getActiveGoals(): Flow<List<Goal>> {
        return goalDao.getActiveGoals().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override fun getCompletedGoals(): Flow<List<Goal>> {
        return goalDao.getCompletedGoals().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override fun getGoalsWithoutAiBreakdown(): Flow<List<Goal>> {
        return goalDao.getGoalsWithoutAiBreakdown().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override fun getGoalsNeedingReview(): Flow<List<Goal>> {
        return goalDao.getGoalsNeedingReview().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override suspend fun saveSubTask(subTask: GoalSubTask) {
        val subTaskEntity = mapSubTaskModelToEntity(subTask)
        goalDao.insertSubTask(subTaskEntity)
    }

    override suspend fun updateSubTask(subTask: GoalSubTask) {
        val subTaskEntity = mapSubTaskModelToEntity(subTask)
        goalDao.updateSubTask(subTaskEntity)
    }

    override suspend fun deleteSubTask(subTaskId: String) {
        goalDao.deleteSubTaskById(subTaskId)
    }

    override suspend fun getSubTasksForGoal(goalId: String): List<GoalSubTask> {
        val subTaskEntities = goalDao.getSubTasksForGoal(goalId)
        return subTaskEntities.map { mapSubTaskEntityToModel(it) }
    }

    override suspend fun getGoalStats(): GoalStats {
        val totalGoals = goalDao.getTotalGoalsCount()
        val aiBreakdownGoals = goalDao.getAiBreakdownGoalsCount()
        val totalSubTasks = goalDao.getTotalSubTasksCount()
        
        // 获取过去7天内的复盘报告数
        val oneWeekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        val reviewReportsCount = goalDao.getReviewReportsCount(oneWeekAgo)
        
        return GoalStats(
            totalGoals = totalGoals,
            aiBreakdownGoals = aiBreakdownGoals,
            totalSubTasks = totalSubTasks,
            reviewReportsCount = reviewReportsCount
        )
    }

    // 实体与模型转换方法
    private fun mapEntityToModel(entity: GoalEntity): Goal {
        // 解析JSON和分隔字符串内容
        val relatedTaskIds = entity.relatedTaskIds.split(",").filter { it.isNotEmpty() }
        
        val tags = entity.tags.split(",").filter { it.isNotEmpty() }
        
        val aiRecommendations = try {
            val jsonArray = JSONArray(entity.aiRecommendationsJson)
            List(jsonArray.length()) { i -> jsonArray.getString(i) }
        } catch (e: Exception) {
            emptyList()
        }

        val bestTimeSlots = try {
            val jsonArray = JSONArray(entity.bestTimeSlotsJson)
            List(jsonArray.length()) { i ->
                val obj = jsonArray.getJSONObject(i)
                TimeSlotInfo(
                    dayOfWeek = obj.getString("dayOfWeek"),
                    startHour = obj.getInt("startHour"),
                    endHour = obj.getInt("endHour"),
                    efficiency = obj.getDouble("efficiency").toFloat(),
                    description = obj.optString("description", "")
                )
            }
        } catch (e: Exception) {
            emptyList()
        }

        val metrics = try {
            val jsonObject = JSONObject(entity.metricsJson)
            val map = mutableMapOf<String, Float>()
            jsonObject.keys().forEach { key ->
                map[key] = jsonObject.getDouble(key).toFloat()
            }
            map
        } catch (e: Exception) {
            emptyMap()
        }

        return Goal(
            id = entity.id,
            title = entity.title,
            description = entity.description,
            startDate = entity.startDate,
            dueDate = entity.dueDate,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            completedAt = entity.completedAt,
            progress = entity.progress,
            priority = try {
                GoalPriority.valueOf(entity.priority)
            } catch (e: Exception) {
                GoalPriority.MEDIUM
            },
            hasAiBreakdown = entity.hasAiBreakdown,
            hasAiAnalysis = entity.hasAiAnalysis,
            relatedTaskIds = relatedTaskIds,
            aiRecommendations = aiRecommendations,
            tags = tags,
            status = entity.status,
            bestTimeSlots = bestTimeSlots,
            metrics = metrics,
            reviewFrequency = try {
                ReviewFrequency.valueOf(entity.reviewFrequency)
            } catch (e: Exception) {
                ReviewFrequency.WEEKLY
            },
            categoryId = entity.categoryId
        )
    }

    private fun mapModelToEntity(model: Goal): GoalEntity {
        // 将集合类型转换为字符串存储
        val relatedTaskIdsStr = model.relatedTaskIds.joinToString(",")
        val tagsStr = model.tags.joinToString(",")
        
        val aiRecommendationsJson = JSONArray().apply {
            model.aiRecommendations.forEach { put(it) }
        }.toString()
        
        val bestTimeSlotsJson = JSONArray().apply {
            model.bestTimeSlots.forEach { timeSlot ->
                put(JSONObject().apply {
                    put("dayOfWeek", timeSlot.dayOfWeek)
                    put("startHour", timeSlot.startHour)
                    put("endHour", timeSlot.endHour)
                    put("efficiency", timeSlot.efficiency)
                    put("description", timeSlot.description)
                })
            }
        }.toString()
        
        val metricsJson = JSONObject().apply {
            model.metrics.forEach { (key, value) ->
                put(key, value)
            }
        }.toString()
        
        return GoalEntity(
            id = model.id,
            title = model.title,
            description = model.description,
            startDate = model.startDate ?: LocalDateTime.now(),
            dueDate = model.dueDate,
            createdAt = model.createdAt,
            updatedAt = model.updatedAt,
            completedAt = model.completedAt,
            progress = model.progress,
            priority = model.priority.name,
            hasAiBreakdown = model.hasAiBreakdown,
            hasAiAnalysis = model.hasAiAnalysis,
            relatedTaskIds = relatedTaskIdsStr,
            aiRecommendationsJson = aiRecommendationsJson,
            tags = tagsStr,
            status = model.status,
            bestTimeSlotsJson = bestTimeSlotsJson,
            metricsJson = metricsJson,
            reviewFrequency = model.reviewFrequency.name,
            categoryId = model.categoryId
        )
    }

    private fun mapSubTaskEntityToModel(entity: GoalSubTaskEntity): GoalSubTask {
        return GoalSubTask(
            id = entity.id,
            goalId = entity.goalId,
            title = entity.title,
            description = entity.description,
            estimatedDurationDays = entity.estimatedDurationDays,
            completedAt = entity.completedAt,
            createdAt = entity.createdAt,
            aiRecommendation = entity.aiRecommendation,
            status = entity.status
        )
    }

    private fun mapSubTaskModelToEntity(model: GoalSubTask): GoalSubTaskEntity {
        return GoalSubTaskEntity(
            id = model.id,
            goalId = model.goalId,
            title = model.title,
            description = model.description,
            estimatedDurationDays = model.estimatedDurationDays,
            completedAt = model.completedAt,
            createdAt = model.createdAt,
            aiRecommendation = model.aiRecommendation,
            status = model.status
        )
    }

    override suspend fun getRecentCompletedGoals(limit: Int): List<Goal> {
        val completedGoalEntities = goalDao.getCompletedGoalsByLimit(limit)
        return completedGoalEntities.map { mapEntityToModel(it) }
    }

    override suspend fun getGoalsWithAiAnalysis(limit: Int): List<Goal> {
        val goalsWithAnalysis = goalDao.getGoalsWithAiAnalysis(limit)
        return goalsWithAnalysis.map { mapEntityToModel(it) }
    }

    /**
     * 获取所有已完成的目标列表(非Flow版本)
     * @return 已完成目标列表
     */
    override suspend fun getCompletedGoalsList(): List<Goal> {
        val goalEntities = goalDao.getCompletedGoalsList()
        return goalEntities.map { mapEntityToModel(it) }
    }
    
    /**
     * 获取正在进行中的目标数量
     * @return 进行中目标数量
     */
    override suspend fun getInProgressGoalsCount(): Int {
        return goalDao.getInProgressGoalsCount()
    }

    override fun getGoalsByCategory(categoryId: String): Flow<List<Goal>> {
        return goalDao.getGoalsByCategory(categoryId).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override fun getGoalsByCategoryAndStatus(categoryId: String, status: String): Flow<List<Goal>> {
        return goalDao.getGoalsByCategoryAndStatus(categoryId, status).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }

    override suspend fun getAllUsedCategories(): List<String> {
        return goalDao.getAllUsedCategories()
    }
    
    /**
     * 根据时间范围获取已完成目标的分布
     * @return 按时间范围分组的目标数量Map
     */
    override suspend fun getCompletedGoalsByTimeRange(): Map<String, Int> {
        // 默认实现，返回年度数据
        return getCompletedGoalsByYear()
    }
    
    override suspend fun getCompletedGoalsByYear(): Map<String, Int> {
        // 获取完成的目标
        val completedGoals = goalDao.getCompletedGoalsList()
        
        // 按季度分组
        val quarters = mutableMapOf(
            "Q1" to 0,
            "Q2" to 0,
            "Q3" to 0,
            "Q4" to 0
        )
        
        completedGoals.forEach { goalEntity ->
            val goal = mapEntityToModel(goalEntity)
            val completionTime = goal.completedAt ?: goal.updatedAt
            val month = completionTime.monthValue
            
            val quarter = when (month) {
                in 1..3 -> "Q1"
                in 4..6 -> "Q2"
                in 7..9 -> "Q3"
                else -> "Q4"
            }
            
            quarters[quarter] = quarters[quarter]!! + 1
        }
        
        return quarters
    }
    
    override suspend fun getCompletedGoalsByQuarter(): Map<String, Int> {
        // 获取完成的目标
        val completedGoals = goalDao.getCompletedGoalsList()
        
        // 获取当前季度的月份
        val currentMonth = java.time.LocalDateTime.now().monthValue
        val currentQuarter = when (currentMonth) {
            in 1..3 -> 1
            in 4..6 -> 2
            in 7..9 -> 3
            else -> 4
        }
        
        // 当前季度的月份名称
        val monthNames = when (currentQuarter) {
            1 -> listOf("一月", "二月", "三月")
            2 -> listOf("四月", "五月", "六月")
            3 -> listOf("七月", "八月", "九月")
            else -> listOf("十月", "十一月", "十二月")
        }
        
        // 初始化月份映射
        val months = monthNames.associateWith { 0 }.toMutableMap()
        
        // 按月份分组
        completedGoals.forEach { goalEntity ->
            val goal = mapEntityToModel(goalEntity)
            val completionTime = goal.completedAt ?: goal.updatedAt
            val monthValue = completionTime.monthValue
            
            // 检查是否在当前季度
            val monthIndex = when (currentQuarter) {
                1 -> monthValue - 1
                2 -> monthValue - 4
                3 -> monthValue - 7
                else -> monthValue - 10
            }
            
            if (monthIndex in 0..2) {
                val monthName = monthNames[monthIndex]
                months[monthName] = months[monthName]!! + 1
            }
        }
        
        return months
    }
    
    override suspend fun getCompletedGoalsByMonth(): Map<String, Int> {
        // 获取完成的目标
        val completedGoals = goalDao.getCompletedGoalsList()
        
        // 获取当前月份和该月的天数
        val now = java.time.LocalDateTime.now()
        val currentMonth = now.monthValue
        val currentYear = now.year
        val daysInMonth = java.time.YearMonth.of(currentYear, currentMonth).lengthOfMonth()
        
        // 按周分组
        val weeks = (1..5).associateWith { weekNum -> 
            "第${weekNum}周"
        }.values.associateWith { 0 }.toMutableMap()
        
        // 分配目标到对应的周
        completedGoals.forEach { goalEntity ->
            val goal = mapEntityToModel(goalEntity)
            val completionTime = goal.completedAt ?: goal.updatedAt
            val day = completionTime.dayOfMonth
            
            val weekNumber = (day - 1) / 7 + 1
            if (weekNumber <= 5) {
                val weekKey = "第${weekNumber}周"
                weeks[weekKey] = weeks[weekKey]!! + 1
            }
        }
        
        return weeks
    }
    
    override suspend fun getCompletedGoalsByDomain(): Map<String, Int> {
        // 获取完成的目标
        val completedGoals = goalDao.getCompletedGoalsList()
        
        // 按领域分组
        val domains = mutableMapOf<String, Int>()
        
        // 处理每个目标，先将实体转换为模型对象
        completedGoals.forEach { goalEntity ->
            val goal = mapEntityToModel(goalEntity)
            // 从模型对象中获取标签作为领域
            val domain = if (goal.tags.isNotEmpty()) goal.tags.first() else "未分类"
            domains[domain] = domains.getOrDefault(domain, 0) + 1
        }
        
        return domains
    }
    
    /**
     * 根据难度分类获取已完成目标的分布
     * @return 按难度分组的目标数量Map
     */
    override suspend fun getCompletedGoalsByDifficulty(): Map<String, Int> {
        val goals = goalDao.getCompletedGoalsList()
        
        // 按目标难度分组
        val difficultyMap = mutableMapOf<String, Int>()
        
        // 初始化所有难度级别
        difficultyMap["简单"] = 0
        difficultyMap["中等"] = 0
        difficultyMap["困难"] = 0
        difficultyMap["紧急"] = 0
        
        // 统计每个难度的目标数量
        goals.forEach { goalEntity ->
            val goal = mapEntityToModel(goalEntity)
            val difficultyKey = when(goal.priority) {
                GoalPriority.LOW -> "简单"
                GoalPriority.MEDIUM -> "中等"
                GoalPriority.HIGH -> "困难"
                GoalPriority.URGENT -> "紧急"
            }
            
            difficultyMap[difficultyKey] = difficultyMap.getOrDefault(difficultyKey, 0) + 1
        }
        
        return difficultyMap
    }

    /**
     * 获取目标及其子任务的详细信息
     * @param goalId 目标ID
     * @return 目标与子任务信息
     */
    override suspend fun getGoalWithSubtasksById(goalId: String): GoalWithSubtasks? {
        val goal = getGoalById(goalId) ?: return null
        val subTasks = getSubTasksForGoal(goalId)
        return GoalWithSubtasks(goal, subTasks)
    }

    override suspend fun getGoalSubTasks(goalId: String): List<GoalSubTask> {
        val subTaskEntities = goalDao.getSubTasksForGoal(goalId)
        return subTaskEntities.map { mapSubTaskEntityToModel(it) }
    }
    
    /**
     * 调用AI API服务
     * @param endpoint API端点
     * @param requestBody 请求体
     * @return 响应字符串
     */
    override suspend fun callAiApi(endpoint: String, requestBody: String): String {
        try {
            // 构建HTTP请求
            val mediaType = "application/json; charset=utf-8".toMediaType()
            val requestBodyObj = requestBody.toRequestBody(mediaType)
            
            // 获取AI配置
            val aiConfig = getAiConfig()
            
            val request = Request.Builder()
                .url("${aiConfig.serverUrl}$endpoint")
                .addHeader("Authorization", "Bearer ${aiConfig.apiKey}")
                .post(requestBodyObj)
                .build()
            
            // 执行请求
            val response = httpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                Log.e(TAG, "API调用失败: ${response.code}, ${response.body?.string()}")
                throw Exception("API调用失败: ${response.code}")
            }
            
            // 解析响应
            val responseBody = response.body?.string() ?: throw Exception("返回空响应")
            val jsonResponse = JSONObject(responseBody)
            
            // 提取结果
            val choices = jsonResponse.getJSONArray("choices")
            if (choices.length() == 0) {
                throw Exception("API返回结果为空")
            }
            
            val message = choices.getJSONObject(0).getJSONObject("message")
            return message.getString("content")
            
        } catch (e: Exception) {
            Log.e(TAG, "调用AI API失败", e)
            throw e
        }
    }
    
    /**
     * 增强的AI API调用，支持重试和备选模型
     * @param endpoint API端点
     * @param requestBody 请求体
     * @param retryCount 重试次数
     * @param useBackupModel 是否在失败时使用备选模型
     * @return 响应结果对象，包含成功状态和响应数据
     */
    override suspend fun callEnhancedAiApi(
        endpoint: String, 
        requestBody: String,
        retryCount: Int,
        useBackupModel: Boolean
    ): AiApiResult {
        var lastException: Exception? = null
        for (i in 1..retryCount) {
            try {
                return AiApiResult(
                    isSuccess = true,
                    data = callAiApi(endpoint, requestBody),
                    usedBackupModel = false
                )
            } catch (e: Exception) {
                lastException = e
                if (useBackupModel) {
                    Log.e(TAG, "重试 $i 失败，使用备选模型", e)
                } else {
                    throw e
                }
            }
        }
        throw lastException ?: Exception("所有重试失败")
    }
    
    /**
     * 获取AI配置
     */
    private suspend fun getAiConfig(): AiConfig {
        // 从设置管理器获取配置信息
        val settingsManager = SettingsManager.getInstance()
        return AiConfig(
            serverUrl = settingsManager.getStringPreference(PreferenceKeys.AI_SERVER_URL, "https://api.openai.com"),
            apiKey = settingsManager.getStringPreference(PreferenceKeys.AI_API_KEY, ""),
            modelName = settingsManager.getStringPreference(PreferenceKeys.AI_MODEL_NAME, "gpt-3.5-turbo"),
            maxTokens = settingsManager.getIntPreference(PreferenceKeys.AI_MAX_TOKENS, 2000)
        )
    }
} 