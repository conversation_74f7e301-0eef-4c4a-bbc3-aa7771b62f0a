package com.timeflow.app.ui.screen.goal

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import androidx.compose.foundation.BorderStroke
import com.timeflow.app.ui.screen.goal.components.AiBreakdownProcessScreen
import com.timeflow.app.ui.screen.goal.components.AiBreakdownResultScreen
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import android.widget.Toast

// 注意：Goal和GoalSubTask使用data.model中的定义

// 子目标数据类
data class SubGoal(
    val id: String,
    val title: String,
    val description: String,
    val priority: GoalPriority,
    val estimatedDays: Int,
    val isCompleted: Boolean = false,
    val isFromAI: Boolean = false
)

// 注：BreakdownState定义在GoalViewModel.kt中

/**
 * 目标拆解屏幕
 * 支持手动输入子目标和AI自动拆解功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalBreakdownScreen(
    navController: NavController,
    goalId: String,
    viewModel: GoalViewModel = hiltViewModel()
) {
    // 基础状态
    val breakdownState by viewModel.breakdownState
    val uiState by viewModel.uiState
    val context = LocalContext.current
    
    // 页面状态管理
    var showAddSubGoalDialog by remember { mutableStateOf(false) }
    var showAiSuggestionsDialog by remember { mutableStateOf(false) }
    var isGeneratingAI by remember { mutableStateOf(false) }
    var currentGoal by remember { mutableStateOf<com.timeflow.app.data.model.Goal?>(null) }
    
    // 子目标状态 - 管理手动添加的子目标
    var subGoals by remember { mutableStateOf<List<SubGoal>>(emptyList()) }
    
    // 手动添加子目标对话框相关状态
    var newSubGoalTitle by remember { mutableStateOf("") }
    var newSubGoalDescription by remember { mutableStateOf("") }
    var newSubGoalPriority by remember { mutableStateOf(GoalPriority.MEDIUM) }
    var newSubGoalEstimatedDays by remember { mutableStateOf("3") }
    
    // AI建议相关状态
    var aiSuggestions by remember { mutableStateOf<List<com.timeflow.app.data.model.GoalSubTask>>(emptyList()) }
    
    val scope = rememberCoroutineScope()

    // 获取目标详情
    LaunchedEffect(goalId) {
        Log.d("GoalBreakdownScreen", "开始加载目标详情: $goalId")
        
        // 🎯 如果是临时ID（以temp_开头），先尝试等待并重新获取真实ID
        if (goalId.startsWith("temp_")) {
            Log.w("GoalBreakdownScreen", "检测到临时ID，尝试获取真实目标ID")
            
            // 等待数据同步
            var realGoal: com.timeflow.app.data.model.Goal? = null
            var retryCount = 0
            val maxRetries = 5
            
            while (realGoal == null && retryCount < maxRetries) {
                delay(500)
                retryCount++
                Log.d("GoalBreakdownScreen", "第${retryCount}次尝试获取最新创建的目标")
                
                // 尝试从ViewModel获取最新目标
                viewModel.loadGoals()
                delay(200) // 等待数据加载
                
                val goals = viewModel.goals.value
                realGoal = goals.maxByOrNull { it.createdAt } // 获取最新创建的目标
                
                if (realGoal != null) {
                    Log.d("GoalBreakdownScreen", "找到最新目标: ${realGoal.id} - ${realGoal.title}")
                    break
                }
            }
            
            if (realGoal != null) {
                // 使用真实ID重新导航
                Log.d("GoalBreakdownScreen", "使用真实ID重新导航: ${realGoal.id}")
                navController.navigate(AppDestinations.goalBreakdownRoute(realGoal.id)) {
                    popUpTo(AppDestinations.goalBreakdownRoute(goalId)) { inclusive = true }
                }
                return@LaunchedEffect
            } else {
                Log.e("GoalBreakdownScreen", "无法获取真实目标ID，显示错误")
                // 继续执行，会显示"目标不存在"错误
            }
        }
        
        viewModel.loadGoalDetail(goalId)
    }

    // 获取当前目标
    LaunchedEffect(uiState) {
        if (uiState is GoalUiState.Success) {
            currentGoal = viewModel.currentGoal.value
            Log.d("GoalBreakdownScreen", "当前目标: ${currentGoal?.title}")
        }
    }

    // 处理AI拆解状态
    LaunchedEffect(breakdownState) {
        when (val state = breakdownState) {
            is BreakdownState.Processing -> {
                isGeneratingAI = true
                Log.d("GoalBreakdownScreen", "AI拆解进行中: isBackupMode=${state.isBackupMode}")
            }
            is BreakdownState.ShowingResult -> {
                isGeneratingAI = false
                Log.d("GoalBreakdownScreen", "AI拆解显示结果: isBackupMode=${state.isBackupMode}")
                
                // 获取生成的子任务并更新UI
                val generatedSubTasks = viewModel.subTasks.value
                if (generatedSubTasks.isNotEmpty()) {
                    // 将GoalSubTask转换为SubGoal用于UI显示
                    val newSubGoals = generatedSubTasks.map { goalSubTask ->
                        SubGoal(
                            id = goalSubTask.id,
                            title = goalSubTask.title,
                            description = goalSubTask.description ?: "",
                            priority = GoalPriority.MEDIUM,
                            estimatedDays = goalSubTask.estimatedDurationDays ?: 3,
                            isFromAI = true
                        )
                    }
                    subGoals = newSubGoals
                    Log.d("GoalBreakdownScreen", "显示AI生成的子目标: ${newSubGoals.size}个")
                }
            }
            is BreakdownState.Completed -> {
                isGeneratingAI = false
                Log.d("GoalBreakdownScreen", "AI拆解完成")
            }
            is BreakdownState.Error -> {
                isGeneratingAI = false
                Log.e("GoalBreakdownScreen", "AI拆解失败: ${state.message}")

                // 🔧 改进：根据错误类型提供不同的提示
                val errorMessage = when {
                    state.message.contains("AI配置未设置") -> {
                        "AI配置未设置，请先在设置中配置AI服务后重试"
                    }
                    state.message.contains("API") -> {
                        "AI服务调用失败，请检查网络连接和AI配置"
                    }
                    else -> {
                        "AI拆解失败: ${state.message}"
                    }
                }

                // 显示错误提示
                Toast.makeText(
                    context,
                    errorMessage,
                    Toast.LENGTH_LONG
                ).show()
            }
            is BreakdownState.Idle -> {
                isGeneratingAI = false
                Log.d("GoalBreakdownScreen", "AI拆解状态重置")
            }
        }
    }

    // 页面内容
    when {
        uiState is GoalUiState.Loading -> {
            LoadingScreen()
        }
        
        uiState is GoalUiState.Error -> {
            val errorState = uiState as GoalUiState.Error
            ErrorScreen(
                message = errorState.message,
                onRetry = { viewModel.loadGoalDetail(goalId) }
            )
        }
        
        currentGoal != null -> {
            GoalBreakdownContent(
                goal = currentGoal!!,
                subGoals = subGoals,
                isGeneratingAI = isGeneratingAI,
                onBackClick = { navController.popBackStack() },
                onManualAddClick = { showAddSubGoalDialog = true },
                onAIGenerateClick = {
                    scope.launch {
                        // 🔧 改进：在开始AI拆解前先检查配置
                        Log.d("GoalBreakdownScreen", "用户点击AI拆解按钮")

                        // 先检查AI配置状态
                        val configStatus = viewModel.checkAiConfigStatus()
                        Log.d("GoalBreakdownScreen", "AI配置状态: $configStatus")

                        if (configStatus.contains("❌")) {
                            // 配置有问题，显示错误信息
                            Toast.makeText(
                                context,
                                configStatus,
                                Toast.LENGTH_LONG
                            ).show()
                            return@launch
                        }

                        isGeneratingAI = true

                        // 显示友好的提示信息
                        Toast.makeText(
                            context,
                            "正在调用AI分析目标内容...",
                            Toast.LENGTH_SHORT
                        ).show()

                        viewModel.requestAiBreakdown(goalId)
                    }
                },
                onTestAiClick = {
                    scope.launch {
                        Log.d("GoalBreakdownScreen", "用户点击测试AI连接")

                        Toast.makeText(
                            context,
                            "正在测试AI连接...",
                            Toast.LENGTH_SHORT
                        ).show()

                        val testResult = viewModel.testAiConnection()
                        Toast.makeText(
                            context,
                            testResult,
                            Toast.LENGTH_LONG
                        ).show()
                    }
                },
                onSubGoalDelete = { subGoal ->
                    subGoals = subGoals.filter { it.id != subGoal.id }
                },
                onSubGoalToggle = { subGoal ->
                    subGoals = subGoals.map {
                        if (it.id == subGoal.id) it.copy(isCompleted = !it.isCompleted)
                        else it
                    }
                },
                onCompleteClick = {
                    // 💾 完善保存逻辑
                    scope.launch {
                        try {
                            Log.d("GoalBreakdownScreen", "开始保存子目标: ${subGoals.size}个")
                            
                            // 将SubGoal转换为GoalSubTask并保存到数据库
                            subGoals.forEach { subGoal ->
                                val goalSubTask = com.timeflow.app.data.model.GoalSubTask(
                                    id = subGoal.id,
                                    goalId = goalId,
                                    title = subGoal.title,
                                    description = subGoal.description,
                                    estimatedDurationDays = subGoal.estimatedDays,
                                    completedAt = if (subGoal.isCompleted) LocalDateTime.now() else null,
                                    createdAt = LocalDateTime.now(),
                                    status = if (subGoal.isCompleted) "已完成" else "待开始",
                                    aiRecommendation = if (subGoal.isFromAI) "AI推荐的子目标" else null
                                )
                                
                                // 保存到数据库
                                viewModel.addSubTaskToGoal(goalId, goalSubTask)
                            }
                            
                            // 标记目标已完成拆解
                            if (subGoals.isNotEmpty()) {
                                val updatedGoal = currentGoal!!.copy(
                                    hasAiBreakdown = true,
                                    updatedAt = LocalDateTime.now()
                                )
                                viewModel.updateGoal(updatedGoal)
                            }
                            
                            // 显示成功提示
                            Toast.makeText(
                                context, 
                                "成功保存${subGoals.size}个子目标", 
                                Toast.LENGTH_SHORT
                            ).show()
                            
                            Log.d("GoalBreakdownScreen", "子目标保存完成")
                            
                            // 返回上一页
                            navController.popBackStack()
                            
                        } catch (e: Exception) {
                            Log.e("GoalBreakdownScreen", "保存子目标失败", e)
                            Toast.makeText(
                                context,
                                "保存失败: ${e.message}",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                }
            )
        }
    }

    // 手动添加子目标对话框
    if (showAddSubGoalDialog) {
        AddSubGoalDialog(
            title = newSubGoalTitle,
            description = newSubGoalDescription,
            priority = newSubGoalPriority,
            estimatedDays = newSubGoalEstimatedDays,
            onTitleChange = { newSubGoalTitle = it },
            onDescriptionChange = { newSubGoalDescription = it },
            onPriorityChange = { newSubGoalPriority = it },
            onEstimatedDaysChange = { newSubGoalEstimatedDays = it },
            onDismiss = {
                showAddSubGoalDialog = false
                // 重置状态
                newSubGoalTitle = ""
                newSubGoalDescription = ""
                newSubGoalPriority = GoalPriority.MEDIUM
                newSubGoalEstimatedDays = "3"
            },
            onConfirm = {
                if (newSubGoalTitle.isNotBlank()) {
                    val newSubGoal = SubGoal(
                        id = java.util.UUID.randomUUID().toString(),
                        title = newSubGoalTitle,
                        description = newSubGoalDescription,
                        priority = newSubGoalPriority,
                        estimatedDays = newSubGoalEstimatedDays.toIntOrNull() ?: 3,
                        isFromAI = false
                    )
                    
                    subGoals = subGoals + newSubGoal
                    Log.d("GoalBreakdownScreen", "添加手动子目标: ${newSubGoal.title}")
                    
                    // 关闭对话框并重置状态
                    showAddSubGoalDialog = false
                    newSubGoalTitle = ""
                    newSubGoalDescription = ""
                    newSubGoalPriority = GoalPriority.MEDIUM
                    newSubGoalEstimatedDays = "3"
                }
            }
        )
    }

    // AI建议对话框
    if (showAiSuggestionsDialog && aiSuggestions.isNotEmpty()) {
        AiSuggestionsDialog(
            suggestions = aiSuggestions,
            onDismiss = { 
                showAiSuggestionsDialog = false
                aiSuggestions = emptyList()
                viewModel.resetBreakdownState()
            },
            onAcceptSuggestions = { selectedSuggestions ->
                // 将AI建议转换为SubGoal并添加到列表
                val newSubGoals = selectedSuggestions.map { suggestion ->
                    SubGoal(
                        id = suggestion.id,
                        title = suggestion.title,
                        description = suggestion.description,
                        priority = GoalPriority.MEDIUM, // AI建议默认为中等优先级
                        estimatedDays = suggestion.estimatedDurationDays,
                        isFromAI = true
                    )
                }
                
                subGoals = subGoals + newSubGoals
                Log.d("GoalBreakdownScreen", "添加AI建议子目标: ${newSubGoals.size}个")
                
                showAiSuggestionsDialog = false
                aiSuggestions = emptyList()
                viewModel.resetBreakdownState()
            }
        )
    }
}

@Composable
private fun GoalInfoCard(goal: com.timeflow.app.data.model.Goal?) {
    goal?.let {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            shadowElevation = 2.dp
                        ) {
                            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = it.title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = it.description,
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                    PriorityTag(priority = it.priority)
                    
                                    Text(
                        text = "创建于 ${it.createdAt.format(DateTimeFormatter.ofPattern("MM-dd"))}",
                        fontSize = 12.sp,
                        color = Color(0xFF999999)
                    )
                }
            }
        }
    }
}

@Composable
private fun AddSubGoalButton(
    onManualAdd: () -> Unit,
    onAIGenerate: () -> Unit,
    onTestAi: () -> Unit = {},
    isGeneratingAI: Boolean = false
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 主要按钮行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 手动添加按钮
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onManualAdd() },
                shape = RoundedCornerShape(12.dp),
                color = Color.White,
                border = BorderStroke(1.dp, DustyLavender)
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        tint = DustyLavender,
                        modifier = Modifier.size(18.dp)
                    )
                Spacer(modifier = Modifier.width(8.dp))
                                            Text(
                    text = "手动添加",
                    fontSize = 13.sp,
                    color = DustyLavender,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        // AI拆解按钮
        Surface(
                                    modifier = Modifier
                .weight(1f)
                .clickable { if (!isGeneratingAI) onAIGenerate() },
            shape = RoundedCornerShape(12.dp),
            color = if (isGeneratingAI) Color.Gray.copy(alpha = 0.6f) else DustyLavender
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                if (isGeneratingAI) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(18.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                                        Icon(
                        imageVector = Icons.Default.Psychology,
                                            contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(18.dp)
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
                                            Text(
                    text = if (isGeneratingAI) "生成中..." else "AI拆解",
                    fontSize = 13.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        }

        // 🔧 测试AI连接按钮
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onTestAi() },
            shape = RoundedCornerShape(8.dp),
            color = Color.White,
            border = BorderStroke(0.5.dp, Color.Gray.copy(alpha = 0.3f))
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.HealthAndSafety,
                    contentDescription = null,
                    tint = Color.Gray,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = "测试AI连接",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun SubGoalCard(
    subGoal: SubGoal,
    index: Int,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    onToggleComplete: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        color = Color.White,
        shadowElevation = 1.dp
                        ) {
                            Column(
            modifier = Modifier.padding(16.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                // 序号
                Surface(
                    modifier = Modifier.size(24.dp),
                    shape = CircleShape,
                    color = if (subGoal.isCompleted) Color(0xFFE0F2FE) else DustyLavender.copy(alpha = 0.1f)
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        if (subGoal.isCompleted) {
                                        Icon(
                                imageVector = Icons.Default.Check,
                                            contentDescription = null,
                                tint = Color(0xFF0284C7),
                                modifier = Modifier.size(12.dp)
                                        )
                        } else {
                                        Text(
                                text = index.toString(),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = DustyLavender
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                                Column(
                                            modifier = Modifier.weight(1f)
                                        ) {
                    Text(
                        text = subGoal.title,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = if (subGoal.isCompleted) Color(0xFF999999) else Color(0xFF333333)
                    )
                    
                    if (subGoal.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = subGoal.description,
                            fontSize = 12.sp,
                            color = Color(0xFF666666),
                            lineHeight = 16.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                        PriorityTag(priority = subGoal.priority)
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Surface(
                            shape = RoundedCornerShape(50),
                            color = Color(0xFFF3F4F6)
                        ) {
                            Text(
                                text = "${subGoal.estimatedDays}天",
                                fontSize = 10.sp,
                                color = Color(0xFF6B7280),
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                        
                        if (subGoal.isFromAI) {
                            Spacer(modifier = Modifier.width(6.dp))
                            Surface(
                                shape = RoundedCornerShape(50),
                                color = Color(0xFFDDD6FE)
                            ) {
                                        Text(
                                    text = "AI",
                                    fontSize = 10.sp,
                                    color = Color(0xFF7C3AED),
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
                
                // 操作按钮
                Row {
                    IconButton(
                        onClick = onToggleComplete,
                        modifier = Modifier.size(32.dp)
                                    ) {
                                        Icon(
                            imageVector = if (subGoal.isCompleted) Icons.Default.Undo else Icons.Default.CheckCircle,
                            contentDescription = if (subGoal.isCompleted) "取消完成" else "标记完成",
                            tint = if (subGoal.isCompleted) Color(0xFF999999) else Color(0xFF10B981),
                                            modifier = Modifier.size(16.dp)
                                        )
                    }
                    
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFEF4444),
                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

@Composable
private fun PriorityTag(priority: GoalPriority) {
    val (text, color) = when (priority) {
        GoalPriority.URGENT -> "紧急" to Color(0xFFFF4444)
        GoalPriority.HIGH -> "高" to Color(0xFFFF8800)
        GoalPriority.MEDIUM -> "中" to Color(0xFF10B981)
        GoalPriority.LOW -> "低" to Color(0xFF3B82F6)
    }
    
    Surface(
        shape = RoundedCornerShape(50),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            fontSize = 10.sp,
            color = color,
            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
        )
    }
}

@Composable
private fun AddSubGoalDialog(
    title: String,
    description: String,
    priority: GoalPriority,
    estimatedDays: String,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onPriorityChange: (GoalPriority) -> Unit,
    onEstimatedDaysChange: (String) -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "添加子目标",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                letterSpacing = (-0.3).sp
            )
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                item {
                    // 标题输入
                    OutlinedTextField(
                        value = title,
                        onValueChange = onTitleChange,
                        label = { Text("子目标标题", fontSize = 11.sp) },
                        placeholder = { 
                            Text(
                                "输入具体的子目标...",
                                color = Color(0xFFBBBBBB),
                                fontSize = 11.sp,
                                letterSpacing = (-0.2).sp
                            ) 
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            focusedLabelColor = DustyLavender
                        )
                    )
                }
                
                item {
                    // 描述输入
                    OutlinedTextField(
                        value = description,
                        onValueChange = onDescriptionChange,
                        label = { Text("详细描述", fontSize = 11.sp) },
                        placeholder = { 
                            Text(
                                "描述如何完成这个子目标...",
                                color = Color(0xFFBBBBBB),
                                fontSize = 11.sp,
                                letterSpacing = (-0.2).sp
                            ) 
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp),
                        maxLines = 3,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            focusedLabelColor = DustyLavender
                        )
                    )
                }
                
                item {
                    // 优先级选择
                            Text(
                        text = "优先级",
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Medium,
                                color = Color(0xFF333333),
                                letterSpacing = (-0.2).sp
                            )
                            
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        GoalPriority.values().forEach { priorityOption ->
                            val isSelected = priority == priorityOption
                            val color = when (priorityOption) {
                                GoalPriority.URGENT -> Color(0xFFFF4444)
                                GoalPriority.HIGH -> Color(0xFFFF8800)
                                GoalPriority.MEDIUM -> Color(0xFF00AA00)
                                GoalPriority.LOW -> Color(0xFF0088FF)
                            }
                            
                            Surface(
                                modifier = Modifier
                                    .weight(1f)
                                    .clickable { onPriorityChange(priorityOption) },
                                shape = RoundedCornerShape(8.dp),
                                color = if (isSelected) color.copy(alpha = 0.1f) else Color.Transparent,
                                border = BorderStroke(
                                    1.dp, 
                                    if (isSelected) color else Color(0xFFE0E0E0)
                                )
                            ) {
                            Text(
                                    text = when (priorityOption) {
                                        GoalPriority.URGENT -> "紧急"
                                        GoalPriority.HIGH -> "高"
                                        GoalPriority.MEDIUM -> "中"
                                        GoalPriority.LOW -> "低"
                                    },
                                    fontSize = 11.sp,
                                    color = if (isSelected) color else Color(0xFF666666),
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }
                    }
                }
                
                item {
                    // 预估天数
                    OutlinedTextField(
                        value = estimatedDays,
                        onValueChange = { 
                            if (it.isEmpty() || it.toIntOrNull() != null) {
                                onEstimatedDaysChange(it)
                            }
                        },
                        label = { Text("预估天数", fontSize = 11.sp) },
                        placeholder = { 
                            Text(
                                "7",
                                color = Color(0xFFBBBBBB),
                                fontSize = 11.sp,
                                letterSpacing = (-0.2).sp
                            ) 
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions.Default.copy(
                            imeAction = ImeAction.Done
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            focusedLabelColor = DustyLavender
                        )
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                enabled = title.isNotBlank(),
                                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender
                )
            ) {
                Text("添加", fontSize = 11.sp, letterSpacing = (-0.2).sp)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 11.sp, letterSpacing = (-0.2).sp)
            }
        },
        containerColor = Color.White
    )
}

@Composable
private fun AiSuggestionsDialog(
    suggestions: List<com.timeflow.app.data.model.GoalSubTask>,
    onDismiss: () -> Unit,
    onAcceptSuggestions: (List<com.timeflow.app.data.model.GoalSubTask>) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                    imageVector = Icons.Default.Psychology,
                                    contentDescription = null,
                    tint = DustyLavender,
                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI智能拆解建议",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    letterSpacing = (-0.3).sp
                )
            }
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp)
            ) {
                if (suggestions.isEmpty()) {
                    Text(
                        text = "AI暂时没有提供建议",
                        fontSize = 13.sp,
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Center
                    )
                } else {
                    Text(
                        text = "AI提供了${suggestions.size}个建议",
                        fontSize = 13.sp,
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "请选择要添加的建议",
                        fontSize = 12.sp,
                        color = Color(0xFF999999),
                        textAlign = TextAlign.Center
                    )
                }
            }
        },
        confirmButton = {
            if (suggestions.isNotEmpty()) {
                Button(
                    onClick = { onAcceptSuggestions(suggestions) },
                    enabled = suggestions.isNotEmpty(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = DustyLavender
                    )
                ) {
                    Text("接受建议", fontSize = 11.sp, letterSpacing = (-0.2).sp)
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 11.sp, letterSpacing = (-0.2).sp)
            }
        },
        containerColor = Color.White
    )
}

@Composable
private fun GoalBreakdownContent(
    goal: com.timeflow.app.data.model.Goal,
    subGoals: List<SubGoal>,
    isGeneratingAI: Boolean,
    onBackClick: () -> Unit,
    onManualAddClick: () -> Unit,
    onAIGenerateClick: () -> Unit,
    onTestAiClick: () -> Unit = {},
    onSubGoalDelete: (SubGoal) -> Unit,
    onSubGoalToggle: (SubGoal) -> Unit,
    onCompleteClick: () -> Unit
) {
    LazyColumn(
                    modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 返回按钮
        item {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF333333)
                    )
                }

                                Text(
                    text = "目标拆解",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )

                Spacer(modifier = Modifier.width(48.dp))
            }
        }

        // 目标信息卡片
        item {
            GoalInfoCard(goal = goal)
        }

        // 添加子目标按钮
        item {
            AddSubGoalButton(
                onManualAdd = onManualAddClick,
                onAIGenerate = onAIGenerateClick,
                onTestAi = onTestAiClick,
                isGeneratingAI = isGeneratingAI
            )
        }

        // 子目标列表
        if (subGoals.isNotEmpty()) {
            item {
                    Text(
                    text = "子目标列表 (${subGoals.size}个)",
                        fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            itemsIndexed(subGoals) { index, subGoal ->
                SubGoalCard(
                    subGoal = subGoal,
                    index = index + 1,
                    onEdit = { /* TODO: 编辑子目标 */ },
                    onDelete = { onSubGoalDelete(subGoal) },
                    onToggleComplete = { onSubGoalToggle(subGoal) }
                )
            }
        }

        // 完成按钮
        item {
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = onCompleteClick,
                    modifier = Modifier
                        .fillMaxWidth()
                    .height(48.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender
                ),
                shape = RoundedCornerShape(12.dp),
                enabled = subGoals.isNotEmpty() // 只有有子目标时才能点击
            ) {
                Text(
                    text = if (subGoals.isEmpty()) "请先添加子目标" else "完成拆解 (${subGoals.size}个子目标)",
                    fontSize = 13.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )
            }
        }
    }
}

@Composable
private fun LoadingScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F8)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                color = DustyLavender,
                strokeWidth = 3.dp,
                modifier = Modifier.size(40.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "正在加载目标信息...",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
        }
    }
}

@Composable
private fun ErrorScreen(
    message: String,
    onRetry: () -> Unit
) {
        Box(
            modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F8)),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(32.dp)
    ) {
        Icon(
                imageVector = Icons.Default.Error,
            contentDescription = null,
                tint = Color(0xFFE57373),
                modifier = Modifier.size(48.dp)
        )
            Spacer(modifier = Modifier.height(16.dp))
        Text(
                text = "加载失败",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF333333)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
            fontSize = 14.sp,
                color = Color(0xFF666666),
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender
                )
            ) {
                Text("重试", color = Color.White)
            }
        }
    }
} 