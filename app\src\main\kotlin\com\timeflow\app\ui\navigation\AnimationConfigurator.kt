package com.timeflow.app.ui.navigation

import androidx.compose.animation.*

/**
 * 🎬 智能动画配置器
 * 根据页面类型和导航场景，自动选择最合适的动画效果
 * 
 * 设计理念：
 * - 主要流程页面使用流畅的滑动动画（类似iOS）
 * - 模态页面使用弹出效果
 * - 快速操作使用轻量级动画
 * - 底部导航使用无动画或交叉淡入淡出
 */
object AnimationConfigurator {
    
    /**
     * 页面类型枚举
     */
    enum class PageType {
        MAIN_PAGE,          // 主要页面（首页、任务列表等）
        MODAL_PAGE,         // 模态页面（设置、详情等）
        CREATION_PAGE,      // 创建页面（新建任务、目标等）
        DETAIL_PAGE,        // 详情页面
        QUICK_ACTION,       // 快速操作页面
        BOTTOM_NAV,         // 底部导航切换
        SPECIAL_EFFECT      // 特殊效果页面
    }
    
    /**
     * 导航方向枚举
     */
    enum class NavigationDirection {
        FORWARD,    // 前进
        BACKWARD,   // 返回
        LATERAL,    // 平级切换（底部导航）
        MODAL       // 模态弹出
    }
    
    /**
     * 根据源页面和目标页面，智能选择动画
     */
    fun getAnimationForNavigation(
        fromRoute: String?,
        toRoute: String,
        direction: NavigationDirection = NavigationDirection.FORWARD
    ): AnimationSet {
        val fromPageType = getPageType(fromRoute)
        val toPageType = getPageType(toRoute)
        
        return when {
            // 🏠 底部导航栏切换 - 使用专门的底部导航动画
            isBottomNavSwitch(fromRoute, toRoute) -> 
                BottomNavAnimationManager.getBottomNavAnimation(
                    fromRoute ?: "", 
                    toRoute, 
                    BottomNavAnimationManager.BottomNavAnimationStyle.WECHAT
                )
            
            // 🆕 创建页面 - 使用模态动画
            toPageType == PageType.CREATION_PAGE -> PageTransitions.MODAL_NAVIGATION
            
            // 📋 详情页面 - 使用模态动画
            toPageType == PageType.DETAIL_PAGE -> PageTransitions.MODAL_NAVIGATION
            
            // ⚙️ 设置等模态页面
            toPageType == PageType.MODAL_PAGE -> PageTransitions.MODAL_NAVIGATION
            
            // ⚡ 快速操作 - 使用轻量级动画
            toPageType == PageType.QUICK_ACTION -> PageTransitions.LIGHT_NAVIGATION
            
            // 🌟 特殊效果页面
            toPageType == PageType.SPECIAL_EFFECT -> PageTransitions.SPECIAL_NAVIGATION
            
            // 📱 主要页面间的导航
            else -> PageTransitions.MAIN_NAVIGATION
        }
    }
    
    /**
     * 根据路由判断页面类型
     */
    private fun getPageType(route: String?): PageType {
        return when {
            route == null -> PageType.MAIN_PAGE
            
            // 底部导航页面
            route in listOf(
                AppDestinations.UNIFIED_HOME_ROUTE,
                AppDestinations.HOME_ROUTE,
                AppDestinations.TASK_ROUTE,
                AppDestinations.CALENDAR_ROUTE,
                AppDestinations.ANALYTICS_ROUTE,
                AppDestinations.PROFILE_ROUTE
            ) -> PageType.BOTTOM_NAV
            
            // 创建页面
            route.contains("add") || route.contains("create") || route.contains("new") -> PageType.CREATION_PAGE
            
            // 详情页面
            route.contains("detail") || route.contains("view") -> PageType.DETAIL_PAGE
            
            // 编辑页面
            route.contains("edit") -> PageType.MODAL_PAGE
            
            // 设置页面
            route.contains("settings") || route.contains("config") -> PageType.MODAL_PAGE
            
            // AI相关页面
            route.contains("ai") -> PageType.SPECIAL_EFFECT
            
            // 其他快速操作
            route.contains("quick") || route.contains("fast") -> PageType.QUICK_ACTION
            
            // 默认为主要页面
            else -> PageType.MAIN_PAGE
        }
    }
    
    /**
     * 判断是否为底部导航栏切换
     */
    private fun isBottomNavSwitch(fromRoute: String?, toRoute: String): Boolean {
        val bottomNavRoutes = setOf(
            AppDestinations.UNIFIED_HOME_ROUTE,
            AppDestinations.HOME_ROUTE,
            AppDestinations.TASK_ROUTE,
            AppDestinations.CALENDAR_ROUTE,
            AppDestinations.ANALYTICS_ROUTE,
            AppDestinations.PROFILE_ROUTE
        )
        
        return fromRoute in bottomNavRoutes && toRoute in bottomNavRoutes
    }
    
    /**
     * 为特定路由获取预定义动画
     */
    fun getAnimationForRoute(route: String): AnimationSet {
        return when (route) {
            // 首页相关 - 轻量级动画
            AppDestinations.UNIFIED_HOME_ROUTE,
            AppDestinations.HOME_ROUTE -> PageTransitions.LIGHT_NAVIGATION
            
            // 任务相关 - 主要导航动画
            AppDestinations.TASK_LIST_ROUTE,
            AppDestinations.TASK_LIST_FULL_ROUTE -> PageTransitions.MAIN_NAVIGATION
            
            // 创建页面 - 模态动画
            AppDestinations.ADD_TASK_ROUTE -> PageTransitions.MODAL_NAVIGATION
            
            // 详情页面 - 模态动画
            AppDestinations.TASK_DETAIL_ROUTE -> PageTransitions.MODAL_NAVIGATION
            
            // 编辑页面 - 模态动画
            AppDestinations.TASK_EDIT_ROUTE -> PageTransitions.MODAL_NAVIGATION
            
            // 日历页面 - 主要导航动画
            AppDestinations.CALENDAR_ROUTE -> PageTransitions.MAIN_NAVIGATION
            
            // 时间追踪 - 特殊效果
            AppDestinations.TIME_TRACKING_ROUTE -> PageTransitions.SPECIAL_NAVIGATION
            
            // 分析页面 - 主要导航动画
            AppDestinations.ANALYTICS_ROUTE -> PageTransitions.MAIN_NAVIGATION
            
            // 设置相关 - 模态动画
            AppDestinations.SETTINGS_ROUTE,
            AppDestinations.THEME_SETTINGS_ROUTE -> PageTransitions.MODAL_NAVIGATION
            
            // AI相关 - 特殊效果
            AppDestinations.AI_ASSISTANT_ROUTE,
            AppDestinations.AI_SETTINGS_ROUTE -> PageTransitions.SPECIAL_NAVIGATION
            
            // 目标相关
            AppDestinations.GOAL_MANAGEMENT -> PageTransitions.MAIN_NAVIGATION
            AppDestinations.GOAL_DETAIL_ROUTE -> PageTransitions.MODAL_NAVIGATION
            AppDestinations.ADD_GOAL_ROUTE -> PageTransitions.MODAL_NAVIGATION
            
            // 健康相关
            AppDestinations.MENSTRUAL_CYCLE_ROUTE,
            AppDestinations.HABIT_TRACKER_ROUTE -> PageTransitions.MAIN_NAVIGATION
            
            // 默认使用主要导航动画
            else -> PageTransitions.MAIN_NAVIGATION
        }
    }
    
    /**
     * 为composable函数提供便捷的动画配置
     */
    fun configureComposable(
        route: String,
        fromRoute: String? = null
    ): AnimationSet {
        // 优先根据导航上下文选择动画
        return if (fromRoute != null) {
            getAnimationForNavigation(fromRoute, route)
        } else {
            // 回退到基于路由的静态配置
            getAnimationForRoute(route)
        }
    }
}

/**
 * 🎯 动画性能优化器
 * 根据设备性能和用户偏好调整动画
 */
object AnimationOptimizer {
    
    // 性能等级
    enum class PerformanceLevel {
        HIGH,    // 高性能设备 - 完整动画
        MEDIUM,  // 中等性能 - 简化动画
        LOW      // 低性能设备 - 最小动画
    }
    
    private var performanceLevel = PerformanceLevel.HIGH
    private var animationsEnabled = true
    
    /**
     * 设置性能等级
     */
    fun setPerformanceLevel(level: PerformanceLevel) {
        performanceLevel = level
    }
    
    /**
     * 启用/禁用动画
     */
    fun setAnimationsEnabled(enabled: Boolean) {
        animationsEnabled = enabled
    }
    
    /**
     * 根据性能等级优化动画
     */
    fun optimizeAnimation(animationSet: AnimationSet): AnimationSet {
        if (!animationsEnabled) {
            return PageTransitions.BOTTOM_NAV // 无动画
        }
        
        return when (performanceLevel) {
            PerformanceLevel.HIGH -> animationSet // 保持原始动画
            PerformanceLevel.MEDIUM -> PageTransitions.LIGHT_NAVIGATION // 轻量级动画
            PerformanceLevel.LOW -> PageTransitions.BOTTOM_NAV // 无动画
        }
    }
}

/**
 * 扩展函数：为NavGraphBuilder提供便捷的动画配置
 */
fun AnimationSet.toTransitions() = object {
    val enterTransition = <EMAIL>
    val exitTransition = <EMAIL>
    val popEnterTransition = <EMAIL>
    val popExitTransition = <EMAIL>
} 