package com.timeflow.app.receiver

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.timeflow.app.R
import com.timeflow.app.TimeFlowApplication
import com.timeflow.app.ui.MainActivity
import com.timeflow.app.utils.NotificationHelper
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * 习惯提醒广播接收器
 * 用于接收习惯提醒的闹钟广播并显示通知
 */
@AndroidEntryPoint
class HabitAlarmReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var notificationHelper: NotificationHelper
    
    companion object {
        private const val TAG = "HabitAlarmReceiver"
        const val EXTRA_HABIT_ID = "habit_id"
        const val EXTRA_HABIT_NAME = "habit_name"
        const val NOTIFICATION_ID_PREFIX = 3000 // 习惯通知的ID前缀
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到习惯提醒广播")
        
        // 获取习惯信息
        val habitId = intent.getStringExtra(EXTRA_HABIT_ID) ?: return
        val habitName = intent.getStringExtra(EXTRA_HABIT_NAME) ?: "您的习惯"
        
        // 创建打开应用的意图
        val notificationIntent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_HABIT", true)
            putExtra("HABIT_ID", habitId)
        }
        
        // 创建PendingIntent
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            habitId.hashCode(),
            notificationIntent,
            pendingIntentFlags
        )
        
        // 创建通知
        val notification = notificationHelper.createNotification(
            channelId = TimeFlowApplication.CHANNEL_HABIT_REMINDER,
            title = "习惯提醒",
            content = "现在是培养「$habitName」的好时机！",
            smallIcon = R.drawable.ic_launcher_foreground,
            contentIntent = pendingIntent,
            priority = android.app.NotificationManager.IMPORTANCE_HIGH,
            ongoing = false,
            autoCancel = true
        )
        
        // 显示通知
        val notificationId = NOTIFICATION_ID_PREFIX + habitId.hashCode()
        notificationHelper.notify(notificationId, notification)
    }
} 