package com.timeflow.app.data.repository

import android.util.Log
import com.timeflow.app.data.converter.TaskConverter
import com.timeflow.app.data.model.Task
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 事务帮助类
 * 用于处理任务相关的事务操作，确保外键约束得到正确处理
 */
class TransactionHelper(
    private val taskRepository: TaskRepository
) {
    companion object {
        private const val TAG = "TransactionHelper"
    }

    /**
     * 在事务中保存任务，确保外键约束得到正确处理
     * @param task 要保存的任务
     * @return 操作是否成功
     */
    suspend fun saveTaskWithForeignKeyCheck(task: Task): Bo<PERSON>an {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始保存任务: ${task.title}，ID: ${task.id}")
                
                // 验证外键参照完整性
                var isValid = true
                var errorMessage = ""
                
                // 检查父任务ID是否存在
                if (task.parentTaskId != null) {
                    val parentTask = taskRepository.getTaskById(task.parentTaskId)
                    if (parentTask == null) {
                        isValid = false
                        errorMessage = "引用了不存在的父任务ID: ${task.parentTaskId}"
                        Timber.e("父任务不存在: ${task.parentTaskId}")
                    }
                }
                
                if (!isValid) {
                    // 记录错误信息便于调试
                    Timber.e("任务保存失败，外键约束错误: $errorMessage")
                    Log.e(TAG, "任务保存失败，外键约束错误: $errorMessage")
                    return@withContext false
                }
                
                // 保存任务
                taskRepository.saveTask(task)
                Log.d(TAG, "任务保存成功: ${task.id}")
                return@withContext true
            } catch (e: Exception) {
                Timber.e(e, "保存任务时发生错误")
                return@withContext false
            }
        }
    }
    
    /**
     * 批量保存任务，确保先保存父任务再保存子任务
     * @param tasks 要保存的任务列表
     * @return 操作是否成功
     */
    suspend fun saveTasksInOrder(tasks: List<Task>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始批量保存${tasks.size}个任务")
                
                // 将任务分为两组：没有父任务的和有父任务的
                val tasksWithoutParent = tasks.filter { it.parentTaskId == null }
                val tasksWithParent = tasks.filter { it.parentTaskId != null }
                
                // 先保存没有父任务的任务
                for (task in tasksWithoutParent) {
                    val success = saveTaskWithForeignKeyCheck(task)
                    if (!success) {
                        Timber.e("保存无父任务失败: ${task.id}")
                        return@withContext false
                    }
                }
                
                // 然后保存有父任务的任务
                for (task in tasksWithParent) {
                    val success = saveTaskWithForeignKeyCheck(task)
                    if (!success) {
                        Timber.e("保存有父任务失败: ${task.id}")
                        return@withContext false
                    }
                }
                
                Log.d(TAG, "批量保存任务成功")
                return@withContext true
            } catch (e: Exception) {
                Timber.e(e, "批量保存任务时发生错误")
                return@withContext false
            }
        }
    }
    
    /**
     * 验证任务的外键引用
     * @param taskId 要验证的任务ID
     * @return 任务是否有效
     */
    suspend fun validateTaskReferences(taskId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val task = taskRepository.getTaskById(taskId) ?: return@withContext false
                
                // 检查父任务ID引用
                if (task.parentTaskId != null) {
                    val parentExists = taskRepository.getTaskById(task.parentTaskId) != null
                    if (!parentExists) {
                        Timber.w("任务 $taskId 引用了不存在的父任务: ${task.parentTaskId}")
                        return@withContext false
                    }
                }
                
                return@withContext true
            } catch (e: Exception) {
                Timber.e(e, "验证任务引用时发生错误")
                return@withContext false
            }
        }
    }
    
    /**
     * 修复任务外键引用
     * @param taskId 要修复的任务ID
     * @return 修复是否成功
     */
    suspend fun fixTaskReferences(taskId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val task = taskRepository.getTaskById(taskId) ?: return@withContext false
                var needsUpdate = false
                var updatedTask = task
                
                // 检查并修复父任务ID引用
                if (task.parentTaskId != null) {
                    val parentExists = taskRepository.getTaskById(task.parentTaskId) != null
                    if (!parentExists) {
                        updatedTask = updatedTask.copy(parentTaskId = null)
                        needsUpdate = true
                        Timber.w("修复任务 $taskId 的无效父任务引用")
                    }
                }
                
                // 保存修复后的任务
                if (needsUpdate) {
                    taskRepository.updateTask(updatedTask)
                    Timber.d("成功修复任务 $taskId 的引用")
                }
                
                return@withContext true
            } catch (e: Exception) {
                Timber.e(e, "修复任务引用时发生错误")
                return@withContext false
            }
        }
    }

    /**
     * 强制删除任务，处理所有外键约束
     * 当普通删除失败时使用此方法尝试清理关联并删除
     * 
     * @param taskId 要删除的任务ID
     * @return 操作是否成功
     */
    suspend fun forceDeleteTask(taskId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "尝试强制删除任务: $taskId")
                
                // 1. 获取要删除的任务
                val task = taskRepository.getTaskById(taskId)
                if (task == null) {
                    Log.w(TAG, "要删除的任务不存在: $taskId")
                    return@withContext true // 任务不存在则视为删除成功
                }
                
                // 2. 获取并删除所有子任务
                val subTasks = taskRepository.getSubTasks(taskId)
                if (subTasks.isNotEmpty()) {
                    Log.d(TAG, "删除${subTasks.size}个子任务")
                    subTasks.forEach { subTask ->
                        try {
                            // 递归使用强制删除确保子任务也能被正确删除
                            forceDeleteTask(subTask.id)
                        } catch (e: Exception) {
                            Log.e(TAG, "删除子任务失败: ${subTask.id}", e)
                            // 继续处理其他子任务，不要中断
                        }
                    }
                }
                
                // 3. 修复任务引用 - 将引用此任务的其他任务的parentTaskId设为null
                val referencingTasks = taskRepository.getTasksReferencingParent(taskId)
                if (referencingTasks.isNotEmpty()) {
                    Log.d(TAG, "修复${referencingTasks.size}个引用此任务作为父任务的任务")
                    referencingTasks.forEach { refTask ->
                        try {
                            val updatedTask = refTask.copy(parentTaskId = null)
                            taskRepository.updateTask(updatedTask)
                        } catch (e: Exception) {
                            Log.e(TAG, "修复引用任务失败: ${refTask.id}", e)
                            // 继续处理其他引用任务
                        }
                    }
                }
                
                // 4. 尝试直接删除任务
                try {
                    taskRepository.deleteTask(taskId)
                    Log.d(TAG, "成功强制删除任务: $taskId")
                    return@withContext true
                } catch (e: Exception) {
                    Log.e(TAG, "直接删除任务失败: $taskId", e)
                    
                    // 5. 如果直接删除失败，尝试通过SQL清理所有相关引用后删除
                    try {
                        // 实现低级别的数据库清理，移除所有关联引用
                        taskRepository.forceCleanupTaskReferences(taskId)
                        taskRepository.deleteTask(taskId)
                        Log.d(TAG, "通过强制清理引用成功删除任务: $taskId")
                        return@withContext true
                    } catch (e2: Exception) {
                        Log.e(TAG, "强制删除任务彻底失败: $taskId", e2)
                        return@withContext false
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "强制删除任务过程中出错: $taskId", e)
                return@withContext false
            }
        }
    }
} 