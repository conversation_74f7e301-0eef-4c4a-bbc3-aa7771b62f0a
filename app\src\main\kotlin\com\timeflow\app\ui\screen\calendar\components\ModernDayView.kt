package com.timeflow.app.ui.screen.calendar.components

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.toArgb
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import android.util.Log
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.task.components.common.event.AppEvent

// 紧凑日视图配色方案 - 参考图片设计，支持自定义颜色
private class CompactDayViewColors {
    companion object {
        val background = Color(0xFFFAFAFA)           // 浅灰背景
        val surface = Color.White                    // 白色表面
        val timeAxisText = Color(0xFF666666)         // 时间轴文字颜色
        val dividerColor = Color(0xFFE0E0E0)         // 虚线颜色
        
        // 待办区颜色（边框样式）
        val todoBackground = Color.Transparent       // 透明背景
        val todoText = Color(0xFF333333)            // 深灰文字
        val todoCheckColor = Color(0xFF4CAF50)      // 绿色勾
        
        // 默认的待办边框和完成背景颜色 - 使用莫兰迪紫
        val defaultEventColor = Color(0xFFC4B5D4)    // 莫兰迪紫（薰衣草色）
        
        // 🎨 莫兰迪色系预设颜色选项 - 温和优雅的色彩搭配
        val colorOptions = listOf(
            Color(0xFFC4B5D4),  // 莫兰迪薰衣草紫（默认）
            Color(0xFFBFAFB0),  // 莫兰迪灰粉
            Color(0xFFB8C5AA),  // 莫兰迪橄榄绿
            Color(0xFFD4C4B0),  // 莫兰迪米色
            Color(0xFFB5C4D4),  // 莫兰迪灰蓝
            Color(0xFFD4B5B8),  // 莫兰迪玫瑰粉
            Color(0xFFC4D4B5),  // 莫兰迪鼠尾草绿
            Color(0xFFB8B5D4),  // 莫兰迪紫灰
        )
    }
    
    // 动态获取待办边框颜色
    fun getTodoBorderColor(customColor: Color?): Color {
        return customColor ?: defaultEventColor
    }
    
    // 动态获取完成区颜色
    fun getCompletedColor(customColor: Color?): Color {
        return customColor ?: defaultEventColor
    }
}

// 日历事件数据类（简化版）
data class CompactCalendarEvent(
    val id: String,
    val title: String,
    val start: LocalDateTime,
    val end: LocalDateTime,
    val isCompleted: Boolean = false,
    val color: Long? = null,
    val customColor: Color? = null, // 添加自定义颜色字段
    val description: String = "",
    val status: String = "未完成",
    val taskId: String? = null // 🔧 新增：关联的任务ID
)

// 时间追踪事件数据类 - 用于显示实际用时
data class TimeTrackingEvent(
    val id: String,
    val taskName: String,
    val actualStartTime: LocalDateTime,
    val actualEndTime: LocalDateTime,
    val duration: Long, // 持续时间（秒）
    val taskId: String,
    val sessionId: String,
    val timerType: String, // NORMAL、POMODORO
    val focusRating: Int? = null,
    val productivityRating: Int? = null
) {
    // 计算实际用时的小时和分钟
    val durationHours: Int get() = (duration / 3600).toInt()
    val durationMinutes: Int get() = ((duration % 3600) / 60).toInt()

    // 格式化显示用时
    val formattedDuration: String get() {
        return when {
            durationHours > 0 -> "${durationHours}h${durationMinutes}m"
            durationMinutes > 0 -> "${durationMinutes}m"
            else -> "${duration}s"
        }
    }
}

// 保持向后兼容性的数据类
data class ModernCalendarEvent(
    val id: String,
    val title: String,
    val description: String = "",
    val start: LocalDateTime,
    val end: LocalDateTime? = null,
    val color: Long? = null,
    val urgency: com.timeflow.app.ui.screen.task.model.TaskUrgency = com.timeflow.app.ui.screen.task.model.TaskUrgency.MEDIUM,
    val taskId: String? = null,
    val isAllDay: Boolean = false
)

/**
 * 紧凑日视图 - 参考图片设计
 * 布局：左侧待办区 | 中间时间轴 | 右侧完成区
 * 每60分钟为一个时间块，背景带虚线
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModernDayView(
    selectedDate: LocalDate,
    events: List<ModernCalendarEvent> = emptyList(),
    timeTrackingEvents: List<TimeTrackingEvent> = emptyList(), // 🔧 新增：时间追踪事件
    modifier: Modifier = Modifier,
    viewModel: CalendarViewModel = hiltViewModel(),
    scrollToCurrentTime: Boolean = false,
    onAddEvent: (LocalDate, LocalTime) -> Unit = { _, _ -> },
    onCompleteTask: (String, Boolean) -> Unit = { _, _ -> }, // 🔧 新增：完成任务的回调 (taskId, isCompleted)
    onTimeTrackingEventClick: (TimeTrackingEvent) -> Unit = { _ -> } // 🔧 新增：时间追踪事件点击回调
) {
    val listState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    
    // 事件详情弹窗状态
    var showEventDialog by remember { mutableStateOf(false) }
    var selectedEvent by remember { mutableStateOf<CompactCalendarEvent?>(null) }

    // 🔧 新增：时间追踪事件详情弹窗状态
    var showTimeTrackingDialog by remember { mutableStateOf(false) }
    var selectedTimeTrackingEvent by remember { mutableStateOf<TimeTrackingEvent?>(null) }
    
    // 事件编辑弹窗状态
    var showEditDialog by remember { mutableStateOf(false) }
    var editingEvent by remember { mutableStateOf<CompactCalendarEvent?>(null) }
    
    // 保留向后兼容性的颜色状态
    var customEventColor by remember { mutableStateOf<Color?>(null) }
    
    // 获取事件数据并转换
    val calendarState by viewModel.calendarState.collectAsState()
    
    // 添加一个额外的状态来强制UI重新计算
    var refreshTrigger by remember { mutableStateOf(0) }
    
    // 添加监听EventBus事件的机制，确保实时同步
    LaunchedEffect(Unit) {
        // 监听EventBus事件，当任务发生变化时刷新日视图
        EventBus.events.collect { event ->
            when (event) {
                is AppEvent.TaskCreated,
                is AppEvent.TaskUpdated,
                is AppEvent.TaskDeleted,
                is AppEvent.TaskCompletionChanged,
                is AppEvent.TaskTimeChanged,
                is AppEvent.TaskTimeUpdated,
                is AppEvent.CalendarEventAdded,
                is AppEvent.CalendarEventUpdated,
                is AppEvent.CalendarEventDeleted,
                is AppEvent.DatabaseRefreshed -> {
                    Log.d("ModernDayView", "收到事件: ${event.javaClass.simpleName}，触发日视图刷新")
                    // 延迟一小段时间确保数据库操作完成
                    delay(100)
                    // 触发重新计算
                    refreshTrigger = (refreshTrigger + 1) % 1000
                    // 请求ViewModell重新加载事件
                    viewModel.loadEvents()
                }
            }
        }
    }
    
    val dayEvents = remember(selectedDate, calendarState.events, refreshTrigger) {
        Log.d("ModernDayView", "重新计算日视图事件，refreshTrigger=$refreshTrigger")
        viewModel.getEventsForDate(selectedDate).map { event ->
            Log.d("ModernDayView", "处理事件: ${event.title}, 颜色Long值: ${event.color}")
            
            // 安全地将Long颜色值转换为Color对象
            val eventCustomColor = event.color?.let { colorLong ->
                try {
                    // 安全的颜色转换：将Long转换为无符号Int，然后提取ARGB分量
                    val colorInt = (colorLong and 0xFFFFFFFFL).toInt()
                    Log.d("ModernDayView", "颜色Long值 ${colorLong} 转换为Int值 ${colorInt.toString(16)}")
                    
                    val a = ((colorInt ushr 24) and 0xFF).toFloat() / 255f
                    val r = ((colorInt ushr 16) and 0xFF).toFloat() / 255f
                    val g = ((colorInt ushr 8) and 0xFF).toFloat() / 255f
                    val b = (colorInt and 0xFF).toFloat() / 255f
                    
                    // 如果透明度太低（比如0），使用默认透明度
                    val safeAlpha = if (a < 0.01f) 1.0f else a
                    
                    val color = Color(r, g, b, safeAlpha)
                    Log.d("ModernDayView", "颜色转换成功: ${event.title} -> ARGB($safeAlpha, $r, $g, $b) = $color")
                    color
                } catch (e: Exception) {
                    Log.w("ModernDayView", "颜色转换失败: ${colorLong.toString(16)}, 使用默认颜色", e)
                    null // 转换失败时返回null，使用默认颜色
                }
            }
            
            CompactCalendarEvent(
                id = event.id,
                title = event.title,
                start = event.start,
                end = event.end ?: event.start.plusMinutes(15), // 改为15分钟
                isCompleted = event.isCompleted,
                color = event.color,
                customColor = eventCustomColor,
                description = event.title,
                status = if (event.isCompleted) "完成" else "未完成",
                taskId = event.taskId // 🔧 保留taskId信息
            )
        }
    }
    
    // 分离待办和已完成事件
    val todoEvents = dayEvents.filter { !it.isCompleted }
    val completedEvents = dayEvents.filter { it.isCompleted }
    
    // 滚动到当前时间
    LaunchedEffect(selectedDate, scrollToCurrentTime) {
        if (selectedDate == LocalDate.now() || scrollToCurrentTime) {
            val currentHour = LocalTime.now().hour
            scope.launch {
                listState.animateScrollToItem((currentHour - 2).coerceAtLeast(0))
            }
        }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = CompactDayViewColors.background
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize()
            ) {
                // 24小时，每小时一个时间块
                items(24) { hour ->
                    HourTimeBlock(
                        hour = hour,
                        todoEvents = todoEvents, // 传递所有待办事件，让HourTimeBlock自己过滤
                        completedEvents = completedEvents, // 传递所有完成事件，让HourTimeBlock自己过滤
                        timeTrackingEvents = timeTrackingEvents, // 🔧 新增：传递时间追踪事件
                        onAddTodo = { time ->
                            // 调用添加事件回调
                            onAddEvent(selectedDate, time)
                        },
                        onEventClick = { event ->
                            selectedEvent = event
                            showEventDialog = true
                        },
                        onTimeTrackingEventClick = { event ->
                            selectedTimeTrackingEvent = event
                            showTimeTrackingDialog = true
                        },
                        customColor = customEventColor
                    )
                }
                
                // 底部空白区域，提供更好的滚动体验
                item {
                    Spacer(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp) // 200dp的底部空白空间
                    )
                }
            }
        }
        

    }
    
    // 事件详情弹窗
    if (showEventDialog && selectedEvent != null) {
        EventDetailDialog(
            event = selectedEvent!!,
            onDismiss = { 
                showEventDialog = false
                selectedEvent = null
            },
            onEdit = { event ->
                // 打开编辑弹窗
                editingEvent = event
                showEditDialog = true
                showEventDialog = false
                selectedEvent = null
            },
            // 🔧 添加删除功能
            onDelete = { event ->
                viewModel.deleteEvent(event.id)
                showEventDialog = false
                selectedEvent = null
            },
            // 🔧 新增：完成任务功能
            onComplete = { event ->
                // 切换任务完成状态
                val newCompletedState = !event.isCompleted
                
                // 🔧 使用正确的任务ID - 优先使用taskId，fallback到event.id
                val actualTaskId = event.taskId ?: event.id
                Log.d("ModernDayView", "标记任务完成: taskId=$actualTaskId, newState=$newCompletedState")
                Log.d("ModernDayView", "  - event.id=${event.id}")
                Log.d("ModernDayView", "  - event.taskId=${event.taskId}")
                
                onCompleteTask(actualTaskId, newCompletedState)
                
                // 关闭弹窗
                showEventDialog = false
                selectedEvent = null
            }
        )
    }
    

    
    // 事件编辑弹窗
    if (showEditDialog && editingEvent != null) {
        EventEditDialog(
            event = editingEvent!!,
            onDismiss = { 
                showEditDialog = false
                editingEvent = null
            },
            onSave = { updatedEvent ->
                // 保存编辑后的事件，包括颜色
                val colorLong = updatedEvent.customColor?.let { color ->
                    try {
                        // 安全的颜色转换：使用toArgb()方法
                        val colorInt = color.toArgb()
                        colorInt.toLong() and 0xFFFFFFFFL
                    } catch (e: Exception) {
                        Log.w("ModernDayView", "颜色保存转换失败", e)
                        null
                    }
                }
                
                Log.d("ModernDayView", "保存事件颜色：${updatedEvent.customColor}, 转换后Long值：$colorLong")
                
                viewModel.updateEventDetails(
                    eventId = updatedEvent.id,
                    title = updatedEvent.title,
                    start = updatedEvent.start,
                    end = updatedEvent.end,
                    color = colorLong
                )
                
                // 关闭对话框
                showEditDialog = false
                editingEvent = null
                
                // 等待数据库更新完成后，强制刷新日历状态
                scope.launch {
                    delay(200) // 等待数据库操作完成
                    viewModel.refreshCalendarData() // 强制刷新日历数据
                    delay(1500) // 🔧 延长等待时间，避免用户看到时间跳变
                    refreshTrigger++ // 强制UI重新计算
                    Log.d("ModernDayView", "触发UI刷新，refreshTrigger=$refreshTrigger")
                }
            },
            onDelete = { event ->
                // 删除事件
                viewModel.deleteEvent(event.id)
                showEditDialog = false
                editingEvent = null
            }
        )
    }

    // 🔧 新增：时间追踪事件详情弹窗
    if (showTimeTrackingDialog && selectedTimeTrackingEvent != null) {
        TimeTrackingEventDetailDialog(
            event = selectedTimeTrackingEvent!!,
            onDismiss = {
                showTimeTrackingDialog = false
                selectedTimeTrackingEvent = null
            }
        )
    }
}



/**
 * 每小时的时间块（28dp高度）
 * 支持根据精确分钟数显示事件位置和宽度
 */
@Composable
private fun HourTimeBlock(
    hour: Int,
    todoEvents: List<CompactCalendarEvent>,
    completedEvents: List<CompactCalendarEvent>,
    timeTrackingEvents: List<TimeTrackingEvent>, // 🔧 新增：时间追踪事件
    onAddTodo: (LocalTime) -> Unit,
    onEventClick: (CompactCalendarEvent) -> Unit,
    onTimeTrackingEventClick: (TimeTrackingEvent) -> Unit, // 🔧 新增：时间追踪事件点击回调
    customColor: Color? = null
) {
    // 过滤在当前小时内的事件，并处理冲突
    val hourTodoEvents = todoEvents.filter { event ->
        val eventStartHour = event.start.hour
        val eventEndHour = event.end.hour

        // 🔧 修复：正确处理跨午夜事件（如23:00-00:00）和整点结束的事件
        val shouldShowInThisHour = if (event.end.minute == 0) {
            // 整点结束的事件：需要特别处理跨午夜情况
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件（如23:00-00:00）：在开始小时显示，但不在结束小时显示
                hour == eventStartHour
            } else {
                // 同日事件：只在开始小时到结束前一小时显示
                hour in eventStartHour until eventEndHour
            }
        } else {
            // 非整点结束的事件：正常范围显示，包括跨午夜情况
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件：在开始小时或结束小时显示
                hour == eventStartHour || hour <= eventEndHour
            } else {
                // 同日事件：正常范围显示
                eventStartHour == hour || (eventStartHour < hour && eventEndHour >= hour)
            }
        }

        shouldShowInThisHour
    }
    
    val hourCompletedEvents = completedEvents.filter { event ->
        val eventStartHour = event.start.hour
        val eventEndHour = event.end.hour

        // 🔧 修复：正确处理跨午夜事件（如23:00-00:00）和整点结束的事件
        val shouldShowInThisHour = if (event.end.minute == 0) {
            // 整点结束的事件：需要特别处理跨午夜情况
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件（如23:00-00:00）：在开始小时显示，但不在结束小时显示
                hour == eventStartHour
            } else {
                // 同日事件：只在开始小时到结束前一小时显示
                hour in eventStartHour until eventEndHour
            }
        } else {
            // 非整点结束的事件：正常范围显示，包括跨午夜情况
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件：在开始小时或结束小时显示
                hour == eventStartHour || hour <= eventEndHour
            } else {
                // 同日事件：正常范围显示
                eventStartHour == hour || (eventStartHour < hour && eventEndHour >= hour)
            }
        }

        shouldShowInThisHour
    }

    // 🔧 新增：过滤在当前小时内的时间追踪事件
    val hourTimeTrackingEvents = timeTrackingEvents.filter { event ->
        val eventStartHour = event.actualStartTime.hour
        val eventEndHour = event.actualEndTime.hour

        // 处理跨小时的时间追踪事件
        val shouldShowInThisHour = if (event.actualEndTime.minute == 0) {
            // 整点结束的事件：只在开始小时到结束前一小时显示
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件：在开始小时显示，但不在结束小时显示
                hour == eventStartHour
            } else {
                // 同日事件：只在开始小时到结束前一小时显示
                hour in eventStartHour until eventEndHour
            }
        } else {
            // 非整点结束的事件：正常范围显示，包括跨午夜情况
            if (eventStartHour > eventEndHour) {
                // 跨午夜事件：在开始小时或结束小时显示
                hour == eventStartHour || hour <= eventEndHour
            } else {
                // 同日事件：正常范围显示
                eventStartHour == hour || (eventStartHour < hour && eventEndHour >= hour)
            }
        }

        shouldShowInThisHour
    }

    // 冲突检测和处理：每个时间段只显示第一个事件，如果有冲突显示警告
    val primaryTodoEvent = hourTodoEvents.firstOrNull()
    val primaryCompletedEvent = hourCompletedEvents.firstOrNull()
    val primaryTimeTrackingEvent = hourTimeTrackingEvents.firstOrNull()
    val hasTimeConflict = hourTodoEvents.size > 1 || hourCompletedEvents.size > 1 || hourTimeTrackingEvents.size > 1
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(28.dp) // 每小时28dp高度
            .padding(horizontal = 4.dp)
            .drawBehind {
                // 绘制底部虚线
                val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f), 0f)
                drawLine(
                    color = CompactDayViewColors.dividerColor,
                    start = Offset(0f, size.height),
                    end = Offset(size.width, size.height),
                    pathEffect = pathEffect,
                    strokeWidth = 1.dp.toPx()
                )
                
                // 绘制垂直虚线（分隔左中右）
                val leftDivider = size.width * 0.4f
                val rightDivider = size.width * 0.6f
                
                drawLine(
                    color = CompactDayViewColors.dividerColor,
                    start = Offset(leftDivider, 0f),
                    end = Offset(leftDivider, size.height),
                    pathEffect = pathEffect,
                    strokeWidth = 1.dp.toPx()
                )
                
                drawLine(
                    color = CompactDayViewColors.dividerColor,
                    start = Offset(rightDivider, 0f),
                    end = Offset(rightDivider, size.height),
                    pathEffect = pathEffect,
                    strokeWidth = 1.dp.toPx()
                )
            }
    ) {
        // 左侧：待办区 (40% 宽度)
        TodoSection(
            modifier = Modifier.weight(0.4f),
            hour = hour,
            todoEvent = primaryTodoEvent, // 只传递主要事件
            hasConflict = hasTimeConflict && primaryTodoEvent != null,
            onAddTodo = onAddTodo,
            onEventClick = onEventClick,
            customColor = customColor
        )
        
        // 中间：时间轴 (20% 宽度)
        TimeAxisSection(
            modifier = Modifier.weight(0.2f),
            hour = hour
        )
        
        // 右侧：完成区 (40% 宽度)
        CompletedSection(
            modifier = Modifier.weight(0.4f),
            hour = hour,
            completedEvent = primaryCompletedEvent, // 只传递主要事件
            timeTrackingEvent = primaryTimeTrackingEvent, // 🔧 新增：传递时间追踪事件
            hasConflict = hasTimeConflict && (primaryCompletedEvent != null || primaryTimeTrackingEvent != null),
            onEventClick = onEventClick,
            onTimeTrackingEventClick = onTimeTrackingEventClick, // 🔧 新增：传递时间追踪事件点击回调
            customColor = customColor
        )
    }
}

/**
 * 待办区域 - 边框样式，只有完成时才显示勾
 * 单事件处理，冲突时显示警告
 */
@Composable
private fun TodoSection(
    modifier: Modifier,
    hour: Int,
    todoEvent: CompactCalendarEvent?,
    hasConflict: Boolean,
    onAddTodo: (LocalTime) -> Unit,
    onEventClick: (CompactCalendarEvent) -> Unit,
    customColor: Color? = null
) {
    Box(
        modifier = modifier
            .fillMaxHeight()
            .padding(end = 2.dp)
    ) {
        if (todoEvent != null) {
            // 显示单个待办事件
            TodoEventCard(
                event = todoEvent,
                hour = hour,
                hasConflict = hasConflict,
                onClick = { onEventClick(todoEvent) },
                customColor = customColor
            )
        } else {
            // 空白区域，点击添加待办
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable {
                        onAddTodo(LocalTime.of(hour, 0))
                    }
                    .background(
                        Color.Transparent,
                        RoundedCornerShape(4.dp)
                    ),
                contentAlignment = Alignment.CenterStart
            ) {
                // 可选：显示添加提示
            }
        }
    }
}

/**
 * 时间轴区域 - 显示小时数
 */
@Composable
private fun TimeAxisSection(
    modifier: Modifier,
    hour: Int
) {
    Box(
        modifier = modifier.fillMaxHeight(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = String.format("%02d", hour),
            fontSize = 8.sp, // 更小的字体适配28dp高度
            color = CompactDayViewColors.timeAxisText,
            fontWeight = FontWeight.Normal
        )
    }
}

/**
 * 完成区域 - 实心颜色块
 * 单事件处理，冲突时显示警告
 */
@Composable
private fun CompletedSection(
    modifier: Modifier,
    hour: Int,
    completedEvent: CompactCalendarEvent?,
    timeTrackingEvent: TimeTrackingEvent?, // 🔧 新增：时间追踪事件
    hasConflict: Boolean,
    onEventClick: (CompactCalendarEvent) -> Unit,
    onTimeTrackingEventClick: (TimeTrackingEvent) -> Unit, // 🔧 新增：时间追踪事件点击回调
    customColor: Color? = null
) {
    Box(
        modifier = modifier
            .fillMaxHeight()
            .padding(start = 2.dp)
    ) {
        when {
            // 优先显示时间追踪事件（实际用时）
            timeTrackingEvent != null -> {
                TimeTrackingEventCard(
                    event = timeTrackingEvent,
                    hour = hour,
                    hasConflict = hasConflict,
                    customColor = customColor,
                    onClick = { onTimeTrackingEventClick(timeTrackingEvent) } // 🔧 新增：点击回调
                )
            }
            // 其次显示已完成事件
            completedEvent != null -> {
                CompletedEventCard(
                    event = completedEvent,
                    hour = hour,
                    colorIndex = 0,
                    hasConflict = hasConflict,
                    onClick = { onEventClick(completedEvent) },
                    customColor = customColor
                )
            }
        }
    }
}

/**
 * 事件编辑弹窗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EventEditDialog(
    event: CompactCalendarEvent,
    onDismiss: () -> Unit,
    onSave: (CompactCalendarEvent) -> Unit,
    onDelete: (CompactCalendarEvent) -> Unit
) {
    var title by remember { mutableStateOf(event.title) }
    var startTime by remember { mutableStateOf(event.start.toLocalTime()) }
    var endTime by remember { mutableStateOf(event.end.toLocalTime()) }
    var isCompleted by remember { mutableStateOf(event.isCompleted) }
    var selectedEventColor by remember { mutableStateOf(event.customColor) }
    var showColorSelector by remember { mutableStateOf(false) }
    // 🔧 时间选择器状态
    var showStartTimePicker by remember { mutableStateOf(false) }
    var showEndTimePicker by remember { mutableStateOf(false) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "编辑事件",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 事件标题编辑
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("事件标题") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 🎨 美化的时间编辑区域
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "设置时间",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1A1A1A),
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 🎨 开始时间卡片
                        ModernTimeCard(
                            label = "开始时间",
                            time = startTime,
                            onClick = { showStartTimePicker = true },
                            modifier = Modifier.weight(1f)
                        )
                        
                        // 🎨 时间连接符
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    color = Color(0xFF6C63FF).copy(alpha = 0.15f), // 取消渐变，使用纯色
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "→",
                                fontSize = 16.sp,
                                color = Color(0xFF6C63FF),
                                fontWeight = FontWeight.Bold
                            )
                        }
                        
                        // 🎨 结束时间卡片
                        ModernTimeCard(
                            label = "结束时间",
                            time = endTime,
                            onClick = { showEndTimePicker = true },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 事件颜色选择 - 添加滑块选色功能
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "事件颜色",
                            fontSize = 14.sp,
                            color = Color.Black
                        )
                        
                        // 切换颜色选择模式的按钮
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            TextButton(
                                onClick = { showColorSelector = false },
                                colors = ButtonDefaults.textButtonColors(
                                    contentColor = if (!showColorSelector) Color(0xFFC4B5D4) else Color.Gray // 莫兰迪薰衣草紫
                                )
                            ) {
                                Text("预设", fontSize = 12.sp)
                            }
                            
                            TextButton(
                                onClick = { showColorSelector = true },
                                colors = ButtonDefaults.textButtonColors(
                                    contentColor = if (showColorSelector) Color(0xFFC4B5D4) else Color.Gray // 莫兰迪薰衣草紫
                                )
                            ) {
                                Text("滑块", fontSize = 12.sp)
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    if (showColorSelector) {
                        // 滑块选色器
                        ColorSliderSelector(
                            initialColor = selectedEventColor ?: CompactDayViewColors.defaultEventColor,
                            onColorChanged = { newColor ->
                                selectedEventColor = newColor
                            },
                            modifier = Modifier.fillMaxWidth()
                        )
                    } else {
                        // 预设颜色选择网格
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(6),
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            items(CompactDayViewColors.colorOptions) { color ->
                                EventColorOption(
                                    color = color,
                                    isSelected = selectedEventColor == color,
                                    onClick = { 
                                        selectedEventColor = color
                                        showColorSelector = false // 选择预设颜色后关闭滑块模式
                                    }
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 🔧 操作按钮（移除删除按钮，优化字体显示）
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp) // 增加按钮高度确保字体显示完整
                    ) {
                        Text(
                            text = "取消", 
                            color = Color.Gray,
                            fontSize = 16.sp // 增大字体确保显示完整
                        )
                    }
                    
                    // 保存按钮
                    Button(
                        onClick = {
                            val updatedEvent = event.copy(
                                title = title,
                                start = event.start.toLocalDate().atTime(startTime),
                                end = event.end.toLocalDate().atTime(endTime),
                                customColor = selectedEventColor
                            )
                            onSave(updatedEvent)
                        },
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                        ),
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp) // 增加按钮高度确保字体显示完整
                    ) {
                        Text(
                            text = "保存", 
                            color = Color.White,
                            fontSize = 16.sp // 增大字体确保显示完整
                        )
                    }
                }
            }
        }
    }
    
    // 🔧 开始时间选择器
    if (showStartTimePicker) {
        TimePickerDialog(
            title = "选择开始时间",
            initialTime = startTime,
            onTimeSelected = { selectedTime ->
                startTime = selectedTime
                // 如果结束时间早于开始时间，调整为开始时间+15分钟
                if (endTime.isBefore(startTime)) {
                    endTime = startTime.plusMinutes(15)
                }
                showStartTimePicker = false
            },
            onDismiss = { showStartTimePicker = false }
        )
    }
    
    // 🔧 结束时间选择器
    if (showEndTimePicker) {
        TimePickerDialog(
            title = "选择结束时间",
            initialTime = endTime,
            onTimeSelected = { selectedTime ->
                endTime = selectedTime
                // 自动调整开始时间（如果开始时间晚于结束时间）
                if (startTime.isAfter(endTime)) {
                    startTime = endTime.minusMinutes(15).let { 
                        if (it.isBefore(LocalTime.of(0, 0))) LocalTime.of(0, 0) else it 
                    }
                }
                showEndTimePicker = false
            },
            onDismiss = { showEndTimePicker = false }
        )
    }
} 

/**
 * 计算跨时间段事件应该在哪个小时显示主要标题
 * 选择停留时间最长的小时作为主要显示小时
 * 🔧 修复：正确处理跨午夜事件（如23:00-00:00）
 */
private fun getPrimaryDisplayHourForEvent(event: CompactCalendarEvent): Int {
    val startHour = event.start.hour
    val endHour = event.end.hour

    if (startHour == endHour) {
        // 单小时事件，直接返回该小时
        return startHour
    }

    // 🔧 修复：正确处理跨午夜事件
    val isCrossMidnightEvent = startHour > endHour

    // 计算每个小时的停留时间
    var maxDuration = 0
    var primaryHour = startHour

    if (isCrossMidnightEvent) {
        // 跨午夜事件：分别处理开始小时和结束小时

        // 开始小时的停留时间
        val startHourDuration = 60 - event.start.minute
        if (startHourDuration > maxDuration) {
            maxDuration = startHourDuration
            primaryHour = startHour
        }

        // 结束小时的停留时间（如果不是整点结束）
        if (event.end.minute > 0) {
            val endHourDuration = event.end.minute
            if (endHourDuration > maxDuration) {
                maxDuration = endHourDuration
                primaryHour = endHour
            }
        }
    } else {
        // 正常事件：使用原有逻辑
        for (hour in startHour..endHour) {
            val duration = when {
                hour == startHour && hour == endHour -> {
                    // 开始和结束在同一小时
                    event.end.minute - event.start.minute
                }
                hour == startHour -> {
                    // 开始小时
                    60 - event.start.minute
                }
                hour == endHour -> {
                    // 结束小时
                    event.end.minute
                }
                else -> {
                    // 中间完整小时
                    60
                }
            }

            if (duration > maxDuration) {
                maxDuration = duration
                primaryHour = hour
            }
        }
    }

    return primaryHour
}

/**
 * 待办事件卡片 - 边框样式，只有完成时才显示勾
 * 根据事件在当前小时内的精确分钟数计算位置和宽度
 * 🆕 支持跨时间段事件标题只显示一次的逻辑
 */
@Composable
private fun TodoEventCard(
    event: CompactCalendarEvent,
    hour: Int,
    hasConflict: Boolean = false,
    onClick: () -> Unit,
    customColor: Color? = null
) {
    // 计算在当前小时内的开始和结束分钟数
    val hourStart = LocalDateTime.of(event.start.toLocalDate(), LocalTime.of(hour, 0))
    val hourEnd = LocalDateTime.of(event.start.toLocalDate(), LocalTime.of(hour, 59, 59))
    
    // 确定事件在当前小时内的实际开始和结束时间
    val effectiveStart = if (event.start.isAfter(hourStart)) event.start else hourStart
    val effectiveEnd = if (event.end.isBefore(hourEnd)) event.end else hourEnd
    
    // 🔧 修复：正确处理跨午夜事件的分钟数计算
    val startMinuteInHour = if (event.start.hour == hour) event.start.minute else 0
    val endMinuteInHour = if (event.end.hour == hour) {
        event.end.minute
    } else if (event.start.hour > event.end.hour && hour == event.start.hour) {
        // 跨午夜事件在开始小时：填满到小时结束
        60
    } else {
        60
    }
    val durationInHour = endMinuteInHour - startMinuteInHour

    // 计算位置偏移（左边距）和宽度比例
    val startOffset = startMinuteInHour / 60f // 开始位置偏移比例

    // 🔧 修复跨时间段事件的宽度计算，包括跨午夜事件
    val isMultiHourEvent = event.start.hour != event.end.hour
    val isCrossMidnightEvent = event.start.hour > event.end.hour

    val widthFraction = if (isMultiHourEvent) {
        if (isCrossMidnightEvent && hour == event.start.hour) {
            // 跨午夜事件在开始小时：从开始位置填满到小时结束
            (60 - startMinuteInHour) / 60f
        } else if (event.end.hour > hour || (isCrossMidnightEvent && hour <= event.end.hour)) {
            // 跨小时事件：从开始位置填满到小时结束
            (60 - startMinuteInHour) / 60f
        } else {
            // 单小时事件：使用实际持续时间，但至少15%宽度
            (durationInHour / 60f).coerceAtLeast(0.15f)
        }
    } else {
        // 单小时事件：使用实际持续时间，但至少15%宽度
        (durationInHour / 60f).coerceAtLeast(0.15f)
    }
    
    // 🆕 判断是否应该在当前小时显示完整标题
    val primaryDisplayHour = getPrimaryDisplayHourForEvent(event)
    val shouldShowFullTitle = hour == primaryDisplayHour
    
    // 调试日志
    Log.d("ModernDayView", "待办事件: ${event.title} (小时 $hour)")
    Log.d("ModernDayView", "  - 原始时间: ${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}")
    Log.d("ModernDayView", "  - 小时内分钟: $startMinuteInHour-$endMinuteInHour")
    Log.d("ModernDayView", "  - 跨小时: ${event.end.hour > hour}")
    Log.d("ModernDayView", "  - 主要显示小时: $primaryDisplayHour")
    Log.d("ModernDayView", "  - 当前显示完整标题: $shouldShowFullTitle")
    Log.d("ModernDayView", "  - 位置偏移: ${(startOffset * 100).toInt()}%")
    Log.d("ModernDayView", "  - 宽度比例: ${(widthFraction * 100).toInt()}%")
    
    // 使用Row和weight来控制精确位置
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 左边的空白占位
        if (startOffset > 0) {
            Spacer(modifier = Modifier.weight(startOffset))
        }
        
        Surface(
            modifier = Modifier
                .weight(widthFraction)
                .height(26.dp) // 恢复原始高度
                .clickable { onClick() },
            color = if (hasConflict) {
                // 冲突时使用红色背景
                Color.Red.copy(alpha = 0.2f)
            } else {
                // 🆕 使用任务颜色作为背景，80%透明度
                val taskColor = event.customColor ?: customColor
                if (taskColor != null) {
                    taskColor.copy(alpha = 0.2f) // 80%透明度
                } else {
                    CompactDayViewColors.todoBackground
                }
            },
            shape = RoundedCornerShape(4.dp),
            border = BorderStroke(
                width = if (hasConflict) 2.dp else 1.dp, // 冲突时增加边框厚度
                color = if (hasConflict) Color.Red else (event.customColor ?: CompactDayViewColors().getTodoBorderColor(customColor))
            )
        ) {
            if (shouldShowFullTitle) {
                // 🆕 主要显示小时：显示完整标题和时间
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 只有完成时才显示勾
                    if (event.isCompleted) {
                        Text(
                            text = "✓",
                            fontSize = 8.sp,
                            color = CompactDayViewColors.todoCheckColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(end = 2.dp)
                        )
                    }
                    
                    Column {
                        Text(
                            text = if (hasConflict) "⚠️ ${event.title}" else event.title,
                            fontSize = 8.sp,
                            color = if (hasConflict) Color.Red else CompactDayViewColors.todoText,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        // 显示完整时间范围
                        Text(
                            text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}${if (hasConflict) " 有冲突" else ""}",
                            fontSize = 7.sp,
                            color = if (hasConflict) Color.Red.copy(alpha = 0.8f) else CompactDayViewColors.todoText.copy(alpha = 0.7f),
                            maxLines = 1
                        )
                    }
                }
            } else if (isMultiHourEvent) {
                // 🆕 非主要显示小时：只显示简化标识
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "···", // 省略号表示事件延续
                        fontSize = 10.sp,
                        color = CompactDayViewColors.todoText.copy(alpha = 0.6f),
                        fontWeight = FontWeight.Bold
                    )
                }
            } else {
                // 🆕 单小时事件：正常显示（向后兼容）
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (event.isCompleted) {
                        Text(
                            text = "✓",
                            fontSize = 8.sp,
                            color = CompactDayViewColors.todoCheckColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(end = 2.dp)
                        )
                    }
                    
                    Column {
                        Text(
                            text = if (hasConflict) "⚠️ ${event.title}" else event.title,
                            fontSize = 8.sp,
                            color = if (hasConflict) Color.Red else CompactDayViewColors.todoText,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Text(
                            text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}${if (hasConflict) " 有冲突" else ""}",
                            fontSize = 7.sp,
                            color = if (hasConflict) Color.Red.copy(alpha = 0.8f) else CompactDayViewColors.todoText.copy(alpha = 0.7f),
                            maxLines = 1
                        )
                    }
                }
            }
        }
        
        // 右边的空白占位
        val remainingWeight = 1.0f - startOffset - widthFraction
        if (remainingWeight > 0.01f) { // 避免负值或过小的权重
            Spacer(modifier = Modifier.weight(remainingWeight))
        }
    }
}

/**
 * 找到跨小时事件中持续时间最长的小时块
 */
private fun findLongestHourBlockForEvent(event: TimeTrackingEvent): Int {
    val startHour = event.actualStartTime.hour
    val endHour = event.actualEndTime.hour

    // 如果事件在同一小时内，直接返回该小时
    if (startHour == endHour) {
        return startHour
    }

    var longestHour = startHour
    var maxDuration = 0

    // 计算每个小时块的持续时间
    val currentHour = startHour
    var hour = currentHour

    while (true) {
        val hourStart = if (hour == startHour) {
            event.actualStartTime.minute
        } else {
            0
        }

        val hourEnd = if (hour == endHour) {
            event.actualEndTime.minute
        } else {
            60
        }

        val duration = hourEnd - hourStart

        if (duration > maxDuration) {
            maxDuration = duration
            longestHour = hour
        }

        // 处理跨午夜的情况
        if (hour == endHour) break
        hour = (hour + 1) % 24
        if (hour == startHour) break // 防止无限循环
    }

    return longestHour
}

/**
 * 时间追踪事件卡片 - 显示实际用时
 * 根据实际开始和结束时间计算位置和宽度
 * 支持跨时间段事件标题只显示一次的逻辑
 */
@Composable
private fun TimeTrackingEventCard(
    event: TimeTrackingEvent,
    hour: Int,
    hasConflict: Boolean = false,
    customColor: Color? = null,
    onClick: () -> Unit = {} // 🔧 新增：点击回调
) {
    // 计算在当前小时内的开始和结束时间
    val hourStart = LocalDateTime.of(event.actualStartTime.toLocalDate(), LocalTime.of(hour, 0))
    val hourEnd = LocalDateTime.of(event.actualStartTime.toLocalDate(), LocalTime.of(hour, 59, 59))

    // 确定事件在当前小时内的实际开始和结束时间
    val effectiveStart = if (event.actualStartTime.isAfter(hourStart)) event.actualStartTime else hourStart
    val effectiveEnd = if (event.actualEndTime.isBefore(hourEnd)) event.actualEndTime else hourEnd

    // 精确计算在当前小时内的分钟数
    val startMinuteInHour = effectiveStart.minute
    val endMinuteInHour = if (effectiveEnd.hour == hour) effectiveEnd.minute else 60
    val durationInHour = endMinuteInHour - startMinuteInHour

    // 计算位置偏移（左边距）和宽度比例
    val startOffset = startMinuteInHour / 60f
    val actualWidthFraction = durationInHour / 60f
    // 确保最小可见宽度，但保持真实比例
    val widthFraction = if (actualWidthFraction < 0.03f) 0.03f else actualWidthFraction

    // 判断事件块是否太短，不显示文字内容
    val isEventTooShort = durationInHour < 10 // 少于10分钟认为太短，不显示文字

    // 🔧 新增：判断是否应该显示标题
    // 对于跨小时的长时间事件（>60分钟），在最长的时间块中显示标题
    val shouldShowTitle = if (event.duration >= 3600) { // 60分钟 = 3600秒
        // 长时间事件：找到持续时间最长的小时块来显示标题
        val longestHourBlock = findLongestHourBlockForEvent(event)
        hour == longestHourBlock
    } else {
        // 短时间事件：正常显示
        !isEventTooShort
    }

    // 时间追踪事件的颜色 - 使用渐变蓝绿色系表示实际用时，区别于普通任务
    val trackingColor = customColor ?: when (event.timerType) {
        "POMODORO" -> Color(0xFF4CAF50) // 番茄钟：绿色
        "NORMAL" -> Color(0xFF26A69A)   // 普通计时：蓝绿色
        else -> Color(0xFF26A69A)
    }
    val borderColor = if (hasConflict) Color.Red else trackingColor

    // 根据专注度评分调整透明度
    val alpha = when (event.focusRating) {
        5 -> 0.9f  // 高专注度
        4 -> 0.8f
        3 -> 0.7f
        2 -> 0.6f
        1 -> 0.5f  // 低专注度
        else -> 0.8f // 默认
    }

    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // 左边空白占位
        if (startOffset > 0) {
            Spacer(modifier = Modifier.fillMaxWidth(startOffset))
        }

        // 时间追踪事件内容
        Box(
            modifier = Modifier
                .fillMaxWidth(widthFraction)
                .fillMaxHeight()
                .background(
                    color = trackingColor.copy(alpha = alpha),
                    shape = RoundedCornerShape(4.dp)
                )
                .border(
                    width = if (hasConflict) 2.dp else 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(4.dp)
                )
                .clickable { onClick() } // 🔧 新增：点击功能
                .then(
                    // 只有当事件块足够长时才添加内边距
                    if (!isEventTooShort) {
                        Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                    } else {
                        Modifier
                    }
                ),
            contentAlignment = if (isEventTooShort) Alignment.Center else Alignment.CenterStart
        ) {
            // 根据shouldShowTitle判断是否显示文字内容
            if (shouldShowTitle) {
                // 根据事件块长度和是否为跨小时事件决定显示内容
                when {
                    event.duration >= 3600 -> {
                        // 跨小时长事件：在最长的时间块中显示任务名称，不显示总用时
                        Text(
                            text = if (hasConflict) "⚠️ ${event.taskName}" else event.taskName,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 10.sp, // 稍大字体
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = Color.White,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                    durationInHour >= 20 -> {
                        // 长事件：显示任务名称
                        Text(
                            text = if (hasConflict) "⚠️ ${event.taskName}" else event.taskName,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color.White,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.align(Alignment.CenterStart)
                        )
                    }
                    durationInHour >= 15 -> {
                        // 中等事件：显示任务名称（稍小字体）
                        Text(
                            text = if (hasConflict) "⚠️ ${event.taskName}" else event.taskName,
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 8.sp,
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color.White,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.align(Alignment.CenterStart)
                        )
                    }
                    // 短事件（10-15分钟）：不显示任何文字内容，只用颜色块表示
                }
            }
        }

        // 右边空白占位
        val remainingSpace = 1.0f - startOffset - widthFraction
        if (remainingSpace > 0) {
            Spacer(modifier = Modifier.fillMaxWidth(remainingSpace))
        }
    }
}

/**
 * 时间追踪事件详情弹窗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimeTrackingEventDetailDialog(
    event: TimeTrackingEvent,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "时间追踪详情",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 任务名称
                DetailRow(
                    label = "任务名称",
                    value = event.taskName
                )

                // 计时类型
                DetailRow(
                    label = "计时类型",
                    value = when (event.timerType) {
                        "POMODORO" -> "🍅 番茄钟"
                        "NORMAL" -> "⏱️ 普通计时"
                        else -> event.timerType
                    }
                )

                // 开始时间
                DetailRow(
                    label = "开始时间",
                    value = event.actualStartTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                )

                // 结束时间
                DetailRow(
                    label = "结束时间",
                    value = event.actualEndTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                )

                // 用时
                DetailRow(
                    label = "用时",
                    value = event.formattedDuration,
                    valueColor = when (event.timerType) {
                        "POMODORO" -> Color(0xFF4CAF50)
                        else -> Color(0xFF26A69A)
                    }
                )

                // 专注度评分（如果有）
                event.focusRating?.let { rating ->
                    DetailRow(
                        label = "专注度",
                        value = "★".repeat(rating) + "☆".repeat(5 - rating) + " ($rating/5)"
                    )
                }

                // 生产力评分（如果有）
                event.productivityRating?.let { rating ->
                    DetailRow(
                        label = "生产力",
                        value = "★".repeat(rating) + "☆".repeat(5 - rating) + " ($rating/5)"
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 详情行组件
 */
@Composable
private fun DetailRow(
    label: String,
    value: String,
    valueColor: Color = MaterialTheme.colorScheme.onSurface
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = valueColor,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End
        )
    }
}

/**
 * 已完成事件卡片 - 实心颜色块
 * 根据事件在当前小时内的精确分钟数计算位置和宽度
 * 🆕 支持跨时间段事件标题只显示一次的逻辑
 */
@Composable
private fun CompletedEventCard(
    event: CompactCalendarEvent,
    hour: Int,
    colorIndex: Int,
    hasConflict: Boolean = false,
    onClick: () -> Unit,
    customColor: Color? = null
) {
    val backgroundColor = event.customColor ?: customColor ?: CompactDayViewColors().getCompletedColor(null)
    
    // 计算在当前小时内的开始和结束分钟数
    val hourStart = LocalDateTime.of(event.start.toLocalDate(), LocalTime.of(hour, 0))
    val hourEnd = LocalDateTime.of(event.start.toLocalDate(), LocalTime.of(hour, 59, 59))
    
    // 确定事件在当前小时内的实际开始和结束时间
    val effectiveStart = if (event.start.isAfter(hourStart)) event.start else hourStart
    val effectiveEnd = if (event.end.isBefore(hourEnd)) event.end else hourEnd
    
    // 🔧 修复：正确处理跨午夜事件的分钟数计算
    val startMinuteInHour = if (event.start.hour == hour) event.start.minute else 0
    val endMinuteInHour = if (event.end.hour == hour) {
        event.end.minute
    } else if (event.start.hour > event.end.hour && hour == event.start.hour) {
        // 跨午夜事件在开始小时：填满到小时结束
        60
    } else {
        60
    }
    val durationInHour = endMinuteInHour - startMinuteInHour

    // 计算位置偏移（左边距）和宽度比例
    val startOffset = startMinuteInHour / 60f // 开始位置偏移比例

    // 🔧 修复跨时间段事件的宽度计算，包括跨午夜事件
    val isMultiHourEvent = event.start.hour != event.end.hour
    val isCrossMidnightEvent = event.start.hour > event.end.hour

    val widthFraction = if (isMultiHourEvent) {
        if (isCrossMidnightEvent && hour == event.start.hour) {
            // 跨午夜事件在开始小时：从开始位置填满到小时结束
            (60 - startMinuteInHour) / 60f
        } else if (event.end.hour > hour || (isCrossMidnightEvent && hour <= event.end.hour)) {
            // 跨小时事件：从开始位置填满到小时结束
            (60 - startMinuteInHour) / 60f
        } else {
            // 单小时事件：使用实际持续时间，但至少15%宽度
            (durationInHour / 60f).coerceAtLeast(0.15f)
        }
    } else {
        // 单小时事件：使用实际持续时间，但至少15%宽度
        (durationInHour / 60f).coerceAtLeast(0.15f)
    }
    
    // 🆕 判断是否应该在当前小时显示完整标题
    val primaryDisplayHour = getPrimaryDisplayHourForEvent(event)
    val shouldShowFullTitle = hour == primaryDisplayHour
    
    // 调试日志
    Log.d("ModernDayView", "完成事件: ${event.title} (小时 $hour)")
    Log.d("ModernDayView", "  - 原始时间: ${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}")
    Log.d("ModernDayView", "  - 小时内分钟: $startMinuteInHour-$endMinuteInHour")
    Log.d("ModernDayView", "  - 跨小时: ${event.end.hour > hour}")
    Log.d("ModernDayView", "  - 主要显示小时: $primaryDisplayHour")
    Log.d("ModernDayView", "  - 当前显示完整标题: $shouldShowFullTitle")
    Log.d("ModernDayView", "  - 位置偏移: ${(startOffset * 100).toInt()}%")
    Log.d("ModernDayView", "  - 宽度比例: ${(widthFraction * 100).toInt()}%")
    
    // 使用Row和weight来控制精确位置
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 左边的空白占位
        if (startOffset > 0) {
            Spacer(modifier = Modifier.weight(startOffset))
        }
        
        Surface(
            modifier = Modifier
                .weight(widthFraction)
                .height(26.dp) // 恢复原始高度
                .clickable { onClick() },
            color = if (hasConflict) backgroundColor.copy(alpha = 0.8f) else backgroundColor,
            shape = RoundedCornerShape(4.dp),
            border = if (hasConflict) BorderStroke(2.dp, Color.Red) else null
        ) {
            if (shouldShowFullTitle) {
                // 🆕 主要显示小时：显示完整标题和时间
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Column {
                        Text(
                            text = if (hasConflict) "⚠️ ${event.title}" else event.title,
                            fontSize = 9.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        // 显示完整时间范围
                        Text(
                            text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}${if (hasConflict) " 有冲突" else ""}",
                            fontSize = 8.sp,
                            color = Color.White.copy(alpha = 0.8f),
                            maxLines = 1
                        )
                    }
                }
            } else if (isMultiHourEvent) {
                // 🆕 非主要显示小时：只显示简化标识
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "···", // 省略号表示事件延续
                        fontSize = 12.sp,
                        color = Color.White.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Bold
                    )
                }
            } else {
                // 🆕 单小时事件：正常显示（向后兼容）
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Column {
                        Text(
                            text = if (hasConflict) "⚠️ ${event.title}" else event.title,
                            fontSize = 9.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Text(
                            text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}${if (hasConflict) " 有冲突" else ""}",
                            fontSize = 8.sp,
                            color = Color.White.copy(alpha = 0.8f),
                            maxLines = 1
                        )
                    }
                }
            }
        }
        
        // 右边的空白占位
        val remainingWeight = 1.0f - startOffset - widthFraction
        if (remainingWeight > 0.01f) { // 避免负值或过小的权重
            Spacer(modifier = Modifier.weight(remainingWeight))
        }
    }
}

/**
 * 事件详情弹窗 - 参考图3、4的设计
 */
@Composable
private fun EventDetailDialog(
    event: CompactCalendarEvent,
    onDismiss: () -> Unit,
    onEdit: (CompactCalendarEvent) -> Unit,
    onDelete: (CompactCalendarEvent) -> Unit = {}, // 🔧 删除回调
    onComplete: (CompactCalendarEvent) -> Unit = {} // 🔧 新增：完成任务回调
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = event.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 时间信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "时间：",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${event.end.format(DateTimeFormatter.ofPattern("HH:mm"))}",
                        fontSize = 12.sp,
                        color = Color.Black
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 状态信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "状态：",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    
                    Surface(
                        color = if (event.isCompleted) Color(0xFF5d733a).copy(alpha = 0.1f) else Color(0xFFFF9800).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = event.status,
                            fontSize = 11.sp,
                            color = if (event.isCompleted) Color(0xFF5d733a) else Color(0xFFFF9800),
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 操作按钮 - 🔧 紧凑型两行布局
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                                        // 第一行：主要操作按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 🔧 左侧空白区域，让按钮居中显示
                        Spacer(modifier = Modifier.weight(0.2f))
                        
                        // 🔧 完成/取消完成按钮 - 紧凑型
                        Button(
                            onClick = { onComplete(event) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (event.isCompleted) Color(0xFFFF9800) else Color(0xFFBEC6A0) // 🔧 新的完成按钮颜色
                            ),
                            modifier = Modifier
                                .weight(0.8f) // 🔧 缩小按钮宽度
                                .height(36.dp), // 🔧 减小按钮高度
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 6.dp) // 🔧 进一步减小内边距
                        ) {
                            Text(
                                text = if (event.isCompleted) "取消完成" else "完成", // 🔧 简化文字
                                color = Color.White,
                                fontSize = 12.sp, // 🔧 减小字体
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        // 编辑按钮 - 紧凑型
                        Button(
                            onClick = { onEdit(event) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                            ),
                            modifier = Modifier
                                .weight(0.8f) // 🔧 统一缩小按钮宽度
                                .height(36.dp), // 🔧 减小按钮高度
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 6.dp) // 🔧 统一减小内边距
                        ) {
                            Text("编辑", color = Color.White, fontSize = 12.sp, fontWeight = FontWeight.Medium)
                        }
                        
                        // 🔧 右侧空白区域，让按钮居中显示
                        Spacer(modifier = Modifier.weight(0.2f))
                    }
                    
                                        // 第二行：次要操作按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 🔧 左侧空白区域，让按钮居中显示
                        Spacer(modifier = Modifier.weight(0.2f))
                        
                        // 删除按钮 - 紧凑型
                        Button(
                            onClick = { onDelete(event) },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFE57373)
                            ),
                            modifier = Modifier
                                .weight(0.8f) // 🔧 统一缩小按钮宽度
                                .height(36.dp), // 🔧 减小按钮高度
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 6.dp) // 🔧 统一减小内边距
                        ) {
                            Text("删除", color = Color.White, fontSize = 12.sp, fontWeight = FontWeight.Medium)
                        }
                        
                        // 取消按钮 - 紧凑型
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier
                                .weight(0.8f) // 🔧 统一缩小按钮宽度
                                .height(36.dp), // 🔧 减小按钮高度
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 6.dp), // 🔧 统一减小内边距
                            border = BorderStroke(1.dp, Color.Gray.copy(alpha = 0.3f))
                        ) {
                            Text("取消", color = Color.Gray, fontSize = 12.sp, fontWeight = FontWeight.Medium)
                        }
                        
                        // 🔧 右侧空白区域，让按钮居中显示
                        Spacer(modifier = Modifier.weight(0.2f))
                    }
                }
            }
        }
    }
}

/**
 * 🎨 现代化时间卡片组件
 */
@Composable
private fun ModernTimeCard(
    label: String,
    time: LocalTime,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .clickable { onClick() }
            .height(80.dp),
        color = Color.Transparent,
        shape = RoundedCornerShape(16.dp)
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .clickable { onClick() },
            colors = CardDefaults.cardColors(
                containerColor = Color.Transparent
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        color = Color(0xFFF8F9FF),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .border(
                        width = 1.dp,
                        color = Color(0xFF6C63FF).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(12.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 🔧 直接显示时间，去掉图标
                    Text(
                        text = time.format(DateTimeFormatter.ofPattern("HH:mm")),
                        fontSize = 16.sp, // 缩小时间字体
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A1A1A)
                    )
                    
                    Spacer(modifier = Modifier.height(6.dp))
                    
                    // 标签
                    Text(
                        text = label,
                        fontSize = 12.sp, // 稍微增大标签字体
                        color = Color(0xFF6C63FF).copy(alpha = 0.7f),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // 点击提示圆点
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(6.dp)
                        .background(
                            Color(0xFF6C63FF).copy(alpha = 0.3f),
                            CircleShape
                        )
                )
            }
        }
    }
}

/**
 * 🎨 现代化时间选择器弹窗
 */
@Composable
private fun TimePickerDialog(
    title: String,
    initialTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedHour by remember { mutableIntStateOf(initialTime.hour) }
    var selectedMinute by remember { mutableIntStateOf(initialTime.minute) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(24.dp),
            color = Color.White,
            shadowElevation = 20.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 🎨 美化标题区域
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 时钟图标
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        Color(0xFF6C63FF).copy(alpha = 0.1f),
                                        Color(0xFF6C63FF).copy(alpha = 0.05f)
                                    )
                                ),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "🕐",
                            fontSize = 24.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = title,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A1A1A)
                    )
                    
                    Text(
                        text = "选择您需要的时间",
                        fontSize = 14.sp,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // 🎨 美化时间选择器
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color(0xFFF8F9FF),
                            RoundedCornerShape(20.dp)
                        )
                        .padding(20.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 🎨 小时选择器
                        ModernTimeSelector(
                            label = "小时",
                            value = selectedHour,
                            range = 0..23,
                            onValueChange = { selectedHour = it },
                            formatter = { String.format("%02d", it) }
                        )
                        
                        // 🎨 分隔符
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Spacer(modifier = Modifier.height(40.dp))
                            Text(
                                text = ":",
                                fontSize = 32.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF6C63FF)
                            )
                        }
                        
                        // 🎨 分钟选择器
                        ModernTimeSelector(
                            label = "分钟",
                            value = selectedMinute,
                            range = 0..59,
                            step = 15,
                            onValueChange = { selectedMinute = it },
                            formatter = { String.format("%02d", it) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // 🎨 美化操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        shape = RoundedCornerShape(16.dp),
                        border = BorderStroke(1.5.dp, Color(0xFFE5E7EB)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF6B7280)
                        )
                    ) {
                        Text(
                            "取消",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    // 确定按钮
                    Button(
                        onClick = {
                            onTimeSelected(LocalTime.of(selectedHour, selectedMinute))
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(52.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF6C63FF)
                        )
                    ) {
                        Text(
                            "确定",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 🎨 现代化时间选择器组件
 */
@Composable
private fun ModernTimeSelector(
    label: String,
    value: Int,
    range: IntRange,
    step: Int = 1,
    onValueChange: (Int) -> Unit,
    formatter: (Int) -> String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标签
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF6C63FF),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        // 选择器容器
        Surface(
            modifier = Modifier.size(100.dp),
            color = Color.White,
            shape = RoundedCornerShape(20.dp),
            shadowElevation = 4.dp
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // 增加按钮
                IconButton(
                    onClick = { 
                        val newValue = if (step == 1) {
                            (value + 1).let { if (it > range.last) range.first else it }
                        } else {
                            (value + step).let { if (it > range.last) range.first else it }
                        }
                        onValueChange(newValue)
                    },
                    modifier = Modifier.size(32.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .background(
                                Color(0xFF6C63FF).copy(alpha = 0.1f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowUp,
                            contentDescription = "增加$label",
                            tint = Color(0xFF6C63FF),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 数值显示
                Text(
                    text = formatter(value),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A1A1A)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 减少按钮
                IconButton(
                    onClick = { 
                        val newValue = if (step == 1) {
                            (value - 1).let { if (it < range.first) range.last else it }
                        } else {
                            (value - step).let { if (it < range.first) {
                                // 对于分钟，找到最接近的步长值
                                val lastStepValue = (range.last / step) * step
                                lastStepValue
                            } else it }
                        }
                        onValueChange(newValue)
                    },
                    modifier = Modifier.size(32.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .background(
                                Color(0xFF6C63FF).copy(alpha = 0.1f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = "减少$label",
                            tint = Color(0xFF6C63FF),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 颜色选择器弹窗
 */
@Composable
private fun ColorPickerDialog(
    currentColor: Color?,
    onDismiss: () -> Unit,
    onColorSelected: (Color) -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "选择卡片颜色",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "选择的颜色将同时应用于待办卡片边框和完成卡片背景",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 颜色选择网格
                LazyVerticalGrid(
                    columns = GridCells.Fixed(4),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(CompactDayViewColors.colorOptions) { color ->
                        ColorOption(
                            color = color,
                            isSelected = currentColor == color,
                            onClick = { onColorSelected(color) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 关闭按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("关闭", color = Color.Gray)
                    }
                }
            }
        }
    }
}

/**
 * 颜色选项组件
 */
@Composable
private fun ColorOption(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(CircleShape)
            .background(color)
            .border(
                width = if (isSelected) 3.dp else 1.dp,
                color = if (isSelected) Color.Black else Color.Gray.copy(alpha = 0.3f),
                shape = CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * 单个颜色滑块行
 */
@Composable
private fun ColorSliderRow(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit,
    gradientColors: List<Color>
) {
    Column {
        // 标签
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Black,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        
        // 渐变背景滑块
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(24.dp)
                .background(
                    brush = Brush.horizontalGradient(gradientColors),
                    shape = RoundedCornerShape(12.dp)
                )
        ) {
            Slider(
                value = value,
                onValueChange = onValueChange,
                valueRange = valueRange,
                modifier = Modifier.fillMaxSize(),
                colors = SliderDefaults.colors(
                    thumbColor = Color.White,
                    activeTrackColor = Color.Transparent,
                    inactiveTrackColor = Color.Transparent
                )
            )
        }
    }
}

/**
 * 滑块颜色选择器 - 参考图片设计
 */
@Composable
private fun ColorSliderSelector(
    initialColor: Color,
    onColorChanged: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    // 提取颜色分量
    var red by remember { mutableFloatStateOf(initialColor.red * 255f) }
    var green by remember { mutableFloatStateOf(initialColor.green * 255f) }
    var blue by remember { mutableFloatStateOf(initialColor.blue * 255f) }
    var alpha by remember { mutableFloatStateOf(initialColor.alpha) }
    
    // 当前颜色（固定透明度为1.0）
    val currentColor = Color(
        red = red / 255f,
        green = green / 255f,
        blue = blue / 255f,
        alpha = 1.0f // 🔧 固定透明度为100%
    )
    
    // 实时更新颜色（移除alpha依赖）
    LaunchedEffect(red, green, blue) {
        onColorChanged(currentColor)
    }
    
    Column(
        modifier = modifier.padding(vertical = 8.dp)
    ) {
        // 颜色预览和十六进制值
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 颜色预览块
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(currentColor, RoundedCornerShape(8.dp))
                    .border(
                        1.dp, 
                        Color.Gray.copy(alpha = 0.3f), 
                        RoundedCornerShape(8.dp)
                    )
            )
            
            // 十六进制颜色值（移除透明度显示）
            Text(
                text = String.format("#%02X%02X%02X", 
                    red.toInt(),
                    green.toInt(),
                    blue.toInt()
                ),
                fontSize = 14.sp,
                color = Color.Gray,
                fontWeight = FontWeight.Medium
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // R 滑块
        ColorSliderRow(
            label = "R: ${red.toInt()}",
            value = red,
            valueRange = 0f..255f,
            onValueChange = { red = it },
            gradientColors = listOf(
                Color(0, green.toInt(), blue.toInt()),
                Color(255, green.toInt(), blue.toInt())
            )
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // G 滑块
        ColorSliderRow(
            label = "G: ${green.toInt()}",
            value = green,
            valueRange = 0f..255f,
            onValueChange = { green = it },
            gradientColors = listOf(
                Color(red.toInt(), 0, blue.toInt()),
                Color(red.toInt(), 255, blue.toInt())
            )
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // B 滑块
        ColorSliderRow(
            label = "B: ${blue.toInt()}",
            value = blue,
            valueRange = 0f..255f,
            onValueChange = { blue = it },
            gradientColors = listOf(
                Color(red.toInt(), green.toInt(), 0),
                Color(red.toInt(), green.toInt(), 255)
            )
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 🔧 删除透明度滑块（A滑块）
    }
}

/**
 * 事件颜色选择组件（更小的尺寸）
 */
@Composable
private fun EventColorOption(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(color)
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color.Black else Color.Gray.copy(alpha = 0.3f),
                shape = CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
} 