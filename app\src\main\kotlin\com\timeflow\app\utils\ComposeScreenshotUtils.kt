package com.timeflow.app.utils

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Picture
import android.os.Build
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.drawscope.draw
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 🎨 Compose UI截图工具类
 * 用于将Compose组件转换为Bitmap图片
 */
object ComposeScreenshotUtils {
    
    /**
     * 截取Compose组件为Bitmap（备用方法）
     * @param context 上下文
     * @param width 截图宽度
     * @param height 截图高度
     * @param content Compose内容
     * @return 截图Bitmap
     */
    suspend fun captureComposable(
        context: Context,
        width: Int,
        height: Int,
        content: @Composable () -> Unit
    ): Bitmap = withContext(Dispatchers.Main) {
        suspendCoroutine { continuation ->
            try {
                val activity = context as? Activity
                    ?: throw IllegalArgumentException("Context must be an Activity")
                
                val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
                val composeView = ComposeView(context)
                val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                val canvas = android.graphics.Canvas(bitmap)
                
                composeView.setContent {
                    Box(
                        modifier = Modifier.size(
                            width = with(LocalDensity.current) { width.toDp() },
                            height = with(LocalDensity.current) { height.toDp() }
                        )
                    ) {
                        content()
                    }
                }
                
                // 临时添加到视图层次结构中
                rootView.addView(composeView)
                
                // 强制布局和绘制
                composeView.measure(
                    android.view.View.MeasureSpec.makeMeasureSpec(width, android.view.View.MeasureSpec.EXACTLY),
                    android.view.View.MeasureSpec.makeMeasureSpec(height, android.view.View.MeasureSpec.EXACTLY)
                )
                composeView.layout(0, 0, width, height)
                
                // 等待布局完成并进行多次重试
                composeView.post {
                    // 再次确保布局完成
                    composeView.post {
                        try {
                            // 强制重新布局
                            composeView.invalidate()
                            composeView.requestLayout()
                            
                            // 等待绘制完成
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                try {
                                    canvas.drawColor(android.graphics.Color.WHITE)
                                    composeView.draw(canvas)
                                    rootView.removeView(composeView)
                                    continuation.resume(bitmap)
                                } catch (e: Exception) {
                                    try {
                                        rootView.removeView(composeView)
                                    } catch (ignored: Exception) {}
                                    continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
                                }
                            }, 100) // 等待100ms确保渲染完成
                        } catch (e: Exception) {
                            try {
                                rootView.removeView(composeView)
                            } catch (ignored: Exception) {}
                            continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
                        }
                    }
                }
            } catch (e: Exception) {
                continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
            }
        }
    }
    
    /**
     * 使用Picture API截取Compose组件（推荐方式）
     * @param context 上下文
     * @param width 截图宽度
     * @param height 截图高度
     * @param content Compose内容
     * @return 截图Bitmap
     */
    suspend fun captureComposableWithPicture(
        context: Context,
        width: Int,
        height: Int,
        content: @Composable () -> Unit
    ): Bitmap = withContext(Dispatchers.Main) {
        suspendCoroutine { continuation ->
            try {
                val activity = context as? android.app.Activity
                    ?: throw IllegalArgumentException("Context must be an Activity")
                
                val rootView = activity.findViewById<android.view.ViewGroup>(android.R.id.content)
                
                // 创建容器和ComposeView
                val container = android.widget.FrameLayout(context)
                val composeView = ComposeView(context)
                
                composeView.setContent {
                    Box(
                        modifier = Modifier.size(
                            width = with(LocalDensity.current) { width.toDp() },
                            height = with(LocalDensity.current) { height.toDp() }
                        )
                    ) {
                        content()
                    }
                }
                
                // 将ComposeView添加到容器中
                container.addView(composeView)
                
                // 临时将容器添加到Activity的根视图中
                rootView.addView(container)
                
                // 强制布局和绘制
                container.measure(
                    android.view.View.MeasureSpec.makeMeasureSpec(width, android.view.View.MeasureSpec.EXACTLY),
                    android.view.View.MeasureSpec.makeMeasureSpec(height, android.view.View.MeasureSpec.EXACTLY)
                )
                container.layout(0, 0, width, height)
                
                // 等待Compose内容准备就绪
                composeView.post {
                    // 再次确保布局完成
                    composeView.post {
                        try {
                            // 强制重新布局
                            composeView.invalidate()
                            composeView.requestLayout()
                            
                            // 等待绘制完成
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                try {
                                    // 创建bitmap并绘制
                                    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                                    val canvas = android.graphics.Canvas(bitmap)
                                    
                                    // 绘制白色背景
                                    canvas.drawColor(android.graphics.Color.WHITE)
                                    
                                    // 绘制ComposeView内容
                                    composeView.draw(canvas)
                                    
                                    // 从视图层次结构中移除容器
                                    rootView.removeView(container)
                                    
                                    continuation.resume(bitmap)
                                } catch (e: Exception) {
                                    // 确保清理视图
                                    try {
                                        rootView.removeView(container)
                                    } catch (ignored: Exception) {}
                                    continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
                                }
                            }, 100) // 等待100ms确保渲染完成
                        } catch (e: Exception) {
                            // 确保清理视图
                            try {
                                rootView.removeView(container)
                            } catch (ignored: Exception) {}
                            continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
                        }
                    }
                }
            } catch (e: Exception) {
                continuation.resume(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))
            }
        }
    }
    
    /**
     * 创建一个可截图的Composable包装器
     * @param modifier 修饰符
     * @param onCapture 截图完成回调
     * @param content 内容
     */
    @Composable
    fun CaptureableComposable(
        modifier: Modifier = Modifier,
        onCapture: ((Bitmap) -> Unit)? = null,
        content: @Composable () -> Unit
    ) {
        val picture = remember { Picture() }
        
        Box(
            modifier = modifier.drawWithCache {
                val width = this.size.width.toInt()
                val height = this.size.height.toInt()
                onDrawWithContent {
                    val pictureCanvas = androidx.compose.ui.graphics.Canvas(
                        picture.beginRecording(width, height)
                    )
                    
                    draw(this, this.layoutDirection, pictureCanvas, this.size) {
                        <EMAIL>()
                    }
                    picture.endRecording()
                    
                    drawIntoCanvas { canvas ->
                        canvas.nativeCanvas.drawPicture(picture)
                    }
                    
                    // 如果需要，创建Bitmap并调用回调
                    onCapture?.let { callback ->
                        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                        val bitmapCanvas = android.graphics.Canvas(bitmap)
                        bitmapCanvas.drawPicture(picture)
                        callback(bitmap)
                    }
                }
            }
        ) {
            content()
        }
    }
    
    /**
     * 获取适合分享的图片尺寸
     * @param context 上下文
     * @return Pair<宽度, 高度>
     */
    fun getShareableImageSize(context: Context): Pair<Int, Int> {
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        
        // 计算适合分享的尺寸（9:16 比例）
        val targetWidth = (screenWidth * 0.9f).toInt()
        val targetHeight = (targetWidth * 16f / 9f).toInt()
        
        return Pair(targetWidth, targetHeight)
    }
    
    /**
     * 获取高质量截图尺寸
     * @param context 上下文
     * @return Pair<宽度, 高度>
     */
    fun getHighQualityImageSize(context: Context): Pair<Int, Int> {
        val displayMetrics = context.resources.displayMetrics
        val density = displayMetrics.density
        
        // 高质量截图尺寸（适合保存和分享的长图格式）
        val targetWidth = (360 * density).toInt()  // 360dp
        val targetHeight = (720 * density).toInt() // 720dp (2:1 比例)
        
        return Pair(targetWidth, targetHeight)
    }
    
    /**
     * 获取感想卡片专用截图尺寸
     * @param context 上下文
     * @return Pair<宽度, 高度>
     */
    fun getReflectionCardImageSize(context: Context): Pair<Int, Int> {
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val density = displayMetrics.density
        
        // 感想卡片专用尺寸（9:16比例，适合手机屏幕）
        val targetWidth = (screenWidth * 0.9f).toInt()
        val targetHeight = (targetWidth * 16f / 9f).toInt()
        
        return Pair(targetWidth, targetHeight)
    }
} 