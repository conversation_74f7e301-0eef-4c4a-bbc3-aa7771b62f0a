<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/widget_background"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/widget_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="统计"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary" />

        <TextView
            android:id="@+id/widget_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="每日趋势"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary" />

    </LinearLayout>

    <!-- 日期范围 -->
    <TextView
        android:id="@+id/widget_date_range"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="03/6 - 03/23"
        android:textSize="11sp"
        android:textColor="@color/widget_text_secondary"
        android:layout_marginBottom="12dp" />

    <!-- 图表区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/widget_chart_background"
        android:padding="12dp">

        <!-- 柱状图 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="bottom"
            android:layout_marginBottom="8dp">

            <!-- 第1天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_1"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="30"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第2天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_2"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="45"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第3天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_3"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="25"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第4天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_4"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="60"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第5天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_5"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="80"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第6天 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_6"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="70"
                    android:progressTint="@color/widget_accent_green" />

            </LinearLayout>

            <!-- 第7天（今天） -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="bottom"
                android:layout_marginHorizontal="1dp">

                <ProgressBar
                    android:id="@+id/widget_bar_7"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:rotation="270"
                    android:progress="90"
                    android:progressTint="@color/widget_accent_blue" />

            </LinearLayout>

        </LinearLayout>

        <!-- 日期标签 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_day_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="6"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="11"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="16"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="21"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="22"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="22"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/widget_day_7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="23"
                android:textSize="10sp"
                android:textColor="@color/widget_accent_blue"
                android:textStyle="bold"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- 统计信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="12dp">

        <!-- 总时长 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="总时长"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:id="@+id/widget_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12h 30m"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/widget_text_primary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 平均时长 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="日均"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:id="@+id/widget_avg_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="107min"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/widget_text_primary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 趋势 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="趋势"
                android:textSize="10sp"
                android:textColor="@color/widget_text_secondary" />

            <TextView
                android:id="@+id/widget_trend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📈 上升"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/widget_accent_green"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
