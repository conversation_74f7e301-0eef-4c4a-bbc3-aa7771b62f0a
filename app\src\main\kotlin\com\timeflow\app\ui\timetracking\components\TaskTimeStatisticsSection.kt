package com.timeflow.app.ui.timetracking.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel
import com.timeflow.app.data.dao.TaskTimeStats
import kotlin.math.*
import androidx.compose.foundation.Canvas
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.text.TextStyle
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 任务时间统计区域
 * 参考用户提供的图片UI设计风格
 * 显示每个任务的实际用时、番茄钟数量等统计信息
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun TaskTimeStatisticsSection(
    modifier: Modifier = Modifier,
    viewModel: TaskTimeStatisticsViewModel = hiltViewModel()
) {
    // 状态收集
    val uiState by viewModel.uiState.collectAsState()
    val selectedTimeRange by viewModel.selectedTimeRange.collectAsState()
    
    // 颜色方案 - 参考图片中的暖色调
    val primaryColor = Color(0xFFFF8B94) // 暖粉色
    val secondaryColor = Color(0xFFFFD93D) // 明黄色  
    val accentColor = Color(0xFF6BCF7F) // 薄荷绿
    val neutralColor = Color(0xFFB8B5FF) // 淡紫色
    val backgroundCard = Color(0xFFFFFBF8) // 暖白色
    val textPrimary = Color(0xFF2C2C2C)
    val textSecondary = Color(0xFF666666)
    
    // 总时长统计
    val totalHours = uiState.taskStats.sumOf { it.totalDuration } / 3600f
    val totalSessions = uiState.taskStats.sumOf { it.sessionCount }
    
    // 背景渐变
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFFFBF8), // 暖白色
            Color(0xFFF8F6F4)  // 浅米色
        )
    )
    
    // 数据加载
    LaunchedEffect(selectedTimeRange) {
        viewModel.loadStatistics()
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(brush = backgroundGradient)
            .padding(16.dp)
    ) {
        // 标题和时间范围选择器
        TimeRangeSelector(
            selectedRange = selectedTimeRange,
            onRangeSelected = viewModel::setTimeRange,
            primaryColor = primaryColor
        )
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 概览卡片区域 - 参考图片顶部概览
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 总时间卡片
            OverviewCard(
                modifier = Modifier.weight(1f),
                title = "总时间",
                value = "${totalHours.toInt()}小时",
                subtitle = "${(totalHours % 1 * 60).toInt()}分钟",
                icon = Icons.Default.AccessTime,
                color = primaryColor,
                backgroundCard = backgroundCard
            )
            
            // 总任务数卡片
            OverviewCard(
                modifier = Modifier.weight(1f),
                title = "完成任务",
                value = "${uiState.taskStats.size}个",
                subtitle = "总计${totalSessions}次",
                icon = Icons.Default.CheckCircle,
                color = accentColor,
                backgroundCard = backgroundCard
            )
        }
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 时间分布饼图 - 参考图片中的圆环图
        if (uiState.taskStats.isNotEmpty()) {
            TimeDistributionChart(
                taskStats = uiState.taskStats,
                totalHours = totalHours,
                primaryColor = primaryColor,
                backgroundCard = backgroundCard
            )
            
            Spacer(modifier = Modifier.height(20.dp))
        }
        
        // 任务详细列表 - 参考图片中的活动列表
        TaskDetailsList(
            taskStats = uiState.taskStats,
            totalHours = totalHours,
            primaryColor = primaryColor,
            secondaryColor = secondaryColor,
            accentColor = accentColor,
            backgroundCard = backgroundCard,
            textPrimary = textPrimary,
            textSecondary = textSecondary
        )
        
        // 月度热力图 - 参考图片底部的热力图
        if (selectedTimeRange == "本月") {
            Spacer(modifier = Modifier.height(20.dp))
            MonthlyHeatmap(
                viewModel = viewModel,
                primaryColor = primaryColor,
                backgroundCard = backgroundCard
            )
        }
    }
}

/**
 * 时间范围选择器
 */
@Composable
private fun TimeRangeSelector(
    selectedRange: String,
    onRangeSelected: (String) -> Unit,
    primaryColor: Color
) {
    val timeRanges = listOf("今日", "本周", "本月", "季度")
    
    Column {
        Text(
            text = "本周进展详情",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2C2C2C)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            timeRanges.forEach { range ->
                FilterChip(
                    selected = selectedRange == range,
                    onClick = { onRangeSelected(range) },
                    label = { 
                        Text(
                            text = range,
                            fontSize = 13.sp,
                            fontWeight = if (selectedRange == range) FontWeight.SemiBold else FontWeight.Medium
                        )
                    },
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = primaryColor,
                        selectedLabelColor = Color.White,
                        containerColor = Color(0xFFF0F0F0),
                        labelColor = Color(0xFF666666)
                    ),
                    modifier = Modifier.height(32.dp)
                )
            }
        }
    }
}

/**
 * 概览卡片
 */
@Composable
private fun OverviewCard(
    modifier: Modifier = Modifier,
    title: String,
    value: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    backgroundCard: Color
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundCard),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color.copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 主要数值
            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2C2C2C)
            )
            
            // 标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF666666)
            )
            
            // 副标题
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF888888)
            )
        }
    }
}

/**
 * 时间分布图
 */
@Composable
private fun TimeDistributionChart(
    taskStats: List<TaskTimeStats>,
    totalHours: Float,
    primaryColor: Color,
    backgroundCard: Color
) {
    // 颜色列表
    val colors = listOf(
        Color(0xFFFF8B94), // 暖粉色
        Color(0xFFFFD93D), // 明黄色
        Color(0xFF6BCF7F), // 薄荷绿
        Color(0xFFB8B5FF), // 淡紫色
        Color(0xFFFFB347), // 橙色
        Color(0xFF87CEEB), // 天蓝色
        Color(0xFFDDA0DD), // 梅紫色
        Color(0xFF98FB98)  // 浅绿色
    )
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundCard),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "时间分布",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2C2C2C)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 饼图
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    PieChart(
                        data = taskStats.map { it.totalDuration / 3600f },
                        colors = colors,
                        modifier = Modifier.size(120.dp)
                    )
                    
                    // 中心文字
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "${totalHours.toInt()}h",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2C2C2C)
                        )
                        Text(
                            text = "总时间",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF666666)
                        )
                    }
                }
                
                // 图例
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 16.dp)
                ) {
                    taskStats.take(5).forEachIndexed { index, stat ->
                        val percentage = if (totalHours > 0) (stat.totalDuration / 3600f / totalHours * 100).toInt() else 0
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 2.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(
                                        colors[index % colors.size],
                                        CircleShape
                                    )
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = stat.taskName,
                                    style = MaterialTheme.typography.bodySmall,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF2C2C2C),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                                Text(
                                    text = "${percentage}%",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF666666)
                                )
                            }
                        }
                    }
                    
                    if (taskStats.size > 5) {
                        Text(
                            text = "...",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF666666),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 饼图组件
 */
@Composable
private fun PieChart(
    data: List<Float>,
    colors: List<Color>,
    modifier: Modifier = Modifier
) {
    val total = data.sum()
    val proportions = data.map { it / total }
    val sweepAngles = proportions.map { it * 360f }
    
    Canvas(modifier = modifier) {
        val center = Offset(size.width / 2, size.height / 2)
        val radius = size.minDimension / 2 * 0.8f
        val strokeWidth = radius * 0.3f
        
        var startAngle = -90f
        
        sweepAngles.forEachIndexed { index, sweepAngle ->
            drawArc(
                color = colors[index % colors.size],
                startAngle = startAngle,
                sweepAngle = sweepAngle,
                useCenter = false,
                topLeft = Offset(center.x - radius, center.y - radius),
                size = Size(radius * 2, radius * 2),
                style = Stroke(width = strokeWidth, cap = StrokeCap.Butt)
            )
            startAngle += sweepAngle
        }
    }
}

/**
 * 任务详细列表 - 参考图片中的活动列表设计
 */
@Composable
private fun TaskDetailsList(
    taskStats: List<TaskTimeStats>,
    totalHours: Float,
    primaryColor: Color,
    secondaryColor: Color,
    accentColor: Color,
    backgroundCard: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundCard),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "任务明细",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = textPrimary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            taskStats.forEach { stat ->
                TaskStatsItem(
                    taskStat = stat,
                    totalHours = totalHours,
                    primaryColor = primaryColor,
                    textPrimary = textPrimary,
                    textSecondary = textSecondary
                )
                
                if (stat != taskStats.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * 单个任务统计项
 */
@Composable
private fun TaskStatsItem(
    taskStat: TaskTimeStats,
    totalHours: Float,
    primaryColor: Color,
    textPrimary: Color,
    textSecondary: Color
) {
    val hours = taskStat.totalDuration / 3600f
    val percentage = if (totalHours > 0) hours / totalHours else 0f
    val minutes = ((taskStat.totalDuration % 3600) / 60f).toInt()
    
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 任务信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = taskStat.taskName,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = textPrimary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = textSecondary,
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${taskStat.sessionCount}次",
                        style = MaterialTheme.typography.bodySmall,
                        color = textSecondary
                    )
                }
            }
            
            // 时间和进度
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${hours.toInt()}小时${minutes}分钟",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = textPrimary
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "${(percentage * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = primaryColor,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度条
        LinearProgressIndicator(
            progress = percentage,
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp)),
            color = primaryColor,
            trackColor = primaryColor.copy(alpha = 0.2f)
        )
    }
}

/**
 * 月度热力图 - 参考图片底部的热力图设计
 */
@Composable
private fun MonthlyHeatmap(
    viewModel: TaskTimeStatisticsViewModel,
    primaryColor: Color,
    backgroundCard: Color
) {
    val heatmapData by viewModel.monthlyHeatmapData.collectAsState()
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundCard),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "3月的时间块",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2C2C2C)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "每个时间代表一个小时",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF666666)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 月份标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "上午",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF666666)
                )
                Text(
                    text = "下午",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF666666)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 热力图网格
            HeatmapGrid(
                data = heatmapData,
                primaryColor = primaryColor
            )
        }
    }
}

/**
 * 热力图网格
 */
@Composable
private fun HeatmapGrid(
    data: Array<IntArray>,
    primaryColor: Color
) {
    val maxValue = data.flatMap { it.toList() }.maxOrNull() ?: 1
    
    Column(
        verticalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        // 日期标题（1-31）
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // 左侧空白（对应日期标签）
            Spacer(modifier = Modifier.width(24.dp))
            
            // 日期数字
            for (day in 1..31) {
                if (day <= 5 || day % 5 == 0 || day > 28) {
                    Text(
                        text = day.toString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF666666),
                        modifier = Modifier.width(12.dp),
                        textAlign = TextAlign.Center,
                        fontSize = 8.sp
                    )
                } else {
                    Spacer(modifier = Modifier.width(12.dp))
                }
            }
        }
        
        // 热力图主体
        for (hour in 0 until 24) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 时间标签
                Text(
                    text = "${hour.toString().padStart(2, '0')}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF666666),
                    modifier = Modifier.width(24.dp),
                    fontSize = 10.sp
                )
                
                // 每日数据点
                for (day in 0 until 31) {
                    val value = if (day < data.size && hour < data[day].size) data[day][hour] else 0
                    val intensity = if (maxValue > 0) value.toFloat() / maxValue else 0f
                    
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = when {
                                    intensity == 0f -> Color(0xFFF0F0F0)
                                    intensity < 0.25f -> primaryColor.copy(alpha = 0.2f)
                                    intensity < 0.5f -> primaryColor.copy(alpha = 0.4f)
                                    intensity < 0.75f -> primaryColor.copy(alpha = 0.6f)
                                    else -> primaryColor
                                },
                                shape = RoundedCornerShape(2.dp)
                            )
                    )
                }
            }
        }
    }
} 