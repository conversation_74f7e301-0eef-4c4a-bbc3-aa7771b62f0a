package com.timeflow.app.ui.screen.goal

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalSubTaskTemplate
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.GoalTemplateRepository
import com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase
import com.timeflow.app.utils.PagedTemplateResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

// UI状态密封类
sealed class TemplateUiState {
    object Idle : TemplateUiState()
    object Loading : TemplateUiState()
    data class Success(val message: String = "") : TemplateUiState()
    data class Error(val message: String) : TemplateUiState()
}

// 模板列表过滤状态
data class TemplateFilterState(
    val searchQuery: String = "",
    val selectedCategory: String? = null,
    val sortOrder: TemplateSortOrder = TemplateSortOrder.NAME_ASC
)

// 模板排序选项
enum class TemplateSortOrder {
    NAME_ASC,
    NAME_DESC,
    USAGE_COUNT,
    RECENTLY_USED,
    RECENTLY_CREATED
}

@HiltViewModel
class GoalTemplateViewModel @Inject constructor(
    private val templateRepository: GoalTemplateRepository,
    private val goalRepository: GoalRepository,
    private val smartTemplateUseCase: SmartTemplateUseCase
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow<TemplateUiState>(TemplateUiState.Idle)
    val uiState: StateFlow<TemplateUiState> = _uiState.asStateFlow()
    
    // 模板列表
    private val _templates = MutableStateFlow<List<GoalTemplate>>(emptyList())
    val templates: StateFlow<List<GoalTemplate>> = _templates.asStateFlow()
    
    // 最近使用的模板
    private val _recentTemplates = MutableStateFlow<List<GoalTemplate>>(emptyList())
    val recentTemplates: StateFlow<List<GoalTemplate>> = _recentTemplates.asStateFlow()
    
    // 常用模板
    private val _popularTemplates = MutableStateFlow<List<GoalTemplate>>(emptyList())
    val popularTemplates: StateFlow<List<GoalTemplate>> = _popularTemplates.asStateFlow()
    
    // 推荐模板（从模板库获取）
    private val _recommendedTemplates = MutableStateFlow<List<GoalTemplate>>(emptyList())
    val recommendedTemplates: StateFlow<List<GoalTemplate>> = _recommendedTemplates.asStateFlow()

    // 分类模板 - 使用SmartTemplateUseCase
    private val _categorizedTemplates = MutableStateFlow<Map<String, List<GoalTemplate>>>(emptyMap())
    val categorizedTemplates: StateFlow<Map<String, List<GoalTemplate>>> = _categorizedTemplates.asStateFlow()
    
    // 分页分类模板 - 新增
    private val _pagedCategorizedTemplates = MutableStateFlow<Map<String, PagedTemplateResult>>(emptyMap())
    val pagedCategorizedTemplates: StateFlow<Map<String, PagedTemplateResult>> = _pagedCategorizedTemplates.asStateFlow()
    
    // 当前编辑的模板
    private val _currentTemplate = MutableStateFlow<GoalTemplate?>(null)
    val currentTemplate: StateFlow<GoalTemplate?> = _currentTemplate.asStateFlow()
    
    // 过滤状态
    private val _filterState = MutableStateFlow(TemplateFilterState())
    val filterState: StateFlow<TemplateFilterState> = _filterState.asStateFlow()
    
    // 可用分类列表
    private val _availableCategories = MutableStateFlow<List<String>>(emptyList())
    val availableCategories: StateFlow<List<String>> = _availableCategories.asStateFlow()
    
    // 分页加载状态
    private val _isLoadingMore = MutableStateFlow(false)
    val isLoadingMore: StateFlow<Boolean> = _isLoadingMore.asStateFlow()
    
    // 初始化
    init {
        loadAllTemplates()
        loadRecentTemplates()
        loadPopularTemplates()
        loadRecommendedTemplates()
        loadCategorizedTemplates()
        updateAvailableCategories()
    }
    
    // 加载所有模板
    fun loadAllTemplates() {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                templateRepository.getAllTemplates()
                    .catch { e ->
                        _uiState.value = TemplateUiState.Error(e.message ?: "加载模板失败")
                    }
                    .collect { templates ->
                        _templates.value = templates
                        _uiState.value = TemplateUiState.Idle
                    }
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "加载模板失败")
            }
        }
    }
    
    // 加载最近使用的模板
    fun loadRecentTemplates(limit: Int = 5) {
        viewModelScope.launch {
            try {
                templateRepository.getRecentlyUsedTemplates(limit)
                    .catch { e ->
                        Log.e("GoalTemplateViewModel", "加载最近模板失败: ${e.message}")
                    }
                    .collect { templates ->
                        _recentTemplates.value = templates
                    }
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "加载最近模板失败: ${e.message}")
            }
        }
    }
    
    // 加载常用模板
    fun loadPopularTemplates(limit: Int = 5) {
        viewModelScope.launch {
            try {
                templateRepository.getMostUsedTemplates(limit)
                    .catch { e ->
                        Log.e("GoalTemplateViewModel", "加载常用模板失败: ${e.message}")
                    }
                    .collect { templates ->
                        _popularTemplates.value = templates
                    }
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "加载常用模板失败: ${e.message}")
            }
        }
    }
    
    // 加载推荐模板
    fun loadRecommendedTemplates() {
        viewModelScope.launch {
            try {
                _recommendedTemplates.value = getTemplateLibraryData()
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "加载推荐模板失败: ${e.message}")
            }
        }
    }
    
    // 加载分类模板
    fun loadCategorizedTemplates() {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                smartTemplateUseCase.getCategorizedTemplates()
                    .catch { e ->
                        _uiState.value = TemplateUiState.Error(e.message ?: "加载分类模板失败")
                    }
                    .collect { categorized ->
                        _categorizedTemplates.value = categorized
                        _uiState.value = TemplateUiState.Idle
                    }
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "加载分类模板失败")
            }
        }
    }
    
    // 加载分页分类模板 - 新增
    fun loadPagedCategorizedTemplates(page: Int = 1, pageSize: Int = 5) {
        viewModelScope.launch {
            if (page == 1) {
                _uiState.value = TemplateUiState.Loading
            } else {
                _isLoadingMore.value = true
            }
            
            try {
                smartTemplateUseCase.getCategorizedTemplatesPaged(page, pageSize)
                    .catch { e ->
                        if (page == 1) {
                            _uiState.value = TemplateUiState.Error(e.message ?: "加载分类模板失败")
                        }
                        _isLoadingMore.value = false
                        Log.e("GoalTemplateViewModel", "加载分页模板失败: ${e.message}")
                    }
                    .collect { pagedCategorized ->
                        if (page == 1) {
                            // 第一页直接替换
                            _pagedCategorizedTemplates.value = pagedCategorized
                        } else {
                            // 后续页合并结果
                            val currentPaged = _pagedCategorizedTemplates.value.toMutableMap()
                            pagedCategorized.forEach { (category, pagedResult) ->
                                val currentResult = currentPaged[category]
                                if (currentResult != null) {
                                    // 合并模板列表
                                    val mergedTemplates = currentResult.templates + pagedResult.templates
                                    currentPaged[category] = PagedTemplateResult(
                                        templates = mergedTemplates,
                                        totalCount = pagedResult.totalCount,
                                        hasNextPage = pagedResult.hasNextPage,
                                        page = pagedResult.page,
                                        pageSize = pagedResult.pageSize
                                    )
                                } else {
                                    currentPaged[category] = pagedResult
                                }
                            }
                            _pagedCategorizedTemplates.value = currentPaged
                        }
                        
                        if (page == 1) {
                            _uiState.value = TemplateUiState.Idle
                        }
                        _isLoadingMore.value = false
                    }
            } catch (e: Exception) {
                if (page == 1) {
                    _uiState.value = TemplateUiState.Error(e.message ?: "加载分类模板失败")
                }
                _isLoadingMore.value = false
                Log.e("GoalTemplateViewModel", "加载分页模板失败: ${e.message}")
            }
        }
    }
    
    // 加载特定分类的更多模板 - 新增
    fun loadMoreTemplatesForCategory(category: String, page: Int) {
        viewModelScope.launch {
            // 设置正在加载状态
            _isLoadingMore.value = true
            
            try {
                // 获取特定分类的下一页数据
                val pageSize = 5 // 每次加载5个模板
                val result = templateRepository.getTemplatesByCategoryPaged(category, page, pageSize)
                
                // 更新特定分类的数据
                val currentCategories = _categorizedTemplates.value.toMutableMap()
                val currentTemplates = currentCategories[category] ?: emptyList()
                
                // 合并现有模板和新加载的模板
                currentCategories[category] = currentTemplates + result.templates
                
                // 更新状态
                _categorizedTemplates.value = currentCategories
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "加载更多模板失败: ${e.message}")
            } finally {
                _isLoadingMore.value = false
            }
        }
    }
    
    // 筛选模板 - 根据过滤条件
    private fun applyFilters(templates: List<GoalTemplate>): List<GoalTemplate> {
        val filterState = _filterState.value
        var filtered = templates
        
        // 应用类别过滤
        if (!filterState.selectedCategory.isNullOrEmpty()) {
            filtered = filtered.filter { it.category == filterState.selectedCategory }
        }
        
        // 应用搜索查询
        if (filterState.searchQuery.isNotEmpty()) {
            val query = filterState.searchQuery.lowercase()
            filtered = filtered.filter {
                it.name.lowercase().contains(query) || 
                it.description.lowercase().contains(query) ||
                it.category.lowercase().contains(query)
            }
        }
        
        // 应用排序
        filtered = when (filterState.sortOrder) {
            TemplateSortOrder.NAME_ASC -> filtered.sortedBy { it.name }
            TemplateSortOrder.NAME_DESC -> filtered.sortedByDescending { it.name }
            TemplateSortOrder.USAGE_COUNT -> filtered.sortedByDescending { it.usageCount }
            TemplateSortOrder.RECENTLY_USED -> filtered.sortedByDescending { it.lastUsed }
            TemplateSortOrder.RECENTLY_CREATED -> filtered.sortedByDescending { it.createdAt }
        }
        
        return filtered
    }
    
    // 更新可用的分类列表
    fun updateAvailableCategories() {
        viewModelScope.launch {
            try {
                templateRepository.getAllTemplates()
                    .collect { templates ->
                        val categories = templates
                            .map { it.category }
                            .filter { it.isNotEmpty() }
                            .distinct()
                            .sorted()
                        
                        _availableCategories.value = categories
                    }
            } catch (e: Exception) {
                // 静默处理，不需要向用户显示错误
            }
        }
    }
    
    // 获取所有预定义的类别
    fun getPredefinedCategories(): List<String> {
        return TemplateCategory.values().map { it.displayName }
    }
    
    // 记录模板使用
    fun recordTemplateUsage(templateId: String) {
        viewModelScope.launch {
            try {
                templateRepository.recordTemplateUsage(templateId)
                // 清除分类模板缓存
                smartTemplateUseCase.clearCache()
                // 重新加载数据
                loadRecentTemplates()
                loadPopularTemplates()
            } catch (e: Exception) {
                // 静默处理，不影响用户体验
                Log.e("GoalTemplateViewModel", "记录模板使用失败: ${e.message}")
            }
        }
    }
    
    // 清除缓存并重新加载数据 - 新增
    fun refreshAllData() {
        viewModelScope.launch {
            try {
                // 清除仓库和用例中的缓存
                templateRepository.clearCache()
                smartTemplateUseCase.clearCache()
                
                // 重新加载所有数据
                loadAllTemplates()
                loadRecentTemplates()
                loadPopularTemplates()
                loadCategorizedTemplates()
                loadPagedCategorizedTemplates(1)
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "刷新数据失败: ${e.message}")
            }
        }
    }
    
    // 获取模板库数据
    private fun getTemplateLibraryData(): List<GoalTemplate> {
        // 在实际实现中，这些应该从远程API获取
        return listOf(
            GoalTemplate(
                id = "lib1",
                name = "每日阅读",
                description = "培养每日阅读习惯",
                category = "学习",
                iconName = "book",
                colorHex = "#4285F4",
                defaultTitle = "每天阅读30分钟",
                defaultDescription = "培养持续阅读的习惯，拓展知识面",
                isRecurring = true,
                defaultTargetValue = 30.0,
                defaultUnit = "分钟"
            ),
            GoalTemplate(
                id = "lib2",
                name = "健身计划",
                description = "每周锻炼身体计划",
                category = "健康",
                iconName = "fitness_center",
                colorHex = "#0F9D58",
                defaultTitle = "每周健身3次",
                defaultDescription = "保持身体健康，增强体质",
                isRecurring = true,
                goalType = com.timeflow.app.data.model.GoalType.NUMERIC,
                defaultTargetValue = 3.0,
                defaultUnit = "次/周"
            ),
            GoalTemplate(
                id = "lib3",
                name = "学习新技能",
                description = "学习一项新的技能或知识",
                category = "个人成长",
                iconName = "school",
                colorHex = "#DB4437",
                defaultTitle = "学习人工智能基础",
                defaultDescription = "掌握基本的AI概念和应用",
                isRecurring = false,
                defaultDurationDays = 90
            ),
            GoalTemplate(
                id = "lib4",
                name = "项目管理",
                description = "管理一个完整的项目",
                category = "工作",
                iconName = "assignment",
                colorHex = "#F4B400",
                defaultTitle = "完成季度项目",
                defaultDescription = "按时高质量完成季度目标",
                isRecurring = false,
                defaultDurationDays = 90,
                goalType = com.timeflow.app.data.model.GoalType.BOOLEAN,
                defaultPriority = com.timeflow.app.data.model.GoalPriority.HIGH
            ),
            GoalTemplate(
                id = "lib5",
                name = "减少社交媒体使用",
                description = "控制社交媒体使用时间",
                category = "数字健康",
                iconName = "phone_android",
                colorHex = "#7B1FA2",
                defaultTitle = "每天限制社交媒体使用1小时",
                defaultDescription = "减少社交媒体对注意力的影响",
                isRecurring = true,
                goalType = com.timeflow.app.data.model.GoalType.NUMERIC,
                defaultTargetValue = 60.0,
                defaultUnit = "分钟/天"
            )
        )
    }
    
    // 加载模板详情
    fun loadTemplateDetail(templateId: String) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                val template = templateRepository.getTemplateById(templateId)
                if (template != null) {
                    _currentTemplate.value = template
                    _uiState.value = TemplateUiState.Idle
                } else {
                    _uiState.value = TemplateUiState.Error("找不到指定的模板")
                }
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "加载模板详情失败")
            }
        }
    }
    
    // 更新模板
    fun updateTemplate(template: GoalTemplate) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                templateRepository.updateTemplate(template)
                _uiState.value = TemplateUiState.Success("模板已更新")
                refreshAllData()
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "更新模板失败")
            }
        }
    }
    
    // 创建新模板
    fun createTemplate(template: GoalTemplate) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                val id = templateRepository.saveTemplate(template)
                _uiState.value = TemplateUiState.Success("已创建新模板")
                refreshAllData()
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "创建模板失败")
            }
        }
    }
    
    // 删除模板
    fun deleteTemplate(templateId: String) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                templateRepository.deleteTemplate(templateId)
                _uiState.value = TemplateUiState.Success("模板已删除")
                
                // 如果删除的是当前模板，清空当前模板
                if (_currentTemplate.value?.id == templateId) {
                    _currentTemplate.value = null
                }
                
                refreshAllData()
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "删除模板失败")
            }
        }
    }
    
    // 设置分类过滤器
    fun setCategoryFilter(category: String?) {
        _filterState.update { it.copy(selectedCategory = category) }
        // 应用更新的过滤条件
        applyFiltersToCurrentData()
    }
    
    // 设置搜索查询
    fun setSearchQuery(query: String) {
        _filterState.update { it.copy(searchQuery = query) }
        // 应用更新的过滤条件
        applyFiltersToCurrentData()
    }
    
    // 设置排序方式
    fun setSortOrder(sortOrder: TemplateSortOrder) {
        _filterState.update { it.copy(sortOrder = sortOrder) }
        // 应用更新的过滤条件
        applyFiltersToCurrentData()
    }
    
    // 将当前过滤条件应用到数据上
    private fun applyFiltersToCurrentData() {
        viewModelScope.launch {
            try {
                _templates.value = applyFilters(_templates.value)
            } catch (e: Exception) {
                Log.e("GoalTemplateViewModel", "应用过滤条件失败: ${e.message}")
            }
        }
    }
    
    // 从模板库导入模板
    fun importTemplate(template: GoalTemplate) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                // 生成新的ID以确保不会与现有模板冲突
                val newTemplate = template.copy(
                    id = "",  // 保存时会自动生成新ID
                    usageCount = 0,
                    lastUsed = null,
                    createdAt = LocalDateTime.now()
                )
                
                templateRepository.saveTemplate(newTemplate)
                _uiState.value = TemplateUiState.Success("成功导入模板")
                refreshAllData()
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "导入模板失败")
            }
        }
    }
    
    // 从模板创建目标
    fun createGoalFromTemplate(templateId: String, customTitle: String? = null) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                val goal = templateRepository.createGoalFromTemplate(templateId, customTitle)
                if (goal != null) {
                    // 记录模板使用
                    recordTemplateUsage(templateId)
                    _uiState.value = TemplateUiState.Success("已从模板创建新目标")
                } else {
                    _uiState.value = TemplateUiState.Error("创建目标失败")
                }
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "创建目标失败")
            }
        }
    }
    
    // 从现有目标创建模板
    fun createTemplateFromGoal(goalId: String, templateName: String, category: String) {
        viewModelScope.launch {
            _uiState.value = TemplateUiState.Loading
            try {
                val template = templateRepository.createTemplateFromGoal(goalId, templateName, category)
                if (template != null) {
                    _uiState.value = TemplateUiState.Success("已从目标创建新模板")
                    refreshAllData()
                } else {
                    _uiState.value = TemplateUiState.Error("创建模板失败")
                }
            } catch (e: Exception) {
                _uiState.value = TemplateUiState.Error(e.message ?: "创建模板失败")
            }
        }
    }
} 