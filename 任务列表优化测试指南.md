# 任务列表优化测试指南 📋

## 🎯 新功能概述

本次优化为 TaskListFullScreen 添加了两个重要功能：

### 1. 默认紧急程度降序排序 ⬇️
- **功能**: 任务默认按紧急程度从高到低排序（CRITICAL → HIGH → MEDIUM → LOW）
- **目的**: 让用户首先看到最重要的任务，提高工作效率

### 2. 30秒完成撤销功能 ↩️
- **功能**: 点击完成任务后，30秒内可以撤销该操作
- **目的**: 避免误操作，提供更好的用户体验

---

## 🧪 测试步骤

### 📊 测试1: 默认紧急程度排序验证

#### 测试前准备
1. **确保有不同紧急程度的任务**
   - 创建几个不同优先级的任务：
     - 紧急任务（CRITICAL/红色）
     - 高优先级任务（HIGH/橙色）  
     - 普通优先级任务（MEDIUM/黄色）
     - 低优先级任务（LOW/绿色）

#### 具体测试步骤
```bash
# 1. 打开应用
adb shell am start -n com.timeflow.app/.MainActivity

# 2. 进入任务列表页面
# - 导航到任务列表
# - 观察任务的排列顺序

# 3. 验证排序是否正确
# 预期结果：任务按以下顺序显示
# ① 所有紧急任务（红色标记）
# ② 所有高优先级任务（橙色标记）
# ③ 所有普通优先级任务（黄色标记）
# ④ 所有低优先级任务（绿色标记）
```

#### 验证要点 ✅
- [ ] 紧急任务显示在列表顶部
- [ ] 相同优先级任务内部保持原有顺序
- [ ] 优先级颜色标记清晰可见
- [ ] 排序不影响任务的其他功能

---

### ↩️ 测试2: 30秒撤销功能验证

#### 具体测试步骤

**测试场景1: 正常撤销流程**
```bash
# 1. 选择一个未完成的任务
# 2. 点击任务的完成按钮（勾选框）
# 3. 观察Snackbar出现
# 4. 点击"撤销"按钮
# 5. 确认任务恢复为未完成状态
```

**测试场景2: 30秒自动过期**
```bash
# 1. 完成一个任务
# 2. 观察Snackbar出现
# 3. 等待30秒不操作
# 4. 确认Snackbar自动消失
# 5. 确认任务保持完成状态
```

**测试场景3: 右滑完成+撤销**
```bash
# 1. 对任务进行右滑操作
# 2. 观察任务标记为完成
# 3. 观察撤销Snackbar出现
# 4. 测试撤销功能
```

#### 验证要点 ✅
- [ ] Snackbar正确显示任务标题
- [ ] "撤销"按钮响应正常
- [ ] 30秒倒计时功能正常
- [ ] 撤销后任务状态正确恢复
- [ ] 多个任务完成时撤销功能互不影响

---

## 🔍 日志监控

### 关键日志标签
在测试过程中关注以下日志：

```bash
# 排序相关日志
adb logcat -s TaskListFullScreen | grep "排序\|优先级\|urgency"

# 撤销功能相关日志  
adb logcat -s UndoComplete UndoTimer

# 综合监控命令
adb logcat -c && adb logcat -s TaskListFullScreen UndoComplete UndoTimer | head -50
```

### 成功日志示例

**排序功能正常日志:**
```
TaskListFullScreen: 🎯 默认按紧急程度降序排序
TaskListFullScreen: 最终显示的任务数: X
TaskListFullScreen: 排序完成，CRITICAL任务在前
```

**撤销功能正常日志:**
```
UndoComplete: 开始处理任务完成: task-id-xxx
UndoComplete: 任务已标记为完成，撤销选项可用30秒: 任务标题
UndoTimer: 撤销时间已过期，移除任务: task-id-xxx
UndoComplete: 任务撤销成功: 任务标题
```

---

## 🎯 预期效果

### 用户体验提升
1. **更高效的任务管理**
   - 重要任务一目了然
   - 减少查找时间

2. **更安全的操作体验**
   - 误操作可以撤销
   - 30秒缓冲时间合理

3. **更直观的界面反馈**
   - 清晰的完成提示
   - 简洁的撤销选项

### 技术指标
- **排序响应时间**: < 100ms
- **撤销响应时间**: < 200ms  
- **内存占用**: 基本无增加
- **电池消耗**: 忽略不计

---

## 🐛 可能的问题与解决

### 常见问题

**问题1: 排序不生效**
- **现象**: 任务顺序仍然是创建时间顺序
- **检查**: 确认sortMode是否设置为PRIORITY_HIGH_TO_LOW
- **解决**: 重启应用或清除缓存

**问题2: 撤销Snackbar不显示**
- **现象**: 完成任务后没有撤销提示
- **检查**: 查看日志是否有错误信息
- **解决**: 确认Scaffold和SnackbarHost配置正确

**问题3: 撤销功能失效**
- **现象**: 点击撤销后任务状态没有恢复
- **检查**: 数据库更新是否成功
- **解决**: 检查网络连接和数据同步

---

## 📈 性能测试

### 压力测试建议
1. **大量任务排序测试**: 100+任务的排序性能
2. **多任务撤销测试**: 同时完成多个任务的撤销管理
3. **长时间使用测试**: 持续使用2小时以上的稳定性

### 监控指标
- CPU使用率变化
- 内存使用量变化  
- 响应时间统计
- 崩溃率统计

---

## ✅ 验收标准

### 功能验收
- [ ] 任务按紧急程度正确排序
- [ ] 撤销功能30秒内正常工作
- [ ] UI反馈及时准确
- [ ] 不影响其他现有功能

### 性能验收  
- [ ] 排序响应时间 < 100ms
- [ ] 撤销操作响应时间 < 200ms
- [ ] 无明显内存泄漏
- [ ] 无崩溃发生

### 兼容性验收
- [ ] 不同Android版本正常工作
- [ ] 不同屏幕尺寸适配良好
- [ ] 深色模式显示正常

---

## 🎉 发布准备

### 发布前检查清单
- [ ] 所有测试用例通过
- [ ] 代码review完成
- [ ] 性能测试合格
- [ ] 用户文档更新
- [ ] 版本号更新

### 用户通知内容
```
🎉 任务管理功能升级！

✨ 新功能：
• 任务智能排序：重要任务自动置顶
• 完成撤销：30秒内可撤销误操作

让任务管理更高效、更安全！
```

---

**祝测试顺利！** 🚀 