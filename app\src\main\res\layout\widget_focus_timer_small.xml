<?xml version="1.0" encoding="utf-8"?>
<!-- 专注计时器小组件 (1x1) - 小尺寸 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_focus_background"
    android:orientation="vertical"
    android:padding="8dp"
    android:gravity="center">

    <!-- 时间显示 -->
    <TextView
        android:id="@+id/widget_timer_display"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="25:00"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/widget_focus_text_primary_light"
        android:fontFamily="sans-serif-light" />

    <!-- 状态指示器 -->
    <View
        android:id="@+id/widget_status_indicator"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/widget_status_idle"
        android:layout_marginTop="4dp" />

    <!-- 控制按钮 -->
    <ImageButton
        android:id="@+id/widget_play_pause_button"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_play"
        android:background="@drawable/widget_button_background"
        android:scaleType="centerInside"
        android:layout_marginTop="4dp"
        android:contentDescription="播放/暂停" />

    <!-- 隐藏的兼容性元素 -->
    <TextView
        android:id="@+id/widget_task_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_session_count"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_today_total"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <ImageButton
        android:id="@+id/widget_stop_button"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</LinearLayout>
