package com.timeflow.app.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.navigation.TimeFlowNavHost
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import com.timeflow.app.ui.theme.TimeFlowTheme
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.LaunchedEffect
import android.util.Log
import androidx.navigation.NavHostController

/**
 * 应用主入口
 */
@Composable
fun TimeFlowApp(navController: NavHostController = rememberNavController()) {
    val viewModel: CalendarViewModel = hiltViewModel()
    val userColorPreference by viewModel.userColorPreference.collectAsState()
    
    // 记录最后一个主页颜色状态，避免导航返回时丢失
    val lastHomeBackgroundColor = remember { mutableStateOf(Color(userColorPreference.homePageBackground)) }
    
    // 当前路由
    val currentRoute = navController.currentBackStackEntry?.destination?.route
    
    // 记录上一个路由，用于检测导航情况
    val previousRoute = remember { mutableStateOf<String?>(null) }
    
    // 监听路由变化，但不应用任何动画
    LaunchedEffect(navController) {
        navController.addOnDestinationChangedListener { _, destination, _ ->
            val newRoute = destination.route
            Log.d("TimeFlowApp", "路由变化: ${previousRoute.value} -> $newRoute")
            
            // 如果从其他页面返回到主页，不应用任何过渡效果
            if (newRoute == AppDestinations.UNIFIED_HOME_ROUTE || newRoute == AppDestinations.HOME_ROUTE) {
                Log.d("TimeFlowApp", "返回主页")
            }
            
            previousRoute.value = newRoute
        }
    }
    
    // 当主页背景色变化时，更新记忆状态
    LaunchedEffect(userColorPreference.homePageBackground) {
        lastHomeBackgroundColor.value = Color(userColorPreference.homePageBackground)
        Log.d("TimeFlowApp", "记录主页背景色: ${userColorPreference.homePageBackground.toString(16)}")
    }
    
    // 获取当前页面背景色
    val backgroundColor = when (currentRoute) {
        AppDestinations.HOME_ROUTE, AppDestinations.UNIFIED_HOME_ROUTE -> lastHomeBackgroundColor.value
        AppDestinations.CALENDAR_ROUTE -> Color(userColorPreference.calendarPageBackground)
        AppDestinations.TIME_STATISTICS_ROUTE -> Color(userColorPreference.statisticsPageBackground)
        AppDestinations.SETTINGS_ROUTE -> Color(userColorPreference.settingsPageBackground)
        AppDestinations.PROFILE_ROUTE -> Color(userColorPreference.settingsPageBackground)
        // 对于其他页面，使用记住的主页背景色
        else -> lastHomeBackgroundColor.value
    }
    
    // 使用主题包装导航宿主
    TimeFlowTheme(
        darkTheme = userColorPreference.useDarkMode,
        customBackgroundColor = backgroundColor
    ) {
        // 直接使用UNIFIED_HOME_ROUTE作为启动路由，避免不必要的导航跳转
        TimeFlowNavHost(
            navController = navController,
            startDestination = AppDestinations.UNIFIED_HOME_ROUTE  // 直接设为首页
        )
    }
} 