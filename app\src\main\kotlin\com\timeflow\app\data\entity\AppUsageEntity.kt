package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.LocalDateConverter
import java.time.LocalDate

/**
 * 应用使用数据实体
 * 存储每个应用在特定日期的使用情况
 */
@Entity(tableName = "app_usage")
@TypeConverters(LocalDateConverter::class)
data class AppUsageEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // 应用包名
    val packageName: String,
    
    // 使用日期
    val date: LocalDate,
    
    // 使用时长（毫秒）
    val durationMs: Long,
    
    // 启动次数
    val launchCount: Int,
    
    // 应用分类
    val category: String,
    
    // 是否为生产力应用
    val isProductivity: Boolean
) 