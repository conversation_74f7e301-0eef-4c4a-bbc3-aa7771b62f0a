package com.timeflow.app.utils

import android.app.Activity
import android.os.Build
import android.view.View
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import android.graphics.Color
import android.util.Log
import android.widget.Toast
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.view.WindowInsetsController
import android.view.WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
import android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
import android.view.WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS
import androidx.compose.ui.graphics.toArgb
import com.timeflow.app.utils.safeParseColor
import androidx.compose.ui.platform.LocalContext

/**
 * 系统栏（状态栏和导航栏）管理工具类
 * 提供统一的方法来管理整个应用的系统栏设置
 */
object SystemBarManager {
    // 调试模式标志
    private const val DEBUG = false
    private const val TAG = "SystemBarManager"
    
    // 跟踪最后设置的状态栏是否为浅色图标
    private var lastLightStatusBars = true
    
    // 系统栏状态
    private var isSystemBarsConfigured = false
    
    // 添加防重复调用变量
    private var lastStatusBarState: Triple<Activity?, Boolean, Boolean>? = null
    private var lastStatusBarChangeTime = 0L
    private const val STATUS_BAR_CHANGE_DEBOUNCE_MS = 300L
    
    /**
     * 记录日志并可选择在屏幕上显示提示
     */
    private fun logDebug(activity: Activity?, message: String) {
        Log.d(TAG, message)
        if (DEBUG && activity != null) {
            Toast.makeText(activity, message, Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 为整个应用设置初始系统栏配置
     * 无动画版本 - 立即设置所有属性
     */
    fun setupActivitySystemBars(activity: Activity) {
        if (isSystemBarsConfigured) {
            Log.d(TAG, "系统栏已配置，跳过重复设置")
            return
        }
        
        try {
            val window = activity.window
            
            // 设置状态栏和导航栏颜色
            window.statusBarColor = Color.BLACK
            window.navigationBarColor = Color.BLACK
            
            // 设置系统栏行为
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val controller = window.insetsController
                controller?.let {
                    it.hide(WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
                    it.setSystemBarsAppearance(
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS,
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS
                    )
                }
            }
            
            isSystemBarsConfigured = true
            Log.d(TAG, "系统栏配置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置系统栏失败: ${e.message}")
        }
    }
    
    /**
     * 应用完全不透明的状态栏颜色以防止HWC LUTs错误
     * 避免使用反射调用不存在的字段，改为直接使用标准API
     */
    fun applySolidStatusBarColor(activity: Activity) {
        try {
            val window = activity.window
            
            // 1. 使用完全不透明的颜色，避免透明效果引起的渲染问题
            window.statusBarColor = Color.BLACK
            
            // 2. 使Window内容延伸到系统栏区域但保持状态栏完全不透明
            WindowCompat.setDecorFitsSystemWindows(window, false)
            
            // 3. 设置状态栏图标为亮色（在黑色背景上）
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = false
            
            // 4. 确保使用安全的状态栏处理方式
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 5. 使用最基本的像素格式避免高级颜色处理可能导致的问题
            window.setFormat(android.graphics.PixelFormat.RGBA_8888)
            
            Log.d(TAG, "已应用完全不透明的状态栏设置")
        } catch (e: Exception) {
            Log.e(TAG, "应用不透明状态栏失败: ${e.message}")
        }
    }
    
    /**
     * 为标准页面（如首页）设置系统栏配置
     * 使用完全不透明的固定状态栏，无动画版本
     */
    fun setupStandardPageSystemBars(activity: Activity) {
        try {
            val window = activity.window
            
            // 设置默认状态栏颜色
            window.statusBarColor = android.graphics.Color.WHITE
            window.navigationBarColor = android.graphics.Color.WHITE
            
            // 确保内容不延伸到系统栏区域
            androidx.core.view.WindowCompat.setDecorFitsSystemWindows(window, true)
            
            // 设置状态栏图标为深色（在白色背景上）
            val controller = androidx.core.view.WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = true
            controller.isAppearanceLightNavigationBars = true
            
            android.util.Log.d(TAG, "已设置标准页面系统栏样式")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "设置标准页面系统栏样式失败: ${e.message}")
        }
    }
    
    /**
     * 为任务页面设置系统栏配置
     * 使用完全不透明的固定状态栏
     */
    fun setupTaskPageSystemBars(activity: Activity) {
        try {
            val window = activity.window
            
            // 设置状态栏为半透明
            window.statusBarColor = android.graphics.Color.parseColor("#DDFFFFFF")
            window.navigationBarColor = android.graphics.Color.WHITE
            
            // 确保内容不延伸到系统栏区域
            androidx.core.view.WindowCompat.setDecorFitsSystemWindows(window, true)
            
            // 设置状态栏图标为深色（在白色背景上）
            val controller = androidx.core.view.WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = true
            controller.isAppearanceLightNavigationBars = true
            
            android.util.Log.d(TAG, "已设置任务页面系统栏样式")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "设置任务页面系统栏样式失败: ${e.message}")
        }
    }
    
    /**
     * 安全地禁用可能导致HWC LUTs错误的功能
     * 这会防止日志中出现的渲染错误
     */
    private fun safeDisableProblematicRendering(window: android.view.Window) {
        try {
            // 1. 避免使用硬件加速的特殊模式，改用标准配置
            window.clearFlags(
                android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
            )

            // 2. 使用安全的FORMAT模式
            window.setFormat(android.graphics.PixelFormat.RGBA_8888)

            // 3. 避免透明度和alpha混合
            val attrs = window.attributes
            attrs.alpha = 1.0f  // 完全不透明
            attrs.dimAmount = 0f  // 禁用背景变暗
            window.attributes = attrs

            // 4. 设置安全的颜色模式
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    window.colorMode = 0 // 使用默认色彩模式
                } catch (e: Exception) {
                    Log.w(TAG, "设置colorMode失败: ${e.message}")
                }
            }

            // 5. 避免直接修改ThreadedRenderer，改用窗口属性
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                try {
                    // 不使用不存在的setBufferSize方法
                    // 改用设置硬件加速相关标志
                    window.setFlags(
                        WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )
                    
                    // 确保使用默认的像素格式
                    window.setFormat(android.graphics.PixelFormat.RGBA_8888)
                    
                    Log.d(TAG, "应用硬件加速优化设置")
                } catch (e: Exception) {
                    Log.w(TAG, "设置硬件加速标志失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "禁用问题渲染功能失败: ${e.message}")
        }
    }
    
    /**
     * 在主要方法失败时应用备用系统栏设置
     * 确保UI不会因为系统栏设置失败而出现问题
     */
    private fun safeFallbackSystemBars(activity: Activity) {
        try {
            val window = activity.window
            
            // 简单设置，避免使用可能失败的高级API
            window.statusBarColor = Color.TRANSPARENT
            
            // 使用更直接的方式设置状态栏图标颜色
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                var flags = window.decorView.systemUiVisibility
                flags = flags or android.view.View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                window.decorView.systemUiVisibility = flags
            }
            
            // 设置一个安全的decorFitsSystemWindows值
            WindowCompat.setDecorFitsSystemWindows(window, true)
            
            Log.d(TAG, "应用了备用系统栏设置")
        } catch (e: Exception) {
            Log.e(TAG, "备用系统栏设置也失败: ${e.message}")
            // 此时不再尝试，让系统使用默认值
        }
    }
    
    /**
     * 重置活动的系统栏设置为默认状态
     */
    fun resetActivitySystemBars(activity: Activity) {
        try {
            val window = activity.window
            
            // 重置状态栏和导航栏颜色
            window.statusBarColor = Color.TRANSPARENT
            window.navigationBarColor = Color.TRANSPARENT
            
            // 重置系统栏行为
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val controller = window.insetsController
                controller?.let {
                    it.show(WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE)
                    it.setSystemBarsAppearance(0, APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS)
                }
            }
            
            isSystemBarsConfigured = false
            Log.d(TAG, "系统栏状态已重置")
        } catch (e: Exception) {
            Log.e(TAG, "重置系统栏状态失败: ${e.message}")
        }
    }
    
    /**
     * 强制刷新系统栏UI，解决某些设备上的状态栏抖动问题
     * 增强版本，强制使用固定不透明颜色，确保跨页面一致性
     */
    fun forceRefreshSystemBars(activity: Activity) {
        try {
            val window = activity.window
            val decorView = window.decorView
            
            // 步骤1: 临时设置完全不透明的黑色状态栏，强制系统重新测量
            window.statusBarColor = Color.BLACK
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 步骤2: 确保内容不延伸到系统栏下，然后再重新设置
            WindowCompat.setDecorFitsSystemWindows(window, true)
            
            // 步骤3: 在下一帧重新应用所需的设置
            decorView.post {
                // 重新启用内容延伸到系统栏
                WindowCompat.setDecorFitsSystemWindows(window, false)
                
                // 恢复原状态栏颜色设置
                window.statusBarColor = Color.TRANSPARENT
                
                // 步骤4: 强制应用一致的状态栏图标颜色
                val controller = WindowCompat.getInsetsController(window, decorView)
                controller.isAppearanceLightStatusBars = lastLightStatusBars
                
                // 步骤5: 强制更新系统栏显示
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // Android 11及以上使用新API
                    decorView.windowInsetsController?.show(WindowInsetsCompat.Type.systemBars())
                } else {
                    // 老版本使用传统方法
                    @Suppress("DEPRECATION")
                    decorView.systemUiVisibility = decorView.systemUiVisibility
                }
                
                // 确保我们不会尝试使用硬件加速的特性
                safeDisableProblematicRendering(window)
                
                Log.d(TAG, "强制刷新系统栏UI完成")
            }
        } catch (e: Exception) {
            Log.e(TAG, "强制刷新系统栏UI失败: ${e.message}")
        }
    }
    
    /**
     * 获取固定高度的状态栏占位空间
     * 使用标准值确保所有页面一致
     */
    fun getFixedStatusBarHeight(): androidx.compose.ui.unit.Dp {
        return androidx.compose.ui.unit.Dp(30f)  // 使用更小的固定值，避免过大的顶部空白
    }
    
    /**
     * 获取标准标题文字大小
     * 确保所有页面标题使用一致的大小
     */
    fun getStandardTitleTextSize(): androidx.compose.ui.unit.TextUnit {
        return 20.sp  // 标准标题文字大小
    }
    
    /**
     * 获取状态栏颜色，确保在所有页面保持一致
     * 返回完全不透明的黑色以避免透明引起的渲染问题
     */
    fun getConsistentStatusBarColor(): Int {
        return Color.BLACK
    }
    
    /**
     * 强制应用完全不透明且固定的状态栏
     * 完全避免任何硬件加速渲染问题和透明引起的闪烁
     * @param activity 当前活动
     * @param lightStatusBar 是否使用浅色状态栏图标，默认为false使用深色图标
     */
    fun forceOpaqueStatusBar(activity: Activity, lightStatusBar: Boolean = false) {
        // 检查是否与上次设置相同且时间间隔小于阈值
        val currentTime = System.currentTimeMillis()
        val lastState = lastStatusBarState
        
        if (lastState != null && 
            lastState.first?.equals(activity) == true && 
            lastState.second == lightStatusBar &&
            currentTime - lastStatusBarChangeTime < STATUS_BAR_CHANGE_DEBOUNCE_MS) {
            // 避免短时间内重复设置相同状态
            Log.d(TAG, "状态栏设置已跳过: 与上次相同且时间间隔小于${STATUS_BAR_CHANGE_DEBOUNCE_MS}ms")
            return
        }
        
        try {
            val window = activity.window
            
            // 1. 设置状态栏颜色 - 根据图标颜色设置对比色背景
            window.statusBarColor = if (lightStatusBar) {
                Color.BLACK  // 使用浅色图标时用黑色背景
            } else {
                Color.WHITE  // 使用深色图标时用白色背景
            }
            
            // 2. 设置导航栏颜色匹配
            window.navigationBarColor = if (lightStatusBar) {
                Color.BLACK
            } else {
                Color.WHITE
            }
            
            // 3. 确保内容不会延伸到系统栏区域
            WindowCompat.setDecorFitsSystemWindows(window, true)
            
            // 4. 设置状态栏图标颜色
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = !lightStatusBar  // !lightStatusBar表示使用深色图标
            controller.isAppearanceLightNavigationBars = !lightStatusBar
            
            // 更新最后设置的状态栏图标模式
            lastLightStatusBars = !lightStatusBar
            
            // 5. 启用绘制系统栏背景标志
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            
            // 6. 清除可能导致不稳定的透明标志
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
            
            // 7. 使用最基本的像素格式以避免HWC LUTs错误
            window.setFormat(android.graphics.PixelFormat.OPAQUE)
            
            // 更新上次设置状态和时间
            lastStatusBarState = Triple(activity, lightStatusBar, !lightStatusBar)
            lastStatusBarChangeTime = currentTime
            
            Log.d(TAG, "已强制应用不透明状态栏，状态栏图标为${if(!lightStatusBar) "深色(黑色)" else "浅色(白色)"}")
        } catch (e: Exception) {
            Log.e(TAG, "设置不透明状态栏失败: ${e.message}")
        }
    }
    
    /**
     * 为我的页面设置系统栏样式
     */
    fun setupProfilePageSystemBars(activity: Activity) {
        val window = activity.window

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+，使用WindowInsetsController
            window.setDecorFitsSystemWindows(false)
            val controller = window.insetsController
            controller?.let {
                // 设置状态栏图标为深色
                it.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                )
                // 设置导航栏图标为深色
                it.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS
                )
            }
        } else {
            // Android 10及以下
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                    or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR)
        }

        // 设置状态栏和导航栏颜色
        window.statusBarColor = safeParseColor("#F8F6F8")
        window.navigationBarColor = safeParseColor("#F8F6F8")
    }
    
    /**
     * 为生理周期页面设置系统栏样式
     */
    fun setupMenstrualCyclePageSystemBars(activity: Activity) {
        val window = activity.window

        // 使用WindowCompat API设置Window行为
        WindowCompat.setDecorFitsSystemWindows(window, false) // 让内容可以延伸到状态栏区域

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+，使用WindowInsetsController
            val controller = window.insetsController
            controller?.let {
                // 设置状态栏图标为深色
                it.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                )
                // 设置导航栏图标为深色
                it.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS
                )
            }
        } else {
            // Android 10及以下
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                    or View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR)
        }

        // 设置状态栏和导航栏颜色为粉色系，但加入一些透明度
        window.statusBarColor = Color.TRANSPARENT // 完全透明的状态栏
        window.navigationBarColor = safeParseColor("#FFF5F7")
        
        // 确保绘制系统栏背景
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
    }
    
    /**
     * 根据主题设置状态栏颜色
     * 默认模式使用白色状态栏和黑色图标
     * 暗黑模式使用黑色状态栏和白色图标
     */
    fun setStatusBarColorByTheme(activity: Activity, isDarkTheme: Boolean) {
        try {
            val window = activity.window
            
            // 设置状态栏颜色
            window.statusBarColor = if (isDarkTheme) {
                Color.BLACK  // 暗黑模式使用黑色状态栏
            } else {
                Color.WHITE  // 默认模式使用白色状态栏
            }
            
            // 设置状态栏图标颜色
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = !isDarkTheme  // 在默认模式下使用深色图标(黑色)，在暗黑模式下使用浅色图标(白色)
            
            // 更新最后设置的状态栏图标模式
            lastLightStatusBars = !isDarkTheme
            
            // 确保Window绘制系统栏背景
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            Log.d(TAG, "已根据主题设置状态栏颜色: ${if (isDarkTheme) "暗黑模式-黑色背景-白色图标" else "默认模式-白色背景-黑色图标"}")
        } catch (e: Exception) {
            Log.e(TAG, "设置状态栏颜色失败: ${e.message}")
        }
    }
    
    /**
     * 设置暗色模式系统栏，适用于黑色背景的页面
     */
    fun setupDarkModeSystemBars(activity: Activity) {
        val window = activity.window
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val controller = WindowCompat.getInsetsController(window, window.decorView)
        controller.isAppearanceLightStatusBars = false
        controller.isAppearanceLightNavigationBars = false
        
        // 设置状态栏和导航栏为透明色
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT
    }
    
    /**
     * 设置浅色系统栏，适用于白色/浅色背景的页面
     */
    fun setupLightModeSystemBars(activity: Activity) {
        val window = activity.window
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val controller = WindowCompat.getInsetsController(window, window.decorView)
        controller.isAppearanceLightStatusBars = true
        controller.isAppearanceLightNavigationBars = true
        
        // 设置状态栏和导航栏为透明色
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT
    }
    
    /**
     * 设置时间追踪页面的系统栏
     * 状态栏为纯白色，与页面顶部背景匹配
     */
    fun setupTimeTrackingSystemBars(activity: Activity) {
        val window = activity.window
        
        try {
            // 1. 设置状态栏为纯白色，与页面顶部背景完全匹配
            window.statusBarColor = android.graphics.Color.WHITE
            // 导航栏依然保持透明
            window.navigationBarColor = android.graphics.Color.TRANSPARENT
            
            // 2. 设置状态栏图标为深色，以便在白色背景上显示
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = true
            controller.isAppearanceLightNavigationBars = true
            
            // 3. 保留窗口标志设置
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            Log.d("SystemBarManager", "时间追踪页面系统栏设置完成：纯白色状态栏")
        } catch (e: Exception) {
            Log.e("SystemBarManager", "设置时间追踪系统栏失败: ${e.message}")
        }
    }
    
    /**
     * 设置统计页面的系统栏
     * 解决状态栏与页面标题栏的重叠问题
     */
    fun setupStatisticsPageSystemBars(activity: Activity) {
        val window = activity.window
        
        try {
            // 设置状态栏为白色，保持与页面顶部颜色一致
            window.statusBarColor = android.graphics.Color.WHITE
            
            // 设置导航栏颜色为白色
            window.navigationBarColor = android.graphics.Color.WHITE
            
            // 确保内容不会延伸到系统栏区域，避免状态栏和内容重叠
            WindowCompat.setDecorFitsSystemWindows(window, true)
            
            // 设置状态栏图标为深色，以便在白色背景上显示
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = true 
            controller.isAppearanceLightNavigationBars = true
            
            // 启用绘制系统栏背景标志
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            
            // 清除可能导致不稳定的透明标志
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
            
            Log.d(TAG, "统计页面系统栏设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置统计页面系统栏失败: ${e.message}")
        }
    }
    
    /**
     * 设置透明系统栏，用于详情页面等需要全屏展示内容的页面
     */
    fun setupTransparentSystemBars(activity: android.app.Activity) {
        try {
            val window = activity.window
            
            // 设置状态栏和导航栏为透明
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT
            
            // 确保内容延伸到系统栏区域
            androidx.core.view.WindowCompat.setDecorFitsSystemWindows(window, false)
            
            // 设置状态栏图标为浅色（在透明背景上）
            val controller = androidx.core.view.WindowCompat.getInsetsController(window, window.decorView)
            controller.isAppearanceLightStatusBars = false
            controller.isAppearanceLightNavigationBars = false
            
            android.util.Log.d(TAG, "已设置透明系统栏")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "设置透明系统栏失败: ${e.message}")
        }
    }
    
    /**
     * 重置系统栏到默认状态
     */
    fun resetSystemBars(activity: android.app.Activity) {
        try {
            val window = activity.window
            
            // 恢复默认系统栏设置
            forceOpaqueStatusBar(activity)
            
            android.util.Log.d(TAG, "已重置系统栏到默认状态")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "重置系统栏失败: ${e.message}")
        }
    }
} 