package com.timeflow.app.ui.viewmodel

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.timeflow.app.data.ai.model.AiSettings
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject

// 创建DataStore实例
val Context.aiSettingsDataStore: DataStore<Preferences> by preferencesDataStore(name = "ai_settings_preferences")

// 定义DataStore的key
private val AI_SETTINGS_KEY = stringPreferencesKey("ai_settings")

/**
 * AI设置ViewModel，用于管理AI助手的个性化设置
 */
@HiltViewModel
class AiSettingsViewModel @Inject constructor() : ViewModel() {

    // 当前设置
    private val _aiSettings = MutableStateFlow(AiSettings())
    val aiSettings: StateFlow<AiSettings> = _aiSettings.asStateFlow()
    
    // Gson实例用于JSON序列化
    private val gson = Gson()
    
    /**
     * 加载AI设置
     */
    fun loadAiSettings(context: Context) {
        viewModelScope.launch {
            try {
                // 从DataStore读取设置
                val settingsJson = context.aiSettingsDataStore.data.firstOrNull()?.get(AI_SETTINGS_KEY)
                if (settingsJson != null) {
                    // 解析JSON为AiSettings对象
                    val settings = gson.fromJson(settingsJson, AiSettings::class.java)
                    _aiSettings.value = settings
                }
            } catch (e: Exception) {
                // 出错时使用默认设置
                _aiSettings.value = AiSettings()
            }
        }
    }
    
    /**
     * 保存AI设置
     */
    fun saveAiSettings(context: Context, settings: AiSettings) {
        viewModelScope.launch {
            try {
                // 将设置转换为JSON字符串
                val settingsJson = gson.toJson(settings)
                
                // 保存到DataStore
                context.aiSettingsDataStore.edit { preferences ->
                    preferences[AI_SETTINGS_KEY] = settingsJson
                }
                
                // 更新当前状态
                _aiSettings.value = settings
            } catch (e: Exception) {
                // 错误处理
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 更新助手名称
     */
    fun updateAssistantName(context: Context, name: String) {
        val currentSettings = _aiSettings.value
        val updatedSettings = currentSettings.copy(
            assistantName = name,
            updatedAt = System.currentTimeMillis()
        )
        saveAiSettings(context, updatedSettings)
    }
    
    /**
     * 更新助手头像
     */
    fun updateAssistantAvatar(context: Context, avatar: String) {
        val currentSettings = _aiSettings.value
        val updatedSettings = currentSettings.copy(
            assistantAvatar = avatar,
            customAvatarUri = null, // 重置自定义头像
            updatedAt = System.currentTimeMillis()
        )
        saveAiSettings(context, updatedSettings)
    }
    
    /**
     * 更新自定义头像
     */
    fun updateCustomAvatar(context: Context, imageUri: String) {
        val currentSettings = _aiSettings.value
        val updatedSettings = currentSettings.copy(
            customAvatarUri = imageUri,
            updatedAt = System.currentTimeMillis()
        )
        saveAiSettings(context, updatedSettings)
    }
    
    /**
     * 更新提示消息前的emoji
     */
    fun updatePromptEmoji(context: Context, emoji: String) {
        val currentSettings = _aiSettings.value
        val updatedSettings = currentSettings.copy(
            promptEmoji = emoji,
            updatedAt = System.currentTimeMillis()
        )
        saveAiSettings(context, updatedSettings)
    }
    
    /**
     * 重置为默认设置
     */
    fun resetToDefault(context: Context) {
        saveAiSettings(context, AiSettings())
    }
} 