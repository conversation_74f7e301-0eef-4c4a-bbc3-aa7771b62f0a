package com.timeflow.app.ui.statistics

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.time.LocalDate
import javax.inject.Inject

/**
 * 时间统计页面的ViewModel
 */
@HiltViewModel
class TimeStatisticsViewModel @Inject constructor(
    // 这里注入相关的repository
) : ViewModel() {
    // 选择的时间范围
    private val _selectedTimeRange = MutableStateFlow("本周")
    val selectedTimeRange: StateFlow<String> = _selectedTimeRange
    
    // 选择的开始日期和结束日期
    private val _startDate = MutableStateFlow(LocalDate.now().minusDays(7))
    val startDate: StateFlow<LocalDate> = _startDate
    
    private val _endDate = MutableStateFlow(LocalDate.now())
    val endDate: StateFlow<LocalDate> = _endDate
    
    // 设置时间范围
    fun setTimeRange(range: String) {
        _selectedTimeRange.value = range
        
        // 根据选择的时间范围更新开始和结束日期
        val today = LocalDate.now()
        when (range) {
            "今日" -> {
                _startDate.value = today
                _endDate.value = today
            }
            "本周" -> {
                _startDate.value = today.minusDays(today.dayOfWeek.value - 1L)
                _endDate.value = today
            }
            "本月" -> {
                _startDate.value = today.withDayOfMonth(1)
                _endDate.value = today
            }
            "季度" -> {
                val month = today.monthValue
                val quarterStartMonth = when {
                    month <= 3 -> 1
                    month <= 6 -> 4
                    month <= 9 -> 7
                    else -> 10
                }
                _startDate.value = today.withMonth(quarterStartMonth).withDayOfMonth(1)
                _endDate.value = today
            }
            "年度" -> {
                _startDate.value = today.withDayOfYear(1)
                _endDate.value = today
            }
        }
        
        // 这里可以添加加载数据的逻辑
        loadStatisticsData()
    }
    
    // 加载统计数据
    private fun loadStatisticsData() {
        // 这里实现加载统计数据的逻辑
        // 可以调用相关的repository方法获取数据
    }
    
    // 初始化加载
    init {
        setTimeRange("本周")
    }
} 