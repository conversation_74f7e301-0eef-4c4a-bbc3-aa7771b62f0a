# 任务时间管理系统架构设计

## 🎯 设计目标

从全栈工程师角度重新设计任务时间的存储、修改、同步功能，解决TaskDetailBottomSheet和TaskListFullScreen之间的时间同步问题，同时保留现有UI布局。

## 🏗️ 系统架构

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                             │
│  ┌─────────────────────┐    ┌─────────────────────────────┐ │
│  │ TaskListFullScreen  │    │ TaskDetailBottomSheet       │ │
│  └─────────────────────┘    └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    ViewModel Layer                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              TaskTimeViewModel                          │ │
│  │  • 状态管理                                              │ │
│  │  • 事件分发                                              │ │
│  │  • UI逻辑协调                                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Domain Layer                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               TaskTimeUseCase                           │ │
│  │  • 业务逻辑封装                                          │ │
│  │  • 数据验证                                              │ │
│  │  • 事件管理                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Data Layer                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │             TaskTimeRepository                          │ │
│  │  • 数据持久化                                            │ │
│  │  • 缓存管理                                              │ │
│  │  • 冲突检测                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Model Layer                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 TaskTime                                │ │
│  │  • 统一数据模型                                          │ │
│  │  • 业务计算                                              │ │
│  │  • 版本控制                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件

### 1. TaskTime (数据模型)

**职责**: 统一的任务时间数据结构
**特性**:
- 包含开始时间、结束时间、截止时间等完整时间信息
- 内置业务计算方法（剩余天数、是否过期等）
- 版本控制支持乐观锁
- 智能时间优先级处理

```kotlin
data class TaskTime(
    val taskId: String,
    val startTime: LocalDateTime? = null,
    val endTime: LocalDateTime? = null,
    val dueDate: LocalDateTime? = null,
    val reminderTime: LocalDateTime? = null,
    val version: Long = 1
) {
    val effectiveTime: LocalDateTime? get() = startTime ?: dueDate
    val daysLeft: Int get() = /* 计算逻辑 */
    val isOverdue: Boolean get() = /* 判断逻辑 */
}
```

### 2. TaskTimeRepository (数据访问层)

**职责**: 数据持久化和缓存管理
**特性**:
- 双层缓存策略（内存 + 数据库）
- 批量操作优化
- 自动冲突检测
- 事务安全保证

**核心方法**:
```kotlin
suspend fun updateTaskTime(taskId: String, ...): Result<TaskTime>
suspend fun getTaskTime(taskId: String): TaskTime?
suspend fun getTaskTimes(taskIds: List<String>): Map<String, TaskTime>
```

### 3. TaskTimeUseCase (业务逻辑层)

**职责**: 封装复杂业务规则
**特性**:
- 数据验证和业务规则检查
- 智能时间建议
- 冲突检测和解决
- 事件驱动的状态同步

**核心方法**:
```kotlin
suspend fun updateTaskTime(...): Result<TaskTime>
suspend fun setTaskDueDate(...): Result<TaskTime>
suspend fun setTaskTimeRange(...): Result<TaskTime>
fun observeTimeUpdates(): Flow<TaskTime>
```

### 4. TaskTimeViewModel (UI状态管理)

**职责**: UI层状态管理和事件协调
**特性**:
- 响应式状态管理
- 事件分发和转换
- 错误处理和用户反馈
- 批量操作支持

**核心方法**:
```kotlin
fun updateTaskTime(...)
fun setDueDate(...)
fun setTimeRange(...)
val timeUpdateEvents: SharedFlow<TaskTimeUpdateEvent>
```

## 🔄 数据流设计

### 时间更新流程

```
用户操作 (TaskDetailBottomSheet)
    ↓
TaskTimeViewModel.updateTaskTime()
    ↓
TaskTimeUseCase.updateTaskTime()
    ↓ (验证 + 业务规则)
TaskTimeRepository.updateTaskTime()
    ↓ (缓存 + 数据库)
发送 TaskTimeUpdateEvent
    ↓
TaskTimeViewModel 监听事件
    ↓
更新 UI 状态 (TaskListFullScreen)
    ↓
UI 自动重组显示最新时间
```

### 事件驱动同步

```kotlin
// 事件定义
data class TaskTimeUpdateEvent(
    val taskId: String,
    val oldTime: TaskTime?,
    val newTime: TaskTime,
    val source: String,
    val timestamp: Long
)

// 事件流
Repository → UseCase → ViewModel → UI Components
```

## 🚀 关键特性

### 1. 立即响应 + 最终一致性

- **缓存优先**: 更新立即反映在UI上
- **异步持久化**: 数据库操作不阻塞UI
- **事件驱动**: 自动同步所有相关组件

### 2. 智能冲突处理

- **自动检测**: 时间重叠、冲突检测
- **用户选择**: 提供冲突解决选项
- **优雅降级**: 冲突时不阻止操作

### 3. 性能优化

- **批量操作**: 支持多任务时间批量更新
- **智能缓存**: LRU缓存策略，减少数据库访问
- **懒加载**: 按需加载时间数据

### 4. 错误恢复

- **乐观锁**: 防止并发更新冲突
- **重试机制**: 网络错误自动重试
- **回滚支持**: 失败时恢复到之前状态

## 🔧 集成方式

### UI层集成

```kotlin
// TaskListFullScreen
@Composable
fun TaskListFullScreen(
    taskTimeViewModel: TaskTimeViewModel = hiltViewModel()
) {
    // 监听时间更新事件
    LaunchedEffect(Unit) {
        taskTimeViewModel.timeUpdateEvents.collect { event ->
            // 更新UI状态
        }
    }
}

// TaskDetailBottomSheet
@Composable
fun TaskDetailBottomSheet(
    taskTimeViewModel: TaskTimeViewModel = hiltViewModel()
) {
    // 时间更新操作
    taskTimeViewModel.updateTaskTime(...)
}
```

### 依赖注入配置

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object TaskTimeModule {
    @Provides @Singleton
    fun provideTaskTimeRepository(): TaskTimeRepository
    
    @Provides @Singleton
    fun provideTaskTimeUseCase(repository: TaskTimeRepository): TaskTimeUseCase
}
```

## 📊 性能指标

### 响应时间目标
- UI更新响应: < 16ms (60fps)
- 缓存访问: < 1ms
- 数据库操作: < 100ms
- 跨组件同步: < 50ms

### 内存使用
- 时间缓存: 最大1000个任务
- 事件队列: 最大100个事件
- 自动清理: 30分钟无访问自动清理

## 🧪 测试策略

### 单元测试
- TaskTime业务逻辑测试
- Repository缓存逻辑测试
- UseCase业务规则测试
- ViewModel状态管理测试

### 集成测试
- 端到端时间同步测试
- 并发更新测试
- 错误恢复测试
- 性能压力测试

## 🔮 未来扩展

### 智能功能
- AI时间建议
- 习惯学习
- 自动调度优化

### 高级特性
- 离线支持
- 多设备同步
- 团队协作
- 时间统计分析

## 📝 迁移计划

### 阶段1: 核心架构
- ✅ 创建TaskTime模型
- ✅ 实现TaskTimeRepository
- ✅ 开发TaskTimeUseCase
- ✅ 构建TaskTimeViewModel

### 阶段2: UI集成
- ✅ 修改TaskListFullScreen
- ✅ 更新TaskDetailBottomSheet
- ⏳ 测试时间同步功能
- ⏳ 性能优化

### 阶段3: 高级功能
- ⏳ 冲突检测实现
- ⏳ 智能建议功能
- ⏳ 批量操作支持
- ⏳ 错误恢复机制

## 🎉 预期效果

1. **完美同步**: TaskDetailBottomSheet和TaskListFullScreen时间完全同步
2. **性能提升**: 响应速度提升50%，内存使用减少30%
3. **用户体验**: 无感知的实时更新，流畅的交互体验
4. **代码质量**: 清晰的架构分层，易于维护和扩展
5. **稳定性**: 99.9%的操作成功率，自动错误恢复 