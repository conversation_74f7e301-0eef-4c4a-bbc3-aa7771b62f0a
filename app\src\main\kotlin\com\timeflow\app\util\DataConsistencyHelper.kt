package com.timeflow.app.util

import android.util.Log
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.repository.TaskRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 数据一致性辅助工具
 * 提供简单的接口来触发数据修复
 */
object DataConsistencyHelper {
    
    private const val TAG = "DataConsistencyHelper"
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * 一键修复指定任务的数据不一致问题
     * 
     * @param taskId 任务ID
     * @param taskDao TaskDao实例
     * @param taskRepository TaskRepository实例
     */
    fun fixTaskDataInconsistency(
        taskId: String,
        taskDao: TaskDao,
        taskRepository: TaskRepository
    ) {
        scope.launch {
            try {
                Log.i(TAG, "开始修复任务 $taskId 的数据不一致问题...")
                
                val fixer = DataConsistencyFixer(taskDao, taskRepository)
                val fixed = fixer.checkAndFixTaskConsistency(taskId)
                
                if (fixed) {
                    Log.i(TAG, "✓ 任务 $taskId 数据不一致问题修复成功")
                } else {
                    Log.i(TAG, "任务 $taskId 数据一致性正常，无需修复")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "修复任务 $taskId 数据不一致问题时发生错误", e)
            }
        }
    }
    
    /**
     * 一键修复所有任务的数据不一致问题
     * 
     * @param taskDao TaskDao实例
     * @param taskRepository TaskRepository实例
     */
    fun fixAllTasksDataInconsistency(
        taskDao: TaskDao,
        taskRepository: TaskRepository
    ) {
        scope.launch {
            try {
                Log.i(TAG, "开始修复所有任务的数据不一致问题...")
                
                val fixer = DataConsistencyFixer(taskDao, taskRepository)
                val fixedCount = fixer.fixAllTasksHasSubtasksFlag()
                
                Log.i(TAG, "✓ 数据一致性修复完成，共修复 $fixedCount 个任务")
                
            } catch (e: Exception) {
                Log.e(TAG, "修复所有任务数据不一致问题时发生错误", e)
            }
        }
    }
    
    /**
     * 生成数据一致性报告
     * 
     * @param taskDao TaskDao实例
     * @param taskRepository TaskRepository实例
     */
    fun generateDataConsistencyReport(
        taskDao: TaskDao,
        taskRepository: TaskRepository
    ) {
        scope.launch {
            try {
                Log.i(TAG, "开始生成数据一致性报告...")
                
                val fixer = DataConsistencyFixer(taskDao, taskRepository)
                val report = fixer.generateConsistencyReport()
                
                Log.i(TAG, "=== 数据一致性报告 ===")
                Log.i(TAG, "不一致任务数量: ${report.totalInconsistentTasks}")
                Log.i(TAG, "检查错误任务数量: ${report.totalErrorTasks}")
                
                if (report.inconsistentTasks.isNotEmpty()) {
                    Log.w(TAG, "发现以下不一致任务:")
                    report.inconsistentTasks.forEach { task ->
                        Log.w(TAG, "- 任务: ${task.title} (${task.taskId})")
                        Log.w(TAG, "  记录的hasSubtasks: ${task.recordedHasSubtasks}")
                        Log.w(TAG, "  实际子任务数量: ${task.actualSubtaskCount}")
                    }
                }
                
                if (report.errorTasks.isNotEmpty()) {
                    Log.e(TAG, "检查时出错的任务:")
                    report.errorTasks.forEach { task ->
                        Log.e(TAG, "- 任务: ${task.title} (${task.taskId})")
                        Log.e(TAG, "  错误: ${task.error}")
                    }
                }
                
                Log.i(TAG, "=== 报告生成完成 ===")
                
            } catch (e: Exception) {
                Log.e(TAG, "生成数据一致性报告时发生错误", e)
            }
        }
    }
} 