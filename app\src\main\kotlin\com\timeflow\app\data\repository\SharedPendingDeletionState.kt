package com.timeflow.app.data.repository

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import javax.inject.Singleton
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.Instant
import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

/**
 * 🗑️ 待删除任务数据类 - 可序列化版本
 */
@Serializable
data class PendingDeletion(
    val taskId: String,
    val taskTitle: String,
    val deletionTimeEpochMilli: Long,
    val timeoutMs: Long = 60_000L // 60秒后永久删除
) {
    fun isExpired(): Boolean {
        return Instant.now().toEpochMilli() > (deletionTimeEpochMilli + timeoutMs)
    }
    
    fun remainingTimeMs(): Long {
        val expirationTime = deletionTimeEpochMilli + timeoutMs
        return maxOf(0, expirationTime - Instant.now().toEpochMilli())
    }
    
    fun getDeletionTime(): Instant {
        return Instant.ofEpochMilli(deletionTimeEpochMilli)
    }
}

/**
 * 🌐 全局待删除状态管理器 - 持久化版本
 * 
 * 解决页面切换和App重启时待删除状态丢失的问题。
 * 使用SharedPreferences进行持久化存储。
 */
@Singleton
class SharedPendingDeletionState @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository
) {
    
    companion object {
        private const val TAG = "SharedPendingDeletionState"
        private const val PREFS_NAME = "pending_deletions"
        private const val KEY_PENDING_DELETIONS = "pending_deletions_data"
    }
    
    // 协程作用域，用于管理定时器
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    // SharedPreferences实例
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // JSON序列化器
    private val json = Json { 
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // 待删除任务状态
    private val _pendingDeletions = MutableStateFlow<Map<String, PendingDeletion>>(emptyMap())
    val pendingDeletions: StateFlow<Map<String, PendingDeletion>> = _pendingDeletions.asStateFlow()
    
    init {
        // 初始化时从持久化存储中恢复状态
        loadPendingDeletionsFromStorage()
        
        // 🔧 App启动时立即清理过期的待删除任务，确保重启后已删除的任务不会重新出现
        scope.launch {
            // 短暂延迟确保TaskRepository已完全初始化
            delay(500)
            
            Log.d(TAG, "🔧 App启动时清理过期待删除任务...")
            cleanupExpiredDeletions()
        }
        
        // 启动后台任务，定期清理过期的待删除任务
        scope.launch {
            while (true) {
                delay(10_000) // 每10秒检查一次
                cleanupExpiredDeletions()
            }
        }
    }
    
    /**
     * 📁 从SharedPreferences加载待删除状态
     */
    private fun loadPendingDeletionsFromStorage() {
        try {
            val jsonString = prefs.getString(KEY_PENDING_DELETIONS, null)
            if (!jsonString.isNullOrEmpty()) {
                val deletionsMap = json.decodeFromString<Map<String, PendingDeletion>>(jsonString)
                
                // 过滤掉已过期的任务
                val validDeletions = deletionsMap.filter { !it.value.isExpired() }
                
                _pendingDeletions.value = validDeletions
                
                Log.d(TAG, "📁 从存储中恢复 ${validDeletions.size} 个待删除任务")
                
                // 为每个恢复的任务重新启动定时器
                validDeletions.values.forEach { pendingDeletion ->
                    val remainingTime = pendingDeletion.remainingTimeMs()
                    if (remainingTime > 0) {
                        scope.launch {
                            delay(remainingTime)
                            cleanupExpiredDeletion(pendingDeletion.taskId)
                        }
                        Log.d(TAG, "⏰ 为任务 ${pendingDeletion.taskId} 重新启动定时器，剩余时间: ${remainingTime}ms")
                    }
                }
            } else {
                Log.d(TAG, "📁 未找到待删除任务存储数据")
            }
        } catch (e: Exception) {
            Log.e(TAG, "📁 加载待删除状态失败", e)
            _pendingDeletions.value = emptyMap()
        }
    }
    
    /**
     * 💾 保存待删除状态到SharedPreferences
     */
    private fun savePendingDeletionsToStorage() {
        try {
            val jsonString = json.encodeToString(_pendingDeletions.value)
            prefs.edit()
                .putString(KEY_PENDING_DELETIONS, jsonString)
                .apply()
            
            Log.d(TAG, "💾 已保存 ${_pendingDeletions.value.size} 个待删除任务到存储")
        } catch (e: Exception) {
            Log.e(TAG, "💾 保存待删除状态失败", e)
        }
    }
    
    /**
     * 🗑️ 标记任务为待删除状态
     */
    fun markForDeletion(taskId: String, taskTitle: String) {
        Log.d(TAG, "🗑️ 标记任务为待删除: $taskId ($taskTitle)")
        
        val pendingDeletion = PendingDeletion(
            taskId = taskId,
            taskTitle = taskTitle,
            deletionTimeEpochMilli = Instant.now().toEpochMilli()
        )
        
        _pendingDeletions.update { current ->
            current + (taskId to pendingDeletion)
        }
        
        // 立即持久化
        savePendingDeletionsToStorage()
        
        // 启动定时器，60秒后自动清理
        scope.launch {
            delay(pendingDeletion.timeoutMs)
            cleanupExpiredDeletion(taskId)
        }
        
        Log.d(TAG, "✅ 任务已标记为待删除，60秒后将自动清理: $taskId")
    }
    
    /**
     * 🔄 撤销删除操作
     */
    fun undoDeletion(taskId: String): Boolean {
        val pendingDeletion = _pendingDeletions.value[taskId]
        if (pendingDeletion != null && !pendingDeletion.isExpired()) {
            _pendingDeletions.update { current ->
                current - taskId
            }
            
            // 立即持久化
            savePendingDeletionsToStorage()
            
            Log.d(TAG, "🔄 已撤销删除操作: $taskId (${pendingDeletion.taskTitle})")
            return true
        }
        Log.w(TAG, "⚠️ 无法撤销删除，任务不存在或已过期: $taskId")
        return false
    }
    
    /**
     * 🧹 清理过期的待删除任务
     */
    fun cleanupExpiredDeletions() {
        scope.launch {
            val currentTime = Instant.now()
            val expiredTasks = mutableListOf<String>()
            
            _pendingDeletions.value.forEach { (taskId, pendingDeletion) ->
                if (pendingDeletion.isExpired()) {
                    expiredTasks.add(taskId)
                    Log.d(TAG, "🧹 发现过期待删除任务: $taskId (${pendingDeletion.taskTitle})")
                }
            }
            
            if (expiredTasks.isNotEmpty()) {
                // 先从状态中移除
                _pendingDeletions.update { current ->
                    current.filterKeys { !expiredTasks.contains(it) }
                }
                
                // 立即持久化
                savePendingDeletionsToStorage()
                
                // 🗑️ 真正从数据库中删除任务
                expiredTasks.forEach { taskId ->
                    try {
                        taskRepository.deleteTask(taskId)
                        Log.d(TAG, "🗑️ 已从数据库中永久删除任务: $taskId")
                    } catch (e: Exception) {
                        Log.e(TAG, "🗑️ 永久删除任务失败: $taskId", e)
                    }
                }
                
                Log.d(TAG, "🧹 已清理 ${expiredTasks.size} 个过期待删除任务")
            }
        }
    }
    
    /**
     * 🧹 清理指定的过期删除任务
     */
    private fun cleanupExpiredDeletion(taskId: String) {
        scope.launch {
            val pendingDeletion = _pendingDeletions.value[taskId]
            if (pendingDeletion != null && pendingDeletion.isExpired()) {
                // 先从状态中移除
                _pendingDeletions.update { current ->
                    current - taskId
                }
                
                // 立即持久化
                savePendingDeletionsToStorage()
                
                // 🗑️ 真正从数据库中删除任务
                try {
                    taskRepository.deleteTask(taskId)
                    Log.d(TAG, "🗑️ 已从数据库中永久删除任务: $taskId (${pendingDeletion.taskTitle})")
                } catch (e: Exception) {
                    Log.e(TAG, "🗑️ 永久删除任务失败: $taskId", e)
                }
                
                Log.d(TAG, "🧹 自动清理过期待删除任务: $taskId (${pendingDeletion.taskTitle})")
            }
        }
    }
    
    /**
     * 🔍 检查任务是否处于待删除状态
     */
    fun isPendingDeletion(taskId: String): Boolean {
        val pendingDeletion = _pendingDeletions.value[taskId]
        return pendingDeletion != null && !pendingDeletion.isExpired()
    }
    
    /**
     * 📊 获取待删除任务数量
     */
    fun getPendingDeletionCount(): Int {
        return _pendingDeletions.value.count { !it.value.isExpired() }
    }
    
    /**
     * 🗑️ 清空所有待删除任务（用于测试或重置）
     */
    fun clearAllPendingDeletions() {
        val currentPending = _pendingDeletions.value
        if (currentPending.isNotEmpty()) {
            Log.d(TAG, "🗑️ 清空所有待删除任务: ${currentPending.size} 个")
            _pendingDeletions.value = emptyMap()
            
            // 立即持久化
            savePendingDeletionsToStorage()
        }
    }
    
    /**
     * 📋 获取所有待删除任务的详细信息（用于调试）
     */
    fun getDebugInfo(): String {
        val pending = _pendingDeletions.value
        if (pending.isEmpty()) {
            return "无待删除任务"
        }
        
        return buildString {
            appendLine("待删除任务列表 (${pending.size}个):")
            pending.forEach { (taskId, deletion) ->
                val remainingTime = deletion.remainingTimeMs() / 1000
                appendLine("- $taskId (${deletion.taskTitle}): 剩余${remainingTime}秒")
            }
        }
    }
} 