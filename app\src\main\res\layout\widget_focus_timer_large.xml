<?xml version="1.0" encoding="utf-8"?>
<!-- 专注计时器小组件 (4x2) - 大尺寸 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_focus_background"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- 顶部任务信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <!-- 状态指示器 -->
        <View
            android:id="@+id/widget_status_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/widget_status_idle"
            android:layout_marginEnd="8dp" />

        <!-- 任务名称 -->
        <TextView
            android:id="@+id/widget_task_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择任务开始专注"
            android:textSize="16sp"
            android:textColor="@color/widget_focus_text_primary_light"
            android:fontFamily="sans-serif-medium"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- 中央时间显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 时间显示 -->
        <TextView
            android:id="@+id/widget_timer_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="25:00"
            android:textSize="48sp"
            android:textStyle="bold"
            android:textColor="@color/widget_focus_text_primary_light"
            android:fontFamily="sans-serif-light" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/widget_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="200dp"
            android:layout_height="8dp"
            android:layout_marginTop="12dp"
            android:progress="0"
            android:max="100"
            android:progressTint="@color/widget_accent_blue"
            android:progressBackgroundTint="#E0E0E0" />

    </LinearLayout>

    <!-- 底部控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp">

        <!-- 播放/暂停按钮 -->
        <ImageButton
            android:id="@+id/widget_play_pause_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_play"
            android:background="@drawable/widget_button_background"
            android:scaleType="centerInside"
            android:layout_marginEnd="16dp"
            android:contentDescription="播放/暂停" />

        <!-- 停止按钮 -->
        <ImageButton
            android:id="@+id/widget_stop_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_stop"
            android:background="@drawable/widget_button_background"
            android:scaleType="centerInside"
            android:contentDescription="停止" />

    </LinearLayout>

    <!-- 底部统计信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="12dp"
        android:padding="8dp"
        android:background="@drawable/widget_stats_background">

        <!-- 今日专注次数 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/widget_session_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/widget_accent_blue"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专注次数"
                android:textSize="12sp"
                android:textColor="@color/widget_focus_text_secondary_light"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- 今日总时长 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/widget_today_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2h 15m"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/widget_accent_blue"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="今日总时长"
                android:textSize="12sp"
                android:textColor="@color/widget_focus_text_secondary_light"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
