package com.timeflow.app.ui.timetracking

import android.app.Activity
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 时间追踪页面
 * 参考网易云音乐播放页面的设计概念，提供高效直观的时间计时体验
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeTrackingScreen(
    navController: NavController,
    viewModel: TimeTrackingViewModel = hiltViewModel()
) {
    // 状态
    val timerState by viewModel.timerState.collectAsState()
    val currentTask by viewModel.currentTask.collectAsState()
    val elapsedTime by viewModel.elapsedTime.collectAsState()
    val focusModeEnabled by viewModel.focusModeEnabled.collectAsState()

    // 颜色设置
    val backgroundColor = Color(0xFFF8F8F8)
    val primaryColor = Color(0xFF6571FF) // 主题蓝色，与目标管理页面一致
    val cardBackgroundColor = Color.White

    // 获取上下文和Activity引用
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it, false)  // 白色状态栏和深色图标
        }
    }
    
    // 动画效果
    val rotation = remember { Animatable(0f) }
    val scope = rememberCoroutineScope()
    
    // 当计时器运行时，添加旋转动画
    LaunchedEffect(timerState) {
        if (timerState == TimerState.RUNNING) {
            scope.launch {
                // 缓慢旋转动画，模拟唱片旋转效果
                animate(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(60000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    )
                ) { value, _ ->
                    rotation.snapTo(value)
                }
            }
        }
    }

    // 主界面结构
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { Text("时间追踪") },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { viewModel.toggleFocusMode() }) {
                    Icon(
                        imageVector = if (focusModeEnabled) Icons.Filled.Visibility else Icons.Filled.VisibilityOff,
                        contentDescription = if (focusModeEnabled) "退出专注模式" else "进入专注模式"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor,
                titleContentColor = Color.Black,
                actionIconContentColor = Color.Black,
                navigationIconContentColor = Color.Black
            )
        )
        
        // 主要内容区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentAlignment = Alignment.Center
        ) {
            // 大型圆形进度指示器 - 类似专辑封面
            Box(
                modifier = Modifier
                    .size(280.dp)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                primaryColor.copy(alpha = 0.2f),
                                primaryColor.copy(alpha = 0.1f)
                            )
                        )
                    )
                    .border(
                        width = 4.dp,
                        brush = Brush.sweepGradient(
                            colors = listOf(
                                primaryColor,
                                primaryColor.copy(alpha = 0.3f),
                                primaryColor.copy(alpha = 0.1f),
                                primaryColor
                            )
                        ),
                        shape = CircleShape
                    )
                    .rotate(rotation.value) // 添加旋转动画，类似唱片旋转
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                // 内部白色圆圈 - 类似CD中心区域
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(80.dp)
                        .clip(CircleShape)
                        .background(cardBackgroundColor),
                    contentAlignment = Alignment.Center
                ) {
                    // 中央显示当前计时
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = formatElapsedTime(elapsedTime),
                            style = MaterialTheme.typography.displayLarge.copy(fontSize = 38.sp),
                            fontWeight = FontWeight.Bold,
                            color = primaryColor
                        )
                    }
                }
            }
            
            // 如果有当前任务，显示任务标题
            currentTask?.let { task ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(top = 16.dp)
                ) {
                    Text(
                        text = task.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = task.description.takeIf { it.isNotEmpty() } ?: "专注计时中...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray
                    )
                }
            }
            
            // 当前活动信息卡片（底部）
            TimeActivityCard(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp, start = 16.dp, end = 16.dp),
                highEfficiencyHours = 0.0f,
                neutralHours = 8.0f,
                distractionHours = 0.0f
            )
        }
        
        // 底部控制栏
        BottomControlBar(
            timerState = timerState,
            onStartClick = { viewModel.startTimer() },
            onPauseClick = { viewModel.pauseTimer() },
            onStopClick = { viewModel.stopTimer() },
            primaryColor = primaryColor
        )
    }
}

@Composable
fun BottomControlBar(
    timerState: TimerState,
    onStartClick: () -> Unit,
    onPauseClick: () -> Unit,
    onStopClick: () -> Unit,
    primaryColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .background(Color.White)
            .padding(horizontal = 24.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧按钮 - 任务选择
        IconButton(
            onClick = { /* 打开任务选择 */ }
        ) {
            Icon(
                imageVector = Icons.Default.List,
                contentDescription = "任务列表",
                tint = Color.Gray,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 中央大按钮 - 开始/暂停
        FloatingActionButton(
            onClick = {
                when (timerState) {
                    TimerState.IDLE, TimerState.PAUSED -> onStartClick()
                    TimerState.RUNNING -> onPauseClick()
                }
            },
            containerColor = primaryColor,
            contentColor = Color.White,
            modifier = Modifier.size(64.dp)
        ) {
            Icon(
                imageVector = if (timerState == TimerState.RUNNING) 
                    Icons.Default.Pause else Icons.Default.PlayArrow,
                contentDescription = if (timerState == TimerState.RUNNING) "暂停" else "开始",
                modifier = Modifier.size(32.dp)
            )
        }
        
        // 右侧按钮 - 结束
        IconButton(
            onClick = onStopClick,
            enabled = timerState != TimerState.IDLE
        ) {
            Icon(
                imageVector = Icons.Default.Stop,
                contentDescription = "结束",
                tint = if (timerState != TimerState.IDLE) Color.Gray else Color.Gray.copy(alpha = 0.3f),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

@Composable
fun TimeActivityCard(
    modifier: Modifier = Modifier,
    highEfficiencyHours: Float,
    neutralHours: Float,
    distractionHours: Float
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "今日活动摘要",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ActivityMetric(
                    value = "${highEfficiencyHours}小时",
                    label = "高效时间",
                    dotColor = Color(0xFF4CAF50)
                )
                
                ActivityMetric(
                    value = "${neutralHours}小时",
                    label = "中性时间",
                    dotColor = Color(0xFF2196F3)
                )
                
                ActivityMetric(
                    value = "${distractionHours}小时",
                    label = "分心时间",
                    dotColor = Color(0xFFF44336)
                )
            }
        }
    }
}

@Composable
fun ActivityMetric(
    value: String,
    label: String,
    dotColor: Color
) {
    Column(horizontalAlignment = Alignment.Start) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(dotColor, CircleShape)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

// ViewModel 类
class TimeTrackingViewModel : ViewModel() {
    
    // 计时器状态
    private val _timerState = MutableStateFlow(TimerState.IDLE)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    // 当前任务
    private val _currentTask = MutableStateFlow<Task?>(Task("测试任务", "这是一个测试任务的描述"))
    val currentTask: StateFlow<Task?> = _currentTask.asStateFlow()
    
    // 计时时间（秒）
    private val _elapsedTime = MutableStateFlow(0L)
    val elapsedTime: StateFlow<Long> = _elapsedTime.asStateFlow()
    
    // 专注模式
    private val _focusModeEnabled = MutableStateFlow(false)
    val focusModeEnabled: StateFlow<Boolean> = _focusModeEnabled.asStateFlow()
    
    // 计时器作业
    private var timerJob: Job? = null
    
    // 开始计时
    fun startTimer() {
        if (_timerState.value != TimerState.RUNNING) {
            _timerState.value = TimerState.RUNNING
            timerJob?.cancel()
            timerJob = viewModelScope.launch {
                while (true) {
                    delay(1000)
                    _elapsedTime.value++
                }
            }
        }
    }
    
    // 暂停计时
    fun pauseTimer() {
        if (_timerState.value == TimerState.RUNNING) {
            _timerState.value = TimerState.PAUSED
            timerJob?.cancel()
        }
    }
    
    // 停止计时
    fun stopTimer() {
        _timerState.value = TimerState.IDLE
        timerJob?.cancel()
        _elapsedTime.value = 0
    }
    
    // 切换专注模式
    fun toggleFocusMode() {
        _focusModeEnabled.value = !_focusModeEnabled.value
    }
    
    // 清理资源
    override fun onCleared() {
        super.onCleared()
        timerJob?.cancel()
    }
}

// 计时器状态
enum class TimerState {
    IDLE, RUNNING, PAUSED
}

// 任务数据类
data class Task(
    val name: String,
    val description: String = "",
    val categoryId: String? = null,
    val color: Color = Color(0xFF6571FF)
)

// 辅助函数：格式化时间
fun formatElapsedTime(timeInSeconds: Long): String {
    val hours = timeInSeconds / 3600
    val minutes = (timeInSeconds % 3600) / 60
    val seconds = timeInSeconds % 60
    return String.format("%02d:%02d:%02d", hours, minutes, seconds)
} 