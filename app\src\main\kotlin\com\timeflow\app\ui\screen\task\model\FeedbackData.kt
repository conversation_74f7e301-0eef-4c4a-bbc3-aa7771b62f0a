package com.timeflow.app.ui.screen.task.model

import java.time.LocalDateTime

/**
 * 任务完成后的反馈数据模型
 */
data class FeedbackData(
    val emotion: String? = null,  // 情绪emoji
    val comment: String? = null,  // 用户感想/评论
    val imagePaths: List<String> = emptyList(),  // 图片文件路径列表
    val timestamp: LocalDateTime = LocalDateTime.now()  // 反馈时间戳
)

// 预定义的emoji选项
val EMOTION_OPTIONS = listOf("😊", "😐", "😢", "😡", "😰")

// 智能建议配置
val FEEDBACK_SUGGESTIONS = mapOf(
    "#感觉" to listOf("充满成就感", "有点吃力", "需要帮助", "很有成就感", "完成得很顺利"),
    "#后续" to listOf("需要跟进", "已完成归档", "待分享", "需要改进", "值得记录"),
    "#难度" to listOf("比预期简单", "符合预期", "比预期困难", "需要学习更多"),
    "#时间" to listOf("提前完成", "按时完成", "稍有延迟", "大幅延迟")
) 