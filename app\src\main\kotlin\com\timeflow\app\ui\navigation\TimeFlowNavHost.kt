package com.timeflow.app.ui.navigation

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.*
import androidx.navigation.compose.*
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.components.BottomNavBar
import com.timeflow.app.ui.screen.home.UnifiedHomeScreen
import com.timeflow.app.ui.screen.analytics.AnalyticsScreen
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel
import com.timeflow.app.ui.screen.discover.DiscoverScreen
import com.timeflow.app.ui.screen.profile.ProfileScreen
import com.timeflow.app.ui.screen.settings.SettingsScreen
import com.timeflow.app.ui.screen.task.TaskDetailScreen
import com.timeflow.app.ui.screen.task.TaskListFullScreen
import com.timeflow.app.ui.screen.task.AddTaskScreen
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.theme.AppBackground
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.ui.graphics.Color
import androidx.core.view.WindowInsetsCompat
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.timeflow.app.ui.task.KanbanViewModel
import timber.log.Timber
import com.timeflow.app.ui.screen.calendar.CalendarScreen
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel
import com.timeflow.app.ui.screen.task.TaskEditScreen
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.ui.platform.LocalView
import android.app.Activity
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavArgument
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.timeflow.app.ui.navigation.bottomNavItems
import com.timeflow.app.ui.navigation.ScreenWrappers
import com.timeflow.app.ui.settings.ThemeSettingsScreen
import com.timeflow.app.ui.screen.health.MenstrualCycleScreen
import com.timeflow.app.ui.screen.health.HabitTrackerScreen
// 🔧 删除：MenstrualCycleStatsScreen 已被融合到 PeriodAnalyticsScreen
import com.timeflow.app.ui.screen.health.SymptomsDetailScreen
import com.timeflow.app.ui.screen.ai.AiAssistantScreen
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel
import com.timeflow.app.ui.screen.settings.AiSettingsScreen
import com.timeflow.app.ui.screen.settings.AiModelSettingsScreen
import com.timeflow.app.ui.screen.goal.GoalManagementScreen
import com.timeflow.app.ui.screen.goal.GoalDetailScreen
import com.timeflow.app.ui.screen.goal.AddGoalScreen
import com.timeflow.app.ui.screen.goal.EditGoalScreen
import com.timeflow.app.ui.screen.ai.AIReviewScreen
import androidx.core.view.WindowCompat
import androidx.compose.runtime.DisposableEffect
import com.timeflow.app.ui.components.MiniTimerBar
import com.timeflow.app.ui.viewmodel.GlobalTimerViewModel
import com.timeflow.app.ui.timetracking.TimerState
import com.timeflow.app.ui.timetracking.screens.TimeTrackingScreen
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.milestone.MilestoneRoutes
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import com.timeflow.app.ui.screen.calendar.UserColorPreference
import com.timeflow.app.ui.screen.profile.EmotionStatisticsScreen
import com.timeflow.app.ui.screen.profile.DetailedEmotionRecordScreen
import com.timeflow.app.ui.screen.profile.EmotionType
import com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel
import com.timeflow.app.ui.screen.profile.EmotionRecord
import com.timeflow.app.utils.NavigationOptimizer
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.runtime.CompositionLocalProvider
import com.timeflow.app.ui.navigation.LocalNavigationDestination
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.core.tween
import com.timeflow.app.ui.screen.goal.GoalTemplateImportScreen
import com.timeflow.app.ui.screen.goal.GoalTemplateEditScreen
import com.timeflow.app.ui.screen.goal.GoalCompletionAnalysisScreen
import com.timeflow.app.ui.navigation.fadeInTransition
import com.timeflow.app.ui.navigation.fadeOutTransition
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing

// Navigation throttling constant
private const val NAV_THROTTLE_MS = 300L

/**
 * 应用导航控制器
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeFlowNavHost(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = AppDestinations.UNIFIED_HOME_ROUTE
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination?.route ?: startDestination
    
    // 获取全局计时器ViewModel
    val globalTimerViewModel = hiltViewModel<GlobalTimerViewModel>()
    val timerState by globalTimerViewModel.timerState.collectAsState()
    val elapsedTime by globalTimerViewModel.elapsedTime.collectAsState()
    val showMiniTimer by globalTimerViewModel.showMiniTimer.collectAsState()
    val currentTaskName by globalTimerViewModel.currentTaskName.collectAsState()
    
    // 设置系统栏处理
    val view = LocalView.current
    val context = LocalContext.current
    
    // 使用SideEffect替代原来的ApplySystemBarColors
    SideEffect {
        val activity = context as? Activity
        if (activity != null) {
            SystemBarManager.setupStandardPageSystemBars(activity)
        }
    }
    
    // 添加导航记录器
    val navigationHistory = mutableListOf<String>()
    var lastNavigationTime = 0L

    // 优化导航到方法
    LaunchedEffect(currentDestination) {
        // 添加导航历史记录
        val now = System.currentTimeMillis()
        
        // 防止日志风暴，只有当新路由与上一个不同或者间隔超过300ms时才记录
        if (navigationHistory.isEmpty() || 
            navigationHistory.last() != currentDestination || 
            now - lastNavigationTime > NAV_THROTTLE_MS) {
            
            Timber.d("导航到: $currentDestination")
            
            // 更新历史记录
            if (navigationHistory.size >= 10) {
                navigationHistory.removeAt(0)
            }
            currentDestination?.let { navigationHistory.add(it) }
            lastNavigationTime = now
        }
    }

    // 优化的导航回调
    val taskNavigation = remember(navController) {
        object {
            val toAddTask: () -> Unit = {
                try {
                    NavigationOptimizer.safeNavigate(
                        navController,
                        AppDestinations.ADD_TASK_ROUTE
                    )
                } catch (e: Exception) {
                    Log.e("TimeFlowNavHost", "导航到添加任务页面失败: ${e.message}")
                }
            }
            val toTaskDetail: (String?) -> Unit = { taskId ->
                if (taskId != null) {
                    try {
                        NavigationOptimizer.safeNavigate(
                            navController,
                            "${AppDestinations.TASK_DETAIL_ROUTE}/$taskId"
                        )
                    } catch (e: Exception) {
                        Log.e("TimeFlowNavHost", "导航到任务详情页面失败: ${e.message}")
                    }
                }
            }
            val toHome: () -> Unit = {
                try {
                    NavigationOptimizer.safeNavigate(
                        navController,
                        AppDestinations.UNIFIED_HOME_ROUTE,
                        popUpTo = AppDestinations.UNIFIED_HOME_ROUTE,
                        inclusive = true
                    )
                } catch (e: Exception) {
                    Log.e("TimeFlowNavHost", "导航到主页失败: ${e.message}")
                }
            }
            val navigateUp: () -> Unit = {
                try {
                    navController.navigateUp()
                } catch (e: Exception) {
                    Log.e("TimeFlowNavHost", "导航返回失败: ${e.message}")
                }
            }
        }
    }
    
    // 创建Scaffold显示底部导航栏，确保背景色正确
    Scaffold(
        containerColor = AppBackground, // 设置容器颜色为AppBackground
        contentColor = MaterialTheme.colorScheme.onBackground, // 确保文字颜色正确
        bottomBar = {
            // 仅在主要导航路径显示底部导航栏
            val showBottomBar = remember(currentDestination) {
                listOf(
                    AppDestinations.TASK_ROUTE,
                    AppDestinations.CALENDAR_ROUTE,
                    AppDestinations.ANALYTICS_ROUTE,
                    AppDestinations.PROFILE_ROUTE,
                    AppDestinations.UNIFIED_HOME_ROUTE
                ).any { it == currentDestination }
            }
            
            Column {
                // 添加迷你计时器
                MiniTimerBar(
                    visible = showMiniTimer && showBottomBar && currentDestination != AppDestinations.TIME_TRACKING_ROUTE,
                    taskName = currentTaskName,
                    elapsedTime = elapsedTime,
                    isRunning = timerState == TimerState.RUNNING,
                    onPlayPauseClick = {
                        if (timerState == TimerState.RUNNING) {
                            globalTimerViewModel.pauseTimer()
                        } else {
                            globalTimerViewModel.startTimer()
                        }
                    },
                    onCloseClick = {
                        globalTimerViewModel.hideMiniTimer()
                    },
                    onClick = {
                        NavigationOptimizer.safeNavigate(
                            navController,
                            AppDestinations.TIME_TRACKING_ROUTE
                        )
                    }
                )
                
                if (showBottomBar) {
                    BottomNavBar(
                        navController = navController,
                        items = bottomNavItems
                    )
                }
            }
        }
    ) { innerPadding ->
        // 🚀 启用流畅的页面切换动画系统
        NavHost(
            navController = navController,
            startDestination = startDestination,
            modifier = modifier,
            // 全局默认动画 - 智能选择
            enterTransition = {
                val fromRoute = navController.previousBackStackEntry?.destination?.route
                val toRoute = targetState.destination.route ?: ""
                val animation = AnimationConfigurator.getAnimationForNavigation(fromRoute, toRoute)
                AnimationOptimizer.optimizeAnimation(animation).enter
            },
            exitTransition = {
                val fromRoute = initialState.destination.route ?: ""
                val toRoute = navController.currentBackStackEntry?.destination?.route
                val animation = AnimationConfigurator.getAnimationForNavigation(fromRoute, toRoute ?: "")
                AnimationOptimizer.optimizeAnimation(animation).exit
            },
            popEnterTransition = {
                val fromRoute = navController.previousBackStackEntry?.destination?.route
                val toRoute = targetState.destination.route ?: ""
                val animation = AnimationConfigurator.getAnimationForNavigation(fromRoute, toRoute)
                AnimationOptimizer.optimizeAnimation(animation).popEnter
            },
            popExitTransition = {
                val fromRoute = initialState.destination.route ?: ""
                val toRoute = navController.currentBackStackEntry?.destination?.route
                val animation = AnimationConfigurator.getAnimationForNavigation(fromRoute, toRoute ?: "")
                AnimationOptimizer.optimizeAnimation(animation).popExit
            }
        ) {
            // 🏠 统一的主页屏幕 - 使用轻量级动画
            composable(
                route = AppDestinations.UNIFIED_HOME_ROUTE
            ) {
                UnifiedHomeScreen(
                    navController = navController,
                    timeFlowViewModel = hiltViewModel(),
                    taskViewModel = hiltViewModel<com.timeflow.app.ui.task.TaskListViewModel>()
                )
            }
            
            // 🏠 各屏幕路由定义 - 智能动画
            composable(
                route = AppDestinations.HOME_ROUTE
            ) {
                // 导航到统一的主页屏幕
                LaunchedEffect(Unit) {
                    NavigationOptimizer.safeNavigate(
                        navController,
                        AppDestinations.UNIFIED_HOME_ROUTE,
                        popUpTo = AppDestinations.HOME_ROUTE,
                        inclusive = true
                    )
                }
                
                // 显示加载指示器，等待导航完成
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            composable(AppDestinations.TASK_ROUTE) {
                // 获取来源信息
                val fromTaskList = remember {
                    navController.previousBackStackEntry?.destination?.route == AppDestinations.TASK_LIST_ROUTE
                }

                if (!fromTaskList) {
                    // 如果不是从任务列表页面返回，导航到统一的主页屏幕
                    LaunchedEffect(Unit) {
                        navController.navigate(AppDestinations.UNIFIED_HOME_ROUTE) {
                            popUpTo(AppDestinations.TASK_ROUTE) { 
                                inclusive = true
                            }
                        }
                    }
                    
                    // 显示加载指示器，等待导航完成
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else {
                    // 从任务列表页面返回的情况，不做任何处理
                    // 这样就可以避免在点击返回按钮时导航到统一主页
                    TaskListFullScreen(
                        navController = navController,
                        viewModel = hiltViewModel()
                    )
                }
            }
            
            // 📋 任务列表页面 - 主要导航动画
            composable(AppDestinations.TASK_LIST_ROUTE) {
                TaskListFullScreen(
                    navController = navController,
                    viewModel = hiltViewModel()
                )
            }
            
            composable(AppDestinations.TASK_LIST_FULL_ROUTE) {
                TaskListFullScreen(
                    navController = navController,
                    viewModel = hiltViewModel()
                )
            }
            
            composable(AppDestinations.CALENDAR_ROUTE) {
                val calendarViewModel = hiltViewModel<com.timeflow.app.ui.screen.calendar.CalendarViewModel>()
                CalendarScreen(
                    navController = navController,
                    viewModel = calendarViewModel
                )
            }
            
            // ➕ 创建任务页面 - 模态动画
            composable(AppDestinations.ADD_TASK_ROUTE) {
                // 使用新的AddTaskScreen
                AddTaskScreen(navController = navController)
            }
            
            // ✏️ 编辑任务页面 - 模态动画
            composable(
                route = "${AppDestinations.TASK_EDIT_ROUTE}/{taskId}",
                arguments = listOf(navArgument("taskId") {
                    type = NavType.StringType
                })
            ) { backStackEntry ->
                val taskId = backStackEntry.arguments?.getString("taskId")
                TaskEditScreen(
                    taskId = taskId,
                    navController = navController
                )
            }
            
            // 📋 任务详情页面 - 模态动画
            composable(
                route = AppDestinations.TASK_DETAIL_ROUTE,
                arguments = AppDestinations.taskDetailArguments
            ) { backStackEntry ->
                val taskId = backStackEntry.arguments?.getString("taskId") ?: ""
                Log.d("Navigation", "Composing TaskDetailScreen with taskId: $taskId")
                Log.d("Navigation", "Full route: ${AppDestinations.TASK_DETAIL_ROUTE}")
                Log.d("Navigation", "Arguments: ${backStackEntry.arguments}")
                
                TaskDetailScreen(
                    taskId = taskId,
                    onEditClick = {
                        NavigationOptimizer.safeNavigate(
                            navController = navController,
                            route = "${AppDestinations.TASK_EDIT_ROUTE}/$taskId"
                        )
                    },
                    onDismiss = {
                        navController.popBackStack()
                    },
                    navController = navController
                )
            }
            
            // 时间追踪页面 - 增强参数处理
            composable(
                route = AppDestinations.TIME_TRACKING_ROUTE,
                arguments = listOf(
                    navArgument("taskId") {
                        type = NavType.StringType
                        defaultValue = "none"
                    },
                    navArgument("autoStart") {
                        type = NavType.BoolType
                        defaultValue = false
                    }
                )
            ) { backStackEntry ->
                val taskId = backStackEntry.arguments?.getString("taskId")?.takeIf { it != "none" }
                val autoStart = backStackEntry.arguments?.getBoolean("autoStart") ?: false
                
                TimeTrackingScreen(
                    navController = navController,
                    globalTimerViewModel = globalTimerViewModel, // 🔧 传递全局计时器ViewModel
                    taskId = taskId,
                    autoStart = autoStart,
                    onTimerStateChanged = { state, taskName, elapsedTime ->
                        globalTimerViewModel.setCurrentTask(taskName)
                        globalTimerViewModel.refreshState()
                    }
                )
            }
            
            composable(AppDestinations.TIME_STATISTICS_ROUTE) {
                com.timeflow.app.ui.timetracking.TaskTimeStatisticsScreen(
                    navController = navController
                )
            }
            
            // 统计分析页面 - 使用懒加载方式优化初始化过程
            composable(
                route = AppDestinations.ANALYTICS_ROUTE,
                enterTransition = { EnterTransition.None },
                exitTransition = { ExitTransition.None }
            ) {
                val viewModel: AnalyticsViewModel = hiltViewModel()
                
                // 预加载处理：提前准备数据但延迟视图渲染
                LaunchedEffect(Unit) {
                    try {
                        // 在页面可见前预加载数据
                        viewModel.preloadData()
                        // 记录日志
                        Log.d("TimeFlowNavHost", "导航到: analytics")
                    } catch (e: Exception) {
                        Log.e("TimeFlowNavHost", "加载analytics数据失败: ${e.message}", e)
                    }
                }
                
                // 优化页面加载，使用Composable函数调用
                AnalyticsScreen(navController = navController, viewModel = viewModel)
            }
            
            // 愿望池页面
            composable(AppDestinations.WISH_LIST_ROUTE) {
                com.timeflow.app.ui.screen.wishlist.WishListScreen(
                    navController = navController,
                    viewModel = hiltViewModel()
                )
            }
            
            // 愿望详情页面
            composable(
                route = AppDestinations.WISH_DETAIL_ROUTE,
                arguments = AppDestinations.wishDetailArguments
            ) { backStackEntry ->
                val wishId = backStackEntry.arguments?.getString("wishId") ?: ""
                // 这里可以创建WishDetailScreen，暂时导航回愿望列表
                com.timeflow.app.ui.screen.wishlist.WishListScreen(
                    navController = navController,
                    viewModel = hiltViewModel()
                )
            }
            
            // 愿望统计页面
            composable(AppDestinations.WISH_STATISTICS_ROUTE) {
                com.timeflow.app.ui.screen.wishlist.WishStatisticsScreen(
                    navController = navController,
                    viewModel = hiltViewModel()
                )
            }
            
            composable(AppDestinations.PROFILE_ROUTE) {
                ProfileScreen(navController = navController)
            }
            
            composable(AppDestinations.EMOTION_STATS_ROUTE) {
                val viewModel = hiltViewModel<com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel>()
                
                // 获取从ProfileScreen传递的情绪记录
                LaunchedEffect(Unit) {
                    try {
                        val emotionRecords = navController.previousBackStackEntry
                            ?.savedStateHandle?.get<List<com.timeflow.app.ui.screen.profile.EmotionRecord>>("emotion_records")
                        
                        if (!emotionRecords.isNullOrEmpty()) {
                            viewModel.setEmotionRecords(emotionRecords)
                            // 消费数据避免重复处理
                            navController.previousBackStackEntry?.savedStateHandle?.remove<List<com.timeflow.app.ui.screen.profile.EmotionRecord>>("emotion_records")
                        }
                    } catch (e: Exception) {
                        Log.e("TimeFlowNavHost", "获取情绪记录失败", e)
                    }
                }
                
                EmotionStatisticsScreen(
                    navController = navController,
                    viewModel = viewModel
                )
            }

            // 情绪记录回顾页面
            composable(AppDestinations.EMOTION_RECORD_REVIEW_ROUTE) {
                com.timeflow.app.ui.screen.profile.EmotionRecordReviewScreen(
                    navController = navController
                )
            }

            // 情绪记录详情页面
            composable(AppDestinations.EMOTION_RECORD_DETAIL_ROUTE) {
                // 从savedStateHandle获取传递的EmotionRecord
                val record = navController.previousBackStackEntry?.savedStateHandle?.get<EmotionRecord>("selected_emotion_record")
                if (record != null) {
                    com.timeflow.app.ui.screen.profile.EmotionRecordDetailScreen(
                        record = record,
                        navController = navController
                    )
                } else {
                    // 如果没有记录数据，返回上一页
                    LaunchedEffect(Unit) {
                        navController.navigateUp()
                    }
                }
            }

            // 详细情绪记录页面
            composable(AppDestinations.DETAILED_EMOTION_RECORD_ROUTE) {
                val selectedEmotion = navController.previousBackStackEntry
                    ?.savedStateHandle?.get<EmotionType>("selectedEmotion") ?: EmotionType.JOY
                
                DetailedEmotionRecordScreen(
                    navController = navController,
                    selectedEmotion = selectedEmotion,
                    onRecordEmotion = { emotionRecord ->
                        try {
                            // 返回上一页并传递记录结果
                            navController.previousBackStackEntry?.savedStateHandle?.set("emotionRecord", emotionRecord)
                        } catch (e: Exception) {
                            // 记录错误以便调试
                            android.util.Log.e("TimeFlow", "保存情绪记录失败", e)
                            
                            // 如果复杂对象传递失败，使用简单数据传递代替
                            navController.previousBackStackEntry?.savedStateHandle?.apply {
                                set("emotion_recorded", true)
                                set("emotion_type", emotionRecord.emotion.name)
                                set("emotion_date", emotionRecord.date.toString())
                            }
                        }
                    }
                )
            }
            
            // 添加设置界面路由
            composable(AppDestinations.SETTINGS_ROUTE) {
                // 获取当前活动
                val context = LocalContext.current
                val activity = remember { context as? Activity }
                
                // 设置全屏显示
                DisposableEffect(Unit) {
                    activity?.let {
                        // 强制使用固定不透明的状态栏
                        SystemBarManager.forceOpaqueStatusBar(it)
                        // 设置窗口延伸到系统栏区域
                        WindowCompat.setDecorFitsSystemWindows(it.window, false)
                    }
                    
                    onDispose {
                        // 恢复标准页面的系统栏设置
                        activity?.let {
                            SystemBarManager.setupStandardPageSystemBars(it)
                        }
                    }
                }
                
                SettingsScreen(
                    navController = navController,
                    onClose = {
                        navController.popBackStack()
                    }
                )
            }
            
            // 关于页面路由
            composable(AppDestinations.ABOUT_ROUTE) {
                // 获取当前活动
                val context = LocalContext.current
                val activity = remember { context as? Activity }
                
                // 设置全屏显示
                DisposableEffect(Unit) {
                    activity?.let {
                        // 强制使用固定不透明的状态栏
                        SystemBarManager.forceOpaqueStatusBar(it)
                        // 设置窗口延伸到系统栏区域
                        WindowCompat.setDecorFitsSystemWindows(it.window, false)
                    }
                    
                    onDispose {
                        // 恢复标准页面的系统栏设置
                        activity?.let {
                            SystemBarManager.setupStandardPageSystemBars(it)
                        }
                    }
                }
                
                com.timeflow.app.ui.screen.settings.AboutScreen(
                    navController = navController,
                    calendarViewModel = hiltViewModel()
                )
            }
            
            // 添加账户页面路由
            composable(AppDestinations.ACCOUNT_ROUTE) {
                com.timeflow.app.ui.screen.account.AccountScreen(
                    navController = navController
                )
            }
            
            // 添加主题设置界面
            composable(AppDestinations.THEME_SETTINGS_ROUTE) {
                ThemeSettingsScreen(
                    navController = navController
                )
            }
            
            // 预设主题选择界面
            composable("preset_themes") {
                com.timeflow.app.ui.settings.PresetThemeScreen(
                    navController = navController,
                    onClose = { navController.navigateUp() }
                )
            }
            
            // 通知设置界面
            composable("notification_settings") {
                com.timeflow.app.ui.screen.settings.NotificationSettingsScreen(
                    navController = navController
                )
            }
            
            // 添加AI助手界面
            composable(
                route = AppDestinations.AI_ASSISTANT_WITH_TASK_ROUTE,
                arguments = AppDestinations.aiAssistantWithTaskArguments
            ) { backStackEntry ->
                val taskId = backStackEntry.arguments?.getString("taskId")
                AiAssistantScreen(
                    onBackClick = { navController.navigateUp() },
                    viewModel = hiltViewModel(),
                    aiSettingsViewModel = hiltViewModel(),
                    navController = navController,
                    taskId = taskId
                )
            }
            
            // 添加无任务参数的AI助手界面路由
            composable(route = AppDestinations.AI_ASSISTANT_ROUTE) {
                AiAssistantScreen(
                    onBackClick = { navController.navigateUp() },
                    viewModel = hiltViewModel(),
                    aiSettingsViewModel = hiltViewModel(),
                    navController = navController,
                    taskId = null
                )
            }
            
            // AI复盘路由
            composable(AppDestinations.AI_REVIEW_ROUTE) {
                AIReviewScreen(
                    navController = navController,
                    viewModel = hiltViewModel(),
                    aiConfigViewModel = hiltViewModel()
                )
            }
            
            // 同步设置
            composable(AppDestinations.SYNC_SETTINGS_ROUTE) {
                com.timeflow.app.ui.screen.settings.SyncSettingsScreen(
                    navController = navController
                )
            }
            
            // 数据管理（统一的备份恢复页面）
            composable(AppDestinations.DATA_MANAGEMENT_ROUTE) {
                com.timeflow.app.ui.screen.settings.DataManagementScreen(
                    navController = navController
                )
            }

            // 备份设置（保留兼容性）
            composable(AppDestinations.BACKUP_SETTINGS_ROUTE) {
                com.timeflow.app.ui.screen.settings.BackupSettingsScreen(
                    navController = navController
                )
            }

            // 数据恢复（保留兼容性）
            composable(AppDestinations.DATA_RECOVERY_ROUTE) {
                com.timeflow.app.ui.screen.settings.DataRecoveryScreen(
                    onNavigateBack = { navController.navigateUp() }
                )
            }
            
            // AI设置
            composable(AppDestinations.AI_SETTINGS_ROUTE) {
                ScreenWrappers.AiSettingsScreenWrapper(navController)
            }
            
            // AI模型设置
            composable(AppDestinations.AI_MODEL_SETTINGS_ROUTE) {
                ScreenWrappers.AiModelSettingsScreenWrapper(navController)
            }
            
            // 带ID参数的AI模型设置
            composable(
                route = AppDestinations.AI_MODEL_SETTINGS_WITH_ID_ROUTE,
                arguments = AppDestinations.aiModelSettingsArguments
            ) { backStackEntry ->
                val configId = backStackEntry.arguments?.getString("configId")
                ScreenWrappers.AiModelSettingsScreenWrapper(navController, configId)
            }
            
            // 反思/感想页面
            composable(AppDestinations.REFLECTION_ROUTE) {
                ScreenWrappers.ReflectionScreenWrapper(navController)
            }
            
            // 反思/感想详情页面
            composable(
                route = AppDestinations.REFLECTION_DETAIL_ROUTE,
                arguments = AppDestinations.reflectionDetailArguments
            ) { backStackEntry ->
                val reflectionId = backStackEntry.arguments?.getString("reflectionId") ?: ""
                ScreenWrappers.ReflectionDetailScreenWrapper(navController, reflectionId)
            }
            
            // 添加感想页面
            composable(AppDestinations.ADD_REFLECTION_ROUTE) {
                ScreenWrappers.ReflectionDetailScreenWrapper(navController, "")
            }
            
            // 目标管理
            composable(AppDestinations.GOAL_MANAGEMENT) {
                ScreenWrappers.GoalManagementScreenWrapper(navController)
            }
            
            // 目标详情
            composable(
                route = AppDestinations.GOAL_DETAIL_ROUTE,
                arguments = AppDestinations.goalDetailArguments
            ) { backStackEntry ->
                val goalId = backStackEntry.arguments?.getString("goalId") ?: ""
                ScreenWrappers.GoalDetailScreenWrapper(navController, goalId)
            }
            
            // 添加目标
            composable(AppDestinations.ADD_GOAL_ROUTE) {
                ScreenWrappers.AddGoalScreenWrapper(navController)
            }
            
            // 编辑目标
            composable(
                route = AppDestinations.EDIT_GOAL_ROUTE,
                arguments = AppDestinations.goalEditArguments
            ) { backStackEntry ->
                val goalId = backStackEntry.arguments?.getString("goalId") ?: ""
                ScreenWrappers.EditGoalScreenWrapper(navController, goalId)
            }
            
            // 目标拆解
            composable(
                route = AppDestinations.GOAL_BREAKDOWN_ROUTE,
                arguments = AppDestinations.goalBreakdownArguments
            ) { backStackEntry ->
                val goalId = backStackEntry.arguments?.getString("goalId") ?: ""
                ScreenWrappers.GoalBreakdownScreenWrapper(navController, goalId)
            }
            
            // 目标复盘分析
            composable(
                route = AppDestinations.GOAL_REVIEW_ROUTE,
                arguments = AppDestinations.goalReviewArguments
            ) { backStackEntry ->
                val goalId = backStackEntry.arguments?.getString("goalId") ?: ""
                ScreenWrappers.GoalReviewScreenWrapper(navController, goalId)
            }
            
            // 目标综合复盘分析
            composable(
                route = AppDestinations.GOAL_COMPLETION_ANALYSIS_ROUTE,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) {
                GoalCompletionAnalysisScreen(navController = navController)
            }
            
            // 生理周期页面
            composable(AppDestinations.MENSTRUAL_CYCLE_ROUTE) {
                ScreenWrappers.MenstrualCycleScreenWrapper(navController)
            }
            
            // 🔧 融合：生理周期统计页面 - 使用融合后的PeriodAnalyticsScreen
            composable(AppDestinations.MENSTRUAL_CYCLE_STATS_ROUTE) {
                com.timeflow.app.ui.screen.health.PeriodAnalyticsScreen(
                    onNavigateBack = { navController.navigateUp() }
                )
            }

            // 🔧 新增：生理周期历史记录页面
            composable("period_history") {
                com.timeflow.app.ui.screen.health.PeriodHistoryScreen(
                    onNavigateBack = { navController.navigateUp() }
                )
            }

            // 🔧 删除：重复的period_analytics路由，已合并到MENSTRUAL_CYCLE_STATS_ROUTE
            
            // 习惯追踪
            composable(AppDestinations.HABIT_TRACKER_ROUTE) {
                ScreenWrappers.HabitTrackerScreenWrapper(navController)
            }
            
            // 习惯详情
            composable(
                route = AppDestinations.HABIT_DETAIL_ROUTE,
                arguments = AppDestinations.habitDetailArguments
            ) { backStackEntry ->
                val habitId = backStackEntry.arguments?.getString("habitId") ?: ""
                android.util.Log.d("TimeFlowNavHost", "习惯详情页面导航: habitId=$habitId")
                ScreenWrappers.HabitDetailScreenWrapper(navController, habitId)
            }
            
            // 添加习惯
            composable(AppDestinations.ADD_HABIT_ROUTE) {
                ScreenWrappers.AddHabitScreenWrapper(navController)
            }
            
            // 症状详情页面
            composable(AppDestinations.SYMPTOMS_DETAIL_ROUTE) {
                ScreenWrappers.SymptomsDetailScreenWrapper(navController)
            }
            
            // 里程碑页面
            composable(AppDestinations.MILESTONE_ROUTE) {
                MilestoneRoutes.MilestoneRoute(navController)
            }
            
            // 目标模板列表路由
            composable(
                route = AppDestinations.GOAL_TEMPLATE_LIST,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) {
                ScreenWrappers.GoalTemplateListScreenWrapper(navController)
            }
            
            // 目标模板导入路由 (带可选的类别参数)
            composable(
                route = AppDestinations.GOAL_TEMPLATE_IMPORT_WITH_CATEGORY,
                arguments = AppDestinations.goalTemplateImportArguments
            ) { backStackEntry ->
                val category = backStackEntry.arguments?.getString("category")
                GoalTemplateImportScreen(
                    navController = navController,
                    initialCategory = category
                )
            }
            
            // 基本目标模板导入路由 (没有类别参数的路径匹配)
            composable(AppDestinations.GOAL_TEMPLATE_IMPORT) {
                GoalTemplateImportScreen(
                    navController = navController
                )
            }
            
            // 目标模板编辑路由 - 新建模式
            composable(AppDestinations.GOAL_TEMPLATE_EDIT) {
                GoalTemplateEditScreen(
                    navController = navController,
                    templateId = null
                )
            }
            
            // 编辑现有目标模板
            composable(
                route = AppDestinations.GOAL_TEMPLATE_EDIT_WITH_ID_ROUTE,
                arguments = AppDestinations.goalTemplateEditArguments,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) { backStackEntry ->
                val templateId = backStackEntry.arguments?.getString("templateId")
                ScreenWrappers.GoalTemplateEditScreenWrapper(navController, templateId)
            }
            
            // 保存目标为模板
            composable(
                route = AppDestinations.SAVE_GOAL_AS_TEMPLATE_ROUTE,
                arguments = AppDestinations.saveGoalAsTemplateArguments,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) { backStackEntry ->
                val goalId = backStackEntry.arguments?.getString("goalId") ?: ""
                ScreenWrappers.SaveGoalAsTemplateScreenWrapper(navController, goalId)
            }
            
            // 创建目标模板
            composable(
                route = AppDestinations.GOAL_TEMPLATE_EDIT,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) {
                ScreenWrappers.GoalTemplateEditScreenWrapper(navController, null)
            }
            
            // 药物管理页面
            composable(AppDestinations.MEDICATION_MANAGEMENT_ROUTE) {
                com.timeflow.app.ui.screen.health.ProfessionalMedicationScreen(navController = navController)
            }

            // 智能分类测试页面
            composable(
                route = AppDestinations.SMART_CATEGORY_TEST_ROUTE,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) {
                com.timeflow.app.ui.screen.goal.SmartCategoryTestScreen(navController = navController)
            }

            // 目标分类管理页面
            composable(
                route = AppDestinations.GOAL_CATEGORY_MANAGEMENT_ROUTE,
                enterTransition = { slideInHorizontally(initialOffsetX = { 1000 }, animationSpec = tween(300)) },
                exitTransition = { slideOutHorizontally(targetOffsetX = { 1000 }, animationSpec = tween(300)) }
            ) {
                com.timeflow.app.ui.screen.goal.GoalCategoryManagementScreen(navController = navController)
            }

            // 其他屏幕路由定义...
        }
    }
}

// 在文件结尾处添加这个替代性的时间分析详情屏幕
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeAnalyticsDetailScreen(
    navController: NavController
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("时间分析详情") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "时间分析详情页面",
                style = MaterialTheme.typography.headlineMedium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "此页面正在开发中...",
                style = MaterialTheme.typography.bodyLarge
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = { navController.popBackStack() }
            ) {
                Text("返回")
            }
        }
    }
}