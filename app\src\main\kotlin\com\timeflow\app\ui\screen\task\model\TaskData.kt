package com.timeflow.app.ui.screen.task.model

import java.time.LocalDateTime
import java.util.UUID
import com.timeflow.app.ui.screen.task.SubTask

/**
 * 任务数据模型 - 用于UI层
 * 注意：重命名为TaskDataModel以避免与TaskData冲突
 */
data class TaskDataModel(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val urgency: TaskUrgency = TaskUrgency.MEDIUM,
    val dueDateTime: LocalDateTime? = null,
    val isCompleted: Boolean = false,
    val tag: String? = null,
    val customTags: List<String> = emptyList(),
    val subTasks: List<SubTask> = emptyList(),
    val parentTaskId: String? = null,
    val displayInTaskList: Boolean = true,
    val feedback: FeedbackData? = null,
    val daysLeft: Int = 0
) 