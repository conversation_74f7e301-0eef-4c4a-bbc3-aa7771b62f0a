package com.timeflow.app.data.repository

import com.timeflow.app.data.model.Event
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

interface EventRepository {
    fun observeEvents(): Flow<List<Event>>
    suspend fun getEvents(): List<Event>
    suspend fun addEvent(event: Event)
    suspend fun updateEvent(event: Event)
    suspend fun deleteEvent(eventId: String)
}

@Singleton
class EventRepositoryImpl @Inject constructor() : EventRepository {
    private val events = MutableStateFlow<List<Event>>(emptyList())

    override fun observeEvents(): Flow<List<Event>> = events.asStateFlow()

    override suspend fun getEvents(): List<Event> = events.value

    override suspend fun addEvent(event: Event) {
        val currentEvents = events.value.toMutableList()
        currentEvents.add(event)
        events.value = currentEvents
    }

    override suspend fun updateEvent(event: Event) {
        val currentEvents = events.value.toMutableList()
        val index = currentEvents.indexOfFirst { it.id == event.id }
        if (index != -1) {
            currentEvents[index] = event
            events.value = currentEvents
        }
    }

    override suspend fun deleteEvent(eventId: String) {
        val currentEvents = events.value.toMutableList()
        currentEvents.removeAll { it.id == eventId }
        events.value = currentEvents
    }
} 