package com.timeflow.app.debug

import android.util.Log
import kotlinx.coroutines.delay
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.screen.task.model.TaskUrgency

/**
 * 优先级更新功能测试
 * 用于验证TaskDetailBottomSheet和TaskListFullScreen之间的优先级同步
 */
object PriorityUpdateTest {
    private const val TAG = "PriorityUpdateTest"
    
    /**
     * 模拟优先级更新测试
     */
    suspend fun testPriorityUpdate(
        taskId: String,
        fromPriority: Priority,
        toPriority: Priority,
        onPriorityUpdated: (String, Priority) -> Unit
    ) {
        Log.d(TAG, "=== 开始优先级更新测试 ===")
        Log.d(TAG, "任务ID: $taskId")
        Log.d(TAG, "从优先级: $fromPriority")
        Log.d(TAG, "到优先级: $toPriority")
        
        try {
            // 1. 模拟优先级更新
            Log.d(TAG, "步骤1: 调用onPriorityUpdated回调")
            onPriorityUpdated(taskId, toPriority)
            
            // 2. 等待数据库保存
            Log.d(TAG, "步骤2: 等待数据库保存 (200ms)")
            delay(200)
            
            // 3. 发送全局刷新事件
            Log.d(TAG, "步骤3: 发送全局刷新事件")
            NotificationCenter.post(TaskRefreshEvent(taskId))
            
            // 4. 等待UI更新
            Log.d(TAG, "步骤4: 等待UI更新 (300ms)")
            delay(300)
            
            Log.d(TAG, "=== 优先级更新测试完成 ===")
            
        } catch (e: Exception) {
            Log.e(TAG, "优先级更新测试失败", e)
        }
    }
    
    /**
     * 验证优先级和紧急程度的转换
     */
    fun testPriorityUrgencyMapping() {
        Log.d(TAG, "=== 测试优先级和紧急程度映射 ===")
        
        val priorities = listOf(Priority.URGENT, Priority.HIGH, Priority.MEDIUM, Priority.LOW)
        val urgencies = listOf(TaskUrgency.CRITICAL, TaskUrgency.HIGH, TaskUrgency.MEDIUM, TaskUrgency.LOW)
        
        priorities.forEachIndexed { index, priority ->
            val expectedUrgency = urgencies[index]
            val actualUrgency = when(priority) {
                Priority.URGENT -> TaskUrgency.CRITICAL
                Priority.HIGH -> TaskUrgency.HIGH
                Priority.MEDIUM -> TaskUrgency.MEDIUM
                Priority.LOW -> TaskUrgency.LOW
                else -> TaskUrgency.MEDIUM
            }
            
            val isCorrect = actualUrgency == expectedUrgency
            Log.d(TAG, "优先级: $priority -> 紧急程度: $actualUrgency ${if (isCorrect) "✓" else "✗"}")
        }
        
        Log.d(TAG, "=== 映射测试完成 ===")
    }
    
    /**
     * 模拟用户操作流程
     */
    suspend fun simulateUserWorkflow(
        taskId: String,
        onPriorityUpdated: (String, Priority) -> Unit,
        forceRefreshTaskData: (() -> Unit)? = null
    ) {
        Log.d(TAG, "=== 模拟用户操作流程 ===")
        
        try {
            // 1. 用户打开TaskDetailBottomSheet
            Log.d(TAG, "1. 用户打开TaskDetailBottomSheet")
            delay(100)
            
            // 2. 用户修改优先级从MEDIUM到HIGH
            Log.d(TAG, "2. 用户修改优先级: MEDIUM -> HIGH")
            testPriorityUpdate(taskId, Priority.MEDIUM, Priority.HIGH, onPriorityUpdated)
            
            // 3. 触发强制刷新
            Log.d(TAG, "3. 触发强制刷新")
            forceRefreshTaskData?.invoke()
            delay(200)
            
            // 4. 用户再次修改优先级从HIGH到URGENT
            Log.d(TAG, "4. 用户再次修改优先级: HIGH -> URGENT")
            testPriorityUpdate(taskId, Priority.HIGH, Priority.URGENT, onPriorityUpdated)
            
            // 5. 触发强制刷新
            Log.d(TAG, "5. 再次触发强制刷新")
            forceRefreshTaskData?.invoke()
            delay(200)
            
            // 6. 用户关闭TaskDetailBottomSheet
            Log.d(TAG, "6. 用户关闭TaskDetailBottomSheet")
            delay(100)
            
            Log.d(TAG, "=== 用户操作流程模拟完成 ===")
            
        } catch (e: Exception) {
            Log.e(TAG, "用户操作流程模拟失败", e)
        }
    }
} 