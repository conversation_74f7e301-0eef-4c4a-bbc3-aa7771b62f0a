package com.timeflow.app.ui.task.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.background
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.AssistChip
import androidx.compose.material3.ElevatedFilterChip
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import androidx.compose.foundation.shape.CircleShape

/**
 * 获取优先级对应的颜色
 */
@Composable
fun getPriorityColor(priority: Int): Color {
    return when (priority) {
        3 -> Color(0xFFD50000)  // 深红色 - 紧急
        2 -> Color(0xFFF44336)  // 红色 - 高
        1 -> Color(0xFFFFA726)  // 橙色 - 中
        0 -> Color(0xFF66BB6A)  // 绿色 - 低
        else -> Color.Gray      // 默认
    }
}

/**
 * 任务筛选芯片组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskFilterChips(
    selectedTags: List<TaskTag>,
    priorityFilter: Priority?,
    showCompleted: Boolean,
    onRemoveTag: (TaskTag) -> Unit,
    onClearTags: () -> Unit,
    onClearPriority: () -> Unit,
    onToggleShowCompleted: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f),
        shadowElevation = 2.dp,
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = 8.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 标签筛选芯片
            if (selectedTags.isNotEmpty()) {
                for (tag in selectedTags) {
                    TagFilterChip(
                        tag = tag,
                        onRemove = { onRemoveTag(tag) }
                    )
                }
                
                // 清除所有标签筛选
                if (selectedTags.size > 1) {
                    ClearFilterChip(
                        label = "清除标签",
                        onClick = onClearTags
                    )
                }
            }
            
            // 优先级筛选芯片
            if (priorityFilter != null) {
                PriorityFilterChip(
                    priority = priorityFilter.ordinal,
                    onClear = onClearPriority
                )
            }
            
            // 已完成任务筛选
            CompletedFilterChip(
                showCompleted = showCompleted,
                onToggle = onToggleShowCompleted
            )
        }
    }
}

/**
 * 标签筛选芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TagFilterChip(
    tag: TaskTag,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        selected = true,
        onClick = onRemove,
        label = {
            Text(text = tag.name)
        },
        leadingIcon = {
            PriorityIndicator(tag.color)
        },
        trailingIcon = {
            Icon(
                imageVector = Icons.Default.Clear,
                contentDescription = "Clear",
                modifier = Modifier.padding(start = 4.dp)
            )
        },
        modifier = modifier
    )
}

/**
 * 优先级指示器组件
 */
@Composable
fun PriorityIndicator(color: Color) {
    androidx.compose.foundation.Canvas(
        modifier = Modifier.padding(4.dp)
    ) {
        drawCircle(color = color, radius = 8.dp.toPx())
    }
}

/**
 * 优先级筛选芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PriorityFilterChip(
    priority: Int,
    onClear: () -> Unit,
    modifier: Modifier = Modifier
) {
    val chipColor by animateColorAsState(
        targetValue = getPriorityColor(priority).copy(alpha = 0.2f),
        label = "Chip Color Animation"
    )
    
    FilterChip(
        selected = true,
        onClick = onClear,
        label = {
            when (priority) {
                0 -> Text("低优先级")
                1 -> Text("中优先级")
                2 -> Text("高优先级")
                3 -> Text("紧急")
                else -> Text("未知优先级")
            }
        },
        leadingIcon = {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = getPriorityColor(priority),
                        shape = CircleShape
                    )
            )
        },
        trailingIcon = {
            Icon(
                imageVector = Icons.Default.Clear,
                contentDescription = "Clear",
                modifier = Modifier.padding(start = 4.dp)
            )
        },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = chipColor
        ),
        modifier = modifier
    )
}

/**
 * 清除筛选芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClearFilterChip(
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AssistChip(
        onClick = onClick,
        label = {
            Text(text = label)
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Clear,
                contentDescription = "Clear filters"
            )
        },
        modifier = modifier
    )
}

/**
 * 完成状态筛选芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CompletedFilterChip(
    showCompleted: Boolean,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    ElevatedFilterChip(
        selected = !showCompleted,
        onClick = onToggle,
        label = {
            Text(text = if (!showCompleted) "隐藏已完成" else "显示已完成")
        },
        leadingIcon = if (!showCompleted) {
            {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Hide completed tasks"
                )
            }
        } else null,
        modifier = modifier
    )
}

@Composable
private fun PriorityFilterChip(
    priority: Int,
    selected: Boolean,
    onSelectedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        selected = selected,
        onClick = { onSelectedChange(!selected) },
        label = { Text(getPriorityLabel(priority)) },
        leadingIcon = {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = getPriorityColor(priority),
                        shape = CircleShape
                    )
            )
        },
        modifier = modifier
    )
}

@Composable
private fun getPriorityLabel(priority: Int): String {
    return when (priority) {
        0 -> "低"
        1 -> "中"
        2 -> "高" 
        3 -> "紧急"
        else -> "未知"
    }
} 