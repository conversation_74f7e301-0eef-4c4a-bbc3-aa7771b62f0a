package com.timeflow.app.ui.screen.health

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringSetPreferencesKey
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import dagger.hilt.android.qualifiers.ApplicationContext
import android.content.Context
import com.timeflow.app.utils.TimeZoneUtils
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.*
import javax.inject.Inject
import com.timeflow.app.service.MedicationReminderManager
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.di.DataStoreModule.MedicationDataStore
import androidx.datastore.preferences.core.stringPreferencesKey
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timeflow.app.data.repository.MedicationRepository
import com.timeflow.app.data.entity.MedicationRecord
import java.util.UUID
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.Flow

/**
 * 专业用药管理ViewModel
 * 管理用药提醒、安全监控、依从性分析等功能
 */

@HiltViewModel
class ProfessionalMedicationViewModel @Inject constructor(
    private val medicationReminderManager: MedicationReminderManager,
    @ApplicationContext private val context: Context,
    @MedicationDataStore private val medicationDataStore: DataStore<Preferences>,
    private val medicationRepository: MedicationRepository // 🔧 注入Repository
) : ViewModel() {
    
    // DataStore keys
    private val DELETED_MEDICATIONS_KEY = stringSetPreferencesKey("deleted_medications")
    private val ARCHIVED_MEDICATIONS_KEY = stringSetPreferencesKey("archived_medications")
    private val COMPLETED_MEDICATIONS_KEY = stringSetPreferencesKey("completed_medications")
    
    // 🔧 新增：用户自定义药物存储
    private val USER_MEDICATIONS_KEY = stringPreferencesKey("user_medications_json")
    
    private val _state = MutableStateFlow(ProfessionalMedicationState())
    val state: StateFlow<ProfessionalMedicationState> = _state.asStateFlow()
    
    init {
        loadInitialData()
    }
    
    /**
     * 🔧 手动刷新数据 - 确保页面恢复时数据一致性
     */
    fun refreshData() {
        Log.d("MedicationViewModel", "=== 手动刷新用药数据 ===")
        loadInitialData()
    }
    
    /**
     * 🔧 获取当前持久化状态信息（用于调试）
     */
    fun logCurrentPersistenceState() {
        viewModelScope.launch {
            try {
                val deletedIds = getDeletedMedicationIds()
                val archivedIds = getArchivedMedicationIds()
                val completedIds = getCompletedMedicationIds()
                
                Log.d("MedicationPersistence", "=== 当前持久化状态 ===")
                Log.d("MedicationPersistence", "已删除药物ID: $deletedIds")
                Log.d("MedicationPersistence", "已归档药物ID: $archivedIds")
                Log.d("MedicationPersistence", "已完成药物ID: $completedIds")
                Log.d("MedicationPersistence", "当前显示药物数量: ${_state.value.todayMedications.size}")
                
                _state.value.todayMedications.forEach { med ->
                    Log.d("MedicationPersistence", "当前药物: ${med.name} (ID: ${med.id}, 已完成: ${med.isCompleted})")
                }
                Log.d("MedicationPersistence", "========================")
            } catch (e: Exception) {
                Log.e("MedicationPersistence", "获取持久化状态失败", e)
            }
        }
    }
    
    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        viewModelScope.launch {
            try {
                // 获取已删除、已归档和已完成的药物ID
                val deletedIds = getDeletedMedicationIds()
                val archivedIds = getArchivedMedicationIds()

                // === 根据数据库记录判断今天的完成情况，避免硬编码并每日重置 ===
                val today = TimeZoneUtils.getCurrentDate() // 🔧 使用统一时区处理
                val completedIds = mutableSetOf<String>()

                // 由于要对所有药物进行检查，这里顺序执行即可，数据量通常很小
                // 如果后续出现性能问题，可并发优化
                suspend fun hasTakenToday(medId: String): Boolean {
                    return medicationRepository
                        .getMedicationRecords(medId, today, today)
                        .firstOrNull()
                        ?.isNotEmpty() ?: false
                }

                // 🔄 之后会用到allMedications，因此先获取
                // 🔧 修复：合并模拟数据和用户自定义数据
                val mockMedications = getMockTodayMedications()
                val userMedications = getUserMedications()
                val allMedications = mockMedications + userMedications

                // 生成今日已完成集合
                for (med in allMedications) {
                    if (hasTakenToday(med.id)) {
                        completedIds.add(med.id)
                    }
                }
                
                Log.d("MedicationViewModel", "加载数据: 模拟${mockMedications.size}个，用户${userMedications.size}个")
                
                val filteredMedications = allMedications.filter { medication ->
                    !deletedIds.contains(medication.id) && !archivedIds.contains(medication.id)
                }.map { medication ->
                    // 🔧 修复：恢复已保存的完成状态
                    medication.copy(isCompleted = completedIds.contains(medication.id))
                }
                
                val archivedMedications = allMedications.filter { medication ->
                    archivedIds.contains(medication.id)
                }.map { medication ->
                    // 归档的药物也要恢复完成状态
                    medication.copy(isCompleted = completedIds.contains(medication.id))
                }
                
                _state.update { currentState ->
                    currentState.copy(
                        todayMedications = filteredMedications,
                        archivedMedications = archivedMedications,
                        safetyAlerts = getMockSafetyAlerts(),
                        todayAdherenceRate = if (filteredMedications.isNotEmpty()) {
                            completedIds.size.toFloat() / filteredMedications.size
                        } else 0f,
                        nextMedicationTime = LocalTime.of(14, 30),
                        unreadSafetyAlerts = 2
                    )
                }
                
                // 初始化智能功能
                initializeSmartFeatures()
                
            } catch (e: Exception) {
                // 错误处理
                _state.update { it.copy(isLoading = false) }
            }
        }
    }
    
    /**
     * 获取已删除药物ID列表
     */
    private suspend fun getDeletedMedicationIds(): Set<String> {
        return medicationDataStore.data
            .map { preferences ->
                preferences[DELETED_MEDICATIONS_KEY] ?: emptySet()
            }
            .first()
    }
    
    /**
     * 获取已归档药物ID列表
     */
    private suspend fun getArchivedMedicationIds(): Set<String> {
        return medicationDataStore.data
            .map { preferences ->
                preferences[ARCHIVED_MEDICATIONS_KEY] ?: emptySet()
            }
            .first()
    }
    
    /**
     * 获取已完成药物ID列表
     */
    private suspend fun getCompletedMedicationIds(): Set<String> {
        return medicationDataStore.data
            .map { preferences ->
                preferences[COMPLETED_MEDICATIONS_KEY] ?: emptySet()
            }
            .first()
    }
    
    /**
     * 🔧 获取用户自定义药物列表
     */
    private suspend fun getUserMedications(): List<ProfessionalMedication> {
        return try {
            val jsonString = medicationDataStore.data
                .map { preferences ->
                    preferences[USER_MEDICATIONS_KEY] ?: ""
                }
                .first()
            
            if (jsonString.isNotEmpty()) {
                val gson = Gson()
                val type = object : TypeToken<List<ProfessionalMedication>>() {}.type
                gson.fromJson(jsonString, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "加载用户药物数据失败", e)
            emptyList()
        }
    }
    
    /**
     * 🔧 保存用户自定义药物列表
     */
    private suspend fun saveUserMedications(medications: List<ProfessionalMedication>) {
        try {
            val gson = Gson()
            val jsonString = gson.toJson(medications)
            
            medicationDataStore.edit { preferences ->
                preferences[USER_MEDICATIONS_KEY] = jsonString
            }
            
            Log.d("MedicationViewModel", "用户药物数据已保存，数量: ${medications.size}")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "保存用户药物数据失败", e)
        }
    }
    
    /**
     * 🔧 从用户药物列表中移除指定药物
     */
    private suspend fun removeUserMedication(medicationId: String) {
        try {
            val currentUserMedications = getUserMedications().toMutableList()
            val originalSize = currentUserMedications.size
            currentUserMedications.removeAll { it.id == medicationId }
            
            if (currentUserMedications.size < originalSize) {
                saveUserMedications(currentUserMedications)
                Log.d("MedicationViewModel", "用户药物已从持久化存储中移除: $medicationId")
            }
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "从用户药物列表移除失败: $medicationId", e)
        }
    }
    
    // 撤销相关状态
    private var undoJob: kotlinx.coroutines.Job? = null
    private val _showUndoSnackbar = MutableStateFlow<UndoSnackbarState?>(null)
    val showUndoSnackbar: StateFlow<UndoSnackbarState?> = _showUndoSnackbar.asStateFlow()
    
    // 删除撤销相关状态
    private var deleteUndoJob: kotlinx.coroutines.Job? = null
    private val _showDeleteUndoSnackbar = MutableStateFlow<DeleteUndoSnackbarState?>(null)
    val showDeleteUndoSnackbar: StateFlow<DeleteUndoSnackbarState?> = _showDeleteUndoSnackbar.asStateFlow()
    
    // 临时存储被删除的药物，用于撤销
    private var tempDeletedMedication: ProfessionalMedication? = null
    
    // 编辑和交互状态
    private val _showEditDialog = MutableStateFlow<ProfessionalMedication?>(null)
    val showEditDialog: StateFlow<ProfessionalMedication?> = _showEditDialog.asStateFlow()
    
    private val _showHeatmapDialog = MutableStateFlow<HeatmapDialogState?>(null)
    val showHeatmapDialog: StateFlow<HeatmapDialogState?> = _showHeatmapDialog.asStateFlow()
    
    private val _showMedicationActions = MutableStateFlow<ProfessionalMedication?>(null)
    val showMedicationActions: StateFlow<ProfessionalMedication?> = _showMedicationActions.asStateFlow()
    
    /**
     * 标记药物已服用（支持撤销）
     */
    fun markMedicationTaken(medication: ProfessionalMedication) {
        // 取消之前的撤销任务
        undoJob?.cancel()
        
        // 先更新UI状态
        updateMedicationStatus(medication.id, true)
        
        // 🔧 核心改造：写入数据库，而不是简单的DataStore
        viewModelScope.launch {
            try {
                val record = MedicationRecord(
                    id = UUID.randomUUID().toString(),
                    medicationId = medication.id,
                    recordDate = TimeZoneUtils.getCurrentDate(), // 🔧 使用统一时区处理
                    completedAt = System.currentTimeMillis(),
                    status = "TAKEN"
                )
                medicationRepository.addMedicationRecord(record)
                Log.d("MedicationViewModel", "✅ 用药记录已保存到数据库: ${medication.name}")
            } catch (e: Exception) {
                Log.e("MedicationViewModel", "保存用药记录到数据库失败", e)
                // 可选：如果数据库写入失败，回滚UI状态
                updateMedicationStatus(medication.id, false)
            }
        }
        
        // 显示撤销提示
        _showUndoSnackbar.value = UndoSnackbarState(
            medicationId = medication.id,
            medicationName = medication.name,
            remainingSeconds = 10
        )
        
        // 启动10秒倒计时
        undoJob = viewModelScope.launch {
            for (i in 9 downTo 0) {
                delay(1000)
                if (_showUndoSnackbar.value?.medicationId == medication.id) {
                    _showUndoSnackbar.value = _showUndoSnackbar.value?.copy(remainingSeconds = i)
                    if (i == 0) {
                        _showUndoSnackbar.value = null
                    }
                }
            }
        }
    }
    
    /**
     * 撤销用药记录
     */
    fun undoMedicationTaken(medicationId: String) {
        undoJob?.cancel()
        updateMedicationStatus(medicationId, false)
        
        // 🔧 核心改造：从数据库中删除记录
        viewModelScope.launch {
            try {
                medicationRepository.deleteMedicationRecord(medicationId, TimeZoneUtils.getCurrentDate()) // 🔧 使用统一时区处理
                Log.d("MedicationViewModel", "✅ 用药记录已从数据库撤销: $medicationId")
            } catch (e: Exception) {
                Log.e("MedicationViewModel", "从数据库撤销用药记录失败", e)
            }
        }
        
        _showUndoSnackbar.value = null
    }
    
    /**
     * 隐藏撤销提示
     */
    fun dismissUndoSnackbar() {
        undoJob?.cancel()
        _showUndoSnackbar.value = null
    }
    
    /**
     * 删除药物（支持10秒内撤销）
     */
    fun deleteMedication(medication: ProfessionalMedication) {
        // 取消之前的删除撤销任务
        deleteUndoJob?.cancel()
        
        // 临时保存药物信息
        tempDeletedMedication = medication
        
        // 从当前状态中移除药物
        val currentMedications = _state.value.todayMedications.toMutableList()
        currentMedications.removeAll { it.id == medication.id }
        _state.update { it.copy(todayMedications = currentMedications) }
        
        // 🔧 修复：立即持久化删除状态，确保退出页面时状态不丢失
        viewModelScope.launch {
            saveDeletedMedicationId(medication.id)
            // 同时从完成列表中移除（如果存在）
            removeCompletedMedicationId(medication.id)
            // 🔧 新增：从用户药物列表中移除（如果是用户添加的药物）
            removeUserMedication(medication.id)
            Log.d("MedicationViewModel", "药物删除状态已立即持久化: ${medication.id}")
        }
        
        // 显示撤销提示
        _showDeleteUndoSnackbar.value = DeleteUndoSnackbarState(
            medicationId = medication.id,
            medicationName = medication.name,
            remainingSeconds = 10
        )
        
        // 启动10秒倒计时
        deleteUndoJob = viewModelScope.launch {
            for (i in 10 downTo 1) {
                delay(1000)
                if (_showDeleteUndoSnackbar.value?.medicationId == medication.id) {
                    _showDeleteUndoSnackbar.value = _showDeleteUndoSnackbar.value?.copy(remainingSeconds = i - 1)
                }
            }
            // 10秒后清理临时数据
            _showDeleteUndoSnackbar.value = null
            tempDeletedMedication = null
            
            Log.d("MedicationViewModel", "删除倒计时结束，药物永久删除: ${medication.id}")
        }
    }
    
    /**
     * 撤销删除药物
     */
    fun undoDeleteMedication(medicationId: String) {
        deleteUndoJob?.cancel()
        
        tempDeletedMedication?.let { medication ->
            if (medication.id == medicationId) {
                // 恢复药物到列表
                val currentMedications = _state.value.todayMedications.toMutableList()
                currentMedications.add(medication)
                _state.update { it.copy(todayMedications = currentMedications.sortedBy { it.scheduledTime }) }
                
                // 清除临时数据
                tempDeletedMedication = null
                
                // 从DataStore中移除已删除ID，并恢复用户药物（如果是用户添加的）
                viewModelScope.launch {
                    removeDeletedMedicationId(medicationId)
                    
                    // 🔧 新增：如果是用户添加的药物，恢复到用户药物列表
                    // 通过检查是否存在于模拟数据中来判断
                    val mockMedications = getMockTodayMedications()
                    val isUserMedication = mockMedications.none { it.id == medicationId }
                    
                    if (isUserMedication) {
                        val currentUserMedications = getUserMedications().toMutableList()
                        if (currentUserMedications.none { it.id == medicationId }) {
                            currentUserMedications.add(medication)
                            saveUserMedications(currentUserMedications)
                            Log.d("MedicationViewModel", "用户药物已恢复到持久化存储: ${medication.name}")
                        }
                    }
                }
            }
        }
        
        _showDeleteUndoSnackbar.value = null
    }
    
    /**
     * 隐藏删除撤销提示
     */
    fun dismissDeleteUndoSnackbar() {
        deleteUndoJob?.cancel()
        _showDeleteUndoSnackbar.value = null
        tempDeletedMedication = null
    }
    
    /**
     * 归档药物（立即持久化）
     */
    fun archiveMedication(medicationId: String) {
        viewModelScope.launch {
            try {
                // 取消提醒
                cancelMedicationReminder(medicationId)
                
                // 从当前药物列表中移除
                val currentMedications = _state.value.todayMedications.toMutableList()
                val medicationToArchive = currentMedications.find { it.id == medicationId }
                currentMedications.removeAll { it.id == medicationId }
                
                // 更新状态 - 移动到归档列表
                medicationToArchive?.let { medication ->
                    val archivedMedications = _state.value.archivedMedications.toMutableList()
                    archivedMedications.add(medication)
                    
                    _state.update { 
                        it.copy(
                            todayMedications = currentMedications,
                            archivedMedications = archivedMedications
                        ) 
                    }
                }
                
                // 持久化到DataStore
                saveArchivedMedicationId(medicationId)
                
                // 隐藏操作菜单
                hideMedicationActions()
                
                Log.d("MedicationViewModel", "药物已归档: $medicationId")
                
            } catch (e: Exception) {
                Log.e("MedicationViewModel", "归档药物失败: $medicationId", e)
            }
        }
    }
    
    /**
     * 恢复归档的药物
     */
    fun unarchiveMedication(medicationId: String) {
        viewModelScope.launch {
            try {
                // 从归档列表中移除
                val archivedMedications = _state.value.archivedMedications.toMutableList()
                val medicationToRestore = archivedMedications.find { it.id == medicationId }
                archivedMedications.removeAll { it.id == medicationId }
                
                // 更新状态 - 恢复到当前药物列表
                medicationToRestore?.let { medication ->
                    val currentMedications = _state.value.todayMedications.toMutableList()
                    currentMedications.add(medication)
                    
                    _state.update { 
                        it.copy(
                            todayMedications = currentMedications.sortedBy { med -> med.scheduledTime },
                            archivedMedications = archivedMedications
                        ) 
                    }
                    
                    // 重新设置提醒
                    scheduleMedicationReminder(medication)
                }
                
                // 从DataStore中移除
                removeArchivedMedicationId(medicationId)
                
                Log.d("MedicationViewModel", "药物已恢复: $medicationId")
                
            } catch (e: Exception) {
                Log.e("MedicationViewModel", "恢复药物失败: $medicationId", e)
            }
        }
    }
    
    /**
     * 将药物ID保存到已删除列表
     */
    private suspend fun saveDeletedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentDeleted = preferences[DELETED_MEDICATIONS_KEY] ?: emptySet()
                preferences[DELETED_MEDICATIONS_KEY] = currentDeleted + medicationId
            }
            Log.d("MedicationViewModel", "已删除药物ID已保存: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "保存已删除药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 将药物ID保存到已归档列表
     */
    private suspend fun saveArchivedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentArchived = preferences[ARCHIVED_MEDICATIONS_KEY] ?: emptySet()
                preferences[ARCHIVED_MEDICATIONS_KEY] = currentArchived + medicationId
            }
            Log.d("MedicationViewModel", "已归档药物ID已保存: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "保存已归档药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 从已删除列表中移除药物ID（用于撤销删除）
     */
    private suspend fun removeDeletedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentDeleted = preferences[DELETED_MEDICATIONS_KEY] ?: emptySet()
                preferences[DELETED_MEDICATIONS_KEY] = currentDeleted - medicationId
            }
            Log.d("MedicationViewModel", "已删除药物ID已移除: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "移除已删除药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 从已归档列表中移除药物ID（用于恢复归档）
     */
    private suspend fun removeArchivedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentArchived = preferences[ARCHIVED_MEDICATIONS_KEY] ?: emptySet()
                preferences[ARCHIVED_MEDICATIONS_KEY] = currentArchived - medicationId
            }
            Log.d("MedicationViewModel", "已归档药物ID已移除: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "移除已归档药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 将药物ID保存到已完成列表
     */
    private suspend fun saveCompletedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentCompleted = preferences[COMPLETED_MEDICATIONS_KEY] ?: emptySet()
                preferences[COMPLETED_MEDICATIONS_KEY] = currentCompleted + medicationId
            }
            Log.d("MedicationViewModel", "已完成药物ID已保存: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "保存已完成药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 从已完成列表中移除药物ID（用于撤销完成）
     */
    private suspend fun removeCompletedMedicationId(medicationId: String) {
        try {
            medicationDataStore.edit { preferences ->
                val currentCompleted = preferences[COMPLETED_MEDICATIONS_KEY] ?: emptySet()
                preferences[COMPLETED_MEDICATIONS_KEY] = currentCompleted - medicationId
            }
            Log.d("MedicationViewModel", "已完成药物ID已移除: $medicationId")
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "移除已完成药物ID失败: $medicationId", e)
        }
    }
    
    /**
     * 更新药物完成状态
     */
    private fun updateMedicationStatus(medicationId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            _state.update { currentState ->
                val updatedMedications = currentState.todayMedications.map { med ->
                    if (med.id == medicationId) {
                        med.copy(isCompleted = isCompleted)
                    } else {
                        med
                    }
                }
                
                // 重新计算依从率
                val completedCount = updatedMedications.count { it.isCompleted }
                val totalCount = updatedMedications.size
                val newAdherenceRate = if (totalCount > 0) completedCount.toFloat() / totalCount else 0f
                
                currentState.copy(
                    todayMedications = updatedMedications,
                    todayAdherenceRate = newAdherenceRate
                )
            }
        }
    }
    
    /**
     * 添加新药物
     */
    fun addMedication(medication: ProfessionalMedication) {
        viewModelScope.launch {
            try {
                // 🔧 修复：先保存到持久化存储
                val currentUserMedications = getUserMedications().toMutableList()
                currentUserMedications.add(medication)
                saveUserMedications(currentUserMedications)
                
                // 更新UI状态
                _state.update { currentState ->
                    currentState.copy(
                        todayMedications = currentState.todayMedications + medication
                    )
                }
                
                // 设置用药提醒
                scheduleMedicationReminder(medication)
                
                Log.d("MedicationViewModel", "✅ 药物已添加并持久化保存: ${medication.name} (ID: ${medication.id})")
                
            } catch (e: Exception) {
                Log.e("MedicationViewModel", "添加药物失败: ${medication.name}", e)
            }
        }
    }
    
    /**
     * 调度用药提醒
     */
    private fun scheduleMedicationReminder(medication: ProfessionalMedication) {
        try {
            // 创建Calendar对象，设置提醒时间
            val calendar = Calendar.getInstance().apply {
                set(Calendar.HOUR_OF_DAY, medication.scheduledTime.hour)
                set(Calendar.MINUTE, medication.scheduledTime.minute)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
                
                // 如果时间已过，则设置为明天
                if (before(Calendar.getInstance())) {
                    add(Calendar.DAY_OF_MONTH, 1)
                }
            }
            
            // 转换为Medication对象
            val medicationForReminder = com.timeflow.app.data.model.Medication(
                id = medication.id,
                name = medication.name,
                dosage = medication.dosage,
                frequency = medication.frequency,
                reminderTimes = listOf(medication.scheduledTime.toString())
            )
            
            // 使用默认通知设置（实际应该从DataStore获取）
            val notificationSettings = NotificationSettings()
            
            // 调度提醒
            medicationReminderManager.scheduleMedicationReminder(
                medicationForReminder,
                calendar,
                notificationSettings
            )
            
            Log.d("MedicationViewModel", "用药提醒已设置: ${medication.name} at ${medication.scheduledTime}")
            
        } catch (e: Exception) {
            Log.e("MedicationViewModel", "设置用药提醒失败: ${medication.name}", e)
        }
    }
    
    /**
     * 取消用药提醒
     */
    fun cancelMedicationReminder(medicationId: String) {
        medicationReminderManager.cancelMedicationReminder(medicationId)
        Log.d("MedicationViewModel", "用药提醒已取消: $medicationId")
    }
    
    /**
     * 重新调度所有今日用药提醒
     */
    fun rescheduleAllReminders() {
        viewModelScope.launch {
            val currentMedications = _state.value.todayMedications
            currentMedications.forEach { medication ->
                if (!medication.isCompleted) {
                    scheduleMedicationReminder(medication)
                }
            }
            Log.d("MedicationViewModel", "已重新调度${currentMedications.size}个用药提醒")
        }
    }
    
    // =================  卡片交互功能  =================
    
    /**
     * 显示药物卡片操作菜单
     */
    fun showMedicationActions(medication: ProfessionalMedication) {
        _showMedicationActions.value = medication
    }
    
    /**
     * 隐藏药物卡片操作菜单
     */
    fun hideMedicationActions() {
        _showMedicationActions.value = null
    }
    
    /**
     * 显示编辑药物对话框
     */
    fun showEditDialog(medication: ProfessionalMedication) {
        _showEditDialog.value = medication
        _showMedicationActions.value = null
    }
    
    /**
     * 隐藏编辑药物对话框
     */
    fun hideEditDialog() {
        _showEditDialog.value = null
    }
    
    /**
     * 显示热力图对话框
     */
    fun showHeatmapDialog(medication: ProfessionalMedication) {
        _showHeatmapDialog.value = HeatmapDialogState(medication)
    }
    
    /**
     * 隐藏热力图对话框
     */
    fun hideHeatmapDialog() {
        _showHeatmapDialog.value = null
    }
    
    /**
     * 切换热力图周期
     */
    fun changeHeatmapPeriod(period: HeatmapPeriod) {
        _showHeatmapDialog.value?.let { currentState ->
            _showHeatmapDialog.value = currentState.copy(selectedPeriod = period)
        }
    }
    
    /**
     * 编辑药物信息
     */
    fun updateMedication(
        medicationId: String,
        name: String,
        dosage: String,
        frequency: String,
        scheduledTime: LocalTime,
        category: String
    ) {
        viewModelScope.launch {
            _state.update { currentState ->
                val updatedMedications = currentState.todayMedications.map { med ->
                    if (med.id == medicationId) {
                        med.copy(
                            name = name,
                            dosage = dosage,
                            frequency = frequency,
                            scheduledTime = scheduledTime,
                            category = category
                        )
                    } else {
                        med
                    }
                }
                currentState.copy(todayMedications = updatedMedications)
            }
            
            // 重新设置提醒
            _state.value.todayMedications.find { it.id == medicationId }?.let { medication ->
                cancelMedicationReminder(medicationId)
                scheduleMedicationReminder(medication)
            }
            
            hideEditDialog()
        }
    }

    /**
     * 获取热力图数据
     */
    fun getHeatmapData(medicationId: String, period: HeatmapPeriod): Flow<HeatmapData> {
        val today = TimeZoneUtils.getCurrentDate() // 🔧 使用统一时区处理

        // 🔍 记录时区信息
        TimeZoneUtils.logTimeZoneInfo()

        // 🔧 修复：正确计算开始日期，确保包含今天
        val startDate = when (period) {
            HeatmapPeriod.WEEK -> today.minusDays(6)  // 包含今天，共7天
            HeatmapPeriod.MONTH -> today.minusDays(29) // 包含今天，共30天
            HeatmapPeriod.YEAR -> today.minusDays(364) // 包含今天，共365天
        }

        // 🔍 添加调试日志
        Log.d("MedicationHeatmap", "=== 热力图数据计算开始 ===")
        Log.d("MedicationHeatmap", "药物ID: $medicationId")
        Log.d("MedicationHeatmap", "周期: $period (${period.days}天)")
        Log.d("MedicationHeatmap", "今天: $today")
        Log.d("MedicationHeatmap", "开始日期: $startDate")
        Log.d("MedicationHeatmap", "日期范围: $startDate 到 $today")

        return medicationRepository.getMedicationRecords(medicationId, startDate, today)
            .map { records ->
                Log.d("MedicationHeatmap", "从数据库获取到 ${records.size} 条记录")
                records.forEach { record ->
                    Log.d("MedicationHeatmap", "记录: ${record.recordDate} - ${record.status}")
                }

                val recordMap = records.associateBy { it.recordDate }

                // 🔧 修复：按正确顺序生成日期点，从最早到最新
                val dataPoints = (0 until period.days).map { i ->
                    val date = startDate.plusDays(i.toLong()) // 从开始日期往后推
                    val record = recordMap[date]

                    Log.d("MedicationHeatmap", "日期点 $i: $date, 有记录: ${record != null}")

                    HeatmapDataPoint(
                        date = date,
                        value = if (record != null) 1f else 0f,
                        plannedDoses = 1,
                        actualDoses = if (record != null) 1 else 0,
                        isOnTime = record != null // 简化处理，可扩展
                    )
                }

                Log.d("MedicationHeatmap", "生成了 ${dataPoints.size} 个数据点")
                Log.d("MedicationHeatmap", "第一个日期: ${dataPoints.firstOrNull()?.date}")
                Log.d("MedicationHeatmap", "最后一个日期: ${dataPoints.lastOrNull()?.date}")

                val totalDoses = dataPoints.sumOf { it.plannedDoses }
                val completedDoses = dataPoints.sumOf { it.actualDoses }

                Log.d("MedicationHeatmap", "总计划剂量: $totalDoses, 完成剂量: $completedDoses")

                // 🔍 验证数据正确性
                validateHeatmapData(dataPoints, period)

                HeatmapData(
                    weekData = if (period == HeatmapPeriod.WEEK) dataPoints else emptyList(),
                    monthData = if (period == HeatmapPeriod.MONTH) dataPoints else emptyList(),
                    yearData = if (period == HeatmapPeriod.YEAR) dataPoints else emptyList(),
                    weeklyAverage = if (period == HeatmapPeriod.WEEK) dataPoints.map { it.value }.average().toFloat() else 0f,
                    monthlyAverage = if (period == HeatmapPeriod.MONTH) dataPoints.map { it.value }.average().toFloat() else 0f,
                    yearlyAverage = if (period == HeatmapPeriod.YEAR) dataPoints.map { it.value }.average().toFloat() else 0f,
                    totalDoses = totalDoses,
                    completedDoses = completedDoses,
                    onTimeDoses = completedDoses // 简化处理
                )
            }
    }
    
    /**
     * 获取模拟的今日用药数据
     */
    private fun getMockTodayMedications(): List<ProfessionalMedication> {
        return listOf(
            ProfessionalMedication(
                id = "1",
                name = "阿司匹林肠溶片",
                dosage = "100mg",
                frequency = "每日一次",
                scheduledTime = LocalTime.of(8, 0),
                isCompleted = true,
                category = "心血管药物"
            ),
            ProfessionalMedication(
                id = "2", 
                name = "二甲双胍片",
                dosage = "500mg",
                frequency = "每日两次",
                scheduledTime = LocalTime.of(12, 0),
                isCompleted = false,
                category = "降糖药物"
            ),
            ProfessionalMedication(
                id = "3",
                name = "维生素D3",
                dosage = "1000IU",
                frequency = "每日一次",
                scheduledTime = LocalTime.of(14, 30),
                isCompleted = false,
                category = "维生素补充剂"
            ),
            ProfessionalMedication(
                id = "4",
                name = "钙片",
                dosage = "600mg",
                frequency = "每日一次",
                scheduledTime = LocalTime.of(20, 0),
                isCompleted = false,
                category = "矿物质补充剂"
            )
        )
    }
    
    /**
     * 获取模拟的安全警示数据
     */
    private fun getMockSafetyAlerts(): List<SafetyAlert> {
        return listOf(
            SafetyAlert(
                id = "alert1",
                message = "阿司匹林与华法林可能存在药物相互作用，请咨询医生",
                severity = AlertSeverity.MEDIUM,
                timestamp = LocalTime.now()
            ),
            SafetyAlert(
                id = "alert2",
                message = "您已连续3天漏服二甲双胍，请及时补服",
                severity = AlertSeverity.HIGH,
                timestamp = LocalTime.now().minusHours(2)
            )
        )
    }

    // ===================== 智能提醒功能 =====================

    /**
     * 创建智能提醒
     */
    fun createSmartReminder(
        medicationId: String,
        type: ReminderType,
        scheduledTime: LocalTime,
        customMessage: String? = null
    ) {
        viewModelScope.launch {
            try {
                val medication = _state.value.todayMedications.find { it.id == medicationId }
                if (medication != null) {
                    val message = customMessage ?: generateReminderMessage(medication, type)
                    val reminder = SmartReminder(
                        id = "reminder_${System.currentTimeMillis()}",
                        medicationId = medicationId,
                        type = type,
                        scheduledTime = scheduledTime,
                        message = message
                    )
                    
                    _state.update { currentState ->
                        currentState.copy(
                            smartReminders = currentState.smartReminders + reminder
                        )
                    }
                    
                    Log.d("MedicationVM", "创建智能提醒成功: $message")
                }
            } catch (e: Exception) {
                Log.e("MedicationVM", "创建提醒失败", e)
            }
        }
    }

    /**
     * 生成提醒消息
     */
    private fun generateReminderMessage(medication: ProfessionalMedication, type: ReminderType): String {
        return when (type) {
            ReminderType.MEDICATION_TIME -> "该服用${medication.name}了"
            ReminderType.MISSED_DOSE -> "您似乎错过了${medication.name}的服药时间"
            ReminderType.REFILL_REMINDER -> "${medication.name}即将用完，请及时补充"
            ReminderType.DOCTOR_VISIT -> "该复诊检查${medication.name}的使用效果了"
            ReminderType.SIDE_EFFECT_CHECK -> "请注意观察${medication.name}的副作用"
        }
    }

    /**
     * 设置提醒偏好
     */
    fun updateReminderSettings(settings: ReminderSettings) {
        viewModelScope.launch {
            try {
                _state.update { currentState ->
                    currentState.copy(reminderSettings = settings)
                }
                Log.d("MedicationVM", "更新提醒设置成功")
            } catch (e: Exception) {
                Log.e("MedicationVM", "更新提醒设置失败", e)
            }
        }
    }

    /**
     * 延迟提醒
     */
    fun snoozeReminder(reminderId: String, minutes: Int = 5) {
        viewModelScope.launch {
            try {
                val updatedReminders = _state.value.smartReminders.map { reminder ->
                    if (reminder.id == reminderId) {
                        reminder.copy(scheduledTime = reminder.scheduledTime.plusMinutes(minutes.toLong()))
                    } else {
                        reminder
                    }
                }
                
                _state.update { currentState ->
                    currentState.copy(smartReminders = updatedReminders)
                }
                
                Log.d("MedicationVM", "延迟提醒: $reminderId, ${minutes}分钟")
            } catch (e: Exception) {
                Log.e("MedicationVM", "延迟提醒失败", e)
            }
        }
    }

    // ===================== 依从分析功能 =====================

    /**
     * 分析用药依从性
     */
    fun analyzeAdherence() {
        viewModelScope.launch {
            try {
                val medications = _state.value.todayMedications
                if (medications.isNotEmpty()) {
                    val analysis = calculateAdherenceAnalysis(medications)
                    val chartData = generateAdherenceChartData(medications)
                    val missedReasons = analyzeMissedDoseReasons(medications)
                    val suggestions = generatePersonalizedSuggestions(analysis, medications)
                    
                    _state.update { currentState ->
                        currentState.copy(
                            adherenceAnalysis = analysis,
                            weeklyAdherenceChart = chartData,
                            missedDosesByReason = missedReasons,
                            personalizedSuggestions = suggestions
                        )
                    }
                    
                    Log.d("MedicationVM", "依从性分析完成: 总体评分${analysis.overallScore}")
                }
            } catch (e: Exception) {
                Log.e("MedicationVM", "依从性分析失败", e)
            }
        }
    }

    /**
     * 计算依从性分析
     */
    private fun calculateAdherenceAnalysis(medications: List<ProfessionalMedication>): AdherenceAnalysis {
        val completedCount = medications.count { it.isCompleted }
        val totalCount = medications.size
        val overallScore = if (totalCount > 0) (completedCount.toFloat() / totalCount * 100) else 0f
        
        val riskLevel = when {
            overallScore >= 90 -> AdherenceRiskLevel.LOW
            overallScore >= 70 -> AdherenceRiskLevel.MODERATE
            overallScore >= 50 -> AdherenceRiskLevel.HIGH
            else -> AdherenceRiskLevel.CRITICAL
        }
        
        return AdherenceAnalysis(
            overallScore = overallScore,
            weeklyScore = maxOf(0f, overallScore + (Math.random() * 10 - 5).toFloat()),
            monthlyScore = maxOf(0f, overallScore + (Math.random() * 5 - 2.5).toFloat()),
            streak = (Math.random() * 14).toInt() + 1,
            longestStreak = (Math.random() * 30).toInt() + 10,
            totalMissedDoses = totalCount - completedCount,
            improvementTrend = if (overallScore >= 80) TrendDirection.IMPROVING else TrendDirection.STABLE,
            riskLevel = riskLevel
        )
    }

    /**
     * 验证热力图数据的正确性
     */
    private fun validateHeatmapData(dataPoints: List<HeatmapDataPoint>, period: HeatmapPeriod) {
        Log.d("MedicationHeatmap", "=== 数据验证开始 ===")

        val today = TimeZoneUtils.getCurrentDate()
        val expectedSize = period.days

        Log.d("MedicationHeatmap", "期望数据点数量: $expectedSize, 实际数量: ${dataPoints.size}")

        if (dataPoints.size != expectedSize) {
            Log.e("MedicationHeatmap", "❌ 数据点数量不匹配！期望: $expectedSize, 实际: ${dataPoints.size}")
        }

        // 验证日期连续性和正确性
        dataPoints.forEachIndexed { index, point ->
            val expectedDate = when (period) {
                HeatmapPeriod.WEEK -> today.minusDays(6 - index.toLong())
                HeatmapPeriod.MONTH -> today.minusDays(29 - index.toLong())
                HeatmapPeriod.YEAR -> today.minusDays(364 - index.toLong())
            }

            if (point.date != expectedDate) {
                Log.e("MedicationHeatmap", "❌ 索引 $index 日期不匹配！期望: $expectedDate, 实际: ${point.date}")
            } else {
                Log.d("MedicationHeatmap", "✅ 索引 $index 日期正确: ${point.date} (${TimeZoneUtils.getDayOfWeekString(point.date)})")
            }
        }

        // 验证今天是否在数据中
        val todayInData = dataPoints.any { TimeZoneUtils.isToday(it.date) }
        if (todayInData) {
            Log.d("MedicationHeatmap", "✅ 今天的数据已包含在热力图中")
        } else {
            Log.e("MedicationHeatmap", "❌ 今天的数据未包含在热力图中！")
        }

        Log.d("MedicationHeatmap", "=== 数据验证结束 ===")
    }

    /**
     * 生成依从性图表数据
     */
    private fun generateAdherenceChartData(medications: List<ProfessionalMedication>): List<AdherenceChartPoint> {
        val chartData = mutableListOf<AdherenceChartPoint>()
        val today = TimeZoneUtils.getCurrentDate() // 🔧 使用统一时区处理
        
        for (i in 6 downTo 0) {
            val date = today.minusDays(i.toLong())
            val plannedDoses = medications.size
            val actualDoses = (plannedDoses * (0.7 + Math.random() * 0.3)).toInt()
            val onTimeDoses = (actualDoses * (0.8 + Math.random() * 0.2)).toInt()
            val adherenceRate = if (plannedDoses > 0) actualDoses.toFloat() / plannedDoses else 0f
            
            chartData.add(
                AdherenceChartPoint(
                    date = date,
                    adherenceRate = adherenceRate,
                    plannedDoses = plannedDoses,
                    actualDoses = actualDoses,
                    onTimeDoses = onTimeDoses
                )
            )
        }
        
        return chartData
    }

    /**
     * 分析漏服原因
     */
    private fun analyzeMissedDoseReasons(medications: List<ProfessionalMedication>): Map<MissedDoseReason, Int> {
        // 基于用药完成情况的模拟分析
        val missedCount = medications.count { !it.isCompleted }
        return if (missedCount > 0) {
            mapOf(
                MissedDoseReason.FORGOT to (missedCount * 0.4).toInt(),
                MissedDoseReason.TOO_BUSY to (missedCount * 0.3).toInt(),
                MissedDoseReason.SIDE_EFFECTS to (missedCount * 0.1).toInt(),
                MissedDoseReason.FEELING_BETTER to (missedCount * 0.2).toInt()
            )
        } else {
            emptyMap()
        }
    }

    /**
     * 生成个性化建议
     */
    private fun generatePersonalizedSuggestions(
        analysis: AdherenceAnalysis,
        medications: List<ProfessionalMedication>
    ): List<PersonalizedSuggestion> {
        val suggestions = mutableListOf<PersonalizedSuggestion>()
        
        when (analysis.riskLevel) {
            AdherenceRiskLevel.CRITICAL, AdherenceRiskLevel.HIGH -> {
                suggestions.add(
                    PersonalizedSuggestion(
                        id = "suggestion_1",
                        title = "强化用药提醒",
                        description = "您的依从性需要紧急改善，建议设置多重提醒机制",
                        type = SuggestionType.REMINDER_IMPROVEMENT,
                        priority = SuggestionPriority.URGENT,
                        actionText = "立即设置"
                    )
                )
                suggestions.add(
                    PersonalizedSuggestion(
                        id = "suggestion_2",
                        title = "建立服药习惯",
                        description = "尝试将服药与日常活动（如刷牙、吃饭）关联起来",
                        type = SuggestionType.HABIT_BUILDING,
                        priority = SuggestionPriority.HIGH,
                        actionText = "查看方法"
                    )
                )
            }
            AdherenceRiskLevel.MODERATE -> {
                suggestions.add(
                    PersonalizedSuggestion(
                        id = "suggestion_3",
                        title = "优化服药时间",
                        description = "将用药时间调整到您最不容易忘记的时段",
                        type = SuggestionType.TIMING_OPTIMIZATION,
                        priority = SuggestionPriority.MEDIUM,
                        actionText = "调整时间"
                    )
                )
            }
            AdherenceRiskLevel.LOW -> {
                suggestions.add(
                    PersonalizedSuggestion(
                        id = "suggestion_4",
                        title = "保持良好习惯",
                        description = "您的用药依从性很好，继续保持这种良好习惯！",
                        type = SuggestionType.HABIT_BUILDING,
                        priority = SuggestionPriority.LOW
                    )
                )
            }
        }
        
        // 基于药物数量的建议
        if (medications.size >= 3) {
            suggestions.add(
                PersonalizedSuggestion(
                    id = "suggestion_pill_box",
                    title = "使用药盒管理",
                    description = "您有多种药物，建议使用分格药盒来避免混淆",
                    type = SuggestionType.LIFESTYLE_ADJUSTMENT,
                    priority = SuggestionPriority.MEDIUM,
                    actionText = "了解更多"
                )
            )
        }
        
        return suggestions
    }

    /**
     * 记录漏服原因
     */
    fun recordMissedDoseReason(medicationId: String, reason: MissedDoseReason, note: String? = null) {
        viewModelScope.launch {
            try {
                Log.d("MedicationVM", "记录漏服原因: $medicationId, $reason")
                
                // 更新漏服原因统计
                val currentReasons = _state.value.missedDosesByReason.toMutableMap()
                currentReasons[reason] = currentReasons.getOrDefault(reason, 0) + 1
                
                _state.update { currentState ->
                    currentState.copy(missedDosesByReason = currentReasons)
                }
                
                // 重新分析依从性
                analyzeAdherence()
            } catch (e: Exception) {
                Log.e("MedicationVM", "记录漏服原因失败", e)
            }
        }
    }

    /**
     * 标记建议为已完成
     */
    fun markSuggestionCompleted(suggestionId: String) {
        val suggestions = _state.value.personalizedSuggestions.map { suggestion ->
            if (suggestion.id == suggestionId) {
                suggestion.copy(isCompleted = true)
            } else {
                suggestion
            }
        }
        
        _state.update { currentState ->
            currentState.copy(personalizedSuggestions = suggestions)
        }
        
        Log.d("MedicationVM", "建议已完成: $suggestionId")
    }

    /**
     * 初始化智能功能
     */
    private fun initializeSmartFeatures() {
        viewModelScope.launch {
            // 分析依从性
            analyzeAdherence()
            
            // 创建默认提醒
            _state.value.todayMedications.forEach { medication ->
                createSmartReminder(
                    medicationId = medication.id,
                    type = ReminderType.MEDICATION_TIME,
                    scheduledTime = medication.scheduledTime
                )
            }
        }
    }


}

/**
 * 专业用药管理状态
 */
data class ProfessionalMedicationState(
    val isLoading: Boolean = false,
    val todayMedications: List<ProfessionalMedication> = emptyList(),
    val allMedications: List<ProfessionalMedication> = emptyList(),
    val activeMedications: List<ProfessionalMedication> = emptyList(),
    val archivedMedications: List<ProfessionalMedication> = emptyList(),
    val safetyAlerts: List<SafetyAlert> = emptyList(),
    val drugInteractions: List<DrugInteraction> = emptyList(),
    val todayAdherenceRate: Float = 0f,
    val weeklyAdherence: List<Float> = emptyList(),
    val monthlyAdherenceTrend: List<Float> = emptyList(),
    val nextMedicationTime: LocalTime? = null,
    val unreadSafetyAlerts: Int = 0,
    val missedDosesAnalysis: MissedDosesAnalysis? = null,
    val medicationEffectiveness: List<EffectivenessData> = emptyList(),
    val safetyResources: List<SafetyResource> = emptyList(),
    val emergencyContacts: List<EmergencyContact> = emptyList(),
    // 新增智能提醒和依从分析相关状态
    val smartReminders: List<SmartReminder> = emptyList(),
    val adherenceAnalysis: AdherenceAnalysis = AdherenceAnalysis(),
    val reminderSettings: ReminderSettings = ReminderSettings(),
    val weeklyAdherenceChart: List<AdherenceChartPoint> = emptyList(),
    val missedDosesByReason: Map<MissedDoseReason, Int> = emptyMap(),
    val personalizedSuggestions: List<PersonalizedSuggestion> = emptyList()
)

/**
 * 专业用药数据模型
 */
data class ProfessionalMedication(
    val id: String,
    val name: String,
    val dosage: String,
    val frequency: String,
    val scheduledTime: LocalTime,
    val isCompleted: Boolean = false,
    val category: String = "处方药",
    val prescriptionDate: LocalDate? = null,
    val expiryDate: LocalDate? = null,
    val instructions: String = "",
    val sideEffects: List<String> = emptyList(),
    val contraindications: List<String> = emptyList(),
    val reminderEnabled: Boolean = true,
    val reminderBeforeMinutes: Int = 15
)

/**
 * 安全警示
 */
data class SafetyAlert(
    val id: String,
    val message: String,
    val severity: AlertSeverity,
    val timestamp: LocalTime,
    val type: AlertType = AlertType.GENERAL,
    val actionRequired: Boolean = false,
    val relatedMedicationIds: List<String> = emptyList()
)

enum class AlertSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

enum class AlertType {
    GENERAL,
    DRUG_INTERACTION,
    MISSED_DOSE,
    EXPIRY_WARNING,
    SIDE_EFFECT,
    CONTRAINDICATION
}

/**
 * 药物相互作用
 */
data class DrugInteraction(
    val id: String,
    val medication1: String,
    val medication2: String,
    val interactionType: InteractionType,
    val severity: InteractionSeverity,
    val description: String,
    val recommendation: String
)

enum class InteractionType {
    ADDITIVE,
    SYNERGISTIC,
    ANTAGONISTIC,
    INCOMPATIBLE
}

enum class InteractionSeverity {
    MILD, MODERATE, SEVERE, CONTRAINDICATED
}

/**
 * 漏服分析
 */
data class MissedDosesAnalysis(
    val totalMissedDoses: Int,
    val missedDosesByMedication: Map<String, Int>,
    val commonMissedTimes: List<LocalTime>,
    val adherenceScore: Float,
    val recommendations: List<String>
)

/**
 * 药物有效性数据
 */
data class EffectivenessData(
    val medicationId: String,
    val effectivenessScore: Float,
    val patientReportedImprovement: Float,
    val sideEffectScore: Float,
    val overallRating: Float
)

/**
 * 安全资源
 */
data class SafetyResource(
    val id: String,
    val title: String,
    val description: String,
    val type: ResourceType,
    val url: String? = null,
    val phoneNumber: String? = null
)

enum class ResourceType {
    ARTICLE, VIDEO, EMERGENCY_CONTACT, MEDICAL_GUIDE, DRUG_REFERENCE
}

/**
 * 紧急联系人
 */
data class EmergencyContact(
    val id: String,
    val name: String,
    val relationship: String,
    val phoneNumber: String,
    val type: ContactType
)

enum class ContactType {
    FAMILY_DOCTOR, SPECIALIST, EMERGENCY_ROOM, PHARMACY, FAMILY_MEMBER
}

/**
 * 撤销提示状态
 */
data class UndoSnackbarState(
    val medicationId: String,
    val medicationName: String,
    val remainingSeconds: Int
)

/**
 * 删除撤销提示状态
 */
data class DeleteUndoSnackbarState(
    val medicationId: String,
    val medicationName: String,
    val remainingSeconds: Int
)

/**
 * 智能提醒
 */
data class SmartReminder(
    val id: String,
    val medicationId: String,
    val type: ReminderType,
    val scheduledTime: LocalTime,
    val message: String,
    val isActive: Boolean = true,
    val priority: ReminderPriority = ReminderPriority.NORMAL
)

enum class ReminderType {
    MEDICATION_TIME,    // 用药时间提醒
    MISSED_DOSE,        // 漏服提醒
    REFILL_REMINDER,    // 补药提醒
    DOCTOR_VISIT,       // 复诊提醒
    SIDE_EFFECT_CHECK   // 副作用检查
}

enum class ReminderPriority {
    LOW, NORMAL, HIGH, URGENT
}

/**
 * 依从性分析
 */
data class AdherenceAnalysis(
    val overallScore: Float = 0f,           // 总体依从性评分 (0-100)
    val weeklyScore: Float = 0f,            // 本周依从性评分
    val monthlyScore: Float = 0f,           // 本月依从性评分
    val streak: Int = 0,                    // 连续服药天数
    val longestStreak: Int = 0,             // 历史最长连续天数
    val totalMissedDoses: Int = 0,          // 总漏服次数
    val improvementTrend: TrendDirection = TrendDirection.STABLE,
    val riskLevel: AdherenceRiskLevel = AdherenceRiskLevel.LOW
)

enum class TrendDirection {
    IMPROVING,      // 改善中
    DECLINING,      // 下降中
    STABLE,         // 稳定
    FLUCTUATING     // 波动
}

enum class AdherenceRiskLevel {
    LOW,        // 低风险 (>90%)
    MODERATE,   // 中等风险 (70-90%)
    HIGH,       // 高风险 (50-70%)
    CRITICAL    // 严重风险 (<50%)
}

/**
 * 提醒设置
 */
data class ReminderSettings(
    val isEnabled: Boolean = true,
    val advanceMinutes: Int = 15,           // 提前提醒分钟数
    val snoozeMinutes: Int = 5,             // 延迟提醒分钟数
    val maxSnoozeCount: Int = 3,            // 最大延迟次数
    val notificationSound: String = "default",
    val vibrationEnabled: Boolean = true,
    val quietHoursStart: LocalTime = LocalTime.of(22, 0),
    val quietHoursEnd: LocalTime = LocalTime.of(7, 0),
    val weekendReminderEnabled: Boolean = true
)

/**
 * 依从性图表数据点
 */
data class AdherenceChartPoint(
    val date: LocalDate,
    val adherenceRate: Float,           // 当日依从率
    val plannedDoses: Int,              // 计划服药次数
    val actualDoses: Int,               // 实际服药次数
    val onTimeDoses: Int                // 准时服药次数
)

/**
 * 漏服原因
 */
enum class MissedDoseReason {
    FORGOT,                 // 忘记了
    TOO_BUSY,               // 太忙了
    SIDE_EFFECTS,           // 副作用
    FEELING_BETTER,         // 感觉好了
    COST_CONCERNS,          // 费用问题
    TRAVEL,                 // 出行
    RAN_OUT,                // 药品用完
    OTHER                   // 其他原因
}

/**
 * 个性化建议
 */
data class PersonalizedSuggestion(
    val id: String,
    val title: String,
    val description: String,
    val type: SuggestionType,
    val priority: SuggestionPriority,
    val actionText: String? = null,
    val isCompleted: Boolean = false
)

enum class SuggestionType {
    TIMING_OPTIMIZATION,    // 时间优化
    REMINDER_IMPROVEMENT,   // 提醒改进
    HABIT_BUILDING,         // 习惯养成
    SIDE_EFFECT_MANAGEMENT, // 副作用管理
    COST_OPTIMIZATION,      // 费用优化
    LIFESTYLE_ADJUSTMENT    // 生活方式调整
}

enum class SuggestionPriority {
    LOW, MEDIUM, HIGH, URGENT
}

/**
 * 热力图对话框状态
 */
data class HeatmapDialogState(
    val medication: ProfessionalMedication,
    val selectedPeriod: HeatmapPeriod = HeatmapPeriod.WEEK
)

enum class HeatmapPeriod(val days: Int) {
    WEEK(7), MONTH(30), YEAR(365)
}

/**
 * 热力图数据点
 */
data class HeatmapDataPoint(
    val date: LocalDate,
    val value: Float,           // 0.0 - 1.0，表示当天的服药完成度
    val plannedDoses: Int,      // 计划服药次数
    val actualDoses: Int,       // 实际服药次数
    val isOnTime: Boolean = false   // 是否按时服药
)

/**
 * 热力图数据
 */
data class HeatmapData(
    val weekData: List<HeatmapDataPoint> = emptyList(),
    val monthData: List<HeatmapDataPoint> = emptyList(),
    val yearData: List<HeatmapDataPoint> = emptyList(),
    val weeklyAverage: Float = 0f,
    val monthlyAverage: Float = 0f,
    val yearlyAverage: Float = 0f,
    val totalDoses: Int = 0,
    val completedDoses: Int = 0,
    val onTimeDoses: Int = 0
)