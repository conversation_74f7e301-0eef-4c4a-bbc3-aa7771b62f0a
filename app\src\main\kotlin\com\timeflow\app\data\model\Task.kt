package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.serialization.Serializable

/**
 * 任务类型枚举
 */
sealed class TaskType {
    object NORMAL : TaskType()
    object AI : TaskType()
    object RECURRING : TaskType()
    object FLOATING : TaskType() // 新增：浮动任务类型
    
    // 添加获取名称的方法
    fun getName(): String {
        return when(this) {
            NORMAL -> "NORMAL"
            AI -> "AI"
            RECURRING -> "RECURRING"
            FLOATING -> "FLOATING"
        }
    }
    
    companion object {
        fun valueOf(name: String): TaskType {
            return when(name.toUpperCase()) {
                "AI" -> AI
                "RECURRING" -> RECURRING
                "FLOATING" -> FLOATING
                else -> NORMAL
            }
        }
    }
}

/**
 * 任务数据模型 - 支持无限层级结构
 */
data class Task(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val dueDate: LocalDateTime? = null,
    val startDate: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val completedAt: LocalDateTime? = null,
    val isCompleted: Boolean = false,
    val priority: Priority? = null,
    val type: TaskType = TaskType.NORMAL,
    val parentTaskId: String? = null,
    val hasSubtasks: Boolean = false,
    val childTasksCount: Int? = null,         // 子任务总数
    val completedChildTasksCount: Int? = null, // 已完成的子任务数
    val depth: Int = 0, // 树深度，根任务为0
    
    // 任务顺序相关字段
    val orderIndex: Int = 0,                   // 在同一组/父任务内的显示顺序
    
    // 任务分组相关字段
    val groupId: String? = null,               // 所属分组ID
    val groupType: String? = null,             // 分组类型（时间分组、优先级分组、自定义分组等）
    val columnId: String? = null,              // 看板列ID，已弃用
    
    val tags: List<TaskTag> = emptyList(),
    val estimatedTimeMinutes: Int = 0,
    val actualTimeMinutes: Int = 0,
    val progress: Float = 0f, // 完成进度，0-1
    val aiGenerated: Boolean = false,
    val emotionState: String? = null,
    val status: String = "待办", // 任务状态：待办、进行中、审核中、已完成
    val displayInTaskList: Boolean = true,  // 是否在任务列表中显示
    
    // 提醒时间
    val reminderTime: LocalDateTime? = null,
    
    // 关联的目标ID
    val goalId: String? = null,
    
    // 日期是否被用户手动修改过，默认为false表示系统自动设置
    val dateManuallyModified: Boolean = false,
    
    // 🆕 浮动任务相关字段
    val isFloatingTask: Boolean = false,       // 是否为浮动任务
    val floatingWeekStart: LocalDateTime? = null, // 浮动任务所属周的开始时间
    val floatingWeekEnd: LocalDateTime? = null,   // 浮动任务所属周的结束时间
    val scheduledDate: LocalDateTime? = null,     // 用户手动安排到具体日期的时间（拖拽后设置）
    val floatingTaskOrder: Int = 0,             // 在浮动任务列表中的顺序

    // 🔧 循环任务相关字段
    val isRecurring: Boolean = false,          // 是否为循环任务
    val recurringPattern: String? = null       // 循环模式（JSON格式的RecurrenceSettings）
) {
    /**
     * 获取任务完成状态 - 考虑子任务
     */
    fun calculateCompletionStatus(childTasks: List<Task>): Float {
        if (isCompleted) return 1.0f
        if (childTasks.isEmpty()) return if (isCompleted) 1.0f else progress
        
        val childrenCount = childTasks.size
        val completedCount = childTasks.count { it.isCompleted }
        return completedCount.toFloat() / childrenCount.toFloat()
    }
    
    /**
     * 检查任务是否为当前周的浮动任务
     */
    fun isFloatingInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): Boolean {
        if (!isFloatingTask) return false
        return floatingWeekStart != null && floatingWeekEnd != null &&
               floatingWeekStart <= weekEnd && floatingWeekEnd >= weekStart
    }
    
    /**
     * 获取任务的有效显示日期
     * 优先级：scheduledDate > dueDate > startDate > floatingWeekStart
     */
    fun getEffectiveDate(): LocalDateTime? {
        return scheduledDate ?: dueDate ?: startDate ?: floatingWeekStart
    }
    
    /**
     * 检查任务是否已安排到具体日期
     */
    fun isScheduled(): Boolean {
        return scheduledDate != null
    }
}

/**
 * 时间槽数据模型
 */
data class TimeSlot(
    val id: String = UUID.randomUUID().toString(),
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val taskId: String? = null,
    val title: String = "",
    val category: TimeSlotCategory,
    // UI相关属性，用于Canvas绘制
    val startX: Float = 0f,
    val startY: Float = 0f,
    val width: Float = 0f,
    val height: Float = 0f
)

/**
 * 时间槽类别
 */
data class TimeSlotCategory(
    val name: String,
    val color: Color
)
 