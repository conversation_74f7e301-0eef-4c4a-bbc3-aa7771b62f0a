package com.timeflow.app.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel
import com.timeflow.app.ui.screen.analytics.analyticsContent
import com.timeflow.app.utils.NavigationOptimizer

// 定义LocalNavigationDestination作为静态CompositionLocal
val LocalNavigationDestination = staticCompositionLocalOf<String> { "unknown" }

@Composable
fun TimeFlowNavHost(
    navController: NavHostController = rememberNavController(),
    startDestination: String = "home"
) {
    NavHost(navController = navController, startDestination = startDestination) {
        composable("home") {
            // Home screen content
            val pageId = remember { "home" }
            CompositionLocalProvider(LocalNavigationDestination provides pageId) {
                // homeContent()
            }
        }
        
        composable("analytics") {
            // 分析页面 - 使用remember优化页面ID以避免不必要的重组
            val pageId = remember { "analytics" }
            
            // 使用viewModel，确保它能够在配置更改时存活
            val analyticsViewModel = hiltViewModel<AnalyticsViewModel>()
            
            // 使用正确的函数名调用analyticsContent函数
            analyticsContent(
                viewModel = analyticsViewModel,
                onNavigateToSettings = { 
                    // 使用正确的导航方法
                    navController.navigate("settings")
                }
            )
        }
        
        composable("settings") {
            val pageId = remember { "settings" }
            CompositionLocalProvider(LocalNavigationDestination provides pageId) {
                // settingsContent()
            }
        }
        
        // 其他页面路由...
    }
} 