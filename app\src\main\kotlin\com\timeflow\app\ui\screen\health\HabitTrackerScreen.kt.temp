package com.timeflow.app.ui.screen.health

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.timeflow.app.R
import com.timeflow.app.ui.screen.health.FrequencyType
import com.timeflow.app.ui.viewmodel.HabitTemplate
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.math.abs
import kotlin.random.Random

/**
 * 习惯类别
 */
enum class HabitCategory(val title: String, val icon: ImageVector, val color: Color) {
    HEALTH("健康", Icons.Outlined.Favorite, Color(0xFFf5c4c4)),
    LEARNING("学习", Icons.Outlined.School, Color(0xFFb6d9f2)),
    WORK("工作", Icons.Outlined.Work, Color(0xFFb6e2de)),
    LIFE("生活", Icons.Outlined.Home, Color(0xFFfbe3c2)),
    FITNESS("健身", Icons.Outlined.FitnessCenter, Color(0xFFd7c9e8)),
    MINDFULNESS("冥想", Icons.Outlined.SelfImprovement, Color(0xFFdcead1))
}

/**
 * 习惯数据类
 */
data class HabitData(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val color: Color,
    val category: HabitCategory,
    val frequency: List<DayOfWeek> = DayOfWeek.values().toList(),
    val completedDates: List<LocalDate> = emptyList(),
    val streak: Int = 0,
    val description: String = "",
    val createdAt: LocalDate = LocalDate.now(),
    val customEmoji: String = "" // 添加自定义emoji字段
)

/**
 * 习惯追踪页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HabitTrackerScreen(
    onBackClick: () -> Unit,
    onHabitDetailClick: (String) -> Unit = {}, // 添加导航到习惯详情的回调
    viewModel: HabitViewModel = androidx.lifecycle.viewmodel.compose.viewModel()
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 使用SystemBarManager设置状态栏
    DisposableEffect(Unit) {
        var originalStatusBarColor: Int = 0
        var originalNavigationBarColor: Int = 0
        
        activity?.let { act ->
            val window = act.window
            
            Log.d("HabitTrackerScreen", "开始设置状态栏")
            
            try {
                // 保存原始状态
                originalStatusBarColor = window.statusBarColor
                originalNavigationBarColor = window.navigationBarColor
                
                // 使用SystemBarManager设置不透明的黑色状态栏
                SystemBarManager.forceOpaqueStatusBar(act)
                
                Log.d("HabitTrackerScreen", "状态栏设置成功")
            } catch (e: Exception) {
                Log.e("HabitTrackerScreen", "设置状态栏出错: ${e.message}")
            }
        }
        
        onDispose {
            activity?.let { act ->
                try {
                    // 恢复原始颜色
                    act.window.statusBarColor = originalStatusBarColor
                    act.window.navigationBarColor = originalNavigationBarColor
                    Log.d("HabitTrackerScreen", "HabitTrackerScreen disposed - 恢复状态栏")
                } catch (e: Exception) {
                    Log.e("HabitTrackerScreen", "恢复状态栏出错: ${e.message}")
                }
            }
        }
    }
    
    // 页面状态
    val showAddHabitSheet = viewModel.showAddHabitDialog.collectAsState()
    val selectedCategory = viewModel.selectedCategory.collectAsState()
    val pagerState = rememberPagerState(pageCount = { HabitCategory.values().size + 1 }) // +1 for "All" category
    
    // 从ViewModel获取习惯列表
    val habitsList = viewModel.habits.collectAsState().value
    
    // 更新习惯的emoji的函数
    fun updateHabitEmoji(habitId: String, emoji: String) {
        val habit = habitsList.find { it.id == habitId } ?: return
        val updatedHabit = habit.copy(customEmoji = emoji)
        viewModel.updateHabit(updatedHabit)
        Log.d("HabitTracker", "习惯 ${habit.name} 的表情已更新为: $emoji")
    }

    // 修改添加习惯对话框显示逻辑
    if (showAddHabitSheet.value) {
        AddHabitBottomSheet(
            onDismiss = { viewModel.setShowAddHabitDialog(false) },
            onSave = { newHabit ->
                // 使用ViewModel添加习惯
                viewModel.addHabit(newHabit)
                viewModel.setShowAddHabitDialog(false)
            }
        )
    }
    
    // 使用ViewModel控制弹窗显示
    FloatingActionButton(
        onClick = { viewModel.setShowAddHabitDialog(true) },
        modifier = Modifier
            .padding(16.dp)
    ) {
        Icon(Icons.Filled.Add, contentDescription = "添加习惯")
    }
    
    Scaffold(
        topBar = {
            // 使用最简单的Box实现自定义TopAppBar，确保不会产生渲染问题
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 使用固定的状态栏高度
                    .height(56.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 返回按钮
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color(0xFF554A60)
                        )
                    }
                    
                    // 标题文本
                    Text(
                        text = "习惯追踪",
                        fontSize = SystemBarManager.getStandardTitleTextSize().value.sp, // 使用标准标题大小
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF554A60),
                        maxLines = 1,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 添加按钮
                    IconButton(
                        onClick = { viewModel.setShowAddHabitDialog(true) },
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加习惯",
                            tint = Color(0xFF9575CD)
                        )
                    }
                }
            }
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.setShowAddHabitDialog(true) },
                containerColor = Color(0xFFd7c9e8),
                shape = CircleShape,
                elevation = FloatingActionButtonDefaults.elevation(
                    defaultElevation = 6.dp
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加习惯",
                    tint = Color.White
                )
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF8F9FA))
        ) {
            // 先显示"今日概览"统计卡片
            HabitStatsCard(habits = habitsList)
            
            // 然后显示分类选项卡
            TabRow(
                selectedTabIndex = pagerState.currentPage,
                containerColor = Color.White,
                contentColor = Color(0xFFd7c9e8),
                divider = {},
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp, bottom = 8.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color.White)
            ) {
                // "全部"选项卡
                Tab(
                    selected = pagerState.currentPage == 0,
                    onClick = {
                        viewModel.setSelectedCategory(null)
                    },
                    text = {
                        Text("全部")
                    },
                    selectedContentColor = Color(0xFFd7c9e8),
                    unselectedContentColor = Color.Gray
                )
                
                // 各分类选项卡
                HabitCategory.values().forEachIndexed { index, category ->
                    Tab(
                        selected = pagerState.currentPage == index + 1,
                        onClick = {
                            viewModel.setSelectedCategory(category)
                        },
                        icon = {
                            Icon(
                                imageVector = category.icon,
                                contentDescription = category.title,
                                modifier = Modifier.size(20.dp)
                            )
                        },
                        text = {
                            Text(category.title, fontSize = 14.sp)
                        },
                        selectedContentColor = category.color,
                        unselectedContentColor = Color.Gray
                    )
                }
            }
            
            // 主要内容
            LazyColumn(
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.fillMaxSize()
            ) {
                // 习惯列表标题
                item {
                    Text(
                        text = "我的习惯",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF554A60),
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
                
                // 习惯列表
                val filteredHabits = if (selectedCategory.value != null) {
                    habitsList.filter { it.category == selectedCategory.value }
                } else {
                    habitsList
                }
                
                itemsIndexed(filteredHabits) { _, habit ->
                    HabitListItem(
                        habit = habit,
                        onHabitClick = { onHabitDetailClick(habit.id) }, // 点击习惯时导航到详情页
                        onCheckClick = { date ->
                            // 当前已完成，调用时传入false来撤销；当前未完成，调用时传入true来打卡
                            val isCurrentlyCompleted = habit.completedDates.contains(date)
                            viewModel.markHabitAsCompleted(habit.id, date, !isCurrentlyCompleted) // 使用ViewModel标记完成
                            
                            // 创建一个临时变量来存储Toast信息
                            val toastMessage = if (isCurrentlyCompleted) "已撤销 ${habit.name} 的打卡" else "已完成 ${habit.name} 的打卡"
                            
                            // 使用LaunchedEffect执行副作用
                            LaunchedEffect(key1 = date, key2 = isCurrentlyCompleted) {
                                // 在Composable上下文中显示Toast提示
                                android.widget.Toast.makeText(
                                    context,
                                    toastMessage,
                                    android.widget.Toast.LENGTH_SHORT
                                ).show()
                            }
                        },
                        onEmojiUpdate = { id, emoji ->
                            updateHabitEmoji(id, emoji)
                        }
                    )
                }
                
                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(80.dp))
                }
            }
        }
    }
}

/**
 * 吉卜力风格欢迎条幅
 */
@Composable
fun WelcomeHabitBanner() {
    val today = LocalDate.now()
    val formatter = DateTimeFormatter.ofPattern("MM月dd日")
    val formattedDate = today.format(formatter)
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(130.dp)
            .padding(16.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                            Color(0xFFE8F5E9),  // 浅草绿
                            Color(0xFFDCEDC8)   // 更浅的草绿
                    )
                )
            )
    ) {
            // 吉卜力风格的装饰元素 - 简单绘制一些草地和小草
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
                // 草地小丘轮廓
                for (i in 0..5) {
                    val y = size.height - (30 + Random.nextInt(15)).dp.toPx()
                    val startX = size.width * (i / 6f)
                    val controlX1 = startX + 60.dp.toPx()
                    val controlY1 = y - 40.dp.toPx()
                    val controlX2 = startX + 120.dp.toPx()
                    val controlY2 = y - 30.dp.toPx()
                    val endX = startX + 180.dp.toPx()
                    
                    drawLine(
                        color = Color(0xFF81C784).copy(alpha = 0.3f),
                        start = Offset(startX, y),
                        end = Offset(endX, y),
                        strokeWidth = 2.dp.toPx()
                    )
                }
                
                // 小草和花朵
                for (i in 0..15) {
                val x = size.width * Random.nextFloat()
                    val y = size.height - (5 + Random.nextInt(20)).dp.toPx()
                    val grassHeight = (5 + Random.nextInt(8)).dp.toPx()
                    
                    // 小草
                    drawLine(
                        color = Color(0xFF7CB342).copy(alpha = 0.4f),
                        start = Offset(x, y),
                        end = Offset(x, y - grassHeight),
                        strokeWidth = 1.dp.toPx()
                    )
                    
                    // 随机加一些花朵
                    if (Random.nextFloat() > 0.7f) {
                drawCircle(
                            color = listOf(
                                Color(0xFFFFF9C4), // 浅黄色
                                Color(0xFFE1BEE7), // 浅紫色
                                Color(0xFFFFCCBC)  // 浅橙色
                            ).random().copy(alpha = 0.6f),
                            radius = 2.dp.toPx(),
                            center = Offset(x, y - grassHeight)
                        )
                    }
                }
                
                // 几朵柔和的云
                for (i in 0..2) {
                    val cloudX = size.width * (0.2f + i * 0.3f)
                    val cloudY = 10.dp.toPx() + Random.nextInt(15).dp.toPx()
                    val cloudRadius = (10 + Random.nextInt(8)).dp.toPx()
                    
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius,
                        center = Offset(cloudX, cloudY)
                    )
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius * 0.7f,
                        center = Offset(cloudX + cloudRadius * 0.8f, cloudY)
                    )
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius * 0.6f,
                        center = Offset(cloudX - cloudRadius * 0.7f, cloudY)
                )
            }
        }
        
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧文本
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "今天是 $formattedDate",
                        color = Color(0xFF5D4037), // 温暖的褐色，吉卜力常用
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "养成好习惯，每天都进步",
                        color = Color(0xFF33691E), // 深绿色
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                }
                
                // 右侧小图标 - 模拟吉卜力风格的小静物
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(80.dp)
            ) {
                    // 浅色圆形背景
                Box(
                    modifier = Modifier
                            .size(60.dp)
                        .clip(CircleShape)
                            .background(Color.White.copy(alpha = 0.7f))
                )
                
                    // 奖杯图标 - 使用吉卜力风格柔和的颜色
                Icon(
                    imageVector = Icons.Outlined.EmojiEvents,
                        contentDescription = "Habit Trophy",
                        tint = Color(0xFFFFB74D).copy(alpha = 0.8f), // 柔和的金色
                    modifier = Modifier.size(40.dp)
                )
                }
            }
        }
    }
}

/**
 * 习惯统计卡片
 */
@Composable
fun HabitStatsCard(habits: List<HabitData>) {
    val today = LocalDate.now()
    
    // 计算相关统计数据
    val totalHabits = habits.size
    val completedToday = habits.count { habit ->
        habit.completedDates.contains(today)
    }
    val completionRate = if (totalHabits > 0) {
        (completedToday.toFloat() / totalHabits) * 100
    } else {
        0f
    }
    val longestStreak = habits.maxOfOrNull { it.streak } ?: 0
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),  // 减小外部padding
        shape = RoundedCornerShape(16.dp),  // 减小圆角
        colors = CardDefaults.cardColors(
            containerColor = Color.White  // 修改为完全不透明
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)  // 减小内部padding
        ) {
            Text(
                text = "今日概览",
                fontSize = 16.sp,  // 减小标题文字大小
                fontWeight = FontWeight.Bold,
                color = Color(0xFF554A60)
            )
            
            Spacer(modifier = Modifier.height(10.dp))  // 减小间距
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 总习惯数
                StatItem(
                    value = totalHabits.toString(),
                    label = "总习惯",
                    icon = Icons.Outlined.AssignmentTurnedIn,
                    color = Color(0xFF64B5F6),
                    iconSize = 20.dp,  // 减小图标大小
                    boxSize = 42.dp,   // 减小外框大小
                    valueSize = 16.sp, // 减小数值文字大小
                    labelSize = 10.sp  // 减小标签文字大小
                )
                
                // 今日完成
                StatItem(
                    value = "$completedToday/$totalHabits",
                    label = "今日完成",
                    icon = Icons.Outlined.Done,
                    color = Color(0xFF81C784),
                    iconSize = 20.dp,
                    boxSize = 42.dp,
                    valueSize = 16.sp,
                    labelSize = 10.sp
                )
                
                // 最长连续
                StatItem(
                    value = "$longestStreak",
                    label = "最长连续",
                    icon = Icons.Outlined.LocalFireDepartment,
                    color = Color(0xFFFFB74D),
                    iconSize = 20.dp,
                    boxSize = 42.dp,
                    valueSize = 16.sp,
                    labelSize = 10.sp
                )
            }
            
            Spacer(modifier = Modifier.height(10.dp))  // 减小间距
            
            // 完成率进度条
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "今日完成率",
                        fontSize = 12.sp,  // 减小文字大小
                        color = Color(0xFF554A60)
                    )
                    
                    Text(
                        text = "${completionRate.toInt()}%",
                        fontSize = 12.sp,  // 减小文字大小
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF9575CD)
                    )
                }
                
                Spacer(modifier = Modifier.height(6.dp))  // 减小间距
                
                LinearProgressIndicator(
                    progress = { completionRate / 100f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(6.dp)  // 减小进度条高度
                        .clip(RoundedCornerShape(3.dp)),  // 调整进度条圆角
                    color = Color(0xFFd7c9e8),
                    trackColor = Color(0xFFE0E0E0)
                )
            }
        }
    }
}

/**
 * 统计项
 */
@Composable
fun StatItem(
    value: String,
    label: String,
    icon: ImageVector,
    color: Color,
    iconSize: Dp = 24.dp,
    boxSize: Dp = 50.dp,
    valueSize: TextUnit = 18.sp,
    labelSize: TextUnit = 12.sp
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(boxSize)
                .clip(CircleShape)
                .background(color.copy(alpha = 0.1f))
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(iconSize)
            )
        }
        
        Spacer(modifier = Modifier.height(6.dp))  // 减小间距
        
        Text(
            text = value,
            fontSize = valueSize,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF554A60)
        )
        
        Text(
            text = label,
            fontSize = labelSize,
            color = Color(0xFF8C8C8C)
        )
    }
}

/**
 * 习惯列表项
 */
@Composable
fun HabitListItem(
    habit: HabitData,
    onHabitClick: () -> Unit,
    onCheckClick: (LocalDate) -> Unit,
    onEmojiUpdate: (String, String) -> Unit
) {
    val today = LocalDate.now()
    val isCompletedToday = habit.completedDates.contains(today)
    
    // Add haptic feedback
    val haptic = LocalHapticFeedback.current
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 动画状态 - 使用多个动画状态提供丰富反馈
    val buttonScale = remember { Animatable(1f) }
    val starsAlpha = remember { Animatable(0f) }
    val checkmarkScale = remember { Animatable(0f) } // 勾选标记的缩放动画
    // 使用颜色状态而不是颜色整数值
    val defaultColor = Color(0xFFF0F0F0)
    val buttonColor = remember { mutableStateOf(if (isCompletedToday) habit.color else defaultColor) }
    var showStars by remember { mutableStateOf(false) }
    
    // 改进的动画效果
    LaunchedEffect(showStars) {
        if (showStars) {
            // 同时开始星星渐显
            starsAlpha.snapTo(1f)
            
            // 更自然的弹性弹跳动画
            buttonScale.animateTo(
                targetValue = 1.3f, // 更大幅度的缩放
                animationSpec = spring(
                    dampingRatio = 0.4f, // 更弹性的阻尼比
                    stiffness = 400f
                )
            )
            
            // 按钮颜色变化
            buttonColor.value = habit.color.copy(alpha = 0.7f)
            
            // 勾选标记显示动画
            checkmarkScale.animateTo(
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = 0.5f,
                    stiffness = 300f
                )
            )
            
            // 缩小回正常大小
            buttonScale.animateTo(
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = 0.6f,
                    stiffness = 200f
                )
            )
            
            // 延迟一会儿再让星星淡出
            delay(400)
            
            // 星星慢慢淡出
            starsAlpha.animateTo(
                targetValue = 0f,
                animationSpec = tween(600)
            )
            
            // 动画完成后重置状态
            showStars = false
        }
    }
    
    // 更新状态监听
    LaunchedEffect(isCompletedToday) {
        // 根据完成状态设置勾选标记的初始缩放
        checkmarkScale.snapTo(if (isCompletedToday) 1f else 0f)
        
        // 根据完成状态设置背景颜色
        buttonColor.value = if (isCompletedToday) habit.color.copy(alpha = 0.7f) else defaultColor
    }
    
    // 添加对最近完成状态变化的监听
    val wasCompletedToday = remember { mutableStateOf(isCompletedToday) }

    // 当完成状态从false变为true时，触发庆祝动画
    LaunchedEffect(isCompletedToday) {
        if (isCompletedToday && !wasCompletedToday.value) {
            // 没有变化，不触发动画
        } else if (!isCompletedToday && wasCompletedToday.value) {
            // 取消完成，播放取消动画
            buttonScale.animateTo(
                targetValue = 0.9f,
                animationSpec = tween(100)
            )
            buttonScale.animateTo(
                targetValue = 1f,
                animationSpec = tween(100)
            )
            
            // 颜色恢复动画
            buttonColor.value = defaultColor
            
            // 勾选标记淡出
            checkmarkScale.animateTo(
                targetValue = 0f,
                animationSpec = tween(200)
            )
        }
        
        // 更新记忆状态
        wasCompletedToday.value = isCompletedToday
    }
    
    // 新增：控制emoji选择器对话框显示
    var showEmojiPicker by remember { mutableStateOf(false) }
    
    // 计算完成进度
    val daysInMonth = today.lengthOfMonth()
    val completedThisMonth = habit.completedDates.count { it.month == today.month && it.year == today.year }
    val progress = completedThisMonth.toFloat() / daysInMonth
    
    // 替换呼吸效果动画为静态效果
    val itemScale = if (isCompletedToday) 1.01f else 1f  // 使用固定缩放代替动画
    
    // 显示emoji选择器对话框
    if (showEmojiPicker) {
        EmojiPickerDialog(
            onDismiss = { showEmojiPicker = false },
            onEmojiSelected = { emoji ->
                onEmojiUpdate(habit.id, emoji)
                showEmojiPicker = false
            }
        )
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .graphicsLayer {
                // 完成时有轻微放大效果
                scaleX = itemScale
                scaleY = itemScale
            }
            .clickable { onHabitClick() },
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Box {
            // 内容
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 表情状态系统 - 根据习惯类型和完成状态显示不同表情
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    habit.color.copy(alpha = 0.2f),
                                    habit.color.copy(alpha = 0.05f)
                                )
                            )
                        )
                        .padding(8.dp)
                        .clickable { showEmojiPicker = true } // 添加点击事件打开emoji选择器
                ) {
                    // 显示自定义emoji或者默认表情
                    val displayEmoji = if (habit.customEmoji.isNotEmpty()) {
                        habit.customEmoji
                    } else {
                        when {
                        isCompletedToday -> "🐈" // 猫咪举奖杯
                        habit.streak > 0 -> "🐇" // 兔子托腮等待
                        else -> "🐶" // 小狗捂脸哭
                        }
                    }
                    
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = displayEmoji,
                            fontSize = 24.sp,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Icon(
                            imageVector = habit.icon,
                            contentDescription = habit.name,
                            tint = habit.color,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 习惯信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = habit.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF554A60)
                    )
                    
                    if (habit.description.isNotEmpty()) {
                        Text(
                            text = habit.description,
                            fontSize = 12.sp,
                            color = Color(0xFF8C8C8C),
                            maxLines = 1
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 进度图标 - 根据习惯类型显示不同图标
                    val progressIcon = when {
                        habit.name.contains("喝水") -> "💧"
                        habit.name.contains("阅读") -> "📖"
                        else -> "✨"
                    }
                    
                    // 完成进度条 - 毛绒效果
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = progressIcon,
                            fontSize = 16.sp,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        
                        Box(modifier = Modifier.weight(1f)) {
                            // 进度条底部
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(10.dp)
                                    .clip(RoundedCornerShape(5.dp))
                                    .background(Color(0xFFEEEEEE))
                            )
                            
                            // 进度条填充
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth(progress)
                                    .height(10.dp)
                                    .clip(RoundedCornerShape(5.dp))
                                    .background(
                                        brush = Brush.horizontalGradient(
                                            colors = listOf(
                                                habit.color.copy(alpha = 0.7f),
                                                habit.color
                                            )
                                        )
                                    )
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "${completedThisMonth}/$daysInMonth",
                            fontSize = 12.sp,
                            color = Color(0xFF8C8C8C)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 打卡按钮和连续天数
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 打卡按钮盒子 - 增加包装以显示星星和涟漪效果
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(48.dp)
                    ) {
                        // 涟漪效果 - 仅在动画激活时显示
                        if (showStars) {
                            Box(
                                modifier = Modifier
                                    .size(60.dp)
                                    .border(
                                        width = 2.dp,
                                        color = habit.color.copy(alpha = starsAlpha.value * 0.7f),
                                        shape = CircleShape
                                    )
                                    .graphicsLayer {
                                        scaleX = 1f + (1f - starsAlpha.value) * 0.8f
                                        scaleY = 1f + (1f - starsAlpha.value) * 0.8f
                                        alpha = (1f - abs(0.5f - starsAlpha.value) * 2f) * 0.7f
                                    }
                            )
                        }

                    // 打卡按钮 - 使用自定义可爱按钮
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(if (isCompletedToday) habit.color.copy(alpha = 0.7f) else buttonColor.value)
                            .clickable { 
                                // 根据当前状态切换打卡/撤销打卡
                                // isCompletedToday为true时撤销打卡，false时打卡
                                onCheckClick(today)
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                
                                // 仅标记状态变量，动画逻辑在LaunchedEffect中执行
                                if (!isCompletedToday) {
                                    showStars = true
                                }
                            }
                            .drawBehind {
                                // 添加细微阴影效果增强立体感
                                drawCircle(
                                    color = Color.Black.copy(alpha = 0.05f),
                                    radius = size.width/2 + 1.dp.toPx(),
                                    center = center.copy(y = center.y + 1.dp.toPx())
                                )
                            }
                            .graphicsLayer {
                                scaleX = buttonScale.value
                                scaleY = buttonScale.value
                                shadowElevation = 2f
                                spotShadowColor = Color.Black.copy(alpha = 0.1f)
                            }
                        ) {
                            // 使用图片中的样式：完成时显示勾选标记，未完成时显示图标
                            // 勾选标记 - 使用缩放动画
                            Icon(
                                imageVector = if (isCompletedToday) Icons.Rounded.Check else Icons.Rounded.Add,
                                contentDescription = if (isCompletedToday) "撤销打卡" else "打卡",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(24.dp)
                                    .graphicsLayer { 
                                        scaleX = if (isCompletedToday) checkmarkScale.value else 1f
                                        scaleY = if (isCompletedToday) checkmarkScale.value else 1f
                                        alpha = if (isCompletedToday) checkmarkScale.value else 1f
                                    }
                            )
                            
                            // 未完成时显示的图标
                            if (!isCompletedToday) {
                                // 未勾选状态的图标或文字 - 根据图片使用水滴或太阳图标
                                if (habit.name.contains("喝水")) {
                                    Icon(
                                        imageVector = Icons.Rounded.WaterDrop,
                                        contentDescription = "水滴",
                                        tint = Color(0xFF29B6F6), // 浅蓝色
                                        modifier = Modifier
                                            .size(20.dp)
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                } else if (habit.name.contains("早起")) {
                                    Icon(
                                        imageVector = Icons.Rounded.WbSunny,
                                        contentDescription = "太阳",
                                        tint = Color(0xFFFFA726), // 橙色
                                        modifier = Modifier
                                            .size(20.dp)
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                } else {
                            Text(
                                text = "✓",
                                        color = Color(0xFF9E9E9E), // 灰色
                                fontSize = 20.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                }
                            }
                        }
                        
                        // 添加打卡状态提示文字
                        if (isCompletedToday) {
                            Box(
                                modifier = Modifier
                                    .offset(y = 48.dp)
                                    .width(70.dp)
                                    .alpha(0.7f),
                                contentAlignment = Alignment.Center
                            ) {
                            Text(
                                    text = "点击撤销",
                                    color = habit.color,
                                    fontSize = 10.sp,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                        
                        // 小星星效果 - 在按钮周围添加
                        if (showStars) {
                            // 右上方星星
                            Text(
                                text = "✨",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 24.dp, y = (-18).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                            
                            // 左上方星星
                            Text(
                                text = "✨",
                                fontSize = 12.sp,
                                modifier = Modifier
                                    .offset(x = (-18).dp, y = (-15).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                            
                            // 上方星星
                            Text(
                                text = "✨",
                                fontSize = 14.sp,
                                modifier = Modifier
                                    .offset(x = 5.dp, y = (-25).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 连续天数 - 火焰图标
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "🔥",
                            fontSize = 14.sp,
                            modifier = Modifier.padding(end = 2.dp)
                        )
                        
                        Text(
                            text = "${habit.streak}天",
                            fontSize = 12.sp,
                            color = Color(0xFFFF7043),
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加习惯底部弹窗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddHabitBottomSheet(
    onDismiss: () -> Unit,
    onSave: (HabitData) -> Unit,
    viewModel: HabitViewModel = androidx.lifecycle.viewmodel.compose.viewModel()
) {
    // 使用ViewModel中的表单状态
    val formState by viewModel.habitFormState.collectAsState()
    
    // 获取可用颜色和图标
    val availableColors by viewModel.availableColors.collectAsState()
    val availableIcons by viewModel.availableIcons.collectAsState()
    val habitTemplates by viewModel.habitTemplates.collectAsState()
    
    // 本地UI状态
    var habitName by remember { mutableStateOf(formState.name) }
    var habitDescription by remember { mutableStateOf(formState.description) }
    var selectedCategory by remember { mutableStateOf(formState.category) }
    var selectedIcon by remember { mutableStateOf(formState.icon) }
    var selectedColor by remember { mutableStateOf(formState.color) }
    
    // 频率选择状态
    var selectedFrequencyType by remember { mutableStateOf(formState.frequencyType) }
    var selectedDays by remember { mutableStateOf(formState.selectedDays) }
    
    // 额外设置状态
    var enableReminder by remember { mutableStateOf(formState.enableReminder) }
    var showInPlan by remember { mutableStateOf(formState.showInPlan) }
    var isQuickMode by remember { mutableStateOf(formState.isQuickMode) }
    var viewStyleIndex by remember { mutableStateOf(formState.viewStyleIndex) }
    var heatmapColorIndex by remember { mutableStateOf(formState.heatmapColorIndex) }
    
    // 颜色选择器状态
    var showColorPicker by remember { mutableStateOf(false) }
    
    // 图标选择器状态
    var showIconPicker by remember { mutableStateOf(false) }
    
    // 模板选择器状态
    var showTemplates by remember { mutableStateOf(false) }
    
    // 日期选择
    val startDate = remember { formState.startDate }
    
    // 分组选择
    var selectedGroup by remember { mutableStateOf("生活") }
    val habitGroups = remember { listOf("生活", "学习", "工作", "健康") }
    
    // 输入框聚焦请求器
    val focusRequester = remember { FocusRequester() }
    
    // 表单验证
    val formErrors = remember { mutableStateMapOf<String, String?>() }
    
    // 底部弹窗状态
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    
    // 更新ViewModel中的表单状态
    LaunchedEffect(habitName, habitDescription, selectedCategory, selectedIcon, 
        selectedColor, selectedFrequencyType, selectedDays, enableReminder, 
        showInPlan, isQuickMode) {
        viewModel.updateHabitFormField("name", habitName)
        viewModel.updateHabitFormField("description", habitDescription)
        viewModel.updateHabitFormField("category", selectedCategory)
        viewModel.updateHabitFormField("icon", selectedIcon)
        viewModel.updateHabitFormField("color", selectedColor)
        viewModel.updateHabitFormField("frequencyType", selectedFrequencyType)
        viewModel.updateHabitFormField("enableReminder", enableReminder)
        
        // 更新选中的星期几
        selectedDays.forEach { day ->
            viewModel.updateSelectedDays(day, true)
        }
    }
    
    ModalBottomSheet(
        onDismissRequest = {
            viewModel.resetHabitForm()
            onDismiss()
        },
        sheetState = sheetState,
        dragHandle = { BottomSheetDefaults.DragHandle() },
        containerColor = Color.White,
        contentColor = Color(0xFF3C3C43),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 32.dp)
        ) {
            // 顶部标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                TextButton(onClick = { 
                    viewModel.resetHabitForm()
                    onDismiss() 
                }) {
                    Text(
                        text = "取消",
                        color = Color(0xFF3C3C43),
                        fontSize = 16.sp
                    )
                }
                
                Text(
                    text = "添加习惯",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF3C3C43)
                )
                
                TextButton(
                    onClick = {
                        val errors = viewModel.validateForm()
                        if (errors.isEmpty()) {
                            viewModel.createHabitFromForm()
                            onDismiss()
                        } else {
                            formErrors.clear()
                            formErrors.putAll(errors)
                        }
                    },
                    enabled = habitName.isNotEmpty()
                ) {
                    Text(
                        text = "保存",
                        color = if (habitName.isNotEmpty()) selectedColor else Color.Gray,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 模板选择器按钮
            if (!showTemplates) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = { showTemplates = true },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = selectedColor
                        )
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                imageVector = Icons.Outlined.List,
                                contentDescription = "习惯库",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("习惯库", fontSize = 14.sp)
                        }
                    }
                }
            }
            
            // 习惯模板选择器
            AnimatedVisibility(
                visible = showTemplates,
                enter = androidx.compose.animation.fadeIn(
                    initialAlpha = 0.0f,
                    animationSpec = tween(150)
                ),
                exit = androidx.compose.animation.fadeOut(
                    targetAlpha = 0.0f,
                    animationSpec = tween(150)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    // 标题和关闭按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "选择习惯模板",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        
                        IconButton(onClick = { showTemplates = false }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = Color.Gray
                            )
                        }
                    }
                    
                    // 模板列表
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        contentPadding = PaddingValues(vertical = 8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        items(habitTemplates.size) { index ->
                            val template = habitTemplates[index]
                            TemplateItem(
                                template = template,
                                onClick = {
                                    // 应用模板到表单
                                    habitName = template.name
                                    habitDescription = template.description
                                    selectedCategory = template.category
                                    selectedIcon = template.icon
                                    selectedColor = template.color
                                    selectedFrequencyType = template.frequencyType
                                    selectedDays = template.days
                                    
                                    showTemplates = false
                                }
                            )
                        }
                    }
                    
                    Divider(modifier = Modifier.padding(vertical = 8.dp))
                }
            }
            
            // 内容区域 - 可滚动
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp)
            ) {
                Spacer(modifier = Modifier.height(8.dp))
                
                // 基本信息卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        // 习惯名称 - 红色必填标记
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .width(4.dp)
                                    .height(16.dp)
                                    .background(Color.Red)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Column {
                                OutlinedTextField(
                                    value = habitName,
                                    onValueChange = { habitName = it },
                                    placeholder = { Text("名称(必填)") },
                                    colors = TextFieldDefaults.outlinedTextFieldColors(
                                        focusedBorderColor = Color.Transparent,
                                        unfocusedBorderColor = Color.Transparent
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                                    isError = formErrors["name"] != null,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .focusRequester(focusRequester)
                                )
                                
                                // 错误提示
                                formErrors["name"]?.let { error ->
                                    Text(
                                        text = error,
                                        color = MaterialTheme.colorScheme.error,
                                        style = MaterialTheme.typography.bodySmall,
                                        modifier = Modifier.padding(start = 4.dp)
                                    )
                                }
                            }
                        }
                        
                        Divider(color = Color(0xFFEEEEEE))
                        
                        // 习惯描述
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .width(4.dp)
                                    .height(16.dp)
                                    .background(Color(0xFFCCCCCC))
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            OutlinedTextField(
                                value = habitDescription,
                                onValueChange = { habitDescription = it },
                                placeholder = { Text("习惯描述(可选)") },
                                colors = TextFieldDefaults.outlinedTextFieldColors(
                                    focusedBorderColor = Color.Transparent,
                                    unfocusedBorderColor = Color.Transparent
                                ),
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 颜色选择卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showColorPicker = !showColorPicker }
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "颜色",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43),
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 颜色选择器 - 显示当前选择的颜色
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape)
                                    .background(selectedColor)
                                    .border(1.dp, Color(0xFFEEEEEE), CircleShape)
                            )
                        }
                        
                        // 颜色选择器展开面板
                        AnimatedVisibility(
                            visible = showColorPicker,
                            enter = androidx.compose.animation.fadeIn(
                                initialAlpha = 0.0f,
                                animationSpec = tween(150)
                            ),
                            exit = androidx.compose.animation.fadeOut(
                                targetAlpha = 0.0f,
                                animationSpec = tween(150)
                            )
                        ) {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(6),
                                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier
                                    .heightIn(max = 120.dp)
                                    .fillMaxWidth()
                            ) {
                                items(availableColors) { color ->
                                    ColorItem(
                                        color = color,
                                        isSelected = selectedColor == color,
                                        onClick = {
                                            selectedColor = color
                                            showColorPicker = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图标选择卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showIconPicker = !showIconPicker }
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "图标",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43),
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 当前选择的图标
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(selectedColor.copy(alpha = 0.1f))
                                    .border(1.dp, selectedColor.copy(alpha = 0.3f), RoundedCornerShape(8.dp))
                            ) {
                                Icon(
                                    imageVector = selectedIcon,
                                    contentDescription = "选择图标",
                                    tint = selectedColor,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                        
                        // 图标选择器展开面板
                        AnimatedVisibility(
                            visible = showIconPicker,
                            enter = androidx.compose.animation.fadeIn(
                                initialAlpha = 0.0f,
                                animationSpec = tween(150)
                            ),
                            exit = androidx.compose.animation.fadeOut(
                                targetAlpha = 0.0f,
                                animationSpec = tween(150)
                            )
                        ) {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(5),
                                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier
                                    .heightIn(max = 200.dp)
                                    .fillMaxWidth()
                            ) {
                                items(availableIcons) { iconPair ->
                                    val (icon, description) = iconPair
                                    IconItem(
                                        icon = icon,
                                        description = description,
                                        isSelected = selectedIcon == icon,
                                        color = selectedColor,
                                        onClick = {
                                            selectedIcon = icon
                                            showIconPicker = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 提醒设置卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "开启提醒",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43),
                            modifier = Modifier.weight(1f)
                        )
                        
                        Switch(
                            checked = enableReminder,
                            onCheckedChange = { enableReminder = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedTrackColor = selectedColor,
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color(0xFFE0E0E0)
                            )
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 快速打卡模式
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "快速打卡模式",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43),
                            modifier = Modifier.weight(1f)
                        )
                        
                        Switch(
                            checked = isQuickMode,
                            onCheckedChange = { isQuickMode = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedTrackColor = selectedColor,
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color(0xFFE0E0E0)
                            )
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 展示风格卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "展示风格",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 视图切换按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            ViewStyleButton(
                                text = "日视图",
                                isSelected = viewStyleIndex == 0,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 0 }
                            )
                            
                            ViewStyleButton(
                                text = "周视图",
                                isSelected = viewStyleIndex == 1,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 1 }
                            )
                            
                            ViewStyleButton(
                                text = "月视图",
                                isSelected = viewStyleIndex == 2,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 2 }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 热力图颜色卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = "热力图颜色",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43)
                            )
                            
                            // 帮助按钮
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .size(24.dp)
                                    .clip(CircleShape)
                                    .border(1.dp, Color(0xFFE0E0E0), CircleShape)
                            ) {
                                Text(
                                    text = "?",
                                    fontSize = 14.sp,
                                    color = Color(0xFF3C3C43)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 热力图颜色选择按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            ViewStyleButton(
                                text = "渐变色",
                                isSelected = heatmapColorIndex == 0,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 0 }
                            )
                            
                            ViewStyleButton(
                                text = "单一色",
                                isSelected = heatmapColorIndex == 1,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 1 }
                            )
                            
                            ViewStyleButton(
                                text = "标签色",
                                isSelected = heatmapColorIndex == 2,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 2 }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分组卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "分组",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 分组标签按钮
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            habitGroups.forEach { group ->
                                GroupButton(
                                    text = group,
                                    isSelected = selectedGroup == group,
                                    color = if (group == "生活") Color(0xFF6c5ce7)
                                        else if (group == "学习") Color(0xFF0984e3)
                                        else if (group == "工作") Color(0xFFfdcb6e)
                                        else Color(0xFF00b894),
                                    onClick = { selectedGroup = group }
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 想在一天什么时候完成它
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "想在一天什么时候完成它",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 时间选择按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            TimeOfDayButton(
                                text = "全天",
                                isSelected = true,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "上午",
                                isSelected = false,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "下午",
                                isSelected = false,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "晚上",
                                isSelected = false,
                                color = selectedColor
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 选择频率
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "选择打卡频率",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 频率类型按钮
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            FrequencyTypeButton(
                                icon = Icons.Outlined.Schedule,
                                text = "固定",
                                isSelected = selectedFrequencyType == FrequencyType.DAILY,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = FrequencyType.DAILY }
                            )
                            
                            FrequencyTypeButton(
                                icon = Icons.Outlined.DateRange,
                                text = "每周",
                                isSelected = selectedFrequencyType == FrequencyType.WEEKLY,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = FrequencyType.WEEKLY }
                            )
                            
                            FrequencyTypeButton(
                                icon = Icons.Outlined.CalendarViewMonth,
                                text = "每月",
                                isSelected = selectedFrequencyType == FrequencyType.MONTHLY,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = FrequencyType.MONTHLY }
                            )
                        }
                        
                        // 如果选择每周，显示星期选择器
                        if (selectedFrequencyType == FrequencyType.WEEKLY) {
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // 星期选择器
                            Row(
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // 周一到周日
                                listOf(
                                    DayOfWeek.MONDAY to "一",
                                    DayOfWeek.TUESDAY to "二",
                                    DayOfWeek.WEDNESDAY to "三",
                                    DayOfWeek.THURSDAY to "四",
                                    DayOfWeek.FRIDAY to "五",
                                    DayOfWeek.SATURDAY to "六",
                                    DayOfWeek.SUNDAY to "日"
                                ).forEach { (day, label) ->
                                    val isSelected = selectedDays.contains(day)
                                    
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier
                                            .size(36.dp)
                                            .clip(CircleShape)
                                            .background(
                                                if (isSelected) selectedColor
                                                else Color(0xFFF0F0F0)
                                            )
                                            .clickable {
                                                selectedDays = if (isSelected) {
                                                    // 至少要选择一天
                                                    if (selectedDays.size > 1)
                                                        selectedDays - day
                                                    else selectedDays
                                                } else {
                                                    selectedDays + day
                                                }
                                            }
                                    ) {
                                        Text(
                                            text = label,
                                            color = if (isSelected) Color.White
                                                  else Color(0xFF3C3C43),
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
    
    // 请求焦点到名称输入框
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

/**
 * 频率类型
 */
    DAILY, WEEKLY, MONTHLY
}

/**
 * 频率类型按钮
 */
@Composable
fun FrequencyTypeButton(
    icon: ImageVector,
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = if (isSelected) color else Color.Gray,
                modifier = Modifier.size(16.dp)
            )
            
            Text(
                text = text,
                fontSize = 14.sp,
                color = if (isSelected) color else Color.Gray
            )
        }
    }
}

/**
 * 视图风格按钮
 */
@Composable
fun ViewStyleButton(
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 20.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) color else Color.Gray
        )
    }
}

/**
 * 分组按钮
 */
@Composable
fun GroupButton(
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(if (isSelected) color else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) Color.White else Color.Gray
        )
    }
}

/**
 * 时间段按钮
 */
@Composable
fun TimeOfDayButton(
    text: String,
    isSelected: Boolean,
    color: Color
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
            .padding(horizontal = 24.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) color else Color.Gray
        )
    }
}

/**
 * Emoji选择对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmojiPickerDialog(
    onDismiss: () -> Unit,
    onEmojiSelected: (String) -> Unit
) {
    var emojiText by remember { mutableStateOf("") }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "自定义图标",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF554A60)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "输入你喜欢的emoji表情:",
                    fontSize = 14.sp,
                    color = Color(0xFF8C8C8C)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Emoji输入框
                OutlinedTextField(
                    value = emojiText,
                    onValueChange = { newValue ->
                        // 限制只输入一个emoji
                        if (newValue.isEmpty() || newValue.length <= 2) {
                            emojiText = newValue
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    textStyle = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 40.sp,
                        textAlign = TextAlign.Center
                    ),
                    placeholder = {
                        Text(
                            text = "😊",
                            fontSize = 40.sp,
                            textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                        )
                    },
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 常用emoji建议
                Text(
                    text = "推荐表情",
                    fontSize = 14.sp,
                    color = Color(0xFF8C8C8C),
                    modifier = Modifier.align(Alignment.Start)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 常用emoji网格
                val commonEmojis = listOf("😊", "🌱", "🚀", "💪", "📚", "💧", "🍎", "🏃", "🧘", "🎯", "⭐", "🌈")
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(commonEmojis) { emoji ->
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .size(48.dp)
                                .clip(RoundedCornerShape(12.dp))
                                .background(Color(0xFFF5F5F5))
                                .clickable { emojiText = emoji }
                        ) {
                            Text(
                                text = emoji,
                                fontSize = 24.sp
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = onDismiss
                    ) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 清除按钮
                    TextButton(
                        onClick = {
                            emojiText = ""
                            onEmojiSelected("")
                            onDismiss()
                        }
                    ) {
                        Text("清除")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            onEmojiSelected(emojiText)
                            onDismiss()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFd7c9e8)
                        )
                    ) {
                        Text("确认")
                    }
                }
            }
        }
    }
}

/**
 * 模板项
 */
@Composable
fun TemplateItem(
    template: HabitTemplate,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(100.dp)
            .clip(RoundedCornerShape(12.dp))
            .clickable(onClick = onClick)
            .border(
                width = 1.dp,
                color = template.color.copy(alpha = 0.3f),
                shape = RoundedCornerShape(12.dp)
            )
            .background(template.color.copy(alpha = 0.05f))
            .padding(12.dp)
    ) {
        // 图标
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(template.color.copy(alpha = 0.1f))
                .padding(8.dp)
        ) {
            Icon(
                imageVector = template.icon,
                contentDescription = template.name,
                tint = template.color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 名称
            Text(
            text = template.name,
                fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF3C3C43),
            textAlign = TextAlign.Center
        )
        
        // 频率标签
        val frequencyText = when(val freq = template.frequencyType) {
            FrequencyType.DAILY -> "每天"
            FrequencyType.WEEKLY -> {
                val days = template.days.size
                if (days == 7) "每天" else "每周${days}天"
            }
            FrequencyType.MONTHLY -> "每月"
            else -> "自定义" // 添加else分支处理未知类型
        }
        
        Text(
            text = frequencyText,
            fontSize = 12.sp,
            color = Color(0xFF8C8C8C),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 颜色选择项
 */
@Composable
fun ColorItem(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(color)
            .border(
                width = 2.dp,
                color = if (isSelected) Color.White else Color.Transparent,
                shape = CircleShape
            )
            .padding(4.dp)
            .clickable(onClick = onClick)
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 图标选择项
 */
@Composable
fun IconItem(
    icon: ImageVector,
    description: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(if (isSelected) color.copy(alpha = 0.2f) else Color(0xFFF5F5F5))
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(onClick = onClick)
            .padding(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = description,
            tint = if (isSelected) color else Color.Gray,
            modifier = Modifier.size(24.dp)
        )
    }
} 
