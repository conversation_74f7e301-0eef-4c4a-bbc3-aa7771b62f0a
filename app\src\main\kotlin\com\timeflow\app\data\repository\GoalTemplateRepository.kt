package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.GoalTemplateDao
import com.timeflow.app.data.entity.GoalTemplate as GoalTemplateEntity
import com.timeflow.app.data.entity.GoalSubTaskTemplate as GoalSubTaskTemplateEntity
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.model.GoalSubTaskTemplate
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.RecurringPeriod
import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.data.model.ReminderType
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.utils.TemplateCache
import com.timeflow.app.utils.PagedTemplateResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.flow
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import org.json.JSONArray
import org.json.JSONObject
import android.util.Log

/**
 * 目标模板仓库接口，定义目标模板相关业务功能
 */
interface GoalTemplateRepository {
    suspend fun saveTemplate(template: GoalTemplate): String
    suspend fun updateTemplate(template: GoalTemplate)
    suspend fun deleteTemplate(templateId: String)
    suspend fun getTemplateById(templateId: String): GoalTemplate?
    fun getAllTemplates(): Flow<List<GoalTemplate>>
    fun getTemplatesByCategory(category: String): Flow<List<GoalTemplate>>
    fun getTemplatesByCategoryId(categoryId: String): Flow<List<GoalTemplate>>
    fun getMostUsedTemplates(limit: Int): Flow<List<GoalTemplate>>
    fun getRecentlyUsedTemplates(limit: Int): Flow<List<GoalTemplate>>
    fun searchTemplates(query: String): Flow<List<GoalTemplate>>
    
    // 根据模板创建目标
    suspend fun createGoalFromTemplate(templateId: String, customTitle: String? = null): Goal?
    
    // 从现有目标创建模板
    suspend fun createTemplateFromGoal(goalId: String, templateName: String, category: String): GoalTemplate?
    
    // 记录模板使用
    suspend fun recordTemplateUsage(templateId: String)
    
    // 添加分页支持
    suspend fun getTemplatesPaged(page: Int, pageSize: Int): PagedTemplateResult
    suspend fun getTemplatesByCategoryPaged(category: String, page: Int, pageSize: Int): PagedTemplateResult
    suspend fun searchTemplatesPaged(query: String, page: Int, pageSize: Int): PagedTemplateResult
    
    // 清除缓存
    suspend fun clearCache()
}

/**
 * 目标模板仓库实现类
 */
@Singleton
class GoalTemplateRepositoryImpl @Inject constructor(
    private val templateDao: GoalTemplateDao,
    private val goalRepository: GoalRepository,
    private val templateCache: TemplateCache
) : GoalTemplateRepository {
    
    override suspend fun saveTemplate(template: GoalTemplate): String {
        val id = template.id.ifEmpty { UUID.randomUUID().toString() }
        val templateEntity = mapModelToEntity(template.copy(id = id))
        templateDao.insertTemplate(templateEntity)
        
        // 保存子任务模板
        templateDao.deleteAllSubTaskTemplatesForTemplate(id)
        val subTaskEntities = template.subTaskTemplates.map { 
            mapSubTaskModelToEntity(it, id) 
        }
        if (subTaskEntities.isNotEmpty()) {
            templateDao.insertSubTaskTemplates(subTaskEntities)
        }
        
        // 更新缓存
        val updatedTemplate = template.copy(id = id)
        templateCache.cacheTemplate(updatedTemplate)
        templateCache.clearAllCaches() // 清除列表缓存，因为新增了模板
        
        return id
    }
    
    override suspend fun updateTemplate(template: GoalTemplate) {
        val templateEntity = mapModelToEntity(template)
        templateDao.updateTemplate(templateEntity)
        
        // 更新子任务模板
        templateDao.deleteAllSubTaskTemplatesForTemplate(template.id)
        val subTaskEntities = template.subTaskTemplates.map { 
            mapSubTaskModelToEntity(it, template.id) 
        }
        if (subTaskEntities.isNotEmpty()) {
            templateDao.insertSubTaskTemplates(subTaskEntities)
        }
        
        // 更新缓存
        templateCache.cacheTemplate(template)
        templateCache.clearAllCaches() // 清除列表缓存，因为更新了模板
    }
    
    override suspend fun deleteTemplate(templateId: String) {
        templateDao.deleteTemplateById(templateId)
        // 级联删除子任务模板会自动处理
        
        // 从缓存中移除
        templateCache.removeTemplate(templateId)
    }
    
    override suspend fun getTemplateById(templateId: String): GoalTemplate? {
        // 尝试从缓存获取
        val cachedTemplate = templateCache.getTemplate(templateId)
        if (cachedTemplate != null) {
            return cachedTemplate
        }
        
        // 缓存中不存在，从数据库获取
        val templateEntity = templateDao.getTemplateById(templateId) ?: return null
        val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(templateId)
        val template = mapEntityToModel(templateEntity, subTaskEntities)
        
        // 更新缓存
        templateCache.cacheTemplate(template)
        
        return template
    }
    
    override fun getAllTemplates(): Flow<List<GoalTemplate>> {
        return flow {
            // 尝试从缓存获取
            val cachedTemplates = templateCache.getAllTemplates()
            if (cachedTemplates != null) {
                emit(cachedTemplates)
                return@flow
            }
            
            // 缓存中不存在，从数据库获取
            val entities = templateDao.getAllTemplatesSync()
            val templates = entities.map { entity ->
                val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                mapEntityToModel(entity, subTaskEntities)
            }
            
            // 更新缓存
            templateCache.cacheAllTemplates(templates)
            
            emit(templates)
        }
    }
    
    override fun getTemplatesByCategory(category: String): Flow<List<GoalTemplate>> {
        return templateDao.getTemplatesByCategory(category).map { entities ->
            val templates = entities.map { entity ->
                val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                mapEntityToModel(entity, subTaskEntities)
            }

            // 按类别缓存
            templateCache.cacheAllTemplates(templates, "category_$category")

            templates
        }
    }

    override fun getTemplatesByCategoryId(categoryId: String): Flow<List<GoalTemplate>> {
        return templateDao.getTemplatesByCategoryId(categoryId).map { entities ->
            val templates = entities.map { entity ->
                val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                mapEntityToModel(entity, subTaskEntities)
            }

            // 按分类ID缓存
            templateCache.cacheAllTemplates(templates, "categoryId_$categoryId")

            templates
        }
    }
    
    override fun getMostUsedTemplates(limit: Int): Flow<List<GoalTemplate>> {
        return flow {
            // 尝试从缓存获取
            val cachedTemplates = templateCache.getAllTemplates("most_used_$limit")
            if (cachedTemplates != null) {
                emit(cachedTemplates)
                return@flow
            }
            
            // 从数据库获取
            templateDao.getMostUsedTemplates(limit).collect { entities ->
                val templates = entities.map { entity ->
                    val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                    mapEntityToModel(entity, subTaskEntities)
                }
                
                // 更新缓存
                templateCache.cacheAllTemplates(templates, "most_used_$limit")
                
                emit(templates)
            }
        }
    }
    
    override fun getRecentlyUsedTemplates(limit: Int): Flow<List<GoalTemplate>> {
        return flow {
            // 尝试从缓存获取
            val cachedTemplates = templateCache.getAllTemplates("recently_used_$limit")
            if (cachedTemplates != null) {
                emit(cachedTemplates)
                return@flow
            }
            
            // 从数据库获取
            templateDao.getRecentlyUsedTemplates(limit).collect { entities ->
                val templates = entities.map { entity ->
                    val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                    mapEntityToModel(entity, subTaskEntities)
                }
                
                // 更新缓存
                templateCache.cacheAllTemplates(templates, "recently_used_$limit")
                
                emit(templates)
            }
        }
    }
    
    override fun searchTemplates(query: String): Flow<List<GoalTemplate>> {
        return flow {
            // 对于搜索，不使用缓存，因为查询条件变化很快
            templateDao.searchTemplates(query).collect { entities ->
                val templates = entities.map { entity ->
                    val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
                    mapEntityToModel(entity, subTaskEntities)
                }
                emit(templates)
            }
        }
    }
    
    override suspend fun createGoalFromTemplate(templateId: String, customTitle: String?): Goal? {
        // 查找模板
        val template = getTemplateById(templateId) ?: return null
        
        // 记录使用
        recordTemplateUsage(templateId)
        
        // TODO: 从模板创建目标的实现
        return null
    }
    
    override suspend fun createTemplateFromGoal(goalId: String, templateName: String, category: String): GoalTemplate? {
        // TODO: 从目标创建模板的实现
        return null
    }
    
    override suspend fun recordTemplateUsage(templateId: String) {
        try {
            // 查找模板
            val template = templateDao.getTemplateById(templateId) ?: return
            
            // 更新使用次数和最后使用时间
            val updatedTemplate = template.copy(
                usageCount = template.usageCount + 1,
                lastUsed = LocalDateTime.now()
            )
            
            // 保存更新
            templateDao.updateTemplate(updatedTemplate)
            
            // 清除缓存
            templateCache.removeTemplate(templateId)
        } catch (e: Exception) {
            Log.e("GoalTemplateRepo", "记录模板使用失败: ${e.message}")
        }
    }
    
    // 添加分页支持实现
    override suspend fun getTemplatesPaged(page: Int, pageSize: Int): PagedTemplateResult {
        // 生成缓存键
        val pageKey = templateCache.generatePageKey(page, pageSize)
        
        // 尝试从缓存获取
        val cachedResult = templateCache.getPagedTemplates(pageKey)
        if (cachedResult != null) {
            return cachedResult
        }
        
        // 计算偏移量
        val offset = (page - 1) * pageSize
        
        // 获取总数
        val totalCount = templateDao.getTemplatesCount()
        
        // 获取当前页数据
        val entities = templateDao.getTemplatesPaged(pageSize, offset)
        
        // 转换成模型
        val templates = entities.map { entity ->
            val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
            mapEntityToModel(entity, subTaskEntities)
        }
        
        // 判断是否有下一页
        val hasNextPage = offset + templates.size < totalCount
        
        // 创建分页结果
        val result = PagedTemplateResult(
            templates = templates,
            totalCount = totalCount,
            hasNextPage = hasNextPage,
            page = page,
            pageSize = pageSize
        )
        
        // 缓存结果
        templateCache.cachePagedTemplates(pageKey, result)
        
        return result
    }
    
    override suspend fun getTemplatesByCategoryPaged(category: String, page: Int, pageSize: Int): PagedTemplateResult {
        // 生成缓存键
        val pageKey = templateCache.generatePageKey(page, pageSize, category)
        
        // 尝试从缓存获取
        val cachedResult = templateCache.getPagedTemplates(pageKey)
        if (cachedResult != null) {
            return cachedResult
        }
        
        // 计算偏移量
        val offset = (page - 1) * pageSize
        
        // 获取总数
        val totalCount = templateDao.getTemplatesCountByCategory(category)
        
        // 获取当前页数据
        val entities = templateDao.getTemplatesByCategoryPaged(category, pageSize, offset)
        
        // 转换成模型
        val templates = entities.map { entity ->
            val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
            mapEntityToModel(entity, subTaskEntities)
        }
        
        // 判断是否有下一页
        val hasNextPage = offset + templates.size < totalCount
        
        // 创建分页结果
        val result = PagedTemplateResult(
            templates = templates,
            totalCount = totalCount,
            hasNextPage = hasNextPage,
            page = page,
            pageSize = pageSize
        )
        
        // 缓存结果
        templateCache.cachePagedTemplates(pageKey, result)
        
        return result
    }
    
    override suspend fun searchTemplatesPaged(query: String, page: Int, pageSize: Int): PagedTemplateResult {
        // 对于搜索，不使用缓存，因为查询条件变化很快
        // 计算偏移量
        val offset = (page - 1) * pageSize
        
        // 获取总数
        val totalCount = templateDao.searchTemplatesCount(query)
        
        // 获取当前页数据
        val entities = templateDao.searchTemplatesPaged(query, pageSize, offset)
        
        // 转换成模型
        val templates = entities.map { entity ->
            val subTaskEntities = templateDao.getSubTaskTemplatesForTemplate(entity.id)
            mapEntityToModel(entity, subTaskEntities)
        }
        
        // 判断是否有下一页
        val hasNextPage = offset + templates.size < totalCount
        
        // 创建分页结果
        return PagedTemplateResult(
            templates = templates,
            totalCount = totalCount,
            hasNextPage = hasNextPage,
            page = page,
            pageSize = pageSize
        )
    }
    
    override suspend fun clearCache() {
        templateCache.clearAllCaches()
    }
    
    // 以下为实体和模型间的映射方法
    private fun mapModelToEntity(model: GoalTemplate): GoalTemplateEntity {
        // 转换RecurringSettings为JSON
        val recurringSettingsJson = if (model.recurringSettings != null) {
            JSONObject().apply {
                put("frequency", model.recurringSettings.frequency.name)
                if (model.recurringSettings.weeklyDays.isNotEmpty()) {
                    put("weeklyDays", JSONArray().apply {
                        model.recurringSettings.weeklyDays.forEach { put(it.value) }
                    })
                }
                put("interval", model.recurringSettings.interval)
                model.recurringSettings.endCount?.let { put("count", it) }
                model.recurringSettings.endDate?.let { put("endDate", it.toString()) }
            }.toString()
        } else {
            "{}"
        }
        
        // 转换ReminderSettings为JSON
        val reminderSettingsJson = if (model.defaultReminderSettings.isNotEmpty()) {
            JSONArray().apply {
                model.defaultReminderSettings.forEach { reminderSetting ->
                    put(JSONObject().apply {
                        put("time", reminderSetting.time?.toString())
                        put("type", reminderSetting.type.name)
                        put("enabled", reminderSetting.enabled)
                        reminderSetting.message?.let { put("message", it) }
                    })
                }
            }.toString()
        } else {
            "[]"
        }
        
        // 将Model转为Entity
        return GoalTemplateEntity(
            id = model.id,
            name = model.name,
            description = model.description,
            category = model.category,
            categoryId = model.categoryId,
            iconName = model.iconName,
            colorHex = model.colorHex,
            usageCount = model.usageCount,
            lastUsed = model.lastUsed,
            createdAt = model.createdAt,
            defaultTitle = model.defaultTitle,
            defaultDescription = model.defaultDescription,
            defaultPriority = model.defaultPriority.name,
            defaultTags = model.defaultTags.joinToString(","),
            defaultDurationDays = model.defaultDurationDays,
            goalType = model.goalType.name,
            defaultTargetValue = model.defaultTargetValue,
            defaultUnit = model.defaultUnit,
            isRecurring = model.isRecurring,
            recurringSettingsJson = recurringSettingsJson,
            reminderSettingsJson = reminderSettingsJson
        )
    }
    
    private fun mapSubTaskModelToEntity(model: GoalSubTaskTemplate, templateId: String): GoalSubTaskTemplateEntity {
        return GoalSubTaskTemplateEntity(
            id = model.id.ifEmpty { UUID.randomUUID().toString() },
            templateId = templateId,
            title = model.title,
            description = model.description,
            estimatedDurationDays = model.estimatedDurationDays,
            orderIndex = model.orderIndex
        )
    }
    
    private fun mapEntityToModel(
        entity: GoalTemplateEntity,
        subTaskEntities: List<GoalSubTaskTemplateEntity>
    ): GoalTemplate {
        // 解析Tag
        val tags = if (entity.defaultTags.isNotEmpty()) {
            entity.defaultTags.split(",")
        } else {
            emptyList()
        }
        
        // 解析RecurringSettings
        val recurringSettings = try {
            val json = JSONObject(entity.recurringSettingsJson)
            if (json.length() > 0) {
                RecurringSettings(
                    frequency = RecurringPeriod.valueOf(
                        json.optString("frequency", RecurringPeriod.WEEKLY.name)
                    ),
                    weeklyDays = if (json.has("weeklyDays")) {
                        val weeklyDaysArray = json.getJSONArray("weeklyDays")
                        (0 until weeklyDaysArray.length()).map { i ->
                            java.time.DayOfWeek.of(weeklyDaysArray.getInt(i))
                        }.toSet()
                    } else emptySet(),
                    interval = json.optInt("interval", 1),
                    endCount = if (json.has("count")) json.optInt("count") else null,
                    endDate = if (json.has("endDate")) {
                        java.time.LocalDate.parse(json.getString("endDate"))
                    } else null
                )
            } else null
        } catch (e: Exception) {
            null
        }
        
        // 解析ReminderSettings
        val reminderSettings = try {
            val jsonArray = JSONArray(entity.reminderSettingsJson)
            (0 until jsonArray.length()).map { i ->
                val reminderJson = jsonArray.getJSONObject(i)
                ReminderSetting(
                    time = if (reminderJson.has("time") && !reminderJson.isNull("time")) 
                        java.time.LocalTime.parse(reminderJson.getString("time")) else null,
                    type = if (reminderJson.has("type")) 
                        ReminderType.valueOf(reminderJson.getString("type")) else ReminderType.ONCE,
                    enabled = reminderJson.optBoolean("enabled", true),
                    message = if (reminderJson.has("message")) reminderJson.getString("message") else ""
                )
            }
        } catch (e: Exception) {
            emptyList()
        }
        
        // 转换子任务
        val subTaskTemplates = subTaskEntities.map { subTaskEntity ->
            GoalSubTaskTemplate(
                id = subTaskEntity.id,
                title = subTaskEntity.title,
                description = subTaskEntity.description,
                estimatedDurationDays = subTaskEntity.estimatedDurationDays,
                orderIndex = subTaskEntity.orderIndex
            )
        }
        
        return GoalTemplate(
            id = entity.id,
            name = entity.name,
            description = entity.description,
            category = entity.category,
            categoryId = entity.categoryId,
            iconName = entity.iconName,
            colorHex = entity.colorHex,
            usageCount = entity.usageCount,
            lastUsed = entity.lastUsed,
            createdAt = entity.createdAt,
            defaultTitle = entity.defaultTitle,
            defaultDescription = entity.defaultDescription,
            defaultPriority = try {
                GoalPriority.valueOf(entity.defaultPriority)
            } catch (e: Exception) {
                GoalPriority.MEDIUM
            },
            defaultTags = tags,
            defaultDurationDays = entity.defaultDurationDays,
            goalType = try {
                GoalType.valueOf(entity.goalType)
            } catch (e: Exception) {
                GoalType.BOOLEAN
            },
            defaultTargetValue = entity.defaultTargetValue,
            defaultUnit = entity.defaultUnit,
            isRecurring = entity.isRecurring,
            recurringSettings = recurringSettings,
            defaultReminderSettings = reminderSettings,
            subTaskTemplates = subTaskTemplates
        )
    }
} 