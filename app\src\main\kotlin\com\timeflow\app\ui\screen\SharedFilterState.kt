package com.timeflow.app.ui.screen

import com.timeflow.app.data.model.Priority
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 共享过滤状态，用于在多个屏幕间共享任务过滤条件
 */
object SharedFilterState {
    private val _filterByPriority = MutableStateFlow<Priority?>(null)
    val filterByPriority: StateFlow<Priority?> = _filterByPriority.asStateFlow()
    
    private val _filterByTagName = MutableStateFlow<String?>(null)
    val filterByTagName: StateFlow<String?> = _filterByTagName.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    private val _showCompleted = MutableStateFlow(false)
    val showCompleted: StateFlow<Boolean> = _showCompleted.asStateFlow()
    
    fun updatePriorityFilter(priority: Priority?) {
        _filterByPriority.value = priority
    }
    
    fun updateTagFilter(tagName: String?) {
        _filterByTagName.value = tagName
    }
    
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    fun updateShowCompleted(show: Boolean) {
        _showCompleted.value = show
    }
    
    fun clearFilters() {
        _filterByPriority.value = null
        _filterByTagName.value = null
        _searchQuery.value = ""
    }
} 