package com.timeflow.app.ui.screen.milestone

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.border
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.layout.ContentScale
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import android.net.Uri

/**
 * 里程碑编辑屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditMilestoneScreen(
    navController: NavController,
    milestoneId: String? = null,
    viewModel: MilestoneViewModel = hiltViewModel()
) {
    // 设置状态栏
    val context = LocalContext.current
    DisposableEffect(Unit) {
        (context as? android.app.Activity)?.let {
            SystemBarManager.setupLightModeSystemBars(it)
        }
        onDispose {}
    }
    
    val coroutineScope = rememberCoroutineScope()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 编辑状态
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var category by remember { mutableStateOf(MilestoneCategory.LIFE) }
    var date by remember { mutableStateOf(LocalDate.now()) }
    var tags by remember { mutableStateOf(listOf<String>()) }
    var newTag by remember { mutableStateOf("") }
    var rating by remember { mutableStateOf(0f) }
    var milestoneType by remember { mutableStateOf(MilestoneType.REGULAR) }
    var completionPercentage by remember { mutableStateOf(0f) }
    var imageUris by remember { mutableStateOf<List<String>>(emptyList()) }
    
    // 初始化编辑数据
    LaunchedEffect(milestoneId, uiState.milestones) {
        if (milestoneId != null) {
            // 查找现有里程碑
            val milestone = uiState.milestones.find { it.id == milestoneId }
            milestone?.let {
                title = it.title
                description = it.description
                category = it.category
                date = it.date
                tags = it.tags
                rating = it.rating
                milestoneType = it.milestoneType
                completionPercentage = it.completionPercentage
                imageUris = it.imageUris
            }
        }
    }
    
    // 验证表单
    val isValid = title.isNotBlank() && description.isNotBlank()
    
    // 日期选择器
    var showDatePicker by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F6F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { 
                Text(
                    text = if (milestoneId != null) "编辑里程碑" else "添加里程碑",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                TextButton(
                    onClick = {
                        if (isValid) {
                            val milestone = Milestone(
                                id = milestoneId ?: "new_${System.currentTimeMillis()}",
                                title = title,
                                description = description,
                                date = date,
                                category = category,
                                tags = tags,
                                rating = rating,
                                completionPercentage = completionPercentage,
                                milestoneType = milestoneType,
                                imageUris = imageUris
                            )
                            
                            coroutineScope.launch {
                                if (milestoneId != null) {
                                    viewModel.updateMilestone(milestone)
                                } else {
                                    viewModel.addMilestone(milestone)
                                }
                                navController.popBackStack()
                            }
                        }
                    },
                    enabled = isValid
                ) {
                    Text("保存")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent,
                navigationIconContentColor = MaterialTheme.colorScheme.onBackground
            )
        )
        
        // 主内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题输入
            OutlinedTextField(
                value = title,
                onValueChange = { title = it },
                label = { Text("标题") },
                placeholder = { Text("输入里程碑标题...") },
                singleLine = true,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp)
            )
            
            // 类别选择
            Text(
                text = "选择类别",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // 类别选择网格
            CategorySelectionGrid(
                selectedCategory = category,
                onCategorySelected = { category = it }
            )
            
            // 日期选择
            OutlinedCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showDatePicker = true },
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "日期",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        
                        Text(
                            text = date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 16.sp,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "选择",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 里程碑类型选择
            Text(
                text = "里程碑类型",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // 里程碑类型选择
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                MilestoneTypeChip(
                    type = MilestoneType.REGULAR,
                    selected = milestoneType == MilestoneType.REGULAR,
                    onClick = { milestoneType = MilestoneType.REGULAR },
                    modifier = Modifier.weight(1f)
                )
                
                MilestoneTypeChip(
                    type = MilestoneType.DECISION,
                    selected = milestoneType == MilestoneType.DECISION,
                    onClick = { milestoneType = MilestoneType.DECISION },
                    modifier = Modifier.weight(1f)
                )
                
                MilestoneTypeChip(
                    type = MilestoneType.OPPORTUNITY,
                    selected = milestoneType == MilestoneType.OPPORTUNITY,
                    onClick = { milestoneType = MilestoneType.OPPORTUNITY },
                    modifier = Modifier.weight(1f)
                )
                
                MilestoneTypeChip(
                    type = MilestoneType.IMPACT,
                    selected = milestoneType == MilestoneType.IMPACT,
                    onClick = { milestoneType = MilestoneType.IMPACT },
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 描述输入 - 移到图片选择前面
            Text(
                text = "详细描述",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            RichTextEditor(
                value = description,
                onValueChange = { description = it },
                placeholder = "记录下这个重要时刻的详细内容...",
                modifier = Modifier.fillMaxWidth()
            )
            
            // 图片选择功能
            Text(
                text = "图片",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // 图片选择器
            val imagePicker = rememberLauncherForActivityResult(
                contract = ActivityResultContracts.GetContent()
            ) { uri: Uri? ->
                uri?.let {
                    // 添加到现有集合而不是替换
                    imageUris = imageUris + it.toString()
                }
            }
            
            // 图片预览和选择组件 - 现代化改版
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 多图横向滚动预览
                if (imageUris.isNotEmpty()) {
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp)
                            .padding(vertical = 8.dp)
                    ) {
                        items(imageUris) { uri ->
                            // 图片项
                            Box(
                                modifier = Modifier
                                    .size(160.dp)
                                    .clip(RoundedCornerShape(12.dp))
                            ) {
                                // 图片
                                Image(
                                    painter = rememberAsyncImagePainter(
                                        model = uri.toUri(),
                                        error = painterResource(R.drawable.ic_image_placeholder)
                                    ),
                                    contentDescription = null,
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Crop
                                )
                                
                                // 删除按钮
                                IconButton(
                                    onClick = { 
                                        imageUris = imageUris.filter { it != uri }
                                    },
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(4.dp)
                                        .size(32.dp)
                                        .clip(CircleShape)
                                        .background(Color(0x99000000))
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "删除图片",
                                        tint = Color.White,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                        
                        // 添加更多图片按钮
                        item {
                            Box(
                                modifier = Modifier
                                    .size(160.dp)
                                    .clip(RoundedCornerShape(12.dp))
                                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f))
                                    .border(
                                        width = 1.dp,
                                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                                        shape = RoundedCornerShape(12.dp)
                                    )
                                    .clickable { imagePicker.launch("image/*") },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "添加更多图片",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(36.dp)
                                )
                            }
                        }
                    }
                } else {
                    // 空状态 - 简洁现代的添加图片按钮
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(120.dp)
                            .clip(RoundedCornerShape(16.dp))
                            .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.05f))
                            .border(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(16.dp)
                            )
                            .clickable { imagePicker.launch("image/*") },
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "添加图片",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(24.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "添加图片",
                                color = MaterialTheme.colorScheme.primary,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
            
            // 标签
            Text(
                text = "标签",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // 已有标签
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        tags.take(3).forEach { tag ->
                            Surface(
                                modifier = Modifier.clickable {
                                    tags = tags - tag
                                },
                                color = MaterialTheme.colorScheme.surfaceVariant,
                                shape = RoundedCornerShape(16.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                                ) {
                                    Text(
                                        text = "#$tag",
                                        fontSize = 14.sp,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "删除",
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }
                    
                    if (tags.size > 3) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            tags.drop(3).forEach { tag ->
                                Surface(
                                    modifier = Modifier.clickable {
                                        tags = tags - tag
                                    },
                                    color = MaterialTheme.colorScheme.surfaceVariant,
                                    shape = RoundedCornerShape(16.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                                    ) {
                                        Text(
                                            text = "#$tag",
                                            fontSize = 14.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = "删除",
                                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 添加新标签
                OutlinedTextField(
                    value = newTag,
                    onValueChange = { newTag = it },
                    placeholder = { Text("新标签...") },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    trailingIcon = {
                        IconButton(
                            onClick = {
                                if (newTag.isNotEmpty() && !tags.contains(newTag)) {
                                    tags = tags + newTag
                                    newTag = ""
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "添加标签"
                            )
                        }
                    },
                    modifier = Modifier.width(150.dp),
                    shape = RoundedCornerShape(16.dp)
                )
            }
            
            // 评分
            Text(
                text = "${category.ratingName}评分",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // 评分选择器
            RatingSelector(
                rating = rating,
                onRatingChanged = { rating = it },
                starColor = category.color,
                modifier = Modifier.fillMaxWidth()
            )
            
            // 底部空间
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
    
    // 日期选择器对话框
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = date.toEpochDay() * 24 * 60 * 60 * 1000
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                Button(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            date = LocalDate.ofEpochDay(millis / (24 * 60 * 60 * 1000))
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDatePicker = false }
                ) {
                    Text("取消")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }
}

/**
 * 类别选择网格
 */
@Composable
fun CategorySelectionGrid(
    selectedCategory: MilestoneCategory,
    onCategorySelected: (MilestoneCategory) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 16.dp),
        modifier = modifier
            .fillMaxWidth()
    ) {
        items(MilestoneCategory.values()) { category ->
            CategoryItem(
                category = category,
                selected = category == selectedCategory,
                onClick = { onCategorySelected(category) }
            )
        }
    }
}

/**
 * 类别选择项
 */
@Composable
fun CategoryItem(
    category: MilestoneCategory,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .width(80.dp)  // 固定宽度确保所有项大小一致
            .clickable(onClick = onClick)
    ) {
        Box(
            modifier = Modifier
                .size(56.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(
                    if (selected) category.color
                    else category.color.copy(alpha = 0.1f)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = category.icon,
                contentDescription = category.displayName,
                tint = if (selected) Color.White else category.color,
                modifier = Modifier.size(28.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = category.displayName,
            fontSize = 12.sp,
            color = if (selected) category.color else MaterialTheme.colorScheme.onBackground,
            fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center,  // 文本居中对齐
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 里程碑类型芯片
 */
@Composable
fun MilestoneTypeChip(
    type: MilestoneType,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val typeInfo = when(type) {
        MilestoneType.REGULAR -> Triple("普通", Icons.Default.Star, MaterialTheme.colorScheme.primary)
        MilestoneType.DECISION -> Triple("决策型", Icons.Default.Share, Color(0xFF4CAF50))
        MilestoneType.OPPORTUNITY -> Triple("机遇型", Icons.Default.Bolt, Color(0xFF2196F3))
        MilestoneType.IMPACT -> Triple("冲击型", Icons.Default.Bolt, Color(0xFFF44336))
    }
    
    val backgroundColor = if (selected) {
        typeInfo.third.copy(alpha = 0.2f)
    } else {
        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
    }
    
    val contentColor = if (selected) {
        typeInfo.third
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Surface(
        color = backgroundColor,
        shape = RoundedCornerShape(12.dp),
        modifier = modifier.clickable(onClick = onClick)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 10.dp)
        ) {
            Icon(
                imageVector = typeInfo.second,
                contentDescription = null,
                tint = contentColor,
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(4.dp))
            
            Text(
                text = typeInfo.first,
                fontSize = 12.sp,
                color = contentColor,
                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

/**
 * 评分选择器
 */
@Composable
fun RatingSelector(
    rating: Float,
    onRatingChanged: (Float) -> Unit,
    starColor: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        // 评分星星
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            for (i in 1..5) {
                Icon(
                    painter = painterResource(
                        id = if (i <= rating) R.drawable.ic_star_filled
                        else if (i > rating && i <= rating + 0.5f) R.drawable.ic_star_half
                        else R.drawable.ic_star_outline
                    ),
                    contentDescription = "星级$i",
                    tint = starColor,
                    modifier = Modifier
                        .size(36.dp)
                        .clickable { onRatingChanged(i.toFloat()) }
                )
            }
        }
        
        // 评分数值
        Text(
            text = rating.toString(),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
} 