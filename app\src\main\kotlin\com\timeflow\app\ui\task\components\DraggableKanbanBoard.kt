package com.timeflow.app.ui.task.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.timeflow.app.data.model.KanbanColumn
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import android.util.Log
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt

/**
 * 可拖拽看板组件 - 支持跨列拖拽任务
 * 参考TickTick和Notion的看板实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DraggableKanbanBoard(
    columns: List<KanbanColumn>,
    onTaskClick: (String) -> Unit,
    onTaskMove: (String, String) -> Unit,
    onTaskStatusChange: (String, Boolean) -> Unit,
    isLoading: Boolean = false,
    modifier: Modifier = Modifier
) {
    // 拖拽状态管理
    var draggedTaskId by remember { mutableStateOf<String?>(null) }
    var draggedTask by remember { mutableStateOf<Task?>(null) }
    var draggedColumnId by remember { mutableStateOf<String?>(null) }
    var targetColumnId by remember { mutableStateOf<String?>(null) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var isDragging by remember { mutableStateOf(false) }
    
    // 列位置信息
    val columnPositions = remember { mutableStateMapOf<String, Pair<Float, Float>>() }
    
    // 任务项位置信息 - 用于同列内排序
    val taskPositions = remember { mutableStateMapOf<String, MutableList<Pair<String, Rect>>>() }
    
    // 目标位置索引 - 用于确定任务拖动后的位置
    var targetPositionIndex by remember { mutableStateOf<Int?>(null) }
    
    // 触觉反馈
    val hapticFeedback = LocalHapticFeedback.current
    
    // 水平滚动
    val scrollState = rememberScrollState()
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 看板列
        Row(
            modifier = Modifier
                .fillMaxSize()
                .horizontalScroll(scrollState)
                .padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            columns.forEach { column ->
                val isTargetColumn = targetColumnId == column.id
                val elevation = animateFloatAsState(
                    targetValue = if (isTargetColumn) 8f else 1f,
                    label = "Column Elevation"
                )
                
                // 列卡片
                Surface(
                    modifier = Modifier
                        .width(280.dp)
                        .fillMaxHeight()
                        .shadow(elevation = elevation.value.dp)
                        .onGloballyPositioned { coordinates ->
                            // 记录列位置
                            val bounds = coordinates.boundsInWindow()
                            columnPositions[column.id] = Pair(bounds.left, bounds.right)
                        },
                    shape = RoundedCornerShape(12.dp),
                    color = when(column.title) {
                        "待办" -> Color(0xFFF0EDFF) // 浅紫色背景
                        "进行中" -> Color(0xFFFFF4ED) // 浅橙色背景
                        "已完成" -> Color(0xFFEDFFF0) // 浅绿色背景
                        else -> MaterialTheme.colorScheme.surfaceVariant
                    }
                ) {
                    Column(
                        modifier = Modifier.padding(8.dp)
                    ) {
                        // 列标题
                        KanbanColumnHeader(
                            title = column.title,
                            count = column.tasks.size,
                            color = when(column.title) {
                                "待办" -> Color(0xFF6750A4) // 深紫色
                                "进行中" -> Color(0xFFFF8A00) // 橙色
                                "已完成" -> Color(0xFF4CAF50) // 绿色
                                else -> MaterialTheme.colorScheme.primary
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 任务列表
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // 初始化当前列的任务位置列表
                            val columnTaskPositions = mutableListOf<Pair<String, Rect>>()
                            taskPositions[column.id] = columnTaskPositions
                            
                            column.tasks.forEachIndexed { index, task ->
                                DraggableTaskItem(
                                    task = task,
                                    onClick = { onTaskClick(task.id) },
                                    onCheckChange = { isCompleted -> 
                                        onTaskStatusChange(task.id, isCompleted)
                                    },
                                    onDragStart = { offset ->
                                        draggedTaskId = task.id
                                        draggedTask = task
                                        draggedColumnId = column.id
                                        isDragging = true
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                    },
                                    onDrag = { offset ->
                                        dragOffset = offset
                                        
                                        // 检测当前位于哪个列上方
                                        columnPositions.forEach { (colId, bounds) ->
                                            if (offset.x > bounds.first && offset.x < bounds.second) {
                                                if (targetColumnId != colId) {
                                                    targetColumnId = colId
                                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                                }
                                                
                                                // 如果在同一列内，确定目标位置索引
                                                if (colId == draggedColumnId) {
                                                    // 查找目标位置索引
                                                    taskPositions[colId]?.let { positions ->
                                                        // 查找哪个任务项位置包含当前拖拽点
                                                        var foundIndex = -1
                                                        for (i in positions.indices) {
                                                            val (id, rect) = positions[i]
                                                            // 跳过当前拖拽的任务
                                                            if (id == draggedTaskId) continue
                                                            
                                                            // 检查鼠标位置是否在任务项范围内
                                                            if (offset.y >= rect.top && offset.y <= rect.bottom) {
                                                                foundIndex = i
                                                                break
                                                            } else if (i > 0 && offset.y > positions[i-1].second.bottom && offset.y < rect.top) {
                                                                // 位于两个任务之间
                                                                foundIndex = i
                                                                break
                                                            } else if (i == positions.size - 1 && offset.y > rect.bottom) {
                                                                // 拖到列表底部
                                                                foundIndex = positions.size
                                                                break
                                                            }
                                                        }
                                                        
                                                        if (foundIndex >= 0 && targetPositionIndex != foundIndex) {
                                                            targetPositionIndex = foundIndex
                                                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    onDragEnd = {
                                        if (draggedTaskId != null && targetColumnId != null) {
                                            if (draggedColumnId != targetColumnId) {
                                                // 跨列移动任务
                                                onTaskMove(draggedTaskId!!, targetColumnId!!)
                                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                            } else if (targetPositionIndex != null) {
                                                // 同列内移动任务 - 重新排序
                                                // 获取当前列中任务的ID列表，移除当前拖拽的任务ID
                                                val columnTasks = columns
                                                    .find { it.id == targetColumnId }
                                                    ?.tasks
                                                    ?.map { it.id }
                                                    ?.toMutableList() ?: mutableListOf()
                                                
                                                if (columnTasks.isNotEmpty()) {
                                                    // 移除当前拖拽的任务
                                                    columnTasks.remove(draggedTaskId)
                                                    
                                                    // 确保目标索引不超出范围
                                                    val safeTargetIndex = targetPositionIndex!!.coerceIn(0, columnTasks.size)
                                                    
                                                    // 在目标位置插入拖拽的任务
                                                    columnTasks.add(safeTargetIndex, draggedTaskId!!)
                                                    
                                                    // 更新任务顺序
                                                    onTaskMove(draggedTaskId!!, targetColumnId!!)
                                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                                }
                                            }
                                        }
                                        
                                        // 重置状态
                                        draggedTaskId = null
                                        draggedTask = null
                                        draggedColumnId = null
                                        targetColumnId = null
                                        targetPositionIndex = null
                                        isDragging = false
                                        dragOffset = Offset.Zero
                                    },
                                    isDragged = task.id == draggedTaskId,
                                    modifier = Modifier.onGloballyPositioned { coordinates ->
                                        // 记录任务项位置
                                        val bounds = coordinates.boundsInWindow()
                                        val rect = Rect(bounds.left, bounds.top, bounds.right, bounds.bottom)
                                        // 更新任务位置列表
                                        val positions = taskPositions[column.id] ?: mutableListOf()
                                        
                                        // 如果列表中已经有该任务，则更新位置，否则添加新的任务位置
                                        val existingIndex = positions.indexOfFirst { it.first == task.id }
                                        if (existingIndex >= 0) {
                                            positions[existingIndex] = task.id to rect
                                        } else {
                                            positions.add(task.id to rect)
                                        }
                                        
                                        taskPositions[column.id] = positions
                                    }
                                )
                            }
                            
                            // 空状态展示
                            if (column.tasks.isEmpty()) {
                                EmptyColumnPlaceholder()
                            }
                        }
                    }
                }
            }
        }
        
        // 拖拽层 - 显示正在拖拽的任务
        AnimatedVisibility(
            visible = isDragging && draggedTask != null,
            enter = fadeIn(tween(100)),
            exit = fadeOut(tween(100)),
            modifier = Modifier.fillMaxSize()
        ) {
            Box(
                modifier = Modifier
                    .offset(
                        x = dragOffset.x.dp - 140.dp, // 居中
                        y = dragOffset.y.dp - 30.dp // 居中且略微上移
                    )
                    .zIndex(10f)
                    .width(280.dp)
            ) {
                draggedTask?.let { task ->
                    DraggedTaskPreview(task = task)
                }
            }
        }
    }
}

@Composable
private fun KanbanColumnHeader(
    title: String,
    count: Int,
    color: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 状态圆点
            Box(
                modifier = Modifier
                    .size(10.dp)
                    .clip(RoundedCornerShape(5.dp))
                    .background(color)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 标题
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = color,
                fontWeight = FontWeight.Bold
            )
        }
        
        // 数量标签
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(8.dp))
                .background(color.copy(alpha = 0.1f))
                .padding(horizontal = 8.dp, vertical = 2.dp)
        ) {
            Text(
                text = "$count",
                fontSize = 12.sp,
                color = color
            )
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun DraggableTaskItem(
    task: Task,
    onClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDragStart: (Offset) -> Unit,
    onDrag: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    isDragged: Boolean,
    modifier: Modifier = Modifier
) {
    val scale = animateFloatAsState(
        targetValue = if (isDragged) 1.05f else 1f,
        label = "Task Scale"
    )
    
    val alpha = animateFloatAsState(
        targetValue = if (isDragged) 0.5f else 1f,
        label = "Task Alpha"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .scale(scale.value)
            .alpha(alpha.value)
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { offset ->
                        onDragStart(offset)
                    },
                    onDrag = { change, dragAmount ->
                        change.consume()
                        onDrag(change.position)
                    },
                    onDragEnd = {
                        onDragEnd()
                    },
                    onDragCancel = {
                        onDragEnd()
                    }
                )
            }
            .clickable { onClick() },
        color = Color.White,
        shadowElevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 任务标题
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                maxLines = 2
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 任务描述
            if (!task.description.isNullOrEmpty()) {
                // 🔧 清理描述中的JSON颜色信息
                val cleanDescription = remember(task.description!!) {
                    val colorPattern = """\{"color":(\d+)\}""".toRegex()
                    task.description.replace(colorPattern, "").trim()
                }
                
                if (cleanDescription.isNotEmpty()) {
                    Text(
                        text = cleanDescription,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 底部栏: 完成状态
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 左侧: 任务状态优先级信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 优先级标签，如果有
                    if (task.priority != null) {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    when (task.priority) {
                                        Priority.LOW -> Color(0xFF4CAF50).copy(alpha = 0.1f) // 低优先级，绿色
                                        Priority.MEDIUM -> Color(0xFFFFA000).copy(alpha = 0.1f) // 中优先级，橙色
                                        Priority.HIGH -> Color(0xFFE53935).copy(alpha = 0.1f) // 高优先级，红色
                                        Priority.URGENT -> Color(0xFFC62828).copy(alpha = 0.1f) // 紧急，深红色
                                        else -> Color.Gray.copy(alpha = 0.1f)
                                    }
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        ) {
                            Text(
                                text = when (task.priority) {
                                    Priority.LOW -> "低"
                                    Priority.MEDIUM -> "中"
                                    Priority.HIGH -> "高"
                                    Priority.URGENT -> "紧急"
                                    else -> "普通"
                                },
                                fontSize = 10.sp,
                                color = when (task.priority) {
                                    Priority.LOW -> Color(0xFF4CAF50) // 低优先级，绿色
                                    Priority.MEDIUM -> Color(0xFFFFA000) // 中优先级，橙色
                                    Priority.HIGH -> Color(0xFFE53935) // 高优先级，红色
                                    Priority.URGENT -> Color(0xFFC62828) // 紧急，深红色
                                    else -> Color.Gray
                                }
                            )
                        }
                    }
                }
                
                // 右侧: 完成复选框
                Checkbox(
                    checked = task.isCompleted,
                    onCheckedChange = onCheckChange,
                    colors = CheckboxDefaults.colors(
                        checkedColor = MaterialTheme.colorScheme.primary,
                        uncheckedColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    ),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
private fun DraggedTaskPreview(
    task: Task,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(elevation = 8.dp),
        color = Color.White,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 简化版任务卡片
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun EmptyColumnPlaceholder() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "暂无任务",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
        )
    }
} 