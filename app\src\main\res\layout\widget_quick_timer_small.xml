<?xml version="1.0" encoding="utf-8"?>
<!-- 小尺寸专注计时器小组件布局 (2x2) - 参照Calflow风格 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_gradient_timetracking"
    android:padding="12dp">

    <!-- 顶部任务名称 -->
    <TextView
        android:id="@+id/widget_task_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:text="专注任务"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:shadowColor="#80000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2" />

    <!-- 中央计时器显示 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:gravity="center">

        <TextView
            android:id="@+id/widget_timer_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="23:45"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-condensed"
            android:shadowColor="#80000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <TextView
            android:id="@+id/widget_timer_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="正计时"
            android:textSize="11sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium"
            android:layout_marginTop="2dp"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </LinearLayout>

    <!-- 底部控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 播放/暂停按钮 -->
        <ImageView
            android:id="@+id/widget_play_pause_button"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_pause"
            android:background="@drawable/widget_circle_button_accent"
            android:padding="6dp"
            android:tint="@android:color/white"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true" />

        <!-- 停止按钮 -->
        <ImageView
            android:id="@+id/widget_stop_button"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_stop"
            android:background="@drawable/widget_circle_button_secondary"
            android:padding="6dp"
            android:tint="@color/widget_text_primary"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</RelativeLayout>
