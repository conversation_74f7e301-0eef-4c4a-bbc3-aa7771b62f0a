package com.timeflow.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.timeflow.app.service.TaskPersistentNotificationService
import dagger.hilt.android.AndroidEntryPoint

/**
 * 任务常驻通知操作广播接收器
 * 处理来自常驻通知栏的任务操作
 */
@AndroidEntryPoint
class TaskPersistentNotificationActionReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "TaskPersistentNotificationActionReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "接收到通知操作: ${intent.action}")
        
        // 将操作转发给TaskPersistentNotificationService
        val serviceIntent = Intent(context, TaskPersistentNotificationService::class.java).apply {
            action = intent.action
            // 传递额外数据
            intent.getStringExtra(TaskPersistentNotificationService.EXTRA_TASK_ID)?.let {
                putExtra(TaskPersistentNotificationService.EXTRA_TASK_ID, it)
            }
            intent.getStringExtra(TaskPersistentNotificationService.EXTRA_TASK_TITLE)?.let {
                putExtra(TaskPersistentNotificationService.EXTRA_TASK_TITLE, it)
            }
        }
        
        try {
            context.startForegroundService(serviceIntent)
            Log.d(TAG, "已转发操作到服务: ${intent.action}")
        } catch (e: Exception) {
            Log.e(TAG, "启动前台服务失败", e)
        }
    }
}
