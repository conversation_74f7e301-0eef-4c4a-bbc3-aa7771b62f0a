package com.timeflow.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.entity.TaskTag
import com.timeflow.app.data.entity.TaskClosure
import kotlinx.coroutines.flow.Flow
import androidx.room.Transaction
import java.time.LocalDateTime

/**
 * 任务DAO接口
 */
@Dao
interface TaskDao {
    
    /**
     * 插入新任务
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(task: Task)
    
    /**
     * 插入多个任务
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(tasks: List<Task>)
    
    /**
     * 更新任务
     */
    @Update
    suspend fun update(task: Task)
    
    /**
     * 删除任务
     */
    @Delete
    suspend fun delete(task: Task)
    
    /**
     * 根据ID获取任务
     */
    @Query("SELECT * FROM tasks WHERE id = :taskId")
    suspend fun getTaskById(taskId: String): Task?
    
    /**
     * 观察指定ID的任务变化
     */
    @Query("SELECT * FROM tasks WHERE id = :taskId")
    fun observeTaskById(taskId: String): Flow<Task?>
    
    /**
     * 获取所有任务
     */
    @Query("SELECT * FROM tasks ORDER BY created_at DESC LIMIT 20")
    suspend fun getAllTasks(): List<Task>

    /**
     * 获取所有任务（无限制，用于导出）
     */
    @Query("SELECT * FROM tasks ORDER BY created_at DESC")
    suspend fun getAllTasksList(): List<Task>
    
    /**
     * 观察所有任务变化
     */
    @Query("SELECT * FROM tasks ORDER BY created_at DESC")
    fun observeAllTasks(): Flow<List<Task>>
    
    /**
     * 获取指定列的所有任务
     */
    @Query("SELECT * FROM tasks WHERE column_id = :columnId ORDER BY order_index")
    suspend fun getTasksByColumnId(columnId: String): List<Task>
    
    /**
     * 观察指定列的所有任务变化
     */
    @Query("SELECT * FROM tasks WHERE column_id = :columnId ORDER BY order_index")
    fun observeTasksByColumnId(columnId: String): Flow<List<Task>>
    
    /**
     * 获取指定列的最大位置值
     */
    @Query("SELECT MAX(order_index) FROM tasks WHERE column_id = :columnId")
    suspend fun getMaxPositionForColumn(columnId: String): Int?
    
    /**
     * 按优先级获取任务
     */
    @Query("SELECT * FROM tasks ORDER BY priority DESC, due_date ASC")
    fun getTasksByPriority(): Flow<List<Task>>
    
    /**
     * 获取包含特定标签的任务（临时实现）
     * 注意：新Task实体不再包含tags字段，此查询只用于兼容旧接口
     */
    @Query("SELECT * FROM tasks WHERE id LIKE '%' || :tag || '%'")
    fun getTasksByTag(tag: String): Flow<List<Task>>
    
    /**
     * 获取包含多个标签中任一标签的任务（临时实现）
     * 注意：新Task实体不再包含tags字段，此查询只用于兼容旧接口
     */
    @Query("SELECT * FROM tasks WHERE id IN (:tags)")
    fun getTasksByTags(tags: List<String>): Flow<List<Task>>
    
    /**
     * 获取已完成的任务
     */
    @Query("SELECT * FROM tasks WHERE completed_at IS NOT NULL")
    fun getCompletedTasks(): Flow<List<Task>>
    
    /**
     * 获取未完成的任务
     */
    @Query("SELECT * FROM tasks WHERE completed_at IS NULL")
    fun getUncompletedTasks(): Flow<List<Task>>
    
    /**
     * 根据截止日期获取任务
     */
    @Query("SELECT * FROM tasks WHERE date(due_date) = date(:dueDate)")
    fun getTasksByDueDate(dueDate: String): Flow<List<Task>>
    
    /**
     * 获取子任务（临时实现，新Task实体不再有parentTaskId）
     */
    @Query("SELECT * FROM tasks WHERE id != :parentId ORDER BY order_index")
    fun getChildTasks(parentId: String): Flow<List<Task>>
    
    /**
     * 获取指定父任务的所有子任务
     */
    @Query("SELECT * FROM tasks WHERE parent_task_id = :parentId ORDER BY order_index")
    suspend fun getTasksByParentId(parentId: String): List<Task>
    
    /**
     * 获取根任务（临时实现，新Task实体不再有parentTaskId）
     */
    @Query("SELECT * FROM tasks ORDER BY order_index")
    fun getRootTasks(): Flow<List<Task>>
    
    /**
     * 根据ID删除任务
     */
    @Query("DELETE FROM tasks WHERE id = :taskId")
    suspend fun deleteTaskById(taskId: String)
    
    /**
     * 按分组获取任务
     * 注意：使用column_id代替旧的groupId字段
     */
    @Query("SELECT * FROM tasks WHERE column_id = :groupId ORDER BY order_index")
    fun getTasksByGroup(groupId: String): Flow<List<Task>>
    
    /**
     * 按分组类型获取任务
     * 注意：新Task实体不再有groupType字段，此方法为兼容旧接口
     */
    @Query("SELECT * FROM tasks WHERE id LIKE '%' || :groupType || '%'")
    fun getTasksByGroupType(groupType: String): Flow<List<Task>>
    
    /**
     * 获取所有标签（从所有任务中提取并去重）
     * 注意：新Task实体不再包含tags字段，此查询只用于兼容旧接口
     */
    @Query("SELECT title FROM tasks LIMIT 1")
    fun getAllTagsRaw(): Flow<List<String>>
    
    /**
     * 这个方法需要在Repository层手动实现
     * 因为Room无法直接从JSON列表中提取TaskTag对象
     */
    fun getAllTags(): Flow<List<TaskTag>> {
        // 这个方法将在Repository中实现
        throw UnsupportedOperationException("This method should be implemented in the repository")
    }

    @Query("""
        SELECT t.* FROM tasks t
        INNER JOIN task_closure tc ON t.id = tc.descendantId
        WHERE tc.ancestorId = :taskId
        ORDER BY tc.depth
    """)
    fun getEntireSubtasks(taskId: String): Flow<List<Task>>

    @Query("""
        SELECT t.* FROM tasks t
        INNER JOIN task_closure tc ON t.id = tc.ancestorId
        WHERE tc.descendantId = :taskId
        ORDER BY tc.depth DESC
    """)
    fun getEntireAncestors(taskId: String): Flow<List<Task>>

    @Query("DELETE FROM task_closure WHERE ancestorId = :taskId OR descendantId = :taskId")
    suspend fun deleteTaskClosures(taskId: String)

    @Insert
    suspend fun insertTaskClosure(taskClosure: TaskClosure)

    @Insert
    suspend fun insertTaskClosures(taskClosures: List<TaskClosure>)

    @Query("""
        SELECT t.* FROM tasks t
        INNER JOIN task_closure tc ON t.id = tc.ancestorId
        WHERE tc.descendantId = :taskId AND tc.depth = 1
        LIMIT 1
    """)
    suspend fun getImmediateParent(taskId: String): Task?

    @Query("""
        SELECT t.* FROM tasks t
        INNER JOIN task_closure tc ON t.id = tc.descendantId
        WHERE tc.ancestorId = :taskId AND tc.depth = 1
    """)
    fun getImmediateChildren(taskId: String): Flow<List<Task>>

    @Transaction
    suspend fun insertTaskWithClosure(task: Task, parentId: String? = null) {
        insert(task)
        if (parentId != null) {
            // Insert self-relation
            insertTaskClosure(TaskClosure(
                ancestorId = task.id,
                descendantId = task.id,
                depth = 0
            ))
            
            // Get all ancestors of the parent
            val parentAncestors = getEntireAncestors(parentId).collect { ancestors ->
                // Create closure entries for all ancestors
                val closures = ancestors.map { ancestor ->
                    TaskClosure(
                        ancestorId = ancestor.id,
                        descendantId = task.id,
                        depth = ancestors.indexOf(ancestor) + 1
                    )
                }
                insertTaskClosures(closures)
            }
        }
    }

    @Transaction
    suspend fun deleteTaskWithClosure(taskId: String) {
        deleteTaskById(taskId)
        deleteTaskClosures(taskId)
    }

    @Transaction
    suspend fun batchUpdateTasks(tasks: List<Task>) {
        val pageSize = 500
        tasks.chunked(pageSize).forEach { chunk ->
            chunk.forEach { task ->
                update(task)
            }
        }
    }

    @Transaction
    suspend fun moveTask(taskId: String, newParentId: String?) {
        // 1. 删除旧的闭包关系
        deleteTaskClosures(taskId)
        
        // 2. 创建新的闭包关系
        if (newParentId != null) {
            getEntireAncestors(newParentId).collect { parentAncestors ->
                val closures = parentAncestors.map { ancestor ->
                    TaskClosure(
                        ancestorId = ancestor.id,
                        descendantId = taskId,
                        depth = parentAncestors.indexOf(ancestor) + 1
                    )
                }
                insertTaskClosures(closures)
            }
        }
        
        // 3. 更新任务的父ID
        getTaskById(taskId)?.let { task ->
            update(task.copy(parentTaskId = newParentId))
        }
    }

    /**
     * 更新任务的排序位置
     */
    @Query("UPDATE tasks SET order_index = :orderPosition WHERE id = :taskId")
    suspend fun updateTaskOrder(taskId: String, orderPosition: Int)

    /**
     * 获取看板列
     */
    @Query("SELECT * FROM kanban_columns WHERE id = :columnId")
    suspend fun getColumnById(columnId: String): com.timeflow.app.data.entity.KanbanColumn?
    
    /**
     * 获取冲突任务（截止日期已过但未完成的任务）
     */
    @Query("SELECT * FROM tasks WHERE due_date < :now AND completed_at IS NULL")
    suspend fun getConflictTasks(now: LocalDateTime = LocalDateTime.now()): List<Task>
    
    /**
     * 观察冲突任务
     */
    @Query("SELECT * FROM tasks WHERE due_date < :now AND completed_at IS NULL")
    fun observeConflictTasks(now: LocalDateTime = LocalDateTime.now()): Flow<List<Task>>

    /**
     * 获取最近更新的任务
     */
    @Query("SELECT * FROM tasks WHERE updated_at > :since ORDER BY updated_at DESC LIMIT 10")
    suspend fun getRecentlyUpdatedTasks(since: LocalDateTime): List<Task>

    /**
     * 获取最近更新的任务
     */
    @Query("SELECT * FROM tasks WHERE updated_at > datetime('now', '-1 hour') ORDER BY updated_at DESC LIMIT 50")
    suspend fun getRecentlyUpdatedTasks(): List<Task>

    /**
     * 根据日期范围获取任务
     */
    @Query("SELECT * FROM tasks WHERE due_date BETWEEN :startDate AND :endDate ORDER BY due_date ASC")
    suspend fun getTasksByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Task>

    /**
     * 更新任务完成状态
     */
    @Query("""
        UPDATE tasks SET 
            completed_at = CASE WHEN :isCompleted = 1 THEN :completionTime ELSE NULL END,
            status = CASE WHEN :isCompleted = 1 THEN '已完成' ELSE '待办' END,
            updated_at = :updateTime 
        WHERE id = :taskId
    """)
    suspend fun updateTaskCompletionStatus(taskId: String, isCompleted: Boolean, completionTime: LocalDateTime, updateTime: LocalDateTime): Int
    
    /**
     * 更新任务hasSubtasks标志
     */
    @Query("UPDATE tasks SET has_subtasks = :hasSubtasks, updated_at = :updateTime WHERE id = :taskId")
    suspend fun updateTaskHasSubtasksFlag(taskId: String, hasSubtasks: Boolean, updateTime: LocalDateTime): Int
    
    /**
     * 更新任务的父任务ID为null
     * 使用场景：当父任务被删除时，清除所有子任务的parentTaskId引用
     */
    @Query("UPDATE tasks SET parent_task_id = NULL, updated_at = datetime('now') WHERE parent_task_id = :parentTaskId")
    suspend fun clearParentTaskReferences(parentTaskId: String): Int

    /**
     * 强制删除任务（绕过外键约束）
     * 此方法首先清理关系，然后删除任务
     */
    @Transaction
    suspend fun forceDeleteTask(taskId: String) {
        try {
            // 1. 获取所有子任务
            val subTasks = getTasksByParentId(taskId)
            
            // 2. 清除所有子任务的父任务引用
            clearParentTaskReferences(taskId)
            
            // 3. 删除所有任务闭包关系
            deleteTaskClosures(taskId)
            
            // 4. 删除子任务
            for (subTask in subTasks) {
                deleteTaskById(subTask.id)
            }
            
            // 5. 删除任务本身
            deleteTaskById(taskId)
        } catch (e: Exception) {
            // 记录异常但不抛出，以确保在外键约束下仍能尽可能删除
            android.util.Log.e("TaskDao", "强制删除任务失败: ${e.message}", e)
        }
    }

    /**
     * 获取指定ID的任务，包括其所有子任务
     */
    @Transaction
    suspend fun getTaskWithSubtasks(taskId: String): Task? = getTaskById(taskId)
    
    /**
     * 更新任务的指定字段
     * 
     * @param taskId 任务ID
     * @param description 更新后的描述
     * @param updatedAt 更新时间
     */
    @Query("UPDATE tasks SET description = :description, updated_at = :updatedAt WHERE id = :taskId")
    suspend fun updateTaskFields(taskId: String, description: String, updatedAt: LocalDateTime)
    
    /**
     * 更新任务的排序索引
     */

    /**
     * 获取所有已完成的任务（同步版本）
     */
    @Query("SELECT * FROM tasks WHERE completed_at IS NOT NULL ORDER BY completed_at DESC")
    suspend fun getCompletedTasksList(): List<Task>
    
    /**
     * 🆕 获取指定周范围内的浮动任务
     */
    @Query("""
        SELECT * FROM tasks 
        WHERE is_floating_task = 1 
        AND floating_week_start <= :weekEnd 
        AND floating_week_end >= :weekStart
        AND completed_at IS NULL
        ORDER BY floating_task_order ASC, created_at ASC
    """)
    suspend fun getFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task>
    
    /**
     * 🆕 观察指定周范围内的浮动任务变化
     */
    @Query("""
        SELECT * FROM tasks 
        WHERE is_floating_task = 1 
        AND floating_week_start <= :weekEnd 
        AND floating_week_end >= :weekStart
        AND completed_at IS NULL
        ORDER BY floating_task_order ASC, created_at ASC
    """)
    fun observeFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): Flow<List<Task>>
    
    /**
     * 🆕 获取未安排的浮动任务（scheduled_date为空）
     */
    @Query("""
        SELECT * FROM tasks 
        WHERE is_floating_task = 1 
        AND scheduled_date IS NULL
        AND floating_week_start <= :weekEnd 
        AND floating_week_end >= :weekStart
        AND completed_at IS NULL
        ORDER BY floating_task_order ASC, created_at ASC
    """)
    suspend fun getUnscheduledFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task>
    
    /**
     * 🆕 将浮动任务安排到具体日期
     */
    @Query("""
        UPDATE tasks 
        SET scheduled_date = :scheduledDate, updated_at = :updatedAt
        WHERE id = :taskId
    """)
    suspend fun scheduleFloatingTask(taskId: String, scheduledDate: LocalDateTime, updatedAt: LocalDateTime)
    
    /**
     * 🆕 取消浮动任务的具体日期安排
     */
    @Query("""
        UPDATE tasks 
        SET scheduled_date = NULL, updated_at = :updatedAt
        WHERE id = :taskId
    """)
    suspend fun unscheduleFloatingTask(taskId: String, updatedAt: LocalDateTime)
    
    /**
     * 🆕 更新浮动任务在列表中的顺序
     */
    @Query("""
        UPDATE tasks 
        SET floating_task_order = :newOrder, updated_at = :updatedAt
        WHERE id = :taskId
    """)
    suspend fun updateFloatingTaskOrder(taskId: String, newOrder: Int, updatedAt: LocalDateTime)
    
    /**
     * 根据目标ID获取关联的任务
     */
    @Query("SELECT * FROM tasks WHERE goal_id = :goalId ORDER BY created_at DESC")
    suspend fun getTasksByGoalId(goalId: String): List<Task>
    
    /**
     * 观察指定目标ID的任务变化
     */
    @Query("SELECT * FROM tasks WHERE goal_id = :goalId ORDER BY created_at DESC")
    fun observeTasksByGoalId(goalId: String): Flow<List<Task>>

    // 🔧 循环任务相关查询方法

    /**
     * 获取所有循环任务
     */
    @Query("SELECT * FROM tasks WHERE isRecurring = 1 ORDER BY created_at DESC")
    suspend fun getRecurringTasks(): List<Task>



    /**
     * 观察循环任务变化
     */
    @Query("SELECT * FROM tasks WHERE isRecurring = 1 ORDER BY created_at DESC")
    fun observeRecurringTasks(): Flow<List<Task>>

    /**
     * 获取指定日期范围内的循环任务
     */
    @Query("""
        SELECT * FROM tasks
        WHERE isRecurring = 1
        AND due_date >= :startDate
        AND due_date <= :endDate
        ORDER BY due_date ASC
    """)
    suspend fun getRecurringTasksInDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Task>

    /**
     * 更新任务的循环设置
     */
    @Query("""
        UPDATE tasks
        SET isRecurring = :isRecurring,
            recurringPattern = :recurringPattern,
            updated_at = :updatedAt
        WHERE id = :taskId
    """)
    suspend fun updateTaskRecurrenceSettings(
        taskId: String,
        isRecurring: Boolean,
        recurringPattern: String?,
        updatedAt: LocalDateTime
    )
}