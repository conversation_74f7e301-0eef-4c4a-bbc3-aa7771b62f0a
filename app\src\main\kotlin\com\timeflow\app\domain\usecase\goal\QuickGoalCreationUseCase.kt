package com.timeflow.app.domain.usecase.goal

import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.UserPreferenceRepository
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 快速目标创建用例类
 * 简化目标创建流程，自动填充常用设置，加速目标创建
 */
class QuickGoalCreationUseCase @Inject constructor(
    private val goalRepository: GoalRepository,
    private val userPreferenceRepository: UserPreferenceRepository
) {
    /**
     * 快速创建基本目标
     * 使用智能默认值和用户偏好自动填充非关键字段
     * 
     * @param title 目标标题（必填）
     * @param description 目标描述（可选）
     * @param daysToComplete 完成天数（可选，默认为7天）
     * @param priority 优先级（可选，默认为中等）
     * @return 创建的目标ID
     */
    suspend fun createQuickGoal(
        title: String,
        description: String = "",
        daysToComplete: Int = 7,
        priority: GoalPriority = GoalPriority.MEDIUM
    ): String {
        // 获取用户偏好以填充默认值
        val preferences = userPreferenceRepository.getUserPreferencesSync()
        
        // 创建目标，自动填充非关键字段
        val goal = Goal(
            title = title,
            description = description,
            priority = priority,
            
            // 智能设置开始和截止时间
            startDate = LocalDateTime.now(),
            dueDate = LocalDateTime.now().plusDays(daysToComplete.toLong()),
            
            // 使用用户常用标签
            tags = preferences.frequentTags.take(2),
            
            // 设置简单目标类型
            goalType = GoalType.BOOLEAN,
            
            // 应用用户偏好的提醒设置
            reminderSettings = preferences.defaultReminderSettings,
            
            // 初始化进度为0
            progress = 0f,
            status = "进行中"
        )
        
        // 保存目标
        val goalId = goalRepository.saveGoal(goal)
        
        // 更新用户目标创建统计
        userPreferenceRepository.recordGoalCreation(goalId)
        
        return goalId
    }
    
    /**
     * 基于语音输入创建目标
     * 将用户的语音描述自动解析为结构化目标
     * 
     * @param voiceInput 用户语音转录文本
     * @return 创建的目标ID
     */
    suspend fun createGoalFromVoiceInput(voiceInput: String): String {
        // 解析语音输入
        val parsedGoal = parseVoiceInput(voiceInput)
        
        // 创建目标
        return goalRepository.saveGoal(parsedGoal)
    }
    
    /**
     * 解析语音输入为结构化目标
     * 简单的实现，实际项目中可以使用更复杂的NLP或AI服务
     */
    private fun parseVoiceInput(input: String): Goal {
        // 简单解析逻辑，实际项目中可使用AI服务
        val lines = input.split("。", ".", ",", "，", ";", "；", "!", "！")
        
        // 假设第一行是标题
        val title = lines.firstOrNull()?.trim() ?: "新目标"
        
        // 剩余的内容作为描述
        val description = lines.drop(1).joinToString(". ").trim()
        
        // 识别时间关键词
        val dueDate = when {
            input.contains("明天") -> LocalDateTime.now().plusDays(1)
            input.contains("下周") -> LocalDateTime.now().plusWeeks(1)
            input.contains("下个月") -> LocalDateTime.now().plusMonths(1)
            input.contains("今天") -> LocalDateTime.now().plusHours(12)
            else -> LocalDateTime.now().plusDays(7) // 默认一周
        }
        
        // 识别优先级关键词
        val priority = when {
            input.contains("紧急") || input.contains("重要") -> GoalPriority.HIGH
            input.contains("低优先级") || input.contains("不急") -> GoalPriority.LOW
            else -> GoalPriority.MEDIUM
        }
        
        // 创建结构化目标
        return Goal(
            title = title,
            description = description,
            startDate = LocalDateTime.now(),
            dueDate = dueDate,
            priority = priority,
            goalType = GoalType.BOOLEAN,
            progress = 0f,
            status = "进行中"
        )
    }
} 