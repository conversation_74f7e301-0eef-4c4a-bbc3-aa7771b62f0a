package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.timeflow.app.TimeFlowApplication",
    rootPackage = "com.timeflow.app",
    originatingRoot = "com.timeflow.app.TimeFlowApplication",
    originatingRootPackage = "com.timeflow.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "TimeFlowApplication",
    originatingRootSimpleNames = "TimeFlowApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_timeflow_app_TimeFlowApplication {
}
