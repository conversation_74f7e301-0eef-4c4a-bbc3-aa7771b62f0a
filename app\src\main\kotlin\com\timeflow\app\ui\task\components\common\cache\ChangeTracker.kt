package com.timeflow.app.ui.task.components.common.cache

import android.util.Log
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * 变更追踪器，用于追踪数据变更和决定何时需要刷新数据
 */
class ChangeTracker {
    private val TAG = "ChangeTracker"
    
    /**
     * 实体类型枚举
     */
    enum class EntityType {
        TASK,       // 单个任务
        SUBTASKS,   // 子任务列表
        TASK_GROUP, // 任务组
        KANBAN      // 看板
    }
    
    // 数据上次变更时间表
    private val lastChangeTimestamps = ConcurrentHashMap<Pair<EntityType, String>, Long>()
    
    // 数据上次获取时间表
    private val lastFetchTimestamps = ConcurrentHashMap<Pair<EntityType, String>, Long>()
    
    /**
     * 标记数据已变更
     * 
     * @param type 实体类型
     * @param id 实体ID
     */
    fun markChanged(type: EntityType, id: String) {
        val key = type to id
        val now = System.currentTimeMillis()
        lastChangeTimestamps[key] = now
        Log.d(TAG, "Marked $type $id as changed at $now")
    }
    
    /**
     * 标记数据已获取
     * 
     * @param type 实体类型
     * @param id 实体ID
     */
    fun markFetched(type: EntityType, id: String) {
        val key = type to id
        val now = System.currentTimeMillis()
        lastFetchTimestamps[key] = now
        Log.d(TAG, "Marked $type $id as fetched at $now")
    }
    
    /**
     * 检查数据是否在上次获取后发生变更
     * 
     * @param type 实体类型
     * @param id 实体ID
     * @return 如果数据自上次获取以来已变更则返回true
     */
    fun hasChangedSinceLastFetch(type: EntityType, id: String): Boolean {
        val key = type to id
        val lastChange = lastChangeTimestamps[key] ?: 0
        val lastFetch = lastFetchTimestamps[key] ?: 0
        
        // 如果变更时间晚于获取时间，则数据已变更
        val hasChanged = lastChange > lastFetch
        Log.d(TAG, "Checking $type $id: lastChange=$lastChange, lastFetch=$lastFetch, hasChanged=$hasChanged")
        return hasChanged
    }
    
    /**
     * 检查数据是否需要刷新(基于变更或最大允许年龄)
     * 
     * @param type 实体类型
     * @param id 实体ID
     * @param maxAgeMs 数据最大允许年龄(毫秒)
     * @return 如果数据需要刷新则返回true
     */
    fun needsRefresh(type: EntityType, id: String, maxAgeMs: Long): Boolean {
        val key = type to id
        val lastChange = lastChangeTimestamps[key] ?: 0
        val lastFetch = lastFetchTimestamps[key] ?: 0
        val now = System.currentTimeMillis()
        
        // 如果从未获取过数据或数据变更或数据太旧，则需要刷新
        val needsRefresh = lastFetch == 0L || lastChange > lastFetch || now - lastFetch > maxAgeMs
        
        Log.d(TAG, "Checking refresh need for $type $id: lastFetch=$lastFetch, " +
                "age=${now - lastFetch}ms, maxAge=${maxAgeMs}ms, needsRefresh=$needsRefresh")
        
        return needsRefresh
    }
    
    /**
     * 清除指定实体的跟踪记录
     * 
     * @param type 实体类型
     * @param id 实体ID
     */
    fun clear(type: EntityType, id: String) {
        val key = type to id
        lastChangeTimestamps.remove(key)
        lastFetchTimestamps.remove(key)
        Log.d(TAG, "Cleared tracking for $type $id")
    }
    
    /**
     * 清除所有跟踪记录
     */
    fun clearAll() {
        lastChangeTimestamps.clear()
        lastFetchTimestamps.clear()
        Log.d(TAG, "Cleared all tracking records")
    }
    
    /**
     * 获取跟踪的实体数量
     */
    fun getTrackedEntityCount(): Int {
        val uniqueEntities = HashSet<Pair<EntityType, String>>()
        uniqueEntities.addAll(lastChangeTimestamps.keys)
        uniqueEntities.addAll(lastFetchTimestamps.keys)
        return uniqueEntities.size
    }
    
    companion object {
        // 默认过期时间常量
        val DEFAULT_TASK_MAX_AGE = TimeUnit.MINUTES.toMillis(5)
        val DEFAULT_SUBTASKS_MAX_AGE = TimeUnit.MINUTES.toMillis(2)
        val DEFAULT_TASKGROUP_MAX_AGE = TimeUnit.MINUTES.toMillis(3)
        val DEFAULT_KANBAN_MAX_AGE = TimeUnit.MINUTES.toMillis(3)
    }
} 