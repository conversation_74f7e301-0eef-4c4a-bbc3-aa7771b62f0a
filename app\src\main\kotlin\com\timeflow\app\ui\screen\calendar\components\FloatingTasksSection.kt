package com.timeflow.app.ui.screen.calendar.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.compose.ui.geometry.Offset
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.LocalDate
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import androidx.compose.foundation.layout.PaddingValues
import java.time.DayOfWeek
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import android.widget.Toast
import com.timeflow.app.ui.screen.calendar.CalendarEvent
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import com.timeflow.app.ui.screen.health.HabitData
import com.timeflow.app.ui.viewmodel.HabitViewModel

/**
 * 浮动任务区域组件
 * 显示本周内可以任意时间完成的任务
 */
@Composable
fun FloatingTasksSection(
    floatingTasks: List<Task>,
    isExpanded: Boolean = false,
    onExpandedChange: (Boolean) -> Unit,
    onTaskSchedule: (Task, LocalDateTime) -> Unit,
    onTaskUnschedule: (Task) -> Unit,
    onCreateFloatingTask: () -> Unit,
    modifier: Modifier = Modifier
) {
    val unscheduledTasks = floatingTasks.filter { !it.isScheduled() }
    val scheduledTasks = floatingTasks.filter { it.isScheduled() }
    
    // 🎨 取消阴影的现代平面设计
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessLow
                )
            ),
        color = Color.White.copy(alpha = 0.7f), // 极淡的白色背景
        shadowElevation = 0.dp, // 🚫 取消阴影
        shape = RoundedCornerShape(12.dp), // 稍小的圆角
        border = BorderStroke(
            width = 0.5.dp,
                                color = Color(0xFFC4B5D4).copy(alpha = 0.1f) // 极淡的莫兰迪紫边框
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题栏
            FloatingTasksHeader(
                taskCount = unscheduledTasks.size,
                isExpanded = isExpanded,
                onExpandedChange = onExpandedChange,
                onCreateTask = onCreateFloatingTask
            )
            
            // 可折叠的任务列表
            AnimatedVisibility(
                visible = isExpanded,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Column(modifier = Modifier.padding(top = 12.dp)) {
                    // 未安排的浮动任务
                    if (unscheduledTasks.isNotEmpty()) {
                        FloatingTaskList(
                            tasks = unscheduledTasks,
                            title = "本周待办",
                            onTaskSchedule = onTaskSchedule,
                            onTaskUnschedule = onTaskUnschedule
                        )
                    }
                    
                    // 已安排的浮动任务
                    if (scheduledTasks.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(16.dp))
                        FloatingTaskList(
                            tasks = scheduledTasks,
                            title = "已安排",
                            onTaskSchedule = onTaskSchedule,
                            onTaskUnschedule = onTaskUnschedule,
                            isScheduledList = true
                        )
                    }
                    
                    // 空状态
                    if (floatingTasks.isEmpty()) {
                        FloatingTasksEmptyState()
                    }
                }
            }
        }
    }
}

/**
 * 浮动任务标题栏
 */
@Composable
private fun FloatingTasksHeader(
    taskCount: Int,
    isExpanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    onCreateTask: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clickable { onExpandedChange(!isExpanded) }
                .weight(1f)
        ) {
            Icon(
                imageVector = Icons.Default.Schedule,
                contentDescription = null,
                tint = Color(0xFFC4B5D4),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "本周任务",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F2937)
            )
            
            if (taskCount > 0) {
                Spacer(modifier = Modifier.width(8.dp))
                FloatingTaskCountBadge(count = taskCount)
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Icon(
                imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                contentDescription = if (isExpanded) "收起" else "展开",
                tint = Color(0xFF6B7280),
                modifier = Modifier
                    .size(20.dp)
                    .graphicsLayer(
                        rotationZ = animateFloatAsState(
                            targetValue = if (isExpanded) 180f else 0f,
                            animationSpec = spring()
                        ).value
                    )
            )
        }
        
        // 添加任务按钮
        IconButton(
            onClick = onCreateTask,
            modifier = Modifier.size(36.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        color = Color(0xFFC4B5D4).copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加浮动任务",
                    tint = Color(0xFFC4B5D4),
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

/**
 * 浮动任务计数徽章
 */
@Composable
private fun FloatingTaskCountBadge(count: Int) {
    Surface(
        color = Color(0xFFC4B5D4),
        shape = CircleShape,
        shadowElevation = 0.dp
    ) {
        Text(
            text = count.toString(),
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            fontSize = 12.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
    }
}

/**
 * 浮动任务列表
 */
@Composable
private fun FloatingTaskList(
    tasks: List<Task>,
    title: String,
    onTaskSchedule: (Task, LocalDateTime) -> Unit,
    onTaskUnschedule: (Task) -> Unit,
    isScheduledList: Boolean = false
) {
    Column {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF6B7280),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        tasks.forEach { task ->
            FloatingTaskItem(
                task = task,
                onTaskSchedule = onTaskSchedule,
                onTaskUnschedule = onTaskUnschedule,
                isScheduled = isScheduledList,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
    }
}

/**
 * 单个浮动任务项 - 支持拖拽功能
 */
@Composable
private fun FloatingTaskItem(
    task: Task,
    onTaskSchedule: (Task, LocalDateTime) -> Unit,
    onTaskUnschedule: (Task) -> Unit,
    isScheduled: Boolean = false,
    modifier: Modifier = Modifier
) {
    var isDragging by remember { mutableStateOf(false) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    
    // 🎨 现代化卡片设计 - 完全取消阴影
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                translationX = dragOffset.x,
                translationY = dragOffset.y,
                scaleX = if (isDragging) 1.05f else 1f,
                scaleY = if (isDragging) 1.05f else 1f,
                alpha = if (isDragging) 0.8f else 1f
            )
            .pointerInput(task.id) {
                detectDragGestures(
                    onDragStart = { 
                        isDragging = true
                        dragOffset = Offset.Zero
                    },
                    onDragEnd = { 
                        isDragging = false
                        
                        // 🆕 增强拖拽检测：如果拖拽距离足够大，触发拖拽事件
                        val dragDistance = kotlin.math.sqrt(dragOffset.x * dragOffset.x + dragOffset.y * dragOffset.y)
                        if (dragDistance > 50f) { // 拖拽阈值
                            // 这里可以通过回调传递拖拽信息给外部组件
                            // 外部组件会处理目标区域检测和任务安排
                            // 暂时先安排到今天作为演示
                            if (!task.isScheduled()) {
                                onTaskSchedule(task, LocalDateTime.now().withHour(9).withMinute(0))
                            }
                        }
                        
                        dragOffset = Offset.Zero
                    }
                ) { change, dragAmount ->
                    dragOffset = dragOffset + dragAmount
                }
            }
            .zIndex(if (isDragging) 1f else 0f),
        color = if (isDragging) 
            Color(0xFFE3F2FD) // 拖拽时的背景色
        else 
            Color.White.copy(alpha = 0.9f), // 正常背景色
        shadowElevation = 0.dp, // 🚫 完全取消阴影
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(
            width = if (isDragging) 2.dp else 1.dp,
            color = if (isDragging) 
                Color(0xFFC4B5D4) // 拖拽时的莫兰迪薰衣草紫边框
            else 
                Color(0xFFE0E0E0).copy(alpha = 0.5f) // 正常边框
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 拖拽手柄
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "拖拽",
                tint = Color(0xFF9E9E9E),
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = getPriorityColor(task.priority),
                        shape = CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (task.description.isNotEmpty()) {
                    Text(
                        text = task.description,
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 显示时间信息
                if (task.isScheduled()) {
                    Text(
                        text = "已安排到 ${task.scheduledDate!!.format(DateTimeFormatter.ofPattern("MM/dd HH:mm"))}",
                        fontSize = 11.sp,
                        color = Color(0xFF059669),
                        fontWeight = FontWeight.Medium
                    )
                } else {
                    Text(
                        text = "本周完成",
                        fontSize = 11.sp,
                        color = Color(0xFFC4B5D4),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 操作按钮
            if (isScheduled) {
                IconButton(
                    onClick = { onTaskUnschedule(task) },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.EventBusy,
                        contentDescription = "取消安排",
                        tint = Color(0xFFEF4444),
                        modifier = Modifier.size(16.dp)
                    )
                }
            } else {
                IconButton(
                    onClick = { 
                        // 快速安排到今天
                        onTaskSchedule(task, LocalDateTime.now()) 
                    },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Event,
                        contentDescription = "安排到今天",
                        tint = Color(0xFF10B981),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 时间预估芯片
 */
@Composable
private fun FloatingTaskTimeChip(minutes: Int) {
    val displayText = when {
        minutes < 60 -> "${minutes}分钟"
        minutes % 60 == 0 -> "${minutes / 60}小时"
        else -> "${minutes / 60}h${minutes % 60}m"
    }
    
    Box(
        modifier = Modifier
            .background(
                color = Color(0xFFC4B5D4).copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 6.dp, vertical = 2.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = displayText,
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFFC4B5D4)
        )
    }
}

/**
 * 空状态显示
 */
@Composable
private fun FloatingTasksEmptyState() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Schedule,
            contentDescription = null,
            tint = Color(0xFFD1D5DB),
            modifier = Modifier.size(48.dp)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Text(
            text = "本周暂无浮动任务",
            fontSize = 14.sp,
            color = Color(0xFF6B7280),
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
        
        Text(
            text = "点击 + 号添加任务",
            fontSize = 12.sp,
            color = Color(0xFF9CA3AF),
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
    }
}

/**
 * 获取优先级颜色的辅助函数
 */
private fun getPriorityColor(priority: Priority?): Color {
    return when (priority) {
        Priority.URGENT -> Color(0xFFEF4444)
        Priority.HIGH -> Color(0xFFF97316)
        Priority.MEDIUM -> Color(0xFFEAB308)
        Priority.LOW -> Color(0xFF22C55E)
        null -> Color(0xFF6B7280)
    }
}

/**
 * 拖拽区域检测组件
 */
@Composable
fun DragTargetArea(
    onTaskDrop: (Task, LocalDateTime) -> Unit,
    targetDate: LocalDateTime,
    isHighlighted: Boolean = false,
    content: @Composable () -> Unit
) {
    var isDropTarget by remember { mutableStateOf(false) }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = when {
                    isDropTarget -> Color(0xFFF0EBFF).copy(alpha = 0.5f) // 莫兰迪紫的浅色背景
                    isHighlighted -> Color(0xFFF3F4F6)
                    else -> Color.Transparent
                }
            )
            .border(
                width = if (isDropTarget) 2.dp else 0.dp,
                color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                shape = RoundedCornerShape(8.dp)
            )
            .padding(
                horizontal = if (isDropTarget) 4.dp else 0.dp,
                vertical = if (isDropTarget) 2.dp else 0.dp
            )
    ) {
        content()
        
        // 拖拽检测覆盖层
        if (isDropTarget) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        color = Color(0xFFC4B5D4).copy(alpha = 0.1f), // 莫兰迪紫
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "松开以安排到这一天",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFC4B5D4) // 莫兰迪紫
                )
            }
        }
    }
}

/**
 * 增强版浮动任务区域 - 支持拖拽到特定日期
 */
@Composable
fun EnhancedFloatingTasksSection(
    floatingTasks: List<Task>,
    isExpanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    onTaskSchedule: (Task, LocalDateTime) -> Unit,
    onTaskUnschedule: (Task) -> Unit,
    onCreateFloatingTask: () -> Unit,
    weekDates: List<LocalDate>,
    onTaskDropToDate: (Task, LocalDate) -> Unit,
    onTaskDragStart: ((Task) -> Unit)? = null, // 🔧 新增拖拽开始回调
    modifier: Modifier = Modifier
) {
    val scrollState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color(0xFFFAFAFA),
        shape = RoundedCornerShape(12.dp),
        shadowElevation = 0.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = Color(0xFFC4B5D4),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "本周任务",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1F2937)
                    )
                    if (floatingTasks.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                        FloatingTaskCountBadge(count = floatingTasks.size)
                    }
                }

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 添加任务按钮
                    IconButton(
                        onClick = onCreateFloatingTask,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    color = Color(0xFFC4B5D4).copy(alpha = 0.1f),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "添加浮动任务",
                                tint = Color(0xFFC4B5D4),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }

                    // 展开/折叠按钮
                    IconButton(
                        onClick = { onExpandedChange(!isExpanded) },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = if (isExpanded) 
                                Icons.Default.ExpandLess 
                            else 
                                Icons.Default.ExpandMore,
                            contentDescription = if (isExpanded) "折叠" else "展开",
                            tint = Color(0xFF6B7280),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }

            // 任务列表
            AnimatedVisibility(
                visible = isExpanded,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                if (floatingTasks.isEmpty()) {
                    FloatingTasksEmptyState()
                } else {
                    // 🔧 支持有限滚动的任务列表
                    LazyColumn(
                        state = scrollState,
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 200.dp), // 限制列表最大高度
                        contentPadding = PaddingValues(vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(floatingTasks) { task ->
                            EnhancedFloatingTaskItem(
                                task = task,
                                onTaskSchedule = onTaskSchedule,
                                onTaskUnschedule = onTaskUnschedule,
                                weekDates = weekDates,
                                onTaskDropToDate = onTaskDropToDate,
                                onTaskDragStart = onTaskDragStart // 🔧 传递拖拽开始回调
                            )
                        }
                    }
                }
            }
            
            // 🆕 添加周日期快速拖拽区域
            if (isExpanded && floatingTasks.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "拖拽到日期",
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(weekDates) { date ->
                        WeekDateDropTarget(
                            date = date,
                            onTaskDrop = { task ->
                                onTaskDropToDate(task, date)
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 增强版浮动任务项 - 支持拖拽到特定日期
 */
@Composable
fun EnhancedFloatingTaskItem(
    task: Task,
    onTaskSchedule: (Task, LocalDateTime) -> Unit,
    onTaskUnschedule: (Task) -> Unit,
    weekDates: List<LocalDate>,
    onTaskDropToDate: (Task, LocalDate) -> Unit,
    onTaskDragStart: ((Task) -> Unit)? = null, // 🔧 新增拖拽开始回调
    modifier: Modifier = Modifier
) {
    var isDragging by remember { mutableStateOf(false) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                translationX = dragOffset.x,
                translationY = dragOffset.y,
                scaleX = if (isDragging) 1.05f else 1f,
                scaleY = if (isDragging) 1.05f else 1f,
                alpha = if (isDragging) 0.8f else 1f
            )
            .pointerInput(task.id) {
                detectDragGestures(
                    onDragStart = { 
                        isDragging = true
                        dragOffset = Offset.Zero
                        onTaskDragStart?.invoke(task) // 🔧 触发拖拽开始回调
                    },
                    onDragEnd = { 
                        isDragging = false
                        dragOffset = Offset.Zero
                    }
                ) { change, dragAmount ->
                    dragOffset = dragOffset + dragAmount
                }
            }
            .zIndex(if (isDragging) 1f else 0f),
        color = if (isDragging) 
            Color(0xFFE3F2FD)
        else 
            Color.White.copy(alpha = 0.9f),
        shadowElevation = if (isDragging) 8.dp else 0.dp,
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(
            width = if (isDragging) 2.dp else 1.dp,
            color = if (isDragging) 
                Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
            else 
                Color(0xFFE0E0E0).copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 拖拽手柄
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "拖拽",
                tint = Color(0xFF9E9E9E),
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = getPriorityColor(task.priority),
                        shape = CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (task.description.isNotEmpty()) {
                    Text(
                        text = task.description,
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Text(
                    text = "本周完成",
                    fontSize = 11.sp,
                    color = Color(0xFFC4B5D4),
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 快速安排按钮
            IconButton(
                onClick = { 
                    onTaskSchedule(task, LocalDateTime.now().withHour(9).withMinute(0)) 
                },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Event,
                    contentDescription = "安排到今天",
                    tint = Color(0xFF10B981),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 周日期拖拽目标
 */
@Composable
fun WeekDateDropTarget(
    date: LocalDate,
    onTaskDrop: (Task) -> Unit
) {
    var isDropTarget by remember { mutableStateOf(false) }
    val isToday = date == LocalDate.now()
    val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
    
    Surface(
        modifier = Modifier
            .size(width = 50.dp, height = 60.dp)
            .background(
                color = when {
                    isDropTarget -> Color(0xFFF0EBFF) // 莫兰迪紫的浅色背景
                    isToday -> Color(0xFFF3F4F6)
                    else -> Color.Transparent
                }
            )
            .border(
                width = if (isDropTarget) 2.dp else 1.dp,
                color = if (isDropTarget) 
                    Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                else 
                    Color(0xFFE5E7EB),
                shape = RoundedCornerShape(8.dp)
            ),
        color = Color.Transparent,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(4.dp)
        ) {
            Text(
                text = date.dayOfMonth.toString(),
                fontSize = 16.sp,
                fontWeight = if (isToday) FontWeight.Bold else FontWeight.Medium,
                color = when {
                    isDropTarget -> Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                    isToday -> Color(0xFFC4B5D4)
                    isWeekend -> Color(0xFFE57373)
                    else -> Color(0xFF374151)
                }
            )
            
            Text(
                text = when (date.dayOfWeek) {
                    DayOfWeek.MONDAY -> "周一"
                    DayOfWeek.TUESDAY -> "周二"
                    DayOfWeek.WEDNESDAY -> "周三"
                    DayOfWeek.THURSDAY -> "周四"
                    DayOfWeek.FRIDAY -> "周五"
                    DayOfWeek.SATURDAY -> "周六"
                    DayOfWeek.SUNDAY -> "周日"
                },
                fontSize = 10.sp,
                color = Color(0xFF9CA3AF)
            )
            
            if (isDropTarget) {
                Spacer(modifier = Modifier.height(2.dp))
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    tint = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                    modifier = Modifier.size(12.dp)
                )
            }
        }
    }
}

/**
 * 增强版今日任务区域 - 支持拖拽目标和有限滚动
 */
@Composable
fun EnhancedTodayTasksSection(
    tasks: List<CalendarEvent>,
    selectedDate: LocalDate,
    viewModel: CalendarViewModel,
    onTaskDrop: (Task, LocalDate) -> Unit,
    draggedTask: Task? = null // 🔧 新增当前拖拽的任务状态
) {
    var isDropTarget by remember { mutableStateOf(false) }
    
    // 🔧 当有任务正在拖拽时，自动高亮显示为拖拽目标
    val shouldHighlight = draggedTask != null || isDropTarget
    
    if (tasks.isEmpty()) {
        // 空状态 - 拖拽目标区域
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 80.dp)
                .background(
                    color = if (shouldHighlight) 
                        Color(0xFFF0EBFF).copy(alpha = 0.5f) // 莫兰迪紫的浅色背景
                    else 
                        Color.Transparent
                )
                .border(
                    width = if (shouldHighlight) 2.dp else 1.dp,
                    color = if (shouldHighlight) 
                        Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                    else 
                        Color(0xFFE5E7EB).copy(alpha = 0.5f),
                    shape = RoundedCornerShape(8.dp)
                ),
            color = Color.Transparent,
            shape = RoundedCornerShape(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (isDropTarget) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "松开以安排到这一天",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                            textAlign = TextAlign.Center
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = null,
                            tint = Color(0xFFD1D5DB),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "今日无任务",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(2.dp))
                        Text(
                            text = "拖拽浮动任务到这里安排",
                            fontSize = 12.sp,
                            color = Color.Gray.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    } else {
        // 有任务时 - 支持有限滚动的任务列表
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = if (isDropTarget) 
                        Color(0xFFF0EBFF).copy(alpha = 0.3f) // 莫兰迪紫的浅色背景
                    else 
                        Color.Transparent
                )
                .border(
                    width = if (isDropTarget) 2.dp else 0.dp,
                    color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                    shape = RoundedCornerShape(8.dp)
                ),
            color = Color.Transparent,
            shape = RoundedCornerShape(8.dp)
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 300.dp), // 限制任务列表最大高度
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(tasks) { event ->
                    SimpleCalendarEventItem(
                        event = event,
                        onEventClick = { /* 处理点击事件 */ }
                    )
                }
                
                // 在任务列表底部添加拖拽提示
                if (isDropTarget) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "松开以安排到${selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 增强版今日习惯区域 - 支持有限滚动
 */
@Composable
fun EnhancedDailyHabitsSection(
    selectedDate: LocalDate,
    habits: List<HabitData>,
    habitViewModel: HabitViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    if (habits.isEmpty()) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "今日无习惯",
                fontSize = 12.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
        return
    }
    
    // 🔧 支持有限滚动的习惯网格
    LazyVerticalGrid(
        columns = GridCells.Fixed(3), // 每行3个习惯
        modifier = modifier
            .fillMaxWidth()
            .heightIn(max = 180.dp), // 限制习惯区域最大高度
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(habits) { habit ->
            val isCompleted = habit.completedDates.contains(selectedDate)
            
            // 习惯项容器
            Surface(
                modifier = Modifier
                    .aspectRatio(1f) // 保持正方形比例
                    .clip(RoundedCornerShape(12.dp))
                    .background(habit.color.copy(alpha = 0.1f))
                    .clickable {
                        if (isCompleted) {
                            habitViewModel.removeCompletedDate(habit.id, selectedDate)
                            Toast.makeText(context, "已取消「${habit.name}」打卡", Toast.LENGTH_SHORT).show()
                        } else {
                            habitViewModel.addCompletedDate(habit.id, selectedDate)
                            Toast.makeText(context, "「${habit.name}」打卡成功", Toast.LENGTH_SHORT).show()
                        }
                    },
                color = habit.color.copy(alpha = if (isCompleted) 0.2f else 0.1f),
                shadowElevation = 0.dp,
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.padding(8.dp)
                ) {
                    // 完成状态指示器
                    Icon(
                        imageVector = if (isCompleted) Icons.Default.CheckCircle else Icons.Default.RadioButtonUnchecked,
                        contentDescription = if (isCompleted) "已完成" else "未完成",
                        tint = if (isCompleted) habit.color else habit.color.copy(alpha = 0.6f),
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 习惯名称
                    Text(
                        text = habit.name,
                        fontSize = 10.sp,
                        color = Color.Black,
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 2,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 简单的日历事件项组件
 */
@Composable
private fun SimpleCalendarEventItem(
    event: CalendarEvent,
    onEventClick: (CalendarEvent) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onEventClick(event) },
        color = Color.White.copy(alpha = 0.9f),
        shape = RoundedCornerShape(8.dp),
        shadowElevation = 0.dp,
        border = BorderStroke(
            width = 1.dp,
            color = Color(0xFFE0E0E0).copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成状态指示器
            Surface(
                modifier = Modifier.size(16.dp),
                color = if (event.isCompleted) 
                    Color(0xFF10B981).copy(alpha = 0.2f)
                else 
                    Color.Transparent,
                shape = CircleShape,
                border = BorderStroke(
                    width = 1.5.dp,
                    color = if (event.isCompleted) 
                        Color(0xFF10B981)
                    else 
                        Color(0xFFD1D5DB)
                )
            ) {
                if (event.isCompleted) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已完成",
                            tint = Color(0xFF10B981),
                            modifier = Modifier.size(10.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = event.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (event.isCompleted) 
                        Color(0xFF6B7280)
                    else 
                        Color(0xFF1F2937),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // CalendarEvent 没有 description 字段，所以注释掉这部分
                /*
                if (event.description.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = event.description,
                        fontSize = 12.sp,
                        color = Color(0xFF9CA3AF),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                */
                
                // 时间信息
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(top = 4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = Color(0xFF9CA3AF),
                        modifier = Modifier.size(12.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    val timeText = if (event.isAllDay == true) {
                        "全天"
                    } else {
                        val startTime = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                        val endTime = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: 
                            event.start.plusMinutes(15).format(DateTimeFormatter.ofPattern("HH:mm"))
                        "$startTime - $endTime"
                    }
                    
                    Text(
                        text = timeText,
                        fontSize = 11.sp,
                        color = Color(0xFF9CA3AF),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
} 