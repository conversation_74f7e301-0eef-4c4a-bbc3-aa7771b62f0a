package com.timeflow.app.utils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/**
 * 日期时间工具类
 */
object DateTimeUtils {
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
    private val friendlyDateFormatter = DateTimeFormatter.ofPattern("MM月dd日")
    private val friendlyDateTimeFormatter = DateTimeFormatter.ofPattern("MM月dd日 HH:mm")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
    
    /**
     * 将LocalDateTime格式化为标准日期字符串 (yyyy-MM-dd)
     */
    fun formatDate(dateTime: LocalDateTime?): String {
        return dateTime?.format(dateFormatter) ?: ""
    }
    
    /**
     * 将LocalDate格式化为标准日期字符串 (yyyy-MM-dd)
     */
    fun formatDate(date: LocalDate?): String {
        return date?.format(dateFormatter) ?: ""
    }
    
    /**
     * 将LocalDateTime格式化为标准日期时间字符串 (yyyy-MM-dd HH:mm)
     */
    fun formatDateTime(dateTime: LocalDateTime?): String {
        return dateTime?.format(dateTimeFormatter) ?: ""
    }
    
    /**
     * 将LocalDateTime格式化为友好的日期字符串 (MM月dd日)
     */
    fun formatFriendlyDate(dateTime: LocalDateTime?): String {
        return dateTime?.format(friendlyDateFormatter) ?: ""
    }
    
    /**
     * 将LocalDateTime格式化为友好的日期时间字符串 (MM月dd日 HH:mm)
     */
    fun formatFriendlyDateTime(dateTime: LocalDateTime?): String {
        return dateTime?.format(friendlyDateTimeFormatter) ?: ""
    }
    
    /**
     * 将LocalDateTime格式化为时间字符串 (HH:mm)
     */
    fun formatTime(dateTime: LocalDateTime?): String {
        return dateTime?.format(timeFormatter) ?: ""
    }
    
    /**
     * 计算两个日期之间的天数差异
     */
    fun daysBetween(start: LocalDateTime?, end: LocalDateTime?): Long {
        if (start == null || end == null) return 0
        return ChronoUnit.DAYS.between(start.toLocalDate(), end.toLocalDate())
    }
    
    /**
     * 获取相对时间描述
     * 如：刚刚、5分钟前、1小时前、昨天、2天前等
     */
    fun getRelativeTimeSpan(dateTime: LocalDateTime?): String {
        if (dateTime == null) return ""
        
        val now = LocalDateTime.now()
        val minutes = ChronoUnit.MINUTES.between(dateTime, now)
        val hours = ChronoUnit.HOURS.between(dateTime, now)
        val days = ChronoUnit.DAYS.between(dateTime, now)
        
        return when {
            minutes < 1 -> "刚刚"
            minutes < 60 -> "${minutes}分钟前"
            hours < 24 -> "${hours}小时前"
            days == 1L -> "昨天"
            days < 30 -> "${days}天前"
            else -> formatDate(dateTime)
        }
    }
    
    /**
     * 检查日期是否为今天
     */
    fun isToday(dateTime: LocalDateTime?): Boolean {
        if (dateTime == null) return false
        val today = LocalDate.now()
        return dateTime.toLocalDate() == today
    }
    
    /**
     * 检查日期是否为明天
     */
    fun isTomorrow(dateTime: LocalDateTime?): Boolean {
        if (dateTime == null) return false
        val tomorrow = LocalDate.now().plusDays(1)
        return dateTime.toLocalDate() == tomorrow
    }
    
    /**
     * 获取友好的截止日期显示
     */
    fun getFriendlyDueDate(dueDate: LocalDateTime?): String {
        return when {
            dueDate == null -> "无截止日期"
            isToday(dueDate) -> "今天 ${formatTime(dueDate)}"
            isTomorrow(dueDate) -> "明天 ${formatTime(dueDate)}"
            else -> formatFriendlyDateTime(dueDate)
        }
    }
    
    /**
     * 将时间戳转换为LocalDateTime
     */
    fun timestampToLocalDateTime(timestamp: Long): LocalDateTime {
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.systemDefault()
        )
    }
    
    /**
     * 将LocalDateTime转换为时间戳
     */
    fun localDateTimeToTimestamp(dateTime: LocalDateTime): Long {
        return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
    }
    
    /**
     * 获取友好的相对时间格式
     * 类似于：刚刚、5分钟前、1小时前、昨天、2天前等
     */
    fun formatRelativeTime(dateTime: LocalDateTime?): String {
        return getRelativeTimeSpan(dateTime)
    }
} 