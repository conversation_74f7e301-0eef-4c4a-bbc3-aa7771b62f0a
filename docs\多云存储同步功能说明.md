# 多云存储同步功能说明

## 功能概述

TimeFlow应用现已支持多个云存储服务提供商的数据同步功能，包括：

- **Amazon S3** - 亚马逊云存储服务
- **七牛云** - 七牛云对象存储
- **腾讯云COS** - 腾讯云对象存储
- **阿里云OSS** - 阿里云对象存储服务

## 功能特性

### 1. 统一的配置界面
- 支持在同一界面中选择不同的云服务提供商
- 根据选择的提供商动态显示相应的配置字段
- 提供清晰的字段说明和示例

### 2. 多云服务支持
- **AWS S3**: 支持标准的S3 API
- **七牛云**: 支持七牛云的对象存储API
- **腾讯云COS**: 支持腾讯云COS API
- **阿里云OSS**: 支持阿里云OSS API

### 3. 统一的数据操作
- 连接测试
- 数据上传（备份）
- 数据下载（恢复）
- 文件列表管理

## 配置说明

### Amazon S3 配置
- **区域节点(Endpoint)**: S3服务端点，如 `s3.amazonaws.com`
- **存储桶(Bucket)**: S3存储桶名称
- **访问密钥(AccessKeyId)**: AWS访问密钥ID
- **密钥(SecretAccessKey)**: AWS秘密访问密钥
- **地区**: AWS区域，如 `us-east-1`

### 七牛云配置
- **区域节点(Endpoint)**: 七牛云S3兼容端点，如 `s3-cn-east-1.qiniucs.com`
- **存储空间(Bucket)**: 七牛云存储空间名称
- **AccessKey**: 七牛云访问密钥
- **SecretKey**: 七牛云秘密密钥
- **自定义域名**: 可选的CDN域名
- **地区**: 七牛云区域，如 `cn-east-1`

### 腾讯云COS配置
- **区域节点(Endpoint)**: 腾讯云COS端点，如 `cos.ap-beijing.myqcloud.com`
- **存储桶(Bucket)**: 腾讯云COS存储桶名称
- **SecretId**: 腾讯云访问密钥ID
- **SecretKey**: 腾讯云秘密访问密钥
- **地区**: 腾讯云区域，如 `ap-beijing`

### 阿里云OSS配置
- **区域节点(Endpoint)**: 阿里云OSS端点，如 `oss-cn-hangzhou.aliyuncs.com`
- **存储桶(Bucket)**: 阿里云OSS存储桶名称
- **AccessKeyId**: 阿里云访问密钥ID
- **AccessKeySecret**: 阿里云秘密访问密钥
- **地区**: 阿里云区域，如 `cn-hangzhou`

## 使用流程

### 1. 配置云存储
1. 打开设置页面
2. 选择"同步设置"
3. 在云存储配置区域选择云服务提供商
4. 填写相应的配置信息
5. 点击"扫描并迁移资源到S3"测试连接

### 2. 数据同步
1. 配置完成后，可以进行手动同步
2. 系统会将任务数据上传到云存储
3. 支持从云存储恢复数据

### 3. 数据格式
- 数据以JSON格式存储
- 文件名格式：`timeflow_backup_YYYYMMDD_HHMMSS.json`
- 包含完整的任务信息和元数据

## 技术架构

### 1. 抽象层设计
```kotlin
interface CloudStorageClient {
    suspend fun testConnection(): Result<String>
    suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String>
    suspend fun downloadData(fileName: String?): Result<ByteArray>
    suspend fun listFiles(prefix: String): Result<List<String>>
}
```

### 2. 工厂模式
使用工厂模式根据配置创建相应的云存储客户端：
```kotlin
object CloudStorageClientFactory {
    fun createClient(config: CloudStorageConfig): CloudStorageClient {
        return when (CloudProvider.valueOf(config.provider)) {
            CloudProvider.AWS_S3 -> AwsS3StorageClient(config)
            CloudProvider.QINIU_CLOUD -> QiniuCloudStorageClient(config)
            CloudProvider.TENCENT_CLOUD -> TencentCloudStorageClient(config)
            CloudProvider.ALIYUN_OSS -> AliyunOssStorageClient(config)
        }
    }
}
```

### 3. 向后兼容
- 保持原有的S3配置接口
- 新增CloudStorageConfig配置模型
- 自动转换旧配置到新格式

## 依赖库

### AWS SDK
```gradle
implementation 'com.amazonaws:aws-android-sdk-core:2.76.0'
implementation 'com.amazonaws:aws-android-sdk-s3:2.76.0'
implementation 'com.amazonaws:aws-android-sdk-auth-core:2.76.0'
```

### 七牛云SDK
```gradle
implementation 'com.qiniu:qiniu-android-sdk:8.7.0'
```

### 腾讯云SDK
```gradle
implementation 'com.tencent.qcloud:cosxml:5.9.15'
```

### 阿里云SDK
```gradle
implementation 'com.aliyun.dpa:oss-android-sdk:2.9.13'
```

## 错误处理

### 1. 连接错误
- 网络连接问题
- 认证信息错误
- 服务端点配置错误

### 2. 上传错误
- 存储空间权限问题
- 文件大小限制
- 网络传输中断

### 3. 下载错误
- 文件不存在
- 权限不足
- 数据格式错误

## 安全考虑

### 1. 密钥管理
- 密钥在UI中以密码形式显示
- 支持显示/隐藏密钥功能
- 建议使用具有最小权限的访问密钥

### 2. 数据加密
- 支持HTTPS传输
- 云存储服务端加密
- 本地配置信息安全存储

### 3. 权限控制
- 建议为应用创建专用的存储桶
- 配置最小必要权限
- 定期轮换访问密钥

## 未来扩展

### 1. 更多云服务支持
- Google Cloud Storage
- Microsoft Azure Blob Storage
- 华为云OBS

### 2. 高级功能
- 增量同步
- 数据压缩
- 多版本备份
- 自动同步调度

### 3. 用户体验优化
- 同步进度显示
- 批量操作
- 同步历史记录
- 冲突解决策略

## 注意事项

1. **网络要求**: 需要稳定的网络连接
2. **存储费用**: 各云服务商收费标准不同，请注意费用控制
3. **数据安全**: 建议定期备份重要数据
4. **配置备份**: 建议记录云存储配置信息
5. **测试环境**: 建议先在测试环境验证配置正确性

## 故障排除

### 常见问题
1. **连接失败**: 检查网络连接和配置信息
2. **权限错误**: 确认访问密钥权限设置
3. **上传失败**: 检查存储空间配额和网络状态
4. **下载失败**: 确认备份文件存在且可访问

### 日志查看
应用会记录详细的同步日志，可通过以下标签查看：
- `AwsS3StorageClient`
- `QiniuCloudStorageClient`
- `TencentCloudStorageClient`
- `AliyunOssStorageClient`
- `SyncRepository`
