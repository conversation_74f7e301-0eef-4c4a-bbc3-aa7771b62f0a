package com.timeflow.app.data.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import java.util.UUID

/**
 * 目标分类数据模型
 * 参考知名应用如TickTick、Todoist、Forest、Any.do等的分类设计
 */
data class GoalCategory(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val icon: ImageVector,
    val color: Color,
    val description: String,
    val timeFrame: GoalTimeFrame = GoalTimeFrame.FLEXIBLE,
    val isDefault: Boolean = true,
    val isCustom: Boolean = false,
    val sortOrder: Int = 0,
    val parentCategoryId: String? = null, // 支持分类层级
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 目标时间框架枚举
 * 参考Project50、年度目标、OKR等应用的时间分类
 */
enum class GoalTimeFrame(
    val displayName: String,
    val description: String,
    val defaultDurationDays: Int?,
    val color: Color
) {
    DAILY("每日目标", "每天完成的小目标", 1, Color(0xFF10B981)),
    WEEKLY("每周目标", "一周内完成的目标", 7, Color(0xFF3B82F6)),
    MONTHLY("月度目标", "一个月内完成的目标", 30, Color(0xFF8B5CF6)),
    QUARTERLY("季度目标", "三个月内完成的目标", 90, Color(0xFFF59E0B)),
    YEARLY("年度目标", "一年内完成的重要目标", 365, Color(0xFFEF4444)),
    PROJECT50("Project50项目", "50天挑战项目", 50, Color(0xFFEC4899)),
    FLEXIBLE("灵活目标", "自定义时间框架的目标", null, Color(0xFF6B7280))
}

/**
 * 分类类型枚举
 */
enum class CategoryType(val displayName: String) {
    TIME_BASED("时间维度"),
    THEME_BASED("主题维度"),
    CUSTOM("自定义分类")
}

/**
 * 获取默认的目标分类
 * 参考知名应用的分类设计
 */
fun getDefaultGoalCategories(): List<GoalCategory> {
    return listOf(
        // 时间维度分类 - 参考OKR、年度规划应用
        GoalCategory(
            id = "yearly_goals",
            name = "年度目标",
            icon = Icons.Default.EventNote,
            color = Color(0xFFEF4444),
            description = "重要的年度计划和长期目标，如职业发展、重大项目",
            timeFrame = GoalTimeFrame.YEARLY,
            sortOrder = 1
        ),
        GoalCategory(
            id = "project50",
            name = "Project50",
            icon = Icons.Default.Flag,
            color = Color(0xFF8B5CF6),
            description = "50天挑战项目，专注突破某个领域或技能",
            timeFrame = GoalTimeFrame.PROJECT50,
            sortOrder = 2
        ),
        GoalCategory(
            id = "quarterly_goals",
            name = "季度目标",
            icon = Icons.Default.DateRange,
            color = Color(0xFFF59E0B),
            description = "三个月内的重要目标，支撑年度目标实现",
            timeFrame = GoalTimeFrame.QUARTERLY,
            sortOrder = 3
        ),
        GoalCategory(
            id = "monthly_goals",
            name = "月度目标",
            icon = Icons.Default.CalendarMonth,
            color = Color(0xFF3B82F6),
            description = "当月要完成的具体目标和里程碑",
            timeFrame = GoalTimeFrame.MONTHLY,
            sortOrder = 4
        ),
        GoalCategory(
            id = "weekly_goals",
            name = "周目标",
            icon = Icons.Default.Today,
            color = Color(0xFF10B981),
            description = "本周内完成的短期目标和任务",
            timeFrame = GoalTimeFrame.WEEKLY,
            sortOrder = 5
        ),
        
        // 主题维度分类 - 参考生活管理应用
        GoalCategory(
            id = "health_fitness",
            name = "健康健身",
            icon = Icons.Default.FitnessCenter,
            color = Color(0xFFEF4444),
            description = "身体健康、运动健身、饮食习惯、生活作息",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 10
        ),
        GoalCategory(
            id = "learning_growth",
            name = "学习成长",
            icon = Icons.Default.School,
            color = Color(0xFF3B82F6),
            description = "知识学习、技能提升、认知升级、自我成长",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 11
        ),
        GoalCategory(
            id = "career_work",
            name = "职业工作",
            icon = Icons.Default.Work,
            color = Color(0xFFF59E0B),
            description = "职业规划、工作技能、项目管理、事业发展",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 12
        ),
        GoalCategory(
            id = "finance_money",
            name = "财务理财",
            icon = Icons.Default.AttachMoney,
            color = Color(0xFF10B981),
            description = "理财投资、储蓄计划、财务目标、收入增长",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 13
        ),
        GoalCategory(
            id = "relationships",
            name = "人际关系",
            icon = Icons.Default.People,
            color = Color(0xFFEC4899),
            description = "社交网络、人际交往、关系维护、情感生活",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 14
        ),
        GoalCategory(
            id = "hobbies_interests",
            name = "兴趣爱好",
            icon = Icons.Default.Palette,
            color = Color(0xFF8B5CF6),
            description = "兴趣培养、创意项目、娱乐活动、艺术创作",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 15
        ),
        GoalCategory(
            id = "family_home",
            name = "家庭生活",
            icon = Icons.Default.Home,
            color = Color(0xFF06B6D4),
            description = "家庭关系、亲子教育、家庭规划、居家生活",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 16
        ),
        GoalCategory(
            id = "personal_development",
            name = "个人提升",
            icon = Icons.Default.Person,
            color = Color(0xFF84CC16),
            description = "个人品质、心理健康、生活品质、精神成长",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 17
        ),
        GoalCategory(
            id = "travel_adventure",
            name = "旅行探索",
            icon = Icons.Default.Flight,
            color = Color(0xFFFF6B6B),
            description = "旅行计划、探索体验、文化交流、冒险挑战",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 18
        ),
        GoalCategory(
            id = "productivity",
            name = "效率提升",
            icon = Icons.Default.Speed,
            color = Color(0xFF4ECDC4),
            description = "时间管理、效率工具、工作流程、生产力优化",
            timeFrame = GoalTimeFrame.FLEXIBLE,
            sortOrder = 19
        )
    )
}

/**
 * 根据时间框架获取分类
 */
fun getCategoriesByTimeFrame(timeFrame: GoalTimeFrame): List<GoalCategory> {
    return getDefaultGoalCategories().filter { it.timeFrame == timeFrame }
}

/**
 * 获取时间维度的分类
 */
fun getTimeBasedCategories(): List<GoalCategory> {
    return getDefaultGoalCategories().filter {
        it.timeFrame != GoalTimeFrame.FLEXIBLE
    }.sortedBy { it.sortOrder }
}

/**
 * 获取主题维度的分类
 */
fun getThemeBasedCategories(): List<GoalCategory> {
    return getDefaultGoalCategories().filter { 
        it.timeFrame == GoalTimeFrame.FLEXIBLE 
    }.sortedBy { it.sortOrder }
}

/**
 * 根据分类ID获取分类
 */
fun getCategoryById(categoryId: String): GoalCategory? {
    return getDefaultGoalCategories().find { it.id == categoryId }
}

/**
 * 获取分类的显示颜色
 */
fun GoalCategory.getDisplayColor(): Color {
    return this.color
}

/**
 * 获取分类的显示图标
 */
fun GoalCategory.getDisplayIcon(): ImageVector {
    return this.icon
}
