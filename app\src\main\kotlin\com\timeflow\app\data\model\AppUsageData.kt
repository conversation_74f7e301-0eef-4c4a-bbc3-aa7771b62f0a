package com.timeflow.app.data.model

import kotlin.time.Duration

/**
 * 应用使用统计数据
 *
 * @param packageName 应用包名
 * @param duration 使用时长
 * @param launchCount 启动次数
 * @param category 应用分类（可选）
 * @param isProductivity 是否为生产力应用
 */
data class AppUsageData(
    val packageName: String,
    val duration: Duration,
    val launchCount: Int,
    val category: String = "未分类",
    val isProductivity: Boolean = false
) {
    companion object {
        // 预定义应用分类
        val CATEGORY_PRODUCTIVITY = "生产力"
        val CATEGORY_SOCIAL = "社交"
        val CATEGORY_ENTERTAINMENT = "娱乐"
        val CATEGORY_UTILITIES = "工具"
        val CATEGORY_COMMUNICATION = "通讯"
    }
} 