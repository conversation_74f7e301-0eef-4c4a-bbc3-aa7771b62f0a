package com.timeflow.app.data.model

import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID

/**
 * 目标优先级枚举
 */
enum class GoalPriority(val value: Int) {
    LOW(0),
    MEDIUM(1),
    HIGH(2),
    URGENT(3)
}

/**
 * 目标审核频率枚举
 */
enum class ReviewFrequency {
    DAILY,
    WEEKLY,
    BIWEEKLY,
    MONTHLY
}

/**
 * 目标模型类 (非实体类，用于业务逻辑)
 */
data class Goal(
    val id: String = UUID.randomUUID().toString(),
    
    // 基本信息
    val title: String,
    val description: String = "",
    val priority: GoalPriority = GoalPriority.MEDIUM,
    val tags: List<String> = emptyList(),
    val categoryId: String = "personal_development", // 目标分类ID
    
    // 时间相关
    val startDate: LocalDateTime? = null,
    val dueDate: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val completedAt: LocalDateTime? = null,
    
    // 状态相关
    val status: String = "进行中",
    val progress: Float = 0f,
    
    // 关联信息
    val relatedTaskIds: List<String> = emptyList(),
    
    // 自定义属性
    val metrics: Map<String, Float> = emptyMap(),
    
    // AI相关特性
    val hasAiBreakdown: Boolean = false,
    val hasAiAnalysis: Boolean = false,
    val aiRecommendations: List<String> = emptyList(),
    val bestTimeSlots: List<TimeSlotInfo> = emptyList(),
    
    // 高级设置
    val reviewFrequency: ReviewFrequency = ReviewFrequency.WEEKLY,
    
    // 量化相关字段
    val goalType: GoalType = GoalType.BOOLEAN,          // 目标类型
    val currentValue: Double? = null,                   // 当前值
    val targetValue: Double? = null,                    // 目标值
    val unit: String? = null,                           // 单位
    val reminderSettings: List<ReminderSetting> = emptyList(), // 提醒设置
    val attachments: List<String> = emptyList(),        // 附件列表
    val isRecurring: Boolean = false,                   // 是否周期性目标
    val recurringSettings: RecurringSettings? = null    // 周期性设置
) {
    /**
     * 计算目标的持续天数
     * 如果目标已完成，返回从开始日期到完成日期的天数
     * 如果目标未完成，返回从开始日期到截止日期的天数
     * 如果没有开始日期，返回0
     */
    fun getDurationDays(): Long {
        val start = startDate ?: return 0

        return when {
            // 已完成目标，计算实际完成耗时
            completedAt != null -> ChronoUnit.DAYS.between(start, completedAt)
            // 未完成但有截止日期，计算计划耗时
            dueDate != null -> ChronoUnit.DAYS.between(start, dueDate)
            // 未完成且无截止日期，计算从开始到现在的天数
            else -> ChronoUnit.DAYS.between(start, LocalDateTime.now())
        }.coerceAtLeast(0) // 确保不返回负数
    }
    
    /**
     * 计算目标进度百分比
     * 返回0到100之间的值
     */
    val progressPercentage: Float
        get() = progress * 100
} 