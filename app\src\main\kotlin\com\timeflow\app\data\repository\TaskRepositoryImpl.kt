package com.timeflow.app.data.repository

import android.util.Log
import androidx.compose.ui.graphics.Color
import com.timeflow.app.data.converter.TaskConverter
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.data.entity.Task as TaskEntity
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.service.TaskReminderScheduler
import com.timeflow.app.utils.TaskReminderUtils
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Provider
import javax.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import androidx.room.withTransaction
import androidx.room.RoomDatabase
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import kotlinx.coroutines.runBlocking
import java.util.UUID

/**
 * 任务仓库实现类
 */
@Singleton
class TaskRepositoryImpl @Inject constructor(
    private val taskDao: TaskDao,
    private val taskDatabase: AppDatabase,
    private val taskReminderSchedulerProvider: Provider<TaskReminderScheduler>,
    private val taskReminderUtils: TaskReminderUtils
) : TaskRepository, BaseRepository<Task, String>() {
    
    companion object {
        private const val TAG = "TaskRepositoryImpl"
    }
    
    /**
     * 将数据库实体映射为业务模型
     */
    private suspend fun mapEntityToModel(entity: TaskEntity): Task {
        // 获取真实的column_id，如果为空或无效则获取默认列ID
        val validColumnId = if (entity.columnId.isNullOrEmpty() || entity.columnId == "default_column") {
            getDefaultColumnId()
        } else {
            // 验证column_id是否存在
            val columnExists = try {
                taskDao.getColumnById(entity.columnId) != null
            } catch (e: Exception) {
                false
            }
            
            if (columnExists) {
                entity.columnId
            } else {
                getDefaultColumnId()
            }
        }
        
        return Task(
            id = entity.id,
            title = entity.title,
            description = entity.description,
            dueDate = entity.dueDate,
            startDate = entity.startDate,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            completedAt = entity.completedAt,
            isCompleted = entity.completedAt != null,
            priority = when (entity.priority) {
                0 -> Priority.URGENT
                1 -> Priority.HIGH
                2 -> Priority.MEDIUM
                3 -> Priority.LOW
                else -> Priority.MEDIUM
            },
            type = if (entity.isFloatingTask) TaskType.FLOATING else TaskType.NORMAL,
            columnId = validColumnId,
            orderIndex = entity.orderIndex,
            status = entity.status ?: "待办",
            parentTaskId = entity.parentTaskId,
            depth = entity.depth,
            hasSubtasks = entity.hasSubtasks,
            progress = entity.progress,
            tags = entity.tagIds.map { tagName -> TaskTag(name = tagName, color = Color.Gray) },
            goalId = entity.goalId,
            dateManuallyModified = entity.dateManuallyModified,
            // 🔧 修复：添加缺失的浮动任务字段映射 - 这是导致浮动任务不显示的关键问题
            isFloatingTask = entity.isFloatingTask,
            floatingWeekStart = entity.floatingWeekStart,
            floatingWeekEnd = entity.floatingWeekEnd,
            scheduledDate = entity.scheduledDate,
            floatingTaskOrder = entity.floatingTaskOrder,
            estimatedTimeMinutes = entity.estimatedTime ?: 0,
            actualTimeMinutes = entity.actualTime ?: 0,
            reminderTime = entity.reminderTime
        )
    }
    
    /**
     * 将业务模型映射为数据库实体
     */
    private fun mapModelToEntity(model: Task): TaskEntity {
        return TaskEntity(
            id = model.id,
            title = model.title,
            description = model.description,
            columnId = model.columnId,
            orderIndex = model.orderIndex,
            priority = model.priority?.ordinal ?: 0,
            dueDate = model.dueDate,
            startDate = model.startDate,
            createdAt = model.createdAt,
            updatedAt = LocalDateTime.now(),
            completedAt = model.completedAt,
            parentTaskId = model.parentTaskId,
            depth = model.depth ?: 0,
            status = model.status,
            hasSubtasks = model.hasSubtasks,
            progress = model.progress,
            tagIds = model.tags.map { it.name },
            goalId = model.goalId,
            dateManuallyModified = model.dateManuallyModified,
            // 🔧 修复：添加缺失的浮动任务字段映射
            isFloatingTask = model.isFloatingTask,
            floatingWeekStart = model.floatingWeekStart,
            floatingWeekEnd = model.floatingWeekEnd,
            scheduledDate = model.scheduledDate,
            floatingTaskOrder = model.floatingTaskOrder,
            estimatedTime = if (model.estimatedTimeMinutes > 0) model.estimatedTimeMinutes else null,
            actualTime = if (model.actualTimeMinutes > 0) model.actualTimeMinutes else null,
            reminderTime = model.reminderTime
        )
    }
    
    override suspend fun getAllTasks(): List<Task> {
        // 使用查询缓存
        val cacheKey = createQuerySignature("getAllTasks")
        val cachedResult = getQueryFromCache(cacheKey)
        
        if (cachedResult != null) {
            return cachedResult
        }
        
        // 缓存未命中，从数据库获取
        return executeSafely {
            val entities = taskDao.getAllTasks()
            val result = entities.map { mapEntityToModel(it) }
            addQueryToCache(cacheKey, result)
            result
        }.getOrElse { 
            Timber.e(it, "获取所有任务失败")
            emptyList() 
        }
    }
    
    override fun observeAllTasks(): Flow<List<Task>> {
        return taskDao.observeAllTasks()
            .map { entities -> 
                // 在Flow中处理suspend函数调用
                entities.map { entity ->
                    runBlocking { mapEntityToModel(entity) }
                }
            }
            .flowOn(databaseDispatcher)
            .onEach { tasks ->
                // 更新缓存
                val cacheKey = createQuerySignature("getAllTasks")
                addQueryToCache(cacheKey, tasks)
            }
            .catch { e ->
                Timber.e(e, "观察任务列表时出错")
                emit(emptyList())
            }
    }
    
    override suspend fun getTaskById(taskId: String): Task? {
        try {
            Timber.d("开始从数据库获取任务: taskId = $taskId")
            
            // 检查缓存
            val cachedTask = getFromCache(taskId)
            if (cachedTask != null) {
                Timber.d("从缓存获取任务: ${cachedTask.title}")
                return cachedTask
            }
            
            // 缓存未命中，从数据库获取
            return executeSafely {
                val entity = taskDao.getTaskById(taskId)
                val task = entity?.let { mapEntityToModel(it) }
                
                if (task != null) {
                    // 更新缓存
                    addToCache(taskId, task)
                    Timber.d("成功获取任务: ${task.title}")
                } else {
                    Timber.w("任务不存在: taskId = $taskId")
                }
                
                task
            }.getOrElse { 
                Timber.e(it, "获取任务失败: taskId = $taskId")
                null 
            }
        } catch (e: Exception) {
            Timber.e(e, "获取任务异常: taskId = $taskId")
            throw e
        }
    }
    
    override fun observeTaskById(taskId: String): Flow<Task?> {
        return taskDao.observeTaskById(taskId)
            .map { entity -> 
                entity?.let { 
                    runBlocking { mapEntityToModel(it) }
                }
            }
            .flowOn(databaseDispatcher)
            .onEach { task ->
                // 更新缓存
                if (task != null) {
                    addToCache(taskId, task)
                }
            }
            .catch { e ->
                Timber.e(e, "观察任务时出错: taskId = $taskId")
                emit(null)
            }
    }
    
    override suspend fun insertTask(task: Task) {
        try {
            Timber.d("开始插入任务: ${task.title}, id=${task.id}")
            
            executeSafely {
                // 检查是否为子任务
                if (task.parentTaskId != null) {
                    val parentTask = taskDao.getTaskById(task.parentTaskId)
                    if (parentTask != null) {
                        Timber.d("父任务存在: ${parentTask.title}, id=${parentTask.id}")
                    } else {
                        Timber.e("父任务不存在: parentTaskId=${task.parentTaskId}，这会导致外键约束错误")
                    }
                }
                
                val entity = mapModelToEntity(task)
                taskDao.insert(entity)
                
                // 🔔 新增：为新任务设置提醒
                Timber.d("🔍 检查任务提醒设置条件: ${task.title}")
                Timber.d("  - dueDate: ${task.dueDate}")
                Timber.d("  - reminderTime: ${task.reminderTime}")
                Timber.d("  - startDate: ${task.startDate}")
                
                if (task.dueDate != null || task.reminderTime != null || task.startDate != null) {
                    Timber.d("✅ 任务符合提醒设置条件，开始设置提醒")
                    try {
                        // 🔧 修复：截断到毫秒精度避免纳秒级别的时间误差
                        val reminderTimeMillisOnly = task.reminderTime?.withNano((task.reminderTime?.nano?.div(1_000_000))?.times(1_000_000) ?: 0)
                        Log.d(TAG, "  - reminderTime (原始): ${task.reminderTime}")
                        Log.d(TAG, "  - reminderTime (毫秒精度): $reminderTimeMillisOnly")
                        
                        // 🔧 修复：获取用户的通知设置
                        val notificationSettings = try {
                            // 从DataStore读取通知设置（这里使用默认设置，实际项目中应注入NotificationSettingsViewModel）
                            com.timeflow.app.ui.screen.settings.NotificationSettings()
                    } catch (e: Exception) {
                            Log.w(TAG, "无法获取通知设置，使用默认设置", e)
                            com.timeflow.app.ui.screen.settings.NotificationSettings()
                        }
                        
                        // 🔧 修复：使用新的suspend setTaskReminder方法
                        val taskEntity = mapModelToEntity(task) // 转换为Entity类型
                        val success = taskReminderUtils.setTaskReminder(
                            task = taskEntity,
                            notificationSettings = notificationSettings
                        )
                        
                        if (success) {
                            Timber.d("✅ 任务提醒已设置: ${task.title}")
                        } else {
                            Timber.w("⚠️ 任务提醒设置失败: ${task.title}")
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 设置任务提醒失败，但任务插入成功: ${task.title}")
                    }
                } else {
                    Timber.w("❌ 任务不符合提醒设置条件，跳过提醒设置: ${task.title}")
                }
                
                // 更新缓存
                addToCache(task.id, task)
                invalidateAllCache() // 插入会影响列表查询的结果
                
                Timber.d("任务插入成功: id = ${task.id}")
            }.onFailure {
                Timber.e(it, "插入任务失败: ${task.title}")
            }
        } catch (e: Exception) {
            Timber.e(e, "插入任务异常: ${task.title}")
            throw e
        }
    }
    
    /**
     * 保存任务 - 根据ID决定插入或更新
     */
    override suspend fun saveTask(task: Task) {
        try {
            Timber.d("开始保存任务: ${task.title}, id=${task.id}")
            
            executeSafely {
                // 检查任务是否已存在
                val existingTask = getTaskById(task.id)
                
                if (existingTask != null) {
                    // 任务已存在，执行更新
                    updateTask(task)
                } else {
                    // 任务不存在，执行插入
                    insertTask(task)
                }
            }.onFailure {
                Timber.e(it, "保存任务失败: ${task.title}")
            }
        } catch (e: Exception) {
            Timber.e(e, "保存任务异常: ${task.title}")
            throw e
        }
    }
    
    override suspend fun insertTasks(tasks: List<Task>) {
        try {
            Timber.d("开始批量插入${tasks.size}个任务")
            
            executeSafely {
                val entities = tasks.map { mapModelToEntity(it) }
                taskDao.insertAll(entities)
                
                // 更新缓存
                tasks.forEach { addToCache(it.id, it) }
                invalidateAllCache() // 批量插入会影响列表查询结果
                
                // 在批量插入后，为每个有提醒时间的任务设置提醒
                Log.d(TAG, "开始为批量插入的任务设置提醒...")
                
                // 获取通知设置
                val notificationSettings = try {
                    com.timeflow.app.ui.screen.settings.NotificationSettings()
                } catch (e: Exception) {
                    Log.w(TAG, "无法获取通知设置，使用默认设置", e)
                    com.timeflow.app.ui.screen.settings.NotificationSettings()
                }
                
                tasks.forEach { task ->
                    if (task.reminderTime != null || task.startDate != null || task.dueDate != null) {
                        try {
                            // 转换为Entity类型
                            val taskEntity = mapModelToEntity(task)
                            val success = taskReminderUtils.setTaskReminder(
                                task = taskEntity,
                                notificationSettings = notificationSettings
                            )
                            if (success) {
                                Log.d(TAG, "✅ 批量任务提醒已设置: ${task.title}")
                            } else {
                                Log.w(TAG, "⚠️ 批量任务提醒设置失败: ${task.title}")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ 批量任务提醒设置异常: ${task.title}", e)
                        }
                    }
                }
                
                Timber.d("批量插入任务成功")
            }.onFailure {
                Timber.e(it, "批量插入任务失败")
            }
        } catch (e: Exception) {
            Timber.e(e, "批量插入任务异常")
            throw e
        }
    }
    
    override suspend fun updateTask(task: Task) {
        try {
            Timber.d("开始更新任务: ${task.title}")
            Log.d("TaskRepositoryImpl", "[updateTask] ===== 开始更新任务 =====")
            Log.d("TaskRepositoryImpl", "[updateTask] 任务标题: ${task.title}")
            Log.d("TaskRepositoryImpl", "[updateTask] 任务ID: ${task.id}")
            Log.d("TaskRepositoryImpl", "[updateTask] 任务优先级: ${task.priority}")
            Log.d("TaskRepositoryImpl", "[updateTask] 更新时间: ${task.updatedAt}")
            
            executeSafely {
                val entity = mapModelToEntity(task)
                Log.d("TaskRepositoryImpl", "[updateTask] 映射实体完成")
                Log.d("TaskRepositoryImpl", "[updateTask] 实体优先级: ${entity.priority}")
                
                taskDao.update(entity)
                Log.d("TaskRepositoryImpl", "[updateTask] 数据库更新完成")
                
                // 🔔 新增：更新任务提醒
                try {
                    // 先取消现有提醒
                    taskReminderUtils.cancelTaskReminder(task.id)
                    
                    // 如果任务有提醒时间且未完成，重新设置提醒
                    if ((task.dueDate != null || task.reminderTime != null || task.startDate != null) && !task.isCompleted) {
                        val success = taskReminderUtils.setTaskReminder(entity)
                        if (success) {
                            Log.d("TaskRepositoryImpl", "[updateTask] ✅ 任务提醒已更新: ${task.title}")
                        } else {
                            Log.w("TaskRepositoryImpl", "[updateTask] ⚠️ 任务提醒更新失败: ${task.title}")
                        }
                    } else {
                        Log.d("TaskRepositoryImpl", "[updateTask] 任务无需提醒或已完成，跳过提醒设置")
                    }
                } catch (e: Exception) {
                    Log.e("TaskRepositoryImpl", "[updateTask] ❌ 更新任务提醒失败，但任务更新成功", e)
                }
                
                // 立即验证数据库更新结果
                val updatedEntity = taskDao.getTaskById(task.id)
                if (updatedEntity != null) {
                    Log.d("TaskRepositoryImpl", "[updateTask] 验证数据库更新: 优先级=${updatedEntity.priority}")
                } else {
                    Log.e("TaskRepositoryImpl", "[updateTask] 验证失败: 无法获取更新后的实体")
                }
                
                // 更新缓存
                addToCache(task.id, task)
                invalidateAllCache() // 更新会影响列表查询结果
                
                Timber.d("任务更新成功: id = ${task.id}")
                Log.d("TaskRepositoryImpl", "[updateTask] ===== 任务更新完成 =====")
            }.onFailure {
                Timber.e(it, "更新任务失败: ${task.title}")
                Log.e("TaskRepositoryImpl", "[updateTask] 更新任务失败", it)
            }
        } catch (e: Exception) {
            Timber.e(e, "更新任务异常: ${task.title}")
            Log.e("TaskRepositoryImpl", "[updateTask] 更新任务异常", e)
            throw e
        }
    }
    
    override suspend fun deleteTask(task: Task) {
        try {
            Timber.d("开始删除任务: ${task.title}")
            
            executeSafely {
                // 🔔 新增：删除任务前先取消相关提醒
                try {
                    val success = taskReminderUtils.cancelTaskReminder(task.id)
                    if (success) {
                        Timber.d("✅ 任务提醒已取消: ${task.title}")
                    } else {
                        Timber.w("⚠️ 任务提醒取消失败: ${task.title}")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 取消任务提醒失败，但继续删除任务: ${task.title}")
                }
                
                val entity = mapModelToEntity(task)
                taskDao.delete(entity)
                
                // 更新缓存
                invalidateCache(task.id)
                invalidateAllCache() // 删除会影响列表查询结果
                
                Timber.d("任务删除成功: id = ${task.id}")
            }.onFailure {
                Timber.e(it, "删除任务失败: ${task.title}")
            }
        } catch (e: Exception) {
            Timber.e(e, "删除任务异常: ${task.title}")
            throw e
        }
    }
    
    /**
     * 根据ID删除任务
     */
    override suspend fun deleteTask(taskId: String) {
        try {
            Timber.d("开始删除任务 ID: $taskId")
            
            executeSafely {
                // 🔔 新增：删除任务前先取消相关提醒
                try {
                    val success = taskReminderUtils.cancelTaskReminder(taskId)
                    if (success) {
                        Timber.d("✅ 任务提醒已取消: $taskId")
                    } else {
                        Timber.w("⚠️ 任务提醒取消失败: $taskId")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "❌ 取消任务提醒失败，但继续删除任务: $taskId")
                }
                
                // 首先尝试正常删除
                try {
                    // 获取任务实体
                    val task = taskDao.getTaskById(taskId)
                    if (task != null) {
                        // 尝试正常删除
                        taskDao.delete(task)
                        
                        // 更新缓存
                        invalidateCache(taskId)
                        invalidateAllCache() // 删除会影响列表查询结果
                        
                        Timber.d("任务 $taskId 正常删除成功")
                        return@executeSafely
                    } else {
                        Timber.e("尝试删除不存在的任务: $taskId")
                        return@executeSafely
                    }
                } catch (e: android.database.sqlite.SQLiteConstraintException) {
                    // 捕获外键约束错误，但继续执行
                    Timber.w("正常删除任务失败，将尝试强制删除: $taskId, 错误: ${e.message}")
                }
                
                // 如果正常删除失败，使用强制删除（会禁用外键约束）
                Timber.w("使用强制删除方式删除任务: $taskId")
                taskDao.forceDeleteTask(taskId)
                
                // 更新缓存
                invalidateCache(taskId)
                invalidateAllCache() // 删除会影响列表查询结果
                
                Timber.d("任务 $taskId 强制删除成功")
            }.onFailure {
                Timber.e(it, "删除任务失败: $taskId")
            }
        } catch (e: Exception) {
            Timber.e(e, "删除任务异常: $taskId")
            throw e
        }
    }
    
    /**
     * 预热缓存，加载常用数据
     */
    override suspend fun warmupCache() {
        executeSafely {
            Timber.d("开始预热任务缓存")
            
            // 加载最近任务列表 - 使用getAllTasks代替未定义的getRecentTasks
            val recentTasks = taskDao.getAllTasks()
            val recentTaskModels = recentTasks.map { entity -> mapEntityToModel(entity) }
            
            // 更新缓存
            recentTaskModels.forEach { task -> addToCache(task.id, task) }
            addQueryToCache(createQuerySignature("getAllTasks"), recentTaskModels)
            
            // 取消预取待办任务，因为getTasksByStatus不存在
            // 可以使用未完成任务列表替代
            val pendingTasksFlow = taskDao.getUncompletedTasks().first()
            val pendingTaskModels = pendingTasksFlow.map { entity -> mapEntityToModel(entity) }
            addQueryToCache(createQuerySignature("getUncompletedTasks"), pendingTaskModels)
            
            Timber.d("任务缓存预热完成: 预加载了 ${recentTasks.size} 个最近任务和 ${pendingTaskModels.size} 个待办任务")
        }
    }
    
    override fun getTasksByPriority(): Flow<List<Task>> {
        return taskDao.getTasksByPriority().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override fun getTasksByTags(tags: List<TaskTag>): Flow<List<Task>> {
        val tagNames = tags.map { it.name }
        return taskDao.getTasksByTags(tagNames).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override fun getChildTasks(parentId: String): Flow<List<Task>> {
        return taskDao.getChildTasks(parentId).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override fun getRootTasks(): Flow<List<Task>> {
        return taskDao.getRootTasks().map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override fun getTasksByGroup(groupId: String): Flow<List<Task>> {
        return taskDao.getTasksByGroup(groupId).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override fun getTasksByGroupType(groupType: String): Flow<List<Task>> {
        return taskDao.getTasksByGroupType(groupType).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    override suspend fun updateTaskStatus(taskId: String, newStatus: String) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(status = newStatus)
        updateTask(updatedTask)
    }
    
    override suspend fun updateTaskCompletion(taskId: String, isCompleted: Boolean) {
        try {
            Timber.d("开始更新任务完成状态: taskId=$taskId, isCompleted=$isCompleted")

            val now = LocalDateTime.now()

            // 直接更新任务完成状态
            val result = taskDao.updateTaskCompletionStatus(
                taskId = taskId,
                isCompleted = isCompleted,
                completionTime = now,
                updateTime = now
            )

            Timber.d("任务完成状态更新成功: taskId=$taskId, isCompleted=$isCompleted, 影响行数: $result")

            // 获取更新后的任务，用于通知观察者
            val updatedTaskEntity = taskDao.getTaskById(taskId)
            if (updatedTaskEntity == null) {
                Timber.e("更新后无法找到任务: $taskId")
                Log.e("TaskRepository", "更新后无法找到任务: $taskId")
                throw IllegalStateException("更新后无法找到任务: $taskId")
            }

            // 🔧 关键修复：立即更新缓存中的任务对象
            val updatedTask = mapEntityToModel(updatedTaskEntity)
            addToCache(taskId, updatedTask)
            invalidateAllCache() // 清除所有查询缓存，确保列表刷新
            Log.d("TaskRepository", "✓ 已更新缓存中的任务: ${updatedTask.title}, 完成状态: ${updatedTask.isCompleted}")

            // 🔧 新增：处理循环任务完成逻辑
            if (isCompleted && updatedTask.isRecurring) {
                try {
                    // 通知循环任务管理器处理任务完成
                    // 注意：这里需要通过依赖注入获取RecurringTaskManager
                    // 暂时记录日志，实际实现需要在调用方处理
                    Log.d("TaskRepository", "🔄 循环任务已完成，需要生成下一个实例: ${updatedTask.title}")
                } catch (e: Exception) {
                    Log.e("TaskRepository", "处理循环任务完成失败", e)
                }
            }

            // 如果是父任务，同时更新所有子任务的状态
            if (updatedTaskEntity.hasSubtasks) {
                val subTasks = taskDao.getTasksByParentId(taskId)
                subTasks.forEach { subTask ->
                    val updateResult = taskDao.updateTaskCompletionStatus(
                        taskId = subTask.id,
                        isCompleted = isCompleted,
                        completionTime = now,
                        updateTime = now
                    )
                    Log.d("TaskRepository", "子任务状态更新: ${subTask.id}, 结果: $updateResult")

                    // 🔧 同时更新子任务的缓存
                    val updatedSubTaskEntity = taskDao.getTaskById(subTask.id)
                    if (updatedSubTaskEntity != null) {
                        val updatedSubTask = mapEntityToModel(updatedSubTaskEntity)
                        addToCache(subTask.id, updatedSubTask)
                        Log.d("TaskRepository", "✓ 已更新子任务缓存: ${updatedSubTask.title}, 完成状态: ${updatedSubTask.isCompleted}")
                    }
                }
            }

        } catch (e: Exception) {
            Log.e("TaskRepository", "更新任务完成状态失败: taskId=$taskId", e)
            throw e
        }
    }
    
    /**
     * 更新任务完成状态 - 新接口方法实现
     */
    override suspend fun updateTaskCompletionStatus(taskId: String, isCompleted: Boolean) {
        try {
            Log.d("TaskRepository", "开始更新任务完成状态 [新方法]: taskId=$taskId, isCompleted=$isCompleted")
            
            // 调用现有的完成状态更新方法
            updateTaskCompletion(taskId, isCompleted)
            
            Log.d("TaskRepository", "任务完成状态更新成功 [新方法]: taskId=$taskId, isCompleted=$isCompleted")
        } catch (e: Exception) {
            Log.e("TaskRepository", "更新任务完成状态失败 [新方法]: taskId=$taskId", e)
            throw e
        }
    }
    
    override suspend fun updateTaskPriority(taskId: String, priority: Priority) {
        Log.d("TaskRepositoryImpl", "[updateTaskPriority] ===== 开始更新任务优先级 =====")
        Log.d("TaskRepositoryImpl", "[updateTaskPriority] 任务ID: $taskId")
        Log.d("TaskRepositoryImpl", "[updateTaskPriority] 目标优先级: $priority")
        
        try {
            // 获取当前任务
            val task = getTaskById(taskId)
            if (task == null) {
                Log.e("TaskRepositoryImpl", "[updateTaskPriority] 错误：找不到任务 $taskId")
                return
            }
            
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] 找到任务: ${task.title}")
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] 当前优先级: ${task.priority}")
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] 准备更新为: $priority")
            
            // 创建更新后的任务
            val updatedTask = task.copy(
                priority = priority,
                updatedAt = LocalDateTime.now()
            )
            
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] 更新后任务优先级: ${updatedTask.priority}")
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] 调用updateTask...")
            
            // 更新任务
            updateTask(updatedTask)
            
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] updateTask调用完成")
            
            // 验证更新结果
            val verifyTask = getTaskById(taskId)
            if (verifyTask != null) {
                Log.d("TaskRepositoryImpl", "[updateTaskPriority] 验证结果 - 优先级: ${verifyTask.priority}")
                if (verifyTask.priority == priority) {
                    Log.d("TaskRepositoryImpl", "[updateTaskPriority] ✓ 优先级更新验证成功")
                } else {
                    Log.e("TaskRepositoryImpl", "[updateTaskPriority] ✗ 优先级更新验证失败: 期望=$priority, 实际=${verifyTask.priority}")
                }
            } else {
                Log.e("TaskRepositoryImpl", "[updateTaskPriority] ✗ 验证失败: 无法重新获取任务")
            }
            
            Log.d("TaskRepositoryImpl", "[updateTaskPriority] ===== 任务优先级更新完成 =====")
            
        } catch (e: Exception) {
            Log.e("TaskRepositoryImpl", "[updateTaskPriority] ✗ 更新任务优先级失败", e)
            throw e
        }
    }
    
    override suspend fun updateTaskHasSubtasks(taskId: String, hasSubtasks: Boolean) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(hasSubtasks = hasSubtasks)
        updateTask(updatedTask)
    }
    
    override suspend fun updateTaskOrder(taskId: String, orderPosition: Int) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(orderIndex = orderPosition)
        updateTask(updatedTask)
    }
    
    override suspend fun updateTasksOrder(updates: List<Pair<String, Int>>) {
        updates.forEach { (taskId, order) ->
            updateTaskOrder(taskId, order)
        }
    }
    
    override suspend fun updateTaskParent(taskId: String, parentId: String?, depth: Int) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(
            parentTaskId = parentId,
            depth = depth
        )
        updateTask(updatedTask)
    }
    
    override suspend fun updateTaskProgress(taskId: String, progress: Float) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(progress = progress)
        updateTask(updatedTask)
    }
    
    override fun getAllTags(): Flow<List<TaskTag>> {
        // 由于Room无法直接从JSON列表中提取TaskTag对象，我们需要在这里手动实现
        return taskDao.getAllTagsRaw().map { tagNames ->
            tagNames.map { TaskTag(it, "#808080") } // 使用十六进制颜色字符串
        }
    }
    
    override suspend fun updateTaskGroup(taskId: String, groupId: String?, newOrder: Int) {
        val task = getTaskById(taskId) ?: return
        val updatedTask = task.copy(
            columnId = groupId,
            orderIndex = newOrder
        )
        updateTask(updatedTask)
    }
    
    override suspend fun getMaxOrderInGroup(groupId: String): Int {
        return taskDao.getMaxPositionForColumn(groupId) ?: -1
    }
    
    /**
     * 获取子任务
     */
    override suspend fun getSubTasks(parentTaskId: String): List<Task> {
        return try {
            val startTime = System.currentTimeMillis()
            Log.d("TaskRepository", "====== [${startTime}] 开始获取 $parentTaskId 的子任务 ======")
            
            // 先检查父任务是否存在
            val parentTask = taskDao.getTaskById(parentTaskId)
            val parentCheckTime = System.currentTimeMillis()
            Log.d("TaskRepository", "父任务检查耗时: ${parentCheckTime - startTime}ms")
            
            if (parentTask == null) {
                Log.w("TaskRepository", "警告：尝试获取不存在父任务的子任务: $parentTaskId")
                return emptyList()
            } else {
                Log.d("TaskRepository", "父任务信息: id=${parentTask.id}, 标题=${parentTask.title}, hasSubtasks=${parentTask.hasSubtasks}, aiGenerated=${parentTask.aiGenerated}")
            }
            
            // 强制查询数据库，而不依赖于父任务的hasSubtasks标志
            val queryStartTime = System.currentTimeMillis()
            val subTaskEntities = taskDao.getTasksByParentId(parentTaskId)
            val queryEndTime = System.currentTimeMillis()
            Log.d("TaskRepository", "子任务查询耗时: ${queryEndTime - queryStartTime}ms")
            
            val mappingStartTime = System.currentTimeMillis()
            val subTasks = subTaskEntities.map { entity -> mapEntityToModel(entity) }
            val mappingEndTime = System.currentTimeMillis()
            Log.d("TaskRepository", "子任务映射耗时: ${mappingEndTime - mappingStartTime}ms")
            
            Log.d("TaskRepository", "查询返回: 父任务 $parentTaskId 有 ${subTasks.size} 个子任务")
            
            // ===== 数据一致性检查和自动修复 =====
            val actualHasSubtasks = subTasks.isNotEmpty()
            if (parentTask.hasSubtasks != actualHasSubtasks) {
                Log.w("TaskRepository", "数据不一致: 父任务hasSubtasks=${parentTask.hasSubtasks}, 但实际子任务数量=${subTasks.size}")
                
                try {
                    // 自动修复数据不一致
                    val updateResult = taskDao.updateTaskHasSubtasksFlag(
                        taskId = parentTaskId,
                        hasSubtasks = actualHasSubtasks,
                        updateTime = LocalDateTime.now()
                    )
                    
                    if (updateResult > 0) {
                        Log.i("TaskRepository", "✓ 自动修复数据不一致: 任务 $parentTaskId hasSubtasks 从 ${parentTask.hasSubtasks} 更新为 $actualHasSubtasks")
                    } else {
                        Log.e("TaskRepository", "✗ 自动修复失败: 任务 $parentTaskId 更新返回0行")
                    }
                } catch (fixException: Exception) {
                    Log.e("TaskRepository", "自动修复数据一致性时发生错误: $parentTaskId", fixException)
                }
            }
            
            // 检查是否需要延迟查询（处理并发写入场景）
            if (subTasks.isEmpty() && parentTask.hasSubtasks == true) {
                // 验证数据库查询是否正确
                Log.w("TaskRepository", "父任务 $parentTaskId 标记有子任务(hasSubtasks=true)但未找到任何子任务")
                val doubleCheck = taskDao.getTasksByParentId(parentTaskId)
                Log.w("TaskRepository", "二次验证: 父任务 $parentTaskId 直接查询返回 ${doubleCheck.size} 个子任务")
                
                // 尝试延迟后再查询一次，应对并发写入场景
                Log.d("TaskRepository", "尝试延迟100ms后再次查询")
                kotlinx.coroutines.delay(100)
                val delayedCheck = taskDao.getTasksByParentId(parentTaskId)
                val delayedTasks = delayedCheck.map { entity -> mapEntityToModel(entity) }
                Log.d("TaskRepository", "延迟查询返回: ${delayedTasks.size} 个子任务")
                
                // 如果延迟后查询到了子任务，返回延迟查询结果
                if (delayedTasks.isNotEmpty()) {
                    Log.d("TaskRepository", "使用延迟查询结果: ${delayedTasks.size} 个子任务")
                    val endTime = System.currentTimeMillis()
                    Log.d("TaskRepository", "====== 完成获取 $parentTaskId 的子任务 ${delayedTasks.size}个, 总耗时: ${endTime - startTime}ms ======")
                    return delayedTasks
                } else {
                    // 延迟查询也没有结果，说明确实没有子任务，需要修复hasSubtasks标志
                    Log.w("TaskRepository", "延迟查询确认无子任务，需要修复hasSubtasks标志")
                    try {
                        val correctionResult = taskDao.updateTaskHasSubtasksFlag(
                            taskId = parentTaskId,
                            hasSubtasks = false,
                            updateTime = LocalDateTime.now()
                        )
                        if (correctionResult > 0) {
                            Log.i("TaskRepository", "✓ 已修正任务 $parentTaskId 的hasSubtasks标志为false")
                        }
                    } catch (correctionException: Exception) {
                        Log.e("TaskRepository", "修正hasSubtasks标志时发生错误: $parentTaskId", correctionException)
                    }
                }
            }
            
            // 记录完整时间戳便于调试
            val endTime = System.currentTimeMillis()
            Log.d("TaskRepository", "====== 完成获取 $parentTaskId 的子任务 ${subTasks.size}个, 总耗时: ${endTime - startTime}ms ======")
            
            subTasks
            
        } catch (e: Exception) {
            Log.e("TaskRepository", "获取子任务时发生异常: $parentTaskId", e)
            emptyList()
        }
    }
    
    /**
     * 生成示例数据，只在应用首次启动时执行
     * 添加强制刷新选项，允许在数据异常时重新生成数据
     */
    override suspend fun generateSampleDataIfNeeded(force: Boolean) {
        // 这里需要实现生成示例数据的逻辑
        // 暂时不做任何操作
    }

    /**
     * 获取冲突任务
     * 冲突任务是指截止日期已过但未完成的任务
     * @return 冲突任务列表
     */
    override suspend fun getConflictTasks(): List<Task> {
        // 这里需要实现获取冲突任务的逻辑
        // 暂时返回空列表
        return emptyList()
    }

    /**
     * 观察任务冲突
     * @return 任务冲突流
     */
    override fun observeTaskConflicts(): Flow<List<Task>> {
        // 这里需要实现观察任务冲突的逻辑
        // 暂时返回空流
        return flowOf(emptyList())
    }

    /**
     * 获取默认列ID
     * 用于替代原来的看板列ID
     * @return 默认列ID
     */
    override suspend fun getDefaultColumnId(): String {
        return try {
            // 尝试获取第一个可用的看板列ID
            val columns = taskDao.getAllTasks().firstOrNull()?.let { 
                taskDao.getColumnById("default_column") 
            }
            
            // 如果default_column不存在，尝试获取任何一个存在的看板列
            if (columns == null) {
                // 查询数据库中第一个可用的看板列
                val availableColumn = taskDatabase.kanbanColumnDao().getColumnCount()
                if (availableColumn > 0) {
                    // 获取第一个看板列的ID
                    val firstColumn = taskDatabase.kanbanColumnDao().getAllColumns().firstOrNull()
                    firstColumn?.id ?: createDefaultColumn()
                } else {
                    createDefaultColumn()
                }
            } else {
                "default_column"
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "获取默认列ID失败，创建新的默认列", e)
            createDefaultColumn()
        }
    }
    
    /**
     * 创建默认看板列
     */
    private suspend fun createDefaultColumn(): String {
        return try {
            val defaultColumnId = java.util.UUID.randomUUID().toString()
            
            // 首先创建默认看板（如果不存在）
            val defaultBoardId = createDefaultBoardIfNeeded()
            
            // 创建默认列
            val defaultColumn = com.timeflow.app.data.entity.KanbanColumn(
                id = defaultColumnId,
                boardId = defaultBoardId,
                title = "待办",
                description = "默认任务列",
                position = 0,
                createdAt = java.time.LocalDateTime.now(),
                updatedAt = java.time.LocalDateTime.now()
            )
            
            taskDatabase.kanbanColumnDao().insert(defaultColumn)
            Log.d("TaskRepository", "创建默认看板列成功: $defaultColumnId")
            
            defaultColumnId
        } catch (e: Exception) {
            Log.e("TaskRepository", "创建默认看板列失败", e)
            "fallback_column" // 最后的备用方案
        }
    }
    
    /**
     * 创建默认看板（如果需要）
     */
    private suspend fun createDefaultBoardIfNeeded(): String {
        return try {
            // 检查是否已有看板
            val boardCount = taskDatabase.kanbanBoardDao().getBoardCount()
            if (boardCount > 0) {
                // 获取第一个看板的ID
                val firstBoard = taskDatabase.kanbanBoardDao().getAllBoards().firstOrNull()
                return firstBoard?.id ?: createNewDefaultBoard()
            } else {
                createNewDefaultBoard()
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "检查默认看板失败", e)
            createNewDefaultBoard()
        }
    }
    
    /**
     * 创建新的默认看板
     */
    private suspend fun createNewDefaultBoard(): String {
        val defaultBoardId = java.util.UUID.randomUUID().toString()
        val defaultBoard = com.timeflow.app.data.entity.KanbanBoard(
            id = defaultBoardId,
            title = "默认看板",
            description = "系统自动创建的默认看板",
            createdAt = java.time.LocalDateTime.now(),
            updatedAt = java.time.LocalDateTime.now(),
            position = 0
        )
        
        taskDatabase.kanbanBoardDao().insert(defaultBoard)
        Log.d("TaskRepository", "创建默认看板成功: $defaultBoardId")
        
        return defaultBoardId
    }

    /**
     * 获取最近更新的任务
     */
    override suspend fun getRecentlyUpdatedTasks(): List<Task> {
        val recentTasks = taskDao.getRecentlyUpdatedTasks()
        return recentTasks.map { taskEntity -> mapEntityToModel(taskEntity) }
    }
    
    /**
     * 更新任务截止时间
     */
    override suspend fun updateTaskDueDate(taskId: String, dueDate: LocalDateTime) {
        try {
            Log.d("TaskRepository", "开始更新任务截止时间: taskId=$taskId, dueDate=$dueDate")
            
            // 获取当前任务
            val existingTask = taskDao.getTaskById(taskId)
            
            if (existingTask != null) {
                // 创建更新后的任务，而不是修改现有实例
                val updatedTask = existingTask.copy(
                    dueDate = dueDate,
                    updatedAt = LocalDateTime.now()
                )
                
                // 保存到数据库
                taskDao.update(updatedTask)
                Log.d("TaskRepository", "任务截止时间更新成功: id=$taskId")
            } else {
                Log.e("TaskRepository", "更新任务截止时间失败: 未找到任务 id=$taskId")
                throw IllegalArgumentException("未找到任务: id=$taskId")
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "更新任务截止时间异常: $e", e)
            throw e
        }
    }

    /**
     * 在事务中添加多个子任务
     * @param parentTaskId 父任务ID
     * @param subtasks 子任务列表
     */
    override suspend fun addSubtasksInTransaction(parentTaskId: String, subtasks: List<Task>) {
        // 首先检查父任务是否存在
        val parentTask = taskDao.getTaskById(parentTaskId)
        if (parentTask == null) {
            Log.w("TaskRepository", "Cannot add subtasks: Parent task $parentTaskId does not exist")
            throw IllegalArgumentException("Parent task does not exist")
        }
        
        // 使用事务添加所有子任务
        taskDatabase.withTransaction {
            subtasks.forEach { task ->
                // 确保每个子任务都引用了正确的父任务ID
                val subtask = task.copy(parentTaskId = parentTaskId)
                
                // 将业务模型转换为数据库实体再插入
                val subtaskEntity = mapModelToEntity(subtask)
                taskDao.insert(subtaskEntity)
                Log.d("TaskRepository", "Added subtask ${task.id} to parent $parentTaskId")
            }
        }
    }

    /**
     * 获取引用指定任务作为父任务的所有任务
     */
    override suspend fun getTasksReferencingParent(parentId: String): List<Task> {
        try {
            Log.d("TaskRepository", "获取引用父任务ID为 $parentId 的所有任务")
            val entities = taskDao.getTasksByParentId(parentId)
            val tasks = entities.map { mapEntityToModel(it) }
            Log.d("TaskRepository", "获取到 ${tasks.size} 个引用该父任务的任务")
            return tasks
        } catch (e: Exception) {
            Log.e("TaskRepository", "获取引用父任务的任务时出错: $parentId", e)
            return emptyList() // 出错时返回空列表而不是抛出异常
        }
    }
    
    /**
     * 强制清理任务的所有相关引用
     */
    override suspend fun forceCleanupTaskReferences(taskId: String) {
        try {
            Log.d("TaskRepository", "开始强制清理任务引用: $taskId")
            
            // 在事务中执行所有操作，确保原子性
            taskDatabase.withTransaction {
                // 1. 将所有引用此任务作为父任务的任务的parentTaskId设为null
                taskDao.clearParentTaskReferences(taskId)
                
                // 2. 删除任务关闭表中的相关记录
                taskDao.deleteTaskClosures(taskId)
                
                // 注意：以下操作因数据库结构不匹配而移除
                // - 删除任务标签关联
                // - 清理任务评论关联 
                // - 清理任务附件关联
                
                Log.d("TaskRepository", "成功清理任务所有引用: $taskId")
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "强制清理任务引用失败: $taskId", e)
            throw e
        }
    }
    
    /**
     * 更新任务颜色
     * 由于Task实体没有直接的颜色字段，我们通过更新任务的属性或使用自定义元数据来存储颜色信息
     */
    override suspend fun updateTaskColor(taskId: String, color: Long) {
        try {
            Log.d("TaskRepository", "开始更新任务颜色: taskId=$taskId, color=${color.toString(16)}")
            
            // 获取当前任务
            val existingTask = taskDao.getTaskById(taskId)
            
            if (existingTask != null) {
                // 构建颜色信息JSON字符串
                val colorInfo = """{"color":${color}}"""
                
                // 构建更新后的任务，将颜色信息添加/更新到描述中
                val updatedDescription = if (existingTask.description.contains("\"color\":")) {
                    // 如果已有颜色信息，替换它
                    existingTask.description.replace(Regex("\\{\"color\":\\d+\\}"), colorInfo)
                } else {
                    // 否则添加颜色信息
                    if (existingTask.description.isEmpty()) colorInfo
                    else "${existingTask.description} $colorInfo"
                }
                
                val updatedTask = existingTask.copy(
                    description = updatedDescription,
                    updatedAt = LocalDateTime.now()
                )
                
                // 更新任务
                taskDao.update(updatedTask)
                Log.d("TaskRepository", "任务颜色更新成功: id=$taskId, color=${color.toString(16)}")
            } else {
                Log.e("TaskRepository", "更新任务颜色失败: 未找到任务 id=$taskId")
                throw IllegalArgumentException("未找到任务: id=$taskId")
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "更新任务颜色异常: ${e.message}", e)
            throw e
        }
    }

    /**
     * 清理被污染的标签数据
     * 修复name字段包含TaskTag对象的问题
     */
    suspend fun cleanupCorruptedTagData() {
        try {
            android.util.Log.d("DataCleanup", "开始清理被污染的标签数据")
            
            // 获取所有任务
            val allTasks = taskDao.getAllTasks()
            var cleanedCount = 0
            
            for (task in allTasks) {
                if (task.tagIds.isNotEmpty()) {
                    // 检查是否有被污染的标签数据
                    val cleanedTagIds = task.tagIds.mapNotNull { tagId: String ->
                        when {
                            // 如果tagId包含"TaskTag("说明被污染了
                            tagId.contains("TaskTag(") -> {
                                android.util.Log.d("DataCleanup", "发现被污染的标签: $tagId")
                                null // 移除被污染的标签
                            }
                            // 如果是正常的字符串，保留
                            tagId.isNotBlank() && !tagId.contains("(") -> {
                                tagId
                            }
                            else -> null
                        }
                    }
                    
                    // 如果清理后的标签列表与原来不同，更新数据库
                    if (cleanedTagIds != task.tagIds) {
                        android.util.Log.d("DataCleanup", "清理任务 ${task.id} 的标签: ${task.tagIds} -> $cleanedTagIds")
                        
                        val cleanedTask = task.copy(tagIds = cleanedTagIds)
                        taskDao.update(cleanedTask)
                        cleanedCount++
                    }
                }
            }
            
            android.util.Log.d("DataCleanup", "数据清理完成，共清理了 $cleanedCount 个任务的标签数据")
            
        } catch (e: Exception) {
            android.util.Log.e("DataCleanup", "清理数据失败: ${e.message}", e)
        }
    }
    
    /**
     * 自动顺延过期的未完成任务到今天
     * 只会顺延那些日期未被用户手动修改过的任务
     */
    override suspend fun autoRescheduleOverdueTasks(): Int {
        return try {
            Log.d("TaskRepository", "===== 开始自动顺延过期任务 =====")
            
            val today = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59)
            val startOfToday = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)
            
            // 查询所有过期且未完成的任务（日期在今天之前，且未被用户手动修改）
            val overdueTasks = taskDao.getAllTasks().filter { task ->
                task.dueDate != null && 
                task.dueDate.isBefore(startOfToday) && 
                task.status != "已完成" && 
                !task.dateManuallyModified
            }
            
            Log.d("TaskRepository", "找到 ${overdueTasks.size} 个需要顺延的过期任务")
            
            if (overdueTasks.isEmpty()) {
                return 0
            }
            
            var rescheduledCount = 0
            
            // 在事务中批量更新任务
            taskDatabase.withTransaction {
                overdueTasks.forEach { task ->
                    try {
                        // 将任务顺延到今天（保持原来的时间）
                        val originalTime = task.dueDate!!.toLocalTime()
                        val newDueDate = LocalDateTime.of(LocalDateTime.now().toLocalDate(), originalTime)
                        
                        val updatedTask = task.copy(
                            dueDate = newDueDate,
                            updatedAt = LocalDateTime.now()
                            // 注意：不修改dateManuallyModified字段，保持为false，表示这是系统自动调整
                        )
                        
                        taskDao.update(updatedTask)
                        rescheduledCount++
                        
                        Log.d("TaskRepository", "顺延任务: ${task.title} 从 ${task.dueDate} 到 $newDueDate")
                        
                    } catch (e: Exception) {
                        Log.e("TaskRepository", "顺延任务失败: ${task.id} - ${task.title}", e)
                    }
                }
            }
            
            Log.d("TaskRepository", "===== 自动顺延完成，共顺延 $rescheduledCount 个任务 =====")
            rescheduledCount
            
        } catch (e: Exception) {
            Log.e("TaskRepository", "自动顺延过期任务失败", e)
            0
        }
    }
    
    /**
     * 更新任务日期手动修改标记
     */
    override suspend fun updateTaskDateManuallyModified(taskId: String, isManuallyModified: Boolean) {
        try {
            Log.d("TaskRepository", "更新任务日期手动修改标记: taskId=$taskId, isManuallyModified=$isManuallyModified")
            
            val existingTask = taskDao.getTaskById(taskId)
            if (existingTask != null) {
                val updatedTask = existingTask.copy(
                    dateManuallyModified = isManuallyModified,
                    updatedAt = LocalDateTime.now()
                )
                
                taskDao.update(updatedTask)
                Log.d("TaskRepository", "任务日期手动修改标记更新成功: taskId=$taskId")
            } else {
                Log.e("TaskRepository", "更新任务日期手动修改标记失败: 未找到任务 taskId=$taskId")
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "更新任务日期手动修改标记异常: taskId=$taskId", e)
            throw e
        }
    }
    
    /**
     * 更新任务的日期时间，并标记为手动修改
     */
    override suspend fun updateTaskDateTime(taskId: String, startDate: LocalDateTime?, dueDate: LocalDateTime?) {
        executeSafely {
            val existingTask = taskDao.getTaskById(taskId)
            if (existingTask != null) {
                    val updatedTask = existingTask.copy(
                        startDate = startDate,
                        dueDate = dueDate,
                    dateManuallyModified = true,
                    updatedAt = LocalDateTime.now()
                )
                taskDao.update(updatedTask)
            }
        }.onFailure { 
            Timber.e(it, "更新任务时间失败: $taskId")
        }
    }
    
    // 🆕 浮动任务相关方法实现
    override suspend fun getFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task> {
        return executeSafely {
            val entities = taskDao.getFloatingTasksInWeek(weekStart, weekEnd)
            entities.map { mapEntityToModel(it) }
        }.getOrElse { 
            Timber.e(it, "获取周浮动任务失败")
            emptyList() 
        }
    }
    
    override fun observeFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): Flow<List<Task>> {
        return taskDao.observeFloatingTasksInWeek(weekStart, weekEnd)
            .map { entities -> entities.map { mapEntityToModel(it) } }
            .catch { throwable ->
                Timber.e(throwable, "观察周浮动任务失败")
                emit(emptyList())
            }
    }
    
    override suspend fun getUnscheduledFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task> {
        return executeSafely {
            val entities = taskDao.getUnscheduledFloatingTasksInWeek(weekStart, weekEnd)
            entities.map { mapEntityToModel(it) }
        }.getOrElse { 
            Timber.e(it, "获取未安排浮动任务失败")
            emptyList() 
        }
    }
    
    override suspend fun scheduleFloatingTask(taskId: String, scheduledDate: LocalDateTime) {
        executeSafely {
            Log.d("TaskRepository", "=== 安排浮动任务到具体日期 ===")
            Log.d("TaskRepository", "任务ID: $taskId")
            Log.d("TaskRepository", "安排日期: $scheduledDate")
            
            val existingTask = taskDao.getTaskById(taskId)
            if (existingTask != null && existingTask.isFloatingTask) {
                Log.d("TaskRepository", "找到浮动任务: ${existingTask.title}")
                
                // 🎯 将浮动任务转换为具体任务
                val updatedTask = existingTask.copy(
                    isFloatingTask = false, // 不再是浮动任务
                    startDate = scheduledDate.withHour(9).withMinute(0), // 默认9:00开始
                    dueDate = scheduledDate.withHour(18).withMinute(0), // 默认18:00结束
                    floatingWeekStart = null, // 🔧 清除浮动周期信息，避免事件创建时使用旧数据
                    floatingWeekEnd = null,   // 🔧 清除浮动周期信息
                    dateManuallyModified = true, // 标记为用户手动安排
                        updatedAt = LocalDateTime.now()
                    )
                    
                    taskDao.update(updatedTask)
                Log.d("TaskRepository", "✓ 浮动任务已转换为具体任务")
                Log.d("TaskRepository", "新的时间安排: ${updatedTask.startDate} - ${updatedTask.dueDate}")
            } else {
                Log.w("TaskRepository", "未找到浮动任务或任务不是浮动类型: $taskId")
            }
        }.onFailure { 
            Timber.e(it, "安排浮动任务失败: $taskId")
        }
    }
    
    override suspend fun unscheduleFloatingTask(taskId: String) {
        executeSafely {
            Log.d("TaskRepository", "=== 取消浮动任务的具体安排 ===")
            Log.d("TaskRepository", "任务ID: $taskId")
            
            val existingTask = taskDao.getTaskById(taskId)
            if (existingTask != null && !existingTask.isFloatingTask && existingTask.floatingWeekStart != null) {
                Log.d("TaskRepository", "找到已安排的浮动任务: ${existingTask.title}")
                
                // 🎯 将具体任务重新转换为浮动任务
                val updatedTask = existingTask.copy(
                    isFloatingTask = true, // 重新变为浮动任务
                    startDate = existingTask.floatingWeekStart?.toLocalDate()?.atStartOfDay(), // 恢复为周开始
                    dueDate = existingTask.floatingWeekEnd?.toLocalDate()?.atTime(23, 59), // 恢复为周结束
                    dateManuallyModified = false, // 重置为系统设置
                    updatedAt = LocalDateTime.now()
                )
                
                taskDao.update(updatedTask)
                Log.d("TaskRepository", "✓ 具体任务已重新转换为浮动任务")
                Log.d("TaskRepository", "恢复的周范围: ${updatedTask.startDate} - ${updatedTask.dueDate}")
            } else {
                Log.w("TaskRepository", "未找到已安排的浮动任务: $taskId")
            }
        }.onFailure { 
            Timber.e(it, "取消安排浮动任务失败: $taskId")
        }
    }
    
    override suspend fun updateFloatingTaskOrder(taskId: String, newOrder: Int) {
        executeSafely {
            taskDao.updateFloatingTaskOrder(taskId, newOrder, LocalDateTime.now())
        }.onFailure { 
            Timber.e(it, "更新浮动任务顺序失败: $taskId")
        }
    }
    
    override suspend fun createFloatingTask(
        title: String,
        description: String,
        priority: Priority?,
        weekStart: LocalDateTime,
        weekEnd: LocalDateTime,
        estimatedMinutes: Int
    ): Task {
        return executeSafely {
            Log.d("TaskRepository", "=== 开始创建浮动任务 ===")
            Log.d("TaskRepository", "任务标题: $title")
            Log.d("TaskRepository", "任务描述: $description")
            Log.d("TaskRepository", "优先级: $priority")
            Log.d("TaskRepository", "周范围: $weekStart 到 $weekEnd")
            Log.d("TaskRepository", "预估时间: ${estimatedMinutes}分钟")
            
            // 获取默认列ID
            val defaultColumnId = getDefaultColumnId()
            Log.d("TaskRepository", "默认列ID: $defaultColumnId")
            
            val task = Task(
                id = UUID.randomUUID().toString(),
                title = title,
                description = description,
                priority = priority ?: Priority.MEDIUM,
                type = TaskType.FLOATING,
                columnId = defaultColumnId,
                orderIndex = 0,
                status = "待办",
                parentTaskId = null,
                depth = 0,
                hasSubtasks = false,
                progress = 0f,
                tags = emptyList(),
                goalId = null,
                isFloatingTask = true,
                // 🎯 浮动任务存储策略：start_date=周一，end_date=周日，不设置具体时间
                startDate = weekStart.toLocalDate().atStartOfDay(), // 周一 00:00
                dueDate = weekEnd.toLocalDate().atTime(23, 59), // 周日 23:59
                floatingWeekStart = weekStart,
                floatingWeekEnd = weekEnd,
                estimatedTimeMinutes = estimatedMinutes,
                dateManuallyModified = false, // 浮动任务初始时为系统设置的时间
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            
            Log.d("TaskRepository", "生成的任务模型: taskId=${task.id}")
            Log.d("TaskRepository", "isFloatingTask=${task.isFloatingTask}")
            Log.d("TaskRepository", "floatingWeekStart=${task.floatingWeekStart}")
            Log.d("TaskRepository", "floatingWeekEnd=${task.floatingWeekEnd}")
            
            // 转换为实体并保存
            val entity = mapModelToEntity(task)
            Log.d("TaskRepository", "转换后的实体: entityId=${entity.id}")
            Log.d("TaskRepository", "实体isFloatingTask=${entity.isFloatingTask}")
            Log.d("TaskRepository", "实体floatingWeekStart=${entity.floatingWeekStart}")
            Log.d("TaskRepository", "实体floatingWeekEnd=${entity.floatingWeekEnd}")
            
            taskDao.insert(entity)
            Log.d("TaskRepository", "✓ 浮动任务实体已插入数据库")
            
            // 验证保存结果
            val savedEntity = taskDao.getTaskById(entity.id)
            if (savedEntity != null) {
                Log.d("TaskRepository", "✓ 数据库验证成功: 任务已保存")
                Log.d("TaskRepository", "保存的任务: title=${savedEntity.title}")
                Log.d("TaskRepository", "保存的任务: isFloatingTask=${savedEntity.isFloatingTask}")
            } else {
                Log.e("TaskRepository", "❌ 数据库验证失败: 任务未保存")
            }
            
            Log.d("TaskRepository", "✓ 浮动任务创建成功: ${task.id}")
            task
        }.getOrThrow()
    }
    
    /**
     * 根据目标ID获取关联的任务
     */
    override suspend fun getTasksByGoalId(goalId: String): List<Task> {
        return executeSafely {
            Log.d("TaskRepository", "=== 获取目标关联任务 ===")
            Log.d("TaskRepository", "目标ID: $goalId")
            
            val entities = taskDao.getTasksByGoalId(goalId)
            val tasks = entities.map { mapEntityToModel(it) }
            
            Log.d("TaskRepository", "找到 ${tasks.size} 个关联任务")
            tasks.forEachIndexed { index, task ->
                Log.d("TaskRepository", "任务${index + 1}: ${task.title} (${task.id})")
            }
            
            tasks
        }.getOrElse { 
            Log.e("TaskRepository", "获取目标关联任务失败: $goalId", it)
            emptyList() 
        }
    }
    
    /**
     * 观察指定目标ID的任务变化
     */
    override fun observeTasksByGoalId(goalId: String): Flow<List<Task>> {
        return taskDao.observeTasksByGoalId(goalId)
            .map { entities -> entities.map { mapEntityToModel(it) } }
            .catch { throwable ->
                Log.e("TaskRepository", "观察目标关联任务失败: $goalId", throwable)
                emit(emptyList())
            }
    }

    // TODO: 暂时注释掉，等接口问题解决后再启用
    /*
    override suspend fun getTaskStatistics(startDate: LocalDate, endDate: LocalDate): TaskStatistics {
        return executeSafely {
            val allTasks = taskDao.getAllTasks()
            val tasksInRange = allTasks.filter { task ->
                val taskDate = task.createdAt.toLocalDate()
                !taskDate.isBefore(startDate) && !taskDate.isAfter(endDate)
            }

            val completedTasks = tasksInRange.filter { it.status == "已完成" }
            val pendingTasks = tasksInRange.filter { it.status != "已完成" && !isTaskOverdue(mapEntityToModel(it)) }
            val overdueTasks = tasksInRange.filter { it.status != "已完成" && isTaskOverdue(mapEntityToModel(it)) }

            val completionRate = if (tasksInRange.isNotEmpty()) {
                completedTasks.size.toFloat() / tasksInRange.size * 100
            } else 0f

            // 计算平均完成时间
            val avgCompletionTime = if (completedTasks.isNotEmpty()) {
                completedTasks.mapNotNull { task ->
                    task.completedAt?.let { completedAt ->
                        java.time.Duration.between(task.createdAt, completedAt).toMillis()
                    }
                }.average().toLong()
            } else 0L

            // 找出最高效的小时
            val mostProductiveHour = completedTasks.mapNotNull { it.completedAt?.hour }
                .groupingBy { it }.eachCount().maxByOrNull { it.value }?.key

            // 按分类统计
            val tasksByCategory = tasksInRange.groupBy { categorizeTaskByName(it.title) }
                .mapValues { it.value.size }

            // 按优先级统计
            val tasksByPriority = tasksInRange.groupBy { it.priority.toString() }
                .mapValues { it.value.size }

            TaskStatistics(
                totalTasks = tasksInRange.size,
                completedTasks = completedTasks.size,
                pendingTasks = pendingTasks.size,
                overdueTasks = overdueTasks.size,
                completionRate = completionRate,
                averageCompletionTime = avgCompletionTime,
                mostProductiveHour = mostProductiveHour,
                tasksByCategory = tasksByCategory,
                tasksByPriority = tasksByPriority
            )
        }.getOrElse {
            Log.e("TaskRepository", "获取任务统计失败", it)
            TaskStatistics(0, 0, 0, 0, 0f, 0L, null, emptyMap(), emptyMap())
        }
    }
    */

    /**
     * 判断任务是否过期
     */
    private fun isTaskOverdue(task: Task): Boolean {
        return task.dueDate?.let { dueDate ->
            dueDate.isBefore(LocalDateTime.now()) && !task.isCompleted
        } ?: false
    }

    /**
     * 根据任务名称分类
     */
    private fun categorizeTaskByName(taskName: String): String {
        return when {
            taskName.contains("工作", ignoreCase = true) ||
            taskName.contains("项目", ignoreCase = true) ||
            taskName.contains("会议", ignoreCase = true) -> "工作"
            taskName.contains("学习", ignoreCase = true) ||
            taskName.contains("阅读", ignoreCase = true) ||
            taskName.contains("课程", ignoreCase = true) -> "学习"
            taskName.contains("运动", ignoreCase = true) ||
            taskName.contains("健身", ignoreCase = true) ||
            taskName.contains("锻炼", ignoreCase = true) -> "健康"
            taskName.contains("娱乐", ignoreCase = true) ||
            taskName.contains("游戏", ignoreCase = true) ||
            taskName.contains("电影", ignoreCase = true) -> "娱乐"
            else -> "其他"
        }
    }

    // 🔧 循环任务相关方法实现

    /**
     * 获取所有循环任务
     */
    override suspend fun getRecurringTasks(): List<Task> {
        return try {
            val entities = taskDao.getAllTasks().filter { it.isRecurring }
            entities.map { mapEntityToModel(it) }
        } catch (e: Exception) {
            Log.e("TaskRepository", "获取循环任务失败", e)
            emptyList()
        }
    }

    /**
     * 根据日期范围获取任务
     */
    override suspend fun getTasksByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Task> {
        return try {
            val entities = taskDao.getTasksByDateRange(startDate, endDate)
            entities.map { mapEntityToModel(it) }
        } catch (e: Exception) {
            Log.e("TaskRepository", "根据日期范围获取任务失败", e)
            emptyList()
        }
    }

    /**
     * 获取指定循环任务的所有实例
     */
    override suspend fun getRecurringTaskInstances(originalTaskId: String): List<Task> {
        return try {
            // 获取原始任务
            val originalTask = taskDao.getTaskById(originalTaskId)
                ?: return emptyList()

            // 查找相同标题的循环任务
            val entities = taskDao.getAllTasks().filter { entity ->
                entity.title == originalTask.title &&
                entity.isRecurring &&
                entity.id != originalTaskId
            }

            entities.map { mapEntityToModel(it) }
        } catch (e: Exception) {
            Log.e("TaskRepository", "获取循环任务实例失败", e)
            emptyList()
        }
    }

    /**
     * 更新循环任务设置
     */
    override suspend fun updateRecurringSettings(taskId: String, isRecurring: Boolean, recurringPattern: String?) {
        try {
            val existingTask = taskDao.getTaskById(taskId)
            if (existingTask != null) {
                val updatedTask = existingTask.copy(
                    isRecurring = isRecurring,
                    recurringPattern = recurringPattern,
                    updatedAt = LocalDateTime.now()
                )
                taskDao.update(updatedTask)
                Log.d("TaskRepository", "循环任务设置更新成功: id=$taskId")
            } else {
                Log.e("TaskRepository", "更新循环任务设置失败: 未找到任务 id=$taskId")
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "更新循环任务设置异常", e)
            throw e
        }
    }

    /**
     * 检查是否存在相同标题的循环任务实例
     */
    override suspend fun hasRecurringTaskInstance(title: String, dateTime: LocalDateTime): Boolean {
        return try {
            val startOfDay = dateTime.toLocalDate().atStartOfDay()
            val endOfDay = dateTime.toLocalDate().atTime(23, 59, 59)

            val entities = taskDao.getTasksByDateRange(startOfDay, endOfDay)
            entities.any { entity ->
                entity.title == title && entity.isRecurring
            }
        } catch (e: Exception) {
            Log.e("TaskRepository", "检查循环任务实例失败", e)
            false
        }
    }
}