package com.timeflow.app.data.repository

import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.data.model.ReminderType
import com.timeflow.app.data.model.TemplateCategory
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.time.LocalTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好数据仓库接口
 */
interface UserPreferenceRepository {
    // 获取用户偏好设置
    fun getUserPreferences(): Flow<UserPreferences>
    
    // 同步获取用户偏好设置
    fun getUserPreferencesSync(): UserPreferences
    
    // 记录目标创建
    suspend fun recordGoalCreation(goalId: String)
    
    // 记录模板选择
    suspend fun recordTemplateSelection(templateId: String)
    
    // 更新用户偏好设置
    suspend fun updateUserPreferences(preferences: UserPreferences)
    
    // 保存字符串偏好设置
    suspend fun savePreference(key: String, value: String)
    
    // 获取字符串偏好设置
    suspend fun getStringPreference(key: String, defaultValue: String): String
}

/**
 * 用户偏好数据类
 * 存储用户的各种偏好设置和历史数据
 */
data class UserPreferences(
    // 用户ID
    val userId: String = "",
    
    // 偏好的模板类别
    val preferredCategories: List<TemplateCategory> = emptyList(),
    
    // 各类别的完成率统计
    val categoryCompletionRates: Map<TemplateCategory, Double> = emptyMap(),
    
    // 模板评分
    val templateRatings: Map<String, Double> = emptyMap(),
    
    // 常用标签
    val frequentTags: List<String> = emptyList(),
    
    // 默认提醒设置
    val defaultReminderSettings: List<ReminderSetting> = emptyList(),
    
    // 历史统计数据
    val completedGoalsCount: Int = 0,
    val createdGoalsCount: Int = 0,
    val averageCompletionRate: Double = 0.0,
    
    // 用户界面偏好
    val prefersCompactUI: Boolean = false,
    val prefersVoiceInput: Boolean = false,
    val prefersDarkMode: Boolean = false,
    
    // AI功能设置
    val enableAIBreakdown: Boolean = true,
    val enableAIAnalysis: Boolean = true,
    val aiInteractionHistory: List<String> = emptyList()
)

/**
 * 用户偏好数据仓库实现类
 */
@Singleton
class UserPreferenceRepositoryImpl @Inject constructor() : UserPreferenceRepository {
    // 用户偏好状态流
    private val _userPreferences = MutableStateFlow(
        UserPreferences(
            userId = "demo_user",
            preferredCategories = listOf(TemplateCategory.STUDY, TemplateCategory.WORK),
            frequentTags = listOf("重要", "学习", "工作", "健康"),
            defaultReminderSettings = listOf(
                ReminderSetting(
                    id = "default_reminder",
                    type = ReminderType.DAILY,
                    time = LocalTime.of(20, 0),
                    message = "记得检查今日目标完成情况"
                )
            )
        )
    )
    
    // 单独的用户偏好键值存储
    private val _preferences = MutableStateFlow<Map<String, String>>(mapOf())
    
    override fun getUserPreferences(): Flow<UserPreferences> {
        return _userPreferences.asStateFlow()
    }
    
    override fun getUserPreferencesSync(): UserPreferences {
        return _userPreferences.value
    }
    
    override suspend fun recordGoalCreation(goalId: String) {
        val currentPrefs = _userPreferences.value
        _userPreferences.value = currentPrefs.copy(
            createdGoalsCount = currentPrefs.createdGoalsCount + 1
        )
    }
    
    override suspend fun recordTemplateSelection(templateId: String) {
        // 实际实现中，这里会更新模板使用统计和用户偏好
        // 为简化演示，此处保持现有实现
    }
    
    override suspend fun updateUserPreferences(preferences: UserPreferences) {
        _userPreferences.value = preferences
    }
    
    override suspend fun savePreference(key: String, value: String) {
        val currentPrefs = _preferences.value.toMutableMap()
        currentPrefs[key] = value
        _preferences.value = currentPrefs
    }
    
    override suspend fun getStringPreference(key: String, defaultValue: String): String {
        return _preferences.value[key] ?: defaultValue
    }
} 