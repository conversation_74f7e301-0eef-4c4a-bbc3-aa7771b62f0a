package com.timeflow.app.ui.screen.goal

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.ai.GoalCategoryClassifier
import com.timeflow.app.ui.components.goal.SmartCategoryRecommendation

/**
 * 智能分类测试和演示页面
 * 用于展示和测试智能分类功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartCategoryTestScreen(
    navController: NavController
) {
    var testTitle by remember { mutableStateOf("") }
    var testDescription by remember { mutableStateOf("") }
    var selectedCategoryId by remember { mutableStateOf("personal_development") }
    
    val classifier = remember { GoalCategoryClassifier() }
    
    // 预设测试用例
    val testCases = remember {
        listOf(
            "每天跑步30分钟" to "提高身体素质，减重5公斤",
            "学习Python编程" to "掌握基础语法，完成一个小项目",
            "读完10本书" to "提升知识面，培养阅读习惯",
            "存钱买房" to "每月存款5000元，攒够首付",
            "学会做饭" to "掌握基本烹饪技能，自己做健康餐",
            "提升工作技能" to "学习新的项目管理方法，争取升职",
            "改善人际关系" to "多参加社交活动，结交新朋友",
            "培养兴趣爱好" to "学习摄影，记录生活美好瞬间",
            "陪伴家人" to "每周至少陪父母吃一次饭",
            "提升自信心" to "克服社交恐惧，勇敢表达自己"
        )
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "智能分类测试",
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        },
        containerColor = Color(0xFFF8F9FA)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 功能介绍
            item {
                IntroductionCard()
            }
            
            // 自定义测试
            item {
                CustomTestCard(
                    title = testTitle,
                    description = testDescription,
                    selectedCategoryId = selectedCategoryId,
                    onTitleChange = { testTitle = it },
                    onDescriptionChange = { testDescription = it },
                    onCategorySelected = { selectedCategoryId = it }
                )
            }
            
            // 预设测试用例
            item {
                Text(
                    text = "预设测试用例",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F2937),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            items(testCases) { (title, description) ->
                TestCaseCard(
                    title = title,
                    description = description,
                    classifier = classifier
                )
            }
        }
    }
}

/**
 * 功能介绍卡片
 */
@Composable
private fun IntroductionCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFEFF6FF)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = null,
                    tint = Color(0xFF3B82F6),
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = "智能分类功能",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F2937)
                )
            }
            
            Text(
                text = "基于先进的文本分析算法，自动识别目标内容并推荐最合适的分类。支持关键词匹配、语义分析和时间框架识别。",
                fontSize = 14.sp,
                color = Color(0xFF374151),
                lineHeight = 20.sp
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.padding(top = 8.dp)
            ) {
                FeatureItem(
                    icon = Icons.Default.Search,
                    text = "关键词匹配",
                    color = Color(0xFF10B981)
                )
                
                FeatureItem(
                    icon = Icons.Default.AutoAwesome,
                    text = "语义分析",
                    color = Color(0xFF8B5CF6)
                )
                
                FeatureItem(
                    icon = Icons.Default.Schedule,
                    text = "时间识别",
                    color = Color(0xFFF59E0B)
                )
            }
        }
    }
}

/**
 * 功能特性项
 */
@Composable
private fun FeatureItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        
        Text(
            text = text,
            fontSize = 12.sp,
            color = Color(0xFF6B7280)
        )
    }
}

/**
 * 自定义测试卡片
 */
@Composable
private fun CustomTestCard(
    title: String,
    description: String,
    selectedCategoryId: String,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onCategorySelected: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "自定义测试",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F2937)
            )
            
            // 输入框
            OutlinedTextField(
                value = title,
                onValueChange = onTitleChange,
                label = { Text("目标标题") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("输入您的目标标题...") }
            )
            
            OutlinedTextField(
                value = description,
                onValueChange = onDescriptionChange,
                label = { Text("目标描述") },
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("详细描述您的目标...") },
                maxLines = 3
            )
            
            // 智能推荐
            if (title.isNotBlank() || description.isNotBlank()) {
                SmartCategoryRecommendation(
                    title = title,
                    description = description,
                    selectedCategoryId = selectedCategoryId,
                    onCategorySelected = onCategorySelected
                )
            }
        }
    }
}

/**
 * 测试用例卡片
 */
@Composable
private fun TestCaseCard(
    title: String,
    description: String,
    classifier: GoalCategoryClassifier
) {
    var selectedCategoryId by remember { mutableStateOf("personal_development") }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 测试用例信息
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937)
                )
                
                Text(
                    text = description,
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280)
                )
            }
            
            // 智能推荐
            SmartCategoryRecommendation(
                title = title,
                description = description,
                selectedCategoryId = selectedCategoryId,
                onCategorySelected = { selectedCategoryId = it }
            )
        }
    }
}
