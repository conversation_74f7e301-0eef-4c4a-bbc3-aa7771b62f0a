package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.DatabaseBackupManager
import com.timeflow.app.utils.SystemBarManager
import android.app.Activity
import com.timeflow.app.viewmodel.BackupSettingsViewModel

/**
 * 数据备份设置页面
 * 支持手动备份和自动备份设置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BackupSettingsScreen(
    navController: NavController,
    viewModel: BackupSettingsViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 获取备份状态
    val backupSettings by viewModel.backupSettings.collectAsState()
    val isBackingUp by viewModel.isBackingUp.collectAsState()
    val backupInfo by viewModel.backupInfo.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it, false)
        }
    }
    
    // 显示错误消息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            // 这里可以显示Snackbar或Toast
            // 暂时只是清除错误
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部标题和返回按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { navController.navigateUp() },
                modifier = Modifier
                    .size(36.dp)
                    .background(Color.LightGray.copy(alpha = 0.2f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Text(
                text = "数据备份设置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Box(modifier = Modifier.size(36.dp))
        }
        
        // 滚动内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // 备份状态卡片
            BackupStatusCard(
                backupInfo = backupInfo,
                onManualBackup = { viewModel.manualBackup() },
                isBackingUp = isBackingUp
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 备份列表卡片
            BackupListCard(
                backupFiles = viewModel.backupFiles.collectAsState().value,
                isRestoring = viewModel.isRestoring.collectAsState().value,
                onDeleteBackup = { fileName -> viewModel.deleteBackup(fileName) },
                onRestoreBackup = { fileName -> viewModel.restoreBackup(fileName) },
                onRestoreToPreviousVersion = { viewModel.restoreToPreviousVersion() }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 自动备份设置
            AutoBackupSettingsCard(
                isAutoBackupEnabled = backupSettings.isAutoBackupEnabled,
                autoBackupFrequency = backupSettings.autoBackupFrequency,
                onToggleAutoBackup = { enabled -> viewModel.toggleAutoBackup(enabled) },
                onChangeFrequency = { frequency -> viewModel.updateAutoBackupFrequency(frequency) }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 备份保留设置
            BackupRetentionCard(
                maxBackupCount = backupSettings.maxBackupCount,
                onChangeMaxBackupCount = { count -> viewModel.updateMaxBackupCount(count) }
            )
            
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

/**
 * 备份状态卡片
 */
@Composable
private fun BackupStatusCard(
    backupInfo: BackupInfo,
    onManualBackup: () -> Unit,
    isBackingUp: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "备份状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "备份数量",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray
                    )
                    Text(
                        text = "${backupInfo.backupCount} 个备份",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                Column {
                    Text(
                        text = "总大小",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray
                    )
                    Text(
                        text = "${formatFileSize(backupInfo.totalSizeBytes)}",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (backupInfo.lastBackupTime > 0) {
                Text(
                    text = "上次备份时间",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
                Text(
                    text = formatDate(backupInfo.lastBackupTime),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 手动备份按钮
            Button(
                onClick = onManualBackup,
                enabled = !isBackingUp,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender
                )
            ) {
                if (isBackingUp) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("备份中...")
                } else {
                    Icon(
                        imageVector = Icons.Default.Backup,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("立即备份")
                }
            }
        }
    }
}

/**
 * 自动备份设置卡片
 */
@Composable
private fun AutoBackupSettingsCard(
    isAutoBackupEnabled: Boolean,
    autoBackupFrequency: AutoBackupFrequency,
    onToggleAutoBackup: (Boolean) -> Unit,
    onChangeFrequency: (AutoBackupFrequency) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "自动备份",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = if (isAutoBackupEnabled) "已启用" else "已禁用",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isAutoBackupEnabled) DustyLavender else Color.Gray
                    )
                }
                
                Switch(
                    checked = isAutoBackupEnabled,
                    onCheckedChange = { enabled -> onToggleAutoBackup(enabled) },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = DustyLavender,
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color.Gray
                    )
                )
            }
            
            if (isAutoBackupEnabled) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "备份频率",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 备份频率选择
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FrequencyChip(
                        text = "每天",
                        selected = autoBackupFrequency == AutoBackupFrequency.DAILY,
                        onClick = { onChangeFrequency(AutoBackupFrequency.DAILY) }
                    )
                    
                    FrequencyChip(
                        text = "每周",
                        selected = autoBackupFrequency == AutoBackupFrequency.WEEKLY,
                        onClick = { onChangeFrequency(AutoBackupFrequency.WEEKLY) }
                    )
                    
                    FrequencyChip(
                        text = "每月",
                        selected = autoBackupFrequency == AutoBackupFrequency.MONTHLY,
                        onClick = { onChangeFrequency(AutoBackupFrequency.MONTHLY) }
                    )
                }
            }
        }
    }
}

/**
 * 备份保留设置卡片
 */
@Composable
private fun BackupRetentionCard(
    maxBackupCount: Int,
    onChangeMaxBackupCount: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "备份保留设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "最大保留备份数量",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 最大备份数量选择
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CountChip(
                    text = "3个",
                    selected = maxBackupCount == 3,
                    onClick = { onChangeMaxBackupCount(3) }
                )
                
                CountChip(
                    text = "5个",
                    selected = maxBackupCount == 5,
                    onClick = { onChangeMaxBackupCount(5) }
                )
                
                CountChip(
                    text = "10个",
                    selected = maxBackupCount == 10,
                    onClick = { onChangeMaxBackupCount(10) }
                )
                
                CountChip(
                    text = "20个",
                    selected = maxBackupCount == 20,
                    onClick = { onChangeMaxBackupCount(20) }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "注意：增加保留数量会占用更多存储空间",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}

@Composable
private fun FrequencyChip(
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    SuggestionChip(
        onClick = onClick,
        label = { Text(text) },
        colors = SuggestionChipDefaults.suggestionChipColors(
            containerColor = if (selected) DustyLavender.copy(alpha = 0.2f) else Color.LightGray.copy(alpha = 0.3f),
            labelColor = if (selected) DustyLavender else Color.DarkGray
        ),
        border = if (selected) androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = DustyLavender
        ) else null
    )
}

@Composable
private fun CountChip(
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    SuggestionChip(
        onClick = onClick,
        label = { Text(text) },
        colors = SuggestionChipDefaults.suggestionChipColors(
            containerColor = if (selected) DustyLavender.copy(alpha = 0.2f) else Color.LightGray.copy(alpha = 0.3f),
            labelColor = if (selected) DustyLavender else Color.DarkGray
        ),
        border = if (selected) androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = DustyLavender
        ) else null
    )
}

/**
 * 备份列表卡片
 */
@Composable
private fun BackupListCard(
    backupFiles: List<DatabaseBackupManager.BackupFileInfo>,
    isRestoring: Boolean,
    onDeleteBackup: (String) -> Unit,
    onRestoreBackup: (String) -> Unit,
    onRestoreToPreviousVersion: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "备份列表",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 恢复到上一版本按钮
                Button(
                    onClick = onRestoreToPreviousVersion,
                    enabled = !isRestoring && backupFiles.size > 1,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = DustyLavender
                    ),
                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                    modifier = Modifier.height(36.dp)
                ) {
                    if (isRestoring) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(14.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                    Text(
                        text = "使用上一版本",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (backupFiles.isEmpty()) {
                Text(
                    text = "暂无备份文件",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            } else {
                // 备份文件列表
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    backupFiles.forEach { backupFile ->
                        BackupFileItem(
                            backupFile = backupFile,
                            isRestoring = isRestoring,
                            onDelete = { onDeleteBackup(backupFile.fileName) },
                            onRestore = { onRestoreBackup(backupFile.fileName) }
                        )
                        Divider(
                            color = Color.LightGray.copy(alpha = 0.5f),
                            thickness = 0.5.dp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 备份文件项
 */
@Composable
private fun BackupFileItem(
    backupFile: DatabaseBackupManager.BackupFileInfo,
    isRestoring: Boolean,
    onDelete: () -> Unit,
    onRestore: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 备份文件信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = "版本 ${backupFile.version}",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            Text(
                text = formatDate(backupFile.timestamp),
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
            Text(
                text = formatFileSize(backupFile.sizeBytes),
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
        
        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 恢复按钮
            IconButton(
                onClick = onRestore,
                enabled = !isRestoring,
                modifier = Modifier
                    .size(36.dp)
                    .background(DustyLavender.copy(alpha = 0.1f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.Restore,
                    contentDescription = "恢复",
                    tint = DustyLavender,
                    modifier = Modifier.size(18.dp)
                )
            }
            
            // 删除按钮
            IconButton(
                onClick = onDelete,
                enabled = !isRestoring,
                modifier = Modifier
                    .size(36.dp)
                    .background(Color.Red.copy(alpha = 0.1f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = Color.Red,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

// 辅助函数：格式化文件大小
private fun formatFileSize(sizeBytes: Long): String {
    return when {
        sizeBytes < 1024 -> "$sizeBytes B"
        sizeBytes < 1024 * 1024 -> String.format("%.2f KB", sizeBytes / 1024.0)
        sizeBytes < 1024 * 1024 * 1024 -> String.format("%.2f MB", sizeBytes / (1024.0 * 1024.0))
        else -> String.format("%.2f GB", sizeBytes / (1024.0 * 1024.0 * 1024.0))
    }
}

// 辅助函数：格式化日期
private fun formatDate(timestamp: Long): String {
    val date = java.util.Date(timestamp)
    val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
    return formatter.format(date)
}

// 备份信息数据类
data class BackupInfo(
    val backupCount: Int = 0,
    val totalSizeBytes: Long = 0L,
    val lastBackupTime: Long = 0L
)

// 自动备份频率枚举
enum class AutoBackupFrequency {
    DAILY, WEEKLY, MONTHLY
}