@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class, androidx.compose.foundation.ExperimentalFoundationApi::class)

package com.timeflow.app.ui.screen.task

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.screen.task.model.FeedbackData
import com.timeflow.app.ui.screen.task.model.EMOTION_OPTIONS
import com.timeflow.app.ui.screen.task.model.FEEDBACK_SUGGESTIONS
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PhotoCamera
import androidx.compose.material.icons.filled.Delete
import androidx.compose.ui.layout.ContentScale
import coil.compose.AsyncImage
import android.net.Uri
import android.content.Context
import androidx.compose.ui.platform.LocalContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.geometry.Offset

/**
 * 任务完成反馈对话框
 * 
 * @param onDismiss 关闭对话框的回调
 * @param onSubmit 提交反馈数据的回调
 * @param taskTitle 任务标题，用于显示在对话框中
 */
@Composable
fun TaskFeedbackDialog(
    onDismiss: () -> Unit,
    onSubmit: (FeedbackData) -> Unit,
    taskTitle: String
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 状态
    var selectedEmoji by remember { mutableStateOf<String?>(null) }
    var comment by remember { mutableStateOf("") }
    var activeSuggestionTag by remember { mutableStateOf<String?>(null) }
    var showSuccessAnimation by remember { mutableStateOf(false) }
    
    // 图片相关状态
    var selectedImages by remember { mutableStateOf<List<Uri>>(emptyList()) }
    var isUploadingImage by remember { mutableStateOf(false) }
    
    // 动画
    val successScale = remember { Animatable(0f) }
    
    // 键盘控制器
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { 
            selectedImages = selectedImages + it
        }
    }
    
    // 建议列表
    val suggestions = remember(comment) {
        // 分析文本中的标签
        val words = comment.split(Regex("\\s+"))
        val lastWord = words.lastOrNull() ?: ""
        val tagMatch = Regex("^#(\\w+)").find(lastWord)
        
        if (tagMatch != null) {
            val tag = "#" + tagMatch.groupValues[1]
            activeSuggestionTag = tag
            FEEDBACK_SUGGESTIONS[tag] ?: emptyList()
        } else {
            activeSuggestionTag = null
            emptyList()
        }
    }
    
    // 显示成功动画
    LaunchedEffect(showSuccessAnimation) {
        if (showSuccessAnimation) {
            successScale.animateTo(
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
            delay(1000)
            onDismiss()
        }
    }
    
    // 保存图片到内部存储
    suspend fun saveImageToInternalStorage(uri: Uri): String? {
        return try {
            withContext(Dispatchers.IO) {
                val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
                inputStream?.use { input ->
                    val fileName = "feedback_${UUID.randomUUID()}.jpg"
                    val file = File(context.filesDir, "feedback_images")
                    if (!file.exists()) {
                        file.mkdirs()
                    }
                    val targetFile = File(file, fileName)
                    
                    FileOutputStream(targetFile).use { output ->
                        input.copyTo(output)
                    }
                    targetFile.absolutePath
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("TaskFeedbackDialog", "保存图片失败", e)
            null
        }
    }
    
    // 提交反馈
    val handleSubmit: () -> Unit = {
        android.util.Log.d("TaskFeedbackDialog", "===== 用户提交反馈 =====")
        android.util.Log.d("TaskFeedbackDialog", "选择的心情: $selectedEmoji")
        android.util.Log.d("TaskFeedbackDialog", "输入的感想: $comment")
        android.util.Log.d("TaskFeedbackDialog", "选择的图片数量: ${selectedImages.size}")
        android.util.Log.d("TaskFeedbackDialog", "感想是否为空: ${comment.isBlank()}")
        
        if (selectedEmoji != null || comment.isNotBlank() || selectedImages.isNotEmpty()) {
            android.util.Log.d("TaskFeedbackDialog", "用户有输入，开始提交反馈")
            
            // 保存图片到内部存储
            isUploadingImage = true
            coroutineScope.launch {
                try {
                    val savedImagePaths = selectedImages.mapNotNull { uri ->
                        saveImageToInternalStorage(uri)
                    }
                    
                    // 创建反馈数据
                    val feedback = FeedbackData(
                        emotion = selectedEmoji,
                        comment = comment.takeIf { it.isNotBlank() },
                        imagePaths = savedImagePaths
                    )
            
                    android.util.Log.d("TaskFeedbackDialog", "创建反馈数据完成:")
                    android.util.Log.d("TaskFeedbackDialog", "  - emotion: ${feedback.emotion}")
                    android.util.Log.d("TaskFeedbackDialog", "  - comment: ${feedback.comment}")
                    android.util.Log.d("TaskFeedbackDialog", "  - imagePaths: ${feedback.imagePaths}")
                    android.util.Log.d("TaskFeedbackDialog", "  - timestamp: ${feedback.timestamp}")
                    
                    // 🔧 关键修复：立即提交数据，不等待动画
                    android.util.Log.d("TaskFeedbackDialog", "立即调用onSubmit回调...")
                    onSubmit(feedback)
                    android.util.Log.d("TaskFeedbackDialog", "✓ 反馈提交完成")
                    
                    isUploadingImage = false
                    
                    // 然后显示成功动画
                    showSuccessAnimation = true
                    successScale.animateTo(
                        targetValue = 1f,
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    )
                    
                    // 动画结束后关闭对话框
                    delay(800)
                    onDismiss()
                } catch (e: Exception) {
                    android.util.Log.e("TaskFeedbackDialog", "保存图片失败", e)
                    isUploadingImage = false
                    onDismiss()
                }
            }
        } else {
            android.util.Log.d("TaskFeedbackDialog", "用户没有输入任何内容，直接关闭对话框")
            // 如果没有选择表情或输入评论，直接关闭
            onDismiss()
        }
    }
    
    // 插入建议
    val insertSuggestion: (String) -> Unit = { suggestion ->
        if (activeSuggestionTag != null) {
            // 替换标签为建议内容
            val newComment = comment.replace(
                Regex("${activeSuggestionTag}\\w*$"),
                "$activeSuggestionTag $suggestion "
            )
            comment = newComment
        }
    }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.4f))
                .clickable { onDismiss() },
            contentAlignment = Alignment.Center
        ) {
            // 成功动画
            AnimatedVisibility(
                visible = showSuccessAnimation,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .clip(CircleShape)
                        .background(Color.White)
                        .scale(successScale.value),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "成功",
                        tint = DustyLavender,
                        modifier = Modifier.size(60.dp)
                    )
                }
            }
            
            // 主对话框内容
            AnimatedVisibility(
                visible = !showSuccessAnimation,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Card(
                    modifier = Modifier
                        .width(320.dp)
                        .wrapContentHeight()
                        .clickable { /* 防止点击事件穿透 */ },
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 8.dp
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 标题
                        Text(
                            text = "任务已完成",
                            style = MaterialTheme.typography.titleMedium,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = TextPrimary
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        // 任务名称
                        Text(
                            text = taskTitle,
                            style = MaterialTheme.typography.bodyMedium,
                            fontSize = 14.sp,
                            color = TextSecondary,
                            maxLines = 1
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 表情选择器
                        Text(
                            text = "你现在的心情是？",
                            style = MaterialTheme.typography.bodyMedium,
                            fontSize = 14.sp,
                            color = TextPrimary,
                            modifier = Modifier.align(Alignment.Start)
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        LazyRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            items(EMOTION_OPTIONS) { emoji ->
                                EmojiButton(
                                    emoji = emoji,
                                    isSelected = emoji == selectedEmoji,
                                    onClick = { selectedEmoji = emoji }
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 智能输入框
                        Text(
                            text = "记录一下感想（可选）",
                            style = MaterialTheme.typography.bodyMedium,
                            fontSize = 14.sp,
                            color = TextPrimary,
                            modifier = Modifier.align(Alignment.Start)
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 自定义输入框
                        SmartTextInput(
                            value = comment,
                            onValueChange = { comment = it },
                            onDone = handleSubmit,
                            focusRequester = focusRequester,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(90.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 图片选择和显示区域
                        ImageSelectionArea(
                            selectedImages = selectedImages,
                            isUploading = isUploadingImage,
                            onAddImage = { imagePickerLauncher.launch("image/*") },
                            onRemoveImage = { imageToRemove ->
                                selectedImages = selectedImages.filter { it != imageToRemove }
                            }
                        )
                        
                        // 智能建议
                        AnimatedVisibility(visible = suggestions.isNotEmpty()) {
                            Column(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                LazyRow(
                                    modifier = Modifier.fillMaxWidth(),
                                    contentPadding = PaddingValues(horizontal = 4.dp),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    items(suggestions) { suggestion ->
                                        SuggestionChip(
                                            suggestion = suggestion,
                                            onClick = { insertSuggestion(suggestion) }
                                        )
                                    }
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 操作按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            OutlinedButton(
                                onClick = onDismiss,
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = TextSecondary
                                )
                            ) {
                                Text("跳过")
                            }
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                            Button(
                                onClick = handleSubmit,
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = DustyLavender
                                ),
                                enabled = (selectedEmoji != null || comment.isNotBlank() || selectedImages.isNotEmpty()) && !isUploadingImage
                            ) {
                                if (isUploadingImage) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(16.dp),
                                            color = Color.White,
                                            strokeWidth = 2.dp
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("上传中...", color = Color.White)
                                    }
                                } else {
                                    Text(
                                        text = when {
                                            selectedImages.isNotEmpty() && selectedEmoji != null -> "保存记录"
                                            selectedImages.isNotEmpty() -> "保存图片"
                                            selectedEmoji != null -> "保存心情"
                                            else -> "仅记录文字"
                                        },
                                        color = Color.White
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 表情按钮组件
 */
@Composable
private fun EmojiButton(
    emoji: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "emojiScale"
    )
    
    Box(
        modifier = Modifier
            .size(42.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(
                if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent
            )
            .border(
                width = if (isSelected) 2.dp else 0.dp,
                color = if (isSelected) DustyLavender else Color.Transparent,
                shape = CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = emoji,
            fontSize = 24.sp
        )
    }
}

/**
 * 建议标签组件
 */
@Composable
private fun SuggestionChip(
    suggestion: String,
    onClick: () -> Unit
) {
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = LavenderAsh.copy(alpha = 0.15f),
        modifier = Modifier
            .padding(vertical = 4.dp)
            .clickable { onClick() }
    ) {
        Text(
            text = suggestion,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            fontSize = 12.sp,
            color = TextPrimary
        )
    }
}

/**
 * 智能文本输入组件
 */
@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun SmartTextInput(
    value: String,
    onValueChange: (String) -> Unit,
    onDone: () -> Unit,
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(LavenderAsh.copy(alpha = 0.1f))
            .padding(12.dp)
    ) {
        BasicTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .fillMaxSize()
                .focusRequester(focusRequester),
            textStyle = TextStyle(
                fontSize = 14.sp,
                color = TextPrimary
            ),
            cursorBrush = SolidColor(DustyLavender),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    keyboardController?.hide()
                    onDone()
                }
            ),
            maxLines = 5
        )
        
        if (value.isEmpty()) {
            Text(
                text = "输入感想或使用 #标签 获取智能建议...",
                fontSize = 14.sp,
                color = TextSecondary.copy(alpha = 0.6f)
            )
        }
    }
}

/**
 * 图片选择和显示区域
 */
@Composable
private fun ImageSelectionArea(
    selectedImages: List<Uri>,
    isUploading: Boolean,
    onAddImage: () -> Unit,
    onRemoveImage: (Uri) -> Unit
) {
    Column {
        // 标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "添加图片（可选）",
                style = MaterialTheme.typography.bodyMedium,
                fontSize = 14.sp,
                color = TextPrimary
            )
            
            if (selectedImages.size < 3) { // 最多选择3张图片
                Surface(
                    modifier = Modifier
                        .size(32.dp)
                        .clickable { onAddImage() },
                    shape = CircleShape,
                    color = DustyLavender.copy(alpha = 0.1f),
                    border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.3f))
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加图片",
                            tint = DustyLavender,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 图片显示区域
        if (selectedImages.isNotEmpty()) {
            LazyRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(selectedImages) { imageUri ->
                    ImagePreviewCard(
                        imageUri = imageUri,
                        onRemove = { onRemoveImage(imageUri) }
                    )
                }
            }
        } else {
            // 空状态
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .clickable { onAddImage() },
                shape = RoundedCornerShape(12.dp),
                color = LavenderAsh.copy(alpha = 0.05f),
                border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.2f))
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.PhotoCamera,
                        contentDescription = "添加图片",
                        tint = TextSecondary.copy(alpha = 0.5f),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "点击添加图片",
                        fontSize = 12.sp,
                        color = TextSecondary.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

/**
 * 图片预览卡片
 */
@Composable
private fun ImagePreviewCard(
    imageUri: Uri,
    onRemove: () -> Unit
) {
    Box(
        modifier = Modifier.size(80.dp)
    ) {
        // 图片
        AsyncImage(
            model = imageUri,
            contentDescription = "预览图片",
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(8.dp))
                .background(LavenderAsh.copy(alpha = 0.1f)),
            contentScale = ContentScale.Crop
        )
        
        // 删除按钮
        Surface(
            modifier = Modifier
                .size(20.dp)
                .align(Alignment.TopEnd)
                .offset(x = 6.dp, y = (-6).dp)
                .clickable { onRemove() },
            shape = CircleShape,
            color = Color.Red,
            shadowElevation = 2.dp
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除图片",
                    tint = Color.White,
                    modifier = Modifier.size(12.dp)
                )
            }
        }
    }
} 