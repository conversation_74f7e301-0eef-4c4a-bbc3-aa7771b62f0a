# 自动任务重新调度功能验证指南

## 功能概述

自动任务重新调度功能会在每次刷新任务列表时，自动将过期的未完成任务移动到今天，但只有在用户没有手动修改过任务日期的情况下才会执行。

## 实现的功能

### 1. 数据库变更
- 添加了 `dateManuallyModified` 字段到Task表
- 数据库版本升级到14
- 实现了MIGRATION_13_14迁移

### 2. 自动重新调度逻辑
- 在 `TaskListViewModel.refreshTasks()` 中集成
- 只重新调度过期且未被手动修改的任务
- 保持原始时间，只修改日期

### 3. 手动修改标记
- 新建任务时选择日期自动标记为手动修改
- 在TaskDetailBottomSheet中修改时间自动标记为手动修改
- 子任务有设置日期时也会标记为手动修改

## 验证步骤

### 步骤1：准备测试数据

1. **启动应用** - 确保数据库升级成功
2. **创建测试任务**：
   - 任务A：标题"过期任务A"，设置昨天的日期（会被重新调度）
   - 任务B：标题"过期任务B"，设置前天的日期（会被重新调度）
   - 任务C：标题"手动修改任务"，设置昨天的日期，然后手动修改一次（不会被重新调度）

### 步骤2：验证自动重新调度

1. **触发重新调度**：
   - 进入任务列表页面
   - 下拉刷新或重新进入页面
   - 观察日志输出：
     ```
     D/TaskRepository: ===== 开始自动顺延过期任务 =====
     D/TaskRepository: 找到 X 个需要顺延的过期任务
     D/TaskRepository: 顺延任务: [任务标题] 从 [旧日期] 到 [新日期]
     D/TaskRepository: ===== 自动顺延完成，共顺延 X 个任务 =====
     ```

2. **验证结果**：
   - 过期任务A和B应该被移动到今天
   - 手动修改任务C应该保持原来的日期不变
   - 时间部分应该保持不变，只有日期部分更新到今天

### 步骤3：验证手动修改标记

1. **新建任务测试**：
   - 创建新任务时选择明天的日期
   - 检查该任务的 `dateManuallyModified` 字段应该为 `true`

2. **编辑任务测试**：
   - 在TaskDetailBottomSheet中修改任务时间
   - 检查该任务的 `dateManuallyModified` 字段应该被设置为 `true`

3. **自动创建任务测试**：
   - 通过AI助手或其他自动方式创建的任务（不手动设置日期）
   - 检查该任务的 `dateManuallyModified` 字段应该为 `false`

### 步骤4：边界情况测试

1. **已完成任务**：
   - 创建过期的已完成任务
   - 验证已完成任务不会被重新调度

2. **无日期任务**：
   - 创建没有设置日期的任务
   - 验证这些任务不会被重新调度

3. **今天的任务**：
   - 创建今天日期的任务
   - 验证这些任务不会被重新调度

## 预期日志输出

### 成功的自动重新调度
```
D/TaskListViewModel: ===== 开始自动顺延过期任务 =====
D/TaskRepository: ===== 开始自动顺延过期任务 =====
D/TaskRepository: 找到 2 个需要顺延的过期任务
D/TaskRepository: 顺延任务: 过期任务A 从 2024-01-10T10:00 到 2024-01-11T10:00
D/TaskRepository: 顺延任务: 过期任务B 从 2024-01-09T14:30 到 2024-01-11T14:30
D/TaskRepository: ===== 自动顺延完成，共顺延 2 个任务 =====
D/TaskListViewModel: ✓ 自动顺延了 2 个过期任务
```

### 没有需要重新调度的任务
```
D/TaskRepository: ===== 开始自动顺延过期任务 =====
D/TaskRepository: 找到 0 个需要顺延的过期任务
D/TaskRepository: ===== 自动顺延完成，共顺延 0 个任务 =====
D/TaskListViewModel: 没有需要顺延的过期任务
```

### 手动修改日期标记
```
D/TaskDetailBottomSheet: 标记任务为手动修改日期: taskId=abc123
D/TaskDetailViewModel: 标记任务日期为手动修改: abc123
D/TaskDetailViewModel: ✓ 任务日期标记为手动修改成功
```

## 验证要点

### ✅ 应该发生的行为
- 过期且未手动修改的任务被移动到今天
- 保持原始时间，只修改日期
- 用户手动设置日期时标记为手动修改
- 手动修改过的任务不被自动重新调度

### ❌ 不应该发生的行为
- 已完成的任务被重新调度
- 用户手动修改过的任务被重新调度
- 任务的时间部分被修改
- 没有日期的任务被重新调度

## 数据库验证

可以通过以下SQL查询验证数据：

```sql
-- 查看所有任务的日期和手动修改标记
SELECT id, title, dueDate, dateManuallyModified, status 
FROM Task 
ORDER BY dueDate;

-- 查看过期的未完成任务
SELECT id, title, dueDate, dateManuallyModified 
FROM Task 
WHERE dueDate < datetime('now', 'start of day') 
AND status != '已完成';

-- 查看被标记为手动修改的任务
SELECT id, title, dueDate, dateManuallyModified 
FROM Task 
WHERE dateManuallyModified = 1;
```

## 故障排除

### 如果自动重新调度不工作：
1. 检查数据库迁移是否成功执行
2. 检查TaskListViewModel.refreshTasks()是否被调用
3. 检查日志输出中的错误信息
4. 验证任务的状态和dateManuallyModified字段

### 如果手动修改标记不工作：
1. 检查TaskDetailBottomSheet中的标记逻辑
2. 检查AddTaskScreen中新任务的标记逻辑
3. 验证TaskRepository.updateTaskDateManuallyModified方法

## 成功标准

- ✅ 过期未手动修改的任务能够自动重新调度到今天
- ✅ 手动修改过的任务不会被自动重新调度
- ✅ 新建任务时选择日期能正确标记为手动修改
- ✅ 在详情页修改时间能正确标记为手动修改
- ✅ 已完成任务和无日期任务不受影响
- ✅ 时间部分保持不变，只修改日期部分 