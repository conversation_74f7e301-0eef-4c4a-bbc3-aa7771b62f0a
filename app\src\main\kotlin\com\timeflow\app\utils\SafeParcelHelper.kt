package com.timeflow.app.utils

import android.content.Intent
import android.os.Bundle
import android.os.Parcel
import android.os.Parcelable
import android.util.Log
import androidx.core.os.bundleOf
import java.io.Serializable
import java.util.concurrent.ConcurrentHashMap
import android.os.Build
import androidx.annotation.RequiresApi
import java.lang.reflect.Field
import java.lang.reflect.Method

/**
 * 安全的Parcel辅助工具
 * 用于解决日志中频繁出现的 "Reading a NULL string not supported here" 错误
 * 以及其他Parcel/Binder相关异常
 */
object SafeParcelHelper {
    const val TAG = "SafeParcelHelper"
    
    // 跟踪错误发生率，用于优化处理
    val errorCountMap = ConcurrentHashMap<String, Int>()
    
    // 辅助标志：记录Parcel处理状态
    private var isInitialized = false
    private var parcelErrorCount = 0
    private const val MAX_ERROR_COUNT = 10
    
    // 安全读取相关变量
    private val safeStringCache = ConcurrentHashMap<String, String>()
    private var isSafeModeEnabled = false
    
    // 紧急重启标志
    private const val EXTRA_EMERGENCY_RESTART = "emergency_restart"
    
    /**
     * 初始化SafeParcelHelper
     */
    fun initialize() {
        if (isInitialized) return
        
        try {
            // 设置默认错误处理程序
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                setDefaultParcelErrorHandler()
            }
            
            isInitialized = true
            Log.d(TAG, "SafeParcelHelper已初始化")
        } catch (e: Exception) {
            Log.e(TAG, "初始化SafeParcelHelper时出错: ${e.message}")
        }
    }
    
    /**
     * 设置默认Parcel错误处理程序
     * 使用反射设置全局默认Parcel错误处理逻辑
     */
    @RequiresApi(Build.VERSION_CODES.P)
    fun setDefaultParcelErrorHandler() {
        try {
            // 尝试通过反射设置Parcel默认错误处理
            val parcelClass = Parcel::class.java
            
            try {
                // 尝试获取setDefaultReturnException方法
                val method = parcelClass.getDeclaredMethod("setDefaultReturnException", Exception::class.java)
                method.isAccessible = true
                
                // 设置一个温和的异常，避免应用崩溃
                val safeException = RuntimeException("Safe Parcel error: NULL value detected")
                method.invoke(null, safeException)
                
                Log.d(TAG, "已设置默认Parcel错误处理程序")
            } catch (e: NoSuchMethodException) {
                // 如果方法不存在，尝试其他方法
                try {
                    // 尝试设置FLAG_IS_REPLY_FROM_BLOCKING_ALLOWED属性
                    val field = parcelClass.getDeclaredField("FLAG_IS_REPLY_FROM_BLOCKING_ALLOWED")
                    field.isAccessible = true
                    field.set(null, 0x20) // 设置标志位允许特殊处理
                    
                    Log.d(TAG, "已设置Parcel标志位")
                } catch (e2: Exception) {
                    Log.e(TAG, "设置Parcel属性失败: ${e2.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置默认Parcel错误处理程序失败: ${e.message}")
        }
    }
    
    /**
     * 安全的Intent参数检查和处理
     * 防止Intent中的NULL值导致应用崩溃
     */
    fun ensureSafeIntent(intent: Intent?): Intent {
        if (intent == null) return Intent()
        
        try {
            // 1. 获取原始extras
            val extras = intent.extras
            
            // 2. 如果extras为空，直接返回原始intent
            if (extras == null) return intent
            
            // 3. 创建新的安全Bundle
            val safeExtras = ensureSafeBundle(extras)
            
            // 4. 清除原始extras并重新设置安全版本
            intent.replaceExtras(safeExtras)
            
            return intent
        } catch (e: Exception) {
            Log.e(TAG, "确保安全Intent时出错: ${e.message}")
            return Intent() // 返回空Intent作为回退方案
        }
    }
    
    /**
     * 确保Bundle中的值都是安全的
     * 移除可能导致Parcel错误的NULL字符串和无效值
     */
    fun ensureSafeBundle(bundle: Bundle?): Bundle {
        if (bundle == null) return Bundle()
        
        try {
            // 创建新的Bundle进行安全复制
            val safeBundle = Bundle()
            
            // 遍历原始Bundle中的所有键
            for (key in bundle.keySet()) {
                try {
                    val value = bundle.get(key)
                    
                    // 特殊处理可能导致问题的值类型
                    when (value) {
                        null -> {
                            // 跳过NULL值，不复制
                            continue
                        }
                        is String -> {
                            // 确保字符串不为null
                            safeBundle.putString(key, value)
                        }
                        is Bundle -> {
                            // 递归处理嵌套Bundle
                            safeBundle.putBundle(key, ensureSafeBundle(value))
                        }
                        is Array<*> -> {
                            // 跳过可能包含null的数组
                            if ((value as Array<*>).any { it == null }) {
                                continue
                            }
                            // 其他数组正常处理
                            bundle.get(key)?.let { safeBundle.putAll(Bundle().apply { putValue(key, it) }) }
                        }
                        else -> {
                            // 其他类型的值直接复制
                            bundle.get(key)?.let { safeBundle.putAll(Bundle().apply { putValue(key, it) }) }
                        }
                    }
                } catch (e: Exception) {
                    // 如果处理某个键时出错，跳过该键并继续
                    Log.w(TAG, "处理Bundle键 '$key' 时出错: ${e.message}")
                }
            }
            
            return safeBundle
        } catch (e: Exception) {
            Log.e(TAG, "确保安全Bundle时出错: ${e.message}")
            return Bundle() // 返回空Bundle作为回退方案
        }
    }
    
    /**
     * 辅助方法：在Bundle中设置值的通用方法
     */
    private fun Bundle.putValue(key: String, value: Any) {
        when (value) {
            is Boolean -> putBoolean(key, value)
            is Int -> putInt(key, value)
            is Long -> putLong(key, value)
            is Float -> putFloat(key, value)
            is Double -> putDouble(key, value)
            is String -> putString(key, value)
            is CharSequence -> putCharSequence(key, value)
            is Bundle -> putBundle(key, value)
            // 其他类型暂不处理
        }
    }
    
    /**
     * 检查Intent是否为紧急重启Intent
     */
    fun isEmergencyRestartIntent(intent: Intent?): Boolean {
        if (intent == null) return false
        return intent.getBooleanExtra(EXTRA_EMERGENCY_RESTART, false)
    }
    
    /**
     * 创建紧急重启Intent
     */
    fun createEmergencyRestartIntent(context: android.content.Context): Intent {
        val packageManager = context.packageManager
        val intent = packageManager.getLaunchIntentForPackage(context.packageName) ?: Intent()
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        intent.putExtra(EXTRA_EMERGENCY_RESTART, true)
        return intent
    }
    
    /**
     * 安全读取Parcel中的字符串，防止NULL字符串错误
     */
    fun safeReadString(parcel: Parcel): String? {
        return try {
            // 检查即将读取的位置是否有有效数据
            checkParcelPosition(parcel)
            
            // 读取字符串，可能返回null
            parcel.readString()
        } catch (e: Exception) {
            parcelErrorCount++
            Log.e(TAG, "安全读取Parcel字符串时出错: ${e.message}")
            
            // 返回空字符串而不是null
            ""
        }
    }
    
    /**
     * 检查Parcel位置，确保不会读取到无效数据
     */
    private fun checkParcelPosition(parcel: Parcel): Boolean {
        try {
            val dataPosition = parcel.dataPosition()
            val dataSize = parcel.dataSize()
            
            // 如果当前位置已经到达或超过数据大小，返回false
            if (dataPosition >= dataSize) {
                return false
            }
            
            return true
        } catch (e: Exception) {
            Log.e(TAG, "检查Parcel位置时出错: ${e.message}")
            return false
        }
    }
    
    /**
     * 安全地从Bundle中读取字符串
     * 防止 "Reading a NULL string not supported here" 错误
     */
    fun safeGetString(bundle: Bundle?, key: String, defaultValue: String = ""): String {
        if (bundle == null) return defaultValue
        
        return try {
            // 首先检查键是否存在
            if (!bundle.containsKey(key)) return defaultValue
            
            // 尝试获取字符串，如果为null则返回默认值
            bundle.getString(key) ?: defaultValue
        } catch (e: Exception) {
            Log.e(TAG, "从Bundle读取字符串时出错: ${e.message}")
            defaultValue
        }
    }
    
    /**
     * 安全地将字符串放入Bundle
     * 防止空字符串导致的序列化问题
     */
    fun safePutString(bundle: Bundle, key: String, value: String?) {
        try {
            // 如果值为null，则存储空字符串而不是null
            bundle.putString(key, value ?: "")
        } catch (e: Exception) {
            Log.e(TAG, "向Bundle写入字符串时出错: ${e.message}")
            // 出错时尝试回退到空字符串
            try {
                bundle.putString(key, "")
            } catch (e2: Exception) {
                Log.e(TAG, "回退写入失败: ${e2.message}")
            }
        }
    }
    
    /**
     * 安全获取Intent中的extras
     * 防止NULL string读取错误
     */
    fun safeGetIntentExtras(intent: Intent): Bundle? {
        return try {
            // 尝试获取extras
            val extras = intent.extras
            
            // 如果extras非空，验证其内容
            if (extras != null) {
                // 创建一个安全的Bundle副本
                val safeBundle = Bundle()
                
                // 安全复制键值对
                for (key in extras.keySet()) {
                    val value = extras.get(key)
                    try {
                        // 专门处理String类型，防止NULL string错误
                        if (value is String?) {
                            if (value != null) {
                                safeBundle.putString(key, value)
                            }
                        } else if (value != null) {
                            // 安全地添加非空值
                            when (value) {
                                is Int -> safeBundle.putInt(key, value)
                                is Long -> safeBundle.putLong(key, value)
                                is Boolean -> safeBundle.putBoolean(key, value)
                                is Float -> safeBundle.putFloat(key, value)
                                is Double -> safeBundle.putDouble(key, value)
                                is Bundle -> safeBundle.putBundle(key, getSafeBundle(value))
                                is Array<*> -> {
                                    when {
                                        value.isArrayOf<String>() -> safeBundle.putStringArray(key, value as Array<String>)
                                        value.isArrayOf<Int>() -> {
                                            // Convert Array<Int> to IntArray
                                            val intArray = (value as Array<Int>).toIntArray()
                                            safeBundle.putIntArray(key, intArray)
                                        }
                                        // 其他数组类型...
                                    }
                                }
                                else -> {
                                    // 其他类型尝试放入但记录警告
                                    Log.w(TAG, "未能直接处理的类型: ${value.javaClass.simpleName}")
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "复制extras值时出错: $key, ${e.message}")
                        // 出错时跳过此键值对
                    }
                }
                
                safeBundle
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "安全获取Intent extras失败: ${e.message}")
            Bundle() // 返回空Bundle而不是null，避免空指针
        }
    }
    
    /**
     * 安全地添加Intent Extra，避免过大数据和序列化错误
     */
    fun safePutExtra(intent: Intent, key: String, value: Any?) {
        try {
            when (value) {
                null -> return
                is String -> intent.putExtra(key, value)
                is Int -> intent.putExtra(key, value)
                is Long -> intent.putExtra(key, value)
                is Boolean -> intent.putExtra(key, value)
                is Float -> intent.putExtra(key, value)
                is Double -> intent.putExtra(key, value)
                is Parcelable -> safeParcelableExtra(intent, key, value)
                is Serializable -> safeSerializableExtra(intent, key, value)
                else -> {
                    // 对于其他类型，转换为字符串
                    Log.w(TAG, "不支持的Extra类型: ${value.javaClass.simpleName}, 已转为字符串")
                    intent.putExtra(key, value.toString())
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加Intent Extra时出错: ${key}, ${e.message}")
        }
    }
    
    /**
     * 安全地添加Parcelable，处理大对象
     */
    private fun safeParcelableExtra(intent: Intent, key: String, value: Parcelable) {
        try {
            // 检测Parcelable大小
            val parcel = Parcel.obtain()
            value.writeToParcel(parcel, 0)
            val size = parcel.dataSize()
            parcel.recycle()
            
            // Binder事务限制通常在1MB左右
            if (size <= 500 * 1024) { // 使用500KB作为安全阈值
                intent.putExtra(key, value)
            } else {
                Log.w(TAG, "Parcelable太大 (${size/1024}KB)，无法通过Intent传递: $key")
                // 可以在此处实现备选方案，如保存到磁盘或数据库
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加Parcelable Extra时出错: ${e.message}")
        }
    }
    
    /**
     * 安全地添加Serializable，处理大对象
     */
    private fun safeSerializableExtra(intent: Intent, key: String, value: Serializable) {
        try {
            intent.putExtra(key, value)
        } catch (e: Exception) {
            Log.e(TAG, "添加Serializable Extra时出错: ${e.message}")
        }
    }
    
    /**
     * 包装Bundle的安全创建方法
     * 用于替代可能不安全的Bundle构造
     */
    fun safeBundle(vararg pairs: Pair<String, Any?>): Bundle {
        return try {
            bundleOf(*pairs)
        } catch (e: Exception) {
            Log.e(TAG, "创建Bundle时出错: ${e.message}")
            Bundle()
        }
    }
    
    /**
     * 批量安全地向Bundle添加键值对
     */
    fun safePutAll(bundle: Bundle, vararg pairs: Pair<String, Any?>) {
        pairs.forEach { (key, value) ->
            when (value) {
                null -> return@forEach
                is String -> safePutString(bundle, key, value)
                is Int -> try { bundle.putInt(key, value) } catch (e: Exception) { logError(key, e) }
                is Long -> try { bundle.putLong(key, value) } catch (e: Exception) { logError(key, e) }
                is Boolean -> try { bundle.putBoolean(key, value) } catch (e: Exception) { logError(key, e) }
                is Float -> try { bundle.putFloat(key, value) } catch (e: Exception) { logError(key, e) }
                is Double -> try { bundle.putDouble(key, value) } catch (e: Exception) { logError(key, e) }
                else -> try { bundle.putString(key, value.toString()) } catch (e: Exception) { logError(key, e) }
            }
        }
    }
    
    /**
     * 记录错误但不中断流程
     */
    private fun logError(key: String, e: Exception) {
        Log.e(TAG, "向Bundle添加键($key)时出错: ${e.message}")
    }
    
    /**
     * 验证Bundle的有效性
     * 用于检测可能包含问题数据的Bundle
     */
    fun validateBundle(bundle: Bundle?): Boolean {
        if (bundle == null) return false
        
        try {
            // 尝试克隆Bundle来验证其有效性
            val clone = Bundle(bundle)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Bundle验证失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 安全地读取Parcel中的字符串
     * 防止NULL string读取错误
     */
    fun <T> safeReadFromParcel(parcel: Parcel, creator: Parcelable.Creator<T>): T? {
        return try {
            val startPos = parcel.dataPosition()
            
            try {
                // 尝试正常读取
                creator.createFromParcel(parcel)
            } catch (e: Exception) {
                // 如果失败，回到起始位置
                parcel.setDataPosition(startPos)
                
                // 记录错误
                val errorType = e.javaClass.simpleName
                errorCountMap[errorType] = errorCountMap.getOrDefault(errorType, 0) + 1
                
                if (e.message?.contains("NULL string") == true) {
                    Log.w(TAG, "检测到NULL string错误，尝试使用安全读取")
                    
                    // 尝试安全读取（跳过字符串）
                    safeSkipNullString(parcel)
                    null
                } else {
                    Log.e(TAG, "从Parcel读取失败: ${e.message}")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Parcel操作完全失败: ${e.message}")
            null
        }
    }
    
    /**
     * 安全地跳过可能的NULL字符串
     */
    fun safeSkipNullString(parcel: Parcel) {
        try {
            // 创建安全的空对象读取
            val dummyBundle = Bundle()
            dummyBundle.writeToParcel(parcel, 0)
        } catch (e: Exception) {
            // 忽略错误，这只是一个恢复尝试
        }
    }
    
    /**
     * 包装Intent以防止Parcel错误
     */
    fun getSafeIntent(original: Intent): Intent {
        val safeIntent = Intent(original.action)
        
        try {
            // 只复制基本动作和类型
            safeIntent.data = original.data
            safeIntent.type = original.type
            
            // 仅复制必要的flags
            safeIntent.flags = original.flags
            
            // 安全复制extras
            val originalExtras = original.extras
            if (originalExtras != null) {
                val safeExtras = getSafeBundle(originalExtras)
                safeIntent.putExtras(safeExtras)
            }
            
            return safeIntent
        } catch (e: Exception) {
            Log.e(TAG, "创建安全Intent失败: ${e.message}")
            return Intent() // 返回空Intent以防止崩溃
        }
    }
    
    /**
     * 创建安全的Bundle副本
     */
    fun getSafeBundle(original: Bundle): Bundle {
        val safe = Bundle()
        
        try {
            // 逐个安全复制键值对
            for (key in original.keySet()) {
                try {
                    val value = original.get(key)
                    
                    // 特殊处理String，防止NULL值
                    if (value is String?) {
                        if (value != null) {
                            safe.putString(key, value)
                        }
                    } 
                    // 特殊处理Bundle，递归确保安全
                    else if (value is Bundle) {
                        safe.putBundle(key, getSafeBundle(value))
                    }
                    // 其他非空值直接添加
                    else if (value != null) {
                        when (value) {
                            is Int -> safe.putInt(key, value)
                            is Long -> safe.putLong(key, value)
                            is Boolean -> safe.putBoolean(key, value)
                            is Float -> safe.putFloat(key, value)
                            is Double -> safe.putDouble(key, value)
                            is CharSequence -> safe.putCharSequence(key, value)
                            is Array<*> -> {
                                when {
                                    value.isArrayOf<String>() -> safe.putStringArray(key, value as Array<String>)
                                    value.isArrayOf<Int>() -> {
                                        // Convert Array<Int> to IntArray
                                        val intArray = (value as Array<Int>).toIntArray()
                                        safe.putIntArray(key, intArray)
                                    }
                                    // 其他数组类型...
                                }
                            }
                            else -> {
                                if (value is Parcelable) {
                                    safe.putParcelable(key, value)
                                } else {
                                    Log.w(TAG, "跳过无法安全存储的类型: ${value.javaClass.simpleName}")
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 出错时跳过此键值对
                    Log.w(TAG, "复制Bundle值失败，跳过键: $key: ${e.message}")
                }
            }
            
            return safe
        } catch (e: Exception) {
            Log.e(TAG, "创建安全Bundle完全失败: ${e.message}")
            return Bundle() // 返回空Bundle以防止崩溃
        }
    }

    fun safeBundle(block: (Bundle) -> Unit): Bundle {
        val safeBundle = Bundle()
        try {
            block(safeBundle)
        } catch (e: Exception) {
            Log.e(TAG, "Error while creating safe bundle", e)
        }
        return safeBundle
    }

    fun Bundle.safeIntArray(key: String, value: Array<Int>?) {
        if (value != null) {
            this.putIntArray(key, value.toIntArray())
        }
    }

    fun Bundle.safeIntArray(key: String, value: IntArray?) {
        if (value != null) {
            this.putIntArray(key, value)
        }
    }

    /**
     * 创建安全的Intent
     * 防止Intent传递时可能发生的Parcel错误
     */
    fun createSafeIntent(action: String): Intent {
        val intent = Intent(action)
        // 设置安全标志，避免常见错误
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        return intent
    }

    /**
     * 创建安全Intent并添加Bundle
     * 确保Bundle内容不会导致Parcel错误
     */
    fun createSafeIntent(action: String, extras: Bundle?): Intent {
        val intent = createSafeIntent(action)
        
        if (extras != null) {
            try {
                // 创建新的安全Bundle替代原始Bundle
                val safeBundle = getSafeBundle(extras)
                intent.putExtras(safeBundle)
            } catch (e: Exception) {
                Log.e(TAG, "添加extras时出错: ${e.message}")
            }
        }
        
        return intent
    }

    /**
     * 处理HwcComposer错误的专用方法
     * 创建一个安全的Intent，避免在处理渲染错误时发生额外的崩溃
     */
    fun createSafeRestartIntent(packageContext: android.content.Context, cls: Class<*>): Intent {
        val intent = Intent(packageContext, cls)
        
        // 设置标志使Intent更稳定
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        
        // 添加安全标记，指示这是紧急重启
        val safeBundle = Bundle()
        safePutString(safeBundle, "is_emergency_restart", "true")
        safePutLong(safeBundle, "restart_time", System.currentTimeMillis())
        
        intent.putExtras(safeBundle)
        return intent
    }

    /**
     * 安全地检查Intent中是否有紧急重启标记
     */
    fun checkEmergencyRestartIntent(intent: Intent?): Boolean {
        if (intent == null) return false
        
        return try {
            val extras = intent.extras ?: return false
            safeGetString(extras, "is_emergency_restart", "") == "true"
        } catch (e: Exception) {
            Log.e(TAG, "检查紧急重启标记时出错: ${e.message}")
            false
        }
    }

    /**
     * 安全地将long值放入Bundle
     */
    fun safePutLong(bundle: Bundle, key: String, value: Long) {
        try {
            bundle.putLong(key, value)
        } catch (e: Exception) {
            Log.e(TAG, "向Bundle写入Long时出错: ${e.message}")
        }
    }

    /**
     * 增强的NULL字符串错误处理
     * 针对日志中频繁出现的"Reading a NULL string not supported here"错误
     */
    fun applyStringErrorFixes() {
        try {
            Log.d(TAG, "正在应用NULL字符串错误修复")
            
            // 1. 通过反射修改Parcel内部行为
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                try {
                    // 尝试获取Parcel类
                    val parcelClass = Parcel::class.java
                    
                    // 查找setBootClustering方法 - 这可以改变Parcel的内部处理方式
                    try {
                        val setBootClusteringMethod = parcelClass.getDeclaredMethod("setBootClustering", Boolean::class.java)
                        setBootClusteringMethod.isAccessible = true
                        setBootClusteringMethod.invoke(null, false)
                        Log.d(TAG, "已禁用Parcel boot clustering")
                    } catch (e: Exception) {
                        Log.w(TAG, "设置boot clustering失败: ${e.message}")
                    }
                    
                    // 尝试设置默认错误处理方式
                    try {
                        val methods = parcelClass.declaredMethods
                        val errorHandlingMethod = methods.firstOrNull { it.name.contains("ErrorHandling") || it.name.contains("ReturnException") }
                        
                        if (errorHandlingMethod != null) {
                            errorHandlingMethod.isAccessible = true
                            
                            // 如果方法接受Exception参数，传递一个自定义异常
                            if (errorHandlingMethod.parameterTypes.size == 1 && 
                                errorHandlingMethod.parameterTypes[0] == Exception::class.java) {
                                val safeException = RuntimeException("Safe null string handling")
                                errorHandlingMethod.invoke(null, safeException)
                                Log.d(TAG, "已设置自定义Parcel错误处理")
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "设置错误处理方法失败: ${e.message}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "通过反射修改Parcel行为失败: ${e.message}")
                }
            }
            
            // 2. 注册JNI库钩子（如果支持）
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    val vmClass = Class.forName("dalvik.system.VMDebug")
                    val setNativeHookMethod = vmClass.getDeclaredMethod("setNativeAllocationHook", Class::class.java)
                    setNativeHookMethod.isAccessible = true
                    
                    // 传入null以关闭钩子而不是启用它
                    setNativeHookMethod.invoke(null, null)
                    Log.d(TAG, "已重置Native分配钩子")
                }
            } catch (e: Exception) {
                // 忽略不支持的操作
            }
            
            // 3. 重置Parcel池
            try {
                // 创建并回收一些Parcel以刷新池
                for (i in 0 until 5) {
                    val p = Parcel.obtain()
                    p.recycle()
                }
                Log.d(TAG, "已刷新Parcel池")
            } catch (e: Exception) {
                Log.w(TAG, "刷新Parcel池失败: ${e.message}")
            }
            
            // 4. 强制GC清理可能损坏的对象
            System.gc()
            
            Log.d(TAG, "NULL字符串错误修复措施已完成")
        } catch (e: Exception) {
            Log.e(TAG, "应用NULL字符串错误修复时出现异常: ${e.message}")
        }
    }
    
    /**
     * 添加自定义NULL字符串拦截器
     * 替换应用中可能有问题的getString调用
     */
    fun installStringInterceptors() {
        try {
            Log.d(TAG, "正在安装字符串拦截器")
            
            // 由于无法完全修改Framework行为，我们使用proguard规则和此方法通知开发者
            Log.d(TAG, "请确保在proguard规则中添加了SafeParcelHelper的调用重定向")
            
            isInitialized = true
        } catch (e: Exception) {
            Log.e(TAG, "安装字符串拦截器失败: ${e.message}")
        }
    }

    /**
     * 启用安全的Parcel读取
     * 轻量级实现，避免使用反射
     */
    fun enableSafeReads() {
        try {
            // 初始化安全数据结构
            safeStringCache.clear()
            
            // 启用安全模式
            isSafeModeEnabled = true
            
            Log.d(TAG, "已启用安全Parcel读取模式")
        } catch (e: Exception) {
            Log.e(TAG, "启用安全Parcel读取失败: ${e.message}")
        }
    }
} 