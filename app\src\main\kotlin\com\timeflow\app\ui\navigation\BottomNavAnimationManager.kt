package com.timeflow.app.ui.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.runtime.*
import kotlinx.coroutines.delay

/**
 * 🎯 底部导航栏动画管理器
 * 专门处理底部导航栏的切换动画，参考微信、抖音等知名应用的设计
 * 提供丝滑的标签页切换体验
 */
object BottomNavAnimationManager {
    
    /**
     * 标签页切换方向
     */
    enum class TabSwitchDirection {
        LEFT_TO_RIGHT,  // 从左到右
        RIGHT_TO_LEFT,  // 从右到左
        NONE           // 无方向（直接跳转）
    }
    
    /**
     * 获取标签页切换方向
     */
    fun getTabSwitchDirection(fromTabIndex: Int, toTabIndex: Int): TabSwitchDirection {
        return when {
            toTabIndex > fromTabIndex -> TabSwitchDirection.LEFT_TO_RIGHT
            toTabIndex < fromTabIndex -> TabSwitchDirection.RIGHT_TO_LEFT
            else -> TabSwitchDirection.NONE
        }
    }
    
    /**
     * 获取底部导航路由的索引
     */
    fun getTabIndex(route: String): Int {
        return when (route) {
            AppDestinations.UNIFIED_HOME_ROUTE, 
            AppDestinations.HOME_ROUTE -> 0
            AppDestinations.TASK_ROUTE,
            AppDestinations.TASK_LIST_ROUTE,
            AppDestinations.TASK_LIST_FULL_ROUTE -> 1
            AppDestinations.CALENDAR_ROUTE -> 2
            AppDestinations.ANALYTICS_ROUTE -> 3
            AppDestinations.PROFILE_ROUTE -> 4
            else -> -1
        }
    }
    
    /**
     * 🚀 微信风格的标签页切换动画
     * 特点：快速、简洁、无过度装饰
     */
    object WeChatStyle {
        fun getAnimation(direction: TabSwitchDirection): AnimationSet {
            return when (direction) {
                TabSwitchDirection.LEFT_TO_RIGHT -> AnimationSet(
                    enter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    exit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    popEnter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> -fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    popExit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(150, easing = LinearEasing)
                    )
                )
                
                TabSwitchDirection.RIGHT_TO_LEFT -> AnimationSet(
                    enter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> -fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    exit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    popEnter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(150, easing = LinearEasing)
                    ),
                    popExit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -fullWidth / 10 },
                        animationSpec = tween(200, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(150, easing = LinearEasing)
                    )
                )
                
                TabSwitchDirection.NONE -> AnimationSet(
                    enter = fadeIn(animationSpec = tween(150)),
                    exit = fadeOut(animationSpec = tween(150)),
                    popEnter = fadeIn(animationSpec = tween(150)),
                    popExit = fadeOut(animationSpec = tween(150))
                )
            }
        }
    }
    
    /**
     * 🎵 抖音风格的标签页切换动画
     * 特点：有节奏感、轻微的弹性效果
     */
    object TikTokStyle {
        fun getAnimation(direction: TabSwitchDirection): AnimationSet {
            return when (direction) {
                TabSwitchDirection.LEFT_TO_RIGHT,
                TabSwitchDirection.RIGHT_TO_LEFT -> AnimationSet(
                    enter = scaleIn(
                        initialScale = 0.95f,
                        animationSpec = spring(
                            dampingRatio = 0.8f,
                            stiffness = 400f
                        )
                    ) + fadeIn(
                        animationSpec = tween(200, easing = LinearEasing)
                    ),
                    exit = scaleOut(
                        targetScale = 0.95f,
                        animationSpec = spring(
                            dampingRatio = 0.8f,
                            stiffness = 400f
                        )
                    ) + fadeOut(
                        animationSpec = tween(200, easing = LinearEasing)
                    ),
                    popEnter = scaleIn(
                        initialScale = 0.95f,
                        animationSpec = spring(
                            dampingRatio = 0.8f,
                            stiffness = 400f
                        )
                    ) + fadeIn(
                        animationSpec = tween(200, easing = LinearEasing)
                    ),
                    popExit = scaleOut(
                        targetScale = 0.95f,
                        animationSpec = spring(
                            dampingRatio = 0.8f,
                            stiffness = 400f
                        )
                    ) + fadeOut(
                        animationSpec = tween(200, easing = LinearEasing)
                    )
                )
                
                TabSwitchDirection.NONE -> AnimationSet(
                    enter = fadeIn(animationSpec = tween(150)),
                    exit = fadeOut(animationSpec = tween(150)),
                    popEnter = fadeIn(animationSpec = tween(150)),
                    popExit = fadeOut(animationSpec = tween(150))
                )
            }
        }
    }
    
    /**
     * 🍎 iOS风格的标签页切换动画
     * 特点：流畅、精确的时机控制
     */
    object iOSStyle {
        fun getAnimation(direction: TabSwitchDirection): AnimationSet {
            val slideDistance = 0.15f // iOS典型的滑动距离比例
            
            return when (direction) {
                TabSwitchDirection.LEFT_TO_RIGHT -> AnimationSet(
                    enter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> (fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeIn(
                        animationSpec = tween(200, delayMillis = 50, easing = LinearEasing)
                    ),
                    exit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -(fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeOut(
                        animationSpec = tween(250, easing = LinearEasing)
                    ),
                    popEnter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> -(fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeIn(
                        animationSpec = tween(200, delayMillis = 50, easing = LinearEasing)
                    ),
                    popExit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> (fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeOut(
                        animationSpec = tween(250, easing = LinearEasing)
                    )
                )
                
                TabSwitchDirection.RIGHT_TO_LEFT -> AnimationSet(
                    enter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> -(fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeIn(
                        animationSpec = tween(200, delayMillis = 50, easing = LinearEasing)
                    ),
                    exit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> (fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeOut(
                        animationSpec = tween(250, easing = LinearEasing)
                    ),
                    popEnter = slideInHorizontally(
                        initialOffsetX = { fullWidth -> (fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeIn(
                        animationSpec = tween(200, delayMillis = 50, easing = LinearEasing)
                    ),
                    popExit = slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -(fullWidth * slideDistance).toInt() },
                        animationSpec = tween(300, easing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f))
                    ) + fadeOut(
                        animationSpec = tween(250, easing = LinearEasing)
                    )
                )
                
                TabSwitchDirection.NONE -> AnimationSet(
                    enter = fadeIn(animationSpec = tween(200)),
                    exit = fadeOut(animationSpec = tween(200)),
                    popEnter = fadeIn(animationSpec = tween(200)),
                    popExit = fadeOut(animationSpec = tween(200))
                )
            }
        }
    }
    
    /**
     * 💫 智能底部导航动画选择器
     * 根据用户偏好和设备性能自动选择最佳动画
     */
    fun getBottomNavAnimation(
        fromRoute: String,
        toRoute: String,
        animationStyle: BottomNavAnimationStyle = BottomNavAnimationStyle.WECHAT
    ): AnimationSet {
        val fromIndex = getTabIndex(fromRoute)
        val toIndex = getTabIndex(toRoute)
        
        // 如果不是底部导航切换，返回无动画
        if (fromIndex == -1 || toIndex == -1) {
            return PageTransitions.BOTTOM_NAV
        }
        
        val direction = getTabSwitchDirection(fromIndex, toIndex)
        
        return when (animationStyle) {
            BottomNavAnimationStyle.WECHAT -> WeChatStyle.getAnimation(direction)
            BottomNavAnimationStyle.TIKTOK -> TikTokStyle.getAnimation(direction)
            BottomNavAnimationStyle.IOS -> iOSStyle.getAnimation(direction)
            BottomNavAnimationStyle.NONE -> PageTransitions.BOTTOM_NAV
        }
    }
    
    /**
     * 底部导航动画风格
     */
    enum class BottomNavAnimationStyle {
        WECHAT,    // 微信风格
        TIKTOK,    // 抖音风格
        IOS,       // iOS风格
        NONE       // 无动画
    }
}

/**
 * 🎨 底部导航动画状态管理器
 * 管理动画状态，提供防重复触发等功能
 */
@Composable
fun rememberBottomNavAnimationState(): BottomNavAnimationState {
    return remember { BottomNavAnimationState() }
}

class BottomNavAnimationState {
    private var lastSwitchTime = 0L
    private var isAnimating = false
    private val minSwitchInterval = 300L // 最小切换间隔，防止快速点击
    
    /**
     * 检查是否可以进行切换
     */
    fun canSwitch(): Boolean {
        val currentTime = System.currentTimeMillis()
        return !isAnimating && (currentTime - lastSwitchTime) >= minSwitchInterval
    }
    
    /**
     * 开始切换动画
     */
    suspend fun startSwitch() {
        if (!canSwitch()) return
        
        isAnimating = true
        lastSwitchTime = System.currentTimeMillis()
        
        // 模拟动画时长
        delay(350)
        
        isAnimating = false
    }
} 