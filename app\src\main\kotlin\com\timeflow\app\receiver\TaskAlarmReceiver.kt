package com.timeflow.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel
import com.timeflow.app.utils.TimeFlowNotificationManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

/**
 * 任务提醒广播接收器
 * 用于接收任务提醒的闹钟广播并显示通知
 */
@AndroidEntryPoint
class TaskAlarmReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var notificationManager: TimeFlowNotificationManager
    
    // 创建协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 通知设置DataStore - 与NotificationSettingsViewModel保持一致
    private val Context.notificationSettingsDataStore by preferencesDataStore(name = "notification_settings")
    
    // DataStore键定义 - 与NotificationSettingsViewModel保持一致
    private object PreferencesKeys {
        val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")
        val TASK_REMINDERS_ENABLED = booleanPreferencesKey("task_reminders_enabled")
        val DEADLINE_REMINDERS_ENABLED = booleanPreferencesKey("deadline_reminders_enabled")
        val OVERDUE_REMINDERS_ENABLED = booleanPreferencesKey("overdue_reminders_enabled")
        val DAILY_REVIEW_ENABLED = booleanPreferencesKey("daily_review_enabled")
        val DAILY_REVIEW_TIME = stringPreferencesKey("daily_review_time")
        val TASK_PERSISTENT_NOTIFICATION_ENABLED = booleanPreferencesKey("task_persistent_notification_enabled")
        
        val HABIT_REMINDERS_ENABLED = booleanPreferencesKey("habit_reminders_enabled")
        val HABIT_STREAK_NOTIFICATION_ENABLED = booleanPreferencesKey("habit_streak_notification_enabled")
        
        val FOCUS_REMINDERS_ENABLED = booleanPreferencesKey("focus_reminders_enabled")
        val FOCUS_SESSION_NOTIFICATIONS_ENABLED = booleanPreferencesKey("focus_session_notifications_enabled")
        
        val AI_SUGGESTIONS_ENABLED = booleanPreferencesKey("ai_suggestions_enabled")
        val PRODUCTIVITY_INSIGHTS_ENABLED = booleanPreferencesKey("productivity_insights_enabled")
        val WEEKLY_REPORTS_ENABLED = booleanPreferencesKey("weekly_reports_enabled")
        
        val MEDICATION_REMINDERS_ENABLED = booleanPreferencesKey("medication_reminders_enabled")
        val MEDICATION_SOUND_ENABLED = booleanPreferencesKey("medication_sound_enabled")
        val MEDICATION_VIBRATION_ENABLED = booleanPreferencesKey("medication_vibration_enabled")
        val MEDICATION_ADVANCE_TIME = intPreferencesKey("medication_advance_time")
        val MEDICATION_REPEAT_ENABLED = booleanPreferencesKey("medication_repeat_enabled")
        val MEDICATION_REPEAT_INTERVAL = intPreferencesKey("medication_repeat_interval")
        val MEDICATION_MAX_REPEATS = intPreferencesKey("medication_max_repeats")
        val MISSED_DOSE_REMINDERS_ENABLED = booleanPreferencesKey("missed_dose_reminders_enabled")
        val ADHERENCE_REPORTS_ENABLED = booleanPreferencesKey("adherence_reports_enabled")
        
        val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")
        val LED_ENABLED = booleanPreferencesKey("led_enabled")
        val BADGE_ENABLED = booleanPreferencesKey("badge_enabled")
        
        val LOCK_SCREEN_VISIBLE = booleanPreferencesKey("lock_screen_visible")
        val BANNER_STYLE = booleanPreferencesKey("banner_style")
        val GROUP_NOTIFICATIONS = booleanPreferencesKey("group_notifications")
        
        val DO_NOT_DISTURB_ENABLED = booleanPreferencesKey("do_not_disturb_enabled")
        val DO_NOT_DISTURB_START_TIME = stringPreferencesKey("do_not_disturb_start_time")
        val DO_NOT_DISTURB_END_TIME = stringPreferencesKey("do_not_disturb_end_time")
        val DO_NOT_DISTURB_WEEKENDS = booleanPreferencesKey("do_not_disturb_weekends")
        
        val DEFAULT_REMINDER_TIME = intPreferencesKey("default_reminder_time")
        val URGENT_REMINDER_TIME = intPreferencesKey("urgent_reminder_time")
        val HABIT_REMINDER_ADVANCE = intPreferencesKey("habit_reminder_advance")
    }
    
    companion object {
        private const val TAG = "TaskAlarmReceiver"
        const val EXTRA_TASK_ID = "task_id"
        const val EXTRA_TASK_TITLE = "task_title"
        const val EXTRA_PRIORITY = "priority"
        const val EXTRA_REMINDER_TYPE = "reminder_type"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "🔔 =================== 接收到任务提醒广播 ===================")
        Log.d(TAG, "🔔 广播时间: ${java.time.LocalDateTime.now()}")
        Log.d(TAG, "🔔 Intent action: ${intent.action}")
        Log.d(TAG, "🔔 Intent extras: ${intent.extras?.keySet()?.joinToString()}")
        
        // 获取任务信息
        val taskId = intent.getStringExtra(EXTRA_TASK_ID)
        val taskTitle = intent.getStringExtra(EXTRA_TASK_TITLE)
        val priority = intent.getStringExtra(EXTRA_PRIORITY)
        val reminderType = intent.getStringExtra(EXTRA_REMINDER_TYPE)
        
        Log.d(TAG, "🔔 任务提醒详情:")
        Log.d(TAG, "🔔   - 任务ID: $taskId")
        Log.d(TAG, "🔔   - 任务标题: $taskTitle")
        Log.d(TAG, "🔔   - 优先级: $priority")
        Log.d(TAG, "🔔   - 提醒类型: $reminderType")
        
        if (taskId == null) {
            Log.e(TAG, "❌ 任务ID为空，无法处理提醒")
            return
        }
        
        if (taskTitle == null) {
            Log.e(TAG, "❌ 任务标题为空，使用默认标题")
        }
        
        val finalTaskTitle = taskTitle ?: "您的任务"
        val finalPriority = priority ?: "中等"
        val finalReminderType = reminderType ?: "task_reminder"
        
        Log.d(TAG, "🔔 开始处理通知发送...")
        
        // 在协程中处理通知发送
        scope.launch {
            try {
                Log.d(TAG, "🔔 协程开始执行通知发送逻辑")
                
                // 获取当前通知设置
                val settings = getNotificationSettings(context)
                Log.d(TAG, "🔔 通知设置: $settings")
                
                // 根据提醒类型发送对应的通知
                when (finalReminderType) {
                    "task_start_reminder" -> {
                        Log.d(TAG, "🔔 发送任务开始提醒通知")
                        notificationManager.showTaskStartReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            startTime = formatCurrentTime(),
                            priority = finalPriority,
                            settings = settings
                        )
                        Log.d(TAG, "✅ 任务开始提醒通知已发送")
                    }
                    "task_deadline_reminder" -> {
                        Log.d(TAG, "🔔 发送任务截止前提醒通知")
                        notificationManager.showTaskDeadlineReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            priority = finalPriority,
                            settings = settings
                        )
                        Log.d(TAG, "✅ 任务截止前提醒通知已发送")
                    }
                    // 保留向后兼容性
                    "task_reminder" -> {
                        Log.d(TAG, "🔔 发送任务提醒通知（兼容模式）")
                        notificationManager.showTaskStartReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            startTime = formatCurrentTime(),
                            priority = finalPriority,
                            settings = settings
                        )
                        Log.d(TAG, "✅ 任务提醒通知已发送（兼容模式）")
                    }
                    "deadline_reminder" -> {
                        Log.d(TAG, "🔔 发送截止日期提醒通知（兼容模式）")
                        notificationManager.showTaskDeadlineReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            priority = finalPriority,
                            settings = settings
                        )
                        Log.d(TAG, "✅ 截止日期提醒通知已发送（兼容模式）")
                    }
                    "overdue_reminder" -> {
                        Log.d(TAG, "🔔 发送逾期提醒通知")
                        notificationManager.showOverdueReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            overdueDays = 1, // 可以根据实际情况计算
                            settings = settings
                        )
                        Log.d(TAG, "✅ 逾期提醒通知已发送")
                    }
                    else -> {
                        Log.w(TAG, "⚠️ 未知的提醒类型: $finalReminderType，使用默认处理")
                        notificationManager.showTaskStartReminder(
                            taskId = taskId,
                            taskTitle = finalTaskTitle,
                            startTime = formatCurrentTime(),
                            priority = finalPriority,
                            settings = settings
                        )
                        Log.d(TAG, "✅ 默认任务提醒通知已发送")
                    }
                }
                
                Log.d(TAG, "🔔 =================== 任务提醒处理完成 ===================")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 发送任务提醒通知失败", e)
                Log.e(TAG, "❌ 错误详情: ${e.message}")
                Log.e(TAG, "❌ 错误堆栈: ${e.stackTrace.joinToString("\n")}")
            }
        }
    }
    
    /**
     * 获取通知设置（从DataStore读取用户实际设置）
     */
    private suspend fun getNotificationSettings(context: Context): NotificationSettings {
        return try {
            context.notificationSettingsDataStore.data
                .map { preferences ->
                    NotificationSettings(
                        notificationsEnabled = preferences[PreferencesKeys.NOTIFICATIONS_ENABLED] ?: true,
                        taskRemindersEnabled = preferences[PreferencesKeys.TASK_REMINDERS_ENABLED] ?: true,
                        deadlineRemindersEnabled = preferences[PreferencesKeys.DEADLINE_REMINDERS_ENABLED] ?: true,
                        overdueRemindersEnabled = preferences[PreferencesKeys.OVERDUE_REMINDERS_ENABLED] ?: true,
                        dailyReviewEnabled = preferences[PreferencesKeys.DAILY_REVIEW_ENABLED] ?: true,
                        dailyReviewTime = preferences[PreferencesKeys.DAILY_REVIEW_TIME] ?: "21:00",
                        taskPersistentNotificationEnabled = preferences[PreferencesKeys.TASK_PERSISTENT_NOTIFICATION_ENABLED] ?: true,
                        
                        // 习惯培养提醒（简化）
                        habitRemindersEnabled = preferences[PreferencesKeys.HABIT_REMINDERS_ENABLED] ?: true,

                        // 专注时间提醒（简化）
                        focusRemindersEnabled = preferences[PreferencesKeys.FOCUS_REMINDERS_ENABLED] ?: true,
                        focusSessionNotificationsEnabled = preferences[PreferencesKeys.FOCUS_SESSION_NOTIFICATIONS_ENABLED] ?: true,

                        // 健康管理提醒（新增用药提醒）
                        medicationRemindersEnabled = preferences[PreferencesKeys.MEDICATION_REMINDERS_ENABLED] ?: true,
                        medicationSoundEnabled = preferences[PreferencesKeys.MEDICATION_SOUND_ENABLED] ?: true,
                        medicationVibrationEnabled = preferences[PreferencesKeys.MEDICATION_VIBRATION_ENABLED] ?: true,
                        medicationAdvanceTime = preferences[PreferencesKeys.MEDICATION_ADVANCE_TIME] ?: 5,

                        // 通知方式设置（简化）
                        soundEnabled = preferences[PreferencesKeys.SOUND_ENABLED] ?: true,
                        vibrationEnabled = preferences[PreferencesKeys.VIBRATION_ENABLED] ?: true,

                        // 免打扰设置（简化）
                        doNotDisturbEnabled = preferences[PreferencesKeys.DO_NOT_DISTURB_ENABLED] ?: false,
                        doNotDisturbStartTime = preferences[PreferencesKeys.DO_NOT_DISTURB_START_TIME] ?: "22:00",
                        doNotDisturbEndTime = preferences[PreferencesKeys.DO_NOT_DISTURB_END_TIME] ?: "08:00",

                        // 提醒时间设置（统一简化）
                        defaultReminderTime = preferences[PreferencesKeys.DEFAULT_REMINDER_TIME] ?: 15
                    )
                }
                .first() // 获取当前设置
        } catch (e: Exception) {
            Log.e(TAG, "读取通知设置失败，使用默认设置", e)
            // 如果读取失败，返回默认设置
            NotificationSettings()
        }
    }
    
    /**
     * 格式化当前时间
     */
    private fun formatCurrentTime(): String {
        val now = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("MM月dd日 HH:mm")
        return now.format(formatter)
    }
} 