package com.timeflow.app.ui.screen.discover

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.task.components.AiSuggestion
import com.timeflow.app.ui.task.components.createDemoSuggestions
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * 发现页面的ViewModel，负责管理AI建议和其他发现功能
 */
@HiltViewModel
class DiscoverViewModel @Inject constructor(
    private val taskRepository: TaskRepository
) : ViewModel() {

    // 错误处理器
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        Log.e(TAG, "ViewModel异常: ${exception.message}", exception)
        _error.value = exception.message ?: "未知错误"
    }

    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    // AI建议列表
    private val _aiSuggestions = MutableStateFlow<List<AiSuggestion>>(emptyList())
    val aiSuggestions: StateFlow<List<AiSuggestion>> = _aiSuggestions

    init {
        loadInitialSuggestions()
    }

    /**
     * 加载初始AI建议
     */
    private fun loadInitialSuggestions() {
        viewModelScope.launch(exceptionHandler) {
            _isLoading.value = true
            try {
                // 模拟从API获取AI建议
                // 实际项目中，这里应该调用repository获取真实的AI建议
                delay(1000) // 模拟网络延迟
                _aiSuggestions.value = createDemoSuggestions()
            } catch (e: Exception) {
                Log.e(TAG, "加载建议失败: ${e.message}", e)
                _error.value = "无法加载AI建议: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 刷新AI建议
     */
    fun refreshSuggestions() {
        viewModelScope.launch(exceptionHandler) {
            _isLoading.value = true
            try {
                // 模拟刷新AI建议
                delay(1000) // 模拟网络延迟
                _aiSuggestions.value = createDemoSuggestions().shuffled()
                _error.value = null
            } catch (e: Exception) {
                Log.e(TAG, "刷新建议失败: ${e.message}", e)
                _error.value = "刷新AI建议失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 添加AI建议的任务到任务列表
     */
    suspend fun addTaskFromSuggestion(suggestion: AiSuggestion) {
        try {
            // 这里应该调用TaskRepository将AI建议转换为实际任务并保存
            // 例如：taskRepository.createTaskFromSuggestion(suggestion)
            Log.d(TAG, "从AI建议创建任务: ${suggestion.text}")
            
            // 从列表中移除已添加的建议
            val updatedList = _aiSuggestions.value.toMutableList()
            updatedList.remove(suggestion)
            _aiSuggestions.value = updatedList
            
            // 实际项目中，这里需要根据业务逻辑进行更多操作
        } catch (e: Exception) {
            Log.e(TAG, "添加AI建议任务失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 忽略AI建议
     */
    fun dismissSuggestion(suggestion: AiSuggestion) {
        viewModelScope.launch(exceptionHandler) {
            try {
                // 从列表中移除已忽略的建议
                val updatedList = _aiSuggestions.value.toMutableList()
                updatedList.remove(suggestion)
                _aiSuggestions.value = updatedList
                
                // 实际项目中，这里可能需要记录用户忽略的建议，以优化未来的推荐
                Log.d(TAG, "忽略AI建议: ${suggestion.text}")
            } catch (e: Exception) {
                Log.e(TAG, "忽略AI建议失败: ${e.message}", e)
                _error.value = "无法忽略建议: ${e.message}"
            }
        }
    }

    companion object {
        private const val TAG = "DiscoverViewModel"
    }
} 