package com.timeflow.app.utils

import android.app.Activity
import java.lang.ref.WeakReference

/**
 * Activity上下文提供者
 * 用于在非Composable上下文中获取当前Activity实例
 */
object ActivityContextProvider {
    private var activityRef: WeakReference<Activity>? = null
    
    /**
     * 设置当前Activity
     * 应在Activity的onCreate方法中调用
     */
    fun setActivityContext(activity: Activity) {
        activityRef = WeakReference(activity)
    }
    
    /**
     * 获取当前Activity
     * @return 当前Activity实例，如果不存在则返回null
     */
    fun getActivityContext(): Activity? {
        return activityRef?.get()
    }
    
    /**
     * 清除Activity引用
     * 应在Activity的onDestroy方法中调用
     */
    fun clearActivityContext() {
        activityRef?.clear()
        activityRef = null
    }
} 