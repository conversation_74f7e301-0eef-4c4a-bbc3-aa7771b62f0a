package com.timeflow.app.data.mapper

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timeflow.app.data.entity.ReflectionEntity
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.ReflectionType
import com.timeflow.app.ui.screen.reflection.ContentBlock

/**
 * 感想数据转换器
 */
object ReflectionMapper {
    
    private val gson = Gson()
    
    /**
     * 将数据库实体转换为UI模型
     */
    fun entityToModel(entity: ReflectionEntity): Reflection {
        return Reflection(
            id = entity.id,
            title = entity.title,
            content = entity.content,
            richContent = parseRichContent(entity.richContentJson),
            date = entity.date,
            rating = entity.rating,
            tags = parseTagsInternal(entity.tagsJson),
            type = parseReflectionType(entity.type),
            mood = parseMoodType(entity.mood),
            plans = parsePlans(entity.plansJson),
            backgroundImage = entity.backgroundImage,
            metrics = parseMetrics(entity.metricsJson)
        )
    }
    
    /**
     * 将UI模型转换为数据库实体
     */
    fun modelToEntity(model: Reflection, taskId: String? = null, taskTitle: String? = null, isFromTaskCompletion: Boolean = false): ReflectionEntity {
        return ReflectionEntity(
            id = model.id,
            title = model.title,
            content = model.content,
            richContentJson = serializeRichContent(model.richContent),
            date = model.date,
            rating = model.rating,
            tagsJson = serializeTags(model.tags),
            type = model.type?.name ?: "LIFE",
            mood = model.mood?.name ?: "CALM",
            plansJson = serializePlans(model.plans),
            backgroundImage = model.backgroundImage,
            metricsJson = serializeMetrics(model.metrics),
            createdAt = model.date,
            updatedAt = model.date,
            taskId = taskId,
            taskTitle = taskTitle,
            isFromTaskCompletion = isFromTaskCompletion
        )
    }
    
    /**
     * 解析富文本内容
     */
    private fun parseRichContent(json: String): List<ContentBlock> {
        return try {
            val type = object : TypeToken<List<ContentBlock>>() {}.type
            gson.fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            // 如果解析失败，返回空列表
            emptyList()
        }
    }
    
    /**
     * 序列化富文本内容
     */
    private fun serializeRichContent(content: List<ContentBlock>): String {
        return try {
            gson.toJson(content)
        } catch (e: Exception) {
            "[]"
        }
    }
    
    /**
     * 解析标签列表 - 公开方法，供Repository使用
     */
    fun parseTags(json: String): List<String> {
        return try {
            val type = object : TypeToken<List<String>>() {}.type
            gson.fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 解析标签列表 - 内部使用
     */
    private fun parseTagsInternal(json: String): List<String> {
        return try {
            val type = object : TypeToken<List<String>>() {}.type
            gson.fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 序列化标签列表
     */
    private fun serializeTags(tags: List<String>): String {
        return try {
            gson.toJson(tags)
        } catch (e: Exception) {
            "[]"
        }
    }
    
    /**
     * 解析计划列表
     */
    private fun parsePlans(json: String): List<String> {
        return try {
            val type = object : TypeToken<List<String>>() {}.type
            gson.fromJson(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 序列化计划列表
     */
    private fun serializePlans(plans: List<String>): String {
        return try {
            gson.toJson(plans)
        } catch (e: Exception) {
            "[]"
        }
    }
    
    /**
     * 解析指标数据
     */
    private fun parseMetrics(json: String): Map<String, String> {
        return try {
            val type = object : TypeToken<Map<String, String>>() {}.type
            gson.fromJson(json, type) ?: emptyMap()
        } catch (e: Exception) {
            emptyMap()
        }
    }
    
    /**
     * 序列化指标数据
     */
    private fun serializeMetrics(metrics: Map<String, String>): String {
        return try {
            gson.toJson(metrics)
        } catch (e: Exception) {
            "{}"
        }
    }
    
    /**
     * 解析感想类型
     */
    private fun parseReflectionType(typeString: String): ReflectionType {
        return try {
            ReflectionType.valueOf(typeString)
        } catch (e: Exception) {
            ReflectionType.LIFE
        }
    }
    
    /**
     * 解析心情类型
     */
    private fun parseMoodType(moodString: String): MoodType {
        return try {
            MoodType.valueOf(moodString)
        } catch (e: Exception) {
            MoodType.CALM
        }
    }
    
    /**
     * 批量转换实体列表为模型列表
     */
    fun entitiesToModels(entities: List<ReflectionEntity>): List<Reflection> {
        return entities.map { entityToModel(it) }
    }
    
    /**
     * 批量转换模型列表为实体列表
     */
    fun modelsToEntities(models: List<Reflection>): List<ReflectionEntity> {
        return models.map { modelToEntity(it) }
    }
} 