package com.timeflow.app.ui.screen.goal

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.model.TimeSlotInfo
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.HabitModel
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import android.util.Log
import com.timeflow.app.ui.theme.DustyLavender
import androidx.compose.foundation.Canvas
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import kotlin.math.min
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.HabitDeletedEvent
import com.timeflow.app.util.HabitArchivedEvent
import com.timeflow.app.util.HabitStatusChangedEvent
import com.timeflow.app.util.TaskRefreshEvent
import androidx.compose.runtime.DisposableEffect
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.first

// 数据类定义
/**
 * 热力图日期数据
 */
data class HeatmapDayData(
    val date: LocalDate,
    val habitContribution: Float, // 0.0 - 1.0
    val taskContribution: Float   // 0.0 - 1.0
)

/**
 * 加载指示器组件
 */
@Composable
fun LoadingIndicator(modifier: Modifier = Modifier) {
    CircularProgressIndicator(modifier = modifier)
}

/**
 * 目标详情屏幕
 * 显示单个目标的详细信息，包括进度、子目标和统计数据
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalDetailScreen(
    navController: NavController,
    goalId: String,
    viewModel: GoalViewModel = hiltViewModel()
) {
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 系统状态栏管理
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    // 加载目标和子目标数据
    LaunchedEffect(goalId) {
        viewModel.loadGoalDetail(goalId)
    }
    
    // 🔧 修复：使用响应式状态观察
    val currentGoal by viewModel.currentGoal
    val subTasks by viewModel.subTasks
    val uiState by viewModel.uiState
    
    // 界面颜色配置
    val primaryColor = DustyLavender
    val backgroundColor = Color(0xFFF8F9FA)
    val cardBackgroundColor = Color.White
    
    // 主内容区域 - 不使用Scaffold，直接处理状态栏间距
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // 自定义顶部导航栏
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = SystemBarManager.getFixedStatusBarHeight()),
            color = backgroundColor,
            shadowElevation = 1.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 返回按钮
                IconButton(
                    onClick = { navController.popBackStack() },
                    modifier = Modifier.size(28.dp) // 缩小按钮
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF1C1C1E),
                        modifier = Modifier.size(18.dp) // 缩小图标
                    )
                }
                
                // 标题
                Text(
                    text = "目标详情",
                    fontSize = 14.sp, // 缩小字体
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1C1C1E)
                )
                
                // 编辑按钮
                IconButton(
                    onClick = { navController.navigate(AppDestinations.editGoalRoute(goalId)) },
                    modifier = Modifier.size(28.dp) // 缩小按钮
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑",
                        tint = Color(0xFF877b85),
                        modifier = Modifier.size(18.dp) // 缩小图标
                    )
                }
            }
        }
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight() + 52.dp) // 减小导航栏高度
                .background(backgroundColor)
        ) {
        when (uiState) {
            is GoalUiState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    LoadingIndicator()
                }
            }
            is GoalUiState.Error -> {
                val errorState = uiState as GoalUiState.Error
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = errorState.message)
                }
            }
            else -> {
                // 显示目标详情
                currentGoal?.let { goal ->
                    GoalDetailContent(
                        goal = goal,
                        subTasks = subTasks,
                        backgroundColor = backgroundColor,
                        primaryColor = primaryColor,
                        cardBackgroundColor = cardBackgroundColor,
                        viewModel = viewModel,
                        onSubTaskClick = { subTask ->
                            viewModel.markSubTaskCompleted(subTask.id, subTask.completedAt == null)
                        },
                        onAddSubTaskClick = { title, description, estimatedDays ->
                            viewModel.addSubTask(goal.id, title, description, estimatedDays)
                        },
                        onRequestAiBreakdown = {
                            // 修改：导航到目标拆解页面，而不是直接调用ViewModel方法
                            Log.d("GoalDetailScreen", "导航到目标拆解页面: goal.id=${goal.id}")
                            navController.navigate(AppDestinations.goalBreakdownRoute(goal.id))
                        },
                        onRequestAiAnalysis = {
                            viewModel.generateAiAnalysis(goal.id)
                        },
                        onToggleCompleted = { completed ->
                            viewModel.markGoalCompleted(goal.id, completed)
                        },
                        onProgressChange = { progress ->
                            viewModel.updateGoalProgress(goal.id, progress)
                        }
                    )
                } ?: run {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(text = "目标不存在")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun GoalDetailContent(
    goal: Goal,
    subTasks: List<GoalSubTask>,
    backgroundColor: Color,
    primaryColor: Color,
    cardBackgroundColor: Color,
    viewModel: GoalViewModel,
    modifier: Modifier = Modifier,
    onSubTaskClick: (GoalSubTask) -> Unit,
    onAddSubTaskClick: (String, String, Int) -> Unit,
    onRequestAiBreakdown: () -> Unit,
    onRequestAiAnalysis: () -> Unit,
    onToggleCompleted: (Boolean) -> Unit,
    onProgressChange: (Float) -> Unit,
    taskViewModel: com.timeflow.app.ui.task.TaskListViewModel = hiltViewModel()
) {
    var showAddSubTaskDialog by remember { mutableStateOf(false) }
    var showEditSubTaskDialog by remember { mutableStateOf(false) }
    var editingSubTask by remember { mutableStateOf<GoalSubTask?>(null) }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp) // 缩小间距
    ) {
        // 头部区域 - 基本信息
        item {
            GoalHeader(
                goal = goal,
                cardBackgroundColor = cardBackgroundColor,
                onToggleCompleted = onToggleCompleted
            )
        }
        
        // 🔥 新增：目标进度热力图
        item {
            GoalProgressHeatmapSection(
                goalId = goal.id,
                cardBackgroundColor = cardBackgroundColor,
                primaryColor = primaryColor,
                taskViewModel = taskViewModel
            )
        }
        
        // 进度区域
        item {
            GoalProgressSection(
                progress = goal.progress,
                cardBackgroundColor = cardBackgroundColor,
                primaryColor = primaryColor,
                onProgressChange = onProgressChange
            )
        }
        
        // 🔥 优化：支撑习惯区域 - 使用真实数据
        item {
            SupportingHabitsSection(
                goalId = goal.id,
                cardBackgroundColor = cardBackgroundColor,
                primaryColor = primaryColor
            )
        }
        
        // 子目标区域（原子任务）
        item {
            GoalSubTasksSection(
                subTasks = subTasks,
                cardBackgroundColor = cardBackgroundColor,
                primaryColor = primaryColor,
                onSubTaskClick = onSubTaskClick,
                onAddSubTaskClick = { showAddSubTaskDialog = true },
                onRequestAiBreakdown = onRequestAiBreakdown,
                hasAiBreakdown = goal.hasAiBreakdown,
                onDeleteSubTask = { subTask ->
                    viewModel.deleteSubTask(subTask.id)
                },
                onEditSubTask = { subTask ->
                    // 显示编辑对话框
                    showEditSubTaskDialog = true
                    editingSubTask = subTask
                }
            )
        }
        
        // AI分析区域
            item {
                GoalAiAnalysisSection(
                    goal = goal,
                    cardBackgroundColor = cardBackgroundColor,
                primaryColor = primaryColor,
                onRequestAiAnalysis = onRequestAiAnalysis
                )
            }
        
        // 🎯 优化：关联任务区域 - 实时数据同步
            item {
            AssociatedTasksSection(
                goalId = goal.id,
                cardBackgroundColor = cardBackgroundColor,
                    primaryColor = primaryColor
                )
        }
        
        // 底部间距
        item {
            Spacer(modifier = Modifier.height(120.dp)) // 减小底部间距
        }
    }
    
    // 添加子目标对话框
    if (showAddSubTaskDialog) {
        AddSubTaskDialog(
            onDismiss = { showAddSubTaskDialog = false },
            onAddSubTask = { title, description, estimatedDays ->
                onAddSubTaskClick(title, description, estimatedDays)
                showAddSubTaskDialog = false
            }
        )
    }
    
    // 编辑子目标对话框
    if (showEditSubTaskDialog && editingSubTask != null) {
        EditSubTaskDialog(
            subTask = editingSubTask!!,
            onDismiss = { 
                showEditSubTaskDialog = false
                editingSubTask = null
            },
            onUpdateSubTask = { updatedSubTask ->
                viewModel.updateSubTask(updatedSubTask)
                showEditSubTaskDialog = false
                editingSubTask = null
            }
        )
    }
}

/**
 * 🔥 新增：目标进度热力图区域
 * 双色热力图展示习惯与任务的贡献
 */
@Composable
private fun GoalProgressHeatmapSection(
    goalId: String,
    cardBackgroundColor: Color,
    primaryColor: Color,
    taskViewModel: com.timeflow.app.ui.task.TaskListViewModel = hiltViewModel()
) {
    // 获取热力图数据状态
    var heatmapData by remember { mutableStateOf<List<HeatmapDayData>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    // 异步加载热力图数据 - 基于实际完成任务
    LaunchedEffect(goalId) {
        try {
            isLoading = true
            Log.d("GoalProgressHeatmapSection", "开始加载目标 $goalId 的热力图数据")
            // 使用基于实际任务数据的生成函数
            heatmapData = generateHeatmapData(goalId, taskViewModel)
            Log.d("GoalProgressHeatmapSection", "热力图数据加载完成，共 ${heatmapData.size} 天数据")
        } catch (e: Exception) {
            Log.e("GoalProgressHeatmapSection", "加载热力图数据失败", e)
            heatmapData = emptyList()
        } finally {
            isLoading = false
        }
    }
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = cardBackgroundColor,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 1.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 缩小内边距
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Timeline,
                        contentDescription = null,
                        tint = primaryColor,
                        modifier = Modifier.size(18.dp) // 缩小图标
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Text(
                        text = "进度热力图",
                        fontSize = 14.sp, // 缩小字体
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1C1C1E)
                    )
                }
                
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(14.dp),
                        strokeWidth = 2.dp,
                        color = primaryColor
                    )
                } else {
                    Text(
                        text = "最近30天",
                        fontSize = 10.sp, // 缩小字体
                        color = Color(0xFF8E8E93)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (isLoading) {
                // 加载状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                        .background(
                            Color(0xFF8E8E93).copy(alpha = 0.1f),
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "加载热力图数据中...",
                        fontSize = 12.sp,
                        color = Color(0xFF8E8E93)
                    )
                }
            } else if (heatmapData.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Timeline,
                            contentDescription = null,
                            tint = Color(0xFF8E8E93),
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "暂无数据",
                            fontSize = 12.sp,
                            color = Color(0xFF8E8E93)
                        )
                        Text(
                            text = "完成习惯和任务后会显示进度热力图",
                            fontSize = 10.sp,
                            color = Color(0xFF8E8E93),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            } else {
                // 热力图网格
                HeatmapGrid(
                    data = heatmapData,
                    primaryColor = primaryColor
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 图例
                HeatmapLegend(primaryColor = primaryColor)
            }
        }
    }
}

/**
 * 热力图网格组件
 */
@Composable
private fun HeatmapGrid(
    data: List<HeatmapDayData>,
    primaryColor: Color,
    modifier: Modifier = Modifier
) {
    val habitColor = Color(0xFFFF6B35) // 橙色代表习惯
    val taskColor = primaryColor // 紫色代表任务
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
    ) {
        val cellSize = size.width / 30f // 30天的数据
        val cellHeight = size.height / 4f // 4行
        
        data.forEachIndexed { index, dayData ->
            val x = (index % 30) * cellSize
            val y = 0f
            
            // 绘制习惯贡献度（上半部分）
            drawRect(
                color = habitColor.copy(alpha = dayData.habitContribution),
                topLeft = Offset(x, y),
                size = Size(cellSize * 0.9f, cellHeight * 1.5f)
            )
            
            // 绘制任务贡献度（下半部分）
            drawRect(
                color = taskColor.copy(alpha = dayData.taskContribution),
                topLeft = Offset(x, y + cellHeight * 1.5f),
                size = Size(cellSize * 0.9f, cellHeight * 1.5f)
            )
        }
    }
}

/**
 * 热力图图例组件
 */
@Composable
private fun HeatmapLegend(
    primaryColor: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        LegendItem(
            color = Color(0xFFFF6B35),
            label = "习惯贡献",
            fontSize = 10.sp
        )
        LegendItem(
            color = primaryColor,
            label = "任务贡献",
            fontSize = 10.sp
        )
    }
}

/**
 * 图例项组件
 */
@Composable
private fun LegendItem(
    color: Color,
    label: String,
    fontSize: androidx.compose.ui.unit.TextUnit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, RoundedCornerShape(2.dp))
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = label,
            fontSize = fontSize,
            color = Color(0xFF8E8E93)
        )
    }
}

/**
 * 生成热力图数据（基于实际完成任务）
 */
private suspend fun generateHeatmapData(
    goalId: String,
    taskViewModel: com.timeflow.app.ui.task.TaskListViewModel
): List<HeatmapDayData> {
    val today = LocalDate.now()

    return try {
        Log.d("GoalProgressHeatmap", "开始生成目标 $goalId 的热力图数据")

        // 获取与目标关联的所有任务 - 使用更安全的方式
        val associatedTasks = try {
            taskViewModel.getTasksByGoalId(goalId).first()
        } catch (e: Exception) {
            Log.w("GoalProgressHeatmap", "获取关联任务失败，使用空列表: $e")
            emptyList()
        }
        Log.d("GoalProgressHeatmap", "获取到 ${associatedTasks.size} 个关联任务")

        // 生成30天的数据
        (0 until 30).map { dayOffset ->
            val date = today.minusDays(dayOffset.toLong())

            // 计算该日期完成的任务数量
            val completedTasksOnDate = associatedTasks.filter { task ->
                task.completedAt?.toLocalDate() == date
            }

            // 根据完成任务数量计算贡献度
            val taskContribution = when (completedTasksOnDate.size) {
                0 -> 0f
                1 -> 0.3f
                2 -> 0.6f
                3 -> 0.8f
                else -> 1.0f // 4个或以上任务完成
            }

            // 暂时将习惯贡献度设为0，后续可以接入习惯数据
            val habitContribution = 0f

            Log.d("GoalProgressHeatmap", "日期 $date: 完成 ${completedTasksOnDate.size} 个任务, 贡献度 $taskContribution")

            HeatmapDayData(
                date = date,
                habitContribution = habitContribution,
                taskContribution = taskContribution
            )
        }.reversed()
    } catch (e: Exception) {
        Log.e("GoalDetailScreen", "生成热力图数据失败", e)
        // 返回空数据作为降级处理
        (0 until 30).map { dayOffset ->
            val date = today.minusDays(dayOffset.toLong())
            HeatmapDayData(
                date = date,
                habitContribution = 0f,
                taskContribution = 0f
            )
        }.reversed()
    }
}

@Composable
fun GoalHeader(
    goal: Goal,
    cardBackgroundColor: Color,
    onToggleCompleted: (Boolean) -> Unit
) {
    val dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                cardBackgroundColor,
                RoundedCornerShape(16.dp)
            )
            .padding(14.dp), // 缩小内边距
        verticalArrangement = Arrangement.spacedBy(6.dp) // 缩小间距
    ) {
        // 标题和状态
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = goal.title,
                fontSize = 16.sp, // 缩小字体
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1C1C1E),
                modifier = Modifier.weight(1f)
            )
            
            // 优先级标识
            PriorityIndicator(priority = goal.priority)
        }
        
        // 描述
        if (goal.description.isNotBlank()) {
            Text(
                text = goal.description,
                fontSize = 12.sp, // 缩小字体
                color = Color(0xFF8E8E93),
                lineHeight = 16.sp // 缩小行高
            )
        }
        
        // 日期信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 创建日期
            Column {
                Text(
                    text = "创建日期",
                    fontSize = 10.sp, // 缩小字体
                    color = Color(0xFF8E8E93)
                )
                Text(
                    text = goal.createdAt.format(dateFormat),
                    fontSize = 11.sp, // 缩小字体
                    color = Color(0xFF1C1C1E),
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 截止日期
            goal.dueDate?.let { dueDate ->
                Column {
                    Text(
                        text = "截止日期",
                        fontSize = 10.sp, // 缩小字体
                        color = Color(0xFF8E8E93)
                    )
                    Text(
                        text = dueDate.format(dateFormat),
                        fontSize = 11.sp, // 缩小字体
                        fontWeight = FontWeight.Medium,
                        color = if (dueDate.isBefore(LocalDateTime.now()) && goal.completedAt == null)
                            Color(0xFFFF3B30)
                        else
                            Color(0xFF1C1C1E)
                    )
                }
            }
        }
    }
}

@Composable
fun PriorityIndicator(priority: GoalPriority) {
    val color = when (priority) {
        GoalPriority.LOW -> Color(0xFF4CAF50)
        GoalPriority.MEDIUM -> Color(0xFF2196F3)
        GoalPriority.HIGH -> Color(0xFFFF9800)
        GoalPriority.URGENT -> Color(0xFFF44336)
    }
    
    val text = when (priority) {
        GoalPriority.LOW -> "低"
        GoalPriority.MEDIUM -> "中"
        GoalPriority.HIGH -> "高"
        GoalPriority.URGENT -> "紧急"
    }
    
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(color)
            .border(1.dp, color, CircleShape),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = Color.White,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun StatusChip(status: String) {
    val chipColor = when (status) {
        "已完成" -> MaterialTheme.colorScheme.primaryContainer
        "进行中" -> MaterialTheme.colorScheme.secondaryContainer
        "待完成" -> MaterialTheme.colorScheme.tertiaryContainer
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
    
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = chipColor,
        modifier = Modifier.padding(end = 8.dp)
    ) {
        Text(
            text = status,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
fun GoalProgressSection(
    progress: Float,
    cardBackgroundColor: Color,
    primaryColor: Color,
    onProgressChange: (Float) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                cardBackgroundColor,
                RoundedCornerShape(16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 标题
        Text(
            text = "目标进度",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 进度百分比显示
        Text(
            text = "${(progress * 100).toInt()}%",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.align(Alignment.End)
        )
        
        // 进度条
        Slider(
            value = progress,
            onValueChange = onProgressChange,
            valueRange = 0f..1f,
            steps = 19, // 5% 一步
            modifier = Modifier.fillMaxWidth(),
            colors = SliderDefaults.colors(
                thumbColor = primaryColor,
                activeTrackColor = primaryColor,
                inactiveTrackColor = Color(0xFFE0E0E0)
            )
        )
    }
}

@Composable
fun GoalSubTasksSection(
    subTasks: List<GoalSubTask>,
    cardBackgroundColor: Color,
    primaryColor: Color,
    onSubTaskClick: (GoalSubTask) -> Unit,
    onAddSubTaskClick: () -> Unit,
    onRequestAiBreakdown: () -> Unit,
    hasAiBreakdown: Boolean,
    onDeleteSubTask: (GoalSubTask) -> Unit,
    onEditSubTask: (GoalSubTask) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                cardBackgroundColor,
                RoundedCornerShape(16.dp)
            )
            .padding(14.dp), // 缩小内边距
        verticalArrangement = Arrangement.spacedBy(10.dp) // 缩小间距
    ) {
        // 标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "子目标",
                fontSize = 14.sp, // 缩小字体
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1C1C1E)
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // AI拆解按钮
                if (!hasAiBreakdown) {
                    Button(
                        onClick = onRequestAiBreakdown,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = primaryColor
                        ),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Icon(
                            Icons.Filled.Psychology,
                            contentDescription = "AI拆解",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "AI拆解",
                            fontSize = 12.sp // 缩小字体
                        )
                    }
                }
                
                // 添加子目标按钮
                IconButton(onClick = onAddSubTaskClick) {
                    Icon(
                        Icons.Filled.Add, 
                        contentDescription = "添加子目标",
                        tint = primaryColor
                    )
                }
            }
        }
        
        // 子目标列表
        if (subTasks.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (hasAiBreakdown) "暂无子目标" else "点击\"AI拆解\"按钮，让AI帮您拆分目标",
                    fontSize = 12.sp, // 缩小字体
                    color = Color.Gray
                )
            }
        } else {
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp) // 缩小子目标之间的间距
            ) {
                subTasks.forEach { subTask ->
                    SubTaskItem(
                        subTask = subTask, 
                        primaryColor = primaryColor,
                        onClick = { onSubTaskClick(subTask) },
                        onDeleteSubTask = { onDeleteSubTask(subTask) },
                        onEditSubTask = { onEditSubTask(subTask) }
                    )
                }
            }
        }
    }
}

@Composable
fun SubTaskItem(
    subTask: GoalSubTask,
    primaryColor: Color,
    onClick: () -> Unit,
    onDeleteSubTask: (GoalSubTask) -> Unit,
    onEditSubTask: (GoalSubTask) -> Unit
) {
    val isCompleted = subTask.completedAt != null
    val hapticFeedback = LocalHapticFeedback.current
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                Color(0xFFFEF9F4), // 修改为指定的背景色 #fef9f4
                RoundedCornerShape(8.dp)
            )
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = {
                        // 长按删除
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        showDeleteConfirmation = true
                    },
                    onTap = { offset ->
                        // 检测点击位置
                        val itemWidth = size.width
                        val checkboxWidth = 48.dp.toPx() // 复选框区域宽度
                        
                        if (offset.x < checkboxWidth) {
                            // 点击复选框区域 - 切换完成状态
                            onClick()
                        } else {
                            // 点击中心区域 - 编辑子目标
                            onEditSubTask(subTask)
                        }
                    }
                )
            }
            .padding(10.dp), // 缩小内边距
        horizontalArrangement = Arrangement.spacedBy(8.dp), // 缩小水平间距
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 完成状态图标
        Checkbox(
            checked = isCompleted,
            onCheckedChange = { onClick() },
            colors = CheckboxDefaults.colors(
                checkedColor = primaryColor,
                uncheckedColor = Color.Gray
            )
        )
        
        // 目标内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 标题
            Text(
                text = subTask.title,
                fontSize = 13.sp, // 缩小字体
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1C1C1E),
                textDecoration = if (isCompleted) androidx.compose.ui.text.style.TextDecoration.LineThrough else null
            )
            
            // 描述
            if (subTask.description.isNotBlank()) {
                Text(
                    text = subTask.description,
                    fontSize = 11.sp, // 缩小字体
                    color = Color.Gray,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 2
                )
            }
            
            // AI建议
            subTask.aiRecommendation?.let { recommendation ->
                Text(
                    text = "AI: $recommendation",
                    fontSize = 10.sp, // 缩小字体
                    color = primaryColor,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
        
        // 预计天数
        if (subTask.estimatedDurationDays > 0) {
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${subTask.estimatedDurationDays}天",
                    fontSize = 10.sp, // 缩小字体
                    color = Color.Gray
                )
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("删除子目标") },
            text = { Text("确定要删除「${subTask.title}」吗？此操作无法撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDeleteSubTask(subTask)
                        showDeleteConfirmation = false
                    }
                ) {
                    Text("删除", color = Color.Red)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
fun GoalAiAnalysisSection(
    goal: Goal,
    cardBackgroundColor: Color,
    primaryColor: Color,
    onRequestAiAnalysis: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                cardBackgroundColor,
                RoundedCornerShape(16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 标题
        Text(
            text = "AI 分析",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        // AI建议
        if (goal.aiRecommendations.isNotEmpty()) {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "个性化建议",
                    style = MaterialTheme.typography.titleSmall
                )
                
                goal.aiRecommendations.forEach { recommendation ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        Icon(
                            Icons.Filled.Lightbulb,
                            contentDescription = null,
                            tint = primaryColor,
                            modifier = Modifier.size(16.dp)
                        )
                        Text(
                            text = recommendation,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
        
        // 效率指标
        if (goal.metrics.isNotEmpty()) {
            Column(
                modifier = Modifier.padding(top = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "效率指标",
                    style = MaterialTheme.typography.titleSmall
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    goal.metrics.forEach { (name, value) ->
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "${(value * 100).toInt()}%",
                                style = MaterialTheme.typography.titleLarge,
                                color = primaryColor
                            )
                            Text(
                                text = name,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        }
        
        // 最佳时间段
        if (goal.bestTimeSlots.isNotEmpty()) {
            Column(
                modifier = Modifier.padding(top = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "最佳时间段",
                    style = MaterialTheme.typography.titleSmall
                )
                
                goal.bestTimeSlots.forEach { timeSlot ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                Color(0xFFF0F0FF),
                                RoundedCornerShape(8.dp)
                            )
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Filled.Schedule,
                                contentDescription = null,
                                tint = primaryColor
                            )
                            Column {
                                Text(
                                    text = "${timeSlot.dayOfWeek} ${timeSlot.startHour}:00-${timeSlot.endHour}:00",
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = timeSlot.description,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                        
                        // 效率值
                        Text(
                            text = "+${(timeSlot.efficiency * 100).toInt()}%",
                            style = MaterialTheme.typography.titleSmall,
                            color = primaryColor
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RequestAiAnalysisButton(
    onRequestAiAnalysis: () -> Unit,
    primaryColor: Color
) {
    Button(
        onClick = onRequestAiAnalysis,
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color(0xFFE9EFFF),
            contentColor = primaryColor
        )
    ) {
        Icon(
            Icons.Filled.Analytics,
            contentDescription = "AI分析"
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text("生成AI个性化分析")
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddSubTaskDialog(
    onDismiss: () -> Unit,
    onAddSubTask: (String, String, Int) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var estimatedDaysText by remember { mutableStateOf("") }
    var estimatedDays by remember { mutableStateOf(0) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("添加子目标", fontSize = 14.sp, fontWeight = FontWeight.SemiBold, letterSpacing = (-0.3).sp)
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("目标标题", fontSize = 11.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = 12.sp, letterSpacing = (-0.2).sp)
                )
                
                // 描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("目标描述", fontSize = 11.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = 12.sp, letterSpacing = (-0.2).sp)
                )
                
                // 预计天数输入
                OutlinedTextField(
                    value = estimatedDaysText,
                    onValueChange = { 
                        estimatedDaysText = it
                        estimatedDays = it.toIntOrNull() ?: 0
                    },
                    label = { Text("预计完成天数", fontSize = 11.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = 12.sp, letterSpacing = (-0.2).sp)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (title.isNotBlank()) {
                        onAddSubTask(title, description, estimatedDays)
                    }
                },
                enabled = title.isNotBlank()
            ) {
                Text("添加", fontSize = 11.sp, letterSpacing = (-0.2).sp)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 11.sp, letterSpacing = (-0.2).sp)
            }
        },
        containerColor = Color.White
    )
}

@Composable
fun AssociatedTasksSection(
    goalId: String,
    cardBackgroundColor: Color,
    primaryColor: Color,
    taskViewModel: com.timeflow.app.ui.task.TaskListViewModel = hiltViewModel()
) {
    var associatedTasks by remember { mutableStateOf<List<com.timeflow.app.data.model.Task>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var showAllTasks by remember { mutableStateOf(false) }
    
    // 🔧 获取关联任务 - 实时数据同步
    LaunchedEffect(goalId) {
        try {
            Log.d("AssociatedTasksSection", "开始监听目标关联任务: goalId=$goalId")
            // 获取与目标关联的任务
            taskViewModel.getTasksByGoalId(goalId).collect { tasks ->
                // 🧹 清理任务描述中的颜色信息
                val cleanedTasks = tasks.map { task ->
                    if (task.description.contains("\"color\":")) {
                        val cleanedDescription = task.description
                            .replace(Regex("\\s*\\{\"color\":\\d+\\}\\s*"), "")
                            .trim()
                        task.copy(description = cleanedDescription)
                    } else {
                        task
                    }
                }
                associatedTasks = cleanedTasks
                isLoading = false
                Log.d("AssociatedTasksSection", "✓ 获取到 ${cleanedTasks.size} 个关联任务（已清理颜色信息）")
            }
        } catch (e: Exception) {
            Log.e("AssociatedTasksSection", "获取关联任务失败", e)
            isLoading = false
        }
    }
    
    // 🔧 监听任务更新事件，实时同步
    DisposableEffect(goalId) {
        val listener = object : com.timeflow.app.util.EventListener<Any> {
            override fun onEvent(event: Any) {
                when (event) {
                    is TaskRefreshEvent -> {
                        Log.d("AssociatedTasksSection", "📢 收到任务刷新事件，刷新关联任务")
                        // 触发重新加载关联任务
                        kotlinx.coroutines.GlobalScope.launch {
                            try {
                                val refreshedTasks = taskViewModel.getTasksByGoalId(goalId)
                                refreshedTasks.first().let { tasks ->
                                    // 🧹 清理任务描述中的颜色信息
                                    val cleanedTasks = tasks.map { task ->
                                        if (task.description.contains("\"color\":")) {
                                            val cleanedDescription = task.description
                                                .replace(Regex("\\s*\\{\"color\":\\d+\\}\\s*"), "")
                                                .trim()
                                            task.copy(description = cleanedDescription)
                                        } else {
                                            task
                                        }
                                    }
                                    associatedTasks = cleanedTasks
                                    Log.d("AssociatedTasksSection", "✓ 事件触发刷新完成，任务数量: ${cleanedTasks.size}（已清理颜色信息）")
                                }
                            } catch (e: Exception) {
                                Log.e("AssociatedTasksSection", "事件触发刷新失败", e)
                            }
                        }
                    }
                }
            }
        }
        
        // 注册事件监听器
        NotificationCenter.observe(Any::class.java, listener)
        
        // 清理监听器
        onDispose {
            NotificationCenter.remove(Any::class.java, listener)
        }
    }
    
    // 计算显示的任务（根据显示模式过滤）
    val displayTasks = remember(associatedTasks, showAllTasks) {
        if (showAllTasks) {
            associatedTasks
        } else {
            // 默认只显示前3个任务
            associatedTasks.take(3)
        }
    }
    
    // 计算统计信息
    val taskStats = remember(associatedTasks) {
        val completed = associatedTasks.count { it.completedAt != null }
        val total = associatedTasks.size
        val progress = if (total > 0) (completed.toFloat() / total) else 0f
        Triple(completed, total, progress)
    }
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = cardBackgroundColor,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 1.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp) // 缩小内边距
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Outlined.Timeline,
                    contentDescription = null,
                    tint = primaryColor,
                    modifier = Modifier.size(18.dp) // 缩小图标
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                Text(
                    text = "关联任务",
                    fontSize = 14.sp, // 缩小字体
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1C1C1E)
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(14.dp), // 缩小加载指示器
                        strokeWidth = 2.dp,
                        color = primaryColor
                    )
                } else {
                    // 显示任务统计和进度
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        // 完成进度指示器
                        if (taskStats.second > 0) {
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .clip(CircleShape)
                                    .background(primaryColor.copy(alpha = 0.1f)),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    progress = taskStats.third,
                                    modifier = Modifier.size(12.dp),
                                    strokeWidth = 2.dp,
                                    color = primaryColor,
                                    trackColor = Color.Transparent
                                )
                            }
                        }
                        
                        Column(
                            horizontalAlignment = Alignment.End
                        ) {
                            Text(
                                text = "${taskStats.first}/${taskStats.second} 完成",
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Medium,
                                color = if (taskStats.first == taskStats.second && taskStats.second > 0) {
                                    Color(0xFF4CAF50)
                                } else {
                                    Color(0xFF8E8E93)
                                }
                            )
                            if (taskStats.second > 0) {
                                Text(
                                    text = "${(taskStats.third * 100).toInt()}%",
                                    fontSize = 8.sp,
                                    color = primaryColor,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (isLoading) {
                // 加载状态
                repeat(3) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .background(
                                    Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Transparent,
                                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f),
                                            Color.Transparent
                                        )
                                    )
                                )
                        )
                    }
                }
            } else if (associatedTasks.isEmpty()) {
                // 空状态
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Task,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                        modifier = Modifier.size(40.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "暂无关联任务",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF8E8E93)
                    )
                    
                    Text(
                        text = "在任务详情中关联此目标来创建任务连接",
                        fontSize = 10.sp,
                        color = Color(0xFF8E8E93),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
                    )
                }
            } else {
                // 任务列表
                Column {
                    displayTasks.forEach { task ->
                        AssociatedTaskItem(
                            task = task,
                            primaryColor = primaryColor,
                            onTaskClick = { clickedTask ->
                                // TODO: 导航到任务详情页面
                                Log.d("AssociatedTasksSection", "点击任务: ${clickedTask.title}")
                            },
                            onToggleComplete = { taskToToggle ->
                                // 切换任务完成状态
                                Log.d("AssociatedTasksSection", "切换任务完成状态: ${taskToToggle.title}")
                                val isCompleted = taskToToggle.completedAt != null
                                taskViewModel.updateTaskStatus(taskToToggle.id, !isCompleted)
                            },
                            modifier = Modifier.padding(vertical = 3.dp)
                        )
                    }
                    
                    // 展开/收起按钮
                    if (associatedTasks.size > 3) {
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showAllTasks = !showAllTasks },
                            color = primaryColor.copy(alpha = 0.05f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = if (showAllTasks) "收起" else "显示全部 ${associatedTasks.size} 个任务",
                                    fontSize = 11.sp,
                                    color = primaryColor,
                                    fontWeight = FontWeight.Medium
                                )
                                
                                Spacer(modifier = Modifier.width(4.dp))
                                
                                Icon(
                                    imageVector = if (showAllTasks) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                    contentDescription = if (showAllTasks) "收起" else "展开",
                                    tint = primaryColor,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun AssociatedTaskItem(
    task: com.timeflow.app.data.model.Task,
    primaryColor: Color,
    onTaskClick: (com.timeflow.app.data.model.Task) -> Unit = {},
    onToggleComplete: (com.timeflow.app.data.model.Task) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val isCompleted = task.completedAt != null
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onTaskClick(task) },
        color = if (isCompleted) {
            Color(0xFF8E8E93).copy(alpha = 0.1f)
        } else {
            Color.White
        },
        shape = RoundedCornerShape(12.dp),
        shadowElevation = 0.dp  // 移除阴影效果
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp), // 减少内边距从10dp到8dp
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成状态指示器 - 可点击切换
            Surface(
                modifier = Modifier
                    .size(20.dp)
                    .clickable { onToggleComplete(task) },
                color = if (isCompleted) {
                    Color(0xFF4CAF50)
                } else {
                    primaryColor.copy(alpha = 0.2f)
                },
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    if (isCompleted) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "标记为未完成",
                            tint = Color.White,
                            modifier = Modifier.size(12.dp)
                        )
                    } else {
                        // 未完成状态显示空圆圈
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .clip(CircleShape)
                                .background(Color.Transparent)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp)) // 减少间距从10dp到8dp
            
            // 任务信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (isCompleted) {
                        Color(0xFF8E8E93)
                    } else {
                        Color(0xFF1C1C1E)
                    },
                    textDecoration = if (isCompleted) TextDecoration.LineThrough else null,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 任务状态和时间信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧：描述或时间信息
                    Column(
                        modifier = Modifier.weight(1f, fill = false)
                    ) {
                        if (task.description.isNotEmpty()) {
                            Text(
                                text = task.description,
                                fontSize = 10.sp,
                                color = Color(0xFF8E8E93),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.padding(top = 0.dp) // 减少顶部间距
                            )
                        }
                        
                        // 显示截止时间信息
                        task.dueDate?.let { dueDate ->
                            val dueDateText = remember(dueDate) {
                                val now = java.time.LocalDateTime.now()
                                when {
                                    dueDate.isBefore(now) -> "已逾期"
                                    dueDate.toLocalDate() == now.toLocalDate() -> "今天到期"
                                    dueDate.toLocalDate() == now.toLocalDate().plusDays(1) -> "明天到期"
                                    else -> dueDate.format(DateTimeFormatter.ofPattern("MM/dd HH:mm"))
                                }
                            }
                            
                            Text(
                                text = dueDateText,
                                fontSize = 9.sp,
                                color = if (task.dueDate?.isBefore(java.time.LocalDateTime.now()) == true && !isCompleted) {
                                    Color(0xFFFF3B30) // 逾期用红色
                                } else {
                                    Color(0xFF8E8E93)
                                },
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(top = 0.dp) // 减少顶部间距
                            )
                        }
                        
                        // 如果任务已完成，显示完成时间
                        if (isCompleted && task.completedAt != null) {
                            val completedText = remember(task.completedAt) {
                                val completedAt = task.completedAt!!
                                val now = java.time.LocalDateTime.now()
                                val days = java.time.temporal.ChronoUnit.DAYS.between(completedAt.toLocalDate(), now.toLocalDate())
                                when {
                                    days == 0L -> "今天完成"
                                    days == 1L -> "昨天完成"
                                    days < 7L -> "${days}天前完成"
                                    else -> completedAt.format(DateTimeFormatter.ofPattern("MM/dd"))
                                }
                            }
                            
                            Text(
                                text = completedText,
                                fontSize = 8.sp,
                                color = Color(0xFF4CAF50),
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(top = 0.dp) // 减少顶部间距
                            )
                        }
                    }
                }
            }
            
            // 右侧：优先级指示器
            val priorityColor = when (task.priority) {
                com.timeflow.app.data.model.Priority.URGENT -> Color(0xFFFF5722)
                com.timeflow.app.data.model.Priority.HIGH -> Color(0xFFFF9800)
                com.timeflow.app.data.model.Priority.MEDIUM -> Color(0xFFFFC107)
                com.timeflow.app.data.model.Priority.LOW -> Color(0xFF4CAF50)
                null -> Color(0xFF9E9E9E)
            }
            
            Surface(
                modifier = Modifier.size(20.dp),
                color = priorityColor.copy(alpha = 0.1f),
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = when (task.priority) {
                            com.timeflow.app.data.model.Priority.URGENT -> "!"
                            com.timeflow.app.data.model.Priority.HIGH -> "H"
                            com.timeflow.app.data.model.Priority.MEDIUM -> "M"
                            com.timeflow.app.data.model.Priority.LOW -> "L"
                            null -> "?"
                        },
                        fontSize = 9.sp,
                        fontWeight = FontWeight.Bold,
                        color = priorityColor
                    )
                }
            }
        }
    }
}

/**
 * 支撑习惯区域组件
 * 显示与目标关联的习惯列表 - 使用真实数据
 */
@Composable
private fun SupportingHabitsSection(
    goalId: String,
    cardBackgroundColor: Color,
    primaryColor: Color,
    habitViewModel: com.timeflow.app.ui.viewmodel.HabitViewModel = hiltViewModel()
) {
    // 获取与目标关联的习惯数据
    var supportingHabits by remember { mutableStateOf<List<HabitModel>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    
    // 加载关联习惯数据
    LaunchedEffect(goalId) {
        try {
            Log.d("SupportingHabitsSection", "🔍 开始加载目标关联习惯: goalId=$goalId")

            // 🧪 调试：检查所有习惯的目标关联情况
            habitViewModel.debugAllHabitsGoalAssociation()

            // 🧪 添加测试调用 - 使用当前目标ID进行测试
            habitViewModel.testGoalAssociation(goalId)

            // 获取与目标关联的习惯
            habitViewModel.getHabitsByGoalId(goalId).collect { habits ->
                Log.d("SupportingHabitsSection", "✅ 获取到 ${habits.size} 个关联习惯")
                habits.forEachIndexed { index, habit ->
                    Log.d("SupportingHabitsSection", "习惯${index + 1}: ${habit.name} (ID: ${habit.id}, 关联目标ID: ${habit.relatedGoalId})")
                }
                supportingHabits = habits
                isLoading = false
            }
        } catch (e: Exception) {
            Log.e("SupportingHabitsSection", "❌ 加载关联习惯失败", e)
            isLoading = false
        }
    }
    
    // 🔥 监听习惯删除/归档事件，实现同步更新
    DisposableEffect(goalId) {
        val listener = object : com.timeflow.app.util.EventListener<Any> {
            override fun onEvent(event: Any) {
                when (event) {
                    is HabitDeletedEvent -> {
                        // 习惯删除事件：如果是当前目标关联的习惯，重新加载列表
                        if (event.relatedGoalId == goalId) {
                            Log.d("SupportingHabitsSection", "🗑️ 习惯删除事件: habitId=${event.habitId}, 关联目标=$goalId - 同步更新")
                            isLoading = true
                            // 过滤掉已删除的习惯
                            supportingHabits = supportingHabits.filter { it.id != event.habitId }
                            isLoading = false
                        }
                    }
                    is HabitArchivedEvent -> {
                        // 习惯归档事件：如果是当前目标关联的习惯，重新加载列表
                        if (event.relatedGoalId == goalId) {
                            Log.d("SupportingHabitsSection", "📦 习惯归档事件: habitId=${event.habitId}, 关联目标=$goalId - 同步更新")
                            isLoading = true
                            // 过滤掉已归档的习惯
                            supportingHabits = supportingHabits.filter { it.id != event.habitId }
                            isLoading = false
                        }
                    }
                    is HabitStatusChangedEvent -> {
                        // 通用习惯状态变更事件
                        if (event.relatedGoalId == goalId) {
                            Log.d("SupportingHabitsSection", "🔄 习惯状态变更: habitId=${event.habitId}, 类型=${event.changeType}, 关联目标=$goalId")
                            when (event.changeType) {
                                com.timeflow.app.util.HabitChangeType.DELETED,
                                com.timeflow.app.util.HabitChangeType.ARCHIVED -> {
                                    // 删除或归档：从列表中移除
                                    supportingHabits = supportingHabits.filter { it.id != event.habitId }
                                }
                                com.timeflow.app.util.HabitChangeType.RESTORED -> {
                                    // 恢复：重新加载整个列表（因为需要从数据库获取最新数据）
                                    isLoading = true
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 注册事件监听器
        NotificationCenter.observe(Any::class.java, listener)
        
        // 清理监听器
        onDispose {
            NotificationCenter.remove(Any::class.java, listener)
        }
    }
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = cardBackgroundColor,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 1.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 缩小内边距
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Repeat,
                        contentDescription = null,
                        tint = Color(0xFFFF6B35), // 橙红色，代表习惯的火焰
                        modifier = Modifier.size(18.dp) // 缩小图标
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Text(
                        text = "支撑习惯",
                        fontSize = 14.sp, // 缩小字体
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1C1C1E)
                    )
                }
                
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(14.dp),
                        strokeWidth = 2.dp,
                        color = primaryColor
                    )
                } else {
                    Text(
                        text = "${supportingHabits.size} 个习惯",
                        fontSize = 10.sp, // 缩小字体
                        color = Color(0xFF8E8E93)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (isLoading) {
                // 加载状态
                repeat(2) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp)
                            .padding(vertical = 4.dp)
                            .background(
                                Color(0xFF8E8E93).copy(alpha = 0.1f),
                                RoundedCornerShape(8.dp)
                            )
                    )
                }
            } else if (supportingHabits.isEmpty()) {
                // 空状态
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 20.dp), // 缩小内边距
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Repeat,
                        contentDescription = null,
                        tint = Color(0xFF8E8E93),
                        modifier = Modifier.size(40.dp) // 缩小图标
                    )
                    
                    Spacer(modifier = Modifier.height(6.dp))
                    
                    Text(
                        text = "暂无支撑习惯",
                        fontSize = 12.sp, // 缩小字体
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF8E8E93)
                    )
                    
                    Text(
                        text = "添加规律性行动来支撑目标达成",
                        fontSize = 10.sp, // 缩小字体
                        color = Color(0xFF8E8E93),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            } else {
                // 习惯列表
                supportingHabits.forEach { habit ->
                    RealSupportingHabitItem(
                        habit = habit,
                        primaryColor = primaryColor,
                        modifier = Modifier.padding(vertical = 4.dp) // 缩小间距
                    )
                }

                // 🧪 调试信息显示
                if (supportingHabits.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "🧪 调试: 成功显示 ${supportingHabits.size} 个关联习惯",
                        fontSize = 10.sp,
                        color = Color(0xFF4CAF50),
                        modifier = Modifier.padding(4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 真实支撑习惯项组件 - 使用HabitModel数据
 */
@Composable
private fun RealSupportingHabitItem(
    habit: HabitModel,
    primaryColor: Color,
    modifier: Modifier = Modifier
) {
    val today = LocalDate.now()
    val isCompletedToday = habit.isCompletedOnDate(today)
    val completionRate = habit.getCompletionRate(30)
    
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 习惯emoji和状态
        Box(
            modifier = Modifier
                .size(36.dp) // 缩小尺寸
                .clip(CircleShape)
                .background(
                    if (isCompletedToday) {
                        Color(0xFF34C759).copy(alpha = 0.1f) // 绿色背景
                    } else {
                        Color(0xFF8E8E93).copy(alpha = 0.1f) // 灰色背景
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = habit.icon,
                fontSize = 16.sp // 缩小字体
            )
            
            // 完成状态指示器
            if (isCompletedToday) {
                Box(
                    modifier = Modifier
                        .size(10.dp) // 缩小尺寸
                        .clip(CircleShape)
                        .background(Color(0xFF34C759))
                        .align(Alignment.BottomEnd),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(6.dp) // 缩小图标
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.width(10.dp))
        
        // 习惯信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = habit.name,
                    fontSize = 12.sp, // 缩小字体
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1C1C1E)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                // 连续天数标识
                if (habit.currentStreak > 0) {
                    Surface(
                        color = Color(0xFFFF6B35).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(6.dp) // 缩小圆角
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 4.dp, vertical = 1.dp), // 缩小内边距
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "🔥",
                                fontSize = 8.sp // 缩小字体
                            )
                            Spacer(modifier = Modifier.width(1.dp))
                            Text(
                                text = "${habit.currentStreak}天",
                                fontSize = 8.sp, // 缩小字体
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFFFF6B35)
                            )
                        }
                    }
                }
            }
            
            Row(
                modifier = Modifier.padding(top = 1.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = habit.frequencyType.displayName,
                    fontSize = 9.sp, // 缩小字体
                    color = Color(0xFF8E8E93)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                Text(
                    text = "•",
                    fontSize = 9.sp, // 缩小字体
                    color = Color(0xFF8E8E93)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                Text(
                    text = "完成率 ${(completionRate * 100).toInt()}%",
                    fontSize = 9.sp, // 缩小字体
                    color = if (completionRate > 0.7f) Color(0xFF34C759) else Color(0xFF8E8E93)
                )
            }
        }
        
        // 操作按钮
        IconButton(
            onClick = { /* 导航到习惯详情 */ },
            modifier = Modifier.size(28.dp) // 缩小按钮
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "查看习惯详情",
                tint = Color(0xFF8E8E93),
                modifier = Modifier.size(14.dp) // 缩小图标
            )
        }
    }
}

/**
 * 支撑习惯数据类
 */
data class SupportingHabit(
    val id: String,
    val name: String,
    val emoji: String,
    val streakDays: Int,
    val lastCompletedDate: String,
    val isCompletedToday: Boolean,
    val frequency: String
)

/**
 * 编辑子目标对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditSubTaskDialog(
    subTask: GoalSubTask,
    onDismiss: () -> Unit,
    onUpdateSubTask: (GoalSubTask) -> Unit
) {
    var title by remember { mutableStateOf(subTask.title) }
    var description by remember { mutableStateOf(subTask.description) }
    var estimatedDays by remember { mutableStateOf(subTask.estimatedDurationDays.toString()) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题
                Text(
                    text = "编辑子目标",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1C1C1E)
                )
                
                // 子目标标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("子目标标题") },
                    placeholder = { Text("请输入子目标标题") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        focusedLabelColor = DustyLavender,
                        cursorColor = DustyLavender
                    )
                )
                
                // 描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述（可选）") },
                    placeholder = { Text("请输入子目标描述") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3,
                    maxLines = 5,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        focusedLabelColor = DustyLavender,
                        cursorColor = DustyLavender
                    )
                )
                
                // 预计天数输入
                OutlinedTextField(
                    value = estimatedDays,
                    onValueChange = { newValue ->
                        // 只允许输入数字
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            estimatedDays = newValue
                        }
                    },
                    label = { Text("预计天数") },
                    placeholder = { Text("0") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    suffix = { Text("天") },
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        focusedLabelColor = DustyLavender,
                        cursorColor = DustyLavender
                    )
                )
                
                // 按钮区域
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF8E8E93)
                        ),
                        border = BorderStroke(1.dp, Color(0xFFE5E5EA))
                    ) {
                        Text("取消")
                    }
                    
                    // 保存按钮
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                val updatedSubTask = subTask.copy(
                                    title = title.trim(),
                                    description = description.trim(),
                                    estimatedDurationDays = estimatedDays.toIntOrNull() ?: 0
                                )
                                onUpdateSubTask(updatedSubTask)
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = DustyLavender,
                            contentColor = Color.White
                        )
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}