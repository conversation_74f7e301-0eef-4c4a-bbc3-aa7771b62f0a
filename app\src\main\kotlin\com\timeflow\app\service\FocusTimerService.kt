package com.timeflow.app.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import com.timeflow.app.ui.timetracking.TimerState
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 专注计时前台服务
 * 参照Forest、番茄专注等知名应用的通知栏设计
 * 提供持久化计时通知，支持锁屏显示和通知栏操作
 */
@AndroidEntryPoint
class FocusTimerService : Service() {
    
    companion object {
        private const val TAG = "FocusTimerService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "focus_timer_channel"
        private const val CHANNEL_NAME = "专注计时"
        private const val CHANNEL_DESCRIPTION = "显示专注计时状态和进度"
        
        // 操作常量
        const val ACTION_START_TIMER = "START_TIMER"
        const val ACTION_PAUSE_TIMER = "PAUSE_TIMER"
        const val ACTION_RESUME_TIMER = "RESUME_TIMER"
        const val ACTION_STOP_TIMER = "STOP_TIMER"
        const val ACTION_SHOW_APP = "SHOW_APP"
        
        // Intent额外数据
        const val EXTRA_TASK_NAME = "task_name"
        const val EXTRA_TIMER_TYPE = "timer_type"
        
        // SharedPreferences键（与TimeTrackingViewModel保持一致）
        private const val PREF_TIMER_STATE = "timer_state"
        private const val PREF_TIMER_START_TIME = "timer_start_time"
        private const val PREF_ELAPSED_TIME = "elapsed_time"
        private const val PREF_CURRENT_TASK_ID = "current_task_id"
    }
    
    @Inject
    lateinit var sharedPreferences: SharedPreferences
    
    private var notificationManager: NotificationManager? = null
    private var serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // 计时状态
    private val _timerState = MutableStateFlow(TimerState.IDLE)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    private val _elapsedTime = MutableStateFlow(0L)
    val elapsedTime: StateFlow<Long> = _elapsedTime.asStateFlow()
    
    private val _currentTaskName = MutableStateFlow("专注任务")
    val currentTaskName: StateFlow<String> = _currentTaskName.asStateFlow()
    
    private var timerStartTime: Long = 0L
    private var updateJob: Job? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🎯 专注计时服务启动")
        
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        createNotificationChannel()
        
        // 启动状态同步
        startStateSync()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "收到服务命令: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_TIMER -> {
                val taskName = intent.getStringExtra(EXTRA_TASK_NAME) ?: "专注任务"
                startForegroundTimer(taskName)
            }
            ACTION_PAUSE_TIMER -> pauseTimer()
            ACTION_RESUME_TIMER -> resumeTimer()
            ACTION_STOP_TIMER -> stopTimerAndService()
            ACTION_SHOW_APP -> showApp()
            else -> {
                // 默认启动前台服务
                startForegroundTimer(_currentTaskName.value)
            }
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🧹 专注计时服务销毁")
        updateJob?.cancel()
        serviceScope.cancel()
    }
    
    /**
     * 创建通知渠道（Android 8.0+）
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW // 低重要性，避免声音打扰
            ).apply {
                description = CHANNEL_DESCRIPTION
                setShowBadge(false) // 不显示角标
                enableLights(false) // 不闪灯
                enableVibration(false) // 不振动
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC // 锁屏可见
            }
            
            notificationManager?.createNotificationChannel(channel)
            Log.d(TAG, "✅ 通知渠道已创建")
        }
    }
    
    /**
     * 启动前台计时器
     */
    private fun startForegroundTimer(taskName: String) {
        Log.d(TAG, "🚀 启动前台计时器: $taskName")
        
        _currentTaskName.value = taskName
        
        // 同步当前状态
        syncFromSharedPreferences()
        
        // 创建并显示前台通知
        val notification = createTimerNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        // 启动定时更新
        startTimerUpdate()
    }
    
    /**
     * 暂停计时器
     */
    private fun pauseTimer() {
        Log.d(TAG, "⏸️ 暂停计时器")
        
        // 更新本地状态
        _timerState.value = TimerState.PAUSED
        
        // 保存当前经过时间
        val currentElapsedTime = _elapsedTime.value
        
        // 将状态保存到SharedPreferences，让TimeTrackingViewModel知道
        with(sharedPreferences.edit()) {
            putString(PREF_TIMER_STATE, TimerState.PAUSED.name)
            putLong(PREF_ELAPSED_TIME, currentElapsedTime)
            putLong(PREF_TIMER_START_TIME, 0L) // 清除开始时间
            apply()
        }
        
        Log.d(TAG, "✅ 计时器已暂停，经过时间: ${currentElapsedTime}秒")
        
        // 停止定时更新
        updateJob?.cancel()
        
        // 更新通知显示
        updateNotification()
    }
    
    /**
     * 恢复计时器
     */
    private fun resumeTimer() {
        Log.d(TAG, "▶️ 恢复计时器")
        
        // 更新本地状态
        _timerState.value = TimerState.RUNNING
        
        // 计算新的开始时间（当前时间 - 已经过时间）
        val currentTime = System.currentTimeMillis()
        val resumeStartTime = currentTime - (_elapsedTime.value * 1000)
        timerStartTime = resumeStartTime
        
        // 将状态保存到SharedPreferences
        with(sharedPreferences.edit()) {
            putString(PREF_TIMER_STATE, TimerState.RUNNING.name)
            putLong(PREF_TIMER_START_TIME, resumeStartTime)
            apply()
        }
        
        Log.d(TAG, "✅ 计时器已恢复，开始时间: $resumeStartTime")
        
        // 重新启动定时更新
        startTimerUpdate()
        
        // 更新通知显示
        updateNotification()
    }
    
    /**
     * 停止计时器并关闭服务
     */
    private fun stopTimerAndService() {
        Log.d(TAG, "⏹️ 停止计时器并关闭服务")
        
        // 更新本地状态
        _timerState.value = TimerState.IDLE
        _elapsedTime.value = 0L
        
        // 将状态保存到SharedPreferences
        with(sharedPreferences.edit()) {
            putString(PREF_TIMER_STATE, TimerState.IDLE.name)
            putLong(PREF_ELAPSED_TIME, 0L)
            putLong(PREF_TIMER_START_TIME, 0L)
            apply()
        }
        
        Log.d(TAG, "✅ 计时器已停止")
        
        // 停止定时更新
        updateJob?.cancel()
        
        // 停止前台服务
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    /**
     * 显示应用
     */
    private fun showApp() {
        Log.d(TAG, "📱 显示应用")
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("navigate_to", "time_tracking") // 导航到计时页面
        }
        startActivity(intent)
    }
    
    /**
     * 从SharedPreferences同步状态
     */
    private fun syncFromSharedPreferences() {
        try {
            val savedState = sharedPreferences.getString(PREF_TIMER_STATE, TimerState.IDLE.name)
            val savedStartTime = sharedPreferences.getLong(PREF_TIMER_START_TIME, 0L)
            val savedElapsedTime = sharedPreferences.getLong(PREF_ELAPSED_TIME, 0L)
            
            when (savedState) {
                TimerState.RUNNING.name -> {
                    if (savedStartTime > 0) {
                        _timerState.value = TimerState.RUNNING
                        timerStartTime = savedStartTime
                        // 计算实时经过时间
                        val currentTime = System.currentTimeMillis()
                        _elapsedTime.value = (currentTime - savedStartTime) / 1000
                    }
                }
                TimerState.PAUSED.name -> {
                    _timerState.value = TimerState.PAUSED
                    _elapsedTime.value = savedElapsedTime
                }
                else -> {
                    _timerState.value = TimerState.IDLE
                    _elapsedTime.value = 0L
                }
            }
            
            Log.d(TAG, "✅ 状态同步完成: ${_timerState.value}, ${_elapsedTime.value}秒")
        } catch (e: Exception) {
            Log.e(TAG, "同步状态失败", e)
        }
    }
    
    /**
     * 启动状态同步任务
     */
    private fun startStateSync() {
        serviceScope.launch {
            while (isActive) {
                delay(1000) // 每秒同步一次
                syncFromSharedPreferences()
                
                // 如果计时器空闲，停止服务
                if (_timerState.value == TimerState.IDLE) {
                    Log.d(TAG, "计时器空闲，停止前台服务")
                    stopForeground(STOP_FOREGROUND_REMOVE)
                    stopSelf()
                    break
                }
                
                // 更新通知
                updateNotification()
            }
        }
    }
    
    /**
     * 启动计时器更新任务
     */
    private fun startTimerUpdate() {
        updateJob?.cancel()
        updateJob = serviceScope.launch {
            while (isActive && _timerState.value == TimerState.RUNNING) {
                delay(1000)
                if (timerStartTime > 0) {
                    val currentTime = System.currentTimeMillis()
                    _elapsedTime.value = (currentTime - timerStartTime) / 1000
                }
            }
        }
    }
    
    /**
     * 创建计时器通知
     */
    private fun createTimerNotification(): Notification {
        val timeText = formatTime(_elapsedTime.value)
        val statusText = when (_timerState.value) {
            TimerState.RUNNING -> "正在专注"
            TimerState.PAUSED -> "已暂停"
            TimerState.IDLE -> "空闲"
        }
        
        // 主要操作按钮
        val mainAction = if (_timerState.value == TimerState.RUNNING) {
            createNotificationAction(
                R.drawable.ic_timer_pause, // 暂停图标
                "暂停",
                ACTION_PAUSE_TIMER
            )
        } else {
            createNotificationAction(
                R.drawable.ic_timer_play, // 开始图标
                "继续",
                ACTION_RESUME_TIMER
            )
        }
        
        val stopAction = createNotificationAction(
            R.drawable.ic_timer_stop, // 停止图标
            "停止",
            ACTION_STOP_TIMER
        )
        
        // 点击通知打开应用
        val contentIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("navigate_to", "time_tracking")
            },
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("🎯 ${_currentTaskName.value}")
            .setContentText("$statusText · $timeText")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setColor(ContextCompat.getColor(this, R.color.primary_color))
            .setOngoing(true) // 持续通知，无法被滑动删除
            .setAutoCancel(false)
            .setShowWhen(false)
            .setContentIntent(contentIntent)
            .addAction(mainAction)
            .addAction(stopAction)
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("$statusText · $timeText\n轻触查看详情"))
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_STOPWATCH)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // 锁屏可见
            .build()
    }
    
    /**
     * 更新通知
     */
    private fun updateNotification() {
        try {
            val notification = createTimerNotification()
            notificationManager?.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }
    
    /**
     * 创建通知操作
     */
    private fun createNotificationAction(icon: Int, title: String, action: String): NotificationCompat.Action {
        val intent = Intent(action).apply {
            setPackage(packageName)
        }
        val pendingIntent = PendingIntent.getBroadcast(
            this,
            action.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Action.Builder(icon, title, pendingIntent).build()
    }
    
    /**
     * 格式化时间显示
     */
    private fun formatTime(totalSeconds: Long): String {
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return if (hours > 0) {
            String.format("%d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }
} 