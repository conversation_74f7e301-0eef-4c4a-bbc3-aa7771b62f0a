package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.Goal
import com.timeflow.app.data.entity.GoalSubTask
import kotlinx.coroutines.flow.Flow

@Dao
interface GoalDao {
    // 目标相关操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGoal(goal: Goal)

    @Update
    suspend fun updateGoal(goal: Goal)

    @Delete
    suspend fun deleteGoal(goal: Goal)

    @Query("DELETE FROM goals WHERE id = :goalId")
    suspend fun deleteGoalById(goalId: String)

    @Query("SELECT * FROM goals WHERE id = :goalId")
    suspend fun getGoalById(goalId: String): Goal?

    @Query("SELECT * FROM goals ORDER BY updatedAt DESC")
    fun getAllGoals(): Flow<List<Goal>>
    
    @Query("SELECT * FROM goals ORDER BY updatedAt DESC")
    suspend fun getAllGoalsList(): List<Goal>

    @Query("SELECT * FROM goals WHERE status = :status ORDER BY updatedAt DESC")
    fun getGoalsByStatus(status: String): Flow<List<Goal>>

    @Query("SELECT * FROM goals WHERE categoryId = :categoryId ORDER BY updatedAt DESC")
    fun getGoalsByCategory(categoryId: String): Flow<List<Goal>>

    @Query("SELECT * FROM goals WHERE categoryId = :categoryId AND status = :status ORDER BY updatedAt DESC")
    fun getGoalsByCategoryAndStatus(categoryId: String, status: String): Flow<List<Goal>>

    @Query("SELECT DISTINCT categoryId FROM goals ORDER BY categoryId")
    suspend fun getAllUsedCategories(): List<String>

    @Query("SELECT * FROM goals WHERE completedAt IS NULL ORDER BY dueDate ASC")
    fun getActiveGoals(): Flow<List<Goal>>

    @Query("SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC")
    fun getCompletedGoals(): Flow<List<Goal>>

    @Query("SELECT * FROM goals WHERE hasAiBreakdown = 0 ORDER BY createdAt DESC")
    fun getGoalsWithoutAiBreakdown(): Flow<List<Goal>>

    @Query("SELECT * FROM goals WHERE hasAiAnalysis = 0 AND completedAt IS NOT NULL ORDER BY completedAt DESC")
    fun getGoalsNeedingReview(): Flow<List<Goal>>

    // 子任务相关操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSubTask(subTask: GoalSubTask)

    @Update
    suspend fun updateSubTask(subTask: GoalSubTask)

    @Delete
    suspend fun deleteSubTask(subTask: GoalSubTask)

    @Query("DELETE FROM goal_subtasks WHERE id = :subTaskId")
    suspend fun deleteSubTaskById(subTaskId: String)

    @Query("SELECT * FROM goal_subtasks WHERE goalId = :goalId ORDER BY createdAt ASC")
    suspend fun getSubTasksForGoal(goalId: String): List<GoalSubTask>

    @Query("SELECT * FROM goal_subtasks ORDER BY createdAt ASC")
    suspend fun getAllSubTasksList(): List<GoalSubTask>

    @Query("SELECT COUNT(*) FROM goal_subtasks WHERE goalId = :goalId")
    suspend fun countSubTasksForGoal(goalId: String): Int

    @Query("SELECT COUNT(*) FROM goal_subtasks WHERE goalId = :goalId AND completedAt IS NOT NULL")
    suspend fun countCompletedSubTasksForGoal(goalId: String): Int

    // 批量操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAllSubTasks(subTasks: List<GoalSubTask>)

    @Query("DELETE FROM goal_subtasks WHERE goalId = :goalId")
    suspend fun deleteAllSubTasksForGoal(goalId: String)

    // 统计查询
    @Query("SELECT COUNT(*) FROM goals")
    suspend fun getTotalGoalsCount(): Int

    @Query("SELECT COUNT(*) FROM goals WHERE hasAiBreakdown = 1")
    suspend fun getAiBreakdownGoalsCount(): Int

    @Query("SELECT COUNT(*) FROM goal_subtasks")
    suspend fun getTotalSubTasksCount(): Int
    
    @Query("SELECT COUNT(*) FROM goals WHERE completedAt IS NOT NULL AND updatedAt > :since")
    suspend fun getReviewReportsCount(since: Long): Int

    @Query("SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC LIMIT :limit")
    suspend fun getCompletedGoalsByLimit(limit: Int): List<Goal>
    
    @Query("SELECT * FROM goals WHERE hasAiAnalysis = 1 ORDER BY updatedAt DESC LIMIT :limit")
    suspend fun getGoalsWithAiAnalysis(limit: Int): List<Goal>

    /**
     * 获取所有已完成的目标列表(非Flow版本)
     */
    @Query("SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC")
    suspend fun getCompletedGoalsList(): List<Goal>
    
    /**
     * 获取正在进行中的目标数量
     */
    @Query("SELECT COUNT(*) FROM goals WHERE completedAt IS NULL")
    suspend fun getInProgressGoalsCount(): Int
} 