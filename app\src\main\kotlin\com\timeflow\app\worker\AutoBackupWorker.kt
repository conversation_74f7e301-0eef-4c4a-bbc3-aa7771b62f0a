package com.timeflow.app.worker

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.timeflow.app.utils.DatabaseBackupManager
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

/**
 * 自动备份工作器
 * 用于定期执行数据库自动备份任务
 */
@HiltWorker
class AutoBackupWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val backupManager: DatabaseBackupManager
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        private const val TAG = "AutoBackupWorker"
        private const val WORK_NAME = "auto_backup_work"
        
        /**
         * 根据备份设置安排自动备份任务
         */
        fun scheduleAutoBackup(context: Context) {
            val backupManager = DatabaseBackupManager(context)
            val settings = backupManager.getBackupSettings()
            
            // 如果自动备份未启用，则取消现有的备份任务
            if (!settings.isAutoBackupEnabled) {
                cancelAutoBackup(context)
                return
            }
            
            // 根据频率设置重复间隔
            val repeatInterval = when (settings.autoBackupFrequency) {
                "daily" -> 1L
                "weekly" -> 7L
                "monthly" -> 30L
                else -> 7L // 默认每周
            }
            
            val timeUnit = when (settings.autoBackupFrequency) {
                "daily", "weekly" -> TimeUnit.DAYS
                "monthly" -> TimeUnit.DAYS
                else -> TimeUnit.DAYS
            }
            
            // 创建周期性工作请求
            val workRequest = PeriodicWorkRequestBuilder<AutoBackupWorker>(repeatInterval, timeUnit)
                .setConstraints(
                    Constraints.Builder()
                        .setRequiresBatteryNotLow(true) // 电池电量不低时执行
                        .build()
                )
                .build()
            
            // 使用唯一工作名称安排工作，确保只有一个备份任务在运行
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.REPLACE, // 替换现有的工作
                    workRequest
                )
            
            Log.d(TAG, "已安排自动备份任务，频率: ${settings.autoBackupFrequency}")
        }
        
        /**
         * 取消自动备份任务
         */
        fun cancelAutoBackup(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
            Log.d(TAG, "已取消自动备份任务")
        }
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始执行自动备份工作...")
            
            // 检查是否需要自动备份
            if (backupManager.shouldAutoBackup()) {
                Log.i(TAG, "执行自动备份...")
                val success = backupManager.backupDatabase()
                
                if (success) {
                    Log.i(TAG, "自动备份成功完成")
                    Result.success()
                } else {
                    Log.e(TAG, "自动备份失败")
                    Result.retry()
                }
            } else {
                Log.d(TAG, "当前不需要执行自动备份")
                Result.success()
            }
        } catch (e: Exception) {
            Log.e(TAG, "自动备份过程中发生错误", e)
            Result.failure()
        }
    }
}