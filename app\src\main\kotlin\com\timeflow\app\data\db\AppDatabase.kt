package com.timeflow.app.data.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.timeflow.app.data.dao.AppUsageDao
import com.timeflow.app.data.dao.KanbanBoardDao
import com.timeflow.app.data.dao.KanbanColumnDao
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.dao.CycleDao
import com.timeflow.app.data.dao.GoalDao
import com.timeflow.app.data.dao.GoalTemplateDao
import com.timeflow.app.data.dao.ReflectionDao
import com.timeflow.app.data.dao.TimeSessionDao
import com.timeflow.app.data.dao.WishDao
import com.timeflow.app.data.dao.HabitDao
import com.timeflow.app.data.dao.MedicationRecordDao
import com.timeflow.app.data.dao.EmotionRecordDao
import com.timeflow.app.data.entity.AppUsageEntity
import com.timeflow.app.data.entity.KanbanBoard
import com.timeflow.app.data.entity.KanbanColumn
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.entity.TaskTag
import com.timeflow.app.data.entity.TaskClosure
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.entity.SymptomRecord
import com.timeflow.app.data.entity.Goal
import com.timeflow.app.data.entity.GoalSubTask
import com.timeflow.app.data.entity.GoalTemplate
import com.timeflow.app.data.entity.GoalSubTaskTemplate
import com.timeflow.app.data.entity.ReflectionEntity
import com.timeflow.app.data.entity.Wish
import com.timeflow.app.data.model.TimeSession
import com.timeflow.app.data.entity.Habit
import com.timeflow.app.data.entity.HabitRecord
import com.timeflow.app.data.entity.HabitReminder
import com.timeflow.app.data.entity.MedicationRecord
import com.timeflow.app.data.entity.EmotionRecordEntity
import com.timeflow.app.data.converter.Converters
import com.timeflow.app.data.converter.ListStringConverter
import com.timeflow.app.data.converter.LocalDateConverter

/**
 * 版本3到版本4的迁移：添加任务顺序和分组字段
 */
val MIGRATION_3_4 = object : Migration(3, 4) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 添加新列
        database.execSQL("ALTER TABLE tasks ADD COLUMN groupId TEXT")
        database.execSQL("ALTER TABLE tasks ADD COLUMN groupType TEXT")
        database.execSQL("ALTER TABLE tasks ADD COLUMN columnId TEXT")
        // orderIndex已存在，不需要添加
    }
}

/**
 * 版本4到版本5的迁移：新增看板相关表和修改任务表
 */
val MIGRATION_4_5 = object : Migration(4, 5) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建看板表
        database.execSQL("CREATE TABLE IF NOT EXISTS kanban_boards (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "name TEXT NOT NULL, " +
                "description TEXT, " +
                "color TEXT, " +
                "icon TEXT, " +
                "created_at INTEGER, " +
                "updated_at INTEGER)")
        
        // 创建看板列表
        database.execSQL("CREATE TABLE IF NOT EXISTS kanban_columns (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "board_id TEXT NOT NULL, " +
                "name TEXT NOT NULL, " +
                "position INTEGER NOT NULL, " +
                "color TEXT, " +
                "created_at INTEGER, " +
                "updated_at INTEGER, " +
                "FOREIGN KEY (board_id) REFERENCES kanban_boards(id) ON DELETE CASCADE)")
        
        // 重命名现有任务表为临时表
        database.execSQL("ALTER TABLE tasks RENAME TO tasks_old")
        
        // 创建新的任务表
        database.execSQL("CREATE TABLE tasks (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT, " +
                "column_id TEXT, " +
                "position INTEGER NOT NULL DEFAULT 0, " +
                "priority INTEGER NOT NULL DEFAULT 0, " +
                "due_date INTEGER, " +
                "created_at INTEGER, " +
                "updated_at INTEGER, " +
                "completed_at INTEGER, " +
                "FOREIGN KEY (column_id) REFERENCES kanban_columns(id) ON DELETE CASCADE)")
        
        // 从旧表复制数据到新表(不复制数据，因为格式不兼容)
        
        // 删除旧表
        database.execSQL("DROP TABLE tasks_old")
    }
}

/**
 * 版本5到版本6的迁移：修复tasks表结构与实体类不匹配问题
 */
val MIGRATION_5_6 = object : Migration(5, 6) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建带有正确结构的临时表
        database.execSQL("CREATE TABLE tasks_new (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT, " +
                "column_id TEXT NOT NULL, " +  // 将column_id设为非空
                "position INTEGER NOT NULL, " +
                "priority INTEGER NOT NULL, " +
                "due_date INTEGER, " +
                "created_at INTEGER NOT NULL, " + // 将created_at设为非空
                "updated_at INTEGER NOT NULL, " + // 将updated_at设为非空
                "completed_at INTEGER, " +
                "parent_task_id TEXT, " +
                "depth INTEGER NOT NULL DEFAULT 0, " +
                "status TEXT NOT NULL DEFAULT '待办', " +
                "has_subtasks INTEGER NOT NULL DEFAULT 0, " +
                "progress REAL NOT NULL DEFAULT 0, " +
                "order_index INTEGER NOT NULL DEFAULT 0, " +
                "FOREIGN KEY (column_id) REFERENCES kanban_columns(id) ON DELETE CASCADE)")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_tasks_column_id ON tasks_new(column_id)")
        
        // 尝试复制数据，使用默认值填充新增的必填字段
        try {
            database.execSQL(
                "INSERT INTO tasks_new (id, title, description, column_id, position, priority, " +
                        "due_date, created_at, updated_at, completed_at, status) " +
                "SELECT id, title, description, IFNULL(column_id, ''), position, priority, " +
                        "due_date, IFNULL(created_at, strftime('%s','now')*1000), " +
                        "IFNULL(updated_at, strftime('%s','now')*1000), " +
                        "completed_at, '待办' FROM tasks"
            )
        } catch (e: Exception) {
            // 如果复制失败，只创建新表结构
        }
        
        // 删除旧表
        database.execSQL("DROP TABLE tasks")
        
        // 重命名新表
        database.execSQL("ALTER TABLE tasks_new RENAME TO tasks")
    }
}

/**
 * 版本6到版本7的迁移：添加Task的parent_task_id外键约束
 */
val MIGRATION_6_7 = object : Migration(6, 7) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建临时表，包含Task的parent_task_id外键约束
        database.execSQL("CREATE TABLE tasks_new (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT, " +
                "column_id TEXT NOT NULL, " + 
                "position INTEGER NOT NULL, " +
                "priority INTEGER NOT NULL, " +
                "due_date INTEGER, " +
                "created_at INTEGER NOT NULL, " +
                "updated_at INTEGER NOT NULL, " +
                "completed_at INTEGER, " +
                "parent_task_id TEXT, " +
                "depth INTEGER NOT NULL DEFAULT 0, " +
                "status TEXT NOT NULL DEFAULT '待办', " +
                "has_subtasks INTEGER NOT NULL DEFAULT 0, " +
                "progress REAL NOT NULL DEFAULT 0, " +
                "order_index INTEGER NOT NULL DEFAULT 0, " +
                "FOREIGN KEY (column_id) REFERENCES kanban_columns(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (parent_task_id) REFERENCES tasks_new(id) ON DELETE CASCADE)")
        
        // 先删除可能存在的索引，避免"index already exists"错误
        database.execSQL("DROP INDEX IF EXISTS index_tasks_column_id")
        database.execSQL("DROP INDEX IF EXISTS index_tasks_parent_task_id")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_tasks_column_id ON tasks_new(column_id)")
        database.execSQL("CREATE INDEX index_tasks_parent_task_id ON tasks_new(parent_task_id)")
        
        // 复制数据到新表
        database.execSQL(
            "INSERT INTO tasks_new SELECT * FROM tasks"
        )
        
        // 删除旧表
        database.execSQL("DROP TABLE tasks")
        
        // 重命名新表
        database.execSQL("ALTER TABLE tasks_new RENAME TO tasks")
    }
}

/**
 * 版本7到版本8的迁移：添加app_usage表
 */
val MIGRATION_7_8 = object : Migration(7, 8) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建应用使用统计表
        database.execSQL("CREATE TABLE IF NOT EXISTS app_usage (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                "package_name TEXT NOT NULL, " +
                "date TEXT NOT NULL, " +
                "duration_ms INTEGER NOT NULL, " +
                "launch_count INTEGER NOT NULL, " +
                "category TEXT NOT NULL, " +
                "is_productivity INTEGER NOT NULL)")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_app_usage_date ON app_usage(date)")
        database.execSQL("CREATE INDEX index_app_usage_package_name ON app_usage(package_name)")
    }
}

/**
 * 版本8到版本9的迁移：添加Goal相关表
 */
val MIGRATION_8_9 = object : Migration(8, 9) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建目标表
        database.execSQL("CREATE TABLE IF NOT EXISTS goals (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT NOT NULL DEFAULT '', " +
                "start_date INTEGER NOT NULL, " +
                "due_date INTEGER, " +
                "created_at INTEGER NOT NULL, " +
                "updated_at INTEGER NOT NULL, " +
                "completed_at INTEGER, " +
                "progress REAL NOT NULL DEFAULT 0, " +
                "priority TEXT NOT NULL DEFAULT 'MEDIUM', " +
                "has_ai_breakdown INTEGER NOT NULL DEFAULT 0, " +
                "has_ai_analysis INTEGER NOT NULL DEFAULT 0, " +
                "related_task_ids TEXT NOT NULL DEFAULT '', " +
                "ai_recommendations_json TEXT NOT NULL DEFAULT '[]', " +
                "tags TEXT NOT NULL DEFAULT '', " +
                "status TEXT NOT NULL DEFAULT '进行中', " +
                "best_time_slots_json TEXT NOT NULL DEFAULT '[]', " +
                "metrics_json TEXT NOT NULL DEFAULT '{}', " +
                "review_frequency TEXT NOT NULL DEFAULT 'WEEKLY')")
        
        // 创建目标子任务表
        database.execSQL("CREATE TABLE IF NOT EXISTS goal_subtasks (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "goal_id TEXT NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT NOT NULL DEFAULT '', " +
                "estimated_duration_days INTEGER NOT NULL DEFAULT 0, " +
                "completed_at INTEGER, " +
                "created_at INTEGER NOT NULL, " +
                "ai_recommendation TEXT, " +
                "status TEXT NOT NULL DEFAULT '待开始', " +
                "FOREIGN KEY (goal_id) REFERENCES goals(id) ON DELETE CASCADE)")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_goals_status ON goals(status)")
        database.execSQL("CREATE INDEX index_goal_subtasks_goal_id ON goal_subtasks(goal_id)")
    }
}

/**
 * 版本9到版本10的迁移：添加感想表
 */
val MIGRATION_9_10 = object : Migration(9, 10) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建感想表
        database.execSQL("CREATE TABLE IF NOT EXISTS reflections (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "content TEXT NOT NULL, " +
                "rich_content_json TEXT NOT NULL DEFAULT '[]', " +
                "date INTEGER NOT NULL, " +
                "rating INTEGER NOT NULL DEFAULT 3, " +
                "tags_json TEXT NOT NULL DEFAULT '[]', " +
                "type TEXT NOT NULL DEFAULT 'LIFE', " +
                "mood TEXT NOT NULL DEFAULT 'CALM', " +
                "plans_json TEXT NOT NULL DEFAULT '[]', " +
                "background_image TEXT, " +
                "metrics_json TEXT NOT NULL DEFAULT '{}', " +
                "created_at INTEGER NOT NULL DEFAULT (strftime('%s','now')*1000), " +
                "updated_at INTEGER NOT NULL DEFAULT (strftime('%s','now')*1000), " +
                "task_id TEXT, " +
                "task_title TEXT, " +
                "is_from_task_completion INTEGER NOT NULL DEFAULT 0)")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_reflections_date ON reflections(date)")
        database.execSQL("CREATE INDEX index_reflections_type ON reflections(type)")
        database.execSQL("CREATE INDEX index_reflections_mood ON reflections(mood)")
        database.execSQL("CREATE INDEX index_reflections_task_id ON reflections(task_id)")
        database.execSQL("CREATE INDEX index_reflections_is_from_task_completion ON reflections(is_from_task_completion)")
    }
}

/**
 * 版本10到版本11的迁移：添加时间会话表
 */
val MIGRATION_10_11 = object : Migration(10, 11) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建时间会话表
        database.execSQL("CREATE TABLE IF NOT EXISTS time_sessions (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "taskId TEXT NOT NULL, " +
                "taskName TEXT NOT NULL, " +
                "startTime INTEGER NOT NULL, " +
                "endTime INTEGER, " +
                "duration INTEGER NOT NULL, " +
                "timerType TEXT NOT NULL, " +
                "isCompleted INTEGER NOT NULL DEFAULT 0, " +
                "notes TEXT NOT NULL DEFAULT '', " +
                "createdAt INTEGER NOT NULL, " +
                "updatedAt INTEGER NOT NULL, " +
                "tags TEXT NOT NULL DEFAULT '', " +
                "focusRating INTEGER, " +
                "productivityRating INTEGER)")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_time_sessions_taskId ON time_sessions(taskId)")
        database.execSQL("CREATE INDEX index_time_sessions_startTime ON time_sessions(startTime)")
        database.execSQL("CREATE INDEX index_time_sessions_isCompleted ON time_sessions(isCompleted)")
    }
}

/**
 * 版本11到版本12的迁移：添加愿望表
 */
val MIGRATION_11_12 = object : Migration(11, 12) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建愿望表
        database.execSQL("CREATE TABLE IF NOT EXISTS wishes (" +
                "id TEXT PRIMARY KEY NOT NULL, " +
                "title TEXT NOT NULL, " +
                "description TEXT NOT NULL DEFAULT '', " +
                "category TEXT NOT NULL DEFAULT '其他', " +
                "priority INTEGER NOT NULL DEFAULT 3, " +
                "inspiration_items TEXT NOT NULL DEFAULT '[]', " +
                "estimated_cost REAL, " +
                "target_time_period TEXT NOT NULL DEFAULT '', " +
                "tags TEXT NOT NULL DEFAULT '', " +
                "status TEXT NOT NULL DEFAULT '活跃', " +
                "related_goal_id TEXT, " +
                "created_at INTEGER NOT NULL, " +
                "updated_at INTEGER NOT NULL, " +
                "achieved_at INTEGER, " +
                "archived_at INTEGER, " +
                "is_archived INTEGER NOT NULL DEFAULT 0, " +
                "difficulty TEXT NOT NULL DEFAULT '中等', " +
                "motivation TEXT NOT NULL DEFAULT '', " +
                "prerequisites TEXT NOT NULL DEFAULT '[]', " +
                "notes TEXT NOT NULL DEFAULT '')")
        
        // 创建索引
        database.execSQL("CREATE INDEX index_wishes_category ON wishes(category)")
        database.execSQL("CREATE INDEX index_wishes_status ON wishes(status)")
        database.execSQL("CREATE INDEX index_wishes_is_archived ON wishes(is_archived)")
        database.execSQL("CREATE INDEX index_wishes_priority ON wishes(priority)")
    }
}

val MIGRATION_12_13 = object : Migration(12, 13) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 添加图片字段到愿望表
        database.execSQL("ALTER TABLE wishes ADD COLUMN image_uris TEXT NOT NULL DEFAULT '[]'")
    }
}

val MIGRATION_13_14 = object : Migration(13, 14) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 添加日期手动修改标记字段到任务表
        database.execSQL("ALTER TABLE tasks ADD COLUMN date_manually_modified INTEGER NOT NULL DEFAULT 0")
    }
}

/**
 * 版本14到版本15的迁移：添加习惯相关表
 */
val MIGRATION_14_15 = object : Migration(14, 15) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建习惯表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS habits (
                id TEXT PRIMARY KEY NOT NULL,
                name TEXT NOT NULL,
                description TEXT NOT NULL DEFAULT '',
                iconName TEXT NOT NULL DEFAULT 'favorite',
                colorHex TEXT NOT NULL DEFAULT '#f5c4c4',
                category TEXT NOT NULL DEFAULT 'HEALTH',
                frequencyType TEXT NOT NULL DEFAULT 'DAILY',
                frequencyDays TEXT NOT NULL DEFAULT '',
                targetCount INTEGER NOT NULL DEFAULT 1,
                reminderEnabled INTEGER NOT NULL DEFAULT 0,
                reminderTime TEXT,
                fixedTime TEXT,
                difficulty TEXT NOT NULL DEFAULT 'MEDIUM',
                relatedGoalId TEXT,
                currentStreak INTEGER NOT NULL DEFAULT 0,
                longestStreak INTEGER NOT NULL DEFAULT 0,
                totalCompletions INTEGER NOT NULL DEFAULT 0,
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL,
                isActive INTEGER NOT NULL DEFAULT 1,
                customEmoji TEXT NOT NULL DEFAULT '',
                notes TEXT NOT NULL DEFAULT '',
                sortOrder INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY(relatedGoalId) REFERENCES goals(id) ON DELETE SET NULL
            )
        """)
        
        // 创建习惯记录表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS habit_records (
                id TEXT PRIMARY KEY NOT NULL,
                habitId TEXT NOT NULL,
                date INTEGER NOT NULL,
                completed INTEGER NOT NULL DEFAULT 0,
                completedAt INTEGER,
                completionCount INTEGER NOT NULL DEFAULT 1,
                notes TEXT NOT NULL DEFAULT '',
                skipReason TEXT NOT NULL DEFAULT '',
                mood TEXT NOT NULL DEFAULT '',
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL,
                FOREIGN KEY(habitId) REFERENCES habits(id) ON DELETE CASCADE
            )
        """)
        
        // 创建习惯提醒表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS habit_reminders (
                id TEXT PRIMARY KEY NOT NULL,
                habitId TEXT NOT NULL,
                time TEXT NOT NULL,
                days TEXT NOT NULL,
                enabled INTEGER NOT NULL DEFAULT 1,
                message TEXT NOT NULL DEFAULT '',
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL,
                FOREIGN KEY(habitId) REFERENCES habits(id) ON DELETE CASCADE
            )
        """)
        
        // 创建索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_habits_relatedGoalId ON habits(relatedGoalId)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_habit_records_habitId ON habit_records(habitId)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_habit_records_date ON habit_records(date)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_habit_reminders_habitId ON habit_reminders(habitId)")
    }
}

/**
 * 版本15到版本16的迁移：为CycleRecord添加时间字段
 */
val MIGRATION_15_16 = object : Migration(15, 16) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 为生理周期记录表添加开始时间和结束时间字段
        database.execSQL("ALTER TABLE cycle_records ADD COLUMN startTime TEXT")
        database.execSQL("ALTER TABLE cycle_records ADD COLUMN endTime TEXT")
    }
}

/**
 * 版本16到版本17的迁移：为Habit表添加customCategoryId字段，修复无限循环问题
 */
val MIGRATION_16_17 = object : Migration(16, 17) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 为习惯表添加customCategoryId字段
        database.execSQL("ALTER TABLE habits ADD COLUMN customCategoryId TEXT")
        
        // 🔧 根据现有的category字段，为现有习惯设置默认的customCategoryId
        database.execSQL("""
            UPDATE habits 
            SET customCategoryId = CASE 
                WHEN category = 'FITNESS' THEN 'default_fitness'
                WHEN category = 'LEARNING' THEN 'default_learning'
                WHEN category = 'WORK' THEN 'default_work'
                WHEN category = 'HEALTH' THEN 'default_health'
                WHEN category = 'LIFESTYLE' THEN 'default_lifestyle'
                WHEN category = 'SOCIAL' THEN 'default_social'
                WHEN category = 'MINDFULNESS' THEN 'default_mindfulness'
                WHEN category = 'FINANCE' THEN 'default_finance'
                WHEN category = 'CREATIVE' THEN 'default_creative'
                WHEN category = 'HOBBY' THEN 'default_hobby'
                ELSE 'default_other'
            END
            WHERE customCategoryId IS NULL
        """)
        
        // 记录迁移完成
        android.util.Log.i("Migration_16_17", "✅ 习惯表customCategoryId字段迁移完成")
    }
}

/**
 * 版本17到版本18的迁移：添加用药记录表
 */
val MIGRATION_17_18 = object : Migration(17, 18) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `medication_records` (
                `id` TEXT NOT NULL,
                `medicationId` TEXT NOT NULL,
                `recordDate` INTEGER NOT NULL,
                `completedAt` INTEGER NOT NULL,
                `status` TEXT NOT NULL,
                `notes` TEXT,
                PRIMARY KEY(`id`)
            )
        """)
        database.execSQL("CREATE INDEX IF NOT EXISTS index_medication_records_medicationId_recordDate ON medication_records(medicationId, recordDate)")
    }
}

/**
 * 版本18到版本19的迁移：为Goal表添加分类字段
 */
val MIGRATION_18_19 = object : Migration(18, 19) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 为目标表添加分类字段，默认值为个人提升分类
        database.execSQL("ALTER TABLE goals ADD COLUMN categoryId TEXT NOT NULL DEFAULT 'personal_development'")

        // 为现有目标根据其特征智能分配分类
        // 健康相关关键词
        database.execSQL("""
            UPDATE goals SET categoryId = 'health_fitness'
            WHERE LOWER(title) LIKE '%健康%'
               OR LOWER(title) LIKE '%运动%'
               OR LOWER(title) LIKE '%健身%'
               OR LOWER(title) LIKE '%减肥%'
               OR LOWER(title) LIKE '%锻炼%'
               OR LOWER(description) LIKE '%健康%'
               OR LOWER(description) LIKE '%运动%'
        """)

        // 学习相关关键词
        database.execSQL("""
            UPDATE goals SET categoryId = 'learning_growth'
            WHERE LOWER(title) LIKE '%学习%'
               OR LOWER(title) LIKE '%学会%'
               OR LOWER(title) LIKE '%掌握%'
               OR LOWER(title) LIKE '%技能%'
               OR LOWER(title) LIKE '%课程%'
               OR LOWER(description) LIKE '%学习%'
               OR LOWER(description) LIKE '%技能%'
        """)

        // 工作相关关键词
        database.execSQL("""
            UPDATE goals SET categoryId = 'career_work'
            WHERE LOWER(title) LIKE '%工作%'
               OR LOWER(title) LIKE '%职业%'
               OR LOWER(title) LIKE '%项目%'
               OR LOWER(title) LIKE '%升职%'
               OR LOWER(title) LIKE '%跳槽%'
               OR LOWER(description) LIKE '%工作%'
               OR LOWER(description) LIKE '%职业%'
        """)

        // 财务相关关键词
        database.execSQL("""
            UPDATE goals SET categoryId = 'finance_money'
            WHERE LOWER(title) LIKE '%理财%'
               OR LOWER(title) LIKE '%储蓄%'
               OR LOWER(title) LIKE '%投资%'
               OR LOWER(title) LIKE '%赚钱%'
               OR LOWER(title) LIKE '%收入%'
               OR LOWER(description) LIKE '%理财%'
               OR LOWER(description) LIKE '%储蓄%'
        """)

        // 根据目标持续时间智能分配时间维度分类
        // 年度目标（超过300天）
        database.execSQL("""
            UPDATE goals SET categoryId = 'yearly_goals'
            WHERE categoryId = 'personal_development'
              AND (julianday(dueDate) - julianday(startDate)) > 300
        """)

        // 季度目标（60-120天）
        database.execSQL("""
            UPDATE goals SET categoryId = 'quarterly_goals'
            WHERE categoryId = 'personal_development'
              AND (julianday(dueDate) - julianday(startDate)) BETWEEN 60 AND 120
        """)

        // 月度目标（20-40天）
        database.execSQL("""
            UPDATE goals SET categoryId = 'monthly_goals'
            WHERE categoryId = 'personal_development'
              AND (julianday(dueDate) - julianday(startDate)) BETWEEN 20 AND 40
        """)

        // Project50（45-55天）
        database.execSQL("""
            UPDATE goals SET categoryId = 'project50'
            WHERE categoryId = 'personal_development'
              AND (julianday(dueDate) - julianday(startDate)) BETWEEN 45 AND 55
        """)
    }
}

/**
 * 版本19到版本20的迁移：更新目标模板的分类字段
 */
val MIGRATION_19_20 = object : Migration(19, 20) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 为目标模板表添加新的分类字段
        database.execSQL("ALTER TABLE goal_templates ADD COLUMN categoryId TEXT NOT NULL DEFAULT 'personal_development'")

        // 根据现有的category字段智能映射到新的分类ID
        // 学习相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'learning_growth'
            WHERE LOWER(category) LIKE '%学习%'
               OR LOWER(category) LIKE '%学会%'
               OR LOWER(category) LIKE '%技能%'
               OR LOWER(category) LIKE '%课程%'
               OR category = 'LEARNING'
        """)

        // 健康相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'health_fitness'
            WHERE LOWER(category) LIKE '%健康%'
               OR LOWER(category) LIKE '%运动%'
               OR LOWER(category) LIKE '%健身%'
               OR LOWER(category) LIKE '%减肥%'
               OR category = 'EXERCISE'
               OR category = 'HEALTH'
        """)

        // 工作相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'career_work'
            WHERE LOWER(category) LIKE '%工作%'
               OR LOWER(category) LIKE '%职业%'
               OR LOWER(category) LIKE '%项目%'
               OR category = 'WORK'
               OR category = 'CAREER'
        """)

        // 财务相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'finance_money'
            WHERE LOWER(category) LIKE '%理财%'
               OR LOWER(category) LIKE '%储蓄%'
               OR LOWER(category) LIKE '%投资%'
               OR category = 'FINANCE'
        """)

        // 阅读相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'learning_growth'
            WHERE LOWER(category) LIKE '%阅读%'
               OR LOWER(category) LIKE '%读书%'
               OR category = 'READING'
        """)

        // 创意相关
        database.execSQL("""
            UPDATE goal_templates SET categoryId = 'hobbies_interests'
            WHERE LOWER(category) LIKE '%创意%'
               OR LOWER(category) LIKE '%艺术%'
               OR category = 'CREATIVE'
        """)
    }
}

/**
 * 🔧 版本20到版本21的迁移：添加情绪记录表（安全迁移，保护现有数据）
 */
val MIGRATION_20_21 = object : Migration(20, 21) {
    override fun migrate(database: SupportSQLiteDatabase) {
        try {
            // 🔧 创建情绪记录表 - 这是新表，不会影响现有数据
            // 注意：列顺序必须与Room生成的期望顺序一致
            database.execSQL("""
                CREATE TABLE IF NOT EXISTS emotion_records (
                    id TEXT NOT NULL PRIMARY KEY,
                    date TEXT NOT NULL,
                    emotion_type TEXT NOT NULL,
                    triggers TEXT NOT NULL,
                    mindfulness_note TEXT NOT NULL,
                    image_uri TEXT,
                    audio_uri TEXT,
                    is_detailed INTEGER NOT NULL,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL
                )
            """)

            // 🔧 创建索引以提高查询性能（必须与Entity定义一致）
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_date ON emotion_records(date)")
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_emotion_type ON emotion_records(emotion_type)")
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_is_detailed ON emotion_records(is_detailed)")

            android.util.Log.d("AppDatabase", "✅ 情绪记录表创建完成 - 版本21迁移成功")
        } catch (e: Exception) {
            android.util.Log.e("AppDatabase", "情绪记录表创建失败", e)
            throw e
        }
    }
}

/**
 * 🔧 版本21到版本22的迁移：修复情绪记录表索引问题
 */
val MIGRATION_21_22 = object : Migration(21, 22) {
    override fun migrate(database: SupportSQLiteDatabase) {
        try {
            // 🔧 由于索引已经在21版本中创建，这里只需要确保表结构正确
            // 如果需要，可以重新创建索引（IF NOT EXISTS确保安全）
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_date ON emotion_records(date)")
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_emotion_type ON emotion_records(emotion_type)")
            database.execSQL("CREATE INDEX IF NOT EXISTS index_emotion_records_is_detailed ON emotion_records(is_detailed)")

            android.util.Log.d("AppDatabase", "✅ 情绪记录表索引修复完成 - 版本22迁移成功")
        } catch (e: Exception) {
            android.util.Log.e("AppDatabase", "情绪记录表索引修复失败", e)
            // 不抛出异常，因为索引问题不应该阻止应用启动
        }
    }
}

/**
 * 应用数据库类，Room数据库主入口
 */
@Database(
    entities = [
        Task::class,
        TaskTag::class,
        TaskClosure::class,
        KanbanBoard::class,
        KanbanColumn::class,
        AppUsageEntity::class,
        CycleRecord::class,
        SymptomRecord::class,
        Goal::class,
        GoalSubTask::class,
        GoalTemplate::class,
        GoalSubTaskTemplate::class,
        ReflectionEntity::class,
        TimeSession::class,
        Wish::class,
        Habit::class,
        HabitRecord::class,
        HabitReminder::class,
        MedicationRecord::class, // 🔧 新增用药记录实体
        EmotionRecordEntity::class // 🔧 新增情绪记录实体
    ],
    version = 22,  // 🔧 更新数据库版本号到22，修复情绪记录表索引
    exportSchema = false
)
@TypeConverters(Converters::class, ListStringConverter::class, LocalDateConverter::class)
abstract class AppDatabase : RoomDatabase() {
    
    /**
     * 提供任务DAO访问
     */
    abstract fun taskDao(): TaskDao
    
    /**
     * 提供看板DAO访问
     */
    abstract fun kanbanBoardDao(): KanbanBoardDao
    
    /**
     * 提供看板列DAO访问
     */
    abstract fun kanbanColumnDao(): KanbanColumnDao
    
    /**
     * 提供应用使用数据DAO访问
     */
    abstract fun appUsageDao(): AppUsageDao
    
    /**
     * 提供周期DAO访问
     */
    abstract fun cycleDao(): CycleDao
    
    /**
     * 提供目标DAO访问
     */
    abstract fun goalDao(): GoalDao
    
    /**
     * 提供目标模板DAO访问
     */
    abstract fun goalTemplateDao(): GoalTemplateDao
    
    /**
     * 提供感想DAO访问
     */
    abstract fun reflectionDao(): ReflectionDao
    
    /**
     * 提供时间会话DAO访问
     */
    abstract fun timeSessionDao(): TimeSessionDao
    
    /**
     * 提供愿望DAO访问
     */
    abstract fun wishDao(): WishDao
    
    /**
     * 提供习惯DAO访问
     */
    abstract fun habitDao(): HabitDao

    /**
     * 🔧 提供用药记录DAO访问
     */
    abstract fun medicationRecordDao(): MedicationRecordDao

    /**
     * 🔧 提供情绪记录DAO访问
     */
    abstract fun emotionRecordDao(): EmotionRecordDao
    
    companion object {
        // 定义所有迁移策略
        val ALL_MIGRATIONS = arrayOf(
            MIGRATION_3_4,
            MIGRATION_4_5,
            MIGRATION_5_6,
            MIGRATION_6_7,
            MIGRATION_7_8,
            MIGRATION_8_9,
            MIGRATION_9_10,
            MIGRATION_10_11,
            MIGRATION_11_12,
            MIGRATION_12_13,
            MIGRATION_13_14,
            MIGRATION_14_15,
            MIGRATION_15_16,
            MIGRATION_16_17,
            MIGRATION_17_18,
            MIGRATION_18_19, // 🔧 添加目标分类迁移
            MIGRATION_19_20, // 🔧 添加目标模板分类迁移
            MIGRATION_20_21, // 🔧 添加情绪记录表迁移
            MIGRATION_21_22  // 🔧 修复情绪记录表索引迁移
        )
    }
} 