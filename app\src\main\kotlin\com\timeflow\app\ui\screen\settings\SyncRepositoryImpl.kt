package com.timeflow.app.ui.screen.settings

import com.amazonaws.auth.AWSCredentials
import com.amazonaws.auth.AWSCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.auth.CognitoCachingCredentialsProvider
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.*
// 暂时注释掉外部SDK的导入，避免构建失败
// import com.qiniu.android.storage.Configuration
// import com.qiniu.android.storage.UploadManager
// import com.qiniu.android.storage.BucketManager
// import com.qiniu.android.utils.Auth
// import com.qiniu.android.http.ResponseInfo
// import com.qiniu.android.storage.model.DefaultPutRet
// import com.tencent.cos.xml.CosXmlService
// import com.tencent.cos.xml.CosXmlServiceConfig
// import com.tencent.cos.xml.exception.CosXmlClientException
// import com.tencent.cos.xml.exception.CosXmlServiceException
// import com.tencent.cos.xml.listener.CosXmlResultListener
// import com.tencent.cos.xml.model.CosXmlRequest
// import com.tencent.cos.xml.model.CosXmlResult
// import com.tencent.cos.xml.model.`object`.*
// import com.tencent.cos.xml.model.bucket.GetBucketRequest
// import com.tencent.qcloud.core.auth.QCloudCredentialProvider
// import com.tencent.qcloud.core.auth.BasicQCloudCredentials
// import com.tencent.qcloud.core.auth.StaticCredentialProvider
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskType
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import android.util.Log
import java.io.ByteArrayInputStream
import java.text.SimpleDateFormat
import java.util.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
// import okhttp3.OkHttpClient
// import okhttp3.Request

/**
 * 云服务提供商枚举
 */
enum class CloudProvider(
    val displayName: String,
    val description: String,
    val endpointExample: String
) {
    AWS_S3("Amazon S3", "亚马逊云存储服务", "s3.amazonaws.com"),
    QINIU_CLOUD("七牛云", "七牛云对象存储", "s3-cn-east-1.qiniucs.com"),
    TENCENT_CLOUD("腾讯云", "腾讯云对象存储COS", "cos.ap-beijing.myqcloud.com"),
    ALIYUN_OSS("阿里云OSS", "阿里云对象存储服务", "oss-cn-hangzhou.aliyuncs.com")
}

/**
 * 云存储配置数据类
 */
@Serializable
data class CloudStorageConfig(
    val provider: String = CloudProvider.AWS_S3.name,
    val accessKeyId: String = "",
    val secretAccessKey: String = "",
    val bucketName: String = "",
    val region: String = "",
    val endpoint: String = "",
    val customDomain: String = "" // 自定义域名，用于七牛云等
)

/**
 * S3配置数据类 (保持向后兼容)
 */
@Serializable
data class S3Config(
    val accessKeyId: String,
    val secretAccessKey: String,
    val bucketName: String,
    val region: String,
    val endpoint: String = "",
    val provider: String = CloudProvider.AWS_S3.name
) {
    /**
     * 转换为CloudStorageConfig
     */
    fun toCloudStorageConfig(): CloudStorageConfig {
        return CloudStorageConfig(
            provider = provider,
            accessKeyId = accessKeyId,
            secretAccessKey = secretAccessKey,
            bucketName = bucketName,
            region = region,
            endpoint = endpoint
        )
    }

    companion object {
        /**
         * 从CloudStorageConfig创建S3Config
         */
        fun fromCloudStorageConfig(config: CloudStorageConfig): S3Config {
            return S3Config(
                provider = config.provider,
                accessKeyId = config.accessKeyId,
                secretAccessKey = config.secretAccessKey,
                bucketName = config.bucketName,
                region = config.region,
                endpoint = config.endpoint
            )
        }
    }
}

/**
 * 简化的任务序列化模型
 */
@Serializable
data class SerializableTask(
    val id: String,
    val title: String,
    val description: String = "",
    val dueDate: String? = null,
    val startDate: String? = null,
    val createdAt: String,
    val updatedAt: String,
    val completedAt: String? = null,
    val isCompleted: Boolean = false,
    val priority: String? = null,
    val type: String = "NORMAL",
    val parentTaskId: String? = null,
    val hasSubtasks: Boolean = false,
    val childTasksCount: Int? = null,
    val completedChildTasksCount: Int? = null,
    val depth: Int = 0,
    val orderIndex: Int = 0,
    val groupId: String? = null,
    val groupType: String? = null,
    val estimatedTimeMinutes: Int = 0,
    val actualTimeMinutes: Int = 0,
    val progress: Float = 0f,
    val aiGenerated: Boolean = false,
    val emotionState: String? = null,
    val status: String = "待办",
    val displayInTaskList: Boolean = true,
    val reminderTime: String? = null,
    val goalId: String? = null,
    val dateManuallyModified: Boolean = false,
    val isFloatingTask: Boolean = false,
    val floatingWeekStart: String? = null,
    val floatingWeekEnd: String? = null,
    val scheduledDate: String? = null,
    val floatingTaskOrder: Int = 0
)

/**
 * 任务模型转换器
 */
object TaskSerializer {
    private val dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    fun toSerializable(task: Task): SerializableTask {
        return SerializableTask(
            id = task.id,
            title = task.title,
            description = task.description,
            dueDate = task.dueDate?.format(dateFormatter),
            startDate = task.startDate?.format(dateFormatter),
            createdAt = task.createdAt.format(dateFormatter),
            updatedAt = task.updatedAt.format(dateFormatter),
            completedAt = task.completedAt?.format(dateFormatter),
            isCompleted = task.isCompleted,
            priority = task.priority?.name,
            type = task.type.getName(),
            parentTaskId = task.parentTaskId,
            hasSubtasks = task.hasSubtasks,
            childTasksCount = task.childTasksCount,
            completedChildTasksCount = task.completedChildTasksCount,
            depth = task.depth,
            orderIndex = task.orderIndex,
            groupId = task.groupId,
            groupType = task.groupType,
            estimatedTimeMinutes = task.estimatedTimeMinutes,
            actualTimeMinutes = task.actualTimeMinutes,
            progress = task.progress,
            aiGenerated = task.aiGenerated,
            emotionState = task.emotionState,
            status = task.status,
            displayInTaskList = task.displayInTaskList,
            reminderTime = task.reminderTime?.format(dateFormatter),
            goalId = task.goalId,
            dateManuallyModified = task.dateManuallyModified,
            isFloatingTask = task.isFloatingTask,
            floatingWeekStart = task.floatingWeekStart?.format(dateFormatter),
            floatingWeekEnd = task.floatingWeekEnd?.format(dateFormatter),
            scheduledDate = task.scheduledDate?.format(dateFormatter),
            floatingTaskOrder = task.floatingTaskOrder
        )
    }
    
    fun fromSerializable(serializable: SerializableTask): Task {
        return Task(
            id = serializable.id,
            title = serializable.title,
            description = serializable.description,
            dueDate = serializable.dueDate?.let { LocalDateTime.parse(it, dateFormatter) },
            startDate = serializable.startDate?.let { LocalDateTime.parse(it, dateFormatter) },
            createdAt = LocalDateTime.parse(serializable.createdAt, dateFormatter),
            updatedAt = LocalDateTime.parse(serializable.updatedAt, dateFormatter),
            completedAt = serializable.completedAt?.let { LocalDateTime.parse(it, dateFormatter) },
            isCompleted = serializable.isCompleted,
            priority = serializable.priority?.let { Priority.valueOf(it) },
            type = TaskType.valueOf(serializable.type),
            parentTaskId = serializable.parentTaskId,
            hasSubtasks = serializable.hasSubtasks,
            childTasksCount = serializable.childTasksCount,
            completedChildTasksCount = serializable.completedChildTasksCount,
            depth = serializable.depth,
            orderIndex = serializable.orderIndex,
            groupId = serializable.groupId,
            groupType = serializable.groupType,
            estimatedTimeMinutes = serializable.estimatedTimeMinutes,
            actualTimeMinutes = serializable.actualTimeMinutes,
            progress = serializable.progress,
            aiGenerated = serializable.aiGenerated,
            emotionState = serializable.emotionState,
            status = serializable.status,
            displayInTaskList = serializable.displayInTaskList,
            reminderTime = serializable.reminderTime?.let { LocalDateTime.parse(it, dateFormatter) },
            goalId = serializable.goalId,
            dateManuallyModified = serializable.dateManuallyModified,
            isFloatingTask = serializable.isFloatingTask,
            floatingWeekStart = serializable.floatingWeekStart?.let { LocalDateTime.parse(it, dateFormatter) },
            floatingWeekEnd = serializable.floatingWeekEnd?.let { LocalDateTime.parse(it, dateFormatter) },
            scheduledDate = serializable.scheduledDate?.let { LocalDateTime.parse(it, dateFormatter) },
            floatingTaskOrder = serializable.floatingTaskOrder
        )
    }
}

/**
 * 云存储客户端抽象接口
 */
interface CloudStorageClient {
    suspend fun testConnection(): Result<String>
    suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String> = emptyMap()): Result<String>
    suspend fun downloadData(fileName: String? = null): Result<ByteArray>
    suspend fun listFiles(prefix: String = ""): Result<List<String>>
}

/**
 * 同步仓库接口
 */
interface SyncRepository {
    suspend fun testConnection(config: CloudStorageConfig): Result<String>
    suspend fun uploadData(config: CloudStorageConfig, data: List<Task>): Result<String>
    suspend fun downloadData(config: CloudStorageConfig): Result<List<Task>>

    // 保持向后兼容的S3方法
    suspend fun testS3Connection(config: S3Config): Result<String>
    suspend fun uploadDataToS3(config: S3Config, data: List<Task>): Result<String>
    suspend fun downloadDataFromS3(config: S3Config): Result<List<Task>>
}

/**
 * 云存储客户端工厂
 */
object CloudStorageClientFactory {
    fun createClient(config: CloudStorageConfig): CloudStorageClient {
        return when (CloudProvider.valueOf(config.provider)) {
            CloudProvider.AWS_S3 -> AwsS3StorageClient(config)
            CloudProvider.QINIU_CLOUD -> QiniuCloudStorageClient(config)
            CloudProvider.TENCENT_CLOUD -> TencentCloudStorageClient(config)
            CloudProvider.ALIYUN_OSS -> AliyunOssStorageClient(config)
        }
    }
}

/**
 * AWS S3存储客户端实现
 */
class AwsS3StorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    private fun createS3Client(): AmazonS3Client {
        val credentials = BasicAWSCredentials(config.accessKeyId, config.secretAccessKey)
        val credentialsProvider = object : AWSCredentialsProvider {
            override fun getCredentials(): AWSCredentials = credentials
            override fun refresh() {}
        }

        val region = try {
            Region.getRegion(Regions.fromName(config.region))
        } catch (e: Exception) {
            Log.w("AwsS3StorageClient", "无法解析区域 ${config.region}，使用默认区域 us-east-1")
            Region.getRegion(Regions.US_EAST_1)
        }

        return AmazonS3Client(credentialsProvider, region)
    }

    override suspend fun testConnection(): Result<String> = suspendCancellableCoroutine { continuation ->
        try {
            val s3Client = createS3Client()

            // 测试连接：尝试列出存储桶中的对象
            val listObjectsRequest = ListObjectsRequest()
                .withBucketName(config.bucketName)
                .withMaxKeys(1)

            s3Client.listObjects(listObjectsRequest)

            Log.d("AwsS3StorageClient", "AWS S3连接测试成功")
            continuation.resume(Result.success("连接成功"))

        } catch (e: Exception) {
            Log.e("AwsS3StorageClient", "连接测试失败", e)
            continuation.resume(Result.failure(e))
        }
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()
                val putObjectRequest = PutObjectRequest(
                    config.bucketName,
                    fileName,
                    ByteArrayInputStream(data),
                    ObjectMetadata().apply {
                        contentLength = data.size.toLong()
                        contentType = "application/json"
                        metadata.forEach { (key, value) ->
                            addUserMetadata(key, value)
                        }
                    }
                )

                s3Client.putObject(putObjectRequest)
                Log.d("AwsS3StorageClient", "数据上传成功: $fileName")
                continuation.resume(Result.success("上传成功: $fileName"))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "数据上传失败", e)
                continuation.resume(Result.failure(e))
            }
        }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()

                val targetFileName = fileName ?: run {
                    // 找到最新的备份文件
                    val listObjectsRequest = ListObjectsRequest()
                        .withBucketName(config.bucketName)
                        .withPrefix("timeflow_backup_")

                    val objectListing = s3Client.listObjects(listObjectsRequest)
                    val latestBackup = objectListing.objectSummaries
                        .filter { it.key.startsWith("timeflow_backup_") && it.key.endsWith(".json") }
                        .maxByOrNull { it.lastModified }

                    latestBackup?.key ?: throw Exception("未找到备份文件")
                }

                val getObjectRequest = GetObjectRequest(config.bucketName, targetFileName)
                val s3Object = s3Client.getObject(getObjectRequest)
                val data = s3Object.objectContent.readBytes()

                Log.d("AwsS3StorageClient", "数据下载成功: $targetFileName")
                continuation.resume(Result.success(data))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "数据下载失败", e)
                continuation.resume(Result.failure(e))
            }
        }

    override suspend fun listFiles(prefix: String): Result<List<String>> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()
                val listObjectsRequest = ListObjectsRequest()
                    .withBucketName(config.bucketName)
                    .withPrefix(prefix)

                val objectListing = s3Client.listObjects(listObjectsRequest)
                val fileNames = objectListing.objectSummaries.map { it.key }

                continuation.resume(Result.success(fileNames))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "文件列表获取失败", e)
                continuation.resume(Result.failure(e))
            }
        }
}

/**
 * 七牛云存储客户端实现（暂时禁用，等待SDK依赖解决）
 */
class QiniuCloudStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    override suspend fun testConnection(): Result<String> {
        return Result.failure(Exception("七牛云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> {
        return Result.failure(Exception("七牛云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> {
        return Result.failure(Exception("七牛云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> {
        return Result.failure(Exception("七牛云SDK暂未集成，请先配置正确的依赖"))
    }
}

/**
 * 腾讯云存储客户端实现（暂时禁用，等待SDK依赖解决）
 */
class TencentCloudStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    override suspend fun testConnection(): Result<String> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }
}

/**
 * 阿里云OSS存储客户端实现（暂时禁用，等待SDK依赖解决）
 */
class AliyunOssStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {
    override suspend fun testConnection(): Result<String> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }
}

/**
 * 同步仓库实现类
 */
@Singleton
class SyncRepositoryImpl @Inject constructor() : SyncRepository {

    // 新的云存储方法实现
    override suspend fun testConnection(config: CloudStorageConfig): Result<String> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)
            client.testConnection()
        } catch (e: Exception) {
            Log.e("SyncRepository", "连接测试失败", e)
            Result.failure(e)
        }
    }

    override suspend fun uploadData(config: CloudStorageConfig, data: List<Task>): Result<String> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)

            // 转换为可序列化的模型
            val serializableTasks = data.map { TaskSerializer.toSerializable(it) }

            // 序列化数据为JSON
            val json = Json.encodeToString(serializableTasks)
            val jsonBytes = json.toByteArray(Charsets.UTF_8)

            // 生成带时间戳的文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "timeflow_backup_$timestamp.json"

            // 创建元数据
            val metadata = mapOf(
                "app" to "TimeFlow",
                "version" to "1.0",
                "timestamp" to timestamp,
                "provider" to config.provider
            )

            client.uploadData(jsonBytes, fileName, metadata)
        } catch (e: Exception) {
            Log.e("SyncRepository", "数据上传失败", e)
            Result.failure(e)
        }
    }

    override suspend fun downloadData(config: CloudStorageConfig): Result<List<Task>> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)

            val dataResult = client.downloadData()
            dataResult.fold(
                onSuccess = { jsonBytes ->
                    val jsonContent = String(jsonBytes, Charsets.UTF_8)
                    val serializableTasks = Json.decodeFromString<List<SerializableTask>>(jsonContent)
                    val tasks = serializableTasks.map { TaskSerializer.fromSerializable(it) }

                    Log.d("SyncRepository", "数据下载成功，任务数量: ${tasks.size}")
                    Result.success(tasks)
                },
                onFailure = { exception ->
                    Log.e("SyncRepository", "数据下载失败", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e("SyncRepository", "数据下载失败", e)
            Result.failure(e)
        }
    }

    // 保持向后兼容的S3方法
    private fun createS3Client(config: S3Config): AmazonS3Client {
        // 创建AWS凭证
        val credentials = BasicAWSCredentials(config.accessKeyId, config.secretAccessKey)

        // 创建凭证提供者
        val credentialsProvider = object : AWSCredentialsProvider {
            override fun getCredentials(): AWSCredentials = credentials
            override fun refresh() {
                // 不需要刷新静态凭证
            }
        }

        // 创建区域对象
        val region = try {
            Region.getRegion(Regions.fromName(config.region))
        } catch (e: Exception) {
            Log.w("SyncRepository", "无法解析区域 ${config.region}，使用默认区域 us-east-1")
            Region.getRegion(Regions.US_EAST_1)
        }

        // 创建S3客户端
        val s3Client = AmazonS3Client(credentialsProvider, region)

        return s3Client
    }
    
    override suspend fun testS3Connection(config: S3Config): Result<String> {
        // 使用新的架构实现向后兼容
        return testConnection(config.toCloudStorageConfig())
    }
    
    override suspend fun uploadDataToS3(config: S3Config, data: List<Task>): Result<String> {
        // 使用新的架构实现向后兼容
        return uploadData(config.toCloudStorageConfig(), data)
    }
    
    override suspend fun downloadDataFromS3(config: S3Config): Result<List<Task>> {
        // 使用新的架构实现向后兼容
        return downloadData(config.toCloudStorageConfig())
    }
} 