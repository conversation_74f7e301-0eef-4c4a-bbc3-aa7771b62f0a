package com.timeflow.app.data.preferences

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好设置管理器
 * 用于管理应用的各种用户偏好设置和首次使用状态
 */
@Singleton
class UserPreferencesManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val PREFS_NAME = "timeflow_user_preferences"
        
        // 首次使用相关的键
        private const val KEY_FIRST_TIME_MENSTRUAL_CYCLE = "first_time_menstrual_cycle"
        private const val KEY_FIRST_TIME_GOAL_SETTING = "first_time_goal_setting"
        private const val KEY_FIRST_TIME_HABIT_TRACKING = "first_time_habit_tracking"
        private const val KEY_FIRST_TIME_MEDICATION_REMINDER = "first_time_medication_reminder"
        
        // 其他用户偏好设置
        private const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
        private const val KEY_DARK_MODE_ENABLED = "dark_mode_enabled"
        private const val KEY_BACKUP_ENABLED = "backup_enabled"
    }
    
    private val sharedPreferences: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    // ==================== 首次使用状态管理 ====================
    
    /**
     * 检查是否是第一次使用生理周期功能
     */
    fun isFirstTimeMenstrualCycle(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_TIME_MENSTRUAL_CYCLE, true)
    }
    
    /**
     * 标记生理周期功能已经使用过
     */
    fun markMenstrualCycleAsUsed() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_TIME_MENSTRUAL_CYCLE, false)
            .apply()
    }
    
    /**
     * 检查是否是第一次使用目标设定功能
     */
    fun isFirstTimeGoalSetting(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_TIME_GOAL_SETTING, true)
    }
    
    /**
     * 标记目标设定功能已经使用过
     */
    fun markGoalSettingAsUsed() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_TIME_GOAL_SETTING, false)
            .apply()
    }
    
    /**
     * 检查是否是第一次使用习惯追踪功能
     */
    fun isFirstTimeHabitTracking(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_TIME_HABIT_TRACKING, true)
    }
    
    /**
     * 标记习惯追踪功能已经使用过
     */
    fun markHabitTrackingAsUsed() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_TIME_HABIT_TRACKING, false)
            .apply()
    }
    
    /**
     * 检查是否是第一次使用用药提醒功能
     */
    fun isFirstTimeMedicationReminder(): Boolean {
        return sharedPreferences.getBoolean(KEY_FIRST_TIME_MEDICATION_REMINDER, true)
    }
    
    /**
     * 标记用药提醒功能已经使用过
     */
    fun markMedicationReminderAsUsed() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_TIME_MEDICATION_REMINDER, false)
            .apply()
    }
    
    // ==================== 其他用户偏好设置 ====================
    
    /**
     * 检查通知是否启用
     */
    fun isNotificationEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_NOTIFICATION_ENABLED, true)
    }
    
    /**
     * 设置通知启用状态
     */
    fun setNotificationEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_NOTIFICATION_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 检查深色模式是否启用
     */
    fun isDarkModeEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_DARK_MODE_ENABLED, false)
    }
    
    /**
     * 设置深色模式启用状态
     */
    fun setDarkModeEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_DARK_MODE_ENABLED, enabled)
            .apply()
    }
    
    /**
     * 检查备份是否启用
     */
    fun isBackupEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_BACKUP_ENABLED, true)
    }
    
    /**
     * 设置备份启用状态
     */
    fun setBackupEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_BACKUP_ENABLED, enabled)
            .apply()
    }
    
    // ==================== 重置功能 ====================
    
    /**
     * 重置所有首次使用状态（用于测试或重置功能）
     */
    fun resetFirstTimeFlags() {
        sharedPreferences.edit()
            .putBoolean(KEY_FIRST_TIME_MENSTRUAL_CYCLE, true)
            .putBoolean(KEY_FIRST_TIME_GOAL_SETTING, true)
            .putBoolean(KEY_FIRST_TIME_HABIT_TRACKING, true)
            .putBoolean(KEY_FIRST_TIME_MEDICATION_REMINDER, true)
            .apply()
    }
    
    /**
     * 清除所有用户偏好设置
     */
    fun clearAllPreferences() {
        sharedPreferences.edit().clear().apply()
    }
}
