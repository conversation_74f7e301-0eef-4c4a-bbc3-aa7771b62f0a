package com.timeflow.app.ui.screen.calendar

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.screen.task.model.TaskModel
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import javax.inject.Inject
import java.time.YearMonth
import android.util.Log
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.ui.task.components.common.event.EventBus
import timber.log.Timber
import com.timeflow.app.data.model.Priority
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.flow.update
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.delay
import androidx.compose.ui.graphics.Color
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import com.timeflow.app.ui.theme.ThemeMode
import androidx.compose.ui.graphics.toArgb
import kotlinx.coroutines.CancellationException
import com.timeflow.app.ui.theme.ThemeManager
import kotlinx.coroutines.isActive
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.Job
import com.timeflow.app.data.repository.SharedPendingDeletionState
// 添加TaskTimeUseCase导入
import com.timeflow.app.domain.usecase.TaskTimeUseCase
import kotlinx.coroutines.flow.catch
// 添加时间追踪相关导入
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.ui.screen.calendar.components.TimeTrackingEvent
import kotlinx.coroutines.flow.first
import java.time.ZoneId

/**
 * 日历事件数据类，表示日历上的任务事件
 */
data class CalendarEvent(
    val id: String,
    val title: String,
    val start: LocalDateTime,
    val end: LocalDateTime? = null,
    val isAllDay: Boolean = false,
    val color: Long? = null,
    val taskId: String? = null,
    val isCompleted: Boolean = false,
    val urgency: TaskUrgency = TaskUrgency.MEDIUM
)

/**
 * 日历数据状态
 */
data class CalendarState(
    val events: List<CalendarEvent> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 更新优先级颜色系统，使用莫兰迪色系半透明色值
 */
fun getTaskColorByPriority(priority: Priority): Long {
    return when (priority) {
        Priority.URGENT -> 0xCCBFAFB0L  // 紧急：莫兰迪灰粉，80%不透明度
        Priority.HIGH -> 0xCCD4B5B8L    // 高优先级：莫兰迪玫瑰粉，80%不透明度
        Priority.MEDIUM -> 0xCCD4C4B0L  // 中优先级：莫兰迪米色，80%不透明度
        Priority.LOW -> 0xCCC4B5D4L     // 低优先级：莫兰迪薰衣草紫，80%不透明度
    }
}

/**
 * 增加自定义颜色功能的数据模型 - 使用莫兰迪色系
 */
data class UserColorPreference(
    val urgentPriorityColor: Long = 0xCCBFAFB0L,    // 莫兰迪灰粉
    val highPriorityColor: Long = 0xCCD4B5B8L,      // 莫兰迪玫瑰粉
    val mediumPriorityColor: Long = 0xCCD4C4B0L,    // 莫兰迪米色
    val lowPriorityColor: Long = 0xCCC4B5D4L,       // 莫兰迪薰衣草紫
    val useCustomColors: Boolean = false,
    // 添加每个页面的背景颜色设置
    val homePageBackground: Long = 0xFFF9F9F9L,
    val calendarPageBackground: Long = 0xFFFFFFFFL, // 纯白色(#ffffff)
    val statisticsPageBackground: Long = 0xFFF9F9F9L,
    val settingsPageBackground: Long = 0xFFF9F9F9L,
    val useDynamicColor: Boolean = false,
    val useMaterialYou: Boolean = false,
    val useDarkMode: Boolean = false
)

/**
 * 日历ViewModel - 管理日历视图和任务数据的同步
 */
@HiltViewModel
class CalendarViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val sharedPendingDeletionState: SharedPendingDeletionState,
    private val taskTimeUseCase: TaskTimeUseCase, // 添加TaskTimeUseCase依赖
    private val timeSessionRepository: TimeSessionRepository // 🔧 新增：时间追踪仓库
) : ViewModel() {
    
    // 添加TAG常量定义用于日志输出
    companion object {
        private const val TAG = "CalendarViewModel"
        private const val COLOR_UPDATE_DEBOUNCE_MS = 1000L  // 颜色更新防抖时间
    }
    
    // 添加互斥锁防止并发刷新
    private val refreshMutex = Mutex()
    
    // 当前加载任务
    private var currentLoadJob: Job? = null
    
    // 日历事件状态
    private val _calendarState = MutableStateFlow(CalendarState(isLoading = true))
    val calendarState: StateFlow<CalendarState> = _calendarState.asStateFlow()
    
    // 当前选中日期
    private val _selectedDate = MutableStateFlow(LocalDate.now())
    val selectedDate: StateFlow<LocalDate> = _selectedDate.asStateFlow()
    
    // 当前日历视图的年月
    private val _currentYearMonth = MutableStateFlow(YearMonth.now())
    val currentYearMonth: StateFlow<YearMonth> = _currentYearMonth.asStateFlow()
    
    // 用户颜色偏好
    private val _userColorPreference = MutableStateFlow(UserColorPreference())
    val userColorPreference: StateFlow<UserColorPreference> = _userColorPreference.asStateFlow()

    // Calendar event states
    sealed class CalendarUiEvent {
        data class Error(val message: String) : CalendarUiEvent()
        object EventsUpdated : CalendarUiEvent()
    }

    private val _eventFlow = MutableSharedFlow<CalendarUiEvent>(replay = 0)
    val eventFlow = _eventFlow.asSharedFlow()

    // 添加事件去重处理
    private var lastProcessedEventHash: Int = 0
    
    // 使用IO调度器处理颜色提取，减轻主线程负担
    private val colorExtractionDispatcher = Dispatchers.Default.limitedParallelism(2)
    
    // 添加批量处理事件的能力
    private val eventBatchHandler = MutableStateFlow<List<AppEvent.BaseEvent>>(emptyList())
    
    // 添加防抖变量，最小更新间隔(毫秒)
    private var lastColorUpdateTimestamp = 0L
    private var pendingColorUpdate = false
    private var lastCalendarColorValue: Long? = null
    
    // 优化对背景色的处理
    private val backgroundWorker = viewModelScope.launch(Dispatchers.Default) {
        eventBatchHandler.collect { events ->
            if (events.isEmpty()) return@collect
            
            // 只保留每种事件的最新一个
            val deduplicatedEvents = events.reversed()
                .distinctBy { it::class.java.simpleName }
                .reversed()
                
            for (event in deduplicatedEvents) {
                try {
                    processEvent(event)
                } catch (e: Exception) {
                    if (e !is CancellationException) {
                        Log.e(TAG, "事件处理失败: $event", e)
                    }
                }
            }
        }
    }
    
    // 抽取事件处理逻辑到单独的函数
    private suspend fun processEvent(event: AppEvent.BaseEvent) {
        // 使用事件哈希码防止重复处理相同事件
        val eventHash = event.hashCode()
        if (eventHash == lastProcessedEventHash) {
            Log.d(TAG, "跳过重复事件: $event")
            return
        }
        lastProcessedEventHash = eventHash
        
        when (event) {
            is AppEvent.ThemeSettingsChanged -> {
                // 防抖处理
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastColorUpdateTimestamp < COLOR_UPDATE_DEBOUNCE_MS) {
                    pendingColorUpdate = true
                } else {
                    lastColorUpdateTimestamp = currentTime
                    updateColorFromTheme()
                }
            }
            is AppEvent.PageBackgroundChanged -> {
                if (event.pageName == "calendar" && event.colorArgb != 0L) {
                    _userColorPreference.update { pref ->
                        pref.copy(
                            calendarPageBackground = event.colorArgb,
                            useCustomColors = true
                        )
                    }
                    // 记录单次更新，避免后续重复
                    Log.d(TAG, "已更新日历背景色: ${event.colorArgb.toString(16)}")
                }
            }
            is AppEvent.TaskColorChanged -> {
                // 颜色变更事件，不需要重新加载所有事件，只需要发出UI刷新通知
                viewModelScope.launch {
                    try {
                        _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                        Log.d(TAG, "收到颜色变更事件，触发UI刷新")
                    } catch (e: Exception) {
                        Log.e(TAG, "处理颜色变更事件失败", e)
                    }
                }
            }
            is AppEvent.TaskCompletionChanged,
            is AppEvent.TaskCreated,
            is AppEvent.TaskDeleted,
            is AppEvent.TaskTimeChanged,
            is AppEvent.SubTaskAdded,
            is AppEvent.SubTaskUpdated,
            is AppEvent.SubTaskDeleted -> {
                // 在协程作用域内启动一个新的协程来处理重载事件
                // 这样避免阻塞事件处理流程
                viewModelScope.launch(Dispatchers.IO) {
                    loadEvents()
                    
                    // 发送事件更新通知，使UI重组
                    if (isActive) {
                        _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                    }
                }
            }
            is AppEvent.TaskUpdated -> {
                // 对于任务更新事件，只有在不是颜色更新时才重新加载
                // 颜色更新由updateEventDetails方法直接处理
                if (System.currentTimeMillis() - lastColorUpdateTimestamp > COLOR_UPDATE_DEBOUNCE_MS) {
                    viewModelScope.launch(Dispatchers.IO) {
                        loadEvents()
                        
                        if (isActive) {
                            _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                        }
                    }
                }
            }
            is AppEvent.DatabaseRefreshed -> {
                // 数据库刷新事件，需要强制重新加载
                viewModelScope.launch(Dispatchers.IO) {
                    delay(200) // 短暂延迟确保数据库操作完成
                    loadEvents()
                    
                    if (isActive) {
                        _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                    }
                }
            }
            else -> { /* 忽略其他事件 */ }
        }
    }

    // 初始化ViewModel时自动加载日历事件
    init {
        Log.d(TAG, "CalendarViewModel 初始化")
        loadEvents()
        
        // 🔧 新增：监听任务时间更新事件
        viewModelScope.launch {
            taskTimeUseCase.timeUpdateEvents
                .onEach { event ->
                    Log.d(TAG, "收到任务时间更新事件: taskId=${event.taskId}, source=${event.source}")
                    // 当任务时间更新时，刷新日历数据以更新时间块位置
                    refreshCalendarData()
                    
                    // 🔧 新增：如果用户在日视图，自动跳转到任务的新日期
                    try {
                        // 获取更新后的任务
                        val task = taskRepository.getTaskById(event.taskId)
                        if (task != null) {
                            val newDate = task.startDate?.toLocalDate() ?: task.dueDate?.toLocalDate()
                            if (newDate != null) {
                                Log.d(TAG, "🔄 任务时间更新，自动跳转到新日期: $newDate")
                                // 如果当前是日视图且日期不同，自动切换
                                                            if (_selectedDate.value != newDate) {
                                selectDate(newDate)
                            }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "自动跳转到任务新日期失败", e)
                    }
                }
                .catch { e ->
                    Log.e(TAG, "监听任务时间更新事件失败", e)
                }
                .collect { /* onEach已处理事件 */ }
        }
        
        // 监听事件总线
        viewModelScope.launch {
            EventBus.events.collect { event ->
                // 添加事件到批处理队列
                eventBatchHandler.update { list -> list + event }
            }
        }
        
        // 加载保存的用户颜色偏好设置
        loadUserColorPreferences()
        
        // 从ThemeManager获取颜色设置 - 使用单一数据流方式，避免重复订阅
        viewModelScope.launch {
            try {
                // 启动定期检查，处理因防抖而延迟的更新
                launch {
                    while (isActive) {
                        delay(COLOR_UPDATE_DEBOUNCE_MS * 2)
                        if (pendingColorUpdate) {
                            pendingColorUpdate = false
                            lastColorUpdateTimestamp = System.currentTimeMillis()
                            updateColorFromTheme()
                        }
                    }
                }
                
                // 使用collectLatest而非collect，确保始终只处理最新值
                ThemeManager.userThemePreference.collectLatest { preference ->
                    // 检查协程是否仍然活跃
                    if (!isActive) return@collectLatest
                    
                    // 从ThemeManager获取颜色并更新，使用防抖机制
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastColorUpdateTimestamp < COLOR_UPDATE_DEBOUNCE_MS) {
                        // 在防抖时间内，不立即更新但标记待更新
                        pendingColorUpdate = true
                    } else {
                        // 尝试更新颜色
                        try {
                            val colorArgb = preference.calendarPageColor.toArgb()
                            val calendarColor = colorArgb.toLong() and 0xFFFFFFFFL
                            
                            // 检查是否与上次更新颜色相同，避免重复更新
                            if (lastCalendarColorValue == calendarColor) {
                                return@collectLatest
                            }
                            
                            // 检查是否是默认青色(#03dac6)，如果是则跳过更新
                            if (calendarColor == 0xFF03DAC6L) {
                                return@collectLatest
                            }
                            
                            lastCalendarColorValue = calendarColor
                            lastColorUpdateTimestamp = currentTime
                            pendingColorUpdate = false
                            
                            Log.d(TAG, "从ThemeManager获取日历颜色: ${calendarColor.toString(16)}")
                            _userColorPreference.update { pref ->
                                pref.copy(
                                    calendarPageBackground = calendarColor,
                                    useCustomColors = true
                                )
                            }
                        } catch (e: Exception) {
                            // 只记录非取消异常
                            if (e !is CancellationException) {
                                Log.e(TAG, "颜色转换失败: ${e.message}", e)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                // 忽略取消异常，只记录其他异常
                if (e !is CancellationException) {
                    Log.e(TAG, "从ThemeManager获取颜色设置失败: ${e.message}", e)
                }
            }
        }
        
        // 监听主题变更事件 - 使用批量处理方式减少重复处理
        viewModelScope.launch {
            try {
                EventBus.events
                    .collect { event ->
                        // 检查协程是否仍然活跃
                        if (!isActive) return@collect
                        
                        // 将事件批量添加到处理队列
                        val currentEvents = eventBatchHandler.value.toMutableList()
                        currentEvents.add(event)
                        eventBatchHandler.value = currentEvents
                    }
            } catch (e: Exception) {
                // 忽略取消异常，只记录其他异常
                if (e !is CancellationException) {
                    Log.e(TAG, "处理事件失败: ${e.message}", e)
                }
            }
        }
        
        // 移除重复的ThemeSettingsChanged/PageBackgroundChanged事件监听，避免重复处理
    }
    
    /**
     * 设置当前选中日期
     */
    fun setSelectedDate(date: LocalDate) {
        _selectedDate.value = date
        _currentYearMonth.value = YearMonth.from(date)
    }
    
    /**
     * 设置当前年月
     */
    fun setCurrentYearMonth(yearMonth: YearMonth) {
        _currentYearMonth.value = yearMonth
    }
    
    /**
     * 强制刷新日历数据
     * 用于在事件更新后立即刷新UI显示
     * 使用防抖机制避免频繁刷新
     */
    fun refreshCalendarData() {
        Log.d(TAG, "强制刷新日历数据...")
        
        // 清除颜色缓存确保数据新鲜
        colorExtractionCache.clear()
        
        // 调用loadEvents，它内部已有防并发保护
        loadEvents()
    }
    
    /**
     * 加载日历事件
     * 从数据库加载任务并转换为日历事件，确保耗时操作在后台线程执行
     * 使用互斥锁防止并发执行
     */
    fun loadEvents() {
        // 取消之前的加载任务
        currentLoadJob?.cancel()
        
        currentLoadJob = viewModelScope.launch {
            refreshMutex.withLock {
                try {
                    // 设置加载状态 - UI操作在主线程
                    withContext(Dispatchers.Main) {
                        _calendarState.value = _calendarState.value.copy(isLoading = true)
                        Log.d(TAG, "开始加载日历事件...")
                    }
                    
                    // 获取所有任务 - 数据库操作在IO线程
                    val tasks = withContext(Dispatchers.IO) {
                        Log.d(TAG, "从数据库加载任务...")
                        taskRepository.getAllTasks()
                    }
                    Log.d(TAG, "成功加载 ${tasks.size} 个任务")
                    
                    // 🔧 过滤待删除的任务
                    val pendingDeletions = sharedPendingDeletionState.pendingDeletions.value
                    val filteredTasks = tasks.filter { task ->
                        val isPendingDeletion = pendingDeletions.containsKey(task.id)
                        if (isPendingDeletion) {
                            Log.d(TAG, "📅 日历过滤待删除任务: ${task.id} (${task.title})")
                        }
                        !isPendingDeletion
                    }
                    Log.d(TAG, "过滤后任务数: ${filteredTasks.size} (过滤掉 ${tasks.size - filteredTasks.size} 个待删除任务)")
                    
                    // 将任务转换为日历事件 - 转换操作在Default或IO线程
                    // convertTasksToCalendarEvents方法内部已切换到IO线程，无需再次切换
                    val events = convertTasksToCalendarEvents(filteredTasks)
                    Log.d(TAG, "成功生成 ${events.size} 个日历事件")
                    
                    // 更新UI状态 - UI操作在主线程
                    withContext(Dispatchers.Main) {
                        _calendarState.value = CalendarState(
                            events = events,
                            isLoading = false
                        )
                        Log.d(TAG, "日历事件加载完成，已更新UI状态")
                    }
                } catch (e: Exception) {
                    if (e !is CancellationException) {
                        Log.e(TAG, "加载日历事件失败", e)
                        // 更新错误状态 - UI操作在主线程
                        withContext(Dispatchers.Main) {
                            _calendarState.value = CalendarState(
                                events = emptyList(),
                                isLoading = false,
                                error = "加载日历事件失败: ${e.message}"
                            )
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 获取指定日期的事件
     * 🚫 日视图不显示浮动任务，只显示已安排的具体任务
     */
    fun getEventsForDate(date: LocalDate): List<CalendarEvent> {
        Log.d(TAG, "=== 获取日期事件: $date ===")
        
        val filteredEvents = _calendarState.value.events.filter { event ->
            val eventDate = event.start.toLocalDate()
            
            // 过滤掉浮动任务（浮动任务只在周视图的浮动任务区域显示）
            val isFloatingTask = event.taskId?.let { taskId ->
                // 通过任务ID检查是否为浮动任务
                try {
                    // 检查ID前缀和标题标识
                    event.id.startsWith("floating_") || 
                    event.title.contains("[浮动]") ||
                    event.title.contains("本周完成") // 浮动任务的时间显示标识
                } catch (e: Exception) {
                    false
                }
            } ?: false
            
            val isDateMatch = eventDate == date
            val shouldInclude = isDateMatch && !isFloatingTask
            
            Log.d(TAG, "事件过滤: ${event.title}")
            Log.d(TAG, "  - 事件日期: $eventDate, 目标日期: $date, 日期匹配: $isDateMatch")
            Log.d(TAG, "  - 是否浮动任务: $isFloatingTask")
            Log.d(TAG, "  - 是否包含: $shouldInclude")
            
            shouldInclude
        }
        
        Log.d(TAG, "日期 $date 的事件数量: ${filteredEvents.size}")
        return filteredEvents
    }
    
    /**
     * 获取指定周的事件
     */
    fun getEventsForWeek(startDate: LocalDate, endDate: LocalDate): List<CalendarEvent> {
        return _calendarState.value.events.filter { event ->
            val eventDate = event.start.toLocalDate()
            (eventDate.isEqual(startDate) || eventDate.isAfter(startDate)) &&
                    (eventDate.isEqual(endDate) || eventDate.isBefore(endDate))
        }
    }
    
    /**
     * 获取指定月份的事件
     */
    fun getEventsForMonth(yearMonth: YearMonth): List<CalendarEvent> {
        return _calendarState.value.events.filter { event ->
            val eventYearMonth = YearMonth.from(event.start)
            eventYearMonth == yearMonth
        }
    }

    /**
     * 🔧 新增：获取指定日期的时间追踪事件
     */
    suspend fun getTimeTrackingEventsForDate(date: LocalDate): List<TimeTrackingEvent> {
        return try {
            val sessions = timeSessionRepository.getSessionsByDate(date).first()

            // 只显示已完成的会话（有结束时间的）
            val completedSessions = sessions.filter { session ->
                session.endTime != null && session.isCompleted
            }

            Log.d(TAG, "获取日期 $date 的时间追踪事件: ${completedSessions.size} 个")

            completedSessions.map { session ->
                val startTime = session.startTime.atZone(ZoneId.systemDefault()).toLocalDateTime()
                val endTime = session.endTime!!.atZone(ZoneId.systemDefault()).toLocalDateTime()

                TimeTrackingEvent(
                    id = session.id,
                    taskName = session.taskName,
                    actualStartTime = startTime,
                    actualEndTime = endTime,
                    duration = session.duration,
                    taskId = session.taskId,
                    sessionId = session.id,
                    timerType = session.timerType,
                    focusRating = session.focusRating,
                    productivityRating = session.productivityRating
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取时间追踪事件失败", e)
            emptyList()
        }
    }

    /**
     * 将任务转换为日历事件
     * 只显示父任务，不显示子任务
     */
    private suspend fun convertTasksToCalendarEvents(tasks: List<ModelTask>): List<CalendarEvent> {
        // 将耗时操作放到专用调度器执行
        return withContext(colorExtractionDispatcher) {
            val colorCache = HashMap<String, Long?>()
            
            // 🔧 过滤：只处理父任务（parentTaskId为null的任务），忽略子任务
            val parentTasks = tasks.filter { task -> 
                task.parentTaskId == null 
            }
            
            // 🎯 分离浮动任务和普通任务
            val floatingTasks = parentTasks.filter { it.isFloatingTask == true }
            val rawScheduledTasks = parentTasks.filter { it.isFloatingTask != true }
            
            // 🔧 去重逻辑：同一标题的任务只保留最新的一个（解决拖拽后重复任务问题）
            val scheduledTasks = rawScheduledTasks.groupBy { it.title }
                .mapValues { (title, taskList) ->
                    Log.d("CalendarViewModel", "🔍 去重分析 - 标题: $title, 任务数: ${taskList.size}")
                    taskList.forEachIndexed { index, task ->
                        Log.d("CalendarViewModel", "  [$index] 任务ID: ${task.id}")
                        Log.d("CalendarViewModel", "  [$index] 创建时间: ${task.createdAt}")
                        Log.d("CalendarViewModel", "  [$index] 更新时间: ${task.updatedAt}")
                        Log.d("CalendarViewModel", "  [$index] 开始时间: ${task.startDate}")
                        Log.d("CalendarViewModel", "  [$index] 截止时间: ${task.dueDate}")
                        Log.d("CalendarViewModel", "  [$index] 是否浮动: ${task.isFloatingTask}")
                        Log.d("CalendarViewModel", "  [$index] 手动修改: ${task.dateManuallyModified}")
                        Log.d("CalendarViewModel", "  [$index] floatingWeekStart: ${task.floatingWeekStart}")
                        Log.d("CalendarViewModel", "  [$index] floatingWeekEnd: ${task.floatingWeekEnd}")
                        
                        // 计算权重
                        val weight = when {
                            task.dateManuallyModified -> 1000000 + task.updatedAt.toEpochSecond(java.time.ZoneOffset.UTC)
                            else -> task.updatedAt.toEpochSecond(java.time.ZoneOffset.UTC)
                        }
                        Log.d("CalendarViewModel", "  [$index] 权重: $weight")
                    }
                    
                    // 对于同标题任务，优先选择已手动修改日期的，其次选择最新更新的
                    val selectedTask = taskList.maxByOrNull { task ->
                        when {
                            task.dateManuallyModified -> 1000000 + task.updatedAt.toEpochSecond(java.time.ZoneOffset.UTC)
                            else -> task.updatedAt.toEpochSecond(java.time.ZoneOffset.UTC)
                        }
                    }
                    Log.d("CalendarViewModel", "  ✅ 选中任务: ${selectedTask?.id}, 开始时间: ${selectedTask?.startDate}")
                    selectedTask
                }
                .values
                .filterNotNull()
            
            Log.d("CalendarViewModel", "任务分类 - 普通任务: ${rawScheduledTasks.size} -> 去重后: ${scheduledTasks.size}, 浮动任务: ${floatingTasks.size}")
            
            Log.d("CalendarViewModel", "原始任务数: ${tasks.size}, 过滤后的父任务数: ${parentTasks.size}")
            
            // 🎯 只处理已安排的任务（非浮动任务），浮动任务单独处理
            scheduledTasks.mapNotNull { task ->
                try {
                    // 🎯 修复拖拽后任务不显示在正确日期的问题
                    // 对于从浮动任务转换过来的任务，scheduleFloatingTask已经设置了正确的startDate和dueDate
                    // 但原始的floatingWeekStart可能还是旧的日期，这会误导事件创建逻辑
                    
                    val taskStartDate = task.startDate ?: task.dueDate ?: task.createdAt
                    val taskEndDate = task.dueDate
                    
                    if (taskStartDate != null) {
                        // 确定事件开始时间：使用task.startDate（已被scheduleFloatingTask正确更新）
                        val startTime = if (task.startDate != null && (task.startDate.hour != 0 || task.startDate.minute != 0)) {
                            // 使用具体的开始时间
                            task.startDate
                        } else if (taskStartDate.hour != 0 || taskStartDate.minute != 0) {
                            // 使用taskStartDate的具体时间
                            taskStartDate
                        } else {
                            // 默认设置为上午8:00开始
                            taskStartDate.withHour(8).withMinute(0)
                        }
                        
                        // 确定事件结束时间：优先使用task.dueDate，否则为开始时间加15分钟
                        val endTime = if (taskEndDate != null && taskEndDate != startTime) {
                            // 有明确的结束时间且不等于开始时间
                            Log.d("CalendarViewModel", "  - 使用任务的截止时间作为结束时间: $taskEndDate")
                            Log.d("时间调试", "  - 任务结束时间来源: 数据库存储的dueDate")
                            taskEndDate
                        } else {
                            // 没有结束时间或结束时间等于开始时间，默认加15分钟
                            val calculatedEndTime = startTime.plusMinutes(15)
                            Log.d("CalendarViewModel", "  - 使用默认持续时间15分钟")
                            Log.d("时间调试", "  - 任务结束时间来源: 开始时间+15分钟 ($startTime + 15分钟 = $calculatedEndTime)")
                            calculatedEndTime
                        }
                        
                        // 从缓存中获取颜色，如果缓存中不存在则提取
                        val colorFromDescription = colorCache.getOrPut(task.id) {
                            try {
                                // 从任务描述中提取颜色值（支持多种格式）
                                val description = task.description ?: ""
                                
                                // 尝试多种颜色提取方式
                                val colorRegexes = listOf(
                                    """"color"\s*:\s*(\d+)""".toRegex(),
                                    """color=(\d+)""".toRegex(),
                                    """颜色[：:]\s*(\d+)""".toRegex()
                                )
                                
                                for (regex in colorRegexes) {
                                    val matchResult = regex.find(description)
                                    val colorValue = matchResult?.groupValues?.get(1)?.toLongOrNull()
                                    if (colorValue != null) {
                                        Log.d("CalendarViewModel", "从任务 ${task.id} 描述中提取到颜色: ${colorValue.toString(16)}")
                                        return@getOrPut colorValue
                                    }
                                }
                                
                                // 如果没有找到颜色，返回null
                                null
                            } catch (e: Exception) {
                                Log.e("CalendarViewModel", "从任务 ${task.id} 提取颜色失败: ${e.message}", e)
                                null
                            }
                        }
                        
                        // 使用从任务中提取的颜色，或者根据优先级生成默认颜色
                        val color = colorFromDescription ?: getTaskColor(task.priority?.ordinal ?: 0)
                        
                        // 确保所有任务都能正确转换为日历事件
                        Log.d("CalendarViewModel", "创建事件: ${task.title}")
                        Log.d("CalendarViewModel", "  - 任务开始时间: ${task.startDate}")  
                        Log.d("CalendarViewModel", "  - 任务截止时间: ${task.dueDate}")
                        Log.d("CalendarViewModel", "  - 事件开始时间: $startTime")
                        Log.d("CalendarViewModel", "  - 事件结束时间: $endTime")
                        Log.d("CalendarViewModel", "  - 是否浮动任务: ${task.isFloatingTask}")
                        Log.d("CalendarViewModel", "  - floatingWeekStart: ${task.floatingWeekStart}")
                        Log.d("CalendarViewModel", "  - floatingWeekEnd: ${task.floatingWeekEnd}")
                        Log.d("CalendarViewModel", "  - dateManuallyModified: ${task.dateManuallyModified}")
                        
                        CalendarEvent(
                            id = if (task.isFloatingTask == true) "floating_${task.id}" else "task_${task.id}",
                            title = if (task.isFloatingTask == true) task.title else task.title, // 不添加标识符，保持标题干净
                            start = startTime,
                            end = endTime,
                            isAllDay = false, // 除非任务明确标记为全天事件
                            color = color,
                            taskId = task.id,
                            isCompleted = task.completedAt != null,
                            urgency = convertPriorityToUrgency(task.priority?.ordinal ?: 0)
                        )
                    } else {
                        // 对于没有任何日期信息的任务，使用当前日期显示
                        val now = LocalDateTime.now()
                        val startTime = now.withHour(8).withMinute(0)
                        val endTime = startTime.plusMinutes(15) // 改为15分钟
                        
                        val color = getTaskColor(task.priority?.ordinal ?: 0)
                        
                        Log.d("CalendarViewModel", "创建无日期任务事件: ${task.title}, 默认到今天")
                        CalendarEvent(
                            id = if (task.isFloatingTask == true) "floating_${task.id}" else "task_${task.id}",
                            title = if (task.isFloatingTask == true) task.title else task.title,
                            start = startTime,
                            end = endTime,
                            isAllDay = false,
                            color = color,
                            taskId = task.id,
                            isCompleted = task.completedAt != null,
                            urgency = convertPriorityToUrgency(task.priority?.ordinal ?: 0)
                        )
                    }
                } catch (e: Exception) {
                    Log.e("CalendarViewModel", "处理任务出错: ${task.id}, ${e.message}", e)
                    null
                }
            }
        }
    }
    
    /**
     * 根据优先级获取任务颜色（包含80%透明度）- 使用莫兰迪色系
     */
    private fun getTaskColor(priorityOrdinal: Int): Long {
        val colors = listOf(
            0xFFC4B5D4, // 莫兰迪薰衣草紫 (低优先级/默认)
            0xFFD4C4B0, // 莫兰迪米色 (中低优先级)
            0xFFD4B5B8, // 莫兰迪玫瑰粉 (中高优先级)
            0xFFBFAFB0, // 莫兰迪灰粉 (高优先级)
            0xFFC4B5D4  // 莫兰迪薰衣草紫 (默认备用)
        )
        
        val baseColor = colors[priorityOrdinal.coerceIn(0, colors.size - 1)]
        val alpha = (0.8f * 255).toLong() // 80%透明度
        
        // 提取RGB通道并添加透明度
        val r = (baseColor shr 16) and 0xFF
        val g = (baseColor shr 8) and 0xFF
        val b = baseColor and 0xFF
        
        return (alpha shl 24) or (r shl 16) or (g shl 8) or b
    }
    
    /**
     * 优先级整数转枚举
     */
    private fun priorityFromInt(priority: Int): Priority? {
        return when (priority) {
            0 -> Priority.LOW
            1 -> Priority.MEDIUM
            2 -> Priority.HIGH
            3 -> Priority.URGENT
            else -> null
        }
    }
    
    /**
     * 将优先级转换为紧急程度
     */
    private fun convertPriorityToUrgency(priority: Int): TaskUrgency {
        return when(priority) {
            0 -> TaskUrgency.LOW
            1 -> TaskUrgency.MEDIUM
            2 -> TaskUrgency.HIGH
            3 -> TaskUrgency.CRITICAL
            else -> TaskUrgency.MEDIUM
        }
    }
    
    /**
     * 更新任务完成状态
     */
    fun updateTaskCompletion(taskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                taskRepository.updateTaskCompletion(taskId, isCompleted)
                // 重新加载事件以更新UI
                loadEvents()
                
                // 发布事件通知其他组件
                EventBus.tryEmit(AppEvent.TaskCompletionChanged(taskId, isCompleted))
            } catch (e: Exception) {
                Timber.e(e, "更新任务完成状态失败")
            }
        }
    }

    /**
     * 更新用户颜色偏好
     */
    fun updateUserColorPreference(newPreference: UserColorPreference) {
        _userColorPreference.value = newPreference
    }
    
    /**
     * 重置为默认颜色
     */
    fun resetToDefaultColors() {
        _userColorPreference.value = UserColorPreference()
    }
    
    /**
     * 更新特定页面的背景颜色
     */
    fun updatePageBackground(pageName: String, color: Long) {
        _userColorPreference.update { currentPreference -> 
            when (pageName) {
                "home" -> currentPreference.copy(homePageBackground = color, useCustomColors = true)
                "calendar" -> currentPreference.copy(calendarPageBackground = color, useCustomColors = true)
                "statistics" -> currentPreference.copy(statisticsPageBackground = color, useCustomColors = true)
                "profile" -> currentPreference.copy(settingsPageBackground = color, useCustomColors = true)
                "settings" -> currentPreference.copy(settingsPageBackground = color, useCustomColors = true)
                else -> currentPreference
            }
        }
        
        // 打印日志确认更新
        Log.d("CalendarViewModel", "手动更新页面背景: 页面=$pageName, 背景色=$color")
    }
    
    /**
     * 更新动态颜色设置
     */
    fun updateDynamicColorSetting(useDynamic: Boolean) {
        _userColorPreference.update { 
            it.copy(useDynamicColor = useDynamic) 
        }
    }
    
    /**
     * 更新Material You主题设置
     */
    fun updateMaterialYouSetting(useMaterialYou: Boolean) {
        _userColorPreference.update { 
            it.copy(useMaterialYou = useMaterialYou) 
        }
    }

    fun subscribeToEvents() {
        viewModelScope.launch {
            try {
                EventBus.events.collect { event ->
                    when (event) {
                        is AppEvent.TaskCompletionChanged,
                        is AppEvent.TaskUpdated,
                        is AppEvent.DatabaseRefreshed,
                        is AppEvent.TaskCreated,
                        is AppEvent.TaskDeleted -> {
                            Log.d(TAG, "📅 收到任务事件: ${event.javaClass.simpleName}")
                            loadEvents()
                            _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                        }
                        else -> { /* ignore other events */ }
                    }
                }
            } catch (e: Exception) {
                _eventFlow.emit(CalendarUiEvent.Error("Failed to subscribe to events: ${e.message}"))
                Log.e("CalendarViewModel", "Event subscription error", e)
            }
        }
    }

    fun unsubscribeFromEvents() {
        // Clean up resources
        viewModelScope.coroutineContext.cancelChildren()
    }

    /**
     * 更新事件时间
     */
    fun updateTaskTime(taskId: String, start: LocalDateTime, end: LocalDateTime) {
        viewModelScope.launch {
            try {
                // 转换ID格式 - 如果是"task_"开头则提取数字部分
                val numericId = if (taskId.startsWith("task_")) {
                    taskId.substring(5)
                } else {
                    taskId
                }
                
                Log.d("CalendarViewModel", "调用updateTaskTime: taskId=$taskId, numericId=$numericId")
                taskRepository.updateTaskDueDate(numericId, end)
                
                // 🔧 新增：标记为手动修改日期，确保不会被自动重新调度
                Log.d("CalendarViewModel", "标记任务为手动修改日期: taskId=$numericId")
                try {
                    taskRepository.updateTaskDateManuallyModified(numericId, true)
                } catch (e: Exception) {
                    Log.e("CalendarViewModel", "标记任务日期手动修改失败", e)
                }
                
                // 发送任务时间变更事件
                EventBus.tryEmit(AppEvent.TaskTimeChanged(numericId))
                
                // 刷新事件列表
                loadEvents()
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "更新事件时间失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 更新事件颜色
     */
    fun updateTaskColor(taskId: String, color: Long) {
        viewModelScope.launch {
            try {
                // 转换ID格式 - 如果是"task_"开头则提取数字部分
                val numericId = if (taskId.startsWith("task_")) {
                    taskId.substring(5)
                } else {
                    taskId
                }
                
                Log.d("CalendarViewModel", "开始更新事件颜色: taskId=$taskId, numericId=$numericId, color=${color.toString(16)}")
                
                // 首先立即更新内存中的状态，以便UI立刻响应
                val updatedEvents = _calendarState.value.events.map { event ->
                    if (event.id == taskId || (event.taskId != null && event.taskId == numericId)) {
                        val updatedEvent = event.copy(color = color)
                        Log.d("CalendarViewModel", "更新内存事件颜色: eventId=${event.id}, 新颜色=${color.toString(16)}")
                        updatedEvent
                    } else {
                        event
                    }
                }
                
                // 立即更新UI状态
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 通知UI事件已更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                // 然后异步更新数据库中的颜色
                taskRepository.updateTaskColor(numericId, color)
                Log.d("CalendarViewModel", "已更新数据库颜色记录")
                
                // 为防止其他事件处理覆盖更新的内存状态，我们使用一个标志来跟踪
                val lastUpdateTime = System.currentTimeMillis()
                lastColorUpdateTimestamp = lastUpdateTime
                
                // 发送自定义事件，避免触发完整的loadEvents
                EventBus.tryEmit(AppEvent.TaskColorChanged(numericId, color))
                
                // 最终再次通知UI刷新，并设置防抖标志
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                Log.d("CalendarViewModel", "事件颜色更新完成，UI已刷新")
                
                // 防抖延迟后再次检查和刷新，确保颜色修改被保留
                viewModelScope.launch {
                    delay(500) // 等待500毫秒，让其他可能的刷新操作完成
                    
                    // 如果自从我们上次更新后没有新的颜色更新，则检查颜色是否仍然正确
                    if (lastUpdateTime == lastColorUpdateTimestamp) {
                        // 检查当前事件列表中的颜色
                        val currentEvents = _calendarState.value.events
                        val targetEvent = currentEvents.find { it.id == taskId || (it.taskId != null && it.taskId == numericId) }
                        
                        // 如果事件存在且颜色不匹配，则再次更新
                        if (targetEvent != null && targetEvent.color != color) {
                            Log.d("CalendarViewModel", "检测到颜色不一致，再次强制更新: 当前=${targetEvent.color?.toString(16)}, 期望=${color.toString(16)}")
                            
                            // 再次更新事件列表
                            val refreshedEvents = currentEvents.map { event ->
                                if (event.id == taskId || (event.taskId != null && event.taskId == numericId)) {
                                    event.copy(color = color) 
                                } else {
                                    event
                                }
                            }
                            
                            // 更新状态并通知UI
                            _calendarState.value = _calendarState.value.copy(events = refreshedEvents)
                            _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "更新事件颜色失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("更新颜色失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 切换事件完成状态
     */
    fun toggleEventCompletion(eventId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                // 转换ID格式 - 如果是"task_"开头则提取数字部分
                val numericId = if (eventId.startsWith("task_")) {
                    eventId.substring(5)
                } else {
                    eventId
                }
                
                Log.d("CalendarViewModel", "调用toggleEventCompletion: eventId=$eventId, numericId=$numericId, isCompleted=$isCompleted")
                
                // 更新任务完成状态
                taskRepository.updateTaskCompletionStatus(numericId, isCompleted)
                
                // 更新内存中的状态
                val updatedEvents = _calendarState.value.events.map { event ->
                    if (event.id == eventId) {
                        event.copy(isCompleted = isCompleted)
                    } else {
                        event
                    }
                }
                
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 发送任务完成状态变更事件
                EventBus.tryEmit(AppEvent.TaskCompletionChanged(numericId, isCompleted))
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "切换事件完成状态失败: ${e.message}", e)
            }
        }
    }

    /**
     * 创建新事件
     */
    fun createEvent(
        title: String,
        start: LocalDateTime,
        end: LocalDateTime? = null,
        color: Long? = null
    ) {
        viewModelScope.launch {
            try {
                Log.d("时间调试", "=== createEvent 被调用 ===")
                Log.d("时间调试", "标题: $title")
                Log.d("时间调试", "原始开始时间: $start")
                Log.d("时间调试", "原始结束时间: $end")
                Log.d("CalendarViewModel", "创建新事件: $title, 开始时间: $start, 结束时间: $end")
                if (color != null) {
                    Log.d("CalendarViewModel", "事件颜色设置为: ${color.toString(16)}")
                } else {
                    Log.d("CalendarViewModel", "未指定事件颜色，将使用默认颜色")
                }
                
                // 创建新任务ID
                val taskId = UUID.randomUUID().toString()
                
                // 检查并处理特殊情况 - 开始时间等于结束时间
                val safeEnd = if (end != null && end == start) {
                    Log.d("CalendarViewModel", "检测到开始时间等于结束时间，自动调整为+15分钟")
                    start.plusMinutes(15) // 确保至少15分钟持续时间
                } else {
                    end
                }
                
                // 🔧 修复：尊重用户设置的时间，如果没有结束时间，默认15分钟
                val finalEnd = safeEnd ?: start.plusMinutes(15)
                
                Log.d("CalendarViewModel", "保持用户设置的时间: 开始=$start, 结束=$finalEnd")
                
                Log.d("CalendarViewModel", "调整后的结束时间: $finalEnd, 持续时间(分钟): ${ChronoUnit.MINUTES.between(start, finalEnd)}")
                
                // 创建新任务
                val task = ModelTask(
                    id = taskId,
                    title = title,
                    description = if (color != null) """{"color":${color}}""" else "",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    startDate = start, // 🔧 修复：设置正确的开始时间
                    dueDate = finalEnd,
                    priority = Priority.MEDIUM,
                    isCompleted = false,
                    // 用户在日历中创建事件时标记为手动修改
                    dateManuallyModified = true
                )
                
                // 保存任务
                taskRepository.saveTask(task)
                Log.d("CalendarViewModel", "已保存任务到数据库，ID: $taskId")
                Log.d("时间调试", "保存到数据库的任务时间:")
                Log.d("时间调试", "  - startDate: ${task.startDate}")
                Log.d("时间调试", "  - dueDate: ${task.dueDate}")
                
                // 创建新事件并添加到状态中
                val newEvent = CalendarEvent(
                    id = "task_$taskId",
                    title = title,
                    start = start,
                    end = finalEnd,
                    color = color,
                    taskId = taskId,
                    isCompleted = false,
                    urgency = TaskUrgency.MEDIUM
                )
                
                val updatedEvents = _calendarState.value.events + newEvent
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                Log.d("CalendarViewModel", "已更新内存中的事件列表")
                
                // 如果颜色不为空且Task保存成功，单独更新颜色
                if (color != null) {
                    try {
                        // 确保颜色信息已被保存
                        taskRepository.updateTaskColor(taskId, color)
                        Log.d("CalendarViewModel", "已单独更新任务颜色: ID=$taskId, color=${color.toString(16)}")
                    } catch (e: Exception) {
                        Log.e("CalendarViewModel", "更新任务颜色失败: ${e.message}", e)
                    }
                }
                
                // 发送任务创建事件
                EventBus.tryEmit(AppEvent.TaskCreated(taskId))
                
                // 通知UI更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                Log.d("CalendarViewModel", "新事件创建成功: $title, 开始时间: $start, 结束时间: $finalEnd, 颜色: ${color?.toString(16) ?: "默认"}")
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "创建新事件失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("创建事件失败: ${e.message}"))
            }
        }
    }

    private fun generateNewEventId(): String {
        return (_calendarState.value.events.maxOfOrNull { it.id.substring(5).toInt() }?.plus(1) ?: 1).toString()
    }

    /**
     * 加载用户颜色偏好设置
     */
    private fun loadUserColorPreferences() {
        viewModelScope.launch {
            try {
                // 不再直接设置为白色，而是保留当前颜色设置
                // 只有当当前颜色为默认白色时才尝试从ThemeManager获取颜色
                if (_userColorPreference.value.calendarPageBackground == 0xFFFFFFFFL) {
                    Log.d(TAG, "当前背景是白色，尝试从ThemeManager获取颜色设置")
                } else {
                    Log.d(TAG, "保留当前非白色背景: ${_userColorPreference.value.calendarPageBackground.toString(16)}")
                }
                
                // 立即请求主题设置状态更新
                EventBus.tryEmit(AppEvent.ThemeSettingsRequested)
                
                Log.d(TAG, "已请求主题设置状态更新")
                
                // 延迟一下等待响应
                try {
                    delay(100)
                    if (!isActive) return@launch // 检查协程是否仍然活跃
                    
                    Log.d(TAG, "当前背景色设置: calendar=${_userColorPreference.value.calendarPageBackground.toString(16)}")
                } catch (e: Exception) {
                    // 忽略取消异常
                    if (e !is CancellationException) {
                        Log.e(TAG, "背景色设置更新延迟失败", e)
                    }
                }
            } catch (e: Exception) {
                // 忽略取消异常
                if (e !is CancellationException) {
                    Log.e(TAG, "加载用户颜色偏好设置失败", e)
                }
            }
        }
    }

    /**
     * 创建新任务（从日历视图）
     */
    fun createTask(title: String, startTime: LocalDateTime, endTime: LocalDateTime?, urgency: TaskUrgency = TaskUrgency.MEDIUM) {
        viewModelScope.launch {
            try {
                // 检查和处理特殊情况 - 开始时间等于结束时间
                val safeEndTime = if (endTime != null && endTime == startTime) {
                    Log.d("CalendarViewModel", "检测到开始时间等于结束时间，自动调整为+15分钟")
                    startTime.plusMinutes(15) // 确保至少15分钟持续时间
                } else {
                    endTime
                }
                
                // 🔧 修复：尊重用户设置的时间，不自动调整持续时间
                val finalEndTime = safeEndTime ?: startTime.plusMinutes(15)
                
                // 创建临时事件（为UI展示）
                val tempEvent = CalendarEvent(
                    id = "temp_${System.currentTimeMillis()}",
                    title = title,
                    start = startTime,
                    end = finalEndTime,
                    color = getTaskColor(urgency.ordinal),
                    isCompleted = false,
                    urgency = urgency
                )
                
                // 立即更新UI以提供即时反馈
                val currentEvents = _calendarState.value.events
                _calendarState.value = _calendarState.value.copy(
                    events = currentEvents + tempEvent
                )
                
                // 创建任务数据模型
                val task = com.timeflow.app.data.model.Task(
                    id = UUID.randomUUID().toString(),
                    title = title,
                    description = "",
                    startDate = startTime,
                    dueDate = finalEndTime,
                    isCompleted = false,
                    priority = Priority.MEDIUM,
                    type = com.timeflow.app.data.model.TaskType.NORMAL
                )
                
                // 保存到数据库
                taskRepository.saveTask(task)
                
                // 刷新事件列表
                loadEvents()
                
                // 发送事件通知（触发任务列表视图更新）
                EventBus.tryEmit(AppEvent.TaskCreated(task.id))
                
                // 通知UI更新成功
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                Log.d("CalendarViewModel", "已创建新任务: ${task.id}, 标题: ${task.title}")
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "创建任务失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("创建任务失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 更新任务状态（完成状态）
     */
    fun updateTaskStatus(taskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                // 获取任务
                val task = taskRepository.getTaskById(taskId)
                
                if (task != null) {
                    // 更新任务状态
                    val updatedTask = task.copy(isCompleted = isCompleted)
                    
                    // 保存到数据库
                    taskRepository.updateTask(updatedTask)
                    
                    // 立即更新UI状态
                    val currentEvents = _calendarState.value.events.map { event ->
                        if (event.id == taskId) {
                            event.copy(isCompleted = isCompleted)
                        } else {
                            event
                        }
                    }
                    
                    _calendarState.value = _calendarState.value.copy(
                        events = currentEvents
                    )
                    
                    // 发送事件通知（触发任务列表视图更新）
                    EventBus.tryEmit(AppEvent.TaskCompletionChanged(taskId, isCompleted))
                    
                    // 通知UI更新成功
                    _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                    
                    Log.d("CalendarViewModel", "已更新任务状态: $taskId, 完成状态: $isCompleted")
                } else {
                    _eventFlow.emit(CalendarUiEvent.Error("未找到任务: $taskId"))
                }
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "更新任务状态失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("更新任务状态失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 删除任务
     */
    fun deleteTask(taskId: String) {
        viewModelScope.launch {
            try {
                // 转换ID格式 - 如果是"task_"开头则提取数字部分
                val numericId = if (taskId.startsWith("task_")) {
                    taskId.substring(5)
                } else {
                    taskId
                }
                
                Log.d("CalendarViewModel", "开始删除任务: taskId=$taskId, numericId=$numericId")
                
                // 从数据库删除
                taskRepository.deleteTask(numericId)
                
                // 立即从UI状态中移除 - 需要处理两种可能的ID格式
                val updatedEvents = _calendarState.value.events.filter { event -> 
                    // 检查所有可能的ID匹配情况
                    event.id != taskId && // 不匹配完整的taskId
                    event.id != "task_$numericId" && // 不匹配带前缀的ID
                    (event.taskId == null || event.taskId != numericId) // 不匹配taskId字段
                }
                
                Log.d("CalendarViewModel", "从事件列表中移除后的事件数量: ${updatedEvents.size} (原有${_calendarState.value.events.size})")
                
                // 立即更新UI状态
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 发送事件通知（触发任务列表视图更新）
                EventBus.tryEmit(AppEvent.TaskDeleted(numericId))
                
                // 通知UI更新成功
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                // 强制刷新事件列表，确保视图正确更新
                loadEvents()
                
                Log.d("CalendarViewModel", "已删除任务: $numericId, UI已更新")
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "删除任务失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("删除任务失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 将TaskUrgency转换为Priority
     */
    private fun getPriorityFromUrgency(urgency: TaskUrgency): com.timeflow.app.data.model.Priority {
        return when(urgency) {
            TaskUrgency.CRITICAL -> com.timeflow.app.data.model.Priority.URGENT
            TaskUrgency.HIGH -> com.timeflow.app.data.model.Priority.HIGH
            TaskUrgency.MEDIUM -> com.timeflow.app.data.model.Priority.MEDIUM
            TaskUrgency.LOW -> com.timeflow.app.data.model.Priority.LOW
        }
    }

    private fun updateColorFromTheme() {
        // 避免重复启动协程
        viewModelScope.launch {
            try {
                if (!isActive) return@launch
                
                val preference = ThemeManager.userThemePreference.value
                val colorArgb = preference.calendarPageColor.toArgb()
                val calendarColor = colorArgb.toLong() and 0xFFFFFFFFL
                
                // 检查是否与上次更新颜色相同，避免重复更新
                if (lastCalendarColorValue == calendarColor) {
                    Log.d(TAG, "从主题更新颜色: 颜色未变化 ${calendarColor.toString(16)}")
                    return@launch
                }
                
                // 检查是否是默认青色(#03dac6)，如果是则不更新
                if (calendarColor == 0xFF03DAC6L) {
                    Log.d(TAG, "从主题更新颜色: 检测到默认青色(#03dac6)，保持当前背景色")
                    return@launch
                }
                
                // 记录当前颜色值，避免重复更新
                lastCalendarColorValue = calendarColor
                
                Log.d(TAG, "从主题更新颜色: ${calendarColor.toString(16)}")
                
                _userColorPreference.update { pref ->
                    pref.copy(
                        calendarPageBackground = calendarColor,
                        useCustomColors = true
                    )
                }
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "从主题更新颜色失败: ${e.message}", e)
                }
            }
        }
    }

    /**
     * 清理资源，在ViewModel销毁前调用
     */
    override fun onCleared() {
        super.onCleared()
        // 取消所有协程，防止内存泄漏
        viewModelScope.coroutineContext.cancelChildren()
        Log.d(TAG, "ViewModel已清理，协程已取消")
    }

    /**
     * 保存用户颜色偏好设置到持久存储
     */
    private fun saveUserColorPreferences() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "保存颜色设置: ${_userColorPreference.value.homePageBackground.toString(16)}")
                // 实际应用中应保存到DataStore或SharedPreferences
            } catch (e: Exception) {
                Log.e(TAG, "保存颜色设置失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 确保背景色一致性，防止返回时颜色变为白色
     */
    fun ensureBackgroundColorConsistency() {
        viewModelScope.launch {
            try {
                // 记录当前使用的颜色
                val currentColors = _userColorPreference.value
                
                // 如果主页背景色是默认白色，检查是否可以从ThemeManager获取颜色
                if (currentColors.homePageBackground == 0xFFFFFFFFL || currentColors.homePageBackground == 0xFFF9F9F9L) {
                    Log.d(TAG, "检测到默认背景色，尝试从ThemeManager获取颜色")
                    
                    // 尝试从ThemeManager获取颜色而不是加载默认设置
                    try {
                        val preference = ThemeManager.userThemePreference.value
                        val colorArgb = preference.homePageColor.toArgb()
                        val homeColor = colorArgb.toLong() and 0xFFFFFFFFL
                        
                        // 只有在颜色不是默认青色的情况下才更新
                        if (homeColor != 0xFF03DAC6L && homeColor != 0L) {
                            Log.d(TAG, "从ThemeManager获取到主页颜色: ${homeColor.toString(16)}")
                            _userColorPreference.update { pref ->
                                pref.copy(homePageBackground = homeColor, useCustomColors = true)
                            }
                            
                            // 发布更新后的背景色事件
                            EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                                pageName = "home",
                                colorArgb = homeColor
                            ))
                        } else {
                            Log.d(TAG, "ThemeManager返回默认颜色，保持当前设置")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "获取ThemeManager颜色失败: ${e.message}")
                    }
                } else {
                    // 已有非默认背景色，发布背景色事件确保其他组件能正确获取
                    EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                        pageName = "home",
                        colorArgb = currentColors.homePageBackground
                    ))
                    Log.d(TAG, "发布主页背景色事件: ${currentColors.homePageBackground.toString(16)}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "确保背景色一致性失败: ${e.message}", e)
            }
        }
    }

    // 修改PageBackgroundChanged事件处理方法
    fun processPageBackgroundChanged(event: Any) {
        // 计算事件哈希值进行去重
        val eventHash = event.hashCode()
        if (eventHash == lastProcessedEventHash) {
            // 忽略重复事件
            return
        }
        lastProcessedEventHash = eventHash

        // 正常处理事件的逻辑
        Timber.d("收到PageBackgroundChanged事件：页面=${event}, 颜色=背景色变更")
        // ... 其余处理逻辑保持不变 ...
    }

    // 修改颜色提取方法，添加缓存
    private val colorExtractionCache = mutableMapOf<String, String>()

    // 提取任务颜色，使用缓存
    fun extractColorFromTask(task: Any): String? {
        val taskId = task.toString()
        // 检查缓存
        if (colorExtractionCache.containsKey(taskId)) {
            return colorExtractionCache[taskId]
        }
        
        // 原有提取逻辑
        val extractedColor = "默认颜色" // 简化版替代原有提取逻辑
        
        // 保存到缓存
        colorExtractionCache[taskId] = extractedColor
        
        return extractedColor
    }

    /**
     * 更新任务标题
     */
    fun updateTaskTitle(taskId: String, newTitle: String) {
        viewModelScope.launch {
            try {
                // 转换ID格式 - 如果是"task_"开头则提取数字部分
                val numericId = if (taskId.startsWith("task_")) {
                    taskId.substring(5)
                } else {
                    taskId
                }
                
                Log.d("CalendarViewModel", "开始更新事件标题: taskId=$taskId, numericId=$numericId, 新标题=$newTitle")
                
                // 首先立即更新内存中的状态，以便UI立刻响应
                val updatedEvents = _calendarState.value.events.map { event ->
                    if (event.id == taskId || (event.taskId != null && event.taskId == numericId)) {
                        val updatedEvent = event.copy(title = newTitle)
                        Log.d("CalendarViewModel", "更新内存事件标题: eventId=${event.id}, 新标题=$newTitle")
                        updatedEvent
                    } else {
                        event
                    }
                }
                
                // 立即更新UI状态
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 通知UI事件已更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                // 更新数据库中的标题
                val task = taskRepository.getTaskById(numericId)
                if (task != null) {
                    val updatedTask = task.copy(
                        title = newTitle,
                        updatedAt = LocalDateTime.now()
                    )
                    taskRepository.updateTask(updatedTask)
                    Log.d("CalendarViewModel", "已更新数据库任务标题")
                } else {
                    Log.e("CalendarViewModel", "未找到任务: $numericId")
                }
                
                // 发送任务更新事件
                EventBus.tryEmit(AppEvent.TaskUpdated(numericId))
                
                // 再次通知UI刷新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                Log.d("CalendarViewModel", "事件标题更新完成，UI已刷新")
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "更新事件标题失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("更新标题失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 更新事件详情
     */
    fun updateEventDetails(
        eventId: String,
        title: String,
        start: LocalDateTime,
        end: LocalDateTime
    ) {
        updateEventDetails(eventId, title, start, end, null)
    }
    
    /**
     * 更新事件详情（包含颜色）
     */
    fun updateEventDetails(
        eventId: String,
        title: String,
        start: LocalDateTime,
        end: LocalDateTime,
        color: Long? = null
    ) {
        viewModelScope.launch {
            try {
                // 转换ID格式
                val numericId = if (eventId.startsWith("task_")) {
                    eventId.substring(5)
                } else {
                    eventId
                }
                
                Log.d("CalendarViewModel", "更新事件详情: $eventId -> $numericId, 颜色: $color")
                
                // 立即更新内存状态，确保UI快速响应
                val updatedEvents = _calendarState.value.events.map { event ->
                    if (event.id == eventId || (event.taskId != null && event.taskId == numericId)) {
                        event.copy(
                            title = title,
                            start = start,
                            end = end,
                            color = color
                        )
                    } else {
                        event
                    }
                }
                
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 立即通知UI更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                // 异步更新数据库（在后台进行）
                withContext(Dispatchers.IO) {
                    val task = taskRepository.getTaskById(numericId)
                    if (task != null) {
                        // 如果有颜色更新，需要清除相关缓存
                        if (color != null) {
                            colorExtractionCache.remove(numericId)
                        }
                        
                        // 更新任务描述，包含颜色信息
                        val newDescription = if (color != null) {
                            // 在描述中添加或更新颜色信息
                            val existingDescription = task.description ?: ""
                            val colorPattern = """"color"\s*:\s*\d+""".toRegex()
                            
                            if (colorPattern.containsMatchIn(existingDescription)) {
                                // 替换现有颜色
                                existingDescription.replace(colorPattern, "\"color\":${color}")
                            } else {
                                // 添加颜色信息
                                if (existingDescription.isEmpty()) {
                                    "{\"color\":${color}}"
                                } else {
                                    "$existingDescription {\"color\":${color}}"
                                }
                            }
                        } else {
                            task.description
                        }
                        
                        val updatedTask = task.copy(
                            title = title,
                            startDate = start,
                            dueDate = end,
                            description = newDescription,
                            updatedAt = LocalDateTime.now()
                        )
                        taskRepository.updateTask(updatedTask)
                        Log.d("CalendarViewModel", "事件详情更新成功，颜色已保存到描述: $color")
                        
                        // 发送任务更新事件
                        EventBus.tryEmit(AppEvent.TaskUpdated(numericId))
                    }
                }
                
                // 如果更新了颜色，强制重新加载以确保数据一致性
                if (color != null) {
                    delay(300) // 短暂延迟确保数据库写入完成
                    
                    // 清除缓存并重新加载
                    withContext(Dispatchers.Main) {
                        colorExtractionCache.clear()
                        refreshCalendarData()
                        
                        // 再次通知UI更新
                        _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                        Log.d("CalendarViewModel", "颜色更新后强制刷新完成")
                    }
                }
                
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "更新事件详情失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("更新事件失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 删除事件
     */
    fun deleteEvent(eventId: String) {
        viewModelScope.launch {
            try {
                // 转换ID格式
                val numericId = if (eventId.startsWith("task_")) {
                    eventId.substring(5)
                } else {
                    eventId
                }
                
                Log.d("CalendarViewModel", "删除事件: $eventId -> $numericId")
                
                // 从内存中移除
                val updatedEvents = _calendarState.value.events.filter { event ->
                    event.id != eventId && (event.taskId == null || event.taskId != numericId)
                }
                
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 从数据库删除
                taskRepository.deleteTask(numericId)
                
                // 通知UI更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                EventBus.tryEmit(AppEvent.TaskDeleted(numericId))
                
                Log.d("CalendarViewModel", "事件删除成功")
                
            } catch (e: Exception) {
                Log.e("CalendarViewModel", "删除事件失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("删除事件失败: ${e.message}"))
            }
        }
    }

    // 🆕 浮动任务相关方法
    
    /**
     * 获取指定周范围内的浮动任务Flow
     */
    fun getFloatingTasksFlow(weekStart: LocalDateTime, weekEnd: LocalDateTime): kotlinx.coroutines.flow.Flow<List<ModelTask>> {
        Log.d(TAG, "=== 获取浮动任务Flow ===")
        Log.d(TAG, "查询时间范围: $weekStart 到 $weekEnd")
        
        return taskRepository.observeFloatingTasksInWeek(weekStart, weekEnd)
            .onEach { tasks ->
                Log.d(TAG, "浮动任务Flow发出数据: ${tasks.size} 个任务")
                tasks.forEachIndexed { index, task ->
                    Log.d(TAG, "浮动任务[$index]: ${task.title}")
                    Log.d(TAG, "  - isFloatingTask: ${task.isFloatingTask}")
                    Log.d(TAG, "  - floatingWeekStart: ${task.floatingWeekStart}")
                    Log.d(TAG, "  - floatingWeekEnd: ${task.floatingWeekEnd}")
                    Log.d(TAG, "  - isScheduled: ${task.isScheduled()}")
                }
            }
    }
    
    /**
     * 安排浮动任务到具体日期
     */
    fun scheduleFloatingTask(taskId: String, scheduledDate: LocalDateTime) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "安排浮动任务: taskId=$taskId, 安排到=$scheduledDate")
                taskRepository.scheduleFloatingTask(taskId, scheduledDate)
                
                // 刷新日历数据以反映变化
                refreshCalendarData()
                
                Log.d(TAG, "浮动任务安排成功")
            } catch (e: Exception) {
                Log.e(TAG, "安排浮动任务失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("安排任务失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 取消浮动任务的具体日期安排
     */
    fun unscheduleFloatingTask(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "取消浮动任务安排: taskId=$taskId")
                taskRepository.unscheduleFloatingTask(taskId)
                
                // 刷新日历数据以反映变化
                refreshCalendarData()
                
                Log.d(TAG, "浮动任务取消安排成功")
            } catch (e: Exception) {
                Log.e(TAG, "取消浮动任务安排失败: ${e.message}", e)
                _eventFlow.emit(CalendarUiEvent.Error("取消安排失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 创建浮动任务 - 增强版本
     */
    fun createFloatingTask(
        title: String,
        description: String = "",
        priority: Priority? = Priority.MEDIUM,
        weekStart: LocalDateTime,
        weekEnd: LocalDateTime,
        estimatedMinutes: Int = 30
    ) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== 开始创建浮动任务 ===")
                Log.d(TAG, "任务详情: title=$title, description=$description")
                Log.d(TAG, "优先级: $priority")
                Log.d(TAG, "时间范围: $weekStart 到 $weekEnd")
                Log.d(TAG, "预估时间: ${estimatedMinutes}分钟")
                
                val task = taskRepository.createFloatingTask(
                    title = title,
                    description = description,
                    priority = priority ?: Priority.MEDIUM,
                    weekStart = weekStart,
                    weekEnd = weekEnd,
                    estimatedMinutes = estimatedMinutes
                )
                
                Log.d(TAG, "✓ 浮动任务创建成功: taskId=${task.id}")
                Log.d(TAG, "任务类型: isFloatingTask=${task.isFloatingTask}")
                Log.d(TAG, "浮动周期: ${task.floatingWeekStart} 到 ${task.floatingWeekEnd}")
                
                // 等待数据库操作完成
                kotlinx.coroutines.delay(200)
                
                // 刷新日历数据以显示新任务
                Log.d(TAG, "刷新日历数据...")
                refreshCalendarData()
                
                // 触发UI更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                Log.d(TAG, "✓ 浮动任务创建流程完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "创建浮动任务失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("创建任务失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 删除浮动任务
     */
    fun deleteFloatingTask(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== 开始删除浮动任务 ===")
                Log.d(TAG, "任务ID: $taskId")
                
                // 从数据库删除任务
                taskRepository.deleteTask(taskId)
                
                // 从UI状态中立即移除相关事件
                val updatedEvents = _calendarState.value.events.filter { event ->
                    event.id != "floating_$taskId" && 
                    event.id != "task_$taskId" && 
                    event.taskId != taskId
                }
                
                _calendarState.value = _calendarState.value.copy(events = updatedEvents)
                
                // 通知UI更新
                _eventFlow.emit(CalendarUiEvent.EventsUpdated)
                
                // 刷新日历数据
                refreshCalendarData()
                
                Log.d(TAG, "✓ 浮动任务删除成功: $taskId")
                
            } catch (e: Exception) {
                Log.e(TAG, "删除浮动任务失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("删除任务失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 选择日期 - 用于切换日视图到指定日期
     */
    fun selectDate(date: LocalDate) {
        Log.d(TAG, "选择日期: $date")
        _selectedDate.value = date
        
        // 同时更新年月状态，确保月视图也正确显示
        val newYearMonth = YearMonth.from(date)
        if (_currentYearMonth.value != newYearMonth) {
            _currentYearMonth.value = newYearMonth
        }
    }
    
    /**
     * 选择年月 - 用于月视图导航
     */
    fun selectYearMonth(yearMonth: YearMonth) {
        Log.d(TAG, "选择年月: $yearMonth")
        _currentYearMonth.value = yearMonth
        
        // 如果当前选中的日期不在新的年月中，调整到该月的第一天
        val currentSelected = _selectedDate.value
        if (YearMonth.from(currentSelected) != yearMonth) {
            _selectedDate.value = yearMonth.atDay(1)
        }
    }
    
    /**
     * 🔧 新增：切换任务完成状态
     * 用于日历日视图中的完成按钮功能
     */
    fun toggleTaskCompletion(taskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== 切换任务完成状态 ===")
                Log.d(TAG, "任务ID: $taskId")
                Log.d(TAG, "新完成状态: $isCompleted")
                
                // 🔧 先查询当前任务状态，用于验证
                val currentTask = taskRepository.getTaskById(taskId)
                if (currentTask == null) {
                    Log.e(TAG, "❌ 找不到任务: $taskId")
                    _eventFlow.emit(CalendarUiEvent.Error("找不到要更新的任务"))
                    return@launch
                }
                
                Log.d(TAG, "当前任务状态: 标题=${currentTask.title}, 完成状态=${currentTask.isCompleted}")
                
                // 更新数据库中的任务状态
                Log.d(TAG, "📝 开始更新数据库...")
                taskRepository.updateTaskCompletion(taskId, isCompleted)
                
                // 🔧 验证更新结果
                delay(200) // 等待数据库操作完成
                val updatedTask = taskRepository.getTaskById(taskId)
                if (updatedTask != null) {
                    Log.d(TAG, "✅ 更新后任务状态: 标题=${updatedTask.title}, 完成状态=${updatedTask.isCompleted}")
                    if (updatedTask.isCompleted == isCompleted) {
                        Log.d(TAG, "✅ 任务状态更新验证成功")
                    } else {
                        Log.e(TAG, "❌ 任务状态更新验证失败: 期望=$isCompleted, 实际=${updatedTask.isCompleted}")
                    }
                } else {
                    Log.e(TAG, "❌ 更新后无法找到任务")
                }
                
                // 刷新日历数据以反映状态变化
                Log.d(TAG, "🔄 刷新日历数据...")
                refreshCalendarData()
                
                // 发送事件通知其他组件更新
                Log.d(TAG, "📡 发送事件通知...")
                EventBus.tryEmit(AppEvent.TaskCompletionChanged(taskId, isCompleted))
                
                Log.d(TAG, "✓ 任务完成状态切换流程完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 切换任务完成状态失败", e)
                _eventFlow.emit(CalendarUiEvent.Error("更新任务状态失败: ${e.message}"))
            }
        }
    }
} 