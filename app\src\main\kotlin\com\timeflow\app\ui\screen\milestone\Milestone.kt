package com.timeflow.app.ui.screen.milestone

import java.time.LocalDate

/**
 * 里程碑数据模型
 */
data class Milestone(
    val id: String,                      // 唯一标识符
    val title: String,                   // 标题
    val description: String,             // 描述
    val date: LocalDate,                 // 日期
    val category: MilestoneCategory,     // 类别
    val tags: List<String> = emptyList(),// 标签
    val rating: Float = 0f,              // 评分 (0-5)
    val completionPercentage: Float = 0f,// 完成度百分比 (0-100)
    val milestoneType: MilestoneType = MilestoneType.REGULAR, // 里程碑类型
    val imageUris: List<String> = emptyList() // 修改为支持多张图片
) {
    // 向下兼容，保留单张图片属性
    val imageUri: String?
        get() = imageUris.firstOrNull()
} 