package com.timeflow.app.data.ai.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * AI时间预估数据模型 - 基于历史数据预测任务时长
 */
data class AiTimeEstimation(
    val id: String = UUID.randomUUID().toString(),
    val taskId: String,
    val estimatedMinutes: Int,
    val confidence: Float, // 置信度 0.0-1.0
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val basis: String, // 预测依据说明
    val similarTasks: List<SimilarTaskReference> = emptyList(),
    val adjustmentFactors: List<AdjustmentFactor> = emptyList()
)

/**
 * 相似任务引用 - 用于解释时间预估的依据
 */
data class SimilarTaskReference(
    val taskId: String,
    val taskTitle: String,
    val actualMinutes: Int,
    val similarity: Float, // 相似度 0.0-1.0
    val completedAt: LocalDateTime
)

/**
 * 调整因子 - 影响时间预估的因素
 */
data class AdjustmentFactor(
    val name: String,
    val impact: Float, // 影响系数 (-1.0 到 1.0)，负值表示减少时间，正值表示增加时间
    val description: String
)

/**
 * 用户工作效率数据 - 用于优化时间预估
 */
data class UserEfficiencyData(
    val userId: String,
    val timeOfDayEfficiency: Map<TimeOfDay, Float>, // 不同时段的效率系数
    val dayOfWeekEfficiency: Map<Int, Float>, // 一周中不同天的效率系数 (1-7)
    val taskTypeEfficiency: Map<String, Float>, // 不同任务类型的效率系数
    val averageEfficiencyTrend: List<EfficiencyTrendPoint> = emptyList(), // 效率趋势
    val lastUpdated: LocalDateTime = LocalDateTime.now()
)

/**
 * 效率趋势点 - 记录一段时间内的效率变化
 */
data class EfficiencyTrendPoint(
    val date: LocalDateTime,
    val efficiency: Float, // 效率系数
    val taskCount: Int // 该时段完成的任务数
) 