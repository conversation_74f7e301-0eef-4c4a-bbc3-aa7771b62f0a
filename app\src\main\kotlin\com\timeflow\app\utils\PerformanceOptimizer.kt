package com.timeflow.app.utils

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Process
import android.os.StrictMode
import android.util.Log
import android.view.Choreographer
import android.view.FrameMetrics
import android.view.Window
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.core.os.TraceCompat
import androidx.core.view.doOnPreDraw
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import timber.log.Timber

/**
 * 性能优化统一管理器
 * 集中管理应用性能优化相关功能
 */
object PerformanceOptimizer {
    private const val TAG = "PerformanceOptimizer"
    
    // 性能统计数据
    private var frameDropCount = 0
    private var totalFrames = 0
    private var lastFrameTimeNanos = 0L
    
    // 帧率监控状态
    private var isFrameMonitoringEnabled = false
    private val frameCallback = object : Choreographer.FrameCallback {
        override fun doFrame(frameTimeNanos: Long) {
            if (lastFrameTimeNanos > 0) {
                val frameDuration = frameTimeNanos - lastFrameTimeNanos
                val frameDurationMs = TimeUnit.NANOSECONDS.toMillis(frameDuration)
                
                // 检测卡顿帧 (大于16ms视为卡顿)
                if (frameDurationMs > 16) {
                    frameDropCount++
                    if (frameDurationMs > 100) {
                        // 仅记录严重卡顿，减少日志输出频率
                        Timber.w("严重卡顿: ${frameDurationMs}ms")
                    }
                }
                
                totalFrames++

                // 每100帧才计算一次丢帧率，减少计算频率
                if (totalFrames % 100 == 0) {
                    val dropRate = (frameDropCount.toFloat() / totalFrames) * 100
                    if (dropRate > 5) {
                        Timber.w("帧率下降: 丢帧率 %.1f%% (%d/%d)", dropRate, frameDropCount, totalFrames)
                    }
                    
                    // 每1000帧重置计数器，避免长时间累积导致的误差
                    if (totalFrames >= 1000) {
                        // 保持比例，但减少数值大小，避免溢出
                        frameDropCount = (frameDropCount / 10)
                        totalFrames = 100
                    }
                }
            }
            
            lastFrameTimeNanos = frameTimeNanos
            
            // 继续监听下一帧
            if (isFrameMonitoringEnabled) {
                // 使用延迟发布帧回调，减少每一帧的压力
                Handler(Looper.getMainLooper()).post {
                    Choreographer.getInstance().postFrameCallback(this)
                }
            }
        }
    }
    
    // 应用启动跟踪器
    private val startupScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private var startTime = 0L
    
    // 优化配置
    private var isInitialized = false
    private var enableLazyUiInitialization = true
    private var enableStrictModeForDebug = false
    
    // 数据库预热状态
    private val _isDatabaseWarmedUp = MutableStateFlow(false)
    val isDatabaseWarmedUp: StateFlow<Boolean> = _isDatabaseWarmedUp.asStateFlow()
    
    /**
     * 初始化性能优化器
     * @param application 应用实例
     */
    fun initialize(application: Application) {
        if (isInitialized) return
        
        startTime = System.currentTimeMillis()
        TraceCompat.beginSection("AppStartup")
        
        // 注册Activity生命周期监听
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                setupActivityOptimizations(activity)
            }
            
            override fun onActivityStarted(activity: Activity) {}
            
            override fun onActivityResumed(activity: Activity) {
                // 优化渲染性能
                activity.window?.let { window ->
                    window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
                    
                    // 标记页面可见时间点
                    val pageVisibleTime = System.currentTimeMillis() - startTime
                    Timber.d("页面可见耗时: ${pageVisibleTime}ms")
                    
                    // 延迟处理低优先级任务
                    Handler(Looper.getMainLooper()).postDelayed({
                        RenderOptimizer.setupWindowOptimizations(activity)
                    }, 100)
                }
            }
            
            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {}
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            
            override fun onActivityDestroyed(activity: Activity) {
                // 清理资源
                if (activity is ComponentActivity) {
                    cleanupActivityResources(activity)
                }
            }
        })
        
        // 配置严格模式（仅开发环境）
        if (enableStrictModeForDebug) {
            setupStrictMode()
        }
        
        // 启动帧率监控
        startFrameMonitoring()
        
        isInitialized = true
        Timber.i("性能优化器初始化完成")
    }
    
    /**
     * 为单个Activity设置性能优化
     */
    private fun setupActivityOptimizations(activity: Activity) {
        // 优化Activity创建流程
        if (activity is ComponentActivity) {
            // 在视图绘制前执行性能优化
            activity.window.decorView.doOnPreDraw {
                // 已完成首屏绘制，记录时间
                val firstDrawTime = System.currentTimeMillis() - startTime
                Timber.d("首屏绘制完成: ${firstDrawTime}ms")
                TraceCompat.endSection() // 结束AppStartup跟踪
            }
        }
        
        // 设置硬件加速
        activity.window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
    }
    
    /**
     * 清理Activity资源
     */
    private fun cleanupActivityResources(activity: ComponentActivity) {
        // 清理可能导致内存泄漏的资源
    }
    
    /**
     * 设置严格模式（仅开发环境使用）
     */
    private fun setupStrictMode() {
        StrictMode.setThreadPolicy(
            StrictMode.ThreadPolicy.Builder()
                .detectDiskReads()
                .detectDiskWrites()
                .detectNetwork()
                .penaltyLog()
                .build()
        )
        
        StrictMode.setVmPolicy(
            StrictMode.VmPolicy.Builder()
                .detectLeakedSqlLiteObjects()
                .detectLeakedClosableObjects()
                .detectActivityLeaks()
                .penaltyLog()
                .build()
        )
    }
    
    /**
     * 启动帧率监控
     */
    private fun startFrameMonitoring() {
        if (isFrameMonitoringEnabled) return
        
        isFrameMonitoringEnabled = true
        frameDropCount = 0
        totalFrames = 0
        lastFrameTimeNanos = 0
        
        // 开始监听帧回调
        Choreographer.getInstance().postFrameCallback(frameCallback)
        
        // 定期报告性能数据
        startupScope.launch {
            while (isFrameMonitoringEnabled) {
                delay(5000) // 每5秒报告一次
                
                if (totalFrames > 0) {
                    val dropRate = (frameDropCount.toFloat() / totalFrames) * 100
                    if (dropRate > 5) {
                        Timber.w("帧率下降: 丢帧率 %.1f%% (%d/%d)", dropRate, frameDropCount, totalFrames)
                    }
                }
            }
        }
    }
    
    /**
     * 停止帧率监控
     */
    fun stopFrameMonitoring() {
        isFrameMonitoringEnabled = false
        startupScope.cancel()
    }
    
    /**
     * 预热数据库（在后台线程执行常用查询）
     * @param context 上下文
     * @param warmupAction 预热操作
     */
    fun preloadDatabase(context: Context, warmupAction: suspend () -> Unit) {
        if (_isDatabaseWarmedUp.value) return
        
        // 使用IO线程执行预热
        CoroutineScope(Dispatchers.IO + SupervisorJob()).launch {
            TraceCompat.beginSection("DatabasePreload")
            try {
                // 设置线程优先级为后台
                Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND)
                
                // 执行预热操作
                warmupAction()
                
                // 标记预热完成
                _isDatabaseWarmedUp.value = true
                Timber.d("数据库预热完成")
            } catch (e: Exception) {
                Timber.e(e, "数据库预热失败")
            } finally {
                TraceCompat.endSection()
            }
        }
    }
    
    /**
     * 通用懒加载辅助函数，用于延迟初始化非关键组件
     * @param delayMs 延迟加载时间（毫秒）
     * @param loadAction 加载操作
     */
    fun lazyInit(delayMs: Long = 1000, loadAction: () -> Unit) {
        if (!enableLazyUiInitialization) {
            loadAction()
            return
        }
        
        Handler(Looper.getMainLooper()).postDelayed({
            loadAction()
        }, delayMs)
    }
    
    /**
     * 释放不需要的资源
     * 在应用退出或内存不足时调用
     */
    fun releaseResources() {
        stopFrameMonitoring()
    }
} 