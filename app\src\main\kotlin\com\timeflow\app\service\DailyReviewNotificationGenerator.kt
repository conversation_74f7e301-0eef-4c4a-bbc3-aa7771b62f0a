package com.timeflow.app.service

import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.format.DateTimeFormatter
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 每日回顾通知内容生成器
 * 基于实际数据生成个性化的通知内容
 */
@Singleton
class DailyReviewNotificationGenerator @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "DailyReviewNotificationGenerator"
    }

    /**
     * 生成通知内容
     */
    fun generateNotificationContent(reviewData: DailyReviewData): NotificationContent {
        Log.d(TAG, "开始生成通知内容，评分: ${reviewData.overallScore}")
        
        val emoji = getEmoji(reviewData.overallScore)
        val title = generateTitle(reviewData, emoji)
        val shortText = generateShortText(reviewData)
        val longText = generateLongText(reviewData)
        
        return NotificationContent(
            emoji = emoji,
            title = title,
            shortText = shortText,
            longText = longText
        )
    }

    /**
     * 根据评分获取表情符号
     */
    private fun getEmoji(score: Int): String {
        return when {
            score >= 90 -> "🎉"
            score >= 80 -> "🌟"
            score >= 70 -> "👍"
            score >= 60 -> "💪"
            score >= 50 -> "📈"
            score >= 40 -> "🎯"
            score >= 30 -> "💡"
            else -> "🌱"
        }
    }

    /**
     * 生成标题
     */
    private fun generateTitle(reviewData: DailyReviewData, emoji: String): String {
        val dateStr = reviewData.date.format(DateTimeFormatter.ofPattern("M月d日"))
        
        return when {
            reviewData.overallScore >= 90 -> "$emoji $dateStr 完美的一天"
            reviewData.overallScore >= 80 -> "$emoji $dateStr 高效的一天"
            reviewData.overallScore >= 70 -> "$emoji $dateStr 充实的一天"
            reviewData.overallScore >= 60 -> "$emoji $dateStr 不错的一天"
            reviewData.overallScore >= 50 -> "$emoji $dateStr 平稳的一天"
            reviewData.overallScore >= 40 -> "$emoji $dateStr 努力的一天"
            else -> "$emoji $dateStr 新的开始"
        }
    }

    /**
     * 生成简短文本
     */
    private fun generateShortText(reviewData: DailyReviewData): String {
        val taskText = if (reviewData.taskStats.totalTasks > 0) {
            "完成 ${reviewData.taskStats.completedTasks}/${reviewData.taskStats.totalTasks} 个任务"
        } else {
            "今日无任务记录"
        }
        
        val scoreText = "效率评分 ${reviewData.overallScore}分"
        
        return "$taskText，$scoreText"
    }

    /**
     * 生成详细文本
     */
    private fun generateLongText(reviewData: DailyReviewData): String {
        val builder = StringBuilder()
        
        // 添加日期
        val dateStr = reviewData.date.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))
        builder.appendLine("📅 $dateStr 每日回顾")
        builder.appendLine()
        
        // 添加总体评分
        builder.appendLine("🎯 今日效率评分：${reviewData.overallScore}/100")
        builder.appendLine()
        
        // 添加各项统计
        addTaskSummary(builder, reviewData.taskStats)
        addFocusSummary(builder, reviewData.focusStats)
        addHabitSummary(builder, reviewData.habitStats)
        addReflectionSummary(builder, reviewData.reflectionStats)
        addGoalSummary(builder, reviewData.goalStats)
        
        // 添加洞察
        if (reviewData.insights.isNotEmpty()) {
            builder.appendLine("💡 今日洞察：")
            reviewData.insights.take(2).forEach { insight ->
                builder.appendLine("• $insight")
            }
            builder.appendLine()
        }
        
        // 添加建议
        if (reviewData.recommendations.isNotEmpty()) {
            builder.appendLine("🚀 明日建议：")
            reviewData.recommendations.take(2).forEach { recommendation ->
                builder.appendLine("• $recommendation")
            }
            builder.appendLine()
        }
        
        builder.appendLine("点击查看详细分析和更多建议")
        
        return builder.toString().trim()
    }

    /**
     * 添加任务摘要
     */
    private fun addTaskSummary(builder: StringBuilder, taskStats: TaskStats) {
        if (taskStats.totalTasks > 0) {
            builder.appendLine("✅ 任务：${taskStats.completedTasks}/${taskStats.totalTasks} 个 (${taskStats.completionRate}%)")
            
            if (taskStats.highPriorityTasks > 0) {
                builder.append("   高优先级：${taskStats.completedHighPriorityTasks}/${taskStats.highPriorityTasks} 个")
            }
            
            if (taskStats.overdueTasks > 0) {
                builder.append("   逾期：${taskStats.overdueTasks} 个")
            }
            
            builder.appendLine()
        }
    }

    /**
     * 添加专注摘要
     */
    private fun addFocusSummary(builder: StringBuilder, focusStats: FocusStats) {
        if (focusStats.totalFocusTime > 0) {
            val hours = focusStats.totalFocusTime / 3600
            val minutes = (focusStats.totalFocusTime % 3600) / 60
            
            val timeText = when {
                hours > 0 -> "${hours}小时${minutes}分钟"
                minutes > 0 -> "${minutes}分钟"
                else -> "${focusStats.totalFocusTime}秒"
            }
            
            builder.appendLine("⏰ 专注：$timeText (${focusStats.sessionCount} 次会话)")
        }
    }

    /**
     * 添加习惯摘要
     */
    private fun addHabitSummary(builder: StringBuilder, habitStats: HabitStats) {
        if (habitStats.totalHabits > 0) {
            builder.appendLine("🔄 习惯：${habitStats.completedHabits}/${habitStats.totalHabits} 个 (${habitStats.completionRate}%)")
        }
    }

    /**
     * 添加感想摘要
     */
    private fun addReflectionSummary(builder: StringBuilder, reflectionStats: ReflectionStats) {
        if (reflectionStats.reflectionCount > 0) {
            val ratingText = String.format("%.1f", reflectionStats.averageRating)
            builder.appendLine("📝 感想：${reflectionStats.reflectionCount} 条 (平均评分 $ratingText)")
            
            // 添加心情分布
            val topMood = reflectionStats.moodDistribution.maxByOrNull { it.value }
            if (topMood != null && topMood.key != "未知") {
                builder.appendLine("   主要心情：${topMood.key}")
            }
        }
    }

    /**
     * 添加目标摘要
     */
    private fun addGoalSummary(builder: StringBuilder, goalStats: GoalStats) {
        if (goalStats.totalActiveGoals > 0) {
            val progressText = String.format("%.1f", goalStats.averageProgress * 100)
            builder.appendLine("🎯 目标：${goalStats.totalActiveGoals} 个进行中 (平均进度 $progressText%)")
            
            if (goalStats.completedTodayGoals > 0) {
                builder.appendLine("   今日完成：${goalStats.completedTodayGoals} 个")
            }
        }
    }

    /**
     * 生成个性化的鼓励消息
     */
    fun generateEncouragementMessage(reviewData: DailyReviewData): String {
        val score = reviewData.overallScore
        val taskRate = reviewData.taskStats.completionRate
        val focusTime = reviewData.focusStats.totalFocusTime
        
        return when {
            score >= 90 -> {
                val messages = listOf(
                    "今天的表现堪称完美！你是时间管理的大师！",
                    "太棒了！今天的效率让人印象深刻，继续保持！",
                    "卓越的一天！你的专注和执行力都很出色！"
                )
                messages.random()
            }
            score >= 80 -> {
                val messages = listOf(
                    "今天的效率很高，你正在朝着目标稳步前进！",
                    "干得漂亮！今天的成果值得骄傲！",
                    "高效的一天！你的努力正在得到回报！"
                )
                messages.random()
            }
            score >= 70 -> {
                val messages = listOf(
                    "今天完成得不错，保持这种节奏！",
                    "稳步前进！今天的努力为明天打下了基础！",
                    "不错的表现！继续保持这种状态！"
                )
                messages.random()
            }
            score >= 60 -> {
                val messages = listOf(
                    "今天有不错的进展，继续加油！",
                    "虽然还有提升空间，但今天的努力值得肯定！",
                    "每一步都是进步，今天做得不错！"
                )
                messages.random()
            }
            score >= 50 -> {
                val messages = listOf(
                    "今天完成了一半目标，明天可以做得更好！",
                    "有进步就是好的开始，继续努力！",
                    "今天的基础不错，明天可以更进一步！"
                )
                messages.random()
            }
            score >= 40 -> {
                val messages = listOf(
                    "今天虽然进展有限，但每一份努力都有意义！",
                    "不要气馁，明天是新的开始！",
                    "今天的经验将帮助你明天做得更好！"
                )
                messages.random()
            }
            else -> {
                val messages = listOf(
                    "今天可能不太顺利，但明天充满新的可能！",
                    "每个人都有低谷期，重要的是不放弃！",
                    "今天是学习的一天，明天会更好！"
                )
                messages.random()
            }
        }
    }
}

/**
 * 通知内容数据类
 */
data class NotificationContent(
    val emoji: String,
    val title: String,
    val shortText: String,
    val longText: String
)
