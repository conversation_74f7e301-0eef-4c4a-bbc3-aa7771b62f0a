package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.GoalTemplateDao
import com.timeflow.app.data.entity.GoalTemplate
import com.timeflow.app.data.entity.GoalSubTaskTemplate
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.ReminderSetting
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 默认模板初始化器
 * 负责在应用首次启动时初始化默认的目标模板
 */
@Singleton
class DefaultTemplateInitializer @Inject constructor(
    private val templateDao: GoalTemplateDao
) {
    /**
     * 初始化默认模板
     * 如果模板数据库为空，则添加默认模板
     */
    suspend fun initializeDefaultTemplates() {
        withContext(Dispatchers.IO) {
            // 检查数据库是否为空
            val templates = templateDao.getAllTemplatesSync()
            if (templates.isEmpty()) {
                insertDefaultTemplates()
            }
        }
    }

    /**
     * 插入默认模板
     */
    private suspend fun insertDefaultTemplates() {
        // 学习提升类目标模板
        addStudyTemplates()
        
        // 工作项目类目标模板
        addWorkTemplates()
        
        // 健康生活类目标模板
        addHealthTemplates()
        
        // 个人爱好类目标模板
        addPersonalTemplates()
    }

    /**
     * 添加学习提升类模板
     */
    private suspend fun addStudyTemplates() {
        // 1. 掌握一门新语言
        val languageTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "掌握一门新语言",
            description = "系统学习一门新语言，从基础到流利交流",
            category = "LEARN",
            defaultTitle = "掌握[语言名称]基础",
            defaultDescription = "通过系统学习，掌握该语言的基础听说读写能力",
            defaultTags = "学习,语言",
            defaultPriority = GoalPriority.MEDIUM.name,
            defaultDurationDays = 90,
            goalType = "BOOLEAN",
            colorHex = "#5E97F6",
            iconName = "language",
            usageCount = 12,
            lastUsed = LocalDateTime.now().minusDays(5),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(languageTemplate)
        
        // 语言学习子任务
        val languageSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = languageTemplate.id,
                title = "掌握基础词汇(500词)",
                description = "学习并掌握最常用的500个词汇",
                estimatedDurationDays = 15,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = languageTemplate.id,
                title = "学习基础语法",
                description = "掌握基本语法结构和时态",
                estimatedDurationDays = 20,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = languageTemplate.id,
                title = "每日对话练习",
                description = "学习日常对话并进行口语练习",
                estimatedDurationDays = 30,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = languageTemplate.id,
                title = "完成初级阅读训练",
                description = "阅读简单文章并提取关键信息",
                estimatedDurationDays = 25,
                orderIndex = 3
            )
        )
        templateDao.insertSubTaskTemplates(languageSubTasks)

        // 2. 考取专业证书
        val certificationTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "考取专业证书",
            description = "通过系统学习，成功考取特定领域的专业证书",
            category = "STUDY",
            defaultTitle = "考取[证书名称]证书",
            defaultDescription = "通过系统学习和模拟练习，成功通过专业证书考试",
            defaultTags = "职业提升,证书",
            defaultPriority = GoalPriority.HIGH.name,
            defaultDurationDays = 120,
            goalType = "BOOLEAN",
            colorHex = "#7986CB",
            iconName = "school",
            usageCount = 8,
            lastUsed = LocalDateTime.now().minusDays(10),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(certificationTemplate)
        
        // 证书考取子任务
        val certificationSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "确定考试范围和要求",
                description = "研究考试大纲和必要的学习材料",
                estimatedDurationDays = 5,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "制定学习计划",
                description = "创建详细的学习时间表和内容计划",
                estimatedDurationDays = 5,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "系统学习考试内容",
                description = "按计划学习各模块内容",
                estimatedDurationDays = 60,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "完成模拟考试练习",
                description = "做至少5套模拟试题检验学习效果",
                estimatedDurationDays = 30,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "考前强化复习",
                description = "重点复习难点和易错点",
                estimatedDurationDays = 15,
                orderIndex = 4
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = certificationTemplate.id,
                title = "参加正式考试",
                description = "按照考试要求准时参加并完成考试",
                estimatedDurationDays = 1,
                orderIndex = 5
            )
        )
        templateDao.insertSubTaskTemplates(certificationSubTasks)
    }

    /**
     * 添加工作项目类模板
     */
    private suspend fun addWorkTemplates() {
        // 1. 项目管理模板
        val projectTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "项目管理",
            description = "管理一个完整项目从启动到交付的全过程",
            category = "PROJECT",
            defaultTitle = "完成[项目名称]项目",
            defaultDescription = "按时高质量完成项目的所有交付物",
            defaultTags = "工作,项目",
            defaultPriority = GoalPriority.HIGH.name,
            defaultDurationDays = 60,
            goalType = "BOOLEAN",
            colorHex = "#4DB6AC",
            iconName = "business_center",
            usageCount = 15,
            lastUsed = LocalDateTime.now().minusDays(3),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(projectTemplate)
        
        // 项目管理子任务
        val projectSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "项目启动与需求分析",
                description = "确定项目范围、目标和主要需求",
                estimatedDurationDays = 7,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "项目规划与任务分解",
                description = "创建项目计划、时间表和任务分配",
                estimatedDurationDays = 5,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "设计与开发阶段",
                description = "根据规划完成核心设计和开发工作",
                estimatedDurationDays = 25,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "测试与质量保证",
                description = "进行全面测试并解决发现的问题",
                estimatedDurationDays = 15,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "项目交付与验收",
                description = "向利益相关方交付项目成果并获取验收",
                estimatedDurationDays = 5,
                orderIndex = 4
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = projectTemplate.id,
                title = "项目复盘与经验总结",
                description = "总结项目中的经验教训并形成文档",
                estimatedDurationDays = 3,
                orderIndex = 5
            )
        )
        templateDao.insertSubTaskTemplates(projectSubTasks)

        // 2. 工作技能提升
        val skillTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "工作技能提升",
            description = "系统提升特定工作领域的专业技能",
            category = "CAREER",
            defaultTitle = "提升[技能名称]专业水平",
            defaultDescription = "通过学习和实践提高特定工作技能",
            defaultTags = "职业发展,技能",
            defaultPriority = GoalPriority.MEDIUM.name,
            defaultDurationDays = 45,
            goalType = "NUMERIC",
            defaultTargetValue = 100.0,
            defaultUnit = "%",
            colorHex = "#FF7043",
            iconName = "trending_up",
            usageCount = 10,
            lastUsed = LocalDateTime.now().minusDays(7),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(skillTemplate)
        
        // 技能提升子任务
        val skillSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = skillTemplate.id,
                title = "评估当前水平",
                description = "客观评估当前技能水平，找出差距",
                estimatedDurationDays = 3,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = skillTemplate.id,
                title = "学习理论知识",
                description = "通过课程或书籍学习该技能的理论基础",
                estimatedDurationDays = 14,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = skillTemplate.id,
                title = "实践应用技能",
                description = "在实际工作或项目中应用所学技能",
                estimatedDurationDays = 21,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = skillTemplate.id,
                title = "获取反馈并调整",
                description = "寻求专业反馈并针对性改进",
                estimatedDurationDays = 7,
                orderIndex = 3
            )
        )
        templateDao.insertSubTaskTemplates(skillSubTasks)
    }

    /**
     * 添加健康生活类模板
     */
    private suspend fun addHealthTemplates() {
        // 1. 养成健身习惯
        val fitnessRecurringJson = JSONObject().apply {
            put("frequency", "WEEKLY")
            put("weeklyDays", JSONArray().apply {
                put(DayOfWeek.MONDAY.value)
                put(DayOfWeek.WEDNESDAY.value)
                put(DayOfWeek.FRIDAY.value)
            })
            put("interval", 1)
        }.toString()
        
        val fitnessTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "养成健身习惯",
            description = "通过规律锻炼培养健康生活习惯",
            category = "HEALTH",
            defaultTitle = "养成规律健身习惯",
            defaultDescription = "每周坚持3-4次锻炼，培养运动习惯",
            defaultTags = "健康,健身",
            defaultPriority = GoalPriority.MEDIUM.name,
            defaultDurationDays = 60,
            goalType = "BOOLEAN",
            isRecurring = true,
            recurringSettingsJson = fitnessRecurringJson,
            colorHex = "#42A5F5",
            iconName = "fitness_center",
            usageCount = 20,
            lastUsed = LocalDateTime.now().minusDays(2),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(fitnessTemplate)
        
        // 健身子任务
        val fitnessSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = fitnessTemplate.id,
                title = "制定个人健身计划",
                description = "根据个人情况设计适合的健身计划",
                estimatedDurationDays = 3,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = fitnessTemplate.id,
                title = "准备必要的装备",
                description = "购买运动服装和必要的健身装备",
                estimatedDurationDays = 2,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = fitnessTemplate.id,
                title = "第一阶段：养成习惯",
                description = "连续两周坚持计划，形成初步习惯",
                estimatedDurationDays = 14,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = fitnessTemplate.id,
                title = "第二阶段：提高强度",
                description = "适当增加训练强度和难度",
                estimatedDurationDays = 21,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = fitnessTemplate.id,
                title = "第三阶段：巩固习惯",
                description = "将健身完全融入日常生活",
                estimatedDurationDays = 21,
                orderIndex = 4
            )
        )
        templateDao.insertSubTaskTemplates(fitnessSubTasks)

        // 2. 健康饮食计划
        val dietTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "健康饮食计划",
            description = "改善饮食结构，培养健康饮食习惯",
            category = "DIET",
            defaultTitle = "养成健康饮食习惯",
            defaultDescription = "优化日常饮食结构，增加营养摄入平衡",
            defaultTags = "健康,饮食",
            defaultPriority = GoalPriority.MEDIUM.name,
            defaultDurationDays = 30,
            goalType = "BOOLEAN",
            colorHex = "#66BB6A",
            iconName = "restaurant",
            usageCount = 18,
            lastUsed = LocalDateTime.now().minusDays(4),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(dietTemplate)
        
        // 饮食计划子任务
        val dietSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = dietTemplate.id,
                title = "分析当前饮食习惯",
                description = "记录一周饮食并分析不足之处",
                estimatedDurationDays = 7,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = dietTemplate.id,
                title = "制定健康饮食计划",
                description = "设计营养均衡的一周饮食计划",
                estimatedDurationDays = 2,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = dietTemplate.id,
                title = "改善早餐质量",
                description = "每天吃营养丰富的早餐",
                estimatedDurationDays = 7,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = dietTemplate.id,
                title = "增加蔬果摄入",
                description = "每天摄入至少5份蔬果",
                estimatedDurationDays = 7,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = dietTemplate.id,
                title = "减少加工食品",
                description = "减少高糖、高油、高盐食品摄入",
                estimatedDurationDays = 7,
                orderIndex = 4
            )
        )
        templateDao.insertSubTaskTemplates(dietSubTasks)
    }

    /**
     * 添加个人爱好类模板
     */
    private suspend fun addPersonalTemplates() {
        // 1. 培养阅读习惯
        val readingRecurringJson = JSONObject().apply {
            put("frequency", "DAILY")
            put("interval", 1)
        }.toString()
        
        val readingTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "培养阅读习惯",
            description = "养成每日阅读的习惯，扩展知识面",
            category = "HOBBY",
            defaultTitle = "养成每日阅读习惯",
            defaultDescription = "每天至少阅读30分钟，提升知识储备",
            defaultTags = "阅读,自我提升",
            defaultPriority = GoalPriority.MEDIUM.name,
            defaultDurationDays = 30,
            goalType = "BOOLEAN",
            isRecurring = true,
            recurringSettingsJson = readingRecurringJson,
            colorHex = "#EC407A",
            iconName = "menu_book",
            usageCount = 16,
            lastUsed = LocalDateTime.now().minusDays(6),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(readingTemplate)
        
        // 阅读习惯子任务
        val readingSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "创建阅读清单",
                description = "列出想要阅读的10本书",
                estimatedDurationDays = 1,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "安排固定阅读时间",
                description = "每天选择固定的时间段专注阅读",
                estimatedDurationDays = 2,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "第一周：建立习惯",
                description = "每天阅读至少15分钟",
                estimatedDurationDays = 7,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "第二周：延长阅读时间",
                description = "增加到每天30分钟阅读",
                estimatedDurationDays = 7,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "第三周：做阅读笔记",
                description = "记录重要观点和个人感悟",
                estimatedDurationDays = 7,
                orderIndex = 4
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = readingTemplate.id,
                title = "第四周：分享读书心得",
                description = "与朋友分享或在社交媒体发布读书笔记",
                estimatedDurationDays = 7,
                orderIndex = 5
            )
        )
        templateDao.insertSubTaskTemplates(readingSubTasks)

        // 2. 旅行计划
        val travelTemplate = GoalTemplate(
            id = UUID.randomUUID().toString(),
            name = "旅行计划",
            description = "规划并完成一次有意义的旅行",
            category = "PERSONAL",
            defaultTitle = "规划[目的地]旅行",
            defaultDescription = "规划并完成一次难忘的旅行体验",
            defaultTags = "旅行,生活体验",
            defaultPriority = GoalPriority.LOW.name,
            defaultDurationDays = 60,
            goalType = "BOOLEAN",
            colorHex = "#AB47BC",
            iconName = "flight",
            usageCount = 14,
            lastUsed = LocalDateTime.now().minusDays(15),
            createdAt = LocalDateTime.now()
        )
        templateDao.insertTemplate(travelTemplate)
        
        // 旅行计划子任务
        val travelSubTasks = listOf(
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "确定目的地和时间",
                description = "研究并选择旅行目的地和合适的出行时间",
                estimatedDurationDays = 5,
                orderIndex = 0
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "预算规划",
                description = "估算旅行总预算并分配各项开支",
                estimatedDurationDays = 2,
                orderIndex = 1
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "预订交通和住宿",
                description = "预订往返交通和目的地住宿",
                estimatedDurationDays = 3,
                orderIndex = 2
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "行程安排",
                description = "规划详细的每日行程和景点参观",
                estimatedDurationDays = 5,
                orderIndex = 3
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "准备旅行装备",
                description = "准备衣物、必要证件和旅行用品",
                estimatedDurationDays = 3,
                orderIndex = 4
            ),
            GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                templateId = travelTemplate.id,
                title = "出行并记录旅程",
                description = "按计划出行并记录旅行体验",
                estimatedDurationDays = 14,
                orderIndex = 5
            )
        )
        templateDao.insertSubTaskTemplates(travelSubTasks)
    }
} 