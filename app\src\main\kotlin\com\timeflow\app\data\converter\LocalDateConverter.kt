package com.timeflow.app.data.converter

import androidx.room.TypeConverter
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * LocalDate类型转换器
 * 用于在Room数据库中存储和读取LocalDate类型
 */
class LocalDateConverter {
    private val formatter = DateTimeFormatter.ISO_LOCAL_DATE
    
    /**
     * 将LocalDate转换为String
     */
    @TypeConverter
    fun fromLocalDate(date: LocalDate?): String? {
        return date?.format(formatter)
    }
    
    /**
     * 将String转换为LocalDate
     */
    @TypeConverter
    fun toLocalDate(dateString: String?): LocalDate? {
        return dateString?.let { LocalDate.parse(it, formatter) }
    }
} 