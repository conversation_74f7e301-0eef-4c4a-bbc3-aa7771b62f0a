package com.timeflow.app.data.ai.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * AI任务洞察数据模型 - 跟踪任务进度并提供建议
 */
data class AiTaskInsight(
    val id: String = UUID.randomUUID().toString(),
    val taskId: String,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val progressTrackingData: ProgressTrackingData? = null,
    val recommendations: List<TaskRecommendation> = emptyList(),
    val timeSlotRecommendations: List<TimeSlotRecommendation> = emptyList(),
    val productivityInsights: List<ProductivityInsight> = emptyList()
)

/**
 * 进度跟踪数据 - 记录任务的完成情况
 */
data class ProgressTrackingData(
    val expectedProgress: Float, // 预期进度 0.0-1.0
    val actualProgress: Float, // 实际进度 0.0-1.0
    val lastUpdated: LocalDateTime = LocalDateTime.now(),
    val progressHistory: List<ProgressDataPoint> = emptyList(),
    val isAheadOfSchedule: Boolean = false,
    val progressDelta: Float = 0f, // 进度差异 (实际 - 预期)
    val estimatedCompletionTime: LocalDateTime? = null
)

/**
 * 进度数据点 - 记录特定时间点的进度情况
 */
data class ProgressDataPoint(
    val timestamp: LocalDateTime,
    val progress: Float, // 0.0-1.0
    val note: String? = null
)

/**
 * 任务建议 - AI提供的优化建议
 */
data class TaskRecommendation(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String,
    val type: RecommendationType,
    val importance: RecommendationImportance = RecommendationImportance.MEDIUM,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val expiresAt: LocalDateTime? = null,
    val isApplied: Boolean = false,
    val appliedAt: LocalDateTime? = null
)

/**
 * 时间段建议 - 推荐最佳任务时间
 */
data class TimeSlotRecommendation(
    val id: String = UUID.randomUUID().toString(),
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val confidence: Float, // 置信度 0.0-1.0
    val reason: String,
    val expectedProductivityGain: Float, // 预期效率提升 0.0-1.0
    val conflictingEvents: List<String> = emptyList() // 潜在冲突的事件ID
)

/**
 * 生产力洞察 - 分析影响效率的因素
 */
data class ProductivityInsight(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String,
    val impactLevel: Float, // 影响程度 -1.0 (负面) 到 1.0 (正面)
    val category: InsightCategory,
    val supportingData: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 建议类型
 */
enum class RecommendationType {
    TASK_BREAKDOWN, // 任务拆分建议
    TIME_MANAGEMENT, // 时间管理建议
    PRIORITY_ADJUSTMENT, // 优先级调整建议
    FOCUS_IMPROVEMENT, // 专注力提升建议
    SCHEDULING, // 日程安排建议
    DELEGATION, // 任务委派建议
    RESOURCE_ALLOCATION // 资源分配建议
}

/**
 * 建议重要性
 */
enum class RecommendationImportance {
    LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * 洞察类别
 */
enum class InsightCategory {
    TIME_OF_DAY, // 一天中的时间段
    ENVIRONMENT, // 工作环境
    TASK_BATCH, // 任务批处理
    INTERRUPTION, // 中断和干扰
    ENERGY_LEVEL, // 能量水平
    WORK_PATTERNS, // 工作模式
    COLLABORATION // 协作影响
} 