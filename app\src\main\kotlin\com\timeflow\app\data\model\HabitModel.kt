package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

/**
 * 自定义习惯分类数据类
 */
data class CustomHabitCategory(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val icon: String = "🏷️", // 改为String类型存储emoji
    val color: Color = Color(0xFFB6D9F2),
    val sortOrder: Int = 0,
    val isDefault: Boolean = false, // 是否为默认分类（不可删除）
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    companion object {
        // 默认分类
        val defaultCategories = listOf(
            CustomHabitCategory(
                id = "default_health",
                title = "健康",
                icon = "❤️",
                color = Color(0xFFf5c4c4),
                sortOrder = 1,
                isDefault = true
            ),
            CustomHabitCategory(
                id = "default_learning",
                title = "学习",
                icon = "🎓",
                color = Color(0xFFb6d9f2),
                sortOrder = 2,
                isDefault = true
            ),
            CustomHabitCategory(
                id = "default_work",
                title = "工作",
                icon = "💼",
                color = Color(0xFFb6e2de),
                sortOrder = 3,
                isDefault = true
            ),
            CustomHabitCategory(
                id = "default_life",
                title = "生活",
                icon = "🏠",
                color = Color(0xFFfbe3c2),
                sortOrder = 4,
                isDefault = true
            ),
            CustomHabitCategory(
                id = "default_fitness",
                title = "健身",
                icon = "💪",
                color = Color(0xFFd7c9e8),
                sortOrder = 5,
                isDefault = true
            ),
            CustomHabitCategory(
                id = "default_mindfulness",
                title = "冥想",
                icon = "🧘‍♀️",
                color = Color(0xFFdcead1),
                sortOrder = 6,
                isDefault = true
            )
        )
    }
}

/**
 * 习惯类别枚举
 */
enum class HabitCategory(val title: String, val icon: String, val color: Color) {
    HEALTH("健康", "❤️", Color(0xFFf5c4c4)),
    LEARNING("学习", "🎓", Color(0xFFb6d9f2)),
    WORK("工作", "💼", Color(0xFFb6e2de)),
    LIFE("生活", "🏠", Color(0xFFfbe3c2)),
    FITNESS("健身", "💪", Color(0xFFd7c9e8)),
    MINDFULNESS("冥想", "🧘‍♀️", Color(0xFFdcead1))
}

/**
 * 习惯频率类型
 */
enum class HabitFrequencyType(val displayName: String) {
    DAILY("每天"),
    WEEKLY("每周"),
    CUSTOM("自定义")
}

/**
 * 习惯难度
 */
enum class HabitDifficulty(val displayName: String, val level: Int) {
    EASY("简单", 1),
    MEDIUM("中等", 2),
    HARD("困难", 3),
    EXTREME("极难", 4)
}

/**
 * 习惯业务模型
 */
data class HabitModel(
    val id: String = UUID.randomUUID().toString(),
    
    // 基本信息
    val name: String,
    val description: String = "",
    val icon: String = "❤️", // 改为String类型存储emoji
    val color: Color = Color(0xFFf5c4c4),
    val category: HabitCategory = HabitCategory.HEALTH, // 保留原有分类以兼容性
    val customCategoryId: String? = null, // 新增：自定义分类ID
    
    // 频率设置
    val frequencyType: HabitFrequencyType = HabitFrequencyType.DAILY,
    val frequencyDays: List<DayOfWeek> = DayOfWeek.values().toList(),
    val targetCount: Int = 1, // 目标次数（每天/每周）
    
    // 时间设置
    val reminderEnabled: Boolean = false,
    val reminderTime: LocalTime? = null,
    val fixedTime: LocalTime? = null, // 固定时间（如早上7点）
    
    // 难度和目标关联
    val difficulty: HabitDifficulty = HabitDifficulty.MEDIUM,
    val relatedGoalId: String? = null, // 关联的目标ID
    val relatedGoalTitle: String? = null, // 关联目标的标题（用于显示）
    
    // 统计信息
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val totalCompletions: Int = 0,
    val completedDates: List<LocalDate> = emptyList(),
    
    // 时间戳
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val isActive: Boolean = true,
    
    // 自定义设置
    val customEmoji: String = "",
    val notes: String = "",
    val sortOrder: Int = 0
) {
    /**
     * 检查指定日期是否需要完成此习惯
     */
    fun isScheduledForDate(date: LocalDate): Boolean {
        return when (frequencyType) {
            HabitFrequencyType.DAILY -> true
            HabitFrequencyType.WEEKLY -> frequencyDays.contains(date.dayOfWeek)
            HabitFrequencyType.CUSTOM -> frequencyDays.contains(date.dayOfWeek)
        }
    }
    
    /**
     * 检查指定日期是否已完成
     */
    fun isCompletedOnDate(date: LocalDate): Boolean {
        return completedDates.contains(date)
    }
    
    /**
     * 计算完成率（最近30天）
     */
    fun getCompletionRate(days: Int = 30): Float {
        val endDate = LocalDate.now()
        val startDate = endDate.minusDays(days.toLong())
        
        val scheduledDays = (0 until days).count { dayOffset ->
            val date = startDate.plusDays(dayOffset.toLong())
            isScheduledForDate(date)
        }
        
        if (scheduledDays == 0) return 0f
        
        val completedDays = completedDates.count { date ->
            date.isAfter(startDate.minusDays(1)) && date.isBefore(endDate.plusDays(1))
        }
        
        return completedDays.toFloat() / scheduledDays.toFloat()
    }
}

/**
 * 习惯记录业务模型
 */
data class HabitRecordModel(
    val id: String = UUID.randomUUID().toString(),
    val habitId: String,
    val date: LocalDate,
    val completed: Boolean = false,
    val completedAt: LocalDateTime? = null,
    
    // 完成详情
    val completionCount: Int = 1, // 当天完成次数
    val notes: String = "", // 当天备注
    val skipReason: String = "", // 跳过原因（如"生病"）
    val mood: String = "", // 完成时的心情
    
    // 时间戳
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 习惯提醒业务模型
 */
data class HabitReminderModel(
    val id: String = UUID.randomUUID().toString(),
    val habitId: String,
    val time: LocalTime,
    val days: List<DayOfWeek> = DayOfWeek.values().toList(),
    val enabled: Boolean = true,
    val message: String = "", // 自定义提醒消息
    
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 习惯统计数据
 */
data class HabitStatsModel(
    val totalHabits: Int = 0,
    val activeHabits: Int = 0,
    val completedToday: Int = 0,
    val averageCompletionRate: Float = 0f,
    val longestStreak: Int = 0,
    val topHabits: List<HabitCompletionStat> = emptyList()
)

/**
 * 习惯完成统计
 */
data class HabitCompletionStat(
    val habitName: String,
    val completions: Int
)

/**
 * 习惯创建表单状态
 */
data class HabitFormState(
    val name: String = "",
    val description: String = "",
    val category: HabitCategory = HabitCategory.HEALTH,
    val icon: String = "❤️", // 改为String类型存储emoji，默认为心形
    val color: Color = Color(0xFFf5c4c4),
    val frequencyType: HabitFrequencyType = HabitFrequencyType.DAILY,
    val selectedDays: Set<DayOfWeek> = DayOfWeek.values().toSet(),
    val targetCount: Int = 1,
    val difficulty: HabitDifficulty = HabitDifficulty.MEDIUM,
    val relatedGoalId: String? = null,
    val relatedGoalTitle: String? = null,
    val enableReminder: Boolean = false,
    val reminderTime: LocalTime? = LocalTime.of(8, 0),
    val fixedTime: LocalTime? = null,
    val customEmoji: String = "",
    val notes: String = ""
) 