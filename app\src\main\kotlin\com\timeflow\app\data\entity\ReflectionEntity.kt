package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.Converters
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.ReflectionType
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.time.Instant

/**
 * 感想记录数据库实体
 */
@Entity(tableName = "reflections")
@TypeConverters(Converters::class)
@Serializable
data class ReflectionEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "title")
    val title: String,
    
    @ColumnInfo(name = "content")
    val content: String,
    
    @ColumnInfo(name = "rich_content_json")
    val richContentJson: String = "[]", // JSON字符串存储富文本内容
    
    @ColumnInfo(name = "date")
    @Contextual
    val date: Instant,
    
    @ColumnInfo(name = "rating")
    val rating: Int = 3,
    
    @ColumnInfo(name = "tags_json")
    val tagsJson: String = "[]", // JSON字符串存储标签列表
    
    @ColumnInfo(name = "type")
    val type: String = "LIFE", // 感想类型
    
    @ColumnInfo(name = "mood")
    val mood: String = "CALM", // 心情类型
    
    @ColumnInfo(name = "plans_json")
    val plansJson: String = "[]", // JSON字符串存储计划列表
    
    @ColumnInfo(name = "background_image")
    val backgroundImage: String? = null,
    
    @ColumnInfo(name = "metrics_json")
    val metricsJson: String = "{}", // JSON字符串存储指标数据
    
    @ColumnInfo(name = "created_at")
    @Contextual
    val createdAt: Instant = Instant.now(),

    @ColumnInfo(name = "updated_at")
    @Contextual
    val updatedAt: Instant = Instant.now(),
    
    // 任务关联字段
    @ColumnInfo(name = "task_id")
    val taskId: String? = null, // 关联的任务ID
    
    @ColumnInfo(name = "task_title")
    val taskTitle: String? = null, // 关联的任务标题
    
    @ColumnInfo(name = "is_from_task_completion")
    val isFromTaskCompletion: Boolean = false // 是否来自任务完成反馈
) 