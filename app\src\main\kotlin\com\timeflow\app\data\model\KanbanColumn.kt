package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import java.time.LocalDateTime

/**
 * 看板列数据模型
 * 
 * @property id 列的唯一标识符
 * @property boardId 所属看板ID
 * @property title 列标题
 * @property description 列描述
 * @property tasks 列中的任务列表
 * @property color 列的主题颜色
 * @property order 列的显示顺序
 * @property isCollapsed 列是否折叠
 * @property taskLimit 列中任务数量限制，0表示无限制
 * @property createdAt 创建时间
 * @property updatedAt 更新时间
 */
data class KanbanColumn(
    val id: String,
    val boardId: String,
    val title: String,
    val description: String? = null,
    val tasks: List<Task> = emptyList(),
    val color: Color? = null,
    val order: Int = 0,
    val isCollapsed: Boolean = false,
    val taskLimit: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) 