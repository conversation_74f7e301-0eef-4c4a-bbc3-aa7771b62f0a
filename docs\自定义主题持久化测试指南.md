# 自定义主题持久化测试指南

## 🎯 测试目标

验证修复后的自定义主题能够正确持久化，在应用重启后保持用户设置的颜色。

## 🔧 修复内容回顾

### 主要修复点
1. **修复数据竞争条件**：避免ThemeSettingsViewModel和ThemeManager同时写入DataStore
2. **修复初始化时序问题**：添加延迟，避免应用启动时覆盖用户设置
3. **修复颜色值转换问题**：确保32位无符号整数转换
4. **修复异步操作竞争**：添加防抖动机制和状态检查

## 📋 测试场景

### 场景1：基本自定义主题持久化测试
**目标**：验证自定义主题颜色在应用重启后保持不变

**步骤**：
1. 打开应用，进入设置 → 主题设置
2. 点击主色调颜色选择器
3. 选择一个明显的颜色（如红色 #FF0000）
4. 确认颜色已应用到界面
5. 完全退出应用（从最近任务中移除）
6. 重新启动应用，进入设置 → 主题设置
7. **预期结果**：主色调应该仍然是红色，界面元素应该保持红色主题

**验证方法**：
- 检查主色调颜色选择器显示的颜色
- 检查界面元素（如按钮、标题栏）的颜色
- 查看日志：应显示"从DataStore加载主题设置成功"

### 场景2：快速连续修改颜色测试
**目标**：验证防抖动机制正常工作，保存最后设置的颜色

**步骤**：
1. 打开主题设置页面
2. 快速连续修改主色调多次（红→绿→蓝→黄）
3. 最后选择紫色 (#800080)
4. 等待1秒后退出应用
5. 重新启动应用
6. **预期结果**：主色调应该是紫色，而不是中间的任何颜色

**验证方法**：
- 检查主色调是否为最后选择的紫色
- 查看日志：应该有多次"更新主题色"记录，但只有最后一次成功保存

### 场景3：预设主题与自定义主题交互测试
**目标**：验证预设主题和自定义主题状态正确管理

**步骤**：
1. 进入预设主题页面
2. 选择并应用一个预设主题（如"雾霾蓝"）
3. 返回主题设置页面
4. 修改主色调为自定义颜色（如橙色 #FFA500）
5. 退出应用并重新启动
6. 进入预设主题页面
7. **预期结果**：
   - 自定义的橙色主题应该保持
   - 预设主题页面中不应有任何主题显示为已选中

**验证方法**：
- 检查主色调是否为橙色
- 检查预设主题页面中是否没有主题被标记为已选中
- 查看日志：应显示"清除当前预设主题ID"

### 场景4：暗色模式切换测试
**目标**：验证自定义主题在暗色模式切换后仍然保持

**步骤**：
1. 设置自定义主色调（如粉色 #FFC0CB）
2. 开启"跟随系统"选项
3. 切换系统暗色模式（或在设置中手动切换暗色模式）
4. 退出应用并重新启动
5. **预期结果**：
   - 自定义的粉色主题应该在两种模式下都保持
   - 暗色/亮色模式切换正常

**验证方法**：
- 检查主色调在两种模式下是否保持一致
- 检查界面元素在暗色模式下的适应性
- 查看日志：应显示暗色模式状态变化

### 场景5：多页面背景色测试
**目标**：验证各页面背景色设置能够正确保存

**步骤**：
1. 在主题设置页面中设置不同页面的背景色：
   - 首页：浅蓝色 (#ADD8E6)
   - 日历页：浅绿色 (#90EE90)
   - 统计页：浅黄色 (#FFFFE0)
2. 退出应用并重新启动
3. 分别进入各个页面
4. **预期结果**：各页面应该保持设置的背景色

**验证方法**：
- 检查各页面的背景色是否符合设置
- 查看日志：应显示各页面背景色的加载

## 🔍 日志验证

### 关键日志信息

**主题保存成功日志**：
```
✅ 主题偏好设置已保存到DataStore，主色调: ff0000
```

**主题加载成功日志**：
```
✅ 从DataStore加载主题设置成功: primaryColor=ff0000
```

**预设主题清除日志**：
```
✅ 已清除当前预设主题ID
```

### 错误日志排查

如果出现问题，检查以下日志：
- `ThemeSettingsViewModel`: 颜色更新和保存逻辑
- `ThemeManager`: 主题初始化和加载逻辑
- `PresetThemeManager`: 预设主题管理逻辑

## 🧪 自动化测试

### 单元测试验证

可以创建以下单元测试：

```kotlin
@Test
fun testThemeColorPersistence() {
    // 设置自定义颜色
    val testColor = Color(0xFFFF0000)
    runBlocking {
        ThemeManager.updateThemePreference { it.copy(primaryColor = testColor) }
    }
    
    // 模拟应用重启
    ThemeManager.cleanup()
    ThemeManager.initialize(context, themeDataStore)
    
    // 验证颜色保持不变
    val loadedColor = ThemeManager.userThemePreference.value.primaryColor
    assertEquals(testColor, loadedColor)
}
```

## 📱 用户界面验证

### 主题设置页面检查
1. 打开主题设置页面
2. 确认颜色选择器显示正确的当前颜色
3. 确认界面元素（按钮、标题栏等）使用正确的主题色
4. 确认各页面背景色设置正确显示

### 预设主题页面检查
1. 打开预设主题页面
2. 确认当前使用的预设主题（如有）正确显示为已选中
3. 确认自定义主题时预设主题状态正确清除

## 🚨 常见问题排查

### 问题1：主题颜色仍然不保存
**可能原因**：
- DataStore写入失败
- 颜色值转换错误
- 应用启动时覆盖了设置

**解决方法**：
- 检查DataStore文件权限
- 验证颜色值转换逻辑
- 检查应用初始化顺序

### 问题2：预设主题状态不正确
**可能原因**：
- 预设主题ID保存失败
- 状态更新逻辑错误
- 清除预设ID的条件不正确

**解决方法**：
- 检查PresetThemeManager的保存逻辑
- 验证clearPresetId参数传递
- 检查预设主题ID的加载过程

### 问题3：页面背景色不一致
**可能原因**：
- 页面背景色保存不完整
- EventBus事件未正确广播
- 页面未正确响应主题变化

**解决方法**：
- 检查所有页面背景色的保存
- 验证EventBus事件发送和接收
- 检查页面的主题监听逻辑

## 📊 测试报告模板

### 测试结果记录

| 测试场景 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|----------|----------|------|------|
| 基本持久化 | 重启后保持颜色 | | | |
| 快速连续修改 | 保存最后颜色 | | | |
| 预设主题交互 | 自定义后清除预设 | | | |
| 暗色模式切换 | 两种模式下保持 | | | |
| 多页面背景色 | 各页面保持设置 | | | |

### 性能指标

- **设置响应时间**：< 100ms
- **保存完成时间**：< 500ms
- **应用启动加载时间**：无明显增加
- **内存使用**：无异常增加

## 🎉 验收标准

修复被认为成功的标准：

1. ✅ 自定义主题颜色在应用重启后保持不变
2. ✅ 快速连续修改颜色时保存最后设置的值
3. ✅ 预设主题和自定义主题状态正确管理
4. ✅ 系统暗色模式切换不影响自定义颜色
5. ✅ 日志输出清晰反映保存和加载过程
6. ✅ 无数据竞争和状态不一致问题

## 📝 测试注意事项

1. **完全退出应用**：测试时确保完全退出应用（从最近任务中移除），而不仅是返回桌面
2. **系统设置**：测试暗色模式时，确保系统支持暗色模式切换
3. **多次验证**：每个测试场景重复测试2-3次，确保结果一致
4. **日志记录**：保存测试过程中的关键日志，便于分析问题
5. **不同设备**：如可能，在不同Android版本和设备上验证

通过以上测试，可以全面验证自定义主题持久化问题已经得到彻底解决。
