package com.timeflow.app.ui.screen.milestone

import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument

/**
 * 里程碑相关页面路由包装类
 */
object MilestoneRoutes {
    const val MILESTONE_ROUTE = "milestone"
    const val EDIT_MILESTONE_ROUTE = "edit_milestone?milestoneId={milestoneId}"
    
    /**
     * 里程碑页面包装
     */
    @Composable
    fun MilestoneRoute(navController: NavController) {
        MilestoneScreen(navController = navController)
    }
    
    /**
     * 编辑里程碑页面包装
     */
    @Composable
    fun EditMilestoneRoute(navController: NavController, milestoneId: String?) {
        EditMilestoneScreen(navController = navController, milestoneId = milestoneId)
    }
    
    /**
     * 添加里程碑路由
     */
    fun navigateToAddMilestone(navController: NavController) {
        navController.navigate(EDIT_MILESTONE_ROUTE.replace("{milestoneId}", ""))
    }
    
    /**
     * 编辑里程碑路由
     */
    fun navigateToEditMilestone(navController: NavController, milestoneId: String) {
        navController.navigate(EDIT_MILESTONE_ROUTE.replace("{milestoneId}", milestoneId))
    }
    
    /**
     * 注册里程碑相关路由
     */
    fun NavGraphBuilder.milestoneGraph(navController: NavController) {
        composable(MILESTONE_ROUTE) {
            MilestoneRoute(navController)
        }
        
        composable(
            route = "edit_milestone?milestoneId={milestoneId}",
            arguments = listOf(
                navArgument("milestoneId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val milestoneId = backStackEntry.arguments?.getString("milestoneId")
            EditMilestoneRoute(navController, milestoneId)
        }
    }
} 