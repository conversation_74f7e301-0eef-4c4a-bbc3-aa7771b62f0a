package com.timeflow.app.ui.screen.goal

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.ui.navigation.AppDestinations
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.delay
import java.util.Locale
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.height
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.input.pointer.pointerInput

/**
 * 目标复盘分析页面
 * 基于目标执行情况进行动态分析，提供进度趋势、效率分布和AI优化建议
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalReviewScreen(
    navController: NavController,
    goalId: String,
    viewModel: GoalViewModel = hiltViewModel()
) {
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 加载目标
    LaunchedEffect(goalId) {
        viewModel.loadGoalDetail(goalId)
    }
    
    // 获取当前目标
    val goal = viewModel.currentGoal.value
    val uiState = viewModel.uiState.value
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    // 处理状态栏，确保在页面关闭时恢复
    DisposableEffect(Unit) {
        activity?.let { act ->
            val window = act.window
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
        }
        
        onDispose {
            activity?.let { act ->
                // 恢复原始状态栏颜色
                val window = act.window
                val originalStatusBarColor = window.statusBarColor
                window.statusBarColor = originalStatusBarColor
                Log.d("GoalReviewScreen", "GoalReviewScreen disposed")
            }
        }
    }

    // 界面颜色
    val backgroundColor = Color(0xFFF8F9FA)
    val surfaceColor = Color.White
    val textPrimaryColor = MaterialTheme.colorScheme.primary
    val textSecondaryColor = Color(0xFF666666)
    
    // 成功指标颜色
    val successColor = Color(0xFF749e66)
    // 待改进项颜色
    val improvementColor = Color(0xFFED8936)
    // 中性信息颜色
    val neutralColor = Color(0xFF7097a8)
    
    // 动画状态
    var showContent by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        delay(100)
        showContent = true
    }
    
    // 模拟数据 - 在实际应用中，这些数据会从ViewModel中获取
    val isCompleted = remember { goal?.completedAt != null }
    val completionRate = remember { if (isCompleted) 0.92f else 0.86f }
    val bestDay = remember { "每周三" }
    val weakDay = remember { "周末" }
    val currentDay = remember { if (isCompleted) "完成" else "第15天" }
    val totalDays = remember { 30 }
    val expectedEndDate = remember { "2023-11-15" }
    val aiTips = remember { 
        if (isCompleted) "你的运动坚持度非常出色！饮食记录是主要失分项，但体脂下降超预期说明实际执行比记录更好。建议简化记录方式提升持续性。"
        else "你的晨间运动坚持得很好，但周末饮食记录经常遗漏。建议设置简化的周末记录方式。"
    }
    val suggestions = remember {
        if (isCompleted) listOf(
            "保持当前运动习惯",
            "改用每周3次详细饮食记录+日常拍照",
            "开启\"体脂维持计划\"延续成果"
        ) else listOf(
            "将周末饮食改为拍照打卡(无需详细记录)",
            "周六增加10分钟运动作为缓冲"
        )
    }
    
    // 完成报告数据
    val achievements = remember {
        if (isCompleted) mapOf(
            "体重变化" to "-3.5kg (目标-4kg)",
            "体脂率" to "-2.1% (超过目标1.8%)",
            "运动时长" to "共1260分钟"
        ) else null
    }
    
    val executionAnalysis = remember {
        if (isCompleted) mapOf(
            "晨间运动" to 0.98f,
            "饮食记录" to 0.75f,
            "周末执行" to 0.82f
        ) else null
    }
    
    // 进度趋势数据（假设1-30天的数据）
    val progressData = remember {
        List(30) { day ->
            when {
                day == 19 -> 0.5f  // 部分完成
                day == 26 -> 0.3f  // 补打卡
                day < 28 -> 1.0f   // 完成
                else -> 0.0f       // 未来日期
            }
        }
    }
    
    // 最佳时段数据
    val timeSlotData = remember {
        mapOf(
            "早晨(7-9am)" to 0.92f,
            "晚间(6-8pm)" to 0.58f
        )
    }
    
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(top = SystemBarManager.getFixedStatusBarHeight()),
        containerColor = backgroundColor,
        topBar = {
            TopAppBar(
                title = { 
                Column {
                    Text(
                        text = "AI复盘分析",
                            fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = textPrimaryColor
                    )
                        
                    goal?.let {
                        Text(
                                text = "\"${it.title}\"${if (isCompleted) "完成报告" else "中期复盘"}",
                            fontSize = 14.sp,
                            color = textSecondaryColor
                        )
                    }
                }
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { /* 打印 */ }) {
                        Icon(
                            imageVector = Icons.Default.Print,
                            contentDescription = "打印"
                        )
                    }
                    IconButton(onClick = { /* 下载 */ }) {
                        Icon(
                            imageVector = Icons.Default.Download,
                            contentDescription = "导出"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor,
                    titleContentColor = textPrimaryColor
                )
            )
        }
    ) { paddingValues ->
        // 处理加载状态
        if (uiState is GoalUiState.Loading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = DustyLavender)
            }
            return@Scaffold
        }
        
        // 处理目标为空的情况
        if (goal == null) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text("目标不存在或已被删除")
            }
            return@Scaffold
        }
        
        // 主内容 - 使用动画效果
        AnimatedVisibility(
            visible = showContent,
            enter = fadeIn(tween(500)) + slideInVertically(
                initialOffsetY = { it / 5 },
                animationSpec = tween(700)
            )
        ) {
            // 主内容区域
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(bottom = 24.dp)
            ) {
                // 标题区域
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = surfaceColor),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                                .padding(20.dp)
                    ) {
                        Row(
                                verticalAlignment = Alignment.CenterVertically
                        ) {
                                // 图标
                                val icon = if (isCompleted) Icons.Default.Celebration else Icons.Default.Refresh
                                val iconColor = if (isCompleted) Color(0xFFF59E0B) else Color(0xFF3B82F6)
                                
                            Box(
                                modifier = Modifier
                                        .size(48.dp)
                                    .clip(CircleShape)
                                        .background(iconColor.copy(alpha = 0.1f)),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                        imageVector = icon,
                                    contentDescription = null,
                                        tint = iconColor,
                                        modifier = Modifier.size(28.dp)
                                )
                            }
                            
                            Spacer(modifier = Modifier.width(16.dp))
                            
                                Column {
                            Text(
                                        text = if (isCompleted) 
                                            "🎉 ${goal?.title} - 完成报告" 
                                        else 
                                            "🔄 ${goal?.title} - 中期复盘",
                                fontSize = 18.sp,
                                        fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
                                    
                                    Spacer(modifier = Modifier.height(4.dp))
                                    
                Text(
                                        text = if (isCompleted) 
                                            "⭐ 总体完成度: ${(completionRate * 100).toInt()}%" 
                                        else 
                                            "📅 $currentDay | 预计完成: $expectedEndDate",
                                        fontSize = 14.sp,
                                        color = textSecondaryColor
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 当前进度/成果概览
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = surfaceColor),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                                .padding(20.dp)
                        ) {
                            Text(
                                text = if (isCompleted) "📈 成果概览" else "📊 当前进度",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            if (isCompleted && achievements != null) {
                                // 显示成果概览
                                achievements.forEach { (key, value) ->
                                    MetricRow(
                                        title = key,
                                        value = value,
                                        icon = when (key) {
                                            "体重变化" -> Icons.Default.Scale
                                            "体脂率" -> Icons.Default.MonitorWeight
                                            else -> Icons.Default.Timer
                                        }
                                    )
                                }
                            } else {
                                // 显示当前进度
                                MetricRow(
                                    title = "任务完成率",
                                    value = "${(completionRate * 100).toInt()}% (高于平均水平)",
                                    icon = Icons.Default.CheckCircle,
                                    valueColor = successColor
                                )
                                
                                MetricRow(
                                    title = "最佳执行日",
                                    value = "$bestDay (100%完成)",
                                    icon = Icons.Default.Star,
                                    valueColor = successColor
                                )
                                
                                MetricRow(
                                    title = "需改进日",
                                    value = "$weakDay (平均完成率65%)",
                                    icon = Icons.Default.TrendingDown,
                                    valueColor = improvementColor
                                )
                            }
                        }
                    }
                }
                
                // 执行分析（仅完成报告显示）
                if (isCompleted && executionAnalysis != null) {
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = surfaceColor),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                                    .padding(20.dp)
                            ) {
                            Text(
                                    text = "📅 执行分析",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
                        
                                Spacer(modifier = Modifier.height(16.dp))
                        
                                // 执行分析图表
                                executionAnalysis.forEach { (category, rate) ->
                        Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                                        val color = when {
                                            rate > 0.9f -> successColor
                                            rate > 0.7f -> neutralColor
                                            else -> improvementColor
                                        }
                                        
                                Text(
                                            text = category,
                                            modifier = Modifier.width(100.dp),
                                    fontSize = 14.sp,
                                    color = textSecondaryColor
                                )
                                        
                                        Spacer(modifier = Modifier.width(8.dp))
                                        
                                        Box(
                                            modifier = Modifier
                                                .weight(1f)
                                                .height(12.dp)
                                                .clip(RoundedCornerShape(6.dp))
                                                .background(Color(0xFFEEEEEE))
                                        ) {
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxHeight()
                                                    .fillMaxWidth(rate)
                                                    .clip(RoundedCornerShape(6.dp))
                                                    .background(color)
                                            )
                                        }
                                        
                                Spacer(modifier = Modifier.width(8.dp))
                                        
                                        Text(
                                            text = "${(rate * 100).toInt()}%",
                                            fontSize = 14.sp,
                                            color = color,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                
                // AI分析
                item {
    Card(
                        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = surfaceColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                                .padding(20.dp)
        ) {
            Row(
                                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                                        .background(Color(0xFFEAE8F2)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                                        imageVector = Icons.Default.Psychology,
                        contentDescription = null,
                                        tint = DustyLavender,
                                        modifier = Modifier.size(22.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                    Text(
                                    text = if (isCompleted) "🧠 AI深度分析" else "🧠 AI分析",
                        fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                                text = "\"$aiTips\"",
                                fontSize = 14.sp,
                                color = textSecondaryColor,
                                lineHeight = 22.sp
                            )
                        }
                    }
                }
                
                // 优化建议/后续建议
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = surfaceColor),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(20.dp)
                        ) {
                Text(
                                text = if (isCompleted) "🔮 后续建议" else "💡 优化建议",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            suggestions.forEachIndexed { index, suggestion ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                                    Text(
                                        text = "${index + 1}. ",
                                        fontSize = 14.sp,
                                        color = textPrimaryColor,
                                        fontWeight = FontWeight.Medium
                                    )
                    
                    Text(
                                        text = suggestion,
                        fontSize = 14.sp,
                                        color = textSecondaryColor,
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }
}

                // 进度趋势
                item {
    Card(
                        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = surfaceColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                                .padding(20.dp)
            ) {
                Text(
                                text = "📊 进度趋势",
                    fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
                            // 进度趋势图表
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                                    .height(120.dp)
                    .clip(RoundedCornerShape(8.dp))
                                    .background(Color(0xFFF8F9FA))
                                    .padding(horizontal = 8.dp, vertical = 12.dp)
                            ) {
                                Canvas(
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    val barWidth = size.width / progressData.size
                                    val maxHeight = size.height * 0.8f
                                    val bottomY = size.height
                                    
                                    // 绘制网格线
                                    val dashPathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                                    drawLine(
                                        color = Color(0xFFDDDDDD),
                                        start = Offset(0f, bottomY * 0.5f),
                                        end = Offset(size.width, bottomY * 0.5f),
                                        strokeWidth = 1f,
                                        pathEffect = dashPathEffect
                                    )
                                    
                                    // 绘制进度条
                                    progressData.forEachIndexed { index, value ->
                                        val x = index * barWidth + barWidth / 2
                                        val barHeight = value * maxHeight
                                        
                                        // 确定颜色
                                        val color = when {
                                            value <= 0.3f -> improvementColor  // 补打卡
                                            value <= 0.6f -> neutralColor      // 部分完成
                                            else -> successColor               // 完成
                                        }
                                        
                                        // 绘制条形
                                        drawRect(
                                            color = color,
                                            topLeft = Offset(x - barWidth * 0.4f, bottomY - barHeight),
                                            size = androidx.compose.ui.geometry.Size(barWidth * 0.8f, barHeight)
                                        )
                                    }
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // 图例
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.Center
                            ) {
                                LegendItem(text = "完成", color = successColor)
                                Spacer(modifier = Modifier.width(16.dp))
                                LegendItem(text = "部分完成", color = neutralColor)
                                Spacer(modifier = Modifier.width(16.dp))
                                LegendItem(text = "补打卡", color = improvementColor)
                            }
                            
                            // X轴标签
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                Text(
                                    text = "1",
                    fontSize = 12.sp,
                                    color = textSecondaryColor
                                )
                                
                                Text(
                                    text = "15",
                                    fontSize = 12.sp,
                                    color = textSecondaryColor
                                )
                                
                                Text(
                                    text = "30",
                                    fontSize = 12.sp,
                                    color = textSecondaryColor
                )
            }
        }
    }
}

                // 最佳时段
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = surfaceColor),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(
        modifier = Modifier
            .fillMaxWidth()
                                .padding(20.dp)
                        ) {
                            Text(
                                text = "⏱️ 最佳时段",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = textPrimaryColor
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // 最佳时段图表
                            timeSlotData.forEach { (timeSlot, rate) ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = timeSlot,
                                        modifier = Modifier.width(120.dp),
                                        fontSize = 14.sp,
                                        color = textSecondaryColor
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
            Box(
                modifier = Modifier
                                            .weight(1f)
                                            .height(12.dp)
                                            .clip(RoundedCornerShape(6.dp))
                                            .background(Color(0xFFEEEEEE))
                                    ) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxHeight()
                                                .fillMaxWidth(rate)
                                                .clip(RoundedCornerShape(6.dp))
                                                .background(
                                                    if (rate > 0.7f) successColor else neutralColor
                                                )
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                Text(
                                        text = "${(rate * 100).toInt()}%",
                    fontSize = 14.sp,
                                        color = if (rate > 0.7f) successColor else neutralColor,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 按钮区域
                item {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        if (isCompleted) {
                            // 完成后的按钮
                            Button(
                                onClick = { /* 导出报告 */ },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = DustyLavender
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Download,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("导出报告")
                            }
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Button(
                                onClick = { 
                                    navController.navigate(AppDestinations.ADD_GOAL_ROUTE)
                                },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = successColor
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("新目标")
                            }
                        } else {
                            // 进行中的按钮
                            OutlinedButton(
                                onClick = { /* 调整计划 */ },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = DustyLavender
                                ),
                                border = ButtonDefaults.outlinedButtonBorder.copy(
                                    brush = androidx.compose.ui.graphics.SolidColor(DustyLavender)
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("调整计划")
                            }
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Button(
                                onClick = { 
                                    navController.popBackStack()
                                },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = DustyLavender
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PlayArrow,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("继续执行")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MetricRow(
    title: String,
    value: String,
    icon: ImageVector,
    valueColor: Color = Color(0xFF333333)
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        Box(
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .background(Color(0xFFF3F4F6)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = Color(0xFF6B7280),
                modifier = Modifier.size(18.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 标题
        Text(
            text = "• $title:",
            fontSize = 15.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF4B5563)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 值
        Text(
            text = value,
            fontSize = 15.sp,
            color = valueColor,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun LegendItem(text: String, color: Color) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color)
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
            Text(
            text = text,
            fontSize = 12.sp,
            color = Color(0xFF6B7280)
        )
    }
} 