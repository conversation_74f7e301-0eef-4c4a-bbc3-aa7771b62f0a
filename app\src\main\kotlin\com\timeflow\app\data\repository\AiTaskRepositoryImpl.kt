package com.timeflow.app.data.repository

import android.content.Context
import androidx.datastore.preferences.core.stringPreferencesKey
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timeflow.app.data.ai.model.*
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel
import com.timeflow.app.ui.viewmodel.aiSettingsDataStore
import com.timeflow.app.ui.viewmodel.dataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONObject
import java.util.concurrent.TimeUnit
import java.io.IOException
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.emptyPreferences
import kotlinx.coroutines.flow.catch

// 定义DataStore的key (复制自AiSettingsViewModel)
private val AI_SETTINGS_KEY = stringPreferencesKey("ai_settings")
private val AI_CONFIGS_KEY = stringPreferencesKey("ai_configs")
private val SELECTED_CONFIG_ID_KEY = stringPreferencesKey("selected_config_id")

/**
 * AI任务仓库实现 - 目前使用模拟数据实现开发阶段功能
 */
@Singleton
class AiTaskRepositoryImpl @Inject constructor(
    private val taskRepository: TaskRepository,
    private val context: Context
) : AiTaskRepository {

    // Gson实例用于JSON序列化
    private val gson = Gson()

    // 内存缓存
    private val decompositionCache = mutableMapOf<String, AiTaskDecomposition>()
    private val timeEstimationCache = mutableMapOf<String, AiTimeEstimation>()
    private val taskInsightCache = mutableMapOf<String, AiTaskInsight>()
    private val conversationCache = mutableMapOf<String, AiConversation>()
    private val userEfficiencyCache = mutableMapOf<String, UserEfficiencyData>()
    
    // 🚀 AI配置缓存 - 避免重复读取DataStore
    private var cachedAiConfig: AiConfig? = null
    private var configCacheTime: Long = 0
    private val configCacheValidDuration = 300_000L // 5分钟缓存有效期
    
    // 模拟延迟 (ms) - 优化为更快响应
    private val mockDelay = 100L // 从800ms减少到100ms，提升用户体验
    
    // 从DataStore获取AI设置的缓存版本
    private suspend fun getAssistantName(): String {
        try {
            // 从DataStore读取设置
            val settingsJson = context.aiSettingsDataStore.data.firstOrNull()?.get(AI_SETTINGS_KEY)
            if (settingsJson != null) {
                // 解析JSON为AiSettings对象
                return gson.fromJson(settingsJson, AiSettings::class.java).assistantName
            }
        } catch (e: Exception) {
            Timber.e(e, "获取AI设置失败")
        }
        // 出错或没有设置时返回默认名称
        return "小助手"
    }

    override fun getTaskDecomposition(taskId: String): Flow<AiTaskDecomposition?> = flow {
        delay(mockDelay)
        emit(decompositionCache[taskId])
    }

    override suspend fun createTaskDecomposition(
        taskId: String,
        subTasks: List<AiSubTask>,
        aiRecommendation: String?,
        reasonForDecomposition: String?
    ): AiTaskDecomposition = withContext(Dispatchers.IO) {
        delay(mockDelay)
        
        val totalEstimatedMinutes = subTasks.sumOf { it.estimatedMinutes }
        
        val decomposition = AiTaskDecomposition(
            originalTaskId = taskId,
            subTasks = subTasks,
            aiRecommendation = aiRecommendation,
            reasonForDecomposition = reasonForDecomposition,
            totalEstimatedMinutes = totalEstimatedMinutes
        )
        
        decompositionCache[taskId] = decomposition
        return@withContext decomposition
    }

    override suspend fun updateTaskDecomposition(decomposition: AiTaskDecomposition): Boolean = withContext(Dispatchers.IO) {
        delay(mockDelay)
        decompositionCache[decomposition.originalTaskId] = decomposition
        return@withContext true
    }

    override fun getTimeEstimation(taskId: String): Flow<AiTimeEstimation?> = flow {
        delay(mockDelay)
        emit(timeEstimationCache[taskId])
    }

    override suspend fun createTimeEstimation(
        taskId: String,
        estimatedMinutes: Int,
        confidence: Float,
        basis: String,
        similarTasks: List<SimilarTaskReference>,
        adjustmentFactors: List<AdjustmentFactor>
    ): AiTimeEstimation = withContext(Dispatchers.IO) {
        delay(mockDelay)
        
        val estimation = AiTimeEstimation(
            taskId = taskId,
            estimatedMinutes = estimatedMinutes,
            confidence = confidence,
            basis = basis,
            similarTasks = similarTasks,
            adjustmentFactors = adjustmentFactors
        )
        
        timeEstimationCache[taskId] = estimation
        return@withContext estimation
    }

    override fun getUserEfficiencyData(userId: String): Flow<UserEfficiencyData?> = flow {
        delay(mockDelay)
        emit(userEfficiencyCache[userId] ?: createDefaultUserEfficiency(userId))
    }

    override suspend fun updateUserEfficiencyData(efficiencyData: UserEfficiencyData): Boolean = withContext(Dispatchers.IO) {
        delay(mockDelay)
        userEfficiencyCache[efficiencyData.userId] = efficiencyData
        return@withContext true
    }

    override fun getTaskInsight(taskId: String): Flow<AiTaskInsight?> = flow {
        delay(mockDelay)
        emit(taskInsightCache[taskId])
    }

    override suspend fun saveTaskInsight(insight: AiTaskInsight): AiTaskInsight = withContext(Dispatchers.IO) {
        delay(mockDelay)
        taskInsightCache[insight.taskId] = insight
        return@withContext insight
    }

    override fun getRecommendedTimeSlots(
        taskId: String,
        fromDate: LocalDateTime,
        toDate: LocalDateTime
    ): Flow<List<TimeSlotRecommendation>> = flow {
        delay(mockDelay)
        
        // 为开发阶段生成模拟数据
        val recommendations = generateMockTimeSlotRecommendations(taskId, fromDate, toDate)
        emit(recommendations)
    }

    override suspend fun createConversation(userId: String): AiConversation = withContext(Dispatchers.IO) {
        delay(mockDelay)
        
        // 从DataStore获取助手名称
        val assistantName = getAssistantName()
        
        val conversation = AiConversation(
            userId = userId,
            messages = listOf(
                ConversationMessage(
                    content = "你好！我是${assistantName}，你的智能时间管理助手。今天有什么可以帮助你的？",
                    sender = MessageSender.AI,
                    messageType = MessageType.TEXT,
                    intentType = MessageIntent.GREETING
                )
            )
        )
        
        conversationCache[conversation.id] = conversation
        return@withContext conversation
    }

    override fun getUserConversations(userId: String): Flow<List<AiConversation>> = flow {
        delay(mockDelay)
        
        val userConversations = conversationCache.values.filter { it.userId == userId }
        emit(userConversations)
    }

    override fun getConversationMessages(conversationId: String): Flow<List<ConversationMessage>> = flow {
        delay(mockDelay)
        
        val conversation = conversationCache[conversationId]
        emit(conversation?.messages ?: emptyList())
    }

    override suspend fun addMessage(conversationId: String, message: ConversationMessage): AiConversation = withContext(Dispatchers.IO) {
        delay(mockDelay)
        
        val conversation = conversationCache[conversationId] ?: throw IllegalArgumentException("找不到对话: $conversationId")
        
        val updatedMessages = conversation.messages + message
        val updatedConversation = conversation.copy(
            messages = updatedMessages,
            updatedAt = LocalDateTime.now()
        )
        
        conversationCache[conversationId] = updatedConversation
        
        // 如果是用户消息，模拟AI回复
        if (message.sender == MessageSender.USER) {
            val aiResponse = generateAiResponse(message.content, conversation)
            val finalConversation = updatedConversation.copy(
                messages = updatedConversation.messages + aiResponse
            )
            conversationCache[conversationId] = finalConversation
            return@withContext finalConversation
        }
        
        return@withContext updatedConversation
    }

    override suspend fun createTaskFromNaturalLanguage(userId: String, naturalLanguageInput: String): Task = withContext(Dispatchers.IO) {
        delay(mockDelay * 2) // 模拟复杂自然语言处理
        
        Timber.d("从自然语言创建任务: $naturalLanguageInput")
        
        // 从输入中提取基本信息
        val taskTitle = extractTaskTitle(naturalLanguageInput)
        val dueDate = extractDueDate(naturalLanguageInput)
        val priority = extractPriority(naturalLanguageInput)
        
        // 创建任务
        val task = Task(
            id = UUID.randomUUID().toString(),
            title = taskTitle,
            description = "从自然语言生成的任务:\n\n原始输入: $naturalLanguageInput",
            dueDate = dueDate,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            priority = priority,
            type = TaskType.AI,
            aiGenerated = true
        )
        
        // 保存任务
        taskRepository.saveTask(task)
        return@withContext task
    }

    override suspend fun breakdownTask(taskId: String): List<Task> = withContext(Dispatchers.IO) {
        delay(mockDelay * 2) // 模拟复杂分析过程
        
        Timber.d("智能拆分任务: $taskId")
        
        val originalTask = taskRepository.getTaskById(taskId) ?: throw IllegalArgumentException("找不到任务: $taskId")
        
        // 根据任务内容生成子任务
        val subTasks = generateSubTasksForTask(originalTask)
        
        // 将子任务保存到数据库
        val createdSubTasks = mutableListOf<Task>()
        subTasks.forEach { subTask ->
            taskRepository.saveTask(subTask)
            createdSubTasks.add(subTask)
        }
        
        // 更新原始任务为"有子任务"状态
        val updatedOriginalTask = originalTask.copy(hasSubtasks = true)
        taskRepository.updateTask(updatedOriginalTask)
        
        return@withContext createdSubTasks
    }

    override suspend fun generateOptimalTaskSchedule(userId: String, date: LocalDateTime): List<Task> = withContext(Dispatchers.IO) {
        delay(mockDelay * 3) // 模拟复杂的智能日程规划
        
        Timber.d("为用户 $userId 生成最佳任务计划，日期: $date")
        
        // 获取所有未完成的任务
        val tasks = taskRepository.getAllTasks().filter { !it.isCompleted }
        
        // 获取用户的工作效率数据
        val efficiencyData = userEfficiencyCache[userId] ?: createDefaultUserEfficiency(userId)
        
        // 模拟智能排序和时间安排
        val scheduledTasks = optimizeTaskSchedule(tasks, date, efficiencyData)
        
        // 更新任务的开始时间
        scheduledTasks.forEachIndexed { index, task ->
            val startTime = date.withHour(9).plusMinutes((index * 90).toLong())
            val updatedTask = task.copy(
                startDate = startTime
            )
            taskRepository.updateTask(updatedTask)
        }
        
        return@withContext scheduledTasks
    }
    
    // -------------------- 辅助方法 --------------------
    
    private fun createDefaultUserEfficiency(userId: String): UserEfficiencyData {
        val timeOfDayEfficiency = mapOf(
            TimeOfDay.EARLY_MORNING to 0.8f,
            TimeOfDay.MORNING to 1.0f,
            TimeOfDay.AFTERNOON to 0.7f,
            TimeOfDay.EVENING to 0.9f,
            TimeOfDay.NIGHT to 0.6f
        )
        
        val dayOfWeekEfficiency = mapOf(
            1 to 0.8f, // 周一
            2 to 0.9f, // 周二
            3 to 1.0f, // 周三
            4 to 0.9f, // 周四
            5 to 0.8f, // 周五
            6 to 0.7f, // 周六
            7 to 0.6f  // 周日
        )
        
        val efficiencyData = UserEfficiencyData(
            userId = userId,
            timeOfDayEfficiency = timeOfDayEfficiency,
            dayOfWeekEfficiency = dayOfWeekEfficiency,
            taskTypeEfficiency = emptyMap(),
            lastUpdated = LocalDateTime.now()
        )
        
        userEfficiencyCache[userId] = efficiencyData
        return efficiencyData
    }
    
    private fun generateMockTimeSlotRecommendations(
        taskId: String,
        fromDate: LocalDateTime,
        toDate: LocalDateTime
    ): List<TimeSlotRecommendation> {
        val result = mutableListOf<TimeSlotRecommendation>()
        
        // 第一个建议：上午时段
        val morningSlot = TimeSlotRecommendation(
            startTime = fromDate.withHour(9).withMinute(0),
            endTime = fromDate.withHour(10).withMinute(30),
            confidence = 0.85f,
            reason = "你在上午通常效率较高，且日历中此时段无冲突",
            expectedProductivityGain = 0.3f
        )
        result.add(morningSlot)
        
        // 第二个建议：下午时段
        val afternoonSlot = TimeSlotRecommendation(
            startTime = fromDate.withHour(14).withMinute(0),
            endTime = fromDate.withHour(15).withMinute(30),
            confidence = 0.7f,
            reason = "下午此时段无会议安排，且你完成过类似任务",
            expectedProductivityGain = 0.2f
        )
        result.add(afternoonSlot)
        
        // 第三个建议：明天上午
        val tomorrowSlot = TimeSlotRecommendation(
            startTime = fromDate.plusDays(1).withHour(10).withMinute(0),
            endTime = fromDate.plusDays(1).withHour(11).withMinute(30),
            confidence = 0.6f,
            reason = "明天上午时段你通常能够更专注地工作",
            expectedProductivityGain = 0.15f,
            conflictingEvents = listOf("可能与每周团队会议有冲突")
        )
        result.add(tomorrowSlot)
        
        return result
    }
    
    /**
     * 生成AI响应
     * @param userMessage 用户消息
     * @param conversation 当前对话
     * @return 生成的AI响应消息
     */
    private suspend fun generateAiResponse(
        userMessage: String, 
        conversation: AiConversation
    ): ConversationMessage = withContext(Dispatchers.IO) {
        try {
            // 从datastore获取当前选中的AI模型配置
            val aiConfig = getSelectedAiConfig()
            
            if (aiConfig.apiKey.isBlank()) {
                Timber.w("API密钥为空, 提供商: ${aiConfig.provider}, 模型: ${aiConfig.modelName}")
                return@withContext ConversationMessage(
                    content = "未设置API密钥，请在AI设置中完成配置后再试。\n\n请按以下步骤操作：\n1. 进入\"设置\"\n2. 选择\"AI设置\"\n3. 创建新AI配置或编辑现有配置\n4. 输入有效的API密钥\n5. 保存并选择该配置",
                    sender = MessageSender.AI,
                    messageType = MessageType.ERROR,
                    intentType = MessageIntent.NOT_UNDERSTOOD
                )
            }
            
            Timber.d("使用AI配置：${aiConfig.name}, 提供商: ${aiConfig.provider}, API密钥: ${aiConfig.apiKey.take(5)}...")
            
            // 构建对话历史
            val messageHistory = conversation.messages.takeLast(10).map { msg ->
                mapOf(
                    "role" to if (msg.sender == MessageSender.USER) "user" else "assistant",
                    "content" to msg.content
                )
            }
            
            // 添加系统提示，给模型提供角色定义
            val systemPrompt = """
你是TimeFlow应用中的AI助手，专门帮助用户进行任务管理、时间规划、情绪管理、健康规划和效率提升。
请遵循以下原则：
1. 提供准确、具体且实用的回答，专注于任务管理和时间效率相关的问题
2. 考虑对话上下文，确保回答连贯且与用户之前的问题相关
3. 尽量使用简洁明了的语言，避免过长的回应
4. 可以围绕以下主题提供专业建议：任务拆分、时间管理、优先级设置、专注技巧、效率提升、情绪管理等
5. 当用户提出非任务管理相关的问题时，礼貌地引导回到应用的核心功能
6. 永远记住你是一个个人助理，最大化地帮助用户进行自我提升和管理

当前应用支持功能：
- 任务创建与管理
- 子任务拆分
- 番茄工作法计时
- 时间统计与分析
- 目标进度追踪
- 情绪记录
"""
            
            val requestBody = mapOf(
                "model" to aiConfig.modelName,
                "messages" to (listOf(mapOf("role" to "system", "content" to systemPrompt)) + messageHistory),
                "temperature" to aiConfig.temperature,
                "max_tokens" to aiConfig.maxTokens
            )
            
            // 转换为JSON
            val requestJson = gson.toJson(requestBody)
            
            // 🚀 优化HTTP请求超时设置
            val client = OkHttpClient.Builder()
                .connectTimeout(8, TimeUnit.SECONDS)   // 连接超时减少到8s
                .readTimeout(25, TimeUnit.SECONDS)     // 读取超时减少到25s
                .writeTimeout(15, TimeUnit.SECONDS)    // 写入超时减少到15s
                .build()
                
            val mediaType = "application/json; charset=utf-8".toMediaType()
            val requestBodyObj = requestJson.toRequestBody(mediaType)
            
            val request = Request.Builder()
                .url("${aiConfig.serverUrl}/chat/completions")
                .addHeader("Authorization", "Bearer ${aiConfig.apiKey}")
                .addHeader("Content-Type", "application/json")
                .post(requestBodyObj)
                .build()
                
            // 执行请求
            Timber.d("发送AI请求到: ${aiConfig.serverUrl}, 模型: ${aiConfig.modelName}")
            val response = client.newCall(request).execute()
            
            // 处理响应
            if (!response.isSuccessful) {
                val errorBody = response.body?.string() ?: "未知错误"
                Timber.e("API调用失败: ${response.code}, $errorBody")
                
                // 提供更详细的错误信息和解决方案
                val errorMessage = when (response.code) {
                    401 -> "API密钥验证失败 (401)。请检查您的API密钥是否正确，并确保有足够的额度。"
                    403 -> "API访问被拒绝 (403)。请检查您的API密钥权限是否正确设置。"
                    404 -> "API端点未找到 (404)。请确认服务器URL地址正确: ${aiConfig.serverUrl}"
                    429 -> "API请求过多 (429)。您的API密钥已超出限额或请求频率过高，请稍后再试。"
                    500, 502, 503, 504 -> "AI服务器错误 (${response.code})。服务提供商${aiConfig.provider}可能存在暂时性故障，请稍后重试。"
                    else -> "API调用失败: ${response.code}。详情: $errorBody"
                }
                
                return@withContext ConversationMessage(
                    content = "$errorMessage\n\n您可以尝试以下解决方案：\n1. 检查您的网络连接\n2. 确认API密钥是否有效\n3. 尝试更换服务提供商或模型\n4. 如问题持续，可能需要联系服务提供商支持",
                    sender = MessageSender.AI,
                    messageType = MessageType.ERROR,
                    intentType = MessageIntent.NOT_UNDERSTOOD
                )
            }
            
            // 记录API交互详情，方便调试
            val requestMap = request as? Map<*, *>
            val modelName = try {
                val modelValue = requestMap?.get("model")
                if (modelValue is String) modelValue else "未知模型"
            } catch (e: Exception) {
                "未知模型"
            }
            
            val messagesCount = try {
                val messages = requestMap?.get("messages")
                if (messages is List<*>) messages.size else 0
            } catch (e: Exception) {
                0
            }
            
            Timber.d("API请求详情: $modelName, 消息数: $messagesCount")
            
            // 解析响应
            val responseBody = response.body?.string() ?: throw IOException("空响应")
            val jsonResponse = JSONObject(responseBody)
            
            // 提取内容
            val choices = jsonResponse.getJSONArray("choices")
            if (choices.length() == 0) {
                return@withContext ConversationMessage(
                    content = "AI服务返回结果为空。这可能是因为：\n1. 请求格式不正确\n2. 模型无法生成符合要求的回复\n3. 服务提供商存在问题\n\n请尝试重新描述您的问题，或更换另一个AI模型配置。",
                    sender = MessageSender.AI,
                    messageType = MessageType.ERROR,
                    intentType = MessageIntent.NOT_UNDERSTOOD
                )
            }
            
            val aiContent = choices.getJSONObject(0)
                .getJSONObject("message")
                .getString("content")
                
            return@withContext ConversationMessage(
                content = aiContent,
                sender = MessageSender.AI,
                messageType = MessageType.TEXT
            )
            
        } catch (e: Exception) {
            Timber.e(e, "生成AI响应失败: ${e.message}")
            
            // 提供更详细的错误诊断信息
            val errorCause = when {
                e.message?.contains("Failed to connect") == true -> "网络连接失败。请检查您的互联网连接，确保能够访问AI服务提供商的服务器。"
                e.message?.contains("timeout") == true -> "请求超时。服务器响应时间过长，可能是网络拥堵或服务器负载过高。"
                e.message?.contains("JSON") == true -> "API响应解析失败。服务器返回的数据格式不符合预期。"
                else -> "生成AI响应时遇到技术问题: ${e.localizedMessage}"
            }
            
            // 出错时返回错误消息
            return@withContext ConversationMessage(
                content = "抱歉，$errorCause\n\n您可以尝试：\n1. 检查网络连接\n2. 稍后再试\n3. 在设置中更换AI服务提供商或模型",
                sender = MessageSender.AI,
                messageType = MessageType.ERROR,
                intentType = MessageIntent.NOT_UNDERSTOOD
            )
        }
    }
    
    /**
     * 🚀 优化AI配置获取，支持缓存和错误处理
     */
    private suspend fun getSelectedAiConfig(): AiConfig {
        // 检查缓存是否有效
        val now = System.currentTimeMillis()
        if (cachedAiConfig != null && (now - configCacheTime) < configCacheValidDuration) {
            return cachedAiConfig!!
        }
        
        try {
            // 从DataStore读取配置
            val preferences = dataStore.data.firstOrNull()
            
            // 先获取选中的配置ID
            val selectedConfigId = preferences?.get(SELECTED_CONFIG_ID_KEY)
            if (selectedConfigId.isNullOrBlank()) {
                Timber.w("未选择AI配置，使用默认配置")
                val defaultConfig = getDefaultAiConfig()
                cacheConfig(defaultConfig)
                return defaultConfig
            }
            
            // 读取配置列表
            val configsJson = preferences.get(AI_CONFIGS_KEY)
            if (configsJson.isNullOrBlank()) {
                Timber.w("未找到AI配置列表，使用默认配置")
                val defaultConfig = getDefaultAiConfig()
                cacheConfig(defaultConfig)
                return defaultConfig
            }
            
            // 解析配置列表
            val configsType = object : TypeToken<List<AiConfig>>() {}.type
            val configs: List<AiConfig> = gson.fromJson(configsJson, configsType)
            
            // 查找选中的配置
            val selectedConfig = configs.find { it.id == selectedConfigId }
            if (selectedConfig == null) {
                Timber.w("找不到ID为 $selectedConfigId 的配置，使用默认配置")
                val defaultConfig = getDefaultAiConfig()
                cacheConfig(defaultConfig)
                return defaultConfig
            }
            
            // 确认API密钥是否有效
            if (selectedConfig.apiKey.isBlank()) {
                Timber.w("配置的API密钥为空，使用默认配置")
                val defaultConfig = getDefaultAiConfig()
                cacheConfig(defaultConfig)
                return defaultConfig
            }
            
            // 缓存并返回有效配置
            cacheConfig(selectedConfig)
            Timber.d("🚀 成功获取AI配置: ${selectedConfig.name}, 提供商: ${selectedConfig.provider}")
            return selectedConfig
            
        } catch (e: Exception) {
            Timber.e(e, "获取AI配置失败")
            val defaultConfig = getDefaultAiConfig()
            cacheConfig(defaultConfig)
            return defaultConfig
        }
    }
    
    /**
     * 缓存AI配置
     */
    private fun cacheConfig(config: AiConfig) {
        cachedAiConfig = config
        configCacheTime = System.currentTimeMillis()
    }
    
    /**
     * 🚀 清除AI配置缓存 - 配置更新时调用
     */
    fun clearConfigCache() {
        cachedAiConfig = null
        configCacheTime = 0
        Timber.d("🚀 AI配置缓存已清除")
    }
    
    /**
     * 获取默认AI配置
     * @return 默认AI配置
     */
    private fun getDefaultAiConfig(): AiConfig {
        return AiConfig(
            id = "default",
            name = "默认配置",
            provider = "DeepSeek",
            apiKey = "",
            modelName = "deepseek-chat",
            serverUrl = "https://api.deepseek.com/v1",
            isDefault = true
        )
    }
    
    private fun extractTaskTitle(input: String): String {
        // 简单实现，实际应用需要更复杂的NLP
        return when {
            input.contains("标题") -> {
                val afterTitle = input.substringAfter("标题").trim()
                val title = afterTitle.substringBefore("。").trim()
                title.ifEmpty { "新任务" }
            }
            else -> {
                val title = input.split("。").firstOrNull()?.trim() ?: "新任务"
                if (title.length > 50) title.substring(0, 50) + "..." else title
            }
        }
    }
    
    private fun extractDueDate(input: String): LocalDateTime? {
        // 简单实现，实际应用需要日期解析库
        return when {
            input.contains("明天") -> LocalDateTime.now().plusDays(1).withHour(18).withMinute(0)
            input.contains("下周") -> LocalDateTime.now().plusWeeks(1).withHour(18).withMinute(0)
            input.contains("下个月") -> LocalDateTime.now().plusMonths(1).withHour(18).withMinute(0)
            input.contains("今天") -> LocalDateTime.now().withHour(18).withMinute(0)
            else -> null
        }
    }
    
    private fun extractPriority(input: String): Priority {
        return when {
            input.contains("紧急") || input.contains("重要") -> Priority.URGENT
            input.contains("高优先级") -> Priority.HIGH
            input.contains("低优先级") -> Priority.LOW
            else -> Priority.MEDIUM
        }
    }
    
    private fun generateSubTasksForTask(task: Task): List<Task> {
        // 根据任务标题和描述生成子任务
        // 实际应用中应该使用NLP或机器学习方法
        
        val subTasks = mutableListOf<Task>()
        val now = LocalDateTime.now()
        
        // 示例：为"项目提案"生成子任务
        if (task.title.contains("提案") || task.title.contains("计划") || task.title.contains("项目")) {
            subTasks.add(
                Task(
                    title = "用户需求分析",
                    description = "收集和分析用户需求，确定项目目标",
                    dueDate = task.dueDate?.minusDays(3),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 120
                )
            )
            
            subTasks.add(
                Task(
                    title = "市场调研",
                    description = "分析竞品和市场情况",
                    dueDate = task.dueDate?.minusDays(2),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 90
                )
            )
            
            subTasks.add(
                Task(
                    title = "编写提案文档",
                    description = "基于需求和调研结果编写提案",
                    dueDate = task.dueDate?.minusDays(1),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 180
                )
            )
        } 
        // 示例：为"设计"任务生成子任务
        else if (task.title.contains("设计")) {
            subTasks.add(
                Task(
                    title = "设计调研",
                    description = "收集设计灵感和参考资料",
                    dueDate = task.dueDate?.minusDays(4),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 60
                )
            )
            
            subTasks.add(
                Task(
                    title = "创建草图",
                    description = "绘制初步设计草图",
                    dueDate = task.dueDate?.minusDays(3),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 90
                )
            )
            
            subTasks.add(
                Task(
                    title = "制作原型",
                    description = "基于草图创建可交互原型",
                    dueDate = task.dueDate?.minusDays(1),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 120
                )
            )
        }
        // 默认拆分为准备、执行、检查三个阶段
        else {
            subTasks.add(
                Task(
                    title = "准备任务",
                    description = "准备任务所需的资料和工具",
                    dueDate = task.dueDate?.minus(2, ChronoUnit.DAYS),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 30
                )
            )
            
            subTasks.add(
                Task(
                    title = "执行任务",
                    description = "完成任务的主要内容",
                    dueDate = task.dueDate?.minus(1, ChronoUnit.DAYS),
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 60
                )
            )
            
            subTasks.add(
                Task(
                    title = "检查完成情况",
                    description = "检查和优化任务结果",
                    dueDate = task.dueDate,
                    createdAt = now,
                    updatedAt = now,
                    parentTaskId = task.id,
                    depth = task.depth + 1,
                    type = TaskType.AI,
                    estimatedTimeMinutes = 30
                )
            )
        }
        
        return subTasks
    }
    
    private fun optimizeTaskSchedule(
        tasks: List<Task>,
        date: LocalDateTime,
        efficiencyData: UserEfficiencyData
    ): List<Task> {
        // 按类别筛选任务
        val todayTasks = tasks.filter { 
            it.dueDate?.toLocalDate()?.isEqual(date.toLocalDate()) ?: false 
        }
        
        val upcomingTasks = tasks.filter { 
            val dueDate = it.dueDate
            dueDate != null && dueDate.isAfter(date) && dueDate.isBefore(date.plusDays(3))
        }
        
        val highPriorityTasks = tasks.filter { 
            it.priority == Priority.URGENT || it.priority == Priority.HIGH 
        }
        
        // 合并并去重
        val combinedTasks = (todayTasks + upcomingTasks + highPriorityTasks).distinct()
        
        // 排序逻辑
        val sortedTasks = combinedTasks.sortedWith { a, b ->
            // 首先按优先级排序（高优先级在前）
            val priorityA = a.priority ?: Priority.MEDIUM
            val priorityB = b.priority ?: Priority.MEDIUM
            val priorityComparison = priorityToInt(priorityB) - priorityToInt(priorityA)
            if (priorityComparison != 0) {
                return@sortedWith priorityComparison
            }
            
            // 然后按截止日期排序（近期在前）
            val dueDateA = a.dueDate ?: LocalDateTime.now().plusYears(1)
            val dueDateB = b.dueDate ?: LocalDateTime.now().plusYears(1)
            val dateComparison = dueDateA.compareTo(dueDateB)
            if (dateComparison != 0) {
                return@sortedWith dateComparison
            }
            
            // 最后按预计时间排序（耗时长的在前）
            val timeA = a.estimatedTimeMinutes ?: 0
            val timeB = b.estimatedTimeMinutes ?: 0
            timeB - timeA
        }
        
        // 只返回前5个任务
        return sortedTasks.take(5)
    }
    
    private fun priorityToInt(priority: Priority): Int {
        return when (priority) {
            Priority.URGENT -> 4
            Priority.HIGH -> 3
            Priority.MEDIUM -> 2
            Priority.LOW -> 1
            else -> 0
        }
    }

    /**
     * 处理用户消息并获取AI响应
     */
    override suspend fun processUserMessage(
        conversationId: String,
        userMessage: String,
        messageHistory: List<ConversationMessage>,
        relatedTaskId: String?,
        relatedTaskTitle: String?
    ): AiConversation {
        Timber.d("处理用户消息: '$userMessage', 对话ID: $conversationId")
        
        // 1. 获取当前对话
        val conversation = getConversationById(conversationId) ?: throw IllegalArgumentException("找不到对话: $conversationId")
        
        // 2. 添加用户消息到对话
        val userMessageObj = ConversationMessage(
            content = userMessage,
            sender = MessageSender.USER,
            messageType = MessageType.TEXT,
            relatedTaskId = relatedTaskId,
            relatedTaskTitle = relatedTaskTitle
        )
        
        val conversationWithUserMessage = addMessage(conversationId, userMessageObj)
        
        // 3. 构建AI请求
        val request = buildAiRequest(messageHistory, userMessage)
        
        // 4. 调用AI API获取响应
        val aiResponse = try {
            executeApiRequest(request)
        } catch (e: Exception) {
            Timber.e(e, "调用AI API失败")
            throw e
        }
        
        // 5. 添加AI响应到对话
        val aiMessageObj = ConversationMessage(
            content = aiResponse,
            sender = MessageSender.AI,
            messageType = MessageType.TEXT,
            relatedTaskId = relatedTaskId,
            relatedTaskTitle = relatedTaskTitle
        )
        
        return addMessage(conversationId, aiMessageObj)
    }

    /**
     * 构建AI API请求
     */
    private suspend fun buildAiRequest(messageHistory: List<ConversationMessage>, lastUserMessage: String): Map<String, Any> {
        // 获取当前AI配置
        val aiConfig = getAiConfig()
        
        // 格式化消息历史，最多取最近20条消息，避免请求过大
        val formattedMessages = messageHistory.takeLast(20).map { message ->
            mapOf(
                "role" to if (message.sender == MessageSender.USER) "user" else "assistant",
                "content" to message.content
            )
        }
        
        // 🚀 优化系统提示词，更简洁高效
        val systemPrompt = """
你是TimeFlow时间管理助手。专注提供简洁实用的任务管理建议。
核心功能：任务拆分、时间规划、效率提升、番茄工作法、目标追踪。
回答要求：简洁明了、实用具体、专注时间管理主题。
"""
        
        val systemMessage = mapOf(
            "role" to "system",
            "content" to systemPrompt
        )
        
        // 构建完整请求
        return mapOf(
            "model" to (aiConfig.modelName ?: "gpt-3.5-turbo"),
            "messages" to (listOf(systemMessage) + formattedMessages),
            "max_tokens" to 600,  // 🚀 从1000减少到600，提升响应速度
            "temperature" to 0.7,
            "stream" to false  // 🚀 确保使用非流式响应，减少解析复杂度
        )
    }

    /**
     * 执行API请求并获取响应，添加重试机制和增强的错误处理
     */
    private suspend fun executeApiRequest(request: Map<String, Any>): String {
        val aiConfig = getAiConfig()
        val apiKey = aiConfig.apiKey
        val endpoint = aiConfig.serverUrl
        
        if (apiKey.isBlank()) {
            throw IllegalStateException("API密钥未设置")
        }
        
        // 🚀 进一步优化重试配置 - 更快的失败和重试
        val maxRetries = 1  // 从2次减少到1次，快速响应
        var retryCount = 0
        var lastException: Exception? = null
        
        // 使用适当的HTTP客户端进行API请求
        return withContext(Dispatchers.IO) {
            // 重试循环
            while (retryCount < maxRetries) {
                try {
                    // 🚀 极致优化超时设置，确保AI API能够成功响应
                    val client = OkHttpClient.Builder()
                        .connectTimeout(5, TimeUnit.SECONDS)   // 连接超时减少到5s
                        .writeTimeout(10, TimeUnit.SECONDS)    // 写入超时减少到10s
                        .readTimeout(45, TimeUnit.SECONDS)     // 读取超时增加到45s，给AI更多处理时间
                        .build()
                    
                    // 创建HTTP请求
                    val jsonBody = org.json.JSONObject(request).toString()
                    val requestBody = jsonBody.toRequestBody("application/json; charset=utf-8".toMediaType())
                    
                    // 安全构建URL，确保格式正确
                    val sanitizedUrl = buildSafeApiUrl(endpoint)
                    Timber.d("API请求URL (尝试${retryCount+1}/${maxRetries}): $sanitizedUrl")
                    
                    val httpRequest = Request.Builder()
                        .url(sanitizedUrl)
                        .addHeader("Content-Type", "application/json")
                        .addHeader("Authorization", "Bearer $apiKey")
                        .post(requestBody)
                        .build()
                    
                    Timber.d("发送API请求 (尝试${retryCount+1}/${maxRetries})")
                    
                    val response = client.newCall(httpRequest).execute()
                    
                    // 增强错误处理
                    if (!response.isSuccessful) {
                        val errorBody = response.body?.string() ?: "无响应内容"
                        val errorMsg = when (response.code) {
                            401 -> "API密钥验证失败 (401)"
                            403 -> "API访问被拒绝 (403)"
                            404 -> "API端点未找到 (404)，请检查服务器URL: $sanitizedUrl"
                            429 -> "API请求过多 (429)，请求频率过高或超出配额"
                            500, 502, 503, 504 -> "AI服务器错误 (${response.code})"
                            else -> "API错误: ${response.code}"
                        }
                        
                        Timber.e("API响应错误 (尝试${retryCount+1}/${maxRetries}): $errorMsg, 状态码: ${response.code}, 内容: $errorBody")
                        
                        // 针对不同错误码决定是否重试
                        if (response.code in listOf(500, 502, 503, 504, 429)) {
                            // 服务器错误或限流，可以重试
                            retryCount++
                            if (retryCount < maxRetries) {
                                val backoffMs = calculateBackoffDelay(retryCount)
                                Timber.w("将在${backoffMs}ms后重试 (${retryCount+1}/${maxRetries})")
                                delay(backoffMs)
                                continue
                            } else {
                                throw IOException("$errorMsg（已重试${maxRetries}次）")
                            }
                        } else {
                            // 其他错误不重试
                            throw IOException(errorMsg)
                        }
                    }
                    
                    val responseBody = response.body?.string() ?: throw IOException("空响应")
                    
                    // 记录API交互详情，方便调试
                    Timber.d("API请求成功: 状态码 ${response.code}, 响应长度: ${responseBody.length}")
                    
                    // 增强的JSON解析，提供更多错误处理
                    try {
                        // 解析JSON响应
                        val jsonResponse = org.json.JSONObject(responseBody)
                        
                        // 支持多种API格式
                        return@withContext extractContentFromResponse(jsonResponse) 
                            ?: throw IOException("无法从响应中提取内容: $responseBody")
                            
                    } catch (e: Exception) {
                        Timber.e(e, "JSON解析失败: ${responseBody.take(1000)}${if (responseBody.length > 1000) "..." else ""}")
                        throw IOException("解析AI响应失败: ${e.message}")
                    }
                } catch (e: Exception) {
                    lastException = e
                    
                    // 决定是否重试
                    val shouldRetry = when (e) {
                        is java.net.UnknownHostException,
                        is java.net.SocketTimeoutException,
                        is java.net.ConnectException,
                        is java.io.InterruptedIOException -> true // 网络错误可以重试
                        is IOException -> {
                            // 部分IO错误可以重试
                            val message = e.message ?: ""
                            message.contains("超时") || message.contains("timeout") || 
                            message.contains("CONNECTION_RESET") || message.contains("EPIPE")
                        }
                        else -> false // 其他错误不重试
                    }
                    
                    if (shouldRetry && retryCount < maxRetries) {
                        retryCount++
                        val backoffMs = calculateBackoffDelay(retryCount)
                        Timber.w("API请求失败: ${e.message}，将在${backoffMs}ms后重试 (${retryCount}/${maxRetries})")
                        delay(backoffMs)
                        continue
                    } else {
                        val errorMessage = when (e) {
                            is java.net.UnknownHostException -> "网络错误: 无法连接到API服务器，请检查网络连接和服务器地址"
                            is java.net.SocketTimeoutException -> "网络超时: API服务器响应时间过长，请稍后再试"
                            is IOException -> "网络错误: ${e.message ?: "未知IO错误"}"
                            else -> "执行API请求失败: ${e.message}"
                        }
                        Timber.e(e, "API请求失败且无法重试: $errorMessage")
                        throw IOException("$errorMessage (已重试$retryCount/${maxRetries}次)")
                    }
                }
            }
            
            // 如果所有重试都失败
            throw lastException ?: IOException("API请求失败且重试用尽")
        }
    }
    
    /**
     * 🚀 优化退避延迟时间（快速重试策略）
     */
    private fun calculateBackoffDelay(retryCount: Int): Long {
        // 优化: 更短的重试延迟，从指数退避改为线性递增
        return (retryCount * 300L).coerceAtMost(1000L)  // 最多1秒延迟
    }
    
    /**
     * 构建安全的API URL
     */
    private fun buildSafeApiUrl(baseUrl: String): String {
        // 移除末尾的斜杠
        val trimmedUrl = baseUrl.trim().trimEnd('/')
        
        // 检查URL是否包含协议
        val hasProtocol = trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")
        
        // 如果没有协议，添加https://
        val urlWithProtocol = if (hasProtocol) trimmedUrl else "https://$trimmedUrl"
        
        // 确保URL结尾有/chat/completions路径
        return if (urlWithProtocol.endsWith("/chat/completions")) {
            urlWithProtocol
        } else {
            "$urlWithProtocol/chat/completions"
        }
    }
    
    /**
     * 从不同格式的API响应中提取内容
     */
    private fun extractContentFromResponse(jsonResponse: JSONObject): String? {
        // 首先尝试解析OpenAI/DeepSeek格式
        try {
            val choices = jsonResponse.optJSONArray("choices")
            if (choices != null && choices.length() > 0) {
                val firstChoice = choices.getJSONObject(0)
                
                // 尝试消息格式
                val message = firstChoice.optJSONObject("message")
                if (message != null) {
                    val content = message.optString("content", "")
                    if (content.isNotBlank()) {
                        return content
                    }
                }
                
                // 尝试文本格式
                val text = firstChoice.optString("text", "")
                if (text.isNotBlank()) {
                    return text
                }
            }
            
            // 尝试其他常见格式
            val content = jsonResponse.optString("content", "")
            if (content.isNotBlank()) {
                return content
            }
            
            val response = jsonResponse.optString("response", "")
            if (response.isNotBlank()) {
                return response
            }
            
            val text = jsonResponse.optString("text", "")
            if (text.isNotBlank()) {
                return text
            }
            
            // 查找任何内容的字段
            for (key in jsonResponse.keys()) {
                val value = jsonResponse.optString(key, "")
                if (value.isNotBlank() && value.length > 10) { // 长度>10可能是有意义的内容
                    return value
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "解析API响应的内容时出错")
        }
        
        return null
    }
    
    /**
     * 获取当前AI配置
     */
    private suspend fun getAiConfig(): AiConfig {
        // 从DataStore加载配置
        val preferences = try {
            dataStore.data.firstOrNull() ?: throw IllegalStateException("无法获取数据存储")
        } catch (e: Exception) {
            Timber.e(e, "读取DataStore失败")
            throw IllegalStateException("无法读取AI配置: ${e.message}")
        }
        
        val selectedConfigId = preferences[SELECTED_CONFIG_ID_KEY] ?: ""
        val configsJson = preferences[AI_CONFIGS_KEY] ?: ""
        
        if (configsJson.isBlank() || selectedConfigId.isBlank()) {
            throw IllegalStateException("AI配置未设置")
        }
        
        val gson = Gson()
        val configsType = object : TypeToken<List<AiConfig>>() {}.type
        val configs: List<AiConfig> = gson.fromJson(configsJson, configsType)
        
        return configs.find { it.id == selectedConfigId } 
            ?: throw IllegalStateException("找不到所选AI配置: $selectedConfigId")
    }

    /**
     * 通过ID获取对话
     * @param conversationId 对话ID
     * @return 对话对象，如不存在则返回null
     */
    private suspend fun getConversationById(conversationId: String): AiConversation? {
        return conversationCache[conversationId]
    }

    /**
     * 获取DataStore访问实例
     */
    private val dataStore by lazy {
        context.dataStore
    }
    
    /**
     * 获取AI配置DataStore实例
     */
    private val aiSettingsDataStore by lazy {
        context.aiSettingsDataStore
    }

    /**
     * 🚀 AI智能任务拆解 - 优化版，支持性能监控
     */
    override suspend fun aiSplitTaskToSubtasks(task: Task): List<String> = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        val aiConfig = getSelectedAiConfig()
        if (aiConfig.apiKey.isBlank()) {
            Timber.w("API密钥为空，无法调用AI任务拆解")
            return@withContext emptyList<String>()
        }
        
        // 🎯 智能提示词 - 根据可用信息生成精细的针对性拆解
        val contextParts = mutableListOf<String>()

        // 添加目标标题
        contextParts.add("目标：${task.title}")

        // 智能添加描述信息
        if (task.description.isNotBlank() && task.description.length > 5) {
            val cleanDescription = task.description.take(80).trim()
            contextParts.add("描述：$cleanDescription")
        }

        // 添加时间信息（如果有）
        if (task.dueDate != null) {
            val daysLeft = java.time.temporal.ChronoUnit.DAYS.between(
                java.time.LocalDateTime.now().toLocalDate(),
                task.dueDate.toLocalDate()
            ).toInt()
            if (daysLeft > 0 && daysLeft <= 365) {
                contextParts.add("时限：${daysLeft}天")
            }
        }

        // 添加优先级信息（如果有）
        if (task.priority != null && task.priority != Priority.MEDIUM) {
            contextParts.add("优先级：${task.priority}")
        }

        val contextInfo = contextParts.joinToString("，")

        val prompt = """根据以下信息拆解为3-5个具体子任务：
$contextInfo

格式：
1. 子任务名
2. 子任务名
3. 子任务名

每个子任务不超过12字，只返回编号列表。""".trimIndent()
        
        // 🎯 优化请求体 - 平衡精细度和响应速度
        val requestBody = mapOf(
            "model" to aiConfig.modelName,
            "messages" to listOf(
                mapOf("role" to "user", "content" to prompt)  // 只使用user角色，更直接
            ),
            "temperature" to 0.4,  // 适度提高创造性，生成更精细的拆解
            "max_tokens" to 250,   // 适度增加输出长度，支持更精细的表达
            "stream" to false      // 确保非流式响应
        )
        
        try {
            // 重构为使用通用executeApiRequest方法
            val aiResponse = executeApiRequest(requestBody)
            
            // 🎯 增强解析算法 - 支持更精准的子目标提取
            val subTasks = aiResponse.lines()
                .asSequence()  // 使用序列提高性能
                .mapNotNull { line ->
                    // 匹配"数字. 内容"格式，支持多种分隔符
                    val match = Regex("^\\s*(\\d+)\\s*[.．、]\\s*(.+)$").find(line.trim())
                    match?.groupValues?.getOrNull(2)?.trim()?.let { task ->
                        // 更灵活的长度处理：保持完整性，但限制过长内容
                        when {
                            task.length <= 15 -> task  // 理想长度，直接使用
                            task.length <= 25 -> task  // 可接受长度，保持完整
                            else -> {
                                // 过长时智能截取，保持语义完整
                                val truncated = task.take(20)
                                if (truncated.endsWith("的") || truncated.endsWith("和") || truncated.endsWith("或")) {
                                    truncated.dropLast(1)
                                } else {
                                    truncated
                                }
                            }
                        }
                    }
                }
                .filter { it.isNotBlank() && it.length >= 3 }  // 过滤太短的结果
                .take(5)  // 最多5个
                .toList()
            
            val duration = System.currentTimeMillis() - startTime
            Timber.d("🚀 AI拆解成功: $subTasks (耗时: ${duration}ms)")
            return@withContext subTasks
            
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            Timber.e(e, "AI任务拆解失败 (耗时: ${duration}ms): ${e.message}")
            // 🚫 禁用备用机制 - 只使用AI拆解，失败时抛出异常
            throw Exception("AI拆解失败: ${e.message}", e)
        }
    }
    
    /**
     * 🚀 快速本地拆解 - AI失败时的备用方案
     */
    private fun generateQuickLocalSubtasks(task: Task): List<String> {
        val title = task.title.lowercase()
        return when {
            "学习" in title || "课程" in title -> listOf("制定学习计划", "准备学习资料", "开始学习", "总结复习")
            "项目" in title || "开发" in title -> listOf("需求分析", "设计方案", "编码实现", "测试验证")
            "报告" in title || "文档" in title -> listOf("收集资料", "整理大纲", "撰写内容", "修改完善")
            "会议" in title -> listOf("准备材料", "确认议程", "参加会议", "整理纪要")
            "购买" in title || "采购" in title -> listOf("调研对比", "确定需求", "下单购买", "验收确认")
            "健身" in title || "运动" in title -> listOf("热身准备", "核心训练", "放松整理")
            "阅读" in title -> listOf("选择内容", "深度阅读", "记录笔记", "总结反思")
            else -> listOf("准备阶段", "执行阶段", "检查完善")  // 通用拆解
                 }.take(5)  // 最多5个
    }
} 