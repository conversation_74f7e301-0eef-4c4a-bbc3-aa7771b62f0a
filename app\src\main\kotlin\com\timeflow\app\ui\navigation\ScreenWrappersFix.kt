package com.timeflow.app.ui.navigation

import androidx.compose.runtime.Composable 
import androidx.navigation.NavController
import com.timeflow.app.ui.screen.milestone.MilestoneScreen

/**
 * 测试类，用于验证MilestoneScreen的导入
 */
object ScreenWrappersFix {
    @Composable
    fun TestMilestoneScreen(navController: NavController) {
        // 这里测试MilestoneScreen的导入是否正常
        MilestoneScreen(navController = navController)
    }
} 