package com.timeflow.app.ai

import com.timeflow.app.data.model.GoalCategory
import com.timeflow.app.data.model.getDefaultGoalCategories
import kotlin.math.exp

/**
 * 目标分类智能分析器
 * 基于文本内容自动推荐最合适的目标分类
 */
class GoalCategoryClassifier {
    
    private val categories = getDefaultGoalCategories()
    
    /**
     * 分类推荐结果
     */
    data class CategoryRecommendation(
        val category: GoalCategory,
        val confidence: Double, // 置信度 0.0-1.0
        val matchedKeywords: List<String>, // 匹配的关键词
        val reason: String // 推荐理由
    )
    
    /**
     * 分析文本并推荐分类
     */
    fun classifyGoal(
        title: String,
        description: String = ""
    ): List<CategoryRecommendation> {
        val text = "$title $description".lowercase().trim()
        
        if (text.isBlank()) {
            return listOf(getDefaultRecommendation())
        }
        
        val recommendations = mutableListOf<CategoryRecommendation>()
        
        // 为每个分类计算匹配度
        categories.forEach { category ->
            val analysis = analyzeTextForCategory(text, category)
            if (analysis.confidence > 0.1) { // 只保留置信度大于10%的推荐
                recommendations.add(analysis)
            }
        }
        
        // 按置信度排序并返回前3个推荐
        return recommendations
            .sortedByDescending { it.confidence }
            .take(3)
            .ifEmpty { listOf(getDefaultRecommendation()) }
    }
    
    /**
     * 为特定分类分析文本匹配度
     */
    private fun analyzeTextForCategory(
        text: String,
        category: GoalCategory
    ): CategoryRecommendation {
        val keywords = getCategoryKeywords(category.id)
        val matchedKeywords = mutableListOf<String>()
        var totalScore = 0.0
        
        // 关键词匹配分析
        keywords.forEach { (keyword, weight) ->
            if (text.contains(keyword)) {
                matchedKeywords.add(keyword)
                totalScore += weight
            }
        }
        
        // 语义相关性分析
        val semanticScore = calculateSemanticScore(text, category)
        totalScore += semanticScore
        
        // 时间框架匹配分析
        val timeFrameScore = analyzeTimeFrame(text, category)
        totalScore += timeFrameScore
        
        // 计算最终置信度（使用sigmoid函数归一化）
        val confidence = sigmoid(totalScore)
        
        // 生成推荐理由
        val reason = generateReason(matchedKeywords, category, confidence)
        
        return CategoryRecommendation(
            category = category,
            confidence = confidence,
            matchedKeywords = matchedKeywords,
            reason = reason
        )
    }
    
    /**
     * 获取分类关键词及其权重
     */
    private fun getCategoryKeywords(categoryId: String): Map<String, Double> {
        return when (categoryId) {
            "health_fitness" -> mapOf(
                "健康" to 2.0, "健身" to 2.0, "运动" to 2.0, "减肥" to 2.0, "锻炼" to 2.0,
                "跑步" to 1.8, "游泳" to 1.8, "瑜伽" to 1.8, "举重" to 1.8, "有氧" to 1.8,
                "体重" to 1.5, "肌肉" to 1.5, "饮食" to 1.5, "营养" to 1.5, "睡眠" to 1.5,
                "步数" to 1.2, "卡路里" to 1.2, "体脂" to 1.2, "血压" to 1.2, "心率" to 1.2
            )
            
            "learning_growth" -> mapOf(
                "学习" to 2.0, "学会" to 2.0, "掌握" to 2.0, "技能" to 2.0, "知识" to 2.0,
                "课程" to 1.8, "培训" to 1.8, "考试" to 1.8, "证书" to 1.8, "学位" to 1.8,
                "阅读" to 1.5, "读书" to 1.5, "研究" to 1.5, "练习" to 1.5, "复习" to 1.5,
                "语言" to 1.2, "编程" to 1.2, "设计" to 1.2, "写作" to 1.2, "思维" to 1.2
            )
            
            "career_work" -> mapOf(
                "工作" to 2.0, "职业" to 2.0, "事业" to 2.0, "升职" to 2.0, "跳槽" to 2.0,
                "项目" to 1.8, "任务" to 1.8, "业绩" to 1.8, "绩效" to 1.8, "团队" to 1.8,
                "技能" to 1.5, "能力" to 1.5, "经验" to 1.5, "管理" to 1.5, "领导" to 1.5,
                "会议" to 1.2, "汇报" to 1.2, "沟通" to 1.2, "协作" to 1.2, "效率" to 1.2
            )
            
            "finance_money" -> mapOf(
                "理财" to 2.0, "投资" to 2.0, "储蓄" to 2.0, "赚钱" to 2.0, "收入" to 2.0,
                "基金" to 1.8, "股票" to 1.8, "债券" to 1.8, "保险" to 1.8, "贷款" to 1.8,
                "预算" to 1.5, "支出" to 1.5, "消费" to 1.5, "账单" to 1.5, "财务" to 1.5,
                "钱" to 1.2, "元" to 1.2, "万" to 1.2, "资产" to 1.2, "负债" to 1.2
            )
            
            "relationships" -> mapOf(
                "朋友" to 2.0, "家人" to 2.0, "恋人" to 2.0, "社交" to 2.0, "人际" to 2.0,
                "聚会" to 1.8, "约会" to 1.8, "交流" to 1.8, "沟通" to 1.8, "联系" to 1.8,
                "关系" to 1.5, "感情" to 1.5, "友谊" to 1.5, "爱情" to 1.5, "亲情" to 1.5,
                "陪伴" to 1.2, "理解" to 1.2, "支持" to 1.2, "信任" to 1.2, "包容" to 1.2
            )
            
            "hobbies_interests" -> mapOf(
                "兴趣" to 2.0, "爱好" to 2.0, "娱乐" to 2.0, "休闲" to 2.0, "放松" to 2.0,
                "音乐" to 1.8, "电影" to 1.8, "游戏" to 1.8, "绘画" to 1.8, "摄影" to 1.8,
                "旅行" to 1.5, "美食" to 1.5, "购物" to 1.5, "收藏" to 1.5, "手工" to 1.5,
                "乐器" to 1.2, "唱歌" to 1.2, "跳舞" to 1.2, "写作" to 1.2, "园艺" to 1.2
            )
            
            "family_home" -> mapOf(
                "家庭" to 2.0, "家人" to 2.0, "孩子" to 2.0, "父母" to 2.0, "家务" to 2.0,
                "教育" to 1.8, "陪伴" to 1.8, "照顾" to 1.8, "关爱" to 1.8, "责任" to 1.8,
                "房子" to 1.5, "装修" to 1.5, "清洁" to 1.5, "整理" to 1.5, "维护" to 1.5,
                "温馨" to 1.2, "和谐" to 1.2, "幸福" to 1.2, "安全" to 1.2, "舒适" to 1.2
            )
            
            "personal_development" -> mapOf(
                "个人" to 2.0, "自我" to 2.0, "成长" to 2.0, "提升" to 2.0, "改变" to 2.0,
                "习惯" to 1.8, "品格" to 1.8, "性格" to 1.8, "心理" to 1.8, "精神" to 1.8,
                "反思" to 1.5, "总结" to 1.5, "计划" to 1.5, "目标" to 1.5, "梦想" to 1.5,
                "自信" to 1.2, "勇气" to 1.2, "坚持" to 1.2, "毅力" to 1.2, "专注" to 1.2
            )
            
            // 时间维度分类
            "yearly_goals" -> mapOf(
                "年度" to 2.0, "今年" to 2.0, "全年" to 2.0, "长期" to 1.8, "重大" to 1.5
            )
            
            "quarterly_goals" -> mapOf(
                "季度" to 2.0, "三个月" to 2.0, "本季" to 2.0, "中期" to 1.5
            )
            
            "monthly_goals" -> mapOf(
                "月度" to 2.0, "本月" to 2.0, "这个月" to 2.0, "30天" to 1.5
            )
            
            "project50" -> mapOf(
                "50天" to 2.0, "挑战" to 1.8, "突破" to 1.8, "专注" to 1.5, "集中" to 1.5
            )
            
            "weekly_goals" -> mapOf(
                "周" to 2.0, "本周" to 2.0, "这周" to 2.0, "7天" to 1.5, "短期" to 1.2
            )
            
            else -> emptyMap()
        }
    }
    
    /**
     * 计算语义相关性得分
     */
    private fun calculateSemanticScore(text: String, category: GoalCategory): Double {
        // 基于分类描述的语义匹配
        val categoryDescription = category.description.lowercase()
        val commonWords = text.split(" ").intersect(categoryDescription.split(" "))
        return commonWords.size * 0.1
    }
    
    /**
     * 分析时间框架匹配度
     */
    private fun analyzeTimeFrame(text: String, category: GoalCategory): Double {
        val timeKeywords = mapOf(
            "今天" to 0.1, "明天" to 0.1, "本周" to 0.3, "这周" to 0.3,
            "本月" to 0.5, "这个月" to 0.5, "季度" to 0.7, "半年" to 0.8,
            "今年" to 1.0, "全年" to 1.0, "长期" to 1.0
        )
        
        var score = 0.0
        timeKeywords.forEach { (keyword, weight) ->
            if (text.contains(keyword)) {
                score += weight
            }
        }
        
        return score
    }
    
    /**
     * Sigmoid函数，将得分归一化到0-1区间
     */
    private fun sigmoid(x: Double): Double {
        return 1.0 / (1.0 + exp(-x))
    }
    
    /**
     * 生成推荐理由
     */
    private fun generateReason(
        matchedKeywords: List<String>,
        category: GoalCategory,
        confidence: Double
    ): String {
        return when {
            matchedKeywords.isNotEmpty() -> {
                "检测到关键词：${matchedKeywords.take(3).joinToString("、")}，与${category.name}高度相关"
            }
            confidence > 0.5 -> {
                "根据文本语义分析，与${category.name}较为匹配"
            }
            else -> {
                "可能与${category.name}相关"
            }
        }
    }
    
    /**
     * 获取默认推荐（当无法分析时）
     */
    private fun getDefaultRecommendation(): CategoryRecommendation {
        val defaultCategory = categories.first { it.id == "personal_development" }
        return CategoryRecommendation(
            category = defaultCategory,
            confidence = 0.5,
            matchedKeywords = emptyList(),
            reason = "默认分类，您可以根据需要调整"
        )
    }
    
    /**
     * 快速分类（只返回最佳推荐）
     */
    fun quickClassify(title: String, description: String = ""): GoalCategory {
        val recommendations = classifyGoal(title, description)
        return recommendations.firstOrNull()?.category
            ?: categories.first { it.id == "personal_development" }
    }

    /**
     * 获取分类的示例关键词（用于UI展示）
     */
    fun getCategoryExampleKeywords(categoryId: String): List<String> {
        return getCategoryKeywords(categoryId).keys.take(5).toList()
    }

    /**
     * 批量分类多个目标
     */
    fun batchClassify(goals: List<Pair<String, String>>): List<GoalCategory> {
        return goals.map { (title, description) ->
            quickClassify(title, description)
        }
    }

    /**
     * 获取分类统计信息
     */
    fun getClassificationStats(goals: List<Pair<String, String>>): Map<String, Int> {
        val classifications = batchClassify(goals)
        return classifications.groupBy { it.id }.mapValues { it.value.size }
    }

    /**
     * 验证分类准确性（用于调试和优化）
     */
    fun validateClassification(
        title: String,
        description: String,
        expectedCategoryId: String
    ): Boolean {
        val predicted = quickClassify(title, description)
        return predicted.id == expectedCategoryId
    }
}
