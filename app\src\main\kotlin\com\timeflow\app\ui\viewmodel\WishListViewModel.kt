package com.timeflow.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.*
import com.timeflow.app.data.repository.WishRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class WishListViewModel @Inject constructor(
    private val wishRepository: WishRepository,
    private val goalRepository: com.timeflow.app.data.repository.GoalRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(WishListUiState())
    val uiState: StateFlow<WishListUiState> = _uiState.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    private val _selectedCategory = MutableStateFlow<WishCategory?>(null)
    private val _selectedStatus = MutableStateFlow(WishStatus.ACTIVE)

    init {
        loadWishes()
    }

    fun loadWishes() {
        viewModelScope.launch {
            combine(
                if (searchQuery.value.isNotEmpty()) {
                    wishRepository.searchWishes(searchQuery.value)
                } else {
                    when (_selectedStatus.value) {
                        WishStatus.ARCHIVED -> wishRepository.getAllArchivedWishes()
                        else -> wishRepository.getAllActiveWishes()
                    }
                },
                wishRepository.getAllWishes(), // 🔧 新增：获取所有愿望用于统计
                _selectedCategory,
                _selectedStatus
            ) { wishes, allWishes, category, status ->
                var filteredWishes = wishes

                // 按类别过滤
                if (category != null) {
                    filteredWishes = filteredWishes.filter { it.category == category }
                }

                // 🔧 按状态过滤 - 特殊处理已归档状态
                if (status != WishStatus.ACTIVE) {
                    filteredWishes = when (status) {
                        WishStatus.ARCHIVED -> {
                            // 已归档筛选：显示所有已归档的愿望，无论其状态如何
                            filteredWishes.filter { it.isArchived }
                        }
                        else -> {
                            // 其他状态：按状态精确匹配
                            filteredWishes.filter { it.status == status }
                        }
                    }
                }

                WishListUiState(
                    wishes = filteredWishes,
                    allWishes = allWishes, // 🔧 新增：保存所有愿望用于统计
                    isLoading = false,
                    selectedCategory = category,
                    selectedStatus = status
                )
            }.catch { exception ->
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = exception.message ?: "加载愿望失败"
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }

    fun createWish(wish: WishModel) {
        viewModelScope.launch {
            try {
                val newWish = wish.copy(
                    id = UUID.randomUUID().toString(),
                    createdAt = java.time.LocalDateTime.now(),
                    updatedAt = java.time.LocalDateTime.now()
                )
                wishRepository.insertWish(newWish)
                _uiState.value = _uiState.value.copy(
                    showAddDialog = false,
                    successMessage = "愿望「${newWish.title}」已添加到愿望池"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "添加愿望失败: ${e.message}"
                )
            }
        }
    }



    fun deleteWish(wish: WishModel) {
        viewModelScope.launch {
            try {
                wishRepository.deleteWish(wish)
                _uiState.value = _uiState.value.copy(
                    successMessage = "愿望「${wish.title}」已删除"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "删除愿望失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 📦 归档愿望 - 温暖的告别与整理
     */
    fun archiveWish(wishId: String) {
        viewModelScope.launch {
            try {
                val wish = wishRepository.getWishById(wishId)
                wishRepository.archiveWish(wishId)
                
                val archiveMessage = when {
                    wish?.status == WishStatus.CONVERTED_TO_GOAL -> 
                        "🎯 愿望「${wish.title}」已转化为目标，在归档中继续见证成长"
                    else -> 
                        "📦 愿望「${wish?.title ?: ""}」已妥善归档，美好的憧憬永远值得珍藏"
                }
                
                _uiState.value = _uiState.value.copy(
                    successMessage = archiveMessage
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "归档遇到小问题: ${e.message}，愿望依然在这里等你"
                )
            }
        }
    }
    
    /**
     * 🔄 取消归档（从归档中恢复）
     */
    fun unarchiveWish(wishId: String) {
        viewModelScope.launch {
            try {
                val wish = wishRepository.getWishById(wishId)
                wishRepository.unarchiveWish(wishId)
                
                _uiState.value = _uiState.value.copy(
                    successMessage = "✨ 愿望「${wish?.title ?: ""}」重新回到愿望池，继续闪闪发光！"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "恢复遇到小问题: ${e.message}"
                )
            }
        }
    }



    fun markWishAsAchieved(wishId: String) {
        viewModelScope.launch {
            try {
                wishRepository.markWishAsAchieved(wishId)
                _uiState.value = _uiState.value.copy(
                    successMessage = "🎉 恭喜！愿望已实现"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "标记愿望失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 🔄 标记愿望为活跃状态
     */
    fun markWishAsActive(wishId: String) {
        viewModelScope.launch {
            try {
                val wish = wishRepository.getWishById(wishId)
                wish?.let { 
                    val updatedWish = it.copy(
                        status = WishStatus.ACTIVE,
                        updatedAt = java.time.LocalDateTime.now()
                    )
                    wishRepository.updateWish(updatedWish)
                    _uiState.value = _uiState.value.copy(
                        successMessage = "✨ 愿望「${it.title}」重新激活，继续追求梦想吧！"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "重新激活失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 🗑️ 删除愿望 - 温和的永久告别
     */
    fun deleteWish(wishId: String) {
        viewModelScope.launch {
            try {
                val wish = wishRepository.getWishById(wishId)
                wishRepository.deleteWishById(wishId)
                _uiState.value = _uiState.value.copy(
                    successMessage = "💔 愿望「${wish?.title ?: ""}」已删除，虽然告别但美好永远在心中"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "删除失败: ${e.message}"
                )
            }
        }
    }

    fun search(query: String) {
        _searchQuery.value = query
        loadWishes()
    }

    fun filterByCategory(category: WishCategory?) {
        _selectedCategory.value = category
        loadWishes()
    }

    fun filterByStatus(status: WishStatus) {
        _selectedStatus.value = status
        loadWishes()
    }

    fun showAddDialog() {
        _uiState.value = _uiState.value.copy(showAddDialog = true)
    }

    fun hideAddDialog() {
        _uiState.value = _uiState.value.copy(showAddDialog = false)
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearSuccessMessage() {
        _uiState.value = _uiState.value.copy(successMessage = null)
    }
    
    /**
     * 🎯 将愿望转化为目标 - 从憧憬到行动的温暖转化
     */
    fun convertWishToGoal(wishId: String) {
        viewModelScope.launch {
            try {
                val wish = wishRepository.getWishById(wishId)
                wish?.let { wishModel ->
                    // 🌟 创建带有情感的目标描述
                    val goalDescription = buildGoalDescription(wishModel)
                    val estimatedDays = estimateTimeToAchieve(wishModel)
                    
                    // 🎯 创建实际的Goal对象
                    val goalId = java.util.UUID.randomUUID().toString()
                    val newGoal = com.timeflow.app.data.model.Goal(
                        id = goalId,
                        title = wishModel.title,
                        description = goalDescription,
                        priority = mapWishPriorityToGoalPriority(wishModel.priority),
                        tags = wishModel.tags,
                        startDate = java.time.LocalDateTime.now(),
                        dueDate = java.time.LocalDateTime.now().plusDays(estimatedDays.toLong()),
                        createdAt = java.time.LocalDateTime.now(),
                        updatedAt = java.time.LocalDateTime.now(),
                        status = "进行中",
                        progress = 0f
                    )
                    
                    // 💾 保存目标到数据库
                    goalRepository.saveGoal(newGoal)
                    
                    // 🔄 更新愿望状态为"已转目标"
                    wishRepository.convertWishToGoal(wishId, goalId)
                    
                    // 💫 温暖的成功反馈
                    _uiState.value = _uiState.value.copy(
                        successMessage = "✨ 愿望「${wishModel.title}」已转化为目标！预计${estimatedDays}天可实现，一起加油吧~"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "转换遇到小问题: ${e.message}，请稍后再试"
                )
            }
        }
    }
    
    /**
     * 📝 构建充满情感的目标描述
     */
    private fun buildGoalDescription(wish: WishModel): String {
        val motivationText = if (wish.motivation.isNotBlank()) {
            "\n💝 初心：${wish.motivation}"
        } else ""
        
        val prerequisitesText = if (wish.prerequisites.isNotEmpty()) {
            "\n📋 准备事项：${wish.prerequisites.joinToString("、")}"
        } else ""
        
        return "${wish.description}$motivationText$prerequisitesText\n\n🌟 从愿望池转化而来，带着美好憧憬出发！"
    }
    
    /**
     * ⏰ 智能估算实现时间
     */
    private fun estimateTimeToAchieve(wish: WishModel): Int {
        return when (wish.difficulty) {
            WishDifficulty.EASY -> 30  // 简单愿望：1个月
            WishDifficulty.MEDIUM -> 90  // 中等愿望：3个月
            WishDifficulty.HARD -> 180  // 困难愿望：6个月
            WishDifficulty.EXTREME -> 365  // 极难愿望：1年
        }
    }
    
    /**
     * 🎯 愿望优先级映射到目标优先级
     */
    private fun mapWishPriorityToGoalPriority(wishPriority: Int): com.timeflow.app.data.model.GoalPriority {
        return when (wishPriority) {
            1 -> com.timeflow.app.data.model.GoalPriority.LOW
            2 -> com.timeflow.app.data.model.GoalPriority.MEDIUM
            3 -> com.timeflow.app.data.model.GoalPriority.MEDIUM
            4 -> com.timeflow.app.data.model.GoalPriority.HIGH
            5 -> com.timeflow.app.data.model.GoalPriority.URGENT
            else -> com.timeflow.app.data.model.GoalPriority.MEDIUM
        }
    }
    
    // 当前编辑的愿望
    private val _editingWish = MutableStateFlow<WishModel?>(null)
    val editingWish: StateFlow<WishModel?> = _editingWish.asStateFlow()
    
    /**
     * ✏️ 编辑愿望 - 让憧憬保持鲜活
     */
    fun editWish(wish: WishModel) {
        _editingWish.value = wish
        _uiState.value = _uiState.value.copy(
            showAddDialog = true
        )
    }
    
    /**
     * 💾 更新愿望
     */
    fun updateWish(updatedWish: WishModel) {
        viewModelScope.launch {
            try {
                wishRepository.updateWish(updatedWish.copy(
                    updatedAt = java.time.LocalDateTime.now()
                ))
                
                _editingWish.value = null
                _uiState.value = _uiState.value.copy(
                    showAddDialog = false,
                    successMessage = "💫 愿望「${updatedWish.title}」已更新，憧憬更清晰了～"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "更新遇到小问题: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 取消编辑
     */
    fun cancelEdit() {
        _editingWish.value = null
        _uiState.value = _uiState.value.copy(showAddDialog = false)
    }
    
    /**
     * 🌟 分享愿望 - 传递美好憧憬
     */
    fun shareWish(wish: WishModel) {
        viewModelScope.launch {
            try {
                // 🎨 生成优美的分享文案
                val shareText = generateShareText(wish)
                
                // TODO: 集成系统分享功能 (Intent.ACTION_SEND)
                // TODO: 可以考虑生成愿望卡片图片分享
                
                _uiState.value = _uiState.value.copy(
                    successMessage = "✨ 愿望「${wish.title}」的美好分享文案已准备好，快去传递正能量吧～"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "分享遇到小问题: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 📝 生成温暖的分享文案
     */
    private fun generateShareText(wish: WishModel): String {
        val categoryEmoji = wish.category.emoji
        val priorityStars = "⭐".repeat(wish.priority)
        
        val shareContent = buildString {
            appendLine("🌟 我的小愿望 🌟")
            appendLine()
            appendLine("$categoryEmoji ${wish.title}")
            appendLine()
            if (wish.description.isNotBlank()) {
                appendLine("💭 ${wish.description}")
                appendLine()
            }
            appendLine("✨ 期待值：$priorityStars")
            if (wish.motivation.isNotBlank()) {
                appendLine("💝 内心声音：${wish.motivation}")
            }
            if (wish.tags.isNotEmpty()) {
                appendLine("🏷️ ${wish.tags.joinToString(" #", "#")}")
            }
            appendLine()
            appendLine("记录生活中的美好憧憬，让愿望成为前进的光✨")
            appendLine("——来自TimeFlow愿望池")
        }
        
        return shareContent
    }
}

data class WishListUiState(
    val wishes: List<WishModel> = emptyList(),
    val allWishes: List<WishModel> = emptyList(), // 🔧 新增：用于统计的所有愿望数据
    val isLoading: Boolean = true,
    val error: String? = null,
    val successMessage: String? = null,
    val showAddDialog: Boolean = false,
    val selectedCategory: WishCategory? = null,
    val selectedStatus: WishStatus = WishStatus.ACTIVE
)