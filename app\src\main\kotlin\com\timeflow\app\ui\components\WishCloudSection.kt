package com.timeflow.app.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin

// 愿望星云主题配色
private val WishCloudPrimary = Color(0xFFFFD93D) // 金黄色
private val WishCloudSecondary = Color(0xFFFFF3C4) // 浅金色  
private val WishCloudAccent = Color(0xFFFF6B9D) // 粉色
private val WishCloudBackground = Color(0xFFFFFBE6) // 极浅金色
private val WishCloudText = Color(0xFF2D1B2E) // 深色文字

/**
 * ✨ 愿望星云板块
 * 在主页展示愿望池概览，作为四级入口体系的第二级
 */
@Composable
fun WishCloudSection(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    val wishListViewModel: com.timeflow.app.ui.viewmodel.WishListViewModel = hiltViewModel()
    val uiState by wishListViewModel.uiState.collectAsStateWithLifecycle()
    
    // 动画状态
    var isVisible by remember { mutableStateOf(false) }
    val contentAlpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(800, easing = EaseOutCubic),
        label = "content_alpha"
    )
    
    LaunchedEffect(Unit) {
        delay(300)
        isVisible = true
    }
    
    // 获取前3个活跃愿望用于预览
    val topWishes = remember(uiState.wishes) {
        uiState.wishes
            .filter { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE }
            .take(3)
    }
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .alpha(contentAlpha)
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = WishCloudPrimary.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = Color.White
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 📱 头部标题栏
            WishCloudHeader(
                totalWishes = uiState.wishes.size,
                activeWishes = uiState.wishes.count { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE },
                onViewAllClick = { navController.navigate("wish_list") },
                onAddWishClick = { navController.navigate("wish_list") }
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 🌟 愿望预览区域
            if (topWishes.isNotEmpty()) {
                WishPreviewRow(
                    wishes = topWishes,
                    onWishClick = { wish ->
                        // 导航到愿望详情或直接打开愿望池
                        navController.navigate("wish_list")
                    }
                )
            } else {
                // 空状态引导
                WishCloudEmptyState(
                    onAddWishClick = { navController.navigate("wish_list") }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 📊 快速统计信息
            WishCloudStats(
                totalWishes = uiState.wishes.size,
                achievedWishes = uiState.wishes.count { it.status == com.timeflow.app.data.model.WishStatus.ACHIEVED },
                categories = uiState.wishes.map { it.category }.distinct().size
            )
        }
    }
}

/**
 * 📱 愿望星云头部
 */
@Composable
private fun WishCloudHeader(
    totalWishes: Int,
    activeWishes: Int,
    onViewAllClick: () -> Unit,
    onAddWishClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 标题区域
        Column {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 闪烁星星动画
                val infiniteTransition = rememberInfiniteTransition(label = "star_twinkle")
                val starRotation by infiniteTransition.animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(8000, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    ),
                    label = "star_rotation"
                )
                
                Icon(
                    imageVector = Icons.Default.AutoAwesome,
                    contentDescription = null,
                    tint = WishCloudPrimary,
                    modifier = Modifier
                        .size(18.dp)
                        .graphicsLayer(rotationZ = starRotation)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                Text(
                    text = "愿望星云",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = WishCloudText
                )
            }
            
            Text(
                text = if (activeWishes > 0) "$activeWishes 个愿望闪闪发光" else "准备记录第一个愿望",
                fontSize = 11.sp,
                color = Color.Gray,
                modifier = Modifier.padding(start = 24.dp)
            )
        }
        
        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // 添加愿望按钮
            Surface(
                modifier = Modifier
                    .size(32.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .clickable { onAddWishClick() },
                color = WishCloudPrimary.copy(alpha = 0.15f),
                shape = RoundedCornerShape(16.dp)
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加愿望",
                        tint = WishCloudPrimary,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            // 查看全部按钮
            Surface(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .clickable { onViewAllClick() },
                color = Color.Gray.copy(alpha = 0.1f),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "查看全部",
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Gray
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = null,
                        tint = Color.Gray,
                        modifier = Modifier.size(11.dp)
                    )
                }
            }
        }
    }
}

/**
 * 🌟 愿望预览行
 */
@Composable
private fun WishPreviewRow(
    wishes: List<com.timeflow.app.data.model.WishModel>,
    onWishClick: (com.timeflow.app.data.model.WishModel) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),  // 稍微减少间距适应更宽的卡片
        contentPadding = PaddingValues(horizontal = 2.dp)
    ) {
        items(wishes) { wish ->
            WishPreviewCard(
                wish = wish,
                onClick = { onWishClick(wish) }
            )
        }
    }
}

/**
 * 💫 单个愿望预览卡片
 */
@Composable
private fun WishPreviewCard(
    wish: com.timeflow.app.data.model.WishModel,
    onClick: () -> Unit
) {
    val categoryColor = getCategoryColor(wish.category)
    
    Surface(
        modifier = Modifier
            .width(160.dp)  // 大幅增加宽度确保"去北欧做访问学者"能完整显示
            .height(90.dp)  // 减少高度保持紧凑布局
            .clip(RoundedCornerShape(10.dp))
            .clickable { onClick() },
        color = categoryColor.copy(alpha = 0.1f),
        shape = RoundedCornerShape(10.dp),
        border = BorderStroke(1.dp, categoryColor.copy(alpha = 0.3f))
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(4.dp)  // 最小边距确保最大内容空间
        ) {
            // 类别图标和优先级 - 顶部
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopStart),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = wish.category.emoji,
                    fontSize = 14.sp  // 进一步减小emoji尺寸
                )
                
                // 优先级星星（颜色表示状态：活跃-金色，已实现-绿色等）
                Row(
                    horizontalArrangement = Arrangement.spacedBy(1.dp)
                ) {
                    val starColor = when (wish.status) {
                        com.timeflow.app.data.model.WishStatus.ACTIVE -> WishCloudPrimary
                        com.timeflow.app.data.model.WishStatus.ACHIEVED -> Color(0xFF4CAF50)
                        com.timeflow.app.data.model.WishStatus.CONVERTED_TO_GOAL -> Color(0xFF9C27B0)  // 紫色表示已转目标
                        com.timeflow.app.data.model.WishStatus.ARCHIVED -> Color(0xFF757575)  // 灰色表示已归档
                    }
                    
                    repeat(kotlin.math.min(wish.priority, 3)) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = starColor,  // 根据状态显示不同颜色
                            modifier = Modifier.size(8.dp)  // 稍微增大一点，作为状态指示
                        )
                    }
                }
            }
            
            // 愿望标题 - 使用BasicText确保单行显示，完全移除布局约束
            androidx.compose.foundation.text.BasicText(
                text = wish.title,
                style = androidx.compose.ui.text.TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = categoryColor.copy(alpha = 0.95f),
                    textAlign = TextAlign.Center,
                    letterSpacing = 0.sp,
                    lineHeight = 11.sp,
                    platformStyle = androidx.compose.ui.text.PlatformTextStyle(
                        includeFontPadding = false
                    )
                ),
                maxLines = 1,  // 严格单行限制
                overflow = TextOverflow.Ellipsis,
                softWrap = false,  // 绝对不换行
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth()
                    .wrapContentHeight()
            )
        }
    }
}

/**
 * 🎭 愿望星云空状态
 */
@Composable
private fun WishCloudEmptyState(
    onAddWishClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp)
    ) {
        // 动画星星
        val infiniteTransition = rememberInfiniteTransition(label = "empty_star_animation")
        val starScale by infiniteTransition.animateFloat(
            initialValue = 1f,
            targetValue = 1.15f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = EaseInOutSine),
                repeatMode = RepeatMode.Reverse
            ),
            label = "star_scale"
        )
        
        Icon(
            imageVector = Icons.Default.AutoAwesome,
            contentDescription = null,
            tint = WishCloudPrimary.copy(alpha = 0.7f),
            modifier = Modifier
                .size(28.dp)
                .graphicsLayer(scaleX = starScale, scaleY = starScale)
        )
        
        Spacer(modifier = Modifier.height(10.dp))
        
        Text(
            text = "还没有愿望",
            fontSize = 13.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = "记录第一个心愿，让梦想闪闪发光",
            fontSize = 11.sp,
            color = Color.Gray.copy(alpha = 0.8f),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 3.dp)
        )
        
        Spacer(modifier = Modifier.height(14.dp))
        
        Button(
            onClick = onAddWishClick,
            colors = ButtonDefaults.buttonColors(
                containerColor = WishCloudPrimary.copy(alpha = 0.2f),
                contentColor = WishCloudPrimary
            ),
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.height(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(14.dp)
            )
            Spacer(modifier = Modifier.width(5.dp))
            Text(
                text = "添加愿望",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold
            )
        }
    }
}

/**
 * 📊 愿望星云统计
 */
@Composable
private fun WishCloudStats(
    totalWishes: Int,
    achievedWishes: Int,
    categories: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        StatIndicator(
            value = totalWishes.toString(),
            label = "愿望",
            icon = Icons.Default.Star,
            color = WishCloudPrimary
        )
        
        StatIndicator(
            value = achievedWishes.toString(),
            label = "实现",
            icon = Icons.Default.EmojiEvents,
            color = Color(0xFF4CAF50)
        )
        
        StatIndicator(
            value = categories.toString(),
            label = "类别",
            icon = Icons.Default.Category,
            color = WishCloudAccent
        )
    }
}

/**
 * 📈 统计指示器
 */
@Composable
private fun StatIndicator(
    value: String,
    label: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(11.dp)
            )
            Spacer(modifier = Modifier.width(3.dp))
            Text(
                text = value,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
        Text(
            text = label,
            fontSize = 9.sp,
            color = Color.Gray,
            modifier = Modifier.padding(top = 1.dp)
        )
    }
}

// 🎨 辅助函数
private fun getCategoryColor(category: com.timeflow.app.data.model.WishCategory): Color {
    return when (category) {
        com.timeflow.app.data.model.WishCategory.TRAVEL -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.SHOPPING -> Color(0xFFFF9800)
        com.timeflow.app.data.model.WishCategory.LEARNING -> Color(0xFF2196F3)
        com.timeflow.app.data.model.WishCategory.CAREER -> Color(0xFF9C27B0)
        com.timeflow.app.data.model.WishCategory.LIFESTYLE -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.HEALTH -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.HOBBY -> Color(0xFFFF5722)
        com.timeflow.app.data.model.WishCategory.RELATIONSHIP -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.OTHER -> Color(0xFF607D8B)
    }
} 