package com.timeflow.app.ui.timetracking.screens

import android.app.Activity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.WindowInsets
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.timetracking.*
import com.timeflow.app.ui.timetracking.components.TaskSelectorDialog
import com.timeflow.app.ui.viewmodel.GlobalTimerViewModel
import android.widget.Toast
import android.util.Log

/**
 * 时间追踪页面
 * 参考网易云音乐播放页面的设计，提供直观的时间计时体验
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeTrackingScreen(
    navController: NavController,
    viewModel: TimeTrackingViewModel = hiltViewModel(),
    taskListViewModel: TaskListViewModel = hiltViewModel(),
    globalTimerViewModel: GlobalTimerViewModel = hiltViewModel(), // 🔧 新增：获取全局计时器ViewModel
    taskId: String? = null, // 添加任务ID参数
    autoStart: Boolean = false, // 添加自动开始参数
    // 添加计时状态变化回调
    onTimerStateChanged: ((TimerState, String, Long) -> Unit)? = null
) {
    // 状态
    val timerState by viewModel.timerState.collectAsState()
    val currentTask by viewModel.currentTask.collectAsState()
    val elapsedTime by viewModel.elapsedTime.collectAsState()
    val focusModeEnabled by viewModel.focusModeEnabled.collectAsState()
    val showTaskSelector by viewModel.showTaskSelector.collectAsState()
    val timerType by viewModel.timerType.collectAsState()
    val pomodoroCount by viewModel.pomodoroCount.collectAsState()
    val pomodoroGoal by viewModel.pomodoroGoal.collectAsState()
    val pomodoroTimeRemaining by viewModel.pomodoroTimeRemaining.collectAsState()
    val isBreakTime by viewModel.isBreakTime.collectAsState()
    
    // 通知全局计时器状态变化
    LaunchedEffect(timerState, currentTask, elapsedTime) {
        onTimerStateChanged?.invoke(
            timerState,
            currentTask?.name ?: "未命名任务",
            elapsedTime
        )
    }
    
    // 🔧 新增：监听页面重新进入（从小组件返回）
    LaunchedEffect(Unit) {
        Log.d("TimeTrackingScreen", "📱 时间追踪页面重新进入，刷新状态...")
        
        // 强制刷新ViewModel状态，确保与持久化存储同步
        viewModel.refreshTimerState()
        
        // 延迟后再次刷新，确保状态完全同步
        kotlinx.coroutines.delay(200)
        viewModel.refreshTimerState()
        
        Log.d("TimeTrackingScreen", "✅ 状态刷新完成")
    }
    
    // 🔧 新增：页面离开时重新显示小组件
    DisposableEffect(Unit) {
        onDispose {
            // 当离开时间追踪页面时，重新显示小组件（如果有活跃计时）
            Log.d("TimeTrackingScreen", "📱 离开时间追踪页面，重新显示小组件...")
            globalTimerViewModel.showMiniTimer()
        }
    }
    
    // 监听任务列表变化，确保数据是最新的
    LaunchedEffect(taskListViewModel.taskListState.value.tasks.size) {
        // 当任务数量发生变化时，重新加载时间追踪的任务列表
        if (taskListViewModel.taskListState.value.tasks.isNotEmpty()) {
            viewModel.loadTasks(taskListViewModel)
        }
    }
    
    // 🔥 优化：监听当前任务状态变化 - 只在有当前任务且计时器运行时启动监听
    LaunchedEffect(currentTask?.id, timerState) {
        val task = currentTask // 🔧 使用局部变量避免智能转换问题
        if (task != null && (timerState == TimerState.RUNNING || timerState == TimerState.PAUSED)) {
            Log.d("TimeTrackingScreen", "🔍 启动任务状态监听: ${task.name}")
            viewModel.monitorCurrentTaskStatus(taskListViewModel)
        }
    }
    
    // 颜色设置 - 更柔和的配色方案
    val darkBackground = Color(0xFF050505)
    val primaryColor = Color(0xFFCCAEC5) // 新的主色调
    val secondaryColor = Color(0xFFD9C2D4) // 新的辅助色调
    val accentColor = Color(0xFFFF8FB3)
    val neutralColor = Color(0xFFB9A0FF)
    
    // 渐变主题
    val primaryGradient = Brush.linearGradient(
        colors = listOf(primaryColor, secondaryColor)
    )
    
    // 背景渐变 - 改为浅色背景，类似网易云音乐的CD盒效果
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFF2EBF0),  // 更新为淡紫粉色 #f2ebf0
            Color(0xFFD4BBC6)   // 浅玫瑰色 #d4bbc6
        )
    )

    // 获取上下文和Activity引用
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.setupTimeTrackingSystemBars(it)
        }
    }
    
    // 计算进度
    val progress = if (timerType == TimerType.POMODORO) {
        if (isBreakTime) {
            // 在休息阶段，计算休息时间的进度
            val totalBreakTime = if (pomodoroCount % 4 == 0) 15 * 60f else 5 * 60f
            (totalBreakTime - pomodoroTimeRemaining) / totalBreakTime
        } else {
            // 在工作阶段，计算工作时间的进度
            (25 * 60f - pomodoroTimeRemaining) / (25 * 60f)
        }
    } else {
        // 普通计时没有进度
        0f
    }
    
    // 动画效果
    val playButtonScale = animateFloatAsState(
        targetValue = if (timerState == TimerState.RUNNING) 0.9f else 1f,
        label = "playButtonScale"
    )
    
    // 加载任务数据
    LaunchedEffect(Unit) {
        viewModel.loadTasks(taskListViewModel)
    }
    
    // 🔥 新增：当当前任务被删除或完成时的处理
    LaunchedEffect(currentTask, timerState) {
        if (currentTask == null && (timerState == TimerState.RUNNING || timerState == TimerState.PAUSED)) {
            // 当前任务已被清除但计时器仍在运行，显示提示
            Toast.makeText(
                context,
                "当前任务已删除或完成，计时已自动停止 ⏹️",
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    // 处理来自任务列表的自动计时请求
    LaunchedEffect(taskId, autoStart) {
        if (taskId != null && autoStart) {
            // 设置当前任务
            viewModel.setCurrentTask(taskId)
        }
    }
    
    // 监听当前任务变化，当任务设置完成且需要自动开始时，启动计时器
    LaunchedEffect(currentTask, taskId, autoStart) {
        if (taskId != null && autoStart && currentTask?.id == taskId && timerState == TimerState.IDLE) {
            // 确保当前任务已正确设置且计时器还未启动
            kotlinx.coroutines.delay(100) // 短暂延迟确保UI已更新
            viewModel.startTimer()
        }
    }
    
    // 更新计时器控制方法，确保调用onTimerStateChanged
    val handleStartTimer = {
        viewModel.startTimer()
    }
    
    val handlePauseTimer = {
        viewModel.pauseTimer()
    }
    
    val handleStopTimer = {
        viewModel.stopTimer()
        
        // 获取当前任务信息和计时时长，用于用户反馈
        val taskName = currentTask?.name ?: "任务"
        val elapsedMinutes = (elapsedTime / 60).toInt()
        val elapsedSeconds = (elapsedTime % 60).toInt()
        
        // 显示停止计时的提示
        if (elapsedTime > 0) {
            val timeText = if (elapsedMinutes > 0) {
                "${elapsedMinutes}分${elapsedSeconds}秒"
            } else {
                "${elapsedSeconds}秒"
            }
            Toast.makeText(
                context, 
                "「${taskName}」计时已停止，用时 ${timeText}。数据已保存到统计页面 📊", 
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    // 获取任务列表
    val tasksList by viewModel.taskList.collectAsState()
    val isLoadingTasks by viewModel.isLoadingTasks.collectAsState()

    // 主界面结构
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
    ) {
        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 24.dp) // 增加整体顶部间距
        ) {
            // 顶部导航栏 - 更加精简
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 16.dp)
                    .padding(top = 12.dp), // 增加更多顶部内边距
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 返回按钮
                ControlButton(
                    onClick = { navController.popBackStack() },
                    color = Color.Transparent
                ) {
                        Icon(
                        imageVector = Icons.Filled.ArrowBack, 
                        contentDescription = "返回",
                        tint = Color.Black.copy(alpha = 0.8f)
                    )
                }
                
                // 页面标题
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(bottom = 4.dp)
                ) {
                    Spacer(modifier = Modifier.height(10.dp)) // 增加顶部间距
                    Text(
                        text = "时间旋律", 
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                        ),
                        color = Color.Black.copy(alpha = 0.85f),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // 专注模式按钮
                ControlButton(
                    onClick = { viewModel.toggleFocusMode() },
                    color = Color.Transparent
                ) {
                        Icon(
                        imageVector = if (focusModeEnabled) 
                            Icons.Filled.Visibility 
                        else 
                            Icons.Filled.VisibilityOff,
                        contentDescription = if (focusModeEnabled) 
                            "退出专注模式" 
                        else 
                            "进入专注模式",
                        tint = Color.Black.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                // 使用唱片式计时器组件
                VinylStyleTimer(
                    elapsedTime = elapsedTime,
                    timerState = timerState,
                    primaryColor = currentTask?.color ?: primaryColor,
                    progress = progress,
                    showTime = timerType == TimerType.POMODORO,
                    timeText = if (timerType == TimerType.POMODORO) 
                        formatPomodoroTime(pomodoroTimeRemaining) 
                    else 
                        ""
                )
            }
            
            // 任务信息区域（移到唱片下方）
            if (currentTask != null) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 当前任务名称
                    Text(
                        text = currentTask!!.name,
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                        ),
                        color = Color.Black.copy(alpha = 0.8f),
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 任务描述或番茄钟状态
                    if (timerType == TimerType.POMODORO) {
                        Text(
                            text = if (isBreakTime) "休息时间" else "第${pomodoroCount + 1}个番茄",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                            ),
                            color = Color.Black.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center,
                        )
                    } else if (currentTask!!.description.isNotEmpty()) {
                        // 🔧 新增：清理描述中的JSON颜色信息
                        val cleanDescription = remember(currentTask!!.description) {
                            val colorPattern = """\{"color":(\d+)\}""".toRegex()
                            currentTask!!.description.replace(colorPattern, "").trim()
                        }
                        
                        if (cleanDescription.isNotEmpty()) {
                            Text(
                                text = cleanDescription,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                                ),
                                color = Color.Black.copy(alpha = 0.7f),
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                    
                    // 显示子任务
                    val subTasks by viewModel.currentSubTasks.collectAsState()
                    if (subTasks.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 子任务列表显示 - 分行显示
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            val displaySubTasks = subTasks.take(3)
                            val hasMoreTasks = subTasks.size > 3
                            
                            displaySubTasks.forEach { task ->
                                Text(
                                    text = "-${task.title}",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                                    ),
                                    color = Color.Black.copy(alpha = 0.6f),
                                    textAlign = TextAlign.Center,
                                )
                            }
                            
                            if (hasMoreTasks) {
                                Text(
                                    text = "...",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                                    ),
                                    color = Color.Black.copy(alpha = 0.6f),
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }
                    }
                }
            }
            
            // 底部控制区 - 类似网易云音乐样式
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 80.dp) // 从40.dp增加到80.dp，使内容更向上移动
            ) {
                // 进度条 - 模仿网易云音乐
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 当前时间
                    Text(
                        text = formatElapsedTime(elapsedTime).substring(3), // 只显示分:秒
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Black.copy(alpha = 0.6f)
                    )
                    
                    // 进度条
                    Slider(
                        value = 0f, // 只是展示，不可拖动
                        onValueChange = { },
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 8.dp),
                        enabled = false,
                        colors = SliderDefaults.colors(
                            disabledThumbColor = primaryColor,
                            disabledActiveTrackColor = primaryColor,
                            disabledInactiveTrackColor = Color.Gray.copy(alpha = 0.3f)
                        )
                    )
                    
                    // 总时间
                    Text(
                        text = if (timerType == TimerType.POMODORO) "25:00" else "--:--",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Black.copy(alpha = 0.6f)
                    )
                }
                
                // 控制按钮 - 网易云音乐风格
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 随机播放/循环按钮位置替换为任务切换按钮
                    ControlButton(
                        onClick = { viewModel.toggleTaskSelector() },
                        color = Color.Transparent
                    ) {
                        Icon(
                            imageVector = Icons.Filled.List,
                            contentDescription = "切换任务",
                            tint = Color.Black.copy(alpha = 0.7f),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    
                    // 上一个按钮
                    ControlButton(
                        onClick = { 
                            if (timerType == TimerType.POMODORO) {
                                viewModel.skipCurrentPomodoro()
                            }
                        },
                        color = Color.Transparent
                    ) {
                        Icon(
                            imageVector = Icons.Filled.SkipPrevious,
                            contentDescription = "跳过",
                            tint = Color.Black.copy(alpha = 0.7f),
                            modifier = Modifier.size(28.dp)
                        )
                    }
                    
                    // 开始/暂停按钮 - 更精致的动画效果
                    Box(
                        modifier = Modifier
                            .size(64.dp)
                            .clip(CircleShape)
                            .background(
                                // 修改为纯色背景
                                color = Color(0xFFE0CFD8) // 浅粉紫色纯色背景
                            )
                            .clickable {
                                when (timerState) {
                                    TimerState.IDLE, TimerState.PAUSED -> handleStartTimer()
                                    TimerState.RUNNING -> handlePauseTimer()
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        // 播放/暂停图标 - 使用自定义绘制的圆润三角形
                        if (timerState == TimerState.RUNNING) {
                            // 暂停图标
                            Icon(
                                imageVector = Icons.Filled.Pause,
                                contentDescription = "暂停",
                                tint = Color.White,
                                modifier = Modifier.size(34.dp) // 增大暂停图标尺寸
                            )
                        } else {
                            // 播放图标 - 进一步增大尺寸
                            Icon(
                                imageVector = Icons.Filled.PlayArrow,
                                contentDescription = "开始",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(40.dp) // 从36dp增大到40dp
                                    .padding(start = 4.dp) // 保持微调位置使图标居中
                            )
                        }
                    }
                    
                    // 下一个按钮
                    ControlButton(
                        onClick = { /* 暂无操作 */ },
                        color = Color.Transparent
                    ) {
                        Icon(
                            imageVector = Icons.Filled.SkipNext,
                            contentDescription = "下一个",
                            tint = Color.Black.copy(alpha = 0.7f),
                            modifier = Modifier.size(28.dp)
                        )
                    }
                    
                    // 更多按钮 (替代原播放列表按钮)
                    ControlButton(
                        onClick = { 
                            if (timerState == TimerState.IDLE) {
                                viewModel.toggleTimerType()
                            } else {
                                handleStopTimer()
                            } 
                        },
                        color = Color.Transparent
                    ) {
                        Icon(
                            imageVector = if (timerState == TimerState.IDLE) 
                                if (timerType == TimerType.POMODORO) 
                                    Icons.Filled.Timer 
                                else 
                                    Icons.Filled.AvTimer
                            else 
                                Icons.Filled.Stop,
                            contentDescription = if (timerState == TimerState.IDLE) 
                                "切换计时模式" 
                            else 
                                "停止",
                            tint = Color.Black.copy(alpha = 0.7f),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
                
                // 今日任务简要信息
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 添加白色半透明圆角卡片背景 - 显示真实的今日专注时长
                    Surface(
                        modifier = Modifier,
                        shape = RoundedCornerShape(20.dp),
                        color = Color.White.copy(alpha = 0.5f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Favorite,
                                contentDescription = null,
                                tint = primaryColor,
                                modifier = Modifier.size(16.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(4.dp))
                            
                            // 显示今日实际专注时长
                            val todayTotalTime by viewModel.getTodayTotalTime().collectAsState(initial = 0L)
                            
                            Text(
                                text = "今日专注: ${formatTotalTime(todayTotalTime)}",
                                style = MaterialTheme.typography.labelSmall.copy(
                                    fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                                ),
                                color = Color.Black.copy(alpha = 0.6f)
                            )
                        }
                    }
                    
                    // 用同样的样式包装"查看统计"文本
                    Surface(
                        modifier = Modifier.clickable { 
                            navController.navigate(com.timeflow.app.ui.navigation.AppDestinations.TIME_STATISTICS_ROUTE)
                        },
                        shape = RoundedCornerShape(20.dp),
                        color = Color.White.copy(alpha = 0.5f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Text(
                                text = "查看统计 >",
                                style = MaterialTheme.typography.labelSmall.copy(
                                    fontFeatureSettings = "liga 1" // 启用连字特性，更好地支持emoji
                                ),
                                color = primaryColor
                            )
                        }
                    }
                }
            }
        }
        
        // 任务选择器弹窗 - 使用组件化的TaskSelectorDialog
        if (showTaskSelector) {
            TaskSelectorDialog(
                onDismiss = { viewModel.toggleTaskSelector() },
                onSelectTask = { task ->
                    // 设置任务并自动开始计时
                    viewModel.setTaskAndStartTimer(task)
                    viewModel.toggleTaskSelector()
                },
                currentTaskId = currentTask?.id,
                taskListViewModel = taskListViewModel
            )
        }
    }
}

/**
 * 格式化总时长（秒）为可读格式
 * @param totalSeconds 总秒数
 * @return 格式化的时间字符串（如：2小时30分钟）
 */
private fun formatTotalTime(totalSeconds: Long): String {
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    
    return when {
        hours > 0 && minutes > 0 -> "${hours}小时${minutes}分钟"
        hours > 0 -> "${hours}小时"
        minutes > 0 -> "${minutes}分钟"
        else -> "0分钟"
    }
}