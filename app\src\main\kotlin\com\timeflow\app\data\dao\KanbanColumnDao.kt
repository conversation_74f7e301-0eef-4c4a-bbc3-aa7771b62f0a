package com.timeflow.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.timeflow.app.data.entity.KanbanColumn
import kotlinx.coroutines.flow.Flow

/**
 * 看板列DAO接口
 */
@Dao
interface KanbanColumnDao {
    
    /**
     * 插入新看板列
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(column: KanbanColumn)
    
    /**
     * 插入多个看板列
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(columns: List<KanbanColumn>)
    
    /**
     * 更新看板列
     */
    @Update
    suspend fun update(column: KanbanColumn)
    
    /**
     * 删除看板列
     */
    @Delete
    suspend fun delete(column: KanbanColumn)
    
    /**
     * 根据看板ID获取所有列
     */
    @Query("SELECT * FROM kanban_columns WHERE board_id = :boardId ORDER BY position")
    suspend fun getColumnsByBoardId(boardId: String): List<KanbanColumn>
    
    /**
     * 观察指定看板ID的所有列变化
     */
    @Query("SELECT * FROM kanban_columns WHERE board_id = :boardId ORDER BY position")
    fun observeColumnsByBoardId(boardId: String): Flow<List<KanbanColumn>>
    
    /**
     * 根据ID获取列
     */
    @Query("SELECT * FROM kanban_columns WHERE id = :columnId")
    suspend fun getColumnById(columnId: String): KanbanColumn?
    
    /**
     * 观察指定ID的列变化
     */
    @Query("SELECT * FROM kanban_columns WHERE id = :columnId")
    fun observeColumnById(columnId: String): Flow<KanbanColumn?>
    
    /**
     * 获取指定看板的最大位置值
     */
    @Query("SELECT MAX(position) FROM kanban_columns WHERE board_id = :boardId")
    suspend fun getMaxPositionForBoard(boardId: String): Int?
    
    /**
     * 获取所有看板列的数量
     */
    @Query("SELECT COUNT(*) FROM kanban_columns")
    suspend fun getColumnCount(): Int
    
    /**
     * 获取所有看板列
     */
    @Query("SELECT * FROM kanban_columns ORDER BY position")
    suspend fun getAllColumns(): List<KanbanColumn>
} 