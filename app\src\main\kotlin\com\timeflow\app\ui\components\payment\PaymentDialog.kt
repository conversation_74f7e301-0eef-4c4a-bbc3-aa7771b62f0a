package com.timeflow.app.ui.components.payment

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.timeflow.app.BuildConfig
import com.timeflow.app.data.model.*
import com.timeflow.app.ui.screen.settings.MembershipType

/**
 * 支付对话框
 */
@Composable
fun PaymentDialog(
    isVisible: Boolean,
    membershipPlan: MembershipPlan,
    onPaymentMethodSelected: (PaymentMethod) -> Unit,
    onDismiss: () -> Unit,
    paymentState: PaymentResult? = null
) {
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = paymentState?.status != PaymentStatus.PROCESSING,
                dismissOnClickOutside = paymentState?.status != PaymentStatus.PROCESSING
            )
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                when (paymentState?.status) {
                    PaymentStatus.PROCESSING -> PaymentProcessingView(paymentState)
                    PaymentStatus.SUCCESS -> PaymentSuccessView(
                        paymentResult = paymentState,
                        onConfirm = onDismiss
                    )
                    PaymentStatus.FAILED, PaymentStatus.CANCELLED -> PaymentErrorView(
                        paymentResult = paymentState,
                        onRetry = { onPaymentMethodSelected(paymentState.paymentMethod) },
                        onCancel = onDismiss
                    )
                    else -> PaymentMethodSelection(
                        membershipPlan = membershipPlan,
                        onPaymentMethodSelected = onPaymentMethodSelected,
                        onCancel = onDismiss
                    )
                }
            }
        }
    }
}

/**
 * 支付方式选择界面
 */
@Composable
private fun PaymentMethodSelection(
    membershipPlan: MembershipPlan,
    onPaymentMethodSelected: (PaymentMethod) -> Unit,
    onCancel: () -> Unit
) {
    val (price, title, benefits) = when (membershipPlan) {
        MembershipPlan.MONTHLY -> Triple(
            "¥9.90/月",
            "月度会员",
            listOf("AI智能任务拆分", "高级时间统计", "自定义主题", "优先客服支持")
        )
        MembershipPlan.LIFETIME -> Triple(
            "¥29.90",
            "终身会员",
            listOf("所有月度会员功能", "无限AI使用次数", "终身免费更新", "专属客服通道", "💎金色专属标识")
        )
    }

    Column(
        modifier = Modifier.padding(24.dp)
    ) {
        // 标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "购买$title",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            IconButton(onClick = onCancel) {
                Icon(Icons.Default.Close, contentDescription = "关闭")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 价格展示
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = if (membershipPlan == MembershipPlan.LIFETIME) 
                    Color(0xFFFFD700).copy(alpha = 0.1f) 
                else 
                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (membershipPlan == MembershipPlan.LIFETIME) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "👑 ",
                            style = MaterialTheme.typography.headlineMedium
                        )
                        Text(
                            text = title,
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFFFD700)
                        )
                    }
                } else {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = price,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 会员权益
        Text(
            text = "会员权益",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        benefits.forEach { benefit ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = benefit,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 支付方式选择
        Text(
            text = "选择支付方式",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 支付宝
        PaymentMethodItem(
            icon = Icons.Default.AccountBalance, // 使用通用图标
            name = "支付宝",
            description = "推荐使用，支付快速安全",
            color = Color(0xFF1296DB),
            onClick = { onPaymentMethodSelected(PaymentMethod.ALIPAY) }
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 微信支付
        PaymentMethodItem(
            icon = Icons.Default.Smartphone,
            name = "微信支付",
            description = "便捷支付，一键完成",
            color = Color(0xFF07C160),
            onClick = { onPaymentMethodSelected(PaymentMethod.WECHAT) }
        )
        
        // 如果是调试模式，显示模拟支付选项
        if (BuildConfig.DEBUG) {
            Spacer(modifier = Modifier.height(8.dp))
            PaymentMethodItem(
                icon = Icons.Default.BugReport,
                name = "模拟支付",
                description = "测试使用，立即成功",
                color = Color(0xFF9C27B0),
                onClick = { onPaymentMethodSelected(PaymentMethod.MOCK) }
            )
        }
    }
}

/**
 * 支付方式选项组件
 */
@Composable
private fun PaymentMethodItem(
    icon: ImageVector,
    name: String,
    description: String,
    color: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = color.copy(alpha = 0.1f)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.padding(8.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 文字信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            // 箭头
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
            )
        }
    }
}

/**
 * 支付处理中界面
 */
@Composable
private fun PaymentProcessingView(paymentState: PaymentResult) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 加载动画
        CircularProgressIndicator(
            modifier = Modifier.size(64.dp),
            strokeWidth = 4.dp
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "正在处理支付",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = paymentState.message,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "请不要关闭此页面",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.error
        )
    }
}

/**
 * 支付成功界面
 */
@Composable
private fun PaymentSuccessView(
    paymentResult: PaymentResult,
    onConfirm: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 成功图标
        Card(
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
            ),
            shape = RoundedCornerShape(50)
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                tint = Color(0xFF4CAF50),
                modifier = Modifier
                    .size(64.dp)
                    .padding(16.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "支付成功！",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF4CAF50)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "恭喜您成为TimeFlow会员",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 支付详情
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                PaymentDetailRow("订单号", paymentResult.orderId)
                PaymentDetailRow("支付金额", "¥${paymentResult.amount}")
                PaymentDetailRow("支付方式", when (paymentResult.paymentMethod) {
                    PaymentMethod.ALIPAY -> "支付宝"
                    PaymentMethod.WECHAT -> "微信支付"
                    PaymentMethod.MOCK -> "模拟支付"
                })
                paymentResult.transactionId?.let { 
                    PaymentDetailRow("交易号", it)
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onConfirm,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF4CAF50)
            )
        ) {
            Text("确定", color = Color.White)
        }
    }
}

/**
 * 支付失败界面
 */
@Composable
private fun PaymentErrorView(
    paymentResult: PaymentResult,
    onRetry: () -> Unit,
    onCancel: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 错误图标
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.error.copy(alpha = 0.1f)
            ),
            shape = RoundedCornerShape(50)
        ) {
            Icon(
                imageVector = if (paymentResult.status == PaymentStatus.CANCELLED) 
                    Icons.Default.Cancel else Icons.Default.Error,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier
                    .size(64.dp)
                    .padding(16.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = if (paymentResult.status == PaymentStatus.CANCELLED) "支付已取消" else "支付失败",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = paymentResult.message,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 按钮组
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedButton(
                onClick = onCancel,
                modifier = Modifier.weight(1f)
            ) {
                Text("取消")
            }
            
            if (paymentResult.status != PaymentStatus.CANCELLED) {
                Button(
                    onClick = onRetry,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("重试")
                }
            }
        }
    }
}

/**
 * 支付详情行组件
 */
@Composable
private fun PaymentDetailRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
} 