package com.timeflow.app.data.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * 目标子任务模型
 */
data class GoalSubTask(
    val id: String = UUID.randomUUID().toString(),
    val goalId: String,
    val title: String,
    val description: String = "",
    val estimatedDurationDays: Int = 0,
    val completedAt: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val status: String = "待开始",
    val aiRecommendation: String? = ""
) 