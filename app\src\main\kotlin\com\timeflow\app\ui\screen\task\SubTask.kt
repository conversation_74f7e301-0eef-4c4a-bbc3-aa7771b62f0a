package com.timeflow.app.ui.screen.task

import com.timeflow.app.data.model.Priority
import java.time.LocalDateTime

/**
 * UI层子任务数据类
 */
data class SubTask(
    val id: String,
    val title: String,
    val isCompleted: Boolean = false,
    val priority: Priority = Priority.MEDIUM,
    val note: String = "",
    val reminderTime: LocalDateTime? = null,
    val parentTaskId: String? = null,
    val dueDate: LocalDateTime? = null
) {
    // 为了兼容性添加remindTime属性的getter
    val remindTime: LocalDateTime?
        get() = reminderTime
} 