package com.timeflow.app.ui.screen.task.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.ui.screen.goal.GoalViewModel
import com.timeflow.app.ui.screen.goal.GoalUiState
import com.timeflow.app.ui.theme.*
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/**
 * 目标选择组件
 * 用于在任务创建/编辑时选择关联的目标
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalSelectionComponent(
    selectedGoalId: String?,
    onGoalSelected: (String?, String?) -> Unit, // goalId, goalTitle
    modifier: Modifier = Modifier,
    goalViewModel: GoalViewModel = hiltViewModel()
) {
    var expanded by remember { mutableStateOf(false) }
    val availableGoals by goalViewModel.allGoals.collectAsState()
    val uiState by goalViewModel.uiState
    
    // 获取当前选中的目标信息
    val selectedGoal = availableGoals.find { it.id == selectedGoalId }
    
    // 加载所有目标
    LaunchedEffect(Unit) {
        goalViewModel.loadAllGoals()
    }
    
    Column(
        modifier = modifier.animateContentSize()
    ) {
        // 目标选择器标题和展开按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = !expanded }
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Outlined.TrackChanges,
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "关联目标",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            // 显示当前选中的目标
            if (selectedGoal != null) {
                GoalTag(
                    goalTitle = selectedGoal.title,
                    goalPriority = selectedGoal.priority,
                    modifier = Modifier.padding(end = 8.dp)
                )
            } else {
                Text(
                    text = "未关联目标",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            val rotationAngle by animateFloatAsState(
                targetValue = if (expanded) 180f else 0f,
                animationSpec = tween(300)
            )
            
            Icon(
                imageVector = Icons.Default.ExpandMore,
                contentDescription = if (expanded) "收起" else "展开",
                modifier = Modifier
                    .size(20.dp)
                    .rotate(rotationAngle),
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        // 目标列表
        AnimatedVisibility(
            visible = expanded,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Column {
                Divider(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                )
                
                when (val state = uiState) {
                    is GoalUiState.Loading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = DustyLavender
                            )
                        }
                    }
                    
                    is GoalUiState.Error -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "加载目标失败: ${state.message}",
                                color = MaterialTheme.colorScheme.error,
                                fontSize = 14.sp
                            )
                        }
                    }
                    
                    else -> {
                        LazyColumn(
                            modifier = Modifier.heightIn(max = 300.dp)
                        ) {
                            // 添加"不关联目标"选项
                            item {
                                GoalSelectionItem(
                                    goal = null,
                                    isSelected = selectedGoalId == null,
                                    onSelected = { onGoalSelected(null, null) }
                                )
                            }
                            
                            // 目标列表
                            items(availableGoals) { goal ->
                                GoalSelectionItem(
                                    goal = goal,
                                    isSelected = selectedGoalId == goal.id,
                                    onSelected = { onGoalSelected(goal.id, goal.title) }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 目标选择项
 */
@Composable
private fun GoalSelectionItem(
    goal: Goal?,
    isSelected: Boolean,
    onSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onSelected() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 选择状态指示器
        RadioButton(
            selected = isSelected,
            onClick = { onSelected() },
            colors = RadioButtonDefaults.colors(
                selectedColor = DustyLavender,
                unselectedColor = MaterialTheme.colorScheme.outline
            )
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        if (goal == null) {
            // "不关联目标"选项
            Column {
                Text(
                    text = "不关联目标",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                Text(
                    text = "此任务不关联任何目标",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
        } else {
            // 目标信息
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = goal.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 目标优先级标签
                    GoalPriorityChip(priority = goal.priority)
                }
                
                if (goal.description.isNotEmpty()) {
                    Text(
                        text = goal.description,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                // 目标进度和截止时间
                Row(
                    modifier = Modifier.padding(top = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 进度
                    Text(
                        text = "进度 ${(goal.progress * 100).toInt()}%",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                    
                    // 截止时间
                    goal.dueDate?.let { dueDate ->
                        val daysLeft = ChronoUnit.DAYS.between(
                            java.time.LocalDate.now(),
                            dueDate.toLocalDate()
                        ).toInt()
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = when {
                                daysLeft < 0 -> "已逾期 ${-daysLeft} 天"
                                daysLeft == 0 -> "今天截止"
                                daysLeft == 1 -> "明天截止"
                                else -> "剩余 $daysLeft 天"
                            },
                            fontSize = 11.sp,
                            color = when {
                                daysLeft < 0 -> MaterialTheme.colorScheme.error
                                daysLeft <= 3 -> Color(0xFFFF8247)
                                else -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 目标标签（用于显示已选中的目标）
 */
@Composable
fun GoalTag(
    goalTitle: String,
    goalPriority: GoalPriority,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when (goalPriority) {
        GoalPriority.URGENT -> Color(0xFFFFEBEE)
        GoalPriority.HIGH -> Color(0xFFFFF3E0)
        GoalPriority.MEDIUM -> Color(0xFFF3E5F5)
        GoalPriority.LOW -> Color(0xFFE8F5E8)
    }
    
    val textColor = when (goalPriority) {
        GoalPriority.URGENT -> Color(0xFFC62828)
        GoalPriority.HIGH -> Color(0xFFE65100)
        GoalPriority.MEDIUM -> Color(0xFF7B1FA2)
        GoalPriority.LOW -> Color(0xFF2E7D32)
    }
    
    Row(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.TrackChanges,
            contentDescription = null,
            tint = textColor,
            modifier = Modifier.size(12.dp)
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = goalTitle,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * 目标优先级芯片
 */
@Composable
private fun GoalPriorityChip(
    priority: GoalPriority,
    modifier: Modifier = Modifier
) {
    val (backgroundColor, textColor, text) = when (priority) {
        GoalPriority.URGENT -> Triple(
            Color(0xFFFFEBEE),
            Color(0xFFC62828),
            "紧急"
        )
        GoalPriority.HIGH -> Triple(
            Color(0xFFFFF3E0),
            Color(0xFFE65100),
            "高"
        )
        GoalPriority.MEDIUM -> Triple(
            Color(0xFFF3E5F5),
            Color(0xFF7B1FA2),
            "中"
        )
        GoalPriority.LOW -> Triple(
            Color(0xFFE8F5E8),
            Color(0xFF2E7D32),
            "低"
        )
    }
    
    Text(
        text = text,
        fontSize = 10.sp,
        fontWeight = FontWeight.Medium,
        color = textColor,
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 6.dp, vertical = 2.dp)
    )
} 