package com.timeflow.app.ui.screen.task

import android.app.Activity
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowManager
import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.ui.navigation.LocalNavController
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.model.TaskModel
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.screen.task.model.getSampleTasks
import kotlinx.serialization.Serializable
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.theme.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID
import com.timeflow.app.data.model.Task as ModelTask
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.SideEffect
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.material3.Surface
import androidx.compose.runtime.derivedStateOf
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardActions
import com.timeflow.app.ui.navigation.AppDestinations
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import com.timeflow.app.data.model.RecurrenceType
import com.timeflow.app.data.model.RecurrenceSettings

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskEditScreen(
    taskId: String?,
    navController: NavController,
    viewModel: TaskListViewModel = hiltViewModel(),
    parentTaskId: String? = null
) {
    // 提供CompositionLocal
    CompositionLocalProvider(LocalNavController provides navController) {
        // 应用稳定的状态管理，减少重组
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(Priority.MEDIUM) }
    var dueDate by remember { mutableStateOf<LocalDateTime?>(null) }
    var startDate by remember { mutableStateOf<LocalDateTime?>(null) }
    var formattedDueDate by remember { mutableStateOf("选择截止日期") }
    var formattedStartDate by remember { mutableStateOf("选择开始日期") }
    var tags by remember { mutableStateOf(emptyList<String>()) }
    var isCreating by remember { mutableStateOf(false) }

    // 🔧 循环相关状态
    var isRecurring by remember { mutableStateOf(false) }
    var recurrenceSettings by remember { mutableStateOf(RecurrenceSettings.none()) }
    var selectedRecurrenceType by remember { mutableStateOf(RecurrenceType.NONE) }
        // 添加日期选择对话框状态
        var showDatePicker by remember { mutableStateOf(false) }
        var showPriorityDialog by remember { mutableStateOf(false) }
        var showTagDialog by remember { mutableStateOf(false) }
        var showRecurrenceDialog by remember { mutableStateOf(false) } // 🔧 循环设置对话框
        var isSelectingStartDate by remember { mutableStateOf(false) } // 标记是否选择的是开始日期
        
        // 使用LaunchedEffect中的List状态
        val itemsVisibility = remember { 
            List(5) { index -> 
                mutableStateOf(false) 
            } 
        }
        
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
        
        // 保存任务ID以供AI优化使用
        var createdTaskId by remember { mutableStateOf<String?>(null) }

    // 动画状态
    var isPageVisible by remember { mutableStateOf(false) }
    var isContentVisible by remember { mutableStateOf(false) }
    
        // 子任务状态 - 使用稳定的状态管理
    var subTasks by remember { mutableStateOf(emptyList<SubTask>()) }
    var showAddSubTaskInput by remember { mutableStateOf(false) }

        // 使用derivedStateOf更高效地处理排序，避免每次重组都重新排序
        val sortedSubTasks by remember(subTasks) { 
            derivedStateOf { 
                subTasks.sortedWith(compareBy({ !it.isCompleted }, { it.priority })) 
            } 
        }
        
        // 使用SystemBarManager设置状态栏 - 使用SideEffect更安全
    val activity = LocalContext.current as? Activity
    val view = LocalView.current
    if (activity != null && !view.isInEditMode) {
        SideEffect {
            SystemBarManager.setupTaskPageSystemBars(activity)
        }
    }
            
        // 使用协程延迟动画，创建入场序列
        LaunchedEffect(Unit) {
            isPageVisible = true
            delay(100) // 减少延迟时间，让页面显示更快
            isContentVisible = true
        }
        
        // 给LaunchedEffect添加可见性交错动画 - 使用更短的延迟
        LaunchedEffect(isContentVisible) {
            if (isContentVisible) {
                itemsVisibility.forEachIndexed { index, state ->
                    delay(50L * index) // 使用更短的延迟，减少感知滞后
                    state.value = true
                }
        }
    }

    // 如果是编辑现有任务，加载任务数据
    LaunchedEffect(taskId) {
        if (taskId != null) {
            // 实际应用中应该从ViewModel加载任务数据
            val task = getSampleTasks().find { it.id == taskId }
            if (task != null) {
                title = task.title
                description = task.description
                selectedPriority = when(task.urgency) {
                    TaskUrgency.CRITICAL -> Priority.URGENT
                    TaskUrgency.HIGH -> Priority.HIGH
                    TaskUrgency.MEDIUM -> Priority.MEDIUM
                    TaskUrgency.LOW -> Priority.LOW
                }
                formattedDueDate = formatTimeLeft(task.daysLeft)
                tags = task.customTags
            }
        }
    }

    // 创建日期格式化辅助函数
    fun formatDateRange(): String {
        if (startDate == null && dueDate == null) {
            return "未设置时间范围"
        }
        
        val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
        val dateTimeFormatter = DateTimeFormatter.ofPattern("MM月dd日 HH:mm")
        val fullFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")
        
        return when {
            startDate != null && dueDate != null -> {
                    startDate?.let { start ->
                        dueDate?.let { due ->
                            if (start.toLocalDate().isEqual(due.toLocalDate())) {
                                "${dateFormatter.format(start)} ${start.format(DateTimeFormatter.ofPattern("HH:mm"))}-${due.format(DateTimeFormatter.ofPattern("HH:mm"))}"
                } else {
                                "${start.format(fullFormatter)} - ${due.format(fullFormatter)}"
                }
                        }
                    } ?: "日期错误"
            }
                startDate != null -> startDate?.format(fullFormatter)?.plus(" 开始") ?: "日期错误"
                else -> dueDate?.format(fullFormatter)?.plus(" 截止") ?: "日期错误"
        }
    }
    
    // 初始化日期显示
    LaunchedEffect(startDate, dueDate) {
        // 更新日期显示
            startDate?.let { start ->
                formattedStartDate = start.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))
        }
            dueDate?.let { due ->
                formattedDueDate = due.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))
        }
    }

    // 创建任务函数
    fun createTask() {
        if (title.isBlank()) {
            Toast.makeText(context, "任务标题不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 验证开始日期和截止日期逻辑
        if (startDate != null && dueDate != null) {
                startDate?.let { start ->
                    dueDate?.let { due ->
                        if (start.isAfter(due)) {
                Toast.makeText(context, "开始日期不能晚于截止日期", Toast.LENGTH_SHORT).show()
                return
                        }
                    }
            }
        }

        isCreating = true
        
        // 计算截止日期到今天的天数
        val daysLeft = if (dueDate != null) {
            val now = LocalDateTime.now()
                dueDate?.let { due ->
                    val days = ChronoUnit.DAYS.between(now.toLocalDate(), due.toLocalDate())
            days.toInt()
                } ?: 0
        } else {
            0
        }
        
        // 创建TaskTag对象列表
        val taskTags = tags.map { tagName ->
            TaskTag(name = tagName)
        }
                
            // 创建任务ID
            val newTaskId = UUID.randomUUID().toString()
        
        // 🔧 序列化循环设置
        val recurringPatternJson = if (isRecurring) {
            try {
                Json.encodeToString(recurrenceSettings)
            } catch (e: Exception) {
                Timber.e(e, "序列化循环设置失败")
                null
            }
        } else {
            null
        }

        // 创建ModelTask对象
        val modelTask = ModelTask(
                id = newTaskId,
            title = title,
            description = description,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            dueDate = dueDate,
            startDate = startDate,
            priority = selectedPriority,
            status = "待办",
            tags = taskTags,
            hasSubtasks = subTasks.isNotEmpty(),
            parentTaskId = parentTaskId,
            isRecurring = isRecurring, // 🔧 添加循环标记
            recurringPattern = recurringPatternJson // 🔧 添加循环设置
        )
        
        // 调用ViewModal保存任务
        viewModel.viewModelScope.launch {
            try {
                // 保存主任务
                viewModel.saveTask(modelTask)
                        
                    // 保存创建的任务ID
                    createdTaskId = newTaskId
                
                // 如果有子任务，保存子任务
                if (subTasks.isNotEmpty()) {
                    subTasks.forEach { subTask ->
                        val subTaskModel = ModelTask(
                            id = subTask.id,
                            title = subTask.title,
                            parentTaskId = modelTask.id,
                            priority = subTask.priority,
                            depth = 1,
                            status = if (subTask.isCompleted) "已完成" else "待办",
                            createdAt = LocalDateTime.now(),
                            updatedAt = LocalDateTime.now()
                        )
                        viewModel.saveTask(subTaskModel)
                    }
                }
                
                // 刷新任务列表
                viewModel.loadTasks()
                
                // 发布事件通知任务创建成功
                EventBus.tryEmit(AppEvent.TaskCreated(modelTask.id))

                // 添加成功反馈动画
                isPageVisible = false
                delay(200)
                
                // 确保返回时重置窗口设置
                if (activity != null) {
                    val window = activity.window
                    WindowCompat.setDecorFitsSystemWindows(window, false)
                }
                
                // 返回上一页
                navController.popBackStack()
                
                // 显示成功提示
                Toast.makeText(context, "任务创建成功", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                Timber.e(e, "创建任务失败")
                Toast.makeText(context, "创建任务失败: ${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                isCreating = false
            }
        }
    }

    AnimatedVisibility(
        visible = isPageVisible,
        enter = fadeIn(tween(300)) + slideInVertically(initialOffsetY = { it / 4 }),
        exit = fadeOut(tween(200)) + slideOutVertically(targetOffsetY = { -it / 4 })
    ) {
            Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 为状态栏提供适当的空间
    ) {
        // 顶部应用栏
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.Transparent)
            ) {
                // 顶部标题，添加淡入动画
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                        .alpha(
                            animateFloatAsState(
                                targetValue = if (isPageVisible) 1f else 0f,
                                        animationSpec = tween(300),
                                        label = "title alpha"
                            ).value
                        ),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 关闭按钮，添加缩放动画
                    IconButton(
                        onClick = { 
                            // 添加退出动画
                            isPageVisible = false
                            coroutineScope.launch {
                                delay(200)
                                navController.popBackStack()
                            }
                        },
                        modifier = Modifier.scale(
                            animateFloatAsState(
                                targetValue = if (isPageVisible) 1f else 0.8f,
                                        animationSpec = tween(300),
                                        label = "close button scale"
                            ).value
                        )
                    ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = TextPrimary
                    )
                }
                    
                    // 标题
                    Text(
                        text = if (taskId != null) "编辑任务" else "新建任务",
                        fontSize = SystemBarManager.getStandardTitleTextSize(),
                        fontWeight = FontWeight.Bold,
                        color = TextPrimary
                    )
                    
                    // 保存按钮，添加按下缩放反馈
                    var isSavePressed by remember { mutableStateOf(false) }
                TextButton(
                    onClick = {
                            isSavePressed = true
                            coroutineScope.launch {
                                delay(150)
                                createTask()
                                isSavePressed = false
                            }
                        },
                        enabled = !isCreating && title.isNotBlank(),
                        modifier = Modifier.scale(
                            animateFloatAsState(
                                targetValue = if (isSavePressed) 0.9f else 1f,
                                        animationSpec = tween(150),
                                        label = "save button scale"
                            ).value
                        )
                ) {
                    Text(
                            text = "保存",
                            color = if (!isCreating && title.isNotBlank()) DustyLavender else DustyLavender.copy(alpha = 0.5f),
                        fontWeight = FontWeight.Bold
                    )
                }
                }
            }

                    // 内容区域使用可滚动的Column
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f) // 占据剩余空间
                            .verticalScroll(rememberScrollState())
                            .padding(horizontal = 20.dp)
                            .padding(bottom = 120.dp) // 添加足够的底部padding，避免内容被底部按钮遮挡
                    ) {
                        // 保持原有动画可见性内容
            AnimatedVisibility(
                visible = isContentVisible,
                enter = fadeIn(tween(400)) + slideInVertically(
                    initialOffsetY = { it / 8 },
                                animationSpec = tween(durationMillis = 300)
                )
            ) {
                            Column(modifier = Modifier.fillMaxWidth()) {
                    // 任务标题输入，添加进入动画
                    AnimatedVisibility(
                        visible = itemsVisibility[0].value,
                        enter = fadeIn(tween(300)) + slideInVertically(
                            initialOffsetY = { it / 10 }
                        )
                    ) {
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                            placeholder = { Text("任务标题", color = TextSecondary.copy(alpha = 0.6f)) },
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                                unfocusedBorderColor = LavenderAsh.copy(alpha = 0.5f),
                                focusedContainerColor = Color.White,
                                unfocusedContainerColor = Color.White
                    ),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Next
                    ),
                            shape = RoundedCornerShape(12.dp),
                    singleLine = true
                )
            }

                                // 子任务列表
                                if (subTasks.isNotEmpty()) {
                                    Spacer(modifier = Modifier.height(16.dp))
                                    Text(
                                        text = "子任务",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 16.sp,
                                        color = TextPrimary
                                    )
                                    
                                    // 使用普通Column，避免LazyColumn嵌套问题
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                                            // 使用更温和的动画效果
                                            .animateContentSize(
                                                animationSpec = tween(
                                                    durationMillis = 200,
                                                    easing = LinearEasing
                                                )
                                            )
                                            .padding(vertical = 8.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        // 使用forEach而不是LazyColumn
                                        sortedSubTasks.forEach { subTask ->
                                            // 使用key包装每个子任务项，确保稳定渲染
                                            key(subTask.id) {
                                                SubTaskItem(
                                                    subTask = subTask,
                                                    onDelete = {
                                                        // 使用更精确的状态更新
                                                        subTasks = subTasks.filter { it.id != subTask.id }
                                                    },
                                                    onToggleComplete = { isCompleted ->
                                                        subTasks = subTasks.map {
                                                            if (it.id == subTask.id) it.copy(isCompleted = isCompleted)
                                                            else it
                                                        }
                                    }
                                )
                                            }
                                        }
                                    }
                                }

                                // 添加子任务输入框
                                Spacer(modifier = Modifier.height(16.dp))
                                OutlinedButton(
                                    onClick = { showAddSubTaskInput = !showAddSubTaskInput },
                                    modifier = Modifier.fillMaxWidth(),
                                    border = BorderStroke(1.dp, LavenderAsh),
                                    contentPadding = PaddingValues(vertical = 12.dp),
                                    colors = ButtonDefaults.outlinedButtonColors(
                                        contentColor = TextPrimary
                                    )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Add,
                                        contentDescription = "添加子任务",
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = if (subTasks.isEmpty()) "添加子任务" else "添加更多子任务",
                                        fontSize = 14.sp
                                    )
                                }

                                // 🔧 任务属性设置区域
                                Spacer(modifier = Modifier.height(24.dp))
                                Text(
                                    text = "任务设置",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    color = TextPrimary,
                                    modifier = Modifier.padding(bottom = 12.dp)
                                )

                                // 任务属性设置卡片
                                Surface(
                                    modifier = Modifier.fillMaxWidth(),
                                    shape = RoundedCornerShape(12.dp),
                                    color = Color.White,
                                    shadowElevation = 0.dp,
                                    border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.3f))
                                ) {
                                    Column(modifier = Modifier.padding(16.dp)) {
                                        // 优先级设置
                                        DetailItem(
                                            icon = Icons.Default.Flag,
                                            label = "优先级",
                                            value = getTaskEditPriorityText(selectedPriority),
                                            onClick = { showPriorityDialog = true }
                                        )

                                        Divider(
                                            color = LavenderAsh.copy(alpha = 0.3f),
                                            modifier = Modifier.padding(vertical = 8.dp)
                                        )

                                        // 日期设置
                                        DetailItem(
                                            icon = Icons.Default.Schedule,
                                            label = "时间",
                                            value = formatDateRange(),
                                            onClick = { showDatePicker = true }
                                        )

                                        Divider(
                                            color = LavenderAsh.copy(alpha = 0.3f),
                                            modifier = Modifier.padding(vertical = 8.dp)
                                        )

                                        // 🔧 循环设置
                                        DetailItem(
                                            icon = Icons.Default.Repeat,
                                            label = "循环",
                                            value = if (isRecurring) selectedRecurrenceType.displayName else "不循环",
                                            onClick = {
                                                // 显示循环设置对话框
                                                showRecurrenceDialog = true
                                            }
                                        )

                                        Divider(
                                            color = LavenderAsh.copy(alpha = 0.3f),
                                            modifier = Modifier.padding(vertical = 8.dp)
                                        )

                                        // 标签设置
                                        DetailItem(
                                            icon = Icons.Default.Label,
                                            label = "标签",
                                            value = if (tags.isEmpty()) "添加标签" else "${tags.size}个标签",
                                            onClick = { showTagDialog = true }
                                        )
                                    }
                                }

                                // 子任务输入区域
                                AnimatedVisibility(
                                    visible = showAddSubTaskInput,
                                    enter = expandVertically(
                                        animationSpec = tween(200, easing = LinearEasing)
                                    ) + fadeIn(
                                        animationSpec = tween(150)
                                    ),
                                    exit = shrinkVertically(
                                        animationSpec = tween(200, easing = LinearEasing)
                                    ) + fadeOut(
                                        animationSpec = tween(100)
                                    )
                                ) {
                                    var subTaskTitle by remember { mutableStateOf("") }
                                    
                                    Surface(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                            .padding(vertical = 8.dp),
                                        color = Color.White,
                                        shape = RoundedCornerShape(10.dp),
                                        shadowElevation = 0.dp,
                                        border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.3f))
                                    ) {
                                        Column(modifier = Modifier.padding(12.dp)) {
                                            OutlinedTextField(
                                                value = subTaskTitle,
                                                onValueChange = { subTaskTitle = it },
                                                modifier = Modifier.fillMaxWidth(),
                                                placeholder = { Text("输入子任务内容") },
                                                colors = OutlinedTextFieldDefaults.colors(
                                                    focusedBorderColor = LavenderAsh,
                                                    unfocusedBorderColor = LavenderAsh.copy(alpha = 0.5f),
                                                    focusedContainerColor = Color.White,
                                                    unfocusedContainerColor = Color.White
                                                ),
                                                singleLine = true,
                                                keyboardOptions = KeyboardOptions(
                                                    imeAction = ImeAction.Done
                                                ),
                                                trailingIcon = {
                                                    IconButton(
                                                        onClick = {
                                                            if (subTaskTitle.isNotBlank()) {
                                                                val newSubTask = SubTask(
                                                                    id = UUID.randomUUID().toString(),
                                                                    title = subTaskTitle.trim(),
                                                                    isCompleted = false,
                                                                    priority = Priority.MEDIUM
                                                                )
                                                                // 使用不可变方式更新列表
                                                                subTasks = subTasks + newSubTask
                                                                subTaskTitle = ""
                                                            }
                                                        },
                                                        enabled = subTaskTitle.isNotBlank()
                                ) {
                                    Icon(
                                                            Icons.Default.Add,
                                                            contentDescription = "添加",
                                                            tint = if (subTaskTitle.isNotBlank()) DustyLavender else LavenderAsh
                                                        )
                                                    }
                                                }
                                            )
                                            
                                            Spacer(modifier = Modifier.height(8.dp))
                                            
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.End
                                            ) {
                                                OutlinedButton(
                                                    onClick = { showAddSubTaskInput = false },
                                                    colors = ButtonDefaults.outlinedButtonColors(
                                                        contentColor = DustyLavender
                                                    ),
                                                    border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.5f))
                                                ) {
                                                    Text("取消")
                                    }
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                                
                                                Button(
                                                    onClick = { showAddSubTaskInput = false },
                                                    colors = ButtonDefaults.buttonColors(
                                                        containerColor = DustyLavender
                                                    )
                                                ) {
                                                    Text("完成")
                                                }
                                            }
                                }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 底部按钮区域 - 固定在底部，不在滚动区域内
                    Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                        .background(
                                Brush.verticalGradient(
                                    colors = listOf(
                                        Color.White.copy(alpha = 0f),
                                        Color.White.copy(alpha = 0.9f),
                                        Color.White
                                    )
                                )
                                        )
                            .padding(horizontal = 20.dp, vertical = 16.dp)
                    ) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                                ) {
                            // 主要创建按钮
                            Button(
                                onClick = { createTask() },
                                modifier = Modifier.fillMaxWidth(),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF8BAA80),
                                    disabledContainerColor = Color(0xFF8BAA80).copy(alpha = 0.5f)
                                ),
                                shape = RoundedCornerShape(12.dp),
                                contentPadding = PaddingValues(vertical = 16.dp),
                                enabled = title.isNotBlank() && !isCreating
                            ) {
                                if (isCreating) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(20.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                }
                                Text(
                                    text = "创建",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }

                            // AI优化任务按钮 - 创建任务后可用
                            if (title.isNotBlank()) {
                                val context = LocalContext.current
                                
                                OutlinedButton(
                                    onClick = { 
                                        // 显示提示用户稍后优化
                                        Toast.makeText(context, "创建任务后可在任务详情页中使用AI优化功能", Toast.LENGTH_SHORT).show()
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    border = BorderStroke(1.dp, DustyLavender),
                                    colors = ButtonDefaults.outlinedButtonColors(
                                        contentColor = DustyLavender
                                    ),
                                    shape = RoundedCornerShape(12.dp),
                                    contentPadding = PaddingValues(vertical = 16.dp),
                                    enabled = title.isNotBlank() && !isCreating
                ) {
                    Icon(
                                        imageVector = Icons.Default.SmartToy,
                                            contentDescription = null,
                                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                                        text = "创建后使用AI优化",
                                        fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                }
            }
        }
    }
    
    // 日期选择器对话框
    if (showDatePicker) {
            DatePickerDialog(
                        onDismissRequest = { showDatePicker = false },
                        confirmButton = {
                            Button(onClick = {
                                // 从DatePickerState获取日期，添加时间部分
                                showDatePicker = false
                            }) {
                                Text("确认")
                            }
                                            }
                    ) {
                        // 自定义日期选择器内容
                        Text("日期选择器内容将在这里实现")
                    }
    }
    
    // 优先级选择对话框
        if (showPriorityDialog) {
                    Dialog(onDismissRequest = { showPriorityDialog = false }) {
                        Surface(shape = RoundedCornerShape(16.dp), color = Color.White) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text("选择优先级", fontWeight = FontWeight.Bold)
                                Spacer(modifier = Modifier.height(8.dp))
                                // 这里将实现优先级选择内容
                            }
                        }
                    }
    }
    
    // 标签选择对话框
    if (showTagDialog) {
                    Dialog(onDismissRequest = { showTagDialog = false }) {
                        Surface(shape = RoundedCornerShape(16.dp), color = Color.White) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text("选择标签", fontWeight = FontWeight.Bold)
                                Spacer(modifier = Modifier.height(8.dp))
                                // 这里将实现标签选择内容
                            }
                        }
                    }
                }

    // 🔧 循环设置对话框
    if (showRecurrenceDialog) {
        RecurrenceSettingsDialog(
            isRecurring = isRecurring,
            recurrenceSettings = recurrenceSettings,
            selectedRecurrenceType = selectedRecurrenceType,
            onDismiss = { showRecurrenceDialog = false },
            onConfirm = { recurring, settings, type ->
                isRecurring = recurring
                recurrenceSettings = settings
                selectedRecurrenceType = type
                showRecurrenceDialog = false
            }
        )
    }
            }
        }
    }
}

/**
 * 获取优先级对应的颜色 - 重命名以避免冲突
 */
@Composable
private fun getTaskEditPriorityColor(priority: Priority): Color {
    return when (priority) {
        Priority.URGENT -> Color(0xFFD50000)  // 深红色
        Priority.HIGH -> Color(0xFFF44336)    // 红色
        Priority.MEDIUM -> Color(0xFFFFA726)  // 橙色
        Priority.LOW -> Color(0xFF66BB6A)     // 绿色
    }
}

/**
 * 获取优先级对应的文本 - 重命名以避免冲突
 */
@Composable
private fun getTaskEditPriorityText(priority: Priority): String {
    return when (priority) {
        Priority.URGENT -> "紧急"
        Priority.HIGH -> "高优先级"
        Priority.MEDIUM -> "中优先级"
        Priority.LOW -> "低优先级"
    }
}

@Composable
private fun DetailItem(
    icon: ImageVector,
    label: String,
    value: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = TextSecondary.copy(alpha = 0.7f),
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(10.dp))
        Text(
            text = label,
            color = TextSecondary,
            fontSize = 15.sp
        )
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = value,
            color = if (value.contains("选择")) TextSecondary.copy(alpha = 0.5f) else TextPrimary,
            fontSize = 14.sp
        )
        Spacer(modifier = Modifier.width(8.dp))
        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = "选择",
            tint = TextSecondary.copy(alpha = 0.5f)
        )
    }
}

/**
 * 子任务项目组件 - 优化版本，没有嵌套动画，渲染更稳定
 */
@Composable
private fun SubTaskItem(
    subTask: SubTask,
    onDelete: () -> Unit,
    onToggleComplete: (Boolean) -> Unit
) {
    // 使用Surface代替Row+修饰符的组合，减少嵌套层级
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(10.dp),
        color = if (subTask.isCompleted) 
            LavenderAsh.copy(alpha = 0.1f)
        else 
            Color.White,
        // 使用固定的高度，避免高度变化引起的布局抖动
        tonalElevation = 0.dp
    ) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
            // 复选框 - 避免使用可能导致重绘的动画
            Checkbox(
                checked = subTask.isCompleted,
                onCheckedChange = { onToggleComplete(it) },
                colors = CheckboxDefaults.colors(
                    checkedColor = DustyLavender,
                    uncheckedColor = LavenderAsh
                ),
                modifier = Modifier.padding(end = 4.dp)
                )
        
            // 标题 - 使用固定权重和padding，避免文本长度变化引起的布局波动
        Text(
            text = subTask.title,
            modifier = Modifier
                .weight(1f)
                    .padding(horizontal = 8.dp),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                textDecoration = if (subTask.isCompleted) 
                    TextDecoration.LineThrough 
                else 
                    TextDecoration.None,
                color = if (subTask.isCompleted) 
                    TextPrimary.copy(alpha = 0.6f) 
                else 
                    TextPrimary
            )
        
            // 删除按钮 - 简化实现，避免过度动画
        IconButton(
                onClick = onDelete,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "删除子任务",
                    tint = LavenderAsh,
                modifier = Modifier.size(16.dp)
            )
        }
    }
    }
}

/**
 * 循环设置对话框
 */
@Composable
private fun RecurrenceSettingsDialog(
    isRecurring: Boolean,
    recurrenceSettings: RecurrenceSettings,
    selectedRecurrenceType: RecurrenceType,
    onDismiss: () -> Unit,
    onConfirm: (Boolean, RecurrenceSettings, RecurrenceType) -> Unit
) {
    var tempIsRecurring by remember { mutableStateOf(isRecurring) }
    var tempRecurrenceType by remember { mutableStateOf(selectedRecurrenceType) }
    var tempSettings by remember { mutableStateOf(recurrenceSettings) }

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "循环设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 是否启用循环
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "启用循环",
                        fontSize = 16.sp,
                        color = TextPrimary
                    )
                    Switch(
                        checked = tempIsRecurring,
                        onCheckedChange = { tempIsRecurring = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = DustyLavender,
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = LavenderAsh
                        )
                    )
                }

                // 循环类型选择
                if (tempIsRecurring) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "循环类型",
                        fontSize = 14.sp,
                        color = TextSecondary,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // 循环类型选项
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        RecurrenceType.values().filter { it != RecurrenceType.NONE }.forEach { type ->
                            RecurrenceTypeItem(
                                type = type,
                                isSelected = tempRecurrenceType == type,
                                onClick = {
                                    tempRecurrenceType = type
                                    tempSettings = when (type) {
                                        RecurrenceType.DAILY -> RecurrenceSettings.daily()
                                        RecurrenceType.WEEKLY -> RecurrenceSettings.weekly()
                                        RecurrenceType.MONTHLY -> RecurrenceSettings.monthly()
                                        RecurrenceType.YEARLY -> RecurrenceSettings.yearly()
                                        else -> RecurrenceSettings.none()
                                    }
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = TextSecondary
                        ),
                        border = BorderStroke(1.dp, LavenderAsh)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            val finalType = if (tempIsRecurring) tempRecurrenceType else RecurrenceType.NONE
                            val finalSettings = if (tempIsRecurring) tempSettings else RecurrenceSettings.none()
                            onConfirm(tempIsRecurring, finalSettings, finalType)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = DustyLavender
                        )
                    ) {
                        Text("确定", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 循环类型选项组件
 */
@Composable
private fun RecurrenceTypeItem(
    type: RecurrenceType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent,
        border = BorderStroke(
            1.dp,
            if (isSelected) DustyLavender else LavenderAsh.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = DustyLavender,
                    unselectedColor = LavenderAsh
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = type.displayName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                Text(
                    text = type.description,
                    fontSize = 12.sp,
                    color = TextSecondary
                )
            }
        }
    }
}