package com.timeflow.app.ui.screen.task.model

import com.timeflow.app.ui.screen.task.SubTask
import java.time.LocalDateTime

/**
 * 将UI层的SubTask转换为模型层的SubTask
 */
fun convertUISubTaskToModelSubTask(uiSubTask: SubTask): FeedbackData {
    return FeedbackData(
        comment = uiSubTask.title,
        timestamp = LocalDateTime.now()
    )
    // 注：此处将修改为使用UI层SubTask，故原转换为模型层SubTask的方法已不需要
    // 替换为创建FeedbackData以保持编译通过
}

/**
 * 将模型层的TaskModel转换为UI层的TaskData
 */
fun convertModelTaskDataToUITaskData(modelTaskData: TaskModel): com.timeflow.app.ui.screen.task.TaskData {
    return com.timeflow.app.ui.screen.task.TaskData(
        id = modelTaskData.id,
        title = modelTaskData.title,
        description = modelTaskData.description,
        dueDate = modelTaskData.daysLeft.let { 
            if (it > 0) LocalDateTime.now().plusDays(it.toLong()) else null 
        },
        priority = when (modelTaskData.urgency) {
            TaskUrgency.CRITICAL -> com.timeflow.app.data.model.Priority.URGENT
            TaskUrgency.HIGH -> com.timeflow.app.data.model.Priority.HIGH
            TaskUrgency.MEDIUM -> com.timeflow.app.data.model.Priority.MEDIUM
            TaskUrgency.LOW -> com.timeflow.app.data.model.Priority.LOW
        },
        status = if (modelTaskData.isCompleted) "已完成" else "待办",
        tags = modelTaskData.customTags.toMutableList().apply { 
            modelTaskData.tag?.let { add(it) } 
        },
        daysLeft = modelTaskData.daysLeft,
        subTasks = modelTaskData.subTasks
    )
}

/**
 * 将模型层的SubTask转换为UI层的SubTask
 */
fun convertModelSubTaskToUISubTask(modelSubTask: SubTask): SubTask {
    return SubTask(
        id = modelSubTask.id,
        title = modelSubTask.title,
        isCompleted = modelSubTask.isCompleted,
        priority = modelSubTask.priority,
        dueDate = modelSubTask.dueDate,
        note = modelSubTask.note,
        reminderTime = modelSubTask.reminderTime,
        parentTaskId = modelSubTask.parentTaskId
    )
}

/**
 * 检查UI层的所有子任务是否都已完成
 */
fun areAllUISubTasksCompleted(subTasks: List<SubTask>): Boolean {
    return subTasks.isNotEmpty() && subTasks.all { it.isCompleted }
} 

// 将Task转换为SubTask
fun com.timeflow.app.data.model.Task.toSubTask(): SubTask {
    return SubTask(
        id = this.id,
        title = this.title,
        isCompleted = this.status == "已完成",
        priority = this.priority?.let { com.timeflow.app.data.model.Priority.valueOf(it.toString()) } ?: com.timeflow.app.data.model.Priority.MEDIUM,
        note = this.description ?: "",
        reminderTime = this.dueDate,
        parentTaskId = this.parentTaskId,
        dueDate = this.dueDate
    )
}

// 删除重复的FeedbackData定义，使用model.FeedbackData.kt中的定义 