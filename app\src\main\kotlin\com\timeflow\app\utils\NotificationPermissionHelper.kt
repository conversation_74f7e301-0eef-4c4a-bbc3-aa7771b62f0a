package com.timeflow.app.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.height
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 通知权限助手
 * 处理Android 13+的通知权限请求和管理
 */
@Singleton
class NotificationPermissionHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "NotificationPermissionHelper"
        private const val PERMISSION_REQUEST_CODE = 1001
    }
    
    /**
     * 检查是否有通知权限
     */
    fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 13 以下版本默认有通知权限
            true
        }
    }
    
    /**
     * 请求通知权限（需要Activity上下文）
     */
    fun requestNotificationPermission(activity: Activity, onResult: (Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (hasNotificationPermission()) {
                onResult(true)
                return
            }
            
            // 检查是否应该显示权限说明
            if (ActivityCompat.shouldShowRequestPermissionRationale(
                activity, 
                Manifest.permission.POST_NOTIFICATIONS
            )) {
                Log.d(TAG, "需要显示权限说明")
                // 这里可以显示权限说明对话框
            }
            
            // 请求权限
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                PERMISSION_REQUEST_CODE
            )
        } else {
            onResult(true)
        }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "无法打开应用设置", e)
        }
    }
    
    /**
     * 检查系统通知设置
     */
    fun areNotificationsEnabled(): Boolean {
        return try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) 
                as android.app.NotificationManager
            notificationManager.areNotificationsEnabled()
        } catch (e: Exception) {
            Log.e(TAG, "检查通知设置失败", e)
            false
        }
    }
    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatus(): String {
        return when {
            !hasNotificationPermission() -> "未授予通知权限"
            !areNotificationsEnabled() -> "系统通知已关闭"
            else -> "通知权限正常"
        }
    }
}

/**
 * Compose状态管理
 */
data class NotificationPermissionState(
    val showDialog: Boolean,
    val dismissDialog: () -> Unit,
    val requestPermission: () -> Unit,
    val openSettings: () -> Unit
)

/**
 * Compose组合函数 - 通知权限状态管理
 */
@Composable
fun rememberNotificationPermission(
    helper: NotificationPermissionHelper
): NotificationPermissionState {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    var showDialog by remember { mutableStateOf(false) }
    
    // 在应用启动时检查权限
    LaunchedEffect(Unit) {
        if (!helper.hasNotificationPermission()) {
            // 延迟显示对话框，避免在应用启动时立即弹出
            kotlinx.coroutines.delay(1000)
            showDialog = true
        }
    }
    
    return NotificationPermissionState(
        showDialog = showDialog,
        dismissDialog = { showDialog = false },
        requestPermission = {
            activity?.let { act ->
                helper.requestNotificationPermission(act) { granted ->
                    if (!granted) {
                        // 如果权限被拒绝，保持对话框显示，让用户选择打开设置
                        showDialog = true
                    } else {
                        showDialog = false
                    }
                }
            } ?: run {
                // 如果无法获取Activity，直接打开设置
                helper.openAppSettings()
                showDialog = false
            }
        },
        openSettings = {
            helper.openAppSettings()
            showDialog = false
        }
    )
}

/**
 * 通知权限对话框组件
 */
@Composable
fun NotificationPermissionDialog(
    showDialog: Boolean,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    onOpenSettings: () -> Unit
) {
    if (showDialog) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Row(
                    verticalAlignment = androidx.compose.ui.Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.Notifications,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("开启通知权限")
                }
            },
            text = {
                Column {
                    Text(
                        text = "为了及时提醒您的任务和目标，TimeFlow需要通知权限。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "✓ 任务截止提醒\n✓ 习惯培养提醒\n✓ 专注时间提醒\n✓ 目标进度提醒",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
            },
            confirmButton = {
                TextButton(onClick = onConfirm) {
                    Text("开启权限")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("稍后再说")
                }
            }
        )
    }
} 