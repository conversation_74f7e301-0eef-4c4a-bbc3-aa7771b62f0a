package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.LocalDateConverter
import java.time.LocalDate

/**
 * 症状记录实体类
 */
@Entity(tableName = "symptoms")
@TypeConverters(LocalDateConverter::class)
data class SymptomRecord(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val date: LocalDate,
    @ColumnInfo(name = "symptom_type") val symptomType: String,
    val intensity: Int, // 1-5
    val notes: String? = null,
    val createdAt: LocalDate = LocalDate.now()
) 