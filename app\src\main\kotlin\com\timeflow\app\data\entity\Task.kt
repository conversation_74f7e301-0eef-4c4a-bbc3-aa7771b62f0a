package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.ListStringConverter
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 任务实体类
 */
@Entity(
    tableName = "tasks",
    foreignKeys = [
        ForeignKey(
            entity = KanbanColumn::class,
            parentColumns = ["id"],
            childColumns = ["column_id"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Task::class,
            parentColumns = ["id"],
            childColumns = ["parent_task_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("column_id"),
        Index("parent_task_id"),
        Index("created_at")
    ]
)
@TypeConverters(ListStringConverter::class)
@Serializable
data class Task(
    @PrimaryKey
    val id: String,
    
    val title: String,
    
    val description: String = "",
    
    @ColumnInfo(name = "due_date")
    @Contextual
    val dueDate: LocalDateTime? = null,

    @ColumnInfo(name = "start_date")
    @Contextual
    val startDate: LocalDateTime? = null,
    
    @ColumnInfo(defaultValue = "0")
    val priority: Int = 0,
    
    @ColumnInfo(name = "created_at")
    @Contextual
    val createdAt: LocalDateTime,

    @ColumnInfo(name = "updated_at")
    @Contextual
    val updatedAt: LocalDateTime,

    @ColumnInfo(name = "completed_at")
    @Contextual
    val completedAt: LocalDateTime? = null,
    
    @ColumnInfo(name = "parent_task_id")
    val parentTaskId: String? = null,
    
    @ColumnInfo(defaultValue = "0")
    val depth: Int = 0,
    
    @ColumnInfo(defaultValue = "待办")
    val status: String = "待办",
    
    @ColumnInfo(name = "has_subtasks", defaultValue = "0")
    val hasSubtasks: Boolean = false,
    
    @ColumnInfo(defaultValue = "0")
    val progress: Float = 0f,
    
    @ColumnInfo(name = "order_index", defaultValue = "0")
    val orderIndex: Int = 0,
    
    val projectId: String? = null,
    
    val parentId: String? = null,
    
    @Contextual
    val reminderTime: LocalDateTime? = null,
    
    val isRecurring: Boolean = false,
    
    val recurringPattern: String? = null,
    
    @TypeConverters(ListStringConverter::class)
    val attachmentUrls: List<String> = emptyList(),
    
    @TypeConverters(ListStringConverter::class)
    val tagIds: List<String> = emptyList(),
    
    val assignedTo: String? = null,
    
    val estimatedTime: Int? = null,
    
    val actualTime: Int? = null,
    
    @ColumnInfo(defaultValue = "0")
    val sortOrder: Int = 0,
    
    val isStarred: Boolean = false,
    
    // 子任务数量
    @ColumnInfo(name = "child_tasks_count", defaultValue = "0")
    val childTasksCount: Int = 0,
    
    // 已完成的子任务数量
    @ColumnInfo(name = "completed_child_tasks_count", defaultValue = "0")
    val completedChildTasksCount: Int = 0,
    
    // 所属列ID
    @ColumnInfo(name = "column_id")
    val columnId: String? = null,
    
    // 是否由AI生成
    @ColumnInfo(name = "ai_generated", defaultValue = "0") 
    val aiGenerated: Boolean = false,
    
    // 关联的目标ID
    @ColumnInfo(name = "goal_id")
    val goalId: String? = null,
    
    // 日期是否被用户手动修改过，默认为false表示系统自动设置
    @ColumnInfo(name = "date_manually_modified", defaultValue = "0")
    val dateManuallyModified: Boolean = false,
    
    // 🆕 浮动任务相关字段
    @ColumnInfo(name = "is_floating_task", defaultValue = "0")
    val isFloatingTask: Boolean = false,
    
    @ColumnInfo(name = "floating_week_start")
    @Contextual
    val floatingWeekStart: LocalDateTime? = null,

    @ColumnInfo(name = "floating_week_end")
    @Contextual
    val floatingWeekEnd: LocalDateTime? = null,

    @ColumnInfo(name = "scheduled_date")
    @Contextual
    val scheduledDate: LocalDateTime? = null,
    
    @ColumnInfo(name = "floating_task_order", defaultValue = "0")
    val floatingTaskOrder: Int = 0
) {
    /**
     * 将LocalDateTime转换为LocalDate
     */
    fun toLocalDate(): LocalDate {
        return this.dueDate?.toLocalDate() ?: LocalDate.now()
    }
    
    // Room需要忽略的字段，不保存到数据库
    @androidx.room.Ignore
    val subtasks: List<Task> = emptyList()
} 