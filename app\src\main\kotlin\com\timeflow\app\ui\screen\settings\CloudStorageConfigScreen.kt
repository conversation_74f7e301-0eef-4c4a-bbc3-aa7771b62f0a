package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.ui.screen.settings.CloudProvider

/**
 * 云存储配置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CloudStorageConfigScreen(
    navController: NavController,
    initialConfig: CloudStorageConfigData = CloudStorageConfigData(),
    onConfigSaved: (CloudStorageConfigData) -> Unit = {}
) {
    var config by remember { mutableStateOf(initialConfig) }
    var showSecretKey by remember { mutableStateOf(false) }
    var isTestingConnection by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // 顶部导航栏
        TopAppBar(
            title = { 
                Text(
                    text = "S3 配置",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Medium
                ) 
            },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { /* TODO: 显示帮助信息 */ }) {
                    Icon(Icons.Default.Help, contentDescription = "帮助")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.background
            )
        )
        
        // 配置内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // 提示文本
            Text(
                text = "请输入你的 S3 配置信息",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            
            // 云服务提供商选择
            CloudProviderSelector(
                selectedProvider = CloudProvider.valueOf(config.provider),
                onProviderSelected = { provider ->
                    config = config.copy(
                        provider = provider.name,
                        region = getDefaultRegion(provider),
                        endpoint = getDefaultEndpoint(provider)
                    )
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 根据选择的提供商显示相应的配置字段
            when (CloudProvider.valueOf(config.provider)) {
                CloudProvider.AWS_S3 -> AwsS3ConfigFields(config) { config = it }
                CloudProvider.QINIU_CLOUD -> QiniuCloudConfigFields(config) { config = it }
                CloudProvider.TENCENT_CLOUD -> TencentCloudConfigFields(config) { config = it }
                CloudProvider.ALIYUN_OSS -> AliyunOssConfigFields(config) { config = it }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 配置按钮
            Button(
                onClick = {
                    onConfigSaved(config)
                    navController.navigateUp()
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF2196F3)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = "配置",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 扫描并迁移资源到S3按钮
            OutlinedButton(
                onClick = { /* TODO: 实现扫描迁移功能 */ },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color(0xFF2196F3)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = "扫描并迁移资源到S3",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

/**
 * 云服务提供商选择器
 */
@Composable
private fun CloudProviderSelector(
    selectedProvider: CloudProvider,
    onProviderSelected: (CloudProvider) -> Unit
) {
    Column {
        Text(
            text = "云服务提供商",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        CloudProvider.values().forEach { provider ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedProvider == provider,
                    onClick = { onProviderSelected(provider) }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = provider.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = provider.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * AWS S3配置字段
 */
@Composable
private fun AwsS3ConfigFields(
    config: CloudStorageConfigData,
    onConfigChange: (CloudStorageConfigData) -> Unit
) {
    var showSecretKey by remember { mutableStateOf(false) }
    
    Column {
        // 区域节点 (Endpoint)
        OutlinedTextField(
            value = config.endpoint,
            onValueChange = { onConfigChange(config.copy(endpoint = it)) },
            label = { Text("区域节点(Endpoint)") },
            placeholder = { Text("格式不要以 http 开头，只需要填写对应域名即可，如s3.oss-beijing.aliyuncs.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 存储桶 (Bucket)
        OutlinedTextField(
            value = config.bucketName,
            onValueChange = { onConfigChange(config.copy(bucketName = it)) },
            label = { Text("存储桶(Bucket)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 访问密钥 (accessKeyId)
        OutlinedTextField(
            value = config.accessKeyId,
            onValueChange = { onConfigChange(config.copy(accessKeyId = it)) },
            label = { Text("访问密钥(accessKeyId)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 密钥 (secretAccessKey)
        OutlinedTextField(
            value = config.secretAccessKey,
            onValueChange = { onConfigChange(config.copy(secretAccessKey = it)) },
            label = { Text("密钥(secretAccessKey)") },
            modifier = Modifier.fillMaxWidth(),
            visualTransformation = if (showSecretKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showSecretKey = !showSecretKey }) {
                    Icon(
                        imageVector = if (showSecretKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showSecretKey) "隐藏密码" else "显示密码"
                    )
                }
            },
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 地区
        OutlinedTextField(
            value = config.region,
            onValueChange = { onConfigChange(config.copy(region = it)) },
            label = { Text("地区") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
    }
}

/**
 * 七牛云配置字段
 */
@Composable
private fun QiniuCloudConfigFields(
    config: CloudStorageConfigData,
    onConfigChange: (CloudStorageConfigData) -> Unit
) {
    var showSecretKey by remember { mutableStateOf(false) }

    Column {
        // 区域节点 (Endpoint)
        OutlinedTextField(
            value = config.endpoint,
            onValueChange = { onConfigChange(config.copy(endpoint = it)) },
            label = { Text("区域节点(Endpoint)") },
            placeholder = { Text("如：s3-cn-east-1.qiniucs.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 存储空间 (Bucket)
        OutlinedTextField(
            value = config.bucketName,
            onValueChange = { onConfigChange(config.copy(bucketName = it)) },
            label = { Text("存储空间(Bucket)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // AccessKey
        OutlinedTextField(
            value = config.accessKeyId,
            onValueChange = { onConfigChange(config.copy(accessKeyId = it)) },
            label = { Text("AccessKey") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // SecretKey
        OutlinedTextField(
            value = config.secretAccessKey,
            onValueChange = { onConfigChange(config.copy(secretAccessKey = it)) },
            label = { Text("SecretKey") },
            modifier = Modifier.fillMaxWidth(),
            visualTransformation = if (showSecretKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showSecretKey = !showSecretKey }) {
                    Icon(
                        imageVector = if (showSecretKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showSecretKey) "隐藏密码" else "显示密码"
                    )
                }
            },
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 自定义域名
        OutlinedTextField(
            value = config.customDomain,
            onValueChange = { onConfigChange(config.copy(customDomain = it)) },
            label = { Text("自定义域名(可选)") },
            placeholder = { Text("如：cdn.example.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 地区
        OutlinedTextField(
            value = config.region,
            onValueChange = { onConfigChange(config.copy(region = it)) },
            label = { Text("地区") },
            placeholder = { Text("如：cn-east-1") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
    }
}

/**
 * 腾讯云配置字段
 */
@Composable
private fun TencentCloudConfigFields(
    config: CloudStorageConfigData,
    onConfigChange: (CloudStorageConfigData) -> Unit
) {
    var showSecretKey by remember { mutableStateOf(false) }

    Column {
        // 区域节点 (Endpoint)
        OutlinedTextField(
            value = config.endpoint,
            onValueChange = { onConfigChange(config.copy(endpoint = it)) },
            label = { Text("区域节点(Endpoint)") },
            placeholder = { Text("如：cos.ap-beijing.myqcloud.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 存储桶 (Bucket)
        OutlinedTextField(
            value = config.bucketName,
            onValueChange = { onConfigChange(config.copy(bucketName = it)) },
            label = { Text("存储桶(Bucket)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // SecretId
        OutlinedTextField(
            value = config.accessKeyId,
            onValueChange = { onConfigChange(config.copy(accessKeyId = it)) },
            label = { Text("SecretId") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // SecretKey
        OutlinedTextField(
            value = config.secretAccessKey,
            onValueChange = { onConfigChange(config.copy(secretAccessKey = it)) },
            label = { Text("SecretKey") },
            modifier = Modifier.fillMaxWidth(),
            visualTransformation = if (showSecretKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showSecretKey = !showSecretKey }) {
                    Icon(
                        imageVector = if (showSecretKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showSecretKey) "隐藏密码" else "显示密码"
                    )
                }
            },
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 地区
        OutlinedTextField(
            value = config.region,
            onValueChange = { onConfigChange(config.copy(region = it)) },
            label = { Text("地区") },
            placeholder = { Text("如：ap-beijing") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
    }
}

/**
 * 阿里云OSS配置字段
 */
@Composable
private fun AliyunOssConfigFields(
    config: CloudStorageConfigData,
    onConfigChange: (CloudStorageConfigData) -> Unit
) {
    var showSecretKey by remember { mutableStateOf(false) }

    Column {
        // 区域节点 (Endpoint)
        OutlinedTextField(
            value = config.endpoint,
            onValueChange = { onConfigChange(config.copy(endpoint = it)) },
            label = { Text("区域节点(Endpoint)") },
            placeholder = { Text("如：oss-cn-hangzhou.aliyuncs.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 存储桶 (Bucket)
        OutlinedTextField(
            value = config.bucketName,
            onValueChange = { onConfigChange(config.copy(bucketName = it)) },
            label = { Text("存储桶(Bucket)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // AccessKeyId
        OutlinedTextField(
            value = config.accessKeyId,
            onValueChange = { onConfigChange(config.copy(accessKeyId = it)) },
            label = { Text("AccessKeyId") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // AccessKeySecret
        OutlinedTextField(
            value = config.secretAccessKey,
            onValueChange = { onConfigChange(config.copy(secretAccessKey = it)) },
            label = { Text("AccessKeySecret") },
            modifier = Modifier.fillMaxWidth(),
            visualTransformation = if (showSecretKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showSecretKey = !showSecretKey }) {
                    Icon(
                        imageVector = if (showSecretKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showSecretKey) "隐藏密码" else "显示密码"
                    )
                }
            },
            singleLine = true
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 地区
        OutlinedTextField(
            value = config.region,
            onValueChange = { onConfigChange(config.copy(region = it)) },
            label = { Text("地区") },
            placeholder = { Text("如：cn-hangzhou") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
    }
}

/**
 * 获取默认区域
 */
private fun getDefaultRegion(provider: CloudProvider): String {
    return when (provider) {
        CloudProvider.AWS_S3 -> "us-east-1"
        CloudProvider.QINIU_CLOUD -> "cn-east-1"
        CloudProvider.TENCENT_CLOUD -> "ap-beijing"
        CloudProvider.ALIYUN_OSS -> "cn-hangzhou"
    }
}

/**
 * 获取默认端点
 */
private fun getDefaultEndpoint(provider: CloudProvider): String {
    return when (provider) {
        CloudProvider.AWS_S3 -> "s3.amazonaws.com"
        CloudProvider.QINIU_CLOUD -> "s3-cn-east-1.qiniucs.com"
        CloudProvider.TENCENT_CLOUD -> "cos.ap-beijing.myqcloud.com"
        CloudProvider.ALIYUN_OSS -> "oss-cn-hangzhou.aliyuncs.com"
    }
}
