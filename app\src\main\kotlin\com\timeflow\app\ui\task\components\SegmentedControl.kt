package com.timeflow.app.ui.task.components

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex

/**
 * iOS风格分段控制器
 *
 * @param items 选项列表
 * @param selectedIndex 当前选中索引
 * @param onSelectionChanged 选择变更回调
 * @param modifier 修饰符
 */
@Composable
fun SegmentedControl(
    items: List<String>,
    selectedIndex: Int,
    onSelectionChanged: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 记录每个项的宽度
    var itemWidths by remember { mutableStateOf(List(items.size) { 0.dp }) }
    var containerWidth by remember { mutableStateOf(0.dp) }
    
    // 计算选择指示器的位置和宽度
    val indicatorStartPosition = remember(selectedIndex, itemWidths) {
        if (selectedIndex > 0) {
            itemWidths.subList(0, selectedIndex).sumOf { it.value.toDouble() }.dp
        } else {
            0.dp
        }
    }
    
    val indicatorWidth = remember(selectedIndex, itemWidths) {
        if (selectedIndex >= 0 && selectedIndex < itemWidths.size) {
            itemWidths[selectedIndex]
        } else {
            0.dp
        }
    }
    
    // 动画
    val animatedPosition by animateDpAsState(targetValue = indicatorStartPosition, label = "position")
    val animatedWidth by animateDpAsState(targetValue = indicatorWidth, label = "width")
    
    Surface(
        color = MaterialTheme.colorScheme.surfaceVariant,
        shape = RoundedCornerShape(10.dp),
        modifier = modifier
            .clip(RoundedCornerShape(10.dp))
            .height(40.dp)
            .onSizeChanged {
                containerWidth = with(density) { it.width.toDp() }
            }
    ) {
        Box(
            contentAlignment = Alignment.CenterStart
        ) {
            // 选择指示器背景
            if (animatedWidth > 0.dp) {
                Box(
                    modifier = Modifier
                        .offset(x = animatedPosition)
                        .width(animatedWidth)
                        .fillMaxHeight()
                        .padding(2.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(MaterialTheme.colorScheme.primary)
                        .zIndex(1f)
                )
            }
            
            // 选项
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .padding(2.dp)
            ) {
                items.forEachIndexed { index, item ->
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .clip(RoundedCornerShape(8.dp))
                            .clickable {
                                onSelectionChanged(index)
                            }
                            .onSizeChanged {
                                val newItemWidths = itemWidths.toMutableList()
                                newItemWidths[index] = with(density) { it.width.toDp() }
                                itemWidths = newItemWidths
                            }
                            .zIndex(2f)
                    ) {
                        Text(
                            text = item,
                            color = if (selectedIndex == index) Color.White else MaterialTheme.colorScheme.onSurface,
                            fontWeight = if (selectedIndex == index) FontWeight.SemiBold else FontWeight.Normal,
                            textAlign = TextAlign.Center,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
} 