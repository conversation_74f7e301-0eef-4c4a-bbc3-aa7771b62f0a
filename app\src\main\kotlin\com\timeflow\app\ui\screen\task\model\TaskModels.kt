package com.timeflow.app.ui.screen.task.model

import java.time.LocalDateTime
import java.util.UUID
import androidx.compose.ui.graphics.Color
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.data.model.Priority

data class TaskModel(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val daysLeft: Int,
    val tag: String? = null,
    val isCompleted: Boolean = false,
    val urgency: TaskUrgency = TaskUrgency.MEDIUM,
    val customTags: List<String> = emptyList(),
    val subTasks: List<SubTask> = emptyList(),
    val parentTaskId: String? = null,
    val displayInTaskList: Boolean = true,
    val feedback: FeedbackData? = null,
    val dueDate: LocalDateTime? = null,
    val type: String = "NORMAL",
    val aiGenerated: Boolean = false,
    
    // 新增：关联的目标信息
    val goalId: String? = null,
    val goalTitle: String? = null,
    
    // 🎨 Done List功能：完成时间戳
    val completedAt: Long? = null
)

enum class TaskUrgency {
    CRITICAL, HIGH, MEDIUM, LOW
}

/**
 * 紧急度颜色定义
 */
object UrgencyColors {
    val Critical = Color(0xFFB74645) // 红色 - 紧急
    val High = Color(0xFFFF8247)     // 橙色 - 高优先级
    val Medium = Color(0xFFd9b335)    // 黄色 - 中优先级
    val Low = Color(0xFFA8C986)       // 绿色 - 低优先级
}

/**
 * 获取紧急度对应的颜色
 */
fun getUrgencyColor(urgency: TaskUrgency): Color {
    return when (urgency) {
        TaskUrgency.CRITICAL -> UrgencyColors.Critical
        TaskUrgency.HIGH -> UrgencyColors.High
        TaskUrgency.MEDIUM -> UrgencyColors.Medium
        TaskUrgency.LOW -> UrgencyColors.Low
    }
}

// 格式化剩余时间显示
fun formatTimeLeft(daysLeft: Int): String {
    return when {
        daysLeft == 0 -> "今天"
        daysLeft == 1 -> "明天"
        daysLeft > 0 -> "剩余${daysLeft}天"
        else -> "已过期"
    }
}

/**
 * 格式化剩余时间为详细格式，包含具体时间段
 * 例如: "后天，3月31日, 17:25 - 18:25" 或跨天时 "今天，6月5日, 23:00 - 明天，6月6日, 00:00"
 */
fun formatDetailedTimeRange(dueDate: LocalDateTime?): String {
    if (dueDate == null) return ""

    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val inputDate = dueDate.toLocalDate()
    val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endDateTime = dueDate.plusHours(1)
    val endTime = endDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endDate = endDateTime.toLocalDate()

    // 检查是否跨天
    val isCrossDay = !inputDate.isEqual(endDate)

    return when {
        // 今天
        inputDate.isEqual(today) -> {
            if (isCrossDay) {
                "今天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - 明天，${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                "今天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 明天
        inputDate.isEqual(today.plusDays(1)) -> {
            if (isCrossDay) {
                "明天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - 后天，${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                "明天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 后天
        inputDate.isEqual(today.plusDays(2)) -> {
            if (isCrossDay) {
                val dayAfterTomorrow = today.plusDays(3)
                val endDayDesc = if (endDate.isEqual(dayAfterTomorrow)) "大后天" else endDate.format(DateTimeFormatter.ofPattern("M月d日"))
                "后天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endDayDesc，${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                "后天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 本周内 (显示星期几)
        ChronoUnit.DAYS.between(today, inputDate) < 7 -> {
            val dayOfWeek = when (inputDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            if (isCrossDay) {
                val endDayOfWeek = when (endDate.dayOfWeek.value) {
                    1 -> "周一"
                    2 -> "周二"
                    3 -> "周三"
                    4 -> "周四"
                    5 -> "周五"
                    6 -> "周六"
                    7 -> "周日"
                    else -> ""
                }
                "$dayOfWeek，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endDayOfWeek，${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                "$dayOfWeek，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 本年内 (显示月日)
        inputDate.year == today.year -> {
            if (isCrossDay) {
                "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - ${endDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $endTime"
            } else {
                "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
            }
        }
        // 其他年份 (显示年月日)
        else -> {
            if (isCrossDay) {
                "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - ${endDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $endTime"
            } else {
                "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - $endTime"
            }
        }
    }
}

// 检查所有子任务是否已完成
fun areAllSubTasksCompleted(subTasks: List<SubTask>): Boolean {
    return subTasks.isNotEmpty() && subTasks.all { it.isCompleted }
}

// 将SubTask转换为TaskData用于显示
fun convertSubTaskToTaskData(subTask: SubTask): TaskModel {
    val daysLeft = if (subTask.dueDate != null) {
        val now = LocalDateTime.now()
        val days = now.until(subTask.dueDate, java.time.temporal.ChronoUnit.DAYS).toInt()
        if (days < 0) 0 else days
    } else {
        0
    }
    
    val urgency = when(subTask.priority) {
        Priority.HIGH -> TaskUrgency.HIGH
        Priority.MEDIUM -> TaskUrgency.MEDIUM
        Priority.LOW -> TaskUrgency.LOW
        Priority.URGENT -> TaskUrgency.CRITICAL
        else -> TaskUrgency.MEDIUM
    }
    
    return TaskModel(
        id = subTask.id,
        title = subTask.title,
        description = subTask.note,
        daysLeft = daysLeft,
        isCompleted = subTask.isCompleted,
        urgency = urgency,
        parentTaskId = subTask.parentTaskId,
        displayInTaskList = false
    )
}

// 示例任务数据 - 这个函数将被替换为从TaskViewModel获取真实任务
fun getSampleTasks(): List<TaskModel> {
    // 由于这个函数是同步的，而我们需要从异步数据源获取数据
    // 所以这里只是返回一个空列表，实际数据将通过ViewModel获取
    return emptyList()
}

// 在实际应用中，通过TaskViewModel获取真实任务数据的辅助函数
fun getTasksFromViewModel(taskListState: com.timeflow.app.ui.task.TaskListState): List<TaskModel> {
    return taskListState.tasks
}

// 将daysLeft转换为LocalDateTime，用于格式化显示
fun convertDaysLeftToDateTime(daysLeft: Int): LocalDateTime {
    val now = LocalDateTime.now()
    return when {
        daysLeft <= 0 -> now // 已过期或今天
        else -> now.plusDays(daysLeft.toLong()) // 未来日期
    }
}

/**
 * 判断任务是否是今天的任务
 */
fun isTaskForToday(task: TaskModel): Boolean {
    // 如果daysLeft正好是0，或者dueDate的日期部分与今天相同
    if (task.daysLeft == 0) {
        return true
    }
    
    // 如果有具体的dueDate，再次检查是否是今天
    if (task.dueDate != null) {
        val today = LocalDateTime.now().toLocalDate()
        return task.dueDate.toLocalDate().isEqual(today)
    }
    
    return false
}

/**
 * 判断任务是否是明天的任务
 */
fun isTaskForTomorrow(task: TaskModel): Boolean {
    // 如果daysLeft正好是1
    if (task.daysLeft == 1) {
        return true
    }
    
    // 如果有具体的dueDate，检查是否是明天
    if (task.dueDate != null) {
        val tomorrow = LocalDateTime.now().plusDays(1).toLocalDate()
        return task.dueDate.toLocalDate().isEqual(tomorrow)
    }
    
    return false
}

/**
 * 判断任务是否是本周的任务
 */
fun isTaskForThisWeek(task: TaskModel): Boolean {
    // 如果daysLeft在1到7之间（不含今天）
    if (task.daysLeft > 0 && task.daysLeft <= 7) {
        return true
    }
    
    // 如果有具体的dueDate，检查是否在本周内
    if (task.dueDate != null) {
        val today = LocalDateTime.now().toLocalDate()
        val thisWeekEnd = today.plusDays(7)
        val dueDate = task.dueDate.toLocalDate()
        return (dueDate.isAfter(today) || dueDate.isEqual(today)) && 
               (dueDate.isBefore(thisWeekEnd) || dueDate.isEqual(thisWeekEnd))
    }
    
    return false
}

/**
 * 判断任务是否是未定期的任务
 */
fun isTaskUpcoming(task: TaskModel): Boolean {
    return task.dueDate == null || task.daysLeft == Int.MAX_VALUE
} 