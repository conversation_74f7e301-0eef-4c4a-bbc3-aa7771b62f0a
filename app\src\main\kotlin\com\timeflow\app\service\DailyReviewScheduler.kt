package com.timeflow.app.service

import android.content.Context
import android.util.Log
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.timeflow.app.worker.DailyReviewWorker
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 每日回顾调度服务
 * 负责管理每日回顾提醒的调度和取消
 */
@Singleton
class DailyReviewScheduler @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "DailyReviewScheduler"
        private const val DAILY_REVIEW_WORK_NAME = "daily_review_reminder"
    }
    
    /**
     * 设置每日回顾提醒
     * @param reviewTime 回顾时间，格式为"HH:mm"，例如"21:55"
     */
    fun scheduleDailyReview(reviewTime: String) {
        try {
            Log.d(TAG, "开始设置每日回顾提醒，时间: $reviewTime")
            
            // 首先取消之前的工作
            cancelDailyReview()
            
            // 创建WorkManager请求数据
            val inputData = Data.Builder()
                .putString(DailyReviewWorker.KEY_REVIEW_TIME, reviewTime)
                .build()
            
            // 创建一次性工作请求
            val reminderRequest = OneTimeWorkRequestBuilder<DailyReviewWorker>()
                .setInputData(inputData)
                .addTag("daily_review_reminder")
                .build()
            
            // 使用唯一工作名称，确保只有一个每日回顾工作在运行
            WorkManager.getInstance(context).enqueueUniqueWork(
                DAILY_REVIEW_WORK_NAME,
                ExistingWorkPolicy.REPLACE, // 替换现有的工作
                reminderRequest
            )
            
            Log.d(TAG, "✅ 每日回顾提醒已设置: $reviewTime")
            
        } catch (e: Exception) {
            Log.e(TAG, "设置每日回顾提醒失败", e)
        }
    }
    
    /**
     * 取消每日回顾提醒
     */
    fun cancelDailyReview() {
        try {
            Log.d(TAG, "取消每日回顾提醒")
            
            // 取消WorkManager工作
            WorkManager.getInstance(context).cancelUniqueWork(DAILY_REVIEW_WORK_NAME)
            
            // 也可以通过标签取消
            WorkManager.getInstance(context).cancelAllWorkByTag("daily_review_reminder")
            
            // 取消系统闹钟（如果有的话）
            cancelSystemAlarms()
            
            Log.d(TAG, "✅ 每日回顾提醒已取消")
            
        } catch (e: Exception) {
            Log.e(TAG, "取消每日回顾提醒失败", e)
        }
    }
    
    /**
     * 启用每日回顾提醒
     * 根据当前设置的时间重新启动提醒
     */
    fun enableDailyReview(reviewTime: String) {
        Log.d(TAG, "启用每日回顾提醒: $reviewTime")
        scheduleDailyReview(reviewTime)
    }
    
    /**
     * 禁用每日回顾提醒
     */
    fun disableDailyReview() {
        Log.d(TAG, "禁用每日回顾提醒")
        cancelDailyReview()
    }
    
    /**
     * 检查每日回顾提醒是否已设置
     */
    fun isDailyReviewScheduled(): Boolean {
        return try {
            val workInfos = WorkManager.getInstance(context)
                .getWorkInfosForUniqueWork(DAILY_REVIEW_WORK_NAME)
                .get()
            
            workInfos.any { workInfo ->
                workInfo.state == androidx.work.WorkInfo.State.ENQUEUED ||
                workInfo.state == androidx.work.WorkInfo.State.RUNNING
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查每日回顾提醒状态失败", e)
            false
        }
    }
    
    /**
     * 获取当前设置的回顾时间
     * 注意：这个方法只能返回WorkManager中设置的时间，
     * 实际的用户设置时间应该从DataStore中读取
     */
    fun getCurrentReviewTime(): String? {
        return try {
            // 由于WorkInfo的inputData访问较复杂，这里暂时返回null
            // 实际的回顾时间应该从NotificationSettings的DataStore中读取
            Log.d(TAG, "getCurrentReviewTime() - 建议从DataStore读取用户设置")
            null
        } catch (e: Exception) {
            Log.e(TAG, "获取当前回顾时间失败", e)
            null
        }
    }
    
    /**
     * 取消系统闹钟
     */
    private fun cancelSystemAlarms() {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            
            // 创建与DailyReviewWorker中相同的Intent和PendingIntent
            val intent = android.content.Intent(context, com.timeflow.app.receiver.DailyReviewAlarmReceiver::class.java)
            
            val pendingIntentFlags = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
            } else {
                android.app.PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val pendingIntent = android.app.PendingIntent.getBroadcast(
                context,
                com.timeflow.app.worker.DailyReviewWorker.DAILY_REVIEW_REQUEST_CODE,
                intent,
                pendingIntentFlags
            )
            
            // 取消闹钟
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()
            
            Log.d(TAG, "✅ 系统闹钟已取消")
            
        } catch (e: Exception) {
            Log.e(TAG, "取消系统闹钟失败", e)
        }
    }
} 