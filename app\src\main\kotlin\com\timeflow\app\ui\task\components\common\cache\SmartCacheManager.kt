package com.timeflow.app.ui.task.components.common.cache

import android.content.Context
import android.util.Log
import android.util.LruCache
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.google.gson.internal.LinkedTreeMap
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * 智能缓存管理器：提供三级缓存机制
 * 1. 内存缓存：使用 LruCache 实现，用于快速访问
 * 2. 磁盘缓存：将数据序列化到文件，用于持久化存储
 * 3. Flow 缓存：使用 StateFlow 提供响应式数据流，用于 UI 更新
 */
class SmartCacheManager(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
    private val maxMemoryEntries: Int = 100,
    private val diskCacheExpireHours: Int = 24
) {
    private val TAG = "SmartCacheManager"
    
    // 内存缓存
    private val memoryCache = LruCache<String, CacheEntry<Any>>(maxMemoryEntries)
    
    // Flow缓存
    private val flowCache = ConcurrentHashMap<String, MutableStateFlow<Any?>>()
    
    // 访问锁，确保并发安全
    private val mutex = Mutex()
    
    // 缓存统计信息
    private val _statistics = MutableStateFlow(CacheStatistics())
    val statistics: StateFlow<CacheStatistics> = _statistics.asStateFlow()
    
    // 磁盘缓存目录
    private val cacheDir: File by lazy {
        File(context.cacheDir, "smart_cache").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    // 定期清理过期磁盘缓存
    init {
        coroutineScope.launch {
            while (true) {
                cleanupExpiredCache()
                kotlinx.coroutines.delay(TimeUnit.HOURS.toMillis(1))
            }
        }
    }
    
    /**
     * 获取数据：先查内存缓存，再查磁盘缓存，最后执行加载器
     * @param key 缓存键
     * @param forceRefresh 是否强制刷新
     * @param loader 数据加载器函数
     * @return 缓存或新加载的数据
     */
    suspend fun <T : Any> getData(
        key: String,
        forceRefresh: Boolean = false,
        loader: suspend () -> T?
    ): T? = mutex.withLock {
        try {
            // 如果强制刷新，跳过缓存
            if (!forceRefresh) {
                // 1. 先查内存缓存
                memoryCache.get(key)?.let { entry ->
                    if (!entry.isExpired()) {
                        Log.d(TAG, "内存缓存命中: $key")
                        updateStatistics { it.copy(memoryHits = it.memoryHits + 1) }
                        @Suppress("UNCHECKED_CAST")
                        return entry.data as? T
                    }
                }
                
                // 2. 再查磁盘缓存
                loadFromDiskCache<T>(key)?.let { data ->
                    Log.d(TAG, "磁盘缓存命中: $key")
                    updateStatistics { it.copy(diskHits = it.diskHits + 1) }
                    
                    // 内存缓存未命中但磁盘缓存命中，更新内存缓存
                    saveToMemoryCache(key, data)
                    
                    return data
                }
            }
            
            // 3. 缓存未命中或强制刷新，执行加载器
            updateStatistics { it.copy(misses = it.misses + 1) }
            val newData = loader()
            
            // 4. 如果加载到数据，更新缓存
            if (newData != null) {
                saveToMemoryCache(key, newData)
                saveToDiskCache(key, newData)
                updateFlowCache(key, newData)
                Log.d(TAG, "数据已加载并缓存: $key")
            } else {
                Log.d(TAG, "未加载到数据: $key")
            }
            
            return newData
        } catch (e: Exception) {
            Log.e(TAG, "getData异常: $key", e)
            updateStatistics { it.copy(errors = it.errors + 1) }
            throw e
        }
    }
    
    /**
     * 观察数据变化：返回对应键的数据流
     * @param key 缓存键
     * @return 数据流
     */
    fun <T : Any> observeData(key: String): Flow<T?> {
        return getOrCreateFlow<T>(key)
    }
    
    /**
     * 使缓存失效：删除指定键的缓存
     * @param key 缓存键
     */
    suspend fun invalidate(key: String) = mutex.withLock {
        Log.d(TAG, "使缓存失效: $key")
        
        // 删除内存缓存
        memoryCache.remove(key)
        
        // 删除磁盘缓存
        val cacheFile = File(cacheDir, getCacheFileName(key))
        if (cacheFile.exists()) {
            cacheFile.delete()
        }
        
        // 更新Flow（不删除Flow本身，只发送空值）
        flowCache[key]?.value = null
        
        updateStatistics { it.copy(invalidations = it.invalidations + 1) }
    }
    
    /**
     * 清空所有缓存
     */
    suspend fun clearAll() = mutex.withLock {
        Log.d(TAG, "清空所有缓存")
        
        // 清空内存缓存
        memoryCache.evictAll()
        
        // 清空磁盘缓存
        cacheDir.listFiles()?.forEach { it.delete() }
        
        // 清空Flow缓存（发送null值，但不删除Flow本身）
        flowCache.forEach { (_, flow) -> flow.value = null }
        
        // 重置统计信息
        _statistics.value = CacheStatistics()
    }
    
    /**
     * 获取或创建数据流
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T> getOrCreateFlow(key: String): Flow<T?> {
        return (flowCache.getOrPut(key) { 
            MutableStateFlow(null) 
        } as MutableStateFlow<T?>).asStateFlow()
    }
    
    /**
     * 更新Flow缓存
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T : Any> updateFlowCache(key: String, data: T) {
        val flow = flowCache.getOrPut(key) { MutableStateFlow(null) }
        flow.value = data
    }
    
    /**
     * 保存到内存缓存
     */
    private fun <T : Any> saveToMemoryCache(key: String, data: T) {
        try {
            // 检查类型兼容性，确保可以安全存储
            if (data is Collection<*> || data is Map<*, *>) {
                Log.d(TAG, "保存内存缓存: $key (集合类型)")
            } else {
                Log.d(TAG, "保存内存缓存: $key (${data::class.java.simpleName}类型)")
            }
            
            val entry = CacheEntry<Any>(data as Any, System.currentTimeMillis())
            memoryCache.put(key, entry)
        } catch (e: Exception) {
            Log.e(TAG, "保存内存缓存失败: $key", e)
        }
    }
    
    /**
     * 保存到磁盘缓存
     */
    private fun <T : Any> saveToDiskCache(key: String, data: T) {
        coroutineScope.launch(Dispatchers.IO) {
            try {
                // 跳过任务相关数据的缓存，以避免类型转换问题
                if (key.contains("task", ignoreCase = true) || 
                    key.contains("subtask", ignoreCase = true) ||
                    key == "all_tasks" ||
                    key == "kanban_columns" ||
                    key == "task_groups") {
                    
                    Log.d(TAG, "跳过任务相关数据的磁盘缓存: $key")
                    return@launch
                }
                
                // 复杂类型检查，对于KanbanColumn等自定义类，避免序列化问题
                val safeToCache = when (data) {
                    is String, is Number, is Boolean -> true
                    is Collection<*>, is Map<*, *> -> {
                        // 集合类型需要检查其元素
                        // 这里简化处理，实际应用可能需要更复杂的类型检查
                        Log.d(TAG, "集合类型数据缓存: $key")
                        false // 暂时不缓存复杂集合类型
                    }
                    else -> {
                        // 检查是否为自定义类型
                        val className = data::class.java.name
                        val isSafeType = className.startsWith("java.") || 
                                        className.startsWith("kotlin.") ||
                                        className.startsWith("android.")
                        
                        if (!isSafeType) {
                            Log.d(TAG, "自定义类型数据暂不缓存: $key ($className)")
                        }
                        
                        isSafeType
                    }
                }
                
                if (!safeToCache) {
                    Log.d(TAG, "跳过不安全类型的磁盘缓存: $key")
                    return@launch
                }
                
                val entry = DiskCacheEntry(
                    data = data,
                    timestamp = System.currentTimeMillis(),
                    type = data::class.java.name
                )
                
                val json = Gson().toJson(entry)
                val cacheFile = File(cacheDir, getCacheFileName(key))
                cacheFile.writeText(json)
                Log.d(TAG, "数据已保存到磁盘缓存: $key")
            } catch (e: Exception) {
                Log.e(TAG, "保存磁盘缓存失败: $key", e)
            }
        }
    }
    
    /**
     * 从磁盘缓存加载数据
     * 修复类型转换问题 - 处理LinkedTreeMap到实体类的转换
     */
    @Suppress("UNCHECKED_CAST")
    private suspend fun <T : Any> loadFromDiskCache(key: String): T? = withContext(Dispatchers.IO) {
        try {
            val cacheFile = File(cacheDir, getCacheFileName(key))
            if (!cacheFile.exists()) {
                return@withContext null
            }
            
            val json = cacheFile.readText()
            val gson = Gson()
            val type = object : TypeToken<DiskCacheEntry<Any>>() {}.type
            val entry = gson.fromJson<DiskCacheEntry<Any>>(json, type)
            
            if (entry.isExpired(TimeUnit.HOURS.toMillis(diskCacheExpireHours.toLong()))) {
                Log.d(TAG, "磁盘缓存已过期: $key")
                cacheFile.delete()
                return@withContext null
            }
            
            // 检查是否是LinkedTreeMap，需要额外处理
            val data = entry.data
            if (data is LinkedTreeMap<*, *>) {
                // 判断key是否与Task相关，需要特殊处理
                if (key.contains("task", ignoreCase = true) || 
                    key.contains("subtask", ignoreCase = true) ||
                    key == "all_tasks") {
                    
                    Log.d(TAG, "检测到Task相关数据需要特殊处理: $key")
                    // 这里返回null，强制重新从源加载数据
                    // 这样可以避免类型转换错误
                    return@withContext null
                }
            }
            
            return@withContext entry.data as? T
        } catch (e: Exception) {
            Log.e(TAG, "从磁盘缓存加载失败: $key", e)
            // 加载失败时删除可能损坏的缓存文件
            val cacheFile = File(cacheDir, getCacheFileName(key))
            if (cacheFile.exists()) {
                cacheFile.delete()
            }
            return@withContext null
        }
    }
    
    /**
     * 清理过期的磁盘缓存
     */
    private suspend fun cleanupExpiredCache() = withContext(Dispatchers.IO) {
        try {
            val expireTime = TimeUnit.HOURS.toMillis(diskCacheExpireHours.toLong())
            val now = System.currentTimeMillis()
            var cleanedCount = 0
            
            cacheDir.listFiles()?.forEach { file ->
                try {
                    val json = file.readText()
                    val typeToken = object : TypeToken<DiskCacheEntry<Any>>() {}.type
                    val entry = Gson().fromJson<DiskCacheEntry<Any>>(json, typeToken)
                    
                    if (now - entry.timestamp > expireTime) {
                        file.delete()
                        cleanedCount++
                    }
                } catch (e: Exception) {
                    // 如果解析失败，删除可能损坏的缓存文件
                    file.delete()
                    cleanedCount++
                }
            }
            
            if (cleanedCount > 0) {
                Log.d(TAG, "已清理 $cleanedCount 个过期缓存文件")
            } else {
                // 即使没有清理文件也记录日志
                Log.d(TAG, "没有需要清理的过期缓存文件")
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理过期缓存失败", e)
        }
    }
    
    /**
     * 获取缓存文件名
     */
    private fun getCacheFileName(key: String): String {
        return key.hashCode().toString()
    }
    
    /**
     * 更新统计信息
     */
    private fun updateStatistics(update: (CacheStatistics) -> CacheStatistics) {
        _statistics.value = update(_statistics.value)
    }
    
    /**
     * 内存缓存项
     */
    private data class CacheEntry<T>(
        val data: T,
        val timestamp: Long,
        val expireTime: Long = TimeUnit.MINUTES.toMillis(30)
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() - timestamp > expireTime
    }
    
    /**
     * 磁盘缓存项
     */
    private data class DiskCacheEntry<T>(
        val data: T,
        val timestamp: Long,
        val type: String
    ) {
        fun isExpired(expireTime: Long): Boolean = System.currentTimeMillis() - timestamp > expireTime
    }
}

/**
 * 缓存统计信息
 */
data class CacheStatistics(
    val memoryHits: Int = 0,
    val diskHits: Int = 0,
    val misses: Int = 0,
    val errors: Int = 0,
    val invalidations: Int = 0
) {
    val totalRequests: Int get() = memoryHits + diskHits + misses
    val hitRate: Float get() = if (totalRequests > 0) (memoryHits + diskHits).toFloat() / totalRequests else 0f
} 