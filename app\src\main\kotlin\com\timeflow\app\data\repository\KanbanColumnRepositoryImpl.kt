package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.KanbanColumnDao
import com.timeflow.app.data.entity.KanbanColumn
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 看板列仓库实现类
 */
@Singleton
class KanbanColumnRepositoryImpl @Inject constructor(
    private val kanbanColumnDao: KanbanColumnDao
) : KanbanColumnRepository {
    
    override suspend fun getColumnsByBoardId(boardId: String): List<KanbanColumn> {
        return kanbanColumnDao.getColumnsByBoardId(boardId)
    }
    
    override fun observeColumnsByBoardId(boardId: String): Flow<List<KanbanColumn>> {
        return kanbanColumnDao.observeColumnsByBoardId(boardId)
    }
    
    override suspend fun getColumnById(columnId: String): KanbanColumn? {
        return kanbanColumnDao.getColumnById(columnId)
    }
    
    override fun observeColumnById(columnId: String): Flow<KanbanColumn?> {
        return kanbanColumnDao.observeColumnById(columnId)
    }
    
    override suspend fun insertColumn(column: KanbanColumn) {
        kanbanColumnDao.insert(column)
    }
    
    override suspend fun insertColumns(columns: List<KanbanColumn>) {
        kanbanColumnDao.insertAll(columns)
    }
    
    override suspend fun updateColumn(column: KanbanColumn) {
        kanbanColumnDao.update(column)
    }
    
    override suspend fun deleteColumn(column: KanbanColumn) {
        kanbanColumnDao.delete(column)
    }
    
    override suspend fun getMaxPositionForBoard(boardId: String): Int {
        return kanbanColumnDao.getMaxPositionForBoard(boardId) ?: -1
    }
} 