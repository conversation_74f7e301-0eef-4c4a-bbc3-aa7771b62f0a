package com.timeflow.app.data.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * 目标模板数据类
 * 用于保存预定义的目标模板，方便快速创建相似目标
 */
data class GoalTemplate(
    val id: String = UUID.randomUUID().toString(),
    
    // 基本信息
    val name: String,                               // 模板名称
    val description: String = "",                   // 模板描述
    val category: String = "",                      // 模板分类（保留兼容）
    val categoryId: String = "personal_development", // 新的分类ID
    val iconName: String = "target",                // 图标名称
    val colorHex: String = "#9370DB",               // 模板颜色(默认为DustyLavender)
    
    // 使用统计
    val usageCount: Int = 0,                        // 使用次数
    val lastUsed: LocalDateTime? = null,            // 上次使用时间
    val createdAt: LocalDateTime = LocalDateTime.now(), // 创建时间
    
    // 目标默认信息
    val defaultTitle: String = "",                  // 默认标题
    val defaultDescription: String = "",            // 默认描述
    val defaultPriority: GoalPriority = GoalPriority.MEDIUM, // 默认优先级
    val defaultTags: List<String> = emptyList(),    // 默认标签
    
    // 时长设置
    val defaultDurationDays: Int? = null,           // 默认持续时间(天)
    
    // 量化设置
    val goalType: GoalType = GoalType.BOOLEAN,      // 目标类型
    val defaultTargetValue: Double? = null,         // 默认目标值
    val defaultUnit: String? = null,                // 默认单位
    
    // 高级设置
    val isRecurring: Boolean = false,               // 是否为周期性目标模板
    val recurringSettings: RecurringSettings? = null, // 周期性设置
    val defaultReminderSettings: List<ReminderSetting> = emptyList(), // 默认提醒设置
    
    // 子任务模板
    val subTaskTemplates: List<GoalSubTaskTemplate> = emptyList() // 子任务模板列表
)

/**
 * 目标子任务模板数据类
 */
data class GoalSubTaskTemplate(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val estimatedDurationDays: Int = 0,
    val orderIndex: Int = 0 // 用于确定子任务的顺序
)

/**
 * 模板分类枚举
 */
enum class TemplateCategory(val displayName: String) {
    STUDY("学习"),
    LEARNING("学习进修"),
    WORK("工作"),
    EXERCISE("健身"),
    READING("阅读"),
    HEALTH("健康"),
    FINANCE("财务"),
    CAREER("职业"),
    CREATIVE("创意"),
    PERSONAL("个人成长"),
    LIFESTYLE("生活方式"),
    OTHER("其他")
} 