package com.timeflow.app.ui.task.model

import com.timeflow.app.data.entity.Task

/**
 * 任务统计数据模型
 * 提供看板和任务列表的统计信息
 */
data class TasksStatistics(
    val total: Int = 0,
    val completed: Int = 0,
    val overdue: Int = 0,
    val upcoming: Int = 0,
    val inProgress: Int = 0,
    val completionRate: Float = 0f,
    val statusDistribution: Map<String, Int> = emptyMap()
) {
    companion object {
        /**
         * 从任务列表创建统计信息
         */
        fun fromTasks(tasks: List<Task>): TasksStatistics {
            if (tasks.isEmpty()) return TasksStatistics()
            
            val total = tasks.size
            val completed = tasks.count { task -> task.completedAt != null }
            val statusGroups = tasks.groupBy { it.status }
            val inProgress = statusGroups["进行中"]?.size ?: 0
            
            // 计算完成率
            val completionRate = if (total > 0) {
                completed.toFloat() / total.toFloat()
            } else {
                0f
            }
            
            // 统计各状态的任务数量
            val statusDistribution = statusGroups.mapValues { it.value.size }
            
            return TasksStatistics(
                total = total,
                completed = completed,
                inProgress = inProgress,
                overdue = 0, // 需要日期判断，简化实现
                upcoming = 0, // 需要日期判断，简化实现
                completionRate = completionRate,
                statusDistribution = statusDistribution
            )
        }
    }
} 