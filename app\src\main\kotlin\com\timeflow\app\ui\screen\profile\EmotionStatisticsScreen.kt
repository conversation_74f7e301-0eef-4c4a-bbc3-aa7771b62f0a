package com.timeflow.app.ui.screen.profile

import android.app.Activity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import android.util.Log
import kotlinx.coroutines.launch
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.foundation.Canvas
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import kotlin.math.min
import kotlin.math.max
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import kotlin.random.Random
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.foundation.BorderStroke
import java.time.ZoneId

/**
 * 情绪统计数据类
 */
data class EmotionStatistic(
    val type: String,
    val emoji: String,
    val color: Color,
    val count: Int,
    val percentage: Float
)

/**
 * 情绪记录数据类
 */
data class EmotionStatRecord(
    val date: LocalDate,
    val emotionType: String,
    val emoji: String,
    val color: Color,
    val note: String = "",
    val triggers: List<String> = emptyList()
)

/**
 * 情绪趋势数据类
 */
data class EmotionTrend(
    val period: String,
    val emotionCounts: Map<String, Int>,
    val dominantEmotion: String,
    val emotionChange: Float // 正值表示积极情绪增加，负值表示消极情绪增加
)

/**
 * AI分析结果数据类
 */
data class AIAnalysisResult(
    val patterns: List<String>,
    val insights: List<String>,
    val recommendations: List<String>
)

/**
 * 热力图数据点
 */
data class HeatmapDataPoint(
    val date: LocalDate,
    val count: Int,
    val intensity: Float // 0.0 到 1.0，表示强度
)

/**
 * 情绪热力图数据
 */
data class EmotionHeatmapData(
    val emotion: EmotionType,
    val dataPoints: List<HeatmapDataPoint>,
    val maxCount: Int
)

/**
 * 时间范围枚举
 */
enum class TimeRange(val displayName: String, val days: Int) {
    WEEK("本周", 7),
    MONTH("本月", 30),
    QUARTER("季度", 90),
    YEAR("年度", 365),
    ALL("全部", Int.MAX_VALUE)
}

/**
 * 统计页面状态
 */
data class EmotionStatisticsState(
    val isLoading: Boolean = false,
    val selectedTimeRange: TimeRange = TimeRange.MONTH,
    val emotionStats: List<EmotionStatistic> = emptyList(),
    val emotionRecords: List<EmotionStatRecord> = emptyList(),
    val emotionTrend: EmotionTrend? = null,
    val aiAnalysisResult: AIAnalysisResult? = null,
    val emotionHeatmapData: List<EmotionHeatmapData> = emptyList(), // 🔧 添加热力图数据
    val error: String? = null
)

/**
 * 情绪统计ViewModel
 */
@HiltViewModel
class EmotionStatisticsViewModel @Inject constructor(
    private val reflectionRepository: com.timeflow.app.ui.screen.reflection.ReflectionRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(EmotionStatisticsState())
    val uiState: StateFlow<EmotionStatisticsState> = _uiState.asStateFlow()
    
    // 存储从ProfileScreen传递过来的情绪记录
    private val _profileEmotionRecords = MutableStateFlow<List<com.timeflow.app.ui.screen.profile.EmotionRecord>>(emptyList())
    
    init {
        loadEmotionData(TimeRange.MONTH)
    }
    
    /**
     * 接收并处理从ProfileScreen传递过来的情绪记录
     */
    fun setEmotionRecords(records: List<com.timeflow.app.ui.screen.profile.EmotionRecord>) {
        _profileEmotionRecords.value = records
        // 将ProfileScreen的EmotionRecord转换为EmotionStatRecord
        processProfileEmotionRecords()
    }
    

    
    /**
     * 将感想记录转换为情绪统计记录
     */
    private fun convertReflectionsToEmotionRecords(
        reflections: List<com.timeflow.app.ui.screen.reflection.Reflection>, 
        timeRange: TimeRange
    ): List<EmotionStatRecord> {
        val now = java.time.LocalDate.now()
        val startDate = when (timeRange) {
            TimeRange.WEEK -> now.minusDays(7)
            TimeRange.MONTH -> now.minusDays(30)
            TimeRange.QUARTER -> now.minusDays(90)
            TimeRange.YEAR -> now.minusDays(365)
            TimeRange.ALL -> now.minusDays(3650) // 10年前
        }
        
        return reflections
            .filter { reflection ->
                // 转换Instant到LocalDate进行比较
                val reflectionDate = java.time.LocalDateTime.ofInstant(
                    reflection.date, 
                    java.time.ZoneId.systemDefault()
                ).toLocalDate()
                
                // 过滤时间范围内的记录
                !reflectionDate.isBefore(startDate) && !reflectionDate.isAfter(now)
            }
            .map { reflection ->
                // 转换感想记录为情绪统计记录
                EmotionStatRecord(
                    date = java.time.LocalDateTime.ofInstant(
                        reflection.date, 
                        java.time.ZoneId.systemDefault()
                    ).toLocalDate(),
                    emotionType = reflection.mood.displayName,
                    emoji = reflection.mood.emoji,
                    color = reflection.mood.color,
                    note = reflection.content,
                    triggers = reflection.tags
                )
            }
    }
    
    /**
     * 生成情绪热力图数据
     */
    private fun generateEmotionHeatmapData(
        records: List<EmotionStatRecord>,
        timeRange: TimeRange
    ): List<EmotionHeatmapData> {
        val now = LocalDate.now()
        val startDate = when (timeRange) {
            TimeRange.WEEK -> now.minusDays(7)
            TimeRange.MONTH -> now.minusDays(30)
            TimeRange.QUARTER -> now.minusDays(90)
            TimeRange.YEAR -> now.minusDays(365)
            TimeRange.ALL -> now.minusDays(3650) // 10年前作为"全部"的起点
        }

        // 按情绪类型分组
        val emotionGroups = records.groupBy { it.emotionType }

        return emotionGroups.map { (emotionType, emotionRecords) ->
            // 创建日期范围内的所有日期
            val dateRange = generateSequence(startDate) { it.plusDays(1) }
                .takeWhile { !it.isAfter(now) }
                .toList()

            // 统计每天的情绪记录数量
            val dailyCounts = emotionRecords.groupBy { it.date }
                .mapValues { it.value.size }

            // 找出最大计数用于归一化
            val maxCount = dailyCounts.values.maxOrNull() ?: 1

            // 生成热力图数据点
            val dataPoints = dateRange.map { date ->
                val count = dailyCounts[date] ?: 0
                val intensity = if (maxCount > 0) count.toFloat() / maxCount else 0f
                HeatmapDataPoint(date, count, intensity)
            }

            // 获取情绪类型信息
            val firstRecord = emotionRecords.first()
            val emotionTypeEnum = EmotionType.values().find { it.displayName == emotionType }
                ?: EmotionType.CALM

            EmotionHeatmapData(
                emotion = emotionTypeEnum,
                dataPoints = dataPoints,
                maxCount = maxCount
            )
        }
    }

    /**
     * 处理从ProfileScreen传递过来的情绪记录，转换为统计页面所需的格式
     */
    private fun processProfileEmotionRecords() {
        viewModelScope.launch {
            try {
                val profileRecords = _profileEmotionRecords.value
                if (profileRecords.isNotEmpty()) {
                    // 创建情绪类型与Emoji的映射关系
                    val emotionToEmoji = mapOf(
                        "JOY" to "😊",
                        "SAD" to "😢",
                        "ANGRY" to "😡",
                        "ANXIOUS" to "😰",
                        "CALM" to "😐"
                    )
                    
                    // 创建情绪类型与中文名称的映射
                    val emotionToChineseName = mapOf(
                        "JOY" to "开心",
                        "SAD" to "伤心",
                        "ANGRY" to "生气",
                        "ANXIOUS" to "焦虑",
                        "CALM" to "平静"
                    )
                    
                    // 转换记录格式
                    val statRecords = profileRecords.map { record ->
                        val emotionType = record.emotion.name // 使用枚举名称如"JOY"
                        val chineseName = emotionToChineseName[emotionType] ?: "平静"
                        val emoji = emotionToEmoji[emotionType] ?: "😐"
                        
                        EmotionStatRecord(
                            date = record.date,
                            emotionType = chineseName, // 使用中文名称
                            emoji = emoji,
                            color = record.emotion.color,
                            note = record.mindfulnessNote,
                            triggers = record.triggers
                        )
                    }
                    
                    // 更新UI状态
                    val emotionStats = generateEmotionStats(statRecords)
                    val emotionTrend = generateEmotionTrend(statRecords, _uiState.value.selectedTimeRange)
                    val aiAnalysisResult = requestAIAnalysis(statRecords, _uiState.value.selectedTimeRange)
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        emotionStats = emotionStats,
                        emotionRecords = statRecords,
                        emotionTrend = emotionTrend,
                        aiAnalysisResult = aiAnalysisResult,
                        error = null
                    )
                    
                    Log.d("EmotionStatisticsVM", "已处理 ${statRecords.size} 条情绪记录")
                }
            } catch (e: Exception) {
                Log.e("EmotionStatisticsVM", "处理情绪记录失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "处理数据失败：${e.message}"
                )
            }
        }
    }
    
    fun loadEmotionData(timeRange: TimeRange) {
        _uiState.value = _uiState.value.copy(isLoading = true, selectedTimeRange = timeRange)
        
        viewModelScope.launch {
            try {
                // 首先尝试从感想数据库加载实际数据
                val reflections = reflectionRepository.getRecentReflections()
                Log.d("EmotionStatisticsVM", "从感想数据库加载了 ${reflections.size} 条记录")
                
                if (reflections.isNotEmpty()) {
                    // 将感想记录转换为情绪统计记录
                    val emotionRecords = convertReflectionsToEmotionRecords(reflections, timeRange)
                    Log.d("EmotionStatisticsVM", "转换后的情绪记录: ${emotionRecords.size} 条")
                    
                    if (emotionRecords.isNotEmpty()) {
                        // 生成统计数据
                        val emotionStats = generateEmotionStats(emotionRecords)
                        val emotionTrend = generateEmotionTrend(emotionRecords, timeRange)
                        val aiAnalysisResult = requestAIAnalysis(emotionRecords, timeRange)
                        val emotionHeatmapData = generateEmotionHeatmapData(emotionRecords, timeRange) // 🔧 生成热力图数据

                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            emotionStats = emotionStats,
                            emotionRecords = emotionRecords,
                            emotionTrend = emotionTrend,
                            aiAnalysisResult = aiAnalysisResult,
                            emotionHeatmapData = emotionHeatmapData, // 🔧 添加热力图数据
                            error = null
                        )
                        return@launch
                    }
                }
                
                // 检查是否有来自ProfileScreen的记录
                if (_profileEmotionRecords.value.isNotEmpty()) {
                    Log.d("EmotionStatisticsVM", "使用ProfileScreen传递的数据")
                    processProfileEmotionRecords()
                } else {
                    // 如果都没有数据，使用模拟数据
                    Log.d("EmotionStatisticsVM", "没有找到实际数据，使用模拟数据")
                    val emotionRecords = generateMockEmotionRecords(timeRange)
                    val emotionStats = generateEmotionStats(emotionRecords)
                    val emotionTrend = generateEmotionTrend(emotionRecords, timeRange)
                    val aiAnalysisResult = requestAIAnalysis(emotionRecords, timeRange)
                    val emotionHeatmapData = generateEmotionHeatmapData(emotionRecords, timeRange) // 🔧 生成热力图数据

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        emotionStats = emotionStats,
                        emotionRecords = emotionRecords,
                        emotionTrend = emotionTrend,
                        aiAnalysisResult = aiAnalysisResult,
                        emotionHeatmapData = emotionHeatmapData, // 🔧 添加热力图数据
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败：${e.message}"
                )
                Log.e("EmotionStatisticsVM", "Error loading emotion data", e)
            }
        }
    }
    
    private fun generateMockEmotionRecords(timeRange: TimeRange): List<EmotionStatRecord> {
        val today = LocalDate.now()
        val records = mutableListOf<EmotionStatRecord>()
        
        val daysToGenerate = when(timeRange) {
            TimeRange.WEEK -> 7
            TimeRange.MONTH -> 30
            TimeRange.QUARTER -> 90
            TimeRange.YEAR -> 365
            TimeRange.ALL -> 365 // 限制为1年的模拟数据
        }
        
        // 使用ProfileScreen中定义的情绪颜色
        val emotionTypes = listOf(
            Triple("开心", "😊", Color(0xFFa8d8a8)), // JOY
            Triple("平静", "😐", Color(0xFF9ed0e6)), // CALM  
            Triple("伤心", "😢", Color(0xFFf5e1a4)), // SAD
            Triple("生气", "😡", Color(0xFFf5c4b8)), // ANGRY
            Triple("焦虑", "😰", Color(0xFFd7b8e0))  // ANXIOUS
        )
        
        for (i in 0 until daysToGenerate) {
            val date = today.minusDays(i.toLong())
            val random = (0 until emotionTypes.size).random()
            val (type, emoji, color) = emotionTypes[random]
            
            records.add(
                EmotionStatRecord(
                    date = date,
                    emotionType = type,
                    emoji = emoji,
                    color = color,
                    note = "",
                    triggers = listOf("工作", "家庭", "健康").shuffled().take((0..2).random())
                )
            )
        }
        
        return records
    }
    
    private fun generateEmotionStats(records: List<EmotionStatRecord>): List<EmotionStatistic> {
        val emotionCounts = records.groupBy { it.emotionType }
            .mapValues { it.value.size }
        
        val totalCount = records.size.toFloat()
        
        return emotionCounts.map { (type, count) ->
            val record = records.first { it.emotionType == type }
            EmotionStatistic(
                type = type,
                emoji = record.emoji,
                color = record.color,
                count = count,
                percentage = count / totalCount
            )
        }.sortedByDescending { it.count }
    }
    
    private fun generateEmotionTrend(records: List<EmotionStatRecord>, timeRange: TimeRange): EmotionTrend {
        // 按时间分组
        val periodString = when(timeRange) {
            TimeRange.WEEK -> "周趋势"
            TimeRange.MONTH -> "月趋势"
            TimeRange.QUARTER -> "季度趋势"
            TimeRange.YEAR -> "年度趋势"
            TimeRange.ALL -> "全部趋势"
        }
        
        // 统计各情绪数量
        val emotionCounts = records.groupBy { it.emotionType }
            .mapValues { it.value.size }
        
        // 找出主导情绪
        val dominantEmotion = emotionCounts.maxByOrNull { it.value }?.key ?: "平静"
        
        // 计算情绪变化（这里简化为随机值）
        val emotionChange = Random.nextFloat() * 1.0f - 0.5f
        
        return EmotionTrend(
            period = periodString,
            emotionCounts = emotionCounts,
            dominantEmotion = dominantEmotion,
            emotionChange = emotionChange
        )
    }
    
    private fun requestAIAnalysis(records: List<EmotionStatRecord>, timeRange: TimeRange): AIAnalysisResult {
        // 基于实际数据生成AI分析结果
        val patterns = mutableListOf<String>()
        val insights = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        
        if (records.isNotEmpty()) {
            // 分析情绪分布
            val emotionCounts = records.groupBy { it.emotionType }.mapValues { it.value.size }
            val totalRecords = records.size
            val dominantEmotion = emotionCounts.maxByOrNull { it.value }?.key
            
            // 生成模式分析
            dominantEmotion?.let { emotion ->
                val percentage = (emotionCounts[emotion]!! * 100) / totalRecords
                patterns.add("${emotion}情绪占主导地位，占比${percentage}%")
            }
            
            // 分析触发因素
            val allTriggers = records.flatMap { it.triggers }
            if (allTriggers.isNotEmpty()) {
                val triggerCounts = allTriggers.groupBy { it }.mapValues { it.value.size }
                val topTrigger = triggerCounts.maxByOrNull { it.value }?.key
                topTrigger?.let { trigger ->
                    val percentage = (triggerCounts[trigger]!! * 100) / allTriggers.size
                    patterns.add("${trigger}是主要触发因素，占比${percentage}%")
                }
            }
            
            // 分析时间模式
            val timeRangeText = when (timeRange) {
                TimeRange.WEEK -> "一周"
                TimeRange.MONTH -> "一个月"
                TimeRange.QUARTER -> "三个月"
                TimeRange.YEAR -> "一年"
                TimeRange.ALL -> "全部时间"
            }
            patterns.add("在过去${timeRangeText}内共记录${totalRecords}次情绪")
            
            // 生成洞察
            val positiveEmotions = records.filter { it.emotionType in listOf("开心", "平静") }
            val negativeEmotions = records.filter { it.emotionType in listOf("伤心", "生气", "焦虑") }
            
            val positiveRatio = if (totalRecords > 0) (positiveEmotions.size * 100) / totalRecords else 0
            val negativeRatio = if (totalRecords > 0) (negativeEmotions.size * 100) / totalRecords else 0
            
            when {
                positiveRatio > 60 -> {
                    insights.add("您的情绪状态整体积极，正面情绪占${positiveRatio}%")
                    insights.add("保持良好的心理状态，继续维持当前的生活方式")
                }
                negativeRatio > 60 -> {
                    insights.add("负面情绪较多，占${negativeRatio}%，需要关注心理健康")
                    insights.add("建议寻找压力来源并采取相应的调节措施")
                }
                else -> {
                    insights.add("情绪状态相对平衡，正负面情绪比例适中")
                    insights.add("保持当前的情绪管理方式，注意情绪变化")
                }
            }
            
            // 生成建议
            if (negativeRatio > 40) {
                recommendations.add("尝试进行冥想或深呼吸练习来缓解压力")
                recommendations.add("增加运动频率，运动有助于改善情绪")
                recommendations.add("保持规律的作息时间，充足的睡眠很重要")
            }
            
            if (allTriggers.contains("工作")) {
                recommendations.add("建立工作与生活的明确边界")
                recommendations.add("学会在工作中合理安排休息时间")
            }
            
            if (positiveRatio > 50) {
                recommendations.add("继续保持当前的积极生活方式")
                recommendations.add("可以分享您的情绪管理经验给他人")
            } else {
                recommendations.add("尝试培养新的兴趣爱好，丰富生活内容")
                recommendations.add("多与朋友家人沟通，分享内心感受")
            }
        } else {
            // 没有数据时的默认分析
            patterns.add("暂无足够的情绪记录数据")
            insights.add("建议定期记录情绪状态以获得更好的分析")
            recommendations.add("开始记录每天的情绪状态和感想")
            recommendations.add("关注影响情绪的具体事件和因素")
        }
        
        return AIAnalysisResult(
            patterns = patterns.take(3),
            insights = insights.take(3),
            recommendations = recommendations.take(4)
        )
    }
    
    fun exportData() {
        // 实现数据导出功能
        // 在实际应用中，应该调用Repository的导出方法
        Log.d("EmotionStatisticsVM", "Exporting emotion data...")
    }
    
    /**
     * 刷新情绪数据
     * 供外部调用，当感想页面有新数据时可以刷新统计
     */
    fun refreshEmotionData() {
        loadEmotionData(_uiState.value.selectedTimeRange)
    }
    
    /**
     * 获取指定时间范围的情绪数据
     */
    suspend fun getEmotionDataByTimeRange(timeRange: TimeRange): List<EmotionStatRecord> {
        return try {
            val reflections = reflectionRepository.getRecentReflections()
            convertReflectionsToEmotionRecords(reflections, timeRange)
        } catch (e: Exception) {
            Log.e("EmotionStatisticsVM", "获取情绪数据失败", e)
            emptyList()
        }
    }
}

/**
 * 情绪统计页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmotionStatisticsScreen(
    navController: NavController,
    viewModel: EmotionStatisticsViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    val coroutineScope = rememberCoroutineScope()
    
    // 获取UI状态
    val uiState by viewModel.uiState.collectAsState()
    
    // 使用系统状态栏管理
    SideEffect {
        activity?.let { 
            SystemBarManager.setupStatisticsPageSystemBars(it)  // 使用专门为统计页面设计的系统栏设置
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            val originalNavigationBarColor = window.navigationBarColor
            
            // 应用统计页面专用的状态栏设置
            SystemBarManager.setupStatisticsPageSystemBars(act)
            
            onDispose {
                // 恢复原始状态栏和导航栏颜色
                window.statusBarColor = originalStatusBarColor
                window.navigationBarColor = originalNavigationBarColor
                Log.d("EmotionStatisticsScreen", "EmotionStatisticsScreen disposed")
            }
        }
    }
    
    // 界面背景渐变
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFFFFFF),  // 顶部白色
            Color(0xFFF8F6F8)   // 底部淡紫色
        )
    )
    
    // 主界面布局
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    "情绪统计",
                    fontSize = 18.sp, // 从20.sp缩小到18.sp
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF554A60)
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF554A60)
                    )
                }
            },
            actions = {
                // 导出数据按钮
                IconButton(onClick = { viewModel.exportData() }) {
                    Icon(
                        imageVector = Icons.Default.Share,
                        contentDescription = "导出数据",
                        tint = Color(0xFF554A60)
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.White,  // 使用白色背景，与状态栏颜色匹配
                titleContentColor = Color(0xFF554A60)
            )
        )
        
        // 主内容区域
        if (uiState.isLoading) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize()
            ) {
                CircularProgressIndicator()
            }
        } else if (uiState.error != null) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize()
            ) {
                Text(
                    text = uiState.error ?: "未知错误",
                    color = Color.Red
                )
            }
        } else {
            // 时间范围选择器
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 6.dp), // 从8.dp缩小到6.dp
                horizontalArrangement = spacedBy(6.dp) // 从8.dp缩小到6.dp
            ) {
                items(TimeRange.values()) { range ->
                    FilterChip(
                        selected = uiState.selectedTimeRange == range,
                        onClick = { viewModel.loadEmotionData(range) },
                        label = { Text(range.displayName) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = Color(0xFFCCAEC5),
                            selectedLabelColor = Color.White
                        )
                    )
                }
            }
            
            // 内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(12.dp) // 从16.dp缩小到12.dp
                    .verticalScroll(rememberScrollState())
            ) {
                // 情绪总览卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp) // 从16.dp缩小到12.dp
                    ) {
                        Text(
                            text = "情绪总览",
                            fontSize = 16.sp, // 从18.sp缩小到16.sp
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF554A60),
                            modifier = Modifier.padding(bottom = 12.dp) // 从16.dp缩小到12.dp
                        )
                        
                        // 情绪分布统计 - 改为两行显示：上面两列，下面三列
                        if (uiState.emotionStats.isNotEmpty()) {
                            // 第一行 - 显示前两种情绪
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                uiState.emotionStats.take(2).forEach { stat ->
                                    Box(modifier = Modifier.weight(1f)) {
                                        // 🔧 查找对应的热力图数据
                                        val heatmapData = uiState.emotionHeatmapData.find {
                                            it.emotion.displayName == stat.type
                                        }
                                        EmotionStatItem(stat, heatmapData)
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(12.dp))

                            // 第二行 - 显示后三种情绪
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                uiState.emotionStats.drop(2).take(3).forEach { stat ->
                                    Box(modifier = Modifier.weight(1f)) {
                                        // 🔧 查找对应的热力图数据
                                        val heatmapData = uiState.emotionHeatmapData.find {
                                            it.emotion.displayName == stat.type
                                        }
                                        EmotionStatItem(stat, heatmapData)
                                    }
                                }
                            }
                        }
                        
                        // 查看更多按钮不再需要
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp)) // 从16.dp缩小到12.dp
                
                // 情绪趋势图
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp) // 从16.dp缩小到12.dp
                    ) {
                        Text(
                            text = "情绪趋势",
                            fontSize = 16.sp, // 从18.sp缩小到16.sp
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF554A60),
                            modifier = Modifier.padding(bottom = 12.dp) // 从16.dp缩小到12.dp
                        )
                        
                        // 趋势图表
                        EmotionTrendChart(
                            records = uiState.emotionRecords,
                            timeRange = uiState.selectedTimeRange,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp)
                        )
                        
                        // 趋势摘要
                        uiState.emotionTrend?.let { trend ->
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // 主导情绪指示
                                val dominantEmotion = uiState.emotionStats.firstOrNull { it.type == trend.dominantEmotion }
                                dominantEmotion?.let {
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier
                                            .size(40.dp)
                                            .clip(CircleShape)
                                            .background(it.color.copy(alpha = 0.2f))
                                    ) {
                                        Text(
                                            text = it.emoji,
                                            fontSize = 20.sp
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.width(12.dp))
                                }
                                
                                // 趋势描述
                                Column(modifier = Modifier.weight(1f)) {
                                    Text(
                                        text = "主导情绪: ${trend.dominantEmotion}",
                                        fontSize = 14.sp, // 从16.sp缩小到14.sp
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF554A60)
                                    )
                                    
                                    val trendDescription = if (trend.emotionChange > 0) {
                                        "积极情绪呈${(trend.emotionChange * 100).toInt()}%上升趋势"
                                    } else if (trend.emotionChange < 0) {
                                        "消极情绪呈${(-trend.emotionChange * 100).toInt()}%上升趋势"
                                    } else {
                                        "情绪状态保持稳定"
                                    }
                                    
                                    Text(
                                        text = trendDescription,
                                        fontSize = 12.sp, // 从14.sp缩小到12.sp
                                        color = Color(0xFF888888)
                                    )
                                }
                                
                                // 趋势指示箭头
                                Icon(
                                    imageVector = if (trend.emotionChange >= 0) 
                                        Icons.Default.TrendingUp 
                                    else 
                                        Icons.Default.TrendingDown,
                                    contentDescription = "趋势",
                                    tint = if (trend.emotionChange >= 0) 
                                        Color(0xFF4CAF50) 
                                    else 
                                        Color(0xFFF44336),
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp)) // 从16.dp缩小到12.dp
                
                // AI分析与建议卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp) // 从16.dp缩小到12.dp
                    ) {
                        // 标题
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(bottom = 16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Psychology,
                                contentDescription = "AI分析",
                                tint = Color(0xFF9C27B0),
                                modifier = Modifier.size(20.dp) // 从24.dp缩小到20.dp
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "AI情绪分析",
                                fontSize = 16.sp, // 从18.sp缩小到16.sp
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF554A60)
                            )
                        }
                        
                        // AI分析内容
                        uiState.aiAnalysisResult?.let { analysis ->
                            // 情绪模式
                            Text(
                                text = "情绪模式",
                                fontSize = 14.sp, // 从16.sp缩小到14.sp
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF554A60),
                                modifier = Modifier.padding(vertical = 6.dp) // 从8.dp缩小到6.dp
                            )
                            
                            analysis.patterns.forEach { pattern ->
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(vertical = 4.dp)
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(6.dp)
                                            .clip(CircleShape)
                                            .background(Color(0xFF9C27B0))
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Text(
                                        text = pattern,
                                        fontSize = 12.sp, // 从14.sp缩小到12.sp
                                        color = Color(0xFF666666)
                                    )
                                }
                            }
                            
                            Divider(
                                modifier = Modifier.padding(vertical = 12.dp),
                                color = Color.LightGray.copy(alpha = 0.5f)
                            )
                            
                            // 个性化建议
                            Text(
                                text = "改善建议",
                                fontSize = 14.sp, // 从16.sp缩小到14.sp
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF554A60),
                                modifier = Modifier.padding(vertical = 6.dp) // 从8.dp缩小到6.dp
                            )
                            
                            analysis.recommendations.forEach { recommendation ->
                                Row(
                                    verticalAlignment = Alignment.Top,
                                    modifier = Modifier.padding(vertical = 4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Lightbulb,
                                        contentDescription = "建议",
                                        tint = Color(0xFFFFC107),
                                        modifier = Modifier.size(16.dp) // 从18.dp缩小到16.dp
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Text(
                                        text = recommendation,
                                        fontSize = 12.sp, // 从14.sp缩小到12.sp
                                        color = Color(0xFF666666)
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp)) // 从16.dp缩小到12.dp
                
                // 隐私说明卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF5F5F5)
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp) // 从16.dp缩小到12.dp
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                                                            Icon(
                                    imageVector = Icons.Default.Shield,
                                    contentDescription = "隐私保护",
                                    tint = Color(0xFF9E9E9E),
                                    modifier = Modifier.size(18.dp) // 从20.dp缩小到18.dp
                                )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "隐私保护声明",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF757575)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "所有情绪数据均储存在本地设备，AI分析在设备端进行，不会上传个人情绪数据。您可以随时导出或删除自己的数据。",
                            fontSize = 12.sp,
                            color = Color(0xFF9E9E9E),
                            lineHeight = 18.sp
                        )
                    }
                }
                
                // 底部间距
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

/**
 * 情绪热力图组件
 */
@Composable
fun EmotionHeatmapGrid(
    heatmapData: EmotionHeatmapData,
    modifier: Modifier = Modifier
) {
    val gridSize = when {
        heatmapData.dataPoints.size <= 7 -> 7 // 一周
        heatmapData.dataPoints.size <= 30 -> 10 // 一个月，显示为10列
        heatmapData.dataPoints.size <= 90 -> 15 // 季度，显示为15列
        else -> 20 // 年度或全部，显示为20列
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(gridSize),
        modifier = modifier.height(60.dp),
        contentPadding = PaddingValues(2.dp),
        horizontalArrangement = Arrangement.spacedBy(1.dp),
        verticalArrangement = Arrangement.spacedBy(1.dp)
    ) {
        items(heatmapData.dataPoints) { dataPoint ->
            Box(
                modifier = Modifier
                    .aspectRatio(1f)
                    .clip(RoundedCornerShape(2.dp))
                    .background(
                        if (dataPoint.count > 0) {
                            heatmapData.emotion.color.copy(
                                alpha = 0.2f + (dataPoint.intensity * 0.8f)
                            )
                        } else {
                            Color(0xFFEEEEEE)
                        }
                    )
            )
        }
    }
}

/**
 * 情绪统计项组件（增强版，包含热力图）
 */
@Composable
fun EmotionStatItem(
    stat: EmotionStatistic,
    heatmapData: EmotionHeatmapData? = null
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            // 情绪图标
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(32.dp) // 从36.dp缩小到32.dp
                    .clip(CircleShape)
                    .background(stat.color.copy(alpha = 0.2f))
            ) {
                Text(
                    text = stat.emoji,
                    fontSize = 16.sp // 从18.sp缩小到16.sp
                )
            }
            
            Spacer(modifier = Modifier.width(10.dp)) // 从12.dp缩小到10.dp
            
            // 情绪名称和计数
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = stat.type,
                    fontSize = 14.sp, // 从16.sp缩小到14.sp
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF554A60)
                )
                
                Text(
                    text = "${stat.count}次 (${(stat.percentage * 100).toInt()}%)",
                    fontSize = 12.sp, // 从14.sp缩小到12.sp
                    color = Color(0xFF888888)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp)) // 从8.dp缩小到6.dp
        
        // 进度条
        LinearProgressIndicator(
            progress = stat.percentage,
            color = stat.color,
            trackColor = Color(0xFFEEEEEE),
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp) // 从8.dp缩小到6.dp
                .clip(RoundedCornerShape(3.dp)) // 从4.dp缩小到3.dp
        )

        // 🔧 热力图显示
        if (heatmapData != null) {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "活跃度",
                fontSize = 10.sp,
                color = Color(0xFF888888),
                modifier = Modifier.padding(bottom = 4.dp)
            )

            EmotionHeatmapGrid(
                heatmapData = heatmapData,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 情绪趋势图表组件
 */
@Composable
fun EmotionTrendChart(
    records: List<EmotionStatRecord>,
    timeRange: TimeRange,
    modifier: Modifier = Modifier
) {
    // 计算每个时间点的情绪分布
    val groupedRecords = records.groupBy { 
        when(timeRange) {
            TimeRange.WEEK -> ChronoUnit.DAYS.between(records.minByOrNull { it.date }?.date ?: LocalDate.now(), it.date).toInt()
            TimeRange.MONTH -> ChronoUnit.DAYS.between(records.minByOrNull { it.date }?.date ?: LocalDate.now(), it.date).toInt() / 3
            TimeRange.QUARTER -> ChronoUnit.WEEKS.between(records.minByOrNull { it.date }?.date ?: LocalDate.now(), it.date).toInt()
            TimeRange.YEAR -> ChronoUnit.MONTHS.between(records.minByOrNull { it.date }?.date ?: LocalDate.now(), it.date).toInt()
            TimeRange.ALL -> ChronoUnit.MONTHS.between(records.minByOrNull { it.date }?.date ?: LocalDate.now(), it.date).toInt()
        }
    }.toSortedMap()
    
    // 获取情绪类型和颜色映射
    val emotionTypes = records.map { it.emotionType }.distinct()
    val emotionColors = records.associate { it.emotionType to it.color }
    
    // 坐标轴标签
    val xAxisLabels = when(timeRange) {
        TimeRange.WEEK -> listOf("一", "二", "三", "四", "五", "六", "日")
        TimeRange.MONTH -> listOf("初", "中", "下", "末")
        TimeRange.QUARTER -> listOf("第一周", "第四周", "第八周", "第十二周")
        TimeRange.YEAR -> listOf("1月", "3月", "6月", "9月", "12月")
        TimeRange.ALL -> listOf("过去", "较早", "最近", "现在")
    }
    
    val dominantEmotions = mutableListOf<Pair<Int, String>>()
    
    // 计算每个时间点的主导情绪
    groupedRecords.forEach { (timePoint, recordsForTime) ->
        val emotionCount = recordsForTime.groupBy { it.emotionType }
            .mapValues { it.value.size }
        val dominantEmotion = emotionCount.maxByOrNull { it.value }?.key ?: "平静"
        dominantEmotions.add(Pair(timePoint, dominantEmotion))
    }
    
    // 绘制高级图表
    Column(
        modifier = modifier
            .padding(top = 16.dp, bottom = 48.dp, start = 16.dp, end = 16.dp)
            .fillMaxWidth()
    ) {
        // 优化图例 - 取消背景，使用更轻量级的设计
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp, top = 4.dp, start = 8.dp, end = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            emotionTypes.take(5).forEach { emotionType ->
                val color = emotionColors[emotionType] ?: Color.Gray
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(color, CircleShape)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = emotionType,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF555555)
                    )
                }
            }
            
            if (emotionTypes.size > 5) {
                Text(
                    text = "+${emotionTypes.size - 5}",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF888888)
                )
            }
        }
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(220.dp)
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
            ) {
                val width = size.width
                val height = size.height
                val padding = 40f
                val chartWidth = width - padding * 2
                val chartHeight = height - padding * 2
                
                // 绘制网格线
                val gridLineColor = Color(0xFFEEEEEE)
                // 水平网格线
                for (i in 0..3) {
                    val y = padding + (chartHeight * i) / 3
                    drawLine(
                        color = gridLineColor,
                        start = Offset(padding, y),
                        end = Offset(width - padding, y),
                        strokeWidth = 1f,
                        pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                    )
                }
                
                // 垂直网格线
                val segments = if (groupedRecords.isNotEmpty()) groupedRecords.keys.max() + 1 else xAxisLabels.size
                for (i in 0 until segments) {
                    val x = padding + (chartWidth * i) / (segments - 1)
                    drawLine(
                        color = gridLineColor,
                        start = Offset(x, padding),
                        end = Offset(x, height - padding),
                        strokeWidth = 1f,
                        pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                    )
                }
                
                // 绘制X轴和Y轴
                drawLine(
                    color = Color(0xFF9E9E9E),
                    start = Offset(padding, height - padding),
                    end = Offset(width - padding, height - padding),
                    strokeWidth = 2f
                )
                
                drawLine(
                    color = Color(0xFF9E9E9E),
                    start = Offset(padding, padding),
                    end = Offset(padding, height - padding),
                    strokeWidth = 2f
                )
                
                // 如果有足够的数据，绘制情绪线图
                emotionTypes.forEach { emotionType ->
                    // 收集该情绪类型的所有点
                    val points = mutableListOf<Offset>()
                    val color = emotionColors[emotionType] ?: Color.Gray
                    
                    // 计算每个时间点该情绪的比例
                    for (i in 0 until segments) {
                        val records = groupedRecords[i] ?: continue
                        val totalRecords = records.size.toFloat()
                        if (totalRecords > 0) {
                            val emotionCount = records.count { it.emotionType == emotionType }.toFloat()
                            val percentage = emotionCount / totalRecords
                            
                            val x = padding + (chartWidth * i) / (segments - 1)
                            val y = height - padding - (chartHeight * percentage)
                            points.add(Offset(x, y))
                        }
                    }
                    
                    // 如果有足够的点，绘制平滑曲线
                    if (points.size > 1) {
                        // 绘制平滑曲线区域
                        val path = Path()
                        path.moveTo(points.first().x, points.first().y)
                        
                        // 使用二次贝塞尔曲线创建平滑曲线
                        for (i in 0 until points.size - 1) {
                            val currentPoint = points[i]
                            val nextPoint = points[i + 1]
                            
                            val controlX = (currentPoint.x + nextPoint.x) / 2
                            
                            path.cubicTo(
                                controlX, currentPoint.y,
                                controlX, nextPoint.y,
                                nextPoint.x, nextPoint.y
                            )
                        }
                        
                        // 添加底部边界以创建封闭区域
                        path.lineTo(points.last().x, height - padding)
                        path.lineTo(points.first().x, height - padding)
                        path.close()
                        
                        // 绘制渐变填充
                        drawPath(
                            path = path,
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    color.copy(alpha = 0.5f),
                                    color.copy(alpha = 0.1f)
                                ),
                                startY = padding,
                                endY = height - padding
                            )
                        )
                        
                        // 重置路径并再次绘制曲线（不封闭）以便绘制曲线本身
                        path.reset()
                        path.moveTo(points.first().x, points.first().y)
                        
                        for (i in 0 until points.size - 1) {
                            val currentPoint = points[i]
                            val nextPoint = points[i + 1]
                            
                            val controlX = (currentPoint.x + nextPoint.x) / 2
                            
                            path.cubicTo(
                                controlX, currentPoint.y,
                                controlX, nextPoint.y,
                                nextPoint.x, nextPoint.y
                            )
                        }
                        
                        // 绘制曲线
                        drawPath(
                            path = path,
                            color = color,
                            style = Stroke(
                                width = 2f, // 减小曲线宽度
                                cap = StrokeCap.Round,
                                join = StrokeJoin.Round
                            )
                        )
                        
                        // 绘制端点 - 减小圆点大小
                        points.forEach { point ->
                            // 外圆
                            drawCircle(
                                color = color,
                                radius = 3f, // 从5f减小到3f
                                center = point
                            )
                            
                            // 内圆
                            drawCircle(
                                color = Color.White,
                                radius = 1.5f, // 从3f减小到1.5f
                                center = point
                            )
                        }
                    } else if (points.size == 1) {
                        // 如果只有一个点，只绘制一个小圆点
                        val point = points.first()
                        drawCircle(
                            color = color,
                            radius = 3f, // 从5f减小到3f
                            center = point
                        )
                        
                        drawCircle(
                            color = Color.White,
                            radius = 1.5f, // 从3f减小到1.5f
                            center = point
                        )
                    }
                }
                
                // 绘制情绪强度热力条，展示主导情绪
                if (dominantEmotions.isNotEmpty()) {
                    val barHeight = 6f  // 降低高度，使条更细致
                    val barTop = height - padding + 20f  // 降低位置，更靠近x轴
                    val barBottom = barTop + barHeight
                    
                    // 绘制背景线
                    drawLine(
                        color = Color(0xFFEEEEEE),
                        start = Offset(padding, barTop + barHeight / 2),
                        end = Offset(width - padding, barTop + barHeight / 2),
                        strokeWidth = barHeight * 1.2f
                    )
                    
                    // 绘制主导情绪热力条，使用更窄的设计
                    for (i in 0 until segments) {
                        val matchingEmotion = dominantEmotions.firstOrNull { it.first == i }
                        if (matchingEmotion != null) {
                            val emotionType = matchingEmotion.second
                            val color = emotionColors[emotionType] ?: Color.Gray
                            
                            // 计算位置，使条更窄
                            val centerX = padding + (chartWidth * i) / (segments - 1)
                            val barWidth = chartWidth / (segments * 3) // 将宽度除以3，使其更窄
                            val startX = centerX - barWidth / 2
                            val endX = centerX + barWidth / 2
                            
                            drawLine(
                                color = color,
                                start = Offset(centerX, barTop),
                                end = Offset(centerX, barTop + barHeight),
                                strokeWidth = barWidth,
                                cap = StrokeCap.Round
                            )
                        }
                    }
                    
                    // 移除文字标签"情绪强度分布"，简化设计
                    // 改为在Canvas外部添加更美观的标签
                }
            }
        }
        
        // X轴标签容器 - 放在图表下方，使用自定义布局优化显示
        Spacer(modifier = Modifier.height(8.dp))
        
        // 使用更现代的标签设计
        val segments = if (groupedRecords.isNotEmpty()) groupedRecords.keys.max() + 1 else xAxisLabels.size
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 40.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 只在有足够空间的情况下显示所有标签
            if (segments <= xAxisLabels.size) {
                // 计算每个标签的位置
                for (i in 0 until segments) {
                    if (i < xAxisLabels.size) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // 标签使用卡片式设计
                            Card(
                                shape = RoundedCornerShape(4.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = Color.White
                                ),
                                border = BorderStroke(0.5.dp, Color(0xFFE0E0E0)),
                                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                            ) {
                                Text(
                                    text = xAxisLabels[i],
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF555555),
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
} 