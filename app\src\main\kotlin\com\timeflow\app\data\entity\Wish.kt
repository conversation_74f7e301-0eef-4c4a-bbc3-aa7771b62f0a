package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.DateTimeConverter
import com.timeflow.app.data.converter.StringListConverter
import java.time.LocalDateTime

@Entity(tableName = "wishes")
@TypeConverters(DateTimeConverter::class, StringListConverter::class)
data class Wish(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String = "",
    val category: String = "其他", // 旅行、购物、学习、事业、生活、其他
    val priority: Int = 3, // 1-5星级优先级
    val inspirationItems: String = "[]", // JSON string for List<String> - 灵感库内容
    val imageUris: String = "[]", // JSON string for List<String> - 图片URI列表
    val estimatedCost: Float? = null, // 预估成本
    val targetTimePeriod: String = "", // 目标时间段，如"2025年春季"
    val tags: String = "", // 标签列表
    val status: String = "活跃", // 活跃、已转目标、已归档、已完成
    val relatedGoalId: String? = null, // 关联的目标ID（转为目标后）
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val achievedAt: LocalDateTime? = null, // 实现时间
    val archivedAt: LocalDateTime? = null, // 归档时间
    val isArchived: Boolean = false,
    val difficulty: String = "中等", // 简单、中等、困难、极难
    val motivation: String = "", // 动机说明
    val prerequisites: String = "[]", // JSON string for List<String> - 前置条件
    val notes: String = "" // 备注
) 