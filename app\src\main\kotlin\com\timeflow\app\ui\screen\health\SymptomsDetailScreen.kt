package com.timeflow.app.ui.screen.health

import android.app.Activity
import android.content.Context
import android.widget.Toast
import android.util.Log
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.data.entity.SymptomRecord
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.lazy.LazyColumn
import kotlin.math.cos
import kotlin.math.sin
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.getValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.rememberScrollState
import kotlinx.coroutines.delay
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically

import androidx.compose.ui.graphics.vector.ImageVector
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import androidx.compose.runtime.rememberCoroutineScope

/**
 * 添加一个数据类来表示按日期分组的症状
 */
data class DailySymptoms(
    val date: String, // 格式如 "2025.4.3"
    val cyclePhase: String, // 月经期/排卵期等
    val symptoms: List<String> // 症状列表：腹痛、痤疮等
)

/**
 * 增强的症状详情屏幕 - 参照知名生理周期app设计
 */
@Composable
fun SymptomsDetailScreen(
    viewModel: MenstrualCycleViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val selectedDate by viewModel.selectedDate.collectAsState()
    val symptoms by viewModel.selectedDateSymptoms.collectAsState()
    
    // 增加收集周期阶段数据
    val cyclePhases by viewModel.cyclePhases.collectAsState(emptyMap())
    
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 使用SystemBarManager来设置状态栏，确保一致性
    DisposableEffect(Unit) {
        var originalStatusBarColor: Int = 0
        var originalNavigationBarColor: Int = 0
        
        activity?.let { act ->
            val window = act.window
            
            try {
                // 保存原始状态
                originalStatusBarColor = window.statusBarColor
                originalNavigationBarColor = window.navigationBarColor
                
                // 使用SystemBarManager设置不透明的状态栏
                SystemBarManager.forceOpaqueStatusBar(act)
            } catch (e: Exception) {
                Log.e("SymptomsDetailScreen", "设置状态栏出错: ${e.message}")
            }
        }
        
        onDispose {
            activity?.let { act ->
                try {
                    // 恢复原始颜色
                    act.window.statusBarColor = originalStatusBarColor
                    act.window.navigationBarColor = originalNavigationBarColor
                } catch (e: Exception) {
                    Log.e("SymptomsDetailScreen", "恢复状态栏出错: ${e.message}")
                }
            }
        }
    }
    
    // 对话框状态
    var showIntensityDialog by remember { mutableStateOf(false) }
    var selectedSymptomType by remember { mutableStateOf("") }
    var selectedSymptomLabel by remember { mutableStateOf("") }
    
    // 维护历史记录列表状态
    var symptomHistory by remember { mutableStateOf<List<DailySymptoms>>(emptyList()) }
    
    // 设置初始历史记录数据
    LaunchedEffect(Unit) {
        symptomHistory = generateInitialHistoryData()
    }
    
    // 加载时和cyclePhases变化时刷新历史数据
    LaunchedEffect(cyclePhases) {
        symptomHistory = refreshHistoryData(cyclePhases, symptoms)
    }
    
    // 添加刷新状态
    var isRefreshing by remember { mutableStateOf(false) }
    
    // 创建协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 定义呼出强度对话框的函数
    val showSymptomDialog = { type: String, label: String ->
        selectedSymptomType = type
        selectedSymptomLabel = label
        showIntensityDialog = true
        
        // 如果是便秘症状，提前更新历史记录
        if (type == "constipation") {
            // 直接更新历史记录，添加便秘症状到今天的记录中
            val today = LocalDate.now()
            val todayStr = "${today.year}.${today.monthValue}.${today.dayOfMonth}"
            
            // 获取今天的周期阶段
            val todayCyclePhase = getCyclePhaseForDate(today, cyclePhases)
            
            // 查找今天的记录
            val todayRecordIndex = symptomHistory.indexOfFirst { it.date == todayStr }
            
            if (todayRecordIndex != -1) {
                // 今天已有记录，添加便秘症状
                val todayRecord = symptomHistory[todayRecordIndex]
                if (!todayRecord.symptoms.contains("便秘")) {
                    val updatedSymptoms = todayRecord.symptoms + "便秘"
                    // 更新历史记录
                    val updatedRecord = todayRecord.copy(
                        symptoms = updatedSymptoms,
                        cyclePhase = todayCyclePhase // 更新周期阶段
                    )
                    symptomHistory = symptomHistory.toMutableList().apply {
                        set(todayRecordIndex, updatedRecord)
                    }
                }
            } else {
                // 今天没有记录，创建新记录
                val newRecord = DailySymptoms(
                    date = todayStr,
                    cyclePhase = todayCyclePhase, // 使用真实周期阶段
                    symptoms = listOf("便秘")
                )
                // 添加到历史记录列表
                symptomHistory = listOf(newRecord) + symptomHistory
            }
        }
    }
    
    // 修改记录症状的函数，也更新历史
    val recordSymptomAndUpdateHistory = { type: String, intensity: Int, notes: String? ->
        // 调用ViewModel记录症状
        viewModel.recordSymptom(type, intensity, notes)
        
        // 更新今天的历史记录
        val symptomLabel = getSymptomLabel(type)
        val today = LocalDate.now()
        val todayStr = "${today.year}.${today.monthValue}.${today.dayOfMonth}"
        
        // 获取今天的周期阶段
        val todayCyclePhase = getCyclePhaseForDate(today, cyclePhases)
        
        // 查找今天的记录
        val todayRecordIndex = symptomHistory.indexOfFirst { it.date == todayStr }
        
        if (todayRecordIndex != -1) {
            // 今天已有记录，添加新症状
            val todayRecord = symptomHistory[todayRecordIndex]
            val updatedSymptoms = if (!todayRecord.symptoms.contains(symptomLabel)) {
                todayRecord.symptoms + symptomLabel
            } else {
                todayRecord.symptoms
            }
            
            // 更新历史记录，确保周期阶段是最新的
            val updatedRecord = todayRecord.copy(
                symptoms = updatedSymptoms,
                cyclePhase = todayCyclePhase // 更新周期阶段
            )
            symptomHistory = symptomHistory.toMutableList().apply {
                set(todayRecordIndex, updatedRecord)
            }
        } else {
            // 今天没有记录，创建新记录
            val newRecord = DailySymptoms(
                date = todayStr,
                cyclePhase = todayCyclePhase, // 使用真实周期阶段
                symptoms = listOf(symptomLabel)
            )
            
            // 添加到历史记录列表
            symptomHistory = listOf(newRecord) + symptomHistory
        }
        
        // 显示Toast提示
        showToast(context, "已记录${symptomLabel}症状(强度:$intensity)")
    }
    
    Scaffold(
        topBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
                    .height(56.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                    
                    Text(
                        text = "今日症状",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 移除了PCOS模式指示器
                    
                    Text(
                        text = selectedDate.format(dateFormatter),
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(end = 16.dp)
                    )
                }
            }
        },
        containerColor = Color(0xFFFFF9FA)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // 记录症状摘要
            if (symptoms.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // 标题和日期
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.EventNote,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "已记录症状",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 格式化日期为 yyyy.MM.dd
                    val dateText = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd"))
                    
                    Text(
                        text = dateText,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 显示症状内容
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    shape = RoundedCornerShape(12.dp),
                    color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.6f)
                ) {
                    // 症状列表
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        // 按症状类型分组
                        val flowSymptoms = symptoms.filter { it.symptomType == "flow" }
                        val painSymptoms = symptoms.filter { it.symptomType == "pain" }
                        val sleepSymptoms = symptoms.filter { it.symptomType == "sleep" }
                        val headacheSymptoms = symptoms.filter { it.symptomType == "headache" }
                        val otherSymptoms = symptoms.filter { it.symptomType !in listOf("flow", "pain", "sleep", "headache") }
                        
                        // 显示月经流量
                        if (flowSymptoms.isNotEmpty()) {
                            val flowSymptom = flowSymptoms.maxByOrNull { it.id }
                            val flowText = when(flowSymptom?.intensity) {
                                1 -> "很少"
                                2 -> "少量"
                                3 -> "正常"
                                4 -> "较多"
                                5 -> "很多"
                                else -> "未知"
                            }
                            SymptomItem(icon = Icons.Default.Opacity, label = "月经流量", value = flowText)
                        }
                        
                        // 显示腹痛
                        if (painSymptoms.isNotEmpty()) {
                            val painSymptom = painSymptoms.maxByOrNull { it.id }
                            val painText = when(painSymptom?.intensity) {
                                1 -> "轻微"
                                2 -> "轻度"
                                3 -> "中度"
                                4 -> "偏重"
                                5 -> "剧烈"
                                else -> "未知"
                            }
                            SymptomItem(icon = Icons.Default.MedicalServices, label = "腹痛", value = painText)
                        }
                        
                        // 显示睡眠
                        if (sleepSymptoms.isNotEmpty()) {
                            val sleepSymptom = sleepSymptoms.maxByOrNull { it.id }
                            val sleepText = when(sleepSymptom?.intensity) {
                                1 -> "很差"
                                2 -> "较差"
                                3 -> "一般"
                                4 -> "良好"
                                5 -> "很好"
                                else -> "未知"
                            }
                            SymptomItem(icon = Icons.Default.Bedtime, label = "睡眠", value = sleepText)
                        }
                        
                        // 显示头痛
                        if (headacheSymptoms.isNotEmpty()) {
                            val headacheSymptom = headacheSymptoms.maxByOrNull { it.id }
                            val headacheText = when(headacheSymptom?.intensity) {
                                1 -> "轻微"
                                2 -> "轻度"
                                3 -> "中度"
                                4 -> "偏重"
                                5 -> "剧烈"
                                else -> "未知"
                            }
                            SymptomItem(icon = Icons.Default.SentimentDissatisfied, label = "头痛", value = headacheText)
                        }
                        
                        // 显示其他症状
                        otherSymptoms.forEach { symptom ->
                            val valueText = when(symptom.intensity) {
                                1 -> "轻微"
                                2 -> "轻度"
                                3 -> "中度"
                                4 -> "偏重"
                                5 -> "严重"
                                else -> "未知"
                            }
                            
                            val label = when(symptom.symptomType) {
                                "多毛" -> "多毛"
                                "痤疮" -> "痤疮"
                                "不规则月经" -> "不规则月经"
                                "脱发" -> "脱发"
                                "体重增加" -> "体重增加"
                                "胰岛素抵抗" -> "胰岛素抵抗"
                                "月经稀发" -> "月经稀发"
                                "痘痘" -> "痘痘"
                                "恶心" -> "恶心"
                                "头晕" -> "头晕"
                                "失眠" -> "失眠"
                                else -> symptom.symptomType
                            }
                            
                            SymptomItem(
                                icon = Icons.Default.LocalHospital, 
                                label = label, 
                                value = valueText
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 分割线
            Divider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f),
                thickness = 1.dp
            )
            
            // 添加症状区域
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "添加症状",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF880E4F),
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // 症状类别区域
                    Text(
                        text = "身体症状",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF880E4F),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 流量、痛感、情绪、睡眠四个主要症状卡片
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(140.dp), // 调整高度使一行显示
                        contentPadding = PaddingValues(4.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 流量卡片
                        item {
                            SymptomChip(
                                symptomType = "flow",
                                symptomName = "流量",
                                onClick = {
                                    showSymptomDialog("flow", "流量")
                                }
                            )
                        }
                        
                        // 痛感卡片
                        item {
                            SymptomChip(
                                symptomType = "pain",
                                symptomName = "痛感",
                                onClick = {
                                    showSymptomDialog("pain", "痛感")
                                }
                            )
                        }
                        
                        // 睡眠卡片
                        item {
                            SymptomChip(
                                symptomType = "sleep",
                                symptomName = "睡眠",
                                onClick = {
                                    showSymptomDialog("sleep", "睡眠")
                                }
                            )
                        }
                        
                        // 头痛卡片 (替换情绪卡片)
                        item {
                            SymptomChip(
                                symptomType = "headache",
                                symptomName = "头痛",
                                onClick = {
                                    showSymptomDialog("headache", "头痛")
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 扩展症状
                    Text(
                        text = "更多症状",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF880E4F),
                        modifier = Modifier.padding(bottom = 8.dp, top = 8.dp)
                    )
                    
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp),
                        contentPadding = PaddingValues(4.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 乏力
                        item {
                            SymptomChip(
                                symptomType = "fatigue",
                                symptomName = "乏力",
                                onClick = {
                                    showSymptomDialog("fatigue", "乏力")
                                }
                            )
                        }
                        
                        // 食欲增加
                        item {
                            SymptomChip(
                                symptomType = "increased_appetite",
                                symptomName = "食欲增加",
                                onClick = {
                                    showSymptomDialog("increased_appetite", "食欲增加")
                                }
                            )
                        }
                        
                        // 便秘
                        item {
                            SymptomChip(
                                symptomType = "constipation",
                                symptomName = "便秘",
                                onClick = {
                                    showSymptomDialog("constipation", "便秘")
                                }
                            )
                        }
                        
                        // 痘痘
                        item {
                            SymptomChip(
                                symptomType = "acne",
                                symptomName = "痘痘",
                                onClick = {
                                    showSymptomDialog("acne", "痘痘")
                                }
                            )
                        }
                        
                        // 腰酸
                        item {
                            SymptomChip(
                                symptomType = "backache",
                                symptomName = "腰酸",
                                onClick = {
                                    showSymptomDialog("backache", "腰酸")
                                }
                            )
                        }
                        
                        // 恶心
                        item {
                            SymptomChip(
                                symptomType = "nausea",
                                symptomName = "恶心",
                                onClick = {
                                    showSymptomDialog("nausea", "恶心")
                                }
                            )
                        }
                        
                        // 头晕
                        item {
                            SymptomChip(
                                symptomType = "dizziness",
                                symptomName = "头晕",
                                onClick = {
                                    showSymptomDialog("dizziness", "头晕")
                                }
                            )
                        }
                        
                        // 浮肿
                        item {
                            SymptomChip(
                                symptomType = "bloating",
                                symptomName = "浮肿",
                                onClick = {
                                    showSymptomDialog("bloating", "浮肿")
                                }
                            )
                        }
                        
                        // 失眠
                        item {
                            SymptomChip(
                                symptomType = "insomnia",
                                symptomName = "失眠",
                                onClick = {
                                    showSymptomDialog("insomnia", "失眠")
                                }
                            )
                        }
                    }
                    
                    // 移除了PCOS特定症状区域
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 历史记录区块
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 0.dp
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 标题区域 - 添加点击事件
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                // 点击触发刷新操作
                                isRefreshing = true
                                
                                // 尝试刷新症状数据
                                coroutineScope.launch {
                                    // 创建今天的日期字符串
                                    val today = LocalDate.now()
                                    val todayStr = "${today.year}.${today.monthValue}.${today.dayOfMonth}"
                                    
                                    // 更新历史数据 - 使用真实周期阶段数据
                                    val updatedHistory = generateUpdatedHistoryData(symptoms, cyclePhases)
                                    
                                    // 检查是否有便秘症状记录
                                    val hasConstipationSymptom = symptoms.any { it.symptomType == "constipation" }
                                    
                                    // 获取今天的记录
                                    val todayRecord = updatedHistory.find { it.date == todayStr }
                                    
                                    if (todayRecord != null) {
                                        // 如果今天的记录存在
                                        if (hasConstipationSymptom && !todayRecord.symptoms.contains("便秘")) {
                                            // 有便秘症状记录但历史记录中没有，添加它
                                            val updatedTodayRecord = todayRecord.copy(
                                                symptoms = todayRecord.symptoms + "便秘"
                                            )
                                            // 更新历史记录
                                            symptomHistory = updatedHistory.map { 
                                                if (it.date == todayStr) updatedTodayRecord else it 
                                            }
                                        } else {
                                            // 没有便秘症状或已经包含，直接更新
                                            symptomHistory = updatedHistory
                                        }
                                    } else if (hasConstipationSymptom) {
                                        // 如果今天没有记录但有便秘症状，创建今天的记录
                                        val todayCyclePhase = getCyclePhaseForDate(today, cyclePhases)
                                        val newTodayRecord = DailySymptoms(
                                            date = todayStr,
                                            cyclePhase = todayCyclePhase, // 使用真实周期阶段
                                            symptoms = listOf("便秘")
                                        )
                                        // 添加到历史记录
                                        symptomHistory = listOf(newTodayRecord) + updatedHistory.filterNot { it.date == todayStr }
                                    } else {
                                        // 没有便秘症状且今天没有记录，直接使用更新的历史
                                        symptomHistory = updatedHistory
                                    }
                                    
                                    // 显示刷新中效果
                                    delay(500)
                                    
                                    // 显示Toast提示
                                    context.let {
                                        Toast.makeText(it, "症状数据已同步", Toast.LENGTH_SHORT).show()
                                    }
                                    
                                    // 完成刷新
                                    isRefreshing = false
                                }
                            }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 根据刷新状态显示不同图标
                        if (isRefreshing) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = Color(0xFF880E4F),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = "刷新数据",
                                tint = Color(0xFF880E4F),
                                modifier = Modifier.size(24.dp)
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "同步记录数据",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF880E4F)
                        )
                        
                        Spacer(modifier = Modifier.weight(1f))
                        
                        // 添加刷新提示
                        if (!isRefreshing) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "点击刷新",
                                tint = Color(0xFF880E4F).copy(alpha = 0.6f),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 动态显示历史记录列表
                    if (symptomHistory.isEmpty()) {
                        Text(
                            text = "暂无记录数据",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    } else {
                        // 限制只显示最近3天的记录
                        val recentHistory = symptomHistory.take(3)
                        
                        recentHistory.forEachIndexed { index, dailyRecord ->
                            HistoryItem(
                                date = dailyRecord.date,
                                cyclePhase = dailyRecord.cyclePhase,
                                symptoms = dailyRecord.symptoms
                            )
                            
                            if (index < recentHistory.size - 1) {
                                Divider(
                                    modifier = Modifier.padding(vertical = 8.dp),
                                    color = Color(0xFFEEEEEE),
                                    thickness = 1.dp
                                )
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }

    // 添加对话框
    SymptomIntensityDialog(
        symptomType = selectedSymptomType,
        symptomLabel = selectedSymptomLabel,
        showDialog = showIntensityDialog,
        onDismiss = { showIntensityDialog = false },
        onConfirm = { intensity, notes ->
            // 使用新的函数记录症状并更新历史
            recordSymptomAndUpdateHistory(selectedSymptomType, intensity, notes)
        }
    )

    // 添加底部提示
    var showLastRecordedSymptom by remember { mutableStateOf(false) }
    LaunchedEffect(symptoms) {
        if (symptoms.isNotEmpty()) {
            // 当有新症状记录时显示底部提示
            showLastRecordedSymptom = true
            // 5秒后自动隐藏
            delay(5000)
            showLastRecordedSymptom = false
        }
    }
    
    // 底部浮动提示框，显示最近记录的症状
    if (showLastRecordedSymptom && symptoms.isNotEmpty()) {
        val lastSymptom = symptoms.maxByOrNull { it.id }
        if (lastSymptom != null) {
            val symptomText = when (lastSymptom.symptomType) {
                "flow" -> "流量"
                "pain" -> "腹痛"
                "sleep" -> "睡眠"
                "headache" -> "头痛"
                else -> lastSymptom.symptomType
            }
            
            val intensityText = when (lastSymptom.intensity) {
                1 -> "轻微"
                2 -> "轻度"
                3 -> "中度"
                4 -> "偏重"
                5 -> "严重"
                else -> ""
            }
            
            // 底部浮动提示
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    modifier = Modifier
                        .wrapContentSize()
                        .padding(bottom = 16.dp),
                    shape = RoundedCornerShape(24.dp),
                    color = Color(0xFF880E4F).copy(alpha = 0.9f),
                    shadowElevation = 0.dp // 移除阴影
                ) {
                    Row(
                        modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 10.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "已记录${symptomText}症状(强度:${intensityText})",
                            color = Color.White,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 症状项组件
 */
@Composable
fun SymptomItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.SemiBold
        )
    }
}

/**
 * 症状编辑对话框
 */
@Composable
fun SymptomEditDialog(
    symptom: SymptomRecord,
    symptomLabel: String,
    onDismiss: () -> Unit,
    onConfirm: (Int, String?) -> Unit
) {
    var intensityValue by rememberSaveable { mutableIntStateOf(symptom.intensity) }
    var notesText by rememberSaveable { mutableStateOf(symptom.notes ?: "") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "编辑${symptomLabel}症状",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "调整症状程度",
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 强度选择器
                IntensitySelector(
                    value = intensityValue,
                    onValueChange = { intensityValue = it }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 症状说明文本
                val intensityDescription = when (intensityValue) {
                    1 -> "轻微 - 几乎不影响日常生活"
                    2 -> "轻度 - 稍微影响日常活动"
                    3 -> "中度 - 明显感受到不适"
                    4 -> "偏重 - 影响正常活动"
                    5 -> "严重 - 无法进行正常活动"
                    else -> ""
                }
                
                Text(
                    text = intensityDescription,
                    fontSize = 13.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 编辑备注
                Text(
                    text = "编辑备注",
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 备注输入框
                TextField(
                    value = notesText,
                    onValueChange = { notesText = it },
                    placeholder = { Text("例如：在右侧腹部感到疼痛") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = TextFieldDefaults.colors(
                        focusedContainerColor = Color(0xFFF5F5F5),
                        unfocusedContainerColor = Color(0xFFF5F5F5),
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent
                    ),
                    shape = RoundedCornerShape(8.dp),
                    maxLines = 3
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val finalNotes = if (notesText.isBlank()) null else notesText
                    onConfirm(intensityValue, finalNotes)
                    onDismiss()
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF880E4F)
                )
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            OutlinedButton(
                onClick = onDismiss,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color(0xFF880E4F)
                )
            ) {
                Text("取消")
            }
        },
        containerColor = Color.White,
        shape = RoundedCornerShape(16.dp)
    )
}

/**
 * 症状分类卡片
 */
@Composable
fun SymptomCategoryCard(
    title: String,
    icon: @Composable () -> Unit,
    backgroundColor: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(110.dp)  // 调整卡片高度
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),  // 调整圆角
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),  // 减小内边距
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center  // 垂直居中
        ) {
            Box(
                modifier = Modifier
                    .size(36.dp)  // 减小图标容器大小
                    .clip(CircleShape)
                    .background(Color.White.copy(alpha = 0.5f)),
                contentAlignment = Alignment.Center
            ) {
                icon()
            }
            
            Spacer(modifier = Modifier.height(8.dp))  // 减小间距
            
            Text(
                text = title,
                fontWeight = FontWeight.Medium,
                fontSize = 12.sp  // 减小文字大小
            )
        }
    }
}

/**
 * 症状选择芯片
 */
@Composable
fun SymptomChip(
    symptomType: String,
    symptomName: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(85.dp) // 增加宽度确保文字完整显示
            .height(95.dp)
            .padding(4.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp // 移除阴影
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 使用自定义图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFFCE4EC)),
                contentAlignment = Alignment.Center
            ) {
                when (symptomType) {
                    "flow" -> FlowIcon(modifier = Modifier.size(24.dp))
                    "pain" -> PainIcon(modifier = Modifier.size(24.dp))
                    "sleep" -> SleepIcon(modifier = Modifier.size(24.dp))
                    "mood" -> MoodIcon(modifier = Modifier.size(24.dp))
                    "headache" -> {
                        Canvas(modifier = Modifier.size(24.dp)) {
                            drawCircle(
                                color = Color(0xFFE57373),
                                radius = size.minDimension / 4,
                                center = Offset(size.width / 2, size.height / 2)
                            )
                            
                            // 画放射线条
                            for (i in 0 until 8) {
                                val angle = i * Math.PI / 4
                                val startX = size.width / 2 + (size.minDimension / 3) * cos(angle).toFloat()
                                val startY = size.height / 2 + (size.minDimension / 3) * sin(angle).toFloat()
                                val endX = size.width / 2 + (size.minDimension / 2) * cos(angle).toFloat()
                                val endY = size.height / 2 + (size.minDimension / 2) * sin(angle).toFloat()
                                
                                drawLine(
                                    color = Color(0xFFE57373),
                                    start = Offset(startX, startY),
                                    end = Offset(endX, endY),
                                    strokeWidth = 2f
                                )
                            }
                        }
                    }
                    "nausea" -> {
                        Canvas(modifier = Modifier.size(24.dp)) {
                            // 绘制一个波浪状的线，表示恶心
                            val path = Path()
                            path.moveTo(0f, size.height / 2)
                            
                            val waveHeight = size.height / 6
                            val segmentWidth = size.width / 4
                            
                            for (i in 0..4) {
                                val yOffset = if (i % 2 == 0) waveHeight else -waveHeight
                                path.quadraticBezierTo(
                                    (i * segmentWidth) - segmentWidth / 2, size.height / 2 + yOffset,
                                    i * segmentWidth, size.height / 2
                                )
                            }
                            
                            drawPath(
                                path = path,
                                color = Color(0xFF66BB6A),
                                style = Stroke(width = 2.5f)
                            )
                        }
                    }
                    "dizziness" -> {
                        Canvas(modifier = Modifier.size(24.dp)) {
                            // 画一个头晕的旋转图标
                            val center = Offset(size.width / 2, size.height / 2)
                            val radius = size.minDimension / 2.5f
                            
                            val path = Path()
                            path.moveTo(center.x, center.y - radius)
                            
                            // 创建螺旋形状
                            for (angle in 0..1080 step 30) {
                                val angleRad = Math.toRadians(angle.toDouble())
                                val spiralRadius = radius * (1 - angle / 1080f)
                                val x = center.x + spiralRadius * cos(angleRad).toFloat()
                                val y = center.y + spiralRadius * sin(angleRad).toFloat()
                                path.lineTo(x, y)
                            }
                            
                            drawPath(
                                path = path,
                                color = Color(0xFF9575CD),
                                style = Stroke(width = 2f)
                            )
                        }
                    }
                    "insomnia" -> {
                        Canvas(modifier = Modifier.size(24.dp)) {
                            // 绘制月亮图标表示失眠
                            val moonPath = Path()
                            
                            // 外部圆形
                            moonPath.addOval(
                                Rect(
                                    left = 2f,
                                    top = 2f,
                                    right = size.width - 2f,
                                    bottom = size.height - 2f
                                )
                            )
                            
                            // 内部圆形（用于切除）
                            val innerPath = Path()
                            innerPath.addOval(
                                Rect(
                                    left = size.width * 0.3f,
                                    top = 2f,
                                    right = size.width - 2f,
                                    bottom = size.height - 2f
                                )
                            )
                            
                            // 从外部圆形中切除内部圆形
                            val resultPath = Path().apply {
                                addPath(moonPath)
                                op(moonPath, innerPath, PathOperation.Difference)
                            }
                            
                            drawPath(
                                path = resultPath,
                                color = Color(0xFF5C6BC0),
                                style = Fill
                            )
                            
                            // 绘制几个小星星
                            for (i in 0..2) {
                                val starX = size.width * (0.2f + i * 0.3f)
                                val starY = size.height * (0.2f + i * 0.25f)
                                val starSize = size.minDimension * 0.12f
                                
                                drawCircle(
                                    color = Color(0xFF5C6BC0),
                                    radius = starSize,
                                    center = Offset(starX, starY)
                                )
                            }
                        }
                    }
                    else -> {
                        Icon(
                            imageVector = Icons.Default.FitnessCenter,
                            contentDescription = null,
                            tint = Color(0xFFE57373),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = symptomName,
                color = Color.Black,
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

// 移除了PCOS相关组件和函数

/**
 * 显示Toast消息
 */
private fun showToast(context: Context, message: String) {
    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
}

/**
 * 流量图标 - 手绘风格云朵
 */
@Composable
fun FlowIcon(modifier: Modifier = Modifier) {
    Canvas(
        modifier = modifier
    ) {
        // 尺寸计算
        val width = size.width
        val height = size.height
        val cloudColor = Color(0xFFFFB6C1)
        val dropColor = Color(0xFFFF69B4)
        
        // 绘制云朵
        val path = Path().apply {
            // 左侧圆形
            moveTo(width * 0.3f, height * 0.45f)
            addOval(Rect(
                offset = Offset(width * 0.2f, height * 0.35f),
                size = Size(width * 0.2f, height * 0.2f)
            ))
            
            // 中间圆形
            addOval(Rect(
                offset = Offset(width * 0.35f, height * 0.25f),
                size = Size(width * 0.3f, height * 0.3f)
            ))
            
            // 右侧圆形
            addOval(Rect(
                offset = Offset(width * 0.6f, height * 0.35f),
                size = Size(width * 0.2f, height * 0.2f)
            ))
            
            // 底部连接
            addRect(Rect(
                offset = Offset(width * 0.25f, height * 0.45f),
                size = Size(width * 0.5f, height * 0.1f)
            ))
            
            close()
        }
        
        drawPath(
            path = path,
            color = cloudColor,
            style = Fill
        )
        
        // 绘制两个水滴
        val dropSize = width * 0.08f
        drawCircle(
            color = dropColor,
            radius = dropSize,
            center = Offset(width * 0.35f, height * 0.75f)
        )
        drawCircle(
            color = dropColor,
            radius = dropSize,
            center = Offset(width * 0.65f, height * 0.75f)
        )
        
        // 绘制眼睛
        val eyeSize = width * 0.03f
        drawCircle(
            color = Color.Black,
            radius = eyeSize,
            center = Offset(width * 0.4f, height * 0.35f)
        )
        drawCircle(
            color = Color.Black,
            radius = eyeSize,
            center = Offset(width * 0.6f, height * 0.35f)
        )
    }
}

/**
 * 痛感图标 - 手绘风格表情
 */
@Composable
fun PainIcon(modifier: Modifier = Modifier) {
    Canvas(
        modifier = modifier
    ) {
        // 尺寸计算
        val width = size.width
        val height = size.height
        val center = Offset(width / 2, height / 2)
        val radius = width.coerceAtMost(height) / 2 * 0.8f
        
        // 绘制圆形脸
        drawCircle(
            color = Color(0xFFFFC107),
            radius = radius,
            center = center
        )
        
        // 绘制眉毛 - 不开心
        val eyebrowLength = radius * 0.4f
        val eyebrowYOffset = radius * 0.3f
        val eyebrowXOffset = radius * 0.3f
        
        // 左眉毛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(center.x - eyebrowXOffset, center.y - eyebrowYOffset),
            end = Offset(center.x - eyebrowXOffset + eyebrowLength, center.y - eyebrowYOffset - eyebrowLength * 0.3f),
            strokeWidth = radius * 0.08f
        )
        
        // 右眉毛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(center.x + eyebrowXOffset, center.y - eyebrowYOffset),
            end = Offset(center.x + eyebrowXOffset - eyebrowLength, center.y - eyebrowYOffset - eyebrowLength * 0.3f),
            strokeWidth = radius * 0.08f
        )
        
        // 绘制眼睛
        val eyeSize = radius * 0.1f
        drawCircle(
            color = Color(0xFF795548),
            radius = eyeSize,
            center = Offset(center.x - radius * 0.3f, center.y - radius * 0.05f)
        )
        drawCircle(
            color = Color(0xFF795548),
            radius = eyeSize,
            center = Offset(center.x + radius * 0.3f, center.y - radius * 0.05f)
        )
        
        // 绘制嘴巴 - 不开心
        val path = Path().apply {
            moveTo(center.x - radius * 0.3f, center.y + radius * 0.3f)
            quadraticBezierTo(
                center.x, center.y + radius * 0.1f,
                center.x + radius * 0.3f, center.y + radius * 0.3f
            )
        }
        
        drawPath(
            path = path,
            color = Color(0xFF795548),
            style = Stroke(width = radius * 0.08f, cap = StrokeCap.Round)
        )
    }
}

/**
 * 情绪图标 - 手绘风格笑脸
 */
@Composable
fun MoodIcon(modifier: Modifier = Modifier) {
    Canvas(
        modifier = modifier
    ) {
        // 尺寸计算
        val width = size.width
        val height = size.height
        val center = Offset(width / 2, height / 2)
        val radius = width.coerceAtMost(height) / 2 * 0.8f
        
        // 绘制圆形脸
        drawCircle(
            color = Color(0xFFFFC107),
            radius = radius,
            center = center
        )
        
        // 绘制眼睛
        val eyeSize = radius * 0.1f
        drawCircle(
            color = Color(0xFF795548),
            radius = eyeSize,
            center = Offset(center.x - radius * 0.3f, center.y - radius * 0.1f)
        )
        drawCircle(
            color = Color(0xFF795548),
            radius = eyeSize,
            center = Offset(center.x + radius * 0.3f, center.y - radius * 0.1f)
        )
        
        // 绘制微笑
        val path = Path().apply {
            moveTo(center.x - radius * 0.3f, center.y + radius * 0.1f)
            quadraticBezierTo(
                center.x, center.y + radius * 0.3f,
                center.x + radius * 0.3f, center.y + radius * 0.1f
            )
        }
        
        drawPath(
            path = path,
            color = Color(0xFF795548),
            style = Stroke(width = radius * 0.08f, cap = StrokeCap.Round)
        )
    }
}

/**
 * 睡眠图标 - 手绘风格叶子
 */
@Composable
fun SleepIcon(modifier: Modifier = Modifier) {
    Canvas(
        modifier = modifier
    ) {
        // 尺寸计算
        val width = size.width
        val height = size.height
        val leafColor = Color(0xFF8BC34A)
        
        // 绘制叶子形状
        val path = Path().apply {
            moveTo(width * 0.2f, height * 0.8f) // 左下角开始
            
            // 绘制叶子轮廓
            cubicTo(
                width * 0.1f, height * 0.5f, // 控制点1
                width * 0.3f, height * 0.1f, // 控制点2
                width * 0.5f, height * 0.2f  // 终点
            )
            
            cubicTo(
                width * 0.7f, height * 0.3f, // 控制点1
                width * 0.9f, height * 0.5f, // 控制点2
                width * 0.7f, height * 0.8f  // 终点
            )
            
            close()
        }
        
        drawPath(
            path = path,
            color = leafColor,
            style = Fill
        )
        
        // 绘制叶脉
        val veinPath = Path().apply {
            moveTo(width * 0.5f, height * 0.3f)
            lineTo(width * 0.5f, height * 0.7f)
        }
        
        drawPath(
            path = veinPath,
            color = Color.White,
            style = Stroke(width = width * 0.03f)
        )
        
        // 绘制侧脉
        for (i in 1..3) {
            val y = height * (0.3f + i * 0.1f)
            val sidePath = Path().apply {
                moveTo(width * 0.5f, y)
                lineTo(width * (0.5f + i * 0.08f), y - height * 0.05f)
            }
            
            drawPath(
                path = sidePath,
                color = Color.White,
                style = Stroke(width = width * 0.02f)
            )
            
            val sidePath2 = Path().apply {
                moveTo(width * 0.5f, y)
                lineTo(width * (0.5f - i * 0.08f), y - height * 0.05f)
            }
            
            drawPath(
                path = sidePath2,
                color = Color.White,
                style = Stroke(width = width * 0.02f)
            )
        }
    }
}

/**
 * 症状强度选择对话框
 */
@Composable
fun SymptomIntensityDialog(
    symptomType: String,
    symptomLabel: String,
    showDialog: Boolean,
    onDismiss: () -> Unit,
    onConfirm: (Int, String?) -> Unit
) {
    var intensityValue by rememberSaveable { mutableIntStateOf(3) }
    var notesText by rememberSaveable { mutableStateOf("") }
    
    if (showDialog) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = "记录$symptomLabel",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "症状程度",
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 强度选择器
                    IntensitySelector(
                        value = intensityValue,
                        onValueChange = { intensityValue = it }
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 症状说明文本
                    val intensityDescription = when (intensityValue) {
                        1 -> "轻微 - 几乎不影响日常生活"
                        2 -> "轻度 - 稍微影响日常活动"
                        3 -> "中度 - 明显感受到不适"
                        4 -> "偏重 - 影响正常活动"
                        5 -> "严重 - 无法进行正常活动"
                        else -> ""
                    }
                    
                    Text(
                        text = intensityDescription,
                        fontSize = 13.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // 添加备注
                    Text(
                        text = "添加备注 (可选)",
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 备注输入框
                    TextField(
                        value = notesText,
                        onValueChange = { notesText = it },
                        placeholder = { Text("例如：在右侧腹部感到疼痛") },
                        modifier = Modifier.fillMaxWidth(),
                        colors = TextFieldDefaults.colors(
                            focusedContainerColor = Color(0xFFF5F5F5),
                            unfocusedContainerColor = Color(0xFFF5F5F5),
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent
                        ),
                        shape = RoundedCornerShape(8.dp),
                        maxLines = 3
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        val finalNotes = if (notesText.isBlank()) null else notesText
                        onConfirm(intensityValue, finalNotes)
                        onDismiss()
                    },
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF880E4F))
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                OutlinedButton(
                    onClick = onDismiss,
                    colors = ButtonDefaults.outlinedButtonColors(contentColor = Color(0xFF880E4F))
                ) {
                    Text("取消")
                }
            },
            containerColor = Color.White,
            shape = RoundedCornerShape(16.dp)
        )
    }
}

/**
 * 强度选择器组件
 */
@Composable
fun IntensitySelector(
    value: Int,
    onValueChange: (Int) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        for (i in 1..5) {
            val isSelected = i == value
            
            val backgroundColor = when {
                isSelected -> when (i) {
                    1 -> Color(0xFFE8F5E9)
                    2 -> Color(0xFFC8E6C9)
                    3 -> Color(0xFFFFF9C4)
                    4 -> Color(0xFFFFCCBC)
                    5 -> Color(0xFFFFCDD2)
                    else -> Color.LightGray
                }
                else -> Color(0xFFF5F5F5)
            }
            
            val textColor = when {
                isSelected -> when (i) {
                    1 -> Color(0xFF388E3C)
                    2 -> Color(0xFF388E3C)
                    3 -> Color(0xFFFFA000)
                    4 -> Color(0xFFE64A19)
                    5 -> Color(0xFFD32F2F)
                    else -> Color.DarkGray
                }
                else -> Color.Gray
            }
            
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(backgroundColor)
                    .border(
                        width = if (isSelected) 2.dp else 1.dp,
                        color = if (isSelected) textColor else Color.LightGray,
                        shape = CircleShape
                    )
                    .clickable { onValueChange(i) },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = i.toString(),
                    fontSize = 16.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    color = textColor
                )
            }
        }
    }
}

// 添加历史记录项组件
@Composable
fun HistoryItem(
    date: String,
    cyclePhase: String,
    symptoms: List<String>
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 日期区域
        Surface(
            shape = RoundedCornerShape(8.dp),
            color = Color(0xFFfef2f6),
            shadowElevation = 0.dp // 移除阴影
        ) {
            Text(
                text = date,
                color = Color(0xFF880E4F),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 周期阶段标签
        Surface(
            shape = RoundedCornerShape(4.dp),
            color = Color(0xFFf9f8f9),
            shadowElevation = 0.dp // 移除阴影
        ) {
            Text(
                text = cyclePhase,
                color = Color(0xFF715f74),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 症状列表，直接显示在同一排
        Text(
            text = symptoms.joinToString("、"),
            color = Color(0xFF303030), 
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal
        )
    }
}

// 根据症状类型获取显示名称
private fun getSymptomLabel(symptomType: String): String {
    return when (symptomType) {
        "flow" -> "流量"
        "pain" -> "腹痛"
        "sleep" -> "睡眠"
        "headache" -> "头痛"
        "fatigue" -> "乏力"
        "increased_appetite" -> "食欲增加"
        "constipation" -> "便秘"
        "acne" -> "痘痘" 
        "backache" -> "腰酸"
        "nausea" -> "恶心"
        "dizziness" -> "头晕"
        "bloating" -> "浮肿"
        "insomnia" -> "失眠"
        // 移除了PCOS症状映射
        else -> symptomType
    }
}

// 获取当前周期阶段
private fun getCyclePhaseForDate(date: LocalDate, cyclePhases: Map<LocalDate, String>): String {
    // 从ViewModel提供的周期阶段数据中获取
    return cyclePhases[date] ?: run {
        // 如果没有找到，使用默认逻辑
        val today = LocalDate.now()
        when {
            date.isEqual(today) || date.isAfter(today.minusDays(5)) && date.isBefore(today.plusDays(1)) -> "月经期"
            date.isAfter(today.plusDays(10)) && date.isBefore(today.plusDays(16)) -> "排卵期"
            else -> "黄体期"
        }
    }
}

// 生成初始历史数据
private fun generateInitialHistoryData(): List<DailySymptoms> {
    // 这里只是为了初始显示，实际数据会在refreshHistoryData或generateUpdatedHistoryData中更新
    return emptyList()
}

// 添加专门的函数用于刷新历史记录数据
private fun refreshHistoryData(cyclePhases: Map<LocalDate, String>, currentSymptoms: List<SymptomRecord>): List<DailySymptoms> {
    val today = LocalDate.now()
    val yesterday = today.minusDays(1)
    val twoDaysAgo = today.minusDays(2)
    
    // 创建过去三天的日期，使用这些对象作为key查询周期阶段
    val todayDate = today
    val yesterdayDate = yesterday
    val twoDaysAgoDate = twoDaysAgo
    
    // 日期格式化为"yyyy.M.d"格式
    val todayStr = "${today.year}.${today.monthValue}.${today.dayOfMonth}"
    val yesterdayStr = "${yesterday.year}.${yesterday.monthValue}.${yesterday.dayOfMonth}"
    val twoDaysAgoStr = "${twoDaysAgo.year}.${twoDaysAgo.monthValue}.${twoDaysAgo.dayOfMonth}"
    
    // 从cyclePhases获取周期阶段
    val todayCyclePhase = cyclePhases[todayDate] ?: "非经期"
    val yesterdayCyclePhase = cyclePhases[yesterdayDate] ?: "非经期"
    val twoDaysAgoCyclePhase = cyclePhases[twoDaysAgoDate] ?: "非经期"
    
    // 获取各天的症状记录
    val todaySymptoms = currentSymptoms
        .filter { it.date.isEqual(todayDate) }
        .map { getSymptomLabel(it.symptomType) }
        .distinct()
        .toMutableList()
    
    val yesterdaySymptoms = currentSymptoms
        .filter { it.date.isEqual(yesterdayDate) }
        .map { getSymptomLabel(it.symptomType) }
        .distinct()
        .toMutableList()
    
    val twoDaysAgoSymptoms = currentSymptoms
        .filter { it.date.isEqual(twoDaysAgoDate) }
        .map { getSymptomLabel(it.symptomType) }
        .distinct()
        .toMutableList()
    
    // 确保便秘症状在记录中
    if (currentSymptoms.any { it.symptomType == "constipation" && it.date.isEqual(todayDate) } && !todaySymptoms.contains("便秘")) {
        todaySymptoms.add("便秘")
    }
    
    // 如果没有症状记录，添加一些样本数据
    if (todaySymptoms.isEmpty() && todayCyclePhase == "月经期") {
        todaySymptoms.add("腹痛")
        todaySymptoms.add("流量")
    }
    
    if (yesterdaySymptoms.isEmpty() && yesterdayCyclePhase == "月经期") {
        yesterdaySymptoms.add("腹痛")
    }
    
    if (twoDaysAgoSymptoms.isEmpty() && twoDaysAgoCyclePhase == "月经期") {
        twoDaysAgoSymptoms.add("腹痛")
        twoDaysAgoSymptoms.add("头痛")
    }
    
    // 创建过去三天的历史记录
    return listOf(
        DailySymptoms(
            date = todayStr,
            cyclePhase = todayCyclePhase,
            symptoms = todaySymptoms
        ),
        DailySymptoms(
            date = yesterdayStr, 
            cyclePhase = yesterdayCyclePhase,
            symptoms = yesterdaySymptoms
        ),
        DailySymptoms(
            date = twoDaysAgoStr,
            cyclePhase = twoDaysAgoCyclePhase,
            symptoms = twoDaysAgoSymptoms
        )
    )
}

// 删除末尾重复的SymptomsDetailScreen函数
private fun generateUpdatedHistoryData(currentSymptoms: List<SymptomRecord>, cyclePhases: Map<LocalDate, String>): List<DailySymptoms> {
    return refreshHistoryData(cyclePhases, currentSymptoms)
}

/**
 * 现代化的日期头部组件
 */
@Composable
fun ModernDateHeader(
    selectedDate: LocalDate,
    cyclePhases: Map<LocalDate, String>
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    val cyclePhase = cyclePhases[selectedDate] ?: "周期中"

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = selectedDate.format(dateFormatter),
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF880E4F)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = getPhaseColor(cyclePhase),
                            shape = CircleShape
                        )
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = cyclePhase,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 快速症状记录区域
 */
@Composable
fun QuickSymptomRecordSection(
    onSymptomSelected: (String, String) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "记录症状",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 症状分类
            SymptomCategorySection(
                title = "月经相关",
                symptoms = listOf(
                    ModernSymptomItem("flow", "流量", "🩸", Color(0xFFFFEBEE)),
                    ModernSymptomItem("pain", "痛感", "😣", Color(0xFFE3F2FD)),
                    ModernSymptomItem("cramps", "痉挛", "💢", Color(0xFFFFF3E0))
                ),
                onSymptomSelected = onSymptomSelected
            )

            Spacer(modifier = Modifier.height(16.dp))

            SymptomCategorySection(
                title = "身体状况",
                symptoms = listOf(
                    ModernSymptomItem("sleep", "睡眠", "😴", Color(0xFFE8F5E9)),
                    ModernSymptomItem("headache", "头痛", "🤕", Color(0xFFF3E5F5)),
                    ModernSymptomItem("mood", "情绪", "😊", Color(0xFFFFF8E1)),
                    ModernSymptomItem("energy", "精力", "⚡", Color(0xFFE0F2F1))
                ),
                onSymptomSelected = onSymptomSelected
            )
        }
    }
}

/**
 * 症状分类区域
 */
@Composable
fun SymptomCategorySection(
    title: String,
    symptoms: List<ModernSymptomItem>,
    onSymptomSelected: (String, String) -> Unit
) {
    Column {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Gray,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.height(80.dp)
        ) {
            items(symptoms) { symptom ->
                ModernSymptomButton(
                    symptom = symptom,
                    onClick = { onSymptomSelected(symptom.type, symptom.label) }
                )
            }
        }
    }
}

/**
 * 现代化症状按钮
 */
@Composable
fun ModernSymptomButton(
    symptom: ModernSymptomItem,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(symptom.backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = symptom.emoji,
                fontSize = 24.sp
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = symptom.label,
            fontSize = 10.sp,
            color = Color.DarkGray,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

/**
 * 已记录症状区域
 */
@Composable
fun RecordedSymptomsSection(
    symptoms: List<SymptomRecord>
) {
    if (symptoms.isEmpty()) return

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "今日记录",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 按症状类型分组显示
            val groupedSymptoms = symptoms.groupBy { it.symptomType }

            groupedSymptoms.forEach { (type, symptomList) ->
                val latestSymptom = symptomList.maxByOrNull { it.id }
                latestSymptom?.let { symptom ->
                    RecordedSymptomItem(
                        type = type,
                        intensity = symptom.intensity,
                        notes = symptom.notes
                    )

                    if (type != groupedSymptoms.keys.last()) {
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }
            }
        }
    }
}

/**
 * 已记录症状项
 */
@Composable
fun RecordedSymptomItem(
    type: String,
    intensity: Int,
    notes: String?
) {
    val (emoji, label, intensityText) = getSymptomDisplayInfo(type, intensity)

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = emoji,
            fontSize = 20.sp,
            modifier = Modifier.size(32.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = label,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray
            )

            Text(
                text = intensityText,
                fontSize = 14.sp,
                color = Color.Gray
            )

            if (!notes.isNullOrBlank()) {
                Text(
                    text = notes,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }

        // 强度指示器
        IntensityIndicator(intensity = intensity)
    }
}

/**
 * 强度指示器
 */
@Composable
fun IntensityIndicator(intensity: Int) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        repeat(5) { index ->
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(
                        color = if (index < intensity) Color(0xFF880E4F) else Color.LightGray,
                        shape = CircleShape
                    )
            )
        }
    }
}

/**
 * 现代化症状数据类
 */
data class ModernSymptomItem(
    val type: String,
    val label: String,
    val emoji: String,
    val backgroundColor: Color
)

/**
 * 辅助函数
 */
private fun getPhaseColor(phase: String): Color {
    return when (phase) {
        "月经期" -> Color(0xFFE57F9B)
        "排卵期" -> Color(0xFF9ABF68)
        "卵泡期" -> Color(0xFF9E68BF)
        "黄体期" -> Color(0xFFCFC432)
        else -> Color.Gray
    }
}

private fun getSymptomDisplayInfo(type: String, intensity: Int): Triple<String, String, String> {
    return when (type) {
        "flow" -> Triple("🩸", "月经流量", when(intensity) {
            1 -> "很少"; 2 -> "少量"; 3 -> "正常"; 4 -> "较多"; 5 -> "很多"
            else -> "未知"
        })
        "pain" -> Triple("😣", "腹痛", when(intensity) {
            1 -> "轻微"; 2 -> "轻度"; 3 -> "中度"; 4 -> "偏重"; 5 -> "剧烈"
            else -> "未知"
        })
        "sleep" -> Triple("😴", "睡眠", when(intensity) {
            1 -> "很差"; 2 -> "较差"; 3 -> "一般"; 4 -> "良好"; 5 -> "很好"
            else -> "未知"
        })
        "headache" -> Triple("🤕", "头痛", when(intensity) {
            1 -> "轻微"; 2 -> "轻度"; 3 -> "中度"; 4 -> "偏重"; 5 -> "剧烈"
            else -> "未知"
        })
        "mood" -> Triple("😊", "情绪", when(intensity) {
            1 -> "很差"; 2 -> "较差"; 3 -> "一般"; 4 -> "良好"; 5 -> "很好"
            else -> "未知"
        })
        else -> Triple("📝", type, "强度 $intensity")
    }
}