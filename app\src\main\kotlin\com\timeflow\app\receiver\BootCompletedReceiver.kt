package com.timeflow.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.model.HabitModel
import com.timeflow.app.data.converter.TaskConverter
import com.timeflow.app.service.TaskReminderScheduler
import com.timeflow.app.service.DailyReviewScheduler
import com.timeflow.app.worker.HabitReminderWorker
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.first
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import javax.inject.Inject

/**
 * 开机完成接收器
 * 在设备重启后恢复所有习惯提醒和任务提醒
 */
@AndroidEntryPoint
class BootCompletedReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var habitRepository: HabitRepository
    
    @Inject
    lateinit var taskRepository: TaskRepository
    
    @Inject
    lateinit var taskReminderScheduler: TaskReminderScheduler
    
    @Inject
    lateinit var dailyReviewScheduler: DailyReviewScheduler
    
    // 创建协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 通知设置DataStore - 与NotificationSettingsViewModel保持一致
    private val Context.notificationSettingsDataStore by preferencesDataStore(name = "notification_settings")
    
    // DataStore键定义
    private object PreferencesKeys {
        val DAILY_REVIEW_ENABLED = booleanPreferencesKey("daily_review_enabled")
        val DAILY_REVIEW_TIME = stringPreferencesKey("daily_review_time")
    }
    
    companion object {
        private const val TAG = "BootCompletedReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            Log.d(TAG, "设备启动完成，恢复习惯提醒和任务提醒")
            
            // 在协程中重新设置所有提醒
            scope.launch {
                try {
                    // 🔔 恢复习惯提醒
                    val habits = habitRepository.getAllHabits()
                        .filter { it.reminderEnabled }
                    
                    Log.d(TAG, "找到 ${habits.size} 个需要恢复提醒的习惯")
                    
                    habits.forEach { habit ->
                        rescheduleHabitReminder(context, habit)
                    }
                    
                    // 🔔 新增：恢复任务提醒
                    val tasks = taskRepository.getAllTasks()
                        .filter { task -> 
                            // 只恢复有提醒时间且未完成的任务
                            (task.dueDate != null || task.reminderTime != null) && !task.isCompleted
                        }
                    
                    Log.d(TAG, "找到 ${tasks.size} 个需要恢复提醒的任务")
                    
                    // 将Task模型转换为Task实体
                    val taskEntities = TaskConverter.toEntityTasks(tasks)
                    taskReminderScheduler.scheduleRemindersForTasks(taskEntities)
                    
                    // 🔔 新增：恢复每日回顾提醒
                    restoreDailyReviewReminder(context)
                    
                    Log.d(TAG, "所有提醒恢复完成")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "恢复提醒失败: ${e.message}", e)
                }
            }
        }
    }
    
    /**
     * 重新调度习惯提醒
     */
    private fun rescheduleHabitReminder(context: Context, habit: HabitModel) {
        try {
            // 创建一次性工作请求，设置提醒
            val reminderRequest = OneTimeWorkRequestBuilder<HabitReminderWorker>()
                .setInputData(
                    androidx.work.Data.Builder()
                        .putString(HabitReminderWorker.KEY_HABIT_ID, habit.id)
                        .putString(HabitReminderWorker.KEY_HABIT_NAME, habit.name)
                        .putString(HabitReminderWorker.KEY_REMINDER_TIME, habit.reminderTime?.toString() ?: "08:00")
                        .build()
                )
                .build()
            
            // 使用WorkManager调度工作
            WorkManager.getInstance(context)
                .enqueue(reminderRequest)
            
            Log.d(TAG, "已重新设置习惯[${habit.name}]的提醒")
        } catch (e: Exception) {
            Log.e(TAG, "重新设置习惯[${habit.name}]提醒失败: ${e.message}", e)
        }
    }
    
    /**
     * 恢复每日回顾提醒
     */
    private suspend fun restoreDailyReviewReminder(context: Context) {
        try {
            // 读取每日回顾设置
            val preferences = context.notificationSettingsDataStore.data.first()
            val dailyReviewEnabled = preferences[PreferencesKeys.DAILY_REVIEW_ENABLED] ?: true
            val dailyReviewTime = preferences[PreferencesKeys.DAILY_REVIEW_TIME] ?: "21:00"
            
            if (dailyReviewEnabled) {
                // 重新设置每日回顾提醒
                dailyReviewScheduler.scheduleDailyReview(dailyReviewTime)
                Log.d(TAG, "✅ 已恢复每日回顾提醒，时间: $dailyReviewTime")
            } else {
                Log.d(TAG, "❌ 每日回顾已禁用，跳过恢复")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "恢复每日回顾提醒失败: ${e.message}", e)
        }
    }
} 