package com.timeflow.app.utils

import android.content.Context
import android.content.Intent
import android.os.Process
import android.util.Log
import com.timeflow.app.di.CrashReporter
import com.timeflow.app.ui.MainActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import java.io.FileOutputStream
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import timber.log.Timber

/**
 * 全局异常捕获处理器
 * 用于捕获未处理的异常，记录日志，并在可能的情况下重新启动应用
 */
@Singleton
class AppExceptionHandler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val crashReporter: CrashReporter
) : Thread.UncaughtExceptionHandler {
    
    private val TAG = "AppExceptionHandler"
    private val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    private val logDir by lazy { File(context.filesDir, "crash_logs") }
    
    init {
        // 确保日志目录存在
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
    }
  
    override fun uncaughtException(thread: Thread, throwable: Throwable) {
        try {
            // 1. 提取关键异常特征
            val crashInfo = analyzeCrash(throwable)
            
            // 2. 保存崩溃日志到本地
            saveCrashLog(crashInfo)
            
            // 3. 上传崩溃日志
            uploadCrashLog(throwable, thread.name)
            
            // 4. 安全重启应用
            safeRestartApp()
        } catch (e: Exception) {
            // 如果我们的处理器本身出现异常，交给系统默认处理器处理
            Timber.e(e, "处理异常时发生错误")
            defaultHandler?.uncaughtException(thread, throwable)
        } finally {
            // 结束当前进程，让重启流程可以接管
            Process.killProcess(Process.myPid())
            System.exit(1)
        }
    }
    
    /**
     * 分析崩溃信息，提取关键特征
     */
    private fun analyzeCrash(throwable: Throwable): String {
        val sw = StringWriter()
        val pw = PrintWriter(sw)
        throwable.printStackTrace(pw)
        
        val stackTrace = sw.toString()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val timestamp = dateFormat.format(Date())
        
        val deviceInfo = """
            |时间: $timestamp
            |设备: ${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}
            |Android版本: ${android.os.Build.VERSION.RELEASE} (API ${android.os.Build.VERSION.SDK_INT})
            |应用版本: ${context.packageManager.getPackageInfo(context.packageName, 0).versionName}
            |异常类型: ${throwable.javaClass.name}
            |异常消息: ${throwable.message}
            |堆栈跟踪:
            |$stackTrace
        """.trimMargin()
        
        return deviceInfo
    }
    
    /**
     * 保存崩溃日志到本地文件
     */
    private fun saveCrashLog(crashInfo: String) {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val filename = "crash_$timestamp.txt"
        val crashFile = File(logDir, filename)
        
        FileOutputStream(crashFile).use { stream ->
            stream.write(crashInfo.toByteArray())
        }
        
        Timber.d("崩溃日志已保存到 ${crashFile.absolutePath}")
    }
    
    /**
     * 上传崩溃日志
     * 使用注入的CrashReporter接口，方便在不同实现间切换
     */
    private fun uploadCrashLog(throwable: Throwable, threadName: String) {
        try {
            // 设置关键设备信息
            crashReporter.setCustomKey("thread_name", threadName)
            crashReporter.setCustomKey("device_manufacturer", android.os.Build.MANUFACTURER)
            crashReporter.setCustomKey("device_model", android.os.Build.MODEL)
            crashReporter.setCustomKey("android_version", android.os.Build.VERSION.RELEASE)
            crashReporter.setCustomKey("api_level", android.os.Build.VERSION.SDK_INT.toString())
            
            // 记录崩溃前的日志
            crashReporter.log("应用在线程 '$threadName' 中崩溃")
            
            // 记录异常
            crashReporter.recordException(throwable)
            
            Timber.d("崩溃日志已通过崩溃报告器记录")
        } catch (e: Exception) {
            Timber.e(e, "上传崩溃日志失败")
        }
    }
    
    /**
     * 安全重启应用
     * 使用非主线程进行重启，避免在异常状态下直接重启
     */
    private fun safeRestartApp() {
        try {
            // 创建一个延迟重启意图，给系统一些时间来完成其他清理工作
            val intent = Intent(context, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or 
                         Intent.FLAG_ACTIVITY_CLEAR_TASK or
                         Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtra("FROM_CRASH", true)
            }
            
            // 延迟100毫秒启动，确保有足够时间完成当前操作
            Thread.sleep(100)
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "重启应用失败")
        }
    }
} 