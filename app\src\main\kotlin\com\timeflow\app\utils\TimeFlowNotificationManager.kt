package com.timeflow.app.utils

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.timeflow.app.R
import com.timeflow.app.TimeFlowApplication
import com.timeflow.app.ui.MainActivity
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TimeFlow通知管理器
 * 参照Todoist、TickTick、Things等知名时间管理应用的通知设计
 */
@Singleton
class TimeFlowNotificationManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val notificationHelper: NotificationHelper
) {
    
    companion object {
        private const val TAG = "TimeFlowNotification"
        
        // 通知ID
        const val TASK_REMINDER_ID = 1001
        const val DEADLINE_REMINDER_ID = 1002
        const val OVERDUE_REMINDER_ID = 1003
        const val DAILY_REVIEW_ID = 1004
        const val HABIT_REMINDER_ID = 1005
        const val FOCUS_START_ID = 1006
        const val FOCUS_END_ID = 1007
        const val BREAK_REMINDER_ID = 1008
        const val MEDICATION_REMINDER_ID = 1009  // 新增用药提醒ID
        const val AI_SUGGESTION_ID = 1010
        const val WEEKLY_REPORT_ID = 1011
        const val DAILY_SUMMARY_ID = 1012

        // 通知分组
        const val GROUP_TASKS = "group_tasks"
        const val GROUP_HABITS = "group_habits"
        const val GROUP_FOCUS = "group_focus"
        const val GROUP_MEDICATION = "group_medication"  // 新增用药分组
        const val GROUP_AI = "group_ai"
    }
    
    /**
     * 显示任务提醒通知
     */
    suspend fun showTaskReminder(
        taskId: String,
        taskTitle: String,
        dueTime: String,
        priority: String = "中等",
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.taskRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASK", true)
            putExtra("TASK_ID", taskId)
        }
        
        val pendingIntent = createPendingIntent(TASK_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("📋 任务提醒")
            .setContentText("$taskTitle 即将到期")
            .setSubText("截止时间：$dueTime")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("任务「$taskTitle」即将到期，截止时间：$dueTime\n\n优先级：$priority\n\n点击查看详情"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(when (priority) {
                "紧急", "高" -> NotificationCompat.PRIORITY_HIGH
                "低" -> NotificationCompat.PRIORITY_LOW
                else -> NotificationCompat.PRIORITY_DEFAULT
            })
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_TASKS)
            .apply {
                configureNotificationStyle(this, settings)
                addTaskActions(this, taskId)
            }
            .build()
        
        notificationHelper.notify(TASK_REMINDER_ID + taskId.hashCode(), notification)
        Log.d(TAG, "任务提醒通知已发送: $taskTitle")
    }
    
    /**
     * 显示任务开始提醒通知
     */
    suspend fun showTaskStartReminder(
        taskId: String,
        taskTitle: String,
        startTime: String,
        priority: String = "中等",
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.taskRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASK", true)
            putExtra("TASK_ID", taskId)
        }
        
        val pendingIntent = createPendingIntent(TASK_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("🚀 该开始任务了")
            .setContentText("$taskTitle 的开始时间到了")
            .setSubText("开始时间：$startTime")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("任务「$taskTitle」的开始时间到了\n\n开始时间：$startTime\n优先级：$priority\n\n点击开始工作"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(when (priority) {
                "紧急", "高" -> NotificationCompat.PRIORITY_HIGH
                "低" -> NotificationCompat.PRIORITY_LOW
                else -> NotificationCompat.PRIORITY_DEFAULT
            })
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_TASKS)
            .apply {
                configureNotificationStyle(this, settings)
                addTaskActions(this, taskId)
            }
            .build()
        
        notificationHelper.notify(TASK_REMINDER_ID + taskId.hashCode(), notification)
        Log.d(TAG, "任务开始提醒通知已发送: $taskTitle")
    }

    /**
     * 显示任务截止前提醒通知
     */
    suspend fun showTaskDeadlineReminder(
        taskId: String,
        taskTitle: String,
        priority: String = "中等",
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.taskRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASK", true)
            putExtra("TASK_ID", taskId)
        }
        
        val pendingIntent = createPendingIntent(TASK_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("⏰ 任务即将截止")
            .setContentText("$taskTitle 还有1小时就要截止了")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("任务「$taskTitle」还有1小时就要截止了\n\n优先级：$priority\n\n请抓紧时间完成"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_TASKS)
            .apply {
                configureNotificationStyle(this, settings)
                addTaskActions(this, taskId)
            }
            .build()
        
        notificationHelper.notify(TASK_REMINDER_ID + taskId.hashCode(), notification)
        Log.d(TAG, "任务截止前提醒通知已发送: $taskTitle")
    }
    
    /**
     * 显示截止日期提醒
     */
    suspend fun showDeadlineReminder(
        taskId: String,
        taskTitle: String,
        deadline: String,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.deadlineRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASK", true)
            putExtra("TASK_ID", taskId)
        }
        
        val pendingIntent = createPendingIntent(DEADLINE_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("⏰ 截止提醒")
            .setContentText("$taskTitle 即将截止")
            .setSubText("截止：$deadline")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("任务「$taskTitle」即将截止\n\n截止时间：$deadline\n\n请及时完成，避免逾期"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_TASKS)
            .apply {
                configureNotificationStyle(this, settings)
                addTaskActions(this, taskId)
            }
            .build()
        
        notificationHelper.notify(DEADLINE_REMINDER_ID + taskId.hashCode(), notification)
        Log.d(TAG, "截止日期提醒已发送: $taskTitle")
    }
    
    /**
     * 显示逾期任务提醒
     */
    suspend fun showOverdueReminder(
        taskId: String,
        taskTitle: String,
        overdueDays: Int,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.overdueRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASK", true)
            putExtra("TASK_ID", taskId)
        }
        
        val pendingIntent = createPendingIntent(OVERDUE_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("🚨 逾期提醒")
            .setContentText("$taskTitle 已逾期 $overdueDays 天")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("任务「$taskTitle」已逾期 $overdueDays 天\n\n建议立即处理或重新安排时间"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_TASKS)
            .apply {
                configureNotificationStyle(this, settings)
                addTaskActions(this, taskId)
            }
            .build()
        
        notificationHelper.notify(OVERDUE_REMINDER_ID + taskId.hashCode(), notification)
        Log.d(TAG, "逾期任务提醒已发送: $taskTitle")
    }
    
    /**
     * 显示每日回顾提醒（基于实际数据）
     */
    suspend fun showDailyReview(
        reviewData: com.timeflow.app.service.DailyReviewData,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.dailyReviewEnabled) return

        // 使用通知内容生成器
        val generator = com.timeflow.app.service.DailyReviewNotificationGenerator(context)
        val content = generator.generateNotificationContent(reviewData)

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_REVIEW", true)
            putExtra("REVIEW_DATE", reviewData.date.toString())
        }

        val pendingIntent = createPendingIntent(DAILY_REVIEW_ID, intent)

        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_DAILY_REVIEW)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(content.title)
            .setContentText(content.shortText)
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(content.longText))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .apply {
                configureNotificationStyle(this, settings)
            }
            .build()

        notificationHelper.notify(DAILY_REVIEW_ID, notification)
        Log.d(TAG, "每日回顾通知已发送，评分: ${reviewData.overallScore}")
    }

    /**
     * 显示每日回顾提醒（兼容旧版本）
     */
    suspend fun showDailyReview(
        completedTasks: Int,
        totalTasks: Int,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.dailyReviewEnabled) return

        val completionRate = if (totalTasks > 0) (completedTasks * 100 / totalTasks) else 0
        val emoji = when {
            completionRate >= 90 -> "🎉"
            completionRate >= 70 -> "👍"
            completionRate >= 50 -> "💪"
            else -> "🤔"
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_REVIEW", true)
        }

        val pendingIntent = createPendingIntent(DAILY_REVIEW_ID, intent)

        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_DAILY_REVIEW)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("$emoji 每日回顾")
            .setContentText("今日完成 $completedTasks/$totalTasks 个任务 ($completionRate%)")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("今日任务完成情况：$completedTasks/$totalTasks ($completionRate%)\n\n${getEncouragementMessage(completionRate)}\n\n点击查看详细回顾"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .apply {
                configureNotificationStyle(this, settings)
            }
            .build()

        notificationHelper.notify(DAILY_REVIEW_ID, notification)
        Log.d(TAG, "每日回顾通知已发送（兼容模式）")
    }
    
    /**
     * 显示习惯提醒
     */
    suspend fun showHabitReminder(
        habitId: String,
        habitName: String,
        streakCount: Int,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.habitRemindersEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_HABIT", true)
            putExtra("HABIT_ID", habitId)
        }
        
        val pendingIntent = createPendingIntent(HABIT_REMINDER_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_HABIT_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("🔄 习惯提醒")
            .setContentText("是时候进行「$habitName」了")
            .setSubText("已连续 $streakCount 天")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("是时候进行「$habitName」了\n\n已连续坚持 $streakCount 天，继续保持！"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_HABITS)
            .apply {
                configureNotificationStyle(this, settings)
                addHabitActions(this, habitId)
            }
            .build()
        
        notificationHelper.notify(HABIT_REMINDER_ID + habitId.hashCode(), notification)
        Log.d(TAG, "习惯提醒已发送: $habitName")
    }

    /**
     * 显示用药提醒通知
     */
    suspend fun showMedicationReminder(
        medicationId: String,
        medicationName: String,
        dosage: String,
        reminderTime: String,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.medicationRemindersEnabled) return

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_MEDICATION", true)
            putExtra("MEDICATION_ID", medicationId)
        }

        val pendingIntent = createPendingIntent(MEDICATION_REMINDER_ID, intent)

        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("💊 用药提醒")
            .setContentText("请按时服用 $medicationName")
            .setSubText("剂量：$dosage | 时间：$reminderTime")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("💊 用药提醒\n\n药物：$medicationName\n剂量：$dosage\n时间：$reminderTime\n\n请按时服用，保持健康！"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_MEDICATION)
            .apply {
                configureMedicationNotificationStyle(this, settings)
                addMedicationActions(this, medicationId)
            }
            .build()

        notificationHelper.notify(MEDICATION_REMINDER_ID + medicationId.hashCode(), notification)
        Log.d(TAG, "用药提醒已发送: $medicationName")
    }

    /**
     * 显示专注开始提醒
     */
    suspend fun showFocusStartReminder(
        sessionName: String,
        duration: Int,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.focusRemindersEnabled || !settings.focusSessionNotificationsEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_FOCUS", true)
        }
        
        val pendingIntent = createPendingIntent(FOCUS_START_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("🎯 专注提醒")
            .setContentText("开始专注：$sessionName")
            .setSubText("预计 $duration 分钟")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("准备开始专注会话：$sessionName\n\n预计时长：$duration 分钟\n\n建议关闭干扰因素，保持专注"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setGroup(GROUP_FOCUS)
            .apply {
                configureNotificationStyle(this, settings)
            }
            .build()
        
        notificationHelper.notify(FOCUS_START_ID, notification)
        Log.d(TAG, "专注开始提醒已发送")
    }
    
    /**
     * 显示专注结束提醒
     */
    suspend fun showFocusEndReminder(
        sessionName: String,
        actualDuration: Int,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.focusRemindersEnabled || !settings.focusSessionNotificationsEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_FOCUS", true)
        }
        
        val pendingIntent = createPendingIntent(FOCUS_END_ID, intent)
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_TASK_REMINDER)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("✅ 专注完成")
            .setContentText("$sessionName 已完成")
            .setSubText("专注了 $actualDuration 分钟")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("专注会话「$sessionName」已完成\n\n专注时长：$actualDuration 分钟\n\n恭喜你保持了专注！"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .setGroup(GROUP_FOCUS)
            .apply {
                configureNotificationStyle(this, settings)
            }
            .build()
        
        notificationHelper.notify(FOCUS_END_ID, notification)
        Log.d(TAG, "专注结束提醒已发送")
    }
    

    
    /**
     * 显示每日工作效率总结通知
     */
    suspend fun showDailyEfficiencySummary(
        dailyStats: DailyStats,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.dailyReviewEnabled) return
        
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_DAILY_REVIEW", true)
        }
        
        val pendingIntent = createPendingIntent(DAILY_SUMMARY_ID, intent)
        
        // 生成鼓励性消息
        val encouragementMessage = when {
            dailyStats.completionRate >= 90 -> "🎉 今天的效率超赞！"
            dailyStats.completionRate >= 70 -> "💪 今天完成得不错！"
            dailyStats.completionRate >= 50 -> "📈 今天有进步空间"
            else -> "💡 明天重新开始！"
        }
        
        val notification = NotificationCompat.Builder(context, TimeFlowApplication.CHANNEL_DAILY_REVIEW)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("📊 今日效率总结")
            .setContentText("$encouragementMessage 完成 ${dailyStats.completedTasks} 个任务")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("今日工作效率总结\n\n✅ 完成任务：${dailyStats.completedTasks}/${dailyStats.totalTasks} 个\n⏱️ 专注时长：${dailyStats.focusTime} 分钟\n📈 完成率：${dailyStats.completionRate}%\n🎯 效率评分：${dailyStats.efficiencyScore}分\n\n$encouragementMessage\n\n点击查看详细分析和明日建议"))
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .apply {
                configureNotificationStyle(this, settings)
            }
            .build()
        
        notificationHelper.notify(DAILY_SUMMARY_ID, notification)
        Log.d(TAG, "每日效率总结通知已发送")
    }


    
    /**
     * 配置通知样式
     */
    private fun configureNotificationStyle(
        builder: NotificationCompat.Builder,
        settings: NotificationSettings
    ) {
        // 设置声音
        if (settings.soundEnabled) {
            builder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
        }
        
        // 🔧 强化震动设置
        if (settings.vibrationEnabled) {
            // 设置强烈的震动模式
            builder.setVibrate(longArrayOf(0, 500, 300, 500, 300, 500))
            
            // 🔧 关键修复：强制触发震动
            try {
                // 检查设备是否支持震动
                val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator?
                if (vibrator?.hasVibrator() == true) {
                    // 在通知显示时立即触发震动
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createWaveform(
                            longArrayOf(0, 500, 300, 500, 300, 500), -1
                        ))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(longArrayOf(0, 500, 300, 500, 300, 500), -1)
                    }
                    Log.d(TAG, "🔧 强制触发震动成功")
                } else {
                    Log.w(TAG, "设备不支持震动功能")
                }
            } catch (e: Exception) {
                Log.e(TAG, "强制震动失败", e)
            }
        }
        
        // 简化后的通知设置 - 移除复杂的LED和角标设置
        // 保持默认的锁屏可见性
        builder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
        
        // 🔧 修复：设置高优先级，确保通知能够正常显示和震动
        builder.setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
    }
    
    /**
     * 为任务通知添加快捷操作
     */
    private fun addTaskActions(builder: NotificationCompat.Builder, taskId: String) {
        // 完成任务动作
        val completeIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "COMPLETE_TASK"
            putExtra("TASK_ID", taskId)
        }
        val completePendingIntent = PendingIntent.getBroadcast(
            context,
            taskId.hashCode(),
            completeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_launcher_foreground, "完成", completePendingIntent)
        
        // 延期动作
        val postponeIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "POSTPONE_TASK"
            putExtra("TASK_ID", taskId)
        }
        val postponePendingIntent = PendingIntent.getBroadcast(
            context,
            taskId.hashCode() + 1,
            postponeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_launcher_foreground, "延期", postponePendingIntent)
    }
    
    /**
     * 为习惯通知添加快捷操作
     */
    private fun addHabitActions(builder: NotificationCompat.Builder, habitId: String) {
        // 完成习惯动作
        val completeIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "COMPLETE_HABIT"
            putExtra("HABIT_ID", habitId)
        }
        val completePendingIntent = PendingIntent.getBroadcast(
            context,
            habitId.hashCode(),
            completeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_launcher_foreground, "完成", completePendingIntent)
        
        // 稍后提醒动作
        val laterIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "REMIND_HABIT_LATER"
            putExtra("HABIT_ID", habitId)
        }
        val laterPendingIntent = PendingIntent.getBroadcast(
            context,
            habitId.hashCode() + 1,
            laterIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_launcher_foreground, "稍后", laterPendingIntent)
    }
    
    /**
     * 创建PendingIntent
     */
    private fun createPendingIntent(requestCode: Int, intent: Intent): PendingIntent {
        return PendingIntent.getActivity(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * 获取鼓励消息
     */
    private fun getEncouragementMessage(completionRate: Int): String {
        return when {
            completionRate >= 90 -> "太棒了！你今天的效率很高，继续保持这种状态！"
            completionRate >= 70 -> "干得不错！你今天完成了大部分任务，加油！"
            completionRate >= 50 -> "还不错！已经完成了一半任务，继续努力！"
            completionRate >= 30 -> "今天的进展有限，明天可以更有计划地安排任务。"
            else -> "今天的任务完成较少，建议重新规划和调整目标。"
        }
    }
    
    /**
     * 取消所有通知
     */
    fun cancelAllNotifications() {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
        Log.d(TAG, "所有通知已取消")
    }
    
    /**
     * 取消特定类型的通知
     */
    fun cancelNotificationsByGroup(group: String) {
        // Android O+ 支持通知分组取消
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.activeNotifications
                .filter { it.notification.group == group }
                .forEach { notificationManager.cancel(it.id) }
        }
    }
    
    /**
     * 强制更新通知渠道配置（用于修复震动问题）
     */
    fun updateNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            try {
                // 重新创建任务提醒渠道（带震动）
                val taskChannel = android.app.NotificationChannel(
                    TimeFlowApplication.CHANNEL_TASK_REMINDER,
                    "任务提醒",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "用于提醒即将到期或重要的任务"
                    enableVibration(true)
                    vibrationPattern = longArrayOf(0, 500, 300, 500, 300, 500)
                    setShowBadge(true)
                }
                
                // 重新创建每日回顾渠道（带震动）
                val dailyChannel = android.app.NotificationChannel(
                    TimeFlowApplication.CHANNEL_DAILY_REVIEW,
                    "每日回顾",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "每日任务完成情况和统计信息"
                    enableVibration(true)
                    vibrationPattern = longArrayOf(0, 300, 200, 300)
                    setShowBadge(false)
                }
                
                // 重新创建习惯提醒渠道（带震动）
                val habitChannel = android.app.NotificationChannel(
                    TimeFlowApplication.CHANNEL_HABIT_REMINDER,
                    "习惯提醒",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "提醒您培养和保持好习惯"
                    enableVibration(true)
                    vibrationPattern = longArrayOf(0, 400, 200, 400)
                    setShowBadge(true)
                }
                
                // 批量更新渠道
                notificationManager.createNotificationChannels(
                    listOf(taskChannel, dailyChannel, habitChannel)
                )
                
                Log.d(TAG, "✅ 通知渠道已更新，震动功能已启用")
            } catch (e: Exception) {
                Log.e(TAG, "更新通知渠道失败", e)
            }
        }
    }

    /**
     * 配置用药提醒通知样式
     */
    private fun configureMedicationNotificationStyle(
        builder: NotificationCompat.Builder,
        settings: NotificationSettings
    ) {
        // 设置声音
        if (settings.medicationSoundEnabled) {
            builder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
        }

        // 设置震动
        if (settings.medicationVibrationEnabled) {
            builder.setVibrate(longArrayOf(0, 1000, 500, 1000, 500, 1000))

            // 强制触发震动
            try {
                val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator?
                if (vibrator?.hasVibrator() == true) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createWaveform(
                            longArrayOf(0, 1000, 500, 1000, 500, 1000), -1
                        ))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(longArrayOf(0, 1000, 500, 1000, 500, 1000), -1)
                    }
                    Log.d(TAG, "用药提醒震动已触发")
                }
            } catch (e: Exception) {
                Log.e(TAG, "用药提醒震动失败", e)
            }
        }

        // 设置高优先级，确保用药提醒能够及时显示
        builder.setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
    }

    /**
     * 添加用药提醒操作按钮
     */
    private fun addMedicationActions(
        builder: NotificationCompat.Builder,
        medicationId: String
    ) {
        // 已服用按钮
        val takenIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "MEDICATION_TAKEN"
            putExtra("MEDICATION_ID", medicationId)
        }
        val takenPendingIntent = PendingIntent.getBroadcast(
            context,
            medicationId.hashCode() + 1,
            takenIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 稍后提醒按钮
        val laterIntent = Intent(context, NotificationActionReceiver::class.java).apply {
            action = "MEDICATION_REMIND_LATER"
            putExtra("MEDICATION_ID", medicationId)
        }
        val laterPendingIntent = PendingIntent.getBroadcast(
            context,
            medicationId.hashCode() + 2,
            laterIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        builder.addAction(
            R.drawable.ic_launcher_foreground,
            "已服用",
            takenPendingIntent
        ).addAction(
            R.drawable.ic_launcher_foreground,
            "稍后提醒",
            laterPendingIntent
        )
    }
}

/**
 * 每日统计数据类
 */
data class DailyStats(
    val completedTasks: Int,      // 完成的任务数
    val totalTasks: Int,          // 总任务数
    val focusTime: Int,           // 专注时长（分钟）
    val completionRate: Int,      // 完成率（百分比）
    val efficiencyScore: Int      // 效率评分（0-100分）
)

/**
 * 周报数据类
 */
data class WeeklyStats(
    val completedTasks: Int,
    val focusTime: Int, // 小时
    val productivityScore: Int // 百分比
)

/**
 * 通知操作接收器
 */
class NotificationActionReceiver : android.content.BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            "COMPLETE_TASK" -> {
                val taskId = intent.getStringExtra("TASK_ID")
                // TODO: 实现任务完成逻辑
                Log.d("NotificationAction", "完成任务: $taskId")
            }
            "POSTPONE_TASK" -> {
                val taskId = intent.getStringExtra("TASK_ID")
                // TODO: 实现任务延期逻辑
                Log.d("NotificationAction", "延期任务: $taskId")
            }
            "COMPLETE_HABIT" -> {
                val habitId = intent.getStringExtra("HABIT_ID")
                // TODO: 实现习惯完成逻辑
                Log.d("NotificationAction", "完成习惯: $habitId")
            }
            "REMIND_HABIT_LATER" -> {
                val habitId = intent.getStringExtra("HABIT_ID")
                // TODO: 实现稍后提醒逻辑
                Log.d("NotificationAction", "稍后提醒习惯: $habitId")
            }
            "MEDICATION_TAKEN" -> {
                val medicationId = intent.getStringExtra("MEDICATION_ID")
                // TODO: 实现用药记录逻辑
                Log.d("NotificationAction", "已服用药物: $medicationId")

                // 取消通知
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.cancel(TimeFlowNotificationManager.MEDICATION_REMINDER_ID + (medicationId?.hashCode() ?: 0))
            }
            "MEDICATION_REMIND_LATER" -> {
                val medicationId = intent.getStringExtra("MEDICATION_ID")
                // TODO: 实现稍后提醒逻辑（例如15分钟后再次提醒）
                Log.d("NotificationAction", "稍后提醒用药: $medicationId")

                // 取消当前通知
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.cancel(TimeFlowNotificationManager.MEDICATION_REMINDER_ID + (medicationId?.hashCode() ?: 0))
            }
        }
    }
} 