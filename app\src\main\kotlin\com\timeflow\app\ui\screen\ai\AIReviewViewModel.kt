package com.timeflow.app.ui.screen.ai

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.*
import com.timeflow.app.data.repository.*
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt
import com.timeflow.app.ui.viewmodel.AiConfigViewModel
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.MediaType.Companion.toMediaType
import org.json.JSONObject
import org.json.JSONArray
import java.util.concurrent.TimeUnit
import okio.IOException

/**
 * AI智能复盘数据模型
 */
data class AIReviewData(
    val timeFrame: String,
    val displayTimeRange: String,
    val taskStats: TaskStats,
    val timeStats: TimeStats,
    val goalStats: GoalStats,
    val insights: List<AIInsight>,
    val recommendations: List<AIRecommendation>,
    val productivityScore: Int,
    val trendAnalysis: TrendAnalysis,
    val efficiencyPatterns: EfficiencyPatterns,
    val personalizedInsights: PersonalizedInsights,
    val comparisonAnalysis: ComparisonAnalysis,
    val isLoading: Boolean = false,
    val error: String? = null
)

data class TaskStats(
    val totalTasks: Int,
    val completedTasks: Int,
    val completionRate: Float,
    val inProgressTasks: Int,
    val tasksCreated: Int,
    val tasksByCategory: Map<String, Int>,
    val tasksByPriority: Map<Priority, Int>
)

data class TimeStats(
    val totalFocusedMinutes: Int,
    val totalTimeSessions: Int,
    val averageSessionDuration: Int,
    val interruptionCount: Int,
    val pomodoroCount: Int,
    val timeByCategory: Map<String, Int>
)

data class GoalStats(
    val totalGoals: Int,
    val completedGoals: Int,
    val goalCompletionRate: Float,
    val milestonesMet: Int,
    val goalsByCategory: Map<String, Int>
)

data class AIInsight(
    val id: String,
    val type: InsightType,
    val title: String,
    val description: String,
    val impact: InsightImpact,
    val data: Map<String, Any> = emptyMap()
)

data class AIRecommendation(
    val id: String,
    val title: String,
    val description: String,
    val actionType: RecommendationType,
    val priority: RecommendationPriority,
    val estimatedImpact: String
)

enum class InsightType {
    PRODUCTIVITY_PATTERN,
    TIME_MANAGEMENT,
    TASK_EFFICIENCY,
    GOAL_PROGRESS,
    HABIT_ANALYSIS,
    FOCUS_QUALITY,
    AI_GENERATED,
    EFFICIENCY_BOTTLENECK,
    GOAL_ALIGNMENT,
    TASK_OPTIMIZATION,
    PERFORMANCE_TREND,
    BEHAVIORAL_INSIGHT,
    GENERAL_OBSERVATION
}

enum class InsightImpact {
    LOW, MEDIUM, HIGH
}

enum class RecommendationType {
    TIME_OPTIMIZATION,
    TASK_PRIORITIZATION,
    FOCUS_IMPROVEMENT,
    GOAL_ADJUSTMENT,
    HABIT_FORMATION,
    WORKFLOW_ENHANCEMENT,
    GENERAL,
    AI_SUGGESTED,
    ACTION_PLAN,
    PROCESS_OPTIMIZATION,
    GOAL_SETTING,
    PERFORMANCE_TRACKING,
    BEHAVIORAL_CHANGE,
    SKILL_DEVELOPMENT,
    GENERAL_IMPROVEMENT
}

enum class RecommendationPriority {
    LOW, MEDIUM, HIGH
}

// 新增：趋势分析数据结构
data class TrendAnalysis(
    val productivityTrend: List<TrendPoint>,
    val focusTrend: List<TrendPoint>,
    val taskCompletionTrend: List<TrendPoint>,
    val trendDirection: TrendDirection,
    val trendStrength: Float // 0-1，趋势强度
)

data class TrendPoint(
    val date: LocalDate,
    val value: Float
)

enum class TrendDirection {
    IMPROVING, DECLINING, STABLE, FLUCTUATING
}

// 新增：效率模式分析
data class EfficiencyPatterns(
    val peakHours: List<Int>, // 高效时段 (0-23)
    val lowEnergyPeriods: List<Int>,
    val taskTypeEfficiency: List<String>, // 最高效的任务类型
    val taskTypeEfficiencyMap: Map<String, Float>, // 不同任务类型的效率
    val interruptionPattern: InterruptionPattern,
    val focusSessionPatterns: FocusSessionPattern,
    val peakProductivityHours: List<Int>, // 新增：峰值时段
    val focusSessionPattern: FocusSessionPattern // 新增：专注会话模式
)

data class InterruptionPattern(
    val impactOnProductivity: Float,
    val commonInterruptions: List<String>,
    val averageRecoveryTime: Float
)

data class FocusSessionPattern(
    val optimalSessionLength: Int, // 最佳专注时长（分钟）
    val averageSessionLength: Int,
    val sessionQualityScore: Float, // 0-100
    val optimalDuration: Int // 新增：最佳专注时长（分钟）
)

// 新增：个性化洞察
data class PersonalizedInsights(
    val workingStyle: WorkingStyle,
    val strengths: List<String>,
    val improvementAreas: List<String>,
    val motivationalFactors: List<String>,
    val stressIndicators: List<String>
)

enum class WorkingStyle {
    DEEP_WORKER, // 深度工作者
    MULTITASKER, // 多任务处理者
    STRUCTURED, // 结构化工作者
    FLEXIBLE, // 灵活工作者
    COLLABORATIVE // 协作型工作者
}

// 新增：对比分析
data class ComparisonAnalysis(
    val vsLastPeriod: PeriodComparison,
    val vsAverage: AverageComparison,
    val vsBestPeriod: BestPeriodComparison
)

data class PeriodComparison(
    val productivityChange: Float, // 与上一期相比的变化 -1 to 1
    val focusChange: Float,
    val completionRateChange: Float,
    val keyChanges: List<String>
)

data class AverageComparison(
    val aboveAverageMetrics: List<String>,
    val belowAverageMetrics: List<String>,
    val overallPerformanceScore: Float // 相对于个人平均的表现分数
)

data class BestPeriodComparison(
    val bestPeriodDate: String,
    val currentVsBest: Float, // 当前与最佳的比较
    val whatMadeBestPeriodGood: List<String>
)

/**
 * AI分析结果数据类
 */
data class AIAnalysisResult(
    val insights: List<AIInsight>,
    val recommendations: List<AIRecommendation>,
    val analysisTimestamp: LocalDateTime,
    val timeFrame: String
)

/**
 * AI智能复盘ViewModel
 */
@HiltViewModel
class AIReviewViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val timeAnalyticsRepository: TimeAnalyticsRepository,
    private val timeSessionRepository: TimeSessionRepository,
    private val goalRepository: GoalRepository,
    private val aiTaskRepository: AiTaskRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    companion object {
        private const val TAG = "AIReviewViewModel"
    }

    // 当前选择的时间范围
    private val _selectedTimeFrame = MutableStateFlow("周")
    val selectedTimeFrame: StateFlow<String> = _selectedTimeFrame.asStateFlow()

    // AI复盘数据
    private val _reviewData = MutableStateFlow(
        AIReviewData(
            timeFrame = "周",
            displayTimeRange = "",
            taskStats = TaskStats(0, 0, 0f, 0, 0, emptyMap(), emptyMap()),
            timeStats = TimeStats(0, 0, 0, 0, 0, emptyMap()),
            goalStats = GoalStats(0, 0, 0f, 0, emptyMap()),
            insights = emptyList(),
            recommendations = emptyList(),
            productivityScore = 0,
            trendAnalysis = TrendAnalysis(emptyList(), emptyList(), emptyList(), TrendDirection.STABLE, 0f),
            efficiencyPatterns = EfficiencyPatterns(
                emptyList(), 
                emptyList(), 
                emptyList(), 
                emptyMap(), 
                InterruptionPattern(0f, emptyList(), 0f), 
                FocusSessionPattern(25, 20, 0f, 25), 
                emptyList(), 
                FocusSessionPattern(25, 20, 0f, 25)
            ),
            personalizedInsights = PersonalizedInsights(WorkingStyle.STRUCTURED, emptyList(), emptyList(), emptyList(), emptyList()),
            comparisonAnalysis = ComparisonAnalysis(PeriodComparison(0f, 0f, 0f, emptyList()), AverageComparison(emptyList(), emptyList(), 0f), BestPeriodComparison("", 0f, emptyList())),
            isLoading = false
        )
    )
    val reviewData: StateFlow<AIReviewData> = _reviewData.asStateFlow()

    // 是否正在生成AI分析
    private val _isGeneratingAnalysis = MutableStateFlow(false)
    val isGeneratingAnalysis: StateFlow<Boolean> = _isGeneratingAnalysis.asStateFlow()

    // AI分析结果缓存 - 按时间范围缓存
    private val _aiAnalysisCache = MutableStateFlow<Map<String, AIAnalysisResult>>(emptyMap())
    
    // 记录每个时间范围是否已经进行过AI分析
    private val _hasGeneratedAnalysis = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val hasGeneratedAnalysis: StateFlow<Map<String, Boolean>> = _hasGeneratedAnalysis.asStateFlow()

    // AI配置ViewModel
    private val aiConfigViewModel = AiConfigViewModel()

    init {
        // 初始化时加载周数据和AI配置
        viewModelScope.launch {
            aiConfigViewModel.loadConfigurations(context)
            loadReviewData("周")
        }
    }

    /**
     * 设置时间范围并重新加载数据
     */
    fun setTimeFrame(timeFrame: String) {
        Log.d(TAG, "切换时间范围: $timeFrame")
        _selectedTimeFrame.value = timeFrame
        loadReviewData(timeFrame)
        
        // 检查新时间范围的AI分析状态
        val hasAnalysis = _hasGeneratedAnalysis.value[timeFrame] == true
        Log.d(TAG, "时间范围 $timeFrame 的AI分析状态: $hasAnalysis")
    }

    /**
     * 加载复盘数据
     */
    private fun loadReviewData(timeFrame: String) {
        viewModelScope.launch {
            try {
                _reviewData.value = _reviewData.value.copy(isLoading = true, error = null)

                // 计算时间范围
                val (startDate, endDate) = calculateDateRange(timeFrame)
                val displayTimeRange = formatDisplayTimeRange(timeFrame, startDate, endDate)

                // 并行加载各种数据
                val taskStats = loadTaskStats(startDate, endDate)
                val timeStats = loadTimeStats(startDate, endDate)
                val goalStats = loadGoalStats(startDate, endDate)

                // 新增：加载趋势分析数据
                val trendAnalysis = loadTrendAnalysis(timeFrame, startDate, endDate)
                
                // 新增：分析效率模式
                val efficiencyPatterns = analyzeEfficiencyPatterns(startDate, endDate)
                
                // 新增：生成个性化洞察
                val personalizedInsights = generatePersonalizedInsights(taskStats, timeStats, goalStats)
                
                // 新增：对比分析
                val comparisonAnalysis = performComparisonAnalysis(taskStats, timeStats, goalStats)

                // 计算生产力得分
                val productivityScore = calculateEnhancedProductivityScore(
                    taskStats, timeStats, goalStats, trendAnalysis, efficiencyPatterns
                )

                // 检查是否已有缓存的AI分析结果
                val cachedAnalysis = _aiAnalysisCache.value[timeFrame]
                val (insights, recommendations) = if (cachedAnalysis != null) {
                    Log.d(TAG, "使用缓存的AI分析结果: $timeFrame")
                    Pair(cachedAnalysis.insights, cachedAnalysis.recommendations)
                } else {
                    Log.d(TAG, "生成默认洞察和建议: $timeFrame")
                    // 生成基础版洞察和建议（非AI生成）
                    val basicInsights = generateEnhancedInsights(
                        taskStats, timeStats, goalStats, trendAnalysis, 
                        efficiencyPatterns, personalizedInsights, comparisonAnalysis, timeFrame
                    )
                    val basicRecommendations = generateEnhancedRecommendations(
                        taskStats, timeStats, goalStats, trendAnalysis,
                        efficiencyPatterns, personalizedInsights, comparisonAnalysis
                    )
                    Pair(basicInsights, basicRecommendations)
                }

                _reviewData.value = AIReviewData(
                    timeFrame = timeFrame,
                    displayTimeRange = displayTimeRange,
                    taskStats = taskStats,
                    timeStats = timeStats,
                    goalStats = goalStats,
                    insights = insights,
                    recommendations = recommendations,
                    productivityScore = productivityScore,
                    trendAnalysis = trendAnalysis,
                    efficiencyPatterns = efficiencyPatterns,
                    personalizedInsights = personalizedInsights,
                    comparisonAnalysis = comparisonAnalysis,
                    isLoading = false
                )

                Log.d(TAG, "增强版复盘数据加载完成: $timeFrame")

            } catch (e: Exception) {
                Log.e(TAG, "加载复盘数据失败", e)
                _reviewData.value = _reviewData.value.copy(
                    isLoading = false,
                    error = "数据加载失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 检查当前时间范围是否已有AI分析结果
     */
    fun hasAIAnalysisForCurrentTimeFrame(): Boolean {
        val currentTimeFrame = _selectedTimeFrame.value
        return _hasGeneratedAnalysis.value[currentTimeFrame] == true
    }

    /**
     * 清除特定时间范围的AI分析缓存
     */
    fun clearAIAnalysisCache(timeFrame: String? = null) {
        val targetTimeFrame = timeFrame ?: _selectedTimeFrame.value
        
        val updatedCache = _aiAnalysisCache.value.toMutableMap()
        updatedCache.remove(targetTimeFrame)
        _aiAnalysisCache.value = updatedCache
        
        val updatedAnalysisStatus = _hasGeneratedAnalysis.value.toMutableMap()
        updatedAnalysisStatus.remove(targetTimeFrame)
        _hasGeneratedAnalysis.value = updatedAnalysisStatus
        
        Log.d(TAG, "已清除AI分析缓存: $targetTimeFrame")
        
        // 重新加载当前时间范围的数据（会使用基础版洞察）
        if (targetTimeFrame == _selectedTimeFrame.value) {
            loadReviewData(targetTimeFrame)
        }
    }

    /**
     * 生成AI智能分析
     */
    fun generateAIAnalysis() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== 开始生成AI分析 ===")
                _isGeneratingAnalysis.value = true
                
                val currentData = _reviewData.value
                Log.d(TAG, "当前数据状态: 任务${currentData.taskStats.totalTasks}个, 时间${currentData.timeStats.totalFocusedMinutes}分钟")
                
                // 尝试调用AI API进行深度分析
                val aiInsights = try {
                    Log.d(TAG, "尝试调用AI API...")
                    callAIAnalysisAPI(currentData)
                } catch (e: Exception) {
                    Log.w(TAG, "AI API调用失败，使用智能模拟分析", e)
                    generateAdvancedMockAnalysis(currentData)
                }

                Log.d(TAG, "AI分析结果: insights=${aiInsights.insights.size}, recommendations=${aiInsights.recommendations.size}")
                
                // 保存AI分析结果到缓存
                val currentTimeFrame = _selectedTimeFrame.value
                val analysisResult = AIAnalysisResult(
                    insights = aiInsights.insights,
                    recommendations = aiInsights.recommendations,
                    analysisTimestamp = LocalDateTime.now(),
                    timeFrame = currentTimeFrame
                )
                
                // 更新缓存
                val updatedCache = _aiAnalysisCache.value.toMutableMap()
                updatedCache[currentTimeFrame] = analysisResult
                _aiAnalysisCache.value = updatedCache
                
                // 标记该时间范围已进行AI分析
                val updatedAnalysisStatus = _hasGeneratedAnalysis.value.toMutableMap()
                updatedAnalysisStatus[currentTimeFrame] = true
                _hasGeneratedAnalysis.value = updatedAnalysisStatus
                
                // 更新UI数据
                val updatedData = currentData.copy(
                    insights = aiInsights.insights,
                    recommendations = aiInsights.recommendations
                )
                
                _reviewData.value = updatedData
                
                Log.d(TAG, "状态更新完成: insights=${updatedData.insights.size}, recommendations=${updatedData.recommendations.size}")
                
                // 验证状态更新
                val verifyData = _reviewData.value
                Log.d(TAG, "状态验证: insights=${verifyData.insights.size}, recommendations=${verifyData.recommendations.size}")

                Log.d(TAG, "AI分析生成完成")

            } catch (e: Exception) {
                Log.e(TAG, "生成AI分析失败", e)
                _reviewData.value = _reviewData.value.copy(
                    error = "AI分析生成失败: ${e.message}"
                )
            } finally {
                _isGeneratingAnalysis.value = false
                Log.d(TAG, "=== AI分析流程结束 ===")
            }
        }
    }

    /**
     * 计算日期范围
     */
    private fun calculateDateRange(timeFrame: String): Pair<LocalDate, LocalDate> {
        val today = LocalDate.now()
        return when (timeFrame) {
            "日" -> today to today
            "周" -> {
                val startOfWeek = today.minusDays(today.dayOfWeek.value - 1L)
                startOfWeek to startOfWeek.plusDays(6)
            }
            "月" -> {
                val startOfMonth = today.withDayOfMonth(1)
                startOfMonth to today
            }
            "年" -> {
                val startOfYear = today.withDayOfYear(1)
                startOfYear to today
            }
            else -> today to today
        }
    }

    /**
     * 格式化显示时间范围
     */
    private fun formatDisplayTimeRange(timeFrame: String, startDate: LocalDate, endDate: LocalDate): String {
        val today = LocalDate.now()
        return when (timeFrame) {
            "日" -> today.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
            "周" -> {
                val weekOfYear = today.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear())
                "${today.year}年第${weekOfYear}周"
            }
            "月" -> "${today.year}年${today.monthValue}月"
            "年" -> "${today.year}年"
            else -> today.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
        }
    }

    /**
     * 加载任务统计数据
     */
    private suspend fun loadTaskStats(startDate: LocalDate, endDate: LocalDate): TaskStats {
        return withContext(Dispatchers.IO) {
            try {
                val allTasks = taskRepository.getAllTasks()
                
                // 过滤时间范围内的任务
                val tasksInRange = allTasks.filter { task ->
                    val taskDate = task.createdAt.toLocalDate()
                    !taskDate.isBefore(startDate) && !taskDate.isAfter(endDate)
                }

                val completedTasks = tasksInRange.filter { it.isCompleted }
                val completionRate = if (tasksInRange.isNotEmpty()) {
                    (completedTasks.size.toFloat() / tasksInRange.size) * 100f
                } else 0f

                // 计算平均任务时长
                val averageDuration = if (completedTasks.isNotEmpty()) {
                    completedTasks.mapNotNull { task ->
                        if (task.actualTimeMinutes > 0) task.actualTimeMinutes.toLong()
                        else if (task.estimatedTimeMinutes > 0) task.estimatedTimeMinutes.toLong()
                        else null
                    }.average().toLong()
                } else 0L

                // 按优先级分组
                val tasksByPriority = tasksInRange.groupBy { 
                    it.priority ?: Priority.MEDIUM
                }.mapValues { it.value.size }

                // 按类别分组（基于标签）
                val tasksByCategory = tasksInRange.flatMap { task ->
                    task.tags.map { it.name }
                }.groupBy { it }.mapValues { it.value.size }

                // 找出最高效的时间段（模拟数据，实际应基于时间追踪）
                val mostProductiveHour = findMostProductiveHour(completedTasks)

                TaskStats(
                    totalTasks = tasksInRange.size,
                    completedTasks = completedTasks.size,
                    completionRate = completionRate,
                    inProgressTasks = tasksInRange.size - completedTasks.size,
                    tasksCreated = tasksInRange.size,
                    tasksByPriority = tasksByPriority,
                    tasksByCategory = tasksByCategory
                )
            } catch (e: Exception) {
                Log.e(TAG, "加载任务统计失败", e)
                TaskStats(0, 0, 0f, 0, 0, emptyMap(), emptyMap())
            }
        }
    }

    /**
     * 加载时间统计数据
     */
    private suspend fun loadTimeStats(startDate: LocalDate, endDate: LocalDate): TimeStats {
        return withContext(Dispatchers.IO) {
            try {
                // 获取时间会话数据
                val timeSessions = getSessionsByDateRange(startDate, endDate)
                
                val totalFocusTime = timeSessions.sumOf { it.duration }
                val dayCount = startDate.until(endDate).days + 1
                val averageDailyFocus = if (dayCount > 0) totalFocusTime / dayCount else 0L
                
                val longestSession = timeSessions.maxOfOrNull { it.duration } ?: 0L
                val pomodoroCount = timeSessions.count { it.timerType == "POMODORO" }
                
                // 计算中断次数（基于短时间会话）
                val interruptionCount = timeSessions.count { it.duration < 300 } // 5分钟以下
                
                // 时间分布（基于任务类别）
                val timeDistribution = calculateTimeDistribution(timeSessions)

                TimeStats(
                    totalFocusedMinutes = (totalFocusTime / 60).toInt(), // 转换为分钟并转为Int
                    totalTimeSessions = timeSessions.size,
                    averageSessionDuration = (averageDailyFocus / 60).toInt(), // 转换为分钟并转为Int
                    interruptionCount = interruptionCount,
                    pomodoroCount = pomodoroCount,
                    timeByCategory = timeDistribution.mapValues { it.value.toInt() } // 转为Map<String, Int>
                )
            } catch (e: Exception) {
                Log.e(TAG, "加载时间统计失败", e)
                TimeStats(0, 0, 0, 0, 0, emptyMap())
            }
        }
    }

    /**
     * 加载目标统计数据
     */
    private suspend fun loadGoalStats(startDate: LocalDate, endDate: LocalDate): GoalStats {
        return withContext(Dispatchers.IO) {
            try {
                val allGoals = goalRepository.getAllGoals().first()
                
                // 过滤时间范围内的目标
                val goalsInRange = allGoals.filter { goal ->
                    val goalDate = goal.createdAt.toLocalDate()
                    !goalDate.isBefore(startDate) && !goalDate.isAfter(endDate)
                }

                val completedGoals = goalsInRange.filter { it.completedAt != null }
                val completionRate = if (goalsInRange.isNotEmpty()) {
                    (completedGoals.size.toFloat() / goalsInRange.size) * 100f
                } else 0f

                // 计算平均目标完成时长
                val averageDuration = if (completedGoals.isNotEmpty()) {
                    completedGoals.mapNotNull { goal ->
                        goal.completedAt?.let { completed ->
                            java.time.temporal.ChronoUnit.DAYS.between(goal.createdAt.toLocalDate(), completed.toLocalDate()).toInt()
                        }
                    }.average().toInt()
                } else 0

                // 按难度分组
                val goalsByDifficulty = goalsInRange.groupBy { 
                    when(it.priority) {
                        com.timeflow.app.data.model.GoalPriority.LOW -> "简单"
                        com.timeflow.app.data.model.GoalPriority.MEDIUM -> "中等"
                        com.timeflow.app.data.model.GoalPriority.HIGH -> "困难"
                        com.timeflow.app.data.model.GoalPriority.URGENT -> "紧急"
                    }
                }.mapValues { it.value.size }

                GoalStats(
                    totalGoals = goalsInRange.size,
                    completedGoals = completedGoals.size,
                    goalCompletionRate = completionRate,
                    milestonesMet = averageDuration,
                    goalsByCategory = goalsByDifficulty
                )
            } catch (e: Exception) {
                Log.e(TAG, "加载目标统计失败", e)
                GoalStats(0, 0, 0f, 0, emptyMap())
            }
        }
    }

    /**
     * 计算生产力得分
     */
    private fun calculateProductivityScore(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats
    ): Int {
        var score = 0

        // 任务完成率权重 40%
        score += (taskStats.completionRate * 0.4).toInt()

        // 专注时间权重 30%
        val focusScore = when {
            timeStats.totalFocusedMinutes >= 480 -> 30 // 8小时以上
            timeStats.totalFocusedMinutes >= 240 -> 25 // 4-8小时
            timeStats.totalFocusedMinutes >= 120 -> 20 // 2-4小时
            timeStats.totalFocusedMinutes >= 60 -> 15  // 1-2小时
            else -> 10
        }
        score += focusScore

        // 目标完成率权重 20%
        score += (goalStats.goalCompletionRate * 0.2).toInt()

        // 专注质量权重 10%（基于中断次数）
        val qualityScore = when {
            timeStats.interruptionCount <= 2 -> 10
            timeStats.interruptionCount <= 5 -> 8
            timeStats.interruptionCount <= 10 -> 6
            else -> 4
        }
        score += qualityScore

        return score.coerceIn(0, 100)
    }

    /**
     * 生成基础洞察
     */
    private fun generateBasicInsights(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats,
        timeFrame: String
    ): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()

        // 任务完成率洞察
        if (taskStats.completionRate > 80) {
            insights.add(
                AIInsight(
                    id = "task_completion_high",
                    type = InsightType.TASK_EFFICIENCY,
                    title = "任务完成率优秀",
                    description = "您的任务完成率达到${taskStats.completionRate.toInt()}%，表现出色！保持这种高效的工作状态。",
                    impact = InsightImpact.HIGH,
                    data = mapOf("completion_rate" to taskStats.completionRate)
                )
            )
        } else if (taskStats.completionRate < 50) {
            insights.add(
                AIInsight(
                    id = "task_completion_low",
                    type = InsightType.TASK_EFFICIENCY,
                    title = "任务完成率需要提升",
                    description = "您的任务完成率为${taskStats.completionRate.toInt()}%，建议重新评估任务优先级和时间分配。",
                    impact = InsightImpact.HIGH,
                    data = mapOf("completion_rate" to taskStats.completionRate)
                )
            )
        }

        // 专注时间洞察
        if (timeStats.totalFocusedMinutes > 0) {
            val hoursPerDay = timeStats.averageSessionDuration / 60.0
            insights.add(
                AIInsight(
                    id = "focus_time_analysis",
                    type = InsightType.TIME_MANAGEMENT,
                    title = "专注时间分析",
                                         description = "您平均每天专注${String.format("%.1f", hoursPerDay)}小时，专注会话总计${timeStats.totalTimeSessions}次。",
                    impact = if (hoursPerDay >= 4) InsightImpact.HIGH else InsightImpact.MEDIUM,
                                          data = mapOf(
                          "daily_average" to hoursPerDay,
                         "total_sessions" to timeStats.totalTimeSessions
                      )
                )
            )
        }

        // 最高效时段洞察
        insights.add(
            AIInsight(
                id = "productive_hours",
                type = InsightType.PRODUCTIVITY_PATTERN,
                title = "最佳工作时段",
                description = "根据数据分析，您在9:00-10:00时段效率较高。",
                impact = InsightImpact.MEDIUM,
                data = mapOf("peak_hour" to 9)
            )
        )

        // 目标进度洞察
        if (goalStats.totalGoals > 0) {
            insights.add(
                AIInsight(
                    id = "goal_progress",
                    type = InsightType.GOAL_PROGRESS,
                    title = "目标进度跟踪",
                    description = "您在${timeFrame}内完成了${goalStats.completedGoals}个目标，完成率${goalStats.goalCompletionRate.toInt()}%。",
                    impact = if (goalStats.goalCompletionRate > 70) InsightImpact.HIGH else InsightImpact.MEDIUM,
                    data = mapOf(
                        "completed_goals" to goalStats.completedGoals,
                        "total_goals" to goalStats.totalGoals
                    )
                )
            )
        }

        return insights
    }

    /**
     * 生成基础建议
     */
    private fun generateBasicRecommendations(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats
    ): List<AIRecommendation> {
        val recommendations = mutableListOf<AIRecommendation>()

        // 基于任务完成率的建议
        if (taskStats.completionRate < 70) {
            recommendations.add(
                AIRecommendation(
                    id = "improve_task_completion",
                    title = "优化任务管理",
                    description = "建议使用番茄工作法，将大任务拆分为小任务，设置合理的截止时间。",
                    actionType = RecommendationType.TASK_PRIORITIZATION,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "可提升完成率15-25%"
                )
            )
        }

        // 基于专注时间的建议
        if (timeStats.averageSessionDuration < 120) { // 少于2小时
            recommendations.add(
                AIRecommendation(
                    id = "increase_focus_time",
                    title = "增加专注时间",
                    description = "建议每天至少安排3-4小时的深度工作时间，关闭通知，创造专注环境。",
                    actionType = RecommendationType.FOCUS_IMPROVEMENT,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "可提升工作效率30-40%"
                )
            )
        }

        // 基于中断次数的建议
        if (timeStats.interruptionCount > 10) {
            recommendations.add(
                AIRecommendation(
                    id = "reduce_interruptions",
                    title = "减少工作中断",
                    description = "建议设置专注时段，关闭非必要通知，使用专注模式应用。",
                    actionType = RecommendationType.FOCUS_IMPROVEMENT,
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "可提升专注质量20-30%"
                )
            )
        }

        // 基于最高效时段的建议
        recommendations.add(
            AIRecommendation(
                id = "optimize_schedule",
                title = "优化时间安排",
                description = "建议在9:00-11:00安排最重要的任务。",
                actionType = RecommendationType.TIME_OPTIMIZATION,
                priority = RecommendationPriority.MEDIUM,
                estimatedImpact = "可提升整体效率10-20%"
            )
        )

        return recommendations
    }

    /**
     * 调用AI API进行深度分析
     */
    private suspend fun callAIAnalysisAPI(data: AIReviewData): AIAnalysisResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "=== 开始AI分析流程 ===")
                
                // 获取AI配置
                val aiConfig = aiConfigViewModel.getSelectedConfig()
                
                if (aiConfig == null || aiConfig.apiKey.isBlank()) {
                    Log.w(TAG, "AI配置无效，使用智能模拟分析")
                    Log.d(TAG, "AI配置状态: config=${aiConfig}, apiKey=${aiConfig?.apiKey?.take(10)}...")
                    return@withContext generateAdvancedMockAnalysis(data)
                }
                
                Log.d(TAG, "AI配置有效: model=${aiConfig.modelName}, url=${aiConfig.serverUrl}")
                
                // 构建增强版分析提示词
                val analysisPrompt = buildEnhancedAnalysisPrompt(data)
                Log.d(TAG, "分析提示构建完成，长度: ${analysisPrompt.length}")
                
                // 构建请求
                val requestBody = JSONObject().apply {
                    put("model", aiConfig.modelName)
                    put("messages", JSONArray().apply {
                        put(JSONObject().apply {
                            put("role", "system")
                            put("content", getSystemPrompt())
                        })
                        put(JSONObject().apply {
                            put("role", "user")
                            put("content", analysisPrompt)
                        })
                    })
                    put("temperature", 0.7)
                    put("max_tokens", 2000)
                    put("top_p", 0.9)
                    put("frequency_penalty", 0.1)
                    put("presence_penalty", 0.1)
                }

                Log.d(TAG, "开始发送AI API请求...")
                
                // 发送请求
                val response = executeAIRequest(aiConfig, requestBody.toString())
                Log.d(TAG, "AI API响应成功，长度: ${response.length}")
                
                // 解析响应
                val result = parseAIAnalysisResponse(response)
                Log.d(TAG, "AI分析解析完成: insights=${result.insights.size}, recommendations=${result.recommendations.size}")
                
                // 如果解析结果为空，使用模拟分析
                if (result.insights.isEmpty() && result.recommendations.isEmpty()) {
                    Log.w(TAG, "AI分析结果为空，使用智能模拟分析")
                    return@withContext generateAdvancedMockAnalysis(data)
                }
                
                return@withContext result
                
            } catch (e: Exception) {
                Log.e(TAG, "AI API调用失败，使用智能模拟分析", e)
                Log.e(TAG, "错误详情: ${e.message}")
                return@withContext generateAdvancedMockAnalysis(data)
            }
        }
    }

    /**
     * 执行AI API请求
     */
    private suspend fun executeAIRequest(aiConfig: com.timeflow.app.data.ai.model.AiConfig, requestBodyJson: String): String {
        Log.d(TAG, "构建HTTP请求...")
        
        val client = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .build()

        val mediaType = "application/json; charset=utf-8".toMediaType()
        val body = requestBodyJson.toRequestBody(mediaType)

        val request = Request.Builder()
            .url("${aiConfig.serverUrl}/chat/completions")
            .post(body)
            .addHeader("Authorization", "Bearer ${aiConfig.apiKey}")
            .addHeader("Content-Type", "application/json")
            .build()

        Log.d(TAG, "发送请求到: ${aiConfig.serverUrl}/chat/completions")
        
        val response = client.newCall(request).execute()
        
        Log.d(TAG, "收到响应: code=${response.code}, message=${response.message}")
        
        if (!response.isSuccessful) {
            val errorBody = response.body?.string() ?: "无错误详情"
            Log.e(TAG, "API请求失败: ${response.code} ${response.message}")
            Log.e(TAG, "错误响应: $errorBody")
            throw IOException("API调用失败: ${response.code} ${response.message}\n错误详情: $errorBody")
        }
        
        val responseBody = response.body?.string() ?: throw IOException("空响应")
        Log.d(TAG, "响应内容预览: ${responseBody.take(200)}...")
        
        return responseBody
    }

    /**
     * 解析AI分析响应
     */
    private fun parseAIAnalysisResponse(response: String): AIAnalysisResult {
        return try {
            Log.d(TAG, "开始解析AI响应...")
            
            val jsonResponse = JSONObject(response)
            val choices = jsonResponse.getJSONArray("choices")
            val content = choices.getJSONObject(0)
                .getJSONObject("message")
                .getString("content")
            
            Log.d(TAG, "提取AI内容成功，长度: ${content.length}")
            Log.d(TAG, "AI内容预览: ${content.take(300)}...")
            
            // 解析AI返回的结构化分析内容
            val result = parseStructuredAnalysisContent(content)
            
            Log.d(TAG, "结构化解析完成: insights=${result.insights.size}, recommendations=${result.recommendations.size}")
            
            return result
            
        } catch (e: Exception) {
            Log.e(TAG, "解析AI响应失败", e)
            Log.e(TAG, "原始响应: $response")
            
            // 如果解析失败，返回基础分析
            return generateFallbackAnalysis(response)
        }
    }

    /**
     * 解析结构化的AI分析内容
     */
    private fun parseStructuredAnalysisContent(content: String): AIAnalysisResult {
        Log.d(TAG, "开始解析AI内容，内容长度: ${content.length}")
        
        val insights = mutableListOf<AIInsight>()
        val recommendations = mutableListOf<AIRecommendation>()
        
        try {
            // 按行分割内容
            val lines = content.split("\n").filter { it.isNotBlank() }
            
            var currentSection = ""
            var insightCounter = 1
            var recommendationCounter = 1
            
            for (line in lines) {
                val trimmedLine = line.trim()
                
                when {
                    // 识别KISS框架的各个部分
                    trimmedLine.contains("Keep - 保持", ignoreCase = true) ||
                    trimmedLine.contains("## Keep", ignoreCase = true) -> {
                        currentSection = "keep"
                        Log.d(TAG, "识别到Keep部分")
                    }
                    
                    trimmedLine.contains("Improve - 改进", ignoreCase = true) ||
                    trimmedLine.contains("## Improve", ignoreCase = true) -> {
                        currentSection = "improve"
                        Log.d(TAG, "识别到Improve部分")
                    }
                    
                    trimmedLine.contains("Stop - 停止", ignoreCase = true) ||
                    trimmedLine.contains("## Stop", ignoreCase = true) -> {
                        currentSection = "stop"
                        Log.d(TAG, "识别到Stop部分")
                    }
                    
                    trimmedLine.contains("Start - 开始", ignoreCase = true) ||
                    trimmedLine.contains("## Start", ignoreCase = true) -> {
                        currentSection = "start"
                        Log.d(TAG, "识别到Start部分")
                    }
                    
                    // 识别PDCA框架的各个部分
                    trimmedLine.contains("Plan - 计划", ignoreCase = true) ||
                    trimmedLine.contains("## Plan", ignoreCase = true) -> {
                        currentSection = "plan"
                        Log.d(TAG, "识别到Plan部分")
                    }
                    
                    trimmedLine.contains("Do - 执行", ignoreCase = true) ||
                    trimmedLine.contains("## Do", ignoreCase = true) -> {
                        currentSection = "do"
                        Log.d(TAG, "识别到Do部分")
                    }
                    
                    trimmedLine.contains("Check - 检查", ignoreCase = true) ||
                    trimmedLine.contains("## Check", ignoreCase = true) -> {
                        currentSection = "check"
                        Log.d(TAG, "识别到Check部分")
                    }
                    
                    trimmedLine.contains("Act - 改进", ignoreCase = true) ||
                    trimmedLine.contains("## Act", ignoreCase = true) -> {
                        currentSection = "act"
                        Log.d(TAG, "识别到Act部分")
                    }
                    
                    // 识别GRAI框架的各个部分
                    trimmedLine.contains("Goal - 目标", ignoreCase = true) ||
                    trimmedLine.contains("## Goal", ignoreCase = true) -> {
                        currentSection = "goal"
                        Log.d(TAG, "识别到Goal部分")
                    }
                    
                    trimmedLine.contains("Result - 结果", ignoreCase = true) ||
                    trimmedLine.contains("## Result", ignoreCase = true) -> {
                        currentSection = "result"
                        Log.d(TAG, "识别到Result部分")
                    }
                    
                    trimmedLine.contains("Analysis - 原因", ignoreCase = true) ||
                    trimmedLine.contains("## Analysis", ignoreCase = true) -> {
                        currentSection = "analysis"
                        Log.d(TAG, "识别到Analysis部分")
                    }
                    
                    trimmedLine.contains("Insight - 深度", ignoreCase = true) ||
                    trimmedLine.contains("## Insight", ignoreCase = true) -> {
                        currentSection = "insight"
                        Log.d(TAG, "识别到Insight部分")
                    }
                    
                    // 识别KPT框架的各个部分
                    trimmedLine.contains("Problem - 问题", ignoreCase = true) ||
                    trimmedLine.contains("## Problem", ignoreCase = true) -> {
                        currentSection = "problem"
                        Log.d(TAG, "识别到Problem部分")
                    }
                    
                    trimmedLine.contains("Try - 尝试", ignoreCase = true) ||
                    trimmedLine.contains("## Try", ignoreCase = true) -> {
                        currentSection = "try"
                        Log.d(TAG, "识别到Try部分")
                    }
                    
                    // 识别通用框架的各个部分
                    trimmedLine.contains("核心洞察", ignoreCase = true) ||
                    trimmedLine.contains("## 核心洞察", ignoreCase = true) -> {
                        currentSection = "insights"
                        Log.d(TAG, "识别到核心洞察部分")
                    }
                    
                    trimmedLine.contains("优化建议", ignoreCase = true) ||
                    trimmedLine.contains("## 优化建议", ignoreCase = true) -> {
                        currentSection = "recommendations"
                        Log.d(TAG, "识别到优化建议部分")
                    }
                    
                    trimmedLine.contains("行动方案", ignoreCase = true) ||
                    trimmedLine.contains("## 行动方案", ignoreCase = true) -> {
                        currentSection = "actions"
                        Log.d(TAG, "识别到行动方案部分")
                    }
                    
                    // 解析具体的洞察和建议条目
                    else -> {
                        val parsedItem = parseAnalysisItem(trimmedLine, currentSection)
                        parsedItem?.let { (title, description, type) ->
                            when (type) {
                                "insight" -> {
                                    insights.add(
                                        AIInsight(
                                            id = "insight_$insightCounter",
                                            type = mapSectionToInsightType(currentSection),
                                            title = title,
                                            description = description,
                                            impact = InsightImpact.MEDIUM,
                                            data = mapOf("section" to currentSection)
                                        )
                                    )
                                    insightCounter++
                                    Log.d(TAG, "解析到洞察: $title")
                                }
                                "recommendation" -> {
                                    recommendations.add(
                                        AIRecommendation(
                                            id = "rec_$recommendationCounter",
                                            title = title,
                                            description = description,
                                            actionType = mapSectionToRecommendationType(currentSection),
                                            priority = RecommendationPriority.MEDIUM,
                                            estimatedImpact = "提升工作效率"
                                        )
                                    )
                                    recommendationCounter++
                                    Log.d(TAG, "解析到建议: $title")
                                }
                                else -> {
                                    Log.d(TAG, "未知类型: $type")
                                }
                            }
                        }
                    }
                }
            }
            
            Log.d(TAG, "解析完成: ${insights.size}个洞察, ${recommendations.size}个建议")
            
            // 如果解析结果太少，尝试通用解析
            if (insights.size < 2 && recommendations.size < 2) {
                Log.d(TAG, "结构化解析结果不足，尝试通用解析")
                return parseGeneralAnalysisContent(content)
            }
            
            return AIAnalysisResult(
                insights = insights,
                recommendations = recommendations,
                analysisTimestamp = LocalDateTime.now(),
                timeFrame = "API"
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "结构化解析失败", e)
            return parseGeneralAnalysisContent(content)
        }
    }

    /**
     * 解析分析条目
     */
    private fun parseAnalysisItem(line: String, section: String): Triple<String, String, String>? {
        try {
            // 匹配格式: [Keep1] **标题** 描述内容
            val pattern1 = Regex("\\[\\w+\\d*\\]\\s*\\*\\*([^*]+)\\*\\*\\s*(.+)")
            val match1 = pattern1.find(line)
            if (match1 != null) {
                val title = match1.groupValues[1].trim()
                val description = match1.groupValues[2].trim()
                val type = if (isInsightSection(section)) "insight" else "recommendation"
                return Triple(title, description, type)
            }
            
            // 匹配格式: [洞察1] **标题** 描述内容
            val pattern2 = Regex("\\[洞察\\d+\\]\\s*\\*\\*([^*]+)\\*\\*\\s*(.+)")
            val match2 = pattern2.find(line)
            if (match2 != null) {
                val title = match2.groupValues[1].trim()
                val description = match2.groupValues[2].trim()
                return Triple(title, description, "insight")
            }
            
            // 匹配格式: [建议1] **标题** 描述内容
            val pattern3 = Regex("\\[建议\\d+\\]\\s*\\*\\*([^*]+)\\*\\*\\s*(.+)")
            val match3 = pattern3.find(line)
            if (match3 != null) {
                val title = match3.groupValues[1].trim()
                val description = match3.groupValues[2].trim()
                return Triple(title, description, "recommendation")
            }
            
            // 匹配简单格式: **标题** 描述内容
            val pattern4 = Regex("\\*\\*([^*]+)\\*\\*\\s*(.+)")
            val match4 = pattern4.find(line)
            if (match4 != null) {
                val title = match4.groupValues[1].trim()
                val description = match4.groupValues[2].trim()
                val type = if (isInsightSection(section)) "insight" else "recommendation"
                return Triple(title, description, type)
            }
            
            return null
        } catch (e: Exception) {
            Log.e(TAG, "解析条目失败: $line", e)
            return null
        }
    }

    /**
     * 判断是否为洞察类型的部分
     */
    private fun isInsightSection(section: String): Boolean {
        return when (section.lowercase()) {
            "keep", "stop", "start", "improve", 
            "plan", "do", "check", "act",
            "goal", "result", "analysis", "insight",
            "problem", "try", "insights" -> true
            else -> false
        }
    }

    /**
     * 将部分映射到洞察类型
     */
    private fun mapSectionToInsightType(section: String): InsightType {
        return when (section.lowercase()) {
            "keep", "start" -> InsightType.PRODUCTIVITY_PATTERN
            "improve", "stop" -> InsightType.EFFICIENCY_BOTTLENECK
            "plan", "goal" -> InsightType.GOAL_ALIGNMENT
            "do", "act" -> InsightType.TASK_OPTIMIZATION
            "check", "result" -> InsightType.PERFORMANCE_TREND
            "analysis", "insight" -> InsightType.BEHAVIORAL_INSIGHT
            "problem" -> InsightType.EFFICIENCY_BOTTLENECK
            "try" -> InsightType.PRODUCTIVITY_PATTERN
            else -> InsightType.GENERAL_OBSERVATION
        }
    }

    /**
     * 将部分映射到建议类型
     */
    private fun mapSectionToRecommendationType(section: String): RecommendationType {
        return when (section.lowercase()) {
            "keep", "start" -> RecommendationType.HABIT_FORMATION
            "improve", "stop" -> RecommendationType.PROCESS_OPTIMIZATION
            "plan", "goal" -> RecommendationType.GOAL_SETTING
            "do", "act" -> RecommendationType.ACTION_PLAN
            "check", "result" -> RecommendationType.PERFORMANCE_TRACKING
            "analysis", "insight" -> RecommendationType.BEHAVIORAL_CHANGE
            "problem" -> RecommendationType.PROCESS_OPTIMIZATION
            "try" -> RecommendationType.SKILL_DEVELOPMENT
            "recommendations", "actions" -> RecommendationType.ACTION_PLAN
            else -> RecommendationType.GENERAL_IMPROVEMENT
        }
    }

    /**
     * 生成降级分析（AI API失败时）
     */
    private fun generateFallbackAnalysis(originalResponse: String): AIAnalysisResult {
        return AIAnalysisResult(
            insights = listOf(
                AIInsight(
                    id = "fallback_analysis",
                    type = InsightType.AI_GENERATED,
                    title = "系统分析结果",
                    description = "AI分析服务暂时不可用，已为您生成基于数据的基础分析。建议检查网络连接或AI配置后重试。",
                    impact = InsightImpact.MEDIUM,
                    data = mapOf("fallback_reason" to "AI服务不可用")
                )
            ),
            recommendations = listOf(
                AIRecommendation(
                    id = "fallback_recommendation",
                    title = "基础优化建议",
                    description = "基于当前数据模式，建议：1）保持当前的高效时段安排；2）适当减少工作中断；3）定期回顾和调整工作计划。",
                    actionType = RecommendationType.GENERAL,
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "基础改进方案"
                )
            ),
            analysisTimestamp = LocalDateTime.now(),
            timeFrame = "fallback"
        )
    }

    /**
     * 系统提示词 - 设定AI角色和分析标准
     */
    private fun getSystemPrompt(): String {
        return """
你是一位拥有20年经验的资深效率管理顾问和数据分析专家，专长于：
1. 工作效率优化和时间管理策略
2. 个人生产力模式识别和改进
3. 基于数据的行为分析和趋势预测
4. 复盘方法论应用（KISS、PDCA、GRAI、KPT）

分析原则：
• 数据驱动：所有洞察必须基于具体数据
• 实用性：建议必须具体可执行
• 个性化：考虑用户的工作风格和环境
• 专业性：体现管理咨询的专业水准
• 渐进性：提供循序渐进的改进方案

请始终保持专业、客观、建设性的分析视角。
        """.trimIndent()
    }

    /**
     * 生成基于真实数据的智能分析（当AI API不可用时）
     */
    private fun generateAdvancedMockAnalysis(data: AIReviewData): AIAnalysisResult {
        Log.d(TAG, "=== 开始生成基于数据的智能分析 ===")
        
        val insights = mutableListOf<AIInsight>()
        val recommendations = mutableListOf<AIRecommendation>()

        // 根据时间框架生成不同的复盘内容
        when (data.timeFrame) {
            "日" -> generateDataDrivenDailyAnalysis(data, insights, recommendations)
            "周" -> generateDataDrivenWeeklyAnalysis(data, insights, recommendations)
            "月" -> generateDataDrivenMonthlyAnalysis(data, insights, recommendations)
            "年" -> generateDataDrivenYearlyAnalysis(data, insights, recommendations)
            else -> generateDataDrivenGeneralAnalysis(data, insights, recommendations)
        }

        Log.d(TAG, "数据驱动分析生成完成: ${insights.size}个洞察, ${recommendations.size}个建议")
        
        return AIAnalysisResult(
            insights = insights,
            recommendations = recommendations,
            analysisTimestamp = LocalDateTime.now(),
            timeFrame = data.timeFrame
        )
    }

    /**
     * 生成基于数据的日度分析（KISS框架）
     */
    private fun generateDataDrivenDailyAnalysis(
        data: AIReviewData,
        insights: MutableList<AIInsight>,
        recommendations: MutableList<AIRecommendation>
    ) {
        val completionRate = data.taskStats.completionRate
        val focusTime = data.timeStats.totalFocusedMinutes
        val avgSession = data.timeStats.averageSessionDuration
        val totalTasks = data.taskStats.totalTasks
        val completedTasks = data.taskStats.completedTasks
        val interruptionCount = data.timeStats.interruptionCount
        
        // 数据驱动的洞察生成
        
        // Keep - 基于实际表现的优势识别
        if (focusTime > 120) { // 超过2小时专注时间
            insights.add(
                AIInsight(
                    id = "keep_focus",
                    type = InsightType.PRODUCTIVITY_PATTERN,
                    title = "专注时间优势",
                    description = "今日专注时间达到${focusTime}分钟，超过推荐的120分钟标准。平均每次专注${avgSession}分钟，显示出良好的深度工作能力。这种专注模式是您的核心优势，建议继续保持。",
                    impact = InsightImpact.HIGH,
                    data = mapOf("focus_time" to focusTime.toString(), "avg_session" to avgSession.toString())
                )
            )
        }
        
        if (completionRate >= 80) {
            insights.add(
                AIInsight(
                    id = "keep_completion",
                    type = InsightType.TASK_OPTIMIZATION,
                    title = "高效执行能力",
                    description = "今日完成${completedTasks}/${totalTasks}个任务，完成率${String.format("%.1f", completionRate)}%，达到高效标准。您在任务规划和执行方面表现出色，这种工作方式值得保持。",
                    impact = InsightImpact.HIGH,
                    data = mapOf("completion_rate" to completionRate.toString(), "completed" to completedTasks.toString())
                )
            )
        }
        
        // Improve - 基于数据识别的改进点
        if (completionRate < 70) {
            val improvementNeeded = 80 - completionRate
            recommendations.add(
                AIRecommendation(
                    id = "improve_completion",
                    title = "提升任务完成效率",
                    description = "当前完成率${String.format("%.1f", completionRate)}%，距离理想的80%还有${String.format("%.1f", improvementNeeded)}%的提升空间。建议：1）减少同时进行的任务数量；2）更准确地估算任务时间；3）优先处理重要且紧急的任务。",
                    actionType = RecommendationType.PROCESS_OPTIMIZATION,
                    priority = if (completionRate < 50) RecommendationPriority.HIGH else RecommendationPriority.MEDIUM,
                    estimatedImpact = "预期提升完成率${String.format("%.0f", improvementNeeded)}%"
                )
            )
        }
        
        if (interruptionCount > 3) {
            recommendations.add(
                AIRecommendation(
                    id = "reduce_interruptions",
                    title = "优化专注环境",
                    description = "今日发生${interruptionCount}次中断，超过理想的3次标准。每次中断平均需要5-10分钟恢复专注状态。建议：1）设置专注时段，关闭通知；2）使用专注工具如番茄钟；3）与同事约定勿扰时间。",
                    actionType = RecommendationType.BEHAVIORAL_CHANGE,
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "减少${interruptionCount - 3}次中断，节省${(interruptionCount - 3) * 7}分钟"
                )
            )
        }
        
        // Stop - 基于效率分析的停止建议
        if (avgSession < 25 && totalTasks > 5) {
            recommendations.add(
                AIRecommendation(
                    id = "stop_task_switching",
                    title = "减少任务切换频率",
                    description = "平均专注时长仅${avgSession}分钟，且处理了${totalTasks}个任务，表明任务切换过于频繁。建议停止：1）同时开启多个任务；2）频繁查看消息和邮件；3）没有明确时间规划就开始工作。",
                    actionType = RecommendationType.HABIT_FORMATION,
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "提升专注质量，延长单次专注时间至35分钟以上"
                )
            )
        }
        
        // Start - 基于数据缺口的新习惯建议
        if (focusTime < 90) {
            recommendations.add(
                AIRecommendation(
                    id = "start_focus_blocks",
                    title = "建立专注时间块",
                    description = "今日专注时间${focusTime}分钟，低于推荐的90分钟最低标准。建议开始：1）每天安排2-3个45分钟的专注时间块；2）使用番茄工作法管理专注节奏；3）在专注前明确具体目标和成果。",
                    actionType = RecommendationType.HABIT_FORMATION,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "将专注时间提升至120分钟以上"
                )
            )
        }
    }

    /**
     * 生成基于数据的周度分析（PDCA框架）
     */
    private fun generateDataDrivenWeeklyAnalysis(
        data: AIReviewData,
        insights: MutableList<AIInsight>,
        recommendations: MutableList<AIRecommendation>
    ) {
        val weeklyCompletionRate = data.taskStats.completionRate
        val dailyAvgFocus = data.timeStats.totalFocusedMinutes / 7
        val totalTasks = data.taskStats.totalTasks
        val completedTasks = data.taskStats.completedTasks
        val weeklyFocusHours = data.timeStats.totalFocusedMinutes / 60f
        val avgSessionLength = data.timeStats.averageSessionDuration
        
        // Plan - 基于实际数据的计划评估
        val planningEffectiveness = when {
            weeklyCompletionRate >= 85 -> "优秀"
            weeklyCompletionRate >= 70 -> "良好"
            weeklyCompletionRate >= 50 -> "一般"
            else -> "需要改进"
        }
        
        insights.add(
            AIInsight(
                id = "plan_effectiveness",
                type = InsightType.GOAL_ALIGNMENT,
                title = "周计划执行效果评估",
                description = "本周完成${completedTasks}/${totalTasks}个任务，完成率${String.format("%.1f", weeklyCompletionRate)}%，计划执行效果${planningEffectiveness}。" +
                        if (weeklyCompletionRate >= 70) "您的任务规划能力较强，时间估算相对准确。" 
                        else "建议优化任务规划方法，提高时间估算准确性。",
                impact = if (weeklyCompletionRate >= 70) InsightImpact.HIGH else InsightImpact.MEDIUM,
                data = mapOf("completion_rate" to weeklyCompletionRate.toString(), "total_tasks" to totalTasks.toString())
            )
        )
        
        // Do - 基于执行数据的分析
        val focusQuality = when {
            avgSessionLength >= 45 -> "深度专注"
            avgSessionLength >= 25 -> "标准专注"
            else -> "碎片化专注"
        }
        
        insights.add(
            AIInsight(
                id = "execution_analysis",
                type = InsightType.PERFORMANCE_TREND,
                title = "执行模式分析",
                description = "本周累计专注${String.format("%.1f", weeklyFocusHours)}小时，日均${String.format("%.0f", dailyAvgFocus)}分钟。平均专注时长${avgSessionLength}分钟，属于${focusQuality}模式。" +
                        if (dailyAvgFocus >= 120) "您的专注投入充足，执行力较强。" 
                        else "建议增加每日专注时间投入。",
                impact = InsightImpact.MEDIUM,
                data = mapOf("weekly_hours" to weeklyFocusHours.toString(), "avg_session" to avgSessionLength.toString())
            )
        )
        
        // Check - 基于目标对比的检查
        val targetCompletionRate = 80f // 理想完成率
        val performanceGap = targetCompletionRate - weeklyCompletionRate
        
        if (performanceGap > 0) {
            insights.add(
                AIInsight(
                    id = "performance_gap",
                    type = InsightType.BEHAVIORAL_INSIGHT,
                    title = "目标达成差距分析",
                    description = "本周完成率${String.format("%.1f", weeklyCompletionRate)}%，距离目标${String.format("%.0f", targetCompletionRate)}%还有${String.format("%.1f", performanceGap)}%差距。" +
                            "主要影响因素：${if (avgSessionLength < 25) "专注时间碎片化、" else ""}${if (totalTasks > completedTasks * 1.5) "任务规划过多、" else ""}执行效率有待提升。",
                    impact = if (performanceGap > 20) InsightImpact.HIGH else InsightImpact.MEDIUM,
                    data = mapOf("gap" to performanceGap.toString(), "target" to targetCompletionRate.toString())
                )
            )
        }
        
        // Act - 基于数据的改进建议
        if (weeklyCompletionRate < 75) {
            val improvementTarget = minOf(weeklyCompletionRate + 15, 90f)
            recommendations.add(
                AIRecommendation(
                    id = "improve_weekly_execution",
                    title = "优化周度执行策略",
                    description = "当前完成率${String.format("%.1f", weeklyCompletionRate)}%，建议下周目标设定为${String.format("%.0f", improvementTarget)}%。具体措施：1）减少${maxOf(1, (totalTasks - completedTasks) / 2)}个非核心任务；2）将大任务分解为2-3个子任务；3）每日预留20%缓冲时间。",
                    actionType = RecommendationType.ACTION_PLAN,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "预期提升完成率${String.format("%.0f", improvementTarget - weeklyCompletionRate)}%"
                )
            )
        }
        
        if (dailyAvgFocus < 90) {
            val focusTarget = dailyAvgFocus + 30
            recommendations.add(
                AIRecommendation(
                    id = "enhance_focus_time",
                    title = "增强专注时间投入",
                    description = "当前日均专注${String.format("%.0f", dailyAvgFocus)}分钟，建议提升至${String.format("%.0f", focusTarget)}分钟。实施方案：1）每天增加1个45分钟专注块；2）使用番茄工作法管理专注节奏；3）建立专注环境和仪式感。",
                    actionType = RecommendationType.PROCESS_OPTIMIZATION,
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "增加${String.format("%.0f", focusTarget - dailyAvgFocus)}分钟日均专注时间"
                )
            )
        }
        
        // 建立PDCA循环机制（仅在表现良好时推荐）
        if (weeklyCompletionRate >= 70) {
            recommendations.add(
                AIRecommendation(
                    id = "establish_pdca_cycle",
                    title = "建立PDCA持续改进循环",
                    description = "基于当前${String.format("%.1f", weeklyCompletionRate)}%的良好表现，建议建立系统化改进机制：周日制定计划(Plan)，工作日执行(Do)，周五检查成果(Check)，周末总结改进(Act)。设置量化指标跟踪进展。",
                    actionType = RecommendationType.PROCESS_OPTIMIZATION,
                    priority = RecommendationPriority.LOW,
                    estimatedImpact = "建立可持续的效率提升机制"
                )
            )
        }
    }

    /**
     * 生成基于数据的月度分析（GRAI框架）
     */
    private fun generateDataDrivenMonthlyAnalysis(
        data: AIReviewData,
        insights: MutableList<AIInsight>,
        recommendations: MutableList<AIRecommendation>
    ) {
        val monthlyCompletionRate = data.taskStats.completionRate
        val monthlyFocusHours = data.timeStats.totalFocusedMinutes / 60f
        val totalTasks = data.taskStats.totalTasks
        val completedTasks = data.taskStats.completedTasks
        val avgDailyFocus = data.timeStats.totalFocusedMinutes / 30f // 假设30天
        val avgSessionLength = data.timeStats.averageSessionDuration
        
        // Goal - 基于实际数据的目标达成分析
        val goalAchievementLevel = when {
            monthlyCompletionRate >= 85 -> "超额完成"
            monthlyCompletionRate >= 75 -> "良好达成"
            monthlyCompletionRate >= 60 -> "基本达成"
            else -> "未达预期"
        }
        
        insights.add(
            AIInsight(
                id = "monthly_goal_analysis",
                type = InsightType.GOAL_ALIGNMENT,
                title = "月度目标达成评估",
                description = "本月完成${completedTasks}/${totalTasks}个任务，完成率${String.format("%.1f", monthlyCompletionRate)}%，目标达成情况：${goalAchievementLevel}。" +
                        "专注投入${String.format("%.1f", monthlyFocusHours)}小时，日均${String.format("%.0f", avgDailyFocus)}分钟。" +
                        if (monthlyCompletionRate >= 75) "您的月度规划和执行能力表现优秀。" 
                        else "建议优化月度目标设定和执行策略。",
                impact = if (monthlyCompletionRate >= 75) InsightImpact.HIGH else InsightImpact.MEDIUM,
                data = mapOf("completion_rate" to monthlyCompletionRate.toString(), "focus_hours" to monthlyFocusHours.toString())
            )
        )
        
        // Result - 基于数据的成果评估
        val productivityLevel = when {
            avgDailyFocus >= 150 && monthlyCompletionRate >= 80 -> "高效能"
            avgDailyFocus >= 90 && monthlyCompletionRate >= 65 -> "稳定高效"
            avgDailyFocus >= 60 && monthlyCompletionRate >= 50 -> "中等效率"
            else -> "效率待提升"
        }
        
        insights.add(
            AIInsight(
                id = "monthly_results",
                type = InsightType.PERFORMANCE_TREND,
                title = "月度成果量化分析",
                description = "本月工作效率等级：${productivityLevel}。核心数据：日均专注${String.format("%.0f", avgDailyFocus)}分钟，平均专注时长${avgSessionLength}分钟。" +
                        "累计完成${completedTasks}个任务，专注总时长${String.format("%.1f", monthlyFocusHours)}小时。" +
                        if (productivityLevel == "高效能") "您已建立了优秀的工作节奏和效率体系。"
                        else "仍有进一步优化的空间。",
                impact = InsightImpact.HIGH,
                data = mapOf("productivity_level" to productivityLevel, "daily_avg" to avgDailyFocus.toString())
            )
        )
        
        // Analysis - 基于数据的深度分析
        val strengthAreas = mutableListOf<String>()
        val improvementAreas = mutableListOf<String>()
        
        if (monthlyCompletionRate >= 75) strengthAreas.add("任务执行力强")
        if (avgSessionLength >= 35) strengthAreas.add("专注深度好")
        if (avgDailyFocus >= 120) strengthAreas.add("时间投入充足")
        
        if (monthlyCompletionRate < 70) improvementAreas.add("任务完成效率")
        if (avgSessionLength < 25) improvementAreas.add("专注时长稳定性")
        if (avgDailyFocus < 90) improvementAreas.add("专注时间投入")
        
        insights.add(
            AIInsight(
                id = "monthly_analysis",
                type = InsightType.BEHAVIORAL_INSIGHT,
                title = "工作模式深度分析",
                description = "本月工作优势：${strengthAreas.joinToString("、")}。" +
                        if (improvementAreas.isNotEmpty()) "需要改进的方面：${improvementAreas.joinToString("、")}。" else "各项指标均表现良好。" +
                        "数据显示您的工作风格偏向${if (avgSessionLength >= 35) "深度专注型" else if (totalTasks > completedTasks * 1.2) "多任务处理型" else "平衡发展型"}。",
                impact = InsightImpact.MEDIUM,
                data = mapOf("strengths" to strengthAreas.size.toString(), "improvements" to improvementAreas.size.toString())
            )
        )
        
        // Insight - 基于数据的洞察和建议
        if (monthlyCompletionRate < 75) {
            val targetImprovement = minOf(monthlyCompletionRate + 20, 90f)
            recommendations.add(
                AIRecommendation(
                    id = "monthly_efficiency_boost",
                    title = "月度效率提升策略",
                    description = "当前完成率${String.format("%.1f", monthlyCompletionRate)}%，建议下月目标${String.format("%.0f", targetImprovement)}%。" +
                            "具体策略：1）优化任务规划，减少${maxOf(1, (totalTasks - completedTasks) / 3)}个低价值任务；" +
                            "2）建立周度检查机制，及时调整计划；3）增加${maxOf(10, 90 - avgDailyFocus.toInt())}分钟日均专注时间。",
                    actionType = RecommendationType.PROCESS_OPTIMIZATION,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "预期提升完成率${String.format("%.0f", targetImprovement - monthlyCompletionRate)}%"
                )
            )
        }
        
        // 基于工作风格的个性化建议
        val workingStyle = when {
            avgSessionLength >= 45 && monthlyCompletionRate >= 75 -> "深度工作专家"
            totalTasks >= 50 && monthlyCompletionRate >= 70 -> "多任务管理者"
            avgDailyFocus >= 120 && avgSessionLength >= 30 -> "稳定执行者"
            else -> "发展中的效率优化者"
        }
        
        recommendations.add(
            AIRecommendation(
                id = "personalized_development",
                title = "个性化能力发展建议",
                description = "基于数据分析，您的工作风格为：${workingStyle}。" +
                        "建议发展方向：${when (workingStyle) {
                            "深度工作专家" -> "1）扩大影响力，分享深度工作经验；2）挑战更复杂的项目；3）建立知识管理体系"
                            "多任务管理者" -> "1）优化任务优先级算法；2）建立更高效的切换机制；3）提升单任务专注深度"
                            "稳定执行者" -> "1）增加工作挑战性；2）提升创新思维能力；3）优化时间分配策略"
                            else -> "1）建立稳定的工作节奏；2）提升专注时间质量；3）优化任务规划能力"
                        }}。",
                actionType = RecommendationType.SKILL_DEVELOPMENT,
                priority = RecommendationPriority.MEDIUM,
                estimatedImpact = "建立个性化的效率提升路径"
            )
        )
    }

    /**
     * 生成基于数据的年度分析（KPT框架）
     */
    private fun generateDataDrivenYearlyAnalysis(
        data: AIReviewData,
        insights: MutableList<AIInsight>,
        recommendations: MutableList<AIRecommendation>
    ) {
        val yearlyCompletionRate = data.taskStats.completionRate
        val yearlyFocusHours = data.timeStats.totalFocusedMinutes / 60f
        val totalTasks = data.taskStats.totalTasks
        val completedTasks = data.taskStats.completedTasks
        val avgDailyFocus = data.timeStats.totalFocusedMinutes / 365f // 年度日均
        val avgSessionLength = data.timeStats.averageSessionDuration
        
        // Keep - 基于数据识别的核心优势
        val coreStrengths = mutableListOf<String>()
        if (yearlyCompletionRate >= 75) coreStrengths.add("高效的任务执行体系")
        if (yearlyFocusHours >= 300) coreStrengths.add("充足的专注时间投入")
        if (avgSessionLength >= 35) coreStrengths.add("优秀的深度工作能力")
        
        insights.add(
            AIInsight(
                id = "yearly_strengths",
                type = InsightType.PRODUCTIVITY_PATTERN,
                title = "年度核心优势盘点",
                description = "今年完成${completedTasks}/${totalTasks}个任务，完成率${String.format("%.1f", yearlyCompletionRate)}%，累计专注${String.format("%.0f", yearlyFocusHours)}小时。" +
                        "核心优势：${if (coreStrengths.isNotEmpty()) coreStrengths.joinToString("、") else "基础能力稳步发展"}。" +
                        "这些优势${if (yearlyCompletionRate >= 75) "为您建立了强大的个人效率体系" else "是明年进一步发展的基础"}。",
                impact = if (yearlyCompletionRate >= 75) InsightImpact.HIGH else InsightImpact.MEDIUM,
                data = mapOf("completion_rate" to yearlyCompletionRate.toString(), "focus_hours" to yearlyFocusHours.toString())
            )
        )
        
        // Problem - 基于数据识别的主要挑战
        val challengeAreas = mutableListOf<String>()
        if (yearlyCompletionRate < 70) challengeAreas.add("任务完成效率有待提升")
        if (avgDailyFocus < 60) challengeAreas.add("专注时间投入不足")
        if (avgSessionLength < 25) challengeAreas.add("专注深度需要加强")
        if (totalTasks > completedTasks * 1.5) challengeAreas.add("任务规划过于乐观")
        
        if (challengeAreas.isNotEmpty()) {
            insights.add(
                AIInsight(
                    id = "yearly_challenges",
                    type = InsightType.EFFICIENCY_BOTTLENECK,
                    title = "年度挑战与改进机会",
                    description = "今年主要挑战：${challengeAreas.joinToString("、")}。" +
                            "数据分析显示：${if (yearlyCompletionRate < 70) "完成率${String.format("%.1f", yearlyCompletionRate)}%低于理想水平70%；" else ""}" +
                            "${if (avgDailyFocus < 60) "日均专注${String.format("%.0f", avgDailyFocus)}分钟需要提升至90分钟以上；" else ""}" +
                            "这些是明年重点突破的方向。",
                    impact = InsightImpact.MEDIUM,
                    data = mapOf("challenges_count" to challengeAreas.size.toString(), "daily_focus" to avgDailyFocus.toString())
                )
            )
        }
        
        // Try - 基于数据缺口的创新尝试建议
        if (yearlyCompletionRate < 80 || avgDailyFocus < 90) {
            val focusImprovement = maxOf(0, 120 - avgDailyFocus.toInt())
            val completionImprovement = maxOf(0.0, 80.0 - yearlyCompletionRate)
            
            recommendations.add(
                AIRecommendation(
                    id = "yearly_breakthrough_plan",
                    title = "明年突破性提升计划",
                    description = "基于今年数据分析，明年重点突破：" +
                            "${if (completionImprovement > 0) "1）提升任务完成率${String.format("%.0f", completionImprovement)}%，通过优化规划和执行流程；" else ""}" +
                            "${if (focusImprovement > 0) "2）增加日均专注时间${focusImprovement}分钟，建立更稳定的工作节奏；" else ""}" +
                            "3）引入新的效率工具和方法，如AI辅助规划、自动化流程等。",
                    actionType = RecommendationType.SKILL_DEVELOPMENT,
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "预期整体效率提升${String.format("%.0f", maxOf(completionImprovement, focusImprovement.toDouble()))}%"
                )
            )
        }
        
        // 基于当前水平的成长战略
        val currentLevel = when {
            yearlyCompletionRate >= 85 && yearlyFocusHours >= 400 -> "效率专家"
            yearlyCompletionRate >= 75 && yearlyFocusHours >= 300 -> "高效执行者"
            yearlyCompletionRate >= 65 && yearlyFocusHours >= 200 -> "稳步发展者"
            else -> "效率探索者"
        }
        
        recommendations.add(
            AIRecommendation(
                id = "yearly_growth_strategy",
                title = "年度成长战略规划",
                description = "基于今年表现，您的效率水平为：${currentLevel}。明年发展策略：" +
                        when (currentLevel) {
                            "效率专家" -> "1）建立个人效率品牌，分享经验；2）挑战更高难度的目标；3）探索效率创新方法"
                            "高效执行者" -> "1）优化现有工作流程；2）扩大影响范围；3）建立知识管理体系"
                            "稳步发展者" -> "1）强化基础能力；2）提升专注质量；3）优化时间分配"
                            else -> "1）建立稳定的工作习惯；2）提升基础效率；3）学习成熟的方法论"
                        } + "。设置季度检查点，确保持续进步。",
                actionType = RecommendationType.ACTION_PLAN,
                priority = RecommendationPriority.HIGH,
                estimatedImpact = "建立系统性的年度成长路径"
            )
        )
    }

    /**
     * 生成基于数据的通用分析
     */
    private fun generateDataDrivenGeneralAnalysis(
        data: AIReviewData,
        insights: MutableList<AIInsight>,
        recommendations: MutableList<AIRecommendation>
    ) {
        val completionRate = data.taskStats.completionRate
        val focusTime = data.timeStats.totalFocusedMinutes
        val totalTasks = data.taskStats.totalTasks
        val completedTasks = data.taskStats.completedTasks
        val avgSessionLength = data.timeStats.averageSessionDuration
        
        // 基于实际数据的效率评估
        val efficiencyLevel = when {
            completionRate >= 80 && focusTime >= 120 -> "高效"
            completionRate >= 65 && focusTime >= 90 -> "良好"
            completionRate >= 50 && focusTime >= 60 -> "中等"
            else -> "待提升"
        }
        
        insights.add(
            AIInsight(
                id = "general_efficiency_analysis",
                type = InsightType.PRODUCTIVITY_PATTERN,
                title = "整体效率水平评估",
                description = "${data.timeFrame}期间完成${completedTasks}/${totalTasks}个任务，完成率${String.format("%.1f", completionRate)}%，" +
                        "专注时间${focusTime}分钟，平均专注时长${avgSessionLength}分钟。" +
                        "整体效率水平：${efficiencyLevel}。" +
                        if (efficiencyLevel == "高效") "您已建立了优秀的工作效率体系。"
                        else "仍有进一步优化的空间。",
                impact = if (efficiencyLevel == "高效") InsightImpact.HIGH else InsightImpact.MEDIUM,
                data = mapOf("efficiency_level" to efficiencyLevel, "completion_rate" to completionRate.toString())
            )
        )
        
        // 基于数据缺口的改进建议
        val improvementAreas = mutableListOf<String>()
        if (completionRate < 75) improvementAreas.add("任务完成效率")
        if (focusTime < 90) improvementAreas.add("专注时间投入")
        if (avgSessionLength < 25) improvementAreas.add("专注深度")
        
        if (improvementAreas.isNotEmpty()) {
            val primaryImprovement = improvementAreas.first()
            val improvementStrategy = when (primaryImprovement) {
                "任务完成效率" -> "1）减少同时进行的任务数量；2）优化任务优先级排序；3）设置更现实的时间预估"
                "专注时间投入" -> "1）建立固定的专注时间块；2）减少外界干扰；3）使用番茄工作法管理节奏"
                "专注深度" -> "1）延长单次专注时间；2）减少任务切换频率；3）创造更好的专注环境"
                else -> "1）建立稳定的工作习惯；2）优化时间分配；3）提升执行效率"
            }
            
            recommendations.add(
                AIRecommendation(
                    id = "general_improvement_plan",
                    title = "效率提升重点方案",
                    description = "当前主要改进方向：${improvementAreas.joinToString("、")}。" +
                            "针对${primaryImprovement}的具体建议：${improvementStrategy}。" +
                            "预期通过这些改进可以提升${if (completionRate < 60) "20-30%" else "10-15%"}的整体效率。",
                    actionType = RecommendationType.PROCESS_OPTIMIZATION,
                    priority = if (improvementAreas.size >= 2) RecommendationPriority.HIGH else RecommendationPriority.MEDIUM,
                    estimatedImpact = "提升整体效率${if (completionRate < 60) "20-30%" else "10-15%"}"
                )
            )
        } else {
            // 当表现良好时，提供进阶建议
            recommendations.add(
                AIRecommendation(
                    id = "general_optimization_plan",
                    title = "效率进阶优化建议",
                    description = "基于当前${efficiencyLevel}的表现，建议进阶优化：1）探索更高效的工作方法和工具；" +
                            "2）建立个人知识管理体系；3）分享经验，帮助他人提升效率；4）挑战更具挑战性的目标。" +
                            "这些措施将帮助您从优秀走向卓越。",
                    actionType = RecommendationType.SKILL_DEVELOPMENT,
                    priority = RecommendationPriority.LOW,
                    estimatedImpact = "建立可持续的效率提升机制"
                )
            )
        }
    }

    // 辅助方法
    private fun findMostProductiveHour(completedTasks: List<com.timeflow.app.data.model.Task>): Int {
        // 基于任务完成时间分析最高效时段
        val hourCounts = mutableMapOf<Int, Int>()
        
        completedTasks.forEach { task ->
            task.completedAt?.let { completedTime ->
                val hour = completedTime.hour
                hourCounts[hour] = hourCounts.getOrDefault(hour, 0) + 1
            }
        }
        
        return hourCounts.maxByOrNull { it.value }?.key ?: 10 // 默认上午10点
    }

    private fun calculateTimeDistribution(timeSessions: List<com.timeflow.app.data.model.TimeSession>): Map<String, Float> {
        if (timeSessions.isEmpty()) return emptyMap()
        
        val totalTime = timeSessions.sumOf { it.duration }.toFloat()
        
        return timeSessions.groupBy { it.taskName }
            .mapValues { (_, sessions) ->
                (sessions.sumOf { it.duration }.toFloat() / totalTime) * 100f
            }
    }

    // 修复getSessionsByDateRange方法的实现
    private suspend fun getSessionsByDateRange(startDate: LocalDate, endDate: LocalDate): List<com.timeflow.app.data.model.TimeSession> {
        return try {
            val allSessions = timeSessionRepository.getAllSessions().first()
            allSessions.filter { session ->
                val sessionDate = session.startTime.atZone(java.time.ZoneId.systemDefault()).toLocalDate()
                !sessionDate.isBefore(startDate) && !sessionDate.isAfter(endDate)
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取时间会话失败", e)
            emptyList()
        }
    }

    /**
     * 加载趋势分析数据
     */
    private suspend fun loadTrendAnalysis(timeFrame: String, startDate: LocalDate, endDate: LocalDate): TrendAnalysis {
        return withContext(Dispatchers.IO) {
            try {
                // 根据时间范围计算历史数据点
                val dataPoints = when (timeFrame) {
                    "日" -> generateHourlyTrends(startDate)
                    "周" -> generateDailyTrends(startDate, endDate)
                    "月" -> generateWeeklyTrends(startDate)
                    "年" -> generateMonthlyTrends(startDate)
                    else -> emptyList()
                }
                
                // 计算趋势方向和强度
                val trendDirection = calculateTrendDirection(dataPoints)
                val trendStrength = calculateTrendStrength(dataPoints)
                
                TrendAnalysis(
                    productivityTrend = dataPoints,
                    focusTrend = generateFocusTimeTrend(startDate, endDate),
                    taskCompletionTrend = generateCompletionRateTrend(startDate, endDate),
                    trendDirection = trendDirection,
                    trendStrength = trendStrength
                )
            } catch (e: Exception) {
                Log.e(TAG, "加载趋势分析失败", e)
                TrendAnalysis(emptyList(), emptyList(), emptyList(), TrendDirection.STABLE, 0f)
            }
        }
    }

    /**
     * 分析效率模式
     */
    private suspend fun analyzeEfficiencyPatterns(startDate: LocalDate, endDate: LocalDate): EfficiencyPatterns {
        return withContext(Dispatchers.IO) {
            try {
                val timeSessions = getSessionsByDateRange(startDate, endDate)
                val completedTasks = taskRepository.getAllTasks().filter { 
                    it.isCompleted && it.completedAt?.toLocalDate()?.let { date ->
                        !date.isBefore(startDate) && !date.isAfter(endDate)
                    } == true
                }
                
                // 分析高效时段
                val peakHours = findPeakProductivityHours(completedTasks, timeSessions)
                val lowEnergyHours = findLowEnergyHours(timeSessions)
                
                // 分析最佳工作日
                val bestDaysOfWeek = findBestDaysOfWeek(completedTasks)
                
                // 分析任务类型效率
                val taskTypeEfficiency = analyzeTaskTypeEfficiency(completedTasks)
                
                // 分析中断模式
                val interruptionPattern = analyzeInterruptionPatterns(timeSessions)
                
                // 分析专注会话模式
                val focusSessionPattern = analyzeFocusSessionPatterns(timeSessions)
                
                EfficiencyPatterns(
                    peakHours = peakHours,
                    lowEnergyPeriods = lowEnergyHours,
                    taskTypeEfficiency = bestDaysOfWeek,
                    taskTypeEfficiencyMap = taskTypeEfficiency,
                    interruptionPattern = interruptionPattern,
                    focusSessionPatterns = focusSessionPattern,
                    peakProductivityHours = peakHours,
                    focusSessionPattern = focusSessionPattern
                )
            } catch (e: Exception) {
                Log.e(TAG, "分析效率模式失败", e)
                EfficiencyPatterns(
                    emptyList(), emptyList(), emptyList(), emptyMap(),
                    InterruptionPattern(0f, emptyList(), 0f),
                    FocusSessionPattern(25, 20, 0f, 25),
                    emptyList(),
                    FocusSessionPattern(25, 20, 0f, 25)
                )
            }
        }
    }

    /**
     * 生成个性化洞察
     */
    private suspend fun generatePersonalizedInsights(
        taskStats: TaskStats, 
        timeStats: TimeStats, 
        goalStats: GoalStats
    ): PersonalizedInsights {
        return withContext(Dispatchers.IO) {
            try {
                // 分析工作风格
                val workingStyle = determineWorkingStyle(taskStats, timeStats)
                
                // 识别激励因素
                val motivationFactors = identifyMotivationFactors(taskStats, goalStats)
                
                // 识别压力指标
                val stressIndicators = identifyStressIndicators(timeStats, taskStats)
                
                // 识别改进领域
                val improvementAreas = identifyImprovementAreas(taskStats, timeStats, goalStats)
                
                // 识别优势
                val strengths = identifyStrengths(taskStats, timeStats, goalStats)
                
                PersonalizedInsights(
                    workingStyle = workingStyle,
                    strengths = strengths,
                    improvementAreas = improvementAreas,
                    motivationalFactors = motivationFactors,
                    stressIndicators = stressIndicators
                )
            } catch (e: Exception) {
                Log.e(TAG, "生成个性化洞察失败", e)
                PersonalizedInsights(
                    WorkingStyle.STRUCTURED,
                    emptyList(), emptyList(), emptyList(), emptyList()
                )
            }
        }
    }

    /**
     * 生成小时级趋势数据（日复盘）
     */
    private suspend fun generateHourlyTrends(date: LocalDate): List<TrendPoint> {
        val timeSessions = getSessionsByDateRange(date, date)
        val hourlyData = mutableMapOf<Int, Float>()
        
        // 按小时统计专注时间
        timeSessions.forEach { session ->
            val hour = session.startTime.atZone(java.time.ZoneId.systemDefault()).hour
            hourlyData[hour] = hourlyData.getOrDefault(hour, 0f) + (session.duration / 60f)
        }
        
        return (0..23).map { hour ->
            TrendPoint(
                date = date,
                value = hourlyData.getOrDefault(hour, 0f)
            )
        }
    }

    /**
     * 生成日级趋势数据（周复盘）
     */
    private suspend fun generateDailyTrends(startDate: LocalDate, endDate: LocalDate): List<TrendPoint> {
        val trendPoints = mutableListOf<TrendPoint>()
        var currentDate = startDate
        
        while (!currentDate.isAfter(endDate)) {
            val dayTasks = taskRepository.getAllTasks().filter { task ->
                task.completedAt?.toLocalDate() == currentDate
            }
            
            val dayScore = calculateDayProductivityScore(dayTasks, currentDate)
            
            trendPoints.add(
                TrendPoint(
                    date = currentDate,
                    value = dayScore
                )
            )
            
            currentDate = currentDate.plusDays(1)
        }
        
        return trendPoints
    }

    /**
     * 计算单日生产力得分
     */
    private suspend fun calculateDayProductivityScore(tasks: List<com.timeflow.app.data.model.Task>, date: LocalDate): Float {
        if (tasks.isEmpty()) return 0f
        
        val completedTasks = tasks.filter { it.isCompleted }
        val completionRate = completedTasks.size.toFloat() / tasks.size
        
        // 获取当日专注时间
        val dayFocusTime = getSessionsByDateRange(date, date).sumOf { it.duration } / 60f
        
        // 综合计算得分
        return (completionRate * 60f + (dayFocusTime / 480f) * 40f).coerceAtMost(100f)
    }

    /**
     * 找出高效时段
     */
    private fun findPeakProductivityHours(
        completedTasks: List<com.timeflow.app.data.model.Task>,
        timeSessions: List<com.timeflow.app.data.model.TimeSession>
    ): List<Int> {
        val hourlyProductivity = mutableMapOf<Int, Float>()
        
        // 基于任务完成时间分析
        completedTasks.forEach { task ->
            task.completedAt?.let { completedTime ->
                val hour = completedTime.hour
                val taskValue = when (task.priority?.name) {
                    "URGENT" -> 4f
                    "HIGH" -> 3f
                    "MEDIUM" -> 2f
                    "LOW" -> 1f
                    else -> 1f
                }
                hourlyProductivity[hour] = hourlyProductivity.getOrDefault(hour, 0f) + taskValue
            }
        }
        
        // 基于专注时间分析
        timeSessions.forEach { session ->
            val hour = session.startTime.atZone(java.time.ZoneId.systemDefault()).hour
            val focusValue = (session.duration / 60f) * 0.1f
            hourlyProductivity[hour] = hourlyProductivity.getOrDefault(hour, 0f) + focusValue
        }
        
        // 返回前3个高效时段
        return hourlyProductivity.toList()
            .sortedByDescending { it.second }
            .take(3)
            .map { it.first }
    }

    /**
     * 分析中断模式
     */
    private fun analyzeInterruptionPatterns(timeSessions: List<com.timeflow.app.data.model.TimeSession>): InterruptionPattern {
        val shortSessions = timeSessions.filter { it.duration < 300 } // 5分钟以下视为中断
        val averageInterruptions = shortSessions.size.toFloat() / maxOf(1, timeSessions.size)
        
        // 分析中断高发时段
        val interruptionTimes = shortSessions.groupBy { session ->
            val hour = session.startTime.atZone(java.time.ZoneId.systemDefault()).hour
            "${hour}:00-${hour + 1}:00"
        }.toList().sortedByDescending { it.second.size }.take(3).map { it.first }
        
        // 计算中断对效率的影响
        val totalFocusTime = timeSessions.sumOf { it.duration }
        val interruptionImpact = if (totalFocusTime > 0) {
            (shortSessions.sumOf { it.duration }.toFloat() / totalFocusTime) * 100f
        } else 0f
        
        return InterruptionPattern(
            impactOnProductivity = interruptionImpact,
            commonInterruptions = interruptionTimes,
            averageRecoveryTime = averageInterruptions
        )
    }

    /**
     * 分析专注会话模式
     */
    private fun analyzeFocusSessionPatterns(timeSessions: List<com.timeflow.app.data.model.TimeSession>): FocusSessionPattern {
        val validSessions = timeSessions.filter { it.duration >= 300 } // 5分钟以上
        
        if (validSessions.isEmpty()) {
            return FocusSessionPattern(25, 0, 0f, 0)
        }
        
        val averageLength = (validSessions.sumOf { it.duration } / validSessions.size / 60).toInt()
        
        // 找出最佳专注时长（完成任务最多的时长区间）
        val sessionLengthGroups = validSessions.groupBy { session ->
            when (session.duration / 60) {
                in 5..15 -> 15
                in 16..25 -> 25
                in 26..45 -> 45
                in 46..90 -> 90
                else -> 120
            }
        }
        
        val optimalLength = sessionLengthGroups.maxByOrNull { it.value.size }?.key ?: 25
        
        // 计算会话质量得分
        val qualityScore = calculateSessionQualityScore(validSessions)
        
        return FocusSessionPattern(
            optimalSessionLength = optimalLength,
            averageSessionLength = averageLength,
            sessionQualityScore = qualityScore,
            optimalDuration = optimalLength
        )
    }

    /**
     * 确定工作风格
     */
    private fun determineWorkingStyle(taskStats: TaskStats, timeStats: TimeStats): WorkingStyle {
        return when {
            // 深度工作者：长时间专注，任务完成质量高
            timeStats.averageSessionDuration > 180 && taskStats.completionRate > 80 -> WorkingStyle.DEEP_WORKER
            
            // 多任务处理者：任务数量多，切换频繁
            taskStats.totalTasks > 20 && timeStats.totalFocusedMinutes < 240 -> WorkingStyle.MULTITASKER
            
            // 结构化工作者：任务分类清晰，时间规律
            taskStats.tasksByCategory.size > 3 -> WorkingStyle.STRUCTURED
            
            // 协作型工作者：任务中有较多协作内容
            taskStats.tasksByCategory.containsKey("会议") -> WorkingStyle.COLLABORATIVE
            
            else -> WorkingStyle.FLEXIBLE
        }
    }

    /**
     * 识别激励因素
     */
    private fun identifyMotivationFactors(taskStats: TaskStats, goalStats: GoalStats): List<String> {
        val factors = mutableListOf<String>()
        
        if (taskStats.completionRate > 80) {
            factors.add("成就感驱动")
        }
        if (goalStats.goalCompletionRate > 75) {
            factors.add("目标导向")
        }
        if (taskStats.tasksByPriority.getOrDefault(Priority.HIGH, 0) > 0) {
            factors.add("挑战性任务偏好")
        }
        
        return factors.ifEmpty { listOf("需要进一步分析") }
    }

    /**
     * 识别压力指标
     */
    private fun identifyStressIndicators(timeStats: TimeStats, taskStats: TaskStats): List<String> {
        val indicators = mutableListOf<String>()
        
        if (timeStats.averageSessionDuration < 60) {
            indicators.add("专注时间不足")
        }
        if (taskStats.completionRate < 50) {
            indicators.add("任务积压严重")
        }
        if (timeStats.totalFocusedMinutes > 600) {
            indicators.add("工作时间过长")
        }
        
        return indicators
    }

    /**
     * 生成增强版AI分析提示词 - 基于复盘原则
     */
    private fun buildEnhancedAnalysisPrompt(data: AIReviewData): String {
        val analysisFramework = when (data.timeFrame) {
            "日" -> "KISS原则（Keep It Simple, Stupid）"
            "周" -> "PDCA循环（Plan-Do-Check-Act）"
            "月" -> "GRAI框架（Goal-Result-Analysis-Insight）"
            "年" -> "KPT模型（Keep-Problem-Try）"
            else -> "综合分析框架"
        }

        return when (data.timeFrame) {
            "日" -> buildKISSPrompt(data)
            "周" -> buildPDCAPrompt(data)
            "月" -> buildGRAIPrompt(data)
            "年" -> buildKPTPrompt(data)
            else -> buildGeneralPrompt(data, analysisFramework)
        }
    }

    /**
     * KISS原则日复盘提示词
     */
    private fun buildKISSPrompt(data: AIReviewData): String {
        return """
请作为20年经验的效率管理顾问，基于KISS原则（Keep It Simple, Stupid）对今日工作进行深度复盘分析：

【今日数据概览】
• 任务完成：${data.taskStats.completedTasks}/${data.taskStats.totalTasks} (${String.format("%.1f", data.taskStats.completionRate)}%)
• 专注时间：${data.timeStats.totalFocusedMinutes}分钟
• 时间会话：${data.timeStats.totalTimeSessions}次
• 平均专注：${data.timeStats.averageSessionDuration}分钟/次
• 中断次数：${data.timeStats.interruptionCount}次

【任务优先级分布】
${data.taskStats.tasksByPriority.entries.joinToString("\n") { "• ${it.key}优先级：${it.value}个任务" }}

【时间投入分布】
${data.timeStats.timeByCategory.entries.joinToString("\n") { "• ${it.key}：${it.value}分钟" }}

请严格按照KISS复盘框架输出分析：

## Keep - 保持（做得好的事情）
[Keep1] **具体事项** 详细分析这件事为什么做得好，好在哪些方面，如何在明天继续保持这个优势
[Keep2] **具体事项** 详细分析成功的关键因素，以及如何将这个成功模式应用到其他工作中
[Keep3] **具体事项** 分析这个好习惯的价值，以及如何强化和巩固

## Improve - 改进（需要提升的地方）
[Improve1] **具体问题** 详细分析问题的根本原因，提出3个具体的改进行动，包含明天的执行计划
[Improve2] **具体问题** 深入分析影响效率的因素，制定渐进式改进策略，设定可量化的目标
[Improve3] **具体问题** 识别改进的优先级，提供具体的工具、方法或技巧建议

## Stop - 停止（没必要做的事情）
[Stop1] **无效行为** 分析这个行为为什么浪费时间，停止后能节省多少时间，如何避免重复
[Stop2] **低价值活动** 详细说明为什么这个活动价值低，用什么更有价值的活动来替代
[Stop3] **干扰因素** 分析干扰的来源和影响，提供具体的消除或减少干扰的方法

## Start - 开始（想做但没做的事情）
[Start1] **新习惯** 详细说明为什么要开始这个习惯，如何在明天开始实施，预期的效果
[Start2] **新方法** 分析这个方法的价值，提供具体的实施步骤和时间安排
[Start3] **新目标** 说明这个目标的重要性，制定明天开始的第一步行动计划

要求：
1. 每个条目都要基于今日的具体数据和表现
2. 提供可操作的具体建议，避免空泛的建议
3. 重点关注明天可以立即实施的改进
4. 体现KISS原则的简单有效特点
5. 语言简洁有力，重点突出
        """.trimIndent()
    }

    /**
     * PDCA循环周复盘提示词
     */
    private fun buildPDCAPrompt(data: AIReviewData): String {
        return """
请作为20年经验的效率管理顾问，基于PDCA循环对本周工作进行系统性复盘分析：

【本周数据概览】
• 任务完成：${data.taskStats.completedTasks}/${data.taskStats.totalTasks} (${String.format("%.1f", data.taskStats.completionRate)}%)
• 专注时间：${data.timeStats.totalFocusedMinutes}分钟
• 日均专注：${data.timeStats.totalFocusedMinutes / 7}分钟
• 时间会话：${data.timeStats.totalTimeSessions}次
• 中断次数：${data.timeStats.interruptionCount}次

请严格按照PDCA循环框架输出分析：

## Plan - 计划回顾
[Plan1] **目标设定分析** 本周目标是否合理？完成度如何？目标与实际能力的匹配度分析
[Plan2] **计划制定评估** 周计划的可执行性如何？时间分配是否合理？哪些计划过于乐观或保守？
[Plan3] **优先级判断** 重要任务的识别和排序是否准确？是否把时间花在了最有价值的事情上？

## Do - 执行分析
[Do1] **行动力评估** 计划执行的效率如何？哪些任务执行得很好？哪些拖延了？
[Do2] **时间管理** 实际时间分配与计划的偏差分析，专注时间的质量评估
[Do3] **工作方式** 工作方法和流程的有效性，团队协作或个人工作的效率分析

## Check - 检查反思
[Check1] **结果对比** 实际成果与预期目标的差距分析，超预期和未达标的原因
[Check2] **过程问题** 执行过程中遇到的主要障碍和挑战，问题的根本原因分析
[Check3] **模式识别** 本周工作中的成功模式和失败模式，可复制的经验和需要避免的陷阱

## Act - 改进行动
[Act1] **下周优化** 基于本周经验，下周计划制定的改进策略，具体的调整措施
[Act2] **流程改进** 工作流程和方法的优化建议，提高效率的具体工具和技巧
[Act3] **习惯养成** 需要建立或改进的工作习惯，具体的实施计划和监控方法

要求：
1. 每个分析都要基于本周的具体数据和表现
2. 体现PDCA循环的系统性和连续改进特点
3. 提供下周可以立即实施的具体改进措施
4. 重点关注可量化的目标和可追踪的指标
        """.trimIndent()
    }

    /**
     * GRAI框架月复盘提示词
     */
    private fun buildGRAIPrompt(data: AIReviewData): String {
        return """
请作为20年经验的效率管理顾问，基于GRAI框架对本月工作进行深度复盘分析：

【本月数据概览】
• 任务完成：${data.taskStats.completedTasks}/${data.taskStats.totalTasks} (${String.format("%.1f", data.taskStats.completionRate)}%)
• 专注时间：${data.timeStats.totalFocusedMinutes}分钟
• 日均专注：${data.timeStats.totalFocusedMinutes / 30}分钟
• 工作效率趋势：${if (data.taskStats.completionRate > 80) "优秀" else if (data.taskStats.completionRate > 60) "良好" else "需要改进"}

请严格按照GRAI框架输出分析：

## Goal - 目标回顾
[Goal1] **月度目标达成** 本月设定的主要目标完成情况如何？哪些目标超额完成？哪些未达标？
[Goal2] **目标质量评估** 目标设定是否合理？是否具有挑战性？目标之间的优先级是否清晰？
[Goal3] **目标调整分析** 执行过程中是否需要调整目标？调整的原因和合理性分析

## Result - 结果评估
[Result1] **具体成果** 本月取得的具体成果和里程碑，量化的工作产出和质量评估
[Result2] **能力提升** 在专业技能、工作方法、个人效率方面的具体提升和进步
[Result3] **影响力扩展** 工作成果对团队、项目或个人发展的积极影响和价值创造

## Analysis - 原因分析
[Analysis1] **成功因素** 本月成功的关键因素分析，哪些策略和方法特别有效？
[Analysis2] **失败原因** 未完成目标或效率低下的根本原因，外部环境和内部因素的影响
[Analysis3] **模式总结** 本月工作中的规律和模式，高效时段、最佳工作状态的特征分析

## Insight - 深度洞察
[Insight1] **自我认知** 通过本月的工作，对自己的工作风格、优势和短板有什么新的认识？
[Insight2] **方法论升级** 在时间管理、任务规划、执行力方面获得了哪些新的方法和理念？
[Insight3] **成长方向** 基于本月的表现和反思，明确下个月的重点发展方向和改进策略

要求：
1. 每个分析都要基于本月的具体数据和长期趋势
2. 体现GRAI框架的深度思考和系统分析特点
3. 重点关注个人成长和能力提升的洞察
4. 为下个月的规划提供有价值的参考依据
        """.trimIndent()
    }

    /**
     * KPT模型年复盘提示词
     */
    private fun buildKPTPrompt(data: AIReviewData): String {
        return """
请作为20年经验的效率管理顾问，基于KPT模型对本年度工作进行全面复盘分析：

【年度数据概览】
• 年度任务完成率：${String.format("%.1f", data.taskStats.completionRate)}%
• 年度专注时间：${data.timeStats.totalFocusedMinutes}分钟
• 工作效率评级：${if (data.taskStats.completionRate > 85) "卓越" else if (data.taskStats.completionRate > 70) "优秀" else if (data.taskStats.completionRate > 55) "良好" else "需要大幅改进"}

请严格按照KPT模型输出分析：

## Keep - 保持（今年特别有效的做法）
[Keep1] **核心优势** 今年最值得保持的工作方法或习惯，为什么特别有效？如何在明年进一步强化？
[Keep2] **成功模式** 今年在项目管理、时间规划或团队协作方面的成功模式，可复制的经验总结
[Keep3] **个人特质** 今年发挥出的个人优势和特长，如何将这些优势转化为更大的价值创造？

## Problem - 问题（遇到的主要挑战）
[Problem1] **效率瓶颈** 今年影响工作效率的主要问题，根本原因分析和影响程度评估
[Problem2] **能力短板** 在专业技能、管理能力或个人发展方面的不足，具体表现和改进紧迫性
[Problem3] **系统性问题** 工作流程、时间管理或目标设定方面的系统性问题，需要根本性改变的地方

## Try - 尝试（明年想要尝试的新方法）
[Try1] **新工具方法** 明年计划尝试的新工具、新方法或新技术，预期效果和实施计划
[Try2] **能力拓展** 明年重点发展的新技能或新领域，学习计划和成长路径设计
[Try3] **工作方式创新** 在工作模式、协作方式或个人管理方面的创新尝试，风险评估和收益预期

## 年度成长总结
基于Keep-Problem-Try分析，制定明年的核心发展策略：
• 个人成长方向（内在提升）
• 职业发展规划（外在拓展）
• 生活工作平衡（全面发展）

要求：
1. 每个分析都要基于全年的数据趋势和重大事件
2. 体现KPT模型的全面性和前瞻性特点
3. 重点关注长期发展和战略规划
4. 为明年的目标设定提供科学依据
        """.trimIndent()
    }

    /**
     * 通用分析提示词
     */
    private fun buildGeneralPrompt(data: AIReviewData, framework: String): String {
        return """
请基于${framework}对以下${data.timeFrame}复盘数据进行专业分析：

【数据概览】
• 任务完成：${data.taskStats.completedTasks}/${data.taskStats.totalTasks} (${String.format("%.1f", data.taskStats.completionRate)}%)
• 专注时间：${data.timeStats.totalFocusedMinutes}分钟
• 时间会话：${data.timeStats.totalTimeSessions}次
• 平均会话：${data.timeStats.averageSessionDuration}分钟
• 中断次数：${data.timeStats.interruptionCount}次

请严格按照以下格式输出分析结果：

## 核心洞察
[洞察1] **标题** 具体分析内容，基于数据发现的关键问题或模式
[洞察2] **标题** 具体分析内容，深入挖掘效率瓶颈或优势
[洞察3] **标题** 具体分析内容，识别改进机会或风险点

## 优化建议  
[建议1] **标题** 具体可执行的改进方案，包含预期效果
[建议2] **标题** 具体可执行的改进方案，包含实施步骤
[建议3] **标题** 具体可执行的改进方案，包含时间安排

## 行动方案
基于${framework}制定的系统性改进计划，包含短期和长期目标。

要求：
1. 每个洞察和建议都要基于具体数据
2. 避免空泛的建议，提供可操作的具体方案
3. 考虑用户的实际工作场景和能力
4. 体现20年管理咨询的专业水准
5. 语言简洁有力，重点突出
        """.trimIndent()
    }

    /**
     * 执行对比分析
     */
    private suspend fun performComparisonAnalysis(
        currentStats: TaskStats,
        currentTimeStats: TimeStats,
        currentGoalStats: GoalStats
    ): ComparisonAnalysis {
        // 获取上一周期的数据进行对比
        val previousPeriodStart = when (selectedTimeFrame.value) {
            "日" -> LocalDate.now().minusDays(1)
            "周" -> LocalDate.now().minusWeeks(1)
            "月" -> LocalDate.now().minusMonths(1)
            "年" -> LocalDate.now().minusYears(1)
            else -> LocalDate.now().minusDays(1)
        }
        
        val previousPeriodEnd = when (selectedTimeFrame.value) {
            "日" -> previousPeriodStart
            "周" -> previousPeriodStart.plusWeeks(1)
            "月" -> previousPeriodStart.plusMonths(1)
            "年" -> previousPeriodStart.plusYears(1)
            else -> previousPeriodStart
        }
        
        // 计算对比数据
        val productivityChange = calculateProductivityChange(currentStats)
        val focusChange = calculateFocusTimeChange(currentTimeStats)
        val completionRateChange = calculateCompletionRateChange(currentStats)
        
        return ComparisonAnalysis(
            vsLastPeriod = PeriodComparison(
                productivityChange = productivityChange,
                focusChange = focusChange,
                completionRateChange = completionRateChange,
                keyChanges = identifyBestPerformanceFactors()
            ),
            vsAverage = AverageComparison(
                aboveAverageMetrics = listOf("任务完成率", "专注时间"),
                belowAverageMetrics = listOf("目标达成率"),
                overallPerformanceScore = 75f
            ),
            vsBestPeriod = BestPeriodComparison(
                bestPeriodDate = "上月第2周",
                currentVsBest = 85f,
                whatMadeBestPeriodGood = identifyImprovementOpportunities()
            )
        )
    }

    /**
     * 计算增强版生产力得分
     */
    private fun calculateEnhancedProductivityScore(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats,
        trendAnalysis: TrendAnalysis,
        efficiencyPatterns: EfficiencyPatterns
    ): Int {
        // 基础得分计算
        val baseScore = (taskStats.completionRate * 40 + 
                        timeStats.averageSessionDuration / 60f * 30 + 
                        goalStats.goalCompletionRate * 20 + 
                        timeStats.totalFocusedMinutes / 60f * 10).coerceIn(0f, 100f)
        
        // 趋势加成
        val trendBonus = when (trendAnalysis.productivityTrend.lastOrNull()?.let { 
            calculateTrendDirection(trendAnalysis.productivityTrend) 
        }) {
            TrendDirection.IMPROVING -> 5f
            TrendDirection.STABLE -> 0f
            TrendDirection.DECLINING -> -5f
            TrendDirection.FLUCTUATING -> -2f
            else -> 0f
        }
        
        // 效率模式加成
        val efficiencyBonus = if (efficiencyPatterns.peakHours.isNotEmpty()) 3f else 0f
        
        return (baseScore + trendBonus + efficiencyBonus).coerceIn(0f, 100f).toInt()
    }

    /**
     * 生成增强版洞察
     */
    private suspend fun generateEnhancedInsights(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats,
        trendAnalysis: TrendAnalysis,
        efficiencyPatterns: EfficiencyPatterns,
        personalizedInsights: PersonalizedInsights,
        comparisonAnalysis: ComparisonAnalysis,
        timeFrame: String
    ): List<AIInsight> {
        val insights = mutableListOf<AIInsight>()
        
        // 生产力模式洞察
        if (efficiencyPatterns.peakProductivityHours.isNotEmpty()) {
            insights.add(AIInsight(
                id = "productivity_pattern_${System.currentTimeMillis()}",
                type = InsightType.PRODUCTIVITY_PATTERN,
                title = "发现您的黄金时段",
                description = "您在${efficiencyPatterns.peakProductivityHours.joinToString(", ")}点效率最高，建议将重要任务安排在这些时段。",
                impact = InsightImpact.HIGH
            ))
        }
        
        // 时间管理洞察
        if (timeStats.averageSessionDuration < 25) {
            insights.add(AIInsight(
                id = "time_management_${System.currentTimeMillis()}",
                type = InsightType.TIME_MANAGEMENT,
                title = "专注时间偏短",
                description = "您的平均专注时间为${timeStats.averageSessionDuration.toInt()}分钟，建议尝试番茄工作法提升专注效果。",
                impact = InsightImpact.MEDIUM
            ))
        }
        
        // 任务效率洞察
        if (taskStats.completionRate < 70) {
            insights.add(AIInsight(
                id = "task_efficiency_${System.currentTimeMillis()}",
                type = InsightType.TASK_EFFICIENCY,
                title = "任务完成率待提升",
                description = "当前完成率${(taskStats.completionRate).toInt()}%，建议优化任务规划和时间分配。",
                impact = InsightImpact.HIGH
            ))
        }
        
        return insights
    }

    /**
     * 生成增强版建议
     */
    private suspend fun generateEnhancedRecommendations(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats,
        trendAnalysis: TrendAnalysis,
        efficiencyPatterns: EfficiencyPatterns,
        personalizedInsights: PersonalizedInsights,
        comparisonAnalysis: ComparisonAnalysis
    ): List<AIRecommendation> {
        val recommendations = mutableListOf<AIRecommendation>()
        
        // 基于工作风格的建议
        when (personalizedInsights.workingStyle) {
            WorkingStyle.DEEP_WORKER -> {
                recommendations.add(AIRecommendation(
                    id = "deep_work_${System.currentTimeMillis()}",
                    title = "保持深度工作优势",
                    description = "继续发挥您的深度专注能力，建议每天安排2-3个深度工作时段。",
                    priority = RecommendationPriority.HIGH,
                    estimatedImpact = "提升20%工作效率",
                    actionType = RecommendationType.FOCUS_IMPROVEMENT
                ))
            }
            WorkingStyle.MULTITASKER -> {
                recommendations.add(AIRecommendation(
                    id = "multitask_${System.currentTimeMillis()}",
                    title = "优化多任务切换",
                    description = "建议使用时间分块技术，减少任务切换成本。",
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "减少15%时间浪费",
                    actionType = RecommendationType.TIME_OPTIMIZATION
                ))
            }
            else -> {
                recommendations.add(AIRecommendation(
                    id = "work_rhythm_${System.currentTimeMillis()}",
                    title = "建立个人工作节奏",
                    description = "根据您的工作模式，建议建立稳定的工作节奏。",
                    priority = RecommendationPriority.MEDIUM,
                    estimatedImpact = "提升整体工作稳定性",
                    actionType = RecommendationType.TIME_OPTIMIZATION
                ))
            }
        }
        
        return recommendations
    }

    /**
     * 生成周级趋势数据
     */
    private suspend fun generateWeeklyTrends(startDate: LocalDate): List<TrendPoint> {
        val endDate = startDate.plusWeeks(1)
        val tasks = getTasksByDateRange(startDate, endDate)
        val dailyData = mutableMapOf<LocalDate, Float>()
        
        // 按日统计完成任务数
        tasks.filter { it.isCompleted }.forEach { task ->
            val date = task.completedAt?.toLocalDate() ?: task.createdAt.toLocalDate()
            dailyData[date] = dailyData.getOrDefault(date, 0f) + 1f
        }
        
        return (0..6).map { dayOffset ->
            val date = startDate.plusDays(dayOffset.toLong())
            TrendPoint(
                date = date,
                value = dailyData.getOrDefault(date, 0f)
            )
        }
    }

    /**
     * 生成月级趋势数据
     */
    private suspend fun generateMonthlyTrends(startDate: LocalDate): List<TrendPoint> {
        val endDate = startDate.plusMonths(1)
        val tasks = getTasksByDateRange(startDate, endDate)
        val weeklyData = mutableMapOf<Int, Float>()
        
        // 按周统计完成任务数
        tasks.filter { it.isCompleted }.forEach { task ->
            val date = task.completedAt?.toLocalDate() ?: task.createdAt.toLocalDate()
            val weekOfMonth = ((date.dayOfMonth - 1) / 7) + 1
            weeklyData[weekOfMonth] = weeklyData.getOrDefault(weekOfMonth, 0f) + 1f
        }
        
        return (1..4).map { week ->
            TrendPoint(
                date = startDate.plusWeeks(week.toLong()),
                value = weeklyData.getOrDefault(week, 0f)
            )
        }
    }

    /**
     * 计算趋势方向
     */
    private fun calculateTrendDirection(dataPoints: List<TrendPoint>): TrendDirection {
        if (dataPoints.size < 3) return TrendDirection.STABLE
        
        val values = dataPoints.map { it.value }
        val firstHalf = values.take(values.size / 2).average()
        val secondHalf = values.drop(values.size / 2).average()
        
        val change = (secondHalf - firstHalf) / firstHalf
        val variance = calculateVariance(values)
        
        return when {
            variance > 0.3 -> TrendDirection.FLUCTUATING
            change > 0.1 -> TrendDirection.IMPROVING
            change < -0.1 -> TrendDirection.DECLINING
            else -> TrendDirection.STABLE
        }
    }

    /**
     * 计算趋势强度
     */
    private fun calculateTrendStrength(dataPoints: List<TrendPoint>): Float {
        if (dataPoints.size < 2) return 0f
        
        val values = dataPoints.map { it.value }
        val firstValue = values.first()
        val lastValue = values.last()
        
        return if (firstValue > 0) {
            kotlin.math.abs((lastValue - firstValue) / firstValue) * 100f
        } else {
            0f
        }
    }

    /**
     * 生成专注时间趋势
     */
    private suspend fun generateFocusTimeTrend(startDate: LocalDate, endDate: LocalDate): List<TrendPoint> {
        val timeSessions = getSessionsByDateRange(startDate, endDate)
        val dailyFocus = mutableMapOf<LocalDate, Float>()
        
        timeSessions.forEach { session ->
            val date = session.startTime.atZone(java.time.ZoneId.systemDefault()).toLocalDate()
            dailyFocus[date] = dailyFocus.getOrDefault(date, 0f) + (session.duration / 60f)
        }
        
        return generateDateRange(startDate, endDate).map { date ->
            TrendPoint(
                date = date,
                value = dailyFocus.getOrDefault(date, 0f)
            )
        }
    }

    /**
     * 生成完成率趋势
     */
    private suspend fun generateCompletionRateTrend(startDate: LocalDate, endDate: LocalDate): List<TrendPoint> {
        val tasks = getTasksByDateRange(startDate, endDate)
        val dailyCompletion = mutableMapOf<LocalDate, Pair<Int, Int>>()
        
        tasks.forEach { task ->
            val date = task.createdAt.toLocalDate()
            val current = dailyCompletion.getOrDefault(date, Pair(0, 0))
            dailyCompletion[date] = if (task.isCompleted) {
                Pair(current.first + 1, current.second + 1)
            } else {
                Pair(current.first, current.second + 1)
            }
        }
        
        return generateDateRange(startDate, endDate).map { date ->
            val (completed, total) = dailyCompletion.getOrDefault(date, Pair(0, 0))
            val rate = if (total > 0) (completed.toFloat() / total * 100f) else 0f
            TrendPoint(
                date = date,
                value = rate
            )
        }
    }

    /**
     * 寻找低效时段
     */
    private suspend fun findLowEnergyHours(timeSessions: List<com.timeflow.app.data.model.TimeSession>): List<Int> {
        val hourlyEfficiency = mutableMapOf<Int, Float>()
        
        timeSessions.forEach { session ->
            val hour = session.startTime.atZone(java.time.ZoneId.systemDefault()).hour
            val efficiency = session.duration / 60f // 简化的效率计算
            hourlyEfficiency[hour] = hourlyEfficiency.getOrDefault(hour, 0f) + efficiency
        }
        
        return hourlyEfficiency.toList()
            .sortedBy { it.second }
            .take(3)
            .map { it.first }
    }

    /**
     * 寻找最佳工作日
     */
    private suspend fun findBestDaysOfWeek(tasks: List<com.timeflow.app.data.model.Task>): List<String> {
        val dayEfficiency = mutableMapOf<Int, Float>()
        
        tasks.filter { it.isCompleted }.forEach { task ->
            val dayOfWeek = (task.completedAt ?: task.createdAt).dayOfWeek.value
            dayEfficiency[dayOfWeek] = dayEfficiency.getOrDefault(dayOfWeek, 0f) + 1f
        }
        
        return dayEfficiency.toList()
            .sortedByDescending { it.second }
            .take(3)
            .map { (day, _) ->
                when (day) {
                    1 -> "周一"
                    2 -> "周二"
                    3 -> "周三"
                    4 -> "周四"
                    5 -> "周五"
                    6 -> "周六"
                    7 -> "周日"
                    else -> ""
                }
            }
    }

    /**
     * 分析任务类型效率
     */
    private suspend fun analyzeTaskTypeEfficiency(tasks: List<com.timeflow.app.data.model.Task>): Map<String, Float> {
        val typeEfficiency = mutableMapOf<String, Pair<Int, Int>>()
        
        tasks.forEach { task ->
            val type = task.tags.firstOrNull()?.name ?: "其他"
            val current = typeEfficiency.getOrDefault(type, Pair(0, 0))
            typeEfficiency[type] = if (task.isCompleted) {
                Pair(current.first + 1, current.second + 1)
            } else {
                Pair(current.first, current.second + 1)
            }
        }
        
        return typeEfficiency.mapValues { (_, pair) ->
            if (pair.second > 0) pair.first.toFloat() / pair.second * 100f else 0f
        }
    }

    /**
     * 识别改进领域
     */
    private suspend fun identifyImprovementAreas(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats
    ): List<String> {
        val areas = mutableListOf<String>()
        
        if (taskStats.completionRate < 70) {
            areas.add("任务完成率")
        }
        if (timeStats.averageSessionDuration < 120) {
            areas.add("专注时间")
        }
        if (goalStats.goalCompletionRate < 60) {
            areas.add("目标达成")
        }
        
        return areas
    }

    /**
     * 识别个人优势
     */
    private suspend fun identifyStrengths(
        taskStats: TaskStats,
        timeStats: TimeStats,
        goalStats: GoalStats
    ): List<String> {
        val strengths = mutableListOf<String>()
        
        if (taskStats.completionRate > 80) {
            strengths.add("高效执行")
        }
        if (timeStats.averageSessionDuration > 180) {
            strengths.add("深度专注")
        }
        if (goalStats.goalCompletionRate > 75) {
            strengths.add("目标导向")
        }
        
        return strengths
    }

    // 辅助方法
    private fun calculateVariance(values: List<Float>): Double {
        if (values.isEmpty()) return 0.0
        val mean = values.average()
        return values.map { (it - mean) * (it - mean) }.average()
    }

    private fun generateDateRange(startDate: LocalDate, endDate: LocalDate): List<LocalDate> {
        val dates = mutableListOf<LocalDate>()
        var current = startDate
        while (!current.isAfter(endDate)) {
            dates.add(current)
            current = current.plusDays(1)
        }
        return dates
    }

    private fun calculateProductivityChange(currentStats: TaskStats): Float {
        // 简化实现，实际应该与历史数据对比
        return 0f
    }

    private fun calculateFocusTimeChange(currentTimeStats: TimeStats): Float {
        // 简化实现，实际应该与历史数据对比
        return 0f
    }

    private fun calculateCompletionRateChange(currentStats: TaskStats): Float {
        // 简化实现，实际应该与历史数据对比
        return 0f
    }

    private fun identifyBestPerformanceFactors(): List<String> {
        return listOf("高效时段利用", "任务优先级管理", "专注时间控制")
    }

    private fun identifyImprovementOpportunities(): List<String> {
        return listOf("减少任务切换", "优化时间分配", "提升专注质量")
    }

    /**
     * 获取指定日期范围内的任务
     */
    private suspend fun getTasksByDateRange(startDate: LocalDate, endDate: LocalDate): List<com.timeflow.app.data.model.Task> {
        return taskRepository.getAllTasks().filter { task ->
            val taskDate = task.createdAt.toLocalDate()
            !taskDate.isBefore(startDate) && !taskDate.isAfter(endDate)
        }
    }

    // 重复的方法定义已删除，保留前面的实现

    /**
     * 计算会话质量得分
     */
    private fun calculateSessionQualityScore(sessions: List<com.timeflow.app.data.model.TimeSession>): Float {
        if (sessions.isEmpty()) return 0f
        
        var totalScore = 0f
        sessions.forEach { session ->
            var sessionScore = 50f // 基础分
            
            // 时长加分
            when (session.duration / 60) {
                in 20..30 -> sessionScore += 20f // 番茄钟时长
                in 31..60 -> sessionScore += 30f // 深度工作时长
                in 61..120 -> sessionScore += 25f // 长时间专注
                else -> sessionScore += 10f
            }
            
            // 完成任务加分
            if (session.taskName.isNotEmpty()) {
                sessionScore += 20f
            }
            
            totalScore += sessionScore.coerceAtMost(100f)
        }
        
        return totalScore / sessions.size
    }

    /**
     * 通用AI内容解析 - 当结构化解析失败时使用
     */
    private fun parseGeneralAnalysisContent(content: String): AIAnalysisResult {
        Log.d(TAG, "开始通用解析，内容长度: ${content.length}")
        
        val insights = mutableListOf<AIInsight>()
        val recommendations = mutableListOf<AIRecommendation>()
        
        try {
            // 按段落分割内容
            val paragraphs = content.split("\n\n").filter { it.isNotBlank() }
            
            var insightCounter = 1
            var recommendationCounter = 1
            
            // 寻找包含关键词的段落
            paragraphs.forEach { paragraph ->
                val trimmedParagraph = paragraph.trim()
                
                when {
                    // 包含洞察关键词的段落
                    trimmedParagraph.contains("洞察", ignoreCase = true) ||
                    trimmedParagraph.contains("分析", ignoreCase = true) ||
                    trimmedParagraph.contains("发现", ignoreCase = true) ||
                    trimmedParagraph.contains("数据", ignoreCase = true) -> {
                        
                        // 提取有意义的句子
                        val sentences = trimmedParagraph.split("。", "！", "？").filter { 
                            it.isNotBlank() && it.length > 10 
                        }
                        
                        sentences.take(2).forEach { sentence ->
                            insights.add(
                                AIInsight(
                                    id = "ai_insight_$insightCounter",
                                    type = InsightType.AI_GENERATED,
                                    title = "AI洞察 $insightCounter",
                                    description = sentence.trim() + "。",
                                    impact = when (insightCounter) {
                                        1 -> InsightImpact.HIGH
                                        else -> InsightImpact.MEDIUM
                                    },
                                    data = mapOf("source" to "general_parsing")
                                )
                            )
                            insightCounter++
                        }
                    }
                    
                    // 包含建议关键词的段落
                    trimmedParagraph.contains("建议", ignoreCase = true) ||
                    trimmedParagraph.contains("推荐", ignoreCase = true) ||
                    trimmedParagraph.contains("应该", ignoreCase = true) ||
                    trimmedParagraph.contains("可以", ignoreCase = true) ||
                    trimmedParagraph.contains("行动", ignoreCase = true) -> {
                        
                        // 提取有意义的句子
                        val sentences = trimmedParagraph.split("。", "！", "？").filter { 
                            it.isNotBlank() && it.length > 10 
                        }
                        
                        sentences.take(2).forEach { sentence ->
                            recommendations.add(
                                AIRecommendation(
                                    id = "ai_recommendation_$recommendationCounter",
                                    title = "AI建议 $recommendationCounter",
                                    description = sentence.trim() + "。",
                                    actionType = RecommendationType.AI_SUGGESTED,
                                    priority = when (recommendationCounter) {
                                        1 -> RecommendationPriority.HIGH
                                        else -> RecommendationPriority.MEDIUM
                                    },
                                    estimatedImpact = "基于数据分析的改进建议"
                                )
                            )
                            recommendationCounter++
                        }
                    }
                }
            }
            
            // 如果还是没有解析到内容，使用简单的分段策略
            if (insights.isEmpty() && recommendations.isEmpty()) {
                Log.d(TAG, "关键词解析无结果，使用分段策略")
                
                val allSentences = content.split("。", "！", "？").filter { 
                    it.isNotBlank() && it.length > 15 
                }.take(6) // 最多取6个句子
                
                // 前半部分作为洞察
                allSentences.take(3).forEachIndexed { index, sentence ->
                    insights.add(
                        AIInsight(
                            id = "ai_insight_${index + 1}",
                            type = InsightType.AI_GENERATED,
                            title = "AI洞察 ${index + 1}",
                            description = sentence.trim() + "。",
                            impact = if (index == 0) InsightImpact.HIGH else InsightImpact.MEDIUM,
                            data = mapOf("source" to "fallback_parsing")
                        )
                    )
                }
                
                // 后半部分作为建议
                allSentences.drop(3).forEachIndexed { index, sentence ->
                    recommendations.add(
                        AIRecommendation(
                            id = "ai_recommendation_${index + 1}",
                            title = "AI建议 ${index + 1}",
                            description = sentence.trim() + "。",
                            actionType = RecommendationType.AI_SUGGESTED,
                            priority = if (index == 0) RecommendationPriority.HIGH else RecommendationPriority.MEDIUM,
                            estimatedImpact = "基于数据分析的改进建议"
                        )
                    )
                }
            }
            
            Log.d(TAG, "通用解析完成: insights=${insights.size}, recommendations=${recommendations.size}")
            
        } catch (e: Exception) {
            Log.e(TAG, "通用解析失败", e)
        }
        
        return AIAnalysisResult(
            insights = insights, 
            recommendations = recommendations,
            analysisTimestamp = LocalDateTime.now(),
            timeFrame = "fallback"
        )
    }
}

