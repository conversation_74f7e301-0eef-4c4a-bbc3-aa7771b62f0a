<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- 背景圆形渐变 -->
    <path
        android:pathData="M24,24 m-24,0 a24,24 0 1,0 48,0 a24,24 0 1,0 -48,0">
        <aapt:attr name="android:fillColor">
            <gradient
                android:type="linear"
                android:startX="0"
                android:startY="0"
                android:endX="48"
                android:endY="48"
                android:angle="135">
                <item android:color="#667eea" android:offset="0.0" />
                <item android:color="#764ba2" android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>
    
    <!-- 主圆形底座 -->
    <path
        android:fillColor="#ffffff"
        android:fillAlpha="0.95"
        android:pathData="M24,24 m-16,0 a16,16 0 1,0 32,0 a16,16 0 1,0 -32,0" />
    
    <!-- 内圆环 -->
    <path
        android:strokeWidth="1.5"
        android:strokeColor="#667eea"
        android:fillColor="#00000000"
        android:pathData="M24,24 m-13,0 a13,13 0 1,0 26,0 a13,13 0 1,0 -26,0" />
    
    <!-- 时钟指针 - 短针 (小时) -->
    <path
        android:strokeWidth="2.5"
        android:strokeColor="#667eea"
        android:strokeLineCap="round"
        android:pathData="M24,24 L24,17" />
    
    <!-- 时钟指针 - 长针 (分钟) -->
    <path
        android:strokeWidth="2"
        android:strokeColor="#764ba2"
        android:strokeLineCap="round"
        android:pathData="M24,24 L29,14" />
    
    <!-- 中心点 -->
    <path
        android:fillColor="#764ba2"
        android:pathData="M24,24 m-2,0 a2,2 0 1,0 4,0 a2,2 0 1,0 -4,0" />
    
    <!-- 时钟刻度点 -->
    <path
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:pathData="M24,8 m-1,0 a1,1 0 1,0 2,0 a1,1 0 1,0 -2,0" />
    
    <path
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:pathData="M40,24 m-1,0 a1,1 0 1,0 2,0 a1,1 0 1,0 -2,0" />
    
    <path
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:pathData="M24,40 m-1,0 a1,1 0 1,0 2,0 a1,1 0 1,0 -2,0" />
    
    <path
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:pathData="M8,24 m-1,0 a1,1 0 1,0 2,0 a1,1 0 1,0 -2,0" />
        
</vector> 