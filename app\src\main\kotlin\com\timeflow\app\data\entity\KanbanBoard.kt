package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * 看板实体类
 */
@Entity(tableName = "kanban_boards")
data class KanbanBoard(
    @PrimaryKey
    val id: String,
    
    val title: String,
    
    val description: String? = null,
    
    val color: String? = null,
    
    val icon: String? = null,
    
    val position: Int = 0,
    
    @ColumnInfo(name = "created_at")
    val createdAt: LocalDateTime,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: LocalDateTime
) 