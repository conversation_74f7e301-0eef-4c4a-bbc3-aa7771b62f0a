package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.CalendarMonth
import androidx.compose.material.icons.outlined.Numbers
import androidx.compose.material.icons.outlined.Upload
import androidx.compose.material.icons.outlined.Check
import androidx.compose.material.icons.outlined.Notifications
import androidx.compose.material.icons.outlined.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringPeriod
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.ReminderType
import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import androidx.compose.ui.platform.LocalContext
import android.app.Activity
import android.util.Log
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import com.timeflow.app.ui.screen.goal.components.RecurringSettingsPanel
import com.timeflow.app.ui.screen.goal.components.ReminderSettingsPanel

/**
 * 编辑目标屏幕
 * 提供表单用于编辑已存在的目标
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditGoalScreen(
    navController: NavController,
    goalId: String,
    viewModel: GoalViewModel = hiltViewModel()
) {
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 加载目标详情
    LaunchedEffect(goalId) {
        viewModel.loadGoalDetail(goalId)
    }
    
    // 获取当前目标
    val currentGoal = viewModel.currentGoal.value
    
    // 状态变量
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var startDate by remember { mutableStateOf<LocalDateTime?>(null) }
    var dueDate by remember { mutableStateOf<LocalDateTime?>(null) }
    var selectedPriority by remember { mutableStateOf(GoalPriority.MEDIUM) }
    
    // 目标量化相关状态
    var goalType by remember { mutableStateOf(GoalType.BOOLEAN) }
    var currentValue by remember { mutableStateOf("0") }
    var targetValue by remember { mutableStateOf("100") }
    var unit by remember { mutableStateOf("") }
    var isRecurring by remember { mutableStateOf(false) }
    var recurringSettings by remember { mutableStateOf<RecurringSettings?>(null) }
    
    // 提醒设置状态
    var reminderSettings = remember { mutableStateListOf<ReminderSetting>() }
    
    // 附件状态
    var attachments = remember { mutableStateListOf<String>() }
    
    // 当目标加载完成后，初始化状态变量
    LaunchedEffect(currentGoal) {
        currentGoal?.let { goal ->
            title = goal.title
            description = goal.description
            startDate = goal.startDate
            dueDate = goal.dueDate
            selectedPriority = goal.priority
            
            // 初始化量化字段
            goal.goalType?.let { goalType = it }
            goal.currentValue?.let { currentValue = it.toString() }
            goal.targetValue?.let { targetValue = it.toString() }
            goal.unit?.let { unit = it }
            goal.isRecurring.let { isRecurring = it }
            goal.recurringSettings?.let { recurringSettings = it }
            
            // 初始化提醒和附件
            reminderSettings.clear()
            reminderSettings.addAll(goal.reminderSettings)
            
            attachments.clear()
            attachments.addAll(goal.attachments)
        }
    }
    
    // 监听UI状态变化
    val uiState = viewModel.uiState.value
    
    // 添加日志输出当前UI状态
    LaunchedEffect(uiState) {
        when(uiState) {
            is GoalUiState.Idle -> android.util.Log.d("EditGoalScreen", "UI状态: Idle")
            is GoalUiState.Loading -> android.util.Log.d("EditGoalScreen", "UI状态: Loading")
            is GoalUiState.Success -> android.util.Log.d("EditGoalScreen", "UI状态: Success")
            is GoalUiState.Error -> android.util.Log.d("EditGoalScreen", "UI状态: Error - ${(uiState as GoalUiState.Error).message}")
        }
    }
    
    // 修改：保存后自动返回逻辑
    // 使用一个布尔值记录是否已尝试过保存操作
    var hasSaveAttempted = remember { mutableStateOf(false) }
    
    LaunchedEffect(uiState) {
        // 只有当曾经尝试过保存，且当前状态为Success时才返回
        if (hasSaveAttempted.value && uiState is GoalUiState.Success) {
            android.util.Log.d("EditGoalScreen", "目标更新成功，返回上级页面")
            navController.popBackStack()
        }
    }
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明状态栏
        }
    }
    
    // 处理状态栏，确保在页面关闭时恢复
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("EditGoalScreen", "EditGoalScreen disposed")
            }
        }
    }
    
    // 主界面 - 与AddGoalScreen保持一致的布局结构
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF7F9FC)) // 与AddGoalScreen统一背景色
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部标题栏 - 使用与AddGoalScreen相同的TopAppBar
        TopAppBar(
            title = {
                Text(
                    text = "编辑目标",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                TextButton(
                    onClick = {
                        try {
                            hasSaveAttempted.value = true  // 标记已尝试保存
                            
                            currentGoal?.let { goal ->
                                // 计算进度
                                val calculatedProgress = if (goalType == GoalType.NUMERIC) {
                                    try {
                                        val current = currentValue.toDoubleOrNull() ?: 0.0
                                        val target = targetValue.toDoubleOrNull() ?: 1.0
                                        if (target == 0.0) 0f else (current / target).toFloat().coerceIn(0f, 1f)
                                    } catch (e: Exception) {
                                        goal.progress
                                    }
                                } else {
                                    goal.progress
                                }
                                
                                val updatedGoal = goal.copy(
                                    title = title,
                                    description = description,
                                    startDate = startDate,
                                    dueDate = dueDate,
                                    priority = selectedPriority,
                                    updatedAt = LocalDateTime.now(),
                                    // 添加新字段
                                    goalType = goalType,
                                    currentValue = currentValue.toDoubleOrNull(),
                                    targetValue = targetValue.toDoubleOrNull(),
                                    unit = unit.takeIf { it.isNotBlank() },
                                    progress = calculatedProgress,
                                    reminderSettings = reminderSettings.toList(),
                                    attachments = attachments.toList(),
                                    isRecurring = isRecurring,
                                    recurringSettings = recurringSettings
                                )
                                viewModel.updateGoal(updatedGoal)
                            }
                        } catch (e: Exception) {
                            // 捕获可能的异常
                            android.util.Log.e("EditGoalScreen", "更新目标失败", e)
                        }
                    },
                    enabled = title.isNotBlank() && currentGoal != null
                ) {
                    Text(
                        text = "保存",
                        fontWeight = FontWeight.Medium,
                        color = DustyLavender
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFFF7F9FC), // 匹配背景色
                titleContentColor = MaterialTheme.colorScheme.primary
            )
        )
        
        // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                .background(Color(0xFFF7F9FC)) // 统一背景色
            ) {
                // 加载状态
                if (uiState is GoalUiState.Loading && currentGoal == null) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = DustyLavender)
                    }
                }
                // 错误状态
                else if (uiState is GoalUiState.Error && currentGoal == null) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = (uiState as GoalUiState.Error).message,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
                // 编辑表单
                else {
                // 表单内容 - 与AddGoalScreen保持一致的结构
                val scrollState = rememberScrollState()
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 目标表单 - 使用与AddGoalScreen相同的Card包装
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(containerColor = Color.White)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                // 标题输入
                                Column {
                                    Text(
                                        text = "目标标题",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    OutlinedTextField(
                                        value = title,
                                        onValueChange = { title = it },
                                        modifier = Modifier.fillMaxWidth(),
                                        placeholder = { Text("输入目标标题，例如：学习Python基础") },
                                        shape = RoundedCornerShape(8.dp),
                                        colors = OutlinedTextFieldDefaults.colors(
                                            focusedBorderColor = DustyLavender,
                                            unfocusedBorderColor = Color(0xFFE2E8F0),
                                            cursorColor = DustyLavender
                                        ),
                                        singleLine = true,
                                        maxLines = 1
                                    )
                                }
                                
                                // 描述输入
                                Column {
                                    Text(
                                        text = "目标描述",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    OutlinedTextField(
                                        value = description,
                                        onValueChange = { description = it },
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(100.dp),
                                        placeholder = { Text("描述这个目标的更多细节（可选）") },
                                        shape = RoundedCornerShape(8.dp),
                                        colors = OutlinedTextFieldDefaults.colors(
                                            focusedBorderColor = DustyLavender,
                                            unfocusedBorderColor = Color(0xFFE2E8F0),
                                            cursorColor = DustyLavender
                                        )
                                    )
                                }
                                
                                // 日期选择
                                Column {
                                    Text(
                                        text = "时间设置",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF333333)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    // 开始日期选择
                                    DatePickerItem(
                                        date = startDate,
                                        icon = Icons.Default.PlayArrow,
                                        title = "开始日期",
                                        subtitle = if (startDate != null) {
                                            startDate!!.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                                        } else {
                                            "点击设置开始日期"
                                        },
                                        onDateSelected = { startDate = it }
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    // 截止日期选择
                                    DatePickerItem(
                                        date = dueDate,
                                        icon = Icons.Outlined.CalendarMonth,
                                        title = "截止日期",
                                        subtitle = if (dueDate != null) {
                                            dueDate!!.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                                        } else {
                                            "点击设置截止日期"
                                        },
                                        onDateSelected = { dueDate = it }
                                    )
                                    
                                    // 提示信息
                                    AnimatedVisibility(
                                        visible = startDate != null && dueDate != null && 
                                                 startDate!!.isAfter(dueDate)
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(top = 8.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Warning,
                                                contentDescription = null,
                                                tint = MaterialTheme.colorScheme.error,
                                                modifier = Modifier.size(16.dp)
                                            )
                                            
                                            Spacer(modifier = Modifier.width(4.dp))
                                            
                                            Text(
                                                text = "开始日期不应晚于截止日期",
                                                fontSize = 12.sp,
                                                color = MaterialTheme.colorScheme.error
                                            )
                                        }
                                    }
                                }
                                
                                // 优先级选择
                                Column {
                                    Text(
                                        text = "优先级",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF333333)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        PriorityOption(
                                            priority = GoalPriority.LOW,
                                            selectedPriority = selectedPriority,
                                            onSelect = { selectedPriority = GoalPriority.LOW }
                                        )
                                        PriorityOption(
                                            priority = GoalPriority.MEDIUM,
                                            selectedPriority = selectedPriority,
                                            onSelect = { selectedPriority = GoalPriority.MEDIUM }
                                        )
                                        PriorityOption(
                                            priority = GoalPriority.HIGH,
                                            selectedPriority = selectedPriority,
                                            onSelect = { selectedPriority = GoalPriority.HIGH }
                                        )
                                        PriorityOption(
                                            priority = GoalPriority.URGENT,
                                            selectedPriority = selectedPriority,
                                            onSelect = { selectedPriority = GoalPriority.URGENT }
                                        )
                                    }
                                }
                                
                                // 目标类型选择
                                Column {
                                    Text(
                                        text = "目标类型",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF333333)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                                    ) {
                                        GoalTypeOption(
                                            type = GoalType.NUMERIC,
                                            selectedType = goalType,
                                            onSelect = { goalType = GoalType.NUMERIC },
                                            modifier = Modifier.weight(1f)
                                        )
                                        GoalTypeOption(
                                            type = GoalType.BOOLEAN,
                                            selectedType = goalType,
                                            onSelect = { goalType = GoalType.BOOLEAN },
                                            modifier = Modifier.weight(1f)
                                        )
                                    }
                                }
                                
                                // 量化设置 (仅当选择数值型目标时显示)
                                AnimatedVisibility(visible = goalType == GoalType.NUMERIC) {
                                    Column {
                                        Text(
                                            text = "量化设置",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = Color(0xFF333333)
                                        )
                                        
                                        Spacer(modifier = Modifier.height(8.dp))
                                        
                                        Card(
                                            modifier = Modifier.fillMaxWidth(),
                                            shape = RoundedCornerShape(8.dp),
                                            colors = CardDefaults.cardColors(
                                                containerColor = Color(0xFFF8FAFC)
                                            ),
                                            elevation = CardDefaults.cardElevation(
                                                defaultElevation = 0.dp
                                            )
                                        ) {
                                            Column(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(16.dp),
                                                verticalArrangement = Arrangement.spacedBy(16.dp)
                                            ) {
                                                // 当前值和目标值
                                                Row(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                                                ) {
                                                    // 当前值输入
                                                    Column(modifier = Modifier.weight(1f)) {
                                                        Text(
                                                            text = "当前值",
                                                            fontSize = 14.sp,
                                                            color = Color(0xFF666666)
                                                        )
                                                        
                                                        Spacer(modifier = Modifier.height(4.dp))
                                                        
                                                        OutlinedTextField(
                                                            value = currentValue,
                                                            onValueChange = { 
                                                                if (it.isEmpty() || it.toDoubleOrNull() != null) {
                                                                    currentValue = it 
                                                                }
                                                            },
                                                            modifier = Modifier.fillMaxWidth(),
                                                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                            shape = RoundedCornerShape(8.dp),
                                                            colors = OutlinedTextFieldDefaults.colors(
                                                                focusedBorderColor = DustyLavender,
                                                                unfocusedBorderColor = Color(0xFFE2E8F0),
                                                                cursorColor = DustyLavender
                                                            ),
                                                            singleLine = true,
                                                            maxLines = 1
                                                        )
                                                    }
                                                    
                                                    // 目标值输入
                                                    Column(modifier = Modifier.weight(1f)) {
                                                        Text(
                                                            text = "目标值",
                                                            fontSize = 14.sp,
                                                            color = Color(0xFF666666)
                                                        )
                                                        
                                                        Spacer(modifier = Modifier.height(4.dp))
                                                        
                                                        OutlinedTextField(
                                                            value = targetValue,
                                                            onValueChange = { 
                                                                if (it.isEmpty() || it.toDoubleOrNull() != null) {
                                                                    targetValue = it 
                                                                }
                                                            },
                                                            modifier = Modifier.fillMaxWidth(),
                                                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                            shape = RoundedCornerShape(8.dp),
                                                            colors = OutlinedTextFieldDefaults.colors(
                                                                focusedBorderColor = DustyLavender,
                                                                unfocusedBorderColor = Color(0xFFE2E8F0),
                                                                cursorColor = DustyLavender
                                                            ),
                                                            singleLine = true,
                                                            maxLines = 1
                                                        )
                                                    }
                                                }
                                                
                                                // 单位和进度
                                                Row(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                                                ) {
                                                    // 单位输入
                                                    Column(modifier = Modifier.weight(1f)) {
                                                        Text(
                                                            text = "单位",
                                                            fontSize = 14.sp,
                                                            color = Color(0xFF666666)
                                                        )
                                                        
                                                        Spacer(modifier = Modifier.height(4.dp))
                                                        
                                                        OutlinedTextField(
                                                            value = unit,
                                                            onValueChange = { unit = it },
                                                            modifier = Modifier.fillMaxWidth(),
                                                            placeholder = { Text("如：kg、元、次") },
                                                            shape = RoundedCornerShape(8.dp),
                                                            colors = OutlinedTextFieldDefaults.colors(
                                                                focusedBorderColor = DustyLavender,
                                                                unfocusedBorderColor = Color(0xFFE2E8F0),
                                                                cursorColor = DustyLavender
                                                            ),
                                                            singleLine = true,
                                                            maxLines = 1
                                                        )
                                                    }
                                                    
                                                    // 进度显示
                                                    Column(modifier = Modifier.weight(1f)) {
                                                        Text(
                                                            text = "当前进度",
                                                            fontSize = 14.sp,
                                                            color = Color(0xFF666666)
                                                        )
                                                        
                                                        Spacer(modifier = Modifier.height(4.dp))
                                                        
                                                        // 计算当前进度百分比
                                                        val progressPercent = remember(currentValue, targetValue) {
                                                            try {
                                                                val current = currentValue.toDoubleOrNull() ?: 0.0
                                                                val target = targetValue.toDoubleOrNull() ?: 1.0
                                                                if (target == 0.0) 0 else ((current / target) * 100).toInt().coerceIn(0, 100)
                                                            } catch (e: Exception) {
                                                                0
                                                            }
                                                        }
                                                        
                                                        LinearProgressIndicator(
                                                            progress = { progressPercent / 100f },
                                                            modifier = Modifier
                                                                .fillMaxWidth()
                                                                .height(48.dp)
                                                                .clip(RoundedCornerShape(8.dp)),
                                                            color = DustyLavender,
                                                            trackColor = DustyLavender.copy(alpha = 0.2f)
                                                        )
                                                        
                                                        Text(
                                                            text = "$progressPercent%",
                                                            fontSize = 12.sp,
                                                            color = Color(0xFF666666),
                                                            textAlign = TextAlign.End,
                                                            modifier = Modifier.fillMaxWidth()
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                
                                // 周期性设置选项
                                RecurringSettingsPanel(
                                    isRecurring = isRecurring,
                                    onRecurringChanged = { isRecurring = it },
                                    recurringSettings = recurringSettings,
                                    onSettingsChanged = { recurringSettings = it }
                                )
                                
                                // 提醒设置
                                ReminderSettingsPanel(
                                    reminderSettings = reminderSettings,
                                    onReminderSettingsChanged = { newSettings ->
                                        reminderSettings.clear()
                                        reminderSettings.addAll(newSettings)
                                    }
                                )
                                
                                // 附件区域
                                Column {
                                    Text(
                                        text = "附件",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF333333)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    // 上传按钮
                                    Button(
                                        onClick = {
                                            // 这里实现文件上传功能
                                            // 暂时模拟添加一个示例附件
                                            attachments.add("attachment_${System.currentTimeMillis()}.pdf")
                                        },
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = Color(0xFFF0F4FF),
                                            contentColor = DustyLavender
                                        ),
                                        shape = RoundedCornerShape(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Outlined.Upload,
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("上传附件")
                                    }
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    // 附件列表
                                    AnimatedVisibility(visible = attachments.isNotEmpty()) {
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            attachments.forEachIndexed { index, attachment ->
                                                AttachmentItem(
                                                    fileName = attachment,
                                                    onDelete = { attachments.removeAt(index) }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                }
            }
        }
    }
}

// 日期选择器项
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DatePickerItem(
    date: LocalDateTime?,
    icon: ImageVector,
    title: String,
    subtitle: String,
    onDateSelected: (LocalDateTime) -> Unit
) {
    var showDatePicker by remember { mutableStateOf(false) }
    
    // 日期选择对话框
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = date?.atZone(java.time.ZoneId.systemDefault())
                ?.toInstant()?.toEpochMilli()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(onClick = {
                    // 获取选择的日期
                    datePickerState.selectedDateMillis?.let { millis ->
                        val selectedDate = java.time.Instant.ofEpochMilli(millis)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDateTime()
                        onDateSelected(selectedDate)
                    }
                    showDatePicker = false
                }) {
                    Text("确定", fontSize = 13.sp, letterSpacing = (-0.2).sp)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("取消", fontSize = 13.sp, letterSpacing = (-0.2).sp)
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                usePlatformDefaultWidth = false // 使用自定义宽度
            ),
            modifier = Modifier
                .fillMaxWidth(0.95f) // 设置对话框宽度为屏幕的95%
                .padding(16.dp),
            colors = DatePickerDefaults.colors(
                containerColor = Color.White,
                titleContentColor = DustyLavender,
                headlineContentColor = Color(0xFF333333),
                weekdayContentColor = Color(0xFF666666),
                dayContentColor = Color(0xFF333333),
                selectedDayContainerColor = DustyLavender,
                todayContentColor = DustyLavender,
                todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
            )
        ) {
            DatePicker(
                state = datePickerState,
                title = { 
                    Text(
                        text = "选择日期", 
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(start = 16.dp, top = 16.dp)
                    ) 
                },
                headline = { 
                    Text(
                        text = datePickerState.selectedDateMillis?.let {
                            val selectedDate = java.time.Instant.ofEpochMilli(it)
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate()
                            selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                        } ?: "未选择日期",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                showModeToggle = false,
                colors = DatePickerDefaults.colors(
                    containerColor = Color.White,
                    titleContentColor = DustyLavender,
                    headlineContentColor = Color(0xFF333333),
                    weekdayContentColor = Color(0xFF666666),
                    dayContentColor = Color(0xFF333333),
                    selectedDayContainerColor = DustyLavender,
                    todayContentColor = DustyLavender,
                    todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
                )
            )
        }
    }
    
    // 日期选择按钮
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable { showDatePicker = true },
        color = Color(0xFFF8FAFC),
        tonalElevation = 0.dp,
        shadowElevation = 0.dp
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标容器
            Box(
                modifier = Modifier
                    .size(44.dp)
                    .clip(CircleShape)
                    .background(
                        color = DustyLavender.copy(alpha = 0.7f) // 取消渐变，使用纯色
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 文本内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = if (date != null) Color(0xFF333333) else Color(0xFF888888),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 清除按钮（仅当有日期时显示）
            AnimatedVisibility(visible = date != null) {
                IconButton(
                    onClick = { onDateSelected(LocalDateTime.now()) },
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "清除日期",
                        tint = Color(0xFF888888),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            IconButton(
                onClick = { showDatePicker = true },
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "选择日期",
                    tint = Color(0xFF888888)
                )
            }
        }
    }
}

// 优先级选项组件 (从AddGoalScreen复用)
@Composable
private fun PriorityOption(
    priority: GoalPriority,
    selectedPriority: GoalPriority,
    onSelect: () -> Unit
) {
    val isSelected = priority == selectedPriority
    
    val priorityColor = when (priority) {
        GoalPriority.LOW -> Color(0xFF10B981)
        GoalPriority.MEDIUM -> Color(0xFFF59E0B)
        GoalPriority.HIGH -> Color(0xFFEF4444)
        GoalPriority.URGENT -> Color(0xFFDC2626)
    }
    
    val priorityText = when (priority) {
        GoalPriority.LOW -> "低"
        GoalPriority.MEDIUM -> "中"
        GoalPriority.HIGH -> "高"
        GoalPriority.URGENT -> "紧急"
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onSelect
            )
            .background(
                if (isSelected) priorityColor.copy(alpha = 0.1f) else Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (isSelected) priorityColor else Color(0xFFE2E8F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = priorityText,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            color = if (isSelected) priorityColor else Color(0xFF666666)
        )
    }
}

// 附件项组件
@Composable
private fun AttachmentItem(
    fileName: String,
    onDelete: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFF8FAFC))
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Description,
            contentDescription = null,
            tint = DustyLavender,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = fileName,
            fontSize = 14.sp,
            color = Color(0xFF333333),
            modifier = Modifier.weight(1f)
        )
        
        IconButton(
            onClick = onDelete,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "删除",
                tint = Color(0xFFAAAAAA),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// 目标类型选项
@Composable
private fun GoalTypeOption(
    type: GoalType,
    selectedType: GoalType,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isSelected = type == selectedType
    
    val typeText = when (type) {
        GoalType.NUMERIC -> "数值型"
        GoalType.BOOLEAN -> "布尔型"
    }
    
    val typeIcon = when (type) {
        GoalType.NUMERIC -> Icons.Outlined.Numbers
        GoalType.BOOLEAN -> Icons.Outlined.Check
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onSelect
            )
            .background(
                if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color(0xFFF8FAFC)
            )
            .border(
                width = 1.dp,
                color = if (isSelected) DustyLavender else Color(0xFFE2E8F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = typeIcon,
                contentDescription = null,
                tint = if (isSelected) DustyLavender else Color(0xFF666666),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = typeText,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                color = if (isSelected) DustyLavender else Color(0xFF666666)
            )
        }
    }
} 