package com.timeflow.app.ui.screen.wishlist

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.net.Uri
import androidx.compose.ui.layout.ContentScale
import coil.compose.AsyncImage
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.ui.platform.LocalContext

// 添加愿望对话框主题配色
private val AddWishPrimary = Color(0xFF9c7d8e)
private val AddWishSecondary = Color(0xFFb899a7)
private val AddWishAccent = Color(0xFFFFD93D)
private val AddWishBackground = Color(0xFFFFFBF7)
private val AddWishSurface = Color(0xFFFFFFFF)
private val AddWishText = Color(0xFF2D1B2E)
private val AddWishTextSecondary = Color(0xFF6B4C6D)

/**
 * 🌟 增强版添加愿望对话框 - 小红书风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedAddWishDialog(
    onDismiss: () -> Unit,
    onAddWish: (com.timeflow.app.data.model.WishModel) -> Unit,
    editingWish: com.timeflow.app.data.model.WishModel? = null // 编辑模式的愿望数据
) {
    // 表单状态 - 支持编辑模式预填充
    var title by remember { mutableStateOf(editingWish?.title ?: "") }
    var description by remember { mutableStateOf(editingWish?.description ?: "") }
    var selectedCategory by remember { mutableStateOf(editingWish?.category ?: com.timeflow.app.data.model.WishCategory.OTHER) }
    var priority by remember { mutableStateOf(editingWish?.priority ?: 3) }
    var tags by remember { mutableStateOf(editingWish?.tags ?: listOf<String>()) }
    var inspirationItems by remember { mutableStateOf(editingWish?.inspirationItems ?: listOf<String>()) }
    var newTag by remember { mutableStateOf("") }
    var newInspiration by remember { mutableStateOf("") }
    var selectedImages by remember { 
        mutableStateOf(
            editingWish?.imageUris?.map { Uri.parse(it) } ?: listOf<Uri>()
        ) 
    }
    
    // UI 状态
    var isSubmitting by remember { mutableStateOf(false) }
    var showTagInput by remember { mutableStateOf(false) }
    var showInspirationInput by remember { mutableStateOf(false) }
    var currentStep by remember { mutableStateOf(0) } // 0: 基本信息, 1: 高级设置
    
    val scope = rememberCoroutineScope()
    val keyboardController = LocalSoftwareKeyboardController.current
    val context = LocalContext.current
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { 
            if (selectedImages.size < 6) { // 最多6张图片
                selectedImages = selectedImages + it
            }
        }
    }
    
    // 动画状态
    val slideOffset by animateIntAsState(
        targetValue = if (currentStep == 0) 0 else -1,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "step_slide"
    )

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(28.dp),
            color = AddWishSurface,
            shadowElevation = 24.dp
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 🎨 渐变顶部栏
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    AddWishPrimary.copy(alpha = 0.1f),
                                    AddWishSecondary.copy(alpha = 0.1f)
                                )
                            )
                        )
                        .padding(20.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 返回/关闭按钮
                        IconButton(
                            onClick = { 
                                if (currentStep > 0) {
                                    currentStep = 0
                                } else {
                                    onDismiss()
                                }
                            },
                            modifier = Modifier
                                .size(36.dp)
                                .background(
                                    Color.White.copy(alpha = 0.9f),
                                    RoundedCornerShape(18.dp)
                                )
                        ) {
                            Icon(
                                imageVector = if (currentStep > 0) Icons.Default.ArrowBack else Icons.Default.Close,
                                contentDescription = if (currentStep > 0) "返回" else "关闭",
                                tint = AddWishText,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                        
                        // 标题和步骤指示
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.AutoAwesome,
                                    contentDescription = null,
                                    tint = AddWishPrimary,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = when {
                                        editingWish != null && currentStep == 0 -> "编辑愿望"
                                        editingWish != null && currentStep == 1 -> "编辑设置"
                                        currentStep == 0 -> "添加愿望"
                                        else -> "详细设置"
                                    },
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = AddWishText
                                )
                            }
                            
                            // 步骤指示器
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.padding(top = 4.dp)
                            ) {
                                repeat(2) { index ->
                                    Box(
                                        modifier = Modifier
                                            .size(6.dp)
                                            .background(
                                                if (index <= currentStep) AddWishPrimary else AddWishPrimary.copy(alpha = 0.3f),
                                                RoundedCornerShape(3.dp)
                                            )
                                    )
                                }
                            }
                        }
                        
                        // 下一步/完成按钮
                        if (title.isNotBlank()) {
                            Surface(
                                modifier = Modifier
                                    .clickable {
                                        if (currentStep == 0) {
                                            currentStep = 1
                                        } else {
                                            // 提交愿望
                                            scope.launch {
                                                isSubmitting = true
                                                delay(500) // 模拟提交延迟
                                                
                                                                                val wish = com.timeflow.app.data.model.WishModel(
                                    id = editingWish?.id ?: UUID.randomUUID().toString(),
                                    title = title.trim(),
                                    description = description.trim(),
                                    category = selectedCategory,
                                    priority = priority,
                                    tags = tags,
                                    inspirationItems = inspirationItems,
                                    imageUris = selectedImages.map { it.toString() }, // 保存图片URI
                                    createdAt = java.time.LocalDateTime.now(),
                                                    updatedAt = java.time.LocalDateTime.now(),
                                                    status = com.timeflow.app.data.model.WishStatus.ACTIVE
                                                )
                                                
                                                onAddWish(wish)
                                                isSubmitting = false
                                            }
                                        }
                                    },
                                color = AddWishPrimary,
                                shape = RoundedCornerShape(18.dp)
                            ) {
                                Row(
                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    if (isSubmitting) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(14.dp),
                                            color = Color.White,
                                            strokeWidth = 2.dp
                                        )
                                    } else {
                                        Icon(
                                            imageVector = if (currentStep == 0) Icons.Default.ArrowForward else Icons.Default.Check,
                                            contentDescription = null,
                                            tint = Color.White,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = when {
                                            isSubmitting && editingWish != null -> "保存中..."
                                            isSubmitting -> "添加中..."
                                            currentStep == 0 -> "下一步"
                                            editingWish != null -> "保存"
                                            else -> "完成"
                                        },
                                        color = Color.White,
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.SemiBold
                                    )
                                }
                            }
                        } else {
                            Spacer(modifier = Modifier.width(36.dp))
                        }
                    }
                }
                
                // 📱 内容区域 - 滑动切换
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(480.dp) // 固定高度避免跳动
                ) {
                    // 第一步：基本信息
                    androidx.compose.animation.AnimatedVisibility(
                        visible = currentStep == 0,
                        enter = slideInHorizontally(
                            initialOffsetX = { if (slideOffset > 0) it else -it },
                            animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
                        ) + fadeIn(),
                        exit = slideOutHorizontally(
                            targetOffsetX = { if (slideOffset > 0) -it else it },
                            animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
                        ) + fadeOut()
                    ) {
                        BasicInfoStep(
                            title = title,
                            onTitleChange = { title = it },
                            description = description,
                            onDescriptionChange = { description = it },
                            selectedCategory = selectedCategory,
                            onCategorySelected = { selectedCategory = it }
                        )
                    }
                    
                    // 第二步：高级设置
                    androidx.compose.animation.AnimatedVisibility(
                        visible = currentStep == 1,
                        enter = slideInHorizontally(
                            initialOffsetX = { if (slideOffset < 0) it else -it },
                            animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
                        ) + fadeIn(),
                        exit = slideOutHorizontally(
                            targetOffsetX = { if (slideOffset < 0) -it else it },
                            animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
                        ) + fadeOut()
                    ) {
                        AdvancedSettingsStep(
                            priority = priority,
                            onPriorityChange = { priority = it },
                            tags = tags,
                            onTagsChange = { tags = it },
                            inspirationItems = inspirationItems,
                            onInspirationItemsChange = { inspirationItems = it },
                            selectedImages = selectedImages,
                            onAddImage = { imagePickerLauncher.launch("image/*") },
                            onRemoveImage = { imageToRemove ->
                                selectedImages = selectedImages.filter { it != imageToRemove }
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 📝 基本信息步骤
 */
@Composable
private fun BasicInfoStep(
    title: String,
    onTitleChange: (String) -> Unit,
    description: String,
    onDescriptionChange: (String) -> Unit,
    selectedCategory: com.timeflow.app.data.model.WishCategory,
    onCategorySelected: (com.timeflow.app.data.model.WishCategory) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // 🎯 愿望标题
        Text(
            text = "愿望标题",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        OutlinedTextField(
            value = title,
            onValueChange = onTitleChange,
            placeholder = { Text("输入你的愿望...", color = AddWishTextSecondary.copy(alpha = 0.6f)) },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = AddWishPrimary,
                cursorColor = AddWishPrimary,
                focusedLabelColor = AddWishPrimary
            ),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(20.dp))
        
        // 📝 愿望描述
        Text(
            text = "详细描述",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        OutlinedTextField(
            value = description,
            onValueChange = onDescriptionChange,
            placeholder = { Text("描述一下这个愿望的具体内容...", color = AddWishTextSecondary.copy(alpha = 0.6f)) },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp),
            shape = RoundedCornerShape(16.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = AddWishPrimary,
                cursorColor = AddWishPrimary,
                focusedLabelColor = AddWishPrimary
            ),
            maxLines = 4
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 🏷️ 愿望类别
        Text(
            text = "愿望类别",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        CategorySelector(
            selectedCategory = selectedCategory,
            onCategorySelected = onCategorySelected
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 💡 提示信息
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = AddWishPrimary.copy(alpha = 0.1f),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = AddWishPrimary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "好的愿望描述能帮助你更清晰地规划实现路径",
                    fontSize = 14.sp,
                    color = AddWishTextSecondary,
                    lineHeight = 20.sp
                )
            }
        }
    }
}

/**
 * ⚙️ 高级设置步骤
 */
@Composable
private fun AdvancedSettingsStep(
    priority: Int,
    onPriorityChange: (Int) -> Unit,
    tags: List<String>,
    onTagsChange: (List<String>) -> Unit,
    inspirationItems: List<String>,
    onInspirationItemsChange: (List<String>) -> Unit,
    selectedImages: List<Uri>,
    onAddImage: () -> Unit,
    onRemoveImage: (Uri) -> Unit
) {
    var newTag by remember { mutableStateOf("") }
    var newInspiration by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // ⭐ 优先级设置
        Text(
            text = "重要程度",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        PrioritySelector(
            priority = priority,
            onPriorityChange = onPriorityChange
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 🏷️ 标签管理
        Text(
            text = "标签",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 标签输入
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedTextField(
                value = newTag,
                onValueChange = { newTag = it },
                placeholder = { Text("添加标签...", color = AddWishTextSecondary.copy(alpha = 0.6f)) },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = AddWishPrimary,
                    cursorColor = AddWishPrimary
                ),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(
                onClick = {
                    if (newTag.isNotBlank() && !tags.contains(newTag.trim())) {
                        onTagsChange(tags + newTag.trim())
                        newTag = ""
                    }
                },
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        if (newTag.isNotBlank()) AddWishPrimary else AddWishPrimary.copy(alpha = 0.3f),
                        RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加标签",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        // 标签展示
        if (tags.isNotEmpty()) {
            Spacer(modifier = Modifier.height(12.dp))
            TagsDisplay(
                tags = tags,
                onRemoveTag = { tagToRemove ->
                    onTagsChange(tags.filter { it != tagToRemove })
                }
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 📸 灵感库
        Text(
            text = "灵感库",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = AddWishText,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 灵感输入
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedTextField(
                value = newInspiration,
                onValueChange = { newInspiration = it },
                placeholder = { Text("记录灵感想法...", color = AddWishTextSecondary.copy(alpha = 0.6f)) },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = AddWishPrimary,
                    cursorColor = AddWishPrimary
                ),
                singleLine = true
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(
                onClick = {
                    if (newInspiration.isNotBlank()) {
                        onInspirationItemsChange(inspirationItems + newInspiration.trim())
                        newInspiration = ""
                    }
                },
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        if (newInspiration.isNotBlank()) AddWishAccent else AddWishAccent.copy(alpha = 0.3f),
                        RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.PhotoLibrary,
                    contentDescription = "添加灵感",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
        
        // 灵感展示
        if (inspirationItems.isNotEmpty()) {
            Spacer(modifier = Modifier.height(12.dp))
            InspirationDisplay(
                inspirationItems = inspirationItems,
                onRemoveItem = { itemToRemove ->
                    onInspirationItemsChange(inspirationItems.filter { it != itemToRemove })
                }
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 📸 图片上传区域
        ImageUploadSection(
            selectedImages = selectedImages,
            onAddImage = onAddImage,
            onRemoveImage = onRemoveImage
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 💡 提示信息
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = AddWishAccent.copy(alpha = 0.1f),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TipsAndUpdates,
                    contentDescription = null,
                    tint = AddWishAccent,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "添加图片让愿望更生动，丰富的标签和灵感能让愿望更容易实现",
                    fontSize = 14.sp,
                    color = AddWishTextSecondary,
                    lineHeight = 20.sp
                )
            }
        }
    }
}

/**
 * 🏷️ 类别选择器
 */
@Composable
private fun CategorySelector(
    selectedCategory: com.timeflow.app.data.model.WishCategory,
    onCategorySelected: (com.timeflow.app.data.model.WishCategory) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(com.timeflow.app.data.model.WishCategory.values()) { category ->
            val isSelected = category == selectedCategory
            
            Surface(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .clickable { onCategorySelected(category) },
                color = if (isSelected) 
                    getCategoryColor(category).copy(alpha = 0.2f) 
                else 
                    Color.Gray.copy(alpha = 0.1f),
                shape = RoundedCornerShape(16.dp),
                border = BorderStroke(
                    1.dp,
                    if (isSelected) getCategoryColor(category) else Color.Transparent
                )
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = category.emoji,
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = category.displayName,
                        fontSize = 12.sp,
                        fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                        color = if (isSelected) getCategoryColor(category) else AddWishTextSecondary
                    )
                }
            }
        }
    }
}

/**
 * ⭐ 优先级选择器
 */
@Composable
private fun PrioritySelector(
    priority: Int,
    onPriorityChange: (Int) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "重要程度: ${priority}星",
                fontSize = 14.sp,
                color = AddWishTextSecondary
            )
            
            Row {
                repeat(5) { index ->
                    val starIndex = index + 1
                    Icon(
                        imageVector = if (starIndex <= priority) Icons.Default.Star else Icons.Outlined.StarBorder,
                        contentDescription = null,
                        tint = if (starIndex <= priority) AddWishAccent else AddWishAccent.copy(alpha = 0.3f),
                        modifier = Modifier
                            .size(32.dp)
                            .clickable { onPriorityChange(starIndex) }
                            .padding(2.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = priority.toFloat(),
            onValueChange = { onPriorityChange(it.toInt()) },
            valueRange = 1f..5f,
            steps = 3,
            colors = SliderDefaults.colors(
                thumbColor = AddWishAccent,
                activeTrackColor = AddWishAccent,
                inactiveTrackColor = AddWishAccent.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * 🏷️ 标签展示
 */
@Composable
private fun TagsDisplay(
    tags: List<String>,
    onRemoveTag: (String) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(tags) { tag ->
            Surface(
                color = AddWishPrimary.copy(alpha = 0.15f),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "#$tag",
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium,
                        color = AddWishPrimary
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "移除标签",
                        tint = AddWishPrimary,
                        modifier = Modifier
                            .size(16.dp)
                            .clickable { onRemoveTag(tag) }
                    )
                }
            }
        }
    }
}

/**
 * 📸 灵感展示
 */
@Composable
private fun InspirationDisplay(
    inspirationItems: List<String>,
    onRemoveItem: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        inspirationItems.forEach { item ->
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = AddWishAccent.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        modifier = Modifier.weight(1f),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.PhotoLibrary,
                            contentDescription = null,
                            tint = AddWishAccent,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = item,
                            fontSize = 14.sp,
                            color = AddWishText
                        )
                    }
                    
                    IconButton(
                        onClick = { onRemoveItem(item) },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "移除灵感",
                            tint = AddWishTextSecondary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 📸 图片上传区域
 */
@Composable
private fun ImageUploadSection(
    selectedImages: List<Uri>,
    onAddImage: () -> Unit,
    onRemoveImage: (Uri) -> Unit
) {
    Column {
        // 标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.PhotoLibrary,
                    contentDescription = null,
                    tint = AddWishPrimary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "愿望图片",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = AddWishText
                )
                if (selectedImages.isNotEmpty()) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Surface(
                        color = AddWishPrimary.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = "${selectedImages.size}/6",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = AddWishPrimary,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
            }
            
            // 添加图片按钮
            if (selectedImages.size < 6) {
                Surface(
                    modifier = Modifier
                        .size(36.dp)
                        .clickable { onAddImage() },
                    color = AddWishPrimary.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(18.dp),
                    border = BorderStroke(1.dp, AddWishPrimary.copy(alpha = 0.3f))
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加图片",
                            tint = AddWishPrimary,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 图片展示区域
        if (selectedImages.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(selectedImages.size) { index ->
                    val imageUri = selectedImages[index]
                    ImagePreviewCard(
                        imageUri = imageUri,
                        onRemove = { onRemoveImage(imageUri) }
                    )
                }
            }
        } else {
            // 空状态 - 上传提示
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp)
                    .clickable { onAddImage() },
                color = AddWishPrimary.copy(alpha = 0.05f),
                shape = RoundedCornerShape(16.dp),
                border = BorderStroke(
                    1.dp, 
                    AddWishPrimary.copy(alpha = 0.2f)
                )
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Outlined.AddPhotoAlternate,
                        contentDescription = "添加图片",
                        tint = AddWishPrimary.copy(alpha = 0.7f),
                        modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "点击添加愿望图片",
                        fontSize = 14.sp,
                        color = AddWishTextSecondary,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "最多可添加6张图片",
                        fontSize = 12.sp,
                        color = AddWishTextSecondary.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 🖼️ 图片预览卡片
 */
@Composable
private fun ImagePreviewCard(
    imageUri: Uri,
    onRemove: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(80.dp)
            .clip(RoundedCornerShape(12.dp))
    ) {
        // 图片
        AsyncImage(
            model = imageUri,
            contentDescription = "愿望图片",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        // 删除按钮
        Surface(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(4.dp)
                .size(20.dp)
                .clickable { onRemove() },
            color = Color.Black.copy(alpha = 0.6f),
            shape = CircleShape
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除图片",
                    tint = Color.White,
                    modifier = Modifier.size(12.dp)
                )
            }
        }
    }
}

// 🎨 辅助函数
private fun getCategoryColor(category: com.timeflow.app.data.model.WishCategory): Color {
    return when (category) {
        com.timeflow.app.data.model.WishCategory.TRAVEL -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.SHOPPING -> Color(0xFFFF9800)
        com.timeflow.app.data.model.WishCategory.LEARNING -> Color(0xFF2196F3)
        com.timeflow.app.data.model.WishCategory.CAREER -> Color(0xFF9C27B0)
        com.timeflow.app.data.model.WishCategory.LIFESTYLE -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.HEALTH -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.HOBBY -> Color(0xFFFF5722)
        com.timeflow.app.data.model.WishCategory.RELATIONSHIP -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.OTHER -> Color(0xFF607D8B)
    }
} 