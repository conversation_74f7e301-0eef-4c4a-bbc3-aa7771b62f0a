package com.timeflow.app.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.ui.screen.goal.AddGoalScreen
import com.timeflow.app.ui.screen.goal.EditGoalScreen
import com.timeflow.app.ui.screen.goal.GoalDetailScreen
import com.timeflow.app.ui.screen.goal.GoalManagementScreen as GoalManagementScreenImpl
import com.timeflow.app.ui.screen.health.HabitTrackerScreen
import com.timeflow.app.ui.screen.health.HabitDetailScreen
import com.timeflow.app.ui.screen.health.MenstrualCycleScreen
// 🔧 删除：MenstrualCycleStatsScreen 已被融合到 PeriodAnalyticsScreen
import com.timeflow.app.ui.screen.health.SymptomsDetailScreen
import com.timeflow.app.ui.screen.settings.AiModelSettingsScreen
import com.timeflow.app.ui.screen.settings.AiSettingsScreen
import com.timeflow.app.ui.screen.goal.GoalBreakdownScreen
import com.timeflow.app.ui.screen.goal.GoalReviewScreen
import com.timeflow.app.ui.screen.milestone.MilestoneScreen
import com.timeflow.app.ui.screen.goal.GoalTemplateListScreen
import com.timeflow.app.ui.screen.goal.GoalTemplateEditScreen
import com.timeflow.app.ui.screen.goal.GoalTemplateImportScreen
import com.timeflow.app.ui.screen.goal.SaveGoalAsTemplateScreen
import com.timeflow.app.ui.screen.reflection.ReflectionScreen
import com.timeflow.app.ui.screen.reflection.ReflectionDetailScreen
import com.timeflow.app.ui.screen.health.AddHabitScreen

/**
 * 屏幕包装器，解决导航控制器和ViewModel传递问题
 */
object ScreenWrappers {
    
    // 🔧 删除：MenstrualCycleStatsScreenWrapper 已不再需要
    
    @Composable
    fun HabitTrackerScreenWrapper(navController: NavController) {
        val viewModel: HabitViewModel = hiltViewModel()
        
        // 监听导航状态，当返回到此页面时刷新数据
        DisposableEffect(navController) {
            val listener = NavController.OnDestinationChangedListener { _, destination, _ ->
                if (destination.route == AppDestinations.HABIT_TRACKER_ROUTE) {
                    viewModel.refreshHabits()
                }
            }
            navController.addOnDestinationChangedListener(listener)
            
            onDispose {
                navController.removeOnDestinationChangedListener(listener)
            }
        }
        
        HabitTrackerScreen(
            onBackClick = { navController.popBackStack() },
            onHabitDetailClick = { habitId -> 
                navController.navigate(AppDestinations.habitDetailRoute(habitId))
            },
            navController = navController, // 传递navController
            viewModel = viewModel
        )
    }
    
    @Composable
    fun HabitDetailScreenWrapper(navController: NavController, habitId: String) {
        android.util.Log.d("ScreenWrappers", "HabitDetailScreenWrapper被调用: habitId=$habitId")
        com.timeflow.app.ui.screen.health.HabitDetailScreenOptimized(
            habitId = habitId,
            navController = navController
        )
    }
    
    @Composable
    fun AddHabitScreenWrapper(navController: NavController) {
        AddHabitScreen(
            onNavigateBack = { navController.popBackStack() },
            viewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun SymptomsDetailScreenWrapper(navController: NavController) {
        SymptomsDetailScreen(
            viewModel = hiltViewModel(),
            onNavigateBack = { navController.popBackStack() }
        )
    }
    
    @Composable
    fun MenstrualCycleScreenWrapper(navController: NavController) {
        MenstrualCycleScreen(
            onBackClick = { navController.popBackStack() },
            viewModel = hiltViewModel(),
            navController = navController
        )
    }
    
    @Composable
    fun GoalManagementScreenWrapper(navController: NavController) {
        GoalManagementScreenImpl(
            navController = navController,
            viewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun GoalDetailScreenWrapper(navController: NavController, goalId: String) {
        GoalDetailScreen(
            navController = navController,
            goalId = goalId
        )
    }
    
    @Composable
    fun AddGoalScreenWrapper(navController: NavController) {
        AddGoalScreen(
            navController = navController,
            viewModel = hiltViewModel(),
            templateViewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun EditGoalScreenWrapper(navController: NavController, goalId: String) {
        EditGoalScreen(
            navController = navController,
            goalId = goalId
        )
    }
    
    @Composable
    fun GoalBreakdownScreenWrapper(navController: NavController, goalId: String) {
        GoalBreakdownScreen(
            navController = navController,
            goalId = goalId
        )
    }
    
    @Composable
    fun GoalReviewScreenWrapper(navController: NavController, goalId: String) {
        GoalReviewScreen(
            navController = navController,
            goalId = goalId
        )
    }
    
    @Composable
    fun AiSettingsScreenWrapper(navController: NavController) {
        AiSettingsScreen(
            navController = navController,
            onBackClick = { navController.popBackStack() },
            aiSettingsViewModel = hiltViewModel(),
            aiConfigViewModel = hiltViewModel(),
            calendarViewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun AiModelSettingsScreenWrapper(navController: NavController) {
        AiModelSettingsScreen(
            navController = navController,
            onBackClick = { navController.popBackStack() },
            viewModel = hiltViewModel(),
            calendarViewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun AiModelSettingsScreenWrapper(navController: NavController, configId: String?) {
        AiModelSettingsScreen(
            navController = navController,
            onBackClick = { navController.popBackStack() },
            viewModel = hiltViewModel(),
            calendarViewModel = hiltViewModel(),
            configId = configId
        )
    }
    
    @Composable
    fun MilestoneScreenWrapper(navController: NavController) {
        MilestoneScreen(
            navController = navController
        )
    }
    
    @Composable
    fun GoalTemplateListScreenWrapper(navController: NavController) {
        GoalTemplateListScreen(
            navController = navController
        )
    }
    
    @Composable
    fun GoalTemplateEditScreenWrapper(navController: NavController, templateId: String?) {
        GoalTemplateEditScreen(
            navController = navController,
            templateId = templateId
        )
    }
    
    @Composable
    fun GoalTemplateImportScreenWrapper(navController: NavController) {
        GoalTemplateImportScreen(
            navController = navController
        )
    }
    
    @Composable
    fun SaveGoalAsTemplateScreenWrapper(navController: NavController, goalId: String) {
        SaveGoalAsTemplateScreen(
            navController = navController,
            goalId = goalId
        )
    }
    
    @Composable
    fun ReflectionScreenWrapper(navController: NavController) {
        ReflectionScreen(
            navController = navController,
            viewModel = hiltViewModel()
        )
    }
    
    @Composable
    fun ReflectionDetailScreenWrapper(navController: NavController, reflectionId: String) {
        ReflectionDetailScreen(
            reflectionId = reflectionId,
            navController = navController,
            viewModel = hiltViewModel()
        )
    }
} 