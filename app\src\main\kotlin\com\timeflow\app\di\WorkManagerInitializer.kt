package com.timeflow.app.di

import android.content.Context
import androidx.hilt.work.HiltWorkerFactory
import androidx.startup.Initializer
import androidx.work.Configuration
import androidx.work.WorkManager
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject
import javax.inject.Singleton

/**
 * WorkManager自定义初始化器
 * 用于结合Hilt使用WorkManager
 */
class WorkManagerInitializer : Initializer<WorkManager> {
    
    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface WorkManagerInitializerEntryPoint {
        fun workerFactory(): HiltWorkerFactory
    }
    
    override fun create(context: Context): WorkManager {
        val appContext = context.applicationContext
        val entryPoint = EntryPointAccessors.fromApplication(
            appContext,
            WorkManagerInitializerEntryPoint::class.java
        )
        
        val configuration = Configuration.Builder()
            .setWorkerFactory(entryPoint.workerFactory())
            .build()
            
        WorkManager.initialize(appContext, configuration)
        return WorkManager.getInstance(appContext)
    }
    
    override fun dependencies(): List<Class<out Initializer<*>>> {
        // No dependencies on other libraries
        return emptyList()
    }
}

@Module
@InstallIn(SingletonComponent::class)
object WorkManagerModule {
    
    @Provides
    @Singleton
    fun provideWorkManager(@ApplicationContext context: Context): WorkManager {
        return WorkManager.getInstance(context)
    }
} 