package com.timeflow.app.ui.viewmodel

import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.ui.timetracking.TimerState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import androidx.compose.ui.graphics.Color
import android.util.Log

/**
 * 全局计时器ViewModel，管理整个应用中的计时状态
 * 🔧 修复：作为TimeTrackingViewModel的状态观察者，不进行独立操作
 */
@HiltViewModel
class GlobalTimerViewModel @Inject constructor(
    private val sharedPreferences: SharedPreferences
) : ViewModel() {
    
    companion object {
        private const val TAG = "GlobalTimerViewModel"
        // 使用与TimeTrackingViewModel相同的SharedPreferences键
        private const val PREF_TIMER_STATE = "timer_state"
        private const val PREF_TIMER_START_TIME = "timer_start_time"
        private const val PREF_ELAPSED_TIME = "elapsed_time"
        private const val PREF_CURRENT_TASK_ID = "current_task_id"
        // 🔧 新增：小组件隐藏状态的持久化键
        private const val PREF_MINI_TIMER_HIDDEN = "mini_timer_hidden"
    }
    
    // 计时器状态 - 只读，从持久化存储同步
    private val _timerState = MutableStateFlow(TimerState.IDLE)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    // 计时时间（秒）- 只读，从持久化存储同步
    private val _elapsedTime = MutableStateFlow(0L)
    val elapsedTime: StateFlow<Long> = _elapsedTime.asStateFlow()
    
    // 是否显示迷你计时器
    private val _showMiniTimer = MutableStateFlow(false)
    val showMiniTimer: StateFlow<Boolean> = _showMiniTimer.asStateFlow()
    
    // 🔧 新增：用户是否手动隐藏了小组件
    private val _isMiniTimerHidden = MutableStateFlow(false)
    val isMiniTimerHidden: StateFlow<Boolean> = _isMiniTimerHidden.asStateFlow()
    
    // 当前任务信息
    private val _currentTaskName = MutableStateFlow("测试任务")
    val currentTaskName: StateFlow<String> = _currentTaskName.asStateFlow()
    
    // 同步作业 - 定期从持久化存储同步状态
    private var syncJob: Job? = null
    
    // 计时开始时间戳（用于实时计算）
    private var timerStartTime: Long = 0L
    
    init {
        Log.d(TAG, "🔧 GlobalTimerViewModel 初始化")
        // 🔧 初始化时立即同步状态
        syncFromPersistentStorage()
        
        // 🔧 启动定期同步任务
        startPeriodicSync()
    }
    
    /**
     * 🔧 核心方法：从持久化存储同步计时状态
     * 作为TimeTrackingViewModel的状态观察者，只读取不修改
     */
    private fun syncFromPersistentStorage() {
        try {
            val savedState = sharedPreferences.getString(PREF_TIMER_STATE, TimerState.IDLE.name)
            val savedStartTime = sharedPreferences.getLong(PREF_TIMER_START_TIME, 0L)
            val savedElapsedTime = sharedPreferences.getLong(PREF_ELAPSED_TIME, 0L)
            // 🔧 加载小组件隐藏状态
            val isMiniTimerHidden = sharedPreferences.getBoolean(PREF_MINI_TIMER_HIDDEN, false)
            
            Log.d(TAG, "🔄 同步状态: state=$savedState, startTime=$savedStartTime, elapsed=$savedElapsedTime, hidden=$isMiniTimerHidden")
            
            // 🔧 更新隐藏状态
            _isMiniTimerHidden.value = isMiniTimerHidden
            
            when (savedState) {
                TimerState.RUNNING.name -> {
                    if (savedStartTime > 0) {
                        // 🔧 计算实时经过时间
                        val currentTime = System.currentTimeMillis()
                        val calculatedElapsedTime = (currentTime - savedStartTime) / 1000
                        
                        _timerState.value = TimerState.RUNNING
                        _elapsedTime.value = calculatedElapsedTime
                        // 🔧 只有在没有被用户隐藏时才显示小组件
                        _showMiniTimer.value = !isMiniTimerHidden
                        timerStartTime = savedStartTime
                        
                        Log.d(TAG, "✅ 同步运行状态: ${calculatedElapsedTime}秒, 显示状态: ${!isMiniTimerHidden}")
                    } else {
                        Log.w(TAG, "⚠️ 运行状态但无开始时间，重置为空闲")
                        resetToIdle()
                    }
                }
                TimerState.PAUSED.name -> {
                    _timerState.value = TimerState.PAUSED
                    _elapsedTime.value = savedElapsedTime
                    // 🔧 只有在没有被用户隐藏时才显示小组件
                    _showMiniTimer.value = !isMiniTimerHidden
                    timerStartTime = 0L
                    
                    Log.d(TAG, "✅ 同步暂停状态: ${savedElapsedTime}秒, 显示状态: ${!isMiniTimerHidden}")
                }
                else -> {
                    resetToIdle()
                    Log.d(TAG, "✅ 同步空闲状态")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步状态失败", e)
            resetToIdle()
        }
    }
    
    /**
     * 重置为空闲状态
     */
    private fun resetToIdle() {
        _timerState.value = TimerState.IDLE
        _elapsedTime.value = 0L
        _showMiniTimer.value = false
        timerStartTime = 0L
    }
    
    /**
     * 🔧 启动定期同步任务
     * 每秒从持久化存储同步状态，确保与主计时器保持一致
     */
    private fun startPeriodicSync() {
        syncJob?.cancel()
        syncJob = viewModelScope.launch {
            while (true) {
                delay(1000) // 每秒同步一次
                
                try {
                    // 🔧 如果是运行状态，实时计算时间
                    if (_timerState.value == TimerState.RUNNING && timerStartTime > 0) {
                        val currentTime = System.currentTimeMillis()
                        val calculatedElapsedTime = (currentTime - timerStartTime) / 1000
                        _elapsedTime.value = calculatedElapsedTime
                    }
                    
                    // 🔧 每5秒重新同步状态（防止状态漂移）
                    if (_elapsedTime.value % 5 == 0L) {
                        syncFromPersistentStorage()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "定期同步异常", e)
                }
            }
        }
    }
    
    /**
     * 🔧 重要：所有操作方法改为通知模式
     * 不直接修改SharedPreferences，而是通过状态同步机制响应变化
     */
    
    /**
     * 开始计时 - 通过TimeTrackingViewModel处理
     * 这里只是触发状态同步，不进行实际操作
     */
    fun startTimer() {
        Log.d(TAG, "🎯 收到开始计时请求 - 委托给TimeTrackingViewModel处理")
        // 🔧 立即同步状态，确保显示最新状态
        syncFromPersistentStorage()
    }
    
    /**
     * 暂停计时 - 通过TimeTrackingViewModel处理
     */
    fun pauseTimer() {
        Log.d(TAG, "⏸️ 收到暂停计时请求 - 委托给TimeTrackingViewModel处理")
        syncFromPersistentStorage()
    }
    
    /**
     * 停止计时 - 通过TimeTrackingViewModel处理
     */
    fun stopTimer() {
        Log.d(TAG, "⏹️ 收到停止计时请求 - 委托给TimeTrackingViewModel处理")
        syncFromPersistentStorage()
    }
    
    /**
     * 设置当前任务 - 仅更新显示信息
     */
    fun setCurrentTask(taskName: String) {
        _currentTaskName.value = taskName
        Log.d(TAG, "📝 设置当前任务: $taskName")
    }
    
    /**
     * 🔧 手动刷新状态
     * 供外部调用，立即同步最新状态
     */
    fun refreshState() {
        Log.d(TAG, "🔄 手动刷新状态")
        syncFromPersistentStorage()
    }
    
    /**
     * 🔧 新增：隐藏小组件
     * 用户点击X按钮时调用
     */
    fun hideMiniTimer() {
        Log.d(TAG, "🙈 用户隐藏小组件")
        _isMiniTimerHidden.value = true
        _showMiniTimer.value = false
        
        // 保存隐藏状态到持久化存储
        try {
            sharedPreferences.edit()
                .putBoolean(PREF_MINI_TIMER_HIDDEN, true)
                .apply()
            Log.d(TAG, "✅ 小组件隐藏状态已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存隐藏状态失败", e)
        }
    }
    
    /**
     * 🔧 新增：显示小组件
     * 离开时间追踪页面时调用，重新显示小组件（如果有活跃计时）
     */
    fun showMiniTimer() {
        Log.d(TAG, "👁️ 重新显示小组件")
        _isMiniTimerHidden.value = false
        
        // 🔧 只有在有活跃计时时才显示
        if (hasActiveSession()) {
            _showMiniTimer.value = true
            Log.d(TAG, "✅ 有活跃计时，显示小组件")
        } else {
            _showMiniTimer.value = false
            Log.d(TAG, "❌ 无活跃计时，保持隐藏")
        }
        
        // 清除隐藏状态
        try {
            sharedPreferences.edit()
                .putBoolean(PREF_MINI_TIMER_HIDDEN, false)
                .apply()
            Log.d(TAG, "✅ 小组件显示状态已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存显示状态失败", e)
        }
    }
    
    /**
     * 获取格式化的时间字符串
     */
    fun getFormattedTime(): String {
        val totalSeconds = _elapsedTime.value
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return if (hours > 0) {
            String.format("%d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }
    
    /**
     * 检查是否正在计时
     */
    fun isTimerRunning(): Boolean = _timerState.value == TimerState.RUNNING
    
    /**
     * 检查是否暂停
     */
    fun isTimerPaused(): Boolean = _timerState.value == TimerState.PAUSED
    
    /**
     * 检查是否有活动的计时会话
     */
    fun hasActiveSession(): Boolean = _timerState.value != TimerState.IDLE
    
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "🧹 GlobalTimerViewModel清理")
        syncJob?.cancel()
    }
} 