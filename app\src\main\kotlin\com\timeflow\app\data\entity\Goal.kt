package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.DateTimeConverter
import com.timeflow.app.data.converter.StringListConverter
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.time.LocalDateTime

@Entity(tableName = "goals")
@TypeConverters(DateTimeConverter::class, StringListConverter::class)
@Serializable
data class Goal(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String = "",
    @Contextual
    val startDate: LocalDateTime = LocalDateTime.now(),
    @Contextual
    val dueDate: LocalDateTime? = null,
    @Contextual
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Contextual
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    @Contextual
    val completedAt: LocalDateTime? = null,
    val progress: Float = 0f,
    val priority: String = "MEDIUM",
    val hasAiBreakdown: Boolean = false,
    val hasAiAnalysis: Boolean = false,
    val relatedTaskIds: String = "", // Use converter for List<String>
    val aiRecommendationsJson: String = "[]", // JSON string for List<String>
    val tags: String = "", // Use converter for List<String>
    val status: String = "进行中",
    val bestTimeSlotsJson: String = "[]", // JSON string for List<TimeSlotInfo>
    val metricsJson: String = "{}", // JSON string for Map<String, Float>
    val reviewFrequency: String = "WEEKLY",
    val categoryId: String = "personal_development" // 目标分类ID，默认为个人提升
)

@Entity(tableName = "goal_subtasks")
@TypeConverters(DateTimeConverter::class)
@Serializable
data class GoalSubTask(
    @PrimaryKey
    val id: String,
    val goalId: String,
    val title: String,
    val description: String = "",
    val estimatedDurationDays: Int = 0,
    @Contextual
    val completedAt: LocalDateTime? = null,
    @Contextual
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val aiRecommendation: String? = null,
    val status: String = "待开始"
) 