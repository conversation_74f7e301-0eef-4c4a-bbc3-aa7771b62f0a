package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.EmotionRecordDao
import com.timeflow.app.data.entity.EmotionRecordEntity
import com.timeflow.app.ui.screen.profile.EmotionRecord
import com.timeflow.app.ui.screen.profile.EmotionType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 情绪记录仓库接口
 */
interface EmotionRecordRepository {
    suspend fun saveEmotionRecord(record: EmotionRecord): String
    suspend fun updateEmotionRecord(record: EmotionRecord)
    suspend fun deleteEmotionRecord(id: String)
    suspend fun getEmotionRecordById(id: String): EmotionRecord?
    suspend fun getEmotionRecordByDate(date: LocalDate): EmotionRecord?
    fun getAllEmotionRecords(): Flow<List<EmotionRecord>>
    fun getEmotionRecordsByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<EmotionRecord>>
    fun getEmotionRecordsByType(emotionType: EmotionType): Flow<List<EmotionRecord>>
    fun getDetailedEmotionRecords(): Flow<List<EmotionRecord>>
    fun getRecentEmotionRecords(limit: Int): Flow<List<EmotionRecord>>
    suspend fun getEmotionRecordCount(): Int
    suspend fun migrateExistingData(existingRecords: List<EmotionRecord>)
}

/**
 * 情绪记录仓库实现
 */
@Singleton
class EmotionRecordRepositoryImpl @Inject constructor(
    private val emotionRecordDao: EmotionRecordDao
) : EmotionRecordRepository {
    
    override suspend fun saveEmotionRecord(record: EmotionRecord): String {
        val id = UUID.randomUUID().toString()
        val entity = record.toEntity(id)
        emotionRecordDao.insertEmotionRecord(entity)
        return id
    }
    
    override suspend fun updateEmotionRecord(record: EmotionRecord) {
        // 如果记录没有ID，先查找是否存在同日期的记录
        val existingRecord = emotionRecordDao.getEmotionRecordByDate(record.date)
        val id = existingRecord?.id ?: UUID.randomUUID().toString()
        val entity = record.toEntity(id)
        emotionRecordDao.insertEmotionRecord(entity)
    }
    
    override suspend fun deleteEmotionRecord(id: String) {
        emotionRecordDao.deleteEmotionRecordById(id)
    }
    
    override suspend fun getEmotionRecordById(id: String): EmotionRecord? {
        return emotionRecordDao.getEmotionRecordById(id)?.toEmotionRecord()
    }
    
    override suspend fun getEmotionRecordByDate(date: LocalDate): EmotionRecord? {
        return emotionRecordDao.getEmotionRecordByDate(date)?.toEmotionRecord()
    }
    
    override fun getAllEmotionRecords(): Flow<List<EmotionRecord>> {
        android.util.Log.d("EmotionRecordRepository", "🔍 开始获取所有情绪记录...")
        return emotionRecordDao.getAllEmotionRecords().map { entities ->
            android.util.Log.d("EmotionRecordRepository", "📊 从数据库获取到 ${entities.size} 条原始记录")
            val records = entities.map { it.toEmotionRecord() }
            android.util.Log.d("EmotionRecordRepository", "✅ 转换完成，返回 ${records.size} 条情绪记录")
            records
        }
    }
    
    override fun getEmotionRecordsByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<EmotionRecord>> {
        return emotionRecordDao.getEmotionRecordsByDateRange(startDate, endDate).map { entities ->
            entities.map { it.toEmotionRecord() }
        }
    }
    
    override fun getEmotionRecordsByType(emotionType: EmotionType): Flow<List<EmotionRecord>> {
        return emotionRecordDao.getEmotionRecordsByType(emotionType.name).map { entities ->
            entities.map { it.toEmotionRecord() }
        }
    }
    
    override fun getDetailedEmotionRecords(): Flow<List<EmotionRecord>> {
        return emotionRecordDao.getDetailedEmotionRecords().map { entities ->
            entities.map { it.toEmotionRecord() }
        }
    }
    
    override fun getRecentEmotionRecords(limit: Int): Flow<List<EmotionRecord>> {
        return emotionRecordDao.getRecentEmotionRecords(limit).map { entities ->
            entities.map { it.toEmotionRecord() }
        }
    }
    
    override suspend fun getEmotionRecordCount(): Int {
        return emotionRecordDao.getEmotionRecordCount()
    }
    
    /**
     * 🔧 安全的数据迁移方法 - 保护现有数据
     */
    override suspend fun migrateExistingData(existingRecords: List<EmotionRecord>) {
        if (existingRecords.isEmpty()) return
        
        try {
            // 检查数据库中是否已有数据
            val existingCount = emotionRecordDao.getEmotionRecordCount()
            
            // 只有在数据库为空时才进行迁移，避免重复数据
            if (existingCount == 0) {
                val entities = existingRecords.map { record ->
                    record.toEntity(UUID.randomUUID().toString())
                }
                emotionRecordDao.insertEmotionRecords(entities)
                android.util.Log.d("EmotionRecordRepository", "✅ 成功迁移 ${entities.size} 条情绪记录")
            } else {
                android.util.Log.d("EmotionRecordRepository", "数据库已有 $existingCount 条记录，跳过迁移")
            }
        } catch (e: Exception) {
            android.util.Log.e("EmotionRecordRepository", "数据迁移失败", e)
        }
    }
}

/**
 * 扩展函数：EmotionRecord 转 EmotionRecordEntity
 */
private fun EmotionRecord.toEntity(id: String): EmotionRecordEntity {
    return EmotionRecordEntity(
        id = id,
        date = this.date,
        emotionType = this.emotion.name,
        triggers = this.triggers,
        mindfulnessNote = this.mindfulnessNote,
        imageUri = this.imageUri,
        audioUri = this.audioUri,
        isDetailed = this.isDetailed,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}

/**
 * 扩展函数：EmotionRecordEntity 转 EmotionRecord
 */
private fun EmotionRecordEntity.toEmotionRecord(): EmotionRecord {
    return EmotionRecord(
        date = this.date,
        emotion = EmotionType.valueOf(this.emotionType),
        triggers = this.triggers,
        mindfulnessNote = this.mindfulnessNote,
        imageUri = this.imageUri,
        audioUri = this.audioUri,
        isDetailed = this.isDetailed
    )
}
