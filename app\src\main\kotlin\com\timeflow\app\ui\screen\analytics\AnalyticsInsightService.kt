package com.timeflow.app.ui.screen.analytics

import android.util.Log
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.model.TimeSession
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据分析洞察服务
 * 基于真实数据生成智能洞察和建议
 */
@Singleton
class AnalyticsInsightService @Inject constructor(
    private val taskRepository: TaskRepository,
    private val timeSessionRepository: TimeSessionRepository,
    private val goalRepository: GoalRepository,
    private val habitRepository: HabitRepository
) {
    
    companion object {
        private const val TAG = "AnalyticsInsightService"
    }
    
    /**
     * 生成今日洞察
     */
    suspend fun generateDailyInsights(timeRange: AnalyticsViewModel.TimeRange): List<String> {
        return try {
            val insights = mutableListOf<String>()
            val today = LocalDate.now()
            
            // 获取今日数据
            val todaySessions = timeSessionRepository.getSessionsByDate(today).first()
            val todayTasks = taskRepository.getAllTasks().filter { task ->
                task.completedAt?.toLocalDate() == today
            }
            
            // 生成专注时段洞察
            if (todaySessions.isNotEmpty()) {
                val bestHour = findBestFocusHour(todaySessions)
                if (bestHour != null) {
                    insights.add("在${bestHour}:00-${bestHour + 1}:00期间专注度最高，建议安排重要任务")
                }
            }
            
            // 生成任务完成洞察
            if (todayTasks.isNotEmpty()) {
                val completionRate = todayTasks.count { it.isCompleted }.toFloat() / todayTasks.size * 100
                when {
                    completionRate >= 80 -> insights.add("今日任务完成率${String.format("%.1f", completionRate)}%，表现优秀！")
                    completionRate >= 60 -> insights.add("今日任务完成率${String.format("%.1f", completionRate)}%，还有提升空间")
                    else -> insights.add("今日任务完成率${String.format("%.1f", completionRate)}%，建议重新评估任务安排")
                }
            }
            
            // 生成时间分布洞察
            if (todaySessions.isNotEmpty()) {
                val totalFocusTime = todaySessions.sumOf { it.duration } / 60 // 转换为分钟
                val avgSessionLength = todaySessions.map { it.duration }.average() / 60
                
                if (totalFocusTime > 0) {
                    insights.add("今日专注时长${totalFocusTime}分钟，平均每次专注${String.format("%.1f", avgSessionLength)}分钟")
                }
            }
            
            // 如果没有数据，提供鼓励性信息
            if (insights.isEmpty()) {
                insights.add("开始记录你的专注时间，获得更多个性化洞察")
                insights.add("完成第一个任务，开启高效的一天")
            }
            
            insights.take(3) // 最多返回3个洞察
        } catch (e: Exception) {
            Log.e(TAG, "生成每日洞察失败", e)
            listOf("数据分析中，请稍后查看洞察")
        }
    }
    
    /**
     * 生成周度洞察
     */
    suspend fun generateWeeklyInsights(): List<String> {
        return try {
            val insights = mutableListOf<String>()
            val today = LocalDate.now()
            val weekStart = today.minusDays(today.dayOfWeek.value - 1L)
            val weekEnd = weekStart.plusDays(6)
            
            // 获取本周数据
            val weekSessions = mutableListOf<TimeSession>()
            for (date in weekStart..weekEnd) {
                weekSessions.addAll(timeSessionRepository.getSessionsByDate(date).first())
            }

            if (weekSessions.isNotEmpty()) {
                // 分析每日专注时长
                val dailyFocusTime = weekSessions.groupBy { session ->
                    session.startTime.atZone(ZoneId.systemDefault()).toLocalDate()
                }.mapValues { (_, sessions) ->
                    sessions.sumOf { it.duration } / 60 // 转换为分钟
                }
                
                val bestDay = dailyFocusTime.maxByOrNull { it.value }
                val worstDay = dailyFocusTime.minByOrNull { it.value }
                
                if (bestDay != null && worstDay != null) {
                    val bestDayName = bestDay.key.dayOfWeek.getDisplayName(
                        java.time.format.TextStyle.FULL, java.util.Locale.CHINESE
                    )
                    val worstDayName = worstDay.key.dayOfWeek.getDisplayName(
                        java.time.format.TextStyle.FULL, java.util.Locale.CHINESE
                    )
                    
                    insights.add("${bestDayName}是本周最高效的一天，专注时长${bestDay.value}分钟")
                    if (worstDay.value < bestDay.value * 0.5) {
                        insights.add("${worstDayName}专注时间较少，可以考虑调整日程安排")
                    }
                }
                
                // 分析专注质量
                val avgSessionLength = weekSessions.map { it.duration }.average() / 60
                when {
                    avgSessionLength >= 45 -> insights.add("本周平均专注时长${String.format("%.1f", avgSessionLength)}分钟，专注质量很高")
                    avgSessionLength >= 25 -> insights.add("本周平均专注时长${String.format("%.1f", avgSessionLength)}分钟，可以尝试延长专注时间")
                    else -> insights.add("本周专注时间较短，建议使用番茄工作法提高专注度")
                }
            }
            
            if (insights.isEmpty()) {
                insights.add("本周开始记录专注时间，下周将有更多洞察")
            }
            
            insights.take(3)
        } catch (e: Exception) {
            Log.e(TAG, "生成周度洞察失败", e)
            listOf("本周数据分析中，请稍后查看")
        }
    }
    
    /**
     * 生成行动建议
     */
    suspend fun generateActionSuggestions(): List<String> {
        return try {
            val suggestions = mutableListOf<String>()
            val today = LocalDate.now()
            val weekStart = today.minusDays(today.dayOfWeek.value - 1L)
            
            // 分析最近一周的数据
            val recentSessions = mutableListOf<TimeSession>()
            for (i in 0..6) {
                val date = weekStart.plusDays(i.toLong())
                recentSessions.addAll(timeSessionRepository.getSessionsByDate(date).first())
            }
            
            if (recentSessions.isNotEmpty()) {
                // 分析专注时间分布
                val avgDailyFocusTime = recentSessions.sumOf { it.duration } / 7 / 60 // 平均每日专注分钟数
                val avgSessionLength = recentSessions.map { it.duration }.average() / 60
                
                when {
                    avgDailyFocusTime < 60 -> suggestions.add("建议每天至少专注1小时，可以分成2-3个专注时段")
                    avgDailyFocusTime < 120 -> suggestions.add("专注时间不错，可以尝试增加到每天2小时")
                    else -> suggestions.add("专注时间充足，保持当前节奏")
                }
                
                if (avgSessionLength < 20) {
                    suggestions.add("专注时段较短，建议使用25分钟番茄工作法")
                } else if (avgSessionLength > 60) {
                    suggestions.add("专注时段较长，记得适当休息避免疲劳")
                }
                
                // 分析中断情况
                val shortSessions = recentSessions.filter { it.duration < 300 } // 5分钟以下
                if (shortSessions.size > recentSessions.size * 0.3) {
                    suggestions.add("专注中断较多，建议关闭通知或使用专注模式")
                }
            }
            
            // 分析习惯完成情况
            val habits = habitRepository.getAllHabits()
            if (habits.isNotEmpty()) {
                val avgCompletionRate = habits.map { habit ->
                    // 简化的完成率计算
                    if (habit.totalCompletions > 0) {
                        val daysSinceCreated = java.time.temporal.ChronoUnit.DAYS.between(
                            habit.createdAt.toLocalDate(), LocalDate.now()
                        )
                        if (daysSinceCreated > 0) {
                            (habit.totalCompletions.toFloat() / daysSinceCreated * 100).coerceAtMost(100f)
                        } else 100f
                    } else 0f
                }.average()
                
                when {
                    avgCompletionRate < 50 -> suggestions.add("习惯完成率较低，建议从简单的习惯开始培养")
                    avgCompletionRate < 80 -> suggestions.add("习惯培养进展不错，坚持下去会有更大收获")
                    else -> suggestions.add("习惯完成率很高，可以考虑添加新的挑战性习惯")
                }
            }
            
            if (suggestions.isEmpty()) {
                suggestions.add("开始使用专注计时功能，获得个性化建议")
                suggestions.add("设定每日目标，让进步更有方向")
            }
            
            suggestions.take(3)
        } catch (e: Exception) {
            Log.e(TAG, "生成行动建议失败", e)
            listOf("正在分析你的使用模式，稍后提供建议")
        }
    }
    
    /**
     * 生成年度成就
     */
    suspend fun generateYearlyAchievements(): List<String> {
        return try {
            val achievements = mutableListOf<String>()
            val thisYear = LocalDate.now().year
            val yearStart = LocalDate.of(thisYear, 1, 1)
            val today = LocalDate.now()
            
            // 获取今年的任务数据
            val yearTasks = taskRepository.getAllTasks().filter { task ->
                task.createdAt.year == thisYear
            }
            
            if (yearTasks.isNotEmpty()) {
                val completedTasks = yearTasks.filter { it.isCompleted }
                achievements.add("今年已完成${completedTasks.size}个任务，持续进步中")
                
                // 计算连续完成任务的天数
                val completionDates = completedTasks.mapNotNull { it.completedAt?.toLocalDate() }.distinct().sorted()
                if (completionDates.isNotEmpty()) {
                    val maxStreak = calculateMaxStreak(completionDates)
                    if (maxStreak > 7) {
                        achievements.add("最长连续${maxStreak}天完成任务，坚持的力量很强大")
                    }
                }
            }
            
            // 获取习惯数据
            val habits = habitRepository.getAllHabits()
            if (habits.isNotEmpty()) {
                val totalCompletions = habits.sumOf { it.totalCompletions }
                if (totalCompletions > 100) {
                    achievements.add("今年习惯打卡${totalCompletions}次，自律成就解锁")
                }
                
                val maxStreak = habits.maxOfOrNull { it.longestStreak } ?: 0
                if (maxStreak > 30) {
                    achievements.add("习惯最长坚持${maxStreak}天，毅力值满分")
                }
            }
            
            if (achievements.isEmpty()) {
                achievements.add("新的一年，新的开始，期待你的精彩表现")
            }
            
            achievements.take(3)
        } catch (e: Exception) {
            Log.e(TAG, "生成年度成就失败", e)
            listOf("年度数据统计中，敬请期待")
        }
    }
    
    /**
     * 找到最佳专注时段
     */
    private fun findBestFocusHour(sessions: List<TimeSession>): Int? {
        if (sessions.isEmpty()) return null
        
        val hourlyDuration = mutableMapOf<Int, Long>()
        sessions.forEach { session ->
            val hour = session.startTime.atZone(ZoneId.systemDefault()).hour
            hourlyDuration[hour] = hourlyDuration.getOrDefault(hour, 0) + session.duration
        }
        
        return hourlyDuration.maxByOrNull { it.value }?.key
    }
    
    /**
     * 计算最大连续天数
     */
    private fun calculateMaxStreak(dates: List<LocalDate>): Int {
        if (dates.isEmpty()) return 0
        
        var maxStreak = 1
        var currentStreak = 1
        
        for (i in 1 until dates.size) {
            if (dates[i] == dates[i - 1].plusDays(1)) {
                currentStreak++
                maxStreak = maxOf(maxStreak, currentStreak)
            } else {
                currentStreak = 1
            }
        }
        
        return maxStreak
    }
}

/**
 * 日期范围扩展函数
 */
private operator fun LocalDate.rangeTo(other: LocalDate): Iterable<LocalDate> {
    return generateSequence(this) { it.plusDays(1) }
        .takeWhile { !it.isAfter(other) }
        .asIterable()
}
