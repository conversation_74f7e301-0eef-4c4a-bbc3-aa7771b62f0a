package com.timeflow.app.di

import android.content.Context
// import com.google.firebase.analytics.FirebaseAnalytics
// import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.PrintWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Singleton

/**
 * Analytics和崩溃报告Hilt模块
 * 提供本地分析和崩溃报告功能
 * 注意: Firebase暂时替换为本地实现，待获取google-services.json后恢复
 */
@Module
@InstallIn(SingletonComponent::class)
object AnalyticsModule {
    
    /**
     * 提供分析跟踪功能
     * 临时使用Timber记录事件，待Firebase配置完成后替换
     */
    @Provides
    @Singleton
    fun provideAnalytics(@ApplicationContext context: Context): AnalyticsTracker {
        return LocalAnalyticsTracker(context)
    }
    
    /**
     * 提供崩溃报告功能
     * 使用本地日志文件存储崩溃报告
     */
    @Provides
    @Singleton
    fun provideCrashReporter(@ApplicationContext context: Context): CrashReporter {
        return LocalCrashReporter(context)
    }
}

/**
 * 分析跟踪接口
 */
interface AnalyticsTracker {
    fun logEvent(eventName: String, params: Map<String, Any>? = null)
    fun setUserProperty(name: String, value: String)
}

/**
 * 本地分析跟踪实现
 */
class LocalAnalyticsTracker(private val context: Context) : AnalyticsTracker {
    override fun logEvent(eventName: String, params: Map<String, Any>?) {
        Timber.d("Analytics Event: $eventName, Params: $params")
    }
    
    override fun setUserProperty(name: String, value: String) {
        Timber.d("User Property: $name = $value")
    }
}

/**
 * 崩溃报告接口
 */
interface CrashReporter {
    fun recordException(throwable: Throwable)
    fun log(message: String)
    fun setCustomKey(key: String, value: String)
}

/**
 * 本地崩溃报告实现
 * 将崩溃信息记录到本地日志文件
 */
class LocalCrashReporter(private val context: Context) : CrashReporter {
    
    private val customData = mutableMapOf<String, String>()
    private val logBuffer = mutableListOf<String>()
    private val logDir by lazy { File(context.filesDir, "crash_logs") }
    
    init {
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
    }
    
    override fun recordException(throwable: Throwable) {
        try {
            // 格式化异常信息
            val sw = java.io.StringWriter()
            val pw = PrintWriter(sw)
            throwable.printStackTrace(pw)
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            
            val report = StringBuilder()
            report.append("崩溃时间: $timestamp\n")
            report.append("异常类型: ${throwable.javaClass.name}\n")
            report.append("异常消息: ${throwable.message}\n\n")
            
            // 添加自定义数据
            if (customData.isNotEmpty()) {
                report.append("自定义数据:\n")
                customData.forEach { (key, value) ->
                    report.append("$key: $value\n")
                }
                report.append("\n")
            }
            
            // 添加日志缓冲区
            if (logBuffer.isNotEmpty()) {
                report.append("日志:\n")
                logBuffer.forEach { log ->
                    report.append("$log\n")
                }
                report.append("\n")
            }
            
            // 添加堆栈跟踪
            report.append("堆栈跟踪:\n")
            report.append(sw.toString())
            
            // 保存到文件
            val fileTimestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename = "crash_$fileTimestamp.txt"
            val crashFile = File(logDir, filename)
            
            FileOutputStream(crashFile).use { stream ->
                stream.write(report.toString().toByteArray())
            }
            
            Timber.e(throwable, "崩溃已记录到 ${crashFile.absolutePath}")
            
            // 清除缓存
            logBuffer.clear()
            customData.clear()
            
        } catch (e: Exception) {
            Timber.e(e, "记录异常失败")
        }
    }
    
    override fun log(message: String) {
        val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
        logBuffer.add("[$timestamp] $message")
        
        // 限制日志缓冲区大小，避免内存占用过多
        if (logBuffer.size > 100) {
            logBuffer.removeAt(0)
        }
        
        Timber.d("崩溃日志: $message")
    }
    
    override fun setCustomKey(key: String, value: String) {
        customData[key] = value
        Timber.d("设置崩溃报告参数: $key = $value")
    }
} 