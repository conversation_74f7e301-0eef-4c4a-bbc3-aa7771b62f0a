package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.GoalTemplate
import com.timeflow.app.data.entity.GoalSubTaskTemplate
import kotlinx.coroutines.flow.Flow

/**
 * 目标模板数据访问对象
 */
@Dao
interface GoalTemplateDao {
    // 基本的CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplate(template: GoalTemplate): Long

    @Update
    suspend fun updateTemplate(template: GoalTemplate)

    @Delete
    suspend fun deleteTemplate(template: GoalTemplate)

    @Query("DELETE FROM goal_templates WHERE id = :templateId")
    suspend fun deleteTemplateById(templateId: String)

    @Query("SELECT * FROM goal_templates ORDER BY name")
    fun getAllTemplates(): Flow<List<GoalTemplate>>
    
    @Query("SELECT * FROM goal_templates ORDER BY name")
    suspend fun getAllTemplatesSync(): List<GoalTemplate>

    @Query("SELECT * FROM goal_templates WHERE id = :templateId")
    suspend fun getTemplateById(templateId: String): GoalTemplate?

    @Query("SELECT * FROM goal_templates WHERE category = :category ORDER BY name")
    fun getTemplatesByCategory(category: String): Flow<List<GoalTemplate>>

    @Query("SELECT * FROM goal_templates WHERE categoryId = :categoryId ORDER BY usageCount DESC, lastUsed DESC")
    fun getTemplatesByCategoryId(categoryId: String): Flow<List<GoalTemplate>>

    @Query("SELECT * FROM goal_templates ORDER BY usageCount DESC LIMIT :limit")
    fun getMostUsedTemplates(limit: Int): Flow<List<GoalTemplate>>

    @Query("SELECT * FROM goal_templates ORDER BY lastUsed DESC LIMIT :limit")
    fun getRecentlyUsedTemplates(limit: Int): Flow<List<GoalTemplate>>

    // 子任务模板操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSubTaskTemplate(subTaskTemplate: GoalSubTaskTemplate): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSubTaskTemplates(subTaskTemplates: List<GoalSubTaskTemplate>)

    @Update
    suspend fun updateSubTaskTemplate(subTaskTemplate: GoalSubTaskTemplate)

    @Query("DELETE FROM goal_subtask_templates WHERE templateId = :templateId")
    suspend fun deleteAllSubTaskTemplatesForTemplate(templateId: String)

    @Query("SELECT * FROM goal_subtask_templates WHERE templateId = :templateId ORDER BY orderIndex")
    suspend fun getSubTaskTemplatesForTemplate(templateId: String): List<GoalSubTaskTemplate>

    // 使用记录更新
    @Query("UPDATE goal_templates SET usageCount = usageCount + 1, lastUsed = :timestamp WHERE id = :templateId")
    suspend fun incrementTemplateUsage(templateId: String, timestamp: Long)

    // 高级搜索
    @Query("SELECT * FROM goal_templates WHERE name LIKE '%' || :searchText || '%' OR description LIKE '%' || :searchText || '%'")
    fun searchTemplates(searchText: String): Flow<List<GoalTemplate>>

    // 添加分页查询支持
    @Query("SELECT * FROM goal_templates ORDER BY name LIMIT :pageSize OFFSET :offset")
    suspend fun getTemplatesPaged(pageSize: Int, offset: Int): List<GoalTemplate>
    
    @Query("SELECT COUNT(*) FROM goal_templates")
    suspend fun getTemplatesCount(): Int
    
    @Query("SELECT * FROM goal_templates WHERE category = :category ORDER BY name LIMIT :pageSize OFFSET :offset")
    suspend fun getTemplatesByCategoryPaged(category: String, pageSize: Int, offset: Int): List<GoalTemplate>
    
    @Query("SELECT COUNT(*) FROM goal_templates WHERE category = :category")
    suspend fun getTemplatesCountByCategory(category: String): Int
    
    @Query("SELECT * FROM goal_templates WHERE name LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%' ORDER BY name LIMIT :pageSize OFFSET :offset")
    suspend fun searchTemplatesPaged(query: String, pageSize: Int, offset: Int): List<GoalTemplate>
    
    @Query("SELECT COUNT(*) FROM goal_templates WHERE name LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%'")
    suspend fun searchTemplatesCount(query: String): Int
} 