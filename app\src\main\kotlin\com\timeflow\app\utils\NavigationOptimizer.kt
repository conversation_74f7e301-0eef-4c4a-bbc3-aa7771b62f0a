package com.timeflow.app.utils

import android.os.Bundle
import android.util.Log
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.ConcurrentHashMap
import com.timeflow.app.R
import com.timeflow.app.ui.navigation.AppDestinations
import kotlinx.coroutines.Dispatchers
import androidx.compose.foundation.interaction.MutableInteractionSource
import kotlinx.coroutines.MainScope

/**
 * 导航优化工具
 * 提供导航相关的优化功能，解决资源ID错误问题和优化页面预加载
 */
object NavigationOptimizer {
    private const val TAG = "NavigationOptimizer"
    
    // 缓存路由到资源ID的映射
    private val routeToResourceIdMap = ConcurrentHashMap<String, Int>()
    
    // 缓存预加载状态
    private val preloadedRoutes = mutableSetOf<String>()
    
    // 最后一次导航的路由和时间戳
    private var lastNavigationRoute: String? = null
    private var lastNavigationTime: Long = 0L
    
    // 防抖时间窗口 (ms)
    private const val DEBOUNCE_WINDOW = 300L
    
    // 导航性能追踪
    private val lastNavigationTimeAtomic = AtomicLong(0)
    private const val NAV_THROTTLE_MS = 300L // 导航防抖时间
    
    // 动画配置
    private const val DEFAULT_ANIM_DURATION = 150
    private const val FAST_SCREEN_TRANSITION = 100
    
    /**
     * 检查是否可以导航（防止快速连续导航）
     * @return 是否允许导航
     */
    fun canNavigate(): Boolean {
        val now = System.currentTimeMillis()
        val lastNav = lastNavigationTimeAtomic.get()
        return if (now - lastNav > NAV_THROTTLE_MS) {
            lastNavigationTimeAtomic.set(now)
            true
        } else {
            Timber.w("导航操作被节流，忽略: ${now - lastNav}ms < ${NAV_THROTTLE_MS}ms")
            false
        }
    }
    
    /**
     * 轻量级Enter过渡动画
     */
    val lightweightEnterTransition: AnimatedContentTransitionScope<*>.() -> EnterTransition = {
        // 移除所有动画效果
        EnterTransition.None
    }
    
    /**
     * 轻量级Exit过渡动画
     */
    val lightweightExitTransition: AnimatedContentTransitionScope<*>.() -> ExitTransition = {
        // 移除所有动画效果
        ExitTransition.None
    }
    
    /**
     * 安全导航方法
     * 解决资源ID错误问题，确保导航安全有效
     */
    fun safeNavigate(
        navController: NavController,
        route: String,
        args: Bundle? = null,
        navOptions: NavOptions? = null,
        popUpTo: String? = null,
        inclusive: Boolean = false,
        singleTop: Boolean = true
    ) {
        try {
            // 防抖处理，避免快速重复导航
            val currentTime = System.currentTimeMillis()
            if (route == lastNavigationRoute && currentTime - lastNavigationTime < DEBOUNCE_WINDOW) {
                Log.d(TAG, "避免频繁导航: $route")
                return
            }
            
            // 检查当前目的地，避免导航到相同路由
            val currentRoute = navController.currentDestination?.route
            if (currentRoute == route || currentRoute?.contains(route.split("/").first()) == true) {
                Log.d(TAG, "已经在目标路由范围内: $route, 当前: $currentRoute")
                return
            }
            
            // 更新最后导航信息
            lastNavigationRoute = route
            lastNavigationTime = currentTime
            
            // 创建优化的NavOptions
            val finalNavOptions = navOptions ?: if (popUpTo != null) {
                NavOptions.Builder()
                    .setPopUpTo(popUpTo, inclusive)
                    .setLaunchSingleTop(singleTop)
                    // 移除所有自定义过渡动画
                    .build()
            } else {
                NavOptions.Builder()
                    .setLaunchSingleTop(singleTop)
                    // 移除所有自定义过渡动画
                    .build()
            }
            
            // 减少GPU渲染压力
            // 导航前短暂暂停渲染复杂UI，只在特定高性能消耗的路由间切换时应用
            // 使用协程非阻塞方式执行
            MainScope().launch {
                if (route.contains("task_detail") || route.contains("task_edit") || 
                    route.contains("task_list") || currentRoute?.contains("task_") == true) {
                    navController.execute { navController.navigate(route, finalNavOptions) }
                } else {
                    // 执行实际导航 - 使用正确的参数组合
                    if (args != null) {
                        // 如果有Bundle参数，使用默认导航
                        navController.navigate(route)
                    } else {
                        // 使用route字符串导航，可以传递navOptions
                        navController.navigate(route, finalNavOptions)
                    }
                }
                
                Log.d(TAG, "成功导航到: $route")
            }
        } catch (e: Exception) {
            // 处理资源ID错误
            if (e.message?.contains("No package ID") == true || 
                e.message?.contains("not found on the current back stack") == true) {
                
                Log.e(TAG, "导航错误，尝试修复: ${e.message}")
                
                // 尝试回到已知的安全路由
                try {
                    navController.navigate("home")
                    Log.d(TAG, "已恢复到主页面")
                } catch (innerEx: Exception) {
                    Log.e(TAG, "恢复导航失败: ${innerEx.message}")
                }
            } else {
                Log.e(TAG, "导航失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 导航控制器扩展，使用阻塞执行
     */
    private suspend fun NavController.execute(action: suspend () -> Unit) {
        // 短暂等待以优化渲染管线
        delay(16)
        withContext(Dispatchers.Main.immediate) {
            action()
        }
    }
    
    /**
     * 预加载路由内容
     * 标记路由为已预加载，可用于优化导航体验
     */
    fun markRouteAsPreloaded(route: String) {
        preloadedRoutes.add(route)
    }
    
    /**
     * 检查路由是否已预加载
     */
    fun isRoutePreloaded(route: String): Boolean {
        return preloadedRoutes.contains(route)
    }
    
    /**
     * 清除预加载状态
     * 当内存不足时可调用此方法释放资源
     */
    fun clearPreloadedRoutes() {
        preloadedRoutes.clear()
    }
    
    /**
     * 记录并缓存路由的资源ID
     * 用于解决"No package ID X found for resource ID"错误
     */
    fun registerRouteResourceId(route: String, resourceId: Int) {
        routeToResourceIdMap[route] = resourceId
    }
    
    /**
     * 获取路由的资源ID
     */
    fun getRouteResourceId(route: String): Int? {
        return routeToResourceIdMap[route]
    }
    
    /**
     * 防抖动的可组合函数
     * 用于防止按钮等UI元素的重复快速点击
     * @param onClick 点击处理函数
     * @return 带防抖的点击处理函数
     */
    @Composable
    fun rememberDebouncedNavCallback(
        delayMillis: Long = NAV_THROTTLE_MS,
        onClick: () -> Unit
    ): () -> Unit {
        var lastClickTime by remember { mutableStateOf(0L) }
        
        return {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime > delayMillis) {
                lastClickTime = currentTime
                onClick()
            } else {
                // 忽略快速连续点击
                Timber.d("快速点击被忽略: ${currentTime - lastClickTime}ms < ${delayMillis}ms")
            }
        }
    }
    
    /**
     * 为BackHandler创建防抖状态
     * 防止快速连续返回
     */
    @Composable
    fun rememberBackHandlerState(): MutableState<Boolean> {
        val backEnabled = rememberSaveable { mutableStateOf(true) }
        
        LaunchedEffect(backEnabled.value) {
            if (!backEnabled.value) {
                // 如果禁用了返回，等待一段时间后重新启用
                delay(NAV_THROTTLE_MS)
                backEnabled.value = true
            }
        }
        
        return backEnabled
    }
} 