package com.timeflow.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Looper
import androidx.annotation.MainThread
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 任务优先级枚举
 */
enum class Priority {
    URGENT,
    HIGH,
    MEDIUM,
    LOW
}

/**
 * 安全防护工具类
 * 提供各种安全检查和边界防御功能
 */
object SafetyGuard {
    
    /**
     * 安全解析优先级枚举值
     * 防止非法值导致的崩溃
     */
    fun parseTaskPriority(priority: Int): Priority {
        return when(priority) {
            in 0..3 -> Priority.values().getOrElse(priority) { Priority.HIGH }
            else -> {
                Timber.w("非法优先级值: $priority，使用默认值")
                Priority.HIGH // 非法值兜底
            }
        }
    }
    
    /**
     * 安全地获取枚举值
     * 如果索引越界则返回默认值
     */
    inline fun <reified T : Enum<T>> safeEnumValue(index: Int, defaultValue: T): T {
        return if (index >= 0 && index < enumValues<T>().size) {
            enumValues<T>()[index]
        } else {
            Timber.w("枚举索引越界: ${T::class.java.simpleName}[$index]，使用默认值")
            defaultValue
        }
    }
    
    /**
     * 安全地转换字符串为整数
     * 如果转换失败则返回默认值
     */
    fun safeParseInt(value: String?, defaultValue: Int): Int {
        if (value.isNullOrEmpty()) return defaultValue
        
        return try {
            value.toInt()
        } catch (e: NumberFormatException) {
            Timber.w("无法解析整数: $value，使用默认值")
            defaultValue
        }
    }
    
    /**
     * 确保在主线程上执行
     * 如果不是主线程则抛出异常
     */
    @MainThread
    fun ensureMainThread(methodName: String) {
        check(Looper.myLooper() == Looper.getMainLooper()) {
            "方法 $methodName 必须在主线程上调用"
        }
    }
    
    /**
     * 安全地加载位图，优化内存使用
     * 适用于主题图片等大图
     */
    suspend fun loadImageSafely(
        context: Context, 
        resId: Int,
        maxWidth: Int = 1024,
        maxHeight: Int = 1024
    ): Bitmap = withContext(Dispatchers.IO) {
        try {
            BitmapFactory.Options().run {
                // 首先只解码尺寸
                inJustDecodeBounds = true
                BitmapFactory.decodeResource(context.resources, resId, this)
                
                // 计算采样率
                inSampleSize = calculateInSampleSize(this, maxWidth, maxHeight)
                
                // 实际解码
                inJustDecodeBounds = false
                BitmapFactory.decodeResource(context.resources, resId, this)
                    ?: createPlaceholderBitmap(maxWidth, maxHeight)
            }
        } catch (e: Exception) {
            Timber.e(e, "加载图片失败: $resId")
            createPlaceholderBitmap(maxWidth, maxHeight)
        }
    }
    
    /**
     * 计算位图采样率
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val (width, height) = options.outWidth to options.outHeight
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 创建占位符位图
     */
    private fun createPlaceholderBitmap(width: Int = 100, height: Int = 100): Bitmap {
        return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888).apply {
            eraseColor(0xFFCCCCCC.toInt()) // 灰色背景
        }
    }
} 