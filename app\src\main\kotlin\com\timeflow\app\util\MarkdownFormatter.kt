package com.timeflow.app.util

/**
 * Markdown文本格式化工具
 * 将AI生成的markdown语法转换为更易读的普通文本格式
 */
object MarkdownFormatter {

    /**
     * 格式化markdown文本为普通文本
     */
    fun formatMarkdownText(text: String): String {
        var formattedText = text
        
        // 处理特定的PDCA框架标题
        formattedText = formattedText.replace(Regex("#{2}\\s*Plan\\s*-\\s*(.*)$", RegexOption.MULTILINE)) { 
            "📋 计划回顾 - ${it.groupValues[1].trim()}"
        }
        formattedText = formattedText.replace(Regex("#{2}\\s*Do\\s*-\\s*(.*)$", RegexOption.MULTILINE)) { 
            "🚀 执行分析 - ${it.groupValues[1].trim()}"
        }
        formattedText = formattedText.replace(Regex("#{2}\\s*Check\\s*-\\s*(.*)$", RegexOption.MULTILINE)) { 
            "📊 检查评估 - ${it.groupValues[1].trim()}"
        }
        formattedText = formattedText.replace(Regex("#{2}\\s*Act\\s*-\\s*(.*)$", RegexOption.MULTILINE)) { 
            "⚡ 改进行动 - ${it.groupValues[1].trim()}"
        }
        
        // 处理其他二级标题 (## 标题)
        formattedText = formattedText.replace(Regex("^#{2}\\s*(.*)$", RegexOption.MULTILINE)) { 
            "📋 ${it.groupValues[1].trim()}"
        }
        
        // 处理一级标题 (# 标题)
        formattedText = formattedText.replace(Regex("^#{1}\\s*(.*)$", RegexOption.MULTILINE)) { 
            "🎯 ${it.groupValues[1].trim()}"
        }
        
        // 处理三级标题 (### 标题)
        formattedText = formattedText.replace(Regex("^#{3}\\s*(.*)$", RegexOption.MULTILINE)) { 
            "▶️ ${it.groupValues[1].trim()}"
        }
        
        // 处理特定类型的方括号标记
        formattedText = formattedText.replace(Regex("\\[Plan(\\d*)\\]\\s*\\*\\*([^*]+)\\*\\*")) { 
            "📝 计划${it.groupValues[1]}: ${it.groupValues[2].trim()}"
        }
        formattedText = formattedText.replace(Regex("\\[Do(\\d*)\\]\\s*\\*\\*([^*]+)\\*\\*")) { 
            "🏃 执行${it.groupValues[1]}: ${it.groupValues[2].trim()}"
        }
        formattedText = formattedText.replace(Regex("\\[Check(\\d*)\\]\\s*\\*\\*([^*]+)\\*\\*")) { 
            "🔍 检查${it.groupValues[1]}: ${it.groupValues[2].trim()}"
        }
        formattedText = formattedText.replace(Regex("\\[Act(\\d*)\\]\\s*\\*\\*([^*]+)\\*\\*")) { 
            "⚡ 行动${it.groupValues[1]}: ${it.groupValues[2].trim()}"
        }
        
        // 处理加粗文本 (**文本**)
        formattedText = formattedText.replace(Regex("\\*\\*([^*]+)\\*\\*")) { 
            "⭐ ${it.groupValues[1].trim()}"
        }
        
        // 处理一般的方括号标记 ([Plan1], [Do2] 等)
        formattedText = formattedText.replace(Regex("\\[([^\\]]+)\\]\\s*")) { 
            "• ${it.groupValues[1].trim()}: "
        }
        
        // 处理列表项 (- 项目 或 * 项目)
        formattedText = formattedText.replace(Regex("^[-*]\\s*(.*)$", RegexOption.MULTILINE)) { 
            "  ◦ ${it.groupValues[1].trim()}"
        }
        
        // 处理数字列表 (1. 项目)
        formattedText = formattedText.replace(Regex("^\\d+\\.\\s*(.*)$", RegexOption.MULTILINE)) { 
            "  ${it.groupValues[0].trim()}"
        }
        
        // 清理多余的空行
        formattedText = formattedText.replace(Regex("\n{3,}"), "\n\n")
        
        return formattedText.trim()
    }

    /**
     * 提取标题和内容
     */
    fun extractTitleAndContent(text: String): Pair<String, String> {
        val lines = text.split("\n").filter { it.isNotBlank() }
        
        if (lines.isEmpty()) return Pair("", "")
        
        val firstLine = lines.first()
        
        // 检查第一行是否为标题格式
        val titlePatterns = listOf(
            Regex("^#{1,3}\\s*(.*)$"),  // # 标题
            Regex("\\*\\*([^*]+)\\*\\*"),  // **标题**
            Regex("\\[([^\\]]+)\\]\\s*\\*\\*([^*]+)\\*\\*")  // [Plan1] **标题**
        )
        
        for (pattern in titlePatterns) {
            val match = pattern.find(firstLine)
            if (match != null) {
                val title = when {
                    match.groupValues.size > 2 -> match.groupValues[2].trim()
                    match.groupValues.size > 1 -> match.groupValues[1].trim()
                    else -> firstLine.trim()
                }
                val content = lines.drop(1).joinToString("\n").trim()
                return Pair(title, formatMarkdownText(content))
            }
        }
        
        // 如果没有找到标题格式，返回原始文本
        return Pair("", formatMarkdownText(text))
    }

    /**
     * 格式化AI洞察内容
     */
    fun formatInsightContent(title: String, description: String): Pair<String, String> {
        val formattedTitle = formatMarkdownText(title)
        val formattedDescription = formatMarkdownText(description)
        
        // 如果标题已经包含特殊字符，直接使用
        val finalTitle = if (formattedTitle.contains(Regex("[📋🎯▶️⭐•]"))) {
            formattedTitle
        } else {
            "💡 $formattedTitle"
        }
        
        return Pair(finalTitle, formattedDescription)
    }

    /**
     * 格式化AI建议内容
     */
    fun formatRecommendationContent(title: String, description: String): Pair<String, String> {
        val formattedTitle = formatMarkdownText(title)
        val formattedDescription = formatMarkdownText(description)
        
        // 如果标题已经包含特殊字符，直接使用
        val finalTitle = if (formattedTitle.contains(Regex("[📋🎯▶️⭐•]"))) {
            formattedTitle
        } else {
            "🚀 $formattedTitle"
        }
        
        return Pair(finalTitle, formattedDescription)
    }

    /**
     * 智能段落分割
     */
    fun smartParagraphSplit(text: String): List<String> {
        val formattedText = formatMarkdownText(text)
        
        // 按照标题和段落分割
        val sections = mutableListOf<String>()
        val lines = formattedText.split("\n")
        
        var currentSection = StringBuilder()
        
        for (line in lines) {
            val trimmedLine = line.trim()
            
            // 检查是否为标题行
            if (trimmedLine.startsWith("📋") || 
                trimmedLine.startsWith("🎯") || 
                trimmedLine.startsWith("▶️") ||
                trimmedLine.startsWith("⭐") ||
                trimmedLine.startsWith("•")) {
                
                // 如果当前段落不为空，保存它
                if (currentSection.isNotEmpty()) {
                    sections.add(currentSection.toString().trim())
                    currentSection.clear()
                }
                
                // 开始新段落
                currentSection.append(trimmedLine)
            } else if (trimmedLine.isNotEmpty()) {
                if (currentSection.isNotEmpty()) {
                    currentSection.append("\n")
                }
                currentSection.append(trimmedLine)
            }
        }
        
        // 添加最后一个段落
        if (currentSection.isNotEmpty()) {
            sections.add(currentSection.toString().trim())
        }
        
        return sections.filter { it.isNotBlank() }
    }
} 