package com.timeflow.app.ui.screen.health

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.timeflow.app.data.entity.CycleRecord
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 添加经期记录对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddPeriodDialog(
    onDismiss: () -> Unit,
    onConfirm: (startDate: LocalDate, endDate: LocalDate?, notes: String?) -> Unit
) {
    var startDate by remember { mutableStateOf(LocalDate.now().minusDays(7)) }
    var endDate by remember { mutableStateOf<LocalDate?>(null) }
    var notes by remember { mutableStateOf("") }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 标题
                Text(
                    text = "添加历史经期",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF880E4F),
                    modifier = Modifier.padding(bottom = 20.dp)
                )
                
                // 开始日期选择
                DatePickerField(
                    label = "开始日期",
                    date = startDate,
                    onDateClick = { showStartDatePicker = true },
                    isRequired = true
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 结束日期选择
                DatePickerField(
                    label = "结束日期（可选）",
                    date = endDate,
                    onDateClick = { showEndDatePicker = true },
                    isRequired = false
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 备注输入
                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = { Text("备注（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF880E4F),
                        focusedLabelColor = Color(0xFF880E4F)
                    )
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            onConfirm(startDate, endDate, notes.takeIf { it.isNotBlank() })
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF880E4F)
                        )
                    ) {
                        Text("确定")
                    }
                }
            }
        }
    }
    
    // 日期选择器
    if (showStartDatePicker) {
        DatePickerDialog(
            initialDate = startDate,
            onDateSelected = { 
                startDate = it
                showStartDatePicker = false
            },
            onDismiss = { showStartDatePicker = false }
        )
    }
    
    if (showEndDatePicker) {
        DatePickerDialog(
            initialDate = endDate ?: startDate.plusDays(5),
            onDateSelected = { 
                endDate = it
                showEndDatePicker = false
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
}

/**
 * 编辑经期记录对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditPeriodDialog(
    cycle: CycleRecord,
    onDismiss: () -> Unit,
    onConfirm: (startDate: LocalDate, endDate: LocalDate?, notes: String?) -> Unit
) {
    var startDate by remember { mutableStateOf(cycle.startDate) }
    var endDate by remember { mutableStateOf(cycle.endDate) }
    var notes by remember { mutableStateOf(cycle.notes ?: "") }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 标题
                Text(
                    text = "编辑经期记录",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF880E4F),
                    modifier = Modifier.padding(bottom = 20.dp)
                )
                
                // 开始日期选择
                DatePickerField(
                    label = "开始日期",
                    date = startDate,
                    onDateClick = { showStartDatePicker = true },
                    isRequired = true
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 结束日期选择
                DatePickerField(
                    label = "结束日期（可选）",
                    date = endDate,
                    onDateClick = { showEndDatePicker = true },
                    isRequired = false
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 备注输入
                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = { Text("备注（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFF880E4F),
                        focusedLabelColor = Color(0xFF880E4F)
                    )
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            onConfirm(startDate, endDate, notes.takeIf { it.isNotBlank() })
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF880E4F)
                        )
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
    
    // 日期选择器
    if (showStartDatePicker) {
        DatePickerDialog(
            initialDate = startDate,
            onDateSelected = { 
                startDate = it
                showStartDatePicker = false
            },
            onDismiss = { showStartDatePicker = false }
        )
    }
    
    if (showEndDatePicker) {
        DatePickerDialog(
            initialDate = endDate ?: startDate.plusDays(5),
            onDateSelected = { 
                endDate = it
                showEndDatePicker = false
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
}

/**
 * 删除确认对话框
 */
@Composable
fun DeleteConfirmDialog(
    cycle: CycleRecord,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "确认删除",
                fontWeight = FontWeight.Bold,
                color = Color(0xFF880E4F)
            )
        },
        text = {
            Text(
                text = "确定要删除 ${cycle.startDate.format(dateFormatter)} 的经期记录吗？\n\n此操作无法撤销。",
                color = Color.DarkGray
            )
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE91E63)
                )
            ) {
                Text("删除")
            }
        },
        dismissButton = {
            OutlinedButton(
                onClick = onDismiss,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.Gray
                )
            ) {
                Text("取消")
            }
        },
        containerColor = Color.White
    )
}

/**
 * 日期选择字段
 */
@Composable
fun DatePickerField(
    label: String,
    date: LocalDate?,
    onDateClick: () -> Unit,
    isRequired: Boolean
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    
    Column {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color.Gray,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onDateClick() },
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF5F5F5)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = date?.format(dateFormatter) ?: "点击选择日期",
                    fontSize = 16.sp,
                    color = if (date != null) Color.DarkGray else Color.Gray
                )
                
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = null,
                    tint = Color(0xFF880E4F),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 日期选择器对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    initialDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialDate.toEpochDay() * 24 * 60 * 60 * 1000
    )

    DatePickerDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(
                onClick = {
                    datePickerState.selectedDateMillis?.let { millis ->
                        val selectedDate = LocalDate.ofEpochDay(millis / (24 * 60 * 60 * 1000))
                        onDateSelected(selectedDate)
                    }
                }
            ) {
                Text(
                    text = "确定",
                    color = Color(0xFF880E4F)
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "取消",
                    color = Color.Gray
                )
            }
        }
    ) {
        DatePicker(
            state = datePickerState,
            colors = DatePickerDefaults.colors(
                selectedDayContainerColor = Color(0xFF880E4F),
                todayContentColor = Color(0xFF880E4F),
                todayDateBorderColor = Color(0xFF880E4F)
            )
        )
    }
}
