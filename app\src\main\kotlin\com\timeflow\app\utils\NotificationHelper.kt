package com.timeflow.app.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import javax.inject.Inject
import javax.inject.Singleton
import dagger.hilt.android.qualifiers.ApplicationContext

/**
 * 通知助手类
 * 封装通知的创建和管理逻辑
 */
@Singleton
class NotificationHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    /**
     * 创建通知渠道
     */
    fun createNotificationChannel(
        channelId: String,
        channelName: String,
        channelDescription: String,
        importance: Int = NotificationManager.IMPORTANCE_DEFAULT
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                channelName,
                importance
            ).apply {
                description = channelDescription
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建基本通知
     */
    fun createNotification(
        channelId: String,
        title: String,
        content: String,
        smallIcon: Int,
        contentIntent: PendingIntent? = null,
        priority: Int = NotificationCompat.PRIORITY_DEFAULT,
        ongoing: Boolean = false,
        autoCancel: Boolean = true
    ): Notification {
        val builder = NotificationCompat.Builder(context, channelId)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(smallIcon)
            .setPriority(priority)
            .setOngoing(ongoing)
            .setAutoCancel(autoCancel)
        
        if (contentIntent != null) {
            builder.setContentIntent(contentIntent)
        }
        
        return builder.build()
    }
    
    /**
     * 创建拓展通知
     */
    fun createExpandableNotification(
        channelId: String,
        title: String,
        content: String,
        expandedContent: String,
        smallIcon: Int,
        contentIntent: PendingIntent? = null,
        priority: Int = NotificationCompat.PRIORITY_DEFAULT,
        ongoing: Boolean = false,
        autoCancel: Boolean = true
    ): Notification {
        val builder = NotificationCompat.Builder(context, channelId)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(smallIcon)
            .setPriority(priority)
            .setOngoing(ongoing)
            .setAutoCancel(autoCancel)
            .setStyle(NotificationCompat.BigTextStyle().bigText(expandedContent))
        
        if (contentIntent != null) {
            builder.setContentIntent(contentIntent)
        }
        
        return builder.build()
    }
    
    /**
     * 显示通知
     */
    fun notify(notificationId: Int, notification: Notification) {
        try {
            if (hasNotificationPermission()) {
                NotificationManagerCompat.from(context).notify(notificationId, notification)
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for showing notification", e)
        }
    }
    
    /**
     * 取消通知
     */
    fun cancel(notificationId: Int) {
        try {
            NotificationManagerCompat.from(context).cancel(notificationId)
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for canceling notification", e)
        }
    }
    
    /**
     * 检查通知权限
     */
    private fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Pre-Android 13 doesn't need runtime notification permission
        }
    }
    
    companion object {
        private const val TAG = "NotificationHelper"
    }
} 