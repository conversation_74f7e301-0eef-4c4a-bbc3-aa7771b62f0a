package com.timeflow.app.utils

import android.graphics.drawable.RippleDrawable
import android.os.Build
import android.view.View
import android.view.ViewGroup
import timber.log.Timber

/**
 * 为View启用硬件加速
 * 解决RippleDrawable在非硬件加速Canvas上的动画问题
 */
fun View.enableHardwareAcceleration() {
    try {
        // 检查是否已经启用了硬件加速，避免重复设置
        if (this.layerType == View.LAYER_TYPE_HARDWARE) {
            return
        }
        
        // 启用硬件加速
        this.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        
        // 如果view背景是RippleDrawable，进行专门优化
        if (this.background is RippleDrawable) {
            this.isClickable = true
            // 对于RippleDrawable，不需要额外优化，硬件加速已经足够
        }
        
        // 如果是ViewGroup，使用优化的方式处理子视图
        if (this is ViewGroup) {
            // 获取子视图数量
            val childCount = this.childCount
            
            // 如果子视图较少，直接处理
            if (childCount <= 5) {
                for (i in 0 until childCount) {
                    this.getChildAt(i).enableHardwareAcceleration()
                }
            } else {
                // 子视图较多，只处理可点击的和RippleDrawable相关的视图
                for (i in 0 until childCount) {
                    val child = this.getChildAt(i)
                    if (child.isClickable || child.background is RippleDrawable) {
                        child.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    } else if (child is ViewGroup && child.childCount > 0) {
                        // 只在可能包含交互元素的ViewGroup上递归
                        child.enableHardwareAcceleration()
                    }
                }
            }
        }
        
        Timber.d("已为视图启用硬件加速")
    } catch (e: Exception) {
        Timber.e(e, "为视图启用硬件加速失败: ${e.message}")
    }
}

/**
 * 优化RippleDrawable
 * 简化实现，避免不必要的反射操作
 */
private fun View.optimizeRippleDrawable() {
    try {
        val ripple = this.background as? RippleDrawable ?: return
        
        // 确保View是可点击的并启用硬件加速
        this.isClickable = true
        this.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        
        // 在API 23及以上版本设置透明度
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 使用统一的颜色状态列表
            val rippleColor = 0x80000000.toInt() // 半透明黑色
            ripple.setColor(android.content.res.ColorStateList.valueOf(rippleColor))
        }
    } catch (e: Exception) {
        Timber.e(e, "优化RippleDrawable失败: ${e.message}")
    }
} 