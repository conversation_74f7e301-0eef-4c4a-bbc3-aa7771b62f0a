package com.timeflow.app.widget

import android.content.Context
import android.util.Log

/**
 * 今日待办小组件数据提供者
 * 负责获取和处理今日任务数据
 */
class TodayTasksDataProvider(
    private val context: Context
) {

    companion object {
        private const val TAG = "TodayTasksDataProvider"
        private const val MAX_TASKS_DISPLAY = 4
    }

    /**
     * 获取今日待办任务数据
     * 暂时返回默认数据，后续可以集成真实的任务数据
     */
    fun getTodayTasksData(): TodayTasksData {
        return try {
            // TODO: 集成真实的任务数据获取
            // 目前返回模拟数据
            Log.d(TAG, "获取今日任务数据（使用模拟数据）")
            getDefaultTasksData()
        } catch (e: Exception) {
            Log.e(TAG, "获取今日任务数据失败", e)
            // 返回默认数据
            getDefaultTasksData()
        }
    }
    

    
    /**
     * 获取默认任务数据（当数据获取失败时使用）
     */
    private fun getDefaultTasksData(): TodayTasksData {
        val defaultTasks = listOf(
            SimpleTaskWithPriority("小组件设计", false, "HIGH"),
            SimpleTaskWithPriority("《哈利波特》", false, "MEDIUM"),
            SimpleTaskWithPriority("每天回答3道面", false, "LOW"),
            SimpleTaskWithPriority("一周锻炼5天", true, "MEDIUM")
        )

        val incompleteTasks = defaultTasks.filter { !it.isCompleted }
        val completedTasks = defaultTasks.filter { it.isCompleted }

        return TodayTasksData(
            simpleIncompleteTasks = incompleteTasks.map {
                SimpleTask(it.title, it.isCompleted)
            },
            simpleIncompleteTasksWithPriority = incompleteTasks,
            totalIncomplete = incompleteTasks.size,
            totalCompleted = completedTasks.size,
            totalTasks = defaultTasks.size
        )
    }
}

/**
 * 今日任务数据
 */
data class TodayTasksData(
    val simpleIncompleteTasks: List<SimpleTask> = emptyList(),
    val simpleIncompleteTasksWithPriority: List<SimpleTaskWithPriority> = emptyList(),
    val totalIncomplete: Int = 0,
    val totalCompleted: Int = 0,
    val totalTasks: Int = 0
) {
    /**
     * 获取显示用的任务列表
     */
    fun getDisplayTasks(): List<DisplayTask> {
        return if (simpleIncompleteTasksWithPriority.isNotEmpty()) {
            simpleIncompleteTasksWithPriority.map { task ->
                DisplayTask(
                    title = task.title,
                    isCompleted = task.isCompleted,
                    priority = task.priority
                )
            }
        } else {
            simpleIncompleteTasks.map { task ->
                DisplayTask(
                    title = task.title,
                    isCompleted = task.isCompleted,
                    priority = "NORMAL"
                )
            }
        }
    }

    /**
     * 获取完成进度百分比
     */
    fun getCompletionPercentage(): Int {
        return if (totalTasks > 0) {
            (totalCompleted * 100) / totalTasks
        } else {
            0
        }
    }
}

/**
 * 简单任务数据（用于默认显示）
 */
data class SimpleTask(
    val title: String,
    val isCompleted: Boolean
)

/**
 * 带优先级的简单任务数据
 */
data class SimpleTaskWithPriority(
    val title: String,
    val isCompleted: Boolean,
    val priority: String = "NORMAL"
)

/**
 * 显示用任务数据
 */
data class DisplayTask(
    val title: String,
    val isCompleted: Boolean,
    val priority: String = "NORMAL"
) {
    /**
     * 获取优先级颜色资源ID
     */
    fun getPriorityColorRes(): Int {
        return when (priority.uppercase()) {
            "HIGH", "URGENT" -> com.timeflow.app.R.color.widget_priority_high
            "MEDIUM", "NORMAL" -> com.timeflow.app.R.color.widget_priority_medium
            "LOW" -> com.timeflow.app.R.color.widget_priority_low
            else -> com.timeflow.app.R.color.widget_accent_blue
        }
    }
    
    /**
     * 获取优先级图标资源ID
     */
    fun getPriorityIconRes(): Int {
        return when (priority.uppercase()) {
            "HIGH", "URGENT" -> com.timeflow.app.R.drawable.widget_circle_red
            "MEDIUM", "NORMAL" -> com.timeflow.app.R.drawable.widget_circle_orange
            "LOW" -> com.timeflow.app.R.drawable.widget_circle_green
            else -> com.timeflow.app.R.drawable.widget_circle_blue
        }
    }
}
