package com.timeflow.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.ai.model.*
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.data.repository.AiTaskRepository
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.UserPreferenceRepository
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.AIAssistantTaskCreatedEvent
import com.timeflow.app.util.TaskRefreshEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import android.content.Context
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStreamReader
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import android.content.Intent
import android.widget.Toast
import androidx.core.content.FileProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import android.util.Log
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.Flow
import com.timeflow.app.data.ai.model.AiConfig
import com.timeflow.app.data.ai.model.AiSettings
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import kotlin.math.pow

/**
 * AI助手界面状态
 */
data class AiAssistantUiState(
    val isLoading: Boolean = false,
    val isProcessing: Boolean = false,
    val currentTask: Task? = null,
    val decomposition: AiTaskDecomposition? = null,
    val timeEstimation: AiTimeEstimation? = null,
    val recommendedTimeSlots: List<TimeSlotRecommendation> = emptyList(),
    val currentConversation: AiConversation? = null,
    val error: String? = null,
    val isAiPopupVisible: Boolean = false
)

// DataStore的key常量
private val AI_CONFIGS_KEY = stringPreferencesKey("ai_configs")
private val SELECTED_CONFIG_ID_KEY = stringPreferencesKey("selected_config_id")
private val AI_SETTINGS_KEY = stringPreferencesKey("ai_settings")

/**
 * AI助手ViewModle - 处理人工智能任务管理功能
 */
@HiltViewModel
class AiAssistantViewModel @Inject constructor(
    private val aiTaskRepository: AiTaskRepository,
    private val taskRepository: TaskRepository,
    private val userPreferenceRepository: UserPreferenceRepository
) : ViewModel() {

    // UI状态
    private val _uiState = MutableStateFlow(AiAssistantUiState())
    val uiState: StateFlow<AiAssistantUiState> = _uiState.asStateFlow()
    
    // 用户ID - 实际应用中应从认证系统获取
    private val userId = "current_user"
    
    // 当前打开的对话ID
    private var currentConversationId: String? = null
    
    // 消息列表
    private val _messages = MutableStateFlow<List<ConversationMessage>>(emptyList())
    val messages: StateFlow<List<ConversationMessage>> = _messages.asStateFlow()
    
    // 任务列表
    private val _tasks = MutableStateFlow<List<Task>>(emptyList())
    val tasks: StateFlow<List<Task>> = _tasks.asStateFlow()
    
    // 获取当前打开的对话
    private fun getCurrentConversation() {
        viewModelScope.launch {
            val conversations = aiTaskRepository.getUserConversations(userId).first()
            if (conversations.isEmpty()) {
                // 没有现有对话时创建新对话
                val newConversation = aiTaskRepository.createConversation(userId)
                currentConversationId = newConversation.id
                _uiState.update { it.copy(currentConversation = newConversation) }
                _messages.value = newConversation.messages
            } else {
                // 使用最近的对话
                val latestConversation = conversations.maxByOrNull { it.updatedAt }
                currentConversationId = latestConversation?.id
                _uiState.update { it.copy(currentConversation = latestConversation) }
                
                // 加载对话消息
                currentConversationId?.let { conversationId ->
                    val conversationMessages = aiTaskRepository.getConversationMessages(conversationId).first()
                    _messages.value = conversationMessages
                }
            }
        }
    }
    
    // 初始化ViewModel
    init {
        getCurrentConversation()
        loadTasks()
    }
    
    /**
     * 加载任务列表
     */
    private fun loadTasks() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                val allTasks = taskRepository.getAllTasks()
                
                // 过滤出未完成的任务
                val incompleteTasks = allTasks.filter { !it.isCompleted }
                _tasks.value = incompleteTasks
                
                _uiState.update { it.copy(isLoading = false) }
            } catch (e: Exception) {
                Timber.e(e, "加载任务失败")
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载任务失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 发送消息并获取AI响应
     * @param message 用户消息内容
     * @param type 消息类型，默认为TEXT
     * @param relatedTaskId 关联的任务ID（可选）
     * @param relatedTaskTitle 关联的任务标题（可选）
     */
    fun sendMessage(
        message: String,
        type: MessageType = MessageType.TEXT,
        relatedTaskId: String? = null,
        relatedTaskTitle: String? = null
    ) {
        if (message.isBlank()) {
            _uiState.update { it.copy(error = "消息不能为空") }
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true, error = null) }
                
                // 添加用户消息到消息列表
                val userMessage = ConversationMessage(
                    content = message,
                    sender = MessageSender.USER,
                    messageType = type,
                    relatedTaskId = relatedTaskId,
                    relatedTaskTitle = relatedTaskTitle
                )
                _messages.value = _messages.value + userMessage
                
                // 获取当前消息历史，提供给API调用
                val messageHistory = _messages.value
                
                Timber.d("发送消息: $message, 类型: $type")
                
                // 在IO线程执行API调用
                withContext(Dispatchers.IO) {
                    try {
                        // 添加重试机制和更友好的错误处理
                        var retryCount = 0
                        val maxRetries = 2
                        var lastError: Throwable? = null
                        
                        while (retryCount <= maxRetries) {
                            try {
                                // 调用AI API获取响应
                                val response = aiTaskRepository.processUserMessage(
                                    conversationId = getCurrentConversationId(),
                                    userMessage = message,
                                    messageHistory = messageHistory,
                                    relatedTaskId = relatedTaskId,
                                    relatedTaskTitle = relatedTaskTitle
                                )
                                
                                // 成功获取响应，更新消息列表
                                val aiMessage = response.messages.last()
                                _messages.value = response.messages
                                
                                // 处理成功，跳出重试循环
                                break
                                
                            } catch (e: IOException) {
                                lastError = e
                                
                                // 判断是否应该重试
                                if (shouldRetryError(e) && retryCount < maxRetries) {
                                    retryCount++
                                    // 记录重试信息
                                    Timber.w("API调用失败，正在尝试重试 (${retryCount}/${maxRetries}): ${e.message}")
                                    
                                    // 添加临时等待消息
                                    withContext(Dispatchers.Main) {
                                        _uiState.update { it.copy(
                                            isProcessing = true,
                                            error = "遇到网络问题，正在重试... (${retryCount}/${maxRetries})"
                                        )}
                                    }
                                    
                                    // 使用指数退避策略延迟重试
                                    val backoffDelay = (2.0.pow(retryCount.toDouble()) * 500).toLong().coerceAtMost(5000)
                                    delay(backoffDelay)
                                    continue
                                } else {
                                    // 不应重试或重试用尽
                                    throw e
                                }
                            }
                        }
                        
                    } catch (e: Exception) {
                        Timber.e(e, "处理消息失败")
                        
                        // 创建友好的错误消息
                        val errorMessage = when {
                            e.message?.contains("404") == true -> createDetailedError404Message(e)
                            e.message?.contains("timeout") == true || e.message?.contains("超时") == true -> 
                                "AI服务响应超时。可能原因：\n• 网络连接较慢\n• 服务器负载过高\n• AI模型处理复杂问题需要更多时间\n\n请稍后重试或简化您的问题。"
                            e.message?.contains("connect") == true -> 
                                "无法连接到AI服务器。可能原因：\n• 网络连接不稳定或断开\n• AI服务提供商服务暂时不可用\n• 服务器URL配置可能有误\n\n请检查网络连接后重试。"
                            else -> "AI响应错误: ${e.message ?: "未知错误"}"
                        }
                        
                        withContext(Dispatchers.Main) {
                            // 添加错误消息到对话中，让用户直接看到
                            val aiErrorMessage = ConversationMessage(
                                content = errorMessage,
                                sender = MessageSender.AI,
                                messageType = MessageType.ERROR,
                                timestamp = LocalDateTime.now(),
                                relatedTaskId = relatedTaskId,
                                relatedTaskTitle = relatedTaskTitle
                            )
                            _messages.value = _messages.value + aiErrorMessage
                            
                            // 更新UI状态，移除全局错误提示（因为已经在对话中显示）
                            _uiState.update { it.copy(
                                isProcessing = false,
                                error = null
                            )}
                        }
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "发送消息失败")
                _uiState.update { it.copy(
                    isProcessing = false,
                    error = "发送失败: ${e.message}"
                )}
            } finally {
                _uiState.update { it.copy(isProcessing = false) }
            }
        }
    }
    
    /**
     * 创建更详细的404错误消息
     */
    private fun createDetailedError404Message(e: Exception): String {
        val baseMessage = "API错误: 404 - 无法连接到AI服务"
        val details = e.message?.let { 
            if (it.length > 100) it.substring(0, 100) + "..." else it 
        } ?: "未提供详细信息"
        
        return """
        $baseMessage
        
        可能原因：
        • AI服务的URL配置不正确
        • AI服务暂时不可用
        • 您的网络无法访问该服务
        
        建议解决方案：
        1. 检查AI配置中的服务器URL是否正确
        2. 确认API密钥是否有效
        3. 检查网络连接是否正常
        4. 尝试更换其他AI模型提供商
        """
    }
    
    /**
     * 判断是否应该重试该错误
     */
    private fun shouldRetryError(error: Throwable): Boolean {
        val errorMessage = error.message?.lowercase() ?: ""
        return when {
            // 典型的网络问题，应该重试
            errorMessage.contains("timeout") -> true
            errorMessage.contains("connection") -> true
            errorMessage.contains("reset") -> true
            errorMessage.contains("interrupted") -> true
            // 服务器临时错误，应该重试
            errorMessage.contains("500") -> true
            errorMessage.contains("502") -> true
            errorMessage.contains("503") -> true
            errorMessage.contains("504") -> true
            // 频率限制，可以重试
            errorMessage.contains("429") -> true
            errorMessage.contains("rate") -> true
            errorMessage.contains("limit") -> true
            // 默认不重试
            else -> false
        }
    }
    
    /**
     * 为任务生成子任务建议
     * 返回子任务标题列表
     */
    private fun generateSubTaskSuggestions(taskId: String): List<String> {
        // 在实际应用中，这里应该调用AI API来生成子任务
        // 这里使用模拟数据
        val task = _uiState.value.currentTask
        
        val subTaskTitles = mutableListOf<String>()
        
        // 根据任务标题生成不同的子任务
        if (task?.title?.contains("报告") == true || task?.title?.contains("文档") == true) {
            subTaskTitles.add("收集相关资料")
            subTaskTitles.add("撰写报告大纲")
            subTaskTitles.add("完成初稿")
            subTaskTitles.add("校对修改")
        } else if (task?.title?.contains("项目") == true || task?.title?.contains("开发") == true) {
            subTaskTitles.add("需求分析")
            subTaskTitles.add("设计方案")
            subTaskTitles.add("开发实现")
            subTaskTitles.add("测试验收")
        } else if (task?.title?.contains("会议") == true || task?.title?.contains("讨论") == true) {
            subTaskTitles.add("准备会议议程")
            subTaskTitles.add("发送会议邀请")
            subTaskTitles.add("准备相关材料")
            subTaskTitles.add("会议记录整理")
        } else {
            // 默认子任务
            subTaskTitles.add("准备阶段")
            subTaskTitles.add("执行阶段")
            subTaskTitles.add("收尾总结")
        }
        
        return subTaskTitles
    }
    
    /**
     * 将子任务保存到原任务中
     * 返回创建的子任务列表
     */
    private suspend fun saveSubTasksToTask(parentId: String, subTaskTitles: List<String>): List<Task> {
        // 在外层函数捕获可能的数据库异常
        return try {
            // 确保在IO调度器上执行所有数据库操作
            withContext(Dispatchers.IO) {
                // 获取已有的子任务，检查是否有重复
                val existingSubTasks = taskRepository.getSubTasks(parentId)
                Log.d("AiAssistantViewModel", "父任务 $parentId 已有 ${existingSubTasks.size} 个子任务")
                
                // 过滤掉重复的子任务标题
                val existingTitles = existingSubTasks.map { it.title.lowercase() }
                val newSubtaskTitles = subTaskTitles.filter { newTitle ->
                    val isDuplicate = existingTitles.any { it.equals(newTitle.lowercase(), ignoreCase = true) }
                    if (isDuplicate) {
                        Log.w("AiAssistantViewModel", "跳过已存在的子任务: $newTitle")
                    }
                    !isDuplicate
                }
                
                // 如果所有子任务都已存在，提前返回已有的子任务列表，避免空结果
                if (newSubtaskTitles.isEmpty()) {
                    Log.d("AiAssistantViewModel", "所有子任务已存在，无需添加")
                    return@withContext existingSubTasks
                }
                
                // 使用更强健的父任务检查和错误恢复
                val parentTask = try {
                    // 先查询父任务是否存在
                    taskRepository.getTaskById(parentId)
                } catch (e: Exception) {
                    Timber.e(e, "查询父任务时出错: $parentId")
                    null
                }
                
                // 如果父任务不存在，提前返回空列表
                if (parentTask == null) {
                    Timber.e("找不到父任务: $parentId，无法创建子任务")
                    throw IllegalArgumentException("找不到父任务，ID: $parentId")
                }
                
                // 检查父任务状态
                if (parentTask.isCompleted) {
                    Timber.w("任务已完成，不应添加子任务: $parentId")
                    throw IllegalStateException("不能为已完成的任务添加子任务")
                }
                
                Log.d("AiAssistantViewModel", "过滤后需要添加 ${newSubtaskTitles.size} 个新子任务")
                
                // 创建子任务集合
                val subTasks = mutableListOf<Task>()
                val now = LocalDateTime.now()
                
                // 尝试更新父任务，但不要让失败阻止子任务创建
                try {
                    // 设置父任务的hasSubtasks标志
                    val updatedParentTask = parentTask.copy(hasSubtasks = true)
                    taskRepository.updateTask(updatedParentTask)
                    Timber.d("成功更新父任务: $parentId, hasSubtasks = true")
                } catch (e: Exception) {
                    // 记录错误但继续执行子任务创建流程
                    Timber.e(e, "更新父任务时出错: $parentId，但将继续创建子任务")
                    // 不再抛出异常中断流程
                }
                
                // 添加延迟确保更新操作已提交
                delay(100)
                
                // 逐个创建并保存子任务
                newSubtaskTitles.forEachIndexed { index, title ->
                    try {
                        // 创建子任务对象
                        val subTask = Task(
                            id = UUID.randomUUID().toString(),
                            title = title,
                            description = "由AI助手自动创建的子任务",
                            createdAt = now,
                            updatedAt = now,
                            parentTaskId = parentId,
                            depth = (parentTask.depth ?: 0) + 1, // 防止空值
                            type = TaskType.AI,
                            status = "待办",
                            estimatedTimeMinutes = 60, // 默认估计时间1小时
                            // 如果父任务有截止日期，设置子任务截止日期为稍早一些
                            dueDate = parentTask.dueDate?.minusDays((newSubtaskTitles.size - index).toLong())
                        )
                        
                        // 保存子任务到数据库
                        taskRepository.saveTask(subTask)
                        Log.d("AiAssistantViewModel", "成功创建子任务: ${subTask.id} for 父任务: $parentId")
                        
                        // 发送AI助手创建子任务事件
                        NotificationCenter.post(AIAssistantTaskCreatedEvent(parentId, subTask.id))
                        
                        // 添加到返回列表
                        subTasks.add(subTask)
                        
                        // 添加短延迟，避免数据库操作过于频繁
                        delay(50)
                    } catch (e: Exception) {
                        // 记录具体子任务创建失败的异常
                        Timber.e(e, "创建子任务'$title'失败")
                        // 继续尝试创建其他子任务，不中断整个过程
                    }
                }
                
                // 3. 合并新创建的子任务和已有的子任务，确保返回完整的子任务列表
                val allSubTasks = subTasks + existingSubTasks
                
                // 如果没能创建任何子任务，但我们知道父任务是有效的，则可能是其他原因导致创建失败
                if (subTasks.isEmpty() && existingSubTasks.isEmpty()) {
                    Timber.w("未能为有效父任务创建任何子任务: $parentId")
                    throw IllegalStateException("未能成功创建任何子任务，请检查数据库权限")
                }
                
                return@withContext allSubTasks
            }
        } catch (e: Exception) {
            // 将各种异常都转换为包含详细信息的运行时异常
            val errorMessage = when {
                e.message?.contains("FOREIGN KEY constraint failed") == true -> 
                    "创建子任务失败：父任务关联错误 (ID: $parentId)，请确认任务是否存在于数据库中"
                e is IllegalArgumentException -> 
                    e.message ?: "父任务不存在"
                e is IllegalStateException -> 
                    e.message ?: "父任务状态错误"
                else -> 
                    "创建子任务失败: ${e.localizedMessage}"
            }
            
            Timber.e(e, errorMessage)
            
            // 返回空列表而不是抛出异常，让调用方能够优雅地处理
            emptyList()
        }
    }
    
    /**
     * 通过自然语言创建任务
     */
    fun createTaskFromText(text: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true) }
                
                val task = aiTaskRepository.createTaskFromNaturalLanguage(userId, text)
                _tasks.value = _tasks.value + task
                
                // 将创建的任务告知用户
                currentConversationId?.let { conversationId ->
                    val systemMessage = ConversationMessage(
                        content = "我已经创建了一个新任务:「${task.title}」",
                        sender = MessageSender.AI,
                        messageType = MessageType.TASK_CARD,
                        relatedTaskId = task.id
                    )
                    
                    val updatedConversation = aiTaskRepository.addMessage(conversationId, systemMessage)
                    _uiState.update { it.copy(currentConversation = updatedConversation) }
                    _messages.value = updatedConversation.messages
                }
                
                _uiState.update { it.copy(isProcessing = false) }
            } catch (e: Exception) {
                Timber.e(e, "通过自然语言创建任务失败")
                _uiState.update { 
                    it.copy(
                        isProcessing = false,
                        error = "创建任务失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 获取任务AI智能拆解结果（异步，自动更新UI状态）
     */
    fun getTaskDecomposition(taskId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true) }
                // 获取任务详情
                val task = taskRepository.getTaskById(taskId)
                _uiState.update { it.copy(currentTask = task) }
                // 查看是否已有拆分结果
                val existingDecomposition = aiTaskRepository.getTaskDecomposition(taskId).first()
                if (existingDecomposition != null) {
                    // 使用已有的拆分结果
                    _uiState.update { it.copy(decomposition = existingDecomposition) }
                } else {
                    // 调用AI API进行智能拆解
                    if (task != null) {
                        val subTaskTitles = aiGenerateSubtasks(task)
                        // 构造AI子任务对象
                        val subTasks = subTaskTitles.mapIndexed { idx, title ->
                            AiSubTask(
                                title = title,
                                description = "由AI助手自动生成的子任务",
                                order = idx + 1
                            )
                        }
                        // 创建AI拆分
                        val decomposition = aiTaskRepository.createTaskDecomposition(
                            taskId = taskId,
                            subTasks = subTasks,
                            aiRecommendation = "建议按照拆分的步骤依次完成，注意第一步需要先完成，因为后续步骤依赖于它",
                            reasonForDecomposition = "该任务较为复杂，拆分为更小的步骤有助于更好地管理和完成"
                        )
                        _uiState.update { it.copy(decomposition = decomposition) }
                    }
                }
                _uiState.update { it.copy(isProcessing = false) }
            } catch (e: Exception) {
                Timber.e(e, "处理AI响应失败")
                _uiState.update {
                    it.copy(
                        isProcessing = false,
                        error = "处理AI响应失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 应用AI生成的任务分解，将子任务添加到指定任务中
     * @param taskId 父任务ID
     */
    fun applyTaskDecomposition(taskId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isProcessing = true, error = null) }
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task == null) {
                    _uiState.update { it.copy(isProcessing = false, error = "无法找到任务: $taskId") }
                    Log.w("AiAssistantViewModel", "Failed to apply task decomposition: Task not found with ID $taskId")
                    return@launch
                }
                val taskPrompt = loadTaskAndGeneratePrompt(taskId)
                if (taskPrompt == null || taskPrompt.toString().isBlank()) {
                    _uiState.update { it.copy(isProcessing = false, error = "无法生成任务分解") }
                    Log.w("AiAssistantViewModel", "Failed to apply task decomposition: Cannot generate task prompt")
                    return@launch
                }
                // 统一调用AI API
                val subtaskTitles = aiGenerateSubtasks(task)
                if (subtaskTitles.isNullOrEmpty()) {
                    _uiState.update { it.copy(isProcessing = false, error = "未能生成子任务") }
                    Log.w("AiAssistantViewModel", "Failed to apply task decomposition: No subtasks generated")
                    return@launch
                }
                val existingSubTasks = withContext(Dispatchers.IO) { taskRepository.getSubTasks(taskId) }
                val existingTitles = existingSubTasks.map { it.title.lowercase() }
                val newSubtaskTitles = subtaskTitles.filter { newTitle ->
                    val isDuplicate = existingTitles.any { it.equals(newTitle.lowercase(), ignoreCase = true) }
                    if (isDuplicate) Log.w("AiAssistantViewModel", "跳过已存在的子任务: $newTitle")
                    !isDuplicate
                }
                if (newSubtaskTitles.isEmpty()) {
                    Log.d("AiAssistantViewModel", "所有子任务已存在，无需添加")
                    _uiState.update { it.copy(isProcessing = false) }
                    return@launch
                }
                var addedSubtasks = false
                withContext(Dispatchers.IO) {
                    try {
                        taskRepository.addSubtasksInTransaction(taskId, newSubtaskTitles.map { subtaskTitle ->
                            Task(
                                id = UUID.randomUUID().toString(),
                                title = subtaskTitle,
                                description = "由AI助手生成的子任务",
                                createdAt = LocalDateTime.now(),
                                updatedAt = LocalDateTime.now(),
                                parentTaskId = taskId,
                                depth = (task.depth ?: 0) + 1,
                                type = TaskType.AI,
                                status = "待办"
                            )
                        })
                        addedSubtasks = true
                        Log.d("AiAssistantViewModel", "Successfully added "+newSubtaskTitles.size+" subtasks to task $taskId")
                    } catch (e: Exception) {
                        Log.e("AiAssistantViewModel", "Error adding subtasks for task $taskId", e)
                        throw e
                    }
                    try {
                        val updatedTask = task.copy(hasSubtasks = true)
                        taskRepository.updateTask(updatedTask)
                    } catch (e: Exception) {
                        Log.e("AiAssistantViewModel", "Failed to update parent task hasSubtasks property: $taskId", e)
                    }
                }
                if (addedSubtasks) {
                    _uiState.update { it.copy(isProcessing = false) }
                    Log.d("AiAssistantViewModel", "Task decomposition applied successfully for task $taskId")
                } else {
                    throw Exception("Failed to add subtasks")
                }
            } catch (e: Exception) {
                Log.e("AiAssistantViewModel", "Error applying task decomposition for task $taskId", e)
                _uiState.update { it.copy(isProcessing = false, error = "添加子任务失败: ${e.localizedMessage ?: "未知错误"}") }
            }
        }
    }
    
    /**
     * 获取任务时间预估
     */
    fun getTimeEstimation(taskId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true) }
                
                // 查看是否已有时间预估
                val existingEstimation = aiTaskRepository.getTimeEstimation(taskId).first()
                
                if (existingEstimation != null) {
                    // 使用已有的时间预估
                    _uiState.update { it.copy(timeEstimation = existingEstimation) }
                } else {
                    // 创建新的时间预估
                    val task = taskRepository.getTaskById(taskId)
                    task?.let {
                        // 模拟计算估计时间
                        val estimatedMinutes = (task.title.length * 5).coerceIn(30, 180)
                        
                        // 创建参考任务
                        val similarTasks = listOf(
                            SimilarTaskReference(
                                taskId = "similar_task_1",
                                taskTitle = "类似任务1",
                                actualMinutes = estimatedMinutes - 15,
                                similarity = 0.8f,
                                completedAt = LocalDateTime.now().minusDays(5)
                            ),
                            SimilarTaskReference(
                                taskId = "similar_task_2",
                                taskTitle = "类似任务2",
                                actualMinutes = estimatedMinutes + 10,
                                similarity = 0.75f,
                                completedAt = LocalDateTime.now().minusDays(10)
                            )
                        )
                        
                        // 创建调整因素
                        val adjustmentFactors = listOf(
                            AdjustmentFactor(
                                name = "任务复杂度",
                                impact = 0.2f,
                                description = "这个任务比平均任务稍复杂，需要考虑额外时间"
                            ),
                            AdjustmentFactor(
                                name = "用户历史效率",
                                impact = -0.1f,
                                description = "你在这类任务上通常比平均速度快10%"
                            )
                        )
                        
                        // 创建时间预估
                        val timeEstimation = aiTaskRepository.createTimeEstimation(
                            taskId = taskId,
                            estimatedMinutes = estimatedMinutes,
                            confidence = 0.85f,
                            basis = "基于你完成的2个类似任务以及任务的复杂度评估",
                            similarTasks = similarTasks,
                            adjustmentFactors = adjustmentFactors
                        )
                        
                        _uiState.update { it.copy(timeEstimation = timeEstimation) }
                    }
                }
                
                _uiState.update { it.copy(isProcessing = false) }
            } catch (e: Exception) {
                Timber.e(e, "获取时间预估失败")
                _uiState.update { 
                    it.copy(
                        isProcessing = false,
                        error = "获取时间预估失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 获取推荐的任务时间段
     */
    fun getRecommendedTimeSlots(taskId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true) }
                
                // 获取未来7天的时间段建议
                val now = LocalDateTime.now()
                val endDate = now.plusDays(7)
                val recommendations = aiTaskRepository.getRecommendedTimeSlots(taskId, now, endDate).first()
                
                _uiState.update { it.copy(
                    recommendedTimeSlots = recommendations,
                    isProcessing = false
                ) }
            } catch (e: Exception) {
                Timber.e(e, "获取推荐时间段失败")
                _uiState.update { 
                    it.copy(
                        isProcessing = false,
                        error = "获取推荐时间段失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 生成最佳任务计划
     */
    fun generateOptimalSchedule() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true) }
                
                // 为今天生成最佳任务计划
                val now = LocalDateTime.now()
                val scheduledTasks = aiTaskRepository.generateOptimalTaskSchedule(userId, now)
                
                // 更新任务列表
                loadTasks()
                
                // 显示成功消息
                currentConversationId?.let { conversationId ->
                    val systemMessage = ConversationMessage(
                        content = "我已为你安排了今天的最佳任务计划，包含${scheduledTasks.size}个任务",
                        sender = MessageSender.AI,
                        messageType = MessageType.TEXT
                    )
                    
                    val updatedConversation = aiTaskRepository.addMessage(conversationId, systemMessage)
                    _uiState.update { it.copy(currentConversation = updatedConversation) }
                    _messages.value = updatedConversation.messages
                }
                
                _uiState.update { it.copy(isProcessing = false) }
            } catch (e: Exception) {
                Timber.e(e, "生成最佳任务计划失败")
                _uiState.update { 
                    it.copy(
                        isProcessing = false,
                        error = "生成最佳任务计划失败: ${e.localizedMessage}"
                    )
                }
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    /**
     * 切换AI弹窗可见性
     */
    fun toggleAiPopup(show: Boolean) {
        _uiState.update { it.copy(isAiPopupVisible = show) }
    }
    
    // -------------------- 辅助方法 --------------------
    
    /**
     * 使用AI API智能拆解任务，返回最多5个子任务标题
     */
    suspend fun aiGenerateSubtasks(task: Task): List<String> {
        return try {
            // 调用Repository层的AI API方法
            if (aiTaskRepository is com.timeflow.app.data.repository.AiTaskRepositoryImpl) {
                aiTaskRepository.aiSplitTaskToSubtasks(task)
            } else {
                // 兼容其他实现，返回空
                emptyList()
            }
        } catch (e: Exception) {
            Timber.e(e, "AI任务拆解失败")
            emptyList()
        }
    }

    // 新增方法：导出对话内容
    fun exportConversation(context: Context) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 获取当前对话内容
                currentConversationId?.let { conversationId ->
                    // 使用getConversationMessages方法获取消息
                    val messages = aiTaskRepository.getConversationMessages(conversationId).first()
                    if (messages.isNotEmpty()) {
                        // 构建导出内容，优化格式布局
                        val exportContent = buildString {
                            append("==========================================\n")
                            append("TimeFlow AI对话记录\n")
                            append("导出时间: ${LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}\n")
                            append("==========================================\n\n")
                            
                            messages.forEach { message ->
                                val sender = if (message.sender == MessageSender.USER) "用户" else "AI助手"
                                val timeFormatted = message.timestamp.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                append("【$sender】$timeFormatted\n")
                                append("----------------------------------------\n")
                                append("${message.content}\n")
                                append("----------------------------------------\n\n")
                            }
                            
                            // 添加统计信息
                            val userMessages = messages.count { it.sender == MessageSender.USER }
                            val aiMessages = messages.count { it.sender == MessageSender.AI }
                            
                            append("\n==========================================\n")
                            append("统计信息\n")
                            append("总消息数: ${messages.size}\n")
                            append("用户消息: $userMessages\n")
                            append("AI回复: $aiMessages\n")
                            if (messages.isNotEmpty()) {
                                val firstTime = messages.first().timestamp
                                val lastTime = messages.last().timestamp
                                append("对话时长: ${java.time.Duration.between(firstTime, lastTime).toMinutes()} 分钟\n")
                            }
                            append("==========================================\n")
                        }
                        
                        // 创建导出文件，使用更友好的文件名格式
                        val dateStr = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
                        val fileName = "TimeFlow对话记录_$dateStr.txt"
                        val file = File(context.cacheDir, fileName)
                        
                        // 写入内容
                        FileOutputStream(file).use { it.write(exportContent.toByteArray()) }
                        
                        withContext(Dispatchers.Main) {
                            try {
                                // 创建分享Intent
                                val uri = FileProvider.getUriForFile(
                                    context,
                                    "${context.packageName}.fileprovider",
                                    file
                                )
                                
                                val shareIntent = Intent().apply {
                                    action = Intent.ACTION_SEND
                                    putExtra(Intent.EXTRA_STREAM, uri)
                                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                    type = "text/plain"
                                }
                                
                                // 添加文件名和描述
                                shareIntent.putExtra(Intent.EXTRA_SUBJECT, "TimeFlow AI对话记录")
                                shareIntent.putExtra(Intent.EXTRA_TEXT, "以下是我与TimeFlow AI助手的对话记录")
                                
                                // 显示分享选择器
                                val chooserIntent = Intent.createChooser(
                                    shareIntent, 
                                    "分享对话记录"
                                )
                                
                                // 检查是否有应用可以处理此Intent
                                if (shareIntent.resolveActivity(context.packageManager) != null) {
                                    context.startActivity(chooserIntent)
                                    Toast.makeText(context, "准备导出对话记录", Toast.LENGTH_SHORT).show()
                                } else {
                                    Toast.makeText(context, "没有找到可以处理此分享的应用", Toast.LENGTH_SHORT).show()
                                }
                            } catch (e: Exception) {
                                Log.e("AiAssistantViewModel", "分享对话记录失败", e)
                                Toast.makeText(context, "分享对话记录失败: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(context, "当前对话没有内容可导出", Toast.LENGTH_SHORT).show()
                        }
                    }
                } ?: withContext(Dispatchers.Main) {
                    Toast.makeText(context, "没有活跃的对话", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("AiAssistantViewModel", "导出对话失败", e)
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        context,
                        "导出对话失败: ${e.localizedMessage}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }

    // 新增方法：导入文档
    fun importDocument(context: Context, uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val documentFile = DocumentFile.fromSingleUri(context, uri)
                val fileName = documentFile?.name ?: "未知文件"
                val mimeType = context.contentResolver.getType(uri)
                
                var documentContent = ""
                
                when {
                    // Excel文件
                    mimeType?.contains("spreadsheet") == true || 
                    fileName.endsWith(".xlsx", ignoreCase = true) || 
                    fileName.endsWith(".xls", ignoreCase = true) -> {
                        documentContent = readExcelFile(context, uri)
                    }
                    
                    // 文本文件
                    else -> {
                        documentContent = readTextFile(context, uri)
                    }
                }
                
                if (documentContent.isNotEmpty()) {
                    // 创建一条系统消息表示文件导入
                    val systemMessage = ConversationMessage(
                        content = "已导入文档: $fileName",
                        sender = MessageSender.AI,
                        messageType = MessageType.TEXT,
                        timestamp = LocalDateTime.now()
                    )
                    
                    // 创建一条用户消息包含文件内容
                    val userMessage = ConversationMessage(
                        content = "我导入了一个文档，内容如下:\n\n$documentContent",
                        sender = MessageSender.USER,
                        messageType = MessageType.TEXT,
                        timestamp = LocalDateTime.now()
                    )
                    
                    withContext(Dispatchers.Main) {
                        // 添加系统消息
                        _messages.update { currentMessages ->
                            currentMessages + systemMessage
                        }
                        
                        // 添加用户消息并发送
                        _messages.update { currentMessages ->
                            currentMessages + userMessage
                        }
                        
                        // 请求AI响应 - 用sendMessage替代
                        sendMessage(userMessage.content)
                        
                        Toast.makeText(context, "已导入文档：$fileName", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "无法读取文档内容", Toast.LENGTH_SHORT).show()
                        _uiState.update { it.copy(error = "无法读取文档内容") }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "导入失败: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                    _uiState.update { it.copy(error = "导入失败: ${e.localizedMessage}") }
                }
            }
        }
    }
    
    // 辅助方法：读取文本文件
    private fun readTextFile(context: Context, uri: Uri): String {
        val stringBuilder = StringBuilder()
        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            BufferedReader(InputStreamReader(inputStream)).use { reader ->
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    stringBuilder.append(line)
                    stringBuilder.append('\n')
                }
            }
        }
        return stringBuilder.toString()
    }
    
    // 辅助方法：读取Excel文件
    private fun readExcelFile(context: Context, uri: Uri): String {
        val stringBuilder = StringBuilder()
        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            try {
                val workbook = XSSFWorkbook(inputStream)
                for (i in 0 until workbook.numberOfSheets) {
                    val sheet = workbook.getSheetAt(i)
                    stringBuilder.append("Sheet ${i + 1}: ${sheet.sheetName}\n")
                    
                    for (row in sheet) {
                        for (cell in row) {
                            stringBuilder.append(cell.toString())
                            stringBuilder.append('\t')
                        }
                        stringBuilder.append('\n')
                    }
                    stringBuilder.append("\n\n")
                }
            } catch (e: Exception) {
                throw IOException("无法解析Excel文件", e)
            }
        }
        return stringBuilder.toString()
    }
    
    // 当前选中的AI模型
    private val _selectedAiModel = MutableStateFlow("DeepSeek")
    val selectedAiModel: StateFlow<String> = _selectedAiModel.asStateFlow()
    
    /**
     * 选择AI模型
     */
    fun onAiModelSelected(modelName: String) {
        _selectedAiModel.value = modelName
        
        // 添加系统消息提示用户已切换模型
        val systemMessage = ConversationMessage(
            content = "已切换到 $modelName 模型",
            sender = MessageSender.AI,
            messageType = MessageType.TEXT
        )
        
        _messages.value = _messages.value + systemMessage
        
        // 清除错误提示
        clearError()
    }

    /**
     * 加载任务并生成优化提示
     * 
     * 自动从任务ID加载任务信息，并根据任务信息生成优化提示发送到对话中
     * 此方法会在任务详情页点击"AI优化"按钮后自动调用
     */
    fun loadTaskAndGeneratePrompt(taskId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessing = true, error = null) }
                Timber.d("开始为任务ID: $taskId 生成优化提示")
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    _uiState.update { it.copy(currentTask = task) }
                    Timber.d("成功获取任务: ${task.title}")
                    val prompt = buildString {
                        append("我需要优化这个任务：\n\n")
                        append("标题：${task.title}\n")
                        append("描述：${task.description}\n")
                        task.dueDate?.let { append("截止日期：${it}\n") }
                        task.priority?.let { append("优先级：${it.name}\n") }
                        append("\n请帮我将这个任务拆解为几个有序的子任务，使其更易于执行和管理。")
                    }
                    if (currentConversationId == null) {
                        val newConversation = aiTaskRepository.createConversation(userId)
                        currentConversationId = newConversation.id
                        _uiState.update { it.copy(currentConversation = newConversation) }
                        Timber.d("创建了新对话 ID: ${newConversation.id}")
                    }
                    currentConversationId?.let { conversationId ->
                        try {
                            val userMessage = ConversationMessage(
                                content = prompt,
                                sender = MessageSender.USER,
                                messageType = MessageType.TASK_OPTIMIZATION,
                                relatedTaskId = taskId,
                                relatedTaskTitle = task.title
                            )
                            val updatedConversation = aiTaskRepository.addMessage(conversationId, userMessage)
                            _messages.value = updatedConversation.messages
                            val processingMessage = ConversationMessage(
                                content = "正在为您分析任务「${task.title}」，并生成优化方案...",
                                sender = MessageSender.AI,
                                messageType = MessageType.TEXT
                            )
                            val processingConversation = aiTaskRepository.addMessage(conversationId, processingMessage)
                            _messages.value = processingConversation.messages
                            delay(1000)
                            Timber.d("开始为任务生成子任务建议")
                            
                            // 统一调用AI API，增加错误处理
                            val subTaskTitles = try {
                                aiGenerateSubtasks(task)
                            } catch (e: Exception) {
                                val errorMsg = when {
                                    e.message?.contains("404") == true -> "AI服务端点未找到 (404)。请在设置中检查API服务器URL是否正确。"
                                    e.message?.contains("401") == true -> "API密钥无效 (401)。请在设置中更新您的API密钥。"
                                    e.message?.contains("429") == true -> "API请求过多 (429)。您的账户可能已超出使用限制。"
                                    e.message?.contains("network") == true || e.message?.contains("超时") == true -> "网络连接问题。请检查网络后重试。"
                                    else -> "AI服务调用失败: ${e.localizedMessage}"
                                }
                                
                                val errorMessage = ConversationMessage(
                                    content = "抱歉，处理您的请求时遇到了问题：$errorMsg 请稍后重试。",
                                    sender = MessageSender.AI,
                                    messageType = MessageType.ERROR
                                )
                                
                                val errorConversation = aiTaskRepository.addMessage(conversationId, errorMessage)
                                _messages.value = errorConversation.messages
                                _uiState.update { it.copy(isProcessing = false) }
                                
                                Timber.e(e, "AI生成子任务失败")
                                return@launch
                            }
                            
                            Timber.d("生成了 ${subTaskTitles.size} 个子任务建议")
                            
                            // 如果未能生成任何子任务
                            if (subTaskTitles.isEmpty()) {
                                val emptyResultMessage = ConversationMessage(
                                    content = "抱歉，我无法为这个任务生成合适的子任务。这可能是因为任务描述过于简单或不够明确。您可以尝试添加更多任务细节后再试。",
                                    sender = MessageSender.AI,
                                    messageType = MessageType.TEXT
                                )
                                
                                val emptyConversation = aiTaskRepository.addMessage(conversationId, emptyResultMessage)
                                _messages.value = emptyConversation.messages
                                _uiState.update { it.copy(isProcessing = false) }
                                return@launch
                            }
                            
                            var savedSubTasks = emptyList<Task>()
                            val verifiedTask = withContext(Dispatchers.IO) { taskRepository.getTaskById(taskId) }
                            if (verifiedTask != null && !verifiedTask.isCompleted) {
                                try {
                                    savedSubTasks = withContext(Dispatchers.IO) { saveSubTasksToTask(taskId, subTaskTitles) }
                                    Timber.d("成功创建 ${savedSubTasks.size} 个子任务")
                                    if (savedSubTasks.isNotEmpty()) {
                                        try {
                                            Timber.d("发送任务刷新事件: $taskId")
                                            NotificationCenter.post(TaskRefreshEvent(taskId))
                                        } catch (e: Exception) {
                                            Timber.e(e, "发送任务刷新事件失败，但继续处理")
                                        }
                                    }
                                } catch (e: Exception) {
                                    Timber.e(e, "创建子任务时出现异常，继续处理但不保存子任务")
                                }
                            } else if (verifiedTask == null) {
                                Timber.w("任务不存在，无法创建子任务: $taskId")
                            }
                            val responseContent = buildString {
                                if (savedSubTasks.isNotEmpty()) {
                                    append("我已经将任务「${task.title}」拆分为以下子任务：\n\n")
                                    savedSubTasks.forEachIndexed { index, subTask ->
                                        append("${index + 1}. ${subTask.title}\n")
                                    }
                                    append("\n这些子任务已自动添加到原任务中，您可以在任务详情页查看和管理。\n")
                                    append("建议您按照这个顺序依次完成各个子任务，这样可以更高效地达成目标。")
                                } else {
                                    append("我建议将任务「${task.title}」拆分为以下子任务：\n\n")
                                    subTaskTitles.forEachIndexed { index, title ->
                                        append("${index + 1}. $title\n")
                                    }
                                    append("\n由于系统原因，这些子任务未能自动添加到原任务中。您可以在任务详情页手动添加这些子任务。")
                                }
                            }
                            val aiResponseMessage = ConversationMessage(
                                content = responseContent,
                                sender = MessageSender.AI,
                                messageType = MessageType.SUBTASKS,
                                relatedTaskId = taskId,
                                relatedTaskTitle = task.title
                            )
                            val finalConversation = aiTaskRepository.addMessage(conversationId, aiResponseMessage)
                            _messages.value = finalConversation.messages
                            try {
                                if (savedSubTasks.isNotEmpty()) {
                                    delay(300)
                                    Timber.d("发送最终任务刷新事件: $taskId")
                                    NotificationCenter.post(TaskRefreshEvent(taskId))
                                }
                            } catch (e: Exception) {
                                Timber.e(e, "发送最终任务刷新事件失败")
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "发送消息到对话时出错")
                            // 为用户提供友好的错误消息
                            val friendlyErrorMsg = when {
                                e.message?.contains("API错误: 404") == true -> "AI服务连接失败。请在设置中检查API服务器URL是否正确，确保您的网络连接正常。"
                                e.message?.contains("API密钥") == true -> "API密钥验证失败。请在设置中检查您的API密钥。"
                                e.message?.contains("network") == true || e.message?.contains("超时") == true -> "网络连接问题。请检查网络后重试。"
                                else -> "处理请求时遇到技术问题: ${e.message?.take(100) ?: "未知错误"}。请稍后再试。"
                            }
                            val errorMessage = ConversationMessage(
                                content = "抱歉，$friendlyErrorMsg",
                                sender = MessageSender.AI,
                                messageType = MessageType.ERROR
                            )
                            try {
                                val errorConversation = aiTaskRepository.addMessage(conversationId, errorMessage)
                                _messages.value = errorConversation.messages
                            } catch (msgError: Exception) {
                                Timber.e(msgError, "添加错误消息到对话失败")
                            }
                        }
                    }
                } else {
                    val errorMsg = "未找到指定任务ID: $taskId"
                    Timber.w(errorMsg)
                    _uiState.update { it.copy(error = errorMsg) }
                    currentConversationId?.let { conversationId ->
                        val errorMessage = ConversationMessage(
                            content = "抱歉，未能找到ID为 '$taskId' 的任务信息。请确认任务是否存在。",
                            sender = MessageSender.AI,
                            messageType = MessageType.ERROR
                        )
                        val updatedConversation = aiTaskRepository.addMessage(conversationId, errorMessage)
                        _messages.value = updatedConversation.messages
                    }
                }
                _uiState.update { it.copy(isProcessing = false) }
            } catch (e: Exception) {
                val errorMsg = "处理任务失败: ${e.localizedMessage}"
                Timber.e(e, errorMsg)
                _uiState.update { it.copy(isProcessing = false, error = errorMsg) }
                currentConversationId?.let { conversationId ->
                    val friendlyErrorMsg = when {
                        e.message?.contains("404") == true -> "AI服务连接失败 (404)。请在设置中检查API服务器URL是否正确。"
                        e.message?.contains("401") == true -> "API密钥无效 (401)。请在设置中更新您的API密钥。"
                        e.message?.contains("429") == true -> "API请求过多 (429)。您的账户可能已超出使用限制，请稍后再试。"
                        e.message?.contains("network") == true || e.message?.contains("超时") == true -> "网络连接问题。请检查您的网络连接后重试。"
                        else -> "处理请求时遇到问题: ${e.message?.take(100) ?: "未知错误"}。请稍后再试。"
                    }
                    val errorMessage = ConversationMessage(
                        content = "抱歉，$friendlyErrorMsg",
                        sender = MessageSender.AI,
                        messageType = MessageType.ERROR
                    )
                    try {
                        val updatedConversation = aiTaskRepository.addMessage(conversationId, errorMessage)
                        _messages.value = updatedConversation.messages
                    } catch (messageError: Exception) {
                        Timber.e(messageError, "无法添加错误消息到对话")
                    }
                }
            }
        }
    }

    /**
     * 生成AI响应文本
     * 根据用户输入生成相关的回复
     */
    private fun generateAiResponse(userText: String): String {
        // 此方法已被弃用，改为调用API获取专业AI响应
        return "正在处理您的请求，请稍候..."
    }

    /**
     * 检查任务是否存在
     * @param taskId 任务ID
     * @param onResult 结果回调，true表示任务存在，false表示不存在
     */
    fun checkTaskExists(taskId: String, onResult: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                onResult(task != null)
                if (task == null) {
                    Log.w("AiAssistantViewModel", "Task with ID $taskId does not exist")
                }
            } catch (e: Exception) {
                Log.e("AiAssistantViewModel", "Error checking if task exists: $taskId", e)
                onResult(false)
            }
        }
    }

    /**
     * 设置处理中状态
     */
    fun setProcessing(isProcessing: Boolean) {
        _uiState.update { it.copy(isProcessing = isProcessing) }
    }
    
    /**
     * 检查任务是否存在
     * 返回任务对象，如果找不到则返回null
     */
    suspend fun checkTaskExists(taskId: String): Task? {
        return try {
            withContext(Dispatchers.IO) {
                taskRepository.getTaskById(taskId)
            }
        } catch (e: Exception) {
            Timber.e(e, "检查任务是否存在时发生错误: $taskId")
            null
        }
    }
    
    /**
     * 添加子任务到父任务
     * 
     * @param parentId 父任务ID
     * @param subtaskTitles 子任务标题列表
     * @param onComplete 完成回调，参数为是否成功
     * @return 是否添加成功
     */
    suspend fun addSubTasksToParent(parentId: String, subtaskTitles: List<String>, onComplete: (Boolean) -> Unit = {}): Boolean {
        val result = try {
            withContext(Dispatchers.IO) {
                // 记录准备添加子任务的日志
                Log.d("AiAssistantViewModel", "准备添加 ${subtaskTitles.size} 个子任务到父任务 $parentId")
                
                // 1. 获取父任务
                val parentTask = taskRepository.getTaskById(parentId)
                if (parentTask == null) {
                    Log.e("AiAssistantViewModel", "找不到父任务: $parentId")
                    return@withContext false
                }
                
                // 2. 检查父任务状态
                if (parentTask.isCompleted) {
                    Log.w("AiAssistantViewModel", "任务已完成，不应添加子任务: $parentId")
                    return@withContext false
                }
                
                // 2.1 检查是否已有相同标题的子任务，防止重复添加
                val existingSubTasks = taskRepository.getSubTasks(parentId)
                val existingTitles = existingSubTasks.map { it.title.lowercase() }
                Log.d("AiAssistantViewModel", "父任务 $parentId 已有 ${existingSubTasks.size} 个子任务")
                
                // 过滤掉已存在的子任务标题
                val newSubtaskTitles = subtaskTitles.filter { newTitle ->
                    val isDuplicate = existingTitles.any { it.equals(newTitle.lowercase(), ignoreCase = true) }
                    if (isDuplicate) {
                        Log.w("AiAssistantViewModel", "跳过已存在的子任务: $newTitle")
                    }
                    !isDuplicate
                }
                
                // 如果所有子任务都已存在，直接返回成功
                if (newSubtaskTitles.isEmpty()) {
                    Log.d("AiAssistantViewModel", "所有子任务已存在，无需添加")
                    return@withContext true
                }
                
                Log.d("AiAssistantViewModel", "过滤后需要添加 ${newSubtaskTitles.size} 个新子任务")
                
                // 3. 创建子任务
                val now = LocalDateTime.now()
                val subTasks = newSubtaskTitles.map { title ->
                    // 明确标注子任务关联到父任务ID
                    Task(
                        id = UUID.randomUUID().toString(),
                        title = title,
                        description = "由AI助手自动创建的子任务",
                        createdAt = now,
                        updatedAt = now,
                        parentTaskId = parentId, // 确保设置了父任务ID
                        depth = (parentTask.depth ?: 0) + 1,
                        type = TaskType.AI,
                        status = "待办",
                        estimatedTimeMinutes = 60, // 默认估计时间1小时
                        // 如果父任务有截止日期，设置子任务截止日期为稍早一些
                        dueDate = parentTask.dueDate?.minusDays(1)
                    )
                }
                
                // 如果没有新的子任务需要添加，直接返回成功
                if (subTasks.isEmpty()) {
                    Log.d("AiAssistantViewModel", "没有新的子任务需要添加")
                    return@withContext true
                }
                
                Log.d("AiAssistantViewModel", "准备添加 ${subTasks.size} 个子任务到父任务 $parentId")
                
                // 4. 使用事务来确保所有操作的原子性
                try {
                    // 使用事务确保子任务添加和父任务更新是原子操作
                    taskRepository.addSubtasksInTransaction(parentId, subTasks)
                    
                    // 5. 显式更新父任务的hasSubtasks标志
                    val updatedParentTask = parentTask.copy(hasSubtasks = true)
                    taskRepository.updateTask(updatedParentTask)
                    
                    // 6. 发送全局任务刷新事件，确保UI及时更新
                    withContext(Dispatchers.Main) {
                        com.timeflow.app.util.NotificationCenter.post(com.timeflow.app.util.TaskRefreshEvent(parentId))
                        Log.d("AiAssistantViewModel", "发送了任务刷新事件: $parentId")
                    }
                    
                    Log.d("AiAssistantViewModel", "成功添加${subTasks.size}个子任务到父任务: $parentId")
                    return@withContext true
                } catch (e: Exception) {
                    Log.e("AiAssistantViewModel", "添加子任务事务时出错: $parentId", e)
                    throw e
                }
            }
        } catch (e: Exception) {
            Log.e("AiAssistantViewModel", "添加子任务时发生异常: $parentId", e)
            false
        }
        
        // 调用完成回调
        onComplete(result)
        
        return result
    }

    /**
     * 诊断AI配置
     * 检查AI配置是否正确设置，并返回诊断信息
     */
    fun diagnoseAiConfig(context: Context, onResult: (DiagnosisResult) -> Unit) {
        viewModelScope.launch {
            val result = withContext(Dispatchers.IO) {
                try {
                    val gson = Gson()
                    val diagnoseInfo = StringBuilder()
                    var configOk = false
                    
                    // 检查AI配置DataStore
                    val configDataStore = context.dataStore
                    val settingsDataStore = context.aiSettingsDataStore
                    
                    // 检查是否有选中的配置ID
                    val selectedConfigId = configDataStore.data.firstOrNull()?.get(SELECTED_CONFIG_ID_KEY)
                    diagnoseInfo.append("1. 选中的配置ID: ${if (selectedConfigId.isNullOrBlank()) "未设置" else selectedConfigId}\n")
                    
                    // 检查是否有配置列表
                    val configsJson = configDataStore.data.firstOrNull()?.get(AI_CONFIGS_KEY)
                    if (configsJson.isNullOrBlank()) {
                        diagnoseInfo.append("2. AI配置列表: 未找到\n")
                    } else {
                        val configsType = object : TypeToken<List<AiConfig>>() {}.type
                        val configs: List<AiConfig> = gson.fromJson(configsJson, configsType)
                        diagnoseInfo.append("2. AI配置列表: 包含${configs.size}个配置\n")
                        
                        // 如果有选中ID，检查配置详情
                        if (!selectedConfigId.isNullOrBlank()) {
                            val selectedConfig = configs.find { it.id == selectedConfigId }
                            if (selectedConfig == null) {
                                diagnoseInfo.append("3. 选中的配置: 未找到ID为 $selectedConfigId 的配置\n")
                            } else {
                                diagnoseInfo.append("3. 选中的配置: ${selectedConfig.name} (${selectedConfig.provider})\n")
                                diagnoseInfo.append("4. API密钥: ${if (selectedConfig.apiKey.isBlank()) "未设置" else "已设置 (${selectedConfig.apiKey.take(5)}...)"}\n")
                                diagnoseInfo.append("5. 服务器URL: ${selectedConfig.serverUrl}\n")
                                diagnoseInfo.append("6. 模型名称: ${selectedConfig.modelName}\n")
                                
                                // 检查API密钥是否有效
                                if (selectedConfig.apiKey.isNotBlank()) {
                                    configOk = true
                                }
                            }
                        }
                    }
                    
                    // 检查AI设置DataStore
                    val settingsJson = settingsDataStore.data.firstOrNull()?.get(AI_SETTINGS_KEY)
                    if (settingsJson.isNullOrBlank()) {
                        diagnoseInfo.append("7. AI设置: 未找到\n")
                    } else {
                        val settings = gson.fromJson(settingsJson, AiSettings::class.java)
                        diagnoseInfo.append("7. AI设置: 助手名称=${settings.assistantName}, 配置ID=${settings.selectedConfigId}\n")
                    }
                    
                    // 生成结论和解决方案
                    if (configOk) {
                        diagnoseInfo.append("\n✅ AI配置状态: 正常\n")
                    } else {
                        diagnoseInfo.append("\n❌ AI配置状态: 异常\n\n")
                        diagnoseInfo.append("解决方案:\n")
                        diagnoseInfo.append("1. 请前往「设置」->「AI设置」\n")
                        diagnoseInfo.append("2. 添加一个新的AI配置或编辑现有配置\n")
                        diagnoseInfo.append("3. 确保填写了有效的API密钥\n")
                        diagnoseInfo.append("4. 保存并选择该配置\n")
                    }
                    
                    DiagnosisResult(isConfigured = configOk, details = diagnoseInfo.toString())
                } catch (e: Exception) {
                    Timber.e(e, "诊断AI配置失败")
                    DiagnosisResult(
                        isConfigured = false,
                        details = "诊断过程发生错误: ${e.message}\n\n请手动检查AI设置。"
                    )
                }
            }
            
            onResult(result)
        }
    }
    
    /**
     * AI配置诊断结果
     */
    data class DiagnosisResult(
        val isConfigured: Boolean,
        val details: String
    )

    /**
     * 清空当前对话内容
     */
    fun clearConversation() {
        viewModelScope.launch {
            try {
                // 清空当前消息列表
                _messages.value = emptyList()
                
                // 如果有对话ID，创建新对话
                if (currentConversationId != null) {
                    val newConversation = aiTaskRepository.createConversation(userId)
                    currentConversationId = newConversation.id
                    _uiState.update { it.copy(currentConversation = newConversation) }
                }
                
                Timber.d("对话已清空")
            } catch (e: Exception) {
                Timber.e(e, "清空对话失败")
                _uiState.update { 
                    it.copy(error = "清空对话失败: ${e.localizedMessage}")
                }
            }
        }
    }

    /**
     * 获取当前会话ID，如果不存在则创建新会话
     */
    private suspend fun getCurrentConversationId(): String {
        // 如果当前没有会话ID，创建新会话
        if (currentConversationId == null) {
            val newConversation = aiTaskRepository.createConversation(userId)
            currentConversationId = newConversation.id
            _uiState.update { it.copy(currentConversation = newConversation) }
        }
        
        return currentConversationId ?: throw IllegalStateException("无法创建会话")
    }
} 