package com.timeflow.app.ui.screen.goal.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInHorizontally
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.ui.theme.DustyLavender
import kotlinx.coroutines.delay

/**
 * AI拆解结果界面
 * 展示AI分析的结果和执行计划
 */
@Composable
fun AiBreakdownResultScreen(
    goalTitle: String,
    onGenerateExecutionPlan: () -> Unit,
    onRegenerate: () -> Unit = {},
    modifier: Modifier = Modifier,
    isBackupMode: Boolean = false
) {
    // 动画控制
    var showCheckmark by remember { mutableStateOf(false) }
    var scaleFactor by remember { mutableStateOf(0f) }
    
    // 文字动画控制
    var textProgress by remember { mutableStateOf(0f) }
    
    // 展示动画
    LaunchedEffect(key1 = true) {
        // 首先显示勾号动画
        scaleFactor = 0f
        delay(300)
        
        // 从小到大的缩放动画
        val startTime = System.currentTimeMillis()
        val animDuration = 500 // 动画持续时间
        
        while (System.currentTimeMillis() - startTime < animDuration) {
            val progress = (System.currentTimeMillis() - startTime) / animDuration.toFloat()
            scaleFactor = progress
            delay(16) // 约60fps
        }
        
        scaleFactor = 1f
        showCheckmark = true
        
        // 然后开始显示文字
        textProgress = 0f
        val textAnimDuration = 1000 // 文字动画持续时间
        val textStartTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - textStartTime < textAnimDuration) {
            val progress = (System.currentTimeMillis() - textStartTime) / textAnimDuration.toFloat()
            textProgress = progress
            delay(16)
        }
        
        textProgress = 1f
        
        // 短暂延迟后自动继续
        delay(1000)
        onGenerateExecutionPlan()
    }
    
    // 颜色定义
    val backgroundColor = Color(0xFFF0F4FF)
    val successColor = Color(0xFF34D399) // 成功绿色
    val primaryTextColor = MaterialTheme.colorScheme.primary
    val secondaryTextColor = Color(0xFF666666)
    
    // 勾号动画值
    val animatedScale by animateFloatAsState(
        targetValue = scaleFactor,
        animationSpec = tween(
            durationMillis = 500,
            easing = FastOutSlowInEasing
        ),
        label = "scale"
    )
    
    // 背景脉冲动画
    val infiniteTransition = rememberInfiniteTransition(label = "pulseAnimation")
    val backgroundAlpha by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "bgAlpha"
    )
    
    val gradientBackground = Brush.radialGradient(
        colors = listOf(
            successColor.copy(alpha = 0.15f * backgroundAlpha),
            backgroundColor.copy(alpha = backgroundAlpha)
        ),
        center = Offset.Infinite,
        radius = 1000f
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        // 成功卡片
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .widthIn(max = 400.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(gradientBackground)
                .padding(32.dp)
        ) {
            // 成功图标区域
            Box(
                modifier = Modifier
                    .size(120.dp)
                    .clip(CircleShape)
                    .background(successColor.copy(alpha = 0.1f))
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                // 内层圆圈
                Box(
                    modifier = Modifier
                        .size(100.dp)
                        .clip(CircleShape)
                        .background(successColor.copy(alpha = 0.2f))
                        .border(
                            width = 2.dp,
                            color = successColor.copy(alpha = 0.5f),
                            shape = CircleShape
                        )
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    // 动态缩放的成功图标
                    Box(
                        modifier = Modifier
                            .scale(animatedScale)
                            .size(60.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        if (showCheckmark) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "成功",
                                tint = successColor,
                                modifier = Modifier.size(60.dp)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 标题区域
            Text(
                text = "拆解完成！",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = primaryTextColor
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 逐字显示的结果文本
            Text(
                text = "已将 \"$goalTitle\" 智能拆解为可执行的子任务",
                fontSize = 16.sp,
                textAlign = TextAlign.Center,
                color = secondaryTextColor,
                modifier = Modifier
                    .animateContentSize()
                    .clip(MaterialTheme.shapes.medium)
                    .fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 统计信息卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White.copy(alpha = 0.8f)
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatItem(
                        icon = Icons.Default.CheckCircle,
                        value = "5-7",
                        label = "子任务数",
                        iconTint = successColor
                    )
                    
                    // 分隔线
                    Box(
                        modifier = Modifier
                            .height(40.dp)
                            .width(1.dp)
                            .background(Color(0xFFEEEEEE))
                    )
                    
                    StatItem(
                        icon = Icons.Default.Schedule,
                        value = "已优化",
                        label = "时间分配",
                        iconTint = DustyLavender
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 继续按钮
            Button(
                onClick = onGenerateExecutionPlan,
                shape = RoundedCornerShape(28.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = successColor
                ),
                modifier = Modifier
                    .height(56.dp)
                    .fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "查看详细拆解结果",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 重新生成按钮
            OutlinedButton(
                onClick = onRegenerate,
                shape = RoundedCornerShape(24.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = DustyLavender
                ),
                border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.5f)),
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "重新拆解",
                    fontSize = 14.sp
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 说明文字
            Text(
                text = "这些任务将帮助您更有效地完成目标",
                fontSize = 14.sp,
                color = secondaryTextColor,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 统计数据项
 */
@Composable
private fun StatItem(
    icon: ImageVector,
    value: String,
    label: String,
    iconTint: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = iconTint,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 数值
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333)
        )
        
        // 标签
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
} 