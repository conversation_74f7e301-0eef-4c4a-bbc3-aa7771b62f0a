package com.timeflow.app.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Window
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalInspectionMode
import com.timeflow.app.BuildConfig
import androidx.metrics.performance.PerformanceMetricsState
import androidx.metrics.performance.JankStats

/**
 * 性能监控工具类
 */
object PerformanceMonitor {
    private const val TAG = "PerformanceMonitor"
    
    // 性能监控相关常量
    private const val FRAME_THRESHOLD_MS = 16 // 60FPS的帧时间
    private const val SEVERE_FRAME_THRESHOLD_MS = 32 // 30FPS的帧时间
    private const val MEMORY_CHECK_INTERVAL_MS = 5000L // 5秒检查一次内存
    private const val MEMORY_WARNING_THRESHOLD = 0.8 // 内存使用率警告阈值
    
    // 性能数据统计
    private var frameCount = 0
    private var jankCount = 0
    private var lastFrameTime = 0L
    private var lastMemoryCheck = 0L
    private var totalFrameTime = 0L
    
    // 性能监控状态
    private var isMonitoring = false
    private var monitoringThread: Thread? = null
    
    /**
     * 初始化性能监控
     */
    fun init(context: Context, window: Window): PerformanceMetricsState.Holder {
        // 初始化帧率监控
        initFrameMetrics(window)
        
        // 初始化性能指标状态
        val metricsState = PerformanceMetricsState.getHolderForHierarchy(window.decorView)
        
        // 设置JankStats监控
        val jankStats = JankStats.createAndTrack(window) { frameData ->
            var frameCount = 0
            var jankFrameCount = 0
            
            frameCount++
            
            if (frameData.isJank) {
                jankFrameCount++
                // 记录卡顿帧的详细信息
                val durationMs = frameData.frameDurationUiNanos / 1_000_000
                Log.w(TAG, "检测到UI卡顿: ${durationMs}ms，原因: ${frameData.states}")
                
                // 如果卡顿严重（>100ms），记录额外信息
                if (durationMs > 100) {
                    val jankRate = if (frameCount > 0) jankFrameCount * 100 / frameCount else 0
                    Log.e(TAG, "严重卡顿! 帧率不足: ${1000 / durationMs}FPS, 累计卡顿率: $jankRate%")
                }
            }
        }
        
        return metricsState
    }
    
    /**
     * 初始化帧率监控
     */
    fun initFrameMetrics(window: Window) {
        if (BuildConfig.DEBUG && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // 由于帧率监控API在运行时可能导致崩溃，暂时禁用
            // 需要实际设备测试后才能启用
            Log.d(TAG, "Frame metrics monitoring disabled for stability")
        }
    }
    
    /**
     * 设置性能监控
     */
    @Composable
    fun MonitorSetup() {
        DisposableEffect(Unit) {
            val startTime = System.nanoTime()
            
            // 启动性能监控
            startMonitoring()
            
            onDispose {
                val duration = (System.nanoTime() - startTime) / 1_000_000
                if (duration > FRAME_THRESHOLD_MS) {
                    Log.w(TAG, "UI渲染耗时: ${duration}ms")
                }
                stopMonitoring()
            }
        }
    }
    
    /**
     * 启动性能监控
     */
    private fun startMonitoring() {
        if (isMonitoring) return
        isMonitoring = true
        
        monitoringThread = Thread {
            while (isMonitoring) {
                try {
                    // 检查内存使用情况
                    checkMemoryUsage()
                    
                    // 检查帧率
                    // checkFrameRate() // 注释掉自定义帧率检查
                    
                    // 每100ms检查一次
                    Thread.sleep(100)
                } catch (e: Exception) {
                    Log.e(TAG, "性能监控异常: ${e.message}")
                }
            }
        }.apply {
            name = "performance-monitor"
            priority = Thread.MIN_PRIORITY
            start()
        }
    }
    
    /**
     * 停止性能监控
     */
    private fun stopMonitoring() {
        isMonitoring = false
        monitoringThread?.join(1000)
        monitoringThread = null
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastMemoryCheck >= MEMORY_CHECK_INTERVAL_MS) {
            try {
                val runtime = Runtime.getRuntime()
                val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
                val maxMemory = runtime.maxMemory() / 1024 / 1024
                val memoryUsage = usedMemory.toFloat() / maxMemory
                
                if (memoryUsage > MEMORY_WARNING_THRESHOLD) {
                    Log.w(TAG, "内存使用率过高: ${(memoryUsage * 100).toInt()}% (${usedMemory}MB/${maxMemory}MB)")
                    // 执行内存清理
                    // System.gc() // 注释掉显式的GC调用
                }
                
                lastMemoryCheck = currentTime
            } catch (e: Exception) {
                Log.e(TAG, "检查内存使用情况失败: ${e.message}")
            }
        }
    }
    
    /**
     * 检查帧率
     */
    private fun checkFrameRate() {
        val currentTime = System.nanoTime()
        if (lastFrameTime > 0) {
            val frameDuration = (currentTime - lastFrameTime) / 1_000_000 // 转换为毫秒
            totalFrameTime += frameDuration
            frameCount++
            
            if (frameDuration > FRAME_THRESHOLD_MS) {
                jankCount++
                Log.w(TAG, "检测到帧率下降: ${frameDuration}ms")
                
                if (frameDuration > SEVERE_FRAME_THRESHOLD_MS) {
                    Log.e(TAG, "严重帧率下降: ${frameDuration}ms")
                }
            }
            
            // 每100帧计算一次平均帧率
            if (frameCount >= 100) {
                val avgFrameTime = totalFrameTime / frameCount
                val fps = 1000f / avgFrameTime
                val jankRate = jankCount * 100f / frameCount
                
                Log.d(TAG, "性能报告: FPS=${fps.toInt()}, 卡顿率=${jankRate.toInt()}%, 平均帧时间=${avgFrameTime}ms")
                
                // 重置计数器
                frameCount = 0
                jankCount = 0
                totalFrameTime = 0
            }
        }
        
        lastFrameTime = currentTime
    }
    
    /**
     * 获取设备渲染模式
     */
    fun getRenderMode(context: Context): RenderMode {
        return when {
            Build.VERSION.SDK_INT < Build.VERSION_CODES.O -> RenderMode.SOFTWARE
            getTotalMemory(context) < 2_000_000_000 -> RenderMode.HARDWARE_LIMITED
            else -> RenderMode.HARDWARE
        }
    }
    
    /**
     * 获取设备总内存
     */
    private fun getTotalMemory(context: Context): Long {
        val memInfo = ActivityManager.MemoryInfo()
        (context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager).getMemoryInfo(memInfo)
        return memInfo.totalMem
    }
    
    /**
     * 启用Compose调试模式
     */
    @Composable
    fun DebugComposition(content: @Composable () -> Unit) {
        if (BuildConfig.DEBUG) {
            CompositionLocalProvider(
                LocalInspectionMode provides true
            ) {
                content()
            }
        } else {
            content()
        }
    }
    
    /**
     * 性能监控作用域
     */
    @Composable
    fun MonitoredComposition(
        tag: String,
        content: @Composable () -> Unit
    ) {
        val startTime = remember { System.nanoTime() }
        
        DisposableEffect(Unit) {
            onDispose {
                val duration = (System.nanoTime() - startTime) / 1_000_000 // 转换为毫秒
                if (duration > FRAME_THRESHOLD_MS) {
                    Log.w(TAG, "Composition of $tag took ${duration}ms")
                }
            }
        }
        
        content()
    }
}

/**
 * 渲染模式枚举
 */
enum class RenderMode {
    SOFTWARE,
    HARDWARE_LIMITED,
    HARDWARE
} 