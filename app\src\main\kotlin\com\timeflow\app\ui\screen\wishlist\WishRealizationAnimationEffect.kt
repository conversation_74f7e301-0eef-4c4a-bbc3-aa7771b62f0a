package com.timeflow.app.ui.screen.wishlist

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

/**
 * 愿望实现动画效果 - 升空化为星星
 */
@Composable
fun WishRealizationAnimationEffect(
    wishTitle: String,
    onAnimationComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showAnimation by remember { mutableStateOf(true) }
    
    LaunchedEffect(Unit) {
        delay(3000) // 动画持续3秒
        showAnimation = false
        onAnimationComplete()
    }

    if (showAnimation) {
        Dialog(
            onDismissRequest = { },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .alpha(0.9f),
                contentAlignment = Alignment.Center
            ) {
                WishToStarAnimation(
                    wishTitle = wishTitle,
                    modifier = modifier
                )
            }
        }
    }
}

@Composable
private fun WishToStarAnimation(
    wishTitle: String,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 动画状态
    val infiniteTransition = rememberInfiniteTransition(label = "wish_realization")
    
    // 愿望卡片升空动画
    val cardOffset by animateFloatAsState(
        targetValue = -200f,
        animationSpec = tween(1500, easing = EaseOutQuart),
        label = "card_offset"
    )
    
    val cardScale by animateFloatAsState(
        targetValue = 0.3f,
        animationSpec = tween(1500, delayMillis = 500, easing = EaseInOut),
        label = "card_scale"
    )
    
    val cardAlpha by animateFloatAsState(
        targetValue = 0f,
        animationSpec = tween(800, delayMillis = 1200, easing = EaseIn),
        label = "card_alpha"
    )
    
    // 星星生成动画
    val starsAlpha by animateFloatAsState(
        targetValue = 1f,
        animationSpec = tween(1000, delayMillis = 1000),
        label = "stars_alpha"
    )
    
    // 星星闪烁动画
    val starsGlow by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(800, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "stars_glow"
    )

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // 背景渐变
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawRect(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E).copy(alpha = 0.7f),
                        Color(0xFF16213E).copy(alpha = 0.9f)
                    ),
                    radius = size.minDimension / 2
                )
            )
        }

        // 愿望卡片
        Card(
            modifier = Modifier
                .width(280.dp)
                .offset(y = cardOffset.dp)
                .scale(cardScale)
                .alpha(cardAlpha),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 12.dp),
            shape = androidx.compose.foundation.shape.RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "🌟 愿望实现",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFFFFD700)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = wishTitle,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.DarkGray
                )
            }
        }
        
        // 粒子效果和星星
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .alpha(starsAlpha)
        ) {
            drawWishParticles(starsGlow)
            drawStarConstellation()
        }
        
        // 祝贺文字
        if (starsAlpha > 0.5f) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 120.dp)
                    .alpha(starsAlpha)
            ) {
                Text(
                    text = "✨ 恭喜你 ✨",
                    style = MaterialTheme.typography.headlineMedium,
                    color = Color(0xFFFFD700)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "又一个愿望闪耀成星",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }
        }
    }
}

/**
 * 绘制愿望粒子效果
 */
private fun DrawScope.drawWishParticles(glowIntensity: Float) {
    val colors = listOf(
        Color(0xFFFFD700), // 金色
        Color(0xFF00FFFF), // 青色
        Color(0xFFFF6B6B), // 粉色
        Color(0xFF9B59B6), // 紫色
        Color.White
    )
    
    repeat(30) { i ->
        val angle = (i * 12f + System.currentTimeMillis() / 20f) % 360f
        val radius = 100f + sin(System.currentTimeMillis() / 1000f + i) * 50f
        val centerX = size.width / 2
        val centerY = size.height / 2
        
        val x = centerX + cos(angle * PI / 180f) * radius
        val y = centerY + sin(angle * PI / 180f) * radius
        
        val particleSize = (3f + sin(System.currentTimeMillis() / 500f + i * 0.5f) * 2f) * glowIntensity
        val alpha = 0.7f * glowIntensity
        
        drawCircle(
            color = colors[i % colors.size].copy(alpha = alpha),
            radius = particleSize,
            center = Offset(x.toFloat(), y.toFloat())
        )
    }
}

/**
 * 绘制星座效果
 */
private fun DrawScope.drawStarConstellation() {
    val starPositions = listOf(
        Offset(size.width * 0.2f, size.height * 0.3f),
        Offset(size.width * 0.8f, size.height * 0.25f),
        Offset(size.width * 0.7f, size.height * 0.7f),
        Offset(size.width * 0.3f, size.height * 0.8f),
        Offset(size.width * 0.5f, size.height * 0.15f),
        Offset(size.width * 0.1f, size.height * 0.6f),
        Offset(size.width * 0.9f, size.height * 0.6f)
    )
    
    // 绘制连接线
    starPositions.zipWithNext { start, end ->
        drawLine(
            color = Color(0xFFFFD700).copy(alpha = 0.3f),
            start = start,
            end = end,
            strokeWidth = 2.dp.toPx()
        )
    }
    
    // 绘制星星
    starPositions.forEach { position ->
        drawStar(
            center = position,
            size = 16f,
            color = Color(0xFFFFD700)
        )
    }
}

/**
 * 绘制五角星
 */
private fun DrawScope.drawStar(
    center: Offset,
    size: Float,
    color: Color
) {
    val path = Path()
    val angles = (0..4).map { it * 144f * PI / 180f } // 五角星的角度
    
    angles.forEachIndexed { index, angle ->
        val radius = if (index % 2 == 0) size else size * 0.4f
        val x = center.x + cos(angle - PI / 2) * radius
        val y = center.y + sin(angle - PI / 2) * radius
        
        if (index == 0) {
            path.moveTo(x.toFloat(), y.toFloat())
        } else {
            path.lineTo(x.toFloat(), y.toFloat())
        }
    }
    path.close()
    
    // 绘制星星主体
    drawPath(
        path = path,
        color = color
    )
    
    // 绘制光晕效果
    drawCircle(
        color = color.copy(alpha = 0.3f),
        radius = size * 1.5f,
        center = center
    )
}

/**
 * 简化版愿望实现动画（用于列表项）
 */
@Composable
fun MiniWishRealizationEffect(
    modifier: Modifier = Modifier,
    onAnimationComplete: () -> Unit = {}
) {
    var isVisible by remember { mutableStateOf(true) }
    
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1.2f else 0f,
        animationSpec = tween(800, easing = EaseOutBack),
        label = "mini_scale",
        finishedListener = { onAnimationComplete() }
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(600, delayMillis = 500),
        label = "mini_alpha"
    )
    
    LaunchedEffect(Unit) {
        delay(300)
        isVisible = false
    }
    
    Box(
        modifier = modifier
            .size(40.dp)
            .scale(scale)
            .alpha(alpha),
        contentAlignment = Alignment.Center
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            // 绘制小星星爆炸效果
            repeat(8) { i ->
                val angle = i * 45f * PI / 180f
                val distance = 15.dp.toPx()
                val x = center.x + cos(angle) * distance
                val y = center.y + sin(angle) * distance
                
                drawCircle(
                    color = Color(0xFFFFD700),
                    radius = 3.dp.toPx(),
                    center = Offset(x.toFloat(), y.toFloat())
                )
            }
            
            // 中心星星
            drawStar(
                center = center,
                size = 8.dp.toPx(),
                color = Color(0xFFFFD700)
            )
        }
    }
} 