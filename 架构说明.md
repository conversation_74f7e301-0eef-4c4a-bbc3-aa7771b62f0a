+------------------------------------------+
|                                          |
|             应用界面层 (UI)               |
|  +------------------------------------+  |
|  |        Compose UI 组件            |  |
|  |  +---------+  +---------+         |  |
|  |  | Screens |  |   组件   |         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |            视图模型层              |  |
|  |  +---------+  +---------+         |  |
|  |  |ViewModels|  |  状态类  |         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |           领域/业务层              |  |
|  |  +---------+  +---------+         |  |
|  |  |UseCase类 |  |Repository|         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |            数据源层               |  |
|  |  +-----------+  +-----------+     |  |
|  |  |本地数据源  |  |远程数据源  |     |  |
|  |  | (Room DB) |  | (API/SDK) |     |  |
|  |  +-----------+  +-----------+     |  |
|  +------------------------------------+  |
|                                          |
+------------------------------------------+
         ↑                      ↑
+-----------------+  +----------------------+
| 通用工具和扩展   |  | 依赖注入 (Hilt/Koin) |
+-----------------+  +----------------------+
数据层设计
数据库设计: 使用Room数据库，当前版本为9
主要实体:
Task (任务)
TaskTag (任务标签)
KanbanBoard (看板)
KanbanColumn (看板列)
Goal (目标)
GoalSubTask (目标子任务)
GoalTemplate (目标模板)
AppUsageEntity (应用使用记录)
CycleRecord (周期记录)
SymptomRecord (症状记录)
仓库层:
每种实体配备专门的Repository接口和实现
实现了BaseRepository抽象类，统一缓存和性能优化策略
使用Kotlin Flow实现响应式数据流
三、核心功能模块
1. 任务管理模块
功能特点:
任务创建、编辑和删除
子任务支持与层级管理
优先级、截止日期和标签系统
任务进度跟踪
任务状态流转
主要组件:
TaskRepository: 数据访问层
TaskViewModel: 业务逻辑层
TaskDetailScreen: 展示任务详情
TaskListScreen: 任务列表视图
2. 目标管理模块
功能特点:
长期目标设置与跟踪
目标拆解为子任务
AI辅助目标拆解与分析
目标进度可视化
目标模板系统
关键组件:
Goal: 目标数据模型
GoalSubTask: 目标子任务模型
GoalTemplate: 目标模板模型
GoalRepository: 数据访问层
GoalViewModel: 业务逻辑层
GoalManagementScreen: 目标管理页面
GoalDetailScreen: 目标详情页面
3. 看板模块
功能特点:
可视化任务管理
自定义列与状态流
拖拽排序
任务分组展示
核心组件:
KanbanBoard: 看板数据模型
KanbanColumn: 看板列数据模型
KanbanRepository: 数据访问层
KanbanViewModel: 业务逻辑层
4. 时间追踪模块
功能特点:
专注计时器
活动自动分类
时间使用统计
生产力分析
主要组件:
TimeTrackingScreen: 时间追踪界面
GlobalTimerViewModel: 全局计时器逻辑
TimeAnalyticsScreen: 时间分析界面
5. 健康与福祉模块
功能特点:
情绪追踪与分析
生理期追踪与预测
症状记录与关联
健康数据可视化
核心组件:
MenstrualCycleScreen: 周期跟踪界面
EmotionStatisticsScreen: 情绪统计分析
CycleRepository: 数据访问层
6. AI助手模块
功能特点:
任务智能拆解
目标分解建议
时间管理建议
生产力优化建议
相关组件:
AiAssistantScreen: AI助手界面
AIReviewScreen: AI复盘分析
AiSettingsScreen: AI设置界面