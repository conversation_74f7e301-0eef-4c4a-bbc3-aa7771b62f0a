package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.KanbanBoardDao
import com.timeflow.app.data.entity.KanbanBoard
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 看板仓库实现类
 */
@Singleton
class KanbanBoardRepositoryImpl @Inject constructor(
    private val kanbanBoardDao: KanbanBoardDao
) : KanbanBoardRepository {
    
    override suspend fun getAllBoards(): List<KanbanBoard> {
        return kanbanBoardDao.getAllBoards()
    }
    
    override fun observeAllBoards(): Flow<List<KanbanBoard>> {
        return kanbanBoardDao.observeAllBoards()
    }
    
    override suspend fun getBoardById(boardId: String): KanbanBoard? {
        return kanbanBoardDao.getBoardById(boardId)
    }
    
    override fun observeBoardById(boardId: String): Flow<KanbanBoard?> {
        return kanbanBoardDao.observeBoardById(boardId)
    }
    
    override suspend fun insertBoard(board: KanbanBoard) {
        kanbanBoardDao.insert(board)
    }
    
    override suspend fun updateBoard(board: KanbanBoard) {
        kanbanBoardDao.update(board)
    }
    
    override suspend fun deleteBoard(board: KanbanBoard) {
        kanbanBoardDao.delete(board)
    }
} 