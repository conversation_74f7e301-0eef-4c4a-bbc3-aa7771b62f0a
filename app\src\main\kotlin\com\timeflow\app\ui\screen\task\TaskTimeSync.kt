package com.timeflow.app.ui.screen.task

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务时间同步器
 * 负责协调ViewModel和TaskTimeManager之间的数据同步
 */
@Singleton
class TaskTimeSync @Inject constructor(
    private val taskTimeManager: TaskTimeManager
) {
    
    /**
     * 同步更新任务时间到数据库和缓存
     * @param taskId 任务ID
     * @param newDateTime 新的时间
     * @param updateDatabase 更新数据库的函数
     */
    suspend fun syncTaskTime(
        taskId: String,
        newDateTime: LocalDateTime?,
        updateDatabase: suspend (String, LocalDateTime?) -> Unit
    ) {
        withContext(Dispatchers.IO) {
            try {
                Log.d("TaskTimeSync", "开始同步任务时间: taskId=$taskId, newDateTime=$newDateTime")
                
                // 1. 先更新TaskTimeManager缓存（立即生效）
                taskTimeManager.updateTaskTime(taskId, newDateTime, "TaskTimeSync")
                
                // 2. 然后更新数据库
                updateDatabase(taskId, newDateTime)
                
                // 3. 清除待同步标记
                taskTimeManager.clearPendingUpdates(setOf(taskId))
                
                Log.d("TaskTimeSync", "任务时间同步完成: taskId=$taskId")
                
            } catch (e: Exception) {
                Log.e("TaskTimeSync", "任务时间同步失败: taskId=$taskId", e)
                throw e
            }
        }
    }
    
    /**
     * 批量同步任务时间
     */
    suspend fun batchSyncTaskTimes(
        updates: Map<String, LocalDateTime?>,
        batchUpdateDatabase: suspend (Map<String, LocalDateTime?>) -> Unit
    ) {
        withContext(Dispatchers.IO) {
            try {
                Log.d("TaskTimeSync", "开始批量同步任务时间: ${updates.size}个任务")
                
                // 1. 批量更新TaskTimeManager缓存
                taskTimeManager.batchUpdateTaskTimes(updates, "TaskTimeSync_Batch")
                
                // 2. 批量更新数据库
                batchUpdateDatabase(updates)
                
                // 3. 清除待同步标记
                taskTimeManager.clearPendingUpdates(updates.keys)
                
                Log.d("TaskTimeSync", "批量任务时间同步完成")
                
            } catch (e: Exception) {
                Log.e("TaskTimeSync", "批量任务时间同步失败", e)
                throw e
            }
        }
    }
    
    /**
     * 从数据库初始化TaskTimeManager缓存
     */
    suspend fun initializeCacheFromDatabase(
        loadTaskTimes: suspend () -> Map<String, LocalDateTime?>
    ) {
        withContext(Dispatchers.IO) {
            try {
                Log.d("TaskTimeSync", "从数据库初始化时间缓存")
                
                val taskTimes = loadTaskTimes()
                taskTimeManager.initializeCache(taskTimes)
                
                Log.d("TaskTimeSync", "时间缓存初始化完成: ${taskTimes.size}个任务")
                
            } catch (e: Exception) {
                Log.e("TaskTimeSync", "初始化时间缓存失败", e)
                throw e
            }
        }
    }
} 