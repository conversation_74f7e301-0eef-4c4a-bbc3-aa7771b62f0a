package com.timeflow.app.ui.screen.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.toArgb
import androidx.compose.foundation.clickable
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.border
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel
import com.timeflow.app.ui.theme.*
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.task.TaskViewModel
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.getSampleTasks
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.screen.task.model.getUrgencyColor
import com.timeflow.app.ui.navigation.Screen
import com.timeflow.app.ui.task.TaskListState
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import android.app.Activity
import android.content.Context
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.displayCutout
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.systemBars
import android.os.Build
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import android.view.WindowManager
import android.util.DisplayMetrics
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material.icons.filled.KeyboardArrowRight

/**
 * 为标签文本生成自适应颜色
 * 根据是否选中和当前主题（亮/暗）来动态调整颜色
 */
@Composable
fun AdaptiveTabColor(isSelected: Boolean): Color {
    val isDarkTheme = isSystemInDarkTheme()
    
    return if (isSelected) {
        if (isDarkTheme) DustyLavender.lighten(0.1f) else DustyLavender
    } else {
        if (isDarkTheme) TabTextColor.copy(alpha = 0.7f) else TabTextColor
    }
}

/**
 * 主页屏幕 - 全新无白边版本
 * 直接使用单一容器确保背景色一致并且没有任何额外的白色区域
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HomeScreen(
    navController: NavController,
    viewModel: TimeFlowViewModel? = null,
    onSettingsClick: () -> Unit = {}
) {
    val pagerState = rememberPagerState(pageCount = { 5 })
    val scope = rememberCoroutineScope()
    val tabItems = listOf("今天", "周", "月", "2025", "Life")
    
    var highContrastModeEnabled by remember { mutableStateOf(false) }
    
    // 获取状态栏高度
    val context = LocalContext.current
    val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    val statusBarHeightPx = try {
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) context.resources.getDimensionPixelSize(resourceId) else 0
    } catch (e: Exception) {
        60 // 默认值，约为状态栏高度
    }
    
    // 将px转为dp
    val density = LocalDensity.current
    val statusBarHeightDp = with(density) { statusBarHeightPx.toDp() }
    
    // 计算偏移量，根据状态栏高度加上额外的调整值
    // 在Android 10及以上版本使用更大的偏移值，处理刘海屏/打孔屏
    val offsetY = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        0.dp  // 移除负偏移，让内容自然显示
    } else {
        0.dp
    }
    
    // 确保Activity窗口已经设置为透明
    val window = (LocalView.current.context as? Activity)?.window
    window?.apply {
        statusBarColor = android.graphics.Color.TRANSPARENT
        navigationBarColor = android.graphics.Color.TRANSPARENT
        
        // 强制消除刘海屏区域的padding
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            attributes = attributes.apply {
                layoutInDisplayCutoutMode = android.view.WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
        
        // 强制设置FLAG_LAYOUT_NO_LIMITS直接在HomeScreen中
        setFlags(
            android.view.WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            android.view.WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
    }
    
    // 更明确地处理窗口插入
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground) // 对整个屏幕应用背景色
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // 状态栏占位
            Spacer(
                modifier = Modifier
                    .windowInsetsPadding(
                        WindowInsets.systemBars.only(WindowInsetsSides.Top)
                    )
                    .height(64.dp) // 增加固定高度确保状态栏下方有足够空间
            )
            
            // 顶部问候区域
            GreetingHeader(
                userName = "糖",
                dateTime = LocalDateTime.now(),
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 16.dp)
            ) {
                navController.navigate("calendar")
            }

            // 标签栏
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                edgePadding = 16.dp,
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (pagerState.currentPage < tabPositions.size) {
                        Box(
                            Modifier
                                .tabIndicatorOffset(tabPositions[pagerState.currentPage])
                                .height(2.dp)
                                .width(24.dp)
                                .align(Alignment.CenterHorizontally)
                                .background(color = DustyLavender, shape = CircleShape)
                                .padding(horizontal = 8.dp)
                        )
                    }
                },
                divider = {},
                modifier = Modifier
                    .padding(top = 8.dp, bottom = 4.dp)
                    .height(48.dp)
            ) {
                tabItems.forEachIndexed { index, title ->
                    val selected = pagerState.currentPage == index
                    val tabColor = if (highContrastModeEnabled) {
                        if (selected) Color(0xFF6D6A75) else Color(0xFF8D8A95)
                    } else {
                        AdaptiveTabColor(selected)
                    }
                    
                    Tab(
                        selected = selected,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = title,
                                fontWeight = if (selected) FontWeight.Bold else FontWeight.SemiBold,
                                style = TextStyle(letterSpacing = 0.15.sp),
                                modifier = Modifier
                                    .padding(vertical = 8.dp)
                                    .run {
                                        if (selected) {
                                            this.border(
                                                width = 1.dp,
                                                color = DustyLavender.copy(alpha = 0.2f),
                                                shape = CircleShape
                                            ).padding(horizontal = 8.dp)
                                        } else {
                                            this
                                        }
                                    }
                            )
                        },
                        selectedContentColor = tabColor,
                        unselectedContentColor = tabColor
                    )
                }
            }

            // 内容区域
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.weight(1f)
            ) { page ->
                when (page) {
                    0 -> TodayContent(
                        navController = navController,
                        timeFlowViewModel = viewModel as TimeFlowViewModel
                    )
                    1 -> WeekContent()
                    2 -> MonthContent()
                    3 -> YearContent()
                    4 -> LifeContent()
                }
            }
        }
    }
}

/**
 * 顶部问候区域 - 简化版本，无额外嵌套
 */
@Composable
fun GreetingHeader(
    userName: String,
    dateTime: LocalDateTime,
    modifier: Modifier = Modifier,
    onCalendarClick: () -> Unit = {}
) {
    // 直接使用Row作为根容器，无额外背景
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 用户问候区域
        Column(
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // 问候语 - 使用19sp字体大小
            Text(
                text = "$userName，今天怎么样呢",
                style = MaterialTheme.typography.headlineSmall,
                fontSize = 19.sp,
                fontWeight = FontWeight.W500,
                letterSpacing = 0.15.sp,
                color = TextPrimary
            )
            
            // 日期显示
            Text(
                text = "周${dateTime.dayOfWeek.getChineseDayOfWeek()} · ${dateTime.monthValue}月${dateTime.dayOfMonth}日",
                style = MaterialTheme.typography.bodyMedium,
                fontSize = 12.sp,
                letterSpacing = 0.2.sp,
                color = TextSecondary.copy(alpha = 0.8f)
            )
        }
        
        // 右侧按钮区域
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 日历按钮
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(DustyLavender.copy(alpha = 0.7f), CircleShape)
                    .clickable(onClick = onCalendarClick),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Filled.CalendarToday,
                    contentDescription = "日历",
                    tint = Color.White.copy(alpha = 0.95f),
                    modifier = Modifier.size(20.dp)
                )
            }
            
            // 设置按钮
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(DustyLavender.copy(alpha = 0.7f), CircleShape)
                    .clickable(onClick = {}),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Filled.Settings,
                    contentDescription = "设置",
                    tint = Color.White.copy(alpha = 0.95f),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 将DayOfWeek转换为中文星期表示
 */
fun DayOfWeek.getChineseDayOfWeek(): String {
    return when (this) {
        DayOfWeek.MONDAY -> "一"
        DayOfWeek.TUESDAY -> "二"
        DayOfWeek.WEDNESDAY -> "三"
        DayOfWeek.THURSDAY -> "四"
        DayOfWeek.FRIDAY -> "五"
        DayOfWeek.SATURDAY -> "六"
        DayOfWeek.SUNDAY -> "日"
    }
}

/**
 * 今天标签内容
 */
@Composable
fun TodayContent(
    modifier: Modifier = Modifier,
    navController: NavController,
    taskViewModel: TaskViewModel = hiltViewModel(),
    timeFlowViewModel: TimeFlowViewModel
) {
    val scrollState = rememberScrollState()
    
    // 使用一个简单的状态变量，避免使用Flow API
    var taskList by remember { mutableStateOf(emptyList<ModelTaskData>()) }
    var isTasksLoading by remember { mutableStateOf(true) }
    
    // 初始加载触发 - 通知TaskViewModel加载任务
    LaunchedEffect(Unit) {
        if (taskList.isEmpty()) {
            taskViewModel.loadTasks()
            // 假设loadTasks会更新taskViewModel.taskListState
            // 手动从taskViewModel获取数据
            delay(500) // 给任务加载一些时间
            taskList = taskViewModel.taskListState.value.tasks
            isTasksLoading = false
        }
    }
    
    // 处理任务状态更新 - 通过ViewModel更新
    val handleTaskStatusChanged = { taskId: String, isCompleted: Boolean ->
        taskViewModel.updateTaskStatus(taskId, isCompleted)
        // 本地更新状态，避免重新从ViewModel获取
        taskList = taskList.map { 
            if (it.id == taskId) it.copy(isCompleted = isCompleted) else it 
        }
    }
    
    // 按分类对任务进行分组并排序，每个分类只取紧急度最高的前三个任务
    val tasksByCategory = remember(taskList) {
        taskList
            .filter { task -> task.displayInTaskList } // 仅显示应该在列表中的任务
            .groupBy { it.tag ?: "未分类" } // 按tag分组，没有tag的归为"未分类"
            .mapValues { (_, tasks) ->
                // 每个分类内部按紧急度排序并取前三个
                tasks.sortedWith(
                    compareByDescending<ModelTaskData> { 
                        when(it.urgency) {
                            TaskUrgency.CRITICAL -> 4
                            TaskUrgency.HIGH -> 3
                            TaskUrgency.MEDIUM -> 2
                            TaskUrgency.LOW -> 1
                            else -> 0
                        }
                    }.thenBy { it.daysLeft } // 同等紧急度下，按剩余天数排序
                ).take(3) // 只取前三个
            }
            .filter { (_, tasks) -> tasks.isNotEmpty() } // 只保留有任务的分类
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        // 移除英文Header，使用现有的头部问候
        Spacer(modifier = Modifier.height(8.dp))

        // 任务管理卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerHigh
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 任务管理卡片标题区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "任务管理",
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    // 查看全部按钮
                    TextButton(
                        onClick = { navController.navigate(Screen.TaskList.route) },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("查看全部")
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowRight,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 任务列表区域
                if (isTasksLoading) {
                    // 加载状态
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = DustyLavender,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                } else if (tasksByCategory.isEmpty()) {
                    // 没有任务时显示空状态
                    EmptyTasksPlaceholder(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp)
                    )
                } else {
                    // 按分类显示任务
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 400.dp)
                    ) {
                        tasksByCategory.forEach { (category, tasks) ->
                            // 分类标题
                            item(key = "category_$category") {
                                CategoryHeader(
                                    title = category,
                                    count = tasks.size
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                            
                            // 分类内的任务
                            items(tasks) { task ->
                                EnhancedTaskItem(
                                    task = task,
                                    onTaskClick = { 
                                        navController.navigate("${Screen.TaskDetail.route}/${task.id}")
                                    },
                                    onStatusChanged = { isCompleted ->
                                        handleTaskStatusChanged(task.id, isCompleted)
                                    }
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                            
                            // 分类之间的分隔线
                            item(key = "divider_$category") {
                                HorizontalDivider(
                                    modifier = Modifier.padding(vertical = 8.dp),
                                    color = LavenderAsh.copy(alpha = 0.2f)
                                )
                            }
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // 目标追踪区域
        Card(
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = TranslucentWhite), // 使用半透明白色
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(14.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "目标追踪",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = TextPrimary
                    )
                    
                    Text(
                        text = "+ 添加目标",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MistyPurple,
                        modifier = Modifier.clickable { /* 添加目标 */ }
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 目标筛选按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FilterButton(
                        text = "全部", 
                        isSelected = true,
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    )
                    FilterButton(
                        text = "进行中", 
                        isSelected = false,
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    )
                    FilterButton(
                        text = "已完成", 
                        isSelected = false,
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    )
                    FilterButton(
                        text = "待完成", 
                        isSelected = false,
                        onClick = { },
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 目标列表
                GoalItem(
                    title = "每周至少阅读3小时",
                    progress = 0.6f,
                    daysLeft = 3
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                GoalItem(
                    title = "完成项目开发",
                    progress = 0.3f,
                    daysLeft = 10
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                GoalItem(
                    title = "每天锻炼30分钟",
                    progress = 0.8f,
                    daysLeft = 0
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun EnhancedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onStatusChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onTaskClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 任务完成状态复选框
        Checkbox(
            checked = task.isCompleted,
            onCheckedChange = { isChecked ->
                onStatusChanged(isChecked)
            },
            colors = CheckboxDefaults.colors(
                checkedColor = getUrgencyColor(task.urgency),
                uncheckedColor = getUrgencyColor(task.urgency).copy(alpha = 0.5f)
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 任务内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 任务标题
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyLarge,
                color = if (task.isCompleted) 
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                else 
                    MaterialTheme.colorScheme.onSurface,
                textDecoration = if (task.isCompleted) 
                    TextDecoration.LineThrough 
                else 
                    TextDecoration.None
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 任务详情行
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 优先级指示器
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = getUrgencyColor(task.urgency),
                            shape = CircleShape
                        )
                )
                
                Spacer(modifier = Modifier.width(4.dp))
                
                // 紧急程度文本
                Text(
                    text = getUrgencyText(task.urgency),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 剩余天数
                if (task.daysLeft != null) {
                    Text(
                        text = getDaysLeftText(task.daysLeft),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
        
        // 右侧箭头图标
        Icon(
            imageVector = Icons.Filled.KeyboardArrowRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f),
            modifier = Modifier.size(16.dp)
        )
    }
}

// 获取紧急程度对应的文本
@Composable
private fun getUrgencyText(urgency: TaskUrgency): String {
    return when (urgency) {
        TaskUrgency.CRITICAL -> "紧急"
        TaskUrgency.HIGH -> "高优先级"
        TaskUrgency.MEDIUM -> "中优先级"
        TaskUrgency.LOW -> "低优先级"
        else -> "普通"
    }
}

// 获取剩余天数的文本
@Composable
private fun getDaysLeftText(daysLeft: Int): String {
    return when {
        daysLeft < 0 -> "已逾期${-daysLeft}天"
        daysLeft == 0 -> "今天到期"
        daysLeft == 1 -> "明天到期"
        else -> "还剩${daysLeft}天"
    }
}

// 空任务占位符
@Composable
private fun EmptyTasksPlaceholder(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.CheckCircleOutline,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f),
            modifier = Modifier.size(48.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无待办任务",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

/**
 * 周标签内容
 */
@Composable
fun WeekContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground) // 使用新的背景色
    ) {
        // 周内容的实现将在这里添加
        Text(
            text = "周标签内容开发中...",
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 月标签内容
 */
@Composable
fun MonthContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground) // 使用新的背景色
    ) {
        // 月内容的实现将在这里添加
        Text(
            text = "月标签内容开发中...",
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 年标签内容
 */
@Composable
fun YearContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground) // 使用新的背景色
    ) {
        // 年内容的实现将在这里添加
        Text(
            text = "2025标签内容开发中...",
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * Life标签内容
 */
@Composable
fun LifeContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppBackground) // 使用新的背景色
    ) {
        // Life内容的实现将在这里添加
        Text(
            text = "Life标签内容开发中...",
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 任务建议项
 */
@Composable
fun TaskSuggestionItem(text: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(vertical = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(DustyLavender, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 任务建议使用14sp
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            fontSize = 14.sp,
            color = TextSecondary
        )
    }
}

/**
 * 筛选按钮
 */
@Composable
fun FilterButton(
    text: String, 
    isSelected: Boolean, 
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) DustyLavender else SoftGray.copy(alpha = 0.2f),
            contentColor = if (isSelected) Color.White else DustyLavender
        ),
        contentPadding = PaddingValues(vertical = 4.dp),
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp
        )
    ) {
        // 按钮文字使用14sp
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            fontSize = 14.sp,
            letterSpacing = 0.15.sp
        )
    }
}

/**
 * 目标项
 */
@Composable
fun GoalItem(
    title: String,
    progress: Float,
    daysLeft: Int
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(LavenderAsh.copy(alpha = 0.12f))
            .padding(12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 目标标题使用16sp
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )
            
            // 辅助信息使用12sp
            Text(
                text = if (daysLeft > 0) "剩余${daysLeft}天" else "今日完成",
                style = MaterialTheme.typography.bodySmall,
                fontSize = 12.sp,
                color = if (daysLeft > 0) TextSecondary.copy(alpha = 0.8f) else Color(0xFF4CAF50)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度条
        LinearProgressIndicator(
            progress = { progress },
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = DustyLavender,
            trackColor = LavenderAsh.copy(alpha = 0.2f)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 进度百分比使用12sp
        Text(
            text = "${(progress * 100).toInt()}%",
            style = MaterialTheme.typography.bodySmall,
            fontSize = 12.sp,
            color = DustyLavender.copy(alpha = 0.9f)
        )
    }
}

/**
 * 分类标题组件
 */
@Composable
private fun CategoryHeader(
    title: String,
    count: Int
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 分类图标或指示器
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(DustyLavender, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 分类名称
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            color = TextPrimary,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 任务数量
        Text(
            text = "($count)",
            style = MaterialTheme.typography.bodySmall,
            color = TextSecondary
        )
    }
} 