package com.timeflow.app.ui.screen.milestone

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.format.DateTimeFormatter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.Image
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import com.timeflow.app.R

/**
 * 时间轴视图 - 左侧单轴，右侧卡片设计
 * 优化版本：减少布局嵌套，优化渲染性能
 */
@Composable
fun TimelineView(
    milestones: List<Milestone>,
    onMilestoneClick: (Milestone) -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 使用LazyColumn直接呈现内容，不使用额外的Box包装
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 16.dp)
    ) {
        itemsIndexed(
            items = milestones.sortedByDescending { it.date },
            key = { _, milestone -> milestone.id }
        ) { index, milestone ->
            // 每个里程碑项
            TimelineItem(
                milestone = milestone,
                isFirst = index == 0,
                isLast = index == milestones.size - 1,
                onMilestoneClick = onMilestoneClick
            )
            
            // 添加间距，但不是最后一项
            if (index < milestones.size - 1) {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
        
        // 底部填充，避免被导航栏遮挡
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * 单个时间轴项目
 * 将逻辑拆分为组件，提高复用性和性能
 */
@Composable
private fun TimelineItem(
    milestone: Milestone,
    isFirst: Boolean,
    isLast: Boolean,
    onMilestoneClick: (Milestone) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        // 左侧时间轴部分
        TimelineIndicator(
            color = milestone.category.color,
            isFirst = isFirst,
            isLast = isLast
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 右侧卡片
        MilestoneCard(
            milestone = milestone,
            onClick = { onMilestoneClick(milestone) }
        )
    }
}

/**
 * 时间轴指示器组件
 * 包含时间点和连接线
 */
@Composable
private fun TimelineIndicator(
    color: Color,
    isFirst: Boolean,
    isLast: Boolean
) {
    Box(
        modifier = Modifier.width(32.dp),
        contentAlignment = Alignment.Center
    ) {
        // 垂直连接线
        if (!isFirst) {
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(16.dp)
                    .background(color.copy(alpha = 0.2f))
                    .align(Alignment.TopCenter)
            )
        }
        
        // 时间点
        Box(
            modifier = Modifier
                .size(16.dp)
                .clip(CircleShape)
                .background(color)
                .align(Alignment.Center),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .clip(CircleShape)
                    .background(Color.White)
            )
        }
        
        // 下方连接线
        if (!isLast) {
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(70.dp)
                    .background(color.copy(alpha = 0.2f))
                    .align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 里程碑卡片组件
 */
@Composable
private fun MilestoneCard(
    milestone: Milestone,
    onClick: () -> Unit
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                isExpanded = !isExpanded
                if (isExpanded) {
                    onClick()
                }
            },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 图片显示（如果有）
            val imageUri = milestone.imageUri // 创建本地变量避免智能类型转换问题
            if (imageUri != null) {
                Image(
                    painter = rememberAsyncImagePainter(
                        model = imageUri.toUri(),
                        error = painterResource(R.drawable.ic_image_placeholder)
                    ),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)),
                    contentScale = ContentScale.Crop
                )
            }
            
            // 卡片内容
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 顶部信息：类别标签和日期
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 类别标签
                    Surface(
                        color = milestone.category.color.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = milestone.category.displayName,
                            modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp),
                            fontSize = 12.sp,
                            color = milestone.category.color,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    // 日期
                    Text(
                        text = milestone.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 里程碑标题和图标
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 图标容器
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .background(milestone.category.color.copy(alpha = 0.1f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = milestone.category.icon,
                            contentDescription = null,
                            tint = milestone.category.color,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    
                    // 标题
                    Text(
                        text = milestone.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                // 里程碑描述 - 始终显示，不受展开状态影响
                if (milestone.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = milestone.description,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        lineHeight = 20.sp
                    )
                }
                
                // 里程碑类型标记（如果不是普通类型）- 始终显示
                if (milestone.milestoneType != MilestoneType.REGULAR) {
                    Spacer(modifier = Modifier.height(8.dp))
                    MilestoneTypeTag(milestone.milestoneType)
                }
                
                // 额外的标签信息（仅在展开时显示）
                AnimatedVisibility(visible = isExpanded) {
                    Column(
                        modifier = Modifier.padding(top = 12.dp)
                    ) {
                        // 标签显示
                        if (milestone.tags.isNotEmpty()) {
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                milestone.tags.take(3).forEach { tag ->
                                    Surface(
                                        color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                                        shape = RoundedCornerShape(12.dp)
                                    ) {
                                        Text(
                                            text = "#$tag",
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 里程碑类型标签组件
 */
@Composable
private fun MilestoneTypeTag(type: MilestoneType) {
    val (typeColor, typeName) = when(type) {
        MilestoneType.DECISION -> Pair(Color(0xFF9C27B0), "决策型事件") // 紫色
        MilestoneType.OPPORTUNITY -> Pair(Color(0xFF4CAF50), "机遇型事件") // 绿色
        MilestoneType.IMPACT -> Pair(Color(0xFFE91E63), "冲击型事件") // 粉色
        else -> Pair(Color.Gray, "")
    }
    
    Surface(
        color = typeColor.copy(alpha = 0.1f),
        shape = RoundedCornerShape(4.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(typeColor, CircleShape)
            )
            Text(
                text = typeName,
                fontSize = 12.sp,
                color = typeColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
} 