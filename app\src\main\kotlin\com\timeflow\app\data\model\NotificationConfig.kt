package com.timeflow.app.data.model

/**
 * 通知配置数据类
 * 支持动态配置通知类型和设置选项，避免硬编码
 */
data class NotificationConfig(
    val categories: List<NotificationCategory> = getDefaultCategories(),
    val reminderTimeOptions: List<ReminderTimeOption> = getDefaultReminderTimeOptions(),
    val medicationReminderTimeOptions: List<ReminderTimeOption> = getDefaultMedicationReminderTimeOptions()
)

/**
 * 通知分类
 */
data class NotificationCategory(
    val id: String,
    val title: String,
    val subtitle: String,
    val icon: String,
    val enabled: Boolean = true,
    val items: List<NotificationItem> = emptyList()
)

/**
 * 通知项目
 */
data class NotificationItem(
    val id: String,
    val title: String,
    val subtitle: String,
    val icon: String,
    val type: NotificationItemType,
    val enabled: Boolean = true,
    val dependsOn: String? = null, // 依赖的父项目ID
    val options: List<NotificationOption> = emptyList()
)

/**
 * 通知项目类型
 */
enum class NotificationItemType {
    SWITCH,      // 开关类型
    TIME_PICKER, // 时间选择器
    DROPDOWN,    // 下拉选择
    INFO         // 信息显示
}

/**
 * 通知选项
 */
data class NotificationOption(
    val value: Any,
    val label: String
)

/**
 * 提醒时间选项
 */
data class ReminderTimeOption(
    val minutes: Int,
    val label: String
)

/**
 * 获取默认通知分类配置
 */
fun getDefaultCategories(): List<NotificationCategory> {
    return listOf(
        // 任务管理提醒
        NotificationCategory(
            id = "task_management",
            title = "任务管理",
            subtitle = "管理任务相关的提醒通知",
            icon = "task",
            items = listOf(
                NotificationItem(
                    id = "task_reminders",
                    title = "任务提醒",
                    subtitle = "开启任务到期前的提醒",
                    icon = "task",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "deadline_reminders",
                    title = "截止日期提醒",
                    subtitle = "任务即将到期时通知",
                    icon = "schedule",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "overdue_reminders",
                    title = "逾期任务提醒",
                    subtitle = "提醒已过期的重要任务",
                    icon = "warning",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "daily_review",
                    title = "每日回顾",
                    subtitle = "每天定时回顾今日任务",
                    icon = "today",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "daily_review_time",
                    title = "回顾时间",
                    subtitle = "21:00",
                    icon = "access_time",
                    type = NotificationItemType.TIME_PICKER,
                    dependsOn = "daily_review"
                )
            )
        ),
        
        // 习惯培养提醒
        NotificationCategory(
            id = "habit_management",
            title = "习惯培养",
            subtitle = "管理习惯培养相关的提醒",
            icon = "loop",
            items = listOf(
                NotificationItem(
                    id = "habit_reminders",
                    title = "习惯提醒",
                    subtitle = "按时提醒培养良好习惯",
                    icon = "loop",
                    type = NotificationItemType.SWITCH
                )
            )
        ),
        
        // 专注时间提醒
        NotificationCategory(
            id = "focus_management",
            title = "专注时间",
            subtitle = "管理专注时间相关的提醒",
            icon = "timer",
            items = listOf(
                NotificationItem(
                    id = "focus_reminders",
                    title = "专注提醒",
                    subtitle = "专注时间开始和结束时通知",
                    icon = "timer",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "focus_session_notifications",
                    title = "专注会话通知",
                    subtitle = "专注会话状态变化时通知",
                    icon = "notifications",
                    type = NotificationItemType.SWITCH,
                    dependsOn = "focus_reminders"
                )
            )
        ),
        
        // 健康管理提醒
        NotificationCategory(
            id = "health_management",
            title = "健康管理",
            subtitle = "管理健康相关的提醒通知",
            icon = "medical_services",
            items = listOf(
                NotificationItem(
                    id = "medication_reminders",
                    title = "用药提醒",
                    subtitle = "按时提醒服用药物，保持健康",
                    icon = "medical_services",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "medication_sound",
                    title = "用药提醒声音",
                    subtitle = "用药提醒时播放声音",
                    icon = "volume_up",
                    type = NotificationItemType.SWITCH,
                    dependsOn = "medication_reminders"
                ),
                NotificationItem(
                    id = "medication_vibration",
                    title = "用药提醒震动",
                    subtitle = "用药提醒时设备震动",
                    icon = "vibration",
                    type = NotificationItemType.SWITCH,
                    dependsOn = "medication_reminders"
                ),
                NotificationItem(
                    id = "medication_advance_time",
                    title = "用药提醒提前时间",
                    subtitle = "提前5分钟",
                    icon = "schedule",
                    type = NotificationItemType.DROPDOWN,
                    dependsOn = "medication_reminders"
                )
            )
        ),
        
        // 通知方式设置
        NotificationCategory(
            id = "notification_style",
            title = "通知方式",
            subtitle = "选择通知的表现形式",
            icon = "notifications",
            items = listOf(
                NotificationItem(
                    id = "sound_enabled",
                    title = "声音提醒",
                    subtitle = "播放通知声音",
                    icon = "volume_up",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "vibration_enabled",
                    title = "震动提醒",
                    subtitle = "设备震动反馈",
                    icon = "vibration",
                    type = NotificationItemType.SWITCH
                )
            )
        ),
        
        // 免打扰设置
        NotificationCategory(
            id = "do_not_disturb",
            title = "免打扰",
            subtitle = "设置静音时段，避免被通知打扰",
            icon = "do_not_disturb",
            items = listOf(
                NotificationItem(
                    id = "do_not_disturb_enabled",
                    title = "免打扰模式",
                    subtitle = "关闭",
                    icon = "do_not_disturb",
                    type = NotificationItemType.SWITCH
                ),
                NotificationItem(
                    id = "do_not_disturb_start_time",
                    title = "开始时间",
                    subtitle = "22:00",
                    icon = "bedtime",
                    type = NotificationItemType.TIME_PICKER,
                    dependsOn = "do_not_disturb_enabled"
                ),
                NotificationItem(
                    id = "do_not_disturb_end_time",
                    title = "结束时间",
                    subtitle = "08:00",
                    icon = "wb_sunny",
                    type = NotificationItemType.TIME_PICKER,
                    dependsOn = "do_not_disturb_enabled"
                )
            )
        ),
        
        // 提醒时间设置
        NotificationCategory(
            id = "reminder_timing",
            title = "提醒时间",
            subtitle = "设置通用的提醒提前时间",
            icon = "schedule",
            items = listOf(
                NotificationItem(
                    id = "default_reminder_time",
                    title = "默认提醒时间",
                    subtitle = "提前15分钟",
                    icon = "schedule",
                    type = NotificationItemType.DROPDOWN
                )
            )
        )
    )
}

/**
 * 获取默认提醒时间选项
 */
fun getDefaultReminderTimeOptions(): List<ReminderTimeOption> {
    return listOf(
        ReminderTimeOption(0, "准时提醒"),
        ReminderTimeOption(5, "提前5分钟"),
        ReminderTimeOption(10, "提前10分钟"),
        ReminderTimeOption(15, "提前15分钟"),
        ReminderTimeOption(30, "提前30分钟"),
        ReminderTimeOption(60, "提前1小时")
    )
}

/**
 * 获取默认用药提醒时间选项
 */
fun getDefaultMedicationReminderTimeOptions(): List<ReminderTimeOption> {
    return listOf(
        ReminderTimeOption(0, "准时提醒"),
        ReminderTimeOption(5, "提前5分钟"),
        ReminderTimeOption(10, "提前10分钟"),
        ReminderTimeOption(15, "提前15分钟"),
        ReminderTimeOption(30, "提前30分钟")
    )
}
