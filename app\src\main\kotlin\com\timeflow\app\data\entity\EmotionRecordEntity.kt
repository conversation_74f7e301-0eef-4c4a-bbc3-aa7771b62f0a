package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.LocalDateConverter
import com.timeflow.app.data.converter.StringListConverter
import java.time.LocalDate

/**
 * 情绪记录数据库实体
 * 用于持久化存储用户的情绪记录数据
 */
@Entity(
    tableName = "emotion_records",
    indices = [
        Index(value = ["date"]),
        Index(value = ["emotion_type"]),
        Index(value = ["is_detailed"])
    ]
)
@TypeConverters(LocalDateConverter::class, StringListConverter::class)
data class EmotionRecordEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "date")
    val date: LocalDate,
    
    @ColumnInfo(name = "emotion_type")
    val emotionType: String, // JOY, CALM, SAD, ANGRY, ANXIOUS
    
    @ColumnInfo(name = "triggers")
    val triggers: List<String> = emptyList(), // 触发因素列表
    
    @ColumnInfo(name = "mindfulness_note")
    val mindfulnessNote: String = "", // 正念笔记
    
    @ColumnInfo(name = "image_uri")
    val imageUri: String? = null, // 图片URI
    
    @ColumnInfo(name = "audio_uri")
    val audioUri: String? = null, // 音频URI
    
    @ColumnInfo(name = "is_detailed")
    val isDetailed: Boolean = false, // 是否为详细记录
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(), // 创建时间戳
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis() // 更新时间戳
)
