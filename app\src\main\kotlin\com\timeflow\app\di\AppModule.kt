package com.timeflow.app.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 应用级基础组件Hilt模块
 * 提供应用全局所需的基础对象
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    // 移除Context提供者，因为Hilt已经提供了@ApplicationContext
    // Context已经由Hilt自动提供，不需要额外声明
} 