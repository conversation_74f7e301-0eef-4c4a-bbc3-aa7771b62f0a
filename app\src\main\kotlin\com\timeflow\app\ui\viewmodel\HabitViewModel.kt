package com.timeflow.app.ui.viewmodel

import android.content.Context
import android.util.Log
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*

import androidx.compose.ui.graphics.Color

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.ui.screen.health.HabitData
import com.timeflow.app.data.repository.HabitRepository
// 移除FrequencyType导入，使用HabitFrequencyType
import com.timeflow.app.data.model.HabitCategory
import com.timeflow.app.data.model.HabitFrequencyType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import javax.inject.Inject
import com.timeflow.app.data.model.HabitModel
import com.timeflow.app.data.model.CustomHabitCategory
import kotlin.random.Random
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.timeflow.app.worker.HabitReminderWorker
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.UUID
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.HabitDeletedEvent
import com.timeflow.app.util.HabitArchivedEvent
import com.timeflow.app.util.HabitStatusChangedEvent
import com.timeflow.app.util.HabitChangeType

/**
 * 习惯模板数据类
 */
data class HabitTemplate(
    val name: String,
    val description: String,
    val icon: String, // 改为String类型存储emoji
    val color: Color,
    val category: HabitCategory,
    val frequencyType: HabitFrequencyType = HabitFrequencyType.DAILY,
    val days: Set<DayOfWeek> = DayOfWeek.values().toSet()
)

/**
 * 习惯管理 ViewModel
 */
@HiltViewModel
class HabitViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    @ApplicationContext private val context: Context
) : ViewModel() {

    // 转换函数：HabitData -> HabitModel
    private fun HabitData.toHabitModel(): HabitModel {
        return HabitModel(
            id = this.id,
            name = this.name,
            description = this.description,
            icon = this.icon,
            color = this.color,
            category = this.category,
            customCategoryId = this.customCategoryId,
            frequencyType = HabitFrequencyType.DAILY, // 默认值
            targetCount = 1,
            reminderEnabled = this.reminderEnabled,
            reminderTime = LocalTime.parse(this.reminderTime),
            currentStreak = this.streak,
            longestStreak = this.streak,
            totalCompletions = this.completedDates.size
        )
    }
    
    // 转换函数：HabitModel -> HabitData  
    private fun HabitModel.toHabitData(): HabitData {
        return HabitData(
            id = this.id,
            name = this.name,
            description = this.description,
            icon = this.icon,
            color = this.color,
            category = this.category,
            customCategoryId = this.customCategoryId,
            frequency = this.frequencyDays, // 🔧 修复：使用正确的频率数据
            completedDates = this.completedDates, // 🔧 修复：使用正确的完成日期数据
            streak = this.currentStreak,
            reminderEnabled = this.reminderEnabled,
            reminderTime = this.reminderTime?.toString() ?: "08:00",
            isActive = this.isActive // 🔧 修复：传递活跃状态
        )
    }

    // 所有习惯列表
    private val _habits = MutableStateFlow<List<HabitData>>(emptyList())
    val habits: StateFlow<List<HabitData>> = _habits.asStateFlow()

    // 当前选中的习惯
    private val _selectedHabit = MutableStateFlow<HabitData?>(null)
    val selectedHabit: StateFlow<HabitData?> = _selectedHabit.asStateFlow()

    // 🔧 添加标志防止重复初始化
    private var isInitialized = false

    // 是否显示添加习惯对话框
    private val _showAddHabitDialog = MutableStateFlow(false)
    val showAddHabitDialog: StateFlow<Boolean> = _showAddHabitDialog.asStateFlow()
    
    // 选中的习惯类别
    private val _selectedCategory = MutableStateFlow<HabitCategory?>(null)
    val selectedCategory: StateFlow<HabitCategory?> = _selectedCategory.asStateFlow()

    // 自定义分类列表
    private val _customCategories = MutableStateFlow<List<CustomHabitCategory>>(emptyList())
    val customCategories: StateFlow<List<CustomHabitCategory>> = _customCategories.asStateFlow()

    // 当前选中的自定义分类
    private val _selectedCustomCategory = MutableStateFlow<CustomHabitCategory?>(null)
    val selectedCustomCategory: StateFlow<CustomHabitCategory?> = _selectedCustomCategory.asStateFlow()

    // 习惯模板列表
    private val _habitTemplates = MutableStateFlow<List<HabitTemplate>>(emptyList())
    val habitTemplates: StateFlow<List<HabitTemplate>> = _habitTemplates.asStateFlow()

    // 可选颜色列表
    private val _availableColors = MutableStateFlow<List<Color>>(emptyList())
    val availableColors: StateFlow<List<Color>> = _availableColors.asStateFlow()

    // 可选图标列表
    private val _availableIcons = MutableStateFlow<List<Pair<String, String>>>(emptyList()) // 改为String类型
    val availableIcons: StateFlow<List<Pair<String, String>>> = _availableIcons.asStateFlow()

    // 当前表单状态
    private val _habitFormState = MutableStateFlow(HabitFormState())
    val habitFormState: StateFlow<HabitFormState> = _habitFormState.asStateFlow()

    init {
        // 初始化习惯模板
        initializeHabitTemplates()
        
        // 初始化可用颜色
        initializeAvailableColors()
        
        // 初始化可用图标
        initializeAvailableIcons()
        
        // 初始化自定义分类
        initializeCustomCategories()
        
        // 加载习惯数据
        loadHabits()
    }

    /**
     * 加载习惯数据 - 简化版本：移除修复逻辑，避免无限循环
     */
    private fun loadHabits() {
        viewModelScope.launch {
            try {
                // 🔧 修复：直接使用Flow监听，并修复customCategoryId为null的数据
                habitRepository.getAllHabitsFlow().collect { habits ->
                    if (habits.isNotEmpty()) {
                        Log.d("HabitViewModel", "从数据库加载了 ${habits.size} 个习惯")
                        val habitDataList = habits.map { it.toHabitData() }

                        // 🔧 修复：检查并修复customCategoryId为null的习惯
                        val fixedHabits = habitDataList.map { habit ->
                            if (habit.customCategoryId == null) {
                                val fixedCategoryId = when (habit.category) {
                                    HabitCategory.HEALTH -> "default_health"
                                    HabitCategory.LEARNING -> "default_learning"
                                    HabitCategory.WORK -> "default_work"
                                    HabitCategory.LIFE -> "default_life"
                                    HabitCategory.FITNESS -> "default_fitness"
                                    HabitCategory.MINDFULNESS -> "default_mindfulness"
                                }
                                Log.d("HabitViewModel", "🔧 修复习惯[${habit.name}]的customCategoryId: null -> $fixedCategoryId")
                                val fixedHabit = habit.copy(customCategoryId = fixedCategoryId)
                                // 异步更新到数据库
                                viewModelScope.launch {
                                    habitRepository.updateHabit(fixedHabit.toHabitModel())
                                }
                                fixedHabit
                            } else {
                                habit
                            }
                        }

                        _habits.value = fixedHabits
                        Log.d("HabitViewModel", "✅ 习惯数据加载完成")
                    } else {
                        // 只在首次启动且数据库为空时加载示例数据
                        if (_habits.value.isEmpty()) {
                            Log.d("HabitViewModel", "数据库为空，加载示例数据")
                            loadSampleHabits()
                        }
                    }
                }
            } catch (e: Exception) {
                // 发生错误时只在列表为空时加载示例数据
                Log.e("HabitViewModel", "加载习惯数据出错: ${e.message}")
                if (_habits.value.isEmpty()) {
                    loadSampleHabits()
                }
            }
        }
    }
    
    /**
     * 手动刷新习惯数据 - 使用一次性查询，避免触发Flow循环
     */
    fun refreshHabits() {
        viewModelScope.launch {
            try {
                Log.d("HabitViewModel", "手动刷新习惯数据")
                val habits = habitRepository.getAllHabits()
                val habitDataList = habits.map { it.toHabitData() }

                // 🔧 修复：检查并修复customCategoryId为null的习惯
                val fixedHabits = habitDataList.map { habit ->
                    if (habit.customCategoryId == null) {
                        val fixedCategoryId = when (habit.category) {
                            HabitCategory.HEALTH -> "default_health"
                            HabitCategory.LEARNING -> "default_learning"
                            HabitCategory.WORK -> "default_work"
                            HabitCategory.LIFE -> "default_life"
                            HabitCategory.FITNESS -> "default_fitness"
                            HabitCategory.MINDFULNESS -> "default_mindfulness"
                        }
                        Log.d("HabitViewModel", "🔧 修复习惯[${habit.name}]的customCategoryId: null -> $fixedCategoryId")
                        val fixedHabit = habit.copy(customCategoryId = fixedCategoryId)
                        // 同步更新到数据库
                        viewModelScope.launch {
                            habitRepository.updateHabit(fixedHabit.toHabitModel())
                        }
                        fixedHabit
                    } else {
                        habit
                    }
                }

                _habits.value = fixedHabits
                Log.d("HabitViewModel", "✅ 手动刷新完成，获取到 ${habits.size} 个习惯")
            } catch (e: Exception) {
                Log.e("HabitViewModel", "刷新习惯数据出错: ${e.message}")
            }
        }
    }

    /**
     * 手动添加示例数据（仅用于开发调试）
     */
    fun addSampleHabitsIfEmpty() {
        if (_habits.value.isEmpty()) {
            Log.d("HabitViewModel", "手动添加示例习惯数据")
            loadSampleHabits()
        } else {
            Log.d("HabitViewModel", "已有习惯数据，跳过示例数据加载")
        }
    }
    
    /**
     * 加载示例习惯数据（修复版本：同时保存到数据库）
     */
    private fun loadSampleHabits() {
        // 🔧 防止重复初始化
        if (isInitialized) {
            Log.d("HabitViewModel", "⚠️ 示例习惯已初始化，跳过重复加载")
            return
        }

        viewModelScope.launch {
            try {
                isInitialized = true
                Log.d("HabitViewModel", "生成示例习惯数据")
                val today = LocalDate.now()
                
                val sampleHabits = listOf(
                    HabitData(
                        id = "sample_1",
                        name = "每日喝水",
                        icon = "💧", // 改为emoji字符串
                        color = Color(0xFF42A5F5),
                        category = HabitCategory.HEALTH,
                        customCategoryId = "default_health", // 🔧 修复：分配到健康分类
                        streak = 3,
                        completedDates = listOf(
                            today.minusDays(1),
                            today.minusDays(2),
                            today.minusDays(3)
                        ),
                        description = "每天至少喝8杯水，保持身体水分充足",
                        customEmoji = "💧",
                        isActive = true
                    ),
                    HabitData(
                        id = "sample_2", 
                        name = "早起运动",
                        icon = "🏃‍♂️", // 改为emoji字符串
                        color = Color(0xFFFF7043),
                        category = HabitCategory.FITNESS,
                        customCategoryId = "default_fitness", // 🔧 修复：分配到健身分类
                        streak = 5,
                        completedDates = listOf(
                            today,
                            today.minusDays(1),
                            today.minusDays(2),
                            today.minusDays(3),
                            today.minusDays(4)
                        ),
                        description = "每天早上7点开始30分钟运动",
                        customEmoji = "🏃‍♂️",
                        isActive = true
                    ),
                    HabitData(
                        id = "sample_3",
                        name = "读书学习",
                        icon = "📚", // 改为emoji字符串
                        color = Color(0xFF66BB6A),
                        category = HabitCategory.LEARNING,
                        customCategoryId = "default_learning", // 🔧 修复：分配到学习分类
                        streak = 2,
                        completedDates = listOf(
                            today.minusDays(1),
                            today.minusDays(2)
                        ),
                        description = "每天阅读至少30分钟，持续学习",
                        customEmoji = "📚",
                        isActive = true
                    ),
                    HabitData(
                        id = "sample_4",
                        name = "冥想放松",
                        icon = "🧘‍♀️", // 改为emoji字符串
                        color = Color(0xFFAB47BC),
                        category = HabitCategory.MINDFULNESS,
                        customCategoryId = "default_mindfulness", // 🔧 修复：分配到冥想分类
                        streak = 1,
                        completedDates = listOf(today.minusDays(1)),
                        description = "每天晚上冥想10分钟，放松身心",
                        customEmoji = "🧘‍♀️",
                        isActive = true
                    ),
                    HabitData(
                        id = "sample_5",
                        name = "整理房间",
                        icon = "🏠", // 新增生活分类的习惯
                        color = Color(0xFFFFC107),
                        category = HabitCategory.LIFE,
                        customCategoryId = "default_life", // 🔧 修复：分配到生活分类
                        streak = 0,
                        completedDates = emptyList(),
                        description = "每天整理房间，保持环境整洁",
                        customEmoji = "🏠",
                        isActive = true
                    ),
                    HabitData(
                        id = "sample_6",
                        name = "工作复盘",
                        icon = "💼", // 新增工作分类的习惯
                        color = Color(0xFF4CAF50),
                        category = HabitCategory.WORK,
                        customCategoryId = "default_work", // 🔧 修复：分配到工作分类
                        streak = 2,
                        completedDates = listOf(
                            today.minusDays(1),
                            today.minusDays(2)
                        ),
                        description = "每天工作结束后进行复盘总结",
                        customEmoji = "💼",
                        isActive = true
                    )
                )

                // 🔧 添加一个关联目标的测试习惯
                val testGoalId = "1" // 使用示例目标ID "1" (每日运动30分钟)
                val habitWithGoal = HabitData(
                    id = "habit_with_goal_test",
                    name = "每日运动习惯",
                    icon = "🏃‍♂️",
                    color = Color(0xFF4CAF50),
                    category = HabitCategory.FITNESS,
                    customCategoryId = "default_fitness",
                    streak = 3,
                    completedDates = listOf(today, today.minusDays(1), today.minusDays(2)),
                    description = "支持每日运动30分钟目标的习惯",
                    customEmoji = "🏃‍♂️",
                    isActive = true
                )

                // 将测试习惯添加到列表中
                val allHabits = sampleHabits + habitWithGoal
                
                // 🔥 关键修复：不仅要设置内存状态，还要保存到数据库
                Log.d("HabitViewModel", "开始将示例习惯保存到数据库...")
                
                // 🔧 修复：使用顺序保存避免协程竞争
                Log.d("HabitViewModel", "开始批量保存 ${allHabits.size} 个习惯到数据库")
                for ((index, habit) in allHabits.withIndex()) {
                    try {
                        val habitModel = habit.toHabitModel()
                        // 🔧 为测试习惯设置关联目标
                        val finalHabitModel = if (habit.id == "habit_with_goal_test") {
                            habitModel.copy(
                                relatedGoalId = testGoalId,
                                relatedGoalTitle = "每日运动30分钟"
                            )
                        } else {
                            habitModel
                        }

                        // 使用延迟避免过快的连续调用
                        if (index > 0) {
                            kotlinx.coroutines.delay(10) // 10ms延迟
                        }

                        habitRepository.addHabit(finalHabitModel)
                        Log.d("HabitViewModel", "✓ 保存习惯到数据库 (${index + 1}/${allHabits.size}): ${habit.name} ${if (habit.id == "habit_with_goal_test") "(关联目标: $testGoalId)" else ""}")
                    } catch (e: Exception) {
                        Log.e("HabitViewModel", "❌ 保存习惯失败 (${index + 1}/${allHabits.size}): ${habit.name}, 错误: ${e.message}", e)
                    }
                }

                // 设置到内存状态
                _habits.value = allHabits
                Log.d("HabitViewModel", "成功加载 ${allHabits.size} 个示例习惯并保存到数据库")
                
            } catch (e: Exception) {
                Log.e("HabitViewModel", "加载示例习惯数据失败: ${e.message}", e)
                // 🔧 重置初始化标志，允许重试
                isInitialized = false
            }
        }
    }

    /**
     * 根据ID获取习惯详情
     */
    fun getHabitById(habitId: String): HabitData? {
        return _habits.value.find { it.id == habitId }
    }

    /**
     * 选择一个习惯
     */
    fun selectHabit(habitId: String) {
        viewModelScope.launch {
            val habit = _habits.value.find { it.id == habitId }
            _selectedHabit.value = habit
        }
    }

    /**
     * 添加一个新习惯（修复版本：确保数据库操作成功后再更新UI状态）
     */
    fun addHabit(habit: HabitData) {
        viewModelScope.launch {
            try {
                Log.d("HabitViewModel", "🔧 开始添加习惯: name=${habit.name}, customCategoryId=${habit.customCategoryId}")

                // 🔥 关键修复：先执行数据库操作，成功后再更新UI状态
                val success = habitRepository.addHabit(habit.toHabitModel())

                if (success) {
                    // 数据库操作成功，更新内存状态
                    val updatedList = _habits.value.toMutableList()
                    updatedList.add(habit)
                    _habits.value = updatedList

                    Log.d("HabitViewModel", "✓ 习惯添加成功: name=${habit.name}, customCategoryId=${habit.customCategoryId}")
                    Log.d("HabitViewModel", "✓ 当前习惯列表大小: ${_habits.value.size}")

                    // 🔧 添加详细的习惯列表调试信息
                    _habits.value.forEach { h ->
                        Log.d("HabitViewModel", "✓ 习惯列表中的习惯: name=${h.name}, customCategoryId=${h.customCategoryId}")
                    }

                    // 🔧 强制刷新数据以确保UI同步
                    delay(100) // 给数据库一点时间
                    refreshHabits()
                } else {
                    Log.e("HabitViewModel", "❌ 数据库添加操作失败: name=${habit.name}")
                }
            } catch (e: Exception) {
                Log.e("HabitViewModel", "❌ 添加习惯失败: ${e.message}", e)
            }
        }
    }

    /**
     * 更新习惯信息
     */
    fun updateHabit(updatedHabit: HabitData) {
        val updatedList = _habits.value.toMutableList()
        val index = updatedList.indexOfFirst { it.id == updatedHabit.id }
        if (index != -1) {
            updatedList[index] = updatedHabit
            _habits.value = updatedList

            // 如果当前选中的是被更新的习惯，同步更新选中的习惯
            if (_selectedHabit.value?.id == updatedHabit.id) {
                _selectedHabit.value = updatedHabit
            }
        }
    }

    /**
     * 删除习惯（修复版本：确保数据库操作成功后再更新UI状态）
     */
    fun deleteHabit(habitId: String) {
        viewModelScope.launch {
            try {
                // 获取要删除的习惯信息，用于事件通知
                val habitToDelete = _habits.value.find { it.id == habitId }
                val relatedGoalId = habitToDelete?.customCategoryId // 获取习惯关联的目标ID（临时使用customCategoryId）
                
                Log.d("HabitViewModel", "开始删除习惯: habitId=$habitId, name=${habitToDelete?.name}")
                
                // 🔥 关键修复：先执行数据库操作，成功后再更新UI状态
                val success = habitRepository.deleteHabit(habitId)
                
                if (success) {
                    // 数据库操作成功，更新内存状态
                    val updatedList = _habits.value.filter { it.id != habitId }
                    _habits.value = updatedList

                    // 如果删除的是当前选中的习惯，清除选中状态
                    if (_selectedHabit.value?.id == habitId) {
                        _selectedHabit.value = null
                    }
                    
                    // 发送删除事件，通知关联的页面同步更新
                    NotificationCenter.post(HabitDeletedEvent(habitId, relatedGoalId))
                    NotificationCenter.post(HabitStatusChangedEvent(habitId, HabitChangeType.DELETED, relatedGoalId))
                    
                    Log.d("HabitViewModel", "✓ 习惯删除成功，已发送事件通知: habitId=$habitId, relatedGoalId=$relatedGoalId")
                } else {
                    Log.e("HabitViewModel", "数据库删除操作失败: habitId=$habitId")
                }
            } catch (e: Exception) {
                Log.e("HabitViewModel", "删除习惯失败: ${e.message}", e)
            }
        }
    }

    /**
     * 归档习惯（设置为非活跃状态）
     */
    fun archiveHabit(habitId: String) {
        val habit = _habits.value.find { it.id == habitId } ?: return
        val relatedGoalId = habit.customCategoryId // 获取习惯关联的目标ID（临时使用customCategoryId）
        
        val updatedHabit = habit.copy(isActive = false)
        updateHabit(updatedHabit)
        
        // 如果归档的是当前选中的习惯，清除选中状态
        if (_selectedHabit.value?.id == habitId) {
            _selectedHabit.value = null
        }
        
        // 🔥 发送习惯归档事件，通知关联的目标页面同步更新
        viewModelScope.launch {
            try {
                // 更新数据库中的习惯状态
                habitRepository.updateHabit(updatedHabit.toHabitModel())
                
                // 发送归档事件
                NotificationCenter.post(HabitArchivedEvent(habitId, relatedGoalId))
                NotificationCenter.post(HabitStatusChangedEvent(habitId, HabitChangeType.ARCHIVED, relatedGoalId))
                
                Log.d("HabitViewModel", "✓ 习惯归档成功，已发送事件通知: habitId=$habitId, relatedGoalId=$relatedGoalId")
            } catch (e: Exception) {
                Log.e("HabitViewModel", "归档习惯失败: ${e.message}", e)
            }
        }
    }

    /**
     * 恢复习惯（设置为活跃状态）
     */
    fun unarchiveHabit(habitId: String) {
        val habit = _habits.value.find { it.id == habitId } ?: return
        val updatedHabit = habit.copy(isActive = true)
        updateHabit(updatedHabit)
    }
    
    /**
     * 🔧 新增：恢复习惯（撤销删除或归档操作）
     */
    fun restoreHabit(habitId: String) {
        viewModelScope.launch {
            try {
                // 调用Repository恢复习惯（将isActive设为true）
                val success = habitRepository.restoreHabit(habitId)
                
                if (success) {
                    // 恢复成功，刷新习惯列表
                    refreshHabits()
                    Log.d("HabitViewModel", "✓ 习惯恢复成功: habitId=$habitId")
                } else {
                    Log.w("HabitViewModel", "恢复习惯失败: habitId=$habitId")
                }
            } catch (e: Exception) {
                Log.e("HabitViewModel", "恢复习惯失败: ${e.message}", e)
            }
        }
    }

    /**
     * 获取活跃的习惯列表
     */
    fun getActiveHabits(): List<HabitData> {
        return _habits.value.filter { it.isActive }
    }

    /**
     * 获取已归档的习惯列表
     */
    fun getArchivedHabits(): List<HabitData> {
        return _habits.value.filter { !it.isActive }
    }
    
    /**
     * 根据目标ID获取关联的习惯
     */
    fun getHabitsByGoalId(goalId: String): kotlinx.coroutines.flow.Flow<List<HabitModel>> {
        Log.d("HabitViewModel", "🔍 开始查询目标关联习惯: goalId=$goalId")
        return habitRepository.getHabitsByGoalIdFlow(goalId)
            .map { habits ->
                Log.d("HabitViewModel", "✅ 从Repository Flow获取到 ${habits.size} 个关联习惯")
                habits.forEachIndexed { index, habit ->
                    Log.d("HabitViewModel", "习惯${index + 1}: ${habit.name} (ID: ${habit.id}, 关联目标ID: ${habit.relatedGoalId}, 关联目标标题: ${habit.relatedGoalTitle})")
                }
                habits
            }
            .catch { e ->
                Log.e("HabitViewModel", "❌ 获取目标关联习惯失败: goalId=$goalId, 错误: ${e.message}", e)
                emit(emptyList())
            }
    }

    /**
     * 标记习惯为已完成
     */
    fun markHabitAsCompleted(habitId: String, date: LocalDate = LocalDate.now(), completed: Boolean = true) {
        val habit = _habits.value.find { it.id == habitId } ?: return
        val completedDates = habit.completedDates.toMutableList()
        
        if (completed && !completedDates.contains(date)) {
            // 添加日期到完成列表
            completedDates.add(date)
        } else if (!completed && completedDates.contains(date)) {
            // 从完成列表中移除日期
            completedDates.remove(date)
        } else {
            // 无需更改
            return
        }
        
        // 对日期进行排序
        completedDates.sortBy { it }
        
        // 计算最新的连续天数
        var streak = 0
        var currentDate = LocalDate.now()
        
        while (completedDates.contains(currentDate)) {
            streak++
            currentDate = currentDate.minusDays(1)
        }
        
        // 更新习惯数据
        val updatedHabit = habit.copy(
            completedDates = completedDates,
            streak = streak
        )
        
        // 更新列表
        updateHabit(updatedHabit)
        
        // 同步到Repository
        viewModelScope.launch {
            habitRepository.markHabitAsCompleted(habitId, date, completed)
        }
    }

    /**
     * 设置是否显示添加习惯对话框
     */
    fun setShowAddHabitDialog(show: Boolean) {
        _showAddHabitDialog.value = show
    }
    
    /**
     * 设置选中的习惯类别
     */
    fun setSelectedCategory(category: HabitCategory?) {
        _selectedCategory.value = category
    }
    
    /**
     * 根据类别获取对应的习惯列表
     */
    fun getHabitsByCategory(category: HabitCategory?): List<HabitData> {
        return if (category == null) {
            _habits.value
        } else {
            _habits.value.filter { it.category == category }
        }
    }

    // 初始化习惯模板
    private fun initializeHabitTemplates() {
        val templates = listOf(
            HabitTemplate(
                name = "每日喝水",
                description = "每天喝够8杯水",
                icon = "💧", // 改为emoji字符串
                color = Color(0xFF29B6F6),
                category = HabitCategory.HEALTH
            ),
            HabitTemplate(
                name = "早起",
                description = "每天早上6点起床",
                icon = "🌅", // 改为emoji字符串
                color = Color(0xFFFFA726),
                category = HabitCategory.LIFE
            ),
            HabitTemplate(
                name = "阅读",
                description = "每天阅读30分钟",
                icon = "📚", // 改为emoji字符串
                color = Color(0xFF66BB6A),
                category = HabitCategory.LEARNING,
                frequencyType = HabitFrequencyType.DAILY
            ),
            HabitTemplate(
                name = "健身",
                description = "规律锻炼，保持健康",
                icon = "💪", // 改为emoji字符串
                color = Color(0xFFd7c9e8),
                category = HabitCategory.FITNESS,
                frequencyType = HabitFrequencyType.WEEKLY,
                days = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY)
            ),
            HabitTemplate(
                name = "冥想",
                description = "每天冥想10分钟",
                icon = "🧘‍♀️", // 改为emoji字符串
                color = Color(0xFFdcead1),
                category = HabitCategory.MINDFULNESS
            )
        )
        _habitTemplates.value = templates
    }

    // 初始化可用颜色
    private fun initializeAvailableColors() {
        val colors = listOf(
            Color(0xFFf5c4c4), // 健康粉红
            Color(0xFFb6d9f2), // 学习蓝
            Color(0xFFb6e2de), // 工作青
            Color(0xFFfbe3c2), // 生活橙
            Color(0xFFd7c9e8), // 健身紫
            Color(0xFFdcead1), // 冥想绿
            Color(0xFF81D4FA), // 浅蓝
            Color(0xFFFFCC80), // 浅橙
            Color(0xFFA5D6A7), // 浅绿
            Color(0xFFE1BEE7), // 浅紫
            Color(0xFFF48FB1), // 浅粉
            Color(0xFFFFAB91)  // 浅珊瑚
        )
        _availableColors.value = colors
    }

    // 初始化可用图标
    private fun initializeAvailableIcons() {
        val icons = listOf(
            "❤️" to "健康",
            "💧" to "喝水", 
            "🌅" to "早晨",
            "🛏️" to "睡眠",
            "🏃‍♂️" to "跑步",
            "💪" to "健身",
            "🍽️" to "饮食",
            "🎓" to "学习",
            "📚" to "阅读",
            "💻" to "编程",
            "🎵" to "音乐",
            "🎨" to "艺术",
            "💼" to "工作",
            "🏠" to "家务",
            "🧘‍♀️" to "冥想",
            "🏆" to "成就",
            "💰" to "存钱",
            "📞" to "通话",
            "✍️" to "写作",
            "🚫🍺" to "戒酒"
        )
        _availableIcons.value = icons
    }

    // 使用模板初始化表单
    fun useHabitTemplate(template: HabitTemplate) {
        _habitFormState.value = HabitFormState(
            name = template.name,
            description = template.description,
            category = template.category,
            icon = template.icon,
            color = template.color,
            frequencyType = template.frequencyType,
            selectedDays = template.days
        )
    }

    // 重置表单状态
    fun resetHabitForm() {
        _habitFormState.value = HabitFormState()
    }

    // 更新表单字段
    fun updateHabitFormField(fieldName: String, value: Any?) {
        val currentState = _habitFormState.value
        
        _habitFormState.value = when(fieldName) {
            "name" -> currentState.copy(name = value as String)
            "description" -> currentState.copy(description = value as String)
            "category" -> currentState.copy(category = value as HabitCategory)
            "icon" -> currentState.copy(icon = value as String) // 改为String类型
            "color" -> currentState.copy(color = value as Color)
            "frequencyType" -> currentState.copy(frequencyType = value as HabitFrequencyType)
            "enableReminder" -> currentState.copy(enableReminder = value as Boolean)
            "reminderTime" -> currentState.copy(reminderTime = value as String)
            "showInPlan" -> currentState.copy(showInPlan = value as Boolean)
            "isQuickMode" -> currentState.copy(isQuickMode = value as Boolean)
            else -> currentState
        }
    }

    // 更新选择的星期几
    fun updateSelectedDays(day: DayOfWeek, selected: Boolean) {
        val currentDays = _habitFormState.value.selectedDays
        val newDays = if (selected) currentDays + day else currentDays - day
        if (newDays.isNotEmpty()) { // 确保至少选择一天
            _habitFormState.value = _habitFormState.value.copy(selectedDays = newDays)
        }
    }

    // 创建习惯并设置提醒
    fun createHabitFromForm() {
        val form = _habitFormState.value

        // 生成随机ID
        val habitId = UUID.randomUUID().toString()

        // 🔧 修复：根据选择的分类设置customCategoryId
        val customCategoryId = when (form.category) {
            HabitCategory.HEALTH -> "default_health"
            HabitCategory.LEARNING -> "default_learning"
            HabitCategory.WORK -> "default_work"
            HabitCategory.LIFE -> "default_life"
            HabitCategory.FITNESS -> "default_fitness"
            HabitCategory.MINDFULNESS -> "default_mindfulness"
        }

        // 创建新习惯对象
        val newHabit = HabitData(
            id = habitId,
            name = form.name,
            icon = form.icon,
            color = form.color,
            category = form.category,
            customCategoryId = customCategoryId, // 🔧 修复：设置customCategoryId
            description = form.description,
            frequency = form.selectedDays.toList(),
            reminderEnabled = form.enableReminder,
            reminderTime = form.reminderTime ?: "08:00"
        )

        Log.d("HabitViewModel", "🔧 createHabitFromForm: name=${newHabit.name}, customCategoryId=${newHabit.customCategoryId}")

        // 添加习惯
        addHabit(newHabit)

        // 如果开启了提醒，设置定时提醒
        if (form.enableReminder) {
            scheduleHabitReminder(newHabit)
        }

        // 重置表单状态
        resetHabitForm()
    }

    // 设置习惯提醒
    private fun scheduleHabitReminder(habit: HabitData) {
        viewModelScope.launch {
            try {
                Log.d("HabitViewModel", "为习惯 ${habit.name} 设置提醒，时间: ${habit.reminderTime}")
                
                // 创建WorkManager请求数据
                val inputData = Data.Builder()
                    .putString(HabitReminderWorker.KEY_HABIT_ID, habit.id)
                    .putString(HabitReminderWorker.KEY_HABIT_NAME, habit.name)
                    .putString(HabitReminderWorker.KEY_REMINDER_TIME, habit.reminderTime)
                    .build()
                
                // 创建一次性工作请求
                val reminderRequest = OneTimeWorkRequestBuilder<HabitReminderWorker>()
                    .setInputData(inputData)
                    .build()
                
                // 通过WorkManager调度工作
                WorkManager.getInstance(context)
                    .enqueue(reminderRequest)
                
                Log.d("HabitViewModel", "习惯提醒设置完成: ${habit.id}")
            } catch (e: Exception) {
                Log.e("HabitViewModel", "设置习惯提醒失败: ${e.message}", e)
            }
        }
    }

    // 验证表单字段
    fun validateForm(): Map<String, String?> {
        val form = _habitFormState.value
        val errors = mutableMapOf<String, String?>()
        
        // 名称验证
        if (form.name.isBlank()) {
            errors["name"] = "习惯名称不能为空"
        }
        
        // 其他验证逻辑...
        
        return errors
    }

    /**
     * 更新习惯排序顺序
     */
    fun updateHabitsOrder(reorderedHabits: List<HabitData>) {
        viewModelScope.launch {
            val currentHabits = _habits.value.toMutableList()
            
            // 更新内存中的数据
            _habits.value = reorderedHabits.toList()
            
            // 在后台更新数据库的排序顺序
            // 这里可以添加实际的数据库保存逻辑，比如更新order字段等
            // 示例：repository.updateHabitsOrder(reorderedHabits)
        }
    }

    /**
     * 添加习惯完成日期
     */
    fun addCompletedDate(habitId: String, date: LocalDate) {
        viewModelScope.launch {
            try {
                val habit = _habits.value.find { it.id == habitId } ?: return@launch
                
                // 检查该日期是否已经在完成列表中
                if (habit.completedDates.contains(date)) {
                    return@launch
                }
                
                // 添加到完成日期列表
                val updatedCompletedDates = habit.completedDates.toMutableList()
                updatedCompletedDates.add(date)
                
                // 更新习惯状态
                val updatedHabit = habit.copy(completedDates = updatedCompletedDates)
                updateHabit(updatedHabit)
                
                // 保存到数据库
                habitRepository.updateHabit(updatedHabit.toHabitModel())
                
                Log.d("HabitViewModel", "习惯[${habit.name}]添加完成日期: $date")
            } catch (e: Exception) {
                Log.e("HabitViewModel", "添加习惯完成日期失败: ${e.message}")
            }
        }
    }
    
    /**
     * 移除习惯完成日期
     */
    fun removeCompletedDate(habitId: String, date: LocalDate) {
        viewModelScope.launch {
            try {
                val habit = _habits.value.find { it.id == habitId } ?: return@launch
                
                // 检查该日期是否在完成列表中
                if (!habit.completedDates.contains(date)) {
                    return@launch
                }
                
                // 从完成日期列表中移除
                val updatedCompletedDates = habit.completedDates.toMutableList()
                updatedCompletedDates.remove(date)
                
                // 更新习惯状态
                val updatedHabit = habit.copy(completedDates = updatedCompletedDates)
                updateHabit(updatedHabit)
                
                // 保存到数据库
                habitRepository.updateHabit(updatedHabit.toHabitModel())
                
                Log.d("HabitViewModel", "习惯[${habit.name}]移除完成日期: $date")
            } catch (e: Exception) {
                Log.e("HabitViewModel", "移除习惯完成日期失败: ${e.message}")
            }
        }
    }

    /**
     * 获取指定日期的习惯列表
     */
    fun getHabitsForDate(date: LocalDate): List<HabitData> {
        // 在函数内同步获取当前值，而不是使用Flow订阅，避免在非主线程中访问状态
        val currentHabits = _habits.value
        
        return currentHabits.filter { habit ->
            // 如果没有设置特定的频率，则假设每天都需要完成
            if (habit.frequency.isEmpty()) {
                true
            } else {
                // 检查当前日期的星期是否在习惯的频率列表中
                habit.frequency.contains(date.dayOfWeek)
            }
        }
    }

    // 习惯表单状态数据类
    data class HabitFormState(
        val name: String = "",
        val description: String = "",
        val category: HabitCategory = HabitCategory.HEALTH,
        val icon: String = "❤️", // 改为String类型存储emoji
        val color: Color = Color(0xFFf5c4c4),
        val frequencyType: HabitFrequencyType = HabitFrequencyType.DAILY,
        val selectedDays: Set<DayOfWeek> = DayOfWeek.values().toSet(),
        val enableReminder: Boolean = false,
        val reminderTime: String? = "08:00",
        val isQuickMode: Boolean = false,
        val showInPlan: Boolean = true,
        val viewStyleIndex: Int = 0,
        val heatmapColorIndex: Int = 0,
        val startDate: LocalDate = LocalDate.now(),
        val customEmoji: String = ""
    )

    // ====================== 自定义分类管理方法 ======================

    /**
     * 初始化自定义分类
     */
    fun initializeCustomCategories() {
        Log.d("HabitViewModel", "🔧 初始化自定义分类")
        _customCategories.value = CustomHabitCategory.defaultCategories
        Log.d("HabitViewModel", "🔧 自定义分类初始化完成，数量: ${_customCategories.value.size}")
        _customCategories.value.forEach { category ->
            Log.d("HabitViewModel", "🔧 默认分类: ${category.title} (${category.id})")
        }
    }

    /**
     * 添加新的自定义分类
     */
    fun addCustomCategory(
        title: String,
        icon: String = "🏷️", // 改为String类型存储emoji
        color: Color = Color(0xFFB6D9F2)
    ) {
        val newCategory = CustomHabitCategory(
            title = title,
            icon = icon,
            color = color,
            sortOrder = _customCategories.value.size + 1
        )
        
        val updatedCategories = _customCategories.value.toMutableList()
        updatedCategories.add(newCategory)
        _customCategories.value = updatedCategories
        
        Log.d("HabitViewModel", "添加新分类: $title")
    }

    /**
     * 删除自定义分类
     */
    fun deleteCustomCategory(categoryId: String) {
        val category = _customCategories.value.find { it.id == categoryId }
        
        // 不允许删除默认分类
        if (category?.isDefault == true) {
            Log.w("HabitViewModel", "无法删除默认分类: ${category.title}")
            return
        }
        
        // 检查是否有习惯正在使用这个分类
        val habitsUsingCategory = _habits.value.filter { it.customCategoryId == categoryId }
        if (habitsUsingCategory.isNotEmpty()) {
            Log.w("HabitViewModel", "无法删除分类，还有 ${habitsUsingCategory.size} 个习惯正在使用")
            return
        }
        
        val updatedCategories = _customCategories.value.filter { it.id != categoryId }
        _customCategories.value = updatedCategories
        
        Log.d("HabitViewModel", "删除分类: ${category?.title}")
    }

    /**
     * 更新自定义分类
     */
    fun updateCustomCategory(
        categoryId: String,
        title: String? = null,
        icon: String? = null, // 改为String类型
        color: Color? = null
    ) {
        val updatedCategories = _customCategories.value.map { category ->
            if (category.id == categoryId) {
                category.copy(
                    title = title ?: category.title,
                    icon = icon ?: category.icon,
                    color = color ?: category.color,
                    updatedAt = java.time.LocalDateTime.now()
                )
            } else {
                category
            }
        }
        _customCategories.value = updatedCategories
    }

    /**
     * 设置选中的自定义分类
     */
    fun setSelectedCustomCategory(category: CustomHabitCategory?) {
        _selectedCustomCategory.value = category
        // 清空旧的选中分类
        _selectedCategory.value = null
    }

    /**
     * 重新排序自定义分类
     */
    fun reorderCustomCategories(reorderedCategories: List<CustomHabitCategory>) {
        val updatedCategories = reorderedCategories.mapIndexed { index, category ->
            category.copy(sortOrder = index + 1)
        }
        _customCategories.value = updatedCategories
    }

    /**
     * 根据自定义分类过滤习惯
     */
    fun getHabitsByCustomCategory(categoryId: String?): List<HabitData> {
        return if (categoryId == null) {
            _habits.value // 返回所有习惯
        } else {
            _habits.value.filter { it.customCategoryId == categoryId }
        }
    }

    /**
     * 获取分类中的习惯数量
     */
    fun getCategoryHabitCount(categoryId: String): Int {
        return _habits.value.count { it.customCategoryId == categoryId }
    }

    /**
     * 测试目标关联功能
     */
    fun testGoalAssociation(goalId: String) {
        viewModelScope.launch {
            try {
                Log.d("HabitViewModel", "🧪 开始测试目标关联功能: goalId=$goalId")

                // 获取关联的习惯
                getHabitsByGoalId(goalId).collect { habits ->
                    Log.d("HabitViewModel", "🧪 测试结果: 找到 ${habits.size} 个关联习惯")
                    habits.forEach { habit ->
                        Log.d("HabitViewModel", "🧪 关联习惯: ${habit.name} (ID: ${habit.id}, 关联目标: ${habit.relatedGoalId})")
                    }

                    // 🔧 如果没有找到关联习惯，检查数据库中是否有我们的测试习惯
                    if (habits.isEmpty()) {
                        Log.w("HabitViewModel", "⚠️ 未找到关联习惯，可能需要重新初始化测试数据")
                        // 可以在这里触发重新加载示例数据
                        loadSampleHabits()
                    }
                }
            } catch (e: Exception) {
                Log.e("HabitViewModel", "🧪 测试目标关联功能失败: ${e.message}", e)
            }
        }
    }
}