package com.timeflow.app.ui.timetracking.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.NavigationBarDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.UrgencyColors
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.timetracking.Task
import com.timeflow.app.ui.timetracking.TimerState
import com.timeflow.app.data.model.Priority
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import com.timeflow.app.data.repository.SharedPendingDeletionState
import javax.inject.Inject

/**
 * 解析任务描述字段中可能存在的颜色JSON信息
 * 返回Pair<String, Color?>，第一个为过滤后的描述文本，第二个为解析出的颜色（可能为null）
 */
private fun parseColorFromDescription(description: String): Pair<String, Color?> {
    // 正则表达式匹配颜色JSON格式
    val colorPattern = """\{"color":(\d+)\}""".toRegex()
    val matchResult = colorPattern.find(description)
    
    return if (matchResult != null) {
        // 从描述中提取颜色值
        val colorValue = matchResult.groupValues[1].toLongOrNull()
        val color = colorValue?.let { Color(it.toInt()) }
        
        // 移除颜色JSON字符串，返回纯文本描述
        val cleanDescription = description.replace(colorPattern, "").trim()
        
        android.util.Log.d("TimeTrackingComponents", "解析到颜色: $colorValue, 净化后描述: $cleanDescription")
        Pair(cleanDescription, color)
    } else {
        // 没有找到颜色信息，返回原始描述
        Pair(description, null)
    }
}

/**
 * 底部导航栏组件
 */
@Composable
fun BottomNavigationBar(
    selectedTab: String,
    onTabSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val tabs = listOf("任务", "日历", "时间", "统计", "我的")
    
    NavigationBar(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp),
        containerColor = Color.White,
        contentColor = Color.Black
    ) {
        tabs.forEach { tab ->
            val isSelected = tab == selectedTab
            
            NavigationBarItem(
                icon = { },  // 空图标位，保持结构一致但不显示图标
                label = { Text(tab, fontSize = 11.sp) },
                selected = isSelected,
                onClick = { if (!isSelected) onTabSelected(tab) },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = Color(0xFF6571FF),
                    selectedTextColor = Color(0xFF6571FF),
                    indicatorColor = Color.White
                )
            )
        }
    }
}

/**
 * 任务选择器弹窗
 * 增强版：实时同步任务列表页面的删除和完成状态，集成全局待删除状态过滤
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskSelectorDialog(
    onDismiss: () -> Unit,
    onSelectTask: (Task) -> Unit,
    currentTaskId: String? = null,
    taskListViewModel: TaskListViewModel = hiltViewModel()
) {
    // 获取Context
    val context = LocalContext.current
    
    // 获取任务列表状态 - 实时监听变化
    val taskListState by taskListViewModel.taskListState.collectAsState()
    
    // 🔧 新增：监听全局待删除状态
    val pendingDeletions by taskListViewModel.pendingDeletions.collectAsState()
    
    // 添加手动刷新状态
    var isRefreshing by remember { mutableStateOf(false) }
    
    // 添加快速添加任务的状态
    var showQuickAddDialog by remember { mutableStateOf(false) }
    var isCreatingTask by remember { mutableStateOf(false) }
    
    // 🔧 增强的任务过滤逻辑：集成全局待删除状态过滤
    val availableTasks = remember(taskListState.tasks, taskListState.lastUpdateTime, pendingDeletions) {
        android.util.Log.d("TaskSelectorDialog", "开始过滤任务，总任务数: ${taskListState.tasks.size}, 待删除任务数: ${pendingDeletions.size}")
        
        val filtered = taskListState.tasks.filter { task ->
            val isValid = 
                // 1. 排除已完成的任务
                !task.isCompleted &&
                // 2. 确保任务ID有效
                task.id.isNotEmpty() &&
                // 3. 确保任务标题不为空
                task.title.isNotBlank() &&
                // 4. 确保任务应该在任务列表中显示（排除被隐藏的子任务等）
                task.displayInTaskList &&
                // 5. 排除被标记为已删除的任务（如果有这个标识）
                (task.type != "DELETED") &&
                // 6. 确保不是已归档的任务
                (task.type != "ARCHIVED") &&
                // 🔧 7. 排除处于待删除状态的任务（关键修复）
                !pendingDeletions.containsKey(task.id)
                
            if (!isValid) {
                val reason = when {
                    task.isCompleted -> "已完成"
                    task.id.isEmpty() -> "ID无效"
                    task.title.isBlank() -> "标题为空"
                    !task.displayInTaskList -> "不显示在列表中"
                    task.type == "DELETED" -> "标记为删除"
                    task.type == "ARCHIVED" -> "已归档"
                    pendingDeletions.containsKey(task.id) -> "待删除状态"
                    else -> "其他原因"
                }
                android.util.Log.d("TaskSelectorDialog", "任务 ${task.id}(${task.title}) 被过滤: $reason")
            }
            
            isValid
        }
        
        android.util.Log.d("TaskSelectorDialog", "过滤后可用任务数: ${filtered.size}")
        filtered
    }
    
    // 🔧 监听任务变化，当当前选中的任务被删除或完成时，给出提示
    LaunchedEffect(taskListState.tasks, currentTaskId, pendingDeletions) {
        if (currentTaskId != null) {
            val currentTask = taskListState.tasks.find { it.id == currentTaskId }
            when {
                currentTask == null -> {
                    android.util.Log.w("TaskSelectorDialog", "当前任务(ID: $currentTaskId)已被删除")
                }
                currentTask.isCompleted -> {
                    android.util.Log.w("TaskSelectorDialog", "当前任务(ID: $currentTaskId)已完成")
                }
                pendingDeletions.containsKey(currentTaskId) -> {
                    android.util.Log.w("TaskSelectorDialog", "当前任务(ID: $currentTaskId)处于待删除状态")
                }
            }
        }
    }
    
    // 🔧 强制刷新任务列表以确保数据最新
    LaunchedEffect(Unit) {
        android.util.Log.d("TaskSelectorDialog", "任务选择器打开，主动刷新任务列表")
        taskListViewModel.refreshTasks()
    }
    
    // 手动刷新函数
    val handleManualRefresh = {
        if (!isRefreshing) {
            isRefreshing = true
            android.util.Log.d("TaskSelectorDialog", "用户手动刷新任务列表")
            taskListViewModel.refreshTasks()
            
            // 模拟刷新延迟，提供用户反馈
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                kotlinx.coroutines.delay(500) // 0.5秒延迟
                isRefreshing = false
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "选择任务",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = MaterialTheme.colorScheme.primary
                )
                
                Row {
                    // 🔧 新增刷新按钮
                    IconButton(
                        onClick = handleManualRefresh,
                        modifier = Modifier.size(32.dp)
                    ) {
                        if (isRefreshing) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp,
                                color = Color(0xFFCCAEC5)
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "刷新任务列表",
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                    
                    // 快速添加任务按钮
                    IconButton(
                        onClick = { showQuickAddDialog = true },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "快速添加任务",
                            tint = Color(0xFFCCAEC5),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        },
        text = {
            if (taskListState.isLoading && availableTasks.isEmpty()) {
                // 加载状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFFCCAEC5),
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "正在加载任务...",
                            style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                            color = Color.Gray
                        )
                    }
                }
            } else if (availableTasks.isEmpty()) {
                // 空状态提示 - 优化提示信息
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Assignment,
                            contentDescription = null,
                            tint = Color.LightGray,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "暂无可选任务",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 🔧 根据实际情况显示不同的提示
                        val totalTasks = taskListState.tasks.size
                        val completedTasks = taskListState.tasks.count { it.isCompleted }
                        val pendingDeletionTasks = pendingDeletions.size
                        
                        val explanationText = when {
                            totalTasks == 0 -> "还没有创建任务"
                            completedTasks == totalTasks -> "所有任务都已完成"
                            pendingDeletionTasks > 0 -> "部分任务处于删除状态"
                            else -> "所有任务都不可用"
                        }
                        
                        Text(
                            text = explanationText,
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 快速添加按钮
                        Button(
                            onClick = { showQuickAddDialog = true },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFCCAEC5)
                            ),
                            modifier = Modifier.fillMaxWidth(0.6f)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("创建新任务", color = Color.White)
                        }
                    }
                }
            } else {
                Column {
                    // 🔧 显示更详细的过滤信息，包括待删除状态
                    val totalTasks = taskListState.tasks.size
                    val completedTasks = taskListState.tasks.count { it.isCompleted }
                    val pendingDeletionTasks = pendingDeletions.size
                    val hiddenTasks = totalTasks - availableTasks.size
                    
                    if (hiddenTasks > 0) {
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp),
                            color = Color(0xFFF8F9FA),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(8.dp)
                            ) {
                                Text(
                                    text = "显示 ${availableTasks.size} 个可选任务",
                                    style = MaterialTheme.typography.labelSmall.copy(
                                        fontSize = 11.sp,
                                        fontWeight = FontWeight.Medium
                                    ),
                                    color = Color(0xFF2E7D32)
                                )
                                
                                // 🔧 详细的隐藏任务统计
                                val hiddenReasons = mutableListOf<String>()
                                if (completedTasks > 0) {
                                    hiddenReasons.add("$completedTasks 个已完成")
                                }
                                if (pendingDeletionTasks > 0) {
                                    hiddenReasons.add("$pendingDeletionTasks 个待删除")
                                }
                                
                                if (hiddenReasons.isNotEmpty()) {
                                    Text(
                                        text = "已隐藏：${hiddenReasons.joinToString("，")}",
                                        style = MaterialTheme.typography.labelSmall.copy(fontSize = 10.sp),
                                        color = Color.Gray
                                    )
                                }
                            }
                        }
                    }
                    
                    LazyColumn(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        items(
                            items = availableTasks,
                            key = { task -> task.id } // 使用任务ID作为key以优化重组性能
                        ) { task ->
                            val isSelected = task.id == currentTaskId
                            val taskColor = when(task.urgency) {
                                TaskUrgency.CRITICAL -> UrgencyColors.Critical
                                TaskUrgency.HIGH -> UrgencyColors.High
                                TaskUrgency.MEDIUM -> UrgencyColors.Medium
                                TaskUrgency.LOW -> UrgencyColors.Low
                            }
                            
                            // 任务项动画
                            AnimatedVisibility(
                                visible = true,
                                enter = fadeIn(animationSpec = tween(300)) + 
                                       expandVertically(animationSpec = tween(300)),
                                exit = fadeOut(animationSpec = tween(200)) + 
                                      shrinkVertically(animationSpec = tween(200))
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp) // 缩小任务间距从8dp到4dp
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(if (isSelected) taskColor.copy(alpha = 0.1f) else Color.Transparent)
                                        .clickable { 
                                            // 将ModelTaskData转换为简化的Task类型，使用清理后的描述
                                            val (cleanDescription, descColor) = parseColorFromDescription(task.description)
                                            val simpleTask = Task(
                                                name = task.title,
                                                description = cleanDescription,
                                                id = task.id,
                                                color = descColor ?: taskColor
                                            )
                                            onSelectTask(simpleTask)
                                            android.util.Log.d("TaskSelectorDialog", "选择任务: ${task.title} (ID: ${task.id})")
                                        }
                                        .padding(6.dp), // 缩小内边距从8dp到6dp
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(10.dp) // 缩小颜色指示器从12dp到10dp
                                            .background(taskColor, CircleShape)
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Column(
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        Text(
                                            text = task.title,
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontSize = 13.sp, // 缩小字体从14sp到13sp
                                                fontWeight = FontWeight.Medium
                                            ),
                                            color = Color(0xFF333333),
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                        
                                        // 解析并显示清理后的描述
                                        val (cleanDescription, _) = parseColorFromDescription(task.description)
                                        if (cleanDescription.isNotEmpty()) {
                                            Spacer(modifier = Modifier.height(2.dp))
                                            Text(
                                                text = cleanDescription,
                                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                                                color = Color.Gray,
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis
                                            )
                                        }
                                        
                                        // 显示截止时间
                                        if (task.daysLeft >= 0) {
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = formatTimeLeft(task.daysLeft),
                                                style = MaterialTheme.typography.labelSmall.copy(fontSize = 10.sp),
                                                color = Color.Gray
                                            )
                                        }
                                    }
                                    
                                    if (isSelected) {
                                        Icon(
                                            imageVector = Icons.Default.CheckCircle,
                                            contentDescription = "已选中",
                                            tint = taskColor,
                                            modifier = Modifier.size(18.dp) // 缩小图标从20dp到18dp
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", color = Color(0xFFCCAEC5))
            }
        }
    )

    // 快速添加任务对话框
    if (showQuickAddDialog) {
        QuickAddTaskDialog(
            onDismiss = { showQuickAddDialog = false },
            isCreating = isCreatingTask,
            onCreateTask = { taskName, taskDesc, priority ->
                if (!isCreatingTask) { // 防止重复创建
                    isCreatingTask = true
                    android.util.Log.d("TaskSelectorDialog", "开始创建任务: $taskName")
                    
                    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                        try {
                            // 创建新任务
                            val newModelTask = com.timeflow.app.data.model.Task(
                                id = java.util.UUID.randomUUID().toString(),
                                title = taskName.trim(),
                                description = taskDesc.trim(),
                                priority = priority,
                                dueDate = null, // 快速添加不设置截止日期
                                createdAt = java.time.LocalDateTime.now(),
                                updatedAt = java.time.LocalDateTime.now()
                            )
                            
                            android.util.Log.d("TaskSelectorDialog", "保存任务到数据库: ${newModelTask.title}")
                            
                            // 保存到数据库
                            taskListViewModel.saveTask(newModelTask)
                            
                            // 等待保存完成
                            kotlinx.coroutines.delay(300)
                            
                            // 刷新任务列表
                            taskListViewModel.refreshTasks()
                            
                            android.util.Log.d("TaskSelectorDialog", "任务创建成功: ${newModelTask.title}")
                            
                            // 创建成功后关闭对话框
                            showQuickAddDialog = false
                            
                            // 自动选择新创建的任务，使用清理后的描述
                            val (cleanDescription, descColor) = parseColorFromDescription(newModelTask.description)
                            val simpleTask = Task(
                                name = newModelTask.title,
                                description = cleanDescription,
                                id = newModelTask.id,
                                color = descColor ?: Color(0xFFCCAEC5)
                            )
                            onSelectTask(simpleTask)
                            
                        } catch (e: Exception) {
                            android.util.Log.e("TaskSelectorDialog", "创建任务失败", e)
                        } finally {
                            isCreatingTask = false
                        }
                    }
                }
            }
        )
    }
}

/**
 * 快速添加任务对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickAddTaskDialog(
    onDismiss: () -> Unit,
    isCreating: Boolean,
    onCreateTask: (String, String, Priority) -> Unit
) {
    var taskTitle by remember { mutableStateOf("") }
    var taskDescription by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(Priority.MEDIUM) }
    
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    tint = Color(0xFFCCAEC5),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("快速添加任务")
            }
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 任务标题输入
                OutlinedTextField(
                    value = taskTitle,
                    onValueChange = { taskTitle = it },
                    label = { Text("任务标题") },
                    placeholder = { Text("请输入任务标题...") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFCCAEC5),
                        cursorColor = Color(0xFFCCAEC5)
                    ),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 任务描述输入
                OutlinedTextField(
                    value = taskDescription,
                    onValueChange = { taskDescription = it },
                    label = { Text("描述 (可选)") },
                    placeholder = { Text("添加一些描述...") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFCCAEC5),
                        cursorColor = Color(0xFFCCAEC5)
                    ),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 优先级选择
                Text(
                    text = "优先级",
                    style = MaterialTheme.typography.labelMedium,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Priority.values().forEach { priority ->
                        val isSelected = selectedPriority == priority
                        val priorityColor = when(priority) {
                            Priority.URGENT -> UrgencyColors.Critical
                            Priority.HIGH -> UrgencyColors.High
                            Priority.MEDIUM -> UrgencyColors.Medium
                            Priority.LOW -> UrgencyColors.Low
                        }
                        
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedPriority = priority },
                            color = if (isSelected) priorityColor.copy(alpha = 0.2f) else Color.Transparent,
                            shape = RoundedCornerShape(8.dp),
                            border = BorderStroke(
                                1.dp,
                                if (isSelected) priorityColor else Color.Gray.copy(alpha = 0.3f)
                            )
                        ) {
                            Text(
                                text = when(priority) {
                                    Priority.URGENT -> "紧急"
                                    Priority.HIGH -> "高"
                                    Priority.MEDIUM -> "中"
                                    Priority.LOW -> "低"
                                },
                                modifier = Modifier.padding(vertical = 8.dp),
                                style = MaterialTheme.typography.labelSmall,
                                color = if (isSelected) priorityColor else Color.Gray,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (taskTitle.isNotBlank()) {
                        onCreateTask(taskTitle, taskDescription, selectedPriority)
                    }
                },
                enabled = taskTitle.isNotBlank() && !isCreating,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFCCAEC5)
                )
            ) {
                if (isCreating) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("创建中...")
                    }
                } else {
                    Text("创建任务")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 底部控制栏组件
 */
@Composable
fun BottomControlBar(
    timerState: TimerState,
    onStartClick: () -> Unit,
    onPauseClick: () -> Unit,
    onStopClick: () -> Unit,
    primaryColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .background(Color.White)
            .padding(horizontal = 24.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧按钮 - 记录查看
        IconButton(
            onClick = { /* 打开记录查看 */ }
        ) {
            Icon(
                imageVector = Icons.Default.History,
                contentDescription = "查看记录",
                tint = Color.Gray,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 中央大按钮 - 开始/暂停
        FloatingActionButton(
            onClick = {
                when (timerState) {
                    TimerState.IDLE, TimerState.PAUSED -> onStartClick()
                    TimerState.RUNNING -> onPauseClick()
                }
            },
            containerColor = primaryColor,
            contentColor = Color.White,
            modifier = Modifier.size(68.dp)
        ) {
            Icon(
                imageVector = if (timerState == TimerState.RUNNING) 
                    Icons.Default.Pause else Icons.Default.PlayArrow,
                contentDescription = if (timerState == TimerState.RUNNING) "暂停" else "开始",
                modifier = Modifier.size(36.dp)
            )
        }
        
        // 右侧按钮 - 结束
        IconButton(
            onClick = onStopClick,
            enabled = timerState != TimerState.IDLE
        ) {
            Icon(
                imageVector = Icons.Default.Stop,
                contentDescription = "结束",
                tint = if (timerState != TimerState.IDLE) Color.Gray else Color.Gray.copy(alpha = 0.3f),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * 时间活动卡片组件
 */
@Composable
fun TimeActivityCard(
    modifier: Modifier = Modifier,
    highEfficiencyHours: Float,
    neutralHours: Float,
    distractionHours: Float,
    onDetailClick: () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "生产力得分",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                TextButton(
                    onClick = onDetailClick,
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 0.dp)
                ) {
                    Text(
                        text = "查看详情 >",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF6571FF)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ActivityMetric(
                    value = "${highEfficiencyHours}小时",
                    label = "高效时间",
                    dotColor = Color(0xFF4CAF50)
                )
                
                ActivityMetric(
                    value = "${neutralHours}小时",
                    label = "中性时间",
                    dotColor = Color(0xFF2196F3)
                )
                
                ActivityMetric(
                    value = "${distractionHours}小时",
                    label = "分心时间",
                    dotColor = Color(0xFFF44336)
                )
            }
        }
    }
}

/**
 * 活动指标组件
 */
@Composable
fun ActivityMetric(
    value: String,
    label: String,
    dotColor: Color
) {
    Column(horizontalAlignment = Alignment.Start) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(dotColor, CircleShape)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 计时器状态枚举
 */
enum class TimerState {
    IDLE, RUNNING, PAUSED
} 