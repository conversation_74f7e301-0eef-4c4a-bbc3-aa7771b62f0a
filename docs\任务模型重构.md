# 任务模型重构文档

## 背景

TimeFlow应用中存在多个重复且功能重叠的任务相关类定义，导致以下问题：
- 任务页面无法正常加载（出现"无法加载或生成任务数据"错误）
- 开发混淆与维护困难
- 潜在的编译和运行时异常

## 问题分析

### 重复的模型类

我们发现存在以下重复的类定义：

1. **Task类**:
   - `com.timeflow.app.data.entity.Task` - Room数据库实体
   - `com.timeflow.app.data.model.Task` - 业务逻辑模型
   - `com.timeflow.app.ui.task.model.Task` - UI层模型

2. **TaskTag类**:
   - `com.timeflow.app.data.entity.TaskTag` - Room数据库实体
   - `com.timeflow.app.data.model.TaskTag` - 视图模型

3. **TaskClosure类**:
   - `com.timeflow.app.data.entity.TaskClosure` - Room数据库实体
   - `com.timeflow.app.data.model.TaskClosure` - 业务层模型

### 任务页面加载错误

当应用尝试加载任务数据时，由于不同的Task类被混合使用，导致类型转换异常，最终显示"无法加载或生成任务数据"的错误信息。

## 重构方案

### 1. 模型清理

删除了以下重复的类文件:
- `app/src/main/kotlin/com/timeflow/app/ui/task/model/Task.kt` - UI层的任务模型
- `app/src/main/kotlin/com/timeflow/app/data/model/TaskClosure.kt` - 重复的任务关系模型
- `app/src/main/kotlin/com/timeflow/app/data/model/TaskTag.kt` - 重复的任务标签模型

### 2. 模型统一策略

采用以下模型架构:

1. **数据层** (`data.entity`):
   - 所有直接映射到Room数据库的实体类
   - 纯数据结构，最小化业务逻辑

2. **业务层** (`data.model`):
   - 业务逻辑模型，可以包含更丰富的功能
   - 适用于Repository和ViewModel之间的数据传递

3. **视图层** (`ui.task.model`):
   - 专注于视图状态的模型类
   - 例如：`TaskStatus`、`TaskStatusChange`等

### 3. 转换器规范

采用明确的转换器类来处理不同层级模型间的转换:
- `TaskConverter` - 负责在不同Task模型之间进行转换
- 清晰的命名约定，避免混淆
- 转换方法应该是幂等的和可靠的

## 升级增强

### 看板功能优化

1. 实现了新的增强看板功能:
   - 支持自定义状态列
   - 任务智能排序
   - 列任务数量限制
   - 详细的任务统计
   - 状态变更和撤销功能
   - 触觉反馈增强

2. 优化了任务拖放体验:
   - 流畅的动画效果
   - 实时状态更新
   - 直观的视觉反馈

## 后续注意事项

1. **避免模型类蔓延**:
   - 新功能开发时，优先使用现有的模型类
   - 必要时扩展现有模型，而不是创建新模型

2. **一致的命名约定**:
   - Entity类: 数据库实体，使用`data.entity`包
   - Model类: 业务逻辑模型，使用`data.model`包
   - UI状态类: 视图状态模型，使用`ui.*.model`包

3. **文档维护**:
   - 在README中更新模型架构说明
   - 为复杂转换逻辑添加详细注释
   - 记录模型类的责任范围 