package com.timeflow.app.di

import com.timeflow.app.data.repository.TaskTimeRepository
import com.timeflow.app.domain.usecase.TaskTimeUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 任务时间模块
 * 提供任务时间相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object TaskTimeModule {
    
    @Provides
    @Singleton
    fun provideTaskTimeRepository(): TaskTimeRepository {
        return TaskTimeRepository()
    }
    
    @Provides
    @Singleton
    fun provideTaskTimeUseCase(
        taskTimeRepository: TaskTimeRepository
    ): TaskTimeUseCase {
        return TaskTimeUseCase(taskTimeRepository)
    }
} 