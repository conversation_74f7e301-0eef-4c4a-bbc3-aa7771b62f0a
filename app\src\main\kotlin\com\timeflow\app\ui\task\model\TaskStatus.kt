package com.timeflow.app.ui.task.model

import androidx.compose.ui.graphics.Color

/**
 * 任务状态模型
 * 表示任务在看板上的不同状态列
 */
data class TaskStatus(
    val id: String,
    val name: String,
    val color: Color,
    val icon: String? = null,
    val limit: Int = 0,
    val isCollapsed: Boolean = false,
    val index: Int = 0
) {
    companion object {
        /**
         * 获取默认的任务状态列表
         */
        fun getDefaultStatuses(): List<TaskStatus> {
            return listOf(
                TaskStatus(
                    id = "todo",
                    name = "待办",
                    color = Color(0xFF5E6AD2),
                    icon = "check_box_outline_blank",
                    index = 0
                ),
                TaskStatus(
                    id = "in_progress",
                    name = "进行中",
                    color = Color(0xFFF5A623),
                    icon = "hourglass_empty",
                    index = 1
                ),
                TaskStatus(
                    id = "review",
                    name = "审核中",
                    color = Color(0xFF42A5F5),
                    icon = "rate_review",
                    index = 2
                ),
                TaskStatus(
                    id = "done",
                    name = "已完成",
                    color = Color(0xFF66BB6A),
                    icon = "check_circle",
                    index = 3
                )
            )
        }
    }
} 