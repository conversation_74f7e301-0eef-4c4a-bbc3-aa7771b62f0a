package com.timeflow.app.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.HardwareRenderer
import android.graphics.PixelFormat
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.RippleDrawable
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Choreographer
import android.view.SurfaceControl
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.CheckBox
import android.widget.EditText
import android.widget.RadioButton
import android.widget.Switch
import androidx.core.view.WindowInsetsControllerCompat
import java.lang.reflect.Method
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import android.hardware.display.DisplayManager
import android.view.Display
import android.app.ActivityManager
import android.graphics.PorterDuff
import android.view.Surface
import android.view.SurfaceHolder
import com.timeflow.app.R
import com.timeflow.app.BuildConfig
import com.timeflow.app.utils.safeParseColor

/**
 * HwcLutsErrorFixer
 * 专门用于修复HwcComposer getLuts失败的问题
 * 通过调整渲染模式和其他参数来避免此类错误
 */
object HwcLutsErrorFixer {
    // 日志标签
    private const val TAG = "HwcLutsErrorFixer"
    
    // 定义WindowInsetsControllerCompat常量，因为可能找不到这些常量
    private const val APPEARANCE_LIGHT_STATUS_BARS = 8
    private const val APPEARANCE_LIGHT_NAVIGATION_BARS = 16
    
    // 错误检测与处理相关常量
    private const val ERROR_CHECK_INTERVAL_MS = 1000L
    
    // 错误监控和统计
    private val errorCount = AtomicInteger(0)
    private val isFixApplied = AtomicBoolean(false)
    private val isStrictModeActive = AtomicBoolean(false)
    private val isCriticalMode = AtomicBoolean(false)
    val isUnsupported8ErrorDetected = AtomicBoolean(false)
    
    // 添加一个变量来跟踪fixRippleDrawables是否已被调用过
    private val hasRippleFixBeenCalled = AtomicBoolean(false)
    
    // 用于定时清理和刷新
    private val handler = Handler(Looper.getMainLooper())
    
    // 最大错误阈值，超过这个值将采取更极端的措施
    private const val CRITICAL_ERROR_THRESHOLD = 10
    private const val STRICT_MODE_THRESHOLD = 5
    
    // 添加errorState变量
    private var errorState = ErrorState.NORMAL
    
    // 新增的lastFrameTime变量
    private var lastFrameTime: Long = 0L
    
    /**
     * 应用完整的HwcLuts错误修复
     * 该方法应在Activity.onCreate中调用
     */
    fun applyCompleteFix(activity: Activity) {
        // 使用AtomicBoolean跟踪是否已对此Activity应用过完整修复
        val isFixAppliedToThisActivity = AtomicBoolean(false)
        if (isFixAppliedToThisActivity.getAndSet(true)) {
            // 如果已经为此Activity应用过修复，则仅进行日志记录
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "已为当前Activity应用过HwcLuts错误修复，跳过重复操作")
            }
            return
        }
        
        Log.d(TAG, "应用完整的HwcLuts错误修复")
        
        val window = activity.window
        
        try {
            // 1. 立即应用基本修复
            applyBasicFix(window)
            
            // 2. 设置特殊的surfaceflinger参数
            applySurfaceFlingerWorkarounds(window)
            
            // 3. 预防性应用针对UNSUPPORTED(8)错误的优化
            applyUnsupported8ErrorFix(window)
            
            // 4. 启动持续监控
            // startLutsErrorMonitoring(activity) // DISABLED: Reduce background activity
            
            // 5. 设置定期刷新机制
            // setupPeriodicRefresh(activity) // DISABLED: Reduce background activity & periodic work
            
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "HwcLuts错误修复措施已完全应用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用HwcLuts修复时出错: ${e.message}")
        }
    }
    
    /**
     * 应用基本修复措施
     */
    fun applyBasicFix(window: Window) {
        try {
            // 1. 重置颜色模式到默认值
            window.colorMode = 0
            
            // 2. 对状态栏和导航栏使用背景色而非黑色，避免黑色状态栏问题
            // 获取当前资源配置，检查是否为暗色模式
            val context = window.context
            val isNightMode = (context.resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK) == 
                android.content.res.Configuration.UI_MODE_NIGHT_YES
            
            // 根据日夜模式设置状态栏颜色和图标颜色
            if (isNightMode) {
                // 夜间模式：稍微透明的深色状态栏，浅色图标
                window.statusBarColor = safeParseColor("#99000000") // 60%不透明黑色
                window.navigationBarColor = safeParseColor("#99000000")
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    window.insetsController?.setSystemBarsAppearance(0, 
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS)
                } else {
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = window.decorView.systemUiVisibility and 
                            View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() and
                            View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
                }
            } else {
                // 日间模式：稍微透明的浅色状态栏，深色图标
                window.statusBarColor = safeParseColor("#CCFAFAFA") // 80%不透明白色
                window.navigationBarColor = safeParseColor("#CCFAFAFA")
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    window.insetsController?.setSystemBarsAppearance(
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS,
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS)
                } else {
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                            View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR or
                            View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
                }
            }
            
            // 3. 使用RGBA_8888像素格式，这是最兼容的格式
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 4. 启用硬件加速但减少复杂特效
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 5. 禁用复杂的系统UI效果
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(true)
            } else {
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            }
            
            isFixApplied.set(true)
            Log.d(TAG, "已应用基本HwcLuts错误修复")
        } catch (e: Exception) {
            Log.e(TAG, "应用基本HwcLuts修复失败: ${e.message}")
        }
    }
    
    /**
     * 应用SurfaceFlinger渲染引擎相关的特殊处理
     */
    private fun applySurfaceFlingerWorkarounds(window: Window) {
        try {
            // 1. 减少绘制层次和复杂度
            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 2. 禁用任何可能引起HWC错误的透明度处理
            val attrs = window.attributes
            
            // 3. 降低窗口刷新率以减轻压力
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                try {
                    // 安全调用，避免在不支持的设备上崩溃
                    val field = attrs.javaClass.getDeclaredField("preferredFrameRateFloat")
                    field.isAccessible = true
                    field.setFloat(attrs, 60.0f) // 使用标准60Hz刷新率
                } catch (e: Exception) {
                    // 忽略不支持的API
                }
            }
            
            // 4. 专门针对HWC的渲染优化
            attrs.alpha = 1.0f  // 完全不透明
            attrs.dimAmount = 0f  // 禁用背景变暗
            
            // 5. 更新窗口属性
            window.attributes = attrs
            
            Log.d(TAG, "已应用SurfaceFlinger渲染引擎相关特殊处理")
        } catch (e: Exception) {
            Log.e(TAG, "应用SurfaceFlinger处理时出错: ${e.message}")
        }
    }
    
    /**
     * 应用针对UNSUPPORTED(8)错误的专项修复
     * 增强版本，专门处理getLuts错误
     */
    fun applyUnsupported8ErrorFix(window: Window) {
        try {
            Log.d(TAG, "应用UNSUPPORTED(8)错误专项修复")
            
            // 标记已检测到UNSUPPORTED(8)错误
            isUnsupported8ErrorDetected.set(true)
            
            // 新策略：混合渲染模式
            // 1. 对窗口使用软件渲染以避免HWC错误
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 2. 禁用硬件加速但保留基本的GPU渲染能力
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // 为ripple等动画特效使用软件渲染 - 只添加一次监听器
                if (!hasRippleFixBeenCalled.get()) {
                    window.decorView.viewTreeObserver.addOnGlobalLayoutListener {
                        fixRippleDrawables(window.decorView)
                    }
                } else {
                    // 如果已经处理过，直接调用一次以确保应用了修复
                    fixRippleDrawables(window.decorView)
                }
                
                // 强制使用软件渲染管道处理ripple效果
                try {
                    val context = window.context
                    val rippleDrawableField = context.theme.javaClass.getDeclaredField("mRippleDrawableField")
                    rippleDrawableField.isAccessible = true
                    val rippleStyle = rippleDrawableField.get(context.theme) as? Int
                    if (rippleStyle != null) {
                        // 使用反射修改特定的ripple样式属性
                        Log.d(TAG, "针对ripple样式应用软件渲染优化")
                    }
                } catch (e: Exception) {
                    // 反射失败时使用备用方案
                    Log.d(TAG, "使用备用方案处理Ripple: ${e.message}")
                }
            }
            
            // 3. 对状态栏和导航栏使用纯色，避免使用透明度和模糊效果
            val context = window.context
            val isNightMode = (context.resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK) == 
                android.content.res.Configuration.UI_MODE_NIGHT_YES
                
            if (isNightMode) {
                window.statusBarColor = Color.BLACK
                window.navigationBarColor = Color.BLACK
            } else {
                window.statusBarColor = Color.WHITE
                window.navigationBarColor = Color.WHITE
            }
            
            // 4. 特别针对getLuts错误的处理
            try {
                // 使用类似SurfaceControl.Transaction的低级API设置色彩空间和矩阵
                val decorView = window.decorView
                
                // 如果当前是Android 11+，使用新的API
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 强制使用DEFAULT色彩模式而非DISPLAY_P3等宽色域
                    window.colorMode = 0 // Activity.COLOR_MODE_DEFAULT
                    
                    // 尝试禁用硬件加速的RenderNode导致的LUT问题
                    val viewRootImpl = getViewRootImpl(decorView)
                    if (viewRootImpl != null) {
                        try {
                            // 尝试使用反射修改渲染配置
                            val threadedRendererField = viewRootImpl.javaClass.getDeclaredField("mThreadedRenderer")
                            threadedRendererField.isAccessible = true
                            val threadedRenderer = threadedRendererField.get(viewRootImpl)
                            if (threadedRenderer != null) {
                                // --- DISABLED: Reflection fails on target device ---
                                /*
                                val avoidUnsupportedColorSpaceMethod = 
                                    threadedRenderer.javaClass.getDeclaredMethod("setAvoidUnsupportedColorSpace", Boolean::class.java)
                                avoidUnsupportedColorSpaceMethod.isAccessible = true
                                avoidUnsupportedColorSpaceMethod.invoke(threadedRenderer, true)
                                Log.d(TAG, "已设置避免使用不支持的色彩空间")
                                */
                                Log.w(TAG, "禁用对 ThreadedRenderer 的反射调用，因为它在目标设备上失败")
                                // --- END DISABLED ---
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "修改ThreadedRenderer色彩空间处理策略失败: ${e.message}") // Log kept for info
                        }
                    }
                }
                
                // 混合模式：为不同的View提供不同的渲染选项
                // applyLayerTypeBasedOnContent(decorView) // DISABLED: Avoid view traversal in this fix
                
            } catch (e: Exception) {
                Log.e(TAG, "应用getLuts错误专项修复时出错: ${e.message}")
            }
            
            Log.d(TAG, "UNSUPPORTED(8) getLuts修复措施已应用完成")
        } catch (e: Exception) {
            Log.e(TAG, "应用UNSUPPORTED(8)错误修复时出错: ${e.message}")
        }
    }
    
    /**
     * 根据View内容应用最佳图层类型
     * 智能混合模式：为不同类型的视图选择最合适的渲染模式
     */
    private fun applyLayerTypeBasedOnContent(view: View) {
        try {
            if (view is ViewGroup) {
                // 递归处理子视图
                for (i in 0 until view.childCount) {
                    applyLayerTypeBasedOnContent(view.getChildAt(i))
                }
                
                // ViewGroup容器使用硬件加速以保持性能
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            } else {
                // 检查是否包含RippleDrawable或其他效果
                val background = view.background
                if (background is LayerDrawable) {
                    // 检查是否包含RippleDrawable
                    var hasRipple = false
                    for (i in 0 until background.numberOfLayers) {
                        if (background.getDrawable(i) is RippleDrawable) {
                            hasRipple = true
                            break
                        }
                    }
                    
                    if (hasRipple) {
                        // 包含RippleDrawable的视图使用软件渲染避免不兼容问题
                        view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                    } else {
                        // 其他LayerDrawable使用硬件加速
                        view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    }
                } else if (background is RippleDrawable) {
                    // RippleDrawable直接使用软件渲染
                    view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                } else {
                    // 普通视图使用硬件加速
                    view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用混合图层类型时出错: ${e.message}")
        }
    }
    
    /**
     * 获取ViewRootImpl实例
     */
    private fun getViewRootImpl(view: View): Any? {
        try {
            val method = View::class.java.getDeclaredMethod("getViewRootImpl")
            method.isAccessible = true
            return method.invoke(view)
        } catch (e: Exception) {
            Log.e(TAG, "获取ViewRootImpl失败: ${e.message}")
            return null
        }
    }
    
    /**
     * 智能渲染模式 - 根据视图类型选择是否使用硬件加速
     */
    private fun applySmartRenderingMode(view: View) {
        try {
            // 递归遍历视图层次结构
            applyLayeredOptimization(view, isInteractive = false)
        } catch (e: Exception) {
            Log.e(TAG, "应用智能渲染模式失败: ${e.message}")
        }
    }
    
    /**
     * 强制刷新渲染状态，解决顽固的HWC LUTs错误
     */
    private fun forceRefreshRenderState(window: Window) {
        try {
            // 1. 如果检测到错误，重新应用修复
            if (errorCount.get() > 0) {
                when {
                    isCriticalMode.get() -> applyCriticalModeFix(window)
                    isStrictModeActive.get() -> applyStrictModeFix(window)
                    isUnsupported8ErrorDetected.get() -> applyUnsupported8ErrorFix(window)
                    else -> applyBasicFix(window)
                }
            }
            
            // 2. 强制全屏重绘
            window.decorView.invalidate()
            
            Log.d(TAG, "渲染状态已刷新")
        } catch (e: Exception) {
            Log.e(TAG, "刷新渲染状态时出错: ${e.message}")
        }
    }
    
    /**
     * 设置定期刷新机制，每隔一段时间刷新一次渲染状态
     */
    private fun setupPeriodicRefresh(activity: Activity) {
        val window = activity.window // Get window from activity
        // 每30秒刷新一次渲染状态
        handler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    if (!activity.isFinishing && !activity.isDestroyed) {
                        // 刷新渲染状态
                        refreshRenderState(window)
                        // 继续下一次刷新
                        handler.postDelayed(this, 30000)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "定期刷新渲染状态时出错: ${e.message}")
                }
            }
        }, 30000)
        
        // 设置Choreographer回调以监控每一帧
        Choreographer.getInstance().postFrameCallback(object : Choreographer.FrameCallback {
            override fun doFrame(frameTimeNanos: Long) {
                try {
                    // --- DISABLED: Reading logcat in doFrame is inefficient ---
                    /*
                    // 检查是否有新的HWC错误
                    val process = Runtime.getRuntime().exec("logcat -d -t 10 HwcComposer:E *:S")
                    val output = process.inputStream.bufferedReader().readText()
                    
                    if (output.contains("getLuts failed") && output.contains("UNSUPPORTED (8)")) {
                        hwcErrorCount++
                        
                        // 如果错误持续出现，应用更激进的修复
                        if (hwcErrorCount > 2) {
                            Log.w(TAG, "在帧回调中检测到HWC错误，应用额外修复")
                            
                            // 在主线程应用额外修复
                            Handler(Looper.getMainLooper()).post {
                                try {
                                    // 确保window仍然可用
                                    if (!window.decorView.isAttachedToWindow) return@post
                                    
                                    // 应用更激进的修复策略
                                    window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                                    reapplyHardwareAccelerationForInteractiveViews(window.decorView)
                                } catch (e: Exception) {
                                    Log.e(TAG, "应用额外修复失败: ${e.message}")
                                }
                            }
                            
                            // 重置计数
                            hwcErrorCount = 0
                        }
                    }
                    */
                    // --- END DISABLED ---
                    
                    // --- Frame delay detection logic (RESTORED & ACTIVE) --- 
                    if (lastFrameTime > 0) {
                        val frameDelay = (frameTimeNanos - lastFrameTime) / 1_000_000 // 转换为毫秒
                        if (frameDelay > 250) { // 如果帧延迟超过250ms
                            Log.w(TAG, "检测到严重的帧延迟: ${frameDelay}ms，可能是HWC问题引起")
                            // Still apply fix based on delay, but not based on logcat content in doFrame
                            // Ensure window is still valid before applying fix
                            if (window.decorView != null && window.decorView.isAttachedToWindow) { // Use captured window
                                 applyUnsupported8ErrorFix(window) // Use captured window
                            }
                        }
                    }
                    lastFrameTime = frameTimeNanos
                    // --- End frame delay detection --- 
                    
                } catch (e: Exception) {
                    Log.e(TAG, "帧回调出错: ${e.message}")
                } finally {
                    // 继续监听下一帧
                    Choreographer.getInstance().postFrameCallback(this)
                }
            }
        })
        
        Log.d(TAG, "定期渲染状态刷新和帧监控已设置")
    }
    
    /**
     * 刷新渲染状态，避免HWC错误累积
     */
    private fun refreshRenderState(window: Window) {
        try {
            // 1. 如果检测到错误，重新应用修复
            if (errorCount.get() > 0) {
                when {
                    isCriticalMode.get() -> applyCriticalModeFix(window)
                    isStrictModeActive.get() -> applyStrictModeFix(window)
                    isUnsupported8ErrorDetected.get() -> applyUnsupported8ErrorFix(window)
                    else -> applyBasicFix(window)
                }
            }
            
            // 2. 强制全屏重绘
            window.decorView.invalidate()
            
            Log.d(TAG, "渲染状态已刷新")
        } catch (e: Exception) {
            Log.e(TAG, "刷新渲染状态时出错: ${e.message}")
        }
    }
    
    /**
     * 启动持续监控以检测和处理HwcLuts错误
     */
    private fun startLutsErrorMonitoring(activity: Activity) {
        val window = activity.window
        
        Thread {
            try {
                // 通过logcat监控HWC错误日志
                val process = Runtime.getRuntime().exec("logcat -v threadtime HwcComposer:E HWComposer:E SurfaceFlinger:E *:S")
                val reader = process.inputStream.bufferedReader()
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    // 检测getLuts相关错误
                    val errorLine = line ?: ""
                    if (errorLine.contains("getLuts failed") || 
                        (errorLine.contains("HWComposer") && errorLine.contains("UNSUPPORTED"))) {
                        
                        // 检测是否是特定的UNSUPPORTED (8)错误
                        if (errorLine.contains("UNSUPPORTED (8)")) {
                            isUnsupported8ErrorDetected.set(true)
                            Log.w(TAG, "检测到UNSUPPORTED (8)错误，需要特殊处理")
                            
                            // 在主线程立即应用特殊修复
                            handler.post {
                                if (!activity.isFinishing && !activity.isDestroyed) {
                                    applyUnsupported8ErrorFix(window)
                                }
                            }
                        }
                        
                        // 记录错误
                        val count = errorCount.incrementAndGet()
                        Log.w(TAG, "检测到HwcLuts错误，当前计数: $count")
                        
                        // 仅在连续发现多个错误时才在UI线程应用修复，减少干扰
                        if (count % 20 == 0) {
                            activity.runOnUiThread {
                                if (!activity.isFinishing && !activity.isDestroyed) {
                                    if (count >= CRITICAL_ERROR_THRESHOLD && !isCriticalMode.getAndSet(true)) {
                                        applyCriticalModeFix(window)
                                    } else if (count >= STRICT_MODE_THRESHOLD && !isStrictModeActive.getAndSet(true)) {
                                        applyStrictModeFix(window)
                                    } else if (isUnsupported8ErrorDetected.get()) {
                                        // 再次应用UNSUPPORTED(8)特定修复
                                        applyUnsupported8ErrorFix(window)
                                    } else {
                                        // 普通修复，重新应用基本修复
                                        applyBasicFix(window)
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "HwcLuts错误监控失败: ${e.message}")
            }
        }.apply {
            isDaemon = true
            name = "hwc-luts-monitor"
            priority = Thread.MIN_PRIORITY // 使用低优先级避免影响UI性能
            start()
        }
        
        Log.d(TAG, "HwcLuts错误监控已启动")
    }
    
    /**
     * 严格模式修复 - 当检测到多次错误时使用
     */
    fun applyStrictModeFix(window: Window) {
        try {
            Log.w(TAG, "应用严格模式HwcLuts错误修复")
            
            // 1. 应用基本修复
            applyBasicFix(window)
            
            // 2. 更激进的措施
            // 完全禁用半透明效果
            window.setFlags(
                WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or 
                WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION,
                0
            )
            
            // 3. 强制使用软件渲染绘制某些内容
            window.decorView.findViewById<View>(android.R.id.content)?.apply {
                setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            }
            
            Log.d(TAG, "严格模式HwcLuts错误修复已应用")
        } catch (e: Exception) {
            Log.e(TAG, "应用严格模式修复时出错: ${e.message}")
        }
    }
    
    /**
     * 关键模式修复 - 最后手段，当频繁发生错误时
     */
    fun applyCriticalModeFix(window: Window) {
        try {
            Log.w(TAG, "应用关键模式HwcLuts错误修复 - 最后手段")
            
            // 对于关键模式，使用混合渲染策略而不是完全禁用硬件加速
            // 1. 设置根视图为软件渲染，但子视图会根据情况选择渲染模式
            window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            
            // 2. 禁用所有动画和过渡效果
            disableAllAnimations(window)
            
            // 3. 最小化渲染复杂度，但保持视觉一致性
            val context = window.context
            val isNightMode = (context.resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK) == 
                android.content.res.Configuration.UI_MODE_NIGHT_YES
                
            if (isNightMode) {
                // 夜间模式：完全不透明的深色背景
                window.statusBarColor = safeParseColor("#121212")
                window.navigationBarColor = safeParseColor("#121212")
                window.decorView.setBackgroundColor(safeParseColor("#121212"))
            } else {
                // 日间模式：完全不透明的浅色背景
                window.statusBarColor = safeParseColor("#FAFAFA") 
                window.navigationBarColor = safeParseColor("#FAFAFA")
                window.decorView.setBackgroundColor(safeParseColor("#FAFAFA"))
                
                // 为浅色背景设置深色图标
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    window.insetsController?.setSystemBarsAppearance(
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS,
                        APPEARANCE_LIGHT_STATUS_BARS or APPEARANCE_LIGHT_NAVIGATION_BARS)
                } else {
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                            View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR or
                            View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR
                }
            }
            
            // 4. 对内容区域应用智能渲染
            window.decorView.findViewById<View>(android.R.id.content)?.apply {
                setBackgroundColor(if (isNightMode) safeParseColor("#121212") else safeParseColor("#FAFAFA"))
                // 对内容视图应用智能渲染
                // applySmartRenderingMode(this) // DISABLED: Avoid view traversal in critical fix
            }
            
            // 5. 针对UNSUPPORTED (8)错误的最后手段
            if (isUnsupported8ErrorDetected.get()) {
                // 设置最基本的显示模式
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    window.colorMode = 0  // 默认颜色模式
                }
                
                // 使用完全不透明的背景
                val attrs = window.attributes
                attrs.alpha = 1.0f
                attrs.dimAmount = 0f
                window.attributes = attrs
            }
            
            Log.d(TAG, "关键模式HwcLuts错误修复已应用 - 智能混合渲染模式")
        } catch (e: Exception) {
            Log.e(TAG, "应用关键模式修复时出错: ${e.message}")
        }
    }
    
    /**
     * 禁用所有动画以减少渲染复杂度
     */
    private fun disableAllAnimations(window: Window) {
        try {
            // 使用可用于各版本的方法禁用动画
            val context = window.context
            
            try {
                // 清除窗口转换动画
                window.setWindowAnimations(0)
                
                // 尝试反射方式禁用过渡动画
                val animationScaleField = android.provider.Settings.Global::class.java.getField("ANIMATOR_DURATION_SCALE")
                val animationScale = animationScaleField.get(null) as String
                
                // 尝试使用内容提供者方式设置
                android.provider.Settings.Global.putFloat(context.contentResolver, animationScale, 0.0f)
            } catch (e: Exception) {
                // 忽略不支持的API
            }
            
            Log.d(TAG, "已禁用所有动画以减少HwcLuts错误")
        } catch (e: Exception) {
            Log.e(TAG, "禁用动画时出错: ${e.message}")
        }
    }
    
    /**
     * 重置修复状态
     * 此方法用于在需要重新应用修复时调用，例如在Activity恢复时
     */
    fun resetFixState() {
        errorCount.set(0)
        isFixApplied.set(false)
        isStrictModeActive.set(false)
        isCriticalMode.set(false)
        // 注意：不重置isUnsupported8ErrorDetected，保留这个设备特性检测结果
        errorState = ErrorState.NORMAL
        
        Log.d(TAG, "HwcLutsErrorFixer状态已重置")
    }
    
    /**
     * 检查当前错误状态
     * @return 反映当前HwcLuts错误严重程度的枚举值
     */
    fun getCurrentErrorState(): ErrorState {
        return when {
            isCriticalMode.get() -> ErrorState.CRITICAL
            isStrictModeActive.get() -> ErrorState.STRICT
            isUnsupported8ErrorDetected.get() -> ErrorState.UNSUPPORTED_8_FIX
            isFixApplied.get() -> ErrorState.BASIC_FIX
            else -> ErrorState.NORMAL
        }
    }
    
    /**
     * 错误状态枚举
     */
    enum class ErrorState {
        NORMAL,           // 正常状态，无错误或已处理
        BASIC_FIX,        // 已应用基本修复
        UNSUPPORTED_8_FIX, // 针对UNSUPPORTED(8)错误的特殊修复
        STRICT,           // 严格模式修复
        MODERATE,         // 中度错误状态（混合渲染）
        CRITICAL,         // 关键模式修复（软件渲染）
        OPTIMIZED_RENDERING // 优化渲染状态（平衡模式）
    }

    /**
     * 增强型UNSUPPORTED(8)错误修复方法
     * 专门设计用于解决getLuts UNSUPPORTED(8)错误，避免NullPointerException
     */
    fun applyEnhancedUnsupported8Fix(window: Window) {
        try {
            Log.d(TAG, "应用增强型UNSUPPORTED(8)错误修复")
            
            // 标记已检测到错误
            isUnsupported8ErrorDetected.set(true)
            
            // 先应用基础修复，不涉及decorView对象
            applyBasicFix(window)
            
            // 1. 降低窗口渲染复杂度 - 通过反射安全设置
            try {
                window.attributes = window.attributes.apply {
                    // 禁用模糊和其他复杂渲染
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        try {
                            val blurBehindEnabledField = this.javaClass.getDeclaredField("blurBehindEnabled")
                            blurBehindEnabledField.isAccessible = true
                            blurBehindEnabledField.setBoolean(this, false)
                        } catch (e: Exception) {
                            // 忽略不支持的API
                        }
                    }
                    
                    // 禁用动画
                    rotationAnimation = 0
                    
                    // 使用标准格式
                    format = PixelFormat.RGBA_8888
                    
                    // 完全不透明窗口
                    alpha = 1.0f
                    dimAmount = 0f
                }
            } catch (e: Exception) {
                Log.e(TAG, "设置窗口属性出错: ${e.message}")
            }
            
            // 安全检查decorView是否为null，避免NPE
            val decorView = window.decorView
            if (decorView == null) {
                Log.w(TAG, "decorView为null，延迟应用增强型UNSUPPORTED(8)错误修复")
                // 安排延迟执行，让窗口初始化
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        // 再次尝试应用修复
                        if (window.decorView != null) {
                            applyDelayedUnsupported8Fix(window)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "延迟应用增强型修复失败: ${e.message}")
                    }
                }, 500)
                return
            }
            
            // 2. 设置一个缓冲的主Handler执行窗口操作
            Handler(Looper.getMainLooper()).post {
                try {
                    // 再次确保decorView不为null并且已附加到窗口
                    if (decorView != null && decorView.isAttachedToWindow) {
                        // 针对性禁用可能触发错误的特性
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                            // 安全访问insetsController
                            val insetsController = window.insetsController
                            if (insetsController != null) {
                                try {
                                    // 移除任何可能触发HWC错误的系统栏行为
                                    insetsController.systemBarsBehavior = 
                                        WindowInsetsControllerCompat.BEHAVIOR_DEFAULT
                                } catch (e: Exception) {
                                    Log.e(TAG, "设置系统栏行为出错: ${e.message}")
                                }
                            }
                        }
                        
                        // 简化绘制，保留互动功能
                        applySmartRenderingMode(decorView)
                    } else {
                        Log.w(TAG, "decorView未附加到窗口，无法完成增强型修复")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "应用增强修复第二阶段出错: ${e.message}")
                }
            }
            
            // 3. 创建一个帧回调来监控错误并应用额外修复
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                setupFrameCallback(window)
            }
            
            Log.d(TAG, "增强型UNSUPPORTED(8)错误修复已完成")
        } catch (e: Exception) {
            Log.e(TAG, "应用增强型UNSUPPORTED(8)错误修复失败: ${e.message}")
        }
    }
    
    /**
     * 延迟应用UNSUPPORTED(8)错误修复
     * 在decorView初始化后再次尝试应用修复
     */
    private fun applyDelayedUnsupported8Fix(window: Window) {
        try {
            Log.d(TAG, "应用延迟的UNSUPPORTED(8)错误修复")
            
            // 确保decorView已初始化
            val decorView = window.decorView ?: return
            
            // 应用简化的智能渲染模式
            decorView.post {
                try {
                    // 应用智能渲染模式，避免使用WindowInsetsController
                    applySmartRenderingMode(decorView)
                    
                    // 设置帧回调以监控渲染
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                        setupFrameCallback(window)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "应用延迟修复时出错: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用延迟的UNSUPPORTED(8)错误修复失败: ${e.message}")
        }
    }

    /**
     * 设置帧回调监控并修复渲染问题
     */
    private fun setupFrameCallback(window: Window) {
        try {
            val frameCallback = object : Choreographer.FrameCallback {
                private var hwcErrorCount = 0
                
                override fun doFrame(frameTimeNanos: Long) {
                    try {
                        // --- DISABLED: Reading logcat in doFrame is inefficient ---
                        /*
                        // 检查是否有新的HWC错误
                        val process = Runtime.getRuntime().exec("logcat -d -t 10 HwcComposer:E *:S")
                        val output = process.inputStream.bufferedReader().readText()
                        
                        if (output.contains("getLuts failed") && output.contains("UNSUPPORTED (8)")) {
                            hwcErrorCount++
                            
                            // 如果错误持续出现，应用更激进的修复
                            if (hwcErrorCount > 2) {
                                Log.w(TAG, "在帧回调中检测到HWC错误，应用额外修复")
                                
                                // 在主线程应用额外修复
                                Handler(Looper.getMainLooper()).post {
                                    try {
                                        // 确保window仍然可用
                                        if (window.decorView == null || !window.decorView.isAttachedToWindow) return@post
                                        
                                        // 应用更激进的修复策略
                                        window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                                        reapplyHardwareAccelerationForInteractiveViews(window.decorView)
                                    } catch (e: Exception) {
                                        Log.e(TAG, "应用额外修复失败: ${e.message}")
                                    }
                                }
                                
                                // 重置计数
                                hwcErrorCount = 0
                            }
                        }
                        */
                        // --- END DISABLED ---
                        
                        // --- Frame delay detection logic (RESTORED & ACTIVE) --- 
                        if (lastFrameTime > 0) {
                            val frameDelay = (frameTimeNanos - lastFrameTime) / 1_000_000 // 转换为毫秒
                            if (frameDelay > 250) { // 如果帧延迟超过250ms
                                Log.w(TAG, "检测到严重的帧延迟: ${frameDelay}ms，可能是HWC问题引起")
                                // Still apply fix based on delay, but not based on logcat content in doFrame
                                // Ensure window is still valid before applying fix
                                if (window.decorView != null && window.decorView.isAttachedToWindow) { // Use captured window
                                     applyUnsupported8ErrorFix(window) // Use captured window
                                }
                            }
                        }
                        lastFrameTime = frameTimeNanos
                        // --- End frame delay detection --- 
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "帧回调出错: ${e.message}")
                    } finally {
                        // 继续监听下一帧
                        Choreographer.getInstance().postFrameCallback(this)
                    }
                }
            }
            
            // 开始监听帧
            Choreographer.getInstance().postFrameCallback(frameCallback)
        } catch (e: Exception) {
            Log.e(TAG, "设置帧回调失败: ${e.message}")
        }
    }

    /**
     * 重新为交互式视图启用硬件加速
     */
    fun reapplyHardwareAccelerationForInteractiveViews(rootView: View) {
        try {
            if (rootView is ViewGroup) {
                for (i in 0 until rootView.childCount) {
                    val child = rootView.getChildAt(i)
                    
                    // 对交互式控件保持硬件加速
                    if (isInteractiveView(child)) {
                        child.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    }
                    
                    // 递归处理
                    if (child is ViewGroup) {
                        reapplyHardwareAccelerationForInteractiveViews(child)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "重新应用硬件加速失败: ${e.message}")
        }
    }

    /**
     * 应用分层优化 - 为不同类型的视图应用不同的渲染策略
     */
    private fun applyLayeredOptimization(view: View, isInteractive: Boolean) {
        // 确定视图是否是交互式的
        val isCurrentViewInteractive = isInteractive || isInteractiveView(view)
        
        // 应用适当的渲染模式
        if (isCurrentViewInteractive) {
            // 交互式视图保持硬件加速以支持动画和触摸反馈
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        } else {
            // 非交互式视图使用软件渲染以避免HWC错误
            view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        }
        
        // 递归处理子视图
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                applyLayeredOptimization(view.getChildAt(i), isCurrentViewInteractive)
            }
        }
    }

    /**
     * 检查视图是否是交互式控件
     */
    private fun isInteractiveView(view: View): Boolean {
        return view.isClickable || 
               view.isLongClickable || 
               view is Button || 
               view is EditText || 
               view is CheckBox || 
               view is RadioButton || 
               view is Switch || 
               view.background is RippleDrawable || 
               (view.background is LayerDrawable && hasRippleInLayers(view.background as LayerDrawable))
    }

    /**
     * 检查LayerDrawable中是否包含RippleDrawable
     */
    private fun hasRippleInLayers(layerDrawable: LayerDrawable): Boolean {
        for (i in 0 until layerDrawable.numberOfLayers) {
            val drawable = layerDrawable.getDrawable(i)
            if (drawable is RippleDrawable) {
                return true
            } else if (drawable is LayerDrawable) {
                if (hasRippleInLayers(drawable)) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 根据设备能力动态配置硬件加速选项
     * 只有在设备支持且不会导致HWC getLuts错误的情况下才启用
     */
    fun configureHardwareAcceleration(activity: Activity) {
        try {
            Log.d(TAG, "正在配置硬件加速设置")
            
            // 检查设备HDR能力作为判断标准之一
            val hasHdrCapability = checkHdrCapability(activity)
            
            // 检查设备GPU/RAM配置
            val activityManager = activity.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            // 低内存设备可能更容易出现HWC错误
            val isLowMemoryDevice = memoryInfo.lowMemory || 
                                    memoryInfo.totalMem < 3L * 1024L * 1024L * 1024L // 小于3GB内存
            
            // 综合评估设备能力
            val canUseFullHardwareAcceleration = hasHdrCapability && 
                                               !isLowMemoryDevice && 
                                               !isUnsupported8ErrorDetected.get() &&
                                               errorCount.get() < STRICT_MODE_THRESHOLD
            
            Log.d(TAG, "设备能力评估: HDR=$hasHdrCapability, 低内存=$isLowMemoryDevice, " +
                      "UNSUPPORTED8=$isUnsupported8ErrorDetected, 错误计数=${errorCount.get()}")
            
            // 设置窗口硬件加速
            val window = activity.window
            if (canUseFullHardwareAcceleration) {
                Log.d(TAG, "设备支持完全硬件加速，启用全部硬件加速功能")
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
                
                // 保持当前渲染状态
                errorState = ErrorState.NORMAL
            } else if (isUnsupported8ErrorDetected.get() || errorCount.get() >= CRITICAL_ERROR_THRESHOLD) {
                Log.w(TAG, "检测到HWC错误，切换到软件渲染模式")
                
                // 对整个窗口使用软件渲染，但内部内容会使用智能渲染模式
                window.setFlags(0, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
                window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                
                // 但对内容区域采用智能渲染，保持交互体验
                window.decorView.findViewById<View>(android.R.id.content)?.let { contentView ->
                    // 智能渲染递归遍历所有子视图
                    applyLayeredOptimization(contentView, isInteractive = false)
                }
                
                errorState = ErrorState.CRITICAL
            } else {
                Log.d(TAG, "设备可能有硬件加速限制，使用混合渲染模式")
                
                // 使用混合渲染模式：窗口硬件加速，但某些内容软件渲染
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
                
                // 采用智能渲染策略
                window.decorView.findViewById<View>(android.R.id.content)?.let { contentView ->
                    // 内容区域保持硬件加速以支持Ripple等效果
                    contentView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    
                    // 对子视图应用分层优化
                    applyLayeredOptimization(contentView, isInteractive = false)
                }
                
                errorState = ErrorState.MODERATE
            }
            
            Log.d(TAG, "硬件加速配置完成，当前状态: $errorState")
        } catch (e: Exception) {
            Log.e(TAG, "配置硬件加速时出错: ${e.message}")
        }
    }
    
    /**
     * 修复RippleDrawable动画问题
     * 在应用启动时频繁调用可能导致卡顿和性能问题
     */
    fun fixRippleDrawables(rootView: View) {
        // 如果已经调用过，直接返回，避免重复处理和日志输出
        if (hasRippleFixBeenCalled.getAndSet(true)) {
            return;
        }
        
        try {
            // 仅在调试模式下记录开始时间和日志
            val startTime = if (BuildConfig.DEBUG) System.currentTimeMillis() else 0
            if (BuildConfig.DEBUG) {
                Log.d(TAG, "开始修复RippleDrawable动画问题")
            }
            
            // --- DISABLED: This function causes major performance issues --- 
            /*
            // 避免在UI线程上执行递归操作
            Handler(Looper.getMainLooper()).post {
                try {
                    // 使用批处理方式处理Ripple效果，而不是递归遍历
                    val viewsToProcess = mutableListOf<View>()
                    collectViewsWithRipple(rootView, viewsToProcess)
                    
                    // 使用延迟批处理，降低主线程负载
                    processBatchedViews(viewsToProcess)
                    
                    val endTime = System.currentTimeMillis()
                    Log.d(TAG, "RippleDrawable动画问题修复完成, 耗时: ${endTime - startTime}ms")
                } catch (e: Exception) {
                    Log.e(TAG, "修复RippleDrawable动画失败: ${e.message}")
                }
            }
            */
            // 仅在调试模式下记录结束时间和警告日志
            if (BuildConfig.DEBUG) {
                val endTime = System.currentTimeMillis()
                Log.i(TAG, "RippleDrawable 修复逻辑已禁用以提高性能, 耗时: ${endTime - startTime}ms")
            }
            // --- END DISABLED ---
        } catch (e: Exception) {
            Log.e(TAG, "修复RippleDrawable动画失败: ${e.message}") // 保留错误日志以便跟踪问题
        }
    }
    
    /**
     * 高效收集包含Ripple效果的视图
     */
    private fun collectViewsWithRipple(view: View, viewsList: MutableList<View>) {
        // 最多处理100个视图，避免过度处理
        if (viewsList.size > 100) return
        
        // 如果视图背景是RippleDrawable，添加到列表
        val background = view.background
        if (background is RippleDrawable) {
            viewsList.add(view)
        }
        
        // 遍历子视图
        if (view is ViewGroup) {
            // 限制处理的子视图数量
            val childCount = Math.min(view.childCount, 20)
            for (i in 0 until childCount) {
                try {
                    val child = view.getChildAt(i)
                    collectViewsWithRipple(child, viewsList)
                } catch (e: Exception) {
                    // 忽略子视图处理错误
                }
            }
        }
    }
    
    /**
     * 批处理Ripple视图优化
     */
    private fun processBatchedViews(views: List<View>) {
        // 如果视图太多，采取不同的策略
        if (views.size > 50) {
            // 对于大量视图，采用全局修复策略
            applyGlobalRippleFix()
            return
        }
        
        // 每批处理10个视图
        val batchSize = 10
        for (i in views.indices step batchSize) {
            val endIndex = minOf(i + batchSize, views.size)
            val batch = views.subList(i, endIndex)
            
            // 使用延迟确保主线程不会被阻塞
            Handler(Looper.getMainLooper()).postDelayed({
                for (view in batch) {
                    try {
                        optimizeRippleDrawable(view)
                    } catch (e: Exception) {
                        // 忽略单个视图处理错误
                    }
                }
            }, (i / batchSize) * 50L) // 每批延迟增加，防止一次性处理过多
        }
    }
    
    /**
     * 优化单个视图的RippleDrawable
     */
    private fun optimizeRippleDrawable(view: View) {
        val background = view.background
        if (background is RippleDrawable) {
            try {
                // 使用反射降低Ripple效果的复杂度
                val field = RippleDrawable::class.java.getDeclaredField("mMaxRadius")
                field.isAccessible = true
                // 将Ripple效果半径限制为较小值，降低渲染复杂度
                field.setInt(background, 20)
            } catch (e: Exception) {
                // 反射失败时使用替代方案
                view.background = createSimplifiedRipple(view.context)
            }
        }
    }
    
    /**
     * 创建简化版本的Ripple效果，降低渲染复杂度
     */
    private fun createSimplifiedRipple(context: Context): RippleDrawable {
        // 创建一个简化的Ripple效果，使用更少的图层和简单的颜色
        val rippleColor = android.content.res.ColorStateList.valueOf(safeParseColor("#20000000"))
        return RippleDrawable(rippleColor, null, null)
    }
    
    /**
     * 应用全局Ripple修复策略
     */
    private fun applyGlobalRippleFix() {
        try {
            // 尝试修改主题中的Ripple效果配置
            val resources = android.content.res.Resources.getSystem()
            val theme = resources.newTheme()
            
            // 尝试使用反射修改主题中的Ripple效果
            try {
                val themeClass = Class.forName("android.content.res.Resources\$Theme")
                val rippleField = themeClass.getDeclaredField("mRippleDrawableField")
                rippleField.isAccessible = true
                // 尝试重置或修改主题中的Ripple配置
            } catch (e: Exception) {
                Log.d(TAG, "使用备用方案处理Ripple: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用全局Ripple修复失败: ${e.message}")
        }
    }
    
    /**
     * 检查设备HDR能力
     * HDR能力通常意味着设备有更好的图形性能
     */
    fun checkHdrCapability(context: Context): Boolean {
        try {
            val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
            val display = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val modes = display.supportedModes
                for (mode in modes) {
                    // 检查是否支持HDR10或HLG或Dolby Vision
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        val hdrCapabilities = display.hdrCapabilities
                        if (hdrCapabilities != null && 
                            (hdrCapabilities.supportedHdrTypes?.isNotEmpty() == true)) {
                            Log.d(TAG, "设备支持HDR")
                            return true
                        }
                    }
                    
                    // 检查是否支持宽色域
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && 
                        display.isWideColorGamut) {
                        Log.d(TAG, "设备支持宽色域")
                        return true
                    }
                }
            }
            
            Log.d(TAG, "设备不支持HDR或宽色域")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "检查HDR能力时出错: ${e.message}")
            return false
        }
    }
    
    /**
     * SurfaceMonitor - 监控Surface完整性并自动恢复
     * 用于检测和恢复可能由HWC错误引起的Surface问题
     */
    class SurfaceMonitor(private val context: Context) {
        private val handler = Handler(Looper.getMainLooper())
        private val surfaceCheckInterval = 5000L // 5秒检查一次
        private var rootView: View? = null
        private var isMonitoring = AtomicBoolean(false)
        
        /**
         * 附加到视图并开始监控
         */
        fun attachToView(view: View) {
            rootView = view
            if (isMonitoring.compareAndSet(false, true)) {
                startMonitoring()
                Log.d(TAG, "Surface监控器已附加到视图")
            }
        }
        
        /**
         * 开始监控Surface完整性
         */
        private fun startMonitoring() {
            handler.post(object : Runnable {
                override fun run() {
                    try {
                        if (isMonitoring.get()) {
                            // 验证Surface完整性
                            verifySurfaceIntegrity()
                            // 继续监控
                            handler.postDelayed(this, surfaceCheckInterval)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Surface监控出错: ${e.message}")
                    }
                }
            })
        }
        
        /**
         * 验证Surface完整性并修复问题
         */
        private fun verifySurfaceIntegrity() {
            try {
                // 检查当前视图层次中是否有SurfaceView
                rootView?.let { view ->
                    findAndCheckSurfaces(view)
                }
            } catch (e: Exception) {
                Log.e(TAG, "验证Surface完整性时出错: ${e.message}")
            }
        }
        
        /**
         * 递归查找并检查所有Surface
         */
        private fun findAndCheckSurfaces(view: View) {
            if (view.javaClass.name.contains("SurfaceView")) {
                // 处理SurfaceView
                try {
                    val getHolderMethod = view.javaClass.getMethod("getHolder")
                    val holder = getHolderMethod.invoke(view) as SurfaceHolder
                    val surface = holder.surface
                    
                    if (surface == null || !surface.isValid) {
                        Log.w(TAG, "检测到无效Surface，尝试恢复...")
                        
                        // 尝试重建Surface
                        view.visibility = View.INVISIBLE
                        handler.postDelayed({
                            view.visibility = View.VISIBLE
                        }, 100)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "检查SurfaceView时出错: ${e.message}")
                }
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && 
                       view.javaClass.name.contains("TextureView")) {
                // 处理TextureView
                try {
                    val getSurfaceMethod = view.javaClass.getMethod("getSurfaceTexture")
                    val surfaceTexture = getSurfaceMethod.invoke(view)
                    
                    if (surfaceTexture == null) {
                        Log.w(TAG, "检测到无效TextureView Surface，尝试恢复...")
                        
                        // 尝试重建TextureView
                        view.visibility = View.INVISIBLE
                        handler.postDelayed({
                            view.visibility = View.VISIBLE
                        }, 100)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "检查TextureView时出错: ${e.message}")
                }
            }
            
            // 递归检查子视图
            if (view is ViewGroup) {
                for (i in 0 until view.childCount) {
                    findAndCheckSurfaces(view.getChildAt(i))
                }
            }
        }
        
        /**
         * 停止监控
         */
        fun stopMonitoring() {
            isMonitoring.set(false)
            Log.d(TAG, "Surface监控器已停止")
        }
    }

    /**
     * 特殊处理RippleDrawable兼容性问题
     * 使用软件渲染的同时支持RippleDrawable动画
     */
    fun handleRippleCompatibility(view: View) {
        try {
            // 记录开始时间
            val startTime = System.currentTimeMillis()
            Log.d(TAG, "开始特殊处理RippleDrawable兼容性问题")
            
            // --- DISABLED: This function causes major performance issues ---
            /*
            // 使用批处理方式处理，而不是递归遍历
            val viewsToProcess = mutableListOf<View>()
            collectViewsWithRipple(view, viewsToProcess)
            
            // 使用延迟批处理，降低主线程负载
            Handler(Looper.getMainLooper()).post {
                // 特殊处理当检测到UNSUPPORTED (8)错误时
                if (isUnsupported8ErrorDetected.get()) {
                    Log.d(TAG, "正在应用RippleDrawable兼容性措施，同时解决HWC错误")
                    
                    // 为顶层视图设置软件渲染
                    if (view is ViewGroup) {
                        view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                        
                        // 为包含RippleDrawable的视图特别处理
                        for (rippleView in viewsToProcess) {
                            // 为包含RippleDrawable的视图单独设置硬件渲染
                            rippleView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                        }
                    }
                } else {
                    // 没有UNSUPPORTED (8)错误，使用标准优化
                    processBatchedViews(viewsToProcess)
                }
                
                val endTime = System.currentTimeMillis()
                Log.d(TAG, "RippleDrawable兼容性处理完成, 耗时: ${endTime - startTime}ms")
            }
            */
            val endTime = System.currentTimeMillis()
            Log.w(TAG, "RippleDrawable 兼容性处理逻辑已禁用以提高性能, 耗时: ${endTime - startTime}ms")
            // --- END DISABLED ---
        } catch (e: Exception) {
            Log.e(TAG, "应用RippleDrawable兼容性措施时出错: ${e.message}") // Log kept for info
        }
    }
} 