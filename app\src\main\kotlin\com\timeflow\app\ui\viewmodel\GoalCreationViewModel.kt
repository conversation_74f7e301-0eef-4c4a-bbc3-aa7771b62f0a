package com.timeflow.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.domain.usecase.goal.QuickGoalCreationUseCase
import com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 目标创建视图模型
 * 管理目标创建流程、智能模板推荐和快速创建功能
 */
@HiltViewModel
class GoalCreationViewModel @Inject constructor(
    private val smartTemplateUseCase: SmartTemplateUseCase,
    private val quickGoalCreationUseCase: QuickGoalCreationUseCase
) : ViewModel() {

    // 视图状态
    sealed class GoalCreationUiState {
        // 正常创建模式
        object Normal : GoalCreationUiState()
        
        // 显示模板推荐
        data class TemplateRecommendation(
            val categorizedTemplates: Map<String, List<GoalTemplate>> = emptyMap(),
            val isLoading: Boolean = false,
            val error: String? = null
        ) : GoalCreationUiState()
        
        // 创建中状态
        data class Creating(val progress: Float = 0f) : GoalCreationUiState()
        
        // 创建成功状态
        data class Success(val goalId: String) : GoalCreationUiState()
        
        // 创建失败状态
        data class Error(val message: String) : GoalCreationUiState()
    }
    
    // 当前UI状态
    private val _uiState = MutableStateFlow<GoalCreationUiState>(GoalCreationUiState.Normal)
    val uiState: StateFlow<GoalCreationUiState> = _uiState.asStateFlow()
    
    // 按类别分组的推荐模板
    val categorizedTemplates = smartTemplateUseCase.getCategorizedTemplates()
        .catch { emit(emptyMap()) }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyMap()
        )
    
    // 个性化推荐模板
    val personalizedTemplates = smartTemplateUseCase.getPersonalizedTemplates()
        .catch { emit(emptyList()) }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    /**
     * 显示智能模板推荐界面
     */
    fun showSmartTemplates() {
        viewModelScope.launch {
            _uiState.value = GoalCreationUiState.TemplateRecommendation(isLoading = true)
            
            try {
                // 确保加载最新的模板数据
                // 等待一小段时间以确保模板数据已经从数据库加载
                kotlinx.coroutines.delay(300)
                
                // 加载已完成，设置成功状态
                _uiState.value = GoalCreationUiState.TemplateRecommendation(
                    categorizedTemplates = categorizedTemplates.value,
                    isLoading = false
                )
                
                // 如果模板数据为空，记录日志
                if (categorizedTemplates.value.isEmpty() || categorizedTemplates.value.all { it.value.isEmpty() }) {
                    android.util.Log.w("GoalCreationViewModel", "模板数据为空，请检查模板初始化")
                } else {
                    android.util.Log.d("GoalCreationViewModel", "成功加载${categorizedTemplates.value.values.sumOf { it.size }}个模板")
                }
            } catch (e: Exception) {
                android.util.Log.e("GoalCreationViewModel", "加载模板失败", e)
                _uiState.value = GoalCreationUiState.TemplateRecommendation(
                    error = "加载模板失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    /**
     * 选择模板
     */
    fun selectTemplate(templateId: String) {
        viewModelScope.launch {
            // 记录模板选择
            smartTemplateUseCase.recordTemplateSelection(templateId)
            
            // 找到被选择的模板
            val selectedTemplate = findTemplateById(templateId)
            
            // 如果找到模板，返回到正常创建模式，并预填充模板内容
            selectedTemplate?.let {
                _uiState.value = GoalCreationUiState.Normal
                // 在实际应用中，这里会预填充模板内容到创建表单
            }
        }
    }
    
    /**
     * 切换到正常创建模式
     */
    fun switchToNormalMode() {
        _uiState.value = GoalCreationUiState.Normal
    }
    
    /**
     * 根据ID查找模板
     */
    private fun findTemplateById(templateId: String): GoalTemplate? {
        categorizedTemplates.value.forEach { (_, templates) ->
            templates.forEach { template ->
                if (template.id == templateId) {
                    return template
                }
            }
        }
        
        return personalizedTemplates.value.find { it.id == templateId }
    }
} 