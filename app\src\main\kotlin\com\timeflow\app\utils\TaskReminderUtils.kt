package com.timeflow.app.utils

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.AlarmManagerCompat
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.entity.Task as EntityTask
import com.timeflow.app.receiver.TaskAlarmReceiver
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.Instant
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务提醒工具类
 * 提供简化的任务提醒设置接口
 */
@Singleton
class TaskReminderUtils @Inject constructor(
    private val context: Context,
    private val notificationPermissionHelper: NotificationPermissionHelper
) {
    
    companion object {
        private const val TAG = "TaskReminderUtils"
    }
    
    /**
     * 为任务设置简单提醒
     */
    fun setTaskReminder(
        taskId: String,
        taskTitle: String,
        reminderTime: LocalDateTime,
        priority: String = "中等"
    ): Boolean {
        Log.d(TAG, "=== 开始设置任务提醒 ===")
        Log.d(TAG, "任务ID: $taskId")
        Log.d(TAG, "任务标题: $taskTitle")
        Log.d(TAG, "提醒时间: $reminderTime")
        Log.d(TAG, "优先级: $priority")
        
        // 检查通知权限
        if (!notificationPermissionHelper.hasNotificationPermission()) {
            Log.w(TAG, "❌ 没有通知权限，无法设置任务提醒")
            return false
        }
        Log.d(TAG, "✅ 通知权限检查通过")
        
        // 检查提醒时间是否在未来
        val now = LocalDateTime.now()
        // 🔧 修复：使用毫秒时间戳进行精确比较，而不是截断到分钟
        val triggerTimeMillis = reminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()

        if (triggerTimeMillis <= System.currentTimeMillis()) {
            Log.w(TAG, "❌ 提醒时间已过期，无法设置任务提醒: $reminderTime (当前时间: $now)")
            return false
        }
        Log.d(TAG, "✅ 提醒时间检查通过")
        
        // 检查精确闹钟权限 (Android 12+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            if (!alarmManager.canScheduleExactAlarms()) {
                Log.w(TAG, "❌ 没有精确闹钟权限，无法设置精确提醒 (Android 12+)")
                // 尝试使用非精确闹钟作为后备方案
                return setInexactTaskReminder(taskId, taskTitle, reminderTime, priority)
            }
            Log.d(TAG, "✅ 精确闹钟权限检查通过")
        }
        
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            // 创建广播意图
            val intent = Intent(context, TaskAlarmReceiver::class.java).apply {
                putExtra(TaskAlarmReceiver.EXTRA_TASK_ID, taskId)
                putExtra(TaskAlarmReceiver.EXTRA_TASK_TITLE, taskTitle)
                putExtra(TaskAlarmReceiver.EXTRA_PRIORITY, priority)
                putExtra(TaskAlarmReceiver.EXTRA_REMINDER_TYPE, "task_start_reminder")
            }
            Log.d(TAG, "✅ 广播意图创建完成")
            
            // 创建PendingIntent
            val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val requestCode = taskId.hashCode()
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                pendingIntentFlags
            )
            Log.d(TAG, "✅ PendingIntent创建完成，requestCode: $requestCode")
            
            // 🔧 修复：直接使用已计算好的精确时间戳
            Log.d(TAG, "🔍 时间转换验证:")
            Log.d(TAG, "  - 原始提醒时间: $reminderTime")
            Log.d(TAG, "  - 时间戳: $triggerTimeMillis")
            
            // 使用 AlarmManagerCompat 来更好地处理精确闹钟
            AlarmManagerCompat.setExactAndAllowWhileIdle(
                alarmManager,
                AlarmManager.RTC_WAKEUP,
                triggerTimeMillis,
                pendingIntent
            )
            
            Log.d(TAG, "✅ 精确任务提醒已设置: $taskTitle at $reminderTime")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 设置精确任务提醒失败", e)
            return false
        }
    }
    
    /**
     * 设置非精确任务提醒（Android 12+后备方案）
     */
    private fun setInexactTaskReminder(
        taskId: String,
        taskTitle: String,
        reminderTime: LocalDateTime,
        priority: String
    ): Boolean {
        Log.d(TAG, "🔄 使用非精确闹钟作为后备方案")
        
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(context, TaskAlarmReceiver::class.java).apply {
                putExtra(TaskAlarmReceiver.EXTRA_TASK_ID, taskId)
                putExtra(TaskAlarmReceiver.EXTRA_TASK_TITLE, taskTitle)
                putExtra(TaskAlarmReceiver.EXTRA_PRIORITY, priority)
                putExtra(TaskAlarmReceiver.EXTRA_REMINDER_TYPE, "task_start_reminder")
            }
            
            val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                taskId.hashCode(),
                intent,
                pendingIntentFlags
            )
            
            // 🔧 修复：使用毫秒时间戳进行精确比较
            val triggerTimeMillis = reminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
            
            Log.d(TAG, "🔍 非精确提醒时间计算:")
            Log.d(TAG, "  - 原始时间: $reminderTime")
            Log.d(TAG, "  - 时间戳: $triggerTimeMillis")
            
            // 使用非精确闹钟
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                triggerTimeMillis,
                pendingIntent
            )
            
            Log.d(TAG, "⚠️ 非精确任务提醒已设置: $taskTitle (可能有几分钟误差)")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 设置非精确任务提醒失败: $taskTitle", e)
            return false
        }
    }

    /**
     * 计算任务的提醒时间（公共方法，供其他类使用）
     */
    fun calculateReminderTime(task: EntityTask, notificationSettings: com.timeflow.app.ui.screen.settings.NotificationSettings): LocalDateTime? {
        val now = LocalDateTime.now()

        return when {
            // 1. 首先检查是否有明确的提醒时间设置
            task.reminderTime != null -> {
                if (task.reminderTime.isAfter(now)) {
                    task.reminderTime
                } else {
                    null
                }
            }
            // 2. 如果有开始时间，在开始时间前提醒
            task.startDate != null -> {
                if (task.startDate.isAfter(now)) {
                    val advanceMinutes = notificationSettings.defaultReminderTime
                    if (advanceMinutes == 0) {
                        task.startDate
                    } else {
                        val reminderTimeCandidate = task.startDate.minusMinutes(advanceMinutes.toLong())
                        if (reminderTimeCandidate.isAfter(now)) {
                            reminderTimeCandidate
                        } else {
                            task.startDate
                        }
                    }
                } else {
                    null
                }
            }
            // 3. 如果只有截止时间，在截止时间前提醒
            task.dueDate != null -> {
                if (task.dueDate.isAfter(now)) {
                    val advanceMinutes = notificationSettings.defaultReminderTime
                    if (advanceMinutes == 0) {
                        task.dueDate
                    } else {
                        val reminderTimeCandidate = task.dueDate.minusMinutes(advanceMinutes.toLong())
                        if (reminderTimeCandidate.isAfter(now)) {
                            reminderTimeCandidate
                        } else {
                            now.plusMinutes(1)
                        }
                    }
                } else {
                    null
                }
            }
            else -> null
        }
    }

    /**
     * 取消任务提醒
     */
    fun cancelTaskReminder(taskId: String): Boolean {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(context, TaskAlarmReceiver::class.java)
            
            val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                taskId.hashCode(),
                intent,
                pendingIntentFlags
            )
            
            // 取消闹钟
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()
            
            Log.d(TAG, "任务提醒已取消: $taskId")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "取消任务提醒失败: $taskId", e)
            return false
        }
    }
    
    /**
     * 为任务设置提醒（从Task对象，支持自定义提前时间）
     */
    suspend fun setTaskReminder(task: EntityTask, notificationSettings: com.timeflow.app.ui.screen.settings.NotificationSettings? = null): Boolean {
        val priorityString = when (task.priority) {
            3 -> "紧急"
            2 -> "高"
            1 -> "中等"
            0 -> "低"
            else -> "中等"
        }
        
        // 获取通知设置（如果没有传入，则使用默认值）
        val settings = notificationSettings ?: com.timeflow.app.ui.screen.settings.NotificationSettings()
        
        // 🔧 修复：获取毫秒精度的当前时间用于比较
        val now = LocalDateTime.now()
        
        // 🔔 修复：支持开始前提醒的新逻辑，使用用户配置的提前时间
        val reminderTime = when {
            // 1. 首先检查是否有明确的提醒时间设置
            task.reminderTime != null -> {
                if (task.reminderTime.isAfter(now)) {
                    Log.d(TAG, "使用明确设置的提醒时间: ${task.reminderTime}")
                    task.reminderTime
                } else {
                    Log.w(TAG, "明确设置的提醒时间已过期: ${task.reminderTime}")
                    null
                }
            }
            // 2. 如果有开始时间，在开始时间前提醒（使用用户配置的提前时间）
            task.startDate != null -> {
                if (task.startDate.isAfter(now)) {
                    // 根据优先级和用户设置决定提前提醒时间
                    // 🐞 BUG修复：将 urgentReminderTime 的默认值处理为 0，以符合“准时”的预期
                    // 🔧 修复：完全遵循用户的提醒时间设置，不再为紧急任务硬编码提前时间
                    val advanceMinutes = settings.defaultReminderTime
                    
                    // 如果设置为0，则不提前
                    if (advanceMinutes == 0) {
                        Log.d(TAG, "使用开始时间进行准时提醒: ${task.startDate}")
                        task.startDate
                    } else {
                        val reminderTimeCandidate = task.startDate.minusMinutes(advanceMinutes.toLong())
                        if (reminderTimeCandidate.isAfter(now)) {
                            Log.d(TAG, "使用开始时间前${advanceMinutes}分钟提醒（用户配置）: ${task.startDate} -> $reminderTimeCandidate")
                            reminderTimeCandidate
                        } else {
                            Log.w(TAG, "开始时间前${advanceMinutes}分钟已过期，改为准时提醒")
                            task.startDate // 如果计算出的提前时间已过时，则直接使用开始时间
                        }
                    }
                } else {
                    Log.w(TAG, "开始时间已过期: ${task.startDate}")
                    null
                }
            }
            // 3. 如果只有截止时间，在截止时间前提醒（根据优先级和用户设置决定提前时间）
            task.dueDate != null -> {
                if (task.dueDate.isAfter(now)) {
                    // 🔧 修复：完全遵循用户的提醒时间设置，不再根据优先级调整
                    val advanceMinutes = settings.defaultReminderTime
                    val reminderTimeCandidate = task.dueDate.minusMinutes(advanceMinutes.toLong())
                    
                    if (advanceMinutes == 0) {
                        // 准时提醒：在截止时间提醒
                        Log.d(TAG, "使用截止时间进行准时提醒（用户设置0分钟）: ${task.dueDate}")
                        task.dueDate
                    } else if (reminderTimeCandidate.isAfter(now)) {
                        Log.d(TAG, "使用截止时间前${advanceMinutes}分钟提醒（用户配置）: ${task.dueDate} -> $reminderTimeCandidate")
                        reminderTimeCandidate
                    } else {
                        Log.w(TAG, "截止时间前${advanceMinutes}分钟已过期，改为立即提醒")
                        now.plusMinutes(1) // 1分钟后提醒
                    }
                } else {
                    Log.w(TAG, "截止时间已过期: ${task.dueDate}")
                    null
                }
            }
            else -> {
                Log.w(TAG, "任务无有效时间设置，无法设置提醒: ${task.title}")
                null
            }
        }
        
        return if (reminderTime != null) {
            setTaskReminder(
                taskId = task.id,
                taskTitle = task.title,
                reminderTime = reminderTime,
                priority = priorityString
            )
        } else {
            false
        }
    }
    
    /**
     * 检查并提示用户开启通知权限
     */
    fun checkNotificationPermissionAndPrompt(): Boolean {
        val hasPermission = notificationPermissionHelper.hasNotificationPermission()
        if (!hasPermission) {
            Log.w(TAG, "通知权限未授予，建议用户到设置页面开启")
        }
        return hasPermission
    }
    
    /**
     * 检查精确闹钟权限 (Android 12+)
     */
    fun canScheduleExactAlarms(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.canScheduleExactAlarms()
        } else {
            true // 低版本Android默认有权限
        }
    }
    
    /**
     * 打开精确闹钟权限设置页面 (Android 12+)
     */
    fun openExactAlarmSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            try {
                val intent = Intent().apply {
                    action = android.provider.Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM
                    data = android.net.Uri.parse("package:${context.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                Log.d(TAG, "已打开精确闹钟权限设置页面")
            } catch (e: Exception) {
                Log.e(TAG, "无法打开精确闹钟权限设置页面", e)
                // 后备方案：打开应用设置页面
                notificationPermissionHelper.openAppSettings()
            }
        }
    }
    
    /**
     * 获取提醒功能状态报告
     */
    fun getReminderStatus(): String {
        val hasNotificationPermission = notificationPermissionHelper.hasNotificationPermission()
        val hasExactAlarmPermission = canScheduleExactAlarms()
        val notificationsEnabled = notificationPermissionHelper.areNotificationsEnabled()
        
        return buildString {
            appendLine("📊 提醒功能状态报告:")
            appendLine("├─ 通知权限: ${if (hasNotificationPermission) "✅ 已授予" else "❌ 未授予"}")
            appendLine("├─ 系统通知: ${if (notificationsEnabled) "✅ 已开启" else "❌ 已关闭"}")
            appendLine("├─ 精确闹钟: ${if (hasExactAlarmPermission) "✅ 已授予" else "⚠️ 未授予 (Android 12+)"}")
            appendLine("└─ 整体状态: ${if (hasNotificationPermission && notificationsEnabled && hasExactAlarmPermission) "✅ 功能完整" else "⚠️ 需要配置"}")
        }
    }
} 