package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.viewmodel.GoalViewModel
import com.timeflow.app.ui.component.goal.CircularProgressIndicator
import com.timeflow.app.ui.component.goal.ProgressMetricCard
import com.timeflow.app.ui.component.goal.StatisticsChart
import com.timeflow.app.ui.component.goal.ChartData
import com.timeflow.app.ui.component.goal.ChartType
import com.timeflow.app.data.model.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 目标仪表板界面 - 综合性的目标概览
 * 显示所有目标的概览、进度和统计信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalDashboardScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel()
) {
    // 模拟数据 - 实际应用中应从ViewModel获取
    val goals = remember { generateSampleGoals() }
    val dashboardStats = remember { calculateDashboardStats(goals) }
    val chartData = remember { generateChartData(goals) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "目标仪表板",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { /* 设置 */ }) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "设置"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        },
        containerColor = Color(0xFFF8F9FA)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 欢迎卡片
            item {
                WelcomeCard(stats = dashboardStats)
            }
            
            // 关键指标
            item {
                KeyMetricsSection(stats = dashboardStats)
            }
            
            // 今日目标
            item {
                TodayGoalsSection(
                    goals = goals.filter { it.isActiveToday() },
                    onGoalClick = { goal ->
                        navController.navigate("goal_detail/${goal.id}")
                    }
                )
            }
            
            // 进度统计图表
            item {
                ProgressChartsSection(chartData = chartData)
            }
            
            // 分类概览
            item {
                CategoryOverviewSection(goals = goals)
            }
            
            // 最近活动
            item {
                RecentActivitySection(goals = goals)
            }
            
            // 快速操作
            item {
                QuickActionsSection(
                    onCreateGoal = { navController.navigate("goal_wizard") },
                    onViewTemplates = { navController.navigate("goal_templates") },
                    onManageCategories = { navController.navigate("goal_categories") }
                )
            }
        }
    }
}

/**
 * 欢迎卡片
 */
@Composable
fun WelcomeCard(stats: DashboardStats) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF6366F1)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFF6366F1),
                            Color(0xFF8B5CF6)
                        )
                    )
                )
                .padding(24.dp)
        ) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = getGreeting(),
                            fontSize = 16.sp,
                            color = Color.White.copy(alpha = 0.9f)
                        )
                        
                        Text(
                            text = "继续您的目标之旅",
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                        
                        Text(
                            text = "今天是实现梦想的又一天",
                            fontSize = 14.sp,
                            color = Color.White.copy(alpha = 0.8f),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                    
                    // 总体进度环形图
                    CircularProgressIndicator(
                        progress = stats.overallProgress,
                        size = 80.dp,
                        strokeWidth = 8.dp,
                        backgroundColor = Color.White.copy(alpha = 0.2f),
                        progressColor = Color.White,
                        showPercentage = true
                    )
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 快速统计
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    QuickStat(
                        label = "活跃目标",
                        value = "${stats.activeGoals}",
                        icon = Icons.Default.PlayArrow
                    )
                    
                    QuickStat(
                        label = "已完成",
                        value = "${stats.completedGoals}",
                        icon = Icons.Default.CheckCircle
                    )
                    
                    QuickStat(
                        label = "连续天数",
                        value = "${stats.streakDays}",
                        icon = Icons.Default.Whatshot
                    )
                }
            }
        }
    }
}

/**
 * 快速统计项
 */
@Composable
fun QuickStat(
    label: String,
    value: String,
    icon: ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.White.copy(alpha = 0.8f)
        )
    }
}

/**
 * 关键指标区域
 */
@Composable
fun KeyMetricsSection(stats: DashboardStats) {
    Column {
        Text(
            text = "关键指标",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                ProgressMetricCard(
                    title = "本周进度",
                    value = "${(stats.weeklyProgress * 100).toInt()}%",
                    subtitle = "比上周提升 ${stats.weeklyImprovement}%",
                    icon = Icons.Default.TrendingUp,
                    color = Color(0xFF10B981),
                    modifier = Modifier.width(160.dp)
                )
            }
            
            item {
                ProgressMetricCard(
                    title = "平均完成率",
                    value = "${(stats.averageCompletionRate * 100).toInt()}%",
                    subtitle = "过去30天",
                    icon = Icons.Default.Analytics,
                    color = Color(0xFF6366F1),
                    modifier = Modifier.width(160.dp)
                )
            }
            
            item {
                ProgressMetricCard(
                    title = "即将到期",
                    value = "${stats.upcomingDeadlines}",
                    subtitle = "7天内",
                    icon = Icons.Default.Schedule,
                    color = Color(0xFFEF4444),
                    modifier = Modifier.width(160.dp)
                )
            }
        }
    }
}

/**
 * 今日目标区域
 */
@Composable
fun TodayGoalsSection(
    goals: List<Goal>,
    onGoalClick: (Goal) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "今日目标",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937)
            )
            
            TextButton(onClick = { /* 查看全部 */ }) {
                Text("查看全部")
            }
        }
        
        if (goals.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF9FAFB))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = Color(0xFF10B981),
                        modifier = Modifier.size(48.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "今日无待办目标",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1F2937)
                    )
                    
                    Text(
                        text = "享受这个轻松的一天吧！",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                }
            }
        } else {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                goals.take(3).forEach { goal ->
                    TodayGoalCard(
                        goal = goal,
                        onClick = { onGoalClick(goal) }
                    )
                }
                
                if (goals.size > 3) {
                    Text(
                        text = "还有 ${goals.size - 3} 个目标...",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 今日目标卡片
 */
@Composable
fun TodayGoalCard(
    goal: Goal,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 进度指示器
            CircularProgressIndicator(
                progress = goal.progress,
                size = 40.dp,
                strokeWidth = 4.dp,
                backgroundColor = Color(0xFFE5E7EB),
                progressColor = goal.category.color,
                showPercentage = false
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 目标信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = goal.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937)
                )
                
                Text(
                    text = goal.category.name,
                    fontSize = 12.sp,
                    color = goal.category.color,
                    modifier = Modifier
                        .background(
                            color = goal.category.color.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
            
            // 进度百分比
            Text(
                text = "${(goal.progress * 100).toInt()}%",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = goal.category.color
            )
        }
    }
}

/**
 * 进度图表区域
 */
@Composable
fun ProgressChartsSection(chartData: List<ChartData>) {
    Column {
        Text(
            text = "进度统计",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        StatisticsChart(
            title = "过去7天进度",
            data = chartData,
            chartType = ChartType.AREA,
            primaryColor = Color(0xFF6366F1)
        )
    }
}

/**
 * 分类概览区域
 */
@Composable
fun CategoryOverviewSection(goals: List<Goal>) {
    Column {
        Text(
            text = "分类概览",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        val categoryStats = goals.groupBy { it.category }
            .mapValues { (_, goals) ->
                CategoryStat(
                    totalGoals = goals.size,
                    completedGoals = goals.count { it.progress >= 1.0f },
                    averageProgress = goals.map { it.progress }.average().toFloat()
                )
            }

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(categoryStats.toList()) { (category, stat) ->
                CategoryOverviewCard(
                    category = category,
                    stat = stat
                )
            }
        }
    }
}

/**
 * 分类概览卡片
 */
@Composable
fun CategoryOverviewCard(
    category: GoalCategory,
    stat: CategoryStat
) {
    Card(
        modifier = Modifier.width(140.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = category.color.copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = category.icon,
                    contentDescription = null,
                    tint = category.color,
                    modifier = Modifier.size(20.dp)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 分类名称
            Text(
                text = category.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(4.dp))

            // 统计信息
            Text(
                text = "${stat.completedGoals}/${stat.totalGoals} 完成",
                fontSize = 12.sp,
                color = Color.Gray
            )

            // 平均进度
            LinearProgressIndicator(
                progress = stat.averageProgress,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                color = category.color,
                trackColor = category.color.copy(alpha = 0.2f)
            )
        }
    }
}

/**
 * 最近活动区域
 */
@Composable
fun RecentActivitySection(goals: List<Goal>) {
    Column {
        Text(
            text = "最近活动",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                val recentActivities = generateRecentActivities(goals)

                recentActivities.forEach { activity ->
                    ActivityItem(activity = activity)
                    if (activity != recentActivities.last()) {
                        HorizontalDivider(
                            modifier = Modifier.padding(vertical = 8.dp),
                            color = Color(0xFFF3F4F6)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 活动项
 */
@Composable
fun ActivityItem(activity: RecentActivity) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 活动图标
        Box(
            modifier = Modifier
                .size(32.dp)
                .background(
                    color = activity.color.copy(alpha = 0.1f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = activity.icon,
                contentDescription = null,
                tint = activity.color,
                modifier = Modifier.size(16.dp)
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 活动内容
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = activity.title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937)
            )

            Text(
                text = activity.description,
                fontSize = 12.sp,
                color = Color.Gray
            )
        }

        // 时间
        Text(
            text = activity.timeAgo,
            fontSize = 11.sp,
            color = Color.Gray
        )
    }
}

/**
 * 快速操作区域
 */
@Composable
fun QuickActionsSection(
    onCreateGoal: () -> Unit,
    onViewTemplates: () -> Unit,
    onManageCategories: () -> Unit
) {
    Column {
        Text(
            text = "快速操作",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            QuickActionCard(
                title = "创建目标",
                icon = Icons.Default.Add,
                color = Color(0xFF6366F1),
                onClick = onCreateGoal,
                modifier = Modifier.weight(1f)
            )

            QuickActionCard(
                title = "浏览模板",
                icon = Icons.Default.Description,
                color = Color(0xFF10B981),
                onClick = onViewTemplates,
                modifier = Modifier.weight(1f)
            )

            QuickActionCard(
                title = "管理分类",
                icon = Icons.Default.Category,
                color = Color(0xFFEF4444),
                onClick = onManageCategories,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 快速操作卡片
 */
@Composable
fun QuickActionCard(
    title: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = color.copy(alpha = 0.1f)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = color,
                textAlign = TextAlign.Center
            )
        }
    }
}

// 数据类和辅助函数

/**
 * 仪表板统计数据
 */
data class DashboardStats(
    val activeGoals: Int,
    val completedGoals: Int,
    val overallProgress: Float,
    val weeklyProgress: Float,
    val weeklyImprovement: Int,
    val averageCompletionRate: Float,
    val upcomingDeadlines: Int,
    val streakDays: Int
)

/**
 * 分类统计
 */
data class CategoryStat(
    val totalGoals: Int,
    val completedGoals: Int,
    val averageProgress: Float
)

/**
 * 最近活动
 */
data class RecentActivity(
    val title: String,
    val description: String,
    val timeAgo: String,
    val icon: ImageVector,
    val color: Color
)

/**
 * 模拟目标数据类
 */
data class Goal(
    val id: String,
    val title: String,
    val description: String,
    val progress: Float,
    val status: String,
    val category: GoalCategory,
    val dueDate: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 获取问候语
 */
fun getGreeting(): String {
    val hour = LocalDateTime.now().hour
    return when {
        hour < 6 -> "深夜好"
        hour < 12 -> "早上好"
        hour < 18 -> "下午好"
        else -> "晚上好"
    }
}

/**
 * 生成示例目标数据
 */
fun generateSampleGoals(): List<Goal> {
    val categories = getGoalCategories()
    return listOf(
        Goal(
            id = "1",
            title = "每日运动30分钟",
            description = "保持健康的生活方式",
            progress = 0.75f,
            status = "进行中",
            category = categories.first { it.id == "health" }
        ),
        Goal(
            id = "2",
            title = "学习Kotlin编程",
            description = "掌握Android开发技能",
            progress = 0.45f,
            status = "进行中",
            category = categories.first { it.id == "learning" }
        ),
        Goal(
            id = "3",
            title = "阅读12本书",
            description = "年度阅读计划",
            progress = 0.33f,
            status = "进行中",
            category = categories.first { it.id == "learning" }
        ),
        Goal(
            id = "4",
            title = "储蓄10万元",
            description = "年度储蓄目标",
            progress = 0.60f,
            status = "进行中",
            category = categories.first { it.id == "finance" }
        )
    )
}

/**
 * 计算仪表板统计
 */
fun calculateDashboardStats(goals: List<Goal>): DashboardStats {
    val activeGoals = goals.count { it.status == "进行中" }
    val completedGoals = goals.count { it.progress >= 1.0f }
    val overallProgress = if (goals.isNotEmpty()) goals.map { it.progress }.average().toFloat() else 0f

    return DashboardStats(
        activeGoals = activeGoals,
        completedGoals = completedGoals,
        overallProgress = overallProgress,
        weeklyProgress = 0.85f,
        weeklyImprovement = 12,
        averageCompletionRate = 0.78f,
        upcomingDeadlines = 2,
        streakDays = 7
    )
}

/**
 * 生成图表数据
 */
fun generateChartData(goals: List<Goal>): List<ChartData> {
    return listOf(
        ChartData("周一", 0.6f),
        ChartData("周二", 0.7f),
        ChartData("周三", 0.5f),
        ChartData("周四", 0.8f),
        ChartData("周五", 0.9f),
        ChartData("周六", 0.7f),
        ChartData("周日", 0.8f)
    )
}

/**
 * 生成最近活动
 */
fun generateRecentActivities(goals: List<Goal>): List<RecentActivity> {
    return listOf(
        RecentActivity(
            title = "完成运动目标",
            description = "今日运动30分钟已完成",
            timeAgo = "2小时前",
            icon = Icons.Default.CheckCircle,
            color = Color(0xFF10B981)
        ),
        RecentActivity(
            title = "学习进度更新",
            description = "Kotlin课程进度提升至45%",
            timeAgo = "5小时前",
            icon = Icons.Default.TrendingUp,
            color = Color(0xFF6366F1)
        ),
        RecentActivity(
            title = "新目标创建",
            description = "创建了\"阅读12本书\"目标",
            timeAgo = "1天前",
            icon = Icons.Default.Add,
            color = Color(0xFFEF4444)
        )
    )
}

/**
 * 检查目标是否在今日活跃
 */
fun Goal.isActiveToday(): Boolean {
    return status == "进行中" && progress < 1.0f
}
