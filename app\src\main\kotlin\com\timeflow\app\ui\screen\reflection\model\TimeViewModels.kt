package com.timeflow.app.ui.screen.reflection.model

import java.time.Instant
import java.time.LocalDate

/**
 * 时间维度枚举
 */
enum class TimeDimension(val displayName: String) {
    WEEK("周"),
    MONTH("月"),
    YEAR("年")
}

/**
 * 日视图数据模型
 */
data class DayViewData(
    val date: LocalDate,
    val timestamp: Instant,
    val isCurrentMonth: Boolean = true,
    val isToday: <PERSON>olean = false,
    val reflectionCount: Int = 0
)

/**
 * 月视图数据模型
 */
data class MonthViewData(
    val year: Int,
    val month: Int,
    val reflectionCount: Int = 0,
    val isCurrentMonth: Boolean = false
) 