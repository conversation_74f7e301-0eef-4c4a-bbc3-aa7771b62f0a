package com.timeflow.app.ui.optimization

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import com.timeflow.app.BuildConfig

/**
 * Compose重组优化工具类
 * 提供各种重组优化的最佳实践和工具函数
 * 
 * <AUTHOR> Team
 * @since 2024-01-XX
 */
object ComposeRecompositionOptimizer {
    
    /**
     * 创建稳定的状态包装器，避免不必要的重组
     * 使用此包装器可以确保只有当状态真正改变时才会触发重组
     */
    @Stable
    data class StableWrapper<T>(val value: T)
    
    /**
     * 将不稳定的类型转换为稳定的类型
     */
    @Composable
    fun <T> rememberStable(value: T): StableWrapper<T> {
        return remember(value) { StableWrapper(value) }
    }
    
    /**
     * 优化的StateFlow收集，只在真正变化时重组
     */
    @Composable
    fun <T> StateFlow<T>.collectAsOptimizedState(): State<T> {
        return if (LocalInspectionMode.current) {
            // 在预览模式下使用简单的实现
            remember { mutableStateOf(value) }
        } else {
            collectAsStateWithLifecycle(initialValue = value)
        }
    }
    
    /**
     * 优化的列表状态收集，使用distinctUntilChanged避免重复重组
     */
    @Composable
    fun <T> StateFlow<List<T>>.collectAsOptimizedListState(): State<List<T>> {
        return remember(this) {
            this.distinctUntilChanged { old, new ->
                // 只比较列表大小和引用，避免深度比较
                old.size == new.size && old === new
            }
        }.collectAsStateWithLifecycle(initialValue = value)
    }
    
    /**
     * 创建记忆化的无参数回调函数
     */
    @Composable
    fun rememberCallback(
        vararg keys: Any?,
        callback: () -> Unit
    ): () -> Unit {
        return remember(keys = keys) { callback }
    }
    
    /**
     * 创建记忆化的单参数回调函数
     */
    @Composable
    fun <P> rememberCallback1(
        vararg keys: Any?,
        callback: (P) -> Unit
    ): (P) -> Unit {
        return remember(keys = keys) { callback }
    }
    
    /**
     * 创建记忆化的双参数回调函数
     */
    @Composable
    fun <P1, P2> rememberCallback2(
        vararg keys: Any?,
        callback: (P1, P2) -> Unit
    ): (P1, P2) -> Unit {
        return remember(keys = keys) { callback }
    }
    
    /**
     * 创建记忆化的无参数回调函数
     */
    @Composable
    fun rememberVoidCallback(
        vararg keys: Any?,
        callback: () -> Unit
    ): () -> Unit {
        return remember(keys = keys, calculation = { callback })
    }
    
    /**
     * 优化的derivedStateOf，带有自定义比较器
     */
    @Composable
    fun <R> rememberDerivedStateOf(
        vararg keys: Any?,
        equalityPolicy: SnapshotMutationPolicy<R> = structuralEqualityPolicy(),
        calculation: () -> R
    ): State<R> {
        return remember(keys = keys) {
            derivedStateOf(equalityPolicy, calculation)
        }
    }
    
    /**
     * 优化的LazyColumn/LazyRow的key生成器
     */
    fun <T> generateStableKey(item: T, idExtractor: (T) -> Any): String {
        return "item_${idExtractor(item).hashCode()}"
    }
    
    /**
     * 批量状态更新优化器，减少多次状态变更引起的重组
     */
    class BatchStateUpdater {
        private val updates = mutableListOf<() -> Unit>()
        
        fun addUpdate(update: () -> Unit) {
            updates.add(update)
        }
        
        @Composable
        fun executeBatch() {
            LaunchedEffect(updates.size) {
                if (updates.isNotEmpty()) {
                    updates.forEach { it() }
                    updates.clear()
                }
            }
        }
    }
    
    /**
     * 条件性记忆化 - 只在条件满足时才进行记忆化
     */
    @Composable
    fun <T> rememberConditional(
        condition: Boolean,
        vararg keys: Any?,
        calculation: () -> T
    ): T {
        return if (condition) {
            remember(keys = keys, calculation = calculation)
        } else {
            calculation()
        }
    }
    
    /**
     * 延迟初始化的记忆化值
     */
    @Composable
    fun <T> rememberLazy(
        vararg keys: Any?,
        calculation: () -> T
    ): Lazy<T> {
        return remember(keys = keys) {
            lazy(calculation)
        }
    }
    
    /**
     * 创建稳定的集合包装器
     */
    @Stable
    data class StableList<T>(val items: List<T>)
    
    @Stable  
    data class StableMap<K, V>(val map: Map<K, V>)
    
    @Stable
    data class StableSet<T>(val set: Set<T>)
    
    /**
     * 将不稳定的集合转换为稳定的集合
     */
    @Composable
    fun <T> List<T>.toStableList(): StableList<T> {
        return remember(this) { StableList(this) }
    }
    
    @Composable
    fun <K, V> Map<K, V>.toStableMap(): StableMap<K, V> {
        return remember(this) { StableMap(this) }
    }
    
    @Composable
    fun <T> Set<T>.toStableSet(): StableSet<T> {
        return remember(this) { StableSet(this) }
    }
    
    /**
     * 优化的状态转换器，只在值真正变化时触发重组
     */
    @Composable
    fun <T, R> StateFlow<T>.mapToOptimizedState(
        transform: (T) -> R
    ): State<R> {
        val initialValue = remember { transform(value) }
        return remember(this) {
            this.map(transform).distinctUntilChanged()
        }.collectAsStateWithLifecycle(initialValue = initialValue)
    }
    
    /**
     * 防抖状态收集器，减少频繁更新引起的重组
     */
    @Composable
    fun <T> StateFlow<T>.collectAsDebounced(
        timeoutMillis: Long = 300L
    ): State<T> {
        var debouncedValue by remember { mutableStateOf(value) }
        
        LaunchedEffect(this) {
            var latestValue = value
            collect { newValue ->
                latestValue = newValue
                kotlinx.coroutines.delay(timeoutMillis)
                if (latestValue == newValue) {
                    debouncedValue = newValue
                }
            }
        }
        
        return remember { derivedStateOf { debouncedValue } }
    }
    
    /**
     * 检查是否需要重组的调试工具
     */
    @Composable
    fun RecompositionLogger(tag: String) {
        val recompositionCount = remember { mutableStateOf(0) }
        recompositionCount.value++
        
        if (BuildConfig.DEBUG) {
            android.util.Log.d("RecompositionOptimizer", "$tag 重组次数: ${recompositionCount.value}")
        }
    }
    
    /**
     * 重组性能分析器
     */
    @Composable
    fun PerformanceAnalyzer(
        name: String,
        enabled: Boolean = BuildConfig.DEBUG,
        content: @Composable () -> Unit
    ) {
        if (enabled) {
            val startTime = remember { System.nanoTime() }
            content()
            val endTime = System.nanoTime()
            val duration = (endTime - startTime) / 1_000_000.0 // 转换为毫秒
            
            DisposableEffect(Unit) {
                android.util.Log.d("PerformanceAnalyzer", "$name 渲染耗时: $duration ms")
                onDispose { }
            }
        } else {
            content()
        }
    }
}

// 扩展函数，提供更便捷的使用方式
/**
 * 为任意值创建稳定包装器的扩展函数
 */
@Composable
fun <T> T.stable(): ComposeRecompositionOptimizer.StableWrapper<T> {
    return ComposeRecompositionOptimizer.rememberStable(this)
}

/**
 * 构建配置，用于控制优化功能的开启
 */
object BuildConfig {
    const val DEBUG = true // 在实际项目中应该从gradle配置获取
} 