package com.timeflow.app.ui.task.components.common.event

import android.util.Log
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.time.LocalDateTime
import java.time.LocalDate
import android.app.Notification
import java.lang.Exception

/**
 * 应用事件密封类，定义所有可能的事件类型
 */
sealed class AppEvent {
    /**
     * 基础事件类，包含时间戳
     */
    abstract class BaseEvent {
        open val timestamp: LocalDateTime = LocalDateTime.now()
    }
    
    /**
     * 任务更新事件
     * @param taskId 被更新的任务ID
     */
    data class TaskUpdated(val taskId: String) : BaseEvent()
    
    /**
     * 任务删除事件
     * @param taskId 被删除的任务ID
     */
    data class TaskDeleted(val taskId: String) : BaseEvent()
    
    /**
     * 子任务变更事件
     * @param parentId 父任务ID
     */
    data class SubtasksChanged(val parentId: String) : BaseEvent()
    
    /**
     * 数据库刷新事件
     */
    object DatabaseRefreshed : BaseEvent()
    
    /**
     * 任务状态变更事件
     * @param taskId 任务ID
     * @param completed 是否完成
     */
    data class TaskCompletionChanged(val taskId: String, val completed: Boolean) : BaseEvent()
    
    /**
     * 任务组变更事件
     */
    data object TaskGroupsChanged : BaseEvent()
    
    /**
     * 看板数据变更事件
     */
    data object KanbanDataChanged : BaseEvent()
    
    /**
     * 任务创建事件
     * @param taskId 新创建的任务ID
     */
    data class TaskCreated(val taskId: String) : BaseEvent()
    
    /**
     * 任务移动事件
     * @param taskId 移动的任务ID
     * @param sourceContainerId 源容器ID
     * @param targetContainerId 目标容器ID
     */
    data class TaskMoved(
        val taskId: String,
        val sourceContainerId: String,
        val targetContainerId: String
    ) : BaseEvent()
    
    /**
     * 主题设置变更事件
     * @param colorArgb 背景色ARGB值
     * @param useUnifiedBackground 是否使用统一背景
     */
    data class ThemeSettingsChanged(
        val colorArgb: Long,
        val useUnifiedBackground: Boolean
    ) : BaseEvent()
    
    /**
     * 页面背景色变更事件
     * @param pageName 页面名称("home", "calendar", "statistics", "profile")
     * @param colorArgb 背景色ARGB值
     */
    data class PageBackgroundChanged(
        val pageName: String,
        val colorArgb: Long
    ) : BaseEvent()
    
    /**
     * 主题设置请求事件
     * 用于请求当前主题设置状态，ThemeSettingsViewModel应响应此事件
     * 并发送当前所有页面的背景色设置
     */
    data object ThemeSettingsRequested : BaseEvent()
    
    /**
     * 任务时间变更事件
     * @param taskId 任务ID
     */
    data class TaskTimeChanged(val taskId: String) : BaseEvent()
    
    /**
     * 任务时间更新事件（详细版本）
     * @param taskId 任务ID
     * @param newTime 新的任务时间
     */
    data class TaskTimeUpdated(val taskId: String, val newTime: LocalDateTime) : BaseEvent()
    
    /**
     * 子任务添加事件
     * @param parentId 父任务ID
     * @param subTaskId 子任务ID
     */
    data class SubTaskAdded(val parentId: String, val subTaskId: String) : BaseEvent()
    
    /**
     * 子任务更新事件
     * @param parentId 父任务ID
     * @param subTaskId 子任务ID
     */
    data class SubTaskUpdated(val parentId: String, val subTaskId: String) : BaseEvent()
    
    /**
     * 子任务删除事件
     * @param parentId 父任务ID
     * @param subTaskId 子任务ID
     */
    data class SubTaskDeleted(val parentId: String, val subTaskId: String) : BaseEvent()
    
    /**
     * 任务颜色变更事件
     * @param taskId 任务ID
     * @param color 颜色值(ARGB)
     */
    data class TaskColorChanged(val taskId: String, val color: Long) : BaseEvent()
    
    /**
     * 主题更改事件
     * @param isDarkMode 是否为暗黑模式
     */
    data class ThemeChanged(val isDarkMode: Boolean) : BaseEvent()
    
    /**
     * 应用通知事件
     * @param notification 通知对象
     */
    data class NotificationReceived(val notification: Notification) : BaseEvent()
    
    /**
     * 日历日期变更事件
     * @param date 新的日期
     */
    data class CalendarDateChanged(val date: LocalDate) : BaseEvent()
    
    /**
     * 日历事件添加事件
     * @param eventId 事件ID
     */
    data class CalendarEventAdded(val eventId: String) : BaseEvent()
    
    /**
     * 日历事件更新事件
     * @param eventId 事件ID
     */
    data class CalendarEventUpdated(val eventId: String) : BaseEvent()
    
    /**
     * 日历事件删除事件
     * @param eventId 事件ID
     */
    data class CalendarEventDeleted(val eventId: String) : BaseEvent()
    
    /**
     * 设置变更事件
     * @param settingsName 设置名称
     * @param value 设置值
     */
    data class SettingsChanged(val settingsName: String, val value: Any) : BaseEvent()
    
    /**
     * 用户登录事件
     * @param userId 用户ID
     */
    data class UserLoggedIn(val userId: String) : BaseEvent()
    
    /**
     * 用户登出事件
     * @param userId 用户ID
     */
    data class UserLoggedOut(val userId: String) : BaseEvent()
    
    /**
     * 同步开始事件
     * @param timestamp 开始时间戳
     */
    data class SyncStarted(override val timestamp: LocalDateTime = LocalDateTime.now()) : BaseEvent()
    
    /**
     * 同步完成事件
     * @param success 是否成功
     * @param message 消息
     */
    data class SyncCompleted(val success: Boolean, val message: String? = null) : BaseEvent()
    
    /**
     * 错误发生事件
     * @param errorMessage 错误消息
     * @param exception 异常对象
     */
    data class ErrorOccurred(val errorMessage: String, val exception: Exception? = null) : BaseEvent()
}

/**
 * 应用级事件总线，用于跨组件通信
 * 
 * 使用SharedFlow实现，确保多个订阅者可以接收到同一事件
 */
object EventBus {
    private val TAG = "EventBus"
    
    // 使用较大的缓冲区确保事件不会丢失
    private val _events = MutableSharedFlow<AppEvent.BaseEvent>(
        replay = 0,
        extraBufferCapacity = 10
    )
    
    /**
     * 可订阅的事件流
     */
    val events: SharedFlow<AppEvent.BaseEvent> = _events.asSharedFlow()
    
    /**
     * 发布事件
     * @param event 要发布的事件
     * @return 是否成功发布
     */
    suspend fun emit(event: AppEvent.BaseEvent): Boolean {
        Log.d(TAG, "Emitting event: $event")
        return _events.tryEmit(event)
    }
    
    /**
     * 同步发布事件（不等待）
     * @param event 要发布的事件
     * @return 是否成功发布
     */
    fun tryEmit(event: AppEvent.BaseEvent): Boolean {
        val result = _events.tryEmit(event)
        Log.d(TAG, "tryEmit event: $event, success=$result")
        return result
    }
} 