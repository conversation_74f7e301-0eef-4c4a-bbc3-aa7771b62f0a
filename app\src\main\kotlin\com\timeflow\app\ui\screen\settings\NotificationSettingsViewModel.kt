package com.timeflow.app.ui.screen.settings

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import android.util.Log
import com.timeflow.app.utils.TimeFlowNotificationManager
import com.timeflow.app.service.DailyReviewScheduler
import com.timeflow.app.service.NotificationConfigManager
import com.timeflow.app.data.model.NotificationConfig
import com.timeflow.app.di.DataStoreModule
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import javax.inject.Inject

/**
 * 通知设置数据类
 * 参考知名时间管理app（Forest、Todoist、Any.do）的简洁设计
 * 保留核心功能，移除冗余设置，支持动态配置
 */
data class NotificationSettings(
    // 总开关
    val notificationsEnabled: Boolean = true,

    // 任务管理提醒
    val taskRemindersEnabled: Boolean = true,
    val deadlineRemindersEnabled: Boolean = true,
    val overdueRemindersEnabled: Boolean = true,
    val dailyReviewEnabled: Boolean = true,
    val dailyReviewTime: String = "21:00",

    // 任务常驻通知
    val taskPersistentNotificationEnabled: Boolean = true,

    // 习惯培养提醒（简化版）
    val habitRemindersEnabled: Boolean = true,

    // 专注时间提醒（简化版）
    val focusRemindersEnabled: Boolean = true,
    val focusSessionNotificationsEnabled: Boolean = true,

    // 健康管理提醒（新增用药提醒）
    val medicationRemindersEnabled: Boolean = true,
    val medicationSoundEnabled: Boolean = true,
    val medicationVibrationEnabled: Boolean = true,
    val medicationAdvanceTime: Int = 5, // 用药提醒提前时间（分钟）

    // 通知方式设置（简化版）
    val soundEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,

    // 免打扰设置（简化版）
    val doNotDisturbEnabled: Boolean = false,
    val doNotDisturbStartTime: String = "22:00",
    val doNotDisturbEndTime: String = "08:00",

    // 提醒时间设置（统一简化）
    val defaultReminderTime: Int = 0 // 🔧 修复：默认为准时提醒（0分钟）
)

/**
 * 通知设置ViewModel
 */
@HiltViewModel
class NotificationSettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val notificationManager: TimeFlowNotificationManager,
    private val dailyReviewScheduler: DailyReviewScheduler,
    private val configManager: NotificationConfigManager,
    private val taskPersistentNotificationManager: com.timeflow.app.service.TaskPersistentNotificationManager,
    @DataStoreModule.NotificationDataStore private val notificationDataStore: DataStore<Preferences>
) : ViewModel() {
    
    // 定义DataStore键 - 简化后的核心设置
    private object PreferencesKeys {
        // 总开关
        val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")

        // 任务管理提醒
        val TASK_REMINDERS_ENABLED = booleanPreferencesKey("task_reminders_enabled")
        val DEADLINE_REMINDERS_ENABLED = booleanPreferencesKey("deadline_reminders_enabled")
        val OVERDUE_REMINDERS_ENABLED = booleanPreferencesKey("overdue_reminders_enabled")
        val DAILY_REVIEW_ENABLED = booleanPreferencesKey("daily_review_enabled")
        val DAILY_REVIEW_TIME = stringPreferencesKey("daily_review_time")

        // 任务常驻通知
        val TASK_PERSISTENT_NOTIFICATION_ENABLED = booleanPreferencesKey("task_persistent_notification_enabled")

        // 习惯培养提醒（简化）
        val HABIT_REMINDERS_ENABLED = booleanPreferencesKey("habit_reminders_enabled")

        // 专注时间提醒（简化）
        val FOCUS_REMINDERS_ENABLED = booleanPreferencesKey("focus_reminders_enabled")
        val FOCUS_SESSION_NOTIFICATIONS_ENABLED = booleanPreferencesKey("focus_session_notifications_enabled")

        // 健康管理提醒（新增用药提醒）
        val MEDICATION_REMINDERS_ENABLED = booleanPreferencesKey("medication_reminders_enabled")
        val MEDICATION_SOUND_ENABLED = booleanPreferencesKey("medication_sound_enabled")
        val MEDICATION_VIBRATION_ENABLED = booleanPreferencesKey("medication_vibration_enabled")
        val MEDICATION_ADVANCE_TIME = intPreferencesKey("medication_advance_time")

        // 通知方式设置（简化）
        val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")

        // 免打扰设置（简化）
        val DO_NOT_DISTURB_ENABLED = booleanPreferencesKey("do_not_disturb_enabled")
        val DO_NOT_DISTURB_START_TIME = stringPreferencesKey("do_not_disturb_start_time")
        val DO_NOT_DISTURB_END_TIME = stringPreferencesKey("do_not_disturb_end_time")

        // 提醒时间设置（统一简化）
        val DEFAULT_REMINDER_TIME = intPreferencesKey("default_reminder_time")
    }
    
    // 通知设置状态
    private val _notificationSettings = MutableStateFlow(NotificationSettings())
    val notificationSettings: StateFlow<NotificationSettings> = _notificationSettings

    // 是否显示时间选择器
    private val _showTimePickerDialog = MutableStateFlow<String?>(null)
    val showTimePickerDialog: StateFlow<String?> = _showTimePickerDialog

    // 动态配置状态
    private val _notificationConfig = MutableStateFlow(NotificationConfig())
    val notificationConfig: StateFlow<NotificationConfig> = _notificationConfig
    
    init {
        loadNotificationSettings()
        loadNotificationConfig()
    }
    
    /**
     * 加载通知设置 - 简化后的核心设置
     */
    private fun loadNotificationSettings() {
        viewModelScope.launch {
            notificationDataStore.data
                .map { preferences ->
                    NotificationSettings(
                        notificationsEnabled = preferences[PreferencesKeys.NOTIFICATIONS_ENABLED] ?: true,

                        // 任务管理提醒
                        taskRemindersEnabled = preferences[PreferencesKeys.TASK_REMINDERS_ENABLED] ?: true,
                        deadlineRemindersEnabled = preferences[PreferencesKeys.DEADLINE_REMINDERS_ENABLED] ?: true,
                        overdueRemindersEnabled = preferences[PreferencesKeys.OVERDUE_REMINDERS_ENABLED] ?: true,
                        dailyReviewEnabled = preferences[PreferencesKeys.DAILY_REVIEW_ENABLED] ?: true,
                        dailyReviewTime = preferences[PreferencesKeys.DAILY_REVIEW_TIME] ?: "21:00",

                        // 任务常驻通知
                        taskPersistentNotificationEnabled = preferences[PreferencesKeys.TASK_PERSISTENT_NOTIFICATION_ENABLED] ?: true,

                        // 习惯培养提醒（简化）
                        habitRemindersEnabled = preferences[PreferencesKeys.HABIT_REMINDERS_ENABLED] ?: true,

                        // 专注时间提醒（简化）
                        focusRemindersEnabled = preferences[PreferencesKeys.FOCUS_REMINDERS_ENABLED] ?: true,
                        focusSessionNotificationsEnabled = preferences[PreferencesKeys.FOCUS_SESSION_NOTIFICATIONS_ENABLED] ?: true,

                        // 健康管理提醒（新增用药提醒）
                        medicationRemindersEnabled = preferences[PreferencesKeys.MEDICATION_REMINDERS_ENABLED] ?: true,
                        medicationSoundEnabled = preferences[PreferencesKeys.MEDICATION_SOUND_ENABLED] ?: true,
                        medicationVibrationEnabled = preferences[PreferencesKeys.MEDICATION_VIBRATION_ENABLED] ?: true,
                        medicationAdvanceTime = preferences[PreferencesKeys.MEDICATION_ADVANCE_TIME] ?: 5,

                        // 通知方式设置（简化）
                        soundEnabled = preferences[PreferencesKeys.SOUND_ENABLED] ?: true,
                        vibrationEnabled = preferences[PreferencesKeys.VIBRATION_ENABLED] ?: true,

                        // 免打扰设置（简化）
                        doNotDisturbEnabled = preferences[PreferencesKeys.DO_NOT_DISTURB_ENABLED] ?: false,
                        doNotDisturbStartTime = preferences[PreferencesKeys.DO_NOT_DISTURB_START_TIME] ?: "22:00",
                        doNotDisturbEndTime = preferences[PreferencesKeys.DO_NOT_DISTURB_END_TIME] ?: "08:00",

                        // 提醒时间设置（统一简化）
                        defaultReminderTime = preferences[PreferencesKeys.DEFAULT_REMINDER_TIME] ?: 0 // 🔧 修复：默认为准时提醒
                    )
                }
                .collect { settings ->
                    _notificationSettings.value = settings
                    
                    // 初始化每日回顾调度
                    // 只在设置加载后进行一次初始化，避免重复调度
                    if (settings.dailyReviewEnabled) {
                        dailyReviewScheduler.scheduleDailyReview(settings.dailyReviewTime)
                        Log.d("NotificationSettings", "🔄 初始化每日回顾提醒，时间: ${settings.dailyReviewTime}")
                    }
                    
                    Log.d("NotificationSettings", "通知设置已加载: $settings")
                }
        }
    }

    /**
     * 加载动态通知配置
     */
    private fun loadNotificationConfig() {
        viewModelScope.launch {
            configManager.getNotificationConfig()
                .collect { config ->
                    _notificationConfig.value = config
                    Log.d("NotificationSettings", "动态配置已加载: ${config.categories.size} 个分类")
                }
        }
    }

    /**
     * 更新通知设置
     */
    private fun updateSettings(updater: (NotificationSettings) -> NotificationSettings) {
        val newSettings = updater(_notificationSettings.value)
        _notificationSettings.value = newSettings
        saveSettings(newSettings)
    }
    
    /**
     * 保存设置到DataStore - 简化后的核心设置
     */
    private fun saveSettings(settings: NotificationSettings) {
        viewModelScope.launch {
            try {
                notificationDataStore.edit { preferences ->
                    preferences[PreferencesKeys.NOTIFICATIONS_ENABLED] = settings.notificationsEnabled

                    // 任务管理提醒
                    preferences[PreferencesKeys.TASK_REMINDERS_ENABLED] = settings.taskRemindersEnabled
                    preferences[PreferencesKeys.DEADLINE_REMINDERS_ENABLED] = settings.deadlineRemindersEnabled
                    preferences[PreferencesKeys.OVERDUE_REMINDERS_ENABLED] = settings.overdueRemindersEnabled
                    preferences[PreferencesKeys.DAILY_REVIEW_ENABLED] = settings.dailyReviewEnabled
                    preferences[PreferencesKeys.DAILY_REVIEW_TIME] = settings.dailyReviewTime

                    // 任务常驻通知
                    preferences[PreferencesKeys.TASK_PERSISTENT_NOTIFICATION_ENABLED] = settings.taskPersistentNotificationEnabled

                    // 习惯培养提醒（简化）
                    preferences[PreferencesKeys.HABIT_REMINDERS_ENABLED] = settings.habitRemindersEnabled

                    // 专注时间提醒（简化）
                    preferences[PreferencesKeys.FOCUS_REMINDERS_ENABLED] = settings.focusRemindersEnabled
                    preferences[PreferencesKeys.FOCUS_SESSION_NOTIFICATIONS_ENABLED] = settings.focusSessionNotificationsEnabled

                    // 健康管理提醒（新增用药提醒）
                    preferences[PreferencesKeys.MEDICATION_REMINDERS_ENABLED] = settings.medicationRemindersEnabled
                    preferences[PreferencesKeys.MEDICATION_SOUND_ENABLED] = settings.medicationSoundEnabled
                    preferences[PreferencesKeys.MEDICATION_VIBRATION_ENABLED] = settings.medicationVibrationEnabled
                    preferences[PreferencesKeys.MEDICATION_ADVANCE_TIME] = settings.medicationAdvanceTime

                    // 通知方式设置（简化）
                    preferences[PreferencesKeys.SOUND_ENABLED] = settings.soundEnabled
                    preferences[PreferencesKeys.VIBRATION_ENABLED] = settings.vibrationEnabled

                    // 免打扰设置（简化）
                    preferences[PreferencesKeys.DO_NOT_DISTURB_ENABLED] = settings.doNotDisturbEnabled
                    preferences[PreferencesKeys.DO_NOT_DISTURB_START_TIME] = settings.doNotDisturbStartTime
                    preferences[PreferencesKeys.DO_NOT_DISTURB_END_TIME] = settings.doNotDisturbEndTime

                    // 提醒时间设置（统一简化）
                    preferences[PreferencesKeys.DEFAULT_REMINDER_TIME] = settings.defaultReminderTime
                }
                Log.d("NotificationSettings", "通知设置已保存")
            } catch (e: Exception) {
                Log.e("NotificationSettings", "保存通知设置失败", e)
            }
        }
    }
    
    // 总开关相关方法
    fun toggleNotifications(enabled: Boolean) {
        updateSettings { it.copy(notificationsEnabled = enabled) }
    }
    
    // 任务提醒相关方法
    fun toggleTaskReminders(enabled: Boolean) {
        updateSettings { it.copy(taskRemindersEnabled = enabled) }
    }
    
    fun toggleDeadlineReminders(enabled: Boolean) {
        updateSettings { it.copy(deadlineRemindersEnabled = enabled) }
    }
    
    fun toggleOverdueReminders(enabled: Boolean) {
        updateSettings { it.copy(overdueRemindersEnabled = enabled) }
    }
    
    fun toggleDailyReview(enabled: Boolean) {
        updateSettings { it.copy(dailyReviewEnabled = enabled) }
        // 根据启用状态调度或取消每日回顾提醒
        if (enabled) {
            val currentTime = _notificationSettings.value.dailyReviewTime
            dailyReviewScheduler.enableDailyReview(currentTime)
            Log.d("NotificationSettings", "✅ 每日回顾已启用，时间: $currentTime")
        } else {
            dailyReviewScheduler.disableDailyReview()
            Log.d("NotificationSettings", "❌ 每日回顾已禁用")
        }
    }
    
    fun setDailyReviewTime(time: String) {
        updateSettings { it.copy(dailyReviewTime = time) }
        // 如果每日回顾已启用，重新设置提醒时间
        if (_notificationSettings.value.dailyReviewEnabled) {
            dailyReviewScheduler.scheduleDailyReview(time)
            Log.d("NotificationSettings", "⏰ 每日回顾时间已更新为: $time")
        }
    }

    fun toggleTaskPersistentNotification(enabled: Boolean) {
        updateSettings { it.copy(taskPersistentNotificationEnabled = enabled) }
        // 根据启用状态启动或停止任务常驻通知服务
        viewModelScope.launch {
            try {
                if (enabled) {
                    taskPersistentNotificationManager.startPersistentNotification()
                    Log.d("NotificationSettings", "✅ 任务常驻通知已启用")
                } else {
                    taskPersistentNotificationManager.stopPersistentNotification()
                    Log.d("NotificationSettings", "❌ 任务常驻通知已禁用")
                }
            } catch (e: Exception) {
                Log.e("NotificationSettings", "切换任务常驻通知失败", e)
            }
        }
    }
    
    // 习惯提醒相关方法（简化）
    fun toggleHabitReminders(enabled: Boolean) {
        updateSettings { it.copy(habitRemindersEnabled = enabled) }
    }
    
    // 专注提醒相关方法（简化）
    fun toggleFocusReminders(enabled: Boolean) {
        updateSettings { it.copy(focusRemindersEnabled = enabled) }
    }

    fun toggleFocusSessionNotifications(enabled: Boolean) {
        updateSettings { it.copy(focusSessionNotificationsEnabled = enabled) }
    }

    // 健康管理提醒相关方法（新增用药提醒）
    fun toggleMedicationReminders(enabled: Boolean) {
        updateSettings { it.copy(medicationRemindersEnabled = enabled) }
        Log.d("NotificationSettings", "用药提醒已${if (enabled) "启用" else "禁用"}")
    }

    fun toggleMedicationSound(enabled: Boolean) {
        updateSettings { it.copy(medicationSoundEnabled = enabled) }
    }

    fun toggleMedicationVibration(enabled: Boolean) {
        updateSettings { it.copy(medicationVibrationEnabled = enabled) }
    }

    fun setMedicationAdvanceTime(minutes: Int) {
        updateSettings { it.copy(medicationAdvanceTime = minutes) }
        Log.d("NotificationSettings", "用药提醒提前时间已设置为: ${minutes}分钟")
    }

    // 通知方式相关方法
    fun toggleSound(enabled: Boolean) {
        updateSettings { it.copy(soundEnabled = enabled) }
    }

    fun toggleVibration(enabled: Boolean) {
        updateSettings { it.copy(vibrationEnabled = enabled) }
    }
    
    // 免打扰相关方法（简化）
    fun toggleDoNotDisturb(enabled: Boolean) {
        updateSettings { it.copy(doNotDisturbEnabled = enabled) }
    }

    fun setDoNotDisturbStartTime(time: String) {
        updateSettings { it.copy(doNotDisturbStartTime = time) }
    }

    fun setDoNotDisturbEndTime(time: String) {
        updateSettings { it.copy(doNotDisturbEndTime = time) }
    }

    // 提醒时间相关方法（统一简化）
    fun setDefaultReminderTime(minutes: Int) {
        updateSettings { it.copy(defaultReminderTime = minutes) }
    }
    
    // 时间选择器相关方法
    fun showTimePicker(settingKey: String) {
        _showTimePickerDialog.value = settingKey
    }
    
    fun hideTimePicker() {
        _showTimePickerDialog.value = null
    }
    
    /**
     * 获取提醒时间选项（动态配置版本）
     */
    fun getReminderTimeOptions(): List<Pair<Int, String>> {
        return _notificationConfig.value.reminderTimeOptions.map {
            it.minutes to it.label
        }
    }

    /**
     * 获取用药提醒时间选项（动态配置版本）
     */
    fun getMedicationReminderTimeOptions(): List<Pair<Int, String>> {
        return _notificationConfig.value.medicationReminderTimeOptions.map {
            it.minutes to it.label
        }
    }
    
    /**
     * 发送测试通知
     */
    fun sendTestNotification() {
        viewModelScope.launch {
            try {
                val currentSettings = _notificationSettings.value

                // 根据当前设置发送不同类型的测试通知（简化版本）
                when {
                    currentSettings.medicationRemindersEnabled -> {
                        // 优先测试用药提醒
                        notificationManager.showMedicationReminder(
                            medicationId = "test_medication_001",
                            medicationName = "测试药物",
                            dosage = "1片",
                            reminderTime = "现在",
                            settings = currentSettings
                        )
                    }
                    currentSettings.taskRemindersEnabled -> {
                        notificationManager.showTaskReminder(
                            taskId = "test_task_001",
                            taskTitle = "测试任务",
                            dueTime = "今天 18:00",
                            priority = "高",
                            settings = currentSettings
                        )
                    }
                    currentSettings.habitRemindersEnabled -> {
                        notificationManager.showHabitReminder(
                            habitId = "test_habit_001",
                            habitName = "测试习惯",
                            streakCount = 7,
                            settings = currentSettings
                        )
                    }
                    currentSettings.focusRemindersEnabled -> {
                        notificationManager.showFocusStartReminder(
                            sessionName = "测试专注",
                            duration = 25,
                            settings = currentSettings
                        )
                    }
                    else -> {
                        // 默认发送每日回顾通知
                        notificationManager.showDailyReview(
                            completedTasks = 5,
                            totalTasks = 8,
                            settings = currentSettings
                        )
                    }
                }
                
                Log.d("NotificationSettings", "测试通知已发送")
            } catch (e: Exception) {
                Log.e("NotificationSettings", "发送测试通知失败", e)
            }
        }
    }
} 