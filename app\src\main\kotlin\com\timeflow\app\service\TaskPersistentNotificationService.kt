package com.timeflow.app.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.timeflow.app.MainActivity
import com.timeflow.app.R
import com.timeflow.app.TimeFlowApplication
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.NotificationHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

/**
 * 任务常驻通知服务
 * 参考TickTick设计，提供任务的常驻通知栏显示
 * 除非手动删除，否则不能清除
 */
@AndroidEntryPoint
class TaskPersistentNotificationService : Service() {
    
    companion object {
        private const val TAG = "TaskPersistentNotificationService"
        private const val NOTIFICATION_ID = 2001
        private const val CHANNEL_ID = "task_persistent_channel"
        private const val CHANNEL_NAME = "任务常驻通知"
        private const val CHANNEL_DESCRIPTION = "显示今日任务和待办事项"
        
        // 操作常量
        const val ACTION_START_PERSISTENT = "START_PERSISTENT"
        const val ACTION_STOP_PERSISTENT = "STOP_PERSISTENT"
        const val ACTION_COMPLETE_TASK = "COMPLETE_TASK"
        const val ACTION_SHOW_TASKS = "SHOW_TASKS"
        const val ACTION_REFRESH_TASKS = "REFRESH_TASKS"
        
        // Intent额外数据
        const val EXTRA_TASK_ID = "task_id"
        const val EXTRA_TASK_TITLE = "task_title"
    }
    
    @Inject
    lateinit var taskRepository: TaskRepository
    
    @Inject
    lateinit var notificationHelper: NotificationHelper
    
    private var notificationManager: NotificationManager? = null
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var updateJob: Job? = null
    
    // 任务数据状态
    private val _todayTasks = MutableStateFlow<List<Task>>(emptyList())
    val todayTasks: StateFlow<List<Task>> = _todayTasks.asStateFlow()
    
    private val _pendingTasks = MutableStateFlow<List<Task>>(emptyList())
    val pendingTasks: StateFlow<List<Task>> = _pendingTasks.asStateFlow()
    
    private val _isServiceRunning = MutableStateFlow(false)
    val isServiceRunning: StateFlow<Boolean> = _isServiceRunning.asStateFlow()
    
    inner class LocalBinder : Binder() {
        fun getService(): TaskPersistentNotificationService = this@TaskPersistentNotificationService
    }
    
    private val binder = LocalBinder()
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "📋 任务常驻通知服务启动")
        
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        createNotificationChannel()
        
        // 启动数据监听
        startDataObservation()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "收到服务命令: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_PERSISTENT -> startPersistentNotification()
            ACTION_STOP_PERSISTENT -> stopPersistentNotification()
            ACTION_COMPLETE_TASK -> {
                val taskId = intent.getStringExtra(EXTRA_TASK_ID)
                if (taskId != null) {
                    completeTask(taskId)
                }
            }
            ACTION_SHOW_TASKS -> showTasksInApp()
            ACTION_REFRESH_TASKS -> refreshTaskData()
            else -> {
                // 默认启动常驻通知
                startPersistentNotification()
            }
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "🧹 任务常驻通知服务销毁")
        updateJob?.cancel()
        serviceScope.cancel()
        _isServiceRunning.value = false
    }
    
    /**
     * 创建通知渠道（Android 8.0+）
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW // 低重要性，避免声音打扰
            ).apply {
                description = CHANNEL_DESCRIPTION
                setShowBadge(true) // 显示角标
                enableLights(false) // 不闪灯
                enableVibration(false) // 不振动
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC // 锁屏可见
            }
            
            notificationManager?.createNotificationChannel(channel)
            Log.d(TAG, "✅ 任务常驻通知渠道已创建")
        }
    }
    
    /**
     * 启动常驻通知
     */
    private fun startPersistentNotification() {
        Log.d(TAG, "🚀 启动任务常驻通知")
        
        _isServiceRunning.value = true
        
        // 立即刷新任务数据
        refreshTaskData()
        
        // 创建并显示前台通知
        val notification = createTaskNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        // 启动定时更新
        startPeriodicUpdate()
    }
    
    /**
     * 停止常驻通知
     */
    private fun stopPersistentNotification() {
        Log.d(TAG, "🛑 停止任务常驻通知")
        
        _isServiceRunning.value = false
        updateJob?.cancel()
        
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    /**
     * 启动数据观察
     */
    private fun startDataObservation() {
        serviceScope.launch {
            // 观察任务变化并自动更新通知
            taskRepository.observeAllTasks().collect { tasks ->
                updateTaskData(tasks)
                if (_isServiceRunning.value) {
                    updateNotification()
                }
            }
        }
    }
    
    /**
     * 启动定期更新
     */
    private fun startPeriodicUpdate() {
        updateJob?.cancel()
        updateJob = serviceScope.launch {
            while (isActive && _isServiceRunning.value) {
                delay(30000) // 每30秒更新一次
                refreshTaskData()
                updateNotification()
            }
        }
    }
    
    /**
     * 刷新任务数据
     */
    private fun refreshTaskData() {
        serviceScope.launch {
            try {
                val allTasks = taskRepository.getAllTasks()
                updateTaskData(allTasks)
                Log.d(TAG, "任务数据已刷新: 今日${_todayTasks.value.size}个，待办${_pendingTasks.value.size}个")
            } catch (e: Exception) {
                Log.e(TAG, "刷新任务数据失败", e)
            }
        }
    }
    
    /**
     * 更新任务数据
     * 注意：只显示主任务，不显示子待办（子任务）
     */
    private fun updateTaskData(allTasks: List<Task>) {
        val today = LocalDate.now()

        // 筛选今日任务（今天到期或今天开始的未完成主任务，排除子任务）
        val todayTasks = allTasks.filter { task ->
            !task.isCompleted &&
            task.parentTaskId == null && // 只显示主任务，排除子任务
            (
                task.dueDate?.toLocalDate() == today ||
                task.startDate?.toLocalDate() == today ||
                (task.dueDate?.toLocalDate()?.isBefore(today) == true) // 包含过期任务
            )
        }.sortedWith(compareBy<Task> {
            // 按优先级排序：紧急 > 高 > 中 > 低
            when (it.priority?.name) {
                "URGENT" -> 0
                "HIGH" -> 1
                "MEDIUM" -> 2
                "LOW" -> 3
                else -> 4
            }
        }.thenBy { it.startDate ?: it.dueDate }) // 优先按开始时间排序，无开始时间则按截止时间

        // 筛选所有未完成的主任务（排除子任务）
        val pendingTasks = allTasks.filter { task ->
            !task.isCompleted &&
            task.parentTaskId == null // 只显示主任务，排除子任务
        }.sortedWith(compareBy<Task> {
            when (it.priority?.name) {
                "URGENT" -> 0
                "HIGH" -> 1
                "MEDIUM" -> 2
                "LOW" -> 3
                else -> 4
            }
        }.thenBy { it.startDate ?: it.dueDate }) // 优先按开始时间排序，无开始时间则按截止时间

        _todayTasks.value = todayTasks
        _pendingTasks.value = pendingTasks

        Log.d(TAG, "任务数据已更新: 今日主任务${todayTasks.size}个，待办主任务${pendingTasks.size}个（已排除子任务）")
    }
    
    /**
     * 完成任务
     */
    private fun completeTask(taskId: String) {
        serviceScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null && !task.isCompleted) {
                    val completedTask = task.copy(
                        isCompleted = true,
                        completedAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    )
                    taskRepository.updateTask(completedTask)
                    Log.d(TAG, "任务已完成: ${task.title}")
                    
                    // 立即更新通知
                    refreshTaskData()
                    updateNotification()
                }
            } catch (e: Exception) {
                Log.e(TAG, "完成任务失败: $taskId", e)
            }
        }
    }
    
    /**
     * 在应用中显示任务
     */
    private fun showTasksInApp() {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASKS", true)
        }
        startActivity(intent)
    }

    /**
     * 创建任务通知
     */
    private fun createTaskNotification(): Notification {
        val todayTasks = _todayTasks.value
        val pendingTasks = _pendingTasks.value

        // 构建通知标题和内容
        val (title, content, expandedContent) = buildNotificationContent(todayTasks, pendingTasks)

        // 主要点击意图 - 打开应用
        val mainIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("NAVIGATE_TO_TASKS", true)
        }
        val mainPendingIntent = PendingIntent.getActivity(
            this,
            0,
            mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建操作按钮
        val actions = createNotificationActions(todayTasks)

        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setColor(ContextCompat.getColor(this, R.color.primary_color))
            .setOngoing(true) // 持续通知，无法被滑动删除
            .setAutoCancel(false)
            .setShowWhen(false)
            .setContentIntent(mainPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // 锁屏可见

        // 添加展开样式
        if (expandedContent.isNotEmpty()) {
            builder.setStyle(NotificationCompat.BigTextStyle()
                .bigText(expandedContent))
        }

        // 添加操作按钮
        actions.forEach { action ->
            builder.addAction(action)
        }

        return builder.build()
    }

    /**
     * 构建通知内容
     */
    private fun buildNotificationContent(
        todayTasks: List<Task>,
        pendingTasks: List<Task>
    ): Triple<String, String, String> {
        val todayCount = todayTasks.size
        val pendingCount = pendingTasks.size

        // 构建标题（只显示主任务数量）
        val title = when {
            todayCount > 0 -> "📋 今日主任务 ($todayCount)"
            pendingCount > 0 -> "📋 待办主任务 ($pendingCount)"
            else -> "📋 TimeFlow"
        }

        // 构建简短内容（显示任务开始时间）
        val content = when {
            todayCount > 0 -> {
                val firstTask = todayTasks.first()
                val timeInfo = firstTask.startDate?.let {
                    it.format(DateTimeFormatter.ofPattern("HH:mm"))
                } ?: ""
                "${firstTask.title}${if (timeInfo.isNotEmpty()) " $timeInfo" else ""}"
            }
            pendingCount > 0 -> {
                val firstTask = pendingTasks.first()
                val timeInfo = firstTask.startDate?.let {
                    it.format(DateTimeFormatter.ofPattern("HH:mm"))
                } ?: ""
                "${firstTask.title}${if (timeInfo.isNotEmpty()) " $timeInfo" else ""}"
            }
            else -> "暂无待办任务"
        }

        // 构建展开内容（只显示主任务，不包含子待办）
        // 注意：不在展开内容中重复显示标题，避免与通知标题重复
        val expandedContent = buildString {
            if (todayCount > 0) {
                // 直接显示任务列表，不重复标题（支持显示5个主任务，显示开始时间）
                todayTasks.take(5).forEach { task ->
                    val timeInfo = task.startDate?.let {
                        it.format(DateTimeFormatter.ofPattern("HH:mm"))
                    } ?: ""
                    val priorityIcon = when (task.priority?.name) {
                        "URGENT" -> "🔴"
                        "HIGH" -> "🟠"
                        "MEDIUM" -> "🟡"
                        "LOW" -> "🟢"
                        else -> "⚪"
                    }
                    appendLine("$priorityIcon ${task.title}${if (timeInfo.isNotEmpty()) " $timeInfo" else ""}")
                }
                if (todayCount > 5) {
                    appendLine("... 还有 ${todayCount - 5} 个主任务")
                }
            }

            if (pendingCount > todayCount) {
                if (todayCount > 0) appendLine()
                appendLine("📋 其他主任务 (${pendingCount - todayCount}):")
                pendingTasks.filter { task ->
                    !todayTasks.contains(task)
                }.take(3).forEach { task ->
                    val priorityIcon = when (task.priority?.name) {
                        "URGENT" -> "🔴"
                        "HIGH" -> "🟠"
                        "MEDIUM" -> "🟡"
                        "LOW" -> "🟢"
                        else -> "⚪"
                    }
                    appendLine("$priorityIcon ${task.title}")
                }
            }

            if (isEmpty()) {
                append("🎉 所有主任务已完成！")
            }
        }

        return Triple(title, content, expandedContent)
    }

    /**
     * 创建通知操作按钮
     */
    private fun createNotificationActions(todayTasks: List<Task>): List<NotificationCompat.Action> {
        val actions = mutableListOf<NotificationCompat.Action>()

        // 如果有今日任务，添加"完成第一个任务"按钮
        if (todayTasks.isNotEmpty()) {
            val firstTask = todayTasks.first()
            val completeIntent = Intent(this, com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver::class.java).apply {
                action = ACTION_COMPLETE_TASK
                putExtra(EXTRA_TASK_ID, firstTask.id)
                putExtra(EXTRA_TASK_TITLE, firstTask.title)
            }
            val completePendingIntent = PendingIntent.getBroadcast(
                this,
                firstTask.id.hashCode(),
                completeIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            actions.add(
                NotificationCompat.Action.Builder(
                    R.drawable.ic_check_circle,
                    "完成",
                    completePendingIntent
                ).build()
            )
        }

        // 添加"查看全部"按钮
        val showAllIntent = Intent(this, com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver::class.java).apply {
            action = ACTION_SHOW_TASKS
        }
        val showAllPendingIntent = PendingIntent.getBroadcast(
            this,
            ACTION_SHOW_TASKS.hashCode(),
            showAllIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        actions.add(
            NotificationCompat.Action.Builder(
                R.drawable.ic_list,
                "查看全部",
                showAllPendingIntent
            ).build()
        )

        return actions
    }

    /**
     * 更新通知
     */
    private fun updateNotification() {
        if (!_isServiceRunning.value) return

        try {
            val notification = createTaskNotification()
            notificationManager?.notify(NOTIFICATION_ID, notification)
            Log.d(TAG, "通知已更新")
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }
}
