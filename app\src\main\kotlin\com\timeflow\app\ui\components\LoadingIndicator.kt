package com.timeflow.app.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 加载指示器组件
 * @param isLoading 是否正在加载
 * @param message 加载提示消息
 * @param modifier 修饰符
 */
@Composable
fun LoadingIndicator(
    isLoading: Bo<PERSON>an,
    message: String = "加载中...",
    modifier: Modifier = Modifier
) {
    if (isLoading) {
        Dialog(
            onDismissRequest = { },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false
            )
        ) {
            Card(
                modifier = Modifier
                    .width(200.dp)
                    .height(150.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(48.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = message,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

/**
 * 内联加载指示器，用于页面内嵌使用
 * @param modifier 修饰符
 * @param color 指示器颜色
 */
@Composable
fun InlineLoadingIndicator(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary
) {
    Box(
        modifier = modifier
            .padding(8.dp)
            .fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(36.dp),
            color = color
        )
    }
}

/**
 * 三点加载动画
 */
@Composable
fun DotsLoadingIndicator(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary
) {
    val dotSize = 8.dp
    val delayUnit = 300
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        val infiniteTransition = rememberInfiniteTransition(label = "dots")
        
        // 第一个点动画
        val firstDotAlpha by infiniteTransition.animateFloat(
            initialValue = 0.2f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(delayUnit, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ), label = "dot1"
        )
        
        // 第二个点动画
        val secondDotAlpha by infiniteTransition.animateFloat(
            initialValue = 0.2f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(delayUnit, delayMillis = delayUnit, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ), label = "dot2"
        )
        
        // 第三个点动画
        val thirdDotAlpha by infiniteTransition.animateFloat(
            initialValue = 0.2f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(delayUnit, delayMillis = delayUnit * 2, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ), label = "dot3"
        )
        
        // 渲染点
        Box(
            modifier = Modifier
                .padding(2.dp)
                .size(dotSize)
                .clip(CircleShape)
                .background(color.copy(alpha = firstDotAlpha))
        )
        
        Box(
            modifier = Modifier
                .padding(2.dp)
                .size(dotSize)
                .clip(CircleShape)
                .background(color.copy(alpha = secondDotAlpha))
        )
        
        Box(
            modifier = Modifier
                .padding(2.dp)
                .size(dotSize)
                .clip(CircleShape)
                .background(color.copy(alpha = thirdDotAlpha))
        )
    }
} 