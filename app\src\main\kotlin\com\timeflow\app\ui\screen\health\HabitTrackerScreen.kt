﻿package com.timeflow.app.ui.screen.health

import android.app.Activity
import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import kotlinx.coroutines.delay
import androidx.compose.foundation.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.foundation.layout.WindowInsets
import androidx.lifecycle.viewmodel.compose.viewModel
import java.time.YearMonth
import java.time.format.TextStyle
import java.util.Locale
import com.timeflow.app.R
import com.timeflow.app.data.model.HabitCategory
import com.timeflow.app.data.model.HabitFrequencyType
import com.timeflow.app.data.model.CustomHabitCategory
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.health.FrequencyType
import com.timeflow.app.ui.viewmodel.HabitTemplate
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.math.*
import kotlin.random.Random
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.zIndex
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.combine
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyListItemInfo
import androidx.compose.runtime.key
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.foundation.border
import androidx.compose.material3.ButtonDefaults
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
// 🔧 新增：滑动手势和撤销功能相关导入
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.material3.SwipeToDismissBox
import androidx.compose.material3.SwipeToDismissBoxValue
import androidx.compose.material3.rememberSwipeToDismissBoxState
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.ui.draw.scale
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.unit.IntOffset
import kotlin.math.roundToInt
import kotlinx.coroutines.delay
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch

// 使用数据模型中的 HabitCategory，这里移除本地定义

/**
 * 🔧 新增：待处理的习惯操作（用于撤销功能）
 */
data class PendingHabitAction(
    val habit: HabitData,
    val actionType: HabitActionType,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 🔧 新增：习惯操作类型
 */
enum class HabitActionType(val displayName: String) {
    ARCHIVE("归档"),
    DELETE("删除")
}

/**
 * 习惯数据类
 */
data class HabitData(
    val id: String,
    val name: String,
    val icon: String, // 改为String类型存储emoji
    val color: Color,
    val category: HabitCategory,
    val customCategoryId: String? = null, // 新增：自定义分类ID
    val frequency: List<DayOfWeek> = DayOfWeek.values().toList(),
    val completedDates: List<LocalDate> = emptyList(),
    val streak: Int = 0,
    val description: String = "",
    val createdAt: LocalDate = LocalDate.now(),
    val customEmoji: String = "", // 添加自定义emoji字段
    val reminderEnabled: Boolean = false, // 是否启用提醒
    val reminderTime: String = "08:00", // 提醒时间，格式为 HH:MM
    val isActive: Boolean = true // 是否为活跃状态（用于归档功能）
)

/**
 * 习惯追踪页面 - 优化版
 * 参考FastLog应用的简洁设计风格
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HabitTrackerScreen(
    onBackClick: () -> Unit,
    onHabitDetailClick: (String) -> Unit = {}, // 添加导航到习惯详情的回调
    navController: androidx.navigation.NavController? = null, // 添加navController参数
    viewModel: HabitViewModel = androidx.lifecycle.viewmodel.compose.viewModel()
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态管理
    var showAddHabitDialog by remember { mutableStateOf(false) }
    var showHabitDetailDialog by remember { mutableStateOf(false) }
    var selectedHabitForDetail by remember { mutableStateOf<HabitData?>(null) }
    var showCategoryManageDialog by remember { mutableStateOf(false) }
    var showAddCategoryDialog by remember { mutableStateOf(false) }
    var showHabitActionDialog by remember { mutableStateOf(false) }
    var selectedHabitForAction by remember { mutableStateOf<HabitData?>(null) }
    // 🔧 新增：编辑习惯对话框状态
    var showEditHabitDialog by remember { mutableStateOf(false) }
    var selectedHabitForEdit by remember { mutableStateOf<HabitData?>(null) }
    
    // 🔧 新增：撤销功能状态管理
    val snackbarHostState = remember { SnackbarHostState() }
    var pendingAction by remember { mutableStateOf<PendingHabitAction?>(null) }
    val scope = rememberCoroutineScope()
    
    val habits by viewModel.habits.collectAsState()
    val selectedCategory by viewModel.selectedCategory.collectAsState()
    val customCategories by viewModel.customCategories.collectAsState()
    val selectedCustomCategory by viewModel.selectedCustomCategory.collectAsState()
    
    // 确保ViewModel初始化
    LaunchedEffect(Unit) {
        android.util.Log.d("HabitTracker", "开始初始化ViewModel")
        viewModel.initializeCustomCategories()
        // 移除重复的强制加载逻辑，ViewModel会自动处理数据加载
    }

    // 🔧 添加调试信息
    LaunchedEffect(customCategories) {
        android.util.Log.d("HabitTracker", "🔧 customCategories更新: 数量=${customCategories.size}")
        customCategories.forEach { category ->
            android.util.Log.d("HabitTracker", "🔧 分类: ${category.title} (${category.id})")
        }
    }
    
    // 简化数据处理，避免使用mutableStateListOf
    val activeHabits = remember(habits) { 
        val filtered = habits.filter { it.isActive }
        android.util.Log.d("HabitTracker", "总习惯数: ${habits.size}, 活跃习惯数: ${filtered.size}")
        filtered
    }

    // 处理系统栏
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it, false)
        }
    }
    
    // 移除复杂的拖拽状态以提高性能
    
    // 页面状态管理（用于分类切换）
    val pagerState = rememberPagerState(pageCount = { customCategories.size + 1 })
    
    // 简化状态同步，避免循环依赖
    LaunchedEffect(selectedCustomCategory, customCategories.size) {
        if (customCategories.isNotEmpty()) {
            val currentCategory = selectedCustomCategory
            val targetPage = if (currentCategory == null) {
                0
            } else {
                customCategories.indexOfFirst { it.id == currentCategory.id } + 1
            }
            
            if (targetPage >= 0 && targetPage != pagerState.currentPage) {
                android.util.Log.d("HabitTracker", "切换到页面: $targetPage")
                try {
                    pagerState.animateScrollToPage(targetPage)
                } catch (e: Exception) {
                    android.util.Log.e("HabitTracker", "页面切换失败", e)
                }
            }
        }
    }
    
    Scaffold(
        topBar = {
            // 简洁的顶部标题栏 - 参考FastLog风格
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
                    .height(48.dp) // 减小高度
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 6.dp), // 减小内边距
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 返回按钮
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(40.dp) // 减小按钮尺寸
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color(0xFF1F1937), // 深色
                            modifier = Modifier.size(20.dp) // 减小图标尺寸
                        )
                    }
                    
                    // 标题文本 - 采用FastLog的简洁风格
                    Text(
                        text = "习惯追踪",
                        fontSize = 18.sp, // 减小字体
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1F1937), // 深色标题
                        maxLines = 1,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 添加按钮
                    IconButton(
                        onClick = { 
                            navController?.navigate(com.timeflow.app.navigation.AppDestinations.ADD_HABIT_ROUTE)
                        },
                        modifier = Modifier.size(40.dp) // 减小按钮尺寸
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加习惯",
                            tint = Color(0xFF1F1937), // 深色
                            modifier = Modifier.size(20.dp) // 减小图标尺寸
                        )
                    }
                }
            }
        },
        snackbarHost = {
            // 🔧 新增：SnackbarHost用于显示撤销提示
            SnackbarHost(
                hostState = snackbarHostState,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        },
        containerColor = Color(0xFFFAFAFA) // 浅灰背景，参考FastLog
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 分类标签栏 - 使用自定义分类，支持管理
            LazyRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(2.dp), // 标签间仅2dp间距
                contentPadding = PaddingValues(horizontal = 8.dp) // 仅在两端保留少量边距
            ) {
                // "所有"选项卡
                item {
                    Box(
                modifier = Modifier
                            .clickable {
                                scope.launch {
                                    viewModel.setSelectedCustomCategory(null)
                                    pagerState.animateScrollToPage(0)
                                }
                            }
                            .background(
                                color = if (pagerState.currentPage == 0) Color(0xFF1F1937) else Color.Transparent,
                                shape = RoundedCornerShape(6.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "所有", 
                            fontSize = 10.sp,
                            fontWeight = if (pagerState.currentPage == 0) FontWeight.SemiBold else FontWeight.Medium,
                            color = if (pagerState.currentPage == 0) Color.White else Color(0xFF6B7280),
                            maxLines = 1,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                // 各自定义分类选项卡
                itemsIndexed(customCategories) { index, category ->
                    Box(
                        modifier = Modifier
                            .clickable {
                                scope.launch {
                                    viewModel.setSelectedCustomCategory(category)
                                    pagerState.animateScrollToPage(index + 1)
                                }
                            }
                            .background(
                                // 统一使用深灰色背景，与"所有"标签保持一致
                                color = if (pagerState.currentPage == index + 1) Color(0xFF1F1937) else Color.Transparent,
                                shape = RoundedCornerShape(6.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                            Text(
                                text = category.title, 
                            fontSize = 10.sp,
                            fontWeight = if (pagerState.currentPage == index + 1) FontWeight.SemiBold else FontWeight.Medium,
                            // 统一文字颜色：选中时白色，未选中时灰色
                            color = if (pagerState.currentPage == index + 1) Color.White else Color(0xFF6B7280),
                                maxLines = 1,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                // 管理分类按钮
                item {
                    Box(
                        modifier = Modifier
                            .clickable { showCategoryManageDialog = true }
                            .background(
                                color = Color(0xFF1F1937).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(6.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 4.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.Settings,
                                contentDescription = "管理分类",
                                tint = Color(0xFF6B7280),
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            Text(
                                text = "管理", 
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF6B7280),
                                maxLines = 1
                            )
                        }
                    }
                }
            }
            
            // 使用HorizontalPager实现滑动切换
            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 12.dp)
            ) { pageIndex ->
                // 根据页面索引确定当前自定义类别
                val currentCategory = if (pageIndex == 0) null else customCategories.getOrNull(pageIndex - 1)
                
                // 过滤对应类别的习惯（只显示活跃的习惯）
                // 🔧 修复：使用derivedStateOf确保数据变化时能正确重新计算
                val filteredHabits by remember(activeHabits, currentCategory) {
                    derivedStateOf {
                        val result = if (currentCategory != null) {
                            activeHabits.filter { it.customCategoryId == currentCategory.id }
                        } else {
                            activeHabits
                        }
                        android.util.Log.d("HabitTracker", "🔧 页面 $pageIndex, 分类: ${currentCategory?.title ?: "所有"}")
                        android.util.Log.d("HabitTracker", "🔧 分类ID: ${currentCategory?.id ?: "null"}")
                        android.util.Log.d("HabitTracker", "🔧 活跃习惯总数: ${activeHabits.size}")
                        android.util.Log.d("HabitTracker", "🔧 过滤后习惯数: ${result.size}")

                        // 🔧 添加详细的习惯过滤调试信息
                        activeHabits.forEach { habit ->
                            android.util.Log.d("HabitTracker", "🔧 习惯[${habit.name}] customCategoryId=${habit.customCategoryId}")
                        }

                        if (result.isEmpty() && currentCategory != null) {
                            android.util.Log.w("HabitTracker", "⚠️ 分类[${currentCategory.title}]下没有找到匹配的习惯")
                            // 🔧 添加更详细的调试信息
                            android.util.Log.d("HabitTracker", "🔧 当前分类ID: ${currentCategory.id}")
                            android.util.Log.d("HabitTracker", "🔧 所有活跃习惯的分类ID:")
                            activeHabits.forEach { habit ->
                                android.util.Log.d("HabitTracker", "  - ${habit.name}: ${habit.customCategoryId}")
                            }
                        }

                        result
                    }
                }
            
                // 习惯列表页面内容
            if (filteredHabits.isEmpty()) {
                // 空状态提示
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = if (currentCategory != null) "该分类下暂无习惯" else "暂无习惯",
                            style = MaterialTheme.typography.bodyLarge,
                            color = Color.Gray
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "点击右上角 + 添加新习惯",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize()
                ) {
                        // 使用key来确保一致性
                        itemsIndexed(
                            items = filteredHabits,
                            key = { _, habit -> habit.id }
                        ) { index, habit ->
                                                android.util.Log.d("HabitTracker", "渲染习惯卡片: ${habit.name}, ID: ${habit.id}")
                        SwipeableHabitListItem(
                            habit = habit,
                            onHabitClick = { 
                                // 显示习惯详情弹窗
                                android.util.Log.d("HabitTracker", "习惯卡片被点击: ${habit.id}, 习惯名称: ${habit.name}")
                                selectedHabitForDetail = habit
                                showHabitDetailDialog = true
                            },
                            onCheckClick = { date -> 
                                // 简化打卡逻辑，直接调用ViewModel
                                android.util.Log.d("HabitTracker", "打卡操作: ${habit.id}, 日期: $date")
                                viewModel.markHabitAsCompleted(habit.id, date)
                            },
                            onEmojiUpdate = { habitId, emoji -> 
                                // 简化emoji更新逻辑
                                android.util.Log.d("HabitTracker", "更新emoji: $habitId -> $emoji")
                            },
                            onLongPress = { selectedHabit ->
                                // 🔧 修改：长按直接显示编辑对话框
                                android.util.Log.d("HabitTracker", "长按编辑习惯: ${selectedHabit.id}")
                                selectedHabitForEdit = selectedHabit
                                showEditHabitDialog = true
                            },
                            onArchive = { habitToArchive ->
                                // 🔧 修复：立即执行归档操作，提供撤销功能
                                android.util.Log.d("HabitTracker", "立即执行归档操作: ${habitToArchive.name}")
                                
                                // 保存原始习惯数据用于撤销
                                val originalHabit = habitToArchive
                                pendingAction = PendingHabitAction(originalHabit, HabitActionType.ARCHIVE)
                                
                                // 立即执行归档操作
                                viewModel.archiveHabit(habitToArchive.id)
                                
                                // 显示撤销提示
                                scope.launch {
                                    val result = snackbarHostState.showSnackbar(
                                        message = "习惯「${habitToArchive.name}」已归档",
                                        actionLabel = "撤销",
                                        duration = androidx.compose.material3.SnackbarDuration.Long
                                    )
                                    
                                    if (result == SnackbarResult.ActionPerformed) {
                                        // 用户点击了撤销 - 恢复习惯
                                        android.util.Log.d("HabitTracker", "用户撤销了归档操作，恢复习惯: ${originalHabit.name}")
                                        viewModel.restoreHabit(originalHabit.id)
                                        pendingAction = null
                                    } else {
                                        // Snackbar消失，清理状态
                                        android.util.Log.d("HabitTracker", "归档操作完成，清理状态: ${originalHabit.name}")
                                        pendingAction = null
                                    }
                                }
                            },
                            onDelete = { habitToDelete ->
                                // 🔧 修复：立即执行删除操作，提供撤销功能
                                android.util.Log.d("HabitTracker", "立即执行删除操作: ${habitToDelete.name}")
                                
                                // 保存原始习惯数据用于撤销
                                val originalHabit = habitToDelete
                                pendingAction = PendingHabitAction(originalHabit, HabitActionType.DELETE)
                                
                                // 立即执行删除操作
                                viewModel.deleteHabit(habitToDelete.id)
                                
                                // 显示撤销提示
                                scope.launch {
                                    val result = snackbarHostState.showSnackbar(
                                        message = "习惯「${habitToDelete.name}」已删除",
                                        actionLabel = "撤销",
                                        duration = androidx.compose.material3.SnackbarDuration.Long
                                    )
                                    
                                    if (result == SnackbarResult.ActionPerformed) {
                                        // 用户点击了撤销 - 恢复习惯
                                        android.util.Log.d("HabitTracker", "用户撤销了删除操作，恢复习惯: ${originalHabit.name}")
                                        viewModel.restoreHabit(originalHabit.id)
                                        pendingAction = null
                                    } else {
                                        // Snackbar消失，清理状态
                                        android.util.Log.d("HabitTracker", "删除操作完成，清理状态: ${originalHabit.name}")
                                        pendingAction = null
                                    }
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 3.dp) // 减小垂直间距
                        )
                    }
                    
                    // 底部间距
                    item {
                        Spacer(modifier = Modifier.height(80.dp))
                    }
                }
            }
        }
        }
    }
    
         // 添加习惯对话框
     if (showAddHabitDialog) {
         AddHabitDialog(
             onDismiss = { showAddHabitDialog = false },
             onConfirm = { habitData ->
                 android.util.Log.d("HabitTracker", "🔧 开始添加新习惯: name=${habitData.name}, customCategoryId=${habitData.customCategoryId}")
                 viewModel.addHabit(habitData)
                 showAddHabitDialog = false

                 // 🔧 修复：新建习惯后自动切换到对应的分类标签页
                 habitData.customCategoryId?.let { categoryId ->
                     val categoryIndex = customCategories.indexOfFirst { it.id == categoryId }
                     android.util.Log.d("HabitTracker", "🔧 查找分类索引: categoryId=$categoryId, categoryIndex=$categoryIndex")
                     if (categoryIndex >= 0) {
                         scope.launch {
                             val targetPage = categoryIndex + 1 // +1 因为第0页是"所有"
                             android.util.Log.d("HabitTracker", "🔧 新建习惯后自动切换到分类页面: $targetPage")
                             pagerState.animateScrollToPage(targetPage)
                             viewModel.setSelectedCustomCategory(customCategories[categoryIndex])
                         }
                     } else {
                         android.util.Log.w("HabitTracker", "⚠️ 未找到对应的分类索引: categoryId=$categoryId")
                     }
                 } ?: android.util.Log.w("HabitTracker", "⚠️ 新建习惯的customCategoryId为空")
             },
             customCategories = customCategories // 🔧 修复：传入自定义分类列表
         )
     }
     
     // 习惯详情弹窗
     if (showHabitDetailDialog && selectedHabitForDetail != null) {
         HabitDetailDialog(
             habit = selectedHabitForDetail!!,
             onDismiss = { 
                 showHabitDetailDialog = false
                 selectedHabitForDetail = null
             },
             onMarkComplete = { date ->
                 // 简化打卡逻辑，直接调用ViewModel
                 selectedHabitForDetail?.let { habit ->
                     viewModel.markHabitAsCompleted(habit.id, date)
                 }
             }
         )
     }
     
     // 分类管理对话框
     if (showCategoryManageDialog) {
         CategoryManageDialog(
             categories = customCategories,
             onDismiss = { showCategoryManageDialog = false },
             onAddCategory = { showAddCategoryDialog = true },
             onDeleteCategory = { categoryId ->
                 viewModel.deleteCustomCategory(categoryId)
             },
             onEditCategory = { categoryId, title, icon, color ->
                 viewModel.updateCustomCategory(categoryId, title, icon, color)
             }
         )
     }
     
     // 添加分类对话框
     if (showAddCategoryDialog) {
         AddCategoryDialog(
             onDismiss = { showAddCategoryDialog = false },
             onConfirm = { title, icon, color ->
                 viewModel.addCustomCategory(title, icon, color)
                 showAddCategoryDialog = false
             }
         )
     }
     
     // 习惯操作对话框
     if (showHabitActionDialog && selectedHabitForAction != null) {
         HabitActionDialog(
             habit = selectedHabitForAction!!,
             onDismiss = { 
                 showHabitActionDialog = false
                 selectedHabitForAction = null
             },
             onArchive = { habit ->
                 viewModel.archiveHabit(habit.id)
                 showHabitActionDialog = false
                 selectedHabitForAction = null
             },
             onDelete = { habit ->
                 viewModel.deleteHabit(habit.id)
                 showHabitActionDialog = false
                 selectedHabitForAction = null
             }
         )
     }
     
     // 🔧 新增：编辑习惯对话框
     if (showEditHabitDialog && selectedHabitForEdit != null) {
         EditHabitDialog(
             habit = selectedHabitForEdit!!,
             onDismiss = { 
                 showEditHabitDialog = false
                 selectedHabitForEdit = null
             },
             onConfirm = { updatedHabit ->
                 viewModel.updateHabit(updatedHabit)
                 showEditHabitDialog = false
                 selectedHabitForEdit = null
             }
         )
     }
 }
 
 /**
  * 简洁的添加习惯对话框 - 参考FastLog风格
  */
 @OptIn(ExperimentalMaterial3Api::class)
 @Composable
 fun AddHabitDialog(
     onDismiss: () -> Unit,
     onConfirm: (HabitData) -> Unit,
     customCategories: List<CustomHabitCategory> = emptyList() // 新增：自定义分类列表
 ) {
     // 🔧 添加调试信息
     android.util.Log.d("AddHabitDialog", "🔧 初始化对话框，customCategories数量: ${customCategories.size}")
     customCategories.forEach { category ->
         android.util.Log.d("AddHabitDialog", "🔧 可用分类: ${category.title} (${category.id})")
     }

     var habitName by remember { mutableStateOf("") }
     var selectedCategory by remember { mutableStateOf(HabitCategory.LIFE) }
     var selectedCustomCategory by remember { mutableStateOf(customCategories.firstOrNull()) } // 新增：选中的自定义分类，默认选择第一个
     var selectedColor by remember { mutableStateOf(customCategories.firstOrNull()?.color ?: Color(0xFF81C784)) }
     var selectedIcon by remember { mutableStateOf("✅") } // 改为emoji字符串

     // 🔧 添加调试信息
     android.util.Log.d("AddHabitDialog", "🔧 初始选中的分类: ${selectedCustomCategory?.title} (${selectedCustomCategory?.id})")
     
     Dialog(
         onDismissRequest = onDismiss
     ) {
         Surface(
             modifier = Modifier
                 .fillMaxWidth()
                 .wrapContentHeight(),
             shape = RoundedCornerShape(16.dp),
             color = Color.White,
             shadowElevation = 8.dp
         ) {
             Column(
                 modifier = Modifier.padding(20.dp)
             ) {
                 // 标题
                 Text(
                     text = "添加习惯",
                     fontSize = 16.sp,
                     fontWeight = FontWeight.SemiBold,
                     color = Color(0xFF1F1937),
                     modifier = Modifier.padding(bottom = 16.dp)
                 )
                 
                 // 习惯名称输入
                 OutlinedTextField(
                     value = habitName,
                     onValueChange = { habitName = it },
                     label = { Text("习惯名称", fontSize = 12.sp) },
                     modifier = Modifier.fillMaxWidth(),
                     colors = OutlinedTextFieldDefaults.colors(
                         focusedBorderColor = selectedColor,
                         cursorColor = selectedColor
                     ),
                     textStyle = androidx.compose.ui.text.TextStyle(fontSize = 14.sp)
                 )
                 
                 Spacer(modifier = Modifier.height(12.dp))
                 
                 // 类别选择
                 Text(
                     text = "选择类别",
                     fontSize = 12.sp,
                     fontWeight = FontWeight.Medium,
                     color = Color(0xFF6B7280),
                     modifier = Modifier.padding(bottom = 8.dp)
                 )
                 
                 LazyRow(
                     horizontalArrangement = Arrangement.spacedBy(8.dp)
                 ) {
                     if (customCategories.isEmpty()) {
                         item {
                             Text(
                                 text = "暂无可用分类，请先创建分类",
                                 fontSize = 12.sp,
                                 color = Color(0xFF6B7280),
                                 modifier = Modifier.padding(16.dp)
                             )
                         }
                     } else {
                         items(customCategories) { category ->
                             val isSelected = selectedCustomCategory?.id == category.id

                             Surface(
                                 modifier = Modifier
                                     .clickable {
                                         selectedCustomCategory = category
                                         selectedColor = category.color
                                         // 清空旧的分类选择
                                         selectedCategory = HabitCategory.LIFE // 设置一个默认值
                                     },
                                 color = if (isSelected) category.color.copy(alpha = 0.15f) else Color(0xFFF3F4F6),
                                 shape = RoundedCornerShape(20.dp),
                                 // border = if (isSelected) BorderStroke(1.dp, category.color) else null
                             ) {
                                 Row(
                                     modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                     verticalAlignment = Alignment.CenterVertically
                                 ) {
                                     Text(
                                         text = category.icon, // 现在是emoji字符串
                                         fontSize = 14.sp, // 缩小分类emoji尺寸：16sp -> 14sp
                                         modifier = Modifier.size(16.dp)
                                     )
                                     Spacer(modifier = Modifier.width(4.dp))
                                     Text(
                                         text = category.title,
                                         fontSize = 11.sp,
                                         color = if (isSelected) category.color else Color(0xFF6B7280),
                                         fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium
                                     )
                                 }
                             }
                         }
                     }
                 }
                 
                 Spacer(modifier = Modifier.height(20.dp))
                 
                 // 按钮
                 Row(
                     modifier = Modifier.fillMaxWidth(),
                     horizontalArrangement = Arrangement.End
                 ) {
                     TextButton(onClick = onDismiss) {
                         Text(
                             "取消", 
                             color = Color(0xFF6B7280),
                             fontSize = 14.sp
                         )
                     }
                     
                     Spacer(modifier = Modifier.width(8.dp))
                     
                     Button(
                         onClick = {
                             android.util.Log.d("AddHabitDialog", "🔧 点击确认按钮: habitName=$habitName, selectedCustomCategory=${selectedCustomCategory?.title}")
                             if (habitName.isNotBlank() && selectedCustomCategory != null) {
                                 val newHabit = HabitData(
                                     id = java.util.UUID.randomUUID().toString(),
                                     name = habitName,
                                     icon = selectedIcon,
                                     color = selectedColor,
                                     category = selectedCategory, // 保留原有字段以兼容性
                                     customCategoryId = selectedCustomCategory!!.id, // 🔧 修复：使用选中的自定义分类ID
                                     frequency = DayOfWeek.values().toList(),
                                     completedDates = emptyList(),
                                     streak = 0,
                                     description = "",
                                     createdAt = LocalDate.now()
                                 )
                                 // 🔧 添加调试日志
                                 android.util.Log.d("AddHabitDialog", "🔧 创建新习惯: name=${newHabit.name}, customCategoryId=${newHabit.customCategoryId}, selectedCategory=${selectedCustomCategory!!.title}")
                                 onConfirm(newHabit)
                             } else {
                                 android.util.Log.w("AddHabitDialog", "⚠️ 无法创建习惯: habitName=$habitName, selectedCustomCategory=${selectedCustomCategory?.title}")
                             }
                         },
                         enabled = habitName.isNotBlank() && selectedCustomCategory != null && customCategories.isNotEmpty(),
                         colors = ButtonDefaults.buttonColors(
                             containerColor = selectedColor
                         ),
                         shape = RoundedCornerShape(8.dp)
                     ) {
                    Text(
                             "确定", 
                             color = Color.White,
                             fontSize = 14.sp
                         )
                     }
                 }
             }
         }
     }
 }

/**
 * 🔧 新增：支持滑动手势的习惯卡片
 * 左滑归档，右滑删除，支持30秒内撤销
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeableHabitListItem(
    habit: HabitData,
    onHabitClick: () -> Unit,
    onCheckClick: (LocalDate) -> Unit,
    onEmojiUpdate: (String, String) -> Unit,
    onLongPress: (HabitData) -> Unit = {},
    onArchive: (HabitData) -> Unit,
    onDelete: (HabitData) -> Unit,
    modifier: Modifier = Modifier
) {
    val hapticFeedback = LocalHapticFeedback.current
    val scope = rememberCoroutineScope()
    
    // 滑动状态
    val swipeToDismissState = rememberSwipeToDismissBoxState(
        confirmValueChange = { dismissValue ->
            when (dismissValue) {
                SwipeToDismissBoxValue.StartToEnd -> {
                    // 右滑删除
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    onDelete(habit)
                    true
                }
                SwipeToDismissBoxValue.EndToStart -> {
                    // 左滑归档
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    onArchive(habit)
                    true
                }
                SwipeToDismissBoxValue.Settled -> false
            }
        }
    )
    
    SwipeToDismissBox(
        state = swipeToDismissState,
        modifier = modifier,
        backgroundContent = {
            // 滑动背景内容
            SwipeBackgroundContent(
                dismissDirection = swipeToDismissState.dismissDirection,
                habit = habit
            )
        },
        content = {
            // 主要的习惯卡片内容
            OptimizedHabitListItem(
                habit = habit,
                onHabitClick = onHabitClick,
                onCheckClick = onCheckClick,
                onEmojiUpdate = onEmojiUpdate,
                onLongPress = onLongPress,
                modifier = Modifier.fillMaxWidth()
            )
        }
    )
}

/**
 * 🔧 新增：滑动背景内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SwipeBackgroundContent(
    dismissDirection: SwipeToDismissBoxValue,
    habit: HabitData
) {
    val backgroundColor = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Color(0xFFFF5252) // 删除 - 红色
        SwipeToDismissBoxValue.EndToStart -> Color(0xFFFF9800) // 归档 - 橙色
        else -> Color.Transparent
    }
    
    val icon = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Icons.Default.Delete
        SwipeToDismissBoxValue.EndToStart -> Icons.Default.Archive
        else -> Icons.Default.Delete
    }
    
    val text = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> "删除"
        SwipeToDismissBoxValue.EndToStart -> "归档"
        else -> ""
    }
    
    val alignment = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Alignment.CenterStart
        SwipeToDismissBoxValue.EndToStart -> Alignment.CenterEnd
        else -> Alignment.Center
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(horizontal = 24.dp),
        contentAlignment = alignment
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
            Text(
                text = text,
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp
            )
        }
    }
}

/**
 * 优化版习惯列表项 - 参考FastLog的简洁设计
 */
@Composable
fun OptimizedHabitListItem(
    habit: HabitData,
    onHabitClick: () -> Unit,
    onCheckClick: (LocalDate) -> Unit,
    onEmojiUpdate: (String, String) -> Unit,
    onLongPress: (HabitData) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val today = LocalDate.now()
    val isCompletedToday = habit.completedDates.contains(today)
    
    // 简化状态管理，移除复杂的撤销功能以提高性能
                                            
    // 计算进度
    val currentMonth = YearMonth.now()
    val daysInMonth = currentMonth.lengthOfMonth()
    val completedThisMonth = habit.completedDates.count { 
        it.year == today.year && it.monthValue == today.monthValue 
    }
    val progress = completedThisMonth.toFloat() / daysInMonth.toFloat()
    
    // 简化动画和触觉反馈以提高性能
    val hapticFeedback = LocalHapticFeedback.current
                                            
    // 习惯列表项卡片 - 简洁风格，无阴影
    Card(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onHabitClick() },
                    onLongPress = { 
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPress(habit)
                    }
                )
            },
        shape = RoundedCornerShape(12.dp), // 减小圆角
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp // 完全取消阴影
        )
    ) {
        Row(
                        modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp), // 减小内边距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：习惯图标
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(42.dp) // 减小图标容器尺寸
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                habit.color.copy(alpha = 0.2f),
                                habit.color.copy(alpha = 0.1f)
                            )
                        )
                    )
            ) {
                // 显示自定义emoji（如果有）或默认图标
                val displayEmoji = habit.customEmoji.takeIf { it.isNotEmpty() }
                if (displayEmoji != null) {
                    Text(
                        text = displayEmoji,
                        fontSize = 16.sp, // 缩小emoji尺寸：20sp -> 16sp
                        modifier = Modifier.padding(2.dp)
                    )
                } else {
                    Text(
                        text = habit.icon, // 现在是emoji字符串
                        fontSize = 16.sp // 缩小emoji尺寸：18sp -> 16sp
                        )
                    }
            }
            
            Spacer(modifier = Modifier.width(12.dp)) // 减小间距
            
            // 中间：习惯信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 习惯名称
                    Text(
                    text = habit.name,
                    fontSize = 14.sp, // 减小字体
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F1937)
                )
                
                // 描述（如果有）
                if (habit.description.isNotEmpty()) {
                    Text(
                        text = habit.description,
                        fontSize = 11.sp, // 减小字体
                        color = Color(0xFF6B7280),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(top = 1.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(6.dp)) // 减小间距
                
                // 进度信息 - 30天进度点横排显示
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 30天进度点网格显示 - 从左到右显示，最新的在左边
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(3.dp), // 增加间距
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(12.dp), // 固定高度
                        contentPadding = PaddingValues(horizontal = 2.dp) // 添加内边距
                    ) {
                        items(30) { index ->
                            // 🔧 修复：从左到右的顺序，最新的日期在左边，最早的在右边
                            val dayOffset = index.toLong()
                            val targetDate = today.minusDays(dayOffset)
                            val isCompleted = habit.completedDates.contains(targetDate)
                            
                            Box(
                                modifier = Modifier
                                    .size(8.dp) // 稍微增大进度点，提高可见性
                                    .clip(CircleShape)
                                    .background(
                                        if (isCompleted) 
                                            habit.color 
                                        else 
                                            Color(0xFFE5E7EB)
                                    )
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 完成统计
                    Text(
                        text = "${completedThisMonth}/${daysInMonth}",
                        fontSize = 11.sp, // 减小字体
                        color = Color(0xFF6B7280)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp)) // 减小间距
            
            // 右侧：打卡按钮和连击数
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 连击数显示 - 参考FastLog的数字徽章
                if (habit.streak > 0) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(24.dp) // 减小尺寸
                            .background(
                                color = habit.color.copy(alpha = 0.1f),
                                shape = CircleShape
                            )
                    ) {
                        Text(
                            text = habit.streak.toString(),
                            fontSize = 12.sp, // 减小字体
                            fontWeight = FontWeight.Bold,
                            color = habit.color
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                }
                
                // 打卡按钮
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .size(36.dp) // 减小按钮尺寸
                        .clip(CircleShape)
                        .background(
                            if (isCompletedToday) habit.color else Color(0xFFe9e8ea) // 使用指定的灰色
                        )
                        .clickable {
                            onCheckClick(today)
                        }
                ) {
                    androidx.compose.animation.AnimatedVisibility(
                        visible = isCompletedToday,
                        enter = scaleIn(animationSpec = tween(200)) + fadeIn(),
                        exit = scaleOut(animationSpec = tween(200)) + fadeOut()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已完成",
                            tint = Color.White,
                            modifier = Modifier.size(16.dp) // 减小图标尺寸
                    )
                }
                
                    androidx.compose.animation.AnimatedVisibility(
                        visible = !isCompletedToday,
                        enter = scaleIn(animationSpec = tween(200)) + fadeIn(),
                        exit = scaleOut(animationSpec = tween(200)) + fadeOut()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "打卡",
                            tint = Color(0xFF9CA3AF),
                            modifier = Modifier.size(16.dp) // 减小图标尺寸
                        )
                    }
                }
            }
        }
        
        // 移除撤销提示条以简化UI
    }
}

/**
 * 吉卜力风格欢迎条幅
 */
@Composable
fun WelcomeHabitBanner() {
    val today = LocalDate.now()
    val formatter = DateTimeFormatter.ofPattern("MM月dd日")
    val formattedDate = today.format(formatter)
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(130.dp)
            .padding(16.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                            Color(0xFFE8F5E9),  // 浅草绿
                            Color(0xFFDCEDC8)   // 更浅的草绿
                    )
                )
            )
    ) {
            // 吉卜力风格的装饰元素 - 简单绘制一些草地和小草
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
                // 草地小丘轮廓
                for (i in 0..5) {
                    val y = size.height - (30 + Random.nextInt(15)).dp.toPx()
                    val startX = size.width * (i / 6f)
                    val controlX1 = startX + 60.dp.toPx()
                    val controlY1 = y - 40.dp.toPx()
                    val controlX2 = startX + 120.dp.toPx()
                    val controlY2 = y - 30.dp.toPx()
                    val endX = startX + 180.dp.toPx()
                    
                    drawLine(
                        color = Color(0xFF81C784).copy(alpha = 0.3f),
                        start = Offset(startX, y),
                        end = Offset(endX, y),
                        strokeWidth = 2.dp.toPx()
                    )
                }
                
                // 小草和花朵
                for (i in 0..15) {
                val x = size.width * Random.nextFloat()
                    val y = size.height - (5 + Random.nextInt(20)).dp.toPx()
                    val grassHeight = (5 + Random.nextInt(8)).dp.toPx()
                    
                    // 小草
                    drawLine(
                        color = Color(0xFF7CB342).copy(alpha = 0.4f),
                        start = Offset(x, y),
                        end = Offset(x, y - grassHeight),
                        strokeWidth = 1.dp.toPx()
                    )
                    
                    // 随机加一些花朵
                    if (Random.nextFloat() > 0.7f) {
                drawCircle(
                            color = listOf(
                                Color(0xFFFFF9C4), // 浅黄色
                                Color(0xFFE1BEE7), // 浅紫色
                                Color(0xFFFFCCBC)  // 浅橙色
                            ).random().copy(alpha = 0.6f),
                            radius = 2.dp.toPx(),
                            center = Offset(x, y - grassHeight)
                        )
                    }
                }
                
                // 几朵柔和的云
                for (i in 0..2) {
                    val cloudX = size.width * (0.2f + i * 0.3f)
                    val cloudY = 10.dp.toPx() + Random.nextInt(15).dp.toPx()
                    val cloudRadius = (10 + Random.nextInt(8)).dp.toPx()
                    
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius,
                        center = Offset(cloudX, cloudY)
                    )
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius * 0.7f,
                        center = Offset(cloudX + cloudRadius * 0.8f, cloudY)
                    )
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = cloudRadius * 0.6f,
                        center = Offset(cloudX - cloudRadius * 0.7f, cloudY)
                )
            }
        }
        
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧文本
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "今天是 $formattedDate",
                        color = Color(0xFF5D4037), // 温暖的褐色，吉卜力常用
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "养成好习惯，每天都进步",
                        color = Color(0xFF33691E), // 深绿色
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                }
                
                // 右侧小图标 - 模拟吉卜力风格的小静物
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(80.dp)
            ) {
                    // 浅色圆形背景
                Box(
                    modifier = Modifier
                            .size(60.dp)
                        .clip(CircleShape)
                            .background(Color.White.copy(alpha = 0.7f))
                )
                
                    // 奖杯图标 - 使用吉卜力风格柔和的颜色
                Icon(
                    imageVector = Icons.Outlined.EmojiEvents,
                        contentDescription = "Habit Trophy",
                        tint = Color(0xFFFFB74D).copy(alpha = 0.8f), // 柔和的金色
                    modifier = Modifier.size(40.dp)
                )
                }
            }
        }
    }
}

/**
 * 优化版习惯统计卡片 - 参考FastLog简洁风格
 */
@Composable
fun HabitStatsCard(habits: List<HabitData>) {
    val today = LocalDate.now()
    
    // 计算相关统计数据
    val totalHabits = habits.size
    val completedToday = habits.count { habit ->
        habit.completedDates.contains(today)
    }
    val completionRate = if (totalHabits > 0) {
        (completedToday.toFloat() / totalHabits) * 100
    } else {
        0f
    }
    val longestStreak = habits.maxOfOrNull { it.streak } ?: 0
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp), // 进一步减小外边距
        shape = RoundedCornerShape(12.dp), // 减小圆角
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp // 完全取消阴影
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(10.dp) // 减小内边距
        ) {
            Text(
                text = "今日概览",
                fontSize = 14.sp, // 减小标题字体
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F1937)
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 减小间距
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 总习惯数
                StatItem(
                    value = totalHabits.toString(),
                    label = "总习惯",
                    icon = Icons.Outlined.AssignmentTurnedIn,
                    color = Color(0xFF64B5F6)
                )
                
                // 今日完成
                StatItem(
                    value = "$completedToday/$totalHabits",
                    label = "今日完成",
                    icon = Icons.Outlined.Done,
                    color = Color(0xFF81C784)
                )
                
                // 最长连续
                StatItem(
                    value = "$longestStreak",
                    label = "最长连续",
                    icon = Icons.Outlined.LocalFireDepartment,
                    color = Color(0xFFFFB74D)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp)) // 减小间距
            
            // 完成率进度条 - 简化设计
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "今日完成率",
                        fontSize = 11.sp, // 减小文字
                        color = Color(0xFF6B7280)
                    )
                    
                    Text(
                        text = "${completionRate.toInt()}%",
                        fontSize = 11.sp, // 减小文字
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1F1937)
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp)) // 减小间距
                
                LinearProgressIndicator(
                    progress = { completionRate / 100f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(4.dp) // 减小进度条高度
                        .clip(RoundedCornerShape(2.dp)), // 减小圆角
                    color = Color(0xFF81C784),
                    trackColor = Color(0xFFE5E7EB)
                )
            }
        }
    }
}

/**
 * 优化版统计项 - 减小尺寸和间距
 */
@Composable
fun StatItem(
    value: String,
    label: String,
    icon: ImageVector,
    color: Color,
    iconSize: Dp = 18.dp, // 减小图标尺寸
    boxSize: Dp = 40.dp, // 减小容器尺寸
    valueSize: TextUnit = 14.sp, // 减小数值字体
    labelSize: TextUnit = 10.sp // 减小标签字体
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(boxSize)
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            color.copy(alpha = 0.15f),
                            color.copy(alpha = 0.08f)
                        )
                    )
                )
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(iconSize)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))  // 减小间距
        
        Text(
            text = value,
            fontSize = valueSize,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F1937)
        )
        
        Text(
            text = label,
            fontSize = labelSize,
            color = Color(0xFF6B7280)
        )
    }
}

/**
 * 习惯列表项
 */
@Composable
fun HabitListItem(
    habit: HabitData,
    onHabitClick: () -> Unit,
    onCheckClick: (LocalDate) -> Unit,
    onEmojiUpdate: (String, String) -> Unit,
    modifier: Modifier = Modifier,
    onHabitLongPress: (HabitData) -> Unit = {} // 🔧 新增：长按编辑回调
) {
    val today = LocalDate.now()
    val isCompletedToday = habit.completedDates.contains(today)
    
    // Add haptic feedback
    val haptic = LocalHapticFeedback.current
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 🔧 新增：长按检测状态
    var isLongPressed by remember { mutableStateOf(false) }
    
    // 动画状态 - 使用多个动画状态提供丰富反馈
    val buttonScale = remember { Animatable(1f) }
    val starsAlpha = remember { Animatable(0f) }
    val checkmarkScale = remember { Animatable(0f) } // 勾选标记的缩放动画
    // 使用颜色状态而不是颜色整数值
    val defaultColor = Color(0xFFF0F0F0)
    val buttonColor = remember { mutableStateOf(if (isCompletedToday) habit.color else defaultColor) }
    var showStars by remember { mutableStateOf(false) }
    
    // 改进的动画效果
    LaunchedEffect(showStars) {
        if (showStars) {
            // 同时开始星星渐显
            starsAlpha.snapTo(1f)
            
            // 更自然的弹性弹跳动画
            buttonScale.animateTo(
                targetValue = 1.3f, // 更大幅度的缩放
                animationSpec = spring(
                    dampingRatio = 0.4f, // 更弹性的阻尼比
                    stiffness = 400f
                )
            )
            
            // 按钮颜色变化
            buttonColor.value = habit.color.copy(alpha = 0.7f)
            
            // 勾选标记显示动画
            checkmarkScale.animateTo(
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = 0.5f,
                    stiffness = 300f
                )
            )
            
            // 缩小回正常大小
            buttonScale.animateTo(
                targetValue = 1f,
                animationSpec = spring(
                    dampingRatio = 0.6f,
                    stiffness = 200f
                )
            )
            
            // 延迟一会儿再让星星淡出
            delay(400)
            
            // 星星慢慢淡出
            starsAlpha.animateTo(
                targetValue = 0f,
                animationSpec = tween(600)
            )
            
            // 动画完成后重置状态
            showStars = false
        }
    }
    
    // 更新状态监听
    LaunchedEffect(isCompletedToday) {
        // 根据完成状态设置勾选标记的初始缩放
        checkmarkScale.snapTo(if (isCompletedToday) 1f else 0f)
        
        // 根据完成状态设置背景颜色
        buttonColor.value = if (isCompletedToday) habit.color.copy(alpha = 0.7f) else defaultColor
    }
    
    // 添加对最近完成状态变化的监听
    val wasCompletedToday = remember { mutableStateOf(isCompletedToday) }

    // 当完成状态从false变为true时，触发庆祝动画
    LaunchedEffect(isCompletedToday) {
        if (isCompletedToday && !wasCompletedToday.value) {
            // 没有变化，不触发动画
        } else if (!isCompletedToday && wasCompletedToday.value) {
            // 取消完成，播放取消动画
            buttonScale.animateTo(
                targetValue = 0.9f,
                animationSpec = tween(100)
            )
            buttonScale.animateTo(
                targetValue = 1f,
                animationSpec = tween(100)
            )
            
            // 颜色恢复动画
            buttonColor.value = defaultColor
            
            // 勾选标记淡出
            checkmarkScale.animateTo(
                targetValue = 0f,
                animationSpec = tween(200)
            )
        }
        
        // 更新记忆状态
        wasCompletedToday.value = isCompletedToday
    }
    
    // 新增：控制emoji选择器对话框显示
    var showEmojiPicker by remember { mutableStateOf(false) }
    
    // 计算完成进度
    val daysInMonth = today.lengthOfMonth()
    val completedThisMonth = habit.completedDates.count { it.month == today.month && it.year == today.year }
    val progress = completedThisMonth.toFloat() / daysInMonth
    
    // 替换呼吸效果动画为静态效果
    val itemScale = if (isCompletedToday) 1.01f else 1f  // 使用固定缩放代替动画
    
    // 显示emoji选择器对话框
    if (showEmojiPicker) {
        EmojiPickerDialog(
            onDismiss = { showEmojiPicker = false },
            onEmojiSelected = { emoji ->
                onEmojiUpdate(habit.id, emoji)
                showEmojiPicker = false
            }
        )
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 4.dp)
            .graphicsLayer {
                // 完成时有轻微放大效果
                scaleX = itemScale
                scaleY = itemScale
            }
            .clickable { onHabitClick() }
            // 🔧 新增：长按手势检测
            .pointerInput(habit.id) {
                detectTapGestures(
                    onTap = { onHabitClick() },
                    onLongPress = { 
                        isLongPressed = true
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onHabitLongPress(habit)
                        // 重置长按状态
                        coroutineScope.launch {
                            delay(200)
                            isLongPressed = false
                        }
                    }
                )
            },
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isLongPressed) habit.color.copy(alpha = 0.1f) else Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isLongPressed) 4.dp else 0.dp)
    ) {
        Box {
            // 内容
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 表情状态系统 - 根据习惯类型和完成状态显示不同表情
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    habit.color.copy(alpha = 0.2f),
                                    habit.color.copy(alpha = 0.05f)
                                )
                            )
                        )
                        .padding(8.dp)
                        .clickable { showEmojiPicker = true } // 添加点击事件打开emoji选择器
                ) {
                    // 显示自定义emoji或者默认表情
                    val displayEmoji = if (habit.customEmoji.isNotEmpty()) {
                        habit.customEmoji
                    } else {
                        when {
                        isCompletedToday -> "🐈" // 猫咪举奖杯
                        habit.streak > 0 -> "🐇" // 兔子托腮等待
                        else -> "🐶" // 小狗捂脸哭
                        }
                    }
                    
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = displayEmoji,
                            fontSize = 24.sp,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Text(
                            text = habit.icon, // 现在是emoji字符串
                            fontSize = 16.sp, // 缩小emoji尺寸：20sp -> 16sp
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 习惯信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = habit.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF554A60)
                    )
                    
                    if (habit.description.isNotEmpty()) {
                        Text(
                            text = habit.description,
                            fontSize = 12.sp,
                            color = Color(0xFF8C8C8C),
                            maxLines = 1
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 进度图标 - 根据习惯类型显示不同图标
                    val progressIcon = when {
                        habit.name.contains("喝水") -> "💧"
                        habit.name.contains("阅读") -> "📖"
                        else -> "✨"
                    }
                    
                    // 完成进度条 - 毛绒效果
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = progressIcon,
                            fontSize = 16.sp,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        
                        Box(modifier = Modifier.weight(1f)) {
                            // 进度条底部
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(10.dp)
                                    .clip(RoundedCornerShape(5.dp))
                                    .background(Color(0xFFEEEEEE))
                            )
                            
                            // 进度条填充
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth(progress)
                                    .height(10.dp)
                                    .clip(RoundedCornerShape(5.dp))
                                    .background(
                                        brush = Brush.horizontalGradient(
                                            colors = listOf(
                                                habit.color.copy(alpha = 0.7f),
                                                habit.color
                                            )
                                        )
                                    )
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "${completedThisMonth}/$daysInMonth",
                            fontSize = 12.sp,
                            color = Color(0xFF8C8C8C)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 打卡按钮和连续天数
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 打卡按钮盒子 - 增加包装以显示星星和涟漪效果
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(48.dp)
                    ) {
                        // 涟漪效果 - 仅在动画激活时显示
                        if (showStars) {
                            Box(
                                modifier = Modifier
                                    .size(60.dp)
                                    .border(
                                        width = 2.dp,
                                        color = habit.color.copy(alpha = starsAlpha.value * 0.7f),
                                        shape = CircleShape
                                    )
                                    .graphicsLayer {
                                        scaleX = 1f + (1f - starsAlpha.value) * 0.8f
                                        scaleY = 1f + (1f - starsAlpha.value) * 0.8f
                                        alpha = (1f - abs(0.5f - starsAlpha.value) * 2f) * 0.7f
                                    }
                            )
                        }

                    // 打卡按钮 - 使用自定义可爱按钮
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(if (isCompletedToday) habit.color.copy(alpha = 0.7f) else buttonColor.value)
                            .clickable { 
                                // 根据当前状态切换打卡/撤销打卡
                                // isCompletedToday为true时撤销打卡，false时打卡
                                onCheckClick(today)
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                
                                // 仅标记状态变量，动画逻辑在LaunchedEffect中执行
                                if (!isCompletedToday) {
                                    showStars = true
                                }
                            }
                            .drawBehind {
                                // 添加细微阴影效果增强立体感
                                drawCircle(
                                    color = Color.Black.copy(alpha = 0.05f),
                                    radius = size.width/2 + 1.dp.toPx(),
                                    center = center.copy(y = center.y + 1.dp.toPx())
                                )
                            }
                            .graphicsLayer {
                                scaleX = buttonScale.value
                                scaleY = buttonScale.value
                                shadowElevation = 2f
                                spotShadowColor = Color.Black.copy(alpha = 0.1f)
                            }
                        ) {
                            // 使用图片中的样式：完成时显示勾选标记，未完成时显示图标
                            // 勾选标记 - 使用缩放动画
                        if (isCompletedToday) {
                                Icon(
                                    imageVector = Icons.Rounded.Check,
                                    contentDescription = "撤销打卡",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .graphicsLayer { 
                                            scaleX = checkmarkScale.value
                                            scaleY = checkmarkScale.value
                                            alpha = checkmarkScale.value
                                        }
                                )
                            }
                            
                            // 未完成时显示的图标
                            if (!isCompletedToday) {
                                // 未勾选状态的图标或文字 - 根据图片使用水滴或太阳图标
                                if (habit.name.contains("喝水")) {
                                    Icon(
                                        imageVector = Icons.Rounded.WaterDrop,
                                        contentDescription = "水滴",
                                        tint = Color(0xFF29B6F6), // 浅蓝色
                                        modifier = Modifier
                                            .size(20.dp)
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                } else if (habit.name.contains("早起")) {
                                    Icon(
                                        imageVector = Icons.Rounded.WbSunny,
                                        contentDescription = "太阳",
                                        tint = Color(0xFFFFA726), // 橙色
                                        modifier = Modifier
                                            .size(20.dp)
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                } else {
                            Text(
                                text = "✓",
                                        color = Color(0xFF9E9E9E), // 灰色
                                fontSize = 20.sp,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier
                                            .graphicsLayer { 
                                                alpha = 1f - checkmarkScale.value
                                            }
                                    )
                                }
                            }
                        }
                        
                        // 添加打卡状态提示文字
                        if (isCompletedToday) {
                            Box(
                                modifier = Modifier
                                    .offset(y = 48.dp)
                                    .width(70.dp)
                                    .alpha(0.7f),
                                contentAlignment = Alignment.Center
                            ) {
                            Text(
                                    text = "",
                                    color = habit.color,
                                    fontSize = 10.sp,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                        
                        // 小星星效果 - 在按钮周围添加
                        if (showStars) {
                            // 右上方星星
                            Text(
                                text = "✨",
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .offset(x = 24.dp, y = (-18).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                            
                            // 左上方星星
                            Text(
                                text = "✨",
                                fontSize = 12.sp,
                                modifier = Modifier
                                    .offset(x = (-18).dp, y = (-15).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                            
                            // 上方星星
                            Text(
                                text = "✨",
                                fontSize = 14.sp,
                                modifier = Modifier
                                    .offset(x = 5.dp, y = (-25).dp)
                                    .graphicsLayer { 
                                        alpha = starsAlpha.value
                                        scaleX = 1f + starsAlpha.value * 0.2f
                                        scaleY = 1f + starsAlpha.value * 0.2f
                                    }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 连续天数 - 火焰图标
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "🔥",
                            fontSize = 14.sp,
                            modifier = Modifier.padding(end = 2.dp)
                        )
                        
                        Text(
                            text = "${habit.streak}天",
                            fontSize = 12.sp,
                            color = Color(0xFFFF7043),
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加习惯底部弹窗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddHabitBottomSheet(
    onDismiss: () -> Unit,
    onSave: (HabitData) -> Unit,
    viewModel: HabitViewModel = androidx.lifecycle.viewmodel.compose.viewModel()
) {
    // 使用ViewModel中的表单状态
    val formState by viewModel.habitFormState.collectAsState()
    
    // 获取可用颜色和图标
    val availableColors by viewModel.availableColors.collectAsState()
    val availableIcons by viewModel.availableIcons.collectAsState()
    val habitTemplates by viewModel.habitTemplates.collectAsState()
    
    // 本地UI状态
    var habitName by remember { mutableStateOf(formState.name) }
    var habitDescription by remember { mutableStateOf(formState.description) }
    var selectedCategory by remember { mutableStateOf(formState.category) }
    var selectedIcon by remember { mutableStateOf(formState.icon) }
    var selectedColor by remember { mutableStateOf(formState.color) }
    
    // 频率选择状态
    var selectedFrequencyType by remember { mutableStateOf(formState.frequencyType) }
    var selectedDays by remember { mutableStateOf(formState.selectedDays) }
    
    // 额外设置状态
    var enableReminder by remember { mutableStateOf(formState.enableReminder) }
    var reminderTime by remember { mutableStateOf(formState.reminderTime ?: "08:00") } // 默认早上8点
    var showTimePicker by remember { mutableStateOf(false) } // 时间选择器显示状态
    var showInPlan by remember { mutableStateOf(formState.showInPlan) }
    var isQuickMode by remember { mutableStateOf(formState.isQuickMode) }
    var viewStyleIndex by remember { mutableStateOf(formState.viewStyleIndex) }
    var heatmapColorIndex by remember { mutableStateOf(formState.heatmapColorIndex) }
    
    // 颜色选择器状态
    var showColorPicker by remember { mutableStateOf(false) }
    
    // 图标选择器状态
    var showIconPicker by remember { mutableStateOf(false) }
    
    // 模板选择器状态
    var showTemplates by remember { mutableStateOf(false) }
    
    // 日期选择
    val startDate = remember { formState.startDate }
    
    // 分组选择
    var selectedGroup by remember { mutableStateOf("生活") }
    val habitGroups = remember { listOf("生活", "学习", "工作", "健康") }
    
    // 输入框聚焦请求器
    val focusRequester = remember { FocusRequester() }
    
    // 表单验证
    val formErrors = remember { mutableStateMapOf<String, String?>() }
    
    // 底部弹窗状态
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    
    // 更新ViewModel中的表单状态
    LaunchedEffect(habitName, habitDescription, selectedCategory, selectedIcon, 
        selectedColor, selectedFrequencyType, selectedDays, enableReminder, 
        reminderTime, showInPlan, isQuickMode) {
        viewModel.updateHabitFormField("name", habitName)
        viewModel.updateHabitFormField("description", habitDescription)
        viewModel.updateHabitFormField("category", selectedCategory)
        viewModel.updateHabitFormField("icon", selectedIcon)
        viewModel.updateHabitFormField("color", selectedColor)
        viewModel.updateHabitFormField("frequencyType", selectedFrequencyType)
        viewModel.updateHabitFormField("enableReminder", enableReminder)
        viewModel.updateHabitFormField("reminderTime", reminderTime)
        
        // 更新选中的星期几
        selectedDays.forEach { day ->
            viewModel.updateSelectedDays(day, true)
        }
    }
    
    // 时间选择器
    if (showTimePicker) {
        TimePickerDialog(
            onDismissRequest = { showTimePicker = false },
            onTimeSelected = { hour, minute ->
                // 格式化时间字符串 HH:MM
                reminderTime = String.format("%02d:%02d", hour, minute)
                showTimePicker = false
            },
            initialHour = reminderTime.split(":")[0].toInt(),
            initialMinute = reminderTime.split(":")[1].toInt()
        )
    }
    
    ModalBottomSheet(
        onDismissRequest = {
            viewModel.resetHabitForm()
            onDismiss()
        },
        sheetState = sheetState,
        dragHandle = { BottomSheetDefaults.DragHandle() },
        containerColor = Color.White,
        contentColor = Color(0xFF3C3C43),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 32.dp)
        ) {
            // 顶部标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                TextButton(onClick = { 
                    viewModel.resetHabitForm()
                    onDismiss() 
                }) {
                    Text(
                        text = "取消",
                        color = Color(0xFF3C3C43),
                        fontSize = 16.sp
                    )
                }
                
                Text(
                    text = "添加习惯",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF3C3C43)
                )
                
                TextButton(
                    onClick = {
                        val errors = viewModel.validateForm()
                        if (errors.isEmpty()) {
                            viewModel.createHabitFromForm()
                            onDismiss()
                        } else {
                            formErrors.clear()
                            formErrors.putAll(errors)
                        }
                    },
                    enabled = habitName.isNotEmpty()
                ) {
                    Text(
                        text = "保存",
                        color = if (habitName.isNotEmpty()) selectedColor else Color.Gray,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 模板选择器按钮
            if (!showTemplates) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = { showTemplates = true },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = selectedColor
                        )
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                imageVector = Icons.Outlined.List,
                                contentDescription = "习惯库",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("习惯库", fontSize = 14.sp)
                        }
                    }
                }
            }
            
            // 习惯模板选择器
            AnimatedVisibility(
                visible = showTemplates,
                enter = androidx.compose.animation.fadeIn(
                    initialAlpha = 0.0f,
                    animationSpec = tween(150)
                ),
                exit = androidx.compose.animation.fadeOut(
                    targetAlpha = 0.0f,
                    animationSpec = tween(150)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    // 标题和关闭按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "选择习惯模板",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        
                        IconButton(onClick = { showTemplates = false }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = Color.Gray
                            )
                        }
                    }
                    
                    // 模板列表
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        contentPadding = PaddingValues(vertical = 8.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        items(habitTemplates.size) { index ->
                            val template = habitTemplates[index]
                            TemplateItem(
                                template = template,
                                onClick = {
                                    // 应用模板到表单
                                    habitName = template.name
                                    habitDescription = template.description
                                    selectedCategory = template.category
                                    selectedIcon = template.icon
                                    selectedColor = template.color
                                    selectedFrequencyType = template.frequencyType
                                    selectedDays = template.days
                                    
                                    showTemplates = false
                                }
                            )
                        }
                    }
                    
                    Divider(modifier = Modifier.padding(vertical = 8.dp))
                }
            }
            
            // 内容区域 - 可滚动
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp)
            ) {
                Spacer(modifier = Modifier.height(8.dp))
                
                // 基本信息卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        // 习惯名称 - 红色必填标记
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .width(4.dp)
                                    .height(16.dp)
                                    .background(Color.Red)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Column {
                                OutlinedTextField(
                                    value = habitName,
                                    onValueChange = { habitName = it },
                                    placeholder = { Text("名称(必填)") },
                                    colors = TextFieldDefaults.outlinedTextFieldColors(
                                        focusedBorderColor = Color.Transparent,
                                        unfocusedBorderColor = Color.Transparent
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                                    isError = formErrors["name"] != null,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .focusRequester(focusRequester)
                                )
                                
                                // 错误提示
                                formErrors["name"]?.let { error ->
                                    Text(
                                        text = error,
                                        color = MaterialTheme.colorScheme.error,
                                        style = MaterialTheme.typography.bodySmall,
                                        modifier = Modifier.padding(start = 4.dp)
                                    )
                                }
                            }
                        }
                        
                        Divider(color = Color(0xFFEEEEEE))
                        
                        // 习惯描述
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Box(
                                modifier = Modifier
                                    .width(4.dp)
                                    .height(16.dp)
                                    .background(Color(0xFFCCCCCC))
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            OutlinedTextField(
                                value = habitDescription,
                                onValueChange = { habitDescription = it },
                                placeholder = { Text("习惯描述(可选)") },
                                colors = TextFieldDefaults.outlinedTextFieldColors(
                                    focusedBorderColor = Color.Transparent,
                                    unfocusedBorderColor = Color.Transparent
                                ),
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 颜色选择卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showColorPicker = !showColorPicker }
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "颜色",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43),
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 颜色选择器 - 显示当前选择的颜色
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape)
                                    .background(selectedColor)
                                    .border(1.dp, Color(0xFFEEEEEE), CircleShape)
                            )
                        }
                        
                        // 颜色选择器展开面板
                        AnimatedVisibility(
                            visible = showColorPicker,
                            enter = androidx.compose.animation.fadeIn(
                                initialAlpha = 0.0f,
                                animationSpec = tween(150)
                            ),
                            exit = androidx.compose.animation.fadeOut(
                                targetAlpha = 0.0f,
                                animationSpec = tween(150)
                            )
                        ) {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(10), // 进一步增加列数：8 -> 10，让颜色选择器更紧凑
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 12.dp), // 优化内边距
                                horizontalArrangement = Arrangement.spacedBy(8.dp), // 适当增加间距
                                verticalArrangement = Arrangement.spacedBy(8.dp), // 适当增加间距
                                modifier = Modifier
                                    .heightIn(max = 200.dp) // 进一步增大高度：160dp -> 200dp
                                    .fillMaxWidth()
                            ) {
                                items(availableColors) { color ->
                                    ColorItem(
                                        color = color,
                                        isSelected = selectedColor == color,
                                        onClick = {
                                            selectedColor = color
                                            showColorPicker = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图标选择卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showIconPicker = !showIconPicker }
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "图标",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43),
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 当前选择的图标
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(selectedColor.copy(alpha = 0.1f))
                                    .border(1.dp, selectedColor.copy(alpha = 0.3f), RoundedCornerShape(8.dp))
                            ) {
                                Text(
                                    text = selectedIcon, // 现在是emoji字符串
                                    fontSize = 16.sp, // 缩小当前选中图标的大小：20sp -> 16sp
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                        
                        // 图标选择器展开面板
                        AnimatedVisibility(
                            visible = showIconPicker,
                            enter = androidx.compose.animation.fadeIn(
                                initialAlpha = 0.0f,
                                animationSpec = tween(150)
                            ),
                            exit = androidx.compose.animation.fadeOut(
                                targetAlpha = 0.0f,
                                animationSpec = tween(150)
                            )
                        ) {
                            LazyVerticalGrid(
                                columns = GridCells.Fixed(8), // 进一步增加列数：6 -> 8，让图标显示更多
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 12.dp), // 优化内边距
                                horizontalArrangement = Arrangement.spacedBy(8.dp), // 适当增加间距
                                verticalArrangement = Arrangement.spacedBy(8.dp), // 适当增加间距
                                modifier = Modifier
                                    .heightIn(max = 320.dp) // 进一步增大高度：280dp -> 320dp
                                    .fillMaxWidth()
                            ) {
                                items(availableIcons) { iconPair ->
                                    val (icon, description) = iconPair
                                    IconItem(
                                        icon = icon,
                                        description = description,
                                        isSelected = selectedIcon == icon,
                                        color = selectedColor,
                                        onClick = {
                                            selectedIcon = icon
                                            showIconPicker = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 开启提醒卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            Text(
                                text = "开启提醒",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43),
                                modifier = Modifier.weight(1f)
                            )
                            
                            Switch(
                                checked = enableReminder,
                                onCheckedChange = { enableReminder = it },
                                colors = SwitchDefaults.colors(
                                    checkedThumbColor = Color.White,
                                    checkedTrackColor = selectedColor,
                                    uncheckedThumbColor = Color.White,
                                    uncheckedTrackColor = Color(0xFFE0E0E0)
                                )
                            )
                        }
                        
                        // 提醒时间选择器 - 仅在开启提醒时显示
                        AnimatedVisibility(
                            visible = enableReminder,
                            enter = expandVertically() + fadeIn(),
                            exit = shrinkVertically() + fadeOut()
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable { showTimePicker = true }
                                    .padding(horizontal = 16.dp, vertical = 8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Outlined.Alarm,
                                    contentDescription = "提醒时间",
                                    tint = Color(0xFF3C3C43),
                                    modifier = Modifier.size(24.dp)
                                )
                                
                                Spacer(modifier = Modifier.width(12.dp))
                                
                                Text(
                                    text = "每天 $reminderTime 提醒",
                                    fontSize = 16.sp,
                                    color = Color(0xFF3C3C43)
                                )
                                
                                Spacer(modifier = Modifier.weight(1f))
                                
                                Icon(
                                    imageVector = Icons.Outlined.KeyboardArrowRight,
                                    contentDescription = "选择时间",
                                    tint = Color(0xFFAAAAAA),
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                            
                            Divider(
                                color = Color(0xFFEEEEEE),
                                modifier = Modifier.padding(start = 16.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 快速打卡模式
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "快速打卡模式",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43),
                            modifier = Modifier.weight(1f)
                        )
                        
                        Switch(
                            checked = isQuickMode,
                            onCheckedChange = { isQuickMode = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedTrackColor = selectedColor,
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color(0xFFE0E0E0)
                            )
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 展示风格卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "展示风格",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 视图切换按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            ViewStyleButton(
                                text = "日视图",
                                isSelected = viewStyleIndex == 0,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 0 }
                            )
                            
                            ViewStyleButton(
                                text = "周视图",
                                isSelected = viewStyleIndex == 1,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 1 }
                            )
                            
                            ViewStyleButton(
                                text = "月视图",
                                isSelected = viewStyleIndex == 2,
                                color = selectedColor,
                                onClick = { viewStyleIndex = 2 }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 热力图颜色卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = "热力图颜色",
                                fontSize = 16.sp,
                                color = Color(0xFF3C3C43)
                            )
                            
                            // 帮助按钮
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .size(24.dp)
                                    .clip(CircleShape)
                                    .border(1.dp, Color(0xFFE0E0E0), CircleShape)
                            ) {
                                Text(
                                    text = "?",
                                    fontSize = 14.sp,
                                    color = Color(0xFF3C3C43)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 热力图颜色选择按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            ViewStyleButton(
                                text = "渐变色",
                                isSelected = heatmapColorIndex == 0,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 0 }
                            )
                            
                            ViewStyleButton(
                                text = "单一色",
                                isSelected = heatmapColorIndex == 1,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 1 }
                            )
                            
                            ViewStyleButton(
                                text = "标签色",
                                isSelected = heatmapColorIndex == 2,
                                color = selectedColor,
                                onClick = { heatmapColorIndex = 2 }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分组卡片
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "分组",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 分组标签按钮
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            habitGroups.forEach { group ->
                                GroupButton(
                                    text = group,
                                    isSelected = selectedGroup == group,
                                    color = if (group == "生活") Color(0xFF6c5ce7)
                                        else if (group == "学习") Color(0xFF0984e3)
                                        else if (group == "工作") Color(0xFFfdcb6e)
                                        else Color(0xFF00b894),
                                    onClick = { selectedGroup = group }
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 想在一天什么时候完成它
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "想在一天什么时候完成它",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 时间选择按钮组
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            TimeOfDayButton(
                                text = "全天",
                                isSelected = true,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "上午",
                                isSelected = false,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "下午",
                                isSelected = false,
                                color = selectedColor
                            )
                            
                            TimeOfDayButton(
                                text = "晚上",
                                isSelected = false,
                                color = selectedColor
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 选择频率
                Card(
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "选择打卡频率",
                            fontSize = 16.sp,
                            color = Color(0xFF3C3C43)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 频率类型按钮
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .horizontalScroll(rememberScrollState())
                        ) {
                            FrequencyTypeButton(
                                icon = Icons.Outlined.Schedule,
                                text = "固定",
                                isSelected = selectedFrequencyType == HabitFrequencyType.DAILY,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = HabitFrequencyType.DAILY }
                            )
                            
                            FrequencyTypeButton(
                                icon = Icons.Outlined.DateRange,
                                text = "每周",
                                isSelected = selectedFrequencyType == HabitFrequencyType.WEEKLY,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = HabitFrequencyType.WEEKLY }
                            )
                            
                            FrequencyTypeButton(
                                icon = Icons.Outlined.CalendarViewMonth,
                                text = "自定义",
                                isSelected = selectedFrequencyType == HabitFrequencyType.CUSTOM,
                                color = selectedColor,
                                onClick = { selectedFrequencyType = HabitFrequencyType.CUSTOM }
                            )
                        }
                        
                        // 如果选择每周，显示星期选择器
                        if (selectedFrequencyType == HabitFrequencyType.WEEKLY) {
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // 星期选择器
                            Row(
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // 周一到周日
                                listOf(
                                    DayOfWeek.MONDAY to "一",
                                    DayOfWeek.TUESDAY to "二",
                                    DayOfWeek.WEDNESDAY to "三",
                                    DayOfWeek.THURSDAY to "四",
                                    DayOfWeek.FRIDAY to "五",
                                    DayOfWeek.SATURDAY to "六",
                                    DayOfWeek.SUNDAY to "日"
                                ).forEach { (day, label) ->
                                    val isSelected = selectedDays.contains(day)
                                    
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier
                                            .size(36.dp)
                                            .clip(CircleShape)
                                            .background(
                                                if (isSelected) selectedColor
                                                else Color(0xFFF0F0F0)
                                            )
                                            .clickable {
                                                selectedDays = if (isSelected) {
                                                    // 至少要选择一天
                                                    if (selectedDays.size > 1)
                                                        selectedDays - day
                                                    else selectedDays
                                                } else {
                                                    selectedDays + day
                                                }
                                            }
                                    ) {
                                        Text(
                                            text = label,
                                            color = if (isSelected) Color.White
                                                  else Color(0xFF3C3C43),
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

/**
 * 频率类型按钮
 */
@Composable
fun FrequencyTypeButton(
    icon: ImageVector,
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = if (isSelected) color else Color.Gray,
                modifier = Modifier.size(16.dp)
            )
            
            Text(
                text = text,
                fontSize = 14.sp,
                color = if (isSelected) color else Color.Gray
            )
        }
    }
}

/**
 * 视图风格按钮
 */
@Composable
fun ViewStyleButton(
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 20.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) color else Color.Gray
        )
    }
}

/**
 * 分组按钮
 */
@Composable
fun GroupButton(
    text: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(if (isSelected) color else Color(0xFFF0F0F0))
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) Color.White else Color.Gray
        )
    }
}

/**
 * 时间段按钮
 */
@Composable
fun TimeOfDayButton(
    text: String,
    isSelected: Boolean,
    color: Color
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF0F0F0))
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
            .padding(horizontal = 24.dp, vertical = 8.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            color = if (isSelected) color else Color.Gray
        )
    }
}

/**
 * Emoji选择对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmojiPickerDialog(
    onDismiss: () -> Unit,
    onEmojiSelected: (String) -> Unit
) {
    var emojiText by remember { mutableStateOf("") }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "自定义图标",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF554A60)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "输入你喜欢的emoji表情:",
                    fontSize = 14.sp,
                    color = Color(0xFF8C8C8C)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Emoji输入框
                OutlinedTextField(
                    value = emojiText,
                    onValueChange = { newValue ->
                        // 限制只输入一个emoji
                        if (newValue.isEmpty() || newValue.length <= 2) {
                            emojiText = newValue
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    textStyle = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 40.sp,
                        textAlign = TextAlign.Center
                    ),
                    placeholder = {
                        Text(
                            text = "😊",
                            fontSize = 40.sp,
                            textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                        )
                    },
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 常用emoji建议
                Text(
                    text = "推荐表情",
                    fontSize = 14.sp,
                    color = Color(0xFF8C8C8C),
                    modifier = Modifier.align(Alignment.Start)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 常用emoji网格
                val commonEmojis = listOf("😊", "🌱", "🚀", "💪", "📚", "💧", "🍎", "🏃", "🧘", "🎯", "⭐", "🌈")
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(commonEmojis) { emoji ->
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .size(48.dp)
                                .clip(RoundedCornerShape(12.dp))
                                .background(Color(0xFFF5F5F5))
                                .clickable { emojiText = emoji }
                        ) {
                            Text(
                                text = emoji,
                                fontSize = 24.sp
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = onDismiss
                    ) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 清除按钮
                    TextButton(
                        onClick = {
                            emojiText = ""
                            onEmojiSelected("")
                            onDismiss()
                        }
                    ) {
                        Text("清除")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            onEmojiSelected(emojiText)
                            onDismiss()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF6B46C1) // 使用统一的主题色
                        )
                    ) {
                        Text("确认")
                    }
                }
            }
        }
    }
}

/**
 * 模板项
 */
@Composable
fun TemplateItem(
    template: HabitTemplate,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(100.dp)
            .clip(RoundedCornerShape(12.dp))
            .clickable(onClick = onClick)
            .border(
                width = 1.dp,
                color = template.color.copy(alpha = 0.3f),
                shape = RoundedCornerShape(12.dp)
            )
            .background(template.color.copy(alpha = 0.05f))
            .padding(12.dp)
    ) {
        // 图标
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(template.color.copy(alpha = 0.1f))
                .padding(8.dp)
        ) {
            Text(
                text = template.icon, // 现在是emoji字符串
                fontSize = 24.sp,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 名称
            Text(
            text = template.name,
                fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF3C3C43),
            textAlign = TextAlign.Center
        )
        
        // 频率标签
        val frequencyText = when(val freq = template.frequencyType) {
            HabitFrequencyType.DAILY -> "每天"
            HabitFrequencyType.WEEKLY -> {
                val days = template.days.size
                if (days == 7) "每天" else "每周${days}天"
            }
            HabitFrequencyType.CUSTOM -> "自定义"
            else -> "自定义" // 添加else分支处理未知类型
        }
        
        Text(
            text = frequencyText,
            fontSize = 12.sp,
            color = Color(0xFF8C8C8C),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 颜色选择项
 */
@Composable
fun ColorItem(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(color)
            .border(
                width = 2.dp,
                color = if (isSelected) Color.White else Color.Transparent,
                shape = CircleShape
            )
            .padding(4.dp)
            .clickable(onClick = onClick)
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "已选择",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 图标选择项
 */
@Composable
fun IconItem(
    icon: String, // 改为String类型存储emoji
    description: String,
    isSelected: Boolean,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(if (isSelected) color.copy(alpha = 0.2f) else Color(0xFFF5F5F5))
            .border(
                width = if (isSelected) 1.dp else 0.dp,
                color = if (isSelected) color else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(onClick = onClick)
            .padding(8.dp)
    ) {
        Text(
            text = icon, // 现在是emoji字符串
            fontSize = 18.sp, // 缩小图标选择器中的emoji尺寸：24sp -> 18sp
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * 时间选择器对话框
 */
@Composable
fun TimePickerDialog(
    onDismissRequest: () -> Unit,
    onTimeSelected: (hour: Int, minute: Int) -> Unit,
    initialHour: Int = 8,
    initialMinute: Int = 0
) {
    var hour by remember { mutableStateOf(initialHour) }
    var minute by remember { mutableStateOf(initialMinute) }
    
    Dialog(onDismissRequest = onDismissRequest) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "选择提醒时间",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 时间选择器
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 小时选择器
                    NumberPicker(
                        value = hour,
                        onValueChange = { hour = it },
                        range = 0..23,
                        modifier = Modifier.weight(1f)
                    )
                    
                    Text(
                        text = ":",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )
                    
                    // 分钟选择器
                    NumberPicker(
                        value = minute,
                        onValueChange = { minute = it },
                        range = 0..59,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismissRequest) {
                        Text("取消")
                    }
                    
                    TextButton(
                        onClick = { onTimeSelected(hour, minute) },
                        colors = ButtonDefaults.textButtonColors(contentColor = MaterialTheme.colorScheme.primary)
                    ) {
                        Text("确定")
                    }
                }
            }
        }
    }
}

/**
 * 数字选择器组件
 */
@Composable
fun NumberPicker(
    value: Int,
    onValueChange: (Int) -> Unit,
    range: IntRange,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .padding(horizontal = 8.dp)
    ) {
        IconButton(
            onClick = {
                if (value < range.last) onValueChange(value + 1) 
                else onValueChange(range.first)
            }
        ) {
            Icon(
                imageVector = Icons.Filled.KeyboardArrowUp,
                contentDescription = "增加",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        Text(
            text = String.format("%02d", value),
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(vertical = 8.dp)
        )
        
        IconButton(
            onClick = {
                if (value > range.first) onValueChange(value - 1) 
                else onValueChange(range.last)
            }
        ) {
            Icon(
                imageVector = Icons.Filled.KeyboardArrowDown,
                contentDescription = "减少",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
} 

/**
 * 习惯详情底部弹窗
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitDetailDialog(
    habit: HabitData,
    onDismiss: () -> Unit,
    onMarkComplete: (LocalDate) -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = false
    )
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState,
        windowInsets = WindowInsets(0),
        dragHandle = {
            // 带颜色的顶部装饰区域
            Column {
                // 顶部颜色条
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(4.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    habit.color.copy(alpha = 0.6f),
                                    habit.color.copy(alpha = 0.3f),
                                    habit.color.copy(alpha = 0.6f)
                                )
                            )
                        )
                )
                
                // 精致的拖拽手柄
                Surface(
                    modifier = Modifier
                        .padding(vertical = 8.dp)
                        .align(Alignment.CenterHorizontally),
                    color = habit.color.copy(alpha = 0.2f), // 使用习惯颜色
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Box(
                        modifier = Modifier.size(width = 32.dp, height = 3.dp)
                    )
                }
            }
        },
        containerColor = Color.White, // 统一使用白色背景
        contentColor = Color.Black
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 24.dp) // 减小底部间距
        ) {
            // 头部区域 - 更精致的设计
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                habit.color.copy(alpha = 0.06f),
                                Color.Transparent
                            )
                        )
                    )
                    .padding(horizontal = 16.dp, vertical = 12.dp) // 减小内边距
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 更精致的图标容器
                    Surface(
                        modifier = Modifier.size(48.dp), // 减小尺寸
                        color = habit.color.copy(alpha = 0.12f),
                        shape = CircleShape
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            Text(
                                text = habit.icon, // 现在是emoji字符串
                                fontSize = 18.sp, // 缩小emoji尺寸：24sp -> 18sp
                                modifier = Modifier.size(24.dp) // 减小图标尺寸
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(12.dp)) // 减小间距
                    
                    Column(horizontalAlignment = Alignment.Start) {
                        Text(
                            text = habit.name,
                            fontSize = 18.sp, // 减小字体
                            fontWeight = FontWeight.SemiBold, // 调整字重
                            color = Color(0xFF1F1937),
                            letterSpacing = 0.2.sp
                        )
                        Spacer(modifier = Modifier.height(2.dp)) // 减小间距
                        Text(
                            text = habit.category.title,
                            fontSize = 12.sp, // 减小字体
                            color = Color(0xFF6B7280),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            // 统计数据区域 - 精致设计
            Column(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp) // 减小内边距
            ) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFF6F7F9), // 更柔和的背景色
                    shape = RoundedCornerShape(12.dp) // 减小圆角
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp), // 减小内边距
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        StatCard(
                            title = "当前连击",
                            value = "${habit.streak}",
                            subtitle = "天",
                            color = habit.color
                        )
                        
                        StatCard(
                            title = "总完成",
                            value = "${habit.completedDates.size}",
                            subtitle = "次",
                            color = habit.color
                        )
                        
                        StatCard(
                            title = "完成率",
                            value = "${(habit.completedDates.size * 100 / maxOf(1, java.time.temporal.ChronoUnit.DAYS.between(habit.createdAt, LocalDate.now()).toInt() + 1))}%",
                            subtitle = "",
                            color = habit.color
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp)) // 减小间距
                
                // 时间周期统计选项卡 - 精致设计
                var selectedPeriod by remember { mutableStateOf("周") }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(6.dp) // 减小间距
                ) {
                    listOf("周", "月", "年").forEach { period ->
                        val isSelected = selectedPeriod == period
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { selectedPeriod = period },
                            color = if (isSelected) habit.color.copy(alpha = 0.08f) else Color(0xFFF1F3F4), // 更柔和的背景
                            shape = RoundedCornerShape(10.dp), // 减小圆角
                            border = if (isSelected) BorderStroke(1.dp, habit.color.copy(alpha = 0.25f)) else null
                        ) {
                            Text(
                                text = period,
                                modifier = Modifier.padding(vertical = 6.dp), // 减小内边距
                                textAlign = TextAlign.Center,
                                fontSize = 13.sp, // 减小字体
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) habit.color else Color(0xFF6B7280),
                                letterSpacing = 0.1.sp
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(10.dp)) // 减小间距
                
                // 根据选择的周期显示不同的统计信息
                when (selectedPeriod) {
                    "周" -> WeeklyHeatmap(habit = habit, onMarkComplete = onMarkComplete)
                    "月" -> MonthlyHeatmap(habit = habit, onMarkComplete = onMarkComplete)
                    "年" -> YearlyHeatmap(habit = habit, onMarkComplete = onMarkComplete)
                }
            }

            // 底部操作区域 - 精致设计
            Spacer(modifier = Modifier.height(12.dp)) // 减小间距
            
            val today = LocalDate.now()
            val isTodayCompleted = habit.completedDates.contains(today)
            
            Button(
                onClick = { onMarkComplete(today) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp) // 减小水平边距
                    .height(44.dp), // 减小高度
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isTodayCompleted) Color(0xFF10B981) else habit.color
                ),
                shape = RoundedCornerShape(12.dp) // 减小圆角
            ) {
                Icon(
                    imageVector = if (isTodayCompleted) Icons.Default.CheckCircle else Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp) // 减小图标尺寸
                )
                Spacer(modifier = Modifier.width(8.dp)) // 减小间距
                Text(
                    text = if (isTodayCompleted) "今日已完成" else "今日打卡",
                    fontSize = 14.sp, // 减小字体
                    fontWeight = FontWeight.SemiBold,
                    letterSpacing = 0.2.sp
                )
            }
        }
    }
}

/**
 * 统计卡片组件
 */
@Composable
private fun StatCard(
    title: String,
    value: String,
    subtitle: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            fontSize = 10.sp, // 减小字体
            color = Color(0xFF6B7280),
            fontWeight = FontWeight.Medium,
            letterSpacing = 0.1.sp
        )
        Spacer(modifier = Modifier.height(2.dp)) // 减小间距
        Row(
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                text = value,
                fontSize = 18.sp, // 减小字体
                fontWeight = FontWeight.Bold,
                color = color,
                letterSpacing = 0.3.sp
            )
            if (subtitle.isNotEmpty()) {
                Text(
                    text = subtitle,
                    fontSize = 9.sp, // 减小字体
                    color = Color(0xFF9CA3AF),
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.padding(start = 1.dp, bottom = 1.dp) // 减小间距
                )
            }
        }
    }
}

/**
 * 周热力图组件
 */
@Composable
private fun WeeklyHeatmap(
    habit: HabitData,
    onMarkComplete: (LocalDate) -> Unit
) {
    Column {
        Text(
            text = "本周完成情况",
            fontSize = 13.sp, // 减小字体
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F1937),
            letterSpacing = 0.2.sp,
            modifier = Modifier.padding(bottom = 6.dp) // 减小间距
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(6.dp) // 减小间距
        ) {
            items(7) { index ->
                // 🔧 修复：从左到右显示，最新的日期在左边
                val date = LocalDate.now().minusDays(index.toLong())
                val isCompleted = habit.completedDates.contains(date)
                val isToday = date == LocalDate.now()
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .clickable { onMarkComplete(date) }
                        .padding(3.dp) // 减小内边距
                ) {
                    Surface(
                        modifier = Modifier.size(36.dp), // 减小尺寸
                        color = when {
                            isCompleted -> habit.color
                            isToday -> habit.color.copy(alpha = 0.3f)
                            else -> Color(0xFFF3F4F6)
                        },
                        shape = CircleShape,
                        border = if (isToday && !isCompleted) {
                            BorderStroke(1.5.dp, habit.color) // 减小边框宽度
                        } else null
                    ) {
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            if (isCompleted) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.size(16.dp) // 减小图标尺寸
                                )
                            } else {
                                Text(
                                    text = date.dayOfMonth.toString(),
                                    fontSize = 11.sp, // 减小字体
                                    color = if (isToday) habit.color else Color(0xFF9CA3AF),
                                    fontWeight = if (isToday) FontWeight.Bold else FontWeight.Medium
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(3.dp)) // 减小间距
                    
                    Text(
                        text = when (date.dayOfWeek.value) {
                            1 -> "一"
                            2 -> "二"
                            3 -> "三"
                            4 -> "四"
                            5 -> "五"
                            6 -> "六"
                            7 -> "日"
                            else -> ""
                        },
                        fontSize = 9.sp, // 减小字体
                        color = Color(0xFF9CA3AF),
                        fontWeight = FontWeight.Normal
                    )
                }
            }
        }
    }
}

/**
 * 月热力图组件
 */
@Composable
private fun MonthlyHeatmap(
    habit: HabitData,
    onMarkComplete: (LocalDate) -> Unit
) {
    val today = LocalDate.now()
    val currentMonth = YearMonth.now()
    val daysInMonth = currentMonth.lengthOfMonth()
    val firstDayOfMonth = currentMonth.atDay(1)
    val startDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 0=周日, 1=周一...
    
    Column {
        Text(
            text = "${currentMonth.month.getDisplayName(TextStyle.FULL, Locale.CHINESE)} ${currentMonth.year}",
            fontSize = 13.sp, // 减小字体
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F1937),
            letterSpacing = 0.2.sp,
            modifier = Modifier.padding(bottom = 6.dp) // 减小间距
        )
        
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                Text(
                    text = day,
                    fontSize = 9.sp, // 减小字体
                    color = Color(0xFF9CA3AF),
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp)) // 减小间距
        
        // 日期网格
        val totalCells = ((daysInMonth + startDayOfWeek - 1) / 7 + 1) * 7
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            horizontalArrangement = Arrangement.spacedBy(3.dp), // 减小间距
            verticalArrangement = Arrangement.spacedBy(3.dp) // 减小间距
        ) {
            items(totalCells) { index ->
                val dayNumber = index - startDayOfWeek + 1
                if (dayNumber in 1..daysInMonth) {
                    val date = currentMonth.atDay(dayNumber)
                    val isCompleted = habit.completedDates.contains(date)
                    val isToday = date == today
                    val isFuture = date.isAfter(today)
                    
                    Surface(
                        modifier = Modifier
                            .size(28.dp) // 减小尺寸
                            .clickable(enabled = !isFuture) { 
                                if (!isFuture) onMarkComplete(date) 
                            },
                        color = when {
                            isCompleted -> habit.color
                            isToday -> habit.color.copy(alpha = 0.3f)
                            isFuture -> Color(0xFFF9FAFB)
                            else -> Color(0xFFF3F4F6)
                        },
                        shape = RoundedCornerShape(5.dp), // 减小圆角
                        border = if (isToday && !isCompleted) {
                            BorderStroke(1.dp, habit.color)
                        } else null
                    ) {
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            if (isCompleted) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.size(12.dp) // 减小图标尺寸
                                )
                            } else {
                                Text(
                                    text = dayNumber.toString(),
                                    fontSize = 10.sp, // 减小字体
                                    color = when {
                                        isToday -> habit.color
                                        isFuture -> Color(0xFFD1D5DB)
                                        else -> Color(0xFF6B7280)
                                    },
                                    fontWeight = if (isToday) FontWeight.Bold else FontWeight.Medium
                                )
                            }
                        }
                    }
                } else {
                    // 空白占位
                    Box(modifier = Modifier.size(32.dp))
                }
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp)) // 减小间距
        
        // 月度统计
        val completedThisMonth = habit.completedDates.count { 
            it.year == today.year && it.monthValue == today.monthValue 
        }
        val completionRate = (completedThisMonth * 100 / daysInMonth)
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "本月完成: $completedThisMonth/$daysInMonth",
                fontSize = 11.sp, // 减小字体
                color = Color(0xFF6B7280),
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "完成率: $completionRate%",
                fontSize = 11.sp, // 减小字体
                color = habit.color,
                fontWeight = FontWeight.SemiBold
            )
        }
    }
}

/**
 * 年热力图组件
 */
@Composable
private fun YearlyHeatmap(
    habit: HabitData,
    onMarkComplete: (LocalDate) -> Unit
) {
    val today = LocalDate.now()
    val currentYear = today.year
    
    Column {
        Text(
            text = "${currentYear}年完成情况",
            fontSize = 13.sp, // 减小字体
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF1F1937),
            letterSpacing = 0.2.sp,
            modifier = Modifier.padding(bottom = 6.dp) // 减小间距
        )
        
        // 月份网格 (3x4)
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            horizontalArrangement = Arrangement.spacedBy(6.dp), // 减小间距
            verticalArrangement = Arrangement.spacedBy(6.dp) // 减小间距
        ) {
            items(12) { monthIndex ->
                val month = YearMonth.of(currentYear, monthIndex + 1)
                val daysInMonth = month.lengthOfMonth()
                val completedInMonth = habit.completedDates.count { 
                    it.year == currentYear && it.monthValue == monthIndex + 1 
                }
                val completionRate = if (daysInMonth > 0) completedInMonth.toFloat() / daysInMonth else 0f
                val isCurrentMonth = month == YearMonth.now()
                
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f)
                        .clickable { /* 可以添加月份详情 */ },
                    color = habit.color.copy(alpha = completionRate * 0.8f + 0.1f),
                    shape = RoundedCornerShape(12.dp),
                    border = if (isCurrentMonth) {
                        BorderStroke(2.dp, habit.color)
                    } else null
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(6.dp), // 减小内边距
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "${monthIndex + 1}月",
                            fontSize = 11.sp, // 减小字体
                            fontWeight = FontWeight.SemiBold,
                            color = if (completionRate > 0.5f) Color.White else habit.color,
                            letterSpacing = 0.1.sp
                        )
                        Spacer(modifier = Modifier.height(2.dp)) // 减小间距
                        Text(
                            text = "$completedInMonth/$daysInMonth",
                            fontSize = 9.sp, // 减小字体
                            color = if (completionRate > 0.5f) Color.White.copy(alpha = 0.9f) else habit.color.copy(alpha = 0.8f),
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "${(completionRate * 100).toInt()}%",
                            fontSize = 9.sp, // 减小字体
                            fontWeight = FontWeight.Bold,
                            color = if (completionRate > 0.5f) Color.White else habit.color
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp)) // 减小间距
        
        // 年度统计
        val completedThisYear = habit.completedDates.count { it.year == currentYear }
        val daysPassedThisYear = today.dayOfYear
        val yearCompletionRate = if (daysPassedThisYear > 0) (completedThisYear * 100 / daysPassedThisYear) else 0
        
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color(0xFFF6F7F9), // 更柔和的背景色
            shape = RoundedCornerShape(10.dp) // 减小圆角
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp), // 减小内边距
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = completedThisYear.toString(),
                        fontSize = 16.sp, // 减小字体
                        fontWeight = FontWeight.Bold,
                        color = habit.color,
                        letterSpacing = 0.2.sp
                    )
                    Text(
                        text = "总完成天数",
                        fontSize = 9.sp, // 减小字体
                        color = Color(0xFF6B7280),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = "$yearCompletionRate%",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = habit.color
                    )
                    Text(
                        text = "年度完成率",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color(0xFF6B7280)
                    )
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = habit.streak.toString(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = habit.color
                    )
                    Text(
                        text = "当前连击",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color(0xFF6B7280)
                    )
                }
            }
        }
    }
} 

// 在HabitCard或相关组件中添加目标关联显示
@Composable
fun HabitGoalAssociation(
    relatedGoalTitle: String?,
    modifier: Modifier = Modifier
) {
    if (relatedGoalTitle != null) {
        Surface(
            modifier = modifier,
            color = Color(0xFF6A45C9).copy(alpha = 0.1f),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Flag,
                    contentDescription = null,
                    tint = Color(0xFF6A45C9),
                    modifier = Modifier.size(12.dp)
                )
                Text(
                    text = relatedGoalTitle,
                    style = MaterialTheme.typography.labelSmall,
                    color = Color(0xFF6A45C9),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 分类管理对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CategoryManageDialog(
    categories: List<CustomHabitCategory>,
    onDismiss: () -> Unit,
    onAddCategory: () -> Unit,
    onDeleteCategory: (String) -> Unit,
    onEditCategory: (String, String, String, Color) -> Unit // 改为String类型
) {
    var editingCategory: CustomHabitCategory? by remember { mutableStateOf(null) }
    var showDeleteConfirm: String? by remember { mutableStateOf(null) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(0.dp)
            ) {
                // 头部
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFF8F9FA),
                    shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "管理分类",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF1F1937)
                        )
                        
                        Row {
                            IconButton(
                                onClick = onAddCategory,
                                modifier = Modifier.size(36.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "添加分类",
                                    tint = Color(0xFF6A45C9)
                                )
                            }
                            
                            IconButton(
                                onClick = onDismiss,
                                modifier = Modifier.size(36.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "关闭",
                                    tint = Color(0xFF6B7280)
                                )
                            }
                        }
                    }
                }
                
                // 分类列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = categories,
                        key = { it.id }
                    ) { category ->
                        CategoryManageItem(
                            category = category,
                            onEdit = { editingCategory = category },
                            onDelete = { 
                                if (!category.isDefault) {
                                    showDeleteConfirm = category.id
                                }
                            }
                        )
                    }
                    
                    if (categories.isEmpty()) {
                        item {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier.padding(32.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Label,
                                    contentDescription = null,
                                    tint = Color.LightGray,
                                    modifier = Modifier.size(48.dp)
                                )
                                Spacer(modifier = Modifier.height(12.dp))
                                Text(
                                    text = "暂无自定义分类",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 编辑分类对话框
    if (editingCategory != null) {
        EditCategoryDialog(
            category = editingCategory!!,
            onDismiss = { editingCategory = null },
            onConfirm = { title, icon, color ->
                onEditCategory(editingCategory!!.id, title, icon, color)
                editingCategory = null
            }
        )
    }
    
    // 删除确认对话框
    if (showDeleteConfirm != null) {
        val category = categories.find { it.id == showDeleteConfirm }
        AlertDialog(
            onDismissRequest = { showDeleteConfirm = null },
            title = { Text("删除分类") },
            text = { Text("确定要删除分类「${category?.title}」吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDeleteCategory(showDeleteConfirm!!)
                        showDeleteConfirm = null
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirm = null }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 分类管理项
 */
@Composable
private fun CategoryManageItem(
    category: CustomHabitCategory,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color(0xFFFAFBFC),
        shape = RoundedCornerShape(12.dp),
        border = BorderStroke(1.dp, Color(0xFFE5E7EB))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分类图标和颜色
            Surface(
                modifier = Modifier.size(40.dp),
                color = category.color.copy(alpha = 0.2f),
                shape = CircleShape
            ) {
                Text(
                    text = category.icon, // 现在是emoji字符串
                    fontSize = 18.sp, // 缩小分类emoji尺寸：24sp -> 18sp
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp)
                        .wrapContentSize()
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 分类信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = category.title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F1937)
                )
                
                if (category.isDefault) {
                    Text(
                        text = "默认分类",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color(0xFF6B7280)
                    )
                }
            }
            
            // 操作按钮
            Row {
                IconButton(
                    onClick = onEdit,
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑",
                        tint = Color(0xFF6A45C9),
                        modifier = Modifier.size(18.dp)
                    )
                }
                
                if (!category.isDefault) {
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加分类对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddCategoryDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, String, Color) -> Unit // 改为String类型
) {
    var title by remember { mutableStateOf("") }
    var selectedIcon by remember { mutableStateOf("🏷️") } // 改为emoji字符串
    var selectedColor by remember { mutableStateOf(Color(0xFFB6D9F2)) }
    
    val availableIcons = listOf(
        "🏷️" to "标签",
        "❤️" to "爱心", 
        "🎓" to "学习",
        "💼" to "工作",
        "🏠" to "居家",
        "💪" to "健身",
        "🧘‍♀️" to "冥想",
        "🍽️" to "饮食",
        "😴" to "睡眠",
        "🏃‍♂️" to "跑步"
    )
    
    val availableColors = listOf(
        Color(0xFFf5c4c4), Color(0xFFb6d9f2), Color(0xFFb6e2de),
        Color(0xFFfbe3c2), Color(0xFFd7c9e8), Color(0xFFdcead1),
        Color(0xFFffd1dc), Color(0xFFc7e9b4), Color(0xFFb3d9ff),
        Color(0xFFffeaa7), Color(0xFFd1c4e9), Color(0xFFf8bbd9)
    )
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "添加分类",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F1937),
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 分类名称输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("分类名称") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        cursorColor = selectedColor
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图标选择
                Text(
                    text = "选择图标",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(8), // 增加列数：5 -> 8，显示更多图标
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    modifier = Modifier
                        .heightIn(max = 160.dp) // 增大高度：120dp -> 160dp
                        .padding(bottom = 16.dp)
                ) {
                    items(availableIcons) { (icon, name) ->
                        val isSelected = selectedIcon == icon
                        Surface(
                            modifier = Modifier
                                .size(36.dp) // 稍微缩小图标大小以适应更多列
                                .clickable { selectedIcon = icon },
                            color = if (isSelected) selectedColor.copy(alpha = 0.2f) else Color(0xFFF3F4F6),
                            shape = CircleShape,
                            border = if (isSelected) BorderStroke(2.dp, selectedColor) else null
                        ) {
                            Text(
                                text = icon, // 现在是emoji字符串
                                fontSize = 14.sp, // 稍微缩小图标emoji：16sp -> 14sp
                                modifier = Modifier.padding(6.dp)
                            )
                        }
                    }
                }
                
                // 颜色选择
                Text(
                    text = "选择颜色",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(10), // 增加列数：6 -> 10，显示更多颜色
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    modifier = Modifier
                        .heightIn(max = 120.dp) // 增大高度：100dp -> 120dp
                        .padding(bottom = 20.dp)
                ) {
                    items(availableColors) { color ->
                        val isSelected = selectedColor == color
                        Surface(
                            modifier = Modifier
                                .size(32.dp)
                                .clickable { selectedColor = color },
                            color = color,
                            shape = CircleShape,
                            border = if (isSelected) BorderStroke(3.dp, Color.White) else null,
                            shadowElevation = if (isSelected) 4.dp else 0.dp
                        ) {
                            if (isSelected) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.padding(6.dp)
                                )
                            }
                        }
                    }
                }
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消", color = Color(0xFF6B7280))
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                onConfirm(title.trim(), selectedIcon, selectedColor)
                            }
                        },
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = selectedColor
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text("添加", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 编辑分类对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditCategoryDialog(
    category: CustomHabitCategory,
    onDismiss: () -> Unit,
    onConfirm: (String, String, Color) -> Unit // 改为String类型
) {
    var title by remember { mutableStateOf(category.title) }
    var selectedIcon by remember { mutableStateOf(category.icon) } // 现在是String类型
    var selectedColor by remember { mutableStateOf(category.color) }
    
    val availableIcons = listOf(
        "🏷️" to "标签",
        "❤️" to "爱心", 
        "🎓" to "学习",
        "💼" to "工作",
        "🏠" to "居家",
        "💪" to "健身",
        "🧘‍♀️" to "冥想",
        "🍽️" to "饮食",
        "😴" to "睡眠",
        "🏃‍♂️" to "跑步"
    )
    
    val availableColors = listOf(
        Color(0xFFf5c4c4), Color(0xFFb6d9f2), Color(0xFFb6e2de),
        Color(0xFFfbe3c2), Color(0xFFd7c9e8), Color(0xFFdcead1),
        Color(0xFFffd1dc), Color(0xFFc7e9b4), Color(0xFFb3d9ff),
        Color(0xFFffeaa7), Color(0xFFd1c4e9), Color(0xFFf8bbd9)
    )
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "编辑分类",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F1937),
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 分类名称输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("分类名称") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !category.isDefault, // 默认分类不允许修改名称
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        cursorColor = selectedColor
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图标选择
                Text(
                    text = "选择图标",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(8), // 增加列数：5 -> 8，显示更多图标
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    modifier = Modifier
                        .heightIn(max = 160.dp) // 增大高度：120dp -> 160dp
                        .padding(bottom = 16.dp)
                ) {
                    items(availableIcons) { (icon, name) ->
                        val isSelected = selectedIcon == icon
                        Surface(
                            modifier = Modifier
                                .size(36.dp) // 稍微缩小图标大小以适应更多列
                                .clickable { selectedIcon = icon },
                            color = if (isSelected) selectedColor.copy(alpha = 0.2f) else Color(0xFFF3F4F6),
                            shape = CircleShape,
                            border = if (isSelected) BorderStroke(2.dp, selectedColor) else null
                        ) {
                            Text(
                                text = icon, // 现在是emoji字符串
                                fontSize = 14.sp, // 稍微缩小图标emoji：16sp -> 14sp
                                modifier = Modifier.padding(6.dp)
                            )
                        }
                    }
                }
                
                // 颜色选择
                Text(
                    text = "选择颜色",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyVerticalGrid(
                    columns = GridCells.Fixed(10), // 增加列数：6 -> 10，显示更多颜色
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    modifier = Modifier
                        .heightIn(max = 120.dp) // 增大高度：100dp -> 120dp
                        .padding(bottom = 20.dp)
                ) {
                    items(availableColors) { color ->
                        val isSelected = selectedColor == color
                        Surface(
                            modifier = Modifier
                                .size(32.dp)
                                .clickable { selectedColor = color },
                            color = color,
                            shape = CircleShape,
                            border = if (isSelected) BorderStroke(3.dp, Color.White) else null,
                            shadowElevation = if (isSelected) 4.dp else 0.dp
                        ) {
                            if (isSelected) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = null,
                                    tint = Color.White,
                                    modifier = Modifier.padding(6.dp)
                                )
                            }
                        }
                    }
                }
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消", color = Color(0xFF6B7280))
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                onConfirm(title.trim(), selectedIcon, selectedColor)
                            }
                        },
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = selectedColor
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text("保存", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 习惯操作对话框
 */
@Composable
private fun HabitActionDialog(
    habit: HabitData,
    onDismiss: () -> Unit,
    onArchive: (HabitData) -> Unit,
    onDelete: (HabitData) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 习惯信息
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 20.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        habit.color.copy(alpha = 0.2f),
                                        habit.color.copy(alpha = 0.1f)
                                    )
                                )
                            )
                    ) {
                        if (habit.customEmoji.isNotEmpty()) {
                            Text(
                                text = habit.customEmoji,
                                fontSize = 18.sp // 缩小emoji尺寸：24sp -> 18sp
                            )
                        } else {
                            Text(
                                text = habit.icon, // 现在是emoji字符串
                                fontSize = 18.sp // 缩小emoji尺寸：24sp -> 18sp
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column {
                        Text(
                            text = habit.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF1F1937)
                        )
                        
                        if (habit.description.isNotEmpty()) {
                            Text(
                                text = habit.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF6B7280),
                                modifier = Modifier.padding(top = 2.dp)
                            )
                        }
                    }
                }
                
                // 操作按钮
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 归档按钮
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onArchive(habit) },
                        color = Color(0xFFF8F9FA),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Archive,
                                contentDescription = "归档",
                                tint = Color(0xFF6B7280),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(12.dp))
                            
                            Column {
                                Text(
                                    text = "归档习惯",
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF1F1937)
                                )
                                
                                Text(
                                    text = "暂停追踪，保留历史数据",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF6B7280)
                                )
                            }
                        }
                    }
                    
                    // 删除按钮
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onDelete(habit) },
                        color = Color(0xFFFEF2F2),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "删除",
                                tint = Color(0xFFEF4444),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(12.dp))
                            
                            Column {
                                Text(
                                    text = "删除习惯",
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFFEF4444)
                                )
                                
                                Text(
                                    text = "永久删除，无法恢复",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFFEF4444).copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 取消按钮
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onDismiss() },
                    color = Color.Transparent,
                    shape = RoundedCornerShape(12.dp),
                    border = BorderStroke(1.dp, Color(0xFFE5E7EB))
                ) {
                    Text(
                        text = "取消",
                        modifier = Modifier.padding(vertical = 12.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF6B7280),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 🔧 新增：习惯编辑对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditHabitDialog(
    habit: HabitData,
    onDismiss: () -> Unit,
    onConfirm: (HabitData) -> Unit
) {
    var habitName by remember { mutableStateOf(habit.name) }
    var habitDescription by remember { mutableStateOf(habit.description) }
    var selectedCategory by remember { mutableStateOf(habit.category) }
    var selectedColor by remember { mutableStateOf(habit.color) }
    var selectedIcon by remember { mutableStateOf(habit.icon) }
    var customEmoji by remember { mutableStateOf(habit.customEmoji) }
    
    // 可选颜色列表
    val availableColors = listOf(
        Color(0xFF81C784), Color(0xFF64B5F6), Color(0xFFFFB74D),
        Color(0xFFFF8A65), Color(0xFFBA68C8), Color(0xFF4DB6AC),
        Color(0xFFF06292), Color(0xFF90A4AE), Color(0xFFFFF176)
    )
    
    // 可选图标列表 (Emoji字符串)
    val availableIcons = listOf(
        "🏃", "💧", "📚", "🧘", "💪", "🎯",
        "⏰", "🌱", "🎨", "✍️", "🏠", "💼"
    )

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight()
                .padding(vertical = 24.dp),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "编辑习惯",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1F1937)
                    )
                    
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = Color(0xFF6B7280),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 习惯名称输入
                OutlinedTextField(
                    value = habitName,
                    onValueChange = { habitName = it },
                    label = { Text("习惯名称") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        cursorColor = selectedColor
                    )
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 描述输入
                OutlinedTextField(
                    value = habitDescription,
                    onValueChange = { habitDescription = it },
                    label = { Text("描述（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        cursorColor = selectedColor
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 类别选择
                Text(
                    text = "选择类别",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(HabitCategory.values()) { category ->
                        Surface(
                            modifier = Modifier
                                .clickable { selectedCategory = category },
                            color = if (selectedCategory == category) category.color else category.color.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(16.dp)
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier.padding(12.dp)
                            ) {
                                Text(
                                    text = category.icon,
                                    fontSize = 20.sp,
                                    color = if (selectedCategory == category) Color.White else category.color
                                )
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = category.title,
                                    fontSize = 10.sp,
                                    color = if (selectedCategory == category) Color.White else category.color,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 颜色选择
                Text(
                    text = "选择颜色",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableColors) { color ->
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(color)
                                .clickable { selectedColor = color }
                                .border(
                                    width = if (selectedColor == color) 3.dp else 0.dp,
                                    color = Color(0xFF1F1937),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            if (selectedColor == color) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "已选中",
                                    tint = Color.White,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图标选择
                Text(
                    text = "选择图标",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableIcons) { icon ->
                        Surface(
                            modifier = Modifier
                                .size(40.dp)
                                .clickable { selectedIcon = icon },
                            color = if (selectedIcon == icon) selectedColor.copy(alpha = 0.2f) else Color.Transparent,
                            shape = RoundedCornerShape(8.dp),
                            border = BorderStroke(
                                width = if (selectedIcon == icon) 2.dp else 1.dp,
                                color = if (selectedIcon == icon) selectedColor else Color(0xFFE5E7EB)
                            )
                        ) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                Text(
                                    text = icon,
                                    fontSize = 18.sp
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 自定义Emoji输入
                OutlinedTextField(
                    value = customEmoji,
                    onValueChange = { 
                        // 限制输入长度为2个字符（支持复合emoji）
                        if (it.length <= 2) customEmoji = it 
                    },
                    label = { Text("自定义Emoji（可选）") },
                    placeholder = { Text("如：🎯") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        cursorColor = selectedColor
                    )
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF6B7280)
                        ),
                        border = BorderStroke(1.dp, Color(0xFFE5E7EB))
                    ) {
                        Text("取消")
                    }
                    
                    // 保存按钮
                    Button(
                        onClick = {
                            if (habitName.isNotBlank()) {
                                val updatedHabit = habit.copy(
                                    name = habitName.trim(),
                                    description = habitDescription.trim(),
                                    category = selectedCategory,
                                    color = selectedColor,
                                    icon = selectedIcon,
                                    customEmoji = customEmoji.trim()
                                )
                                onConfirm(updatedHabit)
                            }
                        },
                        enabled = habitName.isNotBlank(),
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = selectedColor
                        )
                    ) {
                        Text("保存", color = Color.White)
                    }
                }
            }
        }
    }
}
