package com.timeflow.app.ui.screen.analytics

import android.content.Intent
import android.app.Activity
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.math.min
import kotlin.math.roundToInt
import android.util.Log
import com.timeflow.app.R
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.material.icons.filled.Flag
import com.timeflow.app.ui.theme.AnalyticsThemeProvider
import com.timeflow.app.ui.theme.LocalAnalyticsTheme

/**
 * 数据分析统计页面 - 莫奈和莫兰迪风格
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun AnalyticsScreen(
    navController: NavController,
    viewModel: AnalyticsViewModel = hiltViewModel()
) {
    AnalyticsThemeProvider {
        val theme = LocalAnalyticsTheme.current
    
    // 状态管理
    var selectedTimeRange by remember { mutableStateOf("周") }
    val timeRanges = listOf("日", "周", "月", "年")
    
    // 获取Activity和Context
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
                SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            val originalStatusBarColor = window.statusBarColor
            
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                window.statusBarColor = originalStatusBarColor
                Log.d("AnalyticsScreen", "AnalyticsScreen disposed")
            }
        }
    }
    
    // 从ViewModel获取真实数据
    val productivityScore by viewModel.productivityScore.collectAsState()
    val completionRate by viewModel.completionRate.collectAsState()
    val interruptionCount by viewModel.interruptionCount.collectAsState()
    val comparisonData by viewModel.comparisonData.collectAsState()
    val timeDistribution by viewModel.timeDistribution.collectAsState()
    val efficiencyData by viewModel.efficiencyData.collectAsState()
    val goalStatistics by viewModel.goalStatistics.collectAsState()
    val habitStatistics by viewModel.habitStatistics.collectAsState()
    val timeDistributionData by viewModel.timeDistributionData.collectAsState()
    val heatmapData by viewModel.heatmapData.collectAsState()
    val dailyInsights by viewModel.dailyInsights.collectAsState()
    val weeklyInsights by viewModel.weeklyInsights.collectAsState()
    val actionSuggestions by viewModel.actionSuggestions.collectAsState()
    val yearlyAchievements by viewModel.yearlyAchievements.collectAsState()
    val activityDetailData by viewModel.activityDetailData.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    // 时间范围变化时更新ViewModel
    LaunchedEffect(selectedTimeRange) {
        val timeRange = when(selectedTimeRange) {
            "日" -> AnalyticsViewModel.TimeRange.DAY
            "周" -> AnalyticsViewModel.TimeRange.WEEK
            "月" -> AnalyticsViewModel.TimeRange.MONTH
            "年" -> AnalyticsViewModel.TimeRange.YEAR
            else -> AnalyticsViewModel.TimeRange.WEEK
        }
        viewModel.setTimeRange(timeRange)
    }
    
    // 主布局
    Box(modifier = Modifier.fillMaxSize()) {
            // 背景渐变 - 使用莫奈色调
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                                theme.colors.monet.background,
                                theme.colors.monet.surface
                        )
                    )
                )
        )
        
            // 主内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
        ) {
            // 顶部应用栏
            TopAppBar(
                title = { 
                    Text(
                        text = "数据分析",
                            style = theme.typography.headlineLarge,
                            color = theme.colors.text.primary
                    )
                },
                actions = {
                    // 里程碑按钮
                        IconButton(onClick = { 
                            navController.navigate(com.timeflow.app.ui.navigation.AppDestinations.MILESTONE_ROUTE) 
                        }) {
                        Icon(
                            imageVector = Icons.Default.Flag,
                            contentDescription = "里程碑图标",
                                tint = theme.colors.monet.primary,
                                modifier = Modifier.size(theme.dimensions.iconSizeMedium)
                        )
                    }
                    
                    // 分享按钮
                    IconButton(onClick = { /* 分享统计报告 */ }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "分享报告",
                                tint = theme.colors.monet.primary,
                                modifier = Modifier.size(theme.dimensions.iconSizeMedium)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = theme.colors.monet.background,
                        titleContentColor = theme.colors.text.primary
                )
            )
            
            // 内容区域
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                        .padding(horizontal = theme.dimensions.spacingXLarge),
                    verticalArrangement = Arrangement.spacedBy(theme.dimensions.spacingLarge)
            ) {
                item {
                    // 时间范围选择器
                    ChipGroup(
                        items = timeRanges,
                        selectedItem = selectedTimeRange,
                            onItemSelected = { newRange -> 
                                selectedTimeRange = newRange
                                val timeRangeEnum = when(newRange) {
                                "日" -> AnalyticsViewModel.TimeRange.DAY
                                "周" -> AnalyticsViewModel.TimeRange.WEEK
                                "月" -> AnalyticsViewModel.TimeRange.MONTH
                                "年" -> AnalyticsViewModel.TimeRange.YEAR
                                else -> AnalyticsViewModel.TimeRange.WEEK
                            }
                            viewModel.setTimeRange(timeRangeEnum)
                        },
                            theme = theme
                    )
                }
                
                // 概览卡片
                item {
                        OverviewCard(
                            selectedTimeRange = selectedTimeRange,
                            efficiencyData = efficiencyData,
                            goalStatistics = goalStatistics,
                            habitStatistics = habitStatistics,
                            timeDistributionData = timeDistributionData,
                            theme = theme,
                            dailyInsights = dailyInsights
                        )
                }
                
                // 活动分布卡片
                item {
                        ActivityDistributionCard(
                            selectedTimeRange = selectedTimeRange,
                            timeDistribution = timeDistribution,
                            viewModel = viewModel,
                            theme = theme,
                            realActivityData = activityDetailData
                                    )
                                }
                    
                    // 生产力得分卡片
                item {
                        ProductivityScoreCard(
                            productivityScore = productivityScore,
                            completionRate = completionRate,
                            interruptionCount = interruptionCount,
                            comparisonData = comparisonData,
                            theme = theme
                        )
                }
                
                // 高效时段分析
                item {
                        EfficiencyAnalysisCard(theme = theme)
                }
                
                // 专注质量分析
                item {
                        FocusQualityCard(theme = theme)
                }
                
                // AI智能复盘卡片
                item {
                        AIReviewCard(
                            navController = navController,
                            theme = theme,
                            dailyInsights = dailyInsights
                        )
                }
                
                item {
                    // 生成报告按钮
                    Button(
                        onClick = { /* 生成并分享报告 */ },
                        modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(theme.dimensions.cardRadius),
                        colors = ButtonDefaults.buttonColors(
                                containerColor = theme.colors.monet.primary,
                                contentColor = theme.colors.monet.surface
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = null,
                                modifier = Modifier.size(theme.dimensions.iconSizeMedium)
                        )
                            Spacer(modifier = Modifier.width(theme.dimensions.spacingMedium))
                        Text(
                            text = "生成并分享${selectedTimeRange}度报告",
                                style = theme.typography.titleMedium,
                                modifier = Modifier.padding(vertical = theme.dimensions.spacingMedium)
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(72.dp)) // 底部导航栏空间
                    }
                }
            }
        }
    }
}

/**
 * 玻璃卡片组件
 */
@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .border(
                width = 1.dp,
                color = Color.White.copy(alpha = 0.4f),
                shape = RoundedCornerShape(16.dp)
            ),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.7f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        content()
    }
}

/**
 * 统计项组件
 */
@Composable
fun StatItem(
    label: String,
    value: String,
    icon: ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(color.copy(alpha = 0.15f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF3A2E3A)
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF3A2E3A).copy(alpha = 0.7f)
        )
    }
}

/**
 * 图例项组件
 */
@Composable
fun LegendItem(
    color: Color,
    label: String,
    percentage: Float
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF3A2E3A).copy(alpha = 0.8f),
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = "${percentage.roundToInt()}%",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF3A2E3A)
        )
    }
}





/**
 * 柱状图组件
 */
@Composable
fun BarChart(
    data: List<Pair<String, Float>>,
    primaryColor: Color,
    secondaryColor: Color
) {
    val maxValue = data.maxOfOrNull { it.second } ?: 100f
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 图表区域
        Row(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.Bottom
        ) {
            data.forEachIndexed { index, (_, value) ->
                // 🔧 修复NaN问题：安全计算比例值，防止除零和无效数据
                val ratio = when {
                    maxValue <= 0f || !maxValue.isFinite() -> 0f  // 防止除零和无效maxValue
                    !value.isFinite() || value < 0f -> 0f         // 防止无效value
                    else -> (value / maxValue).coerceIn(0f, 1f)   // 确保比例在0-1范围内
                }

                val barColor = androidx.compose.ui.graphics.lerp(
                    secondaryColor,
                    primaryColor,
                    ratio
                )
                
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(ratio)
                        .padding(horizontal = 4.dp)
                        .clip(RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    barColor.copy(alpha = 0.7f),
                                    barColor
                                )
                            )
                        )
                ) {
                    // 值显示在柱状图上方
                    Text(
                        text = "${value.toInt()}",
                        color = Color.White,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(top = 2.dp)
                    )
                }
            }
        }
        
        // X轴标签
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
        ) {
            data.forEach { (label, _) ->
                Text(
                    text = label,
                    fontSize = 12.sp,
                    color = Color(0xFF3A2E3A).copy(alpha = 0.7f),
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 热力图组件
 */
@Composable
fun HeatmapChart(
    data: List<List<Float>>,
    primaryColor: Color
) {
    val daysOfWeek = listOf("周一", "周二", "周三", "周四", "周五", "周六", "周日")
    
    Column(modifier = Modifier.fillMaxWidth()) {
        // 时间标签 - 横向
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 40.dp)
        ) {
            for (hour in 0..23 step 4) {
                Text(
                    text = "${hour}时",
                    fontSize = 10.sp,
                    color = Color(0xFF3A2E3A).copy(alpha = 0.6f),
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 热力图主体
        data.forEachIndexed { dayIndex, dayData ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 日期标签 - 纵向
                Text(
                    text = daysOfWeek[dayIndex],
                    fontSize = 12.sp,
                    color = Color(0xFF3A2E3A).copy(alpha = 0.7f),
                    modifier = Modifier.width(40.dp)
                )
                
                // 热力格子
                Row(
                    modifier = Modifier.weight(1f)
                ) {
                    dayData.forEachIndexed { hourIndex, value ->
                        val alpha = value.coerceIn(0f, 1f)
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(20.dp)
                                .padding(horizontal = 1.dp, vertical = 1.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(primaryColor.copy(alpha = alpha))
                        )
                    }
                }
            }
        }
    }
}

/**
 * 活动详情项
 */
@Composable
fun ActivityDetailItem(
    name: String,
    percentage: String,
    time: String,
    pomos: String,
    color: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 颜色标记
        Box(
            modifier = Modifier
                .size(10.dp)
                .background(color, CircleShape)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 活动名称
        Text(
            text = name,
            fontSize = 14.sp,
            color = Color(0xFF3A2E3A),
            modifier = Modifier.weight(1f)
        )
        
        // 百分比
        Text(
            text = percentage,
            fontSize = 14.sp,
            color = Color(0xFF3A2E3A).copy(alpha = 0.7f),
            modifier = Modifier.width(60.dp)
        )
        
        // 时间
        Text(
            text = time,
            fontSize = 14.sp,
            color = Color(0xFF3A2E3A).copy(alpha = 0.7f),
            modifier = Modifier.width(100.dp),
            textAlign = TextAlign.End
        )
        
        // Pomos
        Text(
            text = "$pomos Pomos",
            fontSize = 14.sp,
            color = Color(0xFF3A2E3A).copy(alpha = 0.7f),
            modifier = Modifier.width(80.dp),
            textAlign = TextAlign.End
        )
    }
}

/**
 * 一天时段效率图
 */
@Composable
fun DayTimeEfficiencyChart() {
    Canvas(modifier = Modifier.fillMaxSize()) {
        val width = size.width
        val height = size.height
        val barWidth = width / 24 // 24小时
        
        // 效率数据 (0-1 范围)
        val efficiencyData = listOf(
            0.2f, 0.3f, 0.1f, 0.05f, 0.05f, 0.1f, 0.2f, 0.4f, 
            0.6f, 0.8f, 0.9f, 0.7f, 0.6f, 0.8f, 0.9f, 0.85f,
            0.7f, 0.5f, 0.4f, 0.3f, 0.25f, 0.2f, 0.15f, 0.1f
        )
        
        // 绘制每小时的效率条
        efficiencyData.forEachIndexed { index, efficiency ->
            val barHeight = height * efficiency
            val barColor = lerp(
                Color(0xFFE57373), // 低效率颜色
                Color(0xFF66BB6A), // 高效率颜色
                efficiency
            )
            
            drawRoundRect(
                color = barColor,
                topLeft = Offset(index * barWidth + 2, height - barHeight),
                size = Size(barWidth - 4, barHeight),
                cornerRadius = CornerRadius(4f, 4f)
            )
        }
    }
}

/**
 * 最佳时段项
 */
@Composable
fun BestTimeItem(
    title: String,
    time: String,
    description: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            color = Color.Gray
        )
        
        Text(
            text = time,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF3A2E3A)
        )
        
        Text(
            text = description,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 专注质量项
 */
@Composable
fun FocusQualityItem(
    title: String,
    value: String,
    description: String,
    icon: ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .background(color.copy(alpha = 0.1f), CircleShape),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF3A2E3A)
        )
        
        Text(
            text = title,
            fontSize = 14.sp,
            color = Color.Gray
        )
        
        Text(
            text = description,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 复盘模型标签组件
 */
@Composable
fun ReviewModelChip(
    title: String,
    model: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 12.dp, vertical = 8.dp)
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = model,
            fontSize = 12.sp,
            color = color.copy(alpha = 0.8f)
        )
    }
}

/**
 * 季度表现盒子
 */
@Composable
fun QuarterBox(
    title: String,
    score: Int,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .padding(4.dp)
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 8.dp, vertical = 12.dp)
            .width(64.dp)
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = color
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "$score",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "分",
            fontSize = 12.sp,
            color = color.copy(alpha = 0.8f)
        )
    }
}

/**
 * 周进度条组件
 */
@Composable
fun WeekProgressBar() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "周二",
            fontSize = 14.sp,
            color = Color.DarkGray
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 显示一周中的块，表示进度
            repeat(10) { index ->
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(24.dp)
                        .background(
                            color = if (index == 0) Color(0xFFB2DFDB) else Color(0xFF26A69A),
                            shape = RoundedCornerShape(4.dp)
                        )
                )
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "查看更多",
            tint = Color.Gray
        )
    }
}

/**
 * 彩虹环形图
 */
@Composable
fun RainbowDonutChart() {
    Canvas(modifier = Modifier.size(240.dp)) {
        val center = Offset(size.width / 2, size.height / 2)
        val radius = size.width * 0.4f
        
        // 绘制多个彩色弧线，形成彩虹效果
        val colors = listOf(
            Color(0xFFE1F5FE),
            Color(0xFFB3E5FC),
            Color(0xFFA8D0DB),
            Color(0xFFD1C4E9),
            Color(0xFFCFBCE2),
            Color(0xFFFFCCBC),
            Color(0xFFFFECB3),
            Color(0xFFF0F4C3)
        )
        
        var startAngle = 0f
        colors.forEachIndexed { index, color ->
            val sweepAngle = if (index == 0) 120f else (240f / (colors.size - 1))
            val segmentGap = 2f
            
            drawArc(
                color = color,
                startAngle = startAngle + segmentGap / 2,
                sweepAngle = sweepAngle - segmentGap,
                useCenter = false,
                topLeft = Offset(center.x - radius, center.y - radius),
                size = Size(radius * 2, radius * 2),
                style = Stroke(width = 30.dp.toPx(), cap = StrokeCap.Round)
            )
            
            startAngle += sweepAngle
        }
    }
}



/**
 * 数据高亮卡片
 */
@Composable
private fun DataHighlightCard(
    icon: ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    color: Color
) {
    Card(
        modifier = modifier
            .height(90.dp),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 4.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(22.dp)
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = value,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
            
            Text(
                text = label,
                fontSize = 12.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/**
 * 洞察卡片
 */
@Composable
private fun InsightCard(
    title: String,
    insights: List<String>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF5F5F5)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            insights.forEach { insight ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .padding(top = 6.dp)
                            .background(Color(0xFF7986CB), CircleShape)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = insight,
                        fontSize = 13.sp,
                        color = Color.DarkGray,
                        lineHeight = 18.sp
                    )
                }
            }
        }
    }
}

/**
 * 行动建议卡片
 */
@Composable
private fun ActionSuggestionsCard(
    suggestions: List<String>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFE8F5E9)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = Color(0xFF66BB6A),
                    modifier = Modifier.size(18.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "行动建议",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2E7D32)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            suggestions.forEach { suggestion ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowRight,
                        contentDescription = null,
                        tint = Color(0xFF66BB6A),
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = suggestion,
                        fontSize = 13.sp,
                        color = Color.DarkGray,
                        lineHeight = 18.sp
                    )
                }
            }
        }
    }
}

/**
 * 成就卡片
 */
@Composable
private fun AchievementCard(
    achievements: List<String>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFE3F2FD)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.EmojiEvents,
                    contentDescription = null,
                    tint = Color(0xFF1976D2),
                    modifier = Modifier.size(18.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "年度成就",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1565C0)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            achievements.forEach { achievement ->
                Row(
                    modifier = Modifier.padding(vertical = 4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = Color(0xFFFFC107),
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = achievement,
                        fontSize = 13.sp,
                        color = Color.DarkGray,
                        lineHeight = 18.sp
                    )
                }
            }
        }
    }
} 