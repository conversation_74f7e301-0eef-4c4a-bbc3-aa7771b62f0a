# 任务循环功能UI设计说明

## 🎨 **界面设计概览**

### 主要界面结构
```
┌─────────────────────────────────────┐
│ 📝 新建任务 / 编辑任务               │
├─────────────────────────────────────┤
│ [任务标题输入框]                     │
│                                     │
│ 📋 子任务                           │
│ ├─ [子任务1] ☑️                     │
│ ├─ [子任务2] ☐                      │
│ └─ [+ 添加子任务]                   │
│                                     │
│ ⚙️ 任务设置                         │
│ ┌─────────────────────────────────┐ │
│ │ 🚩 优先级    │ 中优先级      ▶ │ │
│ │ ─────────────────────────────── │ │
│ │ ⏰ 时间      │ 未设置时间范围 ▶ │ │
│ │ ─────────────────────────────── │ │
│ │ 🔄 循环      │ 不循环        ▶ │ │ ← 新增功能
│ │ ─────────────────────────────── │ │
│ │ 🏷️ 标签      │ 添加标签      ▶ │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [创建任务] [AI优化]                  │
└─────────────────────────────────────┘
```

## 🔄 **循环设置对话框设计**

### 对话框布局
```
┌─────────────────────────────────────┐
│ 🔄 循环设置                         │
├─────────────────────────────────────┤
│                                     │
│ 启用循环                    [🔘 OFF] │
│                                     │
│ ── 当开关打开时显示 ──                │
│                                     │
│ 循环类型                             │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ ⚪ 每天                          │ │
│ │    每天重复                      │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ ⚫ 每周                          │ │ ← 选中状态
│ │    每周重复                      │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ ⚪ 每月                          │ │
│ │    每月重复                      │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ ⚪ 每年                          │ │
│ │    每年重复                      │ │
│ └─────────────────────────────────┘ │
│                                     │
│           [取消]    [确定]           │
└─────────────────────────────────────┘
```

## 🎯 **交互流程设计**

### 1. **进入循环设置**
```
用户操作: 点击"循环"设置项
↓
界面反应: 弹出循环设置对话框
↓
初始状态: 
- 开关处于OFF状态
- 循环类型选项隐藏
- 显示"不循环"状态
```

### 2. **启用循环功能**
```
用户操作: 打开"启用循环"开关
↓
界面反应: 
- 开关变为ON状态
- 展开循环类型选择区域
- 默认选中"每天"选项
↓
状态更新:
- isRecurring = true
- selectedRecurrenceType = DAILY
- recurrenceSettings = RecurrenceSettings.daily()
```

### 3. **选择循环类型**
```
用户操作: 点击不同的循环类型选项
↓
界面反应:
- 之前选中的选项变为未选中状态
- 新选择的选项变为选中状态
- RadioButton状态更新
↓
数据更新:
- selectedRecurrenceType 更新为新选择的类型
- recurrenceSettings 更新为对应的默认设置
```

### 4. **确认设置**
```
用户操作: 点击"确定"按钮
↓
数据处理:
- 如果启用循环: 保存选择的类型和设置
- 如果禁用循环: 重置为NONE状态
↓
界面更新:
- 关闭对话框
- 主界面循环设置项显示更新后的状态
- 如"每周"、"每月"等
```

## 🎨 **视觉设计规范**

### 1. **颜色方案**
```kotlin
// 主题色彩
val DustyLavender = Color(0xFFCCAEC5)  // 主要强调色
val LavenderAsh = Color(0xFFB8A9B8)    // 次要色彩
val TextPrimary = Color(0xFF2C2C2C)    // 主要文本
val TextSecondary = Color(0xFF666666)  // 次要文本

// 状态色彩
val SelectedBackground = DustyLavender.copy(alpha = 0.1f)  // 选中背景
val BorderSelected = DustyLavender                         // 选中边框
val BorderUnselected = LavenderAsh.copy(alpha = 0.3f)     // 未选中边框
```

### 2. **字体规范**
```kotlin
// 对话框标题
fontSize = 18.sp
fontWeight = FontWeight.Bold
color = TextPrimary

// 设置项标签
fontSize = 16.sp
fontWeight = FontWeight.Medium
color = TextPrimary

// 选项描述
fontSize = 12.sp
color = TextSecondary

// 按钮文字
fontSize = 16.sp
fontWeight = FontWeight.Medium
```

### 3. **间距规范**
```kotlin
// 对话框内边距
padding = 20.dp

// 设置项间距
verticalArrangement = Arrangement.spacedBy(8.dp)

// 组件内边距
padding = 12.dp

// 按钮间距
horizontalArrangement = Arrangement.spacedBy(12.dp)
```

### 4. **圆角规范**
```kotlin
// 对话框圆角
shape = RoundedCornerShape(16.dp)

// 设置卡片圆角
shape = RoundedCornerShape(12.dp)

// 选项卡片圆角
shape = RoundedCornerShape(8.dp)
```

## 📱 **响应式设计**

### 1. **适配不同屏幕尺寸**
```kotlin
// 对话框宽度
modifier = Modifier
    .fillMaxWidth()
    .padding(16.dp)  // 左右留白适应小屏幕

// 最大宽度限制
.widthIn(max = 400.dp)  // 大屏幕时限制最大宽度
```

### 2. **触摸目标优化**
```kotlin
// 最小触摸目标
modifier = Modifier
    .fillMaxWidth()
    .heightIn(min = 48.dp)  // 确保足够的触摸面积

// 按钮高度
contentPadding = PaddingValues(vertical = 16.dp)
```

## 🔧 **组件复用设计**

### 1. **DetailItem通用组件**
```kotlin
// 统一的设置项组件
DetailItem(
    icon: ImageVector,      // 左侧图标
    label: String,          // 设置项名称
    value: String,          // 当前值显示
    onClick: () -> Unit     // 点击回调
)

// 使用示例
DetailItem(
    icon = Icons.Default.Repeat,
    label = "循环",
    value = if (isRecurring) selectedRecurrenceType.displayName else "不循环",
    onClick = { showRecurrenceDialog = true }
)
```

### 2. **RecurrenceTypeItem选项组件**
```kotlin
// 循环类型选择项组件
RecurrenceTypeItem(
    type: RecurrenceType,   // 循环类型
    isSelected: Boolean,    // 是否选中
    onClick: () -> Unit     // 点击回调
)

// 自动处理选中状态的视觉反馈
// 自动显示类型名称和描述
// 统一的交互行为
```

## 🎭 **动画效果设计**

### 1. **对话框进入/退出动画**
```kotlin
// 使用系统默认的Dialog动画
// 淡入淡出 + 轻微缩放效果
// 符合Material Design规范
```

### 2. **开关状态切换动画**
```kotlin
// Switch组件自带的滑动动画
// 颜色渐变效果
// 流畅的状态转换
```

### 3. **选项选择动画**
```kotlin
// RadioButton的选中动画
// 边框颜色渐变
// 背景色淡入淡出
```

## 📋 **可访问性设计**

### 1. **语义化标签**
```kotlin
// 为所有交互元素提供contentDescription
Icon(
    imageVector = Icons.Default.Repeat,
    contentDescription = "循环设置"
)

RadioButton(
    selected = isSelected,
    onClick = onClick,
    // 自动提供选中状态的语义信息
)
```

### 2. **键盘导航支持**
```kotlin
// 所有可交互元素支持Tab键导航
// 明确的焦点指示器
// 合理的Tab顺序
```

### 3. **屏幕阅读器支持**
```kotlin
// 清晰的文本标签
// 状态变化的语音反馈
// 结构化的信息层次
```

## 🔍 **用户测试要点**

### 1. **易用性测试**
- [ ] 用户能否快速找到循环设置入口
- [ ] 设置流程是否直观易懂
- [ ] 选项说明是否清晰明确

### 2. **功能测试**
- [ ] 所有循环类型都能正确选择
- [ ] 设置能够正确保存和显示
- [ ] 取消操作不会影响原有设置

### 3. **视觉测试**
- [ ] 界面布局是否美观协调
- [ ] 选中状态是否清晰可见
- [ ] 文字大小是否合适阅读

---

> **设计总结**: 循环功能的UI设计遵循了Material Design规范，参考了知名任务管理app的交互模式，提供了直观、易用的循环设置体验。通过统一的设置卡片、清晰的对话框布局和流畅的交互动画，用户可以轻松配置任务的循环规则。🎨✨
