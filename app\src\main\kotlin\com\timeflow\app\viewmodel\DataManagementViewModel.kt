package com.timeflow.app.viewmodel

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.timeflow.app.utils.DatabaseBackupManager
import com.timeflow.app.ui.screen.settings.StorageInfo
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.entity.Goal
import com.timeflow.app.data.entity.GoalSubTask
import com.timeflow.app.data.entity.ReflectionEntity
import com.timeflow.app.data.entity.Habit
import com.timeflow.app.data.entity.HabitRecord
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.InputStream
import javax.inject.Inject

/**
 * 数据管理ViewModel
 * 统一管理备份、恢复、导入导出、存储清理等功能
 */
@HiltViewModel
class DataManagementViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val backupManager: DatabaseBackupManager,
    private val database: AppDatabase
) : ViewModel() {
    
    companion object {
        private const val TAG = "DataManagementViewModel"
    }

    /**
     * 导出数据模型
     */
    @Serializable
    data class ExportData(
        val version: String = "1.0",
        val exportTime: Long = System.currentTimeMillis(),
        val tasks: List<Task> = emptyList(),
        val goals: List<Goal> = emptyList(),
        val goalSubTasks: List<GoalSubTask> = emptyList(),
        val reflections: List<ReflectionEntity> = emptyList(),
        val habits: List<Habit> = emptyList(),
        val habitRecords: List<HabitRecord> = emptyList()
    )
    
    // UI状态
    private val _uiState = MutableStateFlow(DataManagementUiState())
    val uiState: StateFlow<DataManagementUiState> = _uiState.asStateFlow()
    
    // 存储信息
    private val _storageInfo = MutableStateFlow(StorageInfo())
    val storageInfo: StateFlow<StorageInfo> = _storageInfo.asStateFlow()
    
    /**
     * 加载数据
     */
    fun loadData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 加载备份文件列表
                val backupInfo = backupManager.getBackupInfo()
                val backupFiles = backupInfo.backupFiles

                // 加载备份设置
                val backupSettings = backupManager.getBackupSettings()
                
                // 加载存储信息
                loadStorageInfo()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    backupFiles = backupFiles,
                    autoBackupEnabled = backupSettings.isAutoBackupEnabled,
                    backupFrequency = backupSettings.autoBackupFrequency,
                    lastBackupTime = backupInfo.lastBackupTime
                )
                
                Log.d(TAG, "数据加载完成，备份文件数量: ${backupFiles.size}")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 创建备份
     */
    fun createBackup() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val success = backupManager.backupDatabase()
                
                if (success) {
                    // 重新加载备份文件列表
                    val backupInfo = backupManager.getBackupInfo()

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        backupFiles = backupInfo.backupFiles,
                        lastBackupTime = backupInfo.lastBackupTime
                    )
                    
                    Log.d(TAG, "备份创建成功")
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "备份创建失败"
                    )
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "创建备份失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "创建备份失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 恢复备份
     */
    fun restoreBackup(fileName: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val success = backupManager.restoreBackup(fileName)
                
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        restoreCompleted = true
                    )
                    Log.d(TAG, "备份恢复成功: $fileName")
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "恢复备份失败"
                    )
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "恢复备份失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "恢复备份失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 快速恢复（恢复到最新备份）
     */
    fun quickRestore() {
        val latestBackup = _uiState.value.backupFiles.maxByOrNull { it.timestamp }
        if (latestBackup != null) {
            restoreBackup(latestBackup.fileName)
        } else {
            _uiState.value = _uiState.value.copy(error = "没有可用的备份文件")
        }
    }
    
    /**
     * 删除备份
     */
    fun deleteBackup(fileName: String) {
        viewModelScope.launch {
            try {
                val success = backupManager.deleteBackup(fileName)
                
                if (success) {
                    // 重新加载备份文件列表
                    val backupInfo = backupManager.getBackupInfo()
                    _uiState.value = _uiState.value.copy(backupFiles = backupInfo.backupFiles)
                    
                    Log.d(TAG, "备份删除成功: $fileName")
                } else {
                    _uiState.value = _uiState.value.copy(error = "删除备份失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "删除备份失败", e)
                _uiState.value = _uiState.value.copy(error = "删除备份失败: ${e.message}")
            }
        }
    }
    
    /**
     * 切换自动备份
     */
    fun toggleAutoBackup() {
        viewModelScope.launch {
            try {
                val currentSettings = backupManager.getBackupSettings()
                val newEnabled = !currentSettings.isAutoBackupEnabled
                
                backupManager.setAutoBackupEnabled(newEnabled)
                
                _uiState.value = _uiState.value.copy(autoBackupEnabled = newEnabled)
                
                Log.d(TAG, "自动备份设置已更新: $newEnabled")
                
            } catch (e: Exception) {
                Log.e(TAG, "更新自动备份设置失败", e)
                _uiState.value = _uiState.value.copy(error = "更新设置失败: ${e.message}")
            }
        }
    }
    
    /**
     * 设置备份频率
     */
    fun setBackupFrequency(frequency: String) {
        viewModelScope.launch {
            try {
                val frequencyKey = when (frequency) {
                    "每日" -> "daily"
                    "每周" -> "weekly"
                    "每月" -> "monthly"
                    else -> "weekly"
                }
                
                backupManager.setAutoBackupFrequency(frequencyKey)
                
                _uiState.value = _uiState.value.copy(backupFrequency = frequency)
                
                Log.d(TAG, "备份频率已更新: $frequency")
                
            } catch (e: Exception) {
                Log.e(TAG, "更新备份频率失败", e)
                _uiState.value = _uiState.value.copy(error = "更新频率失败: ${e.message}")
            }
        }
    }
    
    /**
     * 导出数据 - 支持JSON格式
     */
    fun exportData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                // 收集所有数据
                val exportData = collectAllData()

                // 转换为JSON格式
                val jsonData = convertToJson(exportData)

                // 保存到文件
                val fileName = "timeflow_export_${System.currentTimeMillis()}.json"
                val file = File(context.getExternalFilesDir(null), fileName)
                file.writeText(jsonData)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = null
                )

                // 通知用户导出成功
                Log.d(TAG, "数据导出成功: ${file.absolutePath}")

            } catch (e: Exception) {
                Log.e(TAG, "导出数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "导出数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 导入数据 - 支持JSON格式
     */
    fun importData(fileUri: android.net.Uri? = null) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                if (fileUri == null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请选择要导入的文件"
                    )
                    return@launch
                }

                // 读取文件内容
                val jsonData = readFileFromUri(fileUri)

                // 解析JSON数据
                val importData = parseJsonData(jsonData)

                // 导入数据到数据库
                importDataToDatabase(importData)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = null
                )

                Log.d(TAG, "数据导入成功")

            } catch (e: Exception) {
                Log.e(TAG, "导入数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "导入数据失败: ${e.message}"
                )
            }
        }
    }
    

    
    /**
     * 加载存储信息
     */
    private fun loadStorageInfo() {
        try {
            val appDataDir = context.filesDir
            val cacheDir = context.cacheDir
            
            val appDataSize = calculateDirectorySize(appDataDir)
            val cacheSize = calculateDirectorySize(cacheDir)
            val backupSize = calculateDirectorySize(File(appDataDir, "database_backups"))
            
            val totalSpace = appDataDir.totalSpace
            val usedSpace = appDataSize + cacheSize
            
            _storageInfo.value = StorageInfo(
                usedSpace = usedSpace,
                totalSpace = totalSpace,
                appDataSize = appDataSize,
                backupSize = backupSize,
                cacheSize = cacheSize
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "加载存储信息失败", e)
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        if (!directory.exists()) return 0L
        
        var size = 0L
        directory.walkTopDown().forEach { file ->
            if (file.isFile) {
                size += file.length()
            }
        }
        return size
    }
    
    /**
     * 递归删除目录
     */
    private fun deleteRecursively(file: File): Boolean {
        return try {
            if (file.isDirectory) {
                file.listFiles()?.forEach { child ->
                    deleteRecursively(child)
                }
            }
            file.delete()
        } catch (e: Exception) {
            Log.e(TAG, "删除文件失败: ${file.path}", e)
            false
        }
    }
    
    /**
     * 收集所有数据用于导出
     */
    private suspend fun collectAllData(): ExportData {
        return ExportData(
            tasks = database.taskDao().getAllTasksList(),
            goals = database.goalDao().getAllGoalsList(),
            goalSubTasks = database.goalDao().getAllSubTasksList(),
            reflections = database.reflectionDao().getAllReflections(),
            habits = database.habitDao().getAllHabits(),
            habitRecords = database.habitDao().getAllHabitRecords()
        )
    }

    /**
     * 将数据转换为JSON格式
     */
    private fun convertToJson(data: ExportData): String {
        return Json.encodeToString(data)
    }

    /**
     * 从URI读取文件内容
     */
    private fun readFileFromUri(uri: android.net.Uri): String {
        return context.contentResolver.openInputStream(uri)?.use { inputStream ->
            inputStream.bufferedReader().readText()
        } ?: throw IllegalArgumentException("无法读取文件")
    }

    /**
     * 解析JSON数据
     */
    private fun parseJsonData(jsonData: String): ExportData {
        return Json.decodeFromString<ExportData>(jsonData)
    }

    /**
     * 将数据导入到数据库
     */
    private suspend fun importDataToDatabase(data: ExportData) {
        // 导入任务
        if (data.tasks.isNotEmpty()) {
            database.taskDao().insertAll(data.tasks)
        }

        // 导入目标
        data.goals.forEach { goal ->
            database.goalDao().insertGoal(goal)
        }

        // 导入目标子任务
        data.goalSubTasks.forEach { subTask ->
            database.goalDao().insertSubTask(subTask)
        }

        // 导入反思
        data.reflections.forEach { reflection ->
            database.reflectionDao().insertReflection(reflection)
        }

        // 导入习惯
        data.habits.forEach { habit ->
            database.habitDao().insertHabit(habit)
        }

        // 导入习惯记录
        data.habitRecords.forEach { record ->
            database.habitDao().insertHabitRecord(record)
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 数据管理UI状态
 */
data class DataManagementUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val backupFiles: List<DatabaseBackupManager.BackupFileInfo> = emptyList(),
    val autoBackupEnabled: Boolean = false,
    val backupFrequency: String = "每周",
    val lastBackupTime: Long? = null,
    val restoreCompleted: Boolean = false
)
