package com.timeflow.app.ui.screen.home

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.ReflectionViewModel

import com.timeflow.app.ui.theme.TextPrimary
import com.timeflow.app.ui.theme.TextSecondary
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * 首页感想展示区域
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeReflectionSection(
    modifier: Modifier = Modifier,
    navController: NavController,
    viewModel: ReflectionViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    // 获取最近的3条感想记录
    val recentReflections = remember(uiState.reflections) {
        uiState.reflections.take(3)
    }
    
    LaunchedEffect(Unit) {
        // 加载感想数据
        viewModel.loadReflections()
    }
    
    ElevatedCard(
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(14.dp)  // 从16dp缩小到14dp
        ) {
            // 标题栏
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "最近感想",
                    fontSize = 16.sp, // 从18.sp减小到16.sp
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 新增感想按钮
                    IconButton(
                        onClick = {
                            navController.navigate(AppDestinations.ADD_REFLECTION_ROUTE)
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "新增感想",
                            tint = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    
                    // 查看全部按钮
                    TextButton(
                        onClick = {
                            navController.navigate(AppDestinations.REFLECTION_ROUTE)
                        },
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "查看全部",
                            style = MaterialTheme.typography.bodySmall, // 使用bodySmall样式
                            color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                        )
                        Icon(
                            imageVector = Icons.Default.ChevronRight,
                            contentDescription = "查看全部",
                            tint = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(6.dp)) // 从8dp减小到6dp
            
            // 感想列表
            if (recentReflections.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(10.dp),  // 从12dp缩小到10dp
                    contentPadding = PaddingValues(horizontal = 2.dp)  // 从4dp缩小到2dp
                ) {
                    items(recentReflections) { reflection ->
                        EnhancedReflectionCardCompact(
                            reflection = reflection,
                            onClick = {
                                navController.navigate(
                                    AppDestinations.reflectionDetailRoute(reflection.id)
                                )
                            }
                        )
                    }
                }
            } else {
                // 空状态
                ReflectionEmptyState(
                    onCreateClick = {
                        navController.navigate(AppDestinations.ADD_REFLECTION_ROUTE)
                    }
                )
            }
        }
    }
}

/**
 * 🎨 美化紧凑型感想卡片 - 用于首页展示
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun EnhancedReflectionCardCompact(
    reflection: Reflection,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val dateFormatter = remember { DateTimeFormatter.ofPattern("MM/dd") }
    val formattedDate = remember(reflection.date) {
        reflection.date.atZone(ZoneId.systemDefault()).format(dateFormatter)
    }
    
    // 检查是否是任务完成的感想
    val isTaskReflection = reflection.title.startsWith("✓ ")
    val displayTitle = if (isTaskReflection) {
        reflection.title.removePrefix("✓ ").trim()
    } else {
        reflection.title
    }
    
    Card(
        onClick = onClick,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp
        ),
        modifier = modifier.width(150.dp)  // 从180dp缩小到150dp
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFFBFAFB))
                .padding(12.dp)  // 从16dp缩小到12dp
        ) {
            Column {
            // 顶部：日期和心情
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.labelMedium.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 心情图标 - 美化样式
                reflection.mood?.let { mood ->
                    Box(
                        modifier = Modifier
                            .size(28.dp)  // 从32dp缩小到28dp
                            .clip(CircleShape)
                            .background(mood.color.copy(alpha = 0.15f))
                            .border(
                                width = 1.dp,
                                color = mood.color.copy(alpha = 0.3f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = mood.emoji,
                            fontSize = 16.sp  // 从18sp缩小到16sp
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(10.dp))  // 从12dp缩小到10dp
            
            // 标题 - 带任务完成图标
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (isTaskReflection) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(14.dp)  // 从16dp缩小到14dp
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                }
                
                Text(
                    text = displayTitle,
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 14.sp  // 从16sp缩小到14sp
                    ),
                    color = Color(0xFF1A1A1A),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(6.dp))  // 从8dp缩小到6dp
            
            // 内容预览
            Text(
                text = reflection.content,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 11.sp,  // 从12sp缩小到11sp
                    lineHeight = 14.sp  // 从16sp缩小到14sp
                ),
                color = Color(0xFF666666),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))  // 从12dp缩小到8dp
            
            // 标签 - 区分任务标签和感想标签
            val allTags = reflection.tags
            val taskTags = if (isTaskReflection) {
                // 从感想内容中提取任务标签（如果有的话）
                allTags.filter { it !in listOf("任务完成", "反馈") }
            } else {
                emptyList()
            }
            val reflectionTags = if (isTaskReflection) {
                listOf("任务完成")
            } else {
                allTags.take(2)
            }
            
            if (reflectionTags.isNotEmpty() || taskTags.isNotEmpty()) {
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 显示反馈标签
                    reflectionTags.forEach { tag ->
                        val tagColor = if (tag == "任务完成") {
                            Color(0xFF4CAF50)
                        } else {
                            MaterialTheme.colorScheme.secondary
                        }
                        
                        Surface(
                            shape = RoundedCornerShape(8.dp),  // 从10dp缩小到8dp
                            color = tagColor.copy(alpha = 0.1f),
                            modifier = Modifier.height(18.dp)  // 从20dp缩小到18dp
                        ) {
                                                    Text(
                            text = "#$tag",
                            style = MaterialTheme.typography.labelSmall.copy(
                                fontSize = 7.sp  // 从8sp缩小到7sp
                            ),
                            color = tagColor,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(horizontal = 5.dp, vertical = 2.dp)  // 从6dp缩小到5dp
                        )
                        }
                    }
                    
                    // 显示一个任务标签（如果有）
                    if (taskTags.isNotEmpty()) {
                        Surface(
                            shape = RoundedCornerShape(8.dp),  // 从10dp缩小到8dp
                            color = Color(0xFF3182CE).copy(alpha = 0.1f),
                            modifier = Modifier.height(18.dp)  // 从20dp缩小到18dp
                        ) {
                            Text(
                                text = "#${taskTags.first()}",
                                style = MaterialTheme.typography.labelSmall.copy(
                                    fontSize = 7.sp  // 从8sp缩小到7sp
                                ),
                                color = Color(0xFF3182CE),
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 5.dp, vertical = 2.dp)  // 从6dp缩小到5dp
                            )
                        }
                    }
                    
                    // 如果有更多标签，显示数量
                    val remainingCount = (taskTags.size - 1).coerceAtLeast(0)
                    if (remainingCount > 0) {
                        Surface(
                            shape = RoundedCornerShape(10.dp),
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            modifier = Modifier.height(20.dp)
                        ) {
                            Text(
                                text = "+$remainingCount",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
            }
            } // Column 结束
        } // Box 结束
    } // Card 结束
}

/**
 * 感想空状态
 */
@Composable
fun ReflectionEmptyState(
    onCreateClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp)
    ) {
        Text(
            text = "📝",
            fontSize = 32.sp
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "还没有感想记录",
            fontSize = 14.sp,
            color = TextSecondary,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "记录今天的想法吧",
            fontSize = 12.sp,
            color = TextSecondary.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Button(
            onClick = onCreateClick,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
            ),
            shape = RoundedCornerShape(20.dp),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "新增感想",
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "写感想",
                fontSize = 12.sp
            )
        }
    }
} 