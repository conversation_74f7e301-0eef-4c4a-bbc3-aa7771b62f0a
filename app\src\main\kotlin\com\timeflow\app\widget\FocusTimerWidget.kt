package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import com.timeflow.app.ui.timetracking.TimerState
/**
 * 专注计时器小组件 - 显示正在专注的任务和计时状态
 * 参照Calflow app的设计风格，使用时间追踪页面的颜色方案
 */
class FocusTimerWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle?
    ) {
        updateAppWidget(context, appWidgetManager, appWidgetId)
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        try {
            val options = appWidgetManager.getAppWidgetOptions(appWidgetId)
            val minWidth = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH, 250)
            val minHeight = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT, 150)
            
            // 根据尺寸选择布局 - 支持更多尺寸选项
            val layoutId = when {
                // 小尺寸 (1x1)
                minWidth < 180 || minHeight < 110 -> R.layout.widget_focus_timer_small
                // 中等尺寸 (2x2)
                minWidth < 350 || minHeight < 250 -> R.layout.widget_focus_timer
                // 大尺寸 (4x2)
                else -> R.layout.widget_focus_timer_large
            }

            val views = RemoteViews(context.packageName, layoutId)

            // 获取计时器状态
            val timerState = getTimerState(context)

            // 根据布局设置内容
            when (layoutId) {
                R.layout.widget_focus_timer_small -> setupSmallWidget(context, views, timerState)
                R.layout.widget_focus_timer -> setupMediumWidget(context, views, timerState)
                R.layout.widget_focus_timer_large -> setupLargeWidget(context, views, timerState)
                else -> setupMediumWidget(context, views, timerState) // 默认使用中等尺寸
            }
            
            // 设置点击事件
            setupClickEvents(context, views)
            
            appWidgetManager.updateAppWidget(appWidgetId, views)
        } catch (e: Exception) {
            // 错误处理：显示默认状态
            val views = RemoteViews(context.packageName, R.layout.widget_quick_timer_small)
            setupDefaultWidget(context, views)
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }

    private fun getTimerState(context: Context): TimerWidgetState {
        // 使用与TimeTrackingViewModel相同的SharedPreferences
        val prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE)

        // 读取计时器状态
        val timerStateStr = prefs.getString(PREF_TIMER_STATE, "IDLE") ?: "IDLE"
        val isRunning = timerStateStr == "RUNNING"
        val taskId = prefs.getString(PREF_CURRENT_TASK_ID, null)
        val taskName = prefs.getString(PREF_CURRENT_TASK_NAME, "专注任务") ?: "专注任务"
        val startTime = prefs.getLong(PREF_TIMER_START_TIME, 0L)
        val elapsedTime = prefs.getLong(PREF_ELAPSED_TIME, 0L)
        val timerType = prefs.getString(PREF_TIMER_TYPE, "NORMAL") ?: "NORMAL"
        val pomodoroCount = prefs.getInt(PREF_POMODORO_COUNT, 0)
        val pomodoroTimeRemaining = prefs.getLong(PREF_POMODORO_TIME_REMAINING, 25 * 60L)
        val isBreakTime = prefs.getBoolean(PREF_IS_BREAK_TIME, false)

        // 🔧 修改：改进时间计算逻辑
        val currentTime = System.currentTimeMillis()
        val displayTime = when (timerType) {
            "POMODORO" -> {
                if (isBreakTime) {
                    // 休息时间显示剩余休息时间
                    if (isRunning && startTime > 0) {
                        // 实时计算剩余时间
                        val elapsed = (currentTime - startTime) / 1000
                        maxOf(0L, pomodoroTimeRemaining - elapsed)
                    } else {
                        pomodoroTimeRemaining
                    }
                } else {
                    // 工作时间显示剩余工作时间
                    if (isRunning && startTime > 0) {
                        // 实时计算剩余时间
                        val elapsed = (currentTime - startTime) / 1000
                        maxOf(0L, pomodoroTimeRemaining - elapsed)
                    } else {
                        pomodoroTimeRemaining
                    }
                }
            }
            else -> {
                // 正计时模式显示已用时间
                if (isRunning && startTime > 0) {
                    // 🔧 修复：使用更精确的时间计算，考虑到主应用的更新频率
                    val timeSinceStart = (currentTime - startTime) / 1000

                    // 使用保存的elapsedTime作为基准，加上从最后一次保存到现在的时间差
                    // 但限制额外时间不超过5秒（主应用的保存间隔）
                    val additionalTime = minOf(timeSinceStart - elapsedTime, 5L)
                    elapsedTime + maxOf(additionalTime, 0L)
                } else {
                    elapsedTime
                }
            }
        }

        return TimerWidgetState(
            isRunning = isRunning,
            taskName = taskName,
            taskId = taskId,
            elapsedTime = elapsedTime,
            displayTime = displayTime,
            timerType = timerType,
            pomodoroCount = pomodoroCount,
            pomodoroGoal = 4,
            isBreakTime = isBreakTime
        )
    }

    private fun setupSmallWidget(context: Context, views: RemoteViews, state: TimerWidgetState) {
        // 设置任务名称
        views.setTextViewText(R.id.widget_task_name, state.taskName)
        
        // 设置计时器显示
        val timeText = formatTime(state.elapsedTime)
        views.setTextViewText(R.id.widget_timer_display, timeText)
        
        // 设置状态文本
        val statusText = if (state.isRunning) "正计时" else "已暂停"
        views.setTextViewText(R.id.widget_timer_status, statusText)
        
        // 设置播放/暂停按钮图标
        val playPauseIcon = if (state.isRunning) R.drawable.ic_pause else R.drawable.ic_play_arrow
        views.setImageViewResource(R.id.widget_play_pause_button, playPauseIcon)
    }

    private fun setupMediumWidget(context: Context, views: RemoteViews, state: TimerWidgetState) {
        // 设置任务名称
        views.setTextViewText(R.id.widget_task_name, state.taskName)

        // 设置计时器显示
        val timeText = formatTime(state.elapsedTime)
        views.setTextViewText(R.id.widget_timer_display, timeText)
        
        // 设置状态
        val statusText = if (state.isRunning) "专注中" else "已暂停"
        views.setTextViewText(R.id.widget_status_text, statusText)
        
        // 设置计时器类型
        views.setTextViewText(R.id.widget_timer_type, "正计时")
        
        // 设置番茄钟计数
        views.setTextViewText(R.id.widget_pomodoro_count, "${state.pomodoroCount}/${state.pomodoroGoal}")
        
        // 设置播放/暂停按钮
        val playPauseIcon = if (state.isRunning) R.drawable.ic_pause else R.drawable.ic_play_arrow
        val playPauseText = if (state.isRunning) "暂停" else "继续"
        views.setImageViewResource(R.id.widget_play_pause_icon, playPauseIcon)
        views.setTextViewText(R.id.widget_play_pause_text, playPauseText)
        
        // 设置状态指示器颜色
        val indicatorColor = if (state.isRunning) R.drawable.widget_circle_green else R.drawable.widget_circle_gray
        views.setImageViewResource(R.id.widget_status_indicator, indicatorColor)
        
        // 设置进度条（基于25分钟番茄钟）
        val progress = ((state.elapsedTime % (25 * 60)) * 100 / (25 * 60)).toInt()
        views.setProgressBar(R.id.widget_progress_bar, 100, progress, false)
    }

    private fun setupDefaultWidget(context: Context, views: RemoteViews) {
        views.setTextViewText(R.id.widget_task_name, "专注任务")
        views.setTextViewText(R.id.widget_timer_display, "00:00")
        views.setTextViewText(R.id.widget_timer_status, "未开始")
        views.setImageViewResource(R.id.widget_play_pause_button, R.drawable.ic_play_arrow)
    }

    private fun setupClickEvents(context: Context, views: RemoteViews) {
        // 播放/暂停按钮
        val playPauseIntent = Intent(context, FocusTimerWidget::class.java).apply {
            action = ACTION_PLAY_PAUSE
        }
        val playPausePendingIntent = PendingIntent.getBroadcast(
            context, 1, playPauseIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_play_pause_button, playPausePendingIntent)
        
        // 停止按钮
        try {
            val stopIntent = Intent(context, FocusTimerWidget::class.java).apply {
                action = ACTION_STOP
            }
            val stopPendingIntent = PendingIntent.getBroadcast(
                context, 2, stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_stop_button, stopPendingIntent)
        } catch (e: Exception) {
            // 小尺寸布局可能没有停止按钮
        }
        
        // 整个小组件点击跳转到时间追踪页面
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
        }
        val mainPendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, mainPendingIntent)
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)

        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                // 直接在小组件中处理暂停/恢复操作
                handlePlayPauseAction(context)
            }
            ACTION_STOP -> {
                // 直接在小组件中处理停止操作
                handleStopAction(context)
            }
        }
    }

    private fun formatTime(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60
        
        return if (hours > 0) {
            String.format("%d:%02d:%02d", hours, minutes, secs)
        } else {
            String.format("%02d:%02d", minutes, secs)
        }
    }

    /**
     * 处理播放/暂停按钮点击
     */
    private fun handlePlayPauseAction(context: Context) {
        try {
            val prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE)
            val currentState = prefs.getString(PREF_TIMER_STATE, TimerState.IDLE.name)

            Log.d(TAG, "🎯 处理播放/暂停操作，当前状态: $currentState")

            when (currentState) {
                TimerState.RUNNING.name -> {
                    // 当前正在运行，执行暂停
                    pauseTimerInWidget(context, prefs)
                }
                TimerState.PAUSED.name -> {
                    // 当前已暂停，执行恢复
                    resumeTimerInWidget(context, prefs)
                }
                TimerState.IDLE.name -> {
                    // 当前空闲，跳转到app开始新的计时
                    startNewTimerInApp(context)
                }
                else -> {
                    Log.w(TAG, "未知的计时器状态: $currentState")
                    startNewTimerInApp(context)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理播放/暂停操作失败", e)
            // 出错时跳转到app
            startNewTimerInApp(context)
        }
    }

    /**
     * 处理停止按钮点击
     */
    private fun handleStopAction(context: Context) {
        try {
            val prefs = context.getSharedPreferences(SHARED_PREFS_NAME, Context.MODE_PRIVATE)

            Log.d(TAG, "🛑 处理停止操作")

            // 清理所有计时器状态
            with(prefs.edit()) {
                remove(PREF_TIMER_STATE)
                remove(PREF_TIMER_START_TIME)
                remove(PREF_ELAPSED_TIME)
                remove(PREF_CURRENT_TASK_ID)
                remove(PREF_CURRENT_TASK_NAME)
                remove(PREF_TIMER_TYPE)
                remove(PREF_POMODORO_COUNT)
                remove(PREF_POMODORO_TIME_REMAINING)
                remove(PREF_IS_BREAK_TIME)
                remove(PREF_CURRENT_SESSION_ID)
                apply()
            }

            Log.d(TAG, "✅ 计时器已停止，状态已清理")

            // 更新所有计时器小组件
            TimerWidgetUpdater.updateAllTimerWidgets(context)

        } catch (e: Exception) {
            Log.e(TAG, "处理停止操作失败", e)
        }
    }

    /**
     * 在小组件中暂停计时器
     */
    private fun pauseTimerInWidget(context: Context, prefs: SharedPreferences) {
        try {
            val startTime = prefs.getLong(PREF_TIMER_START_TIME, 0L)
            val savedElapsedTime = prefs.getLong(PREF_ELAPSED_TIME, 0L)

            if (startTime > 0) {
                // 计算当前总的经过时间
                val currentTime = System.currentTimeMillis()
                val timeSinceStart = (currentTime - startTime) / 1000
                val totalElapsedTime = savedElapsedTime + timeSinceStart

                // 保存暂停状态
                with(prefs.edit()) {
                    putString(PREF_TIMER_STATE, TimerState.PAUSED.name)
                    putLong(PREF_ELAPSED_TIME, totalElapsedTime)
                    putLong(PREF_TIMER_START_TIME, 0L) // 清除开始时间
                    apply()
                }

                Log.d(TAG, "⏸️ 小组件暂停计时器，总时长: ${totalElapsedTime}秒")
            } else {
                // 没有开始时间，直接设置为暂停状态
                with(prefs.edit()) {
                    putString(PREF_TIMER_STATE, TimerState.PAUSED.name)
                    apply()
                }

                Log.d(TAG, "⏸️ 小组件暂停计时器（无开始时间）")
            }

            // 更新所有计时器小组件
            TimerWidgetUpdater.updateAllTimerWidgets(context)

        } catch (e: Exception) {
            Log.e(TAG, "小组件暂停计时器失败", e)
        }
    }

    /**
     * 在小组件中恢复计时器
     */
    private fun resumeTimerInWidget(context: Context, prefs: SharedPreferences) {
        try {
            val elapsedTime = prefs.getLong(PREF_ELAPSED_TIME, 0L)

            // 重新计算开始时间，确保与主应用计算一致
            val newStartTime = System.currentTimeMillis() - (elapsedTime * 1000)

            // 保存运行状态
            with(prefs.edit()) {
                putString(PREF_TIMER_STATE, TimerState.RUNNING.name)
                putLong(PREF_TIMER_START_TIME, newStartTime)
                apply()
            }

            Log.d(TAG, "▶️ 小组件恢复计时器，已用时长: ${elapsedTime}秒")

            // 更新所有计时器小组件
            TimerWidgetUpdater.updateAllTimerWidgets(context)

        } catch (e: Exception) {
            Log.e(TAG, "小组件恢复计时器失败", e)
        }
    }

    /**
     * 跳转到app开始新的计时
     */
    private fun startNewTimerInApp(context: Context) {
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "timer")
            putExtra("action", "start_new_timer")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(mainIntent)
        Log.d(TAG, "🚀 跳转到app开始新计时")
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
    }

    companion object {
        const val ACTION_PLAY_PAUSE = "com.timeflow.app.widget.ACTION_PLAY_PAUSE"
        const val ACTION_STOP = "com.timeflow.app.widget.ACTION_STOP"
        private const val TAG = "FocusTimerWidget"
        private const val SHARED_PREFS_NAME = "time_tracking_prefs"

        // SharedPreferences键名（与TimeTrackingViewModel保持一致）
        private const val PREF_TIMER_STATE = "timer_state"
        private const val PREF_TIMER_START_TIME = "timer_start_time"
        private const val PREF_ELAPSED_TIME = "elapsed_time"
        private const val PREF_CURRENT_TASK_ID = "current_task_id"
        private const val PREF_CURRENT_TASK_NAME = "current_task_name"
        private const val PREF_TIMER_TYPE = "timer_type"
        private const val PREF_POMODORO_COUNT = "pomodoro_count"
        private const val PREF_POMODORO_TIME_REMAINING = "pomodoro_time_remaining"
        private const val PREF_IS_BREAK_TIME = "is_break_time"
        private const val PREF_CURRENT_SESSION_ID = "current_session_id"
    }

    /**
     * 设置大尺寸小组件 (4x2)
     */
    private fun setupLargeWidget(context: Context, views: RemoteViews, state: TimerWidgetState) {
        try {
            // 设置任务名称
            views.setTextViewText(R.id.widget_task_name, state.taskName)

            // 设置计时器显示
            val timeText = formatTime(state.elapsedTime)
            views.setTextViewText(R.id.widget_timer_display, timeText)

            // 设置状态指示器
            val statusDrawable = when {
                state.isRunning -> R.drawable.widget_status_running
                state.elapsedTime > 0 -> R.drawable.widget_status_paused
                else -> R.drawable.widget_status_idle
            }
            views.setImageViewResource(R.id.widget_status_indicator, statusDrawable)

            // 设置播放/暂停按钮图标
            val playPauseIcon = if (state.isRunning) R.drawable.ic_pause else R.drawable.ic_play_arrow
            views.setImageViewResource(R.id.widget_play_pause_button, playPauseIcon)

            // 设置停止按钮
            views.setImageViewResource(R.id.widget_stop_button, R.drawable.ic_stop)

            // 设置进度条
            try {
                val targetMinutes = if (state.timerType == "POMODORO") 25 else 60
                val targetSeconds = targetMinutes * 60
                val progress = if (targetSeconds > 0) {
                    ((state.elapsedTime.toFloat() / targetSeconds) * 100).toInt().coerceAtMost(100)
                } else 0
                views.setProgressBar(R.id.widget_progress_bar, 100, progress, false)
            } catch (e: Exception) {
                Log.w(TAG, "设置进度条失败", e)
            }

            // 设置今日统计
            try {
                views.setTextViewText(R.id.widget_session_count, state.pomodoroCount.toString())

                // 计算今日总时长
                val totalMinutes = state.elapsedTime / 60
                val hours = totalMinutes / 60
                val minutes = totalMinutes % 60
                val totalTimeText = if (hours > 0) {
                    "${hours}h ${minutes}m"
                } else {
                    "${minutes}m"
                }
                views.setTextViewText(R.id.widget_today_total, totalTimeText)
            } catch (e: Exception) {
                Log.w(TAG, "设置统计信息失败", e)
            }

        } catch (e: Exception) {
            Log.e(TAG, "设置大尺寸小组件失败", e)
        }
    }
}


