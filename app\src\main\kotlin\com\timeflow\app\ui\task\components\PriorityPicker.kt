package com.timeflow.app.ui.task.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.ui.theme.PriorityColors

/**
 * iOS风格优先级选择器
 *
 * @param selectedPriority 当前选中的优先级 (0-3)
 * @param onPrioritySelected 优先级选择回调
 * @param modifier 修饰符
 */
@Composable
fun PriorityPicker(
    selectedPriority: Int,
    onPrioritySelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    val haptics = LocalHapticFeedback.current
    val rotationState by animateFloatAsState(
        targetValue = if (expanded) 180f else 0f,
        label = "rotation"
    )
    
    Column(modifier = modifier) {
        // 选择器标题
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .clickable {
                    expanded = !expanded
                    haptics.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                },
            color = MaterialTheme.colorScheme.surfaceVariant,
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 左边：优先级图标和文本
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 优先级图标
                    Icon(
                        imageVector = Icons.Default.Flag,
                        contentDescription = null,
                        tint = priorityColor(selectedPriority),
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    // 优先级文本
                    Text(
                        text = "优先级",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 优先级值
                    Text(
                        text = priorityText(selectedPriority),
                        style = MaterialTheme.typography.bodyMedium,
                        color = priorityColor(selectedPriority)
                    )
                }
                
                // 右边：展开/收起图标
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = if (expanded) "收起" else "展开",
                    modifier = Modifier.rotate(rotationState)
                )
            }
        }
        
        // 展开的优先级选项
        AnimatedVisibility(
            visible = expanded,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                shape = RoundedCornerShape(12.dp),
                color = MaterialTheme.colorScheme.surfaceVariant
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 优先级选项列表
                    PriorityOption(
                        priority = 3,
                        label = "紧急",
                        selected = selectedPriority == 3,
                        onClick = {
                            onPrioritySelected(3)
                            haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            expanded = false
                        }
                    )
                    
                    PriorityOption(
                        priority = 2,
                        label = "高",
                        selected = selectedPriority == 2,
                        onClick = {
                            onPrioritySelected(2)
                            haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            expanded = false
                        }
                    )
                    
                    PriorityOption(
                        priority = 1,
                        label = "中",
                        selected = selectedPriority == 1,
                        onClick = {
                            onPrioritySelected(1)
                            haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            expanded = false
                        }
                    )
                    
                    PriorityOption(
                        priority = 0,
                        label = "低",
                        selected = selectedPriority == 0,
                        onClick = {
                            onPrioritySelected(0)
                            haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}

/**
 * 优先级选项
 */
@Composable
private fun PriorityOption(
    priority: Int,
    label: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    val color = priorityColor(priority)
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(if (selected) color.copy(alpha = 0.1f) else Color.Transparent)
            .clickable(onClick = onClick)
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 优先级标识圆点
        Box(
            modifier = Modifier
                .size(16.dp)
                .clip(CircleShape)
                .background(color)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 优先级文本
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            color = if (selected) color else MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 选中状态指示器
        if (selected) {
            RadioButton(
                selected = true,
                onClick = null,
                colors = RadioButtonDefaults.colors(
                    selectedColor = color
                )
            )
        }
    }
}

/**
 * 获取优先级颜色
 */
@Composable
private fun priorityColor(priority: Int): Color {
    return PriorityColors.fromPriority(priority)
}

/**
 * 获取优先级文本
 */
private fun priorityText(priority: Int): String {
    return when (priority) {
        3 -> "紧急"
        2 -> "高"
        1 -> "中"
        0 -> "低"
        else -> "无"
    }
}