package com.timeflow.app.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.utils.DatabaseBackupManager
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

/**
 * 备份恢复ViewModel
 * 用于管理数据库备份和恢复操作
 */
@HiltViewModel
class BackupRestoreViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : ViewModel() {
    
    private val backupManager = DatabaseBackupManager(context)
    
    // UI状态
    private val _uiState = MutableStateFlow(BackupRestoreUiState())
    val uiState: StateFlow<BackupRestoreUiState> = _uiState.asStateFlow()
    
    /**
     * 获取备份文件列表
     */
    fun getBackupFiles() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            try {
                val backupFiles = backupManager.getBackupFiles()
                _uiState.value = _uiState.value.copy(
                    backupFiles = backupFiles,
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "获取备份文件失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 恢复备份
     */
    fun restoreBackup(backupFile: File) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            try {
                val success = backupManager.restoreBackup(backupFile.name)
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        restoreCompleted = true
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "恢复备份失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "恢复备份失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 格式化日期
     */
    fun formatDate(timestamp: Long): String {
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return formatter.format(date)
    }
}

/**
 * 备份恢复UI状态
 */
data class BackupRestoreUiState(
    val isLoading: Boolean = false,
    val backupFiles: List<File> = emptyList(),
    val error: String? = null,
    val restoreCompleted: Boolean = false
)