package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.KanbanBoardDao
import com.timeflow.app.data.dao.KanbanColumnDao
import com.timeflow.app.data.entity.KanbanBoard
import com.timeflow.app.data.entity.KanbanColumn
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 看板仓库 - 负责统一处理看板数据
 */
@Singleton
class KanbanRepository @Inject constructor(
    private val kanbanBoardDao: KanbanBoardDao,
    private val kanbanColumnDao: KanbanColumnDao
) {
    // 看板板块相关操作
    val allBoards: Flow<List<KanbanBoard>> = kanbanBoardDao.observeAllBoards()
    
    suspend fun getBoardById(boardId: String): KanbanBoard? = kanbanBoardDao.getBoardById(boardId)
    
    suspend fun insertBoard(board: KanbanBoard) = kanbanBoardDao.insert(board)
    
    suspend fun updateBoard(board: KanbanBoard) = kanbanBoardDao.update(board)
    
    suspend fun deleteBoard(board: KanbanBoard) = kanbanBoardDao.delete(board)
    
    // 看板列相关操作
    fun getColumnsForBoard(boardId: String): Flow<List<KanbanColumn>> = 
        kanbanColumnDao.observeColumnsByBoardId(boardId)
    
    suspend fun getColumnById(columnId: String): KanbanColumn? = 
        kanbanColumnDao.getColumnById(columnId)
    
    suspend fun insertColumn(column: KanbanColumn) = 
        kanbanColumnDao.insert(column)
    
    suspend fun updateColumn(column: KanbanColumn) = 
        kanbanColumnDao.update(column)
    
    suspend fun deleteColumn(column: KanbanColumn) = 
        kanbanColumnDao.delete(column)
    
    suspend fun updateColumnsPositions(columns: List<KanbanColumn>) = 
        columns.forEach { kanbanColumnDao.update(it) }
} 