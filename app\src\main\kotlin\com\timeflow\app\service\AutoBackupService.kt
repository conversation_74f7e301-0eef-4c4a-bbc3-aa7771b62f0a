package com.timeflow.app.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import com.timeflow.app.utils.DatabaseBackupManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import javax.inject.Inject

/**
 * 自动备份服务
 * 用于在应用启动或后台运行时执行自动备份
 */
@AndroidEntryPoint
class AutoBackupService : Service() {
    
    @Inject
    lateinit var backupManager: DatabaseBackupManager
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "AutoBackupService"
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "自动备份服务已创建")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "自动备份服务已启动")
        
        // 检查并执行自动备份
        checkAndPerformBackup()
        
        // 服务完成后自动停止
        return START_NOT_STICKY
    }
    
    private fun checkAndPerformBackup() {
        serviceScope.launch {
            try {
                // 检查是否需要自动备份
                if (backupManager.shouldAutoBackup()) {
                    Log.i(TAG, "开始执行自动备份...")
                    val success = backupManager.backupDatabase()
                    if (success) {
                        Log.i(TAG, "自动备份成功完成")
                    } else {
                        Log.e(TAG, "自动备份失败")
                    }
                } else {
                    Log.d(TAG, "当前不需要执行自动备份")
                }
            } catch (e: Exception) {
                Log.e(TAG, "自动备份过程中发生错误", e)
            } finally {
                // 备份完成后停止服务
                stopSelf()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 取消所有协程
        serviceScope.cancel()
        Log.d(TAG, "自动备份服务已销毁")
    }
}