package com.timeflow.app.ui.timetracking

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import androidx.compose.foundation.BorderStroke

/**
 * 唱片式计时器UI组件
 * 实现类似唱片转动动画的计时器，类似网易云音乐播放界面
 */
@Composable
fun VinylStyleTimer(
    elapsedTime: Long,
    timerState: TimerState,
    primaryColor: Color,
    progress: Float = 0f,
    showTime: Boolean = false,
    timeText: String = "",
    modifier: Modifier = Modifier
) {
    // 更新颜色，与图像保持一致
    val vinylColor = Color.White.copy(alpha = 0.8f) // 透明白色，接近图2的透明唱片
    val labelColor = Color(0xFF616161) // 深灰色唱片标签，接近图2
    val accentColor = Color(0xFFFF8FB3) // 粉色强调色
    
    // 动画效果
    val rotation = remember { Animatable(0f) }
    val armRotation = remember { Animatable(0f) }
    
    // 烟雾效果动画 - 当计时器运行时启用
    val smokeAlpha = remember { Animatable(0f) }
    val smokeBlur = remember { Animatable(0f) }
    val smokeScale = remember { Animatable(1f) }
    
    // 当计时器运行时，添加旋转动画
    LaunchedEffect(timerState) {
        if (timerState == TimerState.RUNNING) {
            // 主唱片缓慢旋转动画
            rotation.animateTo(
                targetValue = rotation.value + 360f,
                animationSpec = infiniteRepeatable(
                    animation = tween(60000, easing = LinearEasing),
                    repeatMode = RepeatMode.Restart
                )
            )
        }
    }
    
    // 唱臂动画
    LaunchedEffect(timerState) {
        if (timerState == TimerState.RUNNING) {
            // 唱臂动画 - 仅在启动时移动到唱片位置
            armRotation.animateTo(
                targetValue = -25f,
                animationSpec = tween(1000, easing = EaseOutQuad)
            )
        } else {
            // 停止时唱臂回到原位
            armRotation.animateTo(
                targetValue = -5f,
                animationSpec = tween(800, easing = EaseInOutQuad)
            )
        }
    }
    
    // 烟雾效果动画
    LaunchedEffect(timerState) {
        if (timerState == TimerState.RUNNING) {
            // 启动烟雾效果
            smokeAlpha.animateTo(
                targetValue = 0.7f,
                animationSpec = tween(1500, easing = EaseInOutCubic)
            )
            smokeBlur.animateTo(
                targetValue = 12f,
                animationSpec = tween(1500, easing = EaseInOutCubic)
            )
            smokeScale.animateTo(
                targetValue = 1.1f,
                animationSpec = infiniteRepeatable(
                    animation = tween(3000, easing = EaseInOutSine),
                    repeatMode = RepeatMode.Reverse
                )
            )
        } else {
            // 停止烟雾效果
            smokeAlpha.animateTo(
                targetValue = 0f,
                animationSpec = tween(800, easing = EaseOutCubic)
            )
            smokeBlur.animateTo(
                targetValue = 0f,
                animationSpec = tween(800, easing = EaseOutCubic)
            )
            smokeScale.animateTo(
                targetValue = 1f,
                animationSpec = tween(800, easing = EaseOutCubic)
            )
        }
    }

    // 唱片主容器
    Box(modifier = modifier) {
        // 光晕效果 - 参考图2中的发光效果
        Canvas(
            modifier = Modifier
                .size(320.dp)
                .align(Alignment.Center)
                .blur(8.dp)
        ) {
            // 绘制外部光晕效果 - 使用粉色调#f2ebf0
            drawCircle(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFFF2EBF0).copy(alpha = 0.8f), // 浅粉色调内层
                        Color(0xFFF2EBF0).copy(alpha = 0.5f), // 浅粉色调中层
                        Color(0xFFF2EBF0).copy(alpha = 0f)    // 透明外层
                    ),
                    center = center,
                    radius = size.minDimension / 1.8f
                ),
                radius = size.minDimension / 2,
                center = center
            )
        }
        
        // 半透明唱片外层 - 更接近图2的透明唱片效果
        Box(
            modifier = Modifier
                .size(320.dp)
                .align(Alignment.Center)
                .shadow(
                    elevation = 4.dp,
                    shape = CircleShape,
                    ambientColor = Color.White.copy(alpha = 0.6f),
                    spotColor = Color.White.copy(alpha = 0.4f)
                )
                .clip(CircleShape)
                .background(Color.Transparent) // 完全透明背景
        ) {
            // 烟雾效果层 - 当计时器运行时显示
            if (smokeAlpha.value > 0) {
                Canvas(
                    modifier = Modifier
                        .size(340.dp)
                        .align(Alignment.Center)
                        .blur(smokeBlur.value.dp)
                        .graphicsLayer {
                            scaleX = smokeScale.value
                            scaleY = smokeScale.value
                            alpha = smokeAlpha.value
                        }
                ) {
                    // 外围烟雾效果
                    drawCircle(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFFFC7D8).copy(alpha = 0.2f), // 内层淡粉色
                                Color(0xFFFFADD1).copy(alpha = 0.15f), // 中层粉色
                                Color(0xFFFFADD1).copy(alpha = 0.0f)  // 外层完全透明
                            ),
                            center = center,
                            radius = size.minDimension * 0.6f
                        ),
                        radius = size.minDimension * 0.55f,
                        center = center
                    )
                }
            }
            
            // 唱片主体 - 使用更高透明度效果接近图2的透明唱片
            Box(
                modifier = Modifier
                    .size(300.dp)
                    .align(Alignment.Center)
                    .clip(CircleShape)
                    .background(Color.White.copy(alpha = 0.15f)) // 几乎透明的唱片本体
                    .rotate(rotation.value),
                contentAlignment = Alignment.Center
            ) {
                // 唱片上的同心圆纹路 - 参考图2中的真实唱片纹路
                Canvas(modifier = Modifier.fillMaxSize()) {
                    val center = Offset(size.width / 2, size.height / 2)
                    
                    // 唱片边缘
                    drawCircle(
                        color = Color.White.copy(alpha = 0.4f),
                        radius = size.minDimension * 0.48f,
                        center = center,
                        style = Stroke(width = 1.5f)
                    )
                    
                    // 绘制主要同心圆 - 更多的细同心圆，更接近真实唱片
                    for (i in 0 until 15) {
                        val radiusRatio = 0.12f + (i * 0.025f)
                        val radius = size.minDimension * radiusRatio
                        
                        // 唱片沟纹越往外越密集
                        val strokeWidth = 0.5f
                        val alpha = if (i < 5) 0.35f else if (i < 10) 0.3f else 0.25f
                        
                        drawCircle(
                            color = Color.White.copy(alpha = alpha),
                            radius = radius,
                            center = center,
                            style = Stroke(width = strokeWidth)
                        )
                    }
                    
                    // 主要唱片分区圆环 - 模拟图2的唱片主分区
                    for (i in 0 until 4) {
                        val radius = size.minDimension * (0.25f + i * 0.05f)
                        drawCircle(
                            color = Color.White.copy(alpha = 0.5f),
                            radius = radius,
                            center = center,
                            style = Stroke(width = 1f)
                        )
                    }
                }
                
                // 中心黑色唱片标签区域 - 更接近图2中的标签
                Box(
                    modifier = Modifier
                        .size(110.dp)
                        .clip(CircleShape)
                        .background(labelColor) // 使用深灰色
                        .border(
                            width = 1.dp,
                            color = Color.White.copy(alpha = 0.4f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    // 中心标签内容 - 参照图2中的标签内容和布局
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center,
                        modifier = Modifier.rotate(-rotation.value) // 使文字保持不动
                    ) {
                        // 上方的标志/文字
                        Text(
                            text = "THE",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color.White.copy(alpha = 0.95f),
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 0.5.sp
                        )
                        
                        Text(
                            text = "LUMINERS",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color.White.copy(alpha = 0.95f),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 1.2.sp
                        )
                        
                        Spacer(modifier = Modifier.height(5.dp))
                        
                        // 中心小圆点/唱片孔
                        Box(
                            modifier = Modifier
                                .size(14.dp)
                                .clip(CircleShape)
                                .background(Color.White.copy(alpha = 0.35f))
                                .border(
                                    width = 1.dp,
                                    color = Color.White.copy(alpha = 0.7f),
                                    shape = CircleShape
                                )
                        )
                        
                        Spacer(modifier = Modifier.height(5.dp))
                        
                        // 时间显示
                        if (showTime) {
                            // 显示番茄钟剩余时间
                            Text(
                                text = timeText,
                                style = MaterialTheme.typography.titleLarge,
                                fontSize = 22.sp,
                                color = Color.White,
                                fontWeight = FontWeight.SemiBold
                            )
                        } else {
                            // 显示累计时间
                            val formattedTime = formatElapsedTime(elapsedTime)
                            val parts = formattedTime.split(":")
                            
                            Text(
                                text = parts[1] + ":" + parts[2], // MM:SS 格式显示
                                style = MaterialTheme.typography.titleLarge,
                                fontSize = 22.sp,
                                color = Color.White,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    }
                }
            }
            
            // 进度圆环
            if (progress > 0) {
                Canvas(modifier = Modifier
                    .size(320.dp)
                    .align(Alignment.Center)
                ) {
                    val strokeWidth = 1.5.dp.toPx()
                    val radius = (size.minDimension - strokeWidth) / 2
                    
                    // 进度环
                    val sweepAngle = progress * 360f
                    drawArc(
                        color = accentColor, // 使用粉色强调色作为进度圆环颜色
                        startAngle = -90f,
                        sweepAngle = sweepAngle,
                        useCenter = false,
                        style = Stroke(
                            width = strokeWidth,
                            cap = StrokeCap.Round
                        )
                    )
                }
            }
        }
        
        // 唱臂 - 修改为更白色的唱臂，参考图2
        Box(
            modifier = Modifier
                .width(100.dp)
                .height(2.5.dp)
                .offset(x = 100.dp, y = (-20).dp)
                .align(Alignment.CenterEnd)
                .rotate(armRotation.value)
                .clip(RoundedCornerShape(1.dp))
                .background(Color.White.copy(alpha = 0.95f)),
            contentAlignment = Alignment.CenterEnd
        ) {
            // 唱针
            Box(
                modifier = Modifier
                    .size(5.dp, 10.dp)
                    .offset(x = 2.dp)
                    .clip(RoundedCornerShape(1.dp))
                    .background(Color.White)
            )
        }
    }
}

/**
 * 任务卡片组件
 */
@Composable
fun TaskCard(
    title: String,
    description: String,
    icon: @Composable () -> Unit,
    isActive: Boolean = false,
    onClick: () -> Unit = {}
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        shape = MaterialTheme.shapes.medium,
        color = if (isActive) 
            Color(0x22000000)
        else 
            Color(0x11000000),
        border = if (isActive) 
            BorderStroke(1.dp, Color.Black.copy(alpha = 0.1f)) 
        else null,
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(
                        if (isActive)
                            Color(0x22000000)
                        else
                            Color(0x11000000)
                    ),
                contentAlignment = Alignment.Center
            ) {
                icon()
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.Black,
                    fontWeight = if (isActive) FontWeight.SemiBold else FontWeight.Normal
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Black.copy(alpha = 0.6f)
                )
            }
            
            if (isActive) {
                Text(
                    text = "进行中",
                    style = MaterialTheme.typography.labelSmall,
                    color = Color.Black.copy(alpha = 0.8f)
                )
            }
        }
    }
}

/**
 * 玻璃卡片效果
 */
@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Surface(
        modifier = modifier.shadow(elevation = 3.dp, shape = MaterialTheme.shapes.large, spotColor = Color.Gray),
        shape = MaterialTheme.shapes.large,
        color = Color.White.copy(alpha = 0.85f),
        border = BorderStroke(1.dp, Color.Gray.copy(alpha = 0.1f))
    ) {
        content()
    }
}

/**
 * 控制按钮 - 更接近网易云音乐的样式
 */
@Composable
fun ControlButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    color: Color = Color.Transparent,
    content: @Composable () -> Unit
) {
    Surface(
        modifier = modifier.size(44.dp),
        shape = CircleShape,
        color = color,
        onClick = onClick
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
} 