package com.timeflow.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.timeflow.app.data.entity.KanbanBoard
import kotlinx.coroutines.flow.Flow

/**
 * 看板DAO接口
 */
@Dao
interface KanbanBoardDao {
    
    /**
     * 插入新看板
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(board: KanbanBoard)
    
    /**
     * 插入多个看板
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(boards: List<KanbanBoard>)
    
    /**
     * 更新看板
     */
    @Update
    suspend fun update(board: KanbanBoard)
    
    /**
     * 删除看板
     */
    @Delete
    suspend fun delete(board: KanbanBoard)
    
    /**
     * 获取所有看板
     */
    @Query("SELECT * FROM kanban_boards ORDER BY created_at DESC")
    suspend fun getAllBoards(): List<KanbanBoard>
    
    /**
     * 观察所有看板变化
     */
    @Query("SELECT * FROM kanban_boards ORDER BY created_at DESC")
    fun observeAllBoards(): Flow<List<KanbanBoard>>
    
    /**
     * 根据ID获取看板
     */
    @Query("SELECT * FROM kanban_boards WHERE id = :boardId")
    suspend fun getBoardById(boardId: String): KanbanBoard?
    
    /**
     * 观察指定ID的看板变化
     */
    @Query("SELECT * FROM kanban_boards WHERE id = :boardId")
    fun observeBoardById(boardId: String): Flow<KanbanBoard?>
    
    /**
     * 获取看板总数
     */
    @Query("SELECT COUNT(*) FROM kanban_boards")
    suspend fun getBoardCount(): Int
} 