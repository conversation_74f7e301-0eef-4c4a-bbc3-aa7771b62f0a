<?xml version="1.0" encoding="utf-8"?>
<!-- 专注计时器小组件 - 参照Calflow设计风格 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_gradient_timetracking"
    android:padding="16dp">

    <!-- 顶部状态区域 -->
    <LinearLayout
        android:id="@+id/widget_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_alignParentTop="true">

        <!-- 专注状态指示 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="start|center_vertical">

            <View
                android:id="@+id/widget_status_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/widget_circle_green"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/widget_status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专注中"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:fontFamily="sans-serif-medium"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2" />

        </LinearLayout>

        <!-- 番茄钟计数 -->
        <TextView
            android:id="@+id/widget_pomodoro_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2/4"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:background="@drawable/widget_pill_background_white"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp" />

    </LinearLayout>

    <!-- 中央计时器区域 -->
    <LinearLayout
        android:id="@+id/widget_timer_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_centerInParent="true">

        <!-- 任务名称 -->
        <TextView
            android:id="@+id/widget_task_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="专注任务"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginBottom="12dp"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="3" />

        <!-- 计时器显示 -->
        <TextView
            android:id="@+id/widget_timer_display"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="23:45"
            android:textSize="48sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-condensed"
            android:layout_marginBottom="8dp"
            android:shadowColor="#80000000"
            android:shadowDx="3"
            android:shadowDy="3"
            android:shadowRadius="6" />

        <!-- 计时器类型 -->
        <TextView
            android:id="@+id/widget_timer_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="正计时"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:fontFamily="sans-serif-medium"
            android:layout_marginBottom="16dp"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/widget_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:progress="65"
            android:progressTint="@color/widget_timetracking_accent"
            android:progressBackgroundTint="@color/widget_text_tertiary"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

    <!-- 底部控制区域 -->
    <LinearLayout
        android:id="@+id/widget_controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <!-- 暂停/继续按钮 -->
        <LinearLayout
            android:id="@+id/widget_play_pause_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/widget_button_primary"
            android:paddingHorizontal="20dp"
            android:paddingVertical="10dp"
            android:layout_marginEnd="12dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/widget_play_pause_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_pause"
                android:tint="@android:color/white"
                android:layout_marginEnd="6dp" />

            <TextView
                android:id="@+id/widget_play_pause_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="暂停"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <!-- 停止按钮 -->
        <LinearLayout
            android:id="@+id/widget_stop_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/widget_button_secondary"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_stop"
                android:tint="@color/widget_text_primary" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
