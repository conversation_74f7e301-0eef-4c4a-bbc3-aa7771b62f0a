package com.timeflow.app.ui.screen.task

import androidx.compose.ui.graphics.Color
import java.util.UUID
import java.time.LocalDateTime
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
// 导入其他辅助类
import com.timeflow.app.ui.screen.task.SubTask

/**
 * 任务数据模型
 */
data class TaskData(
    val id: String,
    val title: String,
    val description: String = "",
    val dueDate: LocalDateTime? = null,
    val priority: Priority = Priority.MEDIUM,
    val status: String = "待办",
    val tags: List<String> = emptyList(),
    val daysLeft: Int = 0,
    val subTasks: List<SubTask> = emptyList(),
    val goalId: String? = null,
    val goalTitle: String? = null,
    // 🔧 循环任务相关字段
    val isRecurring: Boolean = false,
    val recurringPattern: String? = null
) {
    fun updateSubTask(updatedSubTask: SubTask): TaskData {
        return copy(
            subTasks = subTasks.map { 
                if (it.id == updatedSubTask.id) updatedSubTask else it 
            }
        )
    }

    fun deleteSubTask(subTaskId: String): TaskData {
        return copy(
            subTasks = subTasks.filter { it.id != subTaskId }
        )
    }
}

/**
 * 任务紧急程度枚举
 */
enum class TaskUrgency {
    CRITICAL, HIGH, MEDIUM, LOW
}

/**
 * 紧急度颜色定义
 */
object UrgencyColors {
    val Critical = Color(0xFFB74645) // 红色 - 紧急
    val High = Color(0xFFFF8247)     // 橙色 - 高优先级
    val Medium = Color(0xFFd9b335)    // 黄色 - 中优先级
    val Low = Color(0xFFA8C986)       // 绿色 - 低优先级
}

/**
 * 获取紧急度对应的颜色
 */
fun getUrgencyColor(urgency: TaskUrgency): Color {
    return when (urgency) {
        TaskUrgency.CRITICAL -> UrgencyColors.Critical
        TaskUrgency.HIGH -> UrgencyColors.High
        TaskUrgency.MEDIUM -> UrgencyColors.Medium
        TaskUrgency.LOW -> UrgencyColors.Low
    }
}

/**
 * 获取紧急度文本
 */
fun getUrgencyText(urgency: TaskUrgency): String {
    return when (urgency) {
        TaskUrgency.CRITICAL -> "紧急"
        TaskUrgency.HIGH -> "高优先级"
        TaskUrgency.MEDIUM -> "中等优先级"
        TaskUrgency.LOW -> "低优先级"
    }
}

/**
 * 格式化剩余时间显示
 */
fun formatTimeLeft(daysLeft: Int): String {
    return when {
        daysLeft == 0 -> "今天"
        daysLeft == 1 -> "明天"
        daysLeft > 0 -> "剩余${daysLeft}天"
        else -> "已过期"
    }
}

// 添加计算进度的工具函数
private fun calculateProgress(subTasks: List<SubTask>): Float {
    if (subTasks.isEmpty()) return 0f
    val completedTasks = subTasks.count { it.isCompleted }
    return completedTasks.toFloat() / subTasks.size
}

// 添加子任务相关的扩展函数
fun TaskData.addSubTask(subTask: SubTask): TaskData {
    return copy(
        subTasks = subTasks + subTask
    )
}

fun TaskData.updateSubTask(updatedSubTask: SubTask): TaskData {
    return copy(
        subTasks = subTasks.map { if (it.id == updatedSubTask.id) updatedSubTask else it }
    )
}

fun TaskData.deleteSubTask(subTaskId: String): TaskData {
    return copy(
        subTasks = subTasks.filter { it.id != subTaskId }
    )
}