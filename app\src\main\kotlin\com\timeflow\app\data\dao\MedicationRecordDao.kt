package com.timeflow.app.data.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.timeflow.app.data.entity.MedicationRecord
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

@Dao
interface MedicationRecordDao {
    /**
     * 插入一条用药记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: MedicationRecord)

    /**
     * 删除指定药物在某天的记录
     */
    @Query("DELETE FROM medication_records WHERE medicationId = :medicationId AND recordDate = :date")
    suspend fun deleteRecord(medicationId: String, date: LocalDate)

    /**
     * 获取指定药物在一段时间范围内的所有记录
     */
    @Query("SELECT * FROM medication_records WHERE medicationId = :medicationId AND recordDate BETWEEN :startDate AND :endDate")
    fun getRecordsForMedication(medicationId: String, startDate: LocalDate, endDate: LocalDate): Flow<List<MedicationRecord>>

    /**
     * 获取所有用药记录（用于备份或调试）
     */
    @Query("SELECT * FROM medication_records")
    fun getAllRecords(): Flow<List<MedicationRecord>>
} 