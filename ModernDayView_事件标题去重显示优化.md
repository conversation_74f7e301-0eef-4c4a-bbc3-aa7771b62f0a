# ModernDayView 事件标题去重显示优化

## 问题描述

在跨时间段事件的显示中，事件标题在每个涉及的小时都重复显示，造成视觉冗余：
- "完成工作报告"事件在10点和11点都显示完整标题
- "测试"事件在9点和10点都显示完整标题
- "分析关..."事件在多个小时都显示完整标题

用户期望：
- 跨时间段事件的标题只显示一次
- 标题尽量在**停留时间最长的小时**显示
- 其他时间段显示简化的延续标识

## 解决方案

### 1. 主要显示小时计算算法

新增`getPrimaryDisplayHourForEvent()`函数来计算事件应该在哪个小时显示主要标题：

```kotlin
private fun getPrimaryDisplayHourForEvent(event: CompactCalendarEvent): Int {
    val startHour = event.start.hour
    val endHour = event.end.hour
    
    if (startHour == endHour) {
        // 单小时事件，直接返回该小时
        return startHour
    }
    
    // 计算每个小时的停留时间
    var maxDuration = 0
    var primaryHour = startHour
    
    for (hour in startHour..endHour) {
        val duration = when {
            hour == startHour && hour == endHour -> {
                // 开始和结束在同一小时
                event.end.minute - event.start.minute
            }
            hour == startHour -> {
                // 开始小时：60 - 开始分钟
                60 - event.start.minute
            }
            hour == endHour -> {
                // 结束小时：结束分钟 - 0
                event.end.minute
            }
            else -> {
                // 中间完整小时：60分钟
                60
            }
        }
        
        if (duration > maxDuration) {
            maxDuration = duration
            primaryHour = hour
        }
    }
    
    return primaryHour
}
```

### 2. 三种显示模式

#### 模式1：主要显示小时（`shouldShowFullTitle = true`）
- 显示完整的事件标题和时间范围
- 显示完成状态勾选框（待办事件）
- 显示冲突警告标识

#### 模式2：延续小时（`isMultiHourEvent = true && shouldShowFullTitle = false`）
- 只显示省略号"···"表示事件延续
- 保持原有的颜色和边框样式
- 点击仍可查看事件详情

#### 模式3：单小时事件（向后兼容）
- 保持原有的完整显示逻辑
- 确保不影响现有的单小时事件显示

### 3. 实现效果示例

以"测试"事件（9:00-10:00）为例：

**优化前**：
- 9点时间段：显示"测试 9:00-10:00"
- 10点时间段：显示"测试 9:00-10:00"

**优化后**：
- 9点时间段：显示"测试 9:00-10:00"（主要显示，因为停留60分钟）
- 10点时间段：显示"···"（延续标识）

以"完成工作报告"事件（10:39-11:39）为例：

**优化前**：
- 10点时间段：显示"完成工作报告 10:39-11:39"
- 11点时间段：显示"完成工作报告 10:39-11:39"

**优化后**：
- 10点时间段：显示"···"（延续标识，停留21分钟）
- 11点时间段：显示"完成工作报告 10:39-11:39"（主要显示，停留39分钟）

## 技术实现细节

### 时间计算逻辑

```kotlin
// 判断是否应该在当前小时显示完整标题
val primaryDisplayHour = getPrimaryDisplayHourForEvent(event)
val shouldShowFullTitle = hour == primaryDisplayHour
val isMultiHourEvent = event.end.hour > event.start.hour
```

### 待办事件卡片（TodoEventCard）

```kotlin
if (shouldShowFullTitle) {
    // 主要显示：完整标题 + 时间范围 + 完成勾选
    Row { /* 完整布局 */ }
} else if (isMultiHourEvent) {
    // 延续显示：省略号居中
    Box(contentAlignment = Alignment.Center) {
        Text(text = "···", fontSize = 10.sp)
    }
} else {
    // 单小时事件：正常显示（向后兼容）
    Row { /* 原有布局 */ }
}
```

### 已完成事件卡片（CompletedEventCard）

```kotlin
if (shouldShowFullTitle) {
    // 主要显示：完整标题 + 时间范围
    Box(contentAlignment = Alignment.CenterStart) { /* 完整布局 */ }
} else if (isMultiHourEvent) {
    // 延续显示：省略号居中（白色，更大字体）
    Box(contentAlignment = Alignment.Center) {
        Text(text = "···", fontSize = 12.sp, color = Color.White)
    }
} else {
    // 单小时事件：正常显示（向后兼容）
    Box { /* 原有布局 */ }
}
```

## 优化效果

### 视觉改进
1. **减少重复信息**：跨时间段事件的标题不再重复显示
2. **视觉连贯性**：使用省略号"···"清晰表示事件的延续
3. **重点突出**：主要信息显示在停留时间最长的时间段

### 交互体验
1. **保持点击功能**：所有时间段的事件块都可以点击查看详情
2. **向后兼容**：单小时事件的显示逻辑完全不变
3. **冲突检测**：冲突标识仍在主要显示小时正常工作

### 性能优化
1. **计算高效**：主要显示小时的计算复杂度为O(n)，n为跨越的小时数
2. **内存友好**：不增加额外的状态存储
3. **渲染优化**：延续小时的渲染内容更简单

## 调试功能

增强的调试日志包含：
```
Log.d("ModernDayView", "  - 主要显示小时: $primaryDisplayHour")
Log.d("ModernDayView", "  - 当前显示完整标题: $shouldShowFullTitle")
```

## 边界情况处理

1. **相同停留时间**：优先选择较早的小时作为主要显示小时
2. **跨日事件**：当前只处理同一天内的跨小时事件
3. **分钟级事件**：小于15分钟的事件仍保持最小15%宽度显示 