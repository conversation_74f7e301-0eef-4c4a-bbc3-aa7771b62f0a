package com.timeflow.app.utils

import android.app.Activity
import android.graphics.drawable.RippleDrawable
import android.os.Build
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import timber.log.Timber

/**
 * RippleDrawable优化工具
 * 用于解决RippleDrawable在非硬件加速Canvas上的动画问题
 */
object RippleOptimizer {
    
    private const val TAG = "RippleOptimizer"
    
    /**
     * 为Activity的所有View启用硬件加速
     */
    fun enableHardwareAccelerationForAllViews(activity: Activity) {
        try {
            // 为Activity启用硬件加速
            activity.window.setFlags(
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 确保窗口级别的优化
            val window = activity.window
            
            // 设置绘制品质标志，优先性能
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 递归遍历所有视图并启用硬件加速
            enableHardwareAccelerationRecursively(activity.window.decorView)
            
            Timber.d("已为所有视图启用硬件加速")
        } catch (e: Exception) {
            Timber.e(e, "启用硬件加速失败")
        }
    }
    
    /**
     * 为单个视图启用硬件加速，解决RippleDrawable问题
     * 特别用于Compose中的View互操作
     */
    fun enableHardwareAcceleration(view: View) {
        try {
            // 启用硬件加速
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 如果是ViewGroup，递归处理子视图
            if (view is ViewGroup) {
                for (i in 0 until view.childCount) {
                    val child = view.getChildAt(i)
                    enableHardwareAcceleration(child)
                }
            }
            
            // 检查并优化背景如果是RippleDrawable
            if (view.background is RippleDrawable) {
                optimizeRippleDrawable(view)
            }
            
            // 强制刷新视图
            view.invalidate()
            
            Timber.d("已为视图启用硬件加速")
        } catch (e: Exception) {
            Timber.e(e, "为视图启用硬件加速失败: ${e.message}")
        }
    }
    
    /**
     * 递归为所有视图启用硬件加速
     */
    private fun enableHardwareAccelerationRecursively(view: View) {
        try {
            // 启用硬件加速
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 检查背景是否为RippleDrawable
            if (view.background is RippleDrawable) {
                optimizeRippleDrawable(view)
            }
            
            // 递归处理子视图
            if (view is ViewGroup) {
                for (i in 0 until view.childCount) {
                    enableHardwareAccelerationRecursively(view.getChildAt(i))
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "为视图启用硬件加速失败")
        }
    }
    
    /**
     * 优化RippleDrawable
     */
    private fun optimizeRippleDrawable(view: View) {
        try {
            val ripple = view.background as RippleDrawable
            
            // 确保启用硬件加速，这是处理RippleDrawable动画的关键
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 在API 23及以上版本可以使用非反射方式设置
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 设置RippleDrawable的颜色透明度，使其更轻量
                // 默认半透明黑色
                val rippleColor = 0x80000000.toInt()
                
                val alphaColor = (rippleColor and 0x00FFFFFF) or 0x40000000.toInt()
                
                ripple.setColor(android.content.res.ColorStateList.valueOf(alphaColor))
            }
            
            // 对于所有可点击的视图，强制使用硬件层渲染
            if (view.isClickable) {
                // 使用LAYER_TYPE_HARDWARE而不是SOFTWARE，确保硬件加速生效
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                
                // 对于Android 5.0以上，可以设置渲染类型
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.setRenderEffect(null)
                }
            }
            
            // 强制刷新视图
            view.invalidate()
        } catch (e: Exception) {
            Timber.e(e, "优化RippleDrawable失败")
        }
    }
    
    /**
     * 创建轻量级的交互源
     * 用于Compose组件中，减少不必要的重组和动画
     */
    @Composable
    fun rememberLightweightInteractionSource(): MutableInteractionSource {
        return remember { MutableInteractionSource() }
    }
} 