package com.timeflow.app.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.service.TaskReminderScheduler
import dagger.hilt.android.EntryPointAccessors
import javax.inject.Inject

/**
 * 逾期任务检查Worker
 * 定期检查是否有逾期任务，并发送相应的提醒通知
 */
class OverdueTaskCheckWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "OverdueTaskCheckWorker"
        const val WORK_NAME = "overdue_task_check"
    }
    
    override suspend fun doWork(): Result {
        return try {
            Log.d(TAG, "开始检查逾期任务...")
            
            // 由于Worker不能直接使用Hilt注入，需要通过EntryPoint获取依赖
            val entryPoint = EntryPointAccessors.fromApplication(
                context.applicationContext,
                OverdueTaskCheckWorkerEntryPoint::class.java
            )
            
            val taskReminderScheduler = entryPoint.taskReminderScheduler()
            
            // 检查并发送逾期任务提醒
            taskReminderScheduler.checkAndNotifyOverdueTasks()
            
            Log.d(TAG, "逾期任务检查完成")
            Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "检查逾期任务失败: ${e.message}", e)
            Result.failure()
        }
    }
} 