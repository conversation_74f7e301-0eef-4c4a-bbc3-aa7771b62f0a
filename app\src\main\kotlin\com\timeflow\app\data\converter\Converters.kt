package com.timeflow.app.data.converter

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.Instant
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.ReflectionType

/**
 * Room 数据库类型转换器
 */
class Converters {
    private val gson = Gson()

    // Instant 转换 (新增 - 感想使用)
    @TypeConverter
    fun fromTimestampToInstant(value: Long?): Instant? {
        return value?.let { Instant.ofEpochMilli(it) }
    }

    @TypeConverter
    fun instantToTimestamp(instant: Instant?): Long? {
        return instant?.toEpochMilli()
    }

    // LocalDateTime 转换
    @TypeConverter
    fun fromTimestampToDateTime(value: Long?): LocalDateTime? {
        return value?.let { LocalDateTime.ofEpochSecond(it, 0, ZoneOffset.UTC) }
    }

    @TypeConverter
    fun dateTimeToTimestamp(date: LocalDateTime?): Long? {
        return date?.toEpochSecond(ZoneOffset.UTC)
    }
    
    // LocalDate 转换
    @TypeConverter
    fun fromEpochDayToDate(value: Long?): LocalDate? {
        return value?.let { LocalDate.ofEpochDay(it) }
    }
    
    @TypeConverter
    fun dateToEpochDay(date: LocalDate?): Long? {
        return date?.toEpochDay()
    }
    
    // LocalTime 转换 (新增)
    @TypeConverter
    fun fromSecondsOfDayToTime(value: Int?): LocalTime? {
        return value?.let { LocalTime.ofSecondOfDay(it.toLong()) }
    }
    
    @TypeConverter
    fun timeToSecondsOfDay(time: LocalTime?): Int? {
        return time?.toSecondOfDay()
    }

    // ReflectionType 转换 (新增 - 感想类型)
    @TypeConverter
    fun fromReflectionType(reflectionType: ReflectionType?): String? {
        return reflectionType?.name
    }

    @TypeConverter
    fun toReflectionType(value: String?): ReflectionType? {
        return value?.let { typeName ->
            try {
                ReflectionType.valueOf(typeName)
            } catch (e: IllegalArgumentException) {
                ReflectionType.LIFE
            }
        }
    }

    // MoodType 转换 (新增 - 心情类型)
    @TypeConverter
    fun fromMoodType(moodType: MoodType?): String? {
        return moodType?.name
    }

    @TypeConverter
    fun toMoodType(value: String?): MoodType? {
        return value?.let { moodName ->
            try {
                MoodType.valueOf(moodName)
            } catch (e: IllegalArgumentException) {
                MoodType.CALM
            }
        }
    }

    // TaskTag 列表转换
    @TypeConverter
    fun fromTaskTagList(value: List<TaskTag>?): String {
        val dtoList = value?.map { 
            TaskTagDto(
                id = it.id,
                name = it.name,
                colorArgb = it.color.toArgb(),
                iconName = it.iconName
            )
        } ?: emptyList<TaskTagDto>()
        
        return gson.toJson(dtoList)
    }

    @TypeConverter
    fun toTaskTagList(value: String): List<TaskTag> {
        val listType = object : TypeToken<List<TaskTagDto>>() {}.type
        val dtoList: List<TaskTagDto> = gson.fromJson(value, listType) ?: emptyList()
        return dtoList.map { it.toTaskTag() }
    }

    // Color 转换
    @TypeConverter
    fun fromColor(color: Color?): Int? {
        return color?.toArgb()
    }

    @TypeConverter
    fun toColor(colorInt: Int?): Color? {
        return colorInt?.let { Color(it) }
    }

    // Priority 转换
    @TypeConverter
    fun fromPriority(priority: Priority?): String? {
        return priority?.name
    }

    @TypeConverter
    fun toPriority(value: String?): Priority? {
        return value?.let { priorityName ->
            try {
                Priority.valueOf(priorityName)
            } catch (e: IllegalArgumentException) {
                null
            }
        }
    }

    // TaskType 转换
    @TypeConverter
    fun fromTaskType(taskType: TaskType?): String? {
        return taskType?.getName()
    }

    @TypeConverter
    fun toTaskType(value: String?): TaskType? {
        return value?.let { typeName ->
            try {
                TaskType.valueOf(typeName)
            } catch (e: IllegalArgumentException) {
                TaskType.NORMAL
            }
        }
    }
}

/**
 * TaskTag 数据传输对象 - 用于 JSON 序列化
 */
private data class TaskTagDto(
    val id: String,
    val name: String,
    val colorArgb: Int,
    val iconName: String?
) {
    fun toTaskTag(): TaskTag {
        return TaskTag(
            id = id,
            name = name,
            color = Color(colorArgb),
            iconName = iconName
        )
    }
}

/**
 * TaskTag 扩展方法 - 转换为 DTO
 */
private fun TaskTag.toTaskTagDto(): TaskTagDto {
    return TaskTagDto(
        id = id,
        name = name,
        colorArgb = color.toArgb(),
        iconName = iconName
    )
} 