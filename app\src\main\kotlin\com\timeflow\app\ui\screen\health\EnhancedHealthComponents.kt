package com.timeflow.app.ui.screen.health

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.Locale

// 现代化配色方案 - 参考薄荷健康、Keep、Apple Health
private val HealthPrimary = Color(0xFF00C853)      // 健康绿
private val MedicationBlue = Color(0xFF2196F3)     // 医疗蓝
private val NutritionOrange = Color(0xFFFF9800)    // 营养橙
private val HabitPurple = Color(0xFF9C27B0)        // 习惯紫
private val DietPink = Color(0xFFE91E63)           // 饮食粉
private val WellnessTeal = Color(0xFF00BCD4)       // 健康青

private val SurfaceWhite = Color(0xFFFAFBFC)       // 背景白
private val CardWhite = Color(0xFFFFFFFF)          // 卡片白
private val TextPrimary = Color(0xFF1A1B23)        // 主要文字
private val TextSecondary = Color(0xFF6B7280)      // 次要文字
private val TextHint = Color(0xFF9CA3AF)           // 提示文字
private val BorderLight = Color(0xFFE5E7EB)        // 边框颜色

/**
 * 现代化健康打卡卡片
 * 参考Keep、薄荷健康的设计风格
 */
@Composable
fun ModernHealthCheckCard(
    title: String,
    subtitle: String = "",
    icon: ImageVector,
    color: Color,
    isCompleted: Boolean = false,
    completionTime: String? = null,
    streak: Int = 0,
    onToggleComplete: () -> Unit,
    onCardClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val haptic = LocalHapticFeedback.current
    var isPressed by remember { mutableStateOf(false) }
    
    // 动画效果
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(dampingRatio = 0.7f),
        label = "card_scale"
    )
    
    val completionProgress by animateFloatAsState(
        targetValue = if (isCompleted) 1f else 0f,
        animationSpec = spring(dampingRatio = 0.8f, stiffness = 100f),
        label = "completion_progress"
    )
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .scale(scale)
            .clickable {
                isPressed = true
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onCardClick?.invoke() ?: onToggleComplete()
            },
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isCompleted) {
                color.copy(alpha = 0.05f)
            } else {
                CardWhite
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp)
    ) {
        // 顶部进度条
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .background(Color.Transparent)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(completionProgress)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                color,
                                color.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
        }
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标区域
            Box(
                modifier = Modifier
                    .size(64.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                color.copy(alpha = 0.15f),
                                color.copy(alpha = 0.05f)
                            ),
                            radius = 100f
                        ),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                // 完成状态背景动画
                if (isCompleted) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = color.copy(alpha = 0.1f),
                                shape = CircleShape
                            )
                    )
                }
                
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = color,
                    modifier = Modifier
                        .size(32.dp)
                        .graphicsLayer {
                            rotationZ = if (isCompleted) 360f * completionProgress else 0f
                        }
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 内容区域
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = TextPrimary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 连续打卡天数
                    if (streak > 0) {
                        Surface(
                            color = color.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.LocalFireDepartment,
                                    contentDescription = "连续天数",
                                    tint = color,
                                    modifier = Modifier.size(12.dp)
                                )
                                Spacer(modifier = Modifier.width(2.dp))
                                Text(
                                    text = "${streak}天",
                                    fontSize = 11.sp,
                                    color = color,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }
                        }
                    }
                }
                
                if (subtitle.isNotEmpty()) {
                    Text(
                        text = subtitle,
                        fontSize = 14.sp,
                        color = TextSecondary,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                // 完成时间显示
                if (isCompleted && completionTime != null) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = 6.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "已完成",
                            tint = color.copy(alpha = 0.7f),
                            modifier = Modifier.size(14.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "已于 $completionTime 完成",
                            fontSize = 12.sp,
                            color = color.copy(alpha = 0.8f),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 完成状态按钮
            AnimatedCheckButton(
                isCompleted = isCompleted,
                color = color,
                onToggle = {
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    onToggleComplete()
                }
            )
        }
    }
    
    // 处理按压状态重置
    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(150)
            isPressed = false
        }
    }
}

/**
 * 动画完成按钮
 */
@Composable
private fun AnimatedCheckButton(
    isCompleted: Boolean,
    color: Color,
    onToggle: () -> Unit
) {
    var isAnimating by remember { mutableStateOf(false) }
    
    val buttonScale by animateFloatAsState(
        targetValue = if (isAnimating) 1.2f else 1f,
        animationSpec = spring(dampingRatio = 0.5f),
        label = "button_scale"
    )
    
    val checkRotation by animateFloatAsState(
        targetValue = if (isCompleted) 0f else -90f,
        animationSpec = spring(dampingRatio = 0.7f),
        label = "check_rotation"
    )
    
    Surface(
        modifier = Modifier
            .size(48.dp)
            .scale(buttonScale)
            .clickable {
                isAnimating = true
                onToggle()
            },
        shape = CircleShape,
        color = if (isCompleted) color else color.copy(alpha = 0.1f),
        shadowElevation = if (isCompleted) 6.dp else 0.dp
    ) {
        Box(contentAlignment = Alignment.Center) {
            if (isCompleted) {
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = "已完成",
                    tint = CardWhite,
                    modifier = Modifier
                        .size(24.dp)
                        .rotate(checkRotation)
                )
            } else {
                Icon(
                    imageVector = Icons.Outlined.RadioButtonUnchecked,
                    contentDescription = "未完成",
                    tint = color,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
    
    // 重置动画状态
    LaunchedEffect(isAnimating) {
        if (isAnimating) {
            delay(200)
            isAnimating = false
        }
    }
}

/**
 * 健康统计概览卡片
 * 参考Apple Health的环形进度设计
 */
@Composable
fun HealthStatsOverviewCard(
    dailyGoal: Int,
    completed: Int,
    streakDays: Int,
    weeklyCompletion: Float,
    modifier: Modifier = Modifier
) {
    val completionRate = if (dailyGoal > 0) completed.toFloat() / dailyGoal else 0f
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(containerColor = CardWhite),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "今日概览",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary
                )
                
                Text(
                    text = LocalDate.now().format(
                        DateTimeFormatter.ofPattern("MM月dd日", Locale.CHINA)
                    ),
                    fontSize = 14.sp,
                    color = TextSecondary
                )
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 完成进度
                HealthStatItem(
                    value = "$completed",
                    suffix = "/$dailyGoal",
                    label = "今日完成",
                    color = HealthPrimary,
                    progress = completionRate
                )
                
                // 连续天数
                HealthStatItem(
                    value = "$streakDays",
                    suffix = "天",
                    label = "连续打卡",
                    color = MedicationBlue,
                    icon = Icons.Filled.LocalFireDepartment
                )
                
                // 本周完成率
                HealthStatItem(
                    value = "${(weeklyCompletion * 100).toInt()}",
                    suffix = "%",
                    label = "本周完成率",
                    color = HabitPurple,
                    progress = weeklyCompletion
                )
            }
        }
    }
}

/**
 * 健康统计项
 */
@Composable
private fun HealthStatItem(
    value: String,
    suffix: String = "",
    label: String,
    color: Color,
    progress: Float? = null,
    icon: ImageVector? = null
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.size(64.dp),
            contentAlignment = Alignment.Center
        ) {
            // 环形进度条（如果提供了progress）
            if (progress != null) {
                CircularProgressIndicator(
                    progress = { progress },
                    modifier = Modifier.size(64.dp),
                    color = color,
                    strokeWidth = 6.dp,
                    trackColor = color.copy(alpha = 0.1f)
                )
            } else {
                // 图标背景
                Surface(
                    modifier = Modifier.size(64.dp),
                    shape = CircleShape,
                    color = color.copy(alpha = 0.1f)
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        icon?.let {
                            Icon(
                                imageVector = it,
                                contentDescription = label,
                                tint = color,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }
            }
            
            // 数值显示
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    verticalAlignment = Alignment.Bottom
                ) {
                    Text(
                        text = value,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = color
                    )
                    if (suffix.isNotEmpty()) {
                        Text(
                            text = suffix,
                            fontSize = 12.sp,
                            color = color.copy(alpha = 0.7f),
                            modifier = Modifier.padding(bottom = 2.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = TextSecondary,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 快速操作栏
 * 参考薄荷健康的快捷操作设计
 */
@Composable
fun HealthQuickActionBar(
    actions: List<QuickAction>,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(actions) { action ->
            QuickActionItem(
                action = action,
                onClick = action.onClick
            )
        }
    }
}

/**
 * 快速操作数据类
 */
data class QuickAction(
    val icon: ImageVector,
    val label: String,
    val color: Color,
    val onClick: () -> Unit
)

/**
 * 快速操作项
 */
@Composable
private fun QuickActionItem(
    action: QuickAction,
    onClick: () -> Unit
) {
    val haptic = LocalHapticFeedback.current
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(dampingRatio = 0.6f),
        label = "action_scale"
    )
    
    Surface(
        modifier = Modifier
            .scale(scale)
            .clickable {
                isPressed = true
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onClick()
            },
        shape = RoundedCornerShape(16.dp),
        color = action.color.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                action.color.copy(alpha = 0.2f),
                                action.color.copy(alpha = 0.05f)
                            )
                        ),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = action.icon,
                    contentDescription = action.label,
                    tint = action.color,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(6.dp))
            
            Text(
                text = action.label,
                fontSize = 12.sp,
                color = action.color,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
    
    // 重置按压状态
    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(150)
            isPressed = false
        }
    }
}

/**
 * 健康洞察卡片
 * 参考Apple Health的洞察设计
 */
@Composable
fun HealthInsightCard(
    title: String,
    description: String,
    icon: ImageVector,
    color: Color,
    actionText: String = "查看详情",
    onActionClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = CardWhite),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Surface(
                    modifier = Modifier.size(48.dp),
                    shape = CircleShape,
                    color = color.copy(alpha = 0.1f)
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = icon,
                            contentDescription = title,
                            tint = color,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                    
                    Text(
                        text = description,
                        fontSize = 14.sp,
                        color = TextSecondary,
                        modifier = Modifier.padding(top = 2.dp),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onActionClick,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = color.copy(alpha = 0.1f),
                    contentColor = color
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = actionText,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
} 