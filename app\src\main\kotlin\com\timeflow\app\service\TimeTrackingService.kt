package com.timeflow.app.service

import android.app.Service
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.os.PowerManager
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.timeflow.app.R
import com.timeflow.app.data.model.AppUsageData
import com.timeflow.app.utils.NotificationHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

/**
 * 自动时间追踪服务
 * 负责在后台监控应用使用情况并记录时间
 */
@AndroidEntryPoint
class TimeTrackingService : Service() {

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val TRACKING_INTERVAL_MS = 30000L // 30秒
        private const val CHANNEL_ID = "time_tracking_channel"
    }

    private val binder = LocalBinder()
    private val scope = CoroutineScope(Dispatchers.Default)
    private var trackingJob: Job? = null
    
    // 跟踪状态
    private val _isTracking = MutableLiveData<Boolean>(false)
    val isTracking: LiveData<Boolean> = _isTracking
    
    // 当前追踪的应用
    private val _currentApp = MutableLiveData<String>()
    val currentApp: LiveData<String> = _currentApp
    
    // 当前会话时长
    private val _currentSessionDuration = MutableLiveData<Duration>(Duration.ZERO)
    val currentSessionDuration: LiveData<Duration> = _currentSessionDuration
    
    // 今日统计数据
    private val _todayStats = MutableLiveData<Map<String, AppUsageData>>(emptyMap())
    val todayStats: LiveData<Map<String, AppUsageData>> = _todayStats
    
    private var wakeLock: PowerManager.WakeLock? = null
    
    @Inject
    lateinit var notificationHelper: NotificationHelper
    
    inner class LocalBinder : Binder() {
        fun getService(): TimeTrackingService = this@TimeTrackingService
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("TimeTrackingService创建")
        
        // 创建通知渠道
        notificationHelper.createNotificationChannel(
            CHANNEL_ID,
            "时间追踪",
            "自动记录应用使用时间"
        )
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("TimeTrackingService启动命令: ${intent?.action}")
        
        when (intent?.action) {
            "START_TRACKING" -> startTracking()
            "STOP_TRACKING" -> stopTracking()
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent): IBinder {
        return binder
    }
    
    override fun onDestroy() {
        Timber.d("TimeTrackingService销毁")
        stopTracking()
        scope.cancel()
        super.onDestroy()
    }
    
    /**
     * 开始追踪时间
     */
    fun startTracking() {
        if (_isTracking.value == true) return
        
        Timber.d("开始时间追踪")
        _isTracking.postValue(true)
        
        // 获取唤醒锁以防止服务被系统杀死
        acquireWakeLock()
        
        // 创建前台通知
        val notification = notificationHelper.createNotification(
            channelId = CHANNEL_ID,
            title = "TimeFlow正在追踪时间",
            content = "点击查看详情",
            smallIcon = R.drawable.ic_launcher_foreground
        )
        
        startForeground(NOTIFICATION_ID, notification)
        
        // 启动追踪任务
        trackingJob = scope.launch {
            try {
                var lastUpdateTime = System.currentTimeMillis()
                
                while (isActive) {
                    val currentTime = System.currentTimeMillis()
                    val elapsedTime = currentTime - lastUpdateTime
                    lastUpdateTime = currentTime
                    
                    // 更新当前会话时长
                    val currentDuration = _currentSessionDuration.value ?: Duration.ZERO
                    _currentSessionDuration.postValue(currentDuration + elapsedTime.milliseconds)
                    
                    // 获取当前前台应用
                    val currentAppPackage = getCurrentForegroundApp()
                    _currentApp.postValue(currentAppPackage)
                    
                    // 更新应用使用统计
                    updateAppUsageStats(currentAppPackage, elapsedTime)
                    
                    // 更新通知
                    updateTrackingNotification()
                    
                    delay(TRACKING_INTERVAL_MS)
                }
            } catch (e: Exception) {
                Timber.e(e, "时间追踪异常")
            }
        }
    }
    
    /**
     * 停止追踪时间
     */
    fun stopTracking() {
        if (_isTracking.value == false) return
        
        Timber.d("停止时间追踪")
        _isTracking.postValue(false)
        
        trackingJob?.cancel()
        trackingJob = null
        
        releaseWakeLock()
        
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    /**
     * 更新追踪通知
     */
    private fun updateTrackingNotification() {
        val currentApp = _currentApp.value ?: "未知应用"
        val duration = _currentSessionDuration.value ?: Duration.ZERO
        val hours = duration.inWholeHours
        val minutes = duration.inWholeMinutes % 60
        val seconds = duration.inWholeSeconds % 60
        
        val timeString = String.format("%02d:%02d:%02d", hours, minutes, seconds)
        
        val notification = notificationHelper.createNotification(
            channelId = CHANNEL_ID,
            title = "TimeFlow正在追踪时间",
            content = "当前应用: $currentApp\n已追踪: $timeString",
            smallIcon = R.drawable.ic_launcher_foreground
        )
        
        notificationHelper.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * 获取当前前台应用包名
     */
    private fun getCurrentForegroundApp(): String {
        try {
            val usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            val time = System.currentTimeMillis()
            
            // 获取最近使用的应用统计信息
            val events = usageStatsManager.queryEvents(time - 60000, time)
            val event = UsageEvents.Event()
            var lastEvent: UsageEvents.Event? = null
            
            // 查找最近的前台事件
            while (events.hasNextEvent()) {
                events.getNextEvent(event)
                if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                    lastEvent = event  // 使用当前事件
                }
            }
            
            return lastEvent?.packageName ?: "unknown"
        } catch (e: Exception) {
            Timber.e(e, "获取前台应用失败")
            return "unknown"
        }
    }
    
    /**
     * 更新应用使用统计
     */
    private fun updateAppUsageStats(packageName: String, elapsedTimeMs: Long) {
        if (packageName == "unknown") return
        
        val currentStats = _todayStats.value?.toMutableMap() ?: mutableMapOf()
        val appData = currentStats[packageName] ?: AppUsageData(packageName, Duration.ZERO, 0)
        
        // 更新使用时间
        val updatedAppData = appData.copy(
            duration = appData.duration + elapsedTimeMs.milliseconds,
            launchCount = appData.launchCount
        )
        
        currentStats[packageName] = updatedAppData
        _todayStats.postValue(currentStats)
    }
    
    /**
     * 获取唤醒锁
     */
    private fun acquireWakeLock() {
        if (wakeLock == null) {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "TimeFlow:TimeTrackingWakeLock"
            )
            wakeLock?.acquire(10 * 60 * 1000L) // 10分钟
        }
    }
    
    /**
     * 释放唤醒锁
     */
    private fun releaseWakeLock() {
        if (wakeLock?.isHeld == true) {
            wakeLock?.release()
            wakeLock = null
        }
    }
} 