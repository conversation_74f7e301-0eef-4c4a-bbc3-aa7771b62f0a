package com.timeflow.app.di

import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey

/**
 * 全局偏好设置键定义
 */
object PreferenceKeys {
    // 用户偏好设置
    val USER_NAME = stringPreferencesKey("user_name")
    val USER_EMAIL = stringPreferencesKey("user_email")
    val USER_AVATAR = stringPreferencesKey("user_avatar")
    
    // 主题设置
    val IS_DARK_MODE = booleanPreferencesKey("is_dark_mode")
    val USE_SYSTEM_DARK_MODE = booleanPreferencesKey("use_system_dark_mode")
    val USE_UNIFIED_COLORS = booleanPreferencesKey("use_unified_colors")
    val THEME_MODE = stringPreferencesKey("theme_mode")
    val USE_DYNAMIC_COLOR = booleanPreferencesKey("use_dynamic_color")
    val USE_UNIFIED_BACKGROUND = booleanPreferencesKey("use_unified_background")
    
    // 颜色设置
    val PRIMARY_COLOR = longPreferencesKey("primary_color")
    val SECONDARY_COLOR = longPreferencesKey("secondary_color")
    val BACKGROUND_COLOR = longPreferencesKey("background_color")
    val SURFACE_COLOR = longPreferencesKey("surface_color")
    val ERROR_COLOR = longPreferencesKey("error_color")
    
    // 页面颜色设置
    val HOME_PAGE_COLOR = longPreferencesKey("home_page_color")
    val CALENDAR_PAGE_COLOR = longPreferencesKey("calendar_page_color")
    val STATISTICS_PAGE_COLOR = longPreferencesKey("statistics_page_color")
    val SETTINGS_PAGE_COLOR = longPreferencesKey("settings_page_color")
    val PROFILE_PAGE_COLOR = longPreferencesKey("profile_page_color")

    // 预设主题设置
    val CURRENT_PRESET_ID = stringPreferencesKey("current_preset_id")

    // 通知设置
    val ENABLE_NOTIFICATIONS = booleanPreferencesKey("enable_notifications")
    val NOTIFICATION_SOUND = booleanPreferencesKey("notification_sound")
    val NOTIFICATION_VIBRATE = booleanPreferencesKey("notification_vibrate")
    
    // 字体设置
    val CUSTOM_FONT_PATH = stringPreferencesKey("custom_font_path")
    val CUSTOM_FONT_NAME = stringPreferencesKey("custom_font_name")
    val USE_CUSTOM_FONT = booleanPreferencesKey("use_custom_font")
    
    // 其他设置
    val FIRST_TIME_LAUNCH = booleanPreferencesKey("first_time_launch")
} 