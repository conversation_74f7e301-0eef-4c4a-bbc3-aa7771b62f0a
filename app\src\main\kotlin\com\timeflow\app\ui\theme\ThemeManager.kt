package com.timeflow.app.ui.theme

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.isActive
import kotlinx.coroutines.CancellationException
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.ui.task.components.common.event.EventBus

// 定义TAG常量用于日志
private const val TAG = "ThemeManager"

// 定义协程作用域
private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

/**
 * 广播页面背景色变更
 * @param pageName 页面名称
 * @param color 颜色值
 */
fun broadcastPageBackgroundColor(pageName: String, color: Color) {
    scope.launch {
        try {
            if (!isActive) return@launch
            
            // 将Color转换为ARGB Long值，确保使用32位无符号表示
            // 1. 先转换为Int
            val colorInt = color.toArgb()
            // 2. 然后转换为无符号Long
            val colorArgb = colorInt.toLong() and 0xFFFFFFFFL
            
            // 记录详细日志，包括原始颜色和转换后的值
            Log.d(TAG, "准备广播页面背景色事件: 页面=$pageName, 原始Color=${color}, 转Int=${colorInt.toString(16)}, 转Long=${colorArgb.toString(16)}")
            
            // 发送页面背景色变更事件
            val success = EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                pageName = pageName,
                colorArgb = colorArgb
            ))
            
            Log.d(TAG, "已广播页面背景色变更事件: 页面=$pageName, 颜色=${colorArgb.toString(16)}, 结果=$success")
        } catch (e: CancellationException) {
            // 协程取消异常，静默处理
            throw e
        } catch (e: Exception) {
            // 记录其他异常，但不中断应用
            Log.e(TAG, "广播页面背景色变更失败: $e", e)
        }
    }
} 