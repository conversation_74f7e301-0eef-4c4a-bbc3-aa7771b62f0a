package com.timeflow.app.ui.task.components.common

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.OpenInNew
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.icons.outlined.CalendarToday
import androidx.compose.material.icons.outlined.Flag
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.stateDescription
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.data.entity.Task
import com.timeflow.app.ui.utils.formatCompactDate
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import kotlin.math.roundToInt
import java.time.temporal.ChronoUnit

// Define formatters as top-level constants to avoid recreation
private val DATE_FORMATTER_MMM_D = DateTimeFormatter.ofPattern("MMM d")
private val DATE_FORMATTER_HH_MM = DateTimeFormatter.ofPattern("HH:mm")
private val DATE_FORMATTER_YYYY_MMM_D = DateTimeFormatter.ofPattern("yyyy MMM d")

/**
 * Enhanced Task Card with improved UX and visual design
 * Inspired by TickTick and Todoist
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedTaskCard(
    task: Task,
    onClick: () -> Unit,
    onCheckChange: (String, Boolean) -> Unit,
    modifier: Modifier = Modifier,
    showCheckbox: Boolean = true
) {
    // State
    val isCompleted = remember(task.status) { 
        task.status.equals("已完成", ignoreCase = true) || 
        task.status.equals("completed", ignoreCase = true)
    }
    
    // Swipe state
    var offsetX by remember { mutableStateOf(0f) }
    val swipeThreshold = 100f
    
    // Animation states
    val haptics = LocalHapticFeedback.current
    val checkScale by animateFloatAsState(
        targetValue = if (isCompleted) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "check scale"
    )
    
    val cardColor by animateColorAsState(
        targetValue = if (isCompleted) 
            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
        else 
            Color(0xFFF8F8F5), // Soft white
        label = "card color"
    )
    
    val priorityColor = when (task.priority) {
        1 -> Color(0xFFFF9F0A) // Medium - orange
        2 -> Color(0xFFFF453A) // High - red
        3 -> Color(0xFFFF193A) // Urgent - deep red
        else -> Color(0xFF34C759) // Low - green
    }
    
    // Optimized: Remember calculation results based on task inputs
    val dueTimeText = remember(task.dueDate) { formatDueDate(task.dueDate) }
    val progress = remember(task.createdAt, task.dueDate) { calculateTimeProgress(task.createdAt, task.dueDate) }
    
    // Card content
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = cardColor,
        tonalElevation = 0.dp,
        shadowElevation = 0.dp,
        modifier = modifier
            .offset { IntOffset(offsetX.roundToInt(), 0) }
            .pointerInput(Unit) {
                detectHorizontalDragGestures(
                    onDragStart = { /* Drag start */ },
                    onDragEnd = {
                        // Handle swipe actions
                        when {
                            offsetX < -swipeThreshold -> {
                                // Left swipe - complete task
                                onCheckChange(task.id, !isCompleted)
                                haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            }
                            offsetX > swipeThreshold -> {
                                // Right swipe - other action
                                // Could be star, postpone, etc.
                            }
                        }
                        offsetX = 0f // Reset position
                    },
                    onDragCancel = { offsetX = 0f },
                    onHorizontalDrag = { _, dragAmount -> 
                        offsetX += dragAmount
                        // Limit drag distance
                        offsetX = offsetX.coerceIn(-200f, 200f)
                    }
                )
            }
            .clickable(onClick = onClick)
            .semantics {
                contentDescription = if (isCompleted) "Completed task" else "Incomplete task"
                stateDescription = "Priority ${task.priority}"
            }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Task completion checkbox with animation
            if (showCheckbox) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .scale(checkScale)
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null,
                            onClick = { 
                                onCheckChange(task.id, !isCompleted)
                                haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            }
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (isCompleted) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "Task completed",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.fillMaxSize()
                        )
                    } else {
                        Box(
                            modifier = Modifier
                                .size(20.dp)
                                .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f), CircleShape)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Task content
            Column(modifier = Modifier.weight(1f)) {
                // Due date if available
                if (task.dueDate != null) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 4.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.AccessTime,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            modifier = Modifier.size(12.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(4.dp))
                        
                        // Optimized: Use remembered value
                        Text(
                            text = dueTimeText,
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            fontSize = 12.sp
                        )
                    }
                }
                
                // Task title
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = if (isCompleted) FontWeight.Normal else FontWeight.SemiBold
                    ),
                    color = if (isCompleted) 
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    else 
                        MaterialTheme.colorScheme.onSurface,
                    textDecoration = if (isCompleted) TextDecoration.LineThrough else null,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                // Time progress if due date is available
                if (task.dueDate != null && !isCompleted) {
                    Spacer(modifier = Modifier.height(8.dp))
                    // Optimized: Use remembered value
                    LinearProgressIndicator(
                        progress = { progress.coerceIn(0f, 1f) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp)
                            .clip(RoundedCornerShape(50)),
                        color = when {
                            progress > 0.8f -> Color(0xFFFF453A) // Red when close to deadline
                            progress > 0.5f -> Color(0xFFFF9F0A) // Orange for mid progress
                            else -> MaterialTheme.colorScheme.primary // Normal color otherwise
                        },
                        trackColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                    )
                }
                
                // Bottom row with tags and metadata
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Show tag indicators if present
                    if (!task.tagIds.isNullOrEmpty()) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            task.tagIds.take(3).forEach { tagId ->
                                Surface(
                                    color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.6f),
                                    shape = RoundedCornerShape(4.dp),
                                    modifier = Modifier.height(18.dp)
                                ) {
                                    Text(
                                        text = tagId,
                                        style = MaterialTheme.typography.labelSmall,
                                        fontSize = 10.sp,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer,
                                        modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp)
                                    )
                                }
                            }
                            
                            // Show count of additional tags
                            if ((task.tagIds?.size ?: 0) > 3) {
                                Text(
                                    text = "+${(task.tagIds?.size ?: 0) - 3}",
                                    style = MaterialTheme.typography.labelSmall,
                                    fontSize = 10.sp,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                    
                    // Priority indicator
                    if (task.priority > 0) {
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(priorityColor, CircleShape)
                        )
                    }
                    
                    // Star indicator if task is starred
                    if (task.priority > 2) { // Use priority > 2 as a proxy for "starred" until we have proper field
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.Filled.Star,
                            contentDescription = "Starred task",
                            tint = Color(0xFFFFCC00),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
    
    // Swipe indicator overlays
    Box(
        modifier = Modifier
            .fillMaxSize()
            .offset { IntOffset(offsetX.roundToInt(), 0) }
    ) {
        // Left swipe action indicator (Complete)
        AnimatedVisibility(
            visible = offsetX < -swipeThreshold / 2,
            modifier = Modifier.align(Alignment.CenterEnd)
        ) {
            Icon(
                imageVector = if (isCompleted) Icons.Filled.Close else Icons.Filled.Check,
                contentDescription = if (isCompleted) "Mark incomplete" else "Mark complete",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .padding(end = 16.dp)
                    .size(24.dp)
            )
        }
        
        // Right swipe action indicator (Star)
        AnimatedVisibility(
            visible = offsetX > swipeThreshold / 2,
            modifier = Modifier.align(Alignment.CenterStart)
        ) {
            Icon(
                imageVector = Icons.Filled.Star,
                contentDescription = "Star task",
                tint = Color(0xFFFFCC00),
                modifier = Modifier
                    .padding(start = 16.dp)
                    .size(24.dp)
            )
        }
    }
}

/**
 * Calculate progress between creation time and due date
 * Returns a value between 0 and 1
 */
private fun calculateTimeProgress(createdAt: LocalDateTime?, dueDate: LocalDateTime?): Float {
    if (dueDate == null) return 0f

    val now = LocalDateTime.now()
    // Use actual creation time if available, otherwise assume it was created shortly before now
    val creationTime = createdAt ?: now.minusHours(1)

    // Ensure creationTime is not after now or dueDate
    if (creationTime.isAfter(now) || creationTime.isAfter(dueDate)) return 0f

    val totalDuration = Duration.between(creationTime, dueDate)
    val elapsedDuration = Duration.between(creationTime, now)

    // Avoid division by zero or negative duration
    return if (totalDuration.seconds > 0) {
        (elapsedDuration.seconds.toFloat() / totalDuration.seconds.toFloat()).coerceIn(0f, 1f)
    } else if (now.isAfter(dueDate)) {
        1f // Already past due date
    }
     else {
        0f // Due date is same as creation or in the past relative to creation
    }
}

/**
 * Format due date in a user-friendly way
 */
private fun formatDueDate(dueDate: LocalDateTime?): String {
    if (dueDate == null) return ""

    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val tomorrow = today.plusDays(1)
    val dueDateLocal = dueDate.toLocalDate()

    // Calculate end time (assuming 1-hour duration for display)
    val endDateTime = dueDate.plusHours(1)
    val endDate = endDateTime.toLocalDate()

    // Check if the task crosses midnight
    val isCrossDay = !dueDateLocal.isEqual(endDate)

    return when {
        dueDateLocal == today -> {
            if (isCrossDay) {
                // Cross-day: Today to Tomorrow
                "Today, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - Tomorrow, ${endDate.format(DATE_FORMATTER_MMM_D)}, ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            } else {
                // Same day: Today
                "Today, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            }
        }
        dueDateLocal == tomorrow -> {
            if (isCrossDay) {
                // Cross-day: Tomorrow to day after
                val dayAfterTomorrow = tomorrow.plusDays(1)
                val endDayDesc = if (endDate == dayAfterTomorrow) "Day after tomorrow" else endDate.format(DATE_FORMATTER_MMM_D)
                "Tomorrow, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - $endDayDesc, ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            } else {
                // Same day: Tomorrow
                "Tomorrow, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            }
        }
        dueDate.year == now.year -> {
            val dayOfWeek = when (dueDate.dayOfWeek.value) {
                1 -> "Mon"
                2 -> "Tue"
                3 -> "Wed"
                4 -> "Thu"
                5 -> "Fri"
                6 -> "Sat"
                7 -> "Sun"
                else -> ""
            }

            if (ChronoUnit.DAYS.between(today, dueDateLocal) < 7) {
                // Within a week
                if (isCrossDay) {
                    val endDayOfWeek = when (endDate.dayOfWeek.value) {
                        1 -> "Mon"
                        2 -> "Tue"
                        3 -> "Wed"
                        4 -> "Thu"
                        5 -> "Fri"
                        6 -> "Sat"
                        7 -> "Sun"
                        else -> ""
                    }
                    "$dayOfWeek, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - $endDayOfWeek, ${endDate.format(DATE_FORMATTER_MMM_D)}, ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
                } else {
                    "$dayOfWeek, ${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
                }
            } else {
                // This year
                if (isCrossDay) {
                    "${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDate.format(DATE_FORMATTER_MMM_D)}, ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
                } else {
                    "${dueDate.format(DATE_FORMATTER_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
                }
            }
        }
        else -> {
            // Other years
            if (isCrossDay) {
                "${dueDate.format(DATE_FORMATTER_YYYY_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDate.format(DATE_FORMATTER_YYYY_MMM_D)}, ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            } else {
                "${dueDate.format(DATE_FORMATTER_YYYY_MMM_D)}, ${dueDate.format(DATE_FORMATTER_HH_MM)} - ${endDateTime.format(DATE_FORMATTER_HH_MM)}"
            }
        }
    }
}