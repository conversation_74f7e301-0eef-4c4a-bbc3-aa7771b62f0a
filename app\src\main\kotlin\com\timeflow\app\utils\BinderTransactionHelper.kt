package com.timeflow.app.utils

import android.os.Binder
import android.os.Parcel
import android.os.Parcelable
import android.os.TransactionTooLargeException
import android.os.Build
import android.util.Log
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean
import timber.log.Timber

/**
 * Binder事务辅助工具
 * 解决日志中频繁出现的"Binder transaction failure"错误
 */
object BinderTransactionHelper {
    private const val TAG = "BinderTransaction"
    
    // 最大安全Binder事务大小 (500KB，保守值低于系统1MB限制)
    private const val MAX_SAFE_TRANSACTION_SIZE = 500 * 1024
    
    // 允许的最大项目数量 (防止 TransactionTooLargeException)
    private const val MAX_ITEMS_PER_TRANSACTION = 500
    
    // Binder事务大小限制
    // Android系统限制约为1MB，保守设置为768KB
    private const val MAX_BINDER_SIZE = 768 * 1024
    
    // 单个对象估计大小（保守估计）
    const val ESTIMATED_OBJECT_SIZE = 2048 // 2KB
    
    // 单批次最大对象数量
    private var MAX_BATCH_SIZE = 100
    
    // 是否已经显示过警告
    private val binderWarningShown = AtomicBoolean(false)
    
    // 保守模式设置标志
    private var conservativeModeEnabled = false
    
    /**
     * 检查数据大小是否超过Binder事务安全限制
     * @return 如果数据大小安全，返回true；否则返回false
     */
    fun isSafeForBinderTransaction(data: Parcelable): Boolean {
        val parcel = Parcel.obtain()
        try {
            data.writeToParcel(parcel, 0)
            val dataSize = parcel.dataSize()
            
            if (dataSize > MAX_SAFE_TRANSACTION_SIZE) {
                if (!binderWarningShown.getAndSet(true)) {
                    Log.w(TAG, "数据超过安全Binder事务大小: ${dataSize/1024}KB > ${MAX_SAFE_TRANSACTION_SIZE/1024}KB")
                }
                return false
            }
            return true
        } catch (e: Exception) {
            Log.e(TAG, "检查Binder事务大小时出错: ${e.message}")
            return false
        } finally {
            parcel.recycle()
        }
    }
    
    /**
     * 将大型列表分割为安全的批次，避免 TransactionTooLargeException
     */
    fun <T> splitIntoSafeBatches(items: List<T>): List<List<T>> {
        if (items.size <= MAX_ITEMS_PER_TRANSACTION) {
            return listOf(items)
        }
        
        // 分割成多个批次
        return items.chunked(MAX_ITEMS_PER_TRANSACTION)
    }
    
    /**
     * 获取当前进程的Binder事务上限
     * 仅用于日志和调试
     */
    fun getBinderTransactionLimit(): Int {
        try {
            val getMaxObjectsPerTxnMethod = Binder::class.java.getDeclaredMethod("getMaxObjectsPerTxn")
            getMaxObjectsPerTxnMethod.isAccessible = true
            return getMaxObjectsPerTxnMethod.invoke(null) as Int
        } catch (e: Exception) {
            // 如果无法获取，返回保守默认值
            return MAX_ITEMS_PER_TRANSACTION
        }
    }
    
    /**
     * 监控Binder使用情况
     * 仅用于调试目的
     */
    fun monitorBinderUsage() {
        try {
            // 尝试读取进程级Binder统计信息
            val binderActivityFile = "/proc/self/binder/stats"
            
            // 记录当前Binder限制
            val limit = getBinderTransactionLimit()
            Log.d(TAG, "当前Binder事务限制: $limit 对象")
            
        } catch (e: Exception) {
            Log.e(TAG, "监控Binder使用情况失败: ${e.message}")
        }
    }
    
    /**
     * 检查数据量是否可能超过Binder限制
     * @param dataSize 估计的数据大小（字节）
     * @return 是否可能超过限制
     */
    fun isLikelyToExceedBinderLimit(dataSize: Int, isObjectCount: Boolean = false): Boolean {
        // 判断参数是对象数量还是数据大小
        return if (isObjectCount) {
            // 在保守模式下使用更严格的阈值
            val threshold = if (conservativeModeEnabled) {
                MAX_ITEMS_PER_TRANSACTION / 2
            } else {
                MAX_ITEMS_PER_TRANSACTION
            }
            
            dataSize > threshold
        } else {
            // 当作数据大小处理
            dataSize > MAX_BINDER_SIZE * 0.8 // 留20%余量
        }
    }
    
    /**
     * 检查对象数量是否可能超过Binder限制
     * @param count 对象数量
     * @return 是否可能超过限制
     */
    fun isLikelyToExceedObjectLimit(count: Int): Boolean {
        return isLikelyToExceedBinderLimit(count, true)
    }
    
    /**
     * 安全地批量处理大列表，避免Binder事务过大
     * @param items 要处理的项目列表
     * @param estimatedSizePerItem 每项估计大小（字节）
     * @param processBatch 处理一批数据的函数
     */
    suspend fun <T> processSafeBatches(
        items: List<T>,
        estimatedSizePerItem: Int = ESTIMATED_OBJECT_SIZE,
        processBatch: suspend (List<T>) -> Unit
    ) {
        if (items.isEmpty()) return
        
        // 计算适当的批次大小
        val batchSize = calculateOptimalBatchSize(items.size, estimatedSizePerItem)
        
        Log.d(TAG, "将${items.size}个项目分为${(items.size + batchSize - 1) / batchSize}个批次处理，每批${batchSize}个")
        
        withContext(Dispatchers.Default) {
            items.chunked(batchSize).forEachIndexed { index, batch ->
                try {
                    processBatch(batch)
                    Log.d(TAG, "成功处理批次 ${index + 1}/${(items.size + batchSize - 1) / batchSize}")
                } catch (e: TransactionTooLargeException) {
                    // 如果仍然失败，再减半处理
                    Log.w(TAG, "批次过大，减半重试: ${e.message}")
                    
                    val smallerBatchSize = batchSize / 2
                    if (smallerBatchSize > 0) {
                        batch.chunked(smallerBatchSize).forEach { miniBatch ->
                            try {
                                processBatch(miniBatch)
                            } catch (e: Exception) {
                                Log.e(TAG, "处理小批次失败: ${e.message}")
                            }
                        }
                    } else {
                        Log.e(TAG, "无法进一步减小批次大小")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理批次失败: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 计算最佳批处理大小
     */
    fun calculateOptimalBatchSize(totalItems: Int, estimatedSizePerItem: Int): Int {
        // 计算理论上最大可传输项目数
        val theoreticalMaxItems = MAX_BINDER_SIZE / estimatedSizePerItem
        
        // 取理论值和最大批量值中较小的一个，并额外留出20%余量
        val maxSafeItems = (theoreticalMaxItems * 0.8).toInt()
        
        // 不超过最大批次大小
        val batchSize = maxSafeItems.coerceAtMost(MAX_BATCH_SIZE)
        
        // 确保至少处理1个项目
        return batchSize.coerceAtLeast(1)
    }
    
    /**
     * 安全地处理可能导致Binder事务错误的操作
     * @param operation 可能失败的操作
     * @param fallback 失败时的后备操作
     */
    fun <T> safeBinderOperation(
        operation: () -> T,
        fallback: () -> T
    ): T {
        return try {
            operation()
        } catch (e: Exception) {
            if (e is TransactionTooLargeException || 
                e.message?.contains("Binder transaction failed") == true ||
                e.message?.contains("transaction too large") == true) {
                
                Log.w(TAG, "Binder事务失败，使用后备方案: ${e.message}")
                
                try {
                    fallback()
                } catch (fallbackEx: Exception) {
                    Log.e(TAG, "Binder后备操作也失败: ${fallbackEx.message}")
                    throw fallbackEx
                }
            } else {
                throw e
            }
        }
    }
    
    /**
     * Compose修饰符：优化大型列表的滚动状态更新
     * 防止Binder在滚动时因状态更新过于频繁导致崩溃
     */
    @Composable
    fun OptimizedLazyListStateHandler(
        listState: LazyListState,
        onStateUpdate: (Int) -> Unit
    ) {
        // 防止频繁更新导致的Binder问题
        LaunchedEffect(listState) {
            snapshotFlow { listState.firstVisibleItemIndex }
                .distinctUntilChanged()
                .filter { index -> 
                    // 只在索引变化足够大时更新，减少状态传递频率
                    index % 5 == 0 
                }
                .collect { index ->
                    onStateUpdate(index)
                }
        }
        
        // 清理
        DisposableEffect(Unit) {
            onDispose {
                // 组件销毁时可能需要进行的清理
            }
        }
    }
    
    /**
     * 为大列表创建安全的状态处理器
     */
    @Composable
    fun rememberSafeListHandler(
        initialIndex: Int = 0,
        onScroll: (Int) -> Unit = {}
    ): LazyListState {
        val listState = remember {
            LazyListState(
                firstVisibleItemIndex = initialIndex,
                firstVisibleItemScrollOffset = 0
            )
        }
        
        OptimizedLazyListStateHandler(listState) { index ->
            onScroll(index)
        }
        
        return listState
    }
    
    /**
     * 修复Binder事务失败错误 
     * 特别处理"Binder transaction failure. id: XXXXX, BR_*: 29201, error: -1 (Operation not permitted)"错误
     */
    fun fixBinderTransactionFailures() {
        try {
            Log.d(TAG, "开始应用Binder事务错误修复")
            
            // 1. 尝试重置Binder状态
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                try {
                    val ipcThreadStateClass = Class.forName("android.os.IPCThreadState")
                    val getInstanceMethod = ipcThreadStateClass.getDeclaredMethod("getInstance")
                    getInstanceMethod.isAccessible = true
                    val ipcThreadState = getInstanceMethod.invoke(null)
                    
                    // 尝试清除待处理命令
                    val clearPendingCommandsMethod = ipcThreadStateClass.getDeclaredMethod("clearPendingCommands")
                    clearPendingCommandsMethod.isAccessible = true
                    clearPendingCommandsMethod.invoke(ipcThreadState)
                    
                    Log.d(TAG, "成功重置IPCThreadState")
                } catch (e: Exception) {
                    Log.w(TAG, "重置IPCThreadState失败: ${e.message}")
                }
            }
            
            // 2. 尝试手动触发垃圾回收以释放Binder资源
            try {
                // System.gc() // Explicit GC calls are harmful to performance
                // System.runFinalization() // Removed
                Log.d(TAG, "不再主动触发垃圾回收") // Log updated
            } catch (e: Exception) {
                Log.w(TAG, "触发垃圾回收失败: ${e.message}")
            }
            
            // 3. 优化Binder缓冲区配置
            try {
                // 通过反射访问VMRuntime
                val vmRuntimeClass = Class.forName("dalvik.system.VMRuntime")
                val getRuntimeMethod = vmRuntimeClass.getDeclaredMethod("getRuntime")
                getRuntimeMethod.isAccessible = true
                val vmRuntime = getRuntimeMethod.invoke(null)
                
                // 设置较高的堆利用率以减少GC频率
                val setTargetHeapUtilizationMethod = 
                    vmRuntimeClass.getDeclaredMethod("setTargetHeapUtilization", Float::class.java)
                setTargetHeapUtilizationMethod.isAccessible = true
                setTargetHeapUtilizationMethod.invoke(vmRuntime, 0.75f)
                
                Log.d(TAG, "已优化堆利用率")
            } catch (e: Exception) {
                Log.w(TAG, "优化VMRuntime配置失败: ${e.message}")
            }
            
            // 4. 尝试检查并修复Binder驱动状态
            try {
                // 通过注入一个小事务来刷新Binder状态
                Binder().pingBinder()
                Log.d(TAG, "已刷新Binder状态")
            } catch (e: Exception) {
                Log.w(TAG, "刷新Binder状态失败: ${e.message}")
            }
            
            // 5. 检查Binder限制
            try {
                val limit = getBinderTransactionLimit()
                Log.d(TAG, "当前Binder事务限制: $limit 对象")
            } catch (e: Exception) {
                Log.w(TAG, "获取Binder限制失败: ${e.message}")
            }
            
            Log.d(TAG, "Binder事务错误修复已应用")
        } catch (e: Exception) {
            Log.e(TAG, "修复Binder事务错误时出现严重错误: ${e.message}")
        }
    }
    
    /**
     * 处理特定的BR_29201错误
     * 针对logs中反复出现的"BR_*: 29201, error: -1 (Operation not permitted)"
     */
    fun handleSpecificBinderError29201() {
        try {
            Log.d(TAG, "正在处理Binder错误29201")
            
            // 此错误通常是权限问题导致的
            // 1. 首先尝试刷新进程间通信状态
            fixBinderTransactionFailures()
            
            // 2. 特殊处理：针对多数情况是由于进程持有太多Binder引用导致的
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                try {
                    // 尝试手动释放并重建Binder
                    val transaction = Parcel.obtain()
                    val reply = Parcel.obtain()
                    
                    try {
                        // 执行一个空事务来刷新通信
                        transaction.writeInterfaceToken("android.os.IServiceManager")
                        transaction.writeInt(0) // 无数据
                        
                        try {
                            // 获取ServiceManager并执行事务
                            val serviceManagerClass = Class.forName("android.os.ServiceManager")
                            val getIServiceManagerMethod = 
                                serviceManagerClass.getDeclaredMethod("getIServiceManager")
                            getIServiceManagerMethod.isAccessible = true
                            val serviceManager = getIServiceManagerMethod.invoke(null)
                            
                            // 执行事务
                            val binderClass = serviceManager.javaClass.superclass
                            val transactMethod = binderClass.getDeclaredMethod(
                                "transact", Int::class.java, Parcel::class.java, 
                                Parcel::class.java, Int::class.java)
                            
                            // PING_TRANSACTION = 通常是('_'<<24)|('P'<<16)|('N'<<8)|'G'
                            // 使用一个安全的常量值1599098439 ('_PNʇ')或使用IBinder.PING_TRANSACTION
                            transactMethod.invoke(serviceManager, 1599098439, transaction, reply, 0)
                            
                            Log.d(TAG, "成功执行Binder刷新事务")
                        } catch (e: Exception) {
                            Log.w(TAG, "执行Binder事务失败: ${e.message}")
                        }
                    } finally {
                        transaction.recycle()
                        reply.recycle()
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "创建刷新事务失败: ${e.message}")
                }
            }
            
            // 3. 设置更保守的Binder通信策略
            setConservativeBinderPolicy()
            
            Log.d(TAG, "Binder错误29201修复措施已应用")
        } catch (e: Exception) {
            Log.e(TAG, "处理Binder错误29201时出现异常: ${e.message}")
        }
    }
    
    /**
     * 设置保守的Binder通信策略
     * 用于减少Binder事务失败的可能性
     */
    fun setConservativeBinderPolicy() {
        try {
            // 降低单次Binder传输数据量
            MAX_BATCH_SIZE = 50 // 默认值是100，降低为50
            conservativeModeEnabled = true
            
            // 记录调整
            Log.d(TAG, "已设置保守Binder通信策略，最大批次大小: $MAX_BATCH_SIZE")
        } catch (e: Exception) {
            Log.e(TAG, "设置保守Binder策略失败: ${e.message}")
        }
    }
} 