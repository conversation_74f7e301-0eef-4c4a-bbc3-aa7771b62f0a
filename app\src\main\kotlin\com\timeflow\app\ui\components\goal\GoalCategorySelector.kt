package com.timeflow.app.ui.components.goal

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.data.model.GoalCategory
import com.timeflow.app.data.model.getDefaultGoalCategories
import com.timeflow.app.data.model.getTimeBasedCategories
import com.timeflow.app.data.model.getThemeBasedCategories

/**
 * 目标分类选择器组件
 * 参考知名应用的分类选择设计
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalCategorySelector(
    selectedCategoryId: String,
    onCategorySelected: (String) -> Unit,
    modifier: Modifier = Modifier,
    showAllCategories: Boolean = true,
    compactMode: Boolean = false
) {
    var expandedSection by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (compactMode) {
            // 紧凑模式：水平滚动显示
            CompactCategorySelector(
                selectedCategoryId = selectedCategoryId,
                onCategorySelected = onCategorySelected
            )
        } else {
            // 完整模式：分组显示
            FullCategorySelector(
                selectedCategoryId = selectedCategoryId,
                onCategorySelected = onCategorySelected,
                expandedSection = expandedSection,
                onSectionToggle = { section ->
                    expandedSection = if (expandedSection == section) null else section
                },
                showAllCategories = showAllCategories
            )
        }
    }
}

/**
 * 紧凑模式的分类选择器
 */
@Composable
private fun CompactCategorySelector(
    selectedCategoryId: String,
    onCategorySelected: (String) -> Unit
) {
    val categories = getDefaultGoalCategories()
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 4.dp)
    ) {
        items(categories) { category ->
            CategoryChip(
                category = category,
                isSelected = selectedCategoryId == category.id,
                onClick = { onCategorySelected(category.id) }
            )
        }
    }
}

/**
 * 完整模式的分类选择器
 */
@Composable
private fun FullCategorySelector(
    selectedCategoryId: String,
    onCategorySelected: (String) -> Unit,
    expandedSection: String?,
    onSectionToggle: (String) -> Unit,
    showAllCategories: Boolean
) {
    val timeBasedCategories = getTimeBasedCategories()
    val themeBasedCategories = getThemeBasedCategories()
    
    LazyColumn(
        modifier = Modifier.heightIn(max = 400.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 时间维度分类
        item {
            CategorySection(
                title = "时间维度",
                subtitle = "按时间框架分类",
                categories = timeBasedCategories,
                selectedCategoryId = selectedCategoryId,
                onCategorySelected = onCategorySelected,
                isExpanded = expandedSection == "time" || expandedSection == null,
                onToggle = { onSectionToggle("time") }
            )
        }
        
        // 主题维度分类
        if (showAllCategories) {
            item {
                CategorySection(
                    title = "主题维度",
                    subtitle = "按内容主题分类",
                    categories = themeBasedCategories,
                    selectedCategoryId = selectedCategoryId,
                    onCategorySelected = onCategorySelected,
                    isExpanded = expandedSection == "theme" || expandedSection == null,
                    onToggle = { onSectionToggle("theme") }
                )
            }
        }
    }
}

/**
 * 分类区域组件
 */
@Composable
private fun CategorySection(
    title: String,
    subtitle: String,
    categories: List<GoalCategory>,
    selectedCategoryId: String,
    onCategorySelected: (String) -> Unit,
    isExpanded: Boolean,
    onToggle: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 区域标题
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onToggle() },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1F2937)
                    )
                    Text(
                        text = subtitle,
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280)
                    )
                }
                
                Icon(
                    imageVector = Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "收起" else "展开",
                    tint = Color(0xFF6B7280),
                    modifier = Modifier.rotate(if (isExpanded) 180f else 0f)
                )
            }
            
            // 分类列表
            if (isExpanded) {
                Spacer(modifier = Modifier.height(12.dp))
                
                categories.chunked(2).forEach { rowCategories ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        rowCategories.forEach { category ->
                            CategoryCard(
                                category = category,
                                isSelected = selectedCategoryId == category.id,
                                onClick = { onCategorySelected(category.id) },
                                modifier = Modifier.weight(1f)
                            )
                        }
                        
                        // 如果这一行只有一个分类，添加空白占位
                        if (rowCategories.size == 1) {
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

/**
 * 分类卡片组件
 */
@Composable
private fun CategoryCard(
    category: GoalCategory,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) category.color.copy(alpha = 0.1f) else Color.Transparent,
        animationSpec = tween(300)
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) category.color else Color(0xFFE5E7EB),
        animationSpec = tween(300)
    )
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 分类图标
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = category.color.copy(alpha = 0.2f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = category.icon,
                        contentDescription = null,
                        tint = category.color,
                        modifier = Modifier.size(18.dp)
                    )
                }
                
                // 分类信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = category.name,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1F2937)
                    )
                    Text(
                        text = category.timeFrame.displayName,
                        fontSize = 11.sp,
                        color = Color(0xFF6B7280)
                    )
                }
                
                // 选中标识
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已选中",
                        tint = category.color,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 分类芯片组件（用于紧凑模式）
 */
@Composable
private fun CategoryChip(
    category: GoalCategory,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) category.color else Color(0xFFF3F4F6),
        animationSpec = tween(300)
    )
    
    val contentColor by animateColorAsState(
        targetValue = if (isSelected) Color.White else Color(0xFF374151),
        animationSpec = tween(300)
    )
    
    Surface(
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .clickable { onClick() },
        color = backgroundColor,
        shape = RoundedCornerShape(20.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Icon(
                imageVector = category.icon,
                contentDescription = null,
                tint = contentColor,
                modifier = Modifier.size(16.dp)
            )
            
            Text(
                text = category.name,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = contentColor
            )
        }
    }
}
