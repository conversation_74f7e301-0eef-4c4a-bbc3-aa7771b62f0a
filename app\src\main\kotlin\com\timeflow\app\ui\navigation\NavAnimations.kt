package com.timeflow.app.ui.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.IntOffset
import androidx.navigation.NavBackStackEntry

/**
 * 🚀 流畅页面切换动画系统
 * 参照 iOS、Material Design 和微信等知名应用的切换设计
 * 提供丝滑的用户体验
 */

// ===== 动画时长配置 =====
private const val FAST_ANIMATION_DURATION = 250      // 快速切换
private const val STANDARD_ANIMATION_DURATION = 350  // 标准切换
private const val SLOW_ANIMATION_DURATION = 450      // 慢速切换（用于复杂页面）

// ===== 缓动函数配置 =====
// 参照iOS的流畅曲线
private val FluidEasing = CubicBezierEasing(0.25f, 0.1f, 0.25f, 1.0f)
// Material Design 3推荐的标准缓动
private val MaterialEasing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
// 弹性效果  
private val SpringEasing = spring<Float>(dampingRatio = 0.8f, stiffness = 300f)

/**
 * 🎨 主页面切换动画 - 类似iOS的推拉效果
 * 用于主要页面间的导航（如底部导航栏切换）
 */
object MainPageTransitions {
    
    // 从右侧滑入（前进）
    fun slideInFromRight(): EnterTransition {
    return slideInHorizontally(
        initialOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = FluidEasing
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION / 2,
                delayMillis = STANDARD_ANIMATION_DURATION / 4,
                easing = LinearEasing
            )
        )
    }
    
    // 向左侧滑出（前进时的退出）
    fun slideOutToLeft(): ExitTransition {
        return slideOutHorizontally(
            targetOffsetX = { fullWidth -> -fullWidth / 3 },
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = FluidEasing
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
    
    // 从左侧滑入（返回）
    fun slideInFromLeft(): EnterTransition {
        return slideInHorizontally(
            initialOffsetX = { fullWidth -> -fullWidth / 3 },
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = FluidEasing
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION / 2,
                delayMillis = STANDARD_ANIMATION_DURATION / 4,
                easing = LinearEasing
            )
        )
    }
    
    // 向右侧滑出（返回时的退出）
    fun slideOutToRight(): ExitTransition {
        return slideOutHorizontally(
            targetOffsetX = { fullWidth -> fullWidth },
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = FluidEasing
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = STANDARD_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
}

/**
 * 💫 模态页面动画 - 类似iOS的模态弹出效果
 * 用于弹出式页面（如设置、详情等）
 */
object ModalTransitions {
    
    // 从底部滑入 - 类似iOS模态弹出
    fun slideInFromBottom(): EnterTransition {
        return slideInVertically(
            initialOffsetY = { fullHeight -> fullHeight },
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        ) + scaleIn(
            initialScale = 0.95f,
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        )
    }
    
    // 向底部滑出
    fun slideOutToBottom(): ExitTransition {
        return slideOutVertically(
            targetOffsetY = { fullHeight -> fullHeight },
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        ) + scaleOut(
            targetScale = 0.95f,
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        )
    }
    
    // 从顶部滑入
    fun slideInFromTop(): EnterTransition {
        return slideInVertically(
            initialOffsetY = { fullHeight -> -fullHeight },
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
    
    // 向顶部滑出
    fun slideOutToTop(): ExitTransition {
        return slideOutVertically(
            targetOffsetY = { fullHeight -> -fullHeight },
            animationSpec = spring(
                dampingRatio = 0.8f,
                stiffness = 400f
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
}

/**
 * ✨ 轻量级动画 - 类似微信的快速切换
 * 用于快速页面切换，减少等待感
 */
object LightTransitions {
    
    // 快速淡入
    fun quickFadeIn(): EnterTransition {
        return fadeIn(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = MaterialEasing
            )
        ) + scaleIn(
            initialScale = 0.98f,
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = MaterialEasing
            )
        )
    }
    
    // 快速淡出
    fun quickFadeOut(): ExitTransition {
        return fadeOut(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = MaterialEasing
            )
        ) + scaleOut(
            targetScale = 0.98f,
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = MaterialEasing
            )
        )
    }
}

/**
 * 🌟 特殊效果动画 - 用于特殊场景
 */
object SpecialTransitions {
    
    // 弹性进入 - 适合重要操作完成后的页面
    fun bounceIn(): EnterTransition {
        return scaleIn(
            initialScale = 0.8f,
            animationSpec = spring(
                dampingRatio = 0.6f,
                stiffness = 300f
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
    
    // 弹性退出
    fun bounceOut(): ExitTransition {
        return scaleOut(
            targetScale = 0.8f,
            animationSpec = spring(
                dampingRatio = 0.6f,
                stiffness = 300f
            )
        ) + fadeOut(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = LinearEasing
            )
        )
    }
    
    // 爆炸式进入 - 适合创建成功等场景
    fun explodeIn(): EnterTransition {
        return scaleIn(
            initialScale = 1.2f,
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION,
                easing = FastOutSlowInEasing
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = FAST_ANIMATION_DURATION / 2,
                easing = LinearEasing
            )
        )
    }
}

/**
 * 🎯 底部导航栏切换动画 - 类似抖音、微信等app的底部切换
 */
object BottomNavTransitions {
    
    // 无动画切换 - 底部导航栏通常不需要复杂动画
    fun noAnimation(): Pair<EnterTransition, ExitTransition> {
        return Pair(EnterTransition.None, ExitTransition.None)
    }
    
    // 轻微交叉淡入淡出
    fun crossFade(): Pair<EnterTransition, ExitTransition> {
        return Pair(
            fadeIn(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = LinearEasing
                )
            ),
            fadeOut(
                animationSpec = tween(
                    durationMillis = 200,
                    easing = LinearEasing
                )
            )
        )
    }
}

/**
 * 📱 预定义的页面组合
 * 根据页面类型选择合适的动画组合
 */
object PageTransitions {
    
    // 主要页面导航（首页 -> 任务列表）
    val MAIN_NAVIGATION = AnimationSet(
        enter = MainPageTransitions.slideInFromRight(),
        exit = MainPageTransitions.slideOutToLeft(),
        popEnter = MainPageTransitions.slideInFromLeft(),
        popExit = MainPageTransitions.slideOutToRight()
    )
    
    // 模态页面（设置、详情等）
    val MODAL_NAVIGATION = AnimationSet(
        enter = ModalTransitions.slideInFromBottom(),
        exit = ModalTransitions.slideOutToBottom(),
        popEnter = ModalTransitions.slideInFromBottom(),
        popExit = ModalTransitions.slideOutToBottom()
    )
    
    // 轻量级切换（快速跳转）
    val LIGHT_NAVIGATION = AnimationSet(
        enter = LightTransitions.quickFadeIn(),
        exit = LightTransitions.quickFadeOut(),
        popEnter = LightTransitions.quickFadeIn(),
        popExit = LightTransitions.quickFadeOut()
    )
    
    // 底部导航切换
    val BOTTOM_NAV = AnimationSet(
        enter = EnterTransition.None,
        exit = ExitTransition.None,
        popEnter = EnterTransition.None,
        popExit = ExitTransition.None
    )
    
    // 特殊场景（任务创建成功等）
    val SPECIAL_NAVIGATION = AnimationSet(
        enter = SpecialTransitions.bounceIn(),
        exit = SpecialTransitions.bounceOut(),
        popEnter = SpecialTransitions.bounceIn(),
        popExit = SpecialTransitions.bounceOut()
    )
}

/**
 * 动画组合数据类
 */
data class AnimationSet(
    val enter: EnterTransition,
    val exit: ExitTransition,
    val popEnter: EnterTransition,
    val popExit: ExitTransition
)

// ===== 向后兼容的API =====

/**
 * 导航动画 - 默认进入转场效果
 */
fun animatedComposableEnterTransition(): EnterTransition = MainPageTransitions.slideInFromRight()

/**
 * 导航动画 - 默认退出转场效果
 */
fun animatedComposableExitTransition(): ExitTransition = MainPageTransitions.slideOutToLeft()

/**
 * 导航动画 - 返回进入转场效果
 */
fun animatedComposablePopEnterTransition(): EnterTransition = MainPageTransitions.slideInFromLeft()

/**
 * 导航动画 - 返回退出转场效果
 */
fun animatedComposablePopExitTransition(): ExitTransition = MainPageTransitions.slideOutToRight()

/**
 * 导航动画 - 向上浮动进入
 */
fun floatInFromBottom(): EnterTransition = ModalTransitions.slideInFromBottom()

/**
 * 导航动画 - 向下沉退出
 */
fun floatOutToBottom(): ExitTransition = ModalTransitions.slideOutToBottom()

/**
 * 导航动画 - 淡入
 */
fun fadeInTransition(): EnterTransition = LightTransitions.quickFadeIn()

/**
 * 导航动画 - 淡出
 */
fun fadeOutTransition(): ExitTransition = LightTransitions.quickFadeOut()

/**
 * 导航动画 - 缩放进入
 */
fun scaleInTransition(): EnterTransition = SpecialTransitions.bounceIn()

/**
 * 导航动画 - 缩放退出
 */
fun scaleOutTransition(): ExitTransition = SpecialTransitions.bounceOut() 