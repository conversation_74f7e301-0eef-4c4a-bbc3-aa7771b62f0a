package com.timeflow.app.ui.settings

import android.content.Context
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.di.PreferenceKeys
import com.timeflow.app.di.DataStoreModule.ThemeDataStore
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * 主题设置ViewModel
 * 管理主题模式、动态颜色和自定义颜色设置
 */
@HiltViewModel
class ThemeSettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    @ThemeDataStore private val themeDataStore: DataStore<Preferences>
) : ViewModel() {
    
    // 界面状态数据类
    data class UiState(
        val themeMode: ThemeMode = ThemeMode.FOLLOW_SYSTEM,
        val useDynamicColor: Boolean = true,
        val customPrimaryColor: Color = Color(0xFF007AFF),
        val customBackgroundColor: Color = Color(0xFFF9F9F9),
        val homePageColor: Color = Color(0xFFF9F9F9),
        val calendarPageColor: Color = Color(0xFFF9F9F9),
        val statisticsPageColor: Color = Color(0xFFF9F9F9),
        val profilePageColor: Color = Color(0xFFF9F9F9),
        val settingsPageColor: Color = Color(0xFFF9F9F9),
        val useUnifiedBackground: Boolean = false,
        val isColorPickerExpanded: Boolean = false,
        val currentEditingColorType: ColorType = ColorType.PRIMARY,
        val useCustomFont: Boolean = false,
        val customFontName: String = "",
        val customFontPath: String = "",
        val isProcessingFont: Boolean = false,
        val fontProcessingMessage: String = ""
    )
    
    // 界面状态流
    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    init {
        viewModelScope.launch {
            // 从DataStore加载设置
            themeDataStore.data.collect { preferences ->
                val themeModeString = preferences[PreferenceKeys.THEME_MODE] ?: ThemeMode.FOLLOW_SYSTEM.name
                val themeMode = try {
                    ThemeMode.valueOf(themeModeString)
                } catch (e: IllegalArgumentException) {
                    ThemeMode.FOLLOW_SYSTEM
                }
                
                val useDynamicColor = preferences[PreferenceKeys.USE_DYNAMIC_COLOR] ?: false
                
                // 安全读取颜色值，支持Int或Long类型
                val safeGetColor = { key: Preferences.Key<Long>, defaultValue: Long ->
                    try {
                        // 尝试直接获取Long值
                        preferences[key]
                    } catch (e: ClassCastException) {
                        // 如果是类型转换错误，尝试通过其他方式获取值
                        try {
                            // 获取偏好设置map
                            val preferencesMap = preferences.asMap()
                            
                            // 在map中查找与当前键名匹配的键值对
                            val entry = preferencesMap.entries.find { it.key.name == key.name }
                            val value = entry?.value
                            
                            // 根据实际类型转换
                            when (value) {
                                is Int -> value.toLong()
                                is Long -> value
                                else -> null
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("ThemeSettingsViewModel", "读取颜色值失败: ${e.message}", e)
                            null
                        }
                    } ?: defaultValue
                }
                
                // 使用安全读取方法获取颜色值
                val primaryColorLong = safeGetColor(PreferenceKeys.PRIMARY_COLOR, Color.Magenta.toArgb().toLong())
                val backgroundColorLong = safeGetColor(PreferenceKeys.BACKGROUND_COLOR, Color.White.toArgb().toLong())
                val homePageColorLong = safeGetColor(PreferenceKeys.HOME_PAGE_COLOR, backgroundColorLong)
                val calendarPageColorLong = safeGetColor(PreferenceKeys.CALENDAR_PAGE_COLOR, backgroundColorLong)
                val statisticsPageColorLong = safeGetColor(PreferenceKeys.STATISTICS_PAGE_COLOR, backgroundColorLong)
                val profilePageColorLong = safeGetColor(PreferenceKeys.PROFILE_PAGE_COLOR, backgroundColorLong)
                val settingsPageColorLong = safeGetColor(PreferenceKeys.SETTINGS_PAGE_COLOR, backgroundColorLong)
                val useUnifiedBackground = preferences[PreferenceKeys.USE_UNIFIED_BACKGROUND] ?: false
                
                // 读取字体设置
                val useCustomFont = preferences[PreferenceKeys.USE_CUSTOM_FONT] ?: false
                val customFontName = preferences[PreferenceKeys.CUSTOM_FONT_NAME] ?: ""
                val customFontPath = preferences[PreferenceKeys.CUSTOM_FONT_PATH] ?: ""
                
                // 更新UI状态，从Long转换为Int后创建Color对象
                _uiState.value = UiState(
                    themeMode = themeMode,
                    useDynamicColor = useDynamicColor,
                    customPrimaryColor = Color(primaryColorLong.toInt()),
                    customBackgroundColor = Color(backgroundColorLong.toInt()),
                    homePageColor = Color(homePageColorLong.toInt()),
                    calendarPageColor = Color(calendarPageColorLong.toInt()),
                    statisticsPageColor = Color(statisticsPageColorLong.toInt()),
                    profilePageColor = Color(profilePageColorLong.toInt()),
                    settingsPageColor = Color(settingsPageColorLong.toInt()),
                    useUnifiedBackground = useUnifiedBackground,
                    useCustomFont = useCustomFont,
                    customFontName = customFontName,
                    customFontPath = customFontPath
                )
                
                // 🔧 如果有自定义字体且当前FontManager中没有加载，则加载它
                if (useCustomFont && customFontPath.isNotEmpty()) {
                    // 检查是否已经在ThemeManager中加载过相同的字体
                    val currentFontInManager = com.timeflow.app.ui.theme.FontManager.currentFontFamily
                    val isDefaultFont = currentFontInManager == androidx.compose.ui.text.font.FontFamily.Default
                    
                    // 只有当前没有自定义字体或者路径不同时才重新加载
                    if (isDefaultFont) {
                        android.util.Log.d("ThemeSettingsViewModel", "FontManager中没有自定义字体，从设置中加载: $customFontName")
                        loadCustomFont(customFontPath)
                    } else {
                        android.util.Log.d("ThemeSettingsViewModel", "FontManager中已有自定义字体，跳过重复加载")
                    }
                }
                
                android.util.Log.d("ThemeSettingsViewModel", "从DataStore加载设置完成")
            }
        }
        
        // 监听ThemeSettingsRequested事件，响应请求发送当前主题设置
        viewModelScope.launch {
            EventBus.events.collect { event ->
                if (event is AppEvent.ThemeSettingsRequested) {
                    android.util.Log.d("ThemeSettingsViewModel", "收到ThemeSettingsRequested事件，发送当前主题设置")
                    
                    // 获取当前UI状态
                    val currentState = _uiState.value
                    
                    // 发送所有页面的背景色设置
                    try {
                        // 1. 发送统一背景设置
                        if (currentState.useUnifiedBackground) {
                            val colorArgb = currentState.customBackgroundColor.toArgb().toLong()
                            EventBus.tryEmit(AppEvent.ThemeSettingsChanged(
                                colorArgb = colorArgb,
                                useUnifiedBackground = true
                            ))
                            android.util.Log.d("ThemeSettingsViewModel", "发送统一背景设置：颜色=${colorArgb.toString(16)}")
                        }
                        
                        // 2. 发送各个页面的背景色设置
                        val pages = listOf(
                            "home" to currentState.homePageColor,
                            "calendar" to currentState.calendarPageColor,
                            "statistics" to currentState.statisticsPageColor,
                            "profile" to currentState.profilePageColor,
                            "settings" to currentState.settingsPageColor
                        )
                        
                        for ((pageName, color) in pages) {
                            val colorArgb = color.toArgb().toLong()
                            EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                                pageName = pageName,
                                colorArgb = colorArgb
                            ))
                            android.util.Log.d("ThemeSettingsViewModel", "发送${pageName}页面背景色设置：颜色=${colorArgb.toString(16)}")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("ThemeSettingsViewModel", "响应ThemeSettingsRequested事件时出错", e)
                    }
                }
            }
        }
    }
    
    /**
     * 更新动态颜色设置
     */
    fun updateUseDynamicColor(enabled: Boolean) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(useDynamicColor = enabled)
            saveSettingToDataStore(PreferenceKeys.USE_DYNAMIC_COLOR, enabled)
        }
    }
    
    /**
     * 更新主题模式
     */
    fun updateThemeMode(mode: ThemeMode) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(themeMode = mode)
            saveSettingToDataStore(PreferenceKeys.THEME_MODE, mode.name)
            
            // 根据主题模式更新相关设置
            when (mode) {
                ThemeMode.LIGHT -> {
                    // 设置为浅色模式，不跟随系统
                    saveSettingToDataStore(PreferenceKeys.USE_SYSTEM_DARK_MODE, false)
                    saveSettingToDataStore(PreferenceKeys.IS_DARK_MODE, false)
                    
                    // 通过ThemeManager更新主题
                    com.timeflow.app.ui.theme.ThemeManager.updateSystemDarkMode(false)
                    com.timeflow.app.ui.theme.ThemeManager.toggleDarkMode(false)
                }
                ThemeMode.DARK -> {
                    // 设置为暗色模式，不跟随系统
                    saveSettingToDataStore(PreferenceKeys.USE_SYSTEM_DARK_MODE, false)
                    saveSettingToDataStore(PreferenceKeys.IS_DARK_MODE, true)
                    
                    // 通过ThemeManager更新主题
                    com.timeflow.app.ui.theme.ThemeManager.updateSystemDarkMode(false)
                    com.timeflow.app.ui.theme.ThemeManager.toggleDarkMode(true)
                }
                ThemeMode.FOLLOW_SYSTEM -> {
                    // 设置为跟随系统
                    saveSettingToDataStore(PreferenceKeys.USE_SYSTEM_DARK_MODE, true)
                    
                    // 获取当前系统的暗色模式状态
                    val currentNightMode = context.resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
                    val isSystemDark = currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
                    
                    // 更新暗色模式状态以匹配系统
                    saveSettingToDataStore(PreferenceKeys.IS_DARK_MODE, isSystemDark)
                    
                    // 通过ThemeManager更新主题
                    com.timeflow.app.ui.theme.ThemeManager.updateSystemDarkMode(true)
                    com.timeflow.app.ui.theme.ThemeManager.toggleDarkMode(isSystemDark)
                }
            }
            
            android.util.Log.d("ThemeSettingsViewModel", "主题模式已更新为: $mode")
        }
    }
    
    /**
     * 更新自定义主题色
     */
    fun updateCustomPrimaryColor(color: Color) {
        viewModelScope.launch {
            // 🔧 修复：先更新UI状态
            _uiState.value = _uiState.value.copy(customPrimaryColor = color)

            // 🔧 修复：添加防抖动机制，避免频繁更新
            delay(100) // 100ms防抖动

            // 🔧 修复：只通过ThemeManager更新，避免重复写入DataStore
            try {
                com.timeflow.app.ui.theme.ThemeManager.updateThemePreference(
                    updateBlock = { currentPreference ->
                        currentPreference.copy(primaryColor = color)
                    }
                )

                android.util.Log.d("ThemeSettingsViewModel", "✅ 已更新主题色: ${color.toArgb().toString(16)}")
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "❌ 更新主题色失败，使用回退方案: ${e.message}", e)

                // 🔧 如果ThemeManager更新失败，回退到直接保存（修复颜色值转换）
                try {
                    saveSettingToDataStore(PreferenceKeys.PRIMARY_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                    android.util.Log.d("ThemeSettingsViewModel", "✅ 回退保存主题色成功")
                } catch (fallbackError: Exception) {
                    android.util.Log.e("ThemeSettingsViewModel", "❌ 回退保存也失败: ${fallbackError.message}", fallbackError)
                }
            }
        }
    }
    
    /**
     * 更新自定义背景色
     */
    fun updateCustomBackgroundColor(color: Color) {
        viewModelScope.launch {
            // 🔧 修复：先更新UI状态
            _uiState.value = _uiState.value.copy(customBackgroundColor = color)

            // 如果启用了统一背景，同时更新所有页面的背景色
            if (_uiState.value.useUnifiedBackground) {
                _uiState.value = _uiState.value.copy(
                    homePageColor = color,
                    calendarPageColor = color,
                    statisticsPageColor = color,
                    profilePageColor = color,
                    settingsPageColor = color
                )
            }

            // 🔧 修复：只通过ThemeManager更新，避免重复写入DataStore
            try {
                com.timeflow.app.ui.theme.ThemeManager.updateThemePreference(
                    updateBlock = { currentPreference ->
                        currentPreference.copy(
                            backgroundColor = color,
                            homePageColor = if (_uiState.value.useUnifiedBackground) color else currentPreference.homePageColor,
                            calendarPageColor = if (_uiState.value.useUnifiedBackground) color else currentPreference.calendarPageColor,
                            statisticsPageColor = if (_uiState.value.useUnifiedBackground) color else currentPreference.statisticsPageColor,
                            profilePageColor = if (_uiState.value.useUnifiedBackground) color else currentPreference.profilePageColor,
                            settingsPageColor = if (_uiState.value.useUnifiedBackground) color else currentPreference.settingsPageColor
                        )
                    }
                )

                android.util.Log.d("ThemeSettingsViewModel", "已更新背景色: ${color.toArgb().toString(16)}")
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "更新背景色失败: ${e.message}", e)

                // 🔧 如果ThemeManager更新失败，回退到直接保存（修复颜色值转换）
                saveSettingToDataStore(PreferenceKeys.BACKGROUND_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                if (_uiState.value.useUnifiedBackground) {
                    saveSettingToDataStore(PreferenceKeys.HOME_PAGE_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                    saveSettingToDataStore(PreferenceKeys.CALENDAR_PAGE_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                    saveSettingToDataStore(PreferenceKeys.STATISTICS_PAGE_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                    saveSettingToDataStore(PreferenceKeys.PROFILE_PAGE_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                    saveSettingToDataStore(PreferenceKeys.SETTINGS_PAGE_COLOR, color.toArgb().toLong() and 0xFFFFFFFFL)
                }
            }
        }
    }
    
    /**
     * 更新统一背景色
     */
    fun updateUseUnifiedBackground(enabled: Boolean) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(useUnifiedBackground = enabled)
            
            // 保存设置到数据存储
            saveSettingToDataStore(PreferenceKeys.USE_UNIFIED_BACKGROUND, enabled)
            
            // 通过ThemeManager更新主题偏好设置
            try {
                com.timeflow.app.ui.theme.ThemeManager.updateThemePreference(
                    updateBlock = { currentPreference ->
                        if (enabled) {
                            // 如果启用统一背景，使用自定义背景色更新所有页面颜色
                            currentPreference.copy(
                                backgroundColor = _uiState.value.customBackgroundColor,
                                homePageColor = _uiState.value.customBackgroundColor,
                                calendarPageColor = _uiState.value.customBackgroundColor,
                                statisticsPageColor = _uiState.value.customBackgroundColor,
                                settingsPageColor = _uiState.value.customBackgroundColor
                            )
                        } else {
                            // 如果禁用统一背景，保持当前页面颜色不变
                            currentPreference.copy(
                                backgroundColor = _uiState.value.customBackgroundColor
                            )
                        }
                    }
                )
                
                android.util.Log.d("ThemeSettingsViewModel", "已更新统一背景设置: enabled=$enabled")
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "更新统一背景设置时发送事件失败: ${e.message}")
            }
        }
    }
    
    /**
     * 更新特定页面的背景色
     */
    fun updatePageBackgroundColor(type: ColorType, color: Color) {
        viewModelScope.launch {
            // 记录颜色更新开始
            android.util.Log.d("ThemeSettingsViewModel", "开始更新页面背景色: 类型=${type.name}, 颜色=${color.toArgb().toString(16)}")
            
            val newState = when (type) {
                ColorType.HOME -> _uiState.value.copy(homePageColor = color)
                ColorType.CALENDAR -> _uiState.value.copy(calendarPageColor = color)
                ColorType.STATISTICS -> _uiState.value.copy(statisticsPageColor = color)
                ColorType.PROFILE -> _uiState.value.copy(profilePageColor = color)
                ColorType.SETTINGS -> _uiState.value.copy(settingsPageColor = color)
                else -> _uiState.value // 其他颜色类型不处理
            }
            
            _uiState.value = newState
            
            // 记录新状态中的颜色值
            android.util.Log.d("ThemeSettingsViewModel", "新状态中的颜色值: ${type.name}=${color.toArgb().toString(16)}")
            
            // 保存对应页面的背景色
            val preferenceKey = when (type) {
                ColorType.HOME -> PreferenceKeys.HOME_PAGE_COLOR
                ColorType.CALENDAR -> PreferenceKeys.CALENDAR_PAGE_COLOR
                ColorType.STATISTICS -> PreferenceKeys.STATISTICS_PAGE_COLOR
                ColorType.PROFILE -> PreferenceKeys.PROFILE_PAGE_COLOR
                ColorType.SETTINGS -> PreferenceKeys.SETTINGS_PAGE_COLOR
                else -> null
            }
            
            preferenceKey?.let {
                // 🔧 修复：只通过ThemeManager更新，避免重复写入DataStore
                try {
                    com.timeflow.app.ui.theme.ThemeManager.updateThemePreference(
                        updateBlock = { currentPreference ->
                            when (type) {
                                ColorType.HOME -> currentPreference.copy(homePageColor = color)
                                ColorType.CALENDAR -> currentPreference.copy(calendarPageColor = color)
                                ColorType.STATISTICS -> currentPreference.copy(statisticsPageColor = color)
                                ColorType.PROFILE -> currentPreference.copy(profilePageColor = color)
                                ColorType.SETTINGS -> currentPreference.copy(settingsPageColor = color)
                                else -> currentPreference
                            }
                        }
                    )

                    android.util.Log.d("ThemeSettingsViewModel", "已更新${type.name}页面背景色")
                } catch (e: Exception) {
                    android.util.Log.e("ThemeSettingsViewModel", "更新页面背景色失败: ${e.message}", e)

                    // 🔧 如果ThemeManager更新失败，回退到直接保存
                    val longColorValue = color.toArgb().toLong() and 0xFFFFFFFFL
                    saveSettingToDataStore(it, longColorValue)
                }
                
                // 直接通过ThemeManager广播页面背景色变更
                // 确定对应的页面名称
                val pageName = when (type) {
                    ColorType.HOME -> "home"
                    ColorType.CALENDAR -> "calendar"
                    ColorType.STATISTICS -> "statistics"
                    ColorType.PROFILE -> "profile"
                    ColorType.SETTINGS -> "settings"
                    else -> null
                }
                
                // 如果是支持的页面类型，立即广播页面背景色更新事件
                pageName?.let { name ->
                    try {
                        // 直接调用ThemeManager广播方法，确保立即更新
                        android.util.Log.d("ThemeSettingsViewModel", "立即广播${name}页面背景色更新")
                        com.timeflow.app.ui.theme.ThemeManager.broadcastPageBackgroundColor(name, color)
                        
                        // 记录事件发送
                        android.util.Log.d("ThemeSettingsViewModel", "已通过ThemeManager广播${name}页面背景色更新完成")
                    } catch (e: Exception) {
                        // 记录异常，防止应用崩溃
                        android.util.Log.e("ThemeSettingsViewModel", "更新页面背景色时发送事件失败: ${e.message}", e)
                    }
                }
            }
        }
    }
    
    /**
     * 设置颜色选择器是否展开
     */
    fun setColorPickerExpanded(expanded: Boolean) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isColorPickerExpanded = expanded)
        }
    }
    
    /**
     * 设置当前编辑的颜色类型
     */
    fun setCurrentEditingColorType(type: ColorType) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(currentEditingColorType = type)
        }
    }
    
    /**
     * 重置所有设置为默认值
     */
    fun resetAllSettings() {
        viewModelScope.launch {
            val defaultState = UiState()
            _uiState.value = defaultState
            
            // 保存默认设置
            saveSettingToDataStore(PreferenceKeys.USE_DYNAMIC_COLOR, defaultState.useDynamicColor)
            saveSettingToDataStore(PreferenceKeys.THEME_MODE, defaultState.themeMode.name)
            saveSettingToDataStore(PreferenceKeys.PRIMARY_COLOR, defaultState.customPrimaryColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.BACKGROUND_COLOR, defaultState.customBackgroundColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.HOME_PAGE_COLOR, defaultState.homePageColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.CALENDAR_PAGE_COLOR, defaultState.calendarPageColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.STATISTICS_PAGE_COLOR, defaultState.statisticsPageColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.PROFILE_PAGE_COLOR, defaultState.profilePageColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.SETTINGS_PAGE_COLOR, defaultState.settingsPageColor.toArgb().toLong() and 0xFFFFFFFFL)
            saveSettingToDataStore(PreferenceKeys.USE_UNIFIED_BACKGROUND, defaultState.useUnifiedBackground)

            // 🔧 修复：清除当前预设主题ID，因为用户重置了设置
            saveSettingToDataStore(PreferenceKeys.CURRENT_PRESET_ID, "")

            // 通过ThemeManager更新主题偏好设置
            try {
                com.timeflow.app.ui.theme.ThemeManager.updateThemePreference(
                    updateBlock = { currentPreference ->
                        currentPreference.copy(
                            primaryColor = defaultState.customPrimaryColor,
                            secondaryColor = Color(0xFF03DAC6), // 默认次要色
                            backgroundColor = defaultState.customBackgroundColor,
                            surfaceColor = Color(0xFFFFFFFF), // 默认表面色
                            errorColor = Color(0xFFB00020), // 默认错误色
                            homePageColor = defaultState.homePageColor,
                            calendarPageColor = defaultState.calendarPageColor,
                            statisticsPageColor = defaultState.statisticsPageColor,
                            settingsPageColor = defaultState.settingsPageColor
                        )
                    }
                )
                
                android.util.Log.d("ThemeSettingsViewModel", "已重置所有设置并更新主题偏好")
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "重置设置时更新主题偏好失败: ${e.message}")
            }
        }
    }
    
    /**
     * 保存设置到数据存储 - Boolean值
     */
    private suspend fun saveSettingToDataStore(key: Preferences.Key<Boolean>, value: Boolean) {
        themeDataStore.edit { preferences ->
            preferences[key] = value
        }
    }
    
    /**
     * 保存设置到数据存储 - String值
     */
    private suspend fun saveSettingToDataStore(key: Preferences.Key<String>, value: String) {
        themeDataStore.edit { preferences ->
            preferences[key] = value
        }
    }
    
    /**
     * 保存设置到数据存储 - Int值
     */
    private suspend fun saveSettingToDataStore(key: Preferences.Key<Int>, value: Int) {
        themeDataStore.edit { preferences ->
            preferences[key] = value
        }
    }
    
    /**
     * 保存设置到数据存储 - Long值
     */
    private suspend fun saveSettingToDataStore(key: Preferences.Key<Long>, value: Long) {
            themeDataStore.edit { preferences ->
            preferences[key] = value
        }
    }
    
    /**
     * 重置到默认设置
     */
    fun resetToDefault() {
        resetAllSettings()
    }
    
    /**
     * 应用预设主题
     */
    fun applyPresetTheme(themeName: String) {
        viewModelScope.launch {
            val (primaryColor, backgroundColor) = when (themeName) {
                "莫兰迪" -> Color(0xFFB8A9A0) to Color(0xFFF7F5F2) // 莫兰迪灰棕
                "莫奈" -> Color(0xFFA8C4A0) to Color(0xFFF0F4F0)   // 莫奈绿
                "薰衣草" -> Color(0xFFC4B5D1) to Color(0xFFF5F3F7) // 莫兰迪紫
                "暖阳" -> Color(0xFFD4B8A8) to Color(0xFFF7F3F0)   // 莫兰迪暖棕
                "青瓷" -> Color(0xFFA8B8B8) to Color(0xFFF0F3F3)   // 莫兰迪青灰
                else -> Color(0xFFB8A9A0) to Color(0xFFF7F5F2) // 默认莫兰迪
            }
            
            // 更新主色调
            updateCustomPrimaryColor(primaryColor)
            
            // 更新背景色
            updateCustomBackgroundColor(backgroundColor)
            
            // 如果启用了统一背景，更新所有页面
            if (_uiState.value.useUnifiedBackground) {
                updatePageBackgroundColor(ColorType.HOME, backgroundColor)
                updatePageBackgroundColor(ColorType.CALENDAR, backgroundColor)
                updatePageBackgroundColor(ColorType.STATISTICS, backgroundColor)
                updatePageBackgroundColor(ColorType.PROFILE, backgroundColor)
                updatePageBackgroundColor(ColorType.SETTINGS, backgroundColor)
            }
            
            android.util.Log.d("ThemeSettingsViewModel", "已应用预设主题: $themeName")
        }
    }
    
    /**
     * 导入TTF字体文件
     */
    fun importFontFile(fontPath: String, fontName: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("ThemeSettingsViewModel", "开始导入字体: $fontName, 路径: $fontPath")
                
                _uiState.value = _uiState.value.copy(
                    isProcessingFont = true,
                    fontProcessingMessage = "正在处理字体文件..."
                )
                
                // 验证字体文件
                val fontFile = java.io.File(fontPath)
                if (!fontFile.exists()) {
                    android.util.Log.e("ThemeSettingsViewModel", "字体文件不存在: $fontPath")
                    _uiState.value = _uiState.value.copy(
                        isProcessingFont = false,
                        fontProcessingMessage = "字体文件不存在"
                    )
                    return@launch
                }
                
                if (!fontFile.name.lowercase().endsWith(".ttf")) {
                    android.util.Log.e("ThemeSettingsViewModel", "字体文件格式错误: ${fontFile.name}")
                    _uiState.value = _uiState.value.copy(
                        isProcessingFont = false,
                        fontProcessingMessage = "请选择TTF格式的字体文件"
                    )
                    return@launch
                }
                
                _uiState.value = _uiState.value.copy(
                    fontProcessingMessage = "正在加载字体..."
                )
                
                // 尝试加载字体
                val fontFamily = loadCustomFontFamily(fontPath)
                if (fontFamily == null) {
                    android.util.Log.e("ThemeSettingsViewModel", "字体加载失败: $fontPath")
                    _uiState.value = _uiState.value.copy(
                        isProcessingFont = false,
                        fontProcessingMessage = "字体文件格式不受支持或已损坏"
                    )
                    return@launch
                }
                
                // 应用字体
                com.timeflow.app.ui.theme.FontManager.setCustomFont(fontFamily, true)
                
                // 保存字体设置
                saveSettingToDataStore(PreferenceKeys.CUSTOM_FONT_PATH, fontPath)
                saveSettingToDataStore(PreferenceKeys.CUSTOM_FONT_NAME, fontName)
                saveSettingToDataStore(PreferenceKeys.USE_CUSTOM_FONT, true)
                
                _uiState.value = _uiState.value.copy(
                    useCustomFont = true,
                    customFontName = fontName,
                    customFontPath = fontPath,
                    isProcessingFont = false,
                    fontProcessingMessage = "字体已成功导入并应用"
                )
                
                android.util.Log.d("ThemeSettingsViewModel", "字体导入成功: $fontName -> $fontPath")
                
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "字体导入失败", e)
                _uiState.value = _uiState.value.copy(
                    isProcessingFont = false,
                    fontProcessingMessage = "字体导入失败: ${e.localizedMessage}"
                )
            }
        }
    }
    
    /**
     * 加载自定义字体并返回FontFamily
     */
    private fun loadCustomFontFamily(fontPath: String): androidx.compose.ui.text.font.FontFamily? {
        return try {
            android.util.Log.d("ThemeSettingsViewModel", "尝试加载字体: $fontPath")
            val fontFamily = com.timeflow.app.ui.theme.FontManager.loadCustomFont(fontPath)
            if (fontFamily != null) {
                android.util.Log.d("ThemeSettingsViewModel", "字体加载成功: $fontPath")
                fontFamily
            } else {
                android.util.Log.e("ThemeSettingsViewModel", "字体加载失败: $fontPath")
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeSettingsViewModel", "加载字体时出错: $fontPath", e)
            null
        }
    }
    
    /**
     * 加载自定义字体
     */
    private fun loadCustomFont(fontPath: String) {
        try {
            val fontFamily = loadCustomFontFamily(fontPath)
            if (fontFamily != null) {
                com.timeflow.app.ui.theme.FontManager.setCustomFont(fontFamily, true)
                android.util.Log.d("ThemeSettingsViewModel", "自定义字体应用成功: $fontPath")
            } else {
                android.util.Log.e("ThemeSettingsViewModel", "自定义字体加载失败: $fontPath")
            }
        } catch (e: Exception) {
            android.util.Log.e("ThemeSettingsViewModel", "加载自定义字体时出错: $fontPath", e)
        }
    }
    
    /**
     * 切换自定义字体使用状态
     */
    fun toggleCustomFont(useCustomFont: Boolean) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(useCustomFont = useCustomFont)
            saveSettingToDataStore(PreferenceKeys.USE_CUSTOM_FONT, useCustomFont)
            
            if (useCustomFont && _uiState.value.customFontPath.isNotEmpty()) {
                loadCustomFont(_uiState.value.customFontPath)
            } else {
                com.timeflow.app.ui.theme.FontManager.setCustomFont(null, false)
            }
        }
    }
    
    /**
     * 删除自定义字体
     */
    fun deleteCustomFont() {
        viewModelScope.launch {
            try {
                val fontPath = _uiState.value.customFontPath
                if (fontPath.isNotEmpty()) {
                    val fontFile = java.io.File(fontPath)
                    if (fontFile.exists()) {
                        fontFile.delete()
                    }
                }
                
                // 重置字体设置
                com.timeflow.app.ui.theme.FontManager.setCustomFont(null, false)
                saveSettingToDataStore(PreferenceKeys.USE_CUSTOM_FONT, false)
                saveSettingToDataStore(PreferenceKeys.CUSTOM_FONT_NAME, "")
                saveSettingToDataStore(PreferenceKeys.CUSTOM_FONT_PATH, "")
                
                _uiState.value = _uiState.value.copy(
                    useCustomFont = false,
                    customFontName = "",
                    customFontPath = "",
                    fontProcessingMessage = "自定义字体已删除"
                )
                
                android.util.Log.d("ThemeSettingsViewModel", "自定义字体已删除")
                
            } catch (e: Exception) {
                android.util.Log.e("ThemeSettingsViewModel", "删除自定义字体失败", e)
                _uiState.value = _uiState.value.copy(
                    fontProcessingMessage = "删除字体失败: ${e.localizedMessage}"
                )
            }
        }
    }
    
    /**
     * 清除字体处理消息
     */
    fun clearFontProcessingMessage() {
        _uiState.value = _uiState.value.copy(fontProcessingMessage = "")
    }
}