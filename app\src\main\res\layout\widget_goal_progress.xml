<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="20dp">

    <!-- 顶部标题区域 -->
    <LinearLayout
        android:id="@+id/widget_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💙"
                android:textSize="24sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="新技能"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/widget_overall_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="65%"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_accent_blue"
            android:background="@drawable/widget_card_background"
            android:padding="8dp"
            android:elevation="1dp" />

    </LinearLayout>

    <!-- 总体进度条 -->
    <ProgressBar
        android:id="@+id/widget_overall_progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="6dp"
        android:layout_below="@id/widget_header"
        android:layout_marginTop="16dp"
        android:progress="65"
        android:progressTint="@color/widget_accent_blue"
        android:visibility="gone" />

    <!-- 目标列表 -->
    <LinearLayout
        android:id="@+id/widget_goals_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/widget_header"
        android:layout_marginTop="24dp">

        <!-- 目标1 (习惯1) -->
        <LinearLayout
            android:id="@+id/widget_habit_1_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/widget_card_background"
            android:padding="16dp"
            android:layout_marginBottom="12dp"
            android:elevation="1dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📝"
                android:textSize="20sp"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_habit_1_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="新技能"
                    android:textSize="16sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <ProgressBar
                    android:id="@+id/widget_habit_1_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="4dp"
                    android:progress="81"
                    android:progressTint="@color/widget_accent_green"
                    android:visibility="gone" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:id="@+id/widget_habit_1_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="81%"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_accent_green"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/widget_habit_1_stats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="81→100"
                    android:textSize="10sp"
                    android:textColor="@color/widget_text_secondary"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- 目标2 (习惯2) -->
        <LinearLayout
            android:id="@+id/widget_habit_2_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/widget_card_background"
            android:padding="16dp"
            android:layout_marginBottom="12dp"
            android:elevation="1dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📖"
                android:textSize="20sp"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_habit_2_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="阅读 10 页书"
                    android:textSize="16sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <ProgressBar
                    android:id="@+id/widget_habit_2_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="4dp"
                    android:progress="48"
                    android:progressTint="@color/widget_accent_orange"
                    android:visibility="gone" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:id="@+id/widget_habit_2_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="48%"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_accent_orange"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/widget_habit_2_stats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="19→40"
                    android:textSize="10sp"
                    android:textColor="@color/widget_text_secondary"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- 目标3 -->
        <LinearLayout
            android:id="@+id/widget_goal_3_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/widget_card_background"
            android:padding="16dp"
            android:elevation="1dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🏃"
                android:textSize="20sp"
                android:layout_marginEnd="12dp" />

            <TextView
                android:id="@+id/widget_goal_3_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="锻炼 1 小时"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🔄"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部数字显示 -->
    <LinearLayout
        android:id="@+id/widget_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_below="@id/widget_goals_container"
        android:layout_marginTop="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3"
            android:textSize="48sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:fontFamily="sans-serif-light" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Project 50"
            android:textSize="14sp"
            android:textColor="@color/widget_accent_blue"
            android:fontFamily="sans-serif-medium"
            android:layout_marginStart="16dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/widget_empty_goals"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:layout_centerInParent="true"
        android:padding="32dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎯"
            android:textSize="48sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无目标"
            android:textSize="18sp"
            android:textColor="@color/widget_text_secondary"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="轻触添加新目标"
            android:textSize="14sp"
            android:textColor="@color/widget_text_tertiary"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</RelativeLayout>
