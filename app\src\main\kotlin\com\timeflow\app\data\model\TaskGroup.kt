package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import com.timeflow.app.data.model.Task

/**
 * 任务分组数据模型
 * 
 * @property id 分组ID
 * @property title 分组标题
 * @property type 分组类型
 * @property icon 分组图标
 * @property color 分组颜色
 * @property tasks 分组中的任务列表
 * @property order 分组顺序
 * @property isEditable 是否可编辑
 */
data class TaskGroup(
    val id: String,
    val title: String,
    val type: TaskGroupType,
    val icon: String? = null,
    val color: Color? = null,
    val tasks: List<Task> = emptyList(),
    val order: Int = 0,
    val isEditable: Boolean = false
) {
    /**
     * 获取分组内任务数量
     */
    val taskCount: Int
        get() = tasks.size

    /**
     * 获取分组内未完成任务数量
     */
    val uncompletedTaskCount: Int
        get() = tasks.count { !it.isCompleted }

    /**
     * 判断是否为系统分组
     */
    val isSystemGroup: Boolean
        get() = type != TaskGroupType.CUSTOM

    /**
     * 获取分组内任务的总进度
     */
    val progress: Float
        get() = if (tasks.isEmpty()) 0f else {
            tasks.count { it.isCompleted }.toFloat() / tasks.size
        }
}

/**
 * 任务分组类型
 */
enum class TaskGroupType {
    TIME,       // 时间分组（今天、明天、本周等）
    STATUS,     // 状态分组（待办、进行中、已完成等）
    PRIORITY,   // 优先级分组
    SMART,      // 智能分组（最近到期、重要任务等）
    CUSTOM,     // 自定义分组
    DEFAULT     // 默认分组（所有任务）
} 