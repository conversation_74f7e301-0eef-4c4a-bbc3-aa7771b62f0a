# 时间追踪页面实现说明

## 概述

时间追踪页面的设计风格参考了网易云音乐的播放页面，实现了一个优雅的唱片式计时器界面。它具有以下特点：

1. 唱片式旋转计时器：当计时器运行时，大型圆形计时器会像唱片一样旋转
2. 任务选择功能：可以选择不同的任务进行时间追踪
3. 专注模式切换：提供专注模式，减少干扰
4. 活动摘要卡片：显示今天的时间使用情况统计
5. 底部导航栏：方便在不同功能之间切换

## 文件结构

我们已经创建了以下文件：

1. `TimeTrackingComponents.kt` - 包含所有UI组件
2. `TimerUIComponents.kt` - 包含唱片式计时器UI组件
3. `TimeTrackingViewModel.kt` - 包含视图模型和数据类

以下是 `TimeTrackingScreen.kt` 的完整代码，由于系统问题无法直接编辑文件，请手动复制：

```kotlin
package com.timeflow.app.ui.timetracking

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.timetracking.components.*
import com.timeflow.app.utils.SystemBarManager

/**
 * 时间追踪页面
 * 参考网易云音乐播放页面的设计，提供直观的时间计时体验
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimeTrackingScreen(
    navController: NavController,
    viewModel: TimeTrackingViewModel = hiltViewModel()
) {
    // 状态
    val timerState by viewModel.timerState.collectAsState()
    val currentTask by viewModel.currentTask.collectAsState()
    val elapsedTime by viewModel.elapsedTime.collectAsState()
    val focusModeEnabled by viewModel.focusModeEnabled.collectAsState()
    val showTaskSelector by viewModel.showTaskSelector.collectAsState()

    // 颜色设置
    val backgroundColor = Color(0xFFF8F8F8)
    val primaryColor = Color(0xFF6571FF) // 主题蓝色
    
    // 渐变背景
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            primaryColor.copy(alpha = 0.1f),
            backgroundColor
        )
    )

    // 获取上下文和Activity引用
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it, false)
        }
    }

    // 主界面结构
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
    ) {
        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight())
        ) {
            // 顶部导航栏
            TopAppBar(
                title = { Text("时间追踪") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.toggleFocusMode() }) {
                        Icon(
                            imageVector = if (focusModeEnabled) Icons.Filled.Visibility else Icons.Filled.VisibilityOff,
                            contentDescription = if (focusModeEnabled) "退出专注模式" else "进入专注模式"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.Transparent,
                    titleContentColor = Color.Black,
                    actionIconContentColor = Color.Black,
                    navigationIconContentColor = Color.Black
                )
            )
            
            // 任务标题区域
            currentTask?.let { task ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp)
                        .clickable { viewModel.toggleTaskSelector() }
                ) {
                    Text(
                        text = task.name,
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 32.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = task.description.takeIf { it.isNotEmpty() } ?: "专注计时中...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.weight(1f),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        Icon(
                            imageVector = Icons.Filled.KeyboardArrowDown,
                            contentDescription = "选择任务",
                            tint = Color.Gray,
                            modifier = Modifier.padding(start = 4.dp)
                        )
                    }
                }
            } ?: Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp)
                    .clickable { viewModel.toggleTaskSelector() },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "点击选择任务",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.Gray
                )
            }
            
            // 中心计时器区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                // 使用唱片式计时器组件
                VinylStyleTimer(
                    elapsedTime = elapsedTime,
                    timerState = timerState,
                    primaryColor = primaryColor
                )
                
                // 当前活动信息卡片
                TimeActivityCard(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 16.dp, start = 16.dp, end = 16.dp),
                    highEfficiencyHours = 0.0f,
                    neutralHours = 8.0f,
                    distractionHours = 0.0f,
                    onDetailClick = { /* 导航到详细统计 */ }
                )
            }
            
            // 底部控制栏
            BottomControlBar(
                timerState = timerState,
                onStartClick = { viewModel.startTimer() },
                onPauseClick = { viewModel.pauseTimer() },
                onStopClick = { viewModel.stopTimer() },
                primaryColor = primaryColor
            )
        }
        
        // 任务选择器弹窗
        if (showTaskSelector) {
            TaskSelectorDialog(
                onDismiss = { viewModel.toggleTaskSelector() },
                onSelectTask = { task -> 
                    viewModel.setCurrentTask(task)
                    viewModel.toggleTaskSelector()
                },
                currentTaskId = currentTask?.id
            )
        }
        
        // 底部导航栏
        BottomNavigationBar(
            selectedTab = "时间",
            onTabSelected = { tabName ->
                when (tabName) {
                    "任务" -> navController.navigate("tasks")
                    "日历" -> navController.navigate("calendar")
                    "统计" -> navController.navigate("statistics")
                    "我的" -> navController.navigate("profile")
                }
            },
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}
```

## 功能亮点

1. **唱片式设计**：主计时器采用唱片式设计，在计时时会旋转，内圈和外圈以不同速度旋转，增加视觉层次感。
2. **任务选择器**：用户可以点击当前任务来打开任务选择器，选择不同的任务进行时间追踪。
3. **专注模式**：提供专注模式选项，减少应用内的干扰。
4. **活动摘要**：在底部显示今日时间使用情况，分为高效、中性和分心时间。
5. **底部导航**：提供底部导航栏，可以方便地在不同功能间切换。

## 使用说明

1. 点击任务名称可以打开任务选择器
2. 大圆形计时器旋转表示计时器正在运行
3. 底部的开始/暂停按钮控制计时
4. 顶部的专注模式按钮可以切换专注状态
5. 底部的活动摘要卡片显示当天的时间使用统计

这种设计不仅美观，而且直观易用，通过旋转的唱片式计时器，用户可以一目了然地知道当前是否在计时状态。 