package com.timeflow.app.ui.screen.task

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.LocalDateTime

/**
 * 任务时间同步测试工具
 * 用于验证TaskTimeManager和TaskTimeSync的功能
 */
object TaskTimeSyncTest {
    
    /**
     * 测试基本的时间更新和同步功能
     */
    suspend fun testBasicTimeSync(
        taskTimeManager: TaskTimeManager,
        taskTimeSync: TaskTimeSync
    ) {
        Log.d("TaskTimeSyncTest", "开始测试基本时间同步功能")
        
        val testTaskId = "test_task_001"
        val testDateTime = LocalDateTime.now().plusHours(2)
        
        try {
            // 模拟数据库更新函数
            val mockDatabaseUpdate: suspend (String, LocalDateTime?) -> Unit = { taskId, dateTime ->
                Log.d("TaskTimeSyncTest", "模拟数据库更新: taskId=$taskId, dateTime=$dateTime")
                delay(100) // 模拟数据库操作延迟
            }
            
            // 执行同步更新
            taskTimeSync.syncTaskTime(testTaskId, testDateTime, mockDatabaseUpdate)
            
            // 验证缓存是否更新
            val cachedTime = taskTimeManager.getTaskTime(testTaskId)
            if (cachedTime == testDateTime) {
                Log.d("TaskTimeSyncTest", "✅ 基本时间同步测试通过")
            } else {
                Log.e("TaskTimeSyncTest", "❌ 基本时间同步测试失败: 期望=$testDateTime, 实际=$cachedTime")
            }
            
        } catch (e: Exception) {
            Log.e("TaskTimeSyncTest", "❌ 基本时间同步测试异常", e)
        }
    }
    
    /**
     * 测试事件通知功能
     */
    suspend fun testEventNotification(
        taskTimeManager: TaskTimeManager,
        scope: CoroutineScope
    ) {
        Log.d("TaskTimeSyncTest", "开始测试事件通知功能")
        
        val testTaskId = "test_task_002"
        val testDateTime = LocalDateTime.now().plusDays(1)
        var eventReceived = false
        
        // 监听事件
        val job = scope.launch {
            taskTimeManager.timeUpdateEvents.collect { event ->
                if (event.taskId == testTaskId) {
                    Log.d("TaskTimeSyncTest", "收到时间更新事件: ${event.taskId} -> ${event.newDateTime}")
                    eventReceived = true
                }
            }
        }
        
        try {
            // 触发时间更新
            taskTimeManager.updateTaskTime(testTaskId, testDateTime, "Test")
            
            // 等待事件处理
            delay(200)
            
            if (eventReceived) {
                Log.d("TaskTimeSyncTest", "✅ 事件通知测试通过")
            } else {
                Log.e("TaskTimeSyncTest", "❌ 事件通知测试失败: 未收到事件")
            }
            
        } catch (e: Exception) {
            Log.e("TaskTimeSyncTest", "❌ 事件通知测试异常", e)
        } finally {
            job.cancel()
        }
    }
    
    /**
     * 测试批量更新功能
     */
    suspend fun testBatchUpdate(
        taskTimeManager: TaskTimeManager,
        taskTimeSync: TaskTimeSync
    ) {
        Log.d("TaskTimeSyncTest", "开始测试批量更新功能")
        
        val testUpdates = mapOf(
            "batch_task_001" to LocalDateTime.now().plusHours(1),
            "batch_task_002" to LocalDateTime.now().plusHours(2),
            "batch_task_003" to LocalDateTime.now().plusHours(3)
        )
        
        try {
            // 模拟批量数据库更新函数
            val mockBatchDatabaseUpdate: suspend (Map<String, LocalDateTime?>) -> Unit = { updates ->
                Log.d("TaskTimeSyncTest", "模拟批量数据库更新: ${updates.size}个任务")
                delay(200) // 模拟批量操作延迟
            }
            
            // 执行批量同步更新
            taskTimeSync.batchSyncTaskTimes(testUpdates, mockBatchDatabaseUpdate)
            
            // 验证所有任务的缓存是否更新
            var allUpdated = true
            testUpdates.forEach { (taskId, expectedTime) ->
                val cachedTime = taskTimeManager.getTaskTime(taskId)
                if (cachedTime != expectedTime) {
                    Log.e("TaskTimeSyncTest", "任务 $taskId 缓存更新失败: 期望=$expectedTime, 实际=$cachedTime")
                    allUpdated = false
                }
            }
            
            if (allUpdated) {
                Log.d("TaskTimeSyncTest", "✅ 批量更新测试通过")
            } else {
                Log.e("TaskTimeSyncTest", "❌ 批量更新测试失败")
            }
            
        } catch (e: Exception) {
            Log.e("TaskTimeSyncTest", "❌ 批量更新测试异常", e)
        }
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests(
        taskTimeManager: TaskTimeManager,
        taskTimeSync: TaskTimeSync,
        scope: CoroutineScope = CoroutineScope(Dispatchers.IO)
    ) {
        scope.launch {
            Log.d("TaskTimeSyncTest", "🚀 开始运行任务时间同步测试套件")
            
            testBasicTimeSync(taskTimeManager, taskTimeSync)
            delay(500)
            
            testEventNotification(taskTimeManager, scope)
            delay(500)
            
            testBatchUpdate(taskTimeManager, taskTimeSync)
            
            Log.d("TaskTimeSyncTest", "🏁 任务时间同步测试套件完成")
        }
    }
} 