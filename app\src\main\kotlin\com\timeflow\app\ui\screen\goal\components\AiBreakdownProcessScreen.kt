package com.timeflow.app.ui.screen.goal.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Lightbulb
import androidx.compose.material.icons.filled.Psychology
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.ui.theme.DustyLavender
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

/**
 * AI拆解过程界面
 * 显示AI进行目标拆解的进度和状态
 * @param goalTitle 目标标题
 * @param onBreakdownCompleted 拆解完成回调
 * @param modifier 修饰符
 * @param isBackupMode 是否使用备选模式，决定展示的步骤文案
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun AiBreakdownProcessScreen(
    goalTitle: String,
    onBreakdownCompleted: () -> Unit,
    modifier: Modifier = Modifier,
    isBackupMode: Boolean = false
) {
    // 进度状态
    var progress by remember { mutableStateOf(0f) }
    var progressPercent by remember { mutableStateOf(0) }
    var currentStep by remember { mutableStateOf(0) }
    
    // 背景呼吸动画
    val breathAnimation = rememberInfiniteTransition(label = "breathAnimation")
    val breathAlpha by breathAnimation.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "breathAlpha"
    )
    
    // 分析步骤 - 根据是否备选模式选择不同的步骤描述
    val analysisSteps = if (isBackupMode) {
        listOf(
            "分析目标内容",
            "识别目标类型",
            "确定任务模板",
            "调整任务细节",
            "生成执行时间线",
            "优化任务描述",
            "生成拆解方案"
        )
    } else {
        listOf(
            "目标分析与理解",
            "识别关键里程碑",
            "确定任务优先级",
            "生成执行时间线",
            "细化任务详情",
            "优化任务分配",
            "生成完整拆解方案"
        )
    }
    
    // 步骤描述 - 根据是否备选模式选择不同的步骤详情
    val stepDescriptions = if (isBackupMode) {
        listOf(
            "智能分析目标内容和上下文",
            "基于内容识别目标类型和范畴",
            "选择最适合当前目标的任务模板",
            "根据目标特点调整任务内容",
            "合理安排任务顺序和时间",
            "优化任务描述，增加可操作性",
            "生成最终的智能拆解方案"
        )
    } else {
        listOf(
            "理解目标要求和上下文",
            "确定关键成功指标和里程碑",
            "基于紧急性和重要性评估任务",
            "合理安排每个任务的时间",
            "为每个任务添加具体描述和建议",
            "优化整体任务分配和资源利用",
            "生成最终的详细拆解方案"
        )
    }
    
    // 动态处理提示 - 不同步骤展示不同的动态提示
    val stepDynamicHints = if (isBackupMode) {
        listOf(
            listOf("使用智能模板", "分析目标内容", "识别关键词"),
            listOf("判断目标类型", "匹配最佳模板", "优化模板选择"),
            listOf("适配任务模板", "调整任务结构", "优化任务数量"),
            listOf("细化任务描述", "增加可操作性", "确保任务相关性"),
            listOf("安排任务顺序", "调整时间分配", "确保执行可行性"),
            listOf("添加任务建议", "优化任务描述", "检查任务覆盖度"),
            listOf("生成最终方案", "检查任务逻辑性", "确保方案完整性")
        )
    } else {
        listOf(
            listOf("分析目标描述", "理解目标上下文", "提取关键要求"),
            listOf("识别关键节点", "确定成功指标", "明确里程碑"),
            listOf("评估任务优先级", "分析任务依赖性", "确定关键路径"),
            listOf("生成时间轴", "分配任务时间", "优化任务顺序"),
            listOf("丰富任务描述", "添加执行建议", "明确预期结果"),
            listOf("优化资源分配", "检查任务平衡性", "确保计划可行"),
            listOf("整合拆解方案", "校验计划完整性", "生成最终方案")
        )
    }
    
    // 模拟进度 - 添加更真实的进度模拟，使用更随机的时间
    LaunchedEffect(key1 = true) {
        // 生成一个随机因子，使每次的进度动画有些微差异
        val randomFactor = 0.8f + Random.nextFloat() * 0.4f  // 0.8-1.2之间的随机数
        
        // 前20%快速增长
        repeat(20) {
            delay((20 + Random.nextLong(10)) * randomFactor.toLong()) // 更随机的延迟
            progress = (it + 1) / 100f
            progressPercent = it + 1
            currentStep = 0
        }
        
        // 中间70%的处理过程，每个步骤有不同的时间分配
        val stepProgressRanges = listOf(
            0.2f to 0.3f,  // 步骤1: 20%-30%
            0.3f to 0.4f,  // 步骤2: 30%-40%
            0.4f to 0.5f,  // 步骤3: 40%-50%
            0.5f to 0.65f, // 步骤4: 50%-65%
            0.65f to 0.75f,// 步骤5: 65%-75%
            0.75f to 0.85f,// 步骤6: 75%-85%
            0.85f to 0.9f  // 步骤7: 85%-90%
        )
        
        // 逐步更新每个步骤的进度
        stepProgressRanges.forEachIndexed { index, (start, end) ->
            val stepPoints = ((end - start) * 100).toInt()
            
            repeat(stepPoints) {
                val delayTime = when (index) {
                    0, 6 -> 30L + Random.nextLong(10) // 开始和结束步骤较快
                    1, 5 -> 40L + Random.nextLong(20) // 中间步骤适中
                    else -> 50L + Random.nextLong(30) // 核心步骤较慢
                }
                
                delay((delayTime * randomFactor).toLong())
                progress = start + (it + 1) / 100f
                progressPercent = (progress * 100).toInt()
                currentStep = index
            }
        }
        
        // 最后10%快速完成
        repeat(10) {
            delay((25 + Random.nextLong(15)) * randomFactor.toLong())
            progress = 0.9f + (it + 1) / 100f
            progressPercent = 90 + it + 1
            currentStep = 6
        }
        
        // 完成时调用回调
        delay(300)
        onBreakdownCompleted()
    }
    
    // 颜色定义
    val backgroundColor = Color(0xFFF0F4FF)
    val primaryTextColor = MaterialTheme.colorScheme.primary
    val secondaryTextColor = Color(0xFF666666)
    
    // 当前步骤的动态提示
    val currentHints = stepDynamicHints.getOrElse(currentStep) { emptyList() }
    val currentHintIndex = remember { mutableStateOf(0) }
    
    // 自动切换当前提示
    LaunchedEffect(currentStep) {
        while (true) {
            delay(1500)
            currentHintIndex.value = (currentHintIndex.value + 1) % currentHints.size
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        backgroundColor,
                        Color(0xFFE6EDFF).copy(alpha = breathAlpha)
                    )
                )
            )
            .padding(24.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth()
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = null,
                    tint = DustyLavender,
                    modifier = Modifier.size(28.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "AI智能拆解进行中",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = primaryTextColor
                    )
                    Text(
                        text = "正在拆解: $goalTitle",
                        fontSize = 14.sp,
                        color = secondaryTextColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 自定义进度条 - 使用更加醒目的动画效果
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(36.dp)
                    .clip(RoundedCornerShape(18.dp))
                    .background(Color(0xFFE6EDFF))
            ) {
                // 波浪进度动画
                WaveProgressIndicator(
                    progress = progress,
                    modifier = Modifier.fillMaxSize()
                )
                
                // 进度文本
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp)
                ) {
                    Text(
                        text = "$progressPercent%",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    // 添加动态处理中文本
                    val loadingText = remember {
                        derivedStateOf {
                            when ((System.currentTimeMillis() / 300) % 4) {
                                0L -> "处理中"
                                1L -> "处理中."
                                2L -> "处理中.."
                                else -> "处理中..."
                            }
                        }
                    }
                    
                    // 仅在未完成时显示
                    if (progressPercent < 100) {
                        Text(
                            text = loadingText.value,
                            color = Color.White.copy(alpha = 0.9f),
                            fontSize = 14.sp
                        )
                    } else {
                        Text(
                            text = "完成!",
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Spacer(
                        modifier = Modifier
                            .weight(1f)
                            .height(1.dp)
                            .background(Color.Transparent)
                    )
                    
                    // 当前步骤文本
                    AnimatedContent(
                        targetState = if (currentStep < analysisSteps.size) analysisSteps[currentStep] else "",
                        transitionSpec = {
                            (fadeIn(animationSpec = tween(300)) + 
                             scaleIn(initialScale = 0.95f, animationSpec = tween(300))) with 
                            fadeOut(animationSpec = tween(300))
                        },
                        label = "stepAnimation"
                    ) { step ->
                        Text(
                            text = step,
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 分析步骤列表 - 增加步骤说明
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                analysisSteps.forEachIndexed { index, step ->
                    AnimatedVisibility(
                        visible = index <= currentStep,
                        enter = fadeIn(animationSpec = tween(500)) + 
                                expandVertically(animationSpec = tween(500)),
                        exit = fadeOut()
                    ) {
                        AnalysisStepItemImproved(
                            step = step,
                            description = stepDescriptions[index],
                            completed = index < currentStep,
                            isActive = index == currentStep
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 智能提示信息 - 底部提示
            AnimatedVisibility(
                visible = true,
                enter = fadeIn(animationSpec = tween(1000)),
                exit = fadeOut()
            ) {
                Text(
                    text = "AI会根据目标类型和历史完成数据智能拆解",
                    fontSize = 13.sp,
                    color = secondaryTextColor,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 波浪进度指示器
 */
@Composable
private fun WaveProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier
) {
    val waveColor = DustyLavender
    
    // 波浪动画
    val waveAnimatable = rememberInfiniteTransition(label = "waveAnimation")
    val wavePhase by waveAnimatable.animateFloat(
        initialValue = 0f,
        targetValue = 4 * Math.PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wavePhase"
    )
    
    Canvas(modifier = modifier) {
        val width = size.width
        val height = size.height
        val progressWidth = width * progress
        
        // 填充进度条背景
        drawRect(
            color = waveColor,
            size = Size(progressWidth, height)
        )
        
        if (progress > 0f && progress < 1f) {
            val path = Path()
            val amplitude = height * 0.1f
            
            // 波浪起点
            path.moveTo(progressWidth, height)
            
            // 底部直线
            path.lineTo(progressWidth, height)
            
            // 波浪顶部
            for (i in 0..size.width.toInt() step 5) {
                val x = progressWidth + i
                if (x > width) break
                
                val y = (amplitude * sin(i * 0.1f + wavePhase) + height / 2).coerceIn(0f, height)
                path.lineTo(x, y)
            }
            
            // 右侧边界
            path.lineTo(width, height)
            
            // 底部闭合
            path.lineTo(progressWidth, height)
            
            // 绘制波浪形状
            drawPath(
                path = path,
                color = waveColor.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 改进的分析步骤项 - 添加更多视觉效果
 */
@Composable
private fun AnalysisStepItemImproved(
    step: String,
    description: String,
    completed: Boolean,
    isActive: Boolean
) {
    val primaryColor = DustyLavender
    val backgroundColor = if (isActive) {
        primaryColor.copy(alpha = 0.15f)
    } else {
        Color(0xFFF5F7FA)
    }
    
    // 脉冲动画 - 当前步骤有呼吸效果
    val animatedAlpha = if (isActive) {
        val infiniteTransition = rememberInfiniteTransition(label = "pulseAnimation")
        infiniteTransition.animateFloat(
            initialValue = 0.6f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(800),
                repeatMode = RepeatMode.Reverse
            ),
            label = "alphaAnimation"
        )
    } else {
        remember { mutableStateOf(1f) }
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp))
            .background(backgroundColor)
            .border(
                width = if (isActive) 1.dp else 0.dp,
                color = if (isActive) primaryColor.copy(alpha = 0.5f) else Color.Transparent,
                shape = RoundedCornerShape(10.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 步骤状态指示器
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
                .background(primaryColor.copy(alpha = animatedAlpha.value))
                .padding(4.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 步骤内容
        Column {
            Text(
                text = step,
                fontSize = 15.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF333333)
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = description,
                fontSize = 13.sp,
                color = Color(0xFF666666)
            )
        }
    }
} 