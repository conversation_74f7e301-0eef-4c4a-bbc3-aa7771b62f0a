package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color

/**
 * 任务优先级枚举
 */
enum class Priority {
    URGENT,  // 紧急
    HIGH,    // 高
    MEDIUM,  // 中
    LOW      // 低
}

// 优先级颜色扩展属性
val Priority.color: Color
    get() = when (this) {
        Priority.URGENT -> Color(0xFFD50000)  // 深红色
        Priority.HIGH -> Color(0xFFF44336)    // 红色
        Priority.MEDIUM -> Color(0xFFFFA726)  // 橙色
        Priority.LOW -> Color(0xFF66BB6A)     // 绿色
    } 