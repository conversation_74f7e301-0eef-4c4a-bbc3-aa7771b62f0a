package com.timeflow.app.ui.viewmodel

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.repository.GoalRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import java.time.LocalDateTime
import kotlinx.coroutines.delay

@HiltViewModel
class GoalViewModel @Inject constructor(
    private val goalRepository: GoalRepository
) : ViewModel() {

    // UI状态
    private val _uiState = mutableStateOf<GoalUiState>(GoalUiState.Idle)
    val uiState: State<GoalUiState> = _uiState

    // 当前选中的目标及其子任务
    private val _currentGoal = mutableStateOf<Goal?>(null)
    val currentGoal: State<Goal?> = _currentGoal

    private val _subTasks = mutableStateOf<List<GoalSubTask>>(emptyList())
    val subTasks: State<List<GoalSubTask>> = _subTasks

    private val _availableModels = mutableStateOf<List<String>>(emptyList())
    val availableModels: State<List<String>> = _availableModels

    // 目标综合分析相关状态
    private val _totalCompletedGoals = mutableStateOf(0)
    val totalCompletedGoals: State<Int> = _totalCompletedGoals

    private val _inProgressGoals = mutableStateOf(0)
    val inProgressGoals: State<Int> = _inProgressGoals

    private val _avgCompletionRate = mutableStateOf(0f)
    val avgCompletionRate: State<Float> = _avgCompletionRate

    private val _goalsByTimeRange = mutableStateOf<Map<String, Int>>(emptyMap())
    val goalsByTimeRange: State<Map<String, Int>> = _goalsByTimeRange

    private val _goalsByDomain = mutableStateOf<Map<String, Int>>(emptyMap())
    val goalsByDomain: State<Map<String, Int>> = _goalsByDomain

    private val _goalsByDifficulty = mutableStateOf<Map<String, Int>>(emptyMap())
    val goalsByDifficulty: State<Map<String, Int>> = _goalsByDifficulty

    // 当前选择的时间维度
    private val _currentTimeRange = mutableStateOf("年")
    val currentTimeRange: State<String> = _currentTimeRange

    /**
     * 加载当前目标的详细信息
     */
    fun loadGoalDetail(goalId: String) {
        if (goalId.isBlank()) {
            _uiState.value = GoalUiState.Error("目标ID不能为空")
            return
        }
        
        _uiState.value = GoalUiState.Loading
        
        viewModelScope.launch {
            try {
                // 尝试加载目标详情
                val goalWithSubtasks = goalRepository.getGoalWithSubtasksById(goalId)
                if (goalWithSubtasks != null) {
                    _currentGoal.value = goalWithSubtasks.goal
                    _subTasks.value = goalWithSubtasks.subTasks
                    _uiState.value = GoalUiState.Success
                } else {
                    _uiState.value = GoalUiState.Error("找不到指定的目标")
                }
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error("加载目标详情失败: ${e.message}")
            }
        }
    }

    /**
     * 加载所有已完成的目标，用于综合分析
     */
    fun loadCompletedGoals() {
        _uiState.value = GoalUiState.Loading
        
        viewModelScope.launch {
            try {
                // 加载所有已完成的目标
                val completedGoals = goalRepository.getCompletedGoalsList()
                
                // 分析目标完成情况并更新状态
                _totalCompletedGoals.value = completedGoals.size
                
                // 获取进行中的目标数量
                _inProgressGoals.value = goalRepository.getInProgressGoalsCount()
                
                // 计算平均完成度
                val avgCompletionRate = if (completedGoals.isNotEmpty()) {
                    completedGoals.map { it.progressPercentage }.average().toFloat()
                        .coerceAtMost(1.0f) // 限制最大值为1.0 (100%)
                } else 0f
                
                _avgCompletionRate.value = avgCompletionRate
                
                // 获取各种维度的分析数据
                _goalsByTimeRange.value = goalRepository.getCompletedGoalsByTimeRange()
                _goalsByDomain.value = goalRepository.getCompletedGoalsByDomain()
                _goalsByDifficulty.value = goalRepository.getCompletedGoalsByDifficulty()
                
                // 更新UI状态为成功
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error("加载已完成目标失败: ${e.message}")
            }
        }
    }

    /**
     * 根据时间范围加载已完成的目标数据
     * @param timeRange 时间范围，可以是"年"、"季度"或"月"
     */
    fun loadCompletedGoalsByTimeRange(timeRange: String) {
        _uiState.value = GoalUiState.Loading
        _currentTimeRange.value = timeRange  // 更新当前选择的时间范围
        
        viewModelScope.launch {
            try {
                // 根据选择的时间范围加载对应数据
                val timeRangeData = when (timeRange) {
                    "年" -> loadYearlyGoals()
                    "季度" -> loadQuarterlyGoals()
                    "月" -> loadMonthlyGoals()
                    else -> loadYearlyGoals() // 默认
                }
                
                _goalsByTimeRange.value = timeRangeData
                
                // 更新UI状态为成功
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error("加载时间范围数据失败: ${e.message}")
            }
        }
    }

    /**
     * 加载年度目标数据(按季度分布)
     */
    private suspend fun loadYearlyGoals(): Map<String, Int> {
        // 模拟API调用，实际项目中应从goalRepository获取
        // 理想情况下应该从Repository获取，这里为了演示进行模拟
        delay(300) // 模拟网络延迟
        
        // 从Repository获取的数据，这里模拟了数据
        // 在真实项目中，应该调用 goalRepository.getCompletedGoalsByYear()
        return mapOf(
            "Q1" to 6,
            "Q2" to 4,
            "Q3" to 8,
            "Q4" to 5
        )
    }

    /**
     * 加载季度目标数据(按月分布)
     */
    private suspend fun loadQuarterlyGoals(): Map<String, Int> {
        // 模拟API调用
        delay(300)
        
        // 获取当前季度
        val currentMonth = LocalDateTime.now().monthValue
        val currentQuarter = (currentMonth - 1) / 3 + 1
        
        // 根据当前季度返回对应月份数据
        return when (currentQuarter) {
            1 -> mapOf("1月" to 3, "2月" to 2, "3月" to 1)
            2 -> mapOf("4月" to 2, "5月" to 1, "6月" to 3)
            3 -> mapOf("7月" to 4, "8月" to 2, "9月" to 2)
            else -> mapOf("10月" to 2, "11月" to 1, "12月" to 3)
        }
    }

    /**
     * 加载月度目标数据(按周分布)
     */
    private suspend fun loadMonthlyGoals(): Map<String, Int> {
        // 模拟API调用
        delay(300)
        
        // 返回当月的周数据
        return mapOf(
            "第1周" to 2,
            "第2周" to 1,
            "第3周" to 3,
            "第4周" to 2
        )
    }

    /**
     * 重置分析数据，适用于清理资源
     */
    fun resetAnalysisData() {
        viewModelScope.launch {
            // 重置状态，避免内存泄漏
            _goalsByTimeRange.value = emptyMap()
            _goalsByDomain.value = emptyMap()
            _goalsByDifficulty.value = emptyMap()
            
            // 重置状态为空闲
            _uiState.value = GoalUiState.Idle
        }
    }
}

sealed class GoalUiState {
    object Idle : GoalUiState()
    object Loading : GoalUiState()
    object Success : GoalUiState()
    data class Error(val message: String) : GoalUiState()
} 