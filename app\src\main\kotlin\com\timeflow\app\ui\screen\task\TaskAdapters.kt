package com.timeflow.app.ui.screen.task

import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import java.time.LocalDateTime
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.areAllSubTasksCompleted
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.TaskData

/**
 * TaskData与SubTask之间的转换器函数
 */
fun convertSubTaskToTaskData(subTask: SubTask): TaskData {
    return TaskData(
        id = subTask.id,
        title = subTask.title,
        description = subTask.note,
        dueDate = subTask.reminderTime,
        priority = subTask.priority,
        status = if (subTask.isCompleted) "已完成" else "待办",
        tags = emptyList(),
        daysLeft = 0,
        subTasks = emptyList()
    )
}

/**
 * TaskData与Task之间的转换器函数
 */
fun convertTaskDataToTask(taskData: TaskData): Task {
    return Task(
        id = taskData.id,
        title = taskData.title,
        description = taskData.description,
        dueDate = taskData.dueDate,
        priority = taskData.priority,
        status = taskData.status,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now(),
        depth = 0
    )
}

/**
 * 将model.TaskData转换为UI层的TaskData
 * 用于在不同模块之间转换数据
 */
fun convertModelTaskDataToUITaskData(modelTaskData: ModelTaskData): TaskData {
    // 将ModelTaskData的urgency转换为UI层的Priority
    val priority = when(modelTaskData.urgency) {
        TaskUrgency.CRITICAL -> Priority.URGENT
        TaskUrgency.HIGH -> Priority.HIGH
        TaskUrgency.MEDIUM -> Priority.MEDIUM
        TaskUrgency.LOW -> Priority.LOW
    }
    
    // 创建UI层的子任务列表
    val subTasks = modelTaskData.subTasks
    
    return TaskData(
        id = modelTaskData.id,
        title = modelTaskData.title,
        description = modelTaskData.description,
        priority = priority,
        status = if (modelTaskData.isCompleted) "已完成" else "待办",
        tags = modelTaskData.customTags,
        daysLeft = modelTaskData.daysLeft,
        subTasks = subTasks,
        dueDate = modelTaskData.dueDate,
        goalId = modelTaskData.goalId,
        goalTitle = modelTaskData.goalTitle
    )
}

/**
 * 将model.SubTask转换为UI层的SubTask
 * 注：我们已经统一使用com.timeflow.app.ui.screen.task.SubTask
 * 此方法保留用于兼容其他代码
 */
fun convertModelSubTaskToUISubTask(modelSubTask: SubTask): SubTask {
    return modelSubTask // 由于使用统一类型，直接返回
}

/**
 * 将UI层的SubTask转换为model.SubTask
 * 注：我们已经统一使用com.timeflow.app.ui.screen.task.SubTask
 * 此方法保留用于兼容其他代码
 */
fun convertUISubTaskToModelSubTask(subTask: SubTask): SubTask {
    return subTask // 由于使用统一类型，直接返回
}

/**
 * 检查所有子任务是否已完成（UI层）
 */
fun areAllUISubTasksCompleted(subTasks: List<SubTask>): Boolean {
    return subTasks.isNotEmpty() && subTasks.all { it.isCompleted }
} 

/**
 * 将Task转换为ModelTaskData
 * 用于在数据库层和UI层之间转换数据
 */
fun convertTaskToModelTaskData(task: Task): ModelTaskData {
    // 计算剩余天数
    val daysLeft = if (task.dueDate != null) {
        val now = LocalDateTime.now()
        val days = now.until(task.dueDate, java.time.temporal.ChronoUnit.DAYS).toInt()
        if (days < 0) 0 else days
    } else {
        0
    }
    
    // 将Priority转换为TaskUrgency
    val urgency = when(task.priority) {
        Priority.URGENT -> TaskUrgency.CRITICAL
        Priority.HIGH -> TaskUrgency.HIGH
        Priority.MEDIUM -> TaskUrgency.MEDIUM
        Priority.LOW -> TaskUrgency.LOW
        else -> TaskUrgency.MEDIUM
    }
    
    // 🔧 关键修复：将LocalDateTime的completedAt转换为Long时间戳
    val completedAtTimestamp = task.completedAt?.let { localDateTime ->
        localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
    }
    
    return ModelTaskData(
        id = task.id,
        title = task.title,
        description = task.description,
        daysLeft = daysLeft,
        isCompleted = task.status == "已完成" || task.completedAt != null, // 🔧 使用completedAt辅助判断完成状态
        urgency = urgency,
        customTags = task.tags.map { it.name },
        subTasks = emptyList(), // 子任务需要单独查询
        dueDate = task.startDate ?: task.dueDate, // 🔧 修复：优先使用startDate作为显示时间，fallback到dueDate
        goalId = task.goalId,  // 添加目标关联字段
        goalTitle = null, // 暂时设为null，需要额外查询目标标题
        completedAt = completedAtTimestamp // 🔧 关键修复：正确设置完成时间戳
    )
}

/**
 * 将UI层的TaskData转换为ModelTaskData
 * 用于在UI层和模型层之间转换数据
 */
fun convertUITaskDataToModelTaskData(uiTaskData: TaskData): ModelTaskData {
    // 将UI层的Priority转换为TaskUrgency
    val urgency = when(uiTaskData.priority) {
        Priority.URGENT -> TaskUrgency.CRITICAL
        Priority.HIGH -> TaskUrgency.HIGH
        Priority.MEDIUM -> TaskUrgency.MEDIUM
        Priority.LOW -> TaskUrgency.LOW
        else -> TaskUrgency.MEDIUM
    }
    
    return ModelTaskData(
        id = uiTaskData.id,
        title = uiTaskData.title,
        description = uiTaskData.description,
        daysLeft = uiTaskData.daysLeft,
        isCompleted = uiTaskData.status == "已完成",
        urgency = urgency,
        customTags = uiTaskData.tags,
        subTasks = uiTaskData.subTasks,
        dueDate = uiTaskData.dueDate,
        goalId = uiTaskData.goalId,
        goalTitle = uiTaskData.goalTitle
    )
} 