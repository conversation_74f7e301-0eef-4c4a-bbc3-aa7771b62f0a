package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.ai.model.AiConfig
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.viewmodel.AiConfigViewModel
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import java.util.UUID

/**
 * AI模型设置屏幕，用于添加或编辑AI服务配置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AiModelSettingsScreen(
    navController: NavController,
    onBackClick: () -> Unit,
    viewModel: AiConfigViewModel = hiltViewModel(),
    calendarViewModel: CalendarViewModel = hiltViewModel(),
    configId: String? = null
) {
    val context = LocalContext.current
    val configurations by viewModel.configurations.collectAsState()
    val selectedConfigId by viewModel.selectedConfigId.collectAsState()
    
    // 获取用户颜色偏好
    val userColorPreference by calendarViewModel.userColorPreference.collectAsState()
    val settingsBackgroundColor = remember(userColorPreference) {
        Color(userColorPreference.settingsPageBackground)
    }
    
    // 加载配置
    LaunchedEffect(Unit) {
        viewModel.loadConfigurations(context)
    }
    
    // 当前编辑的配置
    var currentConfig by remember { mutableStateOf(
        AiConfig(
            id = UUID.randomUUID().toString(),
            name = "",
            provider = "DeepSeek",
            apiKey = "",
            modelName = "",
            serverUrl = "",
            temperature = 0.7f
        )
    ) }
    
    // 判断是新增还是编辑模式
    var isEditMode by remember { mutableStateOf(false) }
    
    // 如果有指定的配置ID参数，则优先使用它
    LaunchedEffect(configId, configurations) {
        if (configId != null) {
            val config = configurations.find { it.id == configId }
            if (config != null) {
                currentConfig = config
                isEditMode = true
            }
        } else {
            // 如果没有指定配置ID，则使用当前选中的配置（如果有）
            val selected = configurations.find { it.id == selectedConfigId }
            if (selected != null) {
                currentConfig = selected
                isEditMode = true
            }
        }
    }
    
    // 表单状态
    var name by remember { mutableStateOf(currentConfig.name) }
    var provider by remember { mutableStateOf(currentConfig.provider) }
    var apiKey by remember { mutableStateOf(currentConfig.apiKey) }
    var modelName by remember { mutableStateOf(currentConfig.modelName) }
    var serverUrl by remember { mutableStateOf(currentConfig.serverUrl) }
    var temperature by remember { mutableStateOf(currentConfig.temperature.toString()) }
    var maxTokens by remember { mutableStateOf(currentConfig.maxTokens.toString()) }
    var systemPrompt by remember { mutableStateOf(currentConfig.systemPrompt) }
    
    // 🆕 新增：自定义提供商输入状态
    var isCustomProvider by remember { mutableStateOf(provider.equals("自定义", ignoreCase = true)) }
    var customProviderName by remember { mutableStateOf("") }
    
    // 更新表单状态
    LaunchedEffect(currentConfig) {
        name = currentConfig.name
        provider = currentConfig.provider
        apiKey = currentConfig.apiKey
        modelName = currentConfig.modelName
        serverUrl = currentConfig.serverUrl
        temperature = currentConfig.temperature.toString()
        maxTokens = currentConfig.maxTokens.toString()
        systemPrompt = currentConfig.systemPrompt
        
        // 🆕 检查是否为自定义提供商
        val predefinedProviders = listOf("DeepSeek", "GPT-4", "Claude", "Gemini", "通义千问", "文心一言", "智谱AI", "Moonshot", "百川智能", "MiniMax")
        isCustomProvider = !predefinedProviders.contains(provider)
        if (isCustomProvider) {
            customProviderName = provider
        }
    }
    
    // 验证表单是否有效
    val isFormValid = name.isNotEmpty() && 
        (if (isCustomProvider) customProviderName.isNotEmpty() else provider.isNotEmpty())
    
    Scaffold(
        topBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
            ) {
                TopAppBar(
                    title = {
                        Text(
                            text = if (isEditMode) "编辑AI配置" else "添加AI配置",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp // 🔧 缩小标题字体
                            ),
                            color = TextPrimary
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onBackClick) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回",
                                tint = Color.DarkGray,
                                modifier = Modifier.size(20.dp) // 🔧 缩小图标
                            )
                        }
                    },
                    actions = {
                        // 保存按钮
                        IconButton(
                            onClick = {
                                // 保存配置
                                val finalProvider = if (isCustomProvider) customProviderName else provider
                                val updatedConfig = currentConfig.copy(
                                    name = name,
                                    provider = finalProvider,
                                    apiKey = apiKey,
                                    modelName = modelName,
                                    serverUrl = serverUrl,
                                    temperature = temperature.toFloatOrNull() ?: 0.7f,
                                    maxTokens = maxTokens.toIntOrNull() ?: 2048,
                                    systemPrompt = systemPrompt
                                )
                                
                                if (isEditMode) {
                                    viewModel.updateConfiguration(context, updatedConfig)
                                } else {
                                    viewModel.addConfiguration(context, updatedConfig)
                                }
                                
                                // 返回上一页
                                onBackClick()
                            },
                            enabled = isFormValid
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "保存",
                                tint = if (isFormValid) DustyLavender else TextSecondary,
                                modifier = Modifier.size(20.dp) // 🔧 缩小图标
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.White,
                        titleContentColor = MaterialTheme.colorScheme.primary
                    )
                )
            }
        },
        containerColor = settingsBackgroundColor
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(14.dp) // 🔧 缩小内边距
                .verticalScroll(rememberScrollState())
        ) {
            // 配置表单
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp), // 🔧 缩小间距
                shape = RoundedCornerShape(10.dp), // 🔧 缩小圆角
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(14.dp) // 🔧 缩小内边距
                ) {
                    // 配置名称
                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { 
                            Text(
                                "配置名称",
                                fontSize = 12.sp // 🔧 缩小标签字体
                            ) 
                        },
                        placeholder = { 
                            Text(
                                "例如：我的DeepSeek",
                                fontSize = 12.sp // 🔧 缩小占位符字体
                            ) 
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp), // 🔧 缩小间距
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                        )
                    )
                    
                    // 🆕 增强版服务提供商选择器
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp) // 🔧 缩小间距
                    ) {
                        Text(
                            text = "服务提供商",
                            style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp), // 🔧 缩小字体
                            color = Color.Gray,
                            modifier = Modifier.padding(bottom = 6.dp)
                        )
                        
                        // 预设提供商选项
                        val predefinedProviders = listOf(
                            "DeepSeek" to "🧠",
                            "GPT-4" to "🤖", 
                            "Claude" to "👾",
                            "Gemini" to "✨",
                            "通义千问" to "📚",
                            "文心一言" to "🎨",
                            "智谱AI" to "🔮",
                            "Moonshot" to "🌙",
                            "百川智能" to "🏔️",
                            "MiniMax" to "⚡"
                        )
                        
                        // 预设选项的网格布局
                        val rows = predefinedProviders.chunked(2)
                        rows.forEach { rowProviders ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 6.dp),
                                horizontalArrangement = Arrangement.spacedBy(6.dp)
                            ) {
                                rowProviders.forEach { (providerName, emoji) ->
                                    val isSelected = !isCustomProvider && provider == providerName
                                    
                                    Surface(
                                        modifier = Modifier
                                            .weight(1f)
                                            .clickable {
                                                isCustomProvider = false
                                                provider = providerName
                                            },
                                        shape = RoundedCornerShape(8.dp),
                                        color = if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.White,
                                        border = BorderStroke(
                                            width = 1.dp,
                                            color = if (isSelected) DustyLavender else Color.LightGray.copy(alpha = 0.3f)
                                        )
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(10.dp), // 🔧 缩小内边距
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                text = emoji,
                                                fontSize = 16.sp, // 🔧 缩小emoji
                                                modifier = Modifier.padding(end = 6.dp)
                                            )
                                            Text(
                                                text = providerName,
                                                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 11.sp), // 🔧 缩小字体
                                                color = if (isSelected) DustyLavender else Color.DarkGray,
                                                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                                            )
                                        }
                                    }
                                }
                                
                                // 如果行中只有一个元素，添加空白占位符
                                if (rowProviders.size == 1) {
                                    Spacer(modifier = Modifier.weight(1f))
                                }
                            }
                        }
                        
                        // 自定义提供商选项
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    isCustomProvider = true
                                },
                            shape = RoundedCornerShape(8.dp),
                            color = if (isCustomProvider) DustyLavender.copy(alpha = 0.1f) else Color.White,
                            border = BorderStroke(
                                width = 1.dp,
                                color = if (isCustomProvider) DustyLavender else Color.LightGray.copy(alpha = 0.3f)
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(10.dp), // 🔧 缩小内边距
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = "⚙️",
                                    fontSize = 16.sp, // 🔧 缩小emoji
                                    modifier = Modifier.padding(end = 6.dp)
                                )
                                Text(
                                    text = "自定义服务商",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 11.sp), // 🔧 缩小字体
                                    color = if (isCustomProvider) DustyLavender else Color.DarkGray,
                                    fontWeight = if (isCustomProvider) FontWeight.Medium else FontWeight.Normal
                                )
                            }
                        }
                        
                        // 🆕 自定义提供商名称输入框
                        if (isCustomProvider) {
                            Spacer(modifier = Modifier.height(8.dp))
                            OutlinedTextField(
                                value = customProviderName,
                                onValueChange = { customProviderName = it },
                                label = { 
                                    Text(
                                        "自定义提供商名称",
                                        fontSize = 12.sp // 🔧 缩小标签字体
                                    ) 
                                },
                                placeholder = { 
                                    Text(
                                        "例如：我的AI服务",
                                        fontSize = 12.sp // 🔧 缩小占位符字体
                                    ) 
                                },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true,
                                textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = DustyLavender,
                                    unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                                )
                            )
                        }
                    }
                    
                    // API密钥
                    OutlinedTextField(
                        value = apiKey,
                        onValueChange = { apiKey = it },
                        label = { 
                            Text(
                                "API密钥",
                                fontSize = 12.sp // 🔧 缩小标签字体
                            ) 
                        },
                        placeholder = { 
                            Text(
                                "输入API密钥",
                                fontSize = 12.sp // 🔧 缩小占位符字体
                            ) 
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp), // 🔧 缩小间距
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                        )
                    )
                    
                    // 模型名称
                    OutlinedTextField(
                        value = modelName,
                        onValueChange = { modelName = it },
                        label = { 
                            Text(
                                "模型名称",
                                fontSize = 12.sp // 🔧 缩小标签字体
                            ) 
                        },
                        placeholder = { 
                            Text(
                                "例如：deepseek-chat",
                                fontSize = 12.sp // 🔧 缩小占位符字体
                            ) 
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp), // 🔧 缩小间距
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                        )
                    )
                    
                    // 服务器地址
                    OutlinedTextField(
                        value = serverUrl,
                        onValueChange = { serverUrl = it },
                        label = { 
                            Text(
                                "服务器地址",
                                fontSize = 12.sp // 🔧 缩小标签字体
                            ) 
                        },
                        placeholder = { 
                            Text(
                                "例如：https://api.deepseek.com/v1",
                                fontSize = 12.sp // 🔧 缩小占位符字体
                            ) 
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp), // 🔧 缩小间距
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender,
                            unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                        )
                    )
                    
                    // 高级设置卡片
                    var showAdvancedSettings by remember { mutableStateOf(false) }
                    
                    Button(
                        onClick = { showAdvancedSettings = !showAdvancedSettings },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp), // 🔧 缩小间距
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White,
                            contentColor = DustyLavender
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = DustyLavender.copy(alpha = 0.3f)
                        )
                    ) {
                        Text(
                            text = if (showAdvancedSettings) "隐藏高级设置" else "显示高级设置",
                            style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp) // 🔧 缩小字体
                        )
                    }
                    
                    if (showAdvancedSettings) {
                        // 温度
                        OutlinedTextField(
                            value = temperature,
                            onValueChange = { temperature = it },
                            label = { 
                                Text(
                                    "温度 (0.0 - 1.0)",
                                    fontSize = 12.sp // 🔧 缩小标签字体
                                ) 
                            },
                            placeholder = { 
                                Text(
                                    "默认：0.7",
                                    fontSize = 12.sp // 🔧 缩小占位符字体
                                ) 
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 12.dp), // 🔧 缩小间距
                            singleLine = true,
                            textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Next
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = DustyLavender,
                                unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                            )
                        )
                        
                        // 最大Token数
                        OutlinedTextField(
                            value = maxTokens,
                            onValueChange = { maxTokens = it },
                            label = { 
                                Text(
                                    "最大Token数",
                                    fontSize = 12.sp // 🔧 缩小标签字体
                                ) 
                            },
                            placeholder = { 
                                Text(
                                    "默认：2048",
                                    fontSize = 12.sp // 🔧 缩小占位符字体
                                ) 
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 12.dp), // 🔧 缩小间距
                            singleLine = true,
                            textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Next
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = DustyLavender,
                                unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                            )
                        )
                        
                        // 系统提示词
                        OutlinedTextField(
                            value = systemPrompt,
                            onValueChange = { systemPrompt = it },
                            label = { 
                                Text(
                                    "系统提示词",
                                    fontSize = 12.sp // 🔧 缩小标签字体
                                ) 
                            },
                            placeholder = { 
                                Text(
                                    "输入系统提示词，设定AI助手的行为模式",
                                    fontSize = 12.sp // 🔧 缩小占位符字体
                                ) 
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 12.dp) // 🔧 缩小间距
                                .height(100.dp), // 🔧 缩小高度
                            textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = DustyLavender,
                                unfocusedBorderColor = Color.LightGray.copy(alpha = 0.3f)
                            )
                        )
                    }
                }
            }
            
            // 保存按钮
            Button(
                onClick = {
                    // 保存配置
                    val finalProvider = if (isCustomProvider) customProviderName else provider
                    val updatedConfig = currentConfig.copy(
                        name = name,
                        provider = finalProvider,
                        apiKey = apiKey,
                        modelName = modelName,
                        serverUrl = serverUrl,
                        temperature = temperature.toFloatOrNull() ?: 0.7f,
                        maxTokens = maxTokens.toIntOrNull() ?: 2048,
                        systemPrompt = systemPrompt
                    )
                    
                    if (isEditMode) {
                        viewModel.updateConfiguration(context, updatedConfig)
                    } else {
                        viewModel.addConfiguration(context, updatedConfig)
                    }
                    
                    // 返回上一页
                    onBackClick()
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp), // 🔧 缩小间距
                shape = RoundedCornerShape(40.dp), // 🔧 缩小圆角
                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender,
                    disabledContainerColor = Color.LightGray.copy(alpha = 0.3f)
                ),
                enabled = isFormValid
            ) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "保存",
                    tint = Color.White,
                    modifier = Modifier.size(14.dp) // 🔧 缩小图标
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = if (isEditMode) "保存配置" else "添加配置",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp) // 🔧 缩小字体
                )
            }
            
            // 删除按钮 (仅编辑模式显示，且不允许删除默认配置)
            if (isEditMode && !currentConfig.isDefault) {
                Button(
                    onClick = {
                        // 删除配置
                        viewModel.deleteConfiguration(context, currentConfig.id)
                        
                        // 返回上一页
                        onBackClick()
                    },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(40.dp), // 🔧 缩小圆角
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White,
                        contentColor = Color.Red
                    ),
                    border = BorderStroke(
                        width = 1.dp,
                        color = Color.Red.copy(alpha = 0.3f)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = Color.Red,
                        modifier = Modifier.size(14.dp) // 🔧 缩小图标
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    Text(
                        text = "删除配置",
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp) // 🔧 缩小字体
                    )
                }
            }
        }
    }
} 