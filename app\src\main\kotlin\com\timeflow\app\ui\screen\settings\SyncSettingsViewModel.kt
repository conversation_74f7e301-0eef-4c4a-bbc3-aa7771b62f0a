package com.timeflow.app.ui.screen.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import android.util.Log
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.repository.TaskRepository
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

/**
 * 同步设置ViewModel
 * 管理S3同步配置和状态
 */
@HiltViewModel
class SyncSettingsViewModel @Inject constructor(
    private val syncRepository: SyncRepository,
    private val taskRepository: TaskRepository
) : ViewModel() {
    
    // 同步设置状态
    private val _syncSettings = MutableStateFlow(SyncSettings())
    val syncSettings: StateFlow<SyncSettings> = _syncSettings.asStateFlow()
    
    // 同步状态
    private val _syncStatus = MutableStateFlow(SyncStatus.DISCONNECTED)
    val syncStatus: StateFlow<SyncStatus> = _syncStatus.asStateFlow()
    
    // 是否正在连接
    private val _isConnecting = MutableStateFlow(false)
    val isConnecting: StateFlow<Boolean> = _isConnecting.asStateFlow()
    
    // 同步进度
    private val _syncProgress = MutableStateFlow(0f)
    val syncProgress: StateFlow<Float> = _syncProgress.asStateFlow()
    
    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 是否正在同步
    private val _isSyncing = MutableStateFlow(false)
    val isSyncing: StateFlow<Boolean> = _isSyncing.asStateFlow()
    
    /**
     * 更新云存储配置
     */
    fun updateCloudStorageConfig(config: CloudStorageConfigData) {
        _syncSettings.value = _syncSettings.value.copy(
            cloudStorageConfig = config
        )

        // 配置更新后重置连接状态
        _syncStatus.value = SyncStatus.DISCONNECTED
        _errorMessage.value = null

        Log.d("SyncSettingsViewModel", "云存储配置已更新: ${config.provider}")
    }

    /**
     * 更新S3配置 (保持向后兼容)
     */
    fun updateS3Config(
        accessKeyId: String,
        secretAccessKey: String,
        region: String,
        bucketName: String,
        endpoint: String = "",
        provider: String = "AWS_S3"
    ) {
        val newConfig = S3ConfigData(
            accessKeyId = accessKeyId,
            secretAccessKey = secretAccessKey,
            region = region,
            bucketName = bucketName,
            endpoint = endpoint,
            provider = provider
        )

        _syncSettings.value = _syncSettings.value.copy(
            s3Config = newConfig,
            cloudStorageConfig = newConfig.toCloudStorageConfigData()
        )

        // 配置更新后重置连接状态
        _syncStatus.value = SyncStatus.DISCONNECTED
        _errorMessage.value = null

        Log.d("SyncSettingsViewModel", "S3配置已更新")
    }
    
    /**
     * 切换同步开关
     */
    fun toggleSync(enabled: Boolean) {
        _syncSettings.value = _syncSettings.value.copy(isEnabled = enabled)
        
        if (!enabled) {
            _syncStatus.value = SyncStatus.DISCONNECTED
        }
        
        Log.d("SyncSettingsViewModel", "同步功能已${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 测试连接
     */
    fun testConnection() {
        val config = _syncSettings.value.getEffectiveCloudStorageConfig()
        if (config == null) {
            _errorMessage.value = "请先配置云存储信息"
            return
        }

        viewModelScope.launch {
            _isConnecting.value = true
            _errorMessage.value = null

            try {
                // 使用新的云存储配置测试连接
                val result = syncRepository.testConnection(config.toCloudStorageConfig())

                result.onSuccess { message ->
                    _syncStatus.value = SyncStatus.CONNECTED
                    _syncSettings.value = _syncSettings.value.copy(
                        lastSyncTime = getCurrentTimeString()
                    )
                    Log.d("SyncSettingsViewModel", "连接测试成功: $message")
                }.onFailure { exception ->
                    _syncStatus.value = SyncStatus.ERROR
                    _errorMessage.value = exception.message ?: "连接测试失败"
                    Log.e("SyncSettingsViewModel", "连接测试失败", exception)
                }

            } catch (e: Exception) {
                _syncStatus.value = SyncStatus.ERROR
                _errorMessage.value = e.message ?: "连接测试失败"
                Log.e("SyncSettingsViewModel", "连接测试异常", e)
            } finally {
                _isConnecting.value = false
            }
        }
    }
    
    /**
     * 手动同步
     */
    fun manualSync() {
        if (_syncStatus.value != SyncStatus.CONNECTED) {
            _errorMessage.value = "请先建立云存储连接"
            return
        }

        val config = _syncSettings.value.getEffectiveCloudStorageConfig()
        if (config == null) {
            _errorMessage.value = "云存储配置无效"
            return
        }

        viewModelScope.launch {
            _isSyncing.value = true
            _syncProgress.value = 0f
            _errorMessage.value = null

            try {
                // 获取所有任务数据
                _syncProgress.value = 0.2f
                delay(500)

                val tasks = taskRepository.getAllTasks()
                _syncProgress.value = 0.4f
                delay(500)

                // 上传数据
                _syncProgress.value = 0.6f
                val result = syncRepository.uploadData(config.toCloudStorageConfig(), tasks)

                result.onSuccess { message ->
                    _syncProgress.value = 1.0f
                    _syncSettings.value = _syncSettings.value.copy(
                        lastSyncTime = getCurrentTimeString()
                    )
                    Log.d("SyncSettingsViewModel", "数据同步成功: $message")
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "数据上传失败"
                    Log.e("SyncSettingsViewModel", "数据同步失败", exception)
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "同步过程中出现错误"
                Log.e("SyncSettingsViewModel", "同步异常", e)
            } finally {
                _isSyncing.value = false
                _syncProgress.value = 0f
            }
        }
    }
    
    /**
     * 从S3恢复数据
     */
    fun restoreFromS3() {
        if (_syncStatus.value != SyncStatus.CONNECTED) {
            _errorMessage.value = "请先建立S3连接"
            return
        }
        
        val config = _syncSettings.value.getEffectiveCloudStorageConfig()
        if (config == null) {
            _errorMessage.value = "云存储配置无效"
            return
        }

        viewModelScope.launch {
            _isSyncing.value = true
            _errorMessage.value = null

            try {
                val result = syncRepository.downloadData(config.toCloudStorageConfig())

                result.onSuccess { tasks ->
                    if (tasks.isNotEmpty()) {
                        // TODO: 将恢复的任务数据保存到本地数据库
                        // 这里需要根据实际需求决定是否覆盖现有数据或合并数据

                        _syncSettings.value = _syncSettings.value.copy(
                            lastSyncTime = getCurrentTimeString()
                        )
                        Log.d("SyncSettingsViewModel", "数据恢复成功，恢复了${tasks.size}个任务")
                    } else {
                        _errorMessage.value = "未找到备份数据"
                    }
                }.onFailure { exception ->
                    _errorMessage.value = exception.message ?: "数据恢复失败"
                    Log.e("SyncSettingsViewModel", "数据恢复失败", exception)
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "数据恢复失败"
                Log.e("SyncSettingsViewModel", "恢复异常", e)
            } finally {
                _isSyncing.value = false
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 获取当前时间字符串
     */
    private fun getCurrentTimeString(): String {
        return LocalDateTime.now().format(
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        )
    }
}

/**
 * 同步设置数据类
 */
data class SyncSettings(
    val isEnabled: Boolean = false,
    val autoSyncEnabled: Boolean = false,
    val syncInterval: Int = 24, // 小时
    val lastSyncTime: String? = null,
    val cloudStorageConfig: CloudStorageConfigData? = null,
    val s3Config: S3ConfigData? = null // 保持向后兼容
) {
    /**
     * 获取有效的云存储配置
     */
    fun getEffectiveCloudStorageConfig(): CloudStorageConfigData? {
        return cloudStorageConfig ?: s3Config?.toCloudStorageConfigData()
    }
}

/**
 * 云存储配置数据类
 */
data class CloudStorageConfigData(
    val provider: String = "AWS_S3",
    val accessKeyId: String = "",
    val secretAccessKey: String = "",
    val region: String = "",
    val bucketName: String = "",
    val endpoint: String = "",
    val customDomain: String = ""
) {
    /**
     * 转换为CloudStorageConfig
     */
    fun toCloudStorageConfig(): com.timeflow.app.ui.screen.settings.CloudStorageConfig {
        return com.timeflow.app.ui.screen.settings.CloudStorageConfig(
            provider = provider,
            accessKeyId = accessKeyId,
            secretAccessKey = secretAccessKey,
            bucketName = bucketName,
            region = region,
            endpoint = endpoint,
            customDomain = customDomain
        )
    }

    companion object {
        /**
         * 从CloudStorageConfig创建
         */
        fun fromCloudStorageConfig(config: com.timeflow.app.ui.screen.settings.CloudStorageConfig): CloudStorageConfigData {
            return CloudStorageConfigData(
                provider = config.provider,
                accessKeyId = config.accessKeyId,
                secretAccessKey = config.secretAccessKey,
                bucketName = config.bucketName,
                region = config.region,
                endpoint = config.endpoint,
                customDomain = config.customDomain
            )
        }
    }
}

/**
 * S3配置数据类 (保持向后兼容)
 */
data class S3ConfigData(
    val accessKeyId: String = "",
    val secretAccessKey: String = "",
    val region: String = "us-east-1",
    val bucketName: String = "",
    val endpoint: String = "",
    val provider: String = "AWS_S3"
) {
    /**
     * 转换为CloudStorageConfigData
     */
    fun toCloudStorageConfigData(): CloudStorageConfigData {
        return CloudStorageConfigData(
            provider = provider,
            accessKeyId = accessKeyId,
            secretAccessKey = secretAccessKey,
            bucketName = bucketName,
            region = region,
            endpoint = endpoint
        )
    }

    companion object {
        /**
         * 从CloudStorageConfigData创建
         */
        fun fromCloudStorageConfigData(config: CloudStorageConfigData): S3ConfigData {
            return S3ConfigData(
                provider = config.provider,
                accessKeyId = config.accessKeyId,
                secretAccessKey = config.secretAccessKey,
                bucketName = config.bucketName,
                region = config.region,
                endpoint = config.endpoint
            )
        }
    }
}

/**
 * 同步状态枚举
 */
enum class SyncStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    ERROR
} 