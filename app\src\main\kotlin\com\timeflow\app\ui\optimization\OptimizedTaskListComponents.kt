package com.timeflow.app.ui.optimization

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.entity.Task as TaskEntity
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.collectAsOptimizedListState
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberCallback
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberCallback1
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberDerivedStateOf
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.generateStableKey
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.toStableList
import kotlinx.coroutines.flow.StateFlow

/**
 * 优化后的任务列表组件
 * 
 * 优化要点：
 * 1. 使用collectAsOptimizedListState避免不必要的重组
 * 2. 使用rememberCallback缓存回调函数
 * 3. 使用rememberDerivedStateOf优化计算密集的派生状态
 * 4. 使用稳定的key和数据类型
 * 5. 避免在Composable中创建临时对象
 */
@Composable
fun OptimizedTaskList(
    tasksFlow: StateFlow<List<TaskEntity>>,
    onTaskClick: (String) -> Unit,
    onTaskToggle: (String, Boolean) -> Unit,
    filterMode: String = "all",
    modifier: Modifier = Modifier
) {
    // 🔧 优化1: 使用优化的状态收集
    val tasks by tasksFlow.collectAsOptimizedListState()
    
    // 🔧 优化2: 缓存回调函数，避免重组时重新创建
    val optimizedTaskClick = rememberCallback1(onTaskClick) { taskId: String ->
        onTaskClick(taskId)
    }
    
    val optimizedTaskToggle = remember(onTaskToggle) { 
        { taskId: String, isCompleted: Boolean ->
            onTaskToggle(taskId, isCompleted)
        }
    }
    
    // 🔧 优化3: 使用derivedStateOf进行过滤，只在tasks或filterMode变化时重新计算
    val filteredTasks: ComposeRecompositionOptimizer.StableList<TaskEntity> by rememberDerivedStateOf(tasks, filterMode) {
        val filtered = when (filterMode) {
            "today" -> tasks.filter { task ->
                // 简化日期检查逻辑
                true // 暂时返回所有任务
            }
            "completed" -> tasks.filter { task ->
                // 检查任务是否完成，简化逻辑
                task.title.contains("完成") // 简化的完成检查
            }
            "incomplete" -> tasks.filter { task ->
                // 检查任务是否未完成
                !task.title.contains("完成") // 简化的未完成检查
            }
            else -> tasks
        }
        // 🔧 优化4: 转换为稳定类型，避免重组
        ComposeRecompositionOptimizer.StableList(filtered)
    }
    
    // 🔧 优化5: 记忆化LazyListState
    val listState = rememberLazyListState()
    
    // 🔧 优化6: 使用性能分析器（调试模式）
    ComposeRecompositionOptimizer.PerformanceAnalyzer("OptimizedTaskList") {
        LazyColumn(
            state = listState,
            modifier = modifier.fillMaxSize(),
            contentPadding = PaddingValues(
                horizontal = 16.dp,
                vertical = 8.dp
            ),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(
                items = filteredTasks.items, // 使用稳定列表
                // 🔧 优化7: 使用稳定的key生成器
                key = { task: TaskEntity -> generateStableKey(task) { it.id } }
            ) { task: TaskEntity ->
                // 🔧 优化8: 为每个item添加重组计数器（调试模式）
                ComposeRecompositionOptimizer.RecompositionLogger("TaskItem_${task.id}")
                
                OptimizedTaskItem(
                    task = task,
                    onClick = optimizedTaskClick,
                    onToggle = optimizedTaskToggle
                )
            }
        }
    }
}

/**
 * 优化后的任务项组件
 * 
 * 优化要点：
 * 1. 使用@Stable注解标记稳定的数据类
 * 2. 避免在组件内部创建临时对象
 * 3. 使用record计算密集操作的结果
 */
@Composable
private fun OptimizedTaskItem(
    task: TaskEntity,
    onClick: (String) -> Unit,
    onToggle: (String, Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    // 🔧 优化9: 缓存点击处理器，避免重新创建lambda
    val handleClick = remember(task.id) { { onClick(task.id) } }
    val handleToggle: (Boolean) -> Unit = remember(task.id) { { isCompleted: Boolean -> onToggle(task.id, isCompleted) } }
    
    // 🔧 优化10: 预计算UI相关的值，避免重组时重复计算
    val uiProperties = remember(task.priority, task.title) {
        TaskItemUiProperties.from(task)
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        onClick = handleClick
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = uiProperties.titleColor
                )
                
                if (task.description.isNotEmpty()) {
                    Text(
                        text = task.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = uiProperties.descriptionColor
                    )
                }
                
                Text(
                    text = uiProperties.timeText,
                    style = MaterialTheme.typography.labelSmall,
                    color = uiProperties.timeColor
                )
            }
            
            Checkbox(
                checked = task.title.contains("完成"), // 简化的完成状态检查
                onCheckedChange = handleToggle
            )
        }
    }
}

/**
 * 稳定的UI属性数据类
 * 使用@Stable注解告诉Compose这是一个稳定的类型
 */
@Stable
private data class TaskItemUiProperties(
    val titleColor: androidx.compose.ui.graphics.Color,
    val descriptionColor: androidx.compose.ui.graphics.Color,
    val timeColor: androidx.compose.ui.graphics.Color,
    val timeText: String
) {
    companion object {
        fun from(task: TaskEntity): TaskItemUiProperties {
            val titleColor = if (task.title.contains("完成")) {
                Color.Gray
            } else {
                Color.Black
            }
            
            val descriptionColor = Color.Gray
            
            // 简化时间显示逻辑
            val (timeText, timeColor) = "今天" to Color.Gray
            
            return TaskItemUiProperties(
                titleColor = titleColor,
                descriptionColor = descriptionColor,
                timeColor = timeColor,
                timeText = timeText
            )
        }
    }
}

/**
 * 优化的任务过滤器组件
 */
@Composable
fun OptimizedTaskFilter(
    currentFilter: String,
    onFilterChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    // 🔧 优化11: 使用记忆化的过滤选项列表
    val filterOptions = remember {
        listOf(
            "all" to "全部",
            "today" to "今天", 
            "completed" to "已完成",
            "incomplete" to "未完成"
        )
    }
    
    // 🔧 优化12: 缓存过滤变更回调
    val handleFilterChange = rememberCallback1(onFilterChange) { filter: String ->
        onFilterChange(filter)
    }
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        filterOptions.forEach { (key, label) ->
            FilterChip(
                selected = currentFilter == key,
                onClick = { handleFilterChange(key) },
                label = { Text(label) }
            )
        }
    }
}

/**
 * 优化的任务统计组件
 */
@Composable
fun OptimizedTaskStatistics(
    tasksFlow: StateFlow<List<TaskEntity>>,
    modifier: Modifier = Modifier
) {
    val tasks by tasksFlow.collectAsOptimizedListState()
    
    // 🔧 优化13: 使用derivedStateOf计算统计信息，避免重组时重复计算
    val statistics: TaskStatistics by rememberDerivedStateOf(tasks) {
        TaskStatistics(
            total = tasks.size,
            completed = tasks.count { it.title.contains("完成") },
            today = tasks.size, // 简化逻辑，暂时显示所有任务
            overdue = 0 // 简化逻辑
        )
    }
    
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "任务统计",
                style = MaterialTheme.typography.titleMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatisticItem("总计", statistics.total.toString())
                StatisticItem("已完成", statistics.completed.toString())
                StatisticItem("今天", statistics.today.toString())
                StatisticItem("逾期", statistics.overdue.toString())
            }
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall
        )
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall
        )
    }
}

/**
 * 稳定的统计数据类
 */
@Stable
private data class TaskStatistics(
    val total: Int,
    val completed: Int,
    val today: Int,
    val overdue: Int
) 