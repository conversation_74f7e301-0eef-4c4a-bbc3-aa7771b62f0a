package com.timeflow.app.data.analytics

import com.timeflow.app.data.entity.CycleRecord
import java.time.format.DateTimeFormatter
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlin.math.sqrt

/**
 * 高级分析数据类
 */
data class AdvancedAnalytics(
    val cycleTrend: TrendDirection = TrendDirection.STABLE,
    val cycleTrendValue: Double = 0.0,
    val periodTrend: TrendDirection = TrendDirection.STABLE,
    val periodTrendValue: Double = 0.0,
    val cycleVariability: Double = 0.0,
    val regularityScore: Int = 0,
    val standardDeviation: Double = 0.0,
    val maxDeviation: Int = 0,
    val healthIndicators: List<HealthIndicator> = emptyList(),
    val predictionAccuracy: Int = 0,
    val averageError: Double = 0.0,
    val monthlyComparison: List<MonthlyData> = emptyList()
)

/**
 * 趋势方向
 */
enum class TrendDirection {
    INCREASING, DECREASING, STABLE
}

/**
 * 健康状态
 */
enum class HealthStatus {
    NORMAL, WARNING, ATTENTION
}

/**
 * 健康指标
 */
data class HealthIndicator(
    val name: String,
    val value: String,
    val status: HealthStatus
)

/**
 * 月度数据
 */
data class MonthlyData(
    val month: String,
    val averageCycle: Int,
    val cycleCount: Int
)

/**
 * 计算高级分析数据
 */
fun calculateAdvancedAnalytics(cycles: List<CycleRecord>): AdvancedAnalytics {
    if (cycles.size < 3) {
        return AdvancedAnalytics()
    }
    
    val sortedCycles = cycles.sortedBy { it.startDate }
    val cycleLengths = sortedCycles.mapNotNull { it.cycleLength }.map { it.toDouble() }
    val periodLengths = sortedCycles.mapNotNull { it.periodLength }.map { it.toDouble() }
    
    if (cycleLengths.isEmpty()) {
        return AdvancedAnalytics()
    }
    
    // 计算趋势
    val cycleTrend = calculateTrend(cycleLengths)
    val periodTrend = calculateTrend(periodLengths)
    
    // 计算变异性
    val cycleAverage = cycleLengths.average()
    val cycleVariance = cycleLengths.map { (it - cycleAverage) * (it - cycleAverage) }.average()
    val cycleStdDev = sqrt(cycleVariance)
    val cycleVariability = cycleStdDev / cycleAverage
    
    // 计算规律性评分
    val regularityScore = calculateRegularityScore(cycleVariability)
    
    // 计算最大偏差
    val maxDeviation = cycleLengths.map { abs(it - cycleAverage) }.maxOrNull()?.toInt() ?: 0
    
    // 生成健康指标
    val healthIndicators = generateHealthIndicators(cycleLengths, periodLengths)
    
    // 计算预测准确性（模拟）
    val predictionAccuracy = calculatePredictionAccuracy(cycleVariability)
    val averageError = cycleStdDev
    
    // 生成月度对比数据
    val monthlyComparison = generateMonthlyComparison(sortedCycles)
    
    return AdvancedAnalytics(
        cycleTrend = cycleTrend.first,
        cycleTrendValue = cycleTrend.second,
        periodTrend = periodTrend.first,
        periodTrendValue = periodTrend.second,
        cycleVariability = cycleVariability,
        regularityScore = regularityScore,
        standardDeviation = cycleStdDev,
        maxDeviation = maxDeviation,
        healthIndicators = healthIndicators,
        predictionAccuracy = predictionAccuracy,
        averageError = averageError,
        monthlyComparison = monthlyComparison
    )
}

/**
 * 计算趋势
 */
fun calculateTrend(values: List<Double>): Pair<TrendDirection, Double> {
    if (values.size < 3) return Pair(TrendDirection.STABLE, 0.0)
    
    // 使用线性回归计算趋势
    val n = values.size
    val x = (0 until n).map { it.toDouble() }
    val y = values
    
    val sumX = x.sum()
    val sumY = y.sum()
    val sumXY = x.zip(y) { xi, yi -> xi * yi }.sum()
    val sumX2 = x.map { it * it }.sum()
    
    val slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
    
    return when {
        slope > 0.1 -> Pair(TrendDirection.INCREASING, slope)
        slope < -0.1 -> Pair(TrendDirection.DECREASING, abs(slope))
        else -> Pair(TrendDirection.STABLE, abs(slope))
    }
}

/**
 * 计算规律性评分
 */
fun calculateRegularityScore(variability: Double): Int {
    return when {
        variability < 0.05 -> 95
        variability < 0.1 -> 85
        variability < 0.15 -> 75
        variability < 0.2 -> 65
        variability < 0.25 -> 55
        else -> 45
    }.coerceIn(0, 100)
}

/**
 * 生成健康指标
 */
fun generateHealthIndicators(cycleLengths: List<Double>, periodLengths: List<Double>): List<HealthIndicator> {
    val indicators = mutableListOf<HealthIndicator>()
    
    // 周期长度评估
    val avgCycle = cycleLengths.average()
    val cycleStatus = when {
        avgCycle < 21 -> HealthStatus.ATTENTION
        avgCycle > 35 -> HealthStatus.WARNING
        else -> HealthStatus.NORMAL
    }
    indicators.add(
        HealthIndicator(
            name = "周期长度",
            value = "${avgCycle.roundToInt()}天",
            status = cycleStatus
        )
    )
    
    // 经期长度评估
    if (periodLengths.isNotEmpty()) {
        val avgPeriod = periodLengths.average()
        val periodStatus = when {
            avgPeriod < 3 -> HealthStatus.WARNING
            avgPeriod > 7 -> HealthStatus.ATTENTION
            else -> HealthStatus.NORMAL
        }
        indicators.add(
            HealthIndicator(
                name = "经期长度",
                value = "${avgPeriod.roundToInt()}天",
                status = periodStatus
            )
        )
    }
    
    // 周期变异性评估
    val variance = cycleLengths.map { (it - avgCycle) * (it - avgCycle) }.average()
    val stdDev = sqrt(variance)
    val variabilityStatus = when {
        stdDev > 7 -> HealthStatus.ATTENTION
        stdDev > 4 -> HealthStatus.WARNING
        else -> HealthStatus.NORMAL
    }
    indicators.add(
        HealthIndicator(
            name = "周期稳定性",
            value = "±${stdDev.roundToInt()}天",
            status = variabilityStatus
        )
    )
    
    return indicators
}

/**
 * 计算预测准确性
 */
fun calculatePredictionAccuracy(variability: Double): Int {
    return when {
        variability < 0.05 -> 95
        variability < 0.1 -> 88
        variability < 0.15 -> 80
        variability < 0.2 -> 72
        variability < 0.25 -> 65
        else -> 55
    }.coerceIn(50, 100)
}

/**
 * 生成月度对比数据
 */
fun generateMonthlyComparison(cycles: List<CycleRecord>): List<MonthlyData> {
    val monthlyData = mutableListOf<MonthlyData>()
    val formatter = DateTimeFormatter.ofPattern("MM月")
    
    // 按月分组
    val cyclesByMonth = cycles.groupBy { 
        it.startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"))
    }
    
    cyclesByMonth.forEach { (monthKey, monthCycles) ->
        val cycleLengths = monthCycles.mapNotNull { it.cycleLength }
        if (cycleLengths.isNotEmpty()) {
            val avgCycle = cycleLengths.average().roundToInt()
            val month = monthCycles.first().startDate.format(formatter)
            
            monthlyData.add(
                MonthlyData(
                    month = month,
                    averageCycle = avgCycle,
                    cycleCount = monthCycles.size
                )
            )
        }
    }
    
    return monthlyData.sortedBy { it.month }
}

/**
 * 获取规律性文本
 */
fun getRegularityText(regularity: com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity): String {
    return when (regularity) {
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.VERY_REGULAR -> "非常规律"
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.REGULAR -> "规律"
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.SOMEWHAT_IRREGULAR -> "较不规律"
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.IRREGULAR -> "不规律"
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA -> "数据不足"
    }
}

/**
 * 获取规律性颜色
 */
fun getRegularityColor(regularity: com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity): androidx.compose.ui.graphics.Color {
    return when (regularity) {
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.VERY_REGULAR -> androidx.compose.ui.graphics.Color(0xFF4CAF50)
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.REGULAR -> androidx.compose.ui.graphics.Color(0xFF8BC34A)
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.SOMEWHAT_IRREGULAR -> androidx.compose.ui.graphics.Color(0xFFFF9800)
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.IRREGULAR -> androidx.compose.ui.graphics.Color(0xFFE91E63)
        com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA -> androidx.compose.ui.graphics.Color.Gray
    }
}

/**
 * 获取评分颜色
 */
fun getScoreColor(score: Int): androidx.compose.ui.graphics.Color {
    return when {
        score >= 85 -> androidx.compose.ui.graphics.Color(0xFF4CAF50)
        score >= 70 -> androidx.compose.ui.graphics.Color(0xFF8BC34A)
        score >= 55 -> androidx.compose.ui.graphics.Color(0xFFFF9800)
        else -> androidx.compose.ui.graphics.Color(0xFFE91E63)
    }
}

/**
 * 获取变异性描述
 */
fun getVariabilityDescription(variability: Double): String {
    return when {
        variability < 0.05 -> "非常稳定"
        variability < 0.1 -> "稳定"
        variability < 0.15 -> "轻微波动"
        variability < 0.2 -> "中度波动"
        else -> "波动较大"
    }
}
