package com.timeflow.app.ui.screen.reflection.data

import com.timeflow.app.ui.screen.reflection.*
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.data.dao.ReflectionDao
import com.timeflow.app.data.mapper.ReflectionMapper
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 感想仓库实现类 - 使用Room数据库持久化存储
 */
@Singleton
class ReflectionRepositoryImpl @Inject constructor(
    private val reflectionDao: ReflectionDao
) : ReflectionRepository {

    // 保留搜索和标签相关的内存缓存
    private val recentSearches = mutableListOf("运动感受", "工作反思", "读书笔记")
    private val popularTags = mutableListOf("运动", "工作", "学习", "阅读", "冥想", "创意")

    // 🎯 彻底修复：完全禁用示例数据自动初始化
    private var _hasInitializedSampleData = false
    private var _allowSampleDataCreation = false // 新增：控制是否允许创建示例数据

    override suspend fun getRecentReflections(): List<Reflection> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d("ReflectionRepository", "🔄 开始从数据库获取感想记录...")

                // 🔧 添加数据库连接测试
                val count = reflectionDao.getReflectionCount()
                Log.d("ReflectionRepository", "📊 数据库连接正常，总记录数: $count")

                val entities = reflectionDao.getAllReflections()
                Log.d("ReflectionRepository", "✅ 数据库查询完成，获得${entities.size}条原始记录")

                val reflections = ReflectionMapper.entitiesToModels(entities)
                Log.d("ReflectionRepository", "🎯 映射完成，转换了${reflections.size}条感想记录")

                // 🎯 彻底修复：不再自动初始化示例数据，避免删除后重新出现
                // 只有在明确允许且首次初始化时才创建示例数据
                if (reflections.isEmpty() && !_hasInitializedSampleData && _allowSampleDataCreation) {
                    Log.d("ReflectionRepository", "数据库为空且允许初始化，创建示例数据")
                    _hasInitializedSampleData = true
                    initializeSampleData()
                    // 🔧 修复：正确返回新创建的数据
                    val newEntities = reflectionDao.getAllReflections()
                    ReflectionMapper.entitiesToModels(newEntities)
                } else {
                    Log.d("ReflectionRepository", "返回真实数据，不创建示例数据")
                    reflections
                }
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "获取感想列表失败", e)
                emptyList()
            }
        }
    }

    /**
     * 手动初始化示例数据（仅在需要时调用）
     */
    override suspend fun initializeSampleDataIfNeeded() {
        if (!_hasInitializedSampleData) {
            Log.d("ReflectionRepository", "手动初始化示例数据")
            _allowSampleDataCreation = true
            _hasInitializedSampleData = true
            initializeSampleData()
        }
    }

    override suspend fun getRecentSearches(): List<String> {
        return recentSearches.toList()
    }

    override suspend fun getPopularTags(): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                // 从数据库获取所有使用过的标签
                val usedTagsJson = reflectionDao.getAllUsedTags()
                val allTags = mutableSetOf<String>()
                
                usedTagsJson.forEach { jsonString ->
                    try {
                        val tags = ReflectionMapper.parseTags(jsonString)
                        allTags.addAll(tags)
                    } catch (e: Exception) {
                        Log.w("ReflectionRepository", "解析标签失败: $jsonString", e)
                    }
                }
                
                // 如果数据库中没有标签，返回默认标签
                if (allTags.isEmpty()) {
                    popularTags.toList()
                } else {
                    allTags.toList()
                }
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "获取标签失败", e)
                popularTags.toList()
            }
        }
    }

    override suspend fun searchReflections(
        query: String,
        timeFilter: TimeFilter,
        moodFilters: Set<MoodType>,
        typeFilters: Set<ReflectionType>,
        advancedFilters: Set<AdvancedFilter>,
        startDate: Instant?,
        endDate: Instant?
    ): List<Reflection> {
        return withContext(Dispatchers.IO) {
            try {
                // 基础搜索
                val baseResults = if (query.isNotEmpty()) {
                    val entities = reflectionDao.searchReflections(query)
                    ReflectionMapper.entitiesToModels(entities)
                } else {
                    val entities = reflectionDao.getAllReflections()
                    ReflectionMapper.entitiesToModels(entities)
                }
                
                // 应用过滤器
                var filteredResults = baseResults
                
                // 日期范围过滤
                if (startDate != null) {
                    // 只过滤指定日期当天的感想
                    val endOfDay = startDate.plus(1, ChronoUnit.DAYS)
                    filteredResults = filteredResults.filter { reflection ->
                        reflection.date >= startDate && reflection.date < endOfDay
                    }
                } else {
                    // 时间过滤器
                    filteredResults = when (timeFilter) {
                        TimeFilter.THIS_WEEK -> {
                            val weekAgo = Instant.now().minus(7, ChronoUnit.DAYS)
                            filteredResults.filter { it.date.isAfter(weekAgo) }
                        }
                        TimeFilter.THIS_MONTH -> {
                            val monthAgo = Instant.now().minus(30, ChronoUnit.DAYS)
                            filteredResults.filter { it.date.isAfter(monthAgo) }
                        }
                        TimeFilter.CUSTOM -> filteredResults
                    }
                }
                
                // 心情过滤
                if (moodFilters.isNotEmpty()) {
                    filteredResults = filteredResults.filter { it.mood in moodFilters }
                }
                
                // 类型过滤
                if (typeFilters.isNotEmpty()) {
                    filteredResults = filteredResults.filter { it.type in typeFilters }
                }
                
                // 高级过滤
                if (advancedFilters.isNotEmpty()) {
                    filteredResults = filteredResults.filter { reflection ->
                        advancedFilters.all { filter ->
                            when (filter) {
                                AdvancedFilter.CONTAIN_IMAGES -> 
                                    reflection.richContent.any { it.type == "image" }
                                AdvancedFilter.DEEP_REFLECTION -> 
                                    reflection.content.length > 500
                                AdvancedFilter.HAS_ACTION_ITEMS -> 
                                    reflection.plans.isNotEmpty()
                            }
                        }
                    }
                }
                
                Log.d("ReflectionRepository", "搜索结果: ${filteredResults.size}条记录")
                filteredResults
                
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "搜索感想失败", e)
                emptyList()
            }
        }
    }

    override suspend fun saveSearchQuery(query: String) {
        // 将新的搜索添加到列表开头
        recentSearches.remove(query) // 移除已存在的相同查询
        recentSearches.add(0, query) // 添加到开头
        
        // 限制最近搜索的数量
        if (recentSearches.size > 10) {
            recentSearches.removeAt(recentSearches.size - 1)
        }
    }

    override suspend fun getReflectionById(reflectionId: String): Reflection {
        return withContext(Dispatchers.IO) {
            try {
                val entity = reflectionDao.getReflectionById(reflectionId)
                if (entity != null) {
                    ReflectionMapper.entityToModel(entity)
                } else {
                    throw IllegalArgumentException("找不到ID为 $reflectionId 的感想记录")
                }
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "获取感想详情失败: $reflectionId", e)
                throw e
            }
        }
    }
    
    override suspend fun saveReflection(reflection: Reflection): String {
        return withContext(Dispatchers.IO) {
            try {
                // 检查是否来自任务完成
                val taskId = reflection.metrics["taskId"]?.toString()
                val taskTitle = reflection.metrics["taskTitle"]?.toString()
                val isFromTaskCompletion = taskId != null
                
                val entity = ReflectionMapper.modelToEntity(
                    model = reflection,
                    taskId = taskId,
                    taskTitle = taskTitle,
                    isFromTaskCompletion = isFromTaskCompletion
                )
                
                reflectionDao.insertReflection(entity)
                
                // 更新标签缓存
                reflection.tags.forEach { tag ->
                    if (!popularTags.contains(tag)) {
                        popularTags.add(tag)
                    }
                }
                
                Log.d("ReflectionRepository", "Reflection saved to database: id=${reflection.id}, title=${reflection.title}")
                reflection.id
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "保存感想失败", e)
                throw e
            }
        }
    }
    
    override suspend fun deleteReflection(reflectionId: String) {
        withContext(Dispatchers.IO) {
            try {
                reflectionDao.deleteReflectionById(reflectionId)
                Log.d("ReflectionRepository", "感想已从数据库删除: id=$reflectionId")
            } catch (e: Exception) {
                Log.e("ReflectionRepository", "删除感想失败: $reflectionId", e)
                throw e
            }
        }
    }
    
    /**
     * 初始化示例数据
     */
    private suspend fun initializeSampleData() {
        try {
            val sampleReflections = listOf(
                Reflection(
                    id = "sample_1",
                    title = "晨跑体验",
                    content = "今天早上5点起床去晨跑，感觉非常棒！空气清新，遇到了几位同样热爱晨跑的人。",
                    date = Instant.now().minus(2, ChronoUnit.DAYS),
                    rating = 5,
                    tags = listOf("运动", "晨跑", "健康"),
                    type = ReflectionType.EXERCISE,
                    mood = MoodType.HAPPY,
                    richContent = listOf(ContentBlock("text", "今天早上5点起床去晨跑，感觉非常棒！空气清新，遇到了几位同样热爱晨跑的人。")),
                    plans = listOf("明天继续晨跑"),
                    backgroundImage = null,
                    metrics = emptyMap()
                ),
                Reflection(
                    id = "sample_2",
                    title = "工作总结",
                    content = "今天完成了两个重要任务，虽然过程有些困难，但最终结果很满意。团队合作也很顺畅。",
                    date = Instant.now().minus(5, ChronoUnit.DAYS),
                    rating = 4,
                    tags = listOf("工作", "任务", "团队"),
                    type = ReflectionType.WORK,
                    mood = MoodType.CALM,
                    richContent = listOf(ContentBlock("text", "今天完成了两个重要任务，虽然过程有些困难，但最终结果很满意。团队合作也很顺畅。")),
                    plans = listOf("继续保持工作节奏"),
                    backgroundImage = null,
                    metrics = emptyMap()
                ),
                Reflection(
                    id = "sample_3",
                    title = "新书分享",
                    content = "读完了《原子习惯》这本书，收获颇丰。特别是关于习惯养成的四个步骤，对我很有启发。",
                    date = Instant.now().minus(10, ChronoUnit.DAYS),
                    rating = 5,
                    tags = listOf("阅读", "成长", "习惯"),
                    type = ReflectionType.STUDY,
                    mood = MoodType.HAPPY,
                    richContent = listOf(ContentBlock("text", "读完了《原子习惯》这本书，收获颇丰。特别是关于习惯养成的四个步骤，对我很有启发。")),
                    plans = listOf("开始实践书中的方法"),
                    backgroundImage = null,
                    metrics = emptyMap()
                )
            )
            
            sampleReflections.forEach { reflection ->
                val entity = ReflectionMapper.modelToEntity(reflection)
                reflectionDao.insertReflection(entity)
            }
            
            Log.d("ReflectionRepository", "初始化示例数据完成，共${sampleReflections.size}条记录")
        } catch (e: Exception) {
            Log.e("ReflectionRepository", "初始化示例数据失败", e)
        }
    }
} 