package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.EmotionRecordEntity
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * 情绪记录数据访问对象
 * 提供情绪记录的数据库操作方法
 */
@Dao
interface EmotionRecordDao {
    
    /**
     * 插入或更新情绪记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmotionRecord(record: EmotionRecordEntity)
    
    /**
     * 批量插入情绪记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmotionRecords(records: List<EmotionRecordEntity>)
    
    /**
     * 更新情绪记录
     */
    @Update
    suspend fun updateEmotionRecord(record: EmotionRecordEntity)
    
    /**
     * 删除情绪记录
     */
    @Delete
    suspend fun deleteEmotionRecord(record: EmotionRecordEntity)
    
    /**
     * 根据ID删除情绪记录
     */
    @Query("DELETE FROM emotion_records WHERE id = :id")
    suspend fun deleteEmotionRecordById(id: String)
    
    /**
     * 根据日期删除情绪记录
     */
    @Query("DELETE FROM emotion_records WHERE date = :date")
    suspend fun deleteEmotionRecordByDate(date: LocalDate)
    
    /**
     * 获取所有情绪记录
     */
    @Query("SELECT * FROM emotion_records ORDER BY date DESC, created_at DESC")
    fun getAllEmotionRecords(): Flow<List<EmotionRecordEntity>>
    
    /**
     * 根据ID获取情绪记录
     */
    @Query("SELECT * FROM emotion_records WHERE id = :id")
    suspend fun getEmotionRecordById(id: String): EmotionRecordEntity?
    
    /**
     * 根据日期获取情绪记录
     */
    @Query("SELECT * FROM emotion_records WHERE date = :date")
    suspend fun getEmotionRecordByDate(date: LocalDate): EmotionRecordEntity?
    
    /**
     * 获取指定日期范围内的情绪记录
     */
    @Query("SELECT * FROM emotion_records WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getEmotionRecordsByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<EmotionRecordEntity>>
    
    /**
     * 获取指定情绪类型的记录
     */
    @Query("SELECT * FROM emotion_records WHERE emotion_type = :emotionType ORDER BY date DESC")
    fun getEmotionRecordsByType(emotionType: String): Flow<List<EmotionRecordEntity>>
    
    /**
     * 获取详细记录
     */
    @Query("SELECT * FROM emotion_records WHERE is_detailed = 1 ORDER BY date DESC")
    fun getDetailedEmotionRecords(): Flow<List<EmotionRecordEntity>>
    
    /**
     * 获取最近N条记录
     */
    @Query("SELECT * FROM emotion_records ORDER BY date DESC, created_at DESC LIMIT :limit")
    fun getRecentEmotionRecords(limit: Int): Flow<List<EmotionRecordEntity>>
    
    /**
     * 清空所有情绪记录（谨慎使用）
     */
    @Query("DELETE FROM emotion_records")
    suspend fun deleteAllEmotionRecords()
    
    /**
     * 获取记录总数
     */
    @Query("SELECT COUNT(*) FROM emotion_records")
    suspend fun getEmotionRecordCount(): Int
    
    /**
     * 获取指定日期范围内的记录总数
     */
    @Query("SELECT COUNT(*) FROM emotion_records WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getEmotionRecordCountByDateRange(startDate: LocalDate, endDate: LocalDate): Int
}
