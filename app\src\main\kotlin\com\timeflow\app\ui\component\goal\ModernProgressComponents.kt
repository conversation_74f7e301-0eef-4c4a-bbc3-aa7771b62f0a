package com.timeflow.app.ui.component.goal

import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.PI

/**
 * 🎨 现代化环形进度条 - 参照知名app设计，支持莫兰迪/莫奈色系
 */
@Composable
fun CircularProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier,
    size: androidx.compose.ui.unit.Dp = 120.dp,
    strokeWidth: androidx.compose.ui.unit.Dp = 16.dp, // 增大描边宽度
    backgroundColor: Color = Color(0xFFF5F5F5), // 莫兰迪灰色背景
    progressColor: Color = Color(0xFF9BB5A6), // 莫兰迪薄荷绿
    showPercentage: Boolean = true,
    animationDuration: Int = 1200, // 增加动画时长
    useGradient: Boolean = true // 新增：是否使用渐变
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = animationDuration,
            easing = FastOutSlowInEasing
        )
    )
    
    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val center = Offset(size.toPx() / 2, size.toPx() / 2)
            val radius = (size.toPx() - strokeWidth.toPx()) / 2

            // 🎨 背景圆环 - 更柔和的阴影效果
            drawCircle(
                color = backgroundColor,
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round)
            )

            // 🎨 进度圆环 - 支持渐变和光泽效果
            if (animatedProgress > 0) {
                if (useGradient) {
                    // 渐变进度条
                    val gradientBrush = Brush.sweepGradient(
                        colors = listOf(
                            progressColor.copy(alpha = 0.7f),
                            progressColor,
                            progressColor.copy(alpha = 0.9f)
                        ),
                        center = center
                    )

                    drawArc(
                        brush = gradientBrush,
                        startAngle = -90f,
                        sweepAngle = 360f * animatedProgress,
                        useCenter = false,
                        style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round),
                        topLeft = Offset(
                            center.x - radius,
                            center.y - radius
                        ),
                        size = Size(radius * 2, radius * 2)
                    )

                    // 🌟 光泽效果
                    if (animatedProgress > 0.1f) {
                        val highlightBrush = Brush.sweepGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.White.copy(alpha = 0.3f),
                                Color.Transparent
                            ),
                            center = center
                        )

                        drawArc(
                            brush = highlightBrush,
                            startAngle = -90f,
                            sweepAngle = 360f * animatedProgress * 0.3f,
                            useCenter = false,
                            style = Stroke(width = strokeWidth.toPx() * 0.5f, cap = StrokeCap.Round),
                            topLeft = Offset(
                                center.x - radius,
                                center.y - radius
                            ),
                            size = Size(radius * 2, radius * 2)
                        )
                    }
                } else {
                    // 纯色进度条
                    drawArc(
                        color = progressColor,
                        startAngle = -90f,
                        sweepAngle = 360f * animatedProgress,
                        useCenter = false,
                        style = Stroke(width = strokeWidth.toPx(), cap = StrokeCap.Round),
                        topLeft = Offset(
                            center.x - radius,
                            center.y - radius
                        ),
                        size = Size(radius * 2, radius * 2)
                    )
                }
            }
        }
        
        if (showPercentage) {
            // 🎨 美化的百分比显示
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${(animatedProgress * 100).toInt()}%",
                    fontSize = (size.value / 5).sp, // 稍微增大字体
                    fontWeight = FontWeight.Bold,
                    color = progressColor,
                    textAlign = TextAlign.Center
                )

                // 进度状态文字
                Text(
                    text = when {
                        animatedProgress >= 1.0f -> "已完成"
                        animatedProgress >= 0.8f -> "即将完成"
                        animatedProgress >= 0.5f -> "进行中"
                        animatedProgress >= 0.2f -> "刚开始"
                        else -> "未开始"
                    },
                    fontSize = (size.value / 12).sp,
                    fontWeight = FontWeight.Medium,
                    color = progressColor.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 🎨 莫兰迪色系进度条颜色
 */
object MorandiColors {
    val MintGreen = Color(0xFF9BB5A6)      // 薄荷绿
    val WarmOrange = Color(0xFFD4A574)     // 暖橙
    val RosePink = Color(0xFFD4A5A5)       // 玫瑰粉
    val PurpleGray = Color(0xFFB5A6A6)     // 紫灰
    val SoftBlue = Color(0xFFA6B5D4)       // 柔和蓝
    val LightGray = Color(0xFFF5F5F5)      // 浅灰背景
}

/**
 * 🎨 莫奈色系进度条颜色
 */
object MonetColors {
    val NatureGreen = Color(0xFF81C784)    // 自然绿
    val SunsetOrange = Color(0xFFFFB74D)   // 日落橙
    val BloomRed = Color(0xFFE57373)       // 花朵红
    val LavenderPurple = Color(0xFF9575CD) // 薰衣草紫
    val SkyBlue = Color(0xFF64B5F6)        // 天空蓝
    val CloudGray = Color(0xFFF5F5F5)      // 云朵灰
}

/**
 * 🎨 获取莫兰迪色系的进度条颜色
 */
fun getMorandiProgressColor(index: Int): Color {
    val colors = listOf(
        MorandiColors.MintGreen,
        MorandiColors.WarmOrange,
        MorandiColors.RosePink,
        MorandiColors.PurpleGray,
        MorandiColors.SoftBlue
    )
    return colors[index % colors.size]
}

/**
 * 🎨 获取莫奈色系的进度条颜色
 */
fun getMonetProgressColor(index: Int): Color {
    val colors = listOf(
        MonetColors.NatureGreen,
        MonetColors.SunsetOrange,
        MonetColors.BloomRed,
        MonetColors.LavenderPurple,
        MonetColors.SkyBlue
    )
    return colors[index % colors.size]
}

/**
 * 进度热力图 - 显示每日进度
 */
@Composable
fun ProgressHeatmap(
    progressData: List<DayProgress>,
    modifier: Modifier = Modifier,
    columns: Int = 7,
    cellSize: androidx.compose.ui.unit.Dp = 16.dp,
    spacing: androidx.compose.ui.unit.Dp = 2.dp,
    lowColor: Color = Color(0xFFE5E7EB),
    highColor: Color = Color(0xFF6366F1)
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(columns),
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(spacing),
        verticalArrangement = Arrangement.spacedBy(spacing)
    ) {
        items(progressData.size) { index ->
            val dayData = progressData[index]
            val intensity = dayData.progress.coerceIn(0f, 1f)
            val cellColor = lerp(lowColor, highColor, intensity)
            
            Box(
                modifier = Modifier
                    .size(cellSize)
                    .background(
                        color = cellColor,
                        shape = RoundedCornerShape(4.dp)
                    )
                    .clickable { /* 显示当日详情 */ }
            )
        }
    }
}

/**
 * 统计图表卡片
 */
@Composable
fun StatisticsChart(
    title: String,
    data: List<ChartData>,
    modifier: Modifier = Modifier,
    chartType: ChartType = ChartType.LINE,
    primaryColor: Color = Color(0xFF6366F1)
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1F2937)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            when (chartType) {
                ChartType.LINE -> LineChart(data, primaryColor)
                ChartType.BAR -> BarChart(data, primaryColor)
                ChartType.AREA -> AreaChart(data, primaryColor)
            }
        }
    }
}

/**
 * 线性图表
 */
@Composable
fun LineChart(
    data: List<ChartData>,
    color: Color,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
    ) {
        if (data.isEmpty()) return@Canvas
        
        val maxValue = data.maxOfOrNull { it.value } ?: 1f
        val minValue = data.minOfOrNull { it.value } ?: 0f
        val range = maxValue - minValue
        
        val stepX = size.width / (data.size - 1).coerceAtLeast(1)
        val points = data.mapIndexed { index, chartData ->
            val x = index * stepX
            val y = size.height - ((chartData.value - minValue) / range) * size.height
            Offset(x, y)
        }
        
        // 绘制线条
        for (i in 0 until points.size - 1) {
            drawLine(
                color = color,
                start = points[i],
                end = points[i + 1],
                strokeWidth = 3.dp.toPx(),
                cap = StrokeCap.Round
            )
        }
        
        // 绘制点
        points.forEach { point ->
            drawCircle(
                color = color,
                radius = 4.dp.toPx(),
                center = point
            )
        }
    }
}

/**
 * 柱状图表
 */
@Composable
fun BarChart(
    data: List<ChartData>,
    color: Color,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
    ) {
        if (data.isEmpty()) return@Canvas
        
        val maxValue = data.maxOfOrNull { it.value } ?: 1f
        val barWidth = size.width / data.size * 0.8f
        val spacing = size.width / data.size * 0.2f
        
        data.forEachIndexed { index, chartData ->
            val barHeight = (chartData.value / maxValue) * size.height
            val x = index * (barWidth + spacing) + spacing / 2
            
            drawRoundRect(
                color = color,
                topLeft = Offset(x, size.height - barHeight),
                size = Size(barWidth, barHeight),
                cornerRadius = androidx.compose.ui.geometry.CornerRadius(4.dp.toPx())
            )
        }
    }
}

/**
 * 面积图表
 */
@Composable
fun AreaChart(
    data: List<ChartData>,
    color: Color,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
    ) {
        if (data.isEmpty()) return@Canvas
        
        val maxValue = data.maxOfOrNull { it.value } ?: 1f
        val minValue = data.minOfOrNull { it.value } ?: 0f
        val range = maxValue - minValue
        
        val stepX = size.width / (data.size - 1).coerceAtLeast(1)
        val points = data.mapIndexed { index, chartData ->
            val x = index * stepX
            val y = size.height - ((chartData.value - minValue) / range) * size.height
            Offset(x, y)
        }
        
        // 创建路径
        val path = Path().apply {
            moveTo(0f, size.height)
            points.forEach { point ->
                lineTo(point.x, point.y)
            }
            lineTo(size.width, size.height)
            close()
        }
        
        // 绘制面积
        drawPath(
            path = path,
            brush = Brush.verticalGradient(
                colors = listOf(
                    color.copy(alpha = 0.3f),
                    color.copy(alpha = 0.1f)
                )
            )
        )
        
        // 绘制边线
        for (i in 0 until points.size - 1) {
            drawLine(
                color = color,
                start = points[i],
                end = points[i + 1],
                strokeWidth = 2.dp.toPx(),
                cap = StrokeCap.Round
            )
        }
    }
}

/**
 * 进度指标卡片
 */
@Composable
fun ProgressMetricCard(
    title: String,
    value: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = color.copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column {
                Text(
                    text = title,
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Text(
                    text = value,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1F2937)
                )
                
                Text(
                    text = subtitle,
                    fontSize = 11.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 数据类定义
 */
data class DayProgress(
    val date: String,
    val progress: Float
)

data class ChartData(
    val label: String,
    val value: Float
)

enum class ChartType {
    LINE, BAR, AREA
}
