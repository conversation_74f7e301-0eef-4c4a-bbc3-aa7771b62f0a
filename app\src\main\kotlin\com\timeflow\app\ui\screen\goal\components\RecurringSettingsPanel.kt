package com.timeflow.app.ui.screen.goal.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.outlined.CalendarMonth
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.data.model.*
import com.timeflow.app.ui.theme.DustyLavender
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.TextButton
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.window.DialogProperties

/**
 * 周期性设置面板，用于设置目标的重复规则
 */
@Composable
fun RecurringSettingsPanel(
    isRecurring: Boolean,
    onRecurringChanged: (Boolean) -> Unit,
    recurringSettings: RecurringSettings?, 
    onSettingsChanged: (RecurringSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    val settings = remember(recurringSettings) { 
        recurringSettings ?: RecurringSettings() 
    }
    
    // 始终设置为永不结束
    LaunchedEffect(settings) {
        if (settings.endCondition !is EndCondition.Never) {
            onSettingsChanged(settings.copy(
                endCondition = EndCondition.Never,
                endDate = null,
                endCount = null
            ))
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 是否重复开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "目标重复",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333)
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            Switch(
                checked = isRecurring,
                onCheckedChange = onRecurringChanged,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.onPrimary,
                    checkedTrackColor = DustyLavender,
                    uncheckedThumbColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant
                )
            )
        }
        
        // 仅当启用重复时显示设置选项
        AnimatedVisibility(visible = isRecurring) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
            ) {
                // 重复频率选择
                Text(
                    text = "重复模式",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                FrequencySelector(
                    selected = settings.frequency,
                    onFrequencySelected = { frequency ->
                        onSettingsChanged(settings.copy(frequency = frequency))
                    }
                )
                
                // 根据选择的频率显示不同的设置面板
                when (settings.frequency) {
                    RecurringPeriod.DAILY -> DailySettingsPanel(
                        interval = settings.interval,
                        onIntervalChanged = { interval ->
                            onSettingsChanged(settings.copy(interval = interval))
                        }
                    )
                    
                    RecurringPeriod.WEEKLY -> WeeklySettingsPanel(
                        interval = settings.interval,
                        selectedDays = settings.weeklyDays,
                        onIntervalChanged = { interval ->
                            onSettingsChanged(settings.copy(interval = interval))
                        },
                        onDaysChanged = { days ->
                            onSettingsChanged(settings.copy(weeklyDays = days))
                        }
                    )
                    
                    RecurringPeriod.MONTHLY -> MonthlySettingsPanel(
                        settings = settings,
                        onSettingsChanged = onSettingsChanged
                    )
                    
                    RecurringPeriod.YEARLY -> YearlySettingsPanel(
                        interval = settings.interval,
                        onIntervalChanged = { interval ->
                            onSettingsChanged(settings.copy(interval = interval))
                        }
                    )
                }
                
                // 例外日期设置（仅当有例外日期时显示）
                if (settings.exceptions.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "例外日期",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    ExceptionDatesPanel(
                        exceptions = settings.exceptions,
                        onExceptionsChanged = { exceptions ->
                            onSettingsChanged(settings.copy(exceptions = exceptions))
                        }
                    )
                }
                
                // 添加例外日期按钮
                Spacer(modifier = Modifier.height(8.dp))
                
                var showExceptionDatePicker by remember { mutableStateOf(false) }
                
                // 日期选择对话框
                if (showExceptionDatePicker) {
                    ExceptionDatePickerDialog(
                        onDismissRequest = { showExceptionDatePicker = false },
                        onDateSelected = { selectedDate ->
                            if (!settings.exceptions.contains(selectedDate)) {
                                onSettingsChanged(settings.copy(
                                    exceptions = settings.exceptions + selectedDate
                                ))
                            }
                            showExceptionDatePicker = false
                        }
                    )
                }
                
                OutlinedButton(
                    onClick = { showExceptionDatePicker = true },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = DustyLavender
                    ),
                    border = BorderStroke(1.dp, DustyLavender)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("添加例外日期")
                }
                
                // 高级设置（跳过节假日和周末）
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "跳过节假日",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    Switch(
                        checked = settings.skipHolidays,
                        onCheckedChange = { skipHolidays ->
                            onSettingsChanged(settings.copy(skipHolidays = skipHolidays))
                        },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = DustyLavender,
                            checkedTrackColor = DustyLavender.copy(alpha = 0.5f)
                        )
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "跳过周末",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    Switch(
                        checked = settings.skipWeekends,
                        onCheckedChange = { skipWeekends ->
                            onSettingsChanged(settings.copy(skipWeekends = skipWeekends))
                        },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = DustyLavender,
                            checkedTrackColor = DustyLavender.copy(alpha = 0.5f)
                        )
                    )
                }
            }
        }
    }
}

/**
 * 频率选择器
 */
@Composable
private fun FrequencySelector(
    selected: RecurringPeriod,
    onFrequencySelected: (RecurringPeriod) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .selectableGroup(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        RecurringPeriod.values().forEach { period ->
            val text = when (period) {
                RecurringPeriod.DAILY -> "每日"
                RecurringPeriod.WEEKLY -> "每周"
                RecurringPeriod.MONTHLY -> "每月"
                RecurringPeriod.YEARLY -> "每年"
            }
            
            val isSelected = period == selected
            
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 4.dp)
                    .selectable(
                        selected = isSelected,
                        onClick = { onFrequencySelected(period) },
                        role = Role.RadioButton
                    ),
                shape = MaterialTheme.shapes.small,
                color = if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent,
                border = BorderStroke(
                    width = 1.dp,
                    color = if (isSelected) DustyLavender else Color(0xFFE2E8F0)
                )
            ) {
                Text(
                    text = text,
                    color = if (isSelected) DustyLavender else Color(0xFF666666),
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

/**
 * 每日设置面板
 */
@Composable
private fun DailySettingsPanel(
    interval: Int,
    onIntervalChanged: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "每",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 间隔输入框
        OutlinedTextField(
            value = interval.toString(),
            onValueChange = { value ->
                value.toIntOrNull()?.let { 
                    if (it > 0) onIntervalChanged(it)
                }
            },
            modifier = Modifier.width(64.dp),
            keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
            ),
            singleLine = true,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = DustyLavender,
                unfocusedBorderColor = Color(0xFFE2E8F0)
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "天重复一次",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
    }
}

/**
 * 每周设置面板
 */
@Composable
private fun WeeklySettingsPanel(
    interval: Int,
    selectedDays: Set<DayOfWeek>,
    onIntervalChanged: (Int) -> Unit,
    onDaysChanged: (Set<DayOfWeek>) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp)
    ) {
        // 间隔设置
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "每",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 间隔输入框
            OutlinedTextField(
                value = interval.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { 
                        if (it > 0) onIntervalChanged(it)
                    }
                },
                modifier = Modifier.width(64.dp),
                keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                    keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
                ),
                singleLine = true,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = DustyLavender,
                    unfocusedBorderColor = Color(0xFFE2E8F0)
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "周重复一次",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 星期选择
        Text(
            text = "选择具体星期：",
            fontSize = 14.sp,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 星期选择器（每周七天）
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 按顺序显示周一到周日
            val weekDays = listOf(
                DayOfWeek.MONDAY,
                DayOfWeek.TUESDAY,
                DayOfWeek.WEDNESDAY,
                DayOfWeek.THURSDAY,
                DayOfWeek.FRIDAY,
                DayOfWeek.SATURDAY,
                DayOfWeek.SUNDAY
            )
            
            weekDays.forEach { day ->
                val dayText = day.getDisplayName(TextStyle.SHORT, Locale.CHINA)
                val isSelected = selectedDays.contains(day)
                
                Surface(
                    modifier = Modifier
                        .size(36.dp)
                        .padding(horizontal = 2.dp),
                    shape = MaterialTheme.shapes.small,
                    color = if (isSelected) DustyLavender else Color.Transparent,
                    border = BorderStroke(
                        width = 1.dp,
                        color = if (isSelected) DustyLavender else Color(0xFFE2E8F0)
                    ),
                    onClick = {
                        val newSelectedDays = selectedDays.toMutableSet()
                        if (isSelected) {
                            newSelectedDays.remove(day)
                        } else {
                            newSelectedDays.add(day)
                        }
                        onDaysChanged(newSelectedDays)
                    }
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Text(
                            text = dayText,
                            color = if (isSelected) Color.White else Color(0xFF666666),
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 每月设置面板
 */
@Composable
private fun MonthlySettingsPanel(
    settings: RecurringSettings,
    onSettingsChanged: (RecurringSettings) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp)
    ) {
        // 间隔设置
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "每",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 间隔输入框
            OutlinedTextField(
                value = settings.interval.toString(),
                onValueChange = { value ->
                    value.toIntOrNull()?.let { 
                        if (it > 0) onSettingsChanged(settings.copy(interval = it))
                    }
                },
                modifier = Modifier.width(64.dp),
                keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                    keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
                ),
                singleLine = true,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = DustyLavender,
                    unfocusedBorderColor = Color(0xFFE2E8F0)
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "个月重复一次",
                fontSize = 14.sp,
                color = Color(0xFF666666)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 每月类型选择（固定日期或相对日期）
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            // 固定日期选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = settings.monthlyType == MonthlyType.DAY_OF_MONTH,
                        onClick = {
                            onSettingsChanged(settings.copy(
                                monthlyType = MonthlyType.DAY_OF_MONTH,
                                dayOfMonth = settings.dayOfMonth ?: 1
                            ))
                        },
                        role = Role.RadioButton
                    )
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = settings.monthlyType == MonthlyType.DAY_OF_MONTH,
                    onClick = null, // 点击事件已在Row上处理
                    colors = RadioButtonDefaults.colors(
                        selectedColor = DustyLavender
                    )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "每月第",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 日期输入框
                OutlinedTextField(
                    value = (settings.dayOfMonth ?: 1).toString(),
                    onValueChange = { value ->
                        value.toIntOrNull()?.let { day ->
                            if (day in 1..31) {
                                onSettingsChanged(settings.copy(
                                    monthlyType = MonthlyType.DAY_OF_MONTH,
                                    dayOfMonth = day
                                ))
                            }
                        }
                    },
                    enabled = settings.monthlyType == MonthlyType.DAY_OF_MONTH,
                    modifier = Modifier.width(64.dp),
                    keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                        keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
                    ),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        unfocusedBorderColor = Color(0xFFE2E8F0)
                    )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "日",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
            
            // 相对日期选项
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = settings.monthlyType == MonthlyType.DAY_OF_WEEK,
                        onClick = {
                            onSettingsChanged(settings.copy(
                                monthlyType = MonthlyType.DAY_OF_WEEK,
                                weekOfMonth = settings.weekOfMonth ?: WeekOfMonth.FIRST,
                                dayOfWeek = settings.dayOfWeek ?: DayOfWeek.MONDAY
                            ))
                        },
                        role = Role.RadioButton
                    )
                    .padding(vertical = 8.dp)
            ) {
                RadioButton(
                    selected = settings.monthlyType == MonthlyType.DAY_OF_WEEK,
                    onClick = null, // 点击事件已在Row上处理
                    colors = RadioButtonDefaults.colors(
                        selectedColor = DustyLavender
                    )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "每月第",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 第几周下拉菜单
                Box {
                    var weekMenuExpanded by remember { mutableStateOf(false) }
                    
                    OutlinedButton(
                        onClick = { weekMenuExpanded = true },
                        enabled = settings.monthlyType == MonthlyType.DAY_OF_WEEK,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = if (settings.monthlyType == MonthlyType.DAY_OF_WEEK) 
                                DustyLavender else Color.Gray
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = if (settings.monthlyType == MonthlyType.DAY_OF_WEEK) 
                                DustyLavender else Color(0xFFE2E8F0)
                        )
                    ) {
                        Text(
                            text = when (settings.weekOfMonth) {
                                WeekOfMonth.FIRST -> "第一"
                                WeekOfMonth.SECOND -> "第二"
                                WeekOfMonth.THIRD -> "第三"
                                WeekOfMonth.FOURTH -> "第四"
                                WeekOfMonth.LAST -> "最后一"
                                null -> "第一"
                            }
                        )
                    }
                    
                    DropdownMenu(
                        expanded = weekMenuExpanded,
                        onDismissRequest = { weekMenuExpanded = false }
                    ) {
                        listOf(
                            WeekOfMonth.FIRST to "第一",
                            WeekOfMonth.SECOND to "第二",
                            WeekOfMonth.THIRD to "第三",
                            WeekOfMonth.FOURTH to "第四",
                            WeekOfMonth.LAST to "最后一"
                        ).forEach { (week, text) ->
                            DropdownMenuItem(
                                text = { Text(text) },
                                onClick = {
                                    onSettingsChanged(settings.copy(
                                        monthlyType = MonthlyType.DAY_OF_WEEK,
                                        weekOfMonth = week
                                    ))
                                    weekMenuExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "个",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 星期几下拉菜单
                Box {
                    var dayMenuExpanded by remember { mutableStateOf(false) }
                    
                    OutlinedButton(
                        onClick = { dayMenuExpanded = true },
                        enabled = settings.monthlyType == MonthlyType.DAY_OF_WEEK,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = if (settings.monthlyType == MonthlyType.DAY_OF_WEEK) 
                                DustyLavender else Color.Gray
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = if (settings.monthlyType == MonthlyType.DAY_OF_WEEK) 
                                DustyLavender else Color(0xFFE2E8F0)
                        )
                    ) {
                        Text(
                            text = settings.dayOfWeek?.getDisplayName(
                                TextStyle.FULL, Locale.CHINA
                            ) ?: "周一"
                        )
                    }
                    
                    DropdownMenu(
                        expanded = dayMenuExpanded,
                        onDismissRequest = { dayMenuExpanded = false }
                    ) {
                        DayOfWeek.values().forEach { day ->
                            val dayText = day.getDisplayName(TextStyle.FULL, Locale.CHINA)
                            DropdownMenuItem(
                                text = { Text(dayText) },
                                onClick = {
                                    onSettingsChanged(settings.copy(
                                        monthlyType = MonthlyType.DAY_OF_WEEK,
                                        dayOfWeek = day
                                    ))
                                    dayMenuExpanded = false
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 每年设置面板
 */
@Composable
private fun YearlySettingsPanel(
    interval: Int,
    onIntervalChanged: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "每",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 间隔输入框
        OutlinedTextField(
            value = interval.toString(),
            onValueChange = { value ->
                value.toIntOrNull()?.let { 
                    if (it > 0) onIntervalChanged(it)
                }
            },
            modifier = Modifier.width(64.dp),
            keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
            ),
            singleLine = true,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = DustyLavender,
                unfocusedBorderColor = Color(0xFFE2E8F0)
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "年重复一次",
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
    }
}

/**
 * 例外日期面板
 */
@Composable
private fun ExceptionDatesPanel(
    exceptions: List<LocalDate>,
    onExceptionsChanged: (List<LocalDate>) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        exceptions.forEach { date ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                IconButton(
                    onClick = {
                        onExceptionsChanged(exceptions - date)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = Color.Red,
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        }
    }
}

/**
 * 例外日期选择对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ExceptionDatePickerDialog(
    onDismissRequest: () -> Unit,
    onDateSelected: (LocalDate) -> Unit
) {
    val datePickerState = rememberDatePickerState()
    
    DatePickerDialog(
        onDismissRequest = onDismissRequest,
        confirmButton = {
            TextButton(onClick = {
                datePickerState.selectedDateMillis?.let { millis ->
                    val selectedDate = java.time.Instant.ofEpochMilli(millis)
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate()
                    onDateSelected(selectedDate)
                }
            }) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismissRequest) {
                Text("取消")
            }
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            usePlatformDefaultWidth = false // 使用自定义宽度
        ),
        modifier = Modifier
            .fillMaxWidth(0.95f) // 设置对话框宽度为屏幕的95%
            .padding(16.dp),
        colors = DatePickerDefaults.colors(
            containerColor = Color.White,
            titleContentColor = DustyLavender,
            headlineContentColor = Color(0xFF333333),
            weekdayContentColor = Color(0xFF666666),
            dayContentColor = Color(0xFF333333),
            selectedDayContainerColor = DustyLavender,
            todayContentColor = DustyLavender,
            todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
        )
    ) {
        DatePicker(
            state = datePickerState,
            showModeToggle = false,
            title = { 
                Text(
                    text = "选择例外日期", 
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(start = 16.dp, top = 16.dp)
                ) 
            },
            headline = { 
                Text(
                    text = datePickerState.selectedDateMillis?.let {
                        val selectedDate = java.time.Instant.ofEpochMilli(it)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDate()
                        selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                    } ?: "请选择一个日期",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            },
            colors = DatePickerDefaults.colors(
                containerColor = Color.White,
                titleContentColor = DustyLavender,
                headlineContentColor = Color(0xFF333333),
                weekdayContentColor = Color(0xFF666666),
                dayContentColor = Color(0xFF333333),
                selectedDayContainerColor = DustyLavender,
                todayContentColor = DustyLavender,
                todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
            )
        )
    }
}

/**
 * 预览函数
 */
@Composable
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
fun RecurringSettingsPanelPreview() {
    var isRecurring by remember { mutableStateOf(true) }
    var settings by remember { mutableStateOf(RecurringSettings()) }
    
    RecurringSettingsPanel(
        isRecurring = isRecurring,
        onRecurringChanged = { isRecurring = it },
        recurringSettings = settings,
        onSettingsChanged = { settings = it },
        modifier = Modifier.padding(16.dp)
    )
} 