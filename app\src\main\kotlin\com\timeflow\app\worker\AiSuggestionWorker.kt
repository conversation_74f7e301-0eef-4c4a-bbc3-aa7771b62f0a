package com.timeflow.app.worker

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.TimeFlowNotificationManager
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.timeflow.app.data.repository.TaskRepository
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * AI建议通知工作器
 * 负责定期发送智能建议通知
 */
class AiSuggestionWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "AiSuggestionWorker"
        const val WORK_NAME_WEEKLY = "ai_suggestion_weekly"
        const val WORK_NAME_MONTHLY = "ai_suggestion_monthly"
    }
    
    // 通知设置DataStore
    private val Context.notificationSettingsDataStore by preferencesDataStore(name = "notification_settings")
    
    // DataStore键定义
    private object PreferencesKeys {
        val AI_SUGGESTIONS_ENABLED = booleanPreferencesKey("ai_suggestions_enabled")
        val WEEKLY_REPORTS_ENABLED = booleanPreferencesKey("weekly_reports_enabled")
        val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")
        val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")
        val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
    }
    
    override suspend fun doWork(): Result {
        return try {
            Log.d(TAG, "🤖 开始执行AI建议通知任务")
            
            // 读取通知设置
            val settings = loadNotificationSettings()
            
            if (!settings.notificationsEnabled) {
                Log.d(TAG, "❌ 通知已禁用，跳过发送AI建议")
                return Result.success()
            }
            
            // 获取通知类型（周报还是月报）
            val notificationType = inputData.getString("notification_type") ?: "weekly"
            
            // 生成AI建议内容
            val aiSuggestion = generateAiSuggestion(notificationType)
            
            // 发送通知
            val notificationManager = TimeFlowNotificationManager(
                context,
                com.timeflow.app.utils.NotificationHelper(context)
            )
            
            when (notificationType) {
                "daily" -> {
                    // 发送每日效率总结
                    val dailyStats = generateDailyStats()
                    notificationManager.showDailyEfficiencySummary(dailyStats, settings)
                    Log.d(TAG, "📈 每日效率总结通知已发送")
                }
                "weekly", "monthly" -> {
                    // AI建议和周报功能已移除
                    Log.d(TAG, "💡 AI建议和周报功能已移除，跳过发送")
                }
                else -> {
                    // AI建议功能已移除
                    Log.d(TAG, "💡 AI建议功能已移除，跳过发送")
                }
            }
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "AI建议通知任务执行失败", e)
            Result.failure()
        }
    }
    
    /**
     * 读取通知设置
     */
    private suspend fun loadNotificationSettings(): NotificationSettings {
        return try {
            val preferences = context.notificationSettingsDataStore.data.first()
            NotificationSettings(
                notificationsEnabled = preferences[PreferencesKeys.NOTIFICATIONS_ENABLED] ?: true,
                vibrationEnabled = preferences[PreferencesKeys.VIBRATION_ENABLED] ?: true,
                soundEnabled = preferences[PreferencesKeys.SOUND_ENABLED] ?: true,
                // 其他设置使用默认值 - 简化后的结构
                taskRemindersEnabled = true,
                deadlineRemindersEnabled = true,
                overdueRemindersEnabled = true,
                dailyReviewEnabled = true,
                dailyReviewTime = "21:00",
                taskPersistentNotificationEnabled = true,
                // 习惯培养提醒（简化）
                habitRemindersEnabled = true,

                // 专注时间提醒（简化）
                focusRemindersEnabled = true,
                focusSessionNotificationsEnabled = true,

                // 健康管理提醒（新增用药提醒）
                medicationRemindersEnabled = true,
                medicationSoundEnabled = true,
                medicationVibrationEnabled = true,
                medicationAdvanceTime = 5,

                // 免打扰设置（简化）
                doNotDisturbEnabled = false,
                doNotDisturbStartTime = "22:00",
                doNotDisturbEndTime = "08:00",

                // 提醒时间设置（统一简化）
                defaultReminderTime = 15
            )
        } catch (e: Exception) {
            Log.e(TAG, "读取通知设置失败，使用默认设置", e)
            NotificationSettings() // 返回默认设置
        }
    }
    
    /**
     * 生成每日统计数据
     */
    private fun generateDailyStats(): com.timeflow.app.utils.DailyStats {
        // 这里可以集成真实的任务数据统计
        // 目前使用模拟数据
        val completedTasks = (3..12).random()
        val totalTasks = (completedTasks..15).random()
        val focusTime = (30..240).random() // 30分钟到4小时
        val completionRate = if (totalTasks > 0) (completedTasks * 100) / totalTasks else 0
        val efficiencyScore = when {
            completionRate >= 90 -> (85..100).random()
            completionRate >= 70 -> (70..85).random()
            completionRate >= 50 -> (55..70).random()
            completionRate >= 30 -> (40..55).random()
            else -> (20..40).random()
        }
        
        return com.timeflow.app.utils.DailyStats(
            completedTasks = completedTasks,
            totalTasks = totalTasks,
            focusTime = focusTime,
            completionRate = completionRate,
            efficiencyScore = efficiencyScore
        )
    }

    /**
     * 生成AI建议内容
     */
    private fun generateAiSuggestion(type: String): AiSuggestion {
        val currentTime = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
        
        return when (type) {
            "weekly" -> AiSuggestion(
                title = "本周效率提升建议",
                content = "根据您本周的任务完成情况，AI助手建议：\n\n1. 🎯 专注时段优化：建议在上午9-11点安排重要任务\n2. ⏰ 任务时间预估：可以适当增加15%的缓冲时间\n3. 🔄 工作节奏调整：每工作50分钟休息10分钟效果更佳\n\n继续保持良好的工作习惯！"
            )
            "monthly" -> AiSuggestion(
                title = "月度工作模式分析",
                content = "基于您近一个月的数据分析，为您提供以下建议：\n\n1. 📈 效率趋势：您的工作效率在周三和周四最高\n2. 🎯 目标达成：建议将大目标拆分为更小的里程碑\n3. 💪 习惯养成：您在坚持习惯方面表现优秀，可以尝试增加新的挑战\n\n${currentTime.format(formatter)}，继续加油！"
            )
            else -> AiSuggestion(
                title = "智能提醒",
                content = "根据您的使用模式，AI助手为您推荐了一些优化建议，点击查看详情。"
            )
        }
    }
    
    /**
     * 生成周报统计数据
     */
    private fun generateWeeklyStats(): com.timeflow.app.utils.WeeklyStats {
        // 这里可以集成真实的任务数据统计
        // 目前使用模拟数据
        return com.timeflow.app.utils.WeeklyStats(
            completedTasks = (15..35).random(),
            focusTime = (20..45).random(),
            productivityScore = (75..95).random()
        )
    }
}

/**
 * AI建议数据类
 */
data class AiSuggestion(
    val title: String,
    val content: String
) 