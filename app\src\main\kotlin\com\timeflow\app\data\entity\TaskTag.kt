package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "task_tags",
    foreignKeys = [
        ForeignKey(
            entity = Task::class,
            parentColumns = ["id"],
            childColumns = ["taskId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("taskId"),
        Index("name")
    ]
)
data class TaskTag(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val taskId: String,
    val name: String,
    val color: String? = null,
    val createdAt: Long = System.currentTimeMillis()
) 