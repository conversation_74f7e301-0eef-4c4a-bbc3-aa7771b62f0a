package com.timeflow.app.data.model

import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

/**
 * 提醒类型
 */
enum class ReminderType {
    ONCE,       // 单次提醒
    DAILY,      // 每天提醒
    WEEKLY,     // 每周提醒
    MONTHLY,    // 每月提醒
    BEFORE_DUE, // 截止日期前提醒
    COMPLETION, // 目标完成时提醒
    PROGRESS,   // 进度达到某个阈值时提醒
    CUSTOM      // 自定义提醒
}

/**
 * 进度提醒阈值类型
 */
enum class ProgressThresholdType {
    PERCENTAGE, // 百分比
    VALUE       // 具体值
}

/**
 * 提醒设置数据类
 */
data class ReminderSetting(
    val id: String = UUID.randomUUID().toString(),
    val type: ReminderType = ReminderType.ONCE,
    val enabled: Boolean = true,
    val time: LocalTime? = null,
    val daysBeforeDue: Int? = null,
    val progressThreshold: Float? = null,
    val progressThresholdType: ProgressThresholdType? = null,
    val customDateTime: LocalDateTime? = null,
    val description: String? = null,
    val message: String = ""
) {
    companion object {
        // 创建默认提醒设置的工厂方法
        fun createDefault(type: ReminderType): ReminderSetting {
            return when (type) {
                ReminderType.ONCE -> ReminderSetting(
                    type = ReminderType.ONCE,
                    customDateTime = LocalDateTime.now().plusDays(1)
                )
                ReminderType.DAILY -> ReminderSetting(
                    type = ReminderType.DAILY,
                    time = LocalTime.of(9, 0) // 默认每天上午9点提醒
                )
                ReminderType.WEEKLY -> ReminderSetting(
                    type = ReminderType.WEEKLY,
                    time = LocalTime.of(9, 0) // 默认每周一上午9点提醒
                )
                ReminderType.MONTHLY -> ReminderSetting(
                    type = ReminderType.MONTHLY,
                    time = LocalTime.of(9, 0) // 默认每月1日上午9点提醒
                )
                ReminderType.BEFORE_DUE -> ReminderSetting(
                    type = ReminderType.BEFORE_DUE,
                    daysBeforeDue = 1, // 默认截止日期前1天提醒
                    time = LocalTime.of(9, 0)
                )
                ReminderType.COMPLETION -> ReminderSetting(
                    type = ReminderType.COMPLETION
                )
                ReminderType.PROGRESS -> ReminderSetting(
                    type = ReminderType.PROGRESS,
                    progressThreshold = 50f, // 默认进度达到50%时提醒
                    progressThresholdType = ProgressThresholdType.PERCENTAGE
                )
                ReminderType.CUSTOM -> ReminderSetting(
                    type = ReminderType.CUSTOM,
                    customDateTime = LocalDateTime.now().plusDays(1) // 默认明天此时提醒
                )
            }
        }
    }
} 