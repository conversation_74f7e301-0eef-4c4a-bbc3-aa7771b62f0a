package com.timeflow.app.navigation

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.home.UnifiedHomeScreen
import com.timeflow.app.ui.screen.task.TaskListFullScreen
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel

@Composable
fun NavGraph(
    navController: NavHostController,
    startDestination: String = AppDestinations.UNIFIED_HOME_ROUTE
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable(AppDestinations.TASK_LIST_ROUTE) {
            TaskListFullScreen(
                navController = navController,
                viewModel = hiltViewModel()
            )
        }
        
        composable(AppDestinations.HOME_ROUTE) {
            UnifiedHomeScreen(
                navController = navController,
                timeFlowViewModel = hiltViewModel(),
                taskViewModel = hiltViewModel()
            )
        }
        
        composable(AppDestinations.UNIFIED_HOME_ROUTE) {
            UnifiedHomeScreen(
                navController = navController,
                timeFlowViewModel = hiltViewModel(),
                taskViewModel = hiltViewModel()
            )
        }
        
        composable(AppDestinations.ANALYTICS_ROUTE) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "Analytics Screen")
            }
        }
        
        composable(AppDestinations.PROFILE_ROUTE) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "Profile Screen")
            }
        }
    }
}

object NavDestinations {
    const val HOME_ROUTE = "home"
    const val TASK_ROUTE = "task"
    const val ANALYTICS_ROUTE = "analytics"
    const val PROFILE_ROUTE = "profile"
} 