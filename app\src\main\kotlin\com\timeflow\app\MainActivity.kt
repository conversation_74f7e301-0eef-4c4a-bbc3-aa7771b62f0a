package com.timeflow.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.isSystemInDarkTheme
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.timeflow.app.ui.navigation.TimeFlowNavHost
import com.timeflow.app.ui.theme.TimeFlowTheme
import com.timeflow.app.ui.theme.ThemeManager
import com.timeflow.app.ui.settings.ThemeMode
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.RenderOptimizer
import com.timeflow.app.utils.PerformanceMonitor
import dagger.hilt.android.AndroidEntryPoint
import android.view.WindowManager
import android.graphics.PixelFormat
import android.os.Build
import androidx.core.view.WindowCompat
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.WindowManager.LayoutParams
import android.graphics.Color
import android.content.res.Configuration
import com.timeflow.app.utils.ActivityContextProvider
import com.timeflow.app.utils.RippleOptimizer
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.di.DataStoreModule.ThemeDataStore
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    companion object {
        private const val TAG = "MainActivity"
        private const val RENDER_DELAY_MS = 100L
    }

    @Inject
    @ThemeDataStore
    lateinit var themeDataStore: DataStore<Preferences>

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化ActivityContextProvider
        ActivityContextProvider.setActivityContext(this)
        
        // 初始化主题管理器
        ThemeManager.initialize(applicationContext, themeDataStore)
        
        // 初始化预设主题管理器
        com.timeflow.app.ui.theme.PresetThemeManager.initialize(themeDataStore)
        
        // 应用渲染优化
        applyRenderingOptimizations()
        
        // 初始化系统栏管理，使用无动画设置
        SystemBarManager.setupActivitySystemBars(this)
        
        // 设置内容，添加启动画面
        setContent {
            // 获取主题设置
            val themePreference by ThemeManager.userThemePreference.collectAsState()
            val isSystemInDarkTheme = isSystemInDarkTheme()
            
            // 根据用户设置决定是否使用暗色主题
            val isDarkTheme = when {
                themePreference.useSystemDarkMode -> isSystemInDarkTheme
                else -> themePreference.isDarkMode
            }
            
            TimeFlowTheme(
                darkTheme = isDarkTheme
            ) {
                // 添加性能监控
                PerformanceMonitor.MonitorSetup()
                
                // 启动画面状态管理
                var showSplash by remember { mutableStateOf(true) }
                
                if (showSplash) {
                    // 显示启动画面
                    com.timeflow.app.ui.splash.TimeFlowSplashScreen(
                        onSplashFinished = {
                            showSplash = false
                        }
                    )
                } else {
                    // 显示主应用内容
                val navController = rememberNavController()
                    
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    TimeFlowNavHost(navController = navController)
                    }
                }
            }
        }
    }
    
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        // 监听系统暗色模式变化
        val isSystemDark = (newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES
        
        // 如果用户选择跟随系统，则更新主题
        val currentPreference = ThemeManager.userThemePreference.value
        if (currentPreference.useSystemDarkMode && currentPreference.isDarkMode != isSystemDark) {
            Log.d(TAG, "系统暗色模式变化，更新应用主题: $isSystemDark")
            // 通过ThemeManager更新主题
            kotlin.runCatching {
                kotlinx.coroutines.runBlocking {
                    ThemeManager.toggleDarkMode(isSystemDark)
                }
            }.onFailure { e ->
                Log.e(TAG, "更新主题失败: ${e.message}")
            }
        }
    }
    
    private fun applyRenderingOptimizations() {
        try {
            val window = window
            
            // 设置硬件加速
            window.setFlags(
                LayoutParams.FLAG_HARDWARE_ACCELERATED,
                LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 设置窗口格式为RGBA_8888，这是最兼容的格式
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 启用高效绘制模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.colorMode = 0x1 // COLOR_MODE_WIDE_COLOR_GAMUT的值为1
            }
            
            // 设置窗口背景为黑色，避免透明度处理
            window.decorView.setBackgroundColor(Color.BLACK)
            
            // 强制启用硬件加速
            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 设置状态栏和导航栏颜色
            window.statusBarColor = Color.BLACK
            window.navigationBarColor = Color.BLACK
            
            // 设置窗口参数，避免半透明和模糊效果
            val attrs = window.attributes
            attrs.alpha = 1.0f  // 完全不透明
            attrs.dimAmount = 0f  // 禁用背景变暗
            window.attributes = attrs
            
            // 应用RippleOptimizer优化
            RippleOptimizer.enableHardwareAccelerationForAllViews(this)
            
            // 即时应用RenderOptimizer优化，不使用延迟
            try {
                // 应用RenderOptimizer的优化
                RenderOptimizer.setupWindowOptimizations(this)
                
                Log.d(TAG, "已应用渲染优化")
            } catch (e: Exception) {
                Log.e(TAG, "应用渲染优化失败: ${e.message}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "初始化渲染优化失败: ${e.message}")
        }
    }
    
    override fun onPause() {
        super.onPause()
        // 在Activity暂停时重置系统栏状态
        SystemBarManager.resetActivitySystemBars(this)
        // 重置渲染优化器状态
        RenderOptimizer.resetOptimizerState()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清除Activity引用
        ActivityContextProvider.clearActivityContext()
    }
}