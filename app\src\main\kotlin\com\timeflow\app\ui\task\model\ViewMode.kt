package com.timeflow.app.ui.task.model

/**
 * 任务视图模式
 * 定义不同的任务视图展示方式
 */
enum class ViewMode {
    LIST,           // 列表视图
    KANBAN,         // 看板视图
    TIMELINE,       // 时间线视图
    CALENDAR,       // 日历视图
    QUADRANT,       // 四象限视图
    HIERARCHY;      // 层级视图
    
    companion object {
        /**
         * 获取默认视图模式
         */
        fun default() = LIST
        
        /**
         * 获取视图名称
         */
        fun getName(viewMode: ViewMode): String {
            return when(viewMode) {
                LIST -> "列表"
                KANBAN -> "看板"
                TIMELINE -> "时间线"
                CALENDAR -> "日历"
                QUADRANT -> "四象限"
                HIERARCHY -> "层级"
            }
        }
    }
} 