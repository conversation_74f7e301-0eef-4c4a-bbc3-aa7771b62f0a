# 任务模型清理和功能优化

## 问题分析

通过代码分析，我们发现存在以下问题：

1. **代码重复问题**：
   - 在`TaskViewModel`中有大量重复的导入语句，特别是与`HapticFeedbackType`相关的导入
   - 看板功能在`TaskViewModel`和`KanbanViewModel`中存在两套实现

2. **模型重复**：
   - 数据层：`data.entity.Task`和`data.model.Task`两种任务模型
   - UI层通过`TaskConverter`在两个模型之间转换

## 清理计划

我们将采取以下措施清理代码：

1. **修复导入问题**：
   - 移除`TaskViewModel`中的重复导入语句
   - 只保留必要的导入

2. **统一看板实现**：
   - 删除`TaskViewModel`中的`updateKanbanColumns`方法和相关状态流
   - 修改调用位置，委托给`KanbanViewModel`处理看板操作

3. **保留模型转换器**：
   - 保留`TaskConverter`以处理`entity.Task`和`model.Task`之间的转换
   - 逐步迁移到统一的任务模型

## 修改细节

### 1. 导入清理
- 移除重复的`HapticFeedbackType.Companion.TaskStatusChanged`导入
- 确保导入语句合理组织

### 2. 看板功能整合
- 删除`TaskViewModel`中的`_kanbanColumns`和`kanbanColumns`状态流
- 删除`updateKanbanColumns`方法
- 修改`refreshTasks`方法，引用`KanbanViewModel`的实例
- 修改`TaskViewModel`构造函数，注入`KanbanViewModel`

### 3. 文档更新
- 更新README文件，说明任务模型的重构和看板功能的增强
- 记录可能的API变更

## 预期效果

1. 代码更清晰，没有重复导入
2. 看板功能统一由`KanbanViewModel`实现，保持一致性
3. 功能保持一致，性能可能略有提升
4. 为未来进一步合并任务模型做准备 