import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.timeflow.app.ui.navigation.AppDestinations.HABIT_DETAIL_ROUTE
import com.timeflow.app.ui.screen.health.HabitDetailScreen

@Composable
fun NavigationGraph(
    navController: NavHostController
) {
    NavHost(
        navController = navController,
        startDestination = "habit_tracker" // Replace with your actual start destination
    ) {
        // ... existing code ...
        
        // 习惯详情页面
        composable(
            route = HABIT_DETAIL_ROUTE + "/{habitId}",
            arguments = listOf(
                navArgument("habitId") {
                    type = NavType.StringType
                }
            )
        ) { backStackEntry ->
            val habitId = backStackEntry.arguments?.getString("habitId") ?: ""
            HabitDetailScreen(
                habitId = habitId,
                navController = navController
            )
        }
        
        // ... existing code ...
    }
} 