package com.timeflow.app.ui.screen.reflection

import androidx.annotation.DrawableRes
import java.time.Instant
import com.timeflow.app.data.model.MoodType

// 重新编译标记 - 修复重复接口定义问题

/**
 * 感想实体模型
 */
data class Reflection(
    val id: String,
    val title: String,
    val content: String,
    val richContent: List<ContentBlock> = emptyList(),
    val date: Instant,
    val rating: Int,
    val tags: List<String>,
    val type: ReflectionType,
    val mood: MoodType,
    val metrics: Map<String, String> = emptyMap(),
    val plans: List<String> = emptyList(),
    val backgroundImage: String? = null
)

/**
 * 内容块类型
 */
data class ContentBlock(
    val type: String, // "text" 或 "image"
    val value: String
)

/**
 * 反思类型枚举
 */
enum class ReflectionType(val displayName: String, @DrawableRes val iconRes: Int) {
    EXERCISE("运动", 0), // 需要添加实际图标资源
    STUDY("学习", 0),
    WORK("工作", 0),
    CREATIVITY("创意", 0),
    LIFE("生活", 0),
    PERSONAL("个人", 0)
}

/**
 * 时间筛选枚举
 */
enum class TimeFilter(val displayName: String) {
    THIS_WEEK("本周"),
    THIS_MONTH("本月"),
    CUSTOM("自定义")
}

/**
 * 高级筛选枚举
 */
enum class AdvancedFilter(val displayName: String) {
    CONTAIN_IMAGES("包含图片的记录"),
    DEEP_REFLECTION("超过500字的深度反思"),
    HAS_ACTION_ITEMS("标记后续行动的条目")
}

/**
 * 感想页面状态
 */
data class ReflectionScreenState(
    val reflections: List<Reflection> = emptyList(),
    val searchQuery: String = "",
    val isFilterPanelVisible: Boolean = false,
    val timeFilter: TimeFilter = TimeFilter.THIS_WEEK,
    val selectedMoods: Set<MoodType> = emptySet(),
    val selectedTypes: Set<ReflectionType> = emptySet(),
    val advancedFilters: Set<AdvancedFilter> = emptySet(),
    val recentSearches: List<String> = emptyList(),
    val popularTags: List<String> = emptyList(),
    val searchSuggestions: List<String> = emptyList(),
    val extendedSuggestions: List<String> = emptyList(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * 感想仓库接口
 */
interface ReflectionRepository {
    suspend fun getRecentReflections(): List<Reflection>
    suspend fun getRecentSearches(): List<String>
    suspend fun getPopularTags(): List<String>
    suspend fun searchReflections(
        query: String,
        timeFilter: TimeFilter,
        moodFilters: Set<MoodType>,
        typeFilters: Set<ReflectionType>,
        advancedFilters: Set<AdvancedFilter>,
        startDate: Instant? = null,
        endDate: Instant? = null
    ): List<Reflection>
    suspend fun saveSearchQuery(query: String)
    suspend fun getReflectionById(reflectionId: String): Reflection
    suspend fun saveReflection(reflection: Reflection): String // 返回感想ID
    suspend fun deleteReflection(reflectionId: String) // 删除感想
    suspend fun initializeSampleDataIfNeeded() // 手动初始化示例数据
}

/**
 * 搜索建议服务接口
 */
interface SearchSuggestionService {
    suspend fun getContextualSuggestions(query: String): List<String>
    suspend fun getExtendedSuggestions(query: String): List<String>
} 