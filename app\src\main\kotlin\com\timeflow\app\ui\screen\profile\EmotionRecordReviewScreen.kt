package com.timeflow.app.ui.screen.profile

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.math.abs
// 导入TimeRange枚举（在EmotionStatisticsScreen.kt中定义）

/**
 * 情绪记录回顾页面
 * 参考知名应用设计，提供时间线视图和多种观察模式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmotionRecordReviewScreen(
    navController: NavController,
    profileViewModel: ProfileViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }

    // 观察ViewModel状态
    val emotionRecords by profileViewModel.emotionRecords.collectAsState()
    val isLoading by profileViewModel.isLoading.collectAsState()
    val error by profileViewModel.error.collectAsState()

    // 🔧 添加调试日志
    LaunchedEffect(isLoading, emotionRecords.size, error) {
        android.util.Log.d("EmotionRecordReviewScreen", "状态更新: isLoading=$isLoading, records=${emotionRecords.size}, error=$error")
    }

    // 视图模式状态
    var viewMode by remember { mutableStateOf(ViewMode.TIMELINE) }
    var timeRange by remember { mutableStateOf(TimeRange.WEEK) }

    // 筛选状态
    var selectedFilter by remember { mutableStateOf(EmotionFilter.ALL) }
    var showDetailedOnly by remember { mutableStateOf(false) }
    
    // 设置状态栏
    SideEffect {
        activity?.let {
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }

    // 过滤和处理记录
    val filteredRecords = remember(emotionRecords, selectedFilter, showDetailedOnly, timeRange) {
        val filtered = emotionRecords.filter { record ->
            val emotionMatch = when (selectedFilter) {
                EmotionFilter.ALL -> true
                EmotionFilter.JOY -> record.emotion == EmotionType.JOY
                EmotionFilter.CALM -> record.emotion == EmotionType.CALM
                EmotionFilter.SAD -> record.emotion == EmotionType.SAD
                EmotionFilter.ANGRY -> record.emotion == EmotionType.ANGRY
                EmotionFilter.ANXIOUS -> record.emotion == EmotionType.ANXIOUS
            }
            val detailMatch = if (showDetailedOnly) record.isDetailed else true
            val timeMatch = when (timeRange) {
                TimeRange.WEEK -> ChronoUnit.DAYS.between(record.date, LocalDate.now()) <= timeRange.days
                TimeRange.MONTH -> ChronoUnit.DAYS.between(record.date, LocalDate.now()) <= timeRange.days
                TimeRange.QUARTER -> ChronoUnit.DAYS.between(record.date, LocalDate.now()) <= timeRange.days
                TimeRange.YEAR -> ChronoUnit.DAYS.between(record.date, LocalDate.now()) <= timeRange.days
                TimeRange.ALL -> true
            }
            emotionMatch && detailMatch && timeMatch
        }
        filtered.sortedByDescending { it.date }
    }

    // 时间线数据处理
    val timelineData = remember(filteredRecords, viewMode) {
        when (viewMode) {
            ViewMode.TIMELINE -> groupRecordsByTimeline(filteredRecords)
            ViewMode.CALENDAR -> groupRecordsByDate(filteredRecords)
            ViewMode.CHART -> processChartData(filteredRecords, timeRange)
        }
    }
    
    // 🔧 修复状态栏重叠问题 - 使用Column布局而不是Scaffold
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 🔧 添加状态栏高度内边距
    ) {
        // 🔧 自定义顶部栏
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = MaterialTheme.colorScheme.surface,
            shadowElevation = 4.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 4.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 返回按钮
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }

                // 标题
                Text(
                    text = "情绪时光",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )

                // 视图模式切换
                IconButton(
                    onClick = {
                        viewMode = when (viewMode) {
                            ViewMode.TIMELINE -> ViewMode.CALENDAR
                            ViewMode.CALENDAR -> ViewMode.CHART
                            ViewMode.CHART -> ViewMode.TIMELINE
                        }
                    }
                ) {
                    Icon(
                        when (viewMode) {
                            ViewMode.TIMELINE -> Icons.Default.Timeline
                            ViewMode.CALENDAR -> Icons.Default.CalendarMonth
                            ViewMode.CHART -> Icons.Default.BarChart
                        },
                        contentDescription = "切换视图"
                    )
                }

                // 时间范围选择
                IconButton(
                    onClick = {
                        timeRange = when (timeRange) {
                            TimeRange.WEEK -> TimeRange.MONTH
                            TimeRange.MONTH -> TimeRange.QUARTER
                            TimeRange.QUARTER -> TimeRange.YEAR
                            TimeRange.YEAR -> TimeRange.ALL
                            TimeRange.ALL -> TimeRange.WEEK
                        }
                    }
                ) {
                    Icon(Icons.Default.DateRange, contentDescription = "时间范围")
                }
            }
        }

        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            // 时间范围指示器
            TimeRangeIndicator(
                timeRange = timeRange,
                recordCount = filteredRecords.size,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 快速筛选栏
            QuickFilterBar(
                selectedFilter = selectedFilter,
                onFilterSelected = { selectedFilter = it },
                showDetailedOnly = showDetailedOnly,
                onShowDetailedOnlyChanged = { showDetailedOnly = it },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
            )
            
            // 主要内容区域 - 根据视图模式显示不同内容
            when {
                isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                error != null -> {
                    ErrorStateView(
                        error = error!!,
                        onRetry = { profileViewModel.clearError() }
                    )
                }
                filteredRecords.isEmpty() -> {
                    EmptyStateView(
                        hasRecords = emotionRecords.isNotEmpty(),
                        timeRange = timeRange
                    )
                }
                else -> {
                    when (viewMode) {
                        ViewMode.TIMELINE -> {
                            TimelineView(
                                records = filteredRecords,
                                timelineData = timelineData as List<TimelineGroup>,
                                onRecordClick = { record ->
                                    // 🔧 只有详细记录才能点击查看详情
                                    if (record.isDetailed) {
                                        navController.currentBackStackEntry?.savedStateHandle?.set("selected_emotion_record", record)
                                        navController.navigate(AppDestinations.EMOTION_RECORD_DETAIL_ROUTE)
                                    }
                                }
                            )
                        }
                        ViewMode.CALENDAR -> {
                            CalendarView(
                                records = filteredRecords,
                                calendarData = timelineData as Map<LocalDate, List<EmotionRecord>>,
                                onDateClick = { date, records ->
                                    // TODO: 显示当日详情
                                }
                            )
                        }
                        ViewMode.CHART -> {
                            ChartView(
                                chartData = timelineData as EmotionChartData,
                                timeRange = timeRange,
                                onDataPointClick = { date ->
                                    // TODO: 显示详情
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 视图模式枚举
 */
enum class ViewMode(val displayName: String, val description: String) {
    TIMELINE("时间线", "按时间顺序查看情绪变化"),
    CALENDAR("日历", "按日期网格查看情绪分布"),
    CHART("图表", "通过图表分析情绪趋势")
}

// TimeRange枚举已在EmotionStatisticsScreen.kt中定义，这里不重复定义

/**
 * 情绪筛选枚举
 */
enum class EmotionFilter(val displayName: String) {
    ALL("全部"),
    JOY("开心"),
    CALM("平静"),
    SAD("伤心"),
    ANGRY("生气"),
    ANXIOUS("焦虑")
}

/**
 * 时间线分组数据类
 */
data class TimelineGroup(
    val date: LocalDate,
    val records: List<EmotionRecord>,
    val isToday: Boolean = false,
    val daysSinceToday: Long = 0
)

/**
 * 图表数据类
 */
data class EmotionChartData(
    val emotionCounts: Map<EmotionType, Int>,
    val dailyTrends: List<DailyTrend>,
    val weeklyAverages: Map<String, Float>,
    val insights: List<String>
)

/**
 * 每日趋势数据类
 */
data class DailyTrend(
    val date: LocalDate,
    val dominantEmotion: EmotionType?,
    val emotionScore: Float, // -2 到 +2，负数表示消极，正数表示积极
    val recordCount: Int
)

/**
 * 数据处理函数
 */
private fun groupRecordsByTimeline(records: List<EmotionRecord>): List<TimelineGroup> {
    val today = LocalDate.now()
    return records
        .groupBy { it.date }
        .map { (date, recordsForDate) ->
            TimelineGroup(
                date = date,
                records = recordsForDate.sortedBy { it.emotion.ordinal },
                isToday = date == today,
                daysSinceToday = ChronoUnit.DAYS.between(date, today)
            )
        }
        .sortedByDescending { it.date }
}

private fun groupRecordsByDate(records: List<EmotionRecord>): Map<LocalDate, List<EmotionRecord>> {
    return records.groupBy { it.date }
}

private fun processChartData(records: List<EmotionRecord>, timeRange: TimeRange): EmotionChartData {
    val emotionCounts = records.groupBy { it.emotion }.mapValues { it.value.size }

    val dailyTrends = records
        .groupBy { it.date }
        .map { (date, recordsForDate) ->
            val dominantEmotion = recordsForDate
                .groupBy { it.emotion }
                .maxByOrNull { it.value.size }?.key

            val emotionScore = calculateEmotionScore(recordsForDate)

            DailyTrend(
                date = date,
                dominantEmotion = dominantEmotion,
                emotionScore = emotionScore,
                recordCount = recordsForDate.size
            )
        }
        .sortedBy { it.date }

    val weeklyAverages = calculateWeeklyAverages(dailyTrends)
    val insights = generateInsights(records, dailyTrends)

    return EmotionChartData(
        emotionCounts = emotionCounts,
        dailyTrends = dailyTrends,
        weeklyAverages = weeklyAverages,
        insights = insights
    )
}

private fun calculateEmotionScore(records: List<EmotionRecord>): Float {
    if (records.isEmpty()) return 0f

    val scores = records.map { record ->
        when (record.emotion) {
            EmotionType.JOY -> 2f
            EmotionType.CALM -> 1f
            EmotionType.SAD -> -1f
            EmotionType.ANGRY -> -2f
            EmotionType.ANXIOUS -> -1.5f
        }
    }

    return scores.average().toFloat()
}

private fun calculateWeeklyAverages(dailyTrends: List<DailyTrend>): Map<String, Float> {
    // 简化实现，返回基本统计
    return mapOf(
        "平均情绪分数" to dailyTrends.map { it.emotionScore }.average().toFloat(),
        "记录频率" to dailyTrends.size.toFloat() / 7f
    )
}

private fun generateInsights(records: List<EmotionRecord>, dailyTrends: List<DailyTrend>): List<String> {
    val insights = mutableListOf<String>()

    // 最常见的情绪
    val mostCommonEmotion = records.groupBy { it.emotion }.maxByOrNull { it.value.size }?.key
    mostCommonEmotion?.let {
        insights.add("最常记录的情绪是${it.displayName}")
    }

    // 情绪趋势
    val recentTrend = dailyTrends.takeLast(7).map { it.emotionScore }.average()
    if (recentTrend > 0.5) {
        insights.add("最近一周情绪状态较为积极")
    } else if (recentTrend < -0.5) {
        insights.add("最近一周情绪状态需要关注")
    }

    // 记录频率
    if (records.size >= 20) {
        insights.add("记录习惯很好，坚持记录有助于情绪管理")
    }

    return insights
}

/**
 * 时间范围指示器
 */
@Composable
private fun TimeRangeIndicator(
    timeRange: TimeRange,
    recordCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = timeRange.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "共 $recordCount 条记录",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Icon(
                Icons.Default.Timeline,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

/**
 * 快速筛选栏
 */
@Composable
private fun QuickFilterBar(
    selectedFilter: EmotionFilter,
    onFilterSelected: (EmotionFilter) -> Unit,
    showDetailedOnly: Boolean,
    onShowDetailedOnlyChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 情绪筛选
        LazyRow(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            items(EmotionFilter.values()) { filter ->
                FilterChip(
                    selected = selectedFilter == filter,
                    onClick = { onFilterSelected(filter) },
                    label = {
                        Text(
                            filter.displayName,
                            fontSize = 12.sp
                        )
                    },
                    modifier = Modifier.height(32.dp)
                )
            }
        }

        // 详细记录开关
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = showDetailedOnly,
                onCheckedChange = onShowDetailedOnlyChanged,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "详细",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 筛选选项区域（保留原有的，用于兼容）
 */
@Composable
private fun FilterSection(
    selectedFilter: EmotionFilter,
    onFilterSelected: (EmotionFilter) -> Unit,
    showDetailedOnly: Boolean,
    onShowDetailedOnlyChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "筛选选项",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 情绪类型筛选
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                EmotionFilter.values().forEach { filter ->
                    FilterChip(
                        selected = selectedFilter == filter,
                        onClick = { onFilterSelected(filter) },
                        label = { Text(filter.displayName) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 详细记录筛选
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = showDetailedOnly,
                    onCheckedChange = onShowDetailedOnlyChanged
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "仅显示详细记录",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 时间线视图
 */
@Composable
private fun TimelineView(
    records: List<EmotionRecord>,
    timelineData: List<TimelineGroup>,
    onRecordClick: (EmotionRecord) -> Unit,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()

    LazyColumn(
        state = listState,
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(timelineData.size) { index ->
            val group = timelineData[index]
            TimelineGroupItem(
                group = group,
                onRecordClick = onRecordClick,
                isLast = index == timelineData.size - 1
            )
        }

        // 底部间距
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 时间线分组项目
 */
@Composable
private fun TimelineGroupItem(
    group: TimelineGroup,
    onRecordClick: (EmotionRecord) -> Unit,
    isLast: Boolean = false,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth()
    ) {
        // 时间线左侧 - 日期和时间轴
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(80.dp)
        ) {
            // 日期显示
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = if (group.isToday)
                        MaterialTheme.colorScheme.primary
                    else
                        MaterialTheme.colorScheme.surfaceVariant
                ),
                modifier = Modifier.size(60.dp)
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = group.date.dayOfMonth.toString(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (group.isToday)
                            MaterialTheme.colorScheme.onPrimary
                        else
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = group.date.month.getDisplayName(TextStyle.SHORT, Locale.CHINESE),
                        style = MaterialTheme.typography.bodySmall,
                        color = if (group.isToday)
                            MaterialTheme.colorScheme.onPrimary
                        else
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 时间轴线条
            if (!isLast) {
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(40.dp)
                        .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                )
            }
        }

        Spacer(modifier = Modifier.width(16.dp))

        // 时间线右侧 - 情绪记录
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 相对时间显示
            Text(
                text = when {
                    group.isToday -> "今天"
                    group.daysSinceToday == 1L -> "昨天"
                    group.daysSinceToday <= 7 -> "${group.daysSinceToday}天前"
                    else -> group.date.format(DateTimeFormatter.ofPattern("MM月dd日"))
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 情绪记录卡片
            group.records.forEach { record ->
                TimelineRecordCard(
                    record = record,
                    onClick = { onRecordClick(record) },
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }
        }
    }
}

/**
 * 时间线记录卡片
 */
@Composable
private fun TimelineRecordCard(
    record: EmotionRecord,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = record.isDetailed) { onClick() }, // 🔧 只有详细记录才能点击
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (record.isDetailed) 3.dp else 2.dp // 🔧 详细记录有更高的阴影
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 情绪图标
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(record.emotion.color.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = record.emotion.emoji,
                    fontSize = 18.sp
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 记录内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = record.emotion.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )

                if (record.isDetailed && record.mindfulnessNote.isNotEmpty()) {
                    Text(
                        text = record.mindfulnessNote,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }

                if (record.isDetailed && record.triggers.isNotEmpty()) {
                    Text(
                        text = "触发: ${record.triggers.take(2).joinToString(", ")}${if (record.triggers.size > 2) "..." else ""}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            }

            // 详细标记和箭头
            if (record.isDetailed) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Surface(
                        shape = RoundedCornerShape(8.dp),
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = "详细",
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(4.dp))

                    // 🔧 添加箭头图标表示可点击
                    Icon(
                        Icons.Default.ChevronRight,
                        contentDescription = "查看详情",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 统计信息区域
 */
@Composable
private fun StatisticsSection(
    totalRecords: Int,
    filteredRecords: Int,
    detailedRecords: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticItem(
                label = "总记录",
                value = totalRecords.toString(),
                icon = Icons.Default.Timeline
            )
            StatisticItem(
                label = "当前显示",
                value = filteredRecords.toString(),
                icon = Icons.Default.Visibility
            )
            StatisticItem(
                label = "详细记录",
                value = detailedRecords.toString(),
                icon = Icons.Default.Description
            )
        }
    }
}

/**
 * 统计项目
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 情绪记录项目
 */
@Composable
private fun EmotionRecordItem(
    record: EmotionRecord,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 日期和情绪
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 情绪图标
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(record.emotion.color.copy(alpha = 0.2f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = record.emotion.emoji,
                            fontSize = 20.sp
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Column {
                        Text(
                            text = record.date.format(DateTimeFormatter.ofPattern("MM月dd日")),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "${record.date.dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.CHINESE)} · ${record.emotion.displayName}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // 详细标记
                if (record.isDetailed) {
                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = "详细",
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }

            // 详细信息（仅详细记录显示）
            if (record.isDetailed && (record.triggers.isNotEmpty() || record.mindfulnessNote.isNotEmpty())) {
                Spacer(modifier = Modifier.height(12.dp))

                // 触发因素
                if (record.triggers.isNotEmpty()) {
                    Text(
                        text = "触发因素：${record.triggers.joinToString(", ")}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 正念笔记
                if (record.mindfulnessNote.isNotEmpty()) {
                    if (record.triggers.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                    Text(
                        text = "笔记：${record.mindfulnessNote}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * 错误状态视图
 */
@Composable
private fun ErrorStateView(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Error,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRetry
            ) {
                Text("重试")
            }
        }
    }
}

/**
 * 空状态视图
 */
@Composable
private fun EmptyStateView(
    hasRecords: Boolean,
    timeRange: TimeRange,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                if (hasRecords) Icons.Default.FilterList else Icons.Default.SentimentNeutral,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.outline,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = if (hasRecords) {
                    "在${timeRange.displayName}内没有找到符合条件的记录"
                } else {
                    "还没有情绪记录\n开始记录你的情绪变化吧"
                },
                color = MaterialTheme.colorScheme.outline,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )

            if (!hasRecords) {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "💡 定期记录情绪有助于了解自己的情绪模式",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 * 日历视图（占位符）
 */
@Composable
private fun CalendarView(
    records: List<EmotionRecord>,
    calendarData: Map<LocalDate, List<EmotionRecord>>,
    onDateClick: (LocalDate, List<EmotionRecord>) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现日历视图
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "日历视图\n即将推出",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 图表视图（占位符）
 */
@Composable
private fun ChartView(
    chartData: EmotionChartData,
    timeRange: TimeRange,
    onDataPointClick: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现图表视图
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "图表分析",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "即将推出",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(24.dp))

            // 显示一些基本统计
            if (chartData.insights.isNotEmpty()) {
                Card(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "数据洞察",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        chartData.insights.forEach { insight ->
                            Text(
                                text = "• $insight",
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}
