package com.timeflow.app.data.repository

import com.timeflow.app.data.entity.KanbanColumn
import kotlinx.coroutines.flow.Flow

/**
 * 看板列仓库接口
 */
interface KanbanColumnRepository {
    
    /**
     * 根据看板ID获取所有列
     */
    suspend fun getColumnsByBoardId(boardId: String): List<KanbanColumn>
    
    /**
     * 观察指定看板ID的所有列变化
     */
    fun observeColumnsByBoardId(boardId: String): Flow<List<KanbanColumn>>
    
    /**
     * 根据ID获取列
     */
    suspend fun getColumnById(columnId: String): KanbanColumn?
    
    /**
     * 观察指定ID的列变化
     */
    fun observeColumnById(columnId: String): Flow<KanbanColumn?>
    
    /**
     * 插入列
     */
    suspend fun insertColumn(column: KanbanColumn)
    
    /**
     * 插入多个列
     */
    suspend fun insertColumns(columns: List<KanbanColumn>)
    
    /**
     * 更新列
     */
    suspend fun updateColumn(column: KanbanColumn)
    
    /**
     * 删除列
     */
    suspend fun deleteColumn(column: KanbanColumn)
    
    /**
     * 获取指定看板的最大位置值
     */
    suspend fun getMaxPositionForBoard(boardId: String): Int
} 