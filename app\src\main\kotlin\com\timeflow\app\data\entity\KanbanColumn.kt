package com.timeflow.app.data.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * 看板列实体类
 */
@Entity(
    tableName = "kanban_columns",
    foreignKeys = [
        ForeignKey(
            entity = KanbanBoard::class,
            parentColumns = ["id"],
            childColumns = ["board_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("board_id")]
)
data class KanbanColumn(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "board_id")
    val boardId: String,
    
    val title: String,
    
    val description: String? = null,
    
    val position: Int,
    
    val color: String? = null,
    
    @ColumnInfo(name = "created_at")
    val createdAt: LocalDateTime,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: LocalDateTime
) 