package com.timeflow.app.ui.utils

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/**
 * 日期格式化工具类 - 支持iOS和TickTick风格的日期显示
 */
object DateFormatUtils {
    private val formatter = DateTimeFormatter.ofPattern("MM月dd日")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
    
    // 常规日期格式化器
    private val fullDateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
}

/**
 * 将日期格式化为紧凑格式显示
 * 例如：今天 10:30、明天 14:00、后天、12月25日等
 * 
 * @param dateTime 需要格式化的日期时间
 * @return 格式化后的日期字符串
 */
fun formatCompactDate(dateTime: LocalDateTime?): String {
    if (dateTime == null) return ""
    
    val now = LocalDateTime.now()
    val today = LocalDate.now()
    val inputDate = dateTime.toLocalDate()
    val time = dateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
    
    return when {
        // 今天
        inputDate.isEqual(today) -> {
            "今天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time"
        }
        // 明天
        inputDate.isEqual(today.plusDays(1)) -> {
            "明天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time"
        }
        // 后天
        inputDate.isEqual(today.plusDays(2)) -> {
            "后天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time"
        }
        // 本周内 (显示星期几)
        ChronoUnit.DAYS.between(today, inputDate) < 7 -> {
            val dayOfWeek = when (inputDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            "$dayOfWeek，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time"
        }
        // 本年内 (显示月日)
        inputDate.year == today.year -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time"
        }
        // 其他年份 (显示年月日)
        else -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time"
        }
    }
}

/**
 * 格式化为详细日期显示
 */
fun formatDetailDate(dateTime: LocalDateTime?): String {
    if (dateTime == null) return ""
    return dateTime.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))
} 