package com.timeflow.app.ui.task.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.UnfoldMore
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.model.TaskGroup
import com.timeflow.app.ui.task.components.common.EnhancedTaskCard
import com.timeflow.app.data.converter.TaskConverter

/**
 * 分组任务列表组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TaskGroupList(
    taskGroups: List<TaskGroup>,
    onTaskClick: (String) -> Unit,
    onTaskToggleComplete: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()
    val expandedGroups = remember { mutableStateOf(taskGroups.map { it.title }.toSet()) }
    val hapticFeedback = LocalHapticFeedback.current
    
    LazyColumn(
        state = listState,
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        contentPadding = PaddingValues(bottom = 80.dp)
    ) {
        taskGroups.forEach { group ->
            val isExpanded = expandedGroups.value.contains(group.title)
            
            // 分组标题
            item(key = "header_${group.title}") {
                TaskGroupHeader(
                    title = group.title,
                    count = group.tasks.size,
                    isExpanded = isExpanded,
                    onClick = { 
                        expandedGroups.value = if (isExpanded) {
                            expandedGroups.value - group.title
                        } else {
                            expandedGroups.value + group.title
                        }
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 如果分组已展开，显示任务列表
            if (isExpanded) {
                items(
                    items = group.tasks,
                    key = { task -> task.id }
                ) { task ->
                    val entityTask = TaskConverter.toEntityTask(task)
                    EnhancedTaskCard(
                        task = entityTask,
                        onCheckChange = { taskId, isChecked -> onTaskToggleComplete(taskId) },
                        onClick = { onTaskClick(task.id) },
                        showCheckbox = true
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

/**
 * 任务分组标题组件
 */
@Composable
fun TaskGroupHeader(
    title: String,
    count: Int,
    isExpanded: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(MaterialTheme.shapes.small)
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.KeyboardArrowDown,
            contentDescription = if (isExpanded) "Collapse" else "Expand",
            modifier = Modifier
                .rotate(if (isExpanded) 0f else -90f)
                .size(24.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
} 