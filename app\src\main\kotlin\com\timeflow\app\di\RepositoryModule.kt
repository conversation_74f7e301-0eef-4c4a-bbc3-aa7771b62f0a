package com.timeflow.app.di

import android.content.Context
import com.timeflow.app.data.repository.*
import com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache
import com.timeflow.app.data.dao.KanbanBoardDao
import com.timeflow.app.data.dao.KanbanColumnDao
import com.timeflow.app.data.dao.CycleDao
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 仓库模块，提供所有Repository的依赖注入
 * Contains only @Provides methods.
 */
@Module
@InstallIn(SingletonComponent::class)
// Keep as non-abstract class or change to object
object RepositoryModule {

    // @Provides methods remain here
    @Provides
    @Singleton
    fun provideTaskRepository(taskRepositoryImpl: TaskRepositoryImpl): TaskRepository {
        // Return the cached version
        return TaskRepositoryCache(taskRepositoryImpl)
    }

    @Provides
    @Singleton
    fun provideKanbanRepository(kanbanBoardDao: KanbanBoardDao, kanbanColumnDao: KanbanColumnDao): KanbanRepository {
        return KanbanRepository(kanbanBoardDao, kanbanColumnDao)
    }
    
    @Provides
    @Singleton
    fun provideCycleRepository(cycleDao: CycleDao): CycleRepository {
        return CycleRepository(cycleDao)
    }

    @Provides
    @Singleton
    fun provideSharedFilterState(): SharedFilterState {
        return SharedFilterState()
    }
    
    @Provides
    @Singleton
    fun provideSharedPendingDeletionState(
        @ApplicationContext context: Context,
        taskRepository: TaskRepository
    ): SharedPendingDeletionState {
        return SharedPendingDeletionState(context, taskRepository)
    }
    
    /**
     * 提供应用上下文给需要使用Context的仓库
     */
    @Provides
    @Singleton
    fun provideContext(@ApplicationContext appContext: Context): Context {
        return appContext
    }
}

/**
 * Separate abstract module for @Binds
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryBindingsModule {

    // Move all @Binds methods here
    @Binds
    @Singleton
    abstract fun bindKanbanBoardRepository(
        kanbanBoardRepositoryImpl: KanbanBoardRepositoryImpl
    ): KanbanBoardRepository
    
    @Binds
    @Singleton
    abstract fun bindKanbanColumnRepository(
        kanbanColumnRepositoryImpl: KanbanColumnRepositoryImpl
    ): KanbanColumnRepository

    @Binds
    @Singleton
    abstract fun bindTimeAnalyticsRepository(
        timeAnalyticsRepositoryImpl: TimeAnalyticsRepositoryImpl
    ): TimeAnalyticsRepository

    @Binds
    @Singleton
    abstract fun bindEventRepository(
        eventRepositoryImpl: EventRepositoryImpl
    ): EventRepository

    // 绑定AI任务仓库
    @Binds
    @Singleton
    abstract fun bindAiTaskRepository(
        aiTaskRepositoryImpl: AiTaskRepositoryImpl
    ): AiTaskRepository

    @Binds
    @Singleton
    abstract fun bindHabitRepository(
        habitRepositoryImpl: HabitRepositoryImpl
    ): HabitRepository
    
    // 绑定目标模板仓库
    @Binds
    @Singleton
    abstract fun bindGoalTemplateRepository(
        goalTemplateRepositoryImpl: GoalTemplateRepositoryImpl
    ): GoalTemplateRepository
    
    // 绑定用户偏好仓库
    @Binds
    @Singleton
    abstract fun bindUserPreferenceRepository(
        userPreferenceRepositoryImpl: UserPreferenceRepositoryImpl
    ): UserPreferenceRepository
} 