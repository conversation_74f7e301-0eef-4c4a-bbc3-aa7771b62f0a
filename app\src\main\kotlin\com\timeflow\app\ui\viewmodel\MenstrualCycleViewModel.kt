package com.timeflow.app.ui.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.entity.SymptomRecord
import com.timeflow.app.data.repository.CycleRepository
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm
// CycleDay and CycleType are now defined in this file to avoid circular imports
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import kotlinx.coroutines.delay
import kotlin.math.pow
import kotlin.math.sqrt
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity

/**
 * 生理周期ViewModel - 基于改进的流程图实现
 * 
 * 实现的主要功能：
 * 1. 初始化阶段：首次使用检测、健康数据权限请求、数据初始化
 * 2. 数据加载流程：计算平均周期、分析规律性、生成预测模型
 * 3. 视图渲染流程：绘制基础网格、叠加历史记录、覆盖预测周期、添加交互热区
 * 4. 用户交互流程：单击显示详情、长按触发编辑、验证有效性、保存新记录
 * 5. 数据同步流程：本地数据库写入、健康数据同步、多设备同步、更新视图
 * 6. 冲突解决流程：检测冲突、提供解决方案、执行修正、刷新视图
 */

/**
 * UI状态数据类
 */
data class MenstrualCycleUiState(
    val cycleDays: List<CycleDay> = emptyList(),
    val averageCycleLength: Int = 28,
    val averagePeriodLength: Int = 5,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * 日期数据类
 */
data class CycleDay(
    val date: LocalDate,
    val type: CycleType,
    val isToday: Boolean = false,
    val isSelected: Boolean = false,
    val dayOfCycle: Int = 0  // 当前周期的第几天
)

/**
 * 周期类型枚举
 */
enum class CycleType {
    PERIOD,             // 月经期
    FOLLICULAR,         // 卵泡期
    OVULATION,          // 排卵期
    LUTEAL,             // 黄体期
    FERTILE,            // 易孕期
    PREDICTED,          // 预测月经期
    PREDICTED_PERIOD,   // 预测月经期
    PREDICTED_FOLLICULAR, // 预测卵泡期
    PREDICTED_OVULATION,  // 预测排卵期
    PREDICTED_LUTEAL,     // 预测黄体期
    PREDICTED_FERTILE,    // 预测易孕期
    NONE                // 未分类
}

/**
 * 元组类 - 用于返回多个预测数据
 */
data class Tuple7<T1, T2, T3, T4, T5, T6, T7>(
    val first: T1,
    val second: T2,
    val third: T3,
    val fourth: T4,
    val fifth: T5,
    val sixth: T6,
    val seventh: T7
)

@HiltViewModel
class MenstrualCycleViewModel @Inject constructor(
    private val repository: CycleRepository
) : ViewModel() {
    
    // 从仓库获取周期记录
    val cycles: StateFlow<List<CycleRecord>> = repository.getCycles()
        .stateIn(viewModelScope, SharingStarted.Lazily, emptyList())
    
    // 从仓库获取症状记录
    val allSymptoms: StateFlow<List<SymptomRecord>> = repository.getAllSymptoms()
        .stateIn(viewModelScope, SharingStarted.Lazily, emptyList())
    
    // 视图状态管理
    private val _selectedDate = MutableStateFlow(LocalDate.now())
    val selectedDate = _selectedDate.asStateFlow()

    private val _selectedMonth = MutableStateFlow(YearMonth.now())
    val selectedMonth = _selectedMonth.asStateFlow()
    
    // 预测算法
    private val predictionAlgorithm = PeriodPredictionAlgorithm()
    
    // 健康数据权限状态
    private val _healthDataPermissionGranted = MutableStateFlow(false)
    val healthDataPermissionGranted = _healthDataPermissionGranted.asStateFlow()
    
    // 多设备同步状态
    private val _isSyncing = MutableStateFlow(false)
    val isSyncing = _isSyncing.asStateFlow()
    
    // 是否存在数据冲突
    private val _hasDataConflict = MutableStateFlow(false)
    val hasDataConflict = _hasDataConflict.asStateFlow()
    
    // 当前选中日期的症状记录
    val selectedDateSymptoms: StateFlow<List<SymptomRecord>> = selectedDate
        .flatMapLatest { date -> repository.getSymptomsByDate(date) }
        .stateIn(viewModelScope, SharingStarted.Lazily, emptyList())
    
    // 专业预测结果
    val predictionResult: StateFlow<PeriodPredictionAlgorithm.PredictionResult?> = 
        combine(cycles, allSymptoms) { cycleList, symptoms ->
            if (cycleList.isNotEmpty()) {
                predictionAlgorithm.predictNextPeriod(
                    cycles = cycleList,
                    symptoms = symptoms,
                    userAge = null // 可以从用户设置中获取
                )
            } else null
        }.stateIn(viewModelScope, SharingStarted.Lazily, null)
    
    // 周期规律性分析
    val cycleRegularity: StateFlow<PeriodPredictionAlgorithm.CycleRegularity> = 
        predictionResult.map { it?.cycleRegularity ?: PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA }
            .stateIn(viewModelScope, SharingStarted.Lazily, PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA)
    
    // 预测置信度
    val predictionConfidence: StateFlow<Double> = 
        predictionResult.map { it?.confidenceLevel ?: 0.0 }
            .stateIn(viewModelScope, SharingStarted.Lazily, 0.0)
    
    // 个性化健康建议
    val healthRecommendations: StateFlow<List<String>> = 
        predictionResult.map { it?.recommendations ?: emptyList() }
            .stateIn(viewModelScope, SharingStarted.Lazily, emptyList())
    
    // 计算的周期数据
    val cycleData: StateFlow<List<CycleDay>> = 
        combine(cycles, selectedDate, selectedMonth, predictionResult) { cycleList, date, month, prediction ->
            generateCycleDays(cycleList, date, month, prediction).cycleDays
        }.stateIn(viewModelScope, SharingStarted.Lazily, emptyList())
    
    // 当前选中的日期数据
    val selectedDayData: StateFlow<CycleDay> = 
        combine(cycleData, selectedDate) { data, date ->
            data.find { it.date == date } ?: 
            data.firstOrNull { it.isToday } ?: 
            CycleDay(date = date, type = CycleType.NONE, isToday = date == LocalDate.now(), isSelected = true)
        }.stateIn(viewModelScope, SharingStarted.Lazily, 
            CycleDay(date = LocalDate.now(), type = CycleType.NONE, isToday = true, isSelected = true)
        )
    
    // 当前周期状态
    val currentCycle: StateFlow<CycleRecord?> = 
        combine(cycles, selectedDate) { cycleList, date ->
            cycleList.find { cycle -> 
                date >= cycle.startDate && (cycle.endDate == null || date <= cycle.endDate) 
            }
        }.stateIn(viewModelScope, SharingStarted.Lazily, null)
    
    // 当前所有日期对应的周期阶段（月经期、排卵期、黄体期）
    val cyclePhases: StateFlow<Map<LocalDate, String>> = 
        cycleData.map { cycleDays ->
            cycleDays.associate { cycleDay ->
                // 将CycleType转换为中文周期阶段名称
                val phaseNameChinese = when (cycleDay.type) {
                    CycleType.PERIOD, CycleType.PREDICTED_PERIOD -> "月经期"
                    CycleType.OVULATION, CycleType.PREDICTED_OVULATION -> "排卵期"
                    CycleType.FOLLICULAR, CycleType.PREDICTED_FOLLICULAR -> "卵泡期"
                    CycleType.LUTEAL, CycleType.PREDICTED_LUTEAL -> "黄体期"
                    CycleType.FERTILE -> "易孕期"
                    else -> "非经期"
                }
                cycleDay.date to phaseNameChinese
            }
        }.stateIn(viewModelScope, SharingStarted.Lazily, emptyMap())
    
    // 合并所有状态数据
    val uiState = combine(cycles, selectedDate, selectedMonth, predictionResult) { cycleList, date, month, prediction ->
        val cycleDays = generateCycleDays(cycleList, date, month, prediction)
        MenstrualCycleUiState(
            cycleDays = cycleDays.cycleDays,
            averageCycleLength = cycleDays.averageCycleLength,
            averagePeriodLength = cycleDays.averagePeriodLength
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = MenstrualCycleUiState()
    )
    
    init {
        // 初始化流程 - 对应流程图的初始化阶段
        viewModelScope.launch {
            checkFirstTimeUse()
            requestHealthDataPermission()
            initializeDatabaseConnection()
            loadHistoricalData()
            startPredictionAlgorithm()
            
            // 添加测试数据以确保颜色显示效果
            if (repository.getCycles().first().isEmpty()) {
                addSampleCycleData()
            }
        }
    }
    
    /**
     * 生成日历显示数据
     */
    private suspend fun generateCycleDays(
        cycles: List<CycleRecord>,
        selectedDate: LocalDate,
        selectedMonth: YearMonth,
        prediction: PeriodPredictionAlgorithm.PredictionResult?
    ): MenstrualCycleUiState {
        val today = LocalDate.now()
        
        // 获取上次经期开始日期
        val lastPeriod = cycles.maxByOrNull { it.startDate }
        val lastPeriodStart = lastPeriod?.startDate ?: today.minusDays(19) // 如果没有记录，假设上次月经是19天前
        
        // 使用专业预测算法结果，如果没有预测结果则使用简单平均值
        val averageCycleLength = if (prediction != null) {
            // 使用专业算法计算的预测周期长度
            ChronoUnit.DAYS.between(lastPeriodStart, prediction.nextPeriodStart).toInt()
                .coerceIn(21, 35)
        } else if (cycles.size >= 3) {
            cycles
                .filter { it.cycleLength != null }
                .map { it.cycleLength!! }
                .average()
                .toInt()
                .coerceIn(21, 35)
        } else 28 // 默认28天
        
        val averagePeriodLength = if (prediction != null) {
            // 使用专业算法计算的预测经期长度
            ChronoUnit.DAYS.between(prediction.nextPeriodStart, prediction.nextPeriodEnd).toInt() + 1
                .coerceIn(3, 7)
        } else if (cycles.size >= 3) {
            cycles
                .filter { it.periodLength != null }
                .map { it.periodLength!! }
                .average()
                .toInt()
                .coerceIn(3, 7)
        } else 5 // 默认5天
        
        // 计算各阶段日期
        // 1. 月经期 - PERIOD (通常1-5天)
        val periodEnd = lastPeriodStart.plusDays(averagePeriodLength.toLong() - 1)
        
        // 2. 卵泡期 - FOLLICULAR (从月经结束到排卵前)
        // 卵泡期从月经结束后开始，到排卵前一天结束
        val follicularStart = periodEnd.plusDays(1)
        
        // 使用专业预测算法的排卵和易孕期计算，如果没有则使用简单估算
        val predictionData = if (prediction != null) {
            // 使用专业算法的精确预测
            val ovulationDate = prediction.ovulationDate
            val ovulationStart = ovulationDate.minusDays(1)
            val ovulationEnd = ovulationDate.plusDays(1)
            val fertileWindowStart = prediction.fertileWindowStart
            val fertileWindowEnd = prediction.fertileWindowEnd
            val periodStart = prediction.nextPeriodStart
            val periodEnd = prediction.nextPeriodEnd
            
            Tuple7(ovulationDate, ovulationStart, ovulationEnd, fertileWindowStart, fertileWindowEnd, periodStart, periodEnd)
        } else {
            // 简单估算
            val ovulationDate = lastPeriodStart.plusDays((averageCycleLength - 14).toLong())
            val ovulationStart = ovulationDate.minusDays(1)
            val ovulationEnd = ovulationDate.plusDays(1)
            val fertileWindowStart = ovulationDate.minusDays(5)
            val fertileWindowEnd = ovulationDate.plusDays(1)
            val periodStart = lastPeriodStart.plusDays(averageCycleLength.toLong())
            val periodEnd = periodStart.plusDays(averagePeriodLength.toLong() - 1)
            
            Tuple7(ovulationDate, ovulationStart, ovulationEnd, fertileWindowStart, fertileWindowEnd, periodStart, periodEnd)
        }
        
        val ovulationDay = predictionData.first
        val ovulationPeriodStart = predictionData.second  
        val ovulationPeriodEnd = predictionData.third
        val fertileStart = predictionData.fourth
        val fertileEnd = predictionData.fifth
        val nextPeriodStart = predictionData.sixth
        val nextPeriodEnd = predictionData.seventh
        
        // 4. 黄体期 - LUTEAL (从排卵后到下次月经前)
        val lutealStart = ovulationPeriodEnd.plusDays(1)
        val lutealEnd = lastPeriodStart.plusDays(averageCycleLength.toLong() - 1)
        
        // 确保生成完整月份的日期数据 - 扩展前后的天数
        val startDate = selectedMonth.atDay(1)
        val endDate = selectedMonth.atEndOfMonth()
        val firstDay = startDate.with(java.time.temporal.TemporalAdjusters.previousOrSame(java.time.DayOfWeek.SUNDAY))
        val lastDay = endDate.with(java.time.temporal.TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SATURDAY))
        
        val days = mutableListOf<CycleDay>()
        var currentDate = firstDay
        
        // 定义一个函数来确定日期是否在某个范围内，同时处理跨月情况
        fun isWithinCycle(date: LocalDate, start: LocalDate, end: LocalDate): Boolean {
            // 处理跨月或跨年的情况
            if (start.isAfter(end)) {
                // 如果开始日期在结束日期之后，说明周期跨越了月份边界
                return date.isAfter(start) || date.isBefore(end) || date.isEqual(start) || date.isEqual(end)
            }
            // 常规情况
            return (date.isEqual(start) || date.isAfter(start)) && 
                   (date.isEqual(end) || date.isBefore(end))
        }
        
        // 为当前月强制设置一些周期阶段日期，确保视觉效果
        val currentMonth = YearMonth.from(today)
        val isCurrentMonthSelected = selectedMonth == currentMonth
        
        // 确保同一个月经周期内的所有日期使用相同的CycleType
        var previousDate: LocalDate? = null
        var previousType: CycleType? = null
        var sameTypeStartDate: LocalDate? = null // 记录相同类型开始的日期
        
        while (!currentDate.isAfter(lastDay)) {
            // 查找当前日期的实际周期记录
            val actualCycle = cycles.find { cycle ->
                currentDate >= cycle.startDate && (cycle.endDate == null || currentDate <= cycle.endDate)
            }
            
            // 确定日期类型
            val initialType = when {
                // 未来日期只显示预测周期
                currentDate.isAfter(today) -> {
                    // 为未来的各个阶段添加预测标识
                    when {
                        isWithinCycle(currentDate, nextPeriodStart, nextPeriodEnd) -> 
                            CycleType.PREDICTED_PERIOD
                        isWithinCycle(currentDate, nextPeriodEnd.plusDays(1), nextPeriodEnd.plusDays((averageCycleLength - averagePeriodLength).toLong() / 2)) -> 
                            CycleType.PREDICTED_FOLLICULAR
                        isWithinCycle(currentDate, nextPeriodStart.plusDays(averageCycleLength.toLong() - 15), nextPeriodStart.plusDays(averageCycleLength.toLong() - 13)) -> 
                            CycleType.PREDICTED_OVULATION
                        isWithinCycle(currentDate, nextPeriodStart.plusDays(averageCycleLength.toLong() - 12), nextPeriodStart.plusDays(averageCycleLength.toLong() - 1)) -> 
                            CycleType.PREDICTED_LUTEAL
                        else -> CycleType.NONE
                    }
                }
                
                // 实际记录的月经期
                actualCycle != null -> {
                    // 如果有记录，根据记录的类型来判断，而不是一律视为经期
                    val startDate = actualCycle.startDate
                    val endDate = actualCycle.endDate ?: startDate.plusDays(averagePeriodLength.toLong() - 1)
                    if (currentDate.isEqual(startDate) || (currentDate.isAfter(startDate) && currentDate.isBefore(endDate.plusDays(1)))) {
                        CycleType.PERIOD
                    } else {
                        // 如果当前日期不在经期范围内，继续判断其他类型
                        when {
                            isWithinCycle(currentDate, follicularStart, ovulationPeriodStart.minusDays(1)) -> {
                                CycleType.FOLLICULAR
                            }
                            isWithinCycle(currentDate, ovulationPeriodStart, ovulationPeriodEnd) -> CycleType.OVULATION
                            isWithinCycle(currentDate, lutealStart, lutealEnd) -> CycleType.LUTEAL
                            else -> CycleType.NONE
                        }
                    }
                }
                
                // 预测的下次月经期
                isWithinCycle(currentDate, nextPeriodStart, nextPeriodEnd) -> CycleType.PREDICTED
                
                // 各个周期阶段 - 注意处理可能的跨月情况
                isWithinCycle(currentDate, lastPeriodStart, periodEnd) -> CycleType.PERIOD
                isWithinCycle(currentDate, follicularStart, ovulationPeriodStart.minusDays(1)) -> CycleType.FOLLICULAR
                isWithinCycle(currentDate, ovulationPeriodStart, ovulationPeriodEnd) -> CycleType.OVULATION
                isWithinCycle(currentDate, lutealStart, lutealEnd) -> CycleType.LUTEAL
                
                // 当前月强制展示效果（如果没有真实数据）
                isCurrentMonthSelected && cycles.isEmpty() && currentDate.dayOfMonth <= 5 -> 
                    CycleType.PERIOD
                isCurrentMonthSelected && cycles.isEmpty() && currentDate.dayOfMonth in 6..13 -> 
                    CycleType.FOLLICULAR
                isCurrentMonthSelected && cycles.isEmpty() && currentDate.dayOfMonth in 14..16 -> 
                    CycleType.OVULATION
                isCurrentMonthSelected && cycles.isEmpty() && currentDate.dayOfMonth in 17..28 -> 
                    CycleType.LUTEAL
                
                // 其他日期
                else -> CycleType.NONE
            }
            
            // 修复同一周期内颜色不一致的问题
            // 如果今天和昨天是同一个月经周期，并且都是月经期，则使用相同的类型
            val type = if (
                previousDate != null && 
                previousType != null &&
                previousDate.plusDays(1) == currentDate && 
                (initialType == CycleType.PERIOD && previousType == CycleType.PERIOD)
            ) {
                previousType
            } else {
                // 如果是新的月经期开始，记录开始日期
                if (initialType == CycleType.PERIOD && (previousType != CycleType.PERIOD || previousDate == null || previousDate.plusDays(1) != currentDate)) {
                    sameTypeStartDate = currentDate
                }
                initialType
            }
            
            previousDate = currentDate
            previousType = type
            
            // 计算周期天数 - 核心修复点
            val dayOfCycle = when {
                // 1. 如果在月经期内，计算从月经开始的天数
                type == CycleType.PERIOD && sameTypeStartDate != null -> {
                    ChronoUnit.DAYS.between(sameTypeStartDate, currentDate).toInt() + 1 // 从1开始计算
                }
                // 2. 如果有上次月经记录且当前日期在该周期内
                lastPeriod != null && (currentDate.isEqual(lastPeriodStart) || currentDate.isAfter(lastPeriodStart)) -> {
                    // 精确计算从月经开始的天数
                    ChronoUnit.DAYS.between(lastPeriodStart, currentDate).toInt() + 1 // 从1开始计算
                }
                // 3. 如果没有记录，为当前月设置假的周期天数以展示效果
                isCurrentMonthSelected && cycles.isEmpty() -> {
                    // 使用日期的dayOfMonth作为周期天数
                    currentDate.dayOfMonth
                }
                // 4. 其他情况
                else -> {
                    0
                }
            }
            
            days.add(
                CycleDay(
                    date = currentDate,
                    type = type,
                    isToday = currentDate.isEqual(today),
                    isSelected = currentDate.isEqual(selectedDate),
                    dayOfCycle = dayOfCycle
                )
            )
            
            currentDate = currentDate.plusDays(1)
        }
        
        return MenstrualCycleUiState(
            cycleDays = days,
            averageCycleLength = averageCycleLength,
            averagePeriodLength = averagePeriodLength
        )
    }
    
    /**
     * 检查是否首次使用
     */
    private suspend fun checkFirstTimeUse() {
        val hasRecords = repository.hasCycleRecords()
        _healthDataPermissionGranted.value = hasRecords
    }
    
    /**
     * 请求健康数据权限
     */
    private fun requestHealthDataPermission() {
        // 这里应该实现实际的权限请求逻辑
        // 由于是演示，我们直接设置为true
        _healthDataPermissionGranted.value = true
    }
    
    /**
     * 初始化数据库连接
     */
    private fun initializeDatabaseConnection() {
        // 数据库已通过依赖注入初始化，这里不需要额外操作
    }
    
    /**
     * 加载历史数据
     */
    private fun loadHistoricalData() {
        // 历史数据通过Flow自动加载，这里不需要额外操作
    }
    
    /**
     * 启动预测算法
     */
    private fun startPredictionAlgorithm() {
        // 预测模型已在generateCycleDays中实现
    }
    
    /**
     * 开始多设备同步 - 对应流程图的数据同步阶段
     */
    fun startDeviceSync() {
        viewModelScope.launch {
            _isSyncing.value = true
            
            try {
                // 模拟同步过程
                // 1. 本地数据库写入
                // 2. 华为健康数据同步
                // 3. 多设备状态同步
                checkForDataConflicts()
            } finally {
                _isSyncing.value = false
            }
        }
    }
    
    /**
     * 检查数据冲突 - 对应流程图的冲突解决流程
     */
    private fun checkForDataConflicts() {
        // 模拟冲突检测
        val hasConflict = false // 实际应用中应该有真实的冲突检测逻辑
        _hasDataConflict.value = hasConflict
    }
    
    /**
     * 智能合并建议 - 对应流程图的冲突解决流程
     */
    fun getConflictResolutionOptions(): List<String> {
        return listOf(
            "保留本地数据",
            "使用云端数据",
            "智能合并数据"
        )
    }
    
    /**
     * 执行数据修正 - 对应流程图的冲突解决流程
     */
    fun resolveDataConflict(option: Int) {
        viewModelScope.launch {
            when (option) {
                0 -> { /* 保留本地数据 */ }
                1 -> { /* 使用云端数据 */ }
                2 -> { /* 智能合并数据 */ }
            }
            
            // 重置冲突状态
            _hasDataConflict.value = false
            
            // 刷新视图
            refreshCalendarView()
        }
    }
    
    /**
     * 刷新日历视图
     */
    private fun refreshCalendarView() {
        // 通过触发selectedMonth的更新来刷新日历视图
        _selectedMonth.value = _selectedMonth.value
    }
    
    /**
     * 更新选中日期 - 对应流程图的用户交互流程
     */
    fun updateSelectedDate(date: LocalDate) {
        _selectedDate.value = date
        
        // 确保选中日期数据刷新
        Log.d("CycleDebug", "更新选中日期: $date")
        
        // 强制刷新日历视图，确保数据和UI保持同步
        refreshCalendarView()
    }
    
    /**
     * 更新显示月份
     */
    fun updateSelectedMonth(month: YearMonth) {
        _selectedMonth.value = month
    }
    
    /**
     * 验证日期有效性 - 对应流程图的用户交互流程中的验证日期有效性
     */
    fun isValidDate(date: LocalDate): Boolean {
        val today = LocalDate.now()
        
        // 不允许未来日期记录
        if (date.isAfter(today)) {
            return false
        }
        
        // 不允许过早的历史记录(比如一年前)
        if (date.isBefore(today.minusYears(1))) {
            return false
        }
        
        return true
    }
    

    
    /**
     * 记录经期开始 - 对应流程图中的保存新记录
     */
    fun recordPeriodStart(date: LocalDate, startTime: LocalTime) {
        viewModelScope.launch {
            if (!isValidDate(date)) {
                // 如果日期无效，应该显示错误提示
                return@launch
            }
            
            // 先检查是否有未结束的周期
            val latestCycle = repository.getLatestCycle()
            if (latestCycle != null && latestCycle.endDate == null) {
                // 自动结束上一个周期
                repository.updateCycle(latestCycle.copy(
                    endDate = date.minusDays(1)
                ))
            }
            
            // 添加新周期记录
            repository.insertCycle(CycleRecord(
                startDate = date,
                startTime = startTime.format(DateTimeFormatter.ofPattern("HH:mm"))
            ))
            
            // 触发数据同步
            startDeviceSync()
        }
    }
    
    /**
     * 记录经期结束
     */
    fun recordPeriodEnd(date: LocalDate, endTime: LocalTime) {
        viewModelScope.launch {
            if (!isValidDate(date)) {
                // 如果日期无效，应该显示错误提示
                return@launch
            }
            
            val latestCycle = repository.getLatestCycle() ?: return@launch
            if (latestCycle.endDate != null) return@launch // 已经结束
            
            // 计算经期长度
            val periodLength = ChronoUnit.DAYS.between(latestCycle.startDate, date).toInt() + 1
            
            // 更新记录
            repository.updateCycle(latestCycle.copy(
                endDate = date,
                endTime = endTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                periodLength = periodLength
            ))
            
            // 触发数据同步
            startDeviceSync()
        }
    }
    
    /**
     * 记录症状
     */
    fun recordSymptom(symptomType: String, intensity: Int = 1, notes: String? = null) {
        viewModelScope.launch {
            if (!isValidDate(selectedDate.value)) {
                // 如果日期无效，应该显示错误提示
                return@launch
            }
            
            // 记录症状
            repository.insertSymptom(SymptomRecord(
                date = selectedDate.value,
                symptomType = symptomType,
                intensity = intensity,
                notes = notes
            ))
            
            // 触发数据同步
            startDeviceSync()
        }
    }
    
    /**
     * 预测下次经期开始时间
     */
    fun predictNextPeriod(): LocalDate {
        val recordedCycles = cycles.value
        if (recordedCycles.isEmpty()) {
            return LocalDate.now().plusDays(28) // 默认28天
        }
        
        // 如果有足够数据，计算平均周期长度
        val avgCycleLength = if (recordedCycles.size >= 3) {
            recordedCycles
                .filter { it.cycleLength != null }
                .map { it.cycleLength!! }
                .average()
                .toInt()
                .coerceIn(21, 35) // 保证在合理范围内
        } else 28 // 默认28天
        
        // 从最近的周期记录计算
        val lastCycle = recordedCycles.maxByOrNull { it.startDate }
        return lastCycle?.startDate?.plusDays(avgCycleLength.toLong()) ?: LocalDate.now().plusDays(28)
    }
    
    /**
     * 获取当前周期状态文本
     */
    fun getCurrentCycleStatusText(): String {
        val selectedDay = selectedDayData.value
        
        return when (selectedDay.type) {
            CycleType.PERIOD -> "正处于经期第${selectedDay.dayOfCycle}天"
            CycleType.FOLLICULAR -> "正处于卵泡期第${selectedDay.dayOfCycle}天"
            CycleType.LUTEAL -> "正处于黄体期第${selectedDay.dayOfCycle}天"
            CycleType.FERTILE -> "正处于排卵前期(生育窗口期)"
            CycleType.OVULATION -> "正处于排卵期"
            CycleType.PREDICTED -> "预测经期即将开始"
            CycleType.PREDICTED_PERIOD -> "预测将处于经期"
            CycleType.PREDICTED_FOLLICULAR -> "预测将处于卵泡期"
            CycleType.PREDICTED_OVULATION -> "预测将处于排卵期"
            CycleType.PREDICTED_LUTEAL -> "预测将处于黄体期"
            CycleType.PREDICTED_FERTILE -> "预测将处于易孕期"

            CycleType.NONE -> if (selectedDay.dayOfCycle < 14) "处于卵泡期" else "处于黄体期"
        }
    }
    
    /**
     * 获取平均周期长度
     */
    fun getAverageCycleLength(): Int {
        val completedCycles = cycles.value.filter { it.cycleLength != null }
        if (completedCycles.isEmpty()) return 28
        
        return completedCycles
            .map { it.cycleLength!! }
            .average()
            .toInt()
    }
    
    /**
     * 获取平均经期长度
     */
    fun getAveragePeriodLength(): Int {
        val completedCycles = cycles.value.filter { it.periodLength != null }
        if (completedCycles.isEmpty()) return 5
        
        return completedCycles
            .map { it.periodLength!! }
            .average()
            .toInt()
    }
    
    /**
     * 获取专业预测的下次经期开始时间
     */
    fun getNextPeriodPrediction(): LocalDate? {
        return predictionResult.value?.nextPeriodStart
    }
    
    /**
     * 获取专业预测的排卵期
     */
    fun getOvulationPrediction(): LocalDate? {
        return predictionResult.value?.ovulationDate
    }
    
    /**
     * 获取专业预测的易孕期
     */
    fun getFertileWindow(): Pair<LocalDate, LocalDate>? {
        val prediction = predictionResult.value ?: return null
        return Pair(prediction.fertileWindowStart, prediction.fertileWindowEnd)
    }
    
    /**
     * 获取周期规律性评估
     */
    fun getCycleRegularityText(): String {
        return when (cycleRegularity.value) {
            PeriodPredictionAlgorithm.CycleRegularity.VERY_REGULAR -> "非常规律"
            PeriodPredictionAlgorithm.CycleRegularity.REGULAR -> "比较规律"
            PeriodPredictionAlgorithm.CycleRegularity.SOMEWHAT_IRREGULAR -> "略有不规律"
            PeriodPredictionAlgorithm.CycleRegularity.IRREGULAR -> "不规律"
            PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA -> "数据不足"
        }
    }
    
    /**
     * 获取预测置信度百分比
     */
    fun getPredictionConfidencePercent(): Int {
        return (predictionConfidence.value * 100).toInt()
    }
    
    /**
     * 获取个性化健康建议
     */
    fun getHealthRecommendations(): List<String> {
        return healthRecommendations.value
    }
    
    /**
     * 获取详细的预测信息文本
     */
    fun getPredictionSummary(): String {
        val prediction = predictionResult.value ?: return "暂无预测数据，请继续记录周期信息"
        
        val nextPeriod = prediction.nextPeriodStart.format(DateTimeFormatter.ofPattern("M月d日"))
        val ovulation = prediction.ovulationDate.format(DateTimeFormatter.ofPattern("M月d日"))
        val confidence = (prediction.confidenceLevel * 100).toInt()
        val regularity = getCycleRegularityText()
        
        return "基于专业算法预测：\n" +
                "• 下次经期：$nextPeriod\n" +
                "• 排卵期：$ovulation\n" +
                "• 周期规律性：$regularity\n" +
                "• 预测准确度：$confidence%"
    }
    
    /**
     * 添加样本数据以演示四个周期阶段的颜色效果
     * 注意：仅用于演示，实际应用中应该使用真实用户数据
     */
    private suspend fun addSampleCycleData() {
        val today = LocalDate.now()
        
        // 创建一个过去的生理周期记录，设定为上个月开始
        val lastMonthDate = today.minusMonths(1)
        val lastPeriodStart = LocalDate.of(lastMonthDate.year, lastMonthDate.month, 5)
        val lastPeriodEnd = lastPeriodStart.plusDays(5)
        
        // 插入历史记录
        val cycleId = repository.insertCycle(
            CycleRecord(
                startDate = lastPeriodStart,
                endDate = lastPeriodEnd,
                periodLength = 6,
                cycleLength = 28
            )
        )
        
        // 再添加一个更早的记录以建立周期模式
        val twoMonthsAgo = today.minusMonths(2)
        val olderPeriodStart = LocalDate.of(twoMonthsAgo.year, twoMonthsAgo.month, 8)
        val olderPeriodEnd = olderPeriodStart.plusDays(5)
        
        repository.insertCycle(
            CycleRecord(
                startDate = olderPeriodStart,
                endDate = olderPeriodEnd,
                periodLength = 6,
                cycleLength = 29
            )
        )
        
        // 添加当前月的记录（如果在月经期内）
        val thisMonth = YearMonth.now()
        if (today.dayOfMonth <= 10) { // 假设当月前10天是月经期
            repository.insertCycle(
                CycleRecord(
                    startDate = LocalDate.of(thisMonth.year, thisMonth.month, 1),
                    endDate = null // 正在进行中的周期
                )
            )
        }
        
        // 添加一些症状记录
        repository.insertSymptom(
            SymptomRecord(
                date = lastPeriodStart.plusDays(1),
                symptomType = "pain",
                intensity = 2,
                notes = "轻微腹痛"
            )
        )
        
        repository.insertSymptom(
            SymptomRecord(
                date = lastPeriodStart.plusDays(2),
                symptomType = "flow",
                intensity = 3,
                notes = "中度出血"
            )
        )
        
        // 添加排卵期症状
        val ovulationDay = lastPeriodStart.plusDays(14)
        repository.insertSymptom(
            SymptomRecord(
                date = ovulationDay,
                symptomType = "ovulation_pain",
                intensity = 1,
                notes = "轻微排卵痛"
            )
        )
    }
    
    /**
     * 验证日期是否在历史记录编辑范围内（过去六个月）
     */
    fun isValidDateForHistory(date: LocalDate): Boolean {
        val today = LocalDate.now()
        
        // 不允许未来日期记录
        if (date.isAfter(today)) {
            Log.d("CycleDebug", "日期编辑失败: $date 是未来日期")
            return false
        }
        
        // 允许编辑过去6个月内的记录，包括当前月
        val sixMonthsAgo = today.minusMonths(6).withDayOfMonth(1) // 从六个月前的1号开始
        if (date.isBefore(sixMonthsAgo)) {
            Log.d("CycleDebug", "日期编辑失败: $date 超过6个月 (早于 $sixMonthsAgo)")
            return false
        }
        
        Log.d("CycleDebug", "日期编辑成功: $date 可以编辑")
        return true
    }
    
    /**
     * 删除指定日期的月经周期记录
     */
    fun deleteCycleForDate(date: LocalDate) {
        viewModelScope.launch {
            if (!isValidDateForHistory(date)) {
                // 如果日期超出可编辑范围，不执行操作
                return@launch
            }
            
            // 查找包含该日期的周期记录
            val cycleToDelete = cycles.value.find { cycle ->
                val startDate = cycle.startDate
                val endDate = cycle.endDate ?: startDate.plusDays(
                    (cycle.periodLength ?: getAveragePeriodLength()).toLong() - 1
                )
                
                date >= startDate && date <= endDate
            }
            
            // 删除找到的记录
            if (cycleToDelete != null) {
                Log.d(TAG, "🗑️ 删除周期记录: ${cycleToDelete.id}, 日期: ${cycleToDelete.startDate}")
                repository.deleteCycle(cycleToDelete)

                // 🔧 修复：确保数据完全刷新
                delay(100) // 等待数据库操作完成

                // 触发数据同步
                startDeviceSync()

                // 🔧 修复：使用延迟刷新确保UI更新
                refreshCalendarViewWithDelay()

                Log.d(TAG, "✓ 周期记录删除完成，UI已刷新")
            } else {
                Log.d(TAG, "⚠️ 未找到要删除的周期记录，日期: $date")
            }
        }
    }
    
    /**
     * 记录指定日期范围的经期
     */
    fun recordPeriodForDateRange(startDate: LocalDate, endDate: LocalDate) {
        viewModelScope.launch {
            Log.d("CycleDebug", "记录经期范围: 从 $startDate 到 $endDate")
            
            // 验证日期范围是否有效
            if (!isValidDateForHistory(startDate) || !isValidDateForHistory(endDate)) {
                Log.d("CycleDebug", "日期范围无效")
                return@launch
            }
            
            // 确保结束日期不早于开始日期
            if (endDate.isBefore(startDate)) {
                Log.d("CycleDebug", "结束日期早于开始日期")
                return@launch
            }
            
            // 计算经期长度
            val periodLength = ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
            Log.d("CycleDebug", "经期长度: $periodLength 天")
            
            // 检查并删除与新记录重叠的现有记录
            val overlappingCycles = cycles.value.filter { cycle ->
                val cycleStart = cycle.startDate
                val cycleEnd = cycle.endDate ?: cycleStart.plusDays(
                    (cycle.periodLength ?: getAveragePeriodLength()).toLong() - 1
                )
                
                // 检查是否重叠
                (startDate <= cycleEnd && endDate >= cycleStart)
            }
            
            // 删除重叠的记录
            for (cycle in overlappingCycles) {
                Log.d("CycleDebug", "删除重叠的周期记录: ${cycle.startDate} 到 ${cycle.endDate}")
                repository.deleteCycle(cycle)
            }
            
            // 添加新周期记录
            val newCycleId = repository.insertCycle(CycleRecord(
                startDate = startDate,
                endDate = endDate,
                periodLength = periodLength
            ))
            
            Log.d("CycleDebug", "新周期记录ID: $newCycleId")
            
            // 更新周期长度（如果有前一个周期）
            val previousCycle = cycles.value
                .filter { it.startDate.isBefore(startDate) }
                .maxByOrNull { it.startDate }
                
            if (previousCycle != null) {
                val cycleLength = ChronoUnit.DAYS.between(previousCycle.startDate, startDate).toInt()
                
                // 只有在合理范围内才更新周期长度
                if (cycleLength in 21..45) {
                    repository.updateCycle(previousCycle.copy(
                        cycleLength = cycleLength
                    ))
                    Log.d("CycleDebug", "更新上一周期长度: $cycleLength 天")
                }
            }
            
            // 触发数据同步
            startDeviceSync()
            
            // 强制刷新视图数据
            refreshData()
            
            Log.d("CycleDebug", "经期范围记录完成")
        }
    }
    
    /**
     * 刷新日历视图（延迟版）- 确保数据处理完成后再刷新UI
     */
    fun refreshCalendarViewWithDelay() {
        viewModelScope.launch {
            // 给数据处理留出足够时间
            delay(300)
            
            // 更新选中日期，触发状态更新
            _selectedDate.value = selectedDate.value
            
            // 更新显示月份，触发重新生成周期天数据
            _selectedMonth.value = selectedMonth.value
            
            // 强制刷新日历视图
            refreshCalendarView()
        }
    }
    
    /**
     * 是否首次使用
     */
    val isFirstTimeUse: Boolean
        get() = !healthDataPermissionGranted.value
    
    /**
     * 为指定日期记录经期开始
     */
    fun recordPeriodStartForDate(date: LocalDate) {
        viewModelScope.launch {
            if (!isValidDate(date)) {
                return@launch
            }

            // 先检查是否有未结束的周期
            val latestCycle = repository.getLatestCycle()
            if (latestCycle != null && latestCycle.endDate == null) {
                // 自动结束上一个周期
                repository.updateCycle(latestCycle.copy(
                    endDate = date.minusDays(1)
                ))
            }

            // 添加新周期记录
            repository.insertCycle(CycleRecord(
                startDate = date
            ))

            // 触发数据同步
            startDeviceSync()
        }
    }

    // ==================== 经期历史管理功能 ====================

    /**
     * 补记过去的经期 - 增强版本，包含完整验证
     * @param startDate 经期开始日期
     * @param endDate 经期结束日期（可选）
     * @param notes 备注信息（可选）
     * @param onResult 操作结果回调
     */
    fun addHistoricalPeriod(
        startDate: LocalDate,
        endDate: LocalDate? = null,
        notes: String? = null,
        onResult: ((OperationResult) -> Unit)? = null
    ) {
        viewModelScope.launch {
            try {
                // 1. 验证日期有效性
                val validationResult = validatePeriodDates(startDate, endDate)
                when (validationResult) {
                    is ValidationResult.Invalid -> {
                        onResult?.invoke(OperationResult.Error(validationResult.message))
                        return@launch
                    }
                    is ValidationResult.Warning -> {
                        // 继续执行，但会在结果中包含警告
                    }
                    ValidationResult.Valid -> {
                        // 继续执行
                    }
                }

                // 2. 检查日期冲突
                val conflictResult = checkDateConflicts(startDate, endDate)
                when (conflictResult) {
                    is ConflictCheckResult.SingleConflict,
                    is ConflictCheckResult.MultipleConflicts -> {
                        onResult?.invoke(OperationResult.Conflict(conflictResult))
                        return@launch
                    }
                    ConflictCheckResult.NoConflict -> {
                        // 继续执行
                    }
                }

                // 3. 计算经期长度
                val periodLength = endDate?.let {
                    ChronoUnit.DAYS.between(startDate, it).toInt() + 1
                }

                // 4. 创建历史记录
                val historicalCycle = CycleRecord(
                    startDate = startDate,
                    endDate = endDate,
                    periodLength = periodLength,
                    notes = notes,
                    createdAt = LocalDate.now()
                )

                repository.insertCycle(historicalCycle)

                // 5. 重新计算相关周期的长度
                recalculateCycleLengths()

                Log.d(TAG, "成功添加历史经期: $startDate - $endDate")

                // 6. 返回结果
                val result = when (validationResult) {
                    is ValidationResult.Warning -> OperationResult.Warning(validationResult.message)
                    else -> OperationResult.Success
                }
                onResult?.invoke(result)

            } catch (e: Exception) {
                Log.e(TAG, "添加历史经期失败", e)
                onResult?.invoke(OperationResult.Error("添加失败: ${e.message}"))
            }
        }
    }

    /**
     * 编辑现有的经期记录 - 增强版本，包含完整验证
     * @param cycleId 周期记录ID
     * @param startDate 新的开始日期
     * @param endDate 新的结束日期（可选）
     * @param notes 新的备注信息（可选）
     * @param onResult 操作结果回调
     */
    fun editPeriodRecord(
        cycleId: Long,
        startDate: LocalDate,
        endDate: LocalDate? = null,
        notes: String? = null,
        onResult: ((OperationResult) -> Unit)? = null
    ) {
        Log.d(TAG, "🔧 开始编辑经期记录: cycleId=$cycleId, startDate=$startDate, endDate=$endDate")
        viewModelScope.launch {
            try {
                // 1. 检查记录是否存在
                val existingCycle = repository.getCycleById(cycleId)
                if (existingCycle == null) {
                    Log.e(TAG, "❌ 找不到指定的记录: $cycleId")
                    onResult?.invoke(OperationResult.Error("找不到指定的记录"))
                    return@launch
                }
                Log.d(TAG, "✓ 找到现有记录: ${existingCycle.startDate} - ${existingCycle.endDate}")

                // 2. 验证新日期的有效性
                val validationResult = validatePeriodDates(startDate, endDate, cycleId)
                when (validationResult) {
                    is ValidationResult.Invalid -> {
                        onResult?.invoke(OperationResult.Error(validationResult.message))
                        return@launch
                    }
                    is ValidationResult.Warning -> {
                        // 继续执行，但会在结果中包含警告
                    }
                    ValidationResult.Valid -> {
                        // 继续执行
                    }
                }

                // 3. 检查除当前记录外的日期冲突，并自动解决冲突
                val conflictResult = checkDateConflicts(startDate, endDate, cycleId)
                val hasConflict = when (conflictResult) {
                    is ConflictCheckResult.SingleConflict,
                    is ConflictCheckResult.MultipleConflicts -> {
                        // 🔧 修复：以手动编辑的记录为主，自动删除冲突的记录
                        Log.d(TAG, "🔄 检测到日期冲突，以手动编辑记录为主，删除冲突记录")
                        resolveConflictsWithManualPriority(conflictResult, cycleId)
                        true
                    }
                    ConflictCheckResult.NoConflict -> {
                        // 继续执行
                        false
                    }
                }

                // 4. 计算新的经期长度
                val periodLength = endDate?.let {
                    ChronoUnit.DAYS.between(startDate, it).toInt() + 1
                }

                // 5. 更新记录
                val updatedCycle = existingCycle.copy(
                    startDate = startDate,
                    endDate = endDate,
                    periodLength = periodLength,
                    notes = notes
                )
                Log.d(TAG, "🔄 准备更新记录: $updatedCycle")

                repository.updateCycle(updatedCycle)
                Log.d(TAG, "✓ 数据库更新完成")

                // 6. 重新计算相关周期的长度
                recalculateCycleLengths()
                Log.d(TAG, "✓ 周期长度重新计算完成")

                Log.d(TAG, "🎉 成功编辑经期记录: $cycleId")

                // 7. 返回结果
                val result = when {
                    hasConflict -> OperationResult.ConflictResolved("已自动解决日期冲突，以手动编辑记录为准")
                    validationResult is ValidationResult.Warning -> OperationResult.Warning(validationResult.message)
                    else -> OperationResult.Success
                }
                Log.d(TAG, "📤 返回操作结果: $result")
                onResult?.invoke(result)

            } catch (e: Exception) {
                Log.e(TAG, "❌ 编辑经期记录失败: cycleId=$cycleId", e)
                onResult?.invoke(OperationResult.Error("编辑失败: ${e.message}"))
            }
        }
    }

    /**
     * 删除经期记录
     * @param cycleId 周期记录ID
     */
    fun deletePeriodRecord(cycleId: Long) {
        viewModelScope.launch {
            try {
                val cycle = repository.getCycleById(cycleId)
                if (cycle == null) {
                    Log.w(TAG, "找不到周期记录: $cycleId")
                    return@launch
                }

                repository.deleteCycle(cycle)

                // 重新计算相关周期的长度
                recalculateCycleLengths()

                Log.d(TAG, "成功删除经期记录: $cycleId")

            } catch (e: Exception) {
                Log.e(TAG, "删除经期记录失败", e)
            }
        }
    }
    
    // 移除了带时间参数的记录方法 - 简化为只记录日期
    
    /**
     * 为指定日期记录经期结束
     */
    fun recordPeriodEndForDate(date: LocalDate) {
        viewModelScope.launch {
            if (!isValidDate(date)) {
                return@launch
            }
            
            val latestCycle = repository.getLatestCycle() ?: return@launch
            if (latestCycle.endDate != null) return@launch // 已经结束
            
            // 计算经期长度
            val periodLength = ChronoUnit.DAYS.between(latestCycle.startDate, date).toInt() + 1
            
            // 更新记录
            val updatedCycle = latestCycle.copy(
                endDate = date,
                periodLength = periodLength
            )
            repository.updateCycle(updatedCycle)
            
            Log.d("CycleDebug", "经期结束记录: 从${latestCycle.startDate}到${date}, 长度${periodLength}天")
            
            // 🆕 经期结束后自动同步到历史记录中（已经在数据库中了，无需额外操作）
            // 更新相邻周期的周期长度
            updateAdjacentCycleLengths(latestCycle.startDate)
            
            // 触发数据同步
            startDeviceSync()
            
            // 刷新数据显示
            refreshData()
        }
    }

    /**
     * 🆕 取消今天的经期记录
     */
    fun cancelTodayPeriodRecord() {
        viewModelScope.launch {
            val today = LocalDate.now()
            
            // 查找今天开始的经期记录
            val todayCycle = cycles.value.find { cycle ->
                cycle.startDate == today
            }
            
            if (todayCycle != null) {
                Log.d("CycleDebug", "取消今天的经期开始记录: ${todayCycle.startDate}")
                
                // 删除今天开始的记录
                repository.deleteCycle(todayCycle)
                
                // 触发数据同步
                startDeviceSync()
                
                // 刷新数据显示
                refreshData()
                
                Log.d("CycleDebug", "已成功取消今天的经期记录")
            } else {
                // 查找今天结束的经期记录
                val cycleEndingToday = cycles.value.find { cycle ->
                    cycle.endDate == today
                }
                
                if (cycleEndingToday != null) {
                    Log.d("CycleDebug", "取消今天的经期结束记录: ${cycleEndingToday.endDate}")
                    
                    // 将结束日期设为null，表示经期继续
                    repository.updateCycle(cycleEndingToday.copy(
                        endDate = null,
                        periodLength = null
                    ))
                    
                    // 触发数据同步
                    startDeviceSync()
                    
                    // 刷新数据显示
                    refreshData()
                    
                    Log.d("CycleDebug", "已成功取消今天的经期结束记录")
                }
            }
        }
    }
    
    /**
     * 刷新数据 - 强制重新加载所有数据
     */
    fun refreshData() {
        viewModelScope.launch {
            Log.d("CycleDebug", "强制刷新数据开始...")
            
            // 🔧 强制重新加载数据库中的周期数据
            val currentCycles = repository.getCycles().first()
            Log.d("CycleDebug", "重新加载后的周期数量: ${currentCycles.size}")
            
            // 🔧 触发所有相关StateFlow重新计算
            val currentDate = _selectedDate.value
            val currentMonth = _selectedMonth.value
            
            // 先临时更改值，再改回来，强制触发combine的重新计算
            _selectedDate.value = currentDate.minusDays(1)
            _selectedMonth.value = currentMonth.minusMonths(1)
            
            // 等待一帧时间
            delay(50)
            
            // 恢复原始值，这将触发所有依赖的重新计算
            _selectedDate.value = currentDate
            _selectedMonth.value = currentMonth
            
            // 🔧 等待UI重新计算
            delay(200)
            
            Log.d("CycleDebug", "强制刷新数据完成，当前选中日期: $currentDate")
        }
    }
    
    /**
     * 记录过去的经期数据 - 扩展时间范围限制
     */
    fun recordHistoryPeriod(startDate: LocalDate, endDate: LocalDate) {
        viewModelScope.launch {
            Log.d("CycleDebug", "记录历史经期: 从 $startDate 到 $endDate")
            
            // 验证日期范围是否有效（历史记录功能允许更长的时间范围）
            if (!isValidDateForHistoryRecord(startDate) || !isValidDateForHistoryRecord(endDate)) {
                Log.d("CycleDebug", "历史记录日期范围无效")
                return@launch
            }
            
            // 确保结束日期不早于开始日期
            if (endDate.isBefore(startDate)) {
                Log.d("CycleDebug", "结束日期早于开始日期")
                return@launch
            }
            
            // 计算经期长度
            val periodLength = ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
            Log.d("CycleDebug", "历史经期长度: $periodLength 天")
            
            // 验证经期长度合理性（1-10天）
            if (periodLength < 1 || periodLength > 10) {
                Log.d("CycleDebug", "经期长度不合理: $periodLength 天")
                return@launch
            }
            
            // 检查并删除与新记录重叠的现有记录
            val overlappingCycles = cycles.value.filter { cycle ->
                val cycleStart = cycle.startDate
                val cycleEnd = cycle.endDate ?: cycleStart.plusDays(
                    (cycle.periodLength ?: getAveragePeriodLength()).toLong() - 1
                )
                
                // 检查是否重叠
                (startDate <= cycleEnd && endDate >= cycleStart)
            }
            
            // 删除重叠的记录
            for (cycle in overlappingCycles) {
                Log.d("CycleDebug", "删除重叠的周期记录: ${cycle.startDate} 到 ${cycle.endDate}")
                repository.deleteCycle(cycle)
            }
            
            // 添加新的历史记录
            val newCycleId = repository.insertCycle(CycleRecord(
                startDate = startDate,
                endDate = endDate,
                periodLength = periodLength,
                notes = "历史记录 - ${LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}记录的历史数据" // 标记为历史记录数据
            ))
            
            Log.d("CycleDebug", "历史周期记录ID: $newCycleId")
            
            // 更新相邻周期的周期长度
            updateAdjacentCycleLengths(startDate)
            
            // 触发数据同步
            startDeviceSync()
            
            // 强制刷新视图数据
            refreshData()
            
            Log.d("CycleDebug", "历史经期记录完成")
        }
    }
    
    /**
     * 验证历史记录日期是否有效（允许更长的时间范围）
     */
    private fun isValidDateForHistoryRecord(date: LocalDate): Boolean {
        val today = LocalDate.now()
        
        // 不允许未来日期记录
        if (date.isAfter(today)) {
            Log.d("CycleDebug", "历史记录日期失败: $date 是未来日期")
            return false
        }
        
        // 允许记录过去2年内的数据
        val twoYearsAgo = today.minusYears(2)
        if (date.isBefore(twoYearsAgo)) {
            Log.d("CycleDebug", "历史记录日期失败: $date 超过2年 (早于 $twoYearsAgo)")
            return false
        }
        
        Log.d("CycleDebug", "历史记录日期有效: $date")
        return true
    }
    
    /**
     * 更新相邻周期的周期长度
     */
    private suspend fun updateAdjacentCycleLengths(newStartDate: LocalDate) {
        // 找到前一个周期并更新其周期长度
        val previousCycle = cycles.value
            .filter { it.startDate.isBefore(newStartDate) }
            .maxByOrNull { it.startDate }
            
        if (previousCycle != null) {
            val cycleLength = ChronoUnit.DAYS.between(previousCycle.startDate, newStartDate).toInt()
            
            // 只有在合理范围内才更新周期长度
            if (cycleLength in 21..45) {
                repository.updateCycle(previousCycle.copy(
                    cycleLength = cycleLength
                ))
                Log.d("CycleDebug", "更新前一周期长度: $cycleLength 天")
            }
        }
        
        // 找到后一个周期并更新当前周期的周期长度
        val nextCycle = cycles.value
            .filter { it.startDate.isAfter(newStartDate) }
            .minByOrNull { it.startDate }
            
        if (nextCycle != null) {
            val cycleLength = ChronoUnit.DAYS.between(newStartDate, nextCycle.startDate).toInt()
            
            // 只有在合理范围内才更新周期长度
            if (cycleLength in 21..45) {
                // 需要重新查询新插入的记录来更新
                val newCycle = cycles.value.find { it.startDate == newStartDate }
                if (newCycle != null) {
                    repository.updateCycle(newCycle.copy(
                        cycleLength = cycleLength
                    ))
                    Log.d("CycleDebug", "更新当前周期长度: $cycleLength 天")
                }
            }
        }
    }
    
    /**
     * 删除指定的周期记录
     */
    fun deleteCycleRecord(recordId: Long) {
        viewModelScope.launch {
            try {
                Log.d("CycleDebug", "删除周期记录: ID=$recordId")
                
                // 找到要删除的记录
                val recordToDelete = cycles.value.find { it.id == recordId }
                
                if (recordToDelete != null) {
                    // 删除记录
                    repository.deleteCycle(recordToDelete)
                    
                    Log.d("CycleDebug", "周期记录删除成功: ${recordToDelete.startDate}")
                    
                    // 触发数据同步
                    startDeviceSync()
                    
                    // 刷新数据
                    refreshData()
                } else {
                    Log.w("CycleDebug", "未找到要删除的周期记录: ID=$recordId")
                }
            } catch (e: Exception) {
                Log.e("CycleDebug", "删除周期记录失败", e)
            }
        }
    }

    /**
     * 验证历史日期的有效性
     */
    private fun isValidHistoricalDate(date: LocalDate): Boolean {
        val today = LocalDate.now()
        val minDate = today.minusYears(5) // 最多支持5年前的数据

        return when {
            date.isAfter(today) -> {
                Log.w(TAG, "不能添加未来日期的经期记录")
                false
            }
            date.isBefore(minDate) -> {
                Log.w(TAG, "不支持5年前的历史数据")
                false
            }
            else -> true
        }
    }

    /**
     * 解决冲突，以手动编辑的记录为主
     */
    private suspend fun resolveConflictsWithManualPriority(
        conflictResult: ConflictCheckResult,
        excludeCycleId: Long
    ) {
        try {
            when (conflictResult) {
                is ConflictCheckResult.SingleConflict -> {
                    Log.d(TAG, "🗑️ 删除冲突记录: ${conflictResult.conflictingCycle.id}")
                    repository.deleteCycle(conflictResult.conflictingCycle)
                }
                is ConflictCheckResult.MultipleConflicts -> {
                    Log.d(TAG, "🗑️ 删除多个冲突记录: ${conflictResult.conflictingCycles.size}个")
                    conflictResult.conflictingCycles.forEach { cycle ->
                        if (cycle.id != excludeCycleId) {
                            repository.deleteCycle(cycle)
                        }
                    }
                }
                ConflictCheckResult.NoConflict -> {
                    // 无需处理
                }
            }
            Log.d(TAG, "✓ 冲突解决完成，手动编辑记录优先")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 解决冲突失败", e)
        }
    }

    /**
     * 增强的日期验证 - 包含医学合理性检查
     */
    private fun validatePeriodDates(
        startDate: LocalDate,
        endDate: LocalDate?,
        excludeCycleId: Long? = null
    ): ValidationResult {
        // 基础日期验证
        if (!isValidHistoricalDate(startDate)) {
            return ValidationResult.Invalid("开始日期无效")
        }

        endDate?.let { end ->
            if (!isValidHistoricalDate(end)) {
                return ValidationResult.Invalid("结束日期无效")
            }

            if (end.isBefore(startDate)) {
                return ValidationResult.Invalid("结束日期不能早于开始日期")
            }

            val periodLength = ChronoUnit.DAYS.between(startDate, end).toInt() + 1
            if (periodLength > 15) {
                return ValidationResult.Warning("经期长度超过15天，建议咨询医生")
            }

            if (periodLength < 1) {
                return ValidationResult.Invalid("经期长度至少为1天")
            }
        }

        return ValidationResult.Valid
    }

    /**
     * 检查日期冲突 - 增强版本
     */
    private suspend fun checkDateConflicts(
        startDate: LocalDate,
        endDate: LocalDate?,
        excludeCycleId: Long? = null
    ): ConflictCheckResult {
        val cycles = repository.getCycles().first()
        val actualEndDate = endDate ?: startDate.plusDays(6) // 默认7天经期

        val conflicts = cycles.filter { cycle ->
            if (excludeCycleId != null && cycle.id == excludeCycleId) {
                return@filter false
            }

            val cycleEnd = cycle.endDate ?: cycle.startDate.plusDays(6)

            // 检查是否有重叠
            !(actualEndDate.isBefore(cycle.startDate) || startDate.isAfter(cycleEnd))
        }

        return when {
            conflicts.isEmpty() -> ConflictCheckResult.NoConflict
            conflicts.size == 1 -> {
                val conflict = conflicts.first()
                ConflictCheckResult.SingleConflict(
                    conflictingCycle = conflict,
                    suggestion = generateConflictSuggestion(startDate, endDate, conflict)
                )
            }
            else -> ConflictCheckResult.MultipleConflicts(
                conflictingCycles = conflicts,
                suggestion = "检测到多个冲突记录，建议重新检查日期"
            )
        }
    }

    /**
     * 生成冲突解决建议
     */
    private fun generateConflictSuggestion(
        newStart: LocalDate,
        newEnd: LocalDate?,
        existingCycle: CycleRecord
    ): String {
        val existingEnd = existingCycle.endDate ?: existingCycle.startDate.plusDays(6)

        return when {
            newStart.isBefore(existingCycle.startDate) -> {
                "建议将结束日期调整为${existingCycle.startDate.minusDays(1)}"
            }
            newStart.isAfter(existingEnd) -> {
                "建议将开始日期调整为${existingEnd.plusDays(1)}"
            }
            else -> {
                "建议合并或重新调整日期范围"
            }
        }
    }

    /**
     * 检查日期是否与现有记录冲突
     */
    private suspend fun hasDateConflict(startDate: LocalDate, endDate: LocalDate?): Boolean {
        val cycles = repository.getCycles().first()
        val actualEndDate = endDate ?: startDate.plusDays(6) // 默认7天经期

        return cycles.any { cycle ->
            val cycleEnd = cycle.endDate ?: cycle.startDate.plusDays(6)

            // 检查是否有重叠
            !(actualEndDate.isBefore(cycle.startDate) || startDate.isAfter(cycleEnd))
        }
    }

    /**
     * 检查日期是否与现有记录冲突（排除指定记录）
     */
    private suspend fun hasDateConflictExcluding(
        startDate: LocalDate,
        endDate: LocalDate?,
        excludeCycleId: Long
    ): Boolean {
        val cycles = repository.getCycles().first()
        val actualEndDate = endDate ?: startDate.plusDays(6)

        return cycles.any { cycle ->
            if (cycle.id == excludeCycleId) return@any false

            val cycleEnd = cycle.endDate ?: cycle.startDate.plusDays(6)

            // 检查是否有重叠
            !(actualEndDate.isBefore(cycle.startDate) || startDate.isAfter(cycleEnd))
        }
    }

    /**
     * 重新计算所有周期的长度
     */
    private suspend fun recalculateCycleLengths() {
        try {
            val cycles = repository.getCycles().first().sortedBy { it.startDate }

            for (i in 0 until cycles.size - 1) {
                val currentCycle = cycles[i]
                val nextCycle = cycles[i + 1]

                // 计算周期长度（从当前经期开始到下次经期开始）
                val cycleLength = ChronoUnit.DAYS.between(
                    currentCycle.startDate,
                    nextCycle.startDate
                ).toInt()

                // 更新周期长度
                if (currentCycle.cycleLength != cycleLength) {
                    repository.updateCycle(currentCycle.copy(cycleLength = cycleLength))
                }
            }

            Log.d(TAG, "重新计算周期长度完成")

        } catch (e: Exception) {
            Log.e(TAG, "重新计算周期长度失败", e)
        }
    }

    /**
     * 获取所有历史经期记录（用于历史管理界面）
     */
    fun getHistoricalPeriods(): Flow<List<CycleRecord>> {
        return repository.getCycles()
    }

    /**
     * 根据ID获取特定的经期记录
     */
    suspend fun getPeriodRecordById(cycleId: Long): CycleRecord? {
        return repository.getCycleById(cycleId)
    }

    /**
     * 获取经期统计信息
     */
    fun getPeriodStatistics(): Flow<PeriodStatistics> {
        return repository.getCycles().map { cycles ->
            calculatePeriodStatistics(cycles)
        }
    }

    /**
     * 计算经期统计信息
     */
    private fun calculatePeriodStatistics(cycles: List<CycleRecord>): PeriodStatistics {
        if (cycles.isEmpty()) {
            return PeriodStatistics()
        }

        val sortedCycles = cycles.sortedBy { it.startDate }

        // 计算平均周期长度
        val cycleLengths = sortedCycles.mapNotNull { it.cycleLength }
        val averageCycleLength = if (cycleLengths.isNotEmpty()) {
            cycleLengths.average()
        } else {
            DEFAULT_CYCLE_LENGTH.toDouble()
        }

        // 计算平均经期长度
        val periodLengths = sortedCycles.mapNotNull { it.periodLength }
        val averagePeriodLength = if (periodLengths.isNotEmpty()) {
            periodLengths.average()
        } else {
            DEFAULT_PERIOD_LENGTH.toDouble()
        }

        // 计算周期规律性（变异系数）
        val cycleRegularity = if (cycleLengths.size >= 3) {
            val variance = cycleLengths.map { (it - averageCycleLength).pow(2) }.average()
            val standardDeviation = sqrt(variance)
            val coefficientOfVariation = standardDeviation / averageCycleLength

            when {
                coefficientOfVariation < 0.05 -> CycleRegularity.VERY_REGULAR
                coefficientOfVariation < 0.1 -> CycleRegularity.REGULAR
                coefficientOfVariation < 0.2 -> CycleRegularity.SOMEWHAT_IRREGULAR
                else -> CycleRegularity.IRREGULAR
            }
        } else {
            CycleRegularity.INSUFFICIENT_DATA
        }

        // 最短和最长周期
        val shortestCycle = cycleLengths.minOrNull() ?: 0
        val longestCycle = cycleLengths.maxOrNull() ?: 0

        // 最短和最长经期
        val shortestPeriod = periodLengths.minOrNull() ?: 0
        val longestPeriod = periodLengths.maxOrNull() ?: 0

        return PeriodStatistics(
            totalCycles = cycles.size,
            averageCycleLength = averageCycleLength,
            averagePeriodLength = averagePeriodLength,
            cycleRegularity = cycleRegularity,
            shortestCycle = shortestCycle,
            longestCycle = longestCycle,
            shortestPeriod = shortestPeriod,
            longestPeriod = longestPeriod,
            lastPeriodDate = sortedCycles.lastOrNull()?.startDate
        )
    }

    companion object {
        private const val TAG = "MenstrualCycleViewModel"
        private const val DEFAULT_CYCLE_LENGTH = 28
        private const val DEFAULT_PERIOD_LENGTH = 5
    }
}

/**
 * 经期统计信息数据类
 */
data class PeriodStatistics(
    val totalCycles: Int = 0,
    val averageCycleLength: Double = 0.0,
    val averagePeriodLength: Double = 0.0,
    val cycleRegularity: CycleRegularity = CycleRegularity.INSUFFICIENT_DATA,
    val shortestCycle: Int = 0,
    val longestCycle: Int = 0,
    val shortestPeriod: Int = 0,
    val longestPeriod: Int = 0,
    val lastPeriodDate: LocalDate? = null
)

/**
 * 验证结果
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Warning(val message: String) : ValidationResult()
    data class Invalid(val message: String) : ValidationResult()
}

/**
 * 冲突检查结果
 */
sealed class ConflictCheckResult {
    object NoConflict : ConflictCheckResult()
    data class SingleConflict(
        val conflictingCycle: CycleRecord,
        val suggestion: String
    ) : ConflictCheckResult()
    data class MultipleConflicts(
        val conflictingCycles: List<CycleRecord>,
        val suggestion: String
    ) : ConflictCheckResult()
}

/**
 * 操作结果
 */
sealed class OperationResult {
    object Success : OperationResult()
    data class Warning(val message: String) : OperationResult()
    data class Error(val message: String) : OperationResult()
    data class Conflict(val conflictResult: ConflictCheckResult) : OperationResult()
    data class ConflictResolved(val message: String) : OperationResult()
}