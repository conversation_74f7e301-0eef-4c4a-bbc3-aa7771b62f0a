package com.timeflow.app.data.repository

import android.util.Log
import com.timeflow.app.data.model.TaskTime
import com.timeflow.app.data.model.TaskTimeConflict
import com.timeflow.app.data.model.TimeConflictType
import com.timeflow.app.data.model.TaskTimeUpdateEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务时间数据仓库
 * 提供任务时间的CRUD操作和事件流管理
 */
@Singleton
class TaskTimeRepository @Inject constructor() {
    
    companion object {
        private const val TAG = "TaskTimeRepository"
    }
    
    // 内存缓存
    private val _timeCache = MutableStateFlow<Map<String, TaskTime>>(emptyMap())
    val timeCache: StateFlow<Map<String, TaskTime>> = _timeCache.asStateFlow()
    
    // 时间更新事件流
    private val _timeUpdates = MutableSharedFlow<TaskTime>()
    val timeUpdates: Flow<TaskTime> = _timeUpdates.asSharedFlow()
    
    // 时间更新事件流（包含详细信息）
    private val _timeUpdateEvents = MutableSharedFlow<TaskTimeUpdateEvent>()
    val timeUpdateEvents: Flow<TaskTimeUpdateEvent> = _timeUpdateEvents.asSharedFlow()
    
    // 线程安全锁
    private val cacheMutex = Mutex()
    
    /**
     * 获取任务时间
     */
    suspend fun getTaskTime(taskId: String): TaskTime? {
        return cacheMutex.withLock {
            _timeCache.value[taskId] ?: loadFromDatabase(taskId)
        }
    }
    
    /**
     * 更新任务时间
     */
    suspend fun updateTaskTime(
        taskId: String,
        startTime: LocalDateTime? = null,
        endTime: LocalDateTime? = null,
        dueDate: LocalDateTime? = null,
        source: String = "Unknown"
    ): Result<TaskTime> {
        return try {
            cacheMutex.withLock {
                Log.d(TAG, "开始更新任务时间: taskId=$taskId, source=$source")
                
                // 获取旧的时间数据
                val oldTime = _timeCache.value[taskId]
                
                // 创建新的时间数据
                val newTime = if (oldTime != null) {
                    oldTime.withUpdatedTime(
                        newStartTime = startTime,
                        newEndTime = endTime,
                        newDueDate = dueDate
                    )
                } else {
                    TaskTime(
                        taskId = taskId,
                        startTime = startTime,
                        endTime = endTime,
                        dueDate = dueDate
                    )
                }
                
                // 更新缓存
                val currentCache = _timeCache.value.toMutableMap()
                currentCache[taskId] = newTime
                _timeCache.value = currentCache
                
                // 发送更新事件
                _timeUpdates.emit(newTime)
                
                // 发送详细更新事件
                val event = TaskTimeUpdateEvent(
                    taskId = taskId,
                    oldTime = oldTime,
                    newTime = newTime,
                    source = source
                )
                _timeUpdateEvents.emit(event)
                
                Log.d(TAG, "任务时间更新成功: $newTime")
                
                // 异步持久化到数据库（这里可以添加Room数据库操作）
                persistToDatabase(newTime)
                
                Result.success(newTime)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新任务时间失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量获取任务时间
     */
    suspend fun getTaskTimes(taskIds: List<String>): Map<String, TaskTime> {
        return cacheMutex.withLock {
            val result = mutableMapOf<String, TaskTime>()
            val currentCache = _timeCache.value
            
            taskIds.forEach { taskId ->
                val taskTime = currentCache[taskId] ?: loadFromDatabase(taskId)
                if (taskTime != null) {
                    result[taskId] = taskTime
                }
            }
            
            result
        }
    }
    
    /**
     * 删除任务时间
     */
    suspend fun deleteTaskTime(taskId: String): Result<Unit> {
        return try {
            cacheMutex.withLock {
                val currentCache = _timeCache.value.toMutableMap()
                val removedTime = currentCache.remove(taskId)
                _timeCache.value = currentCache
                
                if (removedTime != null) {
                    // 发送删除事件
                    val event = TaskTimeUpdateEvent(
                        taskId = taskId,
                        oldTime = removedTime,
                        newTime = TaskTime(taskId = taskId), // 空的时间数据表示删除
                        source = "Delete"
                    )
                    _timeUpdateEvents.emit(event)
                }
                
                // 从数据库删除
                deleteFromDatabase(taskId)
                
                Log.d(TAG, "任务时间删除成功: taskId=$taskId")
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除任务时间失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量更新任务时间
     */
    suspend fun batchUpdateTaskTimes(updates: Map<String, TaskTime>): Result<Unit> {
        return try {
            cacheMutex.withLock {
                val currentCache = _timeCache.value.toMutableMap()
                
                updates.forEach { (taskId, newTime) ->
                    val oldTime = currentCache[taskId]
                    currentCache[taskId] = newTime
                    
                    // 发送更新事件
                    val event = TaskTimeUpdateEvent(
                        taskId = taskId,
                        oldTime = oldTime,
                        newTime = newTime,
                        source = "BatchUpdate"
                    )
                    _timeUpdateEvents.emit(event)
                }
                
                _timeCache.value = currentCache
                
                Log.d(TAG, "批量更新任务时间成功: ${updates.size}个任务")
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Log.e(TAG, "批量更新任务时间失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 清除所有缓存
     */
    suspend fun clearCache() {
        cacheMutex.withLock {
            _timeCache.value = emptyMap()
            Log.d(TAG, "任务时间缓存已清除")
        }
    }
    
    /**
     * 预加载任务时间数据
     */
    suspend fun preloadTaskTimes(taskIds: List<String>) {
        try {
            val missingIds = taskIds.filter { !_timeCache.value.containsKey(it) }
            if (missingIds.isNotEmpty()) {
                Log.d(TAG, "预加载任务时间: ${missingIds.size}个任务")
                // 这里可以添加批量从数据库加载的逻辑
            }
        } catch (e: Exception) {
            Log.e(TAG, "预加载任务时间失败", e)
        }
    }
    
    /**
     * 持久化到数据库（异步）
     */
    private suspend fun persistToDatabase(taskTime: TaskTime) {
        try {
            // TODO: 实现Room数据库操作
            Log.d(TAG, "持久化任务时间到数据库: ${taskTime.taskId}")
        } catch (e: Exception) {
            Log.e(TAG, "持久化任务时间失败", e)
        }
    }
    
    /**
     * 从数据库加载
     */
    private suspend fun loadFromDatabase(taskId: String): TaskTime? {
        return try {
            // TODO: 实现Room数据库查询
            Log.d(TAG, "从数据库加载任务时间: $taskId")
            null
        } catch (e: Exception) {
            Log.e(TAG, "从数据库加载任务时间失败", e)
            null
        }
    }
    
    /**
     * 从数据库删除
     */
    private suspend fun deleteFromDatabase(taskId: String) {
        try {
            // TODO: 实现Room数据库删除操作
            Log.d(TAG, "从数据库删除任务时间: $taskId")
        } catch (e: Exception) {
            Log.e(TAG, "从数据库删除任务时间失败", e)
        }
    }
    
    /**
     * 检查时间冲突
     */
    private suspend fun checkTimeConflicts(taskTime: TaskTime): List<TaskTimeConflict> {
        val conflicts = mutableListOf<TaskTimeConflict>()
        
        if (taskTime.effectiveTime == null) {
            return conflicts
        }
        
        val allTimes = _timeCache.value.values
        
        allTimes.forEach { otherTime ->
            if (otherTime.taskId != taskTime.taskId && otherTime.effectiveTime != null) {
                val conflictType = detectConflictType(taskTime, otherTime)
                if (conflictType != null) {
                    conflicts.add(
                        TaskTimeConflict(
                            conflictingTaskId = otherTime.taskId,
                            conflictingTaskTitle = "任务${otherTime.taskId}", // 这里需要获取实际标题
                            conflictType = conflictType,
                            conflictTime = otherTime.effectiveTime!!
                        )
                    )
                }
            }
        }
        
        return conflicts
    }
    
    /**
     * 检测冲突类型
     */
    private fun detectConflictType(time1: TaskTime, time2: TaskTime): TimeConflictType? {
        val t1 = time1.effectiveTime ?: return null
        val t2 = time2.effectiveTime ?: return null

        return when {
            t1.isEqual(t2) -> TimeConflictType.SAME_TIME
            // 这里可以添加更复杂的冲突检测逻辑
            else -> null
        }
    }
} 