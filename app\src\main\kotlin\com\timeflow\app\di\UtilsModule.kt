package com.timeflow.app.di

import com.timeflow.app.utils.RenderOptimizer
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.util.DataConsistencyFixer
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.repository.TaskRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 提供工具类实例的Module
 */
@Module
@InstallIn(SingletonComponent::class)
object UtilsModule {
    
    /**
     * 提供RenderOptimizer单例实例
     */
    @Provides
    @Singleton
    fun provideRenderOptimizer(): RenderOptimizer {
        return RenderOptimizer
    }
    
    /**
     * 提供SystemBarManager单例实例
     */
    @Provides
    @Singleton
    fun provideSystemBarManager(): SystemBarManager {
        return SystemBarManager
    }
    
    /**
     * 提供DataConsistencyFixer单例实例
     */
    @Provides
    @Singleton
    fun provideDataConsistencyFixer(
        taskDao: TaskDao,
        taskRepository: TaskRepository
    ): DataConsistencyFixer {
        return DataConsistencyFixer(taskDao, taskRepository)
    }
} 