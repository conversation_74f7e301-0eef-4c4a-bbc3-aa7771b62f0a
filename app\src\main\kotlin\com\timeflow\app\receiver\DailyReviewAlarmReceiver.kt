package com.timeflow.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.TimeFlowNotificationManager
import com.timeflow.app.worker.DailyReviewWorker
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import java.time.LocalDate
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager

/**
 * 每日回顾闹钟广播接收器
 * 接收每日回顾的闹钟广播并显示通知
 */
@AndroidEntryPoint
class DailyReviewAlarmReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var notificationManager: TimeFlowNotificationManager

    @Inject
    lateinit var taskRepository: TaskRepository

    @Inject
    lateinit var habitRepository: com.timeflow.app.data.repository.HabitRepository

    @Inject
    lateinit var timeSessionRepository: com.timeflow.app.data.repository.TimeSessionRepository

    @Inject
    lateinit var reflectionRepository: com.timeflow.app.ui.screen.reflection.ReflectionRepository

    @Inject
    lateinit var goalRepository: com.timeflow.app.data.repository.GoalRepository
    
    // 创建协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 通知设置DataStore - 与NotificationSettingsViewModel保持一致
    private val Context.notificationSettingsDataStore by preferencesDataStore(name = "notification_settings")
    
    // DataStore键定义 - 与NotificationSettingsViewModel保持一致
    private object PreferencesKeys {
        val NOTIFICATIONS_ENABLED = booleanPreferencesKey("notifications_enabled")
        val DAILY_REVIEW_ENABLED = booleanPreferencesKey("daily_review_enabled")
        val DAILY_REVIEW_TIME = stringPreferencesKey("daily_review_time")
        
        val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")
        val LED_ENABLED = booleanPreferencesKey("led_enabled")
        val BADGE_ENABLED = booleanPreferencesKey("badge_enabled")
        val LOCK_SCREEN_VISIBLE = booleanPreferencesKey("lock_screen_visible")
        val BANNER_STYLE = booleanPreferencesKey("banner_style")
        val GROUP_NOTIFICATIONS = booleanPreferencesKey("group_notifications")
    }
    
    companion object {
        private const val TAG = "DailyReviewAlarmReceiver"
        const val EXTRA_REVIEW_TIME = "review_time"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val reviewTime = intent.getStringExtra(EXTRA_REVIEW_TIME) ?: "21:00"
        
        Log.d(TAG, "🔔 收到每日回顾闹钟广播，时间: $reviewTime")
        
        // 在后台协程中处理
        scope.launch {
            try {
                // 获取通知设置
                val settings = getNotificationSettings(context)

                // 检查是否启用了每日回顾
                if (!settings.notificationsEnabled || !settings.dailyReviewEnabled) {
                    Log.d(TAG, "每日回顾通知已禁用，跳过发送")
                    // 仍然需要重新设置明天的闹钟
                    rescheduleTomorrowAlarm(context, reviewTime)
                    return@launch
                }

                // 🎯 新功能：使用数据服务生成完整的每日回顾
                val today = LocalDate.now()
                val dailyReviewDataService = com.timeflow.app.service.DailyReviewDataService(
                    context = context,
                    taskRepository = taskRepository,
                    habitRepository = habitRepository,
                    timeSessionRepository = timeSessionRepository,
                    reflectionRepository = reflectionRepository,
                    goalRepository = goalRepository
                )

                Log.d(TAG, "开始生成每日回顾数据...")
                val reviewData = dailyReviewDataService.generateDailyReviewData(today)
                Log.d(TAG, "每日回顾数据生成完成，评分: ${reviewData.overallScore}")

                // 发送基于实际数据的每日回顾通知
                notificationManager.showDailyReview(
                    reviewData = reviewData,
                    settings = settings
                )

                Log.d(TAG, "✅ 每日回顾通知已发送（基于实际数据）")

                // 重新设置明天的闹钟（因为Android 6.0+无法设置重复的精确闹钟）
                rescheduleTomorrowAlarm(context, reviewTime)

            } catch (e: Exception) {
                Log.e(TAG, "发送每日回顾通知失败", e)

                // 🔄 降级处理：如果新功能失败，使用简单的任务统计
                try {
                    val settings = getNotificationSettings(context)
                    if (settings.notificationsEnabled && settings.dailyReviewEnabled) {
                        val today = LocalDate.now()
                        val allTasks = taskRepository.getAllTasks()
                        val todayTasks = allTasks.filter { task ->
                            (task.startDate?.toLocalDate() == today) ||
                            (task.dueDate?.toLocalDate() == today) ||
                            (task.createdAt.toLocalDate() == today)
                        }
                        val completedTasks = todayTasks.count { it.isCompleted }
                        val totalTasks = todayTasks.size

                        notificationManager.showDailyReview(
                            completedTasks = completedTasks,
                            totalTasks = totalTasks,
                            settings = settings
                        )

                        Log.d(TAG, "✅ 每日回顾通知已发送（降级模式）")
                    }
                } catch (fallbackError: Exception) {
                    Log.e(TAG, "降级处理也失败了", fallbackError)
                }

                // 即使失败也要重新设置明天的闹钟
                rescheduleTomorrowAlarm(context, reviewTime)
            }
        }
    }
    
    /**
     * 重新设置明天的回顾闹钟
     * 由于Android 6.0+无法设置重复的精确闹钟，需要在每次触发后重新设置下一次的闹钟
     */
    private fun rescheduleTomorrowAlarm(context: Context, reviewTime: String) {
        try {
            Log.d(TAG, "重新设置明天的每日回顾闹钟: $reviewTime")
            
            // 创建WorkManager请求数据
            val inputData = Data.Builder()
                .putString(DailyReviewWorker.KEY_REVIEW_TIME, reviewTime)
                .build()
            
            // 创建一次性工作请求
            val reminderRequest = OneTimeWorkRequestBuilder<DailyReviewWorker>()
                .setInputData(inputData)
                .addTag("daily_review_reminder")
                .build()
            
            // 通过WorkManager调度工作
            WorkManager.getInstance(context).enqueue(reminderRequest)
            
            Log.d(TAG, "✅ 明天的每日回顾闹钟已重新设置")
            
        } catch (e: Exception) {
            Log.e(TAG, "重新设置明天的每日回顾闹钟失败", e)
        }
    }
    
    /**
     * 获取通知设置
     */
    private suspend fun getNotificationSettings(context: Context): NotificationSettings {
        return try {
            context.notificationSettingsDataStore.data
                .map { preferences ->
                    NotificationSettings(
                        notificationsEnabled = preferences[PreferencesKeys.NOTIFICATIONS_ENABLED] ?: true,
                        taskRemindersEnabled = true, // 不需要这些设置，设为默认值
                        deadlineRemindersEnabled = true,
                        overdueRemindersEnabled = true,
                        dailyReviewEnabled = preferences[PreferencesKeys.DAILY_REVIEW_ENABLED] ?: true,
                        dailyReviewTime = preferences[PreferencesKeys.DAILY_REVIEW_TIME] ?: "21:00",
                        taskPersistentNotificationEnabled = true,

                        // 习惯培养提醒（简化）
                        habitRemindersEnabled = true,

                        // 专注时间提醒（简化）
                        focusRemindersEnabled = true,
                        focusSessionNotificationsEnabled = true,

                        // 健康管理提醒（新增用药提醒）
                        medicationRemindersEnabled = true,
                        medicationSoundEnabled = true,
                        medicationVibrationEnabled = true,
                        medicationAdvanceTime = 5,

                        // 通知方式设置（简化）
                        soundEnabled = preferences[PreferencesKeys.SOUND_ENABLED] ?: true,
                        vibrationEnabled = preferences[PreferencesKeys.VIBRATION_ENABLED] ?: true,

                        // 免打扰设置（简化）
                        doNotDisturbEnabled = false,
                        doNotDisturbStartTime = "22:00",
                        doNotDisturbEndTime = "08:00",

                        // 提醒时间设置（统一简化）
                        defaultReminderTime = 15
                    )
                }
                .first()
        } catch (e: Exception) {
            Log.e(TAG, "读取通知设置失败，使用默认设置", e)
            // 返回默认设置 - 简化后的结构
            NotificationSettings(
                notificationsEnabled = true,
                taskRemindersEnabled = true,
                deadlineRemindersEnabled = true,
                overdueRemindersEnabled = true,
                dailyReviewEnabled = true,
                dailyReviewTime = "21:00",
                taskPersistentNotificationEnabled = true,

                // 习惯培养提醒（简化）
                habitRemindersEnabled = true,

                // 专注时间提醒（简化）
                focusRemindersEnabled = true,
                focusSessionNotificationsEnabled = true,

                // 健康管理提醒（新增用药提醒）
                medicationRemindersEnabled = true,
                medicationSoundEnabled = true,
                medicationVibrationEnabled = true,
                medicationAdvanceTime = 5,

                // 通知方式设置（简化）
                soundEnabled = true,
                vibrationEnabled = true,

                // 免打扰设置（简化）
                doNotDisturbEnabled = false,
                doNotDisturbStartTime = "22:00",
                doNotDisturbEndTime = "08:00",

                // 提醒时间设置（统一简化）
                defaultReminderTime = 15
            )
        }
    }
} 