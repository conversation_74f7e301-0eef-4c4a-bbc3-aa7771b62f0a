package com.timeflow.app.data.repository

import com.timeflow.app.data.entity.KanbanBoard
import kotlinx.coroutines.flow.Flow

/**
 * 看板仓库接口
 */
interface KanbanBoardRepository {
    
    /**
     * 获取所有看板
     */
    suspend fun getAllBoards(): List<KanbanBoard>
    
    /**
     * 观察所有看板变化
     */
    fun observeAllBoards(): Flow<List<KanbanBoard>>
    
    /**
     * 根据ID获取看板
     */
    suspend fun getBoardById(boardId: String): KanbanBoard?
    
    /**
     * 观察指定ID的看板变化
     */
    fun observeBoardById(boardId: String): Flow<KanbanBoard?>
    
    /**
     * 插入看板
     */
    suspend fun insertBoard(board: KanbanBoard)
    
    /**
     * 更新看板
     */
    suspend fun updateBoard(board: KanbanBoard)
    
    /**
     * 删除看板
     */
    suspend fun deleteBoard(board: KanbanBoard)
} 