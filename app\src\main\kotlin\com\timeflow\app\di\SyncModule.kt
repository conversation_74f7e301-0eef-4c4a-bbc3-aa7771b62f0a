package com.timeflow.app.di

import com.timeflow.app.ui.screen.settings.SyncRepository
import com.timeflow.app.ui.screen.settings.SyncRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 同步功能依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class SyncModule {
    
    /**
     * 绑定同步Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindSyncRepository(
        syncRepositoryImpl: SyncRepositoryImpl
    ): SyncRepository
} 