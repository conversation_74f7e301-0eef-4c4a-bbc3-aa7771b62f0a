<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="20dp">

    <!-- 顶部日期和状态区域 -->
    <LinearLayout
        android:id="@+id/widget_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_alignParentTop="true">

        <!-- 左侧日期信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widget_weekday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="周六"
                android:textSize="18sp"
                android:textColor="@color/widget_today_text_secondary_light"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/widget_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="15"
                android:textSize="52sp"
                android:textStyle="bold"
                android:textColor="@color/widget_today_text_primary_light"
                android:fontFamily="sans-serif-light"
                android:layout_marginTop="-8dp" />

        </LinearLayout>

        <!-- 右侧状态图标 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/widget_mood_emoji"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:text="😰"
                android:textSize="32sp"
                android:gravity="center"
                android:background="@drawable/widget_card_background"
                android:elevation="2dp" />

            <TextView
                android:id="@+id/widget_task_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3/4"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/widget_text_secondary"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 任务列表区域 -->
    <LinearLayout
        android:id="@+id/widget_task_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/widget_header"
        android:layout_marginTop="24dp">

        <!-- 任务项1 -->
        <LinearLayout
            android:id="@+id/widget_task_container_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background"
            android:elevation="1dp">

            <View
                android:id="@+id/widget_task_indicator_1"
                android:layout_width="6dp"
                android:layout_height="32dp"
                android:background="@color/widget_accent_teal"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/widget_task_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="不能在工作中说大话"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 任务项2 -->
        <LinearLayout
            android:id="@+id/widget_task_container_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background"
            android:elevation="1dp">

            <View
                android:id="@+id/widget_task_indicator_2"
                android:layout_width="6dp"
                android:layout_height="32dp"
                android:background="@color/widget_accent_green"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/widget_task_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="吃中药"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 任务项3 -->
        <LinearLayout
            android:id="@+id/widget_task_container_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background"
            android:elevation="1dp">

            <View
                android:id="@+id/widget_task_indicator_3"
                android:layout_width="6dp"
                android:layout_height="32dp"
                android:background="@color/widget_accent_green"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/widget_task_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="饭后漱口"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 任务项4 -->
        <LinearLayout
            android:id="@+id/widget_task_container_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:background="@drawable/widget_task_item_background"
            android:elevation="1dp"
            android:visibility="gone">

            <View
                android:id="@+id/widget_task_indicator_4"
                android:layout_width="6dp"
                android:layout_height="32dp"
                android:background="@color/widget_accent_blue"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/widget_task_4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="图文记录本"
                android:textSize="16sp"
                android:textColor="@color/widget_text_primary"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/widget_empty_state"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginTop="32dp"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🎉"
                android:textSize="32sp"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="今日任务已完成"
                android:textSize="16sp"
                android:textColor="@color/widget_text_secondary"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
