package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.CalendarMonth
import androidx.compose.material.icons.outlined.Numbers
import androidx.compose.material.icons.outlined.Upload
import androidx.compose.material.icons.outlined.Check
import androidx.compose.material.icons.outlined.Notifications
import androidx.compose.material.icons.outlined.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.ui.components.goal.GoalCategorySelector
import com.timeflow.app.ui.components.goal.SmartCategoryRecommendation
import com.timeflow.app.ui.components.goal.QuickSmartCategoryButton
import com.timeflow.app.data.model.getCategoryById
import com.timeflow.app.ai.GoalCategoryClassifier
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import androidx.compose.ui.platform.LocalContext
import android.app.Activity
import android.util.Log
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.ui.screen.goal.components.RecurringSettingsPanel
import com.timeflow.app.ui.screen.goal.components.ReminderSettingsPanel
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.utils.getColorFromHex
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.outlined.Assignment
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.ListAlt
import androidx.compose.material.icons.filled.List
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.DateRange
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import com.timeflow.app.ui.viewmodel.GoalCreationViewModel
import com.timeflow.app.ui.components.goal.GoalAssistantButtons
import com.timeflow.app.ui.components.goal.SmartTemplateList
import androidx.compose.material3.Slider


/**
 * 添加目标屏幕
 * 提供表单用于创建新的目标
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddGoalScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel(),
    templateViewModel: GoalTemplateViewModel = hiltViewModel(),
    goalCreationViewModel: GoalCreationViewModel = hiltViewModel()
) {
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态变量
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var startDate by remember { mutableStateOf<LocalDateTime?>(LocalDateTime.now()) }
    var dueDate by remember { mutableStateOf<LocalDateTime?>(LocalDateTime.now().plusMonths(1)) }
    var selectedPriority by remember { mutableStateOf(GoalPriority.MEDIUM) }
    var selectedCategoryId by remember { mutableStateOf("personal_development") }
    
    // 目标量化相关状态
    var goalType by remember { mutableStateOf(GoalType.BOOLEAN) }
    var currentValue by remember { mutableStateOf("0") }
    var targetValue by remember { mutableStateOf("100") }
    var unit by remember { mutableStateOf("") }
    var isRecurring by remember { mutableStateOf(false) }
    var recurringSettings by remember { mutableStateOf<RecurringSettings?>(null) }
    
    // 提醒设置状态
    var reminderSettings = remember { mutableStateListOf<ReminderSetting>() }
    
    // 附件状态
    var attachments = remember { mutableStateListOf<String>() }

    // 模板相关状态
    var showTemplateDialog by remember { mutableStateOf(false) }
    var showTemplateLibraryDialog by remember { mutableStateOf(false) }
    var selectedTemplate by remember { mutableStateOf<GoalTemplate?>(null) }
    

    
    // 添加AI推荐模板卡片显示状态控制
    var showAiTemplateCard by remember { mutableStateOf(true) }
    
    // 监听UI状态变化
    val uiState = viewModel.uiState.value
    val creationUiState = goalCreationViewModel.uiState.value
    
    // 监听模板数据
    val recentTemplates = templateViewModel.recentTemplates.value
    val popularTemplates = templateViewModel.popularTemplates.value
    val recommendedTemplates = templateViewModel.recommendedTemplates.value
    val templateCategories = templateViewModel.availableCategories.value
    
    // LaunchedEffect加载模板数据
    LaunchedEffect(Unit) {
        templateViewModel.loadRecentTemplates(3)
        templateViewModel.loadPopularTemplates(3)
        templateViewModel.loadRecommendedTemplates()
        templateViewModel.updateAvailableCategories()
    }
    
    // 添加日志输出当前UI状态
    LaunchedEffect(uiState) {
        when(uiState) {
            is GoalUiState.Idle -> android.util.Log.d("AddGoalScreen", "UI状态: Idle")
            is GoalUiState.Loading -> android.util.Log.d("AddGoalScreen", "UI状态: Loading")
            is GoalUiState.Success -> android.util.Log.d("AddGoalScreen", "UI状态: Success")
            is GoalUiState.Error -> android.util.Log.d("AddGoalScreen", "UI状态: Error - ${(uiState as GoalUiState.Error).message}")
        }
    }
    
    // 监听从模板导入界面返回的模板ID
    LaunchedEffect(Unit) {
        val templateId = navController.currentBackStackEntry?.savedStateHandle?.get<String>("selected_template_id")
        if (templateId != null) {
            // 清除savedStateHandle中的数据，防止重复加载
            navController.currentBackStackEntry?.savedStateHandle?.remove<String>("selected_template_id")
            
            // 加载模板
            templateViewModel.loadTemplateDetail(templateId)
            // 等待加载完成后设置为当前选中模板
            templateViewModel.currentTemplate.collect { template ->
                if (template != null) {
                    selectedTemplate = template
                }
            }
        }
    }
    
    // 当模板被选中时，填充表单
    LaunchedEffect(selectedTemplate) {
        selectedTemplate?.let { template ->
            // 填充表单
            if (title.isEmpty()) {
                title = template.defaultTitle
            }
            description = template.defaultDescription
            selectedPriority = template.defaultPriority
            goalType = template.goalType
            
            if (template.defaultTargetValue != null) {
                targetValue = template.defaultTargetValue.toString()
            }
            
            if (template.defaultUnit != null) {
                unit = template.defaultUnit
            }
            
            if (template.defaultDurationDays != null) {
                dueDate = LocalDateTime.now().plusDays(template.defaultDurationDays.toLong())
            }
            
            isRecurring = template.isRecurring
            recurringSettings = template.recurringSettings
            
            // 复制提醒设置
            reminderSettings.clear()
            reminderSettings.addAll(template.defaultReminderSettings)
        }
    }

    // 修改：保存后自动返回逻辑
    // 使用一个布尔值记录是否已尝试过保存操作，以及保存类型
    var hasSaveAttempted = remember { mutableStateOf(false) }
    var isBreakdownOperation = remember { mutableStateOf(false) } // 新增：标记是否是目标拆解操作
    
    LaunchedEffect(uiState) {
        // 只有当曾经尝试过保存，且当前状态为Success时才执行后续操作
        if (hasSaveAttempted.value && uiState is GoalUiState.Success) {
            // 🎯 增加延迟确保数据库操作完成
            kotlinx.coroutines.delay(500)
            
            // 获取当前创建的目标ID - 添加重试机制
            var newGoal: Goal? = null
            var retryCount = 0
            val maxRetries = 3
            
            while (newGoal == null && retryCount < maxRetries) {
                val goals = viewModel.goals.value
                newGoal = goals.find { it.title == title }
                
                if (newGoal == null) {
                    android.util.Log.w("AddGoalScreen", "第${retryCount + 1}次尝试未找到目标，重试中...")
                    retryCount++
                    kotlinx.coroutines.delay(300) // 等待300ms后重试
                    viewModel.loadGoals() // 重新加载目标列表
                }
            }
            
            if (newGoal != null) {
                if (isBreakdownOperation.value) {
                    // 如果是拆解操作，导航到目标拆解页面
                    android.util.Log.d("AddGoalScreen", "目标创建成功，导航到目标拆解页面: goalId=${newGoal.id}")
                    navController.navigate(AppDestinations.goalBreakdownRoute(newGoal.id))
                } else {
                    // 普通保存操作，返回上级页面
                    android.util.Log.d("AddGoalScreen", "目标创建成功，返回上级页面")
                    navController.popBackStack()
                }
            } else {
                // 如果多次重试后仍未找到目标，显示错误
                android.util.Log.e("AddGoalScreen", "创建目标后无法找到新目标，可能是数据同步问题")
                // 仍然尝试导航，使用时间戳作为临时方案
                if (isBreakdownOperation.value) {
                    android.util.Log.w("AddGoalScreen", "使用备用方案导航到目标拆解页面")
                    navController.navigate(AppDestinations.goalBreakdownRoute("temp_${System.currentTimeMillis()}"))
                }
            }
        }
    }
    
    // 处理智能模板推荐显示
    if (creationUiState is GoalCreationViewModel.GoalCreationUiState.TemplateRecommendation) {
        val templateState = creationUiState as GoalCreationViewModel.GoalCreationUiState.TemplateRecommendation
        SmartTemplateList(
            categorizedTemplates = templateState.categorizedTemplates,
            onTemplateSelected = { templateId ->
                goalCreationViewModel.selectTemplate(templateId)
                // 导航到模板详情或者直接使用该模板
                templateViewModel.loadTemplateDetail(templateId)
            },
            onDismiss = {
                goalCreationViewModel.switchToNormalMode()
            },
            isLoading = templateState.isLoading,
            error = templateState.error,
            modifier = Modifier.fillMaxSize()
        )
    } else {
        // 正常的目标添加界面
        
        // 使用更安全的状态栏实现
        SideEffect {
            activity?.let { 
                SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明状态栏
            }
        }
        
        activity?.let { act ->
            DisposableEffect(key1 = Unit) {
                val window = act.window
                
                // 保存原始值以在dispose时恢复
                val originalStatusBarColor = window.statusBarColor
                
                // 应用不透明状态栏设置
                SystemBarManager.forceOpaqueStatusBar(act)
                
                onDispose {
                    // 恢复原始状态栏颜色
                    window.statusBarColor = originalStatusBarColor
                    Log.d("AddGoalScreen", "AddGoalScreen disposed")
                }
            }
        }
        
        // 模板选择对话框
        if (showTemplateDialog) {
            TemplateSelectionDialog(
                onDismiss = { showTemplateDialog = false },
                onTemplateSelected = { template ->
                    selectedTemplate = template
                    showTemplateDialog = false
                },
                recentTemplates = recentTemplates,
                popularTemplates = popularTemplates,
                onBrowseAllTemplates = {
                    showTemplateDialog = false
                    navController.navigate(AppDestinations.GOAL_TEMPLATE_LIST)
                }
            )
        }
        
        // 模板库选择对话框 - 使用全屏导航替代
        LaunchedEffect(showTemplateLibraryDialog) {
            if (showTemplateLibraryDialog) {
                showTemplateLibraryDialog = false
                navController.navigate(AppDestinations.GOAL_TEMPLATE_IMPORT) {
                    launchSingleTop = true
                }
            }
        }
        
        // 保存函数
        fun saveGoalFunction() {
            Log.d("AddGoalScreen", "保存目标: $title")
            
            // 验证输入
            if (title.isBlank()) {
                // 显示错误
                Log.d("AddGoalScreen", "目标标题不能为空")
                return
            }
            
            // 设置hasSaveAttempted为true
            hasSaveAttempted.value = true
            // 标记为普通保存操作，不是目标拆解
            isBreakdownOperation.value = false
            
            // 创建目标对象
            val goal = Goal(
                title = title,
                description = description,
                startDate = startDate,
                dueDate = dueDate,
                priority = selectedPriority,
                categoryId = selectedCategoryId,
                tags = emptyList(), // 暂时清空tags，使用categoryId代替
                goalType = goalType,
                currentValue = if (currentValue.isNotBlank()) currentValue.toDoubleOrNull() ?: 0.0 else 0.0,
                targetValue = if (targetValue.isNotBlank()) targetValue.toDoubleOrNull() else null,
                unit = if (unit.isNotBlank()) unit else null,
                isRecurring = isRecurring,
                recurringSettings = recurringSettings,
                reminderSettings = reminderSettings.toList(),
                progress = 0f,
                status = "进行中", // 默认状态为"进行中"
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            
            // 保存目标
            viewModel.createGoal(
                title = goal.title,
                description = goal.description,
                startDate = goal.startDate,
                dueDate = goal.dueDate,
                priority = goal.priority,
                categoryId = goal.categoryId,
                goalType = goal.goalType,
                currentValue = goal.currentValue,
                targetValue = goal.targetValue,
                unit = goal.unit,
                reminderSettings = goal.reminderSettings,
                attachments = goal.attachments,
                isRecurring = goal.isRecurring,
                recurringSettings = goal.recurringSettings
            )
        }

        // 主界面
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF7F9FC))
                .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距，匹配ProfileScreen
        ) {
            // 顶部标题栏
            TopAppBar(
                title = {
                    Text(
                        text = "设定新目标",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    TextButton(
                        onClick = { saveGoalFunction() }
                    ) {
                        Text(
                            text = "保存",
                            fontWeight = FontWeight.Medium,
                            color = DustyLavender
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFF7F9FC), // 匹配背景色
                    titleContentColor = MaterialTheme.colorScheme.primary
                )
            )
            
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF7F9FC))
            ) {
                // 表单内容
                val scrollState = rememberScrollState()
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {

                    
                    // 添加模板选择卡片
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // 标题
                            Text(
                                text = "使用模板快速创建目标",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF333333)
                            )
                            
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // 当有选中的模板时，显示模板信息
                                if (selectedTemplate != null) {
                                    Surface(
                                        modifier = Modifier.weight(1f),
                                        shape = RoundedCornerShape(8.dp),
                                        color = getColorFromHex(selectedTemplate?.colorHex ?: "#9370DB")?.copy(alpha = 0.1f)
                                            ?: DustyLavender.copy(alpha = 0.1f),
                                        border = BorderStroke(
                                            width = 1.dp,
                                            color = getColorFromHex(selectedTemplate?.colorHex ?: "#9370DB")
                                                ?: DustyLavender
                                        )
                                    ) {
                                        Row(
                                            modifier = Modifier
                                                .padding(horizontal = 12.dp, vertical = 8.dp)
                                                .fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Box(
                                                modifier = Modifier
                                                    .size(32.dp)
                                                    .clip(CircleShape)
                                                    .background(
                                                        getColorFromHex(selectedTemplate?.colorHex ?: "#9370DB")
                                                            ?: DustyLavender
                                                    ),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Outlined.Assignment, 
                                                    contentDescription = null,
                                                    tint = Color.White,
                                                    modifier = Modifier.size(16.dp)
                                                )
                                            }
                                            
                                            Spacer(modifier = Modifier.width(8.dp))
                                            
                                            Column(modifier = Modifier.weight(1f)) {
                                                Text(
                                                    text = selectedTemplate?.name ?: "",
                                                    fontSize = 14.sp,
                                                    fontWeight = FontWeight.Medium,
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                                
                                                Text(
                                                    text = selectedTemplate?.category ?: "",
                                                    fontSize = 12.sp,
                                                    color = Color(0xFF666666),
                                                    maxLines = 1
                                                )
                                            }
                                            
                                            IconButton(
                                                onClick = { selectedTemplate = null },
                                                modifier = Modifier.size(24.dp)
                                            ) {
                                                Icon(
                                                    imageVector = Icons.Default.Close,
                                                    contentDescription = "清除模板",
                                                    tint = Color(0xFF666666),
                                                    modifier = Modifier.size(16.dp)
                                                )
                                            }
                                        }
                                    }
                                } else {
                                    OutlinedButton(
                                        onClick = { showTemplateDialog = true },
                                        modifier = Modifier.weight(1f),
                                        shape = RoundedCornerShape(8.dp),
                                        colors = ButtonDefaults.outlinedButtonColors(
                                            contentColor = DustyLavender
                                        )
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Assignment,
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(text = "选择模板")
                                    }
                                }
                                
                                // 浏览按钮，修改为模板库入口
                                IconButton(
                                    onClick = { showTemplateLibraryDialog = true }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.FileDownload,
                                        contentDescription = "浏览模板库",
                                        tint = DustyLavender
                                    )
                                }
                            }
                            
                            // 新增：快速导入模板分类选择
                            AnimatedVisibility(
                                visible = selectedTemplate == null && templateCategories.isNotEmpty(),
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = "快速导入模板",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color.Gray,
                                        modifier = Modifier.padding(vertical = 8.dp)
                                    )
                                    
                                    LazyRow(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        items(templateCategories) { category ->
                                            CategoryChip(
                                                category = category,
                                                onClick = {
                                                    // 导航到模板导入页面，预先过滤该类别
                                                    navController.navigate("${AppDestinations.GOAL_TEMPLATE_IMPORT}?category=$category") {
                                                        launchSingleTop = true
                                                    }
                                                }
                                            )
                                        }
                                        
                                        item {
                                            Surface(
                                                shape = RoundedCornerShape(16.dp),
                                                color = Color.Transparent,
                                                border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.5f)),
                                                modifier = Modifier
                                                    .clickable {
                                                        navController.navigate(AppDestinations.GOAL_TEMPLATE_IMPORT) {
                                                            launchSingleTop = true
                                                        }
                                                    }
                                            ) {
                                                Row(
                                                    verticalAlignment = Alignment.CenterVertically,
                                                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                                                ) {
                                                    Icon(
                                                        imageVector = Icons.Default.GridView,
                                                        contentDescription = null,
                                                        tint = DustyLavender,
                                                        modifier = Modifier.size(16.dp)
                                                    )
                                                    
                                                    Spacer(modifier = Modifier.width(4.dp))
                                                    
                                                    Text(
                                                        text = "浏览全部",
                                                        fontSize = 12.sp,
                                                        color = DustyLavender
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 目标表单
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // 标题输入
                            Column {
                                Text(
                                    text = "目标标题",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                OutlinedTextField(
                                    value = title,
                                    onValueChange = { title = it },
                                    modifier = Modifier.fillMaxWidth(),
                                    placeholder = { 
                                        Text(
                                            "输入目标标题，例如：学习Python基础",
                                            color = Color(0xFFBBBBBB)
                                        ) 
                                    },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = DustyLavender,
                                        unfocusedBorderColor = Color(0xFFE2E8F0),
                                        cursorColor = DustyLavender
                                    ),
                                    singleLine = true,
                                    maxLines = 1
                                )
                            }
                            
                            // 描述输入
                            Column {
                                Text(
                                    text = "目标描述",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                OutlinedTextField(
                                    value = description,
                                    onValueChange = { description = it },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(100.dp),
                                    placeholder = { 
                                        Text(
                                            "描述这个目标的更多细节（可选）",
                                            color = Color(0xFFBBBBBB)
                                        ) 
                                    },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = DustyLavender,
                                        unfocusedBorderColor = Color(0xFFE2E8F0),
                                        cursorColor = DustyLavender
                                    )
                                )
                            }
                            
                            // 日期选择
                            Column {
                                Text(
                                    text = "时间设置",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 开始日期选择
                                DatePickerItem(
                                    date = startDate,
                                    icon = Icons.Default.PlayArrow,
                                    title = "开始日期",
                                    subtitle = if (startDate != null) {
                                        startDate!!.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                                    } else {
                                        "点击设置开始日期"
                                    },
                                    onDateSelected = { startDate = it }
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 截止日期选择
                                DatePickerItem(
                                    date = dueDate,
                                    icon = Icons.Outlined.CalendarMonth,
                                    title = "截止日期",
                                    subtitle = if (dueDate != null) {
                                        dueDate!!.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                                    } else {
                                        "点击设置截止日期"
                                    },
                                    onDateSelected = { dueDate = it }
                                )
                                
                                // 提示信息
                                AnimatedVisibility(
                                    visible = startDate != null && dueDate != null && 
                                             startDate!!.isAfter(dueDate)
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(top = 8.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Warning,
                                            contentDescription = null,
                                            tint = MaterialTheme.colorScheme.error,
                                            modifier = Modifier.size(16.dp)
                                        )
                                        
                                        Spacer(modifier = Modifier.width(4.dp))
                                        
                                        Text(
                                            text = "开始日期不应晚于截止日期",
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.error
                                        )
                                    }
                                }
                            }
                            
                            // 优先级选择
                            Column {
                                Text(
                                    text = "优先级",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    PriorityOption(
                                        priority = GoalPriority.LOW,
                                        selected = selectedPriority == GoalPriority.LOW,
                                        onClick = { selectedPriority = GoalPriority.LOW }
                                    )
                                    PriorityOption(
                                        priority = GoalPriority.MEDIUM,
                                        selected = selectedPriority == GoalPriority.MEDIUM,
                                        onClick = { selectedPriority = GoalPriority.MEDIUM }
                                    )
                                    PriorityOption(
                                        priority = GoalPriority.HIGH,
                                        selected = selectedPriority == GoalPriority.HIGH,
                                        onClick = { selectedPriority = GoalPriority.HIGH }
                                    )
                                    PriorityOption(
                                        priority = GoalPriority.URGENT,
                                        selected = selectedPriority == GoalPriority.URGENT,
                                        onClick = { selectedPriority = GoalPriority.URGENT }
                                    )
                                }
                            }

                            // 目标分类选择
                            Column {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "目标分类",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = MaterialTheme.colorScheme.primary
                                    )

                                    // 快速智能分类按钮
                                    QuickSmartCategoryButton(
                                        title = title,
                                        description = description,
                                        onCategorySelected = { categoryId ->
                                            selectedCategoryId = categoryId
                                        }
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                // 智能分类推荐
                                SmartCategoryRecommendation(
                                    title = title,
                                    description = description,
                                    selectedCategoryId = selectedCategoryId,
                                    onCategorySelected = { categoryId ->
                                        selectedCategoryId = categoryId
                                    }
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                // 手动分类选择
                                Text(
                                    text = "或手动选择分类",
                                    fontSize = 11.sp,
                                    color = Color(0xFF6B7280),
                                    modifier = Modifier.padding(bottom = 4.dp)
                                )

                                GoalCategorySelector(
                                    selectedCategoryId = selectedCategoryId,
                                    onCategorySelected = { categoryId ->
                                        selectedCategoryId = categoryId
                                    },
                                    compactMode = true,
                                    showAllCategories = true
                                )
                            }

                            // 目标类型选择
                            Column {
                                Text(
                                    text = "目标类型",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                                ) {
                                    GoalTypeOption(
                                        type = GoalType.NUMERIC,
                                        selectedType = goalType,
                                        onSelect = { goalType = GoalType.NUMERIC },
                                        modifier = Modifier.weight(1f)
                                    )
                                    GoalTypeOption(
                                        type = GoalType.BOOLEAN,
                                        selectedType = goalType,
                                        onSelect = { goalType = GoalType.BOOLEAN },
                                        modifier = Modifier.weight(1f)
                                    )
                                }
                            }
                            
                            // 量化设置 (仅当选择数值型目标时显示)
                            AnimatedVisibility(visible = goalType == GoalType.NUMERIC) {
                                Column {
                                    Text(
                                        text = "量化设置",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = Color(0xFF333333)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(8.dp))
                                    
                                    Card(
                                        modifier = Modifier.fillMaxWidth(),
                                        shape = RoundedCornerShape(8.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = Color(0xFFF8FAFC)
                                        ),
                                        elevation = CardDefaults.cardElevation(
                                            defaultElevation = 0.dp
                                        )
                                    ) {
                                        Column(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(16.dp),
                                            verticalArrangement = Arrangement.spacedBy(16.dp)
                                        ) {
                                            // 当前值和目标值
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                                            ) {
                                                // 当前值输入
                                                Column(modifier = Modifier.weight(1f)) {
                                                    Text(
                                                        text = "当前值",
                                                        fontSize = 14.sp,
                                                        color = Color(0xFF666666)
                                                    )
                                                    
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                    
                                                    OutlinedTextField(
                                                        value = currentValue,
                                                        onValueChange = { 
                                                            if (it.isEmpty() || it.toDoubleOrNull() != null) {
                                                                currentValue = it 
                                                            }
                                                        },
                                                        modifier = Modifier.fillMaxWidth(),
                                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                        shape = RoundedCornerShape(8.dp),
                                                        colors = OutlinedTextFieldDefaults.colors(
                                                            focusedBorderColor = DustyLavender,
                                                            unfocusedBorderColor = Color(0xFFE2E8F0),
                                                            cursorColor = DustyLavender
                                                        ),
                                                        singleLine = true,
                                                        maxLines = 1
                                                    )
                                                }
                                                
                                                // 目标值输入
                                                Column(modifier = Modifier.weight(1f)) {
                                                    Text(
                                                        text = "目标值",
                                                        fontSize = 14.sp,
                                                        color = Color(0xFF666666)
                                                    )
                                                    
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                    
                                                    OutlinedTextField(
                                                        value = targetValue,
                                                        onValueChange = { 
                                                            if (it.isEmpty() || it.toDoubleOrNull() != null) {
                                                                targetValue = it 
                                                            }
                                                        },
                                                        modifier = Modifier.fillMaxWidth(),
                                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                        shape = RoundedCornerShape(8.dp),
                                                        colors = OutlinedTextFieldDefaults.colors(
                                                            focusedBorderColor = DustyLavender,
                                                            unfocusedBorderColor = Color(0xFFE2E8F0),
                                                            cursorColor = DustyLavender
                                                        ),
                                                        singleLine = true,
                                                        maxLines = 1
                                                    )
                                                }
                                            }
                                            
                                            // 单位和进度
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                                            ) {
                                                // 单位输入
                                                Column(modifier = Modifier.weight(1f)) {
                                                    Text(
                                                        text = "单位",
                                                        fontSize = 14.sp,
                                                        color = Color(0xFF666666)
                                                    )
                                                    
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                    
                                                    OutlinedTextField(
                                                        value = unit,
                                                        onValueChange = { unit = it },
                                                        modifier = Modifier.fillMaxWidth(),
                                                        placeholder = { 
                                                            Text(
                                                                "如：kg、元、次",
                                                                color = Color(0xFFBBBBBB)
                                                            ) 
                                                        },
                                                        shape = RoundedCornerShape(8.dp),
                                                        colors = OutlinedTextFieldDefaults.colors(
                                                            focusedBorderColor = DustyLavender,
                                                            unfocusedBorderColor = Color(0xFFE2E8F0),
                                                            cursorColor = DustyLavender
                                                        ),
                                                        singleLine = true,
                                                        maxLines = 1
                                                    )
                                                }
                                                
                                                // 进度显示
                                                Column(modifier = Modifier.weight(1f)) {
                                                    Text(
                                                        text = "当前进度",
                                                        fontSize = 14.sp,
                                                        color = Color(0xFF666666)
                                                    )
                                                    
                                                    Spacer(modifier = Modifier.height(4.dp))
                                                    
                                                    // 计算当前进度百分比
                                                    val progressPercent = remember(currentValue, targetValue) {
                                                        try {
                                                            val current = currentValue.toDoubleOrNull() ?: 0.0
                                                            val target = targetValue.toDoubleOrNull() ?: 1.0
                                                            if (target == 0.0) 0 else ((current / target) * 100).toInt().coerceIn(0, 100)
                                                        } catch (e: Exception) {
                                                            0
                                                        }
                                                    }
                                                    
                                                    LinearProgressIndicator(
                                                        progress = { progressPercent / 100f },
                                                        modifier = Modifier
                                                            .fillMaxWidth()
                                                            .height(48.dp)
                                                            .clip(RoundedCornerShape(8.dp)),
                                                        color = DustyLavender,
                                                        trackColor = DustyLavender.copy(alpha = 0.2f)
                                                    )
                                                    
                                                    Text(
                                                        text = "$progressPercent%",
                                                        fontSize = 12.sp,
                                                        color = Color(0xFF666666),
                                                        textAlign = TextAlign.End,
                                                        modifier = Modifier.fillMaxWidth()
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 周期性设置选项
                            RecurringSettingsPanel(
                                isRecurring = isRecurring,
                                onRecurringChanged = { isRecurring = it },
                                recurringSettings = recurringSettings,
                                onSettingsChanged = { recurringSettings = it }
                            )
                            
                            // 提醒设置
                            ReminderSettingsPanel(
                                reminderSettings = reminderSettings,
                                onReminderSettingsChanged = { newSettings ->
                                    reminderSettings.clear()
                                    reminderSettings.addAll(newSettings)
                                }
                            )
                            
                            // 附件区域
                            Column {
                                Text(
                                    text = "附件",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF333333)
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 上传按钮
                                Button(
                                    onClick = {
                                        // 这里实现文件上传功能
                                        // 暂时模拟添加一个示例附件
                                        attachments.add("attachment_${System.currentTimeMillis()}.pdf")
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFFF0F4FF),
                                        contentColor = DustyLavender
                                    ),
                                    shape = RoundedCornerShape(8.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Outlined.Upload,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("上传附件")
                                }
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 附件列表
                                AnimatedVisibility(visible = attachments.isNotEmpty()) {
                                    Column(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        attachments.forEachIndexed { index, attachment ->
                                            AttachmentItem(
                                                fileName = attachment,
                                                onDelete = { attachments.removeAt(index) }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // AI推荐目标模板
                    AnimatedVisibility(visible = showAiTemplateCard) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color.White
                            ),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 2.dp
                            )
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(24.dp)
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(bottom = 16.dp),
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Lightbulb,
                                            contentDescription = null,
                                            tint = Color(0xFFF59E0B),
                                            modifier = Modifier.size(20.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                                                Text(
                            text = "AI推荐目标模板",
                            fontSize = 13.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF333333)
                        )
                                    }
                                    
                                    // 添加关闭按钮
                                    IconButton(
                                        onClick = { showAiTemplateCard = false },
                                        modifier = Modifier.size(28.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = "关闭模板卡片",
                                            tint = Color(0xFF999999),
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                                
                                // 模板网格
                                Column(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    // 学习新技能模板
                                    TemplateCard(
                                        icon = Icons.Default.LaptopChromebook,
                                        iconBgColor = Color(0xFFDDEAFF),
                                        iconColor = Color(0xFF3B82F6),
                                        title = "学习新技能",
                                        description = "3个月内掌握[技能]，每周投入[时间]",
                                        onClick = { 
                                            title = "3个月内掌握编程基础";
                                            description = "通过系统学习，掌握Python编程基础知识，每周投入10小时学习时间，完成至少一个小项目。"
                                        }
                                    )
                                    
                                    // 健身计划模板
                                    TemplateCard(
                                        icon = Icons.Default.DirectionsRun,
                                        iconBgColor = Color(0xFFD1FAE5),
                                        iconColor = Color(0xFF10B981),
                                        title = "健身计划",
                                        description = "每周[次数][运动类型]，每次[时间]",
                                        onClick = { 
                                            title = "建立规律健身习惯";
                                            description = "每周进行3次力量训练和2次有氧运动，每次45分钟，三个月内减脂5kg并增加肌肉线条。"
                                        }
                                    )
                                    
                                    // 阅读计划模板
                                    TemplateCard(
                                        icon = Icons.Default.MenuBook,
                                        iconBgColor = Color(0xFFF3E8FF),
                                        iconColor = Color(0xFF9333EA),
                                        title = "阅读计划",
                                        description = "每月阅读[数量]本书，主题为[类型]",
                                        onClick = { 
                                            title = "培养阅读习惯";
                                            description = "每月阅读2本商业管理类书籍，做好读书笔记，并在工作中实践所学知识点。" 
                                        }
                                    )
                                    
                                    // 职业发展模板
                                    TemplateCard(
                                        icon = Icons.Default.Business,
                                        iconBgColor = Color(0xFFFEE2E2),
                                        iconColor = Color(0xFFEF4444),
                                        title = "职业发展",
                                        description = "[时间]内完成[证书/项目]，提升[技能]",
                                        onClick = { 
                                            title = "获取专业资格认证";
                                            description = "6个月内完成项目管理专业认证(PMP)考试，提升项目管理和团队协作能力。"
                                        }
                                    )
                                }
                            }
                        }
                    }
                    
                    // 操作按钮
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 24.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Button(
                                onClick = {
                                    navController.popBackStack()
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFFE2E8F0)
                                ),
                                shape = RoundedCornerShape(8.dp),
                                contentPadding = PaddingValues(horizontal = 20.dp, vertical = 12.dp)
                            ) {
                                Text(
                                    text = "取消",
                                    fontSize = 12.sp,
                                    color = Color(0xFF4A5568)
                                )
                            }
                            
                            Button(
                                onClick = {
                                    try {
                                        hasSaveAttempted.value = true  // 标记已尝试保存
                                        isBreakdownOperation.value = true  // 标记为目标拆解操作
                                        
                                        // 计算进度
                                        val calculatedProgress = if (goalType == GoalType.NUMERIC) {
                                            try {
                                                val current = currentValue.toDoubleOrNull() ?: 0.0
                                                val target = targetValue.toDoubleOrNull() ?: 1.0
                                                if (target == 0.0) 0f else (current / target).toFloat().coerceIn(0f, 1f)
                                            } catch (e: Exception) {
                                                0f
                                            }
                                        } else {
                                            0f
                                        }
                                        
                                        // 如果使用了模板，记录该模板的使用
                                        selectedTemplate?.let { template ->
                                            templateViewModel.recordTemplateUsage(template.id)
                                        }
                                        
                                        // 创建目标
                                        viewModel.createGoal(
                                            title = title,
                                            description = description,
                                            startDate = startDate,
                                            dueDate = dueDate,
                                            priority = selectedPriority,
                                            categoryId = selectedCategoryId,
                                            goalType = goalType,
                                            currentValue = currentValue.toDoubleOrNull(),
                                            targetValue = targetValue.toDoubleOrNull(),
                                            unit = unit.takeIf { it.isNotBlank() },
                                            reminderSettings = reminderSettings.toList(),
                                            attachments = attachments.toList(),
                                            isRecurring = isRecurring,
                                            recurringSettings = recurringSettings
                                        )
                                        
                                        // 🎯 注意：不在这里直接导航，而是等待目标创建成功后再导航
                                        // 导航逻辑已移至LaunchedEffect中处理，确保使用真实的目标ID
                                    } catch (e: Exception) {
                                        // 捕获可能的异常
                                        android.util.Log.e("AddGoalScreen", "创建目标失败", e)
                                    }
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = DustyLavender
                                ),
                                enabled = title.isNotBlank(),
                                shape = RoundedCornerShape(8.dp),
                                contentPadding = PaddingValues(horizontal = 20.dp, vertical = 12.dp)
                            ) {
                                Text(
                                    text = "下一步：目标拆解",
                                    fontSize = 12.sp
                                )
                            }
                            
                            // 分享按钮
                            Button(
                                onClick = {
                                    // 实现分享功能
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF6B7280)
                                ),
                                shape = RoundedCornerShape(8.dp),
                                contentPadding = PaddingValues(horizontal = 20.dp, vertical = 12.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Share,
                                    contentDescription = "分享",
                                    modifier = Modifier.size(16.dp)
                                )
                                // 删除"分享"文字，只保留图标
                            }
                        }
                    }
                }
                
                // 错误提示
                if (uiState is GoalUiState.Error) {
                    Snackbar(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(16.dp),
                        containerColor = Color(0xFFFEECAD),
                        contentColor = Color(0xFF333333),
                    ) {
                        Text((uiState as GoalUiState.Error).message)
                    }
                }
                
                // 加载指示器
                if (uiState is GoalUiState.Loading) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color(0x80FFFFFF)),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = DustyLavender)
                    }
                }
            }
        }
    }
}

@Composable
fun PriorityButton(
    label: String,
    subLabel: String,
    color: Color,
    bgColor: Color,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick)
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) color else Color(0xFFE0E0E0),
                shape = RoundedCornerShape(8.dp)
            ),
        color = bgColor.copy(alpha = if (isSelected) 0.3f else 0.1f)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = label,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                color = color
            )
            
            Text(
                text = subLabel,
                fontSize = 12.sp,
                color = color.copy(alpha = 0.8f)
            )
        }
    }
}

@Composable
fun CategoryChip(
    category: String,
    onClick: () -> Unit
) {
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = Color.Transparent,
        border = BorderStroke(1.dp, DustyLavender.copy(alpha = 0.5f)),
        modifier = Modifier
            .clickable(onClick = onClick)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        ) {
            // 为不同类别选择不同图标
            val icon = when(category.lowercase()) {
                "工作" -> Icons.Default.Work
                "学习" -> Icons.Default.School
                "健康" -> Icons.Default.FitnessCenter
                "个人成长" -> Icons.Default.SelfImprovement
                "财务" -> Icons.Default.Money
                "关系" -> Icons.Default.People
                "休闲" -> Icons.Default.Hiking
                else -> Icons.Default.Category
            }
            
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(4.dp))
            
            Text(
                text = category,
                fontSize = 12.sp,
                color = DustyLavender
            )
        }
    }
}

@Composable
fun TemplateCard(
    icon: ImageVector,
    iconBgColor: Color,
    iconColor: Color,
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // 顶部彩条
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .background(
                        Brush.horizontalGradient(
                            colors = listOf(
                                iconBgColor,
                                iconBgColor.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 图标
                Box(
                    modifier = Modifier
                        .size(42.dp)
                        .clip(CircleShape)
                        .background(iconBgColor.copy(alpha = 0.15f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = iconColor,
                        modifier = Modifier.size(22.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 文本
                Column {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
    }
}

// 日期选择器项
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DatePickerItem(
    date: LocalDateTime?,
    icon: ImageVector,
    title: String,
    subtitle: String,
    onDateSelected: (LocalDateTime) -> Unit
) {
    var showDatePicker by remember { mutableStateOf(false) }
    
    // 日期选择对话框
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = date?.atZone(java.time.ZoneId.systemDefault())
                ?.toInstant()?.toEpochMilli()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(onClick = {
                    // 获取选择的日期
                    datePickerState.selectedDateMillis?.let { millis ->
                        val selectedDate = java.time.Instant.ofEpochMilli(millis)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDateTime()
                        onDateSelected(selectedDate)
                    }
                    showDatePicker = false
                }) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("取消")
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                usePlatformDefaultWidth = false // 使用自定义宽度
            ),
            modifier = Modifier
                .fillMaxWidth(0.95f) // 设置对话框宽度为屏幕的95%
                .padding(16.dp),
            colors = DatePickerDefaults.colors(
                containerColor = Color.White,
                titleContentColor = DustyLavender,
                headlineContentColor = Color(0xFF333333),
                weekdayContentColor = Color(0xFF666666),
                dayContentColor = Color(0xFF333333),
                selectedDayContainerColor = DustyLavender,
                todayContentColor = DustyLavender,
                todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
            )
        ) {
            DatePicker(
                state = datePickerState,
                title = { 
                    Text(
                        text = "选择日期", 
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(start = 16.dp, top = 16.dp)
                    ) 
                },
                headline = { 
                    Text(
                        text = datePickerState.selectedDateMillis?.let {
                            val selectedDate = java.time.Instant.ofEpochMilli(it)
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate()
                            selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                        } ?: "未选择日期",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                showModeToggle = false,
                colors = DatePickerDefaults.colors(
                    containerColor = Color.White,
                    titleContentColor = DustyLavender,
                    headlineContentColor = Color(0xFF333333),
                    weekdayContentColor = Color(0xFF666666),
                    dayContentColor = Color(0xFF333333),
                    selectedDayContainerColor = DustyLavender,
                    todayContentColor = DustyLavender,
                    todayDateBorderColor = DustyLavender.copy(alpha = 0.5f)
                )
            )
        }
    }
    
    // 日期选择按钮
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable { showDatePicker = true },
        color = Color(0xFFF8FAFC),
        tonalElevation = 0.dp,
        shadowElevation = 0.dp
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标容器
            Box(
                modifier = Modifier
                    .size(44.dp)
                    .clip(CircleShape)
                    .background(
                        color = DustyLavender.copy(alpha = 0.7f) // 取消渐变，使用纯色
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 文本内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = if (date != null) Color(0xFF333333) else Color(0xFF888888),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 清除按钮（仅当有日期时显示）
            AnimatedVisibility(visible = date != null) {
                IconButton(
                    onClick = { onDateSelected(LocalDateTime.now()) },
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "清除日期",
                        tint = Color(0xFF888888),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            IconButton(
                onClick = { showDatePicker = true },
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "选择日期",
                    tint = Color(0xFF888888)
                )
            }
        }
    }
}

// 优先级选项组件
@Composable
private fun PriorityOption(
    priority: GoalPriority,
    selected: Boolean,
    onClick: () -> Unit
) {
    val color = when (priority) {
        GoalPriority.LOW -> Color(0xFF4CAF50)
        GoalPriority.MEDIUM -> Color(0xFFFFC107)
        GoalPriority.HIGH -> Color(0xFFF44336)
        GoalPriority.URGENT -> Color(0xFFDC2626)
    }
    
    val text = when (priority) {
        GoalPriority.LOW -> "低"
        GoalPriority.MEDIUM -> "中"
        GoalPriority.HIGH -> "高"
        GoalPriority.URGENT -> "紧急"
    }
    
    Surface(
        modifier = Modifier
            .width(80.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(8.dp),
        color = if (selected) color.copy(alpha = 0.1f) else Color.White
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(vertical = 12.dp, horizontal = 8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
                    .background(color),
                contentAlignment = Alignment.Center
            ) {
                if (selected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = text,
                fontSize = 12.sp,
                color = if (selected) color else Color.Gray,
                fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
}

// 附件项组件
@Composable
private fun AttachmentItem(
    fileName: String,
    onDelete: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFF8FAFC))
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Description,
            contentDescription = null,
            tint = DustyLavender,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = fileName,
            fontSize = 14.sp,
            color = Color(0xFF333333),
            modifier = Modifier.weight(1f)
        )
        
        IconButton(
            onClick = onDelete,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "删除",
                tint = Color(0xFFAAAAAA),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// 目标类型选项
@Composable
private fun GoalTypeOption(
    type: GoalType,
    selectedType: GoalType,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isSelected = type == selectedType
    
    val typeText = when (type) {
        GoalType.NUMERIC -> "数值型"
        GoalType.BOOLEAN -> "布尔型"
    }
    
    val typeIcon = when (type) {
        GoalType.NUMERIC -> Icons.Outlined.Numbers
        GoalType.BOOLEAN -> Icons.Outlined.Check
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onSelect
            )
            .background(
                if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color(0xFFF8FAFC)
            )
            .border(
                width = 1.dp,
                color = if (isSelected) DustyLavender else Color(0xFFE2E8F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = typeIcon,
                contentDescription = null,
                tint = if (isSelected) DustyLavender else Color(0xFF666666),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = typeText,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                color = if (isSelected) DustyLavender else Color(0xFF666666)
            )
        }
    }
}

/**
 * 模板选择对话框
 */
@Composable
private fun TemplateSelectionDialog(
    onDismiss: () -> Unit,
    onTemplateSelected: (GoalTemplate) -> Unit,
    recentTemplates: List<GoalTemplate>,
    popularTemplates: List<GoalTemplate>,
    onBrowseAllTemplates: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text(
                    text = "选择目标模板",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    letterSpacing = (-0.5).sp
                )
                
                Spacer(modifier = Modifier.height(2.dp))
                
                Text(
                    text = "从预定义的模板快速创建目标",
                    fontSize = 12.sp,
                    color = Color(0xFF666666),
                    letterSpacing = (-0.2).sp
                )
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.heightIn(max = 450.dp)
            ) {
                // 最近使用的模板
                if (recentTemplates.isNotEmpty()) {
                    Text(
                        text = "最近使用",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333)
                    )
                    
                    LazyColumn(
                        modifier = Modifier.height(180.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(recentTemplates) { template ->
                            TemplateListItem(
                                template = template,
                                onClick = { onTemplateSelected(template) }
                            )
                        }
                    }
                }
                
                // 常用模板
                if (popularTemplates.isNotEmpty()) {
                    Text(
                        text = "常用模板",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333)
                    )
                    
                    LazyColumn(
                        modifier = Modifier.height(180.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(popularTemplates) { template ->
                            TemplateListItem(
                                template = template,
                                onClick = { onTemplateSelected(template) }
                            )
                        }
                    }
                }
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 本地模板按钮
                    Button(
                        onClick = onBrowseAllTemplates,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = DustyLavender
                        )
                    ) {
                        Icon(Icons.Default.ListAlt, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("本地模板")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 13.sp, letterSpacing = (-0.2).sp)
            }
        },
        properties = DialogProperties(dismissOnClickOutside = true),
        containerColor = Color.White
    )
}

/**
 * 新增：模板库对话框
 */
@Composable
private fun TemplateLibraryDialog(
    onDismiss: () -> Unit,
    onTemplateSelected: (GoalTemplate) -> Unit,
    recommendedTemplates: List<GoalTemplate>,
    categories: List<String>
) {
    var selectedCategory by remember { mutableStateOf<String?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    
    // 过滤模板
    val filteredTemplates = remember(recommendedTemplates, selectedCategory, searchQuery) {
        recommendedTemplates.filter { template ->
            (selectedCategory == null || template.category == selectedCategory) &&
            (searchQuery.isEmpty() || 
             template.name.contains(searchQuery, ignoreCase = true) ||
             template.description.contains(searchQuery, ignoreCase = true))
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text(
                    text = "模板库",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    letterSpacing = (-0.5).sp
                )
                
                Spacer(modifier = Modifier.height(2.dp))
                
                Text(
                    text = "浏览并导入精选模板",
                    fontSize = 12.sp,
                    color = Color(0xFF666666),
                    letterSpacing = (-0.2).sp
                )
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 500.dp)
            ) {
                // 搜索栏
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { 
                        Text(
                            "搜索模板...",
                            color = Color(0xFFBBBBBB)
                        ) 
                    },
                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
                    singleLine = true,
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        unfocusedBorderColor = Color(0xFFE2E8F0)
                    )
                )
                
                // 分类筛选横向滚动
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    item {
                        TemplateCategoryChip(
                            category = "全部",
                            isSelected = selectedCategory == null,
                            onClick = { selectedCategory = null }
                        )
                    }
                    
                    items(categories) { category ->
                        TemplateCategoryChip(
                            category = category,
                            isSelected = selectedCategory == category,
                            onClick = { selectedCategory = category }
                        )
                    }
                }
                
                // 模板列表
                Text(
                    text = if (selectedCategory != null) "「${selectedCategory}」分类模板" else "推荐模板",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                if (filteredTemplates.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "没有找到符合条件的模板",
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp)
                    ) {
                        items(filteredTemplates) { template ->
                            TemplateLibraryItem(
                                template = template,
                                onImport = { onTemplateSelected(template) }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭", fontSize = 13.sp, letterSpacing = (-0.2).sp)
            }
        },
        properties = DialogProperties(dismissOnClickOutside = true),
        containerColor = Color.White
    )
}

/**
 * 新增：模板列表项
 */
@Composable
private fun TemplateListItem(
    template: GoalTemplate,
    onClick: () -> Unit
) {
    val templateColor = remember(template.colorHex) {
        getColorFromHex(template.colorHex) ?: DustyLavender
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 模板图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(templateColor.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Assignment,
                    contentDescription = null,
                    tint = templateColor,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 模板信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = template.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                if (template.category.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(4.dp))
                                .background(templateColor.copy(alpha = 0.15f))
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        ) {
                            Text(
                                text = template.category,
                                fontSize = 12.sp,
                                color = templateColor,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        if (template.usageCount > 0) {
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = null,
                                tint = Color.Gray,
                                modifier = Modifier.size(14.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(2.dp))
                            
                            Text(
                                text = "${template.usageCount}次",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 箭头图标
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "选择",
                tint = Color.Gray,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 新增：模板库项目
 */
@Composable
private fun TemplateLibraryItem(
    template: GoalTemplate,
    onImport: () -> Unit
) {
    val templateColor = remember(template.colorHex) {
        getColorFromHex(template.colorHex) ?: DustyLavender
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.3f))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 模板图标
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(templateColor.copy(alpha = 0.2f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Assignment,
                        contentDescription = null,
                        tint = templateColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 模板信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = template.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 分类标签
                        if (template.category.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .clip(RoundedCornerShape(4.dp))
                                    .background(templateColor.copy(alpha = 0.15f))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = template.category,
                                    fontSize = 12.sp,
                                    color = templateColor,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = template.description,
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 导入按钮
                IconButton(onClick = onImport) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "导入模板",
                        tint = Color(0xFF4285F4)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 目标详情预览
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(0xFFF8F9FA))
                    .padding(8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "目标标题",
                        fontSize = 12.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        text = template.defaultTitle,
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    )
                }
                
                if (template.defaultDurationDays != null) {
                    Column {
                        Text(
                            text = "预期天数",
                            fontSize = 12.sp,
                            color = Color(0xFF666666)
                        )
                        Text(
                            text = "${template.defaultDurationDays}天",
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        )
                    }
                }
                
                if (template.goalType == GoalType.NUMERIC && template.defaultTargetValue != null) {
                    Column {
                        Text(
                            text = "目标数值",
                            fontSize = 12.sp,
                            color = Color(0xFF666666)
                        )
                        Text(
                            text = "${template.defaultTargetValue}${template.defaultUnit ?: ""}",
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 新增：分类选择芯片
 */
@Composable
private fun TemplateCategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) DustyLavender else Color(0xFFF0F0F0),
        border = if (isSelected) null else BorderStroke(1.dp, Color(0xFFE0E0E0))
    ) {
        Text(
            text = category,
            color = if (isSelected) Color.White else Color(0xFF666666),
            fontSize = 14.sp,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
} 