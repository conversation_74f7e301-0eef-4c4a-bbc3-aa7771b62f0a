package com.timeflow.app.initializer

import android.content.Context
import androidx.startup.Initializer
import com.timeflow.app.service.RecurringTaskManager
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent

/**
 * 循环任务初始化器
 * 在应用启动时自动初始化循环任务管理器
 */
class RecurringTaskInitializer : Initializer<RecurringTaskManager> {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface RecurringTaskInitializerEntryPoint {
        fun recurringTaskManager(): RecurringTaskManager
    }

    override fun create(context: Context): RecurringTaskManager {
        val entryPoint = EntryPointAccessors.fromApplication(
            context.applicationContext,
            RecurringTaskInitializerEntryPoint::class.java
        )
        
        val recurringTaskManager = entryPoint.recurringTaskManager()
        recurringTaskManager.initialize()
        
        return recurringTaskManager
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
