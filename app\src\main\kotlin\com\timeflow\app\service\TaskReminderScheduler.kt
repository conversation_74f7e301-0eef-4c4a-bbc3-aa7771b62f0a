package com.timeflow.app.service

import android.content.Context
import android.util.Log
import androidx.work.Data
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.TimeFlowNotificationManager
import com.timeflow.app.utils.TaskReminderUtils
import com.timeflow.app.worker.TaskReminderWorker
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.runBlocking
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

// 通知设置DataStore - 与NotificationSettingsViewModel保持一致
private val Context.notificationDataStore by preferencesDataStore(name = "notification_settings")

/**
 * 任务提醒调度服务
 * 负责管理任务提醒的调度、取消和检查
 */
@Singleton
class TaskReminderScheduler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository,
    private val notificationManager: TimeFlowNotificationManager
) {
    
    companion object {
        private const val TAG = "TaskReminderScheduler"
    }
    
    /**
     * 为任务设置提醒
     * 新逻辑：基于开始时间提醒，而不是截止时间前提醒
     */
    suspend fun scheduleTaskReminder(task: Task) {
        try {
            Log.d(TAG, "为任务设置提醒: ${task.title}")
            
            // 先取消现有的提醒
            cancelTaskReminders(task.id)
            
            // 优先使用开始时间设置提醒
            if (task.startDate != null && task.startDate.isAfter(LocalDateTime.now())) {
                Log.d(TAG, "基于开始时间设置任务提醒: ${task.title} at ${task.startDate}")
                scheduleReminder(
                    taskId = task.id,
                    taskTitle = task.title,
                    reminderTime = task.startDate,
                    priority = getPriorityString(task.priority),
                    reminderType = "task_start_reminder"
                )
            }
            // 如果没有开始时间但有手动设置的提醒时间，使用提醒时间
            else if (task.reminderTime != null && task.reminderTime.isAfter(LocalDateTime.now())) {
                Log.d(TAG, "基于手动提醒时间设置任务提醒: ${task.title} at ${task.reminderTime}")
                scheduleReminder(
                    taskId = task.id,
                    taskTitle = task.title,
                    reminderTime = task.reminderTime,
                    priority = getPriorityString(task.priority),
                    reminderType = "task_start_reminder"
                )
            }
            // 如果只有截止时间，使用TaskReminderUtils计算正确的提醒时间
            else if (task.dueDate != null && task.dueDate.isAfter(LocalDateTime.now())) {
                // 🔧 修复：使用用户设置来计算提醒时间，遵循用户设置
                val notificationSettings = getNotificationSettings()
                val advanceMinutes = notificationSettings.defaultReminderTime
                val calculatedReminderTime = if (advanceMinutes == 0) {
                    task.dueDate // 准时提醒
                } else {
                    val reminderCandidate = task.dueDate.minusMinutes(advanceMinutes.toLong())
                    if (reminderCandidate.isAfter(LocalDateTime.now())) {
                        reminderCandidate
                    } else {
                        LocalDateTime.now().plusMinutes(1) // 1分钟后提醒
                    }
                }

                if (calculatedReminderTime != null && calculatedReminderTime.isAfter(LocalDateTime.now())) {
                    Log.d(TAG, "基于截止时间设置任务提醒: ${task.title} at $calculatedReminderTime (用户配置: ${notificationSettings.defaultReminderTime}分钟)")
                    scheduleReminder(
                        taskId = task.id,
                        taskTitle = task.title,
                        reminderTime = calculatedReminderTime,
                        priority = getPriorityString(task.priority),
                        reminderType = "task_deadline_reminder"
                    )
                } else {
                    Log.d(TAG, "计算的提醒时间无效或已过期，跳过提醒: ${task.title}")
                }
            }
            else {
                Log.d(TAG, "任务无有效时间设置，跳过提醒: ${task.title}")
                return
            }
            
            Log.d(TAG, "任务提醒设置完成: ${task.title}")
            
        } catch (e: Exception) {
            Log.e(TAG, "设置任务提醒失败: ${task.title}", e)
        }
    }
    
    /**
     * 取消任务的所有提醒
     */
    fun cancelTaskReminders(taskId: String) {
        try {
            Log.d(TAG, "取消任务提醒: $taskId")
            
            // 取消WorkManager中的任务
            WorkManager.getInstance(context).cancelAllWorkByTag("task_reminder_$taskId")
            
            // 直接通过静态方法取消AlarmManager中的闹钟
            val reminderTypes = listOf("task_reminder", "deadline_reminder", "overdue_reminder")
            reminderTypes.forEach { reminderType ->
                TaskReminderWorker.cancelReminder(context, taskId, reminderType)
            }
            
            Log.d(TAG, "任务提醒已取消: $taskId")
            
        } catch (e: Exception) {
            Log.e(TAG, "取消任务提醒失败: $taskId", e)
        }
    }
    
    /**
     * 检查并发送逾期任务提醒
     */
    suspend fun checkAndNotifyOverdueTasks() {
        try {
            Log.d(TAG, "检查逾期任务...")
            
            val now = LocalDateTime.now()
            val overdueTasks = taskRepository.getAllTasks().filter { task ->
                task.dueDate != null &&
                task.dueDate.isBefore(now) &&
                task.status != "已完成" &&
                !task.isCompleted
            }
            
            Log.d(TAG, "发现 ${overdueTasks.size} 个逾期任务")
            
            overdueTasks.forEach { task ->
                val overdueDays = java.time.temporal.ChronoUnit.DAYS.between(
                    task.dueDate!!.toLocalDate(),
                    now.toLocalDate()
                ).toInt()
                
                // 获取通知设置
                val settings = getNotificationSettings()
                
                // 发送逾期提醒
                notificationManager.showOverdueReminder(
                    taskId = task.id,
                    taskTitle = task.title,
                    overdueDays = overdueDays,
                    settings = settings
                )
                
                Log.d(TAG, "已发送逾期提醒: ${task.title} (逾期 $overdueDays 天)")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查逾期任务失败", e)
        }
    }
    
    /**
     * 批量为任务设置提醒
     */
    suspend fun scheduleRemindersForTasks(tasks: List<Task>) {
        Log.d(TAG, "批量设置任务提醒，共 ${tasks.size} 个任务")
        
        tasks.forEach { task ->
            scheduleTaskReminder(task)
        }
        
        Log.d(TAG, "批量任务提醒设置完成")
    }
    
    /**
     * 设置单个提醒
     */
    private fun scheduleReminder(
        taskId: String,
        taskTitle: String,
        reminderTime: LocalDateTime,
        priority: String,
        reminderType: String
    ) {
        try {
            // 创建WorkManager请求数据
            val inputData = Data.Builder()
                .putString(TaskReminderWorker.KEY_TASK_ID, taskId)
                .putString(TaskReminderWorker.KEY_TASK_TITLE, taskTitle)
                .putString(TaskReminderWorker.KEY_REMINDER_TIME, reminderTime.toString())
                .putString(TaskReminderWorker.KEY_PRIORITY, priority)
                .putString(TaskReminderWorker.KEY_REMINDER_TYPE, reminderType)
                .build()
            
            // 🔧 关键修复：计算延迟时间，确保在正确的时间触发
            val now = LocalDateTime.now()
            val delayMillis = java.time.Duration.between(now, reminderTime).toMillis()

            // 创建一次性工作请求，设置延迟时间
            val reminderRequest = OneTimeWorkRequestBuilder<TaskReminderWorker>()
                .setInputData(inputData)
                .setInitialDelay(delayMillis, java.util.concurrent.TimeUnit.MILLISECONDS)
                .addTag("task_reminder_$taskId")
                .addTag("task_reminder")
                .build()

            // 通过WorkManager调度工作
            WorkManager.getInstance(context).enqueue(reminderRequest)
            
            Log.d(TAG, "提醒已调度: $taskTitle ($reminderType) at $reminderTime")
            
        } catch (e: Exception) {
            Log.e(TAG, "调度提醒失败: $taskTitle", e)
        }
    }
    
    /**
     * 获取优先级字符串
     */
    private fun getPriorityString(priority: Int): String {
        return when (priority) {
            3 -> "紧急"
            2 -> "高"
            1 -> "中等"
            0 -> "低"
            else -> "中等"
        }
    }
    
    /**
     * 获取通知设置
     */
    private fun getNotificationSettings(): NotificationSettings {
        return runBlocking {
            try {
                context.notificationDataStore.data.first().let { preferences ->
                    NotificationSettings(
                        notificationsEnabled = preferences[booleanPreferencesKey("notifications_enabled")] ?: true,
                        taskRemindersEnabled = preferences[booleanPreferencesKey("task_reminders_enabled")] ?: true,
                        deadlineRemindersEnabled = preferences[booleanPreferencesKey("deadline_reminders_enabled")] ?: true,
                        overdueRemindersEnabled = preferences[booleanPreferencesKey("overdue_reminders_enabled")] ?: true,
                        dailyReviewEnabled = preferences[booleanPreferencesKey("daily_review_enabled")] ?: true,
                        dailyReviewTime = preferences[stringPreferencesKey("daily_review_time")] ?: "21:00",
                        taskPersistentNotificationEnabled = preferences[booleanPreferencesKey("task_persistent_notification_enabled")] ?: true,
                        habitRemindersEnabled = preferences[booleanPreferencesKey("habit_reminders_enabled")] ?: true,
                        focusRemindersEnabled = preferences[booleanPreferencesKey("focus_reminders_enabled")] ?: true,
                        focusSessionNotificationsEnabled = preferences[booleanPreferencesKey("focus_session_notifications_enabled")] ?: true,
                        medicationRemindersEnabled = preferences[booleanPreferencesKey("medication_reminders_enabled")] ?: true,
                        medicationSoundEnabled = preferences[booleanPreferencesKey("medication_sound_enabled")] ?: true,
                        medicationVibrationEnabled = preferences[booleanPreferencesKey("medication_vibration_enabled")] ?: true,
                        medicationAdvanceTime = preferences[intPreferencesKey("medication_advance_time")] ?: 5,
                        soundEnabled = preferences[booleanPreferencesKey("sound_enabled")] ?: true,
                        vibrationEnabled = preferences[booleanPreferencesKey("vibration_enabled")] ?: true,
                        doNotDisturbEnabled = preferences[booleanPreferencesKey("do_not_disturb_enabled")] ?: false,
                        doNotDisturbStartTime = preferences[stringPreferencesKey("do_not_disturb_start_time")] ?: "22:00",
                        doNotDisturbEndTime = preferences[stringPreferencesKey("do_not_disturb_end_time")] ?: "08:00",
                        // 🔧 关键修复：正确读取用户设置的默认提醒时间
                        defaultReminderTime = preferences[intPreferencesKey("default_reminder_time")] ?: 0 // 默认准时提醒
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "读取通知设置失败，使用默认设置", e)
                NotificationSettings()
            }
        }
    }
} 