package com.timeflow.app.ui.screen.analytics

import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.dao.SessionStats
import com.timeflow.app.data.dao.TaskTimeStats
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * AnalyticsDataService 单元测试
 * 验证数据分析服务的真实数据处理逻辑
 */
class AnalyticsDataServiceTest {

    @Mock
    private lateinit var taskRepository: TaskRepository

    @Mock
    private lateinit var timeSessionRepository: TimeSessionRepository

    @Mock
    private lateinit var goalRepository: GoalRepository

    @Mock
    private lateinit var habitRepository: HabitRepository

    private lateinit var analyticsDataService: AnalyticsDataService

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        analyticsDataService = AnalyticsDataService(
            taskRepository = taskRepository,
            timeSessionRepository = timeSessionRepository,
            goalRepository = goalRepository,
            habitRepository = habitRepository
        )
    }

    @Test
    fun `getProductivityScoreData should return zero when no data available`() = runTest {
        // Given
        val timeRange = AnalyticsViewModel.TimeRange.DAY
        whenever(taskRepository.getAllTasks()).thenReturn(emptyList())
        whenever(timeSessionRepository.getSessionStats(any(), any())).thenReturn(
            SessionStats(sessionCount = 0, totalDuration = 0L, avgDuration = 0.0)
        )
        whenever(timeSessionRepository.getAllSessions()).thenReturn(flowOf(emptyList()))

        // When
        val result = analyticsDataService.getProductivityScoreData(timeRange)

        // Then
        assertEquals(0, result.score)
        assertEquals(0f, result.completionRate)
        assertEquals(0, result.interruptionCount)
    }

    @Test
    fun `getTimeDistributionData should return empty map when no tasks available`() = runTest {
        // Given
        val timeRange = AnalyticsViewModel.TimeRange.DAY
        whenever(taskRepository.getAllTasks()).thenReturn(emptyList())
        whenever(timeSessionRepository.getTaskTimeStats(any(), any())).thenReturn(flowOf(emptyList()))

        // When
        val result = analyticsDataService.getTimeDistributionData(timeRange)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getActivityDetailData should return empty map when no sessions available`() = runTest {
        // Given
        val timeRange = AnalyticsViewModel.TimeRange.DAY
        whenever(timeSessionRepository.getTaskTimeStats(any(), any())).thenReturn(flowOf(emptyList()))

        // When
        val result = analyticsDataService.getActivityDetailData(timeRange)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getActivityDetailData should calculate correct percentages`() = runTest {
        // Given
        val timeRange = AnalyticsViewModel.TimeRange.DAY
        val mockSessions = listOf(
            TaskTimeStats(
                taskId = "1",
                taskName = "工作任务",
                sessionCount = 2,
                totalDuration = 3600000L // 1小时
            ),
            TaskTimeStats(
                taskId = "2", 
                taskName = "学习课程",
                sessionCount = 1,
                totalDuration = 1800000L // 30分钟
            )
        )
        whenever(timeSessionRepository.getTaskTimeStats(any(), any())).thenReturn(flowOf(mockSessions))

        // When
        val result = analyticsDataService.getActivityDetailData(timeRange)

        // Then
        assertEquals(2, result.size)
        assertTrue(result.containsKey("工作"))
        assertTrue(result.containsKey("学习"))
        
        val workData = result["工作"]!!
        assertEquals(66.7f, workData.percentage, 0.1f) // 1小时 / 1.5小时 * 100
        
        val studyData = result["学习"]!!
        assertEquals(33.3f, studyData.percentage, 0.1f) // 30分钟 / 1.5小时 * 100
    }

    @Test
    fun `formatDurationFromMillis should format correctly`() {
        // Test hours and minutes
        assertEquals("2小时30分钟", formatDurationFromMillis(9000000L)) // 2.5 hours
        
        // Test minutes only
        assertEquals("45分钟", formatDurationFromMillis(2700000L)) // 45 minutes
        
        // Test less than a minute
        assertEquals("少于1分钟", formatDurationFromMillis(30000L)) // 30 seconds
    }

    @Test
    fun `getHabitStatistics should return empty stats when no habits available`() = runTest {
        // Given
        val timeRange = AnalyticsViewModel.TimeRange.DAY
        val emptyHabitAnalytics = com.timeflow.app.data.repository.HabitAnalyticsData(
            totalHabits = 0,
            activeHabits = 0,
            completedToday = 0,
            averageCompletionRate = 0f,
            averageStreak = 0f,
            longestStreak = 0,
            habitsByCategory = emptyMap(),
            topPerformingHabits = emptyList()
        )
        whenever(habitRepository.getHabitStatistics(any(), any())).thenReturn(emptyHabitAnalytics)

        // When
        val result = analyticsDataService.getHabitStatistics(timeRange)

        // Then
        assertEquals(0, result.totalHabits)
        assertEquals(0, result.activeHabits)
        assertEquals(0f, result.averageStreak)
        assertEquals(0f, result.completionRate)
    }

    private fun any(): LocalDate = org.mockito.kotlin.any()
}
