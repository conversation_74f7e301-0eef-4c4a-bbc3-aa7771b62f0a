package com.timeflow.app.di

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.timeflow.app.data.dao.KanbanBoardDao
import com.timeflow.app.data.dao.KanbanColumnDao
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.data.dao.AppUsageDao
import com.timeflow.app.data.dao.CycleDao
import com.timeflow.app.data.repository.TimeAnalyticsRepository
import com.timeflow.app.data.repository.TimeAnalyticsRepositoryImpl
import com.timeflow.app.data.dao.GoalDao
import com.timeflow.app.data.dao.GoalTemplateDao
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.GoalRepositoryImpl
import com.timeflow.app.data.dao.ReflectionDao
import com.timeflow.app.data.dao.HabitDao
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.repository.HabitRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.Dispatchers
import java.time.LocalDateTime
import java.util.UUID
import com.timeflow.app.utils.DatabaseBackupManager

/**
 * 数据库模块 - 提供数据库和DAO实例
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * 提供数据库备份管理器
     */
    @Provides
    @Singleton
    fun provideDatabaseBackupManager(@ApplicationContext context: Context): DatabaseBackupManager {
        return DatabaseBackupManager(context)
    }
    
    /**
     * 提供Room数据库实例
     */
    @Provides
    @Singleton
    fun provideAppDatabase(
        @ApplicationContext context: Context,
        backupManager: DatabaseBackupManager
    ): AppDatabase {
        
        // 🔧 版本更新前自动备份数据
        runBlocking(Dispatchers.IO) {
            try {
                if (backupManager.shouldBackupDatabase()) {
                    Log.i("DatabaseModule", "🔄 检测到数据库版本更新，开始自动备份...")
                    val backupSuccess = backupManager.backupDatabase()
                    if (backupSuccess) {
                        Log.i("DatabaseModule", "✅ 数据库备份成功，用户数据已保护")
                    } else {
                        Log.w("DatabaseModule", "⚠️ 数据库备份失败，建议检查存储权限")
                    }
                } else {
                    Log.d("DatabaseModule", "数据库版本未变化，无需备份")
                }
            } catch (e: Exception) {
                Log.e("DatabaseModule", "备份过程出错", e)
            }
        }
        
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "timeflow_database"
        )
        .addCallback(object : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                Log.d("DatabaseModule", "数据库创建成功")
            }

            override fun onOpen(db: SupportSQLiteDatabase) {
                super.onOpen(db)
                Log.d("DatabaseModule", "数据库打开成功")
                // 验证数据完整性
                try {
                    val tableCount = db.query("SELECT count(*) FROM sqlite_master WHERE type='table'").use { cursor ->
                        cursor.moveToFirst()
                        cursor.getInt(0)
                    }
                    Log.d("DatabaseModule", "数据库包含 $tableCount 个表")
                } catch (e: Exception) {
                    Log.e("DatabaseModule", "数据库完整性检查失败", e)
                }
            }

            override fun onDestructiveMigration(db: SupportSQLiteDatabase) {
                super.onDestructiveMigration(db)
                Log.e("DatabaseModule", "🚨 警告：数据库执行了破坏性迁移，用户数据可能已丢失！")
                Log.e("DatabaseModule", "🔧 原因：检测到数据库版本降级（从高版本回退到低版本）")
                Log.e("DatabaseModule", "💡 解决方案：")
                Log.e("DatabaseModule", "   1. 检查是否安装了旧版本应用")
                Log.e("DatabaseModule", "   2. 尝试从备份恢复数据")
                Log.e("DatabaseModule", "   3. 重新安装最新版本应用")

                // 记录破坏性迁移事件，用于后续数据恢复
                try {
                    val prefs = context.getSharedPreferences("database_migration", Context.MODE_PRIVATE)
                    prefs.edit()
                        .putBoolean("destructive_migration_occurred", true)
                        .putLong("destructive_migration_time", System.currentTimeMillis())
                        .putString("destructive_migration_reason", "version_downgrade")
                        .apply()
                    Log.i("DatabaseModule", "已记录破坏性迁移事件，用于数据恢复参考")
                } catch (e: Exception) {
                    Log.e("DatabaseModule", "记录破坏性迁移事件失败", e)
                }
            }
        })

        // ✅ 添加完整的迁移策略，确保数据安全
        .addMigrations(*AppDatabase.ALL_MIGRATIONS)

        // 🚨 紧急修复：添加数据保护机制，防止版本降级导致的数据丢失
        .fallbackToDestructiveMigrationOnDowngrade()  // 只在降级时允许破坏性迁移

        // 🔧 启用WAL模式以提高性能和并发性
        .setJournalMode(RoomDatabase.JournalMode.WRITE_AHEAD_LOGGING)

        // 🔧 移除主线程查询，强制使用后台线程，避免UI阻塞
        // .allowMainThreadQueries() // ❌ 这会导致主线程阻塞，造成转圈圈问题

        .build()
        .also { database ->
            // 在后台线程中执行数据库操作，避免在主线程执行
            runBlocking(Dispatchers.IO) {
                try {
                    // 尝试触发一个简单操作来检查数据库可用性
                    val kanbanColumnsCount = database.kanbanColumnDao().getColumnCount()
                    Log.d("DatabaseModule", "数据库实例创建成功，看板列数量: $kanbanColumnsCount")
                    
                    if (kanbanColumnsCount == 0) {
                        Log.d("DatabaseModule", "检测到空数据库，将自动创建默认看板列")
                        createDefaultColumnsIfNeeded(database)
                    } else {
                        Log.d("DatabaseModule", "数据库已存在看板列，无需创建默认数据")
                    }
                } catch (e: Exception) {
                    Log.e("DatabaseModule", "数据库初始化失败，错误: ${e.message}", e)

                    // 🔧 检查是否发生了破坏性迁移
                    val prefs = context.getSharedPreferences("database_migration", Context.MODE_PRIVATE)
                    val destructiveMigrationOccurred = prefs.getBoolean("destructive_migration_occurred", false)

                    if (destructiveMigrationOccurred) {
                        Log.w("DatabaseModule", "🚨 检测到之前发生过破坏性迁移，数据可能已丢失")
                        val migrationTime = prefs.getLong("destructive_migration_time", 0)
                        val migrationReason = prefs.getString("destructive_migration_reason", "unknown")
                        Log.w("DatabaseModule", "迁移时间: ${java.util.Date(migrationTime)}")
                        Log.w("DatabaseModule", "迁移原因: $migrationReason")

                        // 清除标记，避免重复提示
                        prefs.edit().remove("destructive_migration_occurred").apply()
                    }

                    // 🔧 提供数据恢复提示
                    Log.w("DatabaseModule", "💡 数据恢复选项：")
                    val backupInfo = backupManager.getBackupInfo()
                    if (backupInfo.backupCount > 0) {
                        Log.i("DatabaseModule", "📂 发现 ${backupInfo.backupCount} 个备份文件，可用于数据恢复")
                        Log.i("DatabaseModule", "🔧 建议：在设置页面使用数据恢复功能")
                    } else {
                        Log.d("DatabaseModule", "暂无可用备份文件")
                        Log.d("DatabaseModule", "🔧 建议：重新创建数据或联系技术支持")
                    }
                }
            }
        }
    }
    
    /**
     * 创建默认看板列（如果需要）
     */
    private fun createDefaultColumnsIfNeeded(database: AppDatabase): Unit = runBlocking(Dispatchers.IO) {
        try {
            // 首先检查数据库中是否有任何看板列
            val columnCount = database.kanbanColumnDao().getColumnCount()
            
            if (columnCount > 0) {
                Log.d("DatabaseModule", "数据库中已存在 $columnCount 个看板列，无需创建默认数据")
                return@runBlocking
            }
            
            Log.d("DatabaseModule", "数据库中无看板列，开始创建默认数据...")
            
            // 清空所有表，避免任何外键约束问题
            database.clearAllTables()
            Log.d("DatabaseModule", "清空数据库表，准备创建全新数据")
            
            // 创建默认看板
            val defaultBoardId = UUID.randomUUID().toString()
            val defaultBoard = com.timeflow.app.data.entity.KanbanBoard(
                id = defaultBoardId,
                title = "默认看板",
                description = "系统自动创建的默认看板",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now(),
                position = 0
            )
            database.kanbanBoardDao().insert(defaultBoard)
            Log.d("DatabaseModule", "创建默认看板: ${defaultBoard.title}, ID=${defaultBoard.id}")
            
            // 创建默认列
            val defaultColumns = listOf(
                com.timeflow.app.data.entity.KanbanColumn(
                    id = UUID.randomUUID().toString(),
                    title = "待办",
                    description = "待开始的任务",
                    boardId = defaultBoardId,
                    position = 0,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                ),
                com.timeflow.app.data.entity.KanbanColumn(
                    id = UUID.randomUUID().toString(),
                    title = "进行中",
                    description = "正在进行的任务",
                    boardId = defaultBoardId,
                    position = 1,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                ),
                com.timeflow.app.data.entity.KanbanColumn(
                    id = UUID.randomUUID().toString(),
                    title = "已完成",
                    description = "已完成的任务",
                    boardId = defaultBoardId,
                    position = 2,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                )
            )
            
            database.kanbanColumnDao().insertAll(defaultColumns)
            
            // 记录创建的列信息
            defaultColumns.forEachIndexed { index, column ->
                Log.d("DatabaseModule", "创建默认列 #${index+1}: ${column.title}, ID=${column.id}")
            }
            
            // 验证列是否成功创建
            val createdColumns = database.kanbanColumnDao().getColumnsByBoardId(defaultBoardId)
            Log.d("DatabaseModule", "验证 - 成功创建 ${createdColumns.size} 个看板列")
            
            // 如果需要，也可以在这里创建示例任务
        } catch (e: Exception) {
            Log.e("DatabaseModule", "创建默认看板列失败: ${e.message}", e)
            // 不抛出异常，确保应用仍能继续运行
        }
    }
    
    /**
     * 提供任务DAO
     */
    @Provides
    @Singleton
    fun provideTaskDao(database: AppDatabase): TaskDao {
        return database.taskDao().also {
            Log.d("DatabaseModule", "TaskDao实例创建成功")
        }
    }
    
    /**
     * 提供看板DAO
     */
    @Provides
    @Singleton
    fun provideKanbanBoardDao(database: AppDatabase): KanbanBoardDao {
        return database.kanbanBoardDao().also {
            Log.d("DatabaseModule", "KanbanBoardDao实例创建成功")
        }
    }
    
    /**
     * 提供看板列DAO
     */
    @Provides
    @Singleton
    fun provideKanbanColumnDao(database: AppDatabase): KanbanColumnDao {
        return database.kanbanColumnDao().also {
            Log.d("DatabaseModule", "KanbanColumnDao实例创建成功")
        }
    }

    /**
     * 提供生理周期DAO
     */
    @Provides
    @Singleton
    fun provideCycleDao(database: AppDatabase): CycleDao {
        return database.cycleDao().also {
            Log.d("DatabaseModule", "CycleDao实例创建成功")
        }
    }

    /**
     * 提供AppUsageDao
     */
    @Provides
    @Singleton
    fun provideAppUsageDao(database: AppDatabase): AppUsageDao {
        return database.appUsageDao()
    }

    /**
     * 提供RoomDatabase实例
     * 将AppDatabase向上转型为RoomDatabase以满足TaskRepositoryImpl的依赖需求
     */
    @Provides
    @Singleton
    fun provideRoomDatabase(appDatabase: AppDatabase): RoomDatabase {
        return appDatabase
    }

    /**
     * 提供GoalDao
     */
    @Provides
    @Singleton
    fun provideGoalDao(database: AppDatabase): GoalDao {
        return database.goalDao().also {
            Log.d("DatabaseModule", "GoalDao实例创建成功")
        }
    }

    /**
     * 提供GoalTemplateDao
     */
    @Provides
    @Singleton
    fun provideGoalTemplateDao(database: AppDatabase): GoalTemplateDao {
        return database.goalTemplateDao().also {
            Log.d("DatabaseModule", "GoalTemplateDao实例创建成功")
        }
    }

    /**
     * 提供GoalRepository
     */
    @Provides
    @Singleton
    fun provideGoalRepository(goalDao: GoalDao): GoalRepository {
        return GoalRepositoryImpl(goalDao).also {
            Log.d("DatabaseModule", "GoalRepository实例创建成功")
        }
    }

    /**
     * 提供感想DAO
     */
    @Provides
    @Singleton
    fun provideReflectionDao(database: AppDatabase): ReflectionDao {
        return database.reflectionDao().also {
            Log.d("DatabaseModule", "ReflectionDao实例创建成功")
        }
    }

    /**
     * 提供时间会话DAO
     */
    @Provides
    @Singleton
    fun provideTimeSessionDao(database: AppDatabase): com.timeflow.app.data.dao.TimeSessionDao {
        return database.timeSessionDao().also {
            Log.d("DatabaseModule", "TimeSessionDao实例创建成功")
        }
    }

    /**
     * 提供习惯DAO
     */
    @Provides
    @Singleton
    fun provideHabitDao(database: AppDatabase): HabitDao {
        return database.habitDao().also {
            Log.d("DatabaseModule", "HabitDao实例创建成功")
        }
    }

    /**
     * 🔧 提供情绪记录DAO
     */
    @Provides
    @Singleton
    fun provideEmotionRecordDao(database: AppDatabase): com.timeflow.app.data.dao.EmotionRecordDao {
        return database.emotionRecordDao().also {
            Log.d("DatabaseModule", "EmotionRecordDao实例创建成功")
        }
    }

    /**
     * 🔧 提供情绪记录Repository
     */
    @Provides
    @Singleton
    fun provideEmotionRecordRepository(
        emotionRecordDao: com.timeflow.app.data.dao.EmotionRecordDao
    ): com.timeflow.app.data.repository.EmotionRecordRepository {
        return com.timeflow.app.data.repository.EmotionRecordRepositoryImpl(emotionRecordDao).also {
            Log.d("DatabaseModule", "EmotionRecordRepository实例创建成功")
        }
    }

    /**
     * 提供SharedPreferences
     */
    @Provides
    @Singleton
    fun provideSharedPreferences(@ApplicationContext context: Context): SharedPreferences {
        return context.getSharedPreferences("time_tracking_prefs", Context.MODE_PRIVATE).also {
            Log.d("DatabaseModule", "SharedPreferences实例创建成功")
        }
    }
} 