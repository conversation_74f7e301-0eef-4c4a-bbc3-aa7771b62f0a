package com.timeflow.app.ui.screen.task

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务时间更新事件
 */
data class TaskTimeUpdateEvent(
    val taskId: String,
    val oldDateTime: LocalDateTime?,
    val newDateTime: LocalDateTime?,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 任务时间管理器
 * 负责统一管理任务时间的存储、修改和同步
 */
@Singleton
class TaskTimeManager @Inject constructor() {
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 任务时间缓存，用于快速访问和比较
    private val _taskTimeCache = MutableStateFlow<Map<String, LocalDateTime?>>(emptyMap())
    val taskTimeCache: StateFlow<Map<String, LocalDateTime?>> = _taskTimeCache.asStateFlow()
    
    // 时间更新事件流
    private val _timeUpdateEvents = MutableSharedFlow<TaskTimeUpdateEvent>(
        replay = 0,
        extraBufferCapacity = 10
    )
    val timeUpdateEvents: SharedFlow<TaskTimeUpdateEvent> = _timeUpdateEvents.asSharedFlow()
    
    // 待同步的时间更新队列
    private val _pendingUpdates = MutableStateFlow<Map<String, LocalDateTime?>>(emptyMap())
    
    /**
     * 更新任务时间
     * @param taskId 任务ID
     * @param newDateTime 新的时间
     * @param source 更新来源（用于日志追踪）
     */
    suspend fun updateTaskTime(
        taskId: String, 
        newDateTime: LocalDateTime?,
        source: String = "Unknown"
    ) {
        withContext(Dispatchers.IO) {
            try {
                val oldDateTime = _taskTimeCache.value[taskId]
                
                Log.d("TaskTimeManager", "更新任务时间: taskId=$taskId, 旧时间=$oldDateTime, 新时间=$newDateTime, 来源=$source")
                
                // 检查是否真的需要更新
                if (oldDateTime == newDateTime) {
                    Log.d("TaskTimeManager", "时间未变化，跳过更新")
                    return@withContext
                }
                
                // 更新缓存
                val currentCache = _taskTimeCache.value.toMutableMap()
                currentCache[taskId] = newDateTime
                _taskTimeCache.value = currentCache
                
                // 添加到待同步队列
                val currentPending = _pendingUpdates.value.toMutableMap()
                currentPending[taskId] = newDateTime
                _pendingUpdates.value = currentPending
                
                // 发送更新事件
                val event = TaskTimeUpdateEvent(taskId, oldDateTime, newDateTime)
                _timeUpdateEvents.emit(event)
                
                Log.d("TaskTimeManager", "任务时间更新完成，已发送事件")
                
            } catch (e: Exception) {
                Log.e("TaskTimeManager", "更新任务时间失败: taskId=$taskId", e)
                throw e
            }
        }
    }
    
    /**
     * 批量更新任务时间
     */
    suspend fun batchUpdateTaskTimes(updates: Map<String, LocalDateTime?>, source: String = "Batch") {
        withContext(Dispatchers.IO) {
            try {
                Log.d("TaskTimeManager", "批量更新任务时间: ${updates.size}个任务, 来源=$source")
                
                val currentCache = _taskTimeCache.value.toMutableMap()
                val currentPending = _pendingUpdates.value.toMutableMap()
                val events = mutableListOf<TaskTimeUpdateEvent>()
                
                updates.forEach { (taskId, newDateTime) ->
                    val oldDateTime = currentCache[taskId]
                    if (oldDateTime != newDateTime) {
                        currentCache[taskId] = newDateTime
                        currentPending[taskId] = newDateTime
                        events.add(TaskTimeUpdateEvent(taskId, oldDateTime, newDateTime))
                    }
                }
                
                if (events.isNotEmpty()) {
                    _taskTimeCache.value = currentCache
                    _pendingUpdates.value = currentPending
                    
                    // 发送所有事件
                    events.forEach { event ->
                        _timeUpdateEvents.emit(event)
                    }
                    
                    Log.d("TaskTimeManager", "批量更新完成，发送了${events.size}个事件")
                }
                
            } catch (e: Exception) {
                Log.e("TaskTimeManager", "批量更新任务时间失败", e)
                throw e
            }
        }
    }
    
    /**
     * 获取任务时间
     */
    fun getTaskTime(taskId: String): LocalDateTime? {
        return _taskTimeCache.value[taskId]
    }
    
    /**
     * 初始化任务时间缓存
     */
    suspend fun initializeCache(taskTimes: Map<String, LocalDateTime?>) {
        withContext(Dispatchers.IO) {
            Log.d("TaskTimeManager", "初始化时间缓存: ${taskTimes.size}个任务")
            _taskTimeCache.value = taskTimes
        }
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        scope.launch {
            _taskTimeCache.value = emptyMap()
            _pendingUpdates.value = emptyMap()
            Log.d("TaskTimeManager", "已清除时间缓存")
        }
    }
    
    /**
     * 获取待同步的更新
     */
    fun getPendingUpdates(): Map<String, LocalDateTime?> {
        return _pendingUpdates.value
    }
    
    /**
     * 清除待同步的更新
     */
    suspend fun clearPendingUpdates(taskIds: Set<String>) {
        withContext(Dispatchers.IO) {
            val currentPending = _pendingUpdates.value.toMutableMap()
            taskIds.forEach { taskId ->
                currentPending.remove(taskId)
            }
            _pendingUpdates.value = currentPending
            Log.d("TaskTimeManager", "已清除${taskIds.size}个任务的待同步更新")
        }
    }
    
    /**
     * 强制刷新指定任务的时间
     */
    suspend fun forceRefreshTaskTime(taskId: String, newDateTime: LocalDateTime?) {
        updateTaskTime(taskId, newDateTime, "ForceRefresh")
    }
} 