package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "task_closure",
    foreignKeys = [
        ForeignKey(
            entity = Task::class,
            parentColumns = ["id"],
            childColumns = ["ancestorId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Task::class,
            parentColumns = ["id"],
            childColumns = ["descendantId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("ancestorId"),
        Index("descendantId")
    ]
)
data class TaskClosure(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val ancestorId: String,
    val descendantId: String,
    val depth: Int
) 