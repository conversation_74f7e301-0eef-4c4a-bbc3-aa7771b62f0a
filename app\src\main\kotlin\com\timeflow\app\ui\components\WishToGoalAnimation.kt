package com.timeflow.app.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

@OptIn(ExperimentalAnimationApi::class)

// 动效主题配色
private val AnimationPrimary = Color(0xFFFF6B9D)
private val AnimationSecondary = Color(0xFFFFA8CC) 
private val AnimationAccent = Color(0xFFFFD93D)
private val SakuraColor = Color(0xFFFFA8CC)
private val GoalColor = Color(0xFF6A45C9)

/**
 * 🌸 愿望转化动效对话框
 * 实现樱花飘落 + 文字提示的精美转化动画
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun WishToGoalAnimationDialog(
    wish: com.timeflow.app.data.model.WishModel,
    isVisible: Boolean,
    onAnimationComplete: () -> Unit
) {
    val density = LocalDensity.current
    
    // 动画状态
    var animationPhase by remember { mutableStateOf(0) }
    // 0: 准备阶段, 1: 樱花飘落, 2: 转化提示, 3: 完成
    
    // 樱花粒子状态
    val sakuraParticles = remember {
        List(20) { 
            SakuraParticle(
                x = Random.nextFloat(),
                y = -0.1f,
                rotation = Random.nextFloat() * 360f,
                speed = Random.nextFloat() * 0.5f + 0.3f,
                size = Random.nextFloat() * 0.4f + 0.6f
            )
        }
    }
    
    // 动画值
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isVisible) 0.8f else 0f,
        animationSpec = tween(600),
        label = "background_alpha"
    )
    
    val cardScale by animateFloatAsState(
        targetValue = when (animationPhase) {
            0 -> if (isVisible) 1f else 0.8f
            1 -> 1.05f
            2 -> 1f
            else -> 0.9f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "card_scale"
    )
    
    val textAlpha by animateFloatAsState(
        targetValue = if (animationPhase >= 2) 1f else 0f,
        animationSpec = tween(800),
        label = "text_alpha"
    )
    
    // 启动动画序列
    LaunchedEffect(isVisible) {
        if (isVisible) {
            delay(300)
            animationPhase = 1 // 开始樱花飘落
            
            delay(2000)
            animationPhase = 2 // 显示转化提示
            
            delay(2500)
            animationPhase = 3 // 完成动画
            
            delay(500)
            onAnimationComplete()
        }
    }

    if (isVisible) {
        Dialog(
            onDismissRequest = { },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = backgroundAlpha)),
                contentAlignment = Alignment.Center
            ) {
                // 🌸 樱花粒子背景
                if (animationPhase >= 1) {
                    SakuraParticleField(
                        particles = sakuraParticles,
                        isAnimating = animationPhase == 1
                    )
                }
                
                // 📱 主内容卡片
                Card(
                    modifier = Modifier
                        .fillMaxWidth(0.85f)
                        .scale(cardScale),
                    shape = RoundedCornerShape(24.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 24.dp
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 🎯 愿望转化图标
                        WishToGoalIcon(
                            animationPhase = animationPhase
                        )
                        
                        Spacer(modifier = Modifier.height(24.dp))
                        
                        // 💫 动态标题
                        AnimatedTransformTitle(
                            wishTitle = wish.title,
                            animationPhase = animationPhase,
                            alpha = textAlpha
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 📝 转化提示文字
                        AnimatedContent(
                            targetState = animationPhase,
                            transitionSpec = {
                                fadeIn(animationSpec = tween(600)) with
                                fadeOut(animationSpec = tween(300))
                            },
                            label = "content_transition"
                        ) { phase ->
                            when (phase) {
                                1 -> {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(
                                            text = "正在转化愿望...",
                                            fontSize = 16.sp,
                                            color = Color.Gray,
                                            textAlign = TextAlign.Center
                                        )
                                        
                                        Spacer(modifier = Modifier.height(12.dp))
                                        
                                        LinearProgressIndicator(
                                            modifier = Modifier.width(120.dp),
                                            color = AnimationPrimary,
                                            trackColor = AnimationPrimary.copy(alpha = 0.2f)
                                        )
                                    }
                                }
                                2, 3 -> {
                                    SuccessMessage(wish = wish)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 🌸 樱花粒子场
 */
@Composable
private fun SakuraParticleField(
    particles: List<SakuraParticle>,
    isAnimating: Boolean
) {
    val infiniteTransition = rememberInfiniteTransition(label = "sakura_animation")
    
    // 粒子下落动画
    val fallProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "fall_progress"
    )
    
    // 粒子旋转动画
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    if (isAnimating) {
        Box(modifier = Modifier.fillMaxSize()) {
            particles.forEach { particle ->
                val currentY = particle.y + fallProgress * (1.2f + particle.speed)
                val currentX = particle.x + sin(fallProgress * 2 * Math.PI).toFloat() * 0.1f
                
                if (currentY <= 1.1f) {
                    SakuraPetal(
                        modifier = Modifier
                            .fillMaxSize()
                            .alpha(1f - (currentY.coerceAtLeast(0.8f) - 0.8f) / 0.3f)
                            .graphicsLayer {
                                translationX = currentX * size.width
                                translationY = currentY * size.height
                                rotationZ = particle.rotation + rotation * particle.speed
                                scaleX = particle.size
                                scaleY = particle.size
                            }
                    )
                }
            }
        }
    }
}

/**
 * 🌸 单个樱花花瓣
 */
@Composable
private fun SakuraPetal(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopStart
    ) {
        Icon(
            imageVector = Icons.Default.LocalFlorist,
            contentDescription = null,
            tint = SakuraColor,
            modifier = Modifier.size(16.dp)
        )
    }
}

/**
 * 🎯 愿望转目标图标动画
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun WishToGoalIcon(
    animationPhase: Int
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier.size(80.dp)
    ) {
        // 背景圆圈动画
        val circleScale by animateFloatAsState(
            targetValue = if (animationPhase >= 2) 1f else 0.8f,
            animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
            label = "circle_scale"
        )
        
        // 渐变背景圆圈
        Box(
            modifier = Modifier
                .size(80.dp)
                .scale(circleScale)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            AnimationPrimary.copy(alpha = 0.2f),
                            AnimationSecondary.copy(alpha = 0.1f)
                        )
                    ),
                    shape = CircleShape
                )
        )
        
        // 中心图标切换动画
        AnimatedContent(
            targetState = animationPhase >= 2,
            transitionSpec = {
                (scaleIn() + fadeIn()) with (scaleOut() + fadeOut())
            },
            label = "icon_transition"
        ) { isGoal ->
            if (isGoal) {
                Icon(
                    imageVector = Icons.Default.Flag,
                    contentDescription = "目标",
                    tint = GoalColor,
                    modifier = Modifier.size(32.dp)
                )
            } else {
                Icon(
                    imageVector = Icons.Default.Star,
                    contentDescription = "愿望",
                    tint = AnimationAccent,
                    modifier = Modifier.size(32.dp)
                )
            }
        }
        
        // 闪烁效果
        if (animationPhase == 1) {
            val sparkleAlpha by animateFloatAsState(
                targetValue = if (animationPhase == 1) 1f else 0f,
                animationSpec = infiniteRepeatable(
                    animation = tween(500, easing = EaseInOutSine),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "sparkle_alpha"
            )
            
            Icon(
                imageVector = Icons.Default.AutoAwesome,
                contentDescription = null,
                tint = Color.White.copy(alpha = sparkleAlpha),
                modifier = Modifier.size(40.dp)
            )
        }
    }
}

/**
 * 💫 动态转化标题
 */
@Composable
private fun AnimatedTransformTitle(
    wishTitle: String,
    animationPhase: Int,
    alpha: Float
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.alpha(alpha)
    ) {
        Text(
            text = "🎉 愿望升级成功！",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = AnimationPrimary,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "「$wishTitle」",
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color.DarkGray,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "已升级为目标",
            fontSize = 14.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * ✅ 成功消息组件
 */
@Composable
private fun SuccessMessage(
    wish: com.timeflow.app.data.model.WishModel
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 自动生成任务提示
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = Color(0xFFF0F9FF),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.AutoAwesome,
                        contentDescription = null,
                        tint = Color(0xFF0EA5E9),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "AI 自动生成推荐任务",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0EA5E9)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 根据愿望类别生成示例任务
                val sampleTasks = generateSampleTasks(wish.category)
                sampleTasks.forEach { task ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 2.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircleOutline,
                            contentDescription = null,
                            tint = Color(0xFF10B981),
                            modifier = Modifier.size(12.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = task,
                            fontSize = 13.sp,
                            color = Color.DarkGray
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "继续努力，让梦想照进现实！ ✨",
            fontSize = 14.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
        )
    }
}

/**
 * 🔧 根据愿望类别生成示例任务
 */
private fun generateSampleTasks(category: com.timeflow.app.data.model.WishCategory): List<String> {
    return when (category) {
        com.timeflow.app.data.model.WishCategory.TRAVEL -> listOf(
            "制定旅行预算计划",
            "研究目的地攻略",
            "预订机票和住宿"
        )
        com.timeflow.app.data.model.WishCategory.SHOPPING -> listOf(
            "对比产品价格",
            "查看用户评价",
            "制定购买时间表"
        )
        com.timeflow.app.data.model.WishCategory.LEARNING -> listOf(
            "制定学习计划",
            "准备学习资料",
            "设定学习里程碑"
        )
        com.timeflow.app.data.model.WishCategory.CAREER -> listOf(
            "更新简历内容",
            "提升相关技能",
            "建立人脉网络"
        )
        com.timeflow.app.data.model.WishCategory.HEALTH -> listOf(
            "制定运动计划",
            "调整饮食结构",
            "定期健康检查"
        )
        else -> listOf(
            "分解具体步骤",
            "设定时间节点",
            "准备必要资源"
        )
    }
}

/**
 * 🌸 樱花粒子数据类
 */
private data class SakuraParticle(
    val x: Float,
    val y: Float,
    val rotation: Float,
    val speed: Float,
    val size: Float
) 