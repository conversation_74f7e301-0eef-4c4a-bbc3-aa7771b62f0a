package com.timeflow.app.ui.screen.health

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import androidx.lifecycle.viewmodel.compose.viewModel
import com.timeflow.app.ui.viewmodel.HabitViewModel
import kotlinx.coroutines.delay
import androidx.navigation.NavController

// 导入HabitData类型
import com.timeflow.app.ui.screen.health.HabitData

// 移除重复的数据类定义
@Stable
data class HabitStats(
    val currentStreak: Int,
    val longestStreak: Int,
    val totalCompletions: Int,
    val completionRate: Float,
    val last7DaysCompletion: Int
)

/**
 * 习惯详情页面 - 性能优化版本
 * 显示习惯的详细信息、统计数据和完成情况
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitDetailScreen(
    habitId: String,
    navController: NavController
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 使用ViewModel获取习惯数据
    val viewModel = androidx.lifecycle.viewmodel.compose.viewModel<HabitViewModel>()
    
    // 只在habitId变化时重新加载数据
    LaunchedEffect(habitId) {
        viewModel.selectHabit(habitId)
    }
    
    val habitState = viewModel.selectedHabit.collectAsState()
    val habit = habitState.value
    
    // 如果没有找到习惯数据，显示简化的错误界面
    if (habit == null) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "没有找到习惯数据",
                    style = MaterialTheme.typography.headlineMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(onClick = { navController.popBackStack() }) {
                    Text("返回")
                }
            }
        }
        return
    }
    
    // 优化：使用derivedStateOf减少重复计算
    val habitStats by remember {
        derivedStateOf {
            calculateHabitStats(habit)
        }
    }
    
    // 简化状态管理
    var showDatePicker by remember { mutableStateOf(false) }
    var showStatusMessage by remember { mutableStateOf(false) }
    var statusMessage by remember { mutableStateOf("") }
    
    // 管理状态栏 - 简化版本
    DisposableEffect(Unit) {
        activity?.let { SystemBarManager.forceOpaqueStatusBar(it) }
        onDispose { }
    }
    
    // 主界面内容
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F9FA))
            .padding(top = SystemBarManager.getFixedStatusBarHeight()),
        contentPadding = PaddingValues(bottom = 32.dp)
    ) {
        // 顶部导航栏
        item {
            TopNavigationBar(
                habitName = habit.name,
                habitColor = habit.color,
                onBackClick = { navController.popBackStack() },
                onMenuClick = { /* 暂不实现 */ }
            )
        }
        
        // 数据概览卡片 - 简化版本
        item {
            Spacer(modifier = Modifier.height(16.dp))
            DataOverviewCard(
                habitStats = habitStats,
                habitColor = habit.color,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
        
        // 简化的热力图
        item {
            Spacer(modifier = Modifier.height(16.dp))
            SimpleHeatmapCard(
                completedDates = habit.completedDates,
                habitColor = habit.color,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
        
        // 统计数据卡片
        item {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticsCard(
                habitStats = habitStats,
                habitColor = habit.color,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
        
        // 快速操作按钮
        item {
            Spacer(modifier = Modifier.height(16.dp))
            QuickActionsCard(
                onAddCheckIn = { showDatePicker = true },
                habitColor = habit.color,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
    }
    
    // 简化的日期选择器
    if (showDatePicker) {
        SimpleDatePickerDialog(
            onDismiss = { showDatePicker = false },
            onDateSelected = { date ->
                viewModel.markHabitAsCompleted(habitId, date, true)
                showStatusMessage = true
                statusMessage = "已为 ${date.format(DateTimeFormatter.ofPattern("MM月dd日"))} 打卡"
                showDatePicker = false
            },
            habitColor = habit.color
        )
    }
    
    // 简化的状态提示
    if (showStatusMessage) {
        LaunchedEffect(showStatusMessage) {
            delay(2000)
            showStatusMessage = false
        }
        
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Surface(
                modifier = Modifier.padding(16.dp),
                color = Color.Black.copy(alpha = 0.8f),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = statusMessage,
                    color = Color.White,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

// 优化：将统计计算提取为纯函数
private fun calculateHabitStats(habit: HabitData): HabitStats {
    val today = LocalDate.now()
    val completedDates = habit.completedDates
    
    // 当前连续天数
    var currentStreak = 0
    var currentDate = today
    while (completedDates.contains(currentDate)) {
        currentStreak++
        currentDate = currentDate.minusDays(1)
    }
    
    // 最长连续天数 - 简化算法
    var longestStreak = 0
    var tempStreak = 0
    val startDate = habit.createdAt
    var date = startDate
    
    while (!date.isAfter(today)) {
        if (completedDates.contains(date)) {
            tempStreak++
            longestStreak = maxOf(longestStreak, tempStreak)
        } else {
            tempStreak = 0
        }
        date = date.plusDays(1)
    }
    
    // 总完成次数
    val totalCompletions = completedDates.size
    
    // 完成率
    val daysBetween = ChronoUnit.DAYS.between(startDate, today) + 1
    val completionRate = if (daysBetween > 0) {
        (totalCompletions.toFloat() / daysBetween.toFloat() * 100f)
    } else 0f
    
    // 最近7天完成情况
    val last7DaysCompletion = (0..6).count { day ->
        completedDates.contains(today.minusDays(day.toLong()))
    }
    
    return HabitStats(
        currentStreak = currentStreak,
        longestStreak = longestStreak,
        totalCompletions = totalCompletions,
        completionRate = completionRate,
        last7DaysCompletion = last7DaysCompletion
    )
}

// 简化的顶部导航栏
@Composable
private fun TopNavigationBar(
    habitName: String,
    habitColor: Color,
    onBackClick: () -> Unit,
    onMenuClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = Color(0xFF1C1C1E)
                )
            }
            
            Text(
                text = habitName,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1C1C1E),
                modifier = Modifier.weight(1f)
            )
            
            IconButton(onClick = onMenuClick) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多",
                    tint = Color(0xFF1C1C1E)
                )
            }
        }
    }
}

// 简化的数据概览卡片
@Composable
private fun DataOverviewCard(
    habitStats: HabitStats,
    habitColor: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 2.dp
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(
                icon = Icons.Default.LocalFireDepartment,
                value = habitStats.currentStreak.toString(),
                label = "当前连击",
                color = habitColor
            )
            
            StatItem(
                icon = Icons.Default.EmojiEvents,
                value = habitStats.longestStreak.toString(),
                label = "最长连击",
                color = habitColor
            )
            
            StatItem(
                icon = Icons.Default.CheckCircle,
                value = habitStats.totalCompletions.toString(),
                label = "总完成",
                color = habitColor
            )
        }
    }
}

@Composable
private fun StatItem(
    icon: ImageVector,
    value: String,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1C1C1E)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF8E8E93)
        )
    }
}

// 简化的热力图
@Composable
private fun SimpleHeatmapCard(
    completedDates: List<LocalDate>,
    habitColor: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "最近30天",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1C1C1E)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 简化的点状热力图 - 从左到右显示，最新的在左边
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(30) { index ->
                    // 🔧 修复：从左到右显示，最新的日期在左边
                    val date = LocalDate.now().minusDays(index.toLong())
                    val isCompleted = completedDates.contains(date)
                    
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = if (isCompleted) habitColor else Color(0xFFE5E5EA),
                                shape = CircleShape
                            )
                    )
                }
            }
        }
    }
}

// 简化的统计卡片
@Composable
private fun StatisticsCard(
    habitStats: HabitStats,
    habitColor: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "统计数据",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1C1C1E)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "完成率",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF8E8E93)
                )
                
                Text(
                    text = "${habitStats.completionRate.toInt()}%",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = habitColor
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "本周完成",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF8E8E93)
                )
                
                Text(
                    text = "${habitStats.last7DaysCompletion}/7",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = habitColor
                )
            }
        }
    }
}

// 快速操作卡片
@Composable
private fun QuickActionsCard(
    onAddCheckIn: () -> Unit,
    habitColor: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "快速操作",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1C1C1E)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onAddCheckIn,
                colors = ButtonDefaults.buttonColors(
                    containerColor = habitColor,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "补打卡",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

// 简化的日期选择器对话框
@Composable
private fun SimpleDatePickerDialog(
    onDismiss: () -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    habitColor: Color
) {
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = System.currentTimeMillis()
    )
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column {
                DatePicker(
                    state = datePickerState,
                    colors = DatePickerDefaults.colors(
                        selectedDayContainerColor = habitColor,
                        todayContentColor = habitColor,
                        todayDateBorderColor = habitColor
                    )
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            datePickerState.selectedDateMillis?.let { millis ->
                                val selectedDate = Instant.ofEpochMilli(millis)
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDate()
                                onDateSelected(selectedDate)
                            }
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = habitColor)
                    ) {
                        Text("确认")
                    }
                }
            }
        }
    }
} 