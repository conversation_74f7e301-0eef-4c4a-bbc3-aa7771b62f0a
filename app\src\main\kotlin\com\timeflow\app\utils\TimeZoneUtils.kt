package com.timeflow.app.utils

import android.util.Log
import java.time.*
import java.time.format.DateTimeFormatter

/**
 * 统一的时区处理工具类
 * 确保整个应用中时间处理的一致性
 */
object TimeZoneUtils {
    private const val TAG = "TimeZoneUtils"
    
    /**
     * 应用统一使用的时区 - 系统默认时区
     */
    val APP_ZONE_ID: ZoneId = ZoneId.systemDefault()
    
    /**
     * 数据库存储使用的时区 - UTC
     */
    val DB_ZONE_ID: ZoneId = ZoneId.of("UTC")
    
    /**
     * 获取当前的LocalDate（基于系统时区）
     */
    fun getCurrentDate(): LocalDate {
        val now = LocalDate.now(APP_ZONE_ID)
        Log.d(TAG, "获取当前日期: $now (时区: $APP_ZONE_ID)")
        return now
    }
    
    /**
     * 获取当前的LocalDateTime（基于系统时区）
     */
    fun getCurrentDateTime(): LocalDateTime {
        val now = LocalDateTime.now(APP_ZONE_ID)
        Log.d(TAG, "获取当前时间: $now (时区: $APP_ZONE_ID)")
        return now
    }
    
    /**
     * 将LocalDate转换为数据库存储格式（epoch day）
     */
    fun localDateToEpochDay(date: LocalDate?): Long? {
        return date?.toEpochDay()
    }
    
    /**
     * 从数据库格式（epoch day）转换为LocalDate
     */
    fun epochDayToLocalDate(epochDay: Long?): LocalDate? {
        return epochDay?.let { LocalDate.ofEpochDay(it) }
    }
    
    /**
     * 将LocalDateTime转换为数据库存储格式（UTC时间戳）
     */
    fun localDateTimeToUtcTimestamp(dateTime: LocalDateTime?): Long? {
        return dateTime?.atZone(APP_ZONE_ID)?.withZoneSameInstant(DB_ZONE_ID)?.toEpochSecond()
    }
    
    /**
     * 从数据库格式（UTC时间戳）转换为LocalDateTime
     */
    fun utcTimestampToLocalDateTime(timestamp: Long?): LocalDateTime? {
        return timestamp?.let { 
            LocalDateTime.ofEpochSecond(it, 0, ZoneOffset.UTC)
                .atZone(DB_ZONE_ID)
                .withZoneSameInstant(APP_ZONE_ID)
                .toLocalDateTime()
        }
    }
    
    /**
     * 将LocalDateTime转换为毫秒时间戳（用于通知等）
     */
    fun localDateTimeToMillis(dateTime: LocalDateTime?): Long? {
        return dateTime?.atZone(APP_ZONE_ID)?.toInstant()?.toEpochMilli()
    }
    
    /**
     * 从毫秒时间戳转换为LocalDateTime
     */
    fun millisToLocalDateTime(millis: Long?): LocalDateTime? {
        return millis?.let {
            LocalDateTime.ofInstant(Instant.ofEpochMilli(it), APP_ZONE_ID)
        }
    }
    
    /**
     * 检查日期是否为今天
     */
    fun isToday(date: LocalDate?): Boolean {
        if (date == null) return false
        val today = getCurrentDate()
        return date == today
    }
    
    /**
     * 检查日期时间是否为今天
     */
    fun isToday(dateTime: LocalDateTime?): Boolean {
        if (dateTime == null) return false
        val today = getCurrentDate()
        return dateTime.toLocalDate() == today
    }
    
    /**
     * 检查日期是否为明天
     */
    fun isTomorrow(date: LocalDate?): Boolean {
        if (date == null) return false
        val tomorrow = getCurrentDate().plusDays(1)
        return date == tomorrow
    }
    
    /**
     * 检查日期时间是否为明天
     */
    fun isTomorrow(dateTime: LocalDateTime?): Boolean {
        if (dateTime == null) return false
        val tomorrow = getCurrentDate().plusDays(1)
        return dateTime.toLocalDate() == tomorrow
    }
    
    /**
     * 获取友好的日期显示
     */
    fun getFriendlyDateString(date: LocalDate?): String {
        if (date == null) return "未知日期"
        
        return when {
            isToday(date) -> "今天"
            isTomorrow(date) -> "明天"
            date == getCurrentDate().minusDays(1) -> "昨天"
            else -> {
                val formatter = DateTimeFormatter.ofPattern("M月d日")
                date.format(formatter)
            }
        }
    }
    
    /**
     * 获取友好的日期时间显示
     */
    fun getFriendlyDateTimeString(dateTime: LocalDateTime?): String {
        if (dateTime == null) return "未知时间"
        
        val dateStr = getFriendlyDateString(dateTime.toLocalDate())
        val timeStr = dateTime.format(DateTimeFormatter.ofPattern("HH:mm"))
        
        return "$dateStr $timeStr"
    }
    
    /**
     * 获取星期几的中文显示
     */
    fun getDayOfWeekString(date: LocalDate?): String {
        if (date == null) return "?"
        
        return when (date.dayOfWeek.value) {
            1 -> "一"
            2 -> "二"
            3 -> "三"
            4 -> "四"
            5 -> "五"
            6 -> "六"
            7 -> "日"
            else -> "?"
        }
    }
    
    /**
     * 调试信息：打印当前时区信息
     */
    fun logTimeZoneInfo() {
        Log.d(TAG, "=== 时区信息 ===")
        Log.d(TAG, "应用时区: $APP_ZONE_ID")
        Log.d(TAG, "数据库时区: $DB_ZONE_ID")
        Log.d(TAG, "当前时间: ${getCurrentDateTime()}")
        Log.d(TAG, "当前日期: ${getCurrentDate()}")
        Log.d(TAG, "===============")
    }
}
