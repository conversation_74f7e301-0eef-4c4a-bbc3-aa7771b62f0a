package com.timeflow.app.ui.components

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.ui.theme.DustyLavender
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp


/**
 * 迷你计时器状态栏 - 显示在底部导航栏上方
 * 参考网易云音乐的迷你播放器和图2的设计
 * 支持emoji显示，如👳🏻‍♂️
 * 
 * 🔧 功能说明：
 * - 点击主体区域：导航到时间追踪页面
 * - 点击播放/暂停按钮：控制计时器
 * - 点击X按钮：隐藏小组件（再次进入并离开时间追踪页面时重新显示）
 */
@Composable
fun MiniTimerBar(
    visible: Boolean,
    taskName: String,
    elapsedTime: Long,
    isRunning: Boolean,
    onPlayPauseClick: () -> Unit,
    onCloseClick: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(300)) + expandVertically(),
        exit = fadeOut(animationSpec = tween(300)) + shrinkVertically()
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .background(Color(0xE6FFFFFF)) // 白色背景，90%不透明度 (E6 = 90%)
                .clickable(onClick = {
                    Log.d("MiniTimerBar", "🎯 点击小组件，导航到时间追踪页面")
                    onClick()
                    Log.d("MiniTimerBar", "✅ 导航请求已发送")
                })
                .padding(horizontal = 16.dp, vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 🔧 任务信息和时间显示
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = taskName,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    // 🔧 显示格式化的时间和状态
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = formatTime(elapsedTime),
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 🔧 状态指示器
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    color = if (isRunning) Color(0xFF4CAF50) else Color(0xFFFF9800),
                                    shape = CircleShape
                                )
                        )
                        
                        Spacer(modifier = Modifier.width(4.dp))
                        
                        Text(
                            text = if (isRunning) "计时中" else "已暂停",
                            style = MaterialTheme.typography.labelSmall,
                            color = if (isRunning) Color(0xFF4CAF50) else Color(0xFFFF9800)
                        )
                    }
                }

                // 🔧 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 播放/暂停按钮
                    IconButton(
                        onClick = {
                            Log.d("MiniTimerBar", if (isRunning) "⏸️ 暂停计时" else "▶️ 开始计时")
                            onPlayPauseClick()
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = if (isRunning) Icons.Default.Pause else Icons.Default.PlayArrow,
                            contentDescription = if (isRunning) "暂停" else "开始",
                            tint = DustyLavender,
                            modifier = Modifier.size(20.dp)
                        )
                    }

                    // 关闭按钮
                    IconButton(
                        onClick = {
                            Log.d("MiniTimerBar", "🙈 隐藏计时小组件")
                            onCloseClick()
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "隐藏",
                            tint = Color.Gray,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 🔧 格式化时间显示
 */
private fun formatTime(seconds: Long): String {
    val hours = seconds / 3600
    val minutes = (seconds % 3600) / 60
    val secs = seconds % 60
    
    return if (hours > 0) {
        String.format("%d:%02d:%02d", hours, minutes, secs)
    } else {
        String.format("%02d:%02d", minutes, secs)
    }
} 