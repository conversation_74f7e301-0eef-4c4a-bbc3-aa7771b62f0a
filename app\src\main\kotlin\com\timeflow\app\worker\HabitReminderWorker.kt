package com.timeflow.app.worker

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.receiver.HabitAlarmReceiver
import java.util.Calendar
import java.util.concurrent.TimeUnit

/**
 * 习惯提醒工作器
 * 用于管理习惯的定时提醒
 */
class HabitReminderWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "HabitReminderWorker"
        const val KEY_HABIT_ID = "habit_id"
        const val KEY_HABIT_NAME = "habit_name"
        const val KEY_REMINDER_TIME = "reminder_time" // 格式: HH:mm
    }
    
    override suspend fun doWork(): Result {
        try {
            val habitId = inputData.getString(KEY_HABIT_ID) ?: return Result.failure()
            val habitName = inputData.getString(KEY_HABIT_NAME) ?: "习惯"
            val reminderTime = inputData.getString(KEY_REMINDER_TIME) ?: return Result.failure()
            
            // 设置提醒的闹钟
            scheduleAlarm(habitId, habitName, reminderTime)
            
            return Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "设置习惯提醒失败: ${e.message}", e)
            return Result.failure()
        }
    }
    
    /**
     * 设置每日重复的闹钟提醒
     */
    private fun scheduleAlarm(habitId: String, habitName: String, reminderTime: String) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        // 解析提醒时间
        val (hour, minute) = reminderTime.split(":").map { it.toInt() }
        
        // 创建广播意图
        val intent = Intent(context, HabitAlarmReceiver::class.java).apply {
            putExtra(HabitAlarmReceiver.EXTRA_HABIT_ID, habitId)
            putExtra(HabitAlarmReceiver.EXTRA_HABIT_NAME, habitName)
        }
        
        // 创建PendingIntent
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            habitId.hashCode(),
            intent,
            pendingIntentFlags
        )
        
        // 设置闹钟时间
        val calendar = Calendar.getInstance().apply {
            timeInMillis = System.currentTimeMillis()
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // 如果设置时间已经过去，则设置为明天的这个时间
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_YEAR, 1)
            }
        }
        
        // 设置重复闹钟
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0及以上版本需要考虑Doze模式
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                pendingIntent
            )
            
            // 由于无法直接设置重复闹钟，我们需要在通知发送后重新设置下一次的闹钟
            // 这里可以在BroadcastReceiver中处理，或者使用WorkManager
            Log.d(TAG, "习惯[$habitName]的提醒已设置: ${calendar.time}")
        } else {
            // 低版本Android可以直接设置重复闹钟
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                AlarmManager.INTERVAL_DAY,
                pendingIntent
            )
            Log.d(TAG, "习惯[$habitName]的每日提醒已设置: ${calendar.time}")
        }
    }
    
    /**
     * 取消习惯提醒
     */
    fun cancelReminder(context: Context, habitId: String) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        val intent = Intent(context, HabitAlarmReceiver::class.java)
        
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            habitId.hashCode(),
            intent,
            pendingIntentFlags
        )
        
        // 取消闹钟
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
        
        Log.d(TAG, "习惯[$habitId]的提醒已取消")
    }
} 