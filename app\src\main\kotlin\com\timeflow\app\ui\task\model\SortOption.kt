package com.timeflow.app.ui.task.model

/**
 * 任务排序选项
 */
enum class SortOption {
    PRIORITY,       // 按优先级排序
    DUE_DATE,       // 按截止日期排序
    CREATION_DATE,  // 按创建日期排序
    CREATE_TIME,    // 按创建时间排序（与CREATION_DATE同义，保留兼容性）
    NAME,           // 按名称排序
    STATUS;         // 按状态排序
    
    companion object {
        /**
         * 获取排序选项名称
         */
        fun getName(option: SortOption): String {
            return when(option) {
                PRIORITY -> "优先级"
                DUE_DATE -> "截止日期"
                CREATION_DATE, CREATE_TIME -> "创建日期"
                NAME -> "名称"
                STATUS -> "状态"
            }
        }
        
        /**
         * 获取默认排序选项
         */
        fun default() = DUE_DATE
    }
} 