package com.timeflow.app.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.TimeFlowNotificationManager
import com.timeflow.app.utils.WeeklyStats
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import javax.inject.Inject

/**
 * 通知测试服务
 * 用于模拟各种通知场景，帮助开发和测试
 */
@AndroidEntryPoint
class NotificationTestService : Service() {
    
    @Inject
    lateinit var notificationManager: TimeFlowNotificationManager
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "NotificationTestService"
        const val ACTION_TEST_TASK_REMINDER = "TEST_TASK_REMINDER"
        const val ACTION_TEST_DEADLINE_REMINDER = "TEST_DEADLINE_REMINDER"
        const val ACTION_TEST_OVERDUE_REMINDER = "TEST_OVERDUE_REMINDER"
        const val ACTION_TEST_DAILY_REVIEW = "TEST_DAILY_REVIEW"
        const val ACTION_TEST_HABIT_REMINDER = "TEST_HABIT_REMINDER"
        const val ACTION_TEST_FOCUS_START = "TEST_FOCUS_START"
        const val ACTION_TEST_FOCUS_END = "TEST_FOCUS_END"
        const val ACTION_TEST_AI_SUGGESTION = "TEST_AI_SUGGESTION"
        const val ACTION_TEST_WEEKLY_REPORT = "TEST_WEEKLY_REPORT"
        const val ACTION_TEST_ALL_NOTIFICATIONS = "TEST_ALL_NOTIFICATIONS"
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action ?: return START_NOT_STICKY
        
        Log.d(TAG, "通知测试服务收到action: $action")
        
        serviceScope.launch {
            try {
                val testSettings = createTestSettings()
                
                when (action) {
                    ACTION_TEST_TASK_REMINDER -> testTaskReminder(testSettings)
                    ACTION_TEST_DEADLINE_REMINDER -> testDeadlineReminder(testSettings)
                    ACTION_TEST_OVERDUE_REMINDER -> testOverdueReminder(testSettings)
                    ACTION_TEST_DAILY_REVIEW -> testDailyReview(testSettings)
                    ACTION_TEST_HABIT_REMINDER -> testHabitReminder(testSettings)
                    ACTION_TEST_FOCUS_START -> testFocusStart(testSettings)
                    ACTION_TEST_FOCUS_END -> testFocusEnd(testSettings)
                    ACTION_TEST_AI_SUGGESTION -> testMedicationReminder(testSettings) // 替换为用药提醒测试
                    ACTION_TEST_WEEKLY_REPORT -> testMedicationReminder(testSettings) // 替换为用药提醒测试
                    ACTION_TEST_ALL_NOTIFICATIONS -> testAllNotifications(testSettings)
                    else -> Log.w(TAG, "未知的测试action: $action")
                }
            } catch (e: Exception) {
                Log.e(TAG, "测试通知失败", e)
            } finally {
                stopSelf(startId)
            }
        }
        
        return START_NOT_STICKY
    }
    
    /**
     * 创建测试用的通知设置 - 简化后的结构
     */
    private fun createTestSettings(): NotificationSettings {
        return NotificationSettings(
            notificationsEnabled = true,
            taskRemindersEnabled = true,
            deadlineRemindersEnabled = true,
            overdueRemindersEnabled = true,
            dailyReviewEnabled = true,
            dailyReviewTime = "21:00",
            taskPersistentNotificationEnabled = true,
            // 习惯培养提醒（简化）
            habitRemindersEnabled = true,

            // 专注时间提醒（简化）
            focusRemindersEnabled = true,
            focusSessionNotificationsEnabled = true,

            // 健康管理提醒（新增用药提醒）
            medicationRemindersEnabled = true,
            medicationSoundEnabled = true,
            medicationVibrationEnabled = true,
            medicationAdvanceTime = 5,

            // 通知方式设置（简化）
            soundEnabled = true,
            vibrationEnabled = true,

            // 免打扰设置（简化）
            doNotDisturbEnabled = false,
            doNotDisturbStartTime = "22:00",
            doNotDisturbEndTime = "08:00",

            // 提醒时间设置（统一简化）
            defaultReminderTime = 15
        )
    }
    
    /**
     * 测试任务提醒通知
     */
    private suspend fun testTaskReminder(settings: NotificationSettings) {
        notificationManager.showTaskReminder(
            taskId = "test_task_001",
            taskTitle = "完成项目报告",
            dueTime = "今天 18:00",
            priority = "高",
            settings = settings
        )
        Log.d(TAG, "已发送任务提醒测试通知")
    }
    
    /**
     * 测试截止日期提醒通知
     */
    private suspend fun testDeadlineReminder(settings: NotificationSettings) {
        notificationManager.showDeadlineReminder(
            taskId = "test_task_002",
            taskTitle = "提交设计方案",
            deadline = "明天 12:00",
            settings = settings
        )
        Log.d(TAG, "已发送截止日期提醒测试通知")
    }
    
    /**
     * 测试逾期提醒通知
     */
    private suspend fun testOverdueReminder(settings: NotificationSettings) {
        notificationManager.showOverdueReminder(
            taskId = "test_task_003",
            taskTitle = "更新个人简历",
            overdueDays = 3,
            settings = settings
        )
        Log.d(TAG, "已发送逾期提醒测试通知")
    }
    
    /**
     * 测试每日回顾通知
     */
    private suspend fun testDailyReview(settings: NotificationSettings) {
        notificationManager.showDailyReview(
            completedTasks = 8,
            totalTasks = 12,
            settings = settings
        )
        Log.d(TAG, "已发送每日回顾测试通知")
    }
    
    /**
     * 测试习惯提醒通知
     */
    private suspend fun testHabitReminder(settings: NotificationSettings) {
        notificationManager.showHabitReminder(
            habitId = "test_habit_001",
            habitName = "晨间冥想",
            streakCount = 14,
            settings = settings
        )
        Log.d(TAG, "已发送习惯提醒测试通知")
    }
    
    /**
     * 测试专注开始提醒
     */
    private suspend fun testFocusStart(settings: NotificationSettings) {
        notificationManager.showFocusStartReminder(
            sessionName = "深度工作时间",
            duration = 45,
            settings = settings
        )
        Log.d(TAG, "已发送专注开始测试通知")
    }
    
    /**
     * 测试专注结束提醒
     */
    private suspend fun testFocusEnd(settings: NotificationSettings) {
        notificationManager.showFocusEndReminder(
            sessionName = "深度工作时间",
            actualDuration = 43,
            settings = settings
        )
        Log.d(TAG, "已发送专注结束测试通知")
    }
    
    /**
     * 测试用药提醒通知
     */
    private suspend fun testMedicationReminder(settings: NotificationSettings) {
        notificationManager.showMedicationReminder(
            medicationId = "test_med_001",
            medicationName = "维生素D",
            dosage = "1片",
            reminderTime = "现在",
            settings = settings
        )
        Log.d(TAG, "已发送用药提醒测试通知")
    }
    
    /**
     * 测试所有类型的通知（延时发送）
     */
    private suspend fun testAllNotifications(settings: NotificationSettings) {
        Log.d(TAG, "开始发送所有类型的测试通知...")
        
        // 依次发送不同类型的通知，间隔2秒
        testTaskReminder(settings)
        delay(2000)
        
        testDeadlineReminder(settings)
        delay(2000)
        
        testHabitReminder(settings)
        delay(2000)
        
        testFocusStart(settings)
        delay(2000)

        testMedicationReminder(settings)
        delay(2000)

        testDailyReview(settings)
        
        Log.d(TAG, "所有测试通知发送完成")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        Log.d(TAG, "通知测试服务已停止")
    }
} 