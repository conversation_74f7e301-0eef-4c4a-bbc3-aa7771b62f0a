# 多云存储同步功能使用指南

## 功能概述

TimeFlow应用现已支持多个云存储服务的数据同步功能，让您可以安全地备份和恢复应用数据。

## 支持的云服务

### ✅ 当前可用
- **Amazon S3** - 完全支持，推荐使用

### 🚧 即将支持
- **七牛云** - 架构已完成，等待SDK集成
- **腾讯云COS** - 架构已完成，等待SDK集成  
- **阿里云OSS** - 架构已完成，等待SDK集成

## 使用步骤

### 1. 进入同步设置

1. 打开TimeFlow应用
2. 进入"设置"页面
3. 选择"同步设置"选项

### 2. 配置云存储

#### AWS S3 配置步骤

1. **选择云服务提供商**
   - 在云存储配置区域，确保选择"Amazon S3"

2. **填写配置信息**
   - **区域节点(Endpoint)**: 填写S3端点，如 `s3.amazonaws.com`
   - **存储桶(Bucket)**: 填写您的S3存储桶名称
   - **访问密钥(AccessKeyId)**: 填写AWS访问密钥ID
   - **密钥(SecretAccessKey)**: 填写AWS秘密访问密钥
   - **地区**: 填写AWS区域，如 `us-east-1`

3. **测试连接**
   - 点击"扫描并迁移资源到S3"按钮
   - 等待连接测试结果
   - 如果显示"连接成功"，说明配置正确

### 3. 数据同步

#### 手动同步
1. 配置完成后，返回同步设置主页面
2. 点击"手动同步"按钮
3. 等待同步完成

#### 自动同步
1. 在同步设置中开启"自动同步"
2. 设置同步间隔（默认24小时）
3. 应用将定期自动备份数据

### 4. 数据恢复

1. 在同步设置页面
2. 点击"数据恢复"按钮
3. 系统将从云存储下载最新备份
4. 确认恢复操作

## AWS S3 配置指南

### 1. 创建AWS账户
1. 访问 [AWS官网](https://aws.amazon.com/)
2. 注册AWS账户
3. 完成身份验证

### 2. 创建S3存储桶
1. 登录AWS控制台
2. 搜索并进入"S3"服务
3. 点击"创建存储桶"
4. 输入存储桶名称（全球唯一）
5. 选择区域（建议选择离您较近的区域）
6. 保持默认设置，点击"创建存储桶"

### 3. 创建访问密钥
1. 在AWS控制台搜索"IAM"
2. 进入IAM服务
3. 点击"用户" → "创建用户"
4. 输入用户名，如"timeflow-sync"
5. 选择"编程访问"
6. 附加策略：选择"AmazonS3FullAccess"（或创建自定义策略）
7. 完成创建，保存访问密钥ID和秘密访问密钥

### 4. 安全建议
- 为TimeFlow创建专用的IAM用户
- 只授予必要的S3权限
- 定期轮换访问密钥
- 启用S3存储桶的版本控制

## 数据格式说明

### 备份文件格式
- **文件名**: `timeflow_backup_YYYYMMDD_HHMMSS.json`
- **格式**: JSON
- **内容**: 包含所有任务数据和元数据

### 备份内容
- 任务信息（标题、描述、状态等）
- 时间信息（创建时间、截止时间等）
- 分类和标签
- 优先级设置
- 自定义字段

## 故障排除

### 常见问题

#### 1. 连接失败
**可能原因**:
- 网络连接问题
- 访问密钥错误
- 存储桶名称错误
- 区域设置错误

**解决方案**:
- 检查网络连接
- 验证AWS凭证
- 确认存储桶名称和区域
- 检查IAM权限设置

#### 2. 上传失败
**可能原因**:
- 存储桶权限不足
- 网络中断
- 文件大小限制

**解决方案**:
- 检查IAM用户权限
- 重试上传
- 检查网络稳定性

#### 3. 下载失败
**可能原因**:
- 备份文件不存在
- 权限不足
- 网络问题

**解决方案**:
- 确认已有备份文件
- 检查读取权限
- 重试下载

### 错误代码说明

- **连接超时**: 网络连接问题，请检查网络设置
- **认证失败**: 访问密钥错误，请检查AWS凭证
- **权限不足**: IAM权限不够，请检查用户权限
- **存储桶不存在**: 存储桶名称错误或不存在

## 安全和隐私

### 数据安全
- 所有数据传输使用HTTPS加密
- AWS S3提供服务端加密
- 访问密钥本地安全存储

### 隐私保护
- 数据仅存储在您的私人S3存储桶中
- TimeFlow不会访问您的云存储数据
- 您完全控制数据的访问权限

### 最佳实践
1. 使用强密码保护AWS账户
2. 启用AWS账户的双因素认证
3. 定期检查S3访问日志
4. 设置存储桶的访问策略
5. 定期备份重要数据

## 费用说明

### AWS S3费用
- 存储费用：根据数据量计算
- 请求费用：上传/下载操作费用
- 数据传输费用：下载数据的费用

### 成本优化建议
1. 选择合适的存储类别
2. 设置生命周期策略
3. 监控使用量
4. 删除不需要的旧备份

## 技术支持

### 获取帮助
1. 查看应用内帮助文档
2. 检查错误日志
3. 联系技术支持

### 反馈问题
如果遇到问题，请提供：
- 错误信息截图
- 操作步骤描述
- 设备和系统信息
- 网络环境信息

## 更新说明

### 当前版本特性
- 支持AWS S3完整功能
- 现代化配置界面
- 自动错误检测
- 向后兼容性

### 即将推出
- 七牛云存储支持
- 腾讯云COS支持
- 阿里云OSS支持
- 增量同步功能
- 数据压缩优化

---

**注意**: 本功能需要稳定的网络连接。建议在WiFi环境下进行大量数据的同步操作。
