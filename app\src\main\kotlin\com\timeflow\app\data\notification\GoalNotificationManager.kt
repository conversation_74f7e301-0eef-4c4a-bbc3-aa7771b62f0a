package com.timeflow.app.data.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.timeflow.app.R
import com.timeflow.app.data.model.Goal
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 目标通知管理器 - 智能提醒系统
 * 包括时间提醒、进度提醒和成就庆祝
 */
@Singleton
class GoalNotificationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val CHANNEL_ID_REMINDERS = "goal_reminders"
        private const val CHANNEL_ID_PROGRESS = "goal_progress"
        private const val CHANNEL_ID_ACHIEVEMENTS = "goal_achievements"
        
        private const val NOTIFICATION_ID_DAILY_REMINDER = 1001
        private const val NOTIFICATION_ID_PROGRESS_UPDATE = 1002
        private const val NOTIFICATION_ID_ACHIEVEMENT = 1003
        private const val NOTIFICATION_ID_DEADLINE_WARNING = 1004
    }
    
    private val notificationManager = NotificationManagerCompat.from(context)
    
    init {
        createNotificationChannels()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_ID_REMINDERS,
                    "目标提醒",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "日常目标提醒和截止日期提醒"
                },
                
                NotificationChannel(
                    CHANNEL_ID_PROGRESS,
                    "进度更新",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "目标进度更新和统计信息"
                },
                
                NotificationChannel(
                    CHANNEL_ID_ACHIEVEMENTS,
                    "成就庆祝",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "目标完成和里程碑达成庆祝"
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                systemNotificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    /**
     * 发送每日目标提醒
     */
    fun sendDailyReminder(goals: List<Goal>) {
        val activeGoals = goals.filter { it.status == "进行中" }
        if (activeGoals.isEmpty()) return
        
        val title = "今日目标提醒"
        val content = when (activeGoals.size) {
            1 -> "继续努力完成「${activeGoals.first().title}」"
            else -> "您有 ${activeGoals.size} 个目标等待完成"
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_REMINDERS)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText(buildDailyReminderText(activeGoals))
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(createMainActivityPendingIntent())
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_DAILY_REMINDER, notification)
    }
    
    /**
     * 发送进度更新通知
     */
    fun sendProgressUpdate(goal: Goal, oldProgress: Float, newProgress: Float) {
        val progressIncrease = newProgress - oldProgress
        if (progressIncrease <= 0) return
        
        val title = "目标进度更新"
        val content = "「${goal.title}」进度提升了 ${(progressIncrease * 100).toInt()}%"
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_PROGRESS)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("当前进度：${(newProgress * 100).toInt()}%\n" +
                            "距离完成还需：${((1 - newProgress) * 100).toInt()}%")
            )
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .setContentIntent(createGoalDetailPendingIntent(goal.id))
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_PROGRESS_UPDATE, notification)
    }
    
    /**
     * 发送成就庆祝通知
     */
    fun sendAchievementNotification(goal: Goal, achievementType: AchievementType) {
        val (title, content, emoji) = when (achievementType) {
            AchievementType.GOAL_COMPLETED -> Triple(
                "🎉 目标完成！",
                "恭喜您完成了「${goal.title}」！",
                "🎉"
            )
            AchievementType.MILESTONE_REACHED -> Triple(
                "🏆 里程碑达成！",
                "「${goal.title}」达到了重要里程碑！",
                "🏆"
            )
            AchievementType.STREAK_ACHIEVED -> Triple(
                "🔥 连续达成！",
                "您已连续完成目标任务，保持这个势头！",
                "🔥"
            )
            AchievementType.PROGRESS_MILESTONE -> Triple(
                "📈 进度里程碑！",
                "「${goal.title}」已完成 ${(goal.progress * 100).toInt()}%！",
                "📈"
            )
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ACHIEVEMENTS)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText(buildAchievementText(goal, achievementType))
            )
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(createGoalDetailPendingIntent(goal.id))
            .addAction(
                android.R.drawable.ic_menu_share,
                "分享成就",
                createShareAchievementPendingIntent(goal, achievementType)
            )
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_ACHIEVEMENT, notification)
    }
    
    /**
     * 发送截止日期警告
     */
    fun sendDeadlineWarning(goal: Goal, daysLeft: Int) {
        val title = when {
            daysLeft <= 0 -> "⚠️ 目标已过期"
            daysLeft == 1 -> "⚠️ 目标明天到期"
            daysLeft <= 3 -> "⚠️ 目标即将到期"
            daysLeft <= 7 -> "📅 目标一周内到期"
            else -> return // 不发送通知
        }
        
        val content = when {
            daysLeft <= 0 -> "「${goal.title}」已经过期，请及时处理"
            daysLeft == 1 -> "「${goal.title}」明天就要到期了"
            else -> "「${goal.title}」还有 $daysLeft 天到期"
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_REMINDERS)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText(buildDeadlineWarningText(goal, daysLeft))
            )
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(createGoalDetailPendingIntent(goal.id))
            .addAction(
                android.R.drawable.ic_menu_edit,
                "调整目标",
                createEditGoalPendingIntent(goal.id)
            )
            .build()
        
        notificationManager.notify(NOTIFICATION_ID_DEADLINE_WARNING, notification)
    }
    
    /**
     * 发送智能建议通知
     */
    fun sendSmartSuggestion(suggestion: SmartSuggestion) {
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_REMINDERS)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle("💡 智能建议")
            .setContentText(suggestion.title)
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText(suggestion.description)
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(createMainActivityPendingIntent())
            .build()
        
        notificationManager.notify(suggestion.id.hashCode(), notification)
    }
    
    /**
     * 构建每日提醒文本
     */
    private fun buildDailyReminderText(goals: List<Goal>): String {
        return buildString {
            appendLine("今日待完成的目标：")
            goals.take(3).forEach { goal ->
                val progress = (goal.progress * 100).toInt()
                appendLine("• ${goal.title} (${progress}%)")
            }
            if (goals.size > 3) {
                appendLine("还有 ${goals.size - 3} 个目标...")
            }
            appendLine("\n点击查看详情，开始今天的努力！")
        }
    }
    
    /**
     * 构建成就文本
     */
    private fun buildAchievementText(goal: Goal, achievementType: AchievementType): String {
        return buildString {
            when (achievementType) {
                AchievementType.GOAL_COMPLETED -> {
                    appendLine("🎉 恭喜您成功完成了「${goal.title}」！")
                    appendLine("您的坚持和努力得到了回报。")
                    goal.dueDate?.let { dueDate ->
                        val formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
                        appendLine("完成时间：${LocalDateTime.now().format(formatter)}")
                    }
                }
                AchievementType.MILESTONE_REACHED -> {
                    appendLine("🏆 「${goal.title}」达到了重要里程碑！")
                    appendLine("当前进度：${(goal.progress * 100).toInt()}%")
                    appendLine("继续保持这个势头！")
                }
                AchievementType.STREAK_ACHIEVED -> {
                    appendLine("🔥 您已连续完成目标任务！")
                    appendLine("坚持就是胜利，继续加油！")
                }
                AchievementType.PROGRESS_MILESTONE -> {
                    appendLine("📈 进度里程碑达成！")
                    appendLine("「${goal.title}」已完成 ${(goal.progress * 100).toInt()}%")
                    appendLine("距离目标越来越近了！")
                }
            }
        }
    }
    
    /**
     * 构建截止日期警告文本
     */
    private fun buildDeadlineWarningText(goal: Goal, daysLeft: Int): String {
        return buildString {
            when {
                daysLeft <= 0 -> {
                    appendLine("⚠️ 目标「${goal.title}」已经过期")
                    appendLine("当前进度：${(goal.progress * 100).toInt()}%")
                    appendLine("建议：调整截止日期或重新规划目标")
                }
                daysLeft <= 3 -> {
                    appendLine("⚠️ 目标「${goal.title}」即将到期")
                    appendLine("剩余时间：$daysLeft 天")
                    appendLine("当前进度：${(goal.progress * 100).toInt()}%")
                    appendLine("建议：加快进度或调整目标范围")
                }
                else -> {
                    appendLine("📅 目标「${goal.title}」一周内到期")
                    appendLine("剩余时间：$daysLeft 天")
                    appendLine("当前进度：${(goal.progress * 100).toInt()}%")
                    appendLine("建议：制定详细的完成计划")
                }
            }
        }
    }
    
    /**
     * 创建主活动PendingIntent
     */
    private fun createMainActivityPendingIntent(): PendingIntent {
        val intent = Intent(context, Class.forName("com.timeflow.app.MainActivity"))
        return PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * 创建目标详情PendingIntent
     */
    private fun createGoalDetailPendingIntent(goalId: String): PendingIntent {
        val intent = Intent(context, Class.forName("com.timeflow.app.MainActivity")).apply {
            putExtra("goal_id", goalId)
            putExtra("action", "view_goal_detail")
        }
        return PendingIntent.getActivity(
            context,
            goalId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * 创建编辑目标PendingIntent
     */
    private fun createEditGoalPendingIntent(goalId: String): PendingIntent {
        val intent = Intent(context, Class.forName("com.timeflow.app.MainActivity")).apply {
            putExtra("goal_id", goalId)
            putExtra("action", "edit_goal")
        }
        return PendingIntent.getActivity(
            context,
            goalId.hashCode() + 1000,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * 创建分享成就PendingIntent
     */
    private fun createShareAchievementPendingIntent(goal: Goal, achievementType: AchievementType): PendingIntent {
        val shareText = when (achievementType) {
            AchievementType.GOAL_COMPLETED -> "我刚刚完成了目标「${goal.title}」！🎉"
            AchievementType.MILESTONE_REACHED -> "我的目标「${goal.title}」达到了重要里程碑！🏆"
            AchievementType.STREAK_ACHIEVED -> "我已连续完成目标任务！🔥"
            AchievementType.PROGRESS_MILESTONE -> "我的目标「${goal.title}」已完成 ${(goal.progress * 100).toInt()}%！📈"
        }
        
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
        }
        
        return PendingIntent.getActivity(
            context,
            goal.id.hashCode() + 2000,
            Intent.createChooser(intent, "分享成就"),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }
    
    /**
     * 取消所有通知
     */
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
    }
    
    /**
     * 取消特定通知
     */
    fun cancelNotification(notificationId: Int) {
        notificationManager.cancel(notificationId)
    }
}

/**
 * 成就类型
 */
enum class AchievementType {
    GOAL_COMPLETED,      // 目标完成
    MILESTONE_REACHED,   // 里程碑达成
    STREAK_ACHIEVED,     // 连续达成
    PROGRESS_MILESTONE   // 进度里程碑
}

/**
 * 智能建议
 */
data class SmartSuggestion(
    val id: String,
    val title: String,
    val description: String,
    val type: SuggestionType,
    val priority: Int = 0
)

/**
 * 建议类型
 */
enum class SuggestionType {
    TIME_MANAGEMENT,     // 时间管理
    GOAL_ADJUSTMENT,     // 目标调整
    HABIT_FORMATION,     // 习惯养成
    MOTIVATION_BOOST     // 动机提升
}
