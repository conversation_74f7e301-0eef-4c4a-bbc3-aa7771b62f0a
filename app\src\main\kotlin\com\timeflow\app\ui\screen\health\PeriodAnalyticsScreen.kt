package com.timeflow.app.ui.screen.health

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.entity.SymptomRecord
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel
import com.timeflow.app.ui.viewmodel.PeriodStatistics
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity
import com.timeflow.app.data.analytics.AdvancedAnalytics
import com.timeflow.app.data.analytics.TrendDirection
import com.timeflow.app.data.analytics.HealthIndicator
import com.timeflow.app.data.analytics.HealthStatus
import com.timeflow.app.data.analytics.MonthlyData
import com.timeflow.app.data.analytics.calculateAdvancedAnalytics
import com.timeflow.app.utils.CycleUtils
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.math.roundToInt

/**
 * 经期统计分析界面
 * 基于实际历史数据进行深度分析
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PeriodAnalyticsScreen(
    viewModel: MenstrualCycleViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }

    // 🔧 修复：处理状态栏重叠问题
    DisposableEffect(Unit) {
        var originalStatusBarColor: Int = 0
        var originalNavigationBarColor: Int = 0

        activity?.let { act ->
            val window = act.window

            try {
                // 保存原始状态
                originalStatusBarColor = window.statusBarColor
                originalNavigationBarColor = window.navigationBarColor

                // 🔧 使用统计页面专用的系统栏设置，解决状态栏重叠问题
                SystemBarManager.setupStatisticsPageSystemBars(act)

                android.util.Log.d("PeriodAnalyticsScreen", "状态栏设置成功")
            } catch (e: Exception) {
                android.util.Log.e("PeriodAnalyticsScreen", "设置状态栏出错: ${e.message}")
            }
        }

        onDispose {
            activity?.let { act ->
                try {
                    // 恢复原始颜色
                    act.window.statusBarColor = originalStatusBarColor
                    act.window.navigationBarColor = originalNavigationBarColor

                    android.util.Log.d("PeriodAnalyticsScreen", "状态栏恢复成功")
                } catch (e: Exception) {
                    android.util.Log.e("PeriodAnalyticsScreen", "恢复状态栏出错: ${e.message}")
                }
            }
        }
    }

    val historicalPeriods by viewModel.getHistoricalPeriods().collectAsState(initial = emptyList())
    val statistics by viewModel.getPeriodStatistics().collectAsState(initial = PeriodStatistics())

    val analyticsData = remember(historicalPeriods) {
        calculateAdvancedAnalytics(historicalPeriods)
    }
    
    Scaffold(
        containerColor = Color(0xFFFFF9FA),
        topBar = {
            // 🔧 修复：参照MenstrualCycleScreen的成功实现，添加状态栏空间
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
                    .height(56.dp)
                    .background(Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.Black
                        )
                    }

                    Text(
                        text = "数据分析",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 基础统计卡片
            item {
                BasicStatisticsCard(statistics = statistics)
            }
            
            // 周期趋势分析
            item {
                CycleTrendCard(analyticsData = analyticsData)
            }
            
            // 规律性分析
            item {
                RegularityAnalysisCard(analyticsData = analyticsData)
            }
            
            // 健康指标
            item {
                HealthIndicatorsCard(analyticsData = analyticsData)
            }

            // 🔧 融合：症状统计（从MenstrualCycleStatsScreen迁移）
            item {
                SymptomsStatisticsCard(viewModel = viewModel)
            }

            // 🔧 融合：AI智能分析（基于实际数据和AI API）
            item {
                AIInsightsCard(
                    statistics = statistics,
                    analyticsData = analyticsData,
                    viewModel = viewModel
                )
            }

            // 预测准确性
            item {
                PredictionAccuracyCard(analyticsData = analyticsData)
            }

            // 月度对比
            if (analyticsData.monthlyComparison.isNotEmpty()) {
                item {
                    MonthlyComparisonCard(monthlyData = analyticsData.monthlyComparison)
                }
            }

            // 🔧 融合：历史趋势图表（从MenstrualCycleStatsScreen迁移）
            item {
                HistoricalTrendsCard(analyticsData = analyticsData)
            }
        }
    }
}

/**
 * 基础统计卡片
 */
@Composable
fun BasicStatisticsCard(statistics: PeriodStatistics) {
    AnalyticsCard(
        title = "基础统计",
        icon = Icons.Default.Analytics
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticColumn(
                value = "${statistics.totalCycles}",
                label = "记录周期",
                color = Color(0xFF2196F3)
            )
            
            StatisticColumn(
                value = "${statistics.averageCycleLength.roundToInt()}天",
                label = "平均周期",
                color = Color(0xFF4CAF50)
            )
            
            StatisticColumn(
                value = "${statistics.averagePeriodLength.roundToInt()}天",
                label = "平均经期",
                color = Color(0xFFFF9800)
            )
        }
        
        if (statistics.totalCycles >= 3) {
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticColumn(
                    value = "${statistics.shortestCycle}-${statistics.longestCycle}天",
                    label = "周期范围",
                    color = Color(0xFF9C27B0)
                )
                
                StatisticColumn(
                    value = "${statistics.shortestPeriod}-${statistics.longestPeriod}天",
                    label = "经期范围",
                    color = Color(0xFFE91E63)
                )
                
                StatisticColumn(
                    value = CycleUtils.getRegularityText(statistics.cycleRegularity),
                    label = "规律性",
                    color = CycleUtils.getRegularityColor(statistics.cycleRegularity)
                )
            }
        }
    }
}

/**
 * 周期趋势分析卡片
 */
@Composable
fun CycleTrendCard(analyticsData: AdvancedAnalytics) {
    AnalyticsCard(
        title = "周期趋势",
        icon = Icons.Default.TrendingUp
    ) {
        Column {
            // 趋势指标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                TrendIndicator(
                    label = "周期趋势",
                    trend = analyticsData.cycleTrend,
                    value = "${analyticsData.cycleTrendValue.roundToInt()}天"
                )
                
                TrendIndicator(
                    label = "经期趋势",
                    trend = analyticsData.periodTrend,
                    value = "${analyticsData.periodTrendValue.roundToInt()}天"
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 变异系数
            InfoRow(
                label = "周期变异系数",
                value = "${(analyticsData.cycleVariability * 100).roundToInt()}%",
                description = CycleUtils.getVariabilityDescription(analyticsData.cycleVariability)
            )
        }
    }
}

/**
 * 规律性分析卡片
 */
@Composable
fun RegularityAnalysisCard(analyticsData: AdvancedAnalytics) {
    AnalyticsCard(
        title = "规律性分析",
        icon = Icons.Default.CheckCircle
    ) {
        Column {
            // 规律性评分
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "规律性评分",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = "${analyticsData.regularityScore}/100",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleUtils.getScoreColor(analyticsData.regularityScore)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 规律性进度条
            LinearProgressIndicator(
                progress = analyticsData.regularityScore / 100f,
                modifier = Modifier.fillMaxWidth(),
                color = CycleUtils.getScoreColor(analyticsData.regularityScore),
                trackColor = Color.LightGray
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 详细分析
            InfoRow(
                label = "标准差",
                value = "${analyticsData.standardDeviation.roundToInt()}天",
                description = "周期长度的标准差"
            )
            
            InfoRow(
                label = "最大偏差",
                value = "${analyticsData.maxDeviation}天",
                description = "与平均值的最大偏差"
            )
        }
    }
}

/**
 * 健康指标卡片
 */
@Composable
fun HealthIndicatorsCard(analyticsData: AdvancedAnalytics) {
    AnalyticsCard(
        title = "健康指标",
        icon = Icons.Default.Favorite
    ) {
        Column {
            analyticsData.healthIndicators.forEach { indicator ->
                HealthIndicatorRow(indicator = indicator)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 预测准确性卡片
 */
@Composable
fun PredictionAccuracyCard(analyticsData: AdvancedAnalytics) {
    AnalyticsCard(
        title = "预测准确性",
        icon = Icons.Default.Psychology
    ) {
        Column {
            InfoRow(
                label = "预测准确率",
                value = "${analyticsData.predictionAccuracy}%",
                description = "基于历史数据的预测准确性"
            )
            
            InfoRow(
                label = "平均误差",
                value = "${analyticsData.averageError}天",
                description = "预测与实际的平均偏差"
            )
        }
    }
}

/**
 * 月度对比卡片
 */
@Composable
fun MonthlyComparisonCard(monthlyData: List<MonthlyData>) {
    AnalyticsCard(
        title = "月度对比",
        icon = Icons.Default.CalendarMonth
    ) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(monthlyData.takeLast(6)) { data ->
                MonthlyDataItem(data = data)
            }
        }
    }
}

/**
 * 分析卡片容器
 */
@Composable
fun AnalyticsCard(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color(0xFF880E4F),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.DarkGray
                )
            }
            
            content()
        }
    }
}

/**
 * 统计列
 */
@Composable
fun StatisticColumn(
    value: String,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 趋势指示器
 */
@Composable
fun TrendIndicator(
    label: String,
    trend: TrendDirection,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (trend) {
                    TrendDirection.INCREASING -> Icons.Default.TrendingUp
                    TrendDirection.DECREASING -> Icons.Default.TrendingDown
                    TrendDirection.STABLE -> Icons.Default.TrendingFlat
                },
                contentDescription = null,
                tint = when (trend) {
                    TrendDirection.INCREASING -> Color(0xFFE91E63)
                    TrendDirection.DECREASING -> Color(0xFF4CAF50)
                    TrendDirection.STABLE -> Color.Gray
                },
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = value,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray
            )
        }

        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 信息行
 */
@Composable
fun InfoRow(
    label: String,
    value: String,
    description: String
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                fontSize = 14.sp,
                color = Color.Gray
            )

            Text(
                text = value,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray
            )
        }

        Text(
            text = description,
            fontSize = 12.sp,
            color = Color.LightGray
        )
    }
}

/**
 * 健康指标行
 */
@Composable
fun HealthIndicatorRow(indicator: HealthIndicator) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (indicator.status) {
                    HealthStatus.NORMAL -> Icons.Default.CheckCircle
                    HealthStatus.WARNING -> Icons.Default.Warning
                    HealthStatus.ATTENTION -> Icons.Default.Error
                },
                contentDescription = null,
                tint = when (indicator.status) {
                    HealthStatus.NORMAL -> Color(0xFF4CAF50)
                    HealthStatus.WARNING -> Color(0xFFFF9800)
                    HealthStatus.ATTENTION -> Color(0xFFE91E63)
                },
                modifier = Modifier.size(16.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = indicator.name,
                fontSize = 14.sp,
                color = Color.DarkGray
            )
        }

        Text(
            text = indicator.value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = when (indicator.status) {
                HealthStatus.NORMAL -> Color(0xFF4CAF50)
                HealthStatus.WARNING -> Color(0xFFFF9800)
                HealthStatus.ATTENTION -> Color(0xFFE91E63)
            }
        )
    }
}

/**
 * 月度数据项
 */
@Composable
fun MonthlyDataItem(data: MonthlyData) {
    Card(
        modifier = Modifier.width(80.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF5F5F5)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = data.month,
                fontSize = 12.sp,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "${data.averageCycle}天",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray
            )

            Text(
                text = "${data.cycleCount}个",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 🔧 融合：症状统计卡片（基于实际数据）
 */
@Composable
fun SymptomsStatisticsCard(viewModel: MenstrualCycleViewModel) {
    val allSymptoms by viewModel.allSymptoms.collectAsState()

    // 🔧 修复：基于实际数据计算症状统计
    val symptomStats = remember(allSymptoms) {
        calculateSymptomStatistics(allSymptoms)
    }

    AnalyticsCard(
        title = "症状统计",
        icon = Icons.Default.Healing
    ) {
        Column {
            if (symptomStats.isEmpty()) {
                // 无数据时的提示
                Text(
                    text = "暂无症状记录数据",
                    fontSize = 14.sp,
                    color = Color(0xFF9CA3AF),
                    modifier = Modifier.padding(16.dp)
                )
            } else {
                // 显示实际症状统计
                symptomStats.take(5).forEach { (symptom, percentage) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = symptom,
                        fontSize = 14.sp,
                        color = Color.DarkGray,
                        modifier = Modifier.weight(1f)
                    )

                    LinearProgressIndicator(
                        progress = percentage / 100f,
                        modifier = Modifier
                            .weight(2f)
                            .padding(horizontal = 8.dp),
                        color = Color(0xFFE91E63),
                        trackColor = Color.LightGray
                    )

                    Text(
                        text = "$percentage%",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier.width(40.dp),
                        textAlign = TextAlign.End
                    )
                }
                } // 🔧 修复：添加forEach的闭合大括号
            }
        }
    }
}

/**
 * 🔧 融合：历史趋势图表卡片（从MenstrualCycleStatsScreen迁移）
 */
@Composable
fun HistoricalTrendsCard(analyticsData: AdvancedAnalytics) {
    AnalyticsCard(
        title = "历史趋势",
        icon = Icons.Default.Timeline
    ) {
        Column {
            // 趋势说明
            Text(
                text = "最近6个月周期变化趋势",
                fontSize = 14.sp,
                color = Color(0xFF666666),
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 简化的趋势指标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                TrendBubble(
                    label = "周期趋势",
                    trend = analyticsData.cycleTrend,
                    value = "${analyticsData.cycleTrendValue.roundToInt()}天"
                )

                TrendBubble(
                    label = "经期趋势",
                    trend = analyticsData.periodTrend,
                    value = "${analyticsData.periodTrendValue.roundToInt()}天"
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 趋势分析提示
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFFF0F9FF)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = "💡",
                        fontSize = 20.sp,
                        modifier = Modifier.padding(end = 8.dp)
                    )

                    Text(
                        text = generateTrendInsight(analyticsData),
                        fontSize = 14.sp,
                        color = Color(0xFF374151),
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }
}

/**
 * 趋势气泡组件
 */
@Composable
fun TrendBubble(
    label: String,
    trend: TrendDirection,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 趋势图标
        val (icon, color) = when (trend) {
            TrendDirection.INCREASING -> Icons.Default.TrendingUp to Color(0xFFEF4444)
            TrendDirection.DECREASING -> Icons.Default.TrendingDown to Color(0xFF10B981)
            TrendDirection.STABLE -> Icons.Default.TrendingFlat to Color(0xFF6B7280)
        }

        Box(
            modifier = Modifier
                .size(48.dp)
                .background(
                    color = color.copy(alpha = 0.1f),
                    shape = androidx.compose.foundation.shape.CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF1F2937)
        )

        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF6B7280)
        )
    }
}

/**
 * 生成趋势洞察
 */
fun generateTrendInsight(analyticsData: AdvancedAnalytics): String {
    return when {
        analyticsData.cycleTrend == TrendDirection.STABLE &&
        analyticsData.periodTrend == TrendDirection.STABLE ->
            "您的周期非常规律！继续保持健康的生活方式。"

        analyticsData.cycleTrend == TrendDirection.INCREASING ->
            "周期有延长趋势，可能与压力、饮食或运动变化有关。建议关注生活规律。"

        analyticsData.cycleTrend == TrendDirection.DECREASING ->
            "周期有缩短趋势，如果持续建议咨询医生。"

        else -> "继续记录数据，我们将为您提供更准确的分析。"
    }
}

/**
 * 计算症状统计
 */
fun calculateSymptomStatistics(allSymptoms: List<SymptomRecord>): List<Pair<String, Int>> {
    if (allSymptoms.isEmpty()) return emptyList()

    // 🔧 修复：基于SymptomRecord的symptomType字段统计
    val symptomCounts = allSymptoms
        .groupingBy { it.symptomType }
        .eachCount()

    val totalRecords = allSymptoms.size

    // 计算百分比并排序
    return symptomCounts
        .map { (symptom, count) ->
            val percentage = (count * 100) / totalRecords
            symptom to percentage
        }
        .sortedByDescending { it.second }
}

/**
 * 🔧 融合：AI智能总结卡片（基于实际数据和AI API）
 */
@Composable
fun AIInsightsCard(
    statistics: PeriodStatistics,
    analyticsData: AdvancedAnalytics,
    viewModel: MenstrualCycleViewModel
) {
    var aiInsight by remember { mutableStateOf<String?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var hasError by remember { mutableStateOf(false) }

    // 🔧 修复：基于实际数据调用AI API
    LaunchedEffect(statistics, analyticsData) {
        if (statistics.totalCycles > 0) {
            isLoading = true
            hasError = false

            try {
                val insight = generateAIInsight(statistics, analyticsData)
                aiInsight = insight
            } catch (e: Exception) {
                hasError = true
                aiInsight = "AI分析暂时不可用，请稍后再试"
            } finally {
                isLoading = false
            }
        }
    }

    AnalyticsCard(
        title = "AI智能分析",
        icon = Icons.Default.Psychology
    ) {
        Column {
            when {
                isLoading -> {
                    // 加载状态
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "AI正在分析您的数据...",
                            fontSize = 14.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                statistics.totalCycles == 0 -> {
                    // 无数据状态
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFF9FAFB)
                        ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.AutoAwesome,
                            contentDescription = null,
                            tint = Color(0xFF9C27B0),
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "个性化分析",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF9C27B0)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = aiInsight ?: "正在分析您的数据...",
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = if (hasError) Color(0xFFEF4444) else Color.DarkGray
                    )
                }
            }
                }

                else -> {
                    // AI分析结果状态
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = if (hasError) Color(0xFFFEF2F2) else Color(0xFFF3E5F5)
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.Top
                        ) {
                            Text(
                                text = if (hasError) "⚠️" else "🤖",
                                fontSize = 24.sp,
                                modifier = Modifier.padding(end = 12.dp)
                            )

                            Column {
                                Text(
                                    text = if (hasError) "AI分析遇到问题" else "AI智能分析结果",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = if (hasError) Color(0xFFDC2626) else Color(0xFF374151),
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )

                                Text(
                                    text = aiInsight ?: "正在分析您的数据...",
                                    fontSize = 13.sp,
                                    color = if (hasError) Color(0xFFEF4444) else Color(0xFF6B7280),
                                    lineHeight = 18.sp
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 生成AI分析内容
 */
private fun generateAIInsight(statistics: PeriodStatistics): String {
    return when {
        statistics.totalCycles < 3 ->
            "数据收集中...建议继续记录至少3个周期以获得更准确的分析。"
        statistics.averageCycleLength in 25.0..35.0 ->
            "您的周期长度在正常范围内(${statistics.averageCycleLength.toInt()}天)，显示良好的生殖健康状态。建议保持规律作息和适量运动。"
        statistics.averageCycleLength < 25.0 ->
            "您的周期较短(${statistics.averageCycleLength.toInt()}天)，可能与压力、体重变化或激素水平有关。建议咨询医生进行进一步评估。"
        else ->
            "您的周期较长(${statistics.averageCycleLength.toInt()}天)，可能需要关注生活方式因素。建议保持健康饮食和规律运动。"
    }
}

/**
 * 🔧 新增：基于实际数据调用AI API生成洞察
 */
suspend fun generateAIInsight(
    statistics: PeriodStatistics,
    analyticsData: AdvancedAnalytics
): String {
    try {
        // 构建AI分析的数据输入
        val analysisData = buildString {
            appendLine("用户生理周期数据分析：")
            appendLine("- 总周期数：${statistics.totalCycles}")
            appendLine("- 平均周期长度：${statistics.averageCycleLength.toInt()}天")
            appendLine("- 平均经期长度：${statistics.averagePeriodLength.toInt()}天")
            appendLine("- 周期规律性：${CycleUtils.getRegularityText(statistics.cycleRegularity)}")
            appendLine("- 周期趋势：${CycleUtils.getTrendText(analyticsData.cycleTrend)}")
            appendLine("- 经期趋势：${CycleUtils.getTrendText(analyticsData.periodTrend)}")
            appendLine("- 变异系数：${String.format("%.2f", analyticsData.cycleVariability)}")
            appendLine("- 规律性评分：${analyticsData.regularityScore}/100")
            appendLine("- 预测准确率：${analyticsData.predictionAccuracy}%")
        }

        // 调用AI API
        return callAIAnalysisAPI(analysisData)

    } catch (e: Exception) {
        throw Exception("AI分析服务暂时不可用：${e.message}")
    }
}

/**
 * 调用AI分析API（模拟实现）
 */
suspend fun callAIAnalysisAPI(data: String): String {
    // 🔧 TODO: 这里应该调用真实的AI API，如OpenAI、百度文心一言等
    // 目前使用基于规则的智能分析作为替代

    kotlinx.coroutines.delay(1500) // 模拟API调用延迟

    // 基于数据生成智能分析
    return generateIntelligentAnalysis(data)
}

/**
 * 基于规则的智能分析（AI API的替代方案）
 */
fun generateIntelligentAnalysis(data: String): String {
    return when {
        data.contains("规律性：非常规律") ->
            "🎉 您的生理周期非常规律！这表明您的内分泌系统运作良好。建议继续保持健康的生活方式，包括规律作息、均衡饮食和适量运动。"

        data.contains("规律性：比较规律") ->
            "✅ 您的生理周期比较规律，整体健康状况良好。可以适当关注生活压力和饮食习惯，保持身心平衡。"

        data.contains("规律性：不太规律") ->
            "⚠️ 您的周期存在一定波动。建议关注生活规律，减少压力，保证充足睡眠。如果持续不规律，建议咨询妇科医生。"

        data.contains("规律性：很不规律") ->
            "🚨 您的周期波动较大，建议及时咨询专业医生。同时注意调节生活作息，避免过度劳累和精神压力。"

        data.contains("总周期数：0") ->
            "📊 开始记录您的生理周期数据，AI将为您提供个性化的健康分析和专业建议。"

        else ->
            "📈 基于您的数据，建议保持健康的生活方式，定期记录周期变化。如有异常情况，请及时咨询医生。"
    }
}

// 🔧 删除：重复的工具函数已移动到CycleUtils
