package com.timeflow.app.ui.task.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.data.entity.Task
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import androidx.compose.foundation.clickable

/**
 * 增强版可滑动任务卡片 - 参考TickTick实现
 * 支持左滑/右滑操作，与TickTick类似的视觉设计
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnhancedSwipeableTaskCard(
    task: Task,
    onComplete: (Task) -> Unit,
    onDelete: (Task) -> Unit,
    onStar: (Task) -> Unit,
    onTaskClick: (Task) -> Unit,
    modifier: Modifier = Modifier
) {
    val haptics = LocalHapticFeedback.current
    val scope = rememberCoroutineScope()
    val isCompleted = task.completedAt != null
    
    // 滑动状态
    val dismissState = rememberSwipeToDismissBoxState(
        positionalThreshold = { totalDistance -> totalDistance * 0.3f }, // 更敏感的滑动阈值
        confirmValueChange = { dismissValue ->
            when (dismissValue) {
                SwipeToDismissBoxValue.StartToEnd -> {
                    // 右滑标记完成
                    haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                    onComplete(task)
                    false // 不自动关闭，由动画处理
                }
                SwipeToDismissBoxValue.EndToStart -> {
                    // 左滑显示多个操作
                    false // 不自动关闭，等待用户选择操作
                }
                SwipeToDismissBoxValue.Settled -> true
            }
        }
    )
    
    // 卡片动画
    val scale by animateFloatAsState(
        targetValue = if (dismissState.targetValue != SwipeToDismissBoxValue.Settled) 0.98f else 1f,
        label = "cardScale"
    )
    
    // 格式化日期
    val dateFormatter = DateTimeFormatter.ofPattern("MM-dd HH:mm")
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
    ) {
        // 左右滑动显示的背景
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Min)
                .clip(RoundedCornerShape(16.dp)),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 右滑区域 - 完成 (绿色)
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .weight(1f)
                    .background(Color(0xFF4AC367)),
                contentAlignment = Alignment.CenterStart
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 20.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.CheckCircle,
                        contentDescription = "完成",
                        tint = Color.White,
                        modifier = Modifier.size(26.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "完成",
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                }
            }
            
            // 左滑区域 - 多操作
            Row(
                modifier = Modifier
                    .fillMaxHeight(),
                horizontalArrangement = Arrangement.End
            ) {
                // 星标按钮 (橙色)
                SwipeActionButton(
                    icon = if (task.isStarred) Icons.Filled.Star else Icons.Outlined.Star,
                    label = if (task.isStarred) "取消星标" else "星标",
                    color = Color(0xFFFFB036),
                    onClick = {
                        haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                        onStar(task)
                        scope.launch { dismissState.reset() }
                    }
                )
                
                // 日程按钮 (蓝色) - 示例操作
                SwipeActionButton(
                    icon = Icons.Outlined.DateRange,
                    label = "日程",
                    color = Color(0xFF4A8AF4),
                    onClick = {
                        haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                        scope.launch { dismissState.reset() }
                    }
                )
                
                // 删除按钮 (红色)
                SwipeActionButton(
                    icon = Icons.Outlined.Delete,
                    label = "删除",
                    color = Color(0xFFFF5147),
                    onClick = {
                        haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                        onDelete(task)
                        scope.launch { dismissState.reset() }
                    }
                )
            }
        }
        
        // 任务卡片
        SwipeToDismissBox(
            state = dismissState,
            modifier = Modifier
                .fillMaxWidth()
                .scale(scale),
            backgroundContent = {},
            content = {
                TaskCardContent(
                    task = task,
                    isCompleted = isCompleted,
                    dateFormatter = dateFormatter,
                    onTaskClick = { onTaskClick(task) },
                    onCompletedChange = {
                        haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                        onComplete(task)
                    }
                )
            }
        )
    }
}

/**
 * 左滑动作按钮
 */
@Composable
private fun SwipeActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    color: Color,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxHeight()
            .width(80.dp)
            .background(color)
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = label,
                color = Color.White,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 任务卡片内容 - 与TickTick类似的设计
 */
@Composable
private fun TaskCardContent(
    task: Task,
    isCompleted: Boolean,
    dateFormatter: DateTimeFormatter,
    onTaskClick: () -> Unit,
    onCompletedChange: () -> Unit
) {
    Surface(
        onClick = onTaskClick,
        color = MaterialTheme.colorScheme.surface,
        shape = RoundedCornerShape(16.dp),
        tonalElevation = 1.dp,
        shadowElevation = 1.dp,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 完成状态复选框
            Checkbox(
                checked = isCompleted,
                onCheckedChange = { onCompletedChange() },
                colors = CheckboxDefaults.colors(
                    checkedColor = MaterialTheme.colorScheme.primary,
                    uncheckedColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.7f)
                ),
                modifier = Modifier.padding(end = 12.dp)
            )
            
            // 任务内容区
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 顶部时间信息
                if (task.dueDate != null) {
                    DueDateInfo(
                        dueDate = task.dueDate,
                        isCompleted = isCompleted,
                        dateFormatter = dateFormatter
                    )
                }
                
                // 任务标题
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = if (isCompleted) FontWeight.Normal else FontWeight.Medium
                    ),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    textDecoration = if (isCompleted) TextDecoration.LineThrough else null,
                    color = if (isCompleted) 
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    else
                        MaterialTheme.colorScheme.onSurface
                )
                
                // 任务描述 (如果有)
                if (!task.description.isNullOrBlank()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = task.description,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
                
                // 底部标签区
                TaskMetadata(task, isCompleted)
            }
            
            // 右侧指示器
            TaskIndicators(task)
        }
    }
}

/**
 * 截止日期信息
 */
@Composable
private fun DueDateInfo(
    dueDate: LocalDateTime,
    isCompleted: Boolean,
    dateFormatter: DateTimeFormatter
) {
    val now = LocalDateTime.now()
    val isPastDue = dueDate.isBefore(now) && !isCompleted
    val isToday = dueDate.toLocalDate().isEqual(now.toLocalDate())
    val isTomorrow = dueDate.toLocalDate().isEqual(now.toLocalDate().plusDays(1))
    val isAfterTomorrow = dueDate.toLocalDate().isEqual(now.toLocalDate().plusDays(2))
    
    val displayText = when {
        isToday -> "今天，${dueDate.format(DateTimeFormatter.ofPattern("M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
        isTomorrow -> "明天，${dueDate.format(DateTimeFormatter.ofPattern("M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
        isAfterTomorrow -> "后天，${dueDate.format(DateTimeFormatter.ofPattern("M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
        else -> {
            // 获取星期几
            val dayOfWeek = when (dueDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            
            if (ChronoUnit.DAYS.between(now.toLocalDate(), dueDate.toLocalDate()) < 7) {
                // 一周内
                "$dayOfWeek，${dueDate.format(DateTimeFormatter.ofPattern("M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
            } else if (dueDate.year == now.year) {
                // 今年内
                "${dueDate.format(DateTimeFormatter.ofPattern("M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
            } else {
                // 其他年份
                "${dueDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, ${dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))}"
            }
        }
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(bottom = 4.dp)
    ) {
        Icon(
            imageVector = Icons.Outlined.Schedule,
            contentDescription = null,
            tint = if (isPastDue) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
            modifier = Modifier.size(14.dp)
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = displayText,
            fontSize = 12.sp,
            color = if (isPastDue) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
        )
        
        // 显示逾期标记
        if (isPastDue) {
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "已逾期",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * 任务元数据区域 - 显示标签、子任务等信息
 */
@Composable
private fun TaskMetadata(task: Task, isCompleted: Boolean) {
    val hasSubtasks = task.hasSubtasks
    val hasTag = task.tagIds.isNotEmpty()
    
    if (hasSubtasks || hasTag) {
        Spacer(modifier = Modifier.height(4.dp))
        
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(top = 2.dp)
        ) {
            // 子任务信息
            if (hasSubtasks) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(end = 12.dp)
                ) {
                    Icon(
                        imageVector = Icons.Outlined.SubdirectoryArrowRight,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                        modifier = Modifier.size(14.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(2.dp))
                    
                    Text(
                        text = "子任务",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                }
            }
            
            // 标签信息
            if (hasTag) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Label,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                        modifier = Modifier.size(14.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(2.dp))
                    
                    Text(
                        text = task.tagIds.joinToString(", ").take(15) + if (task.tagIds.joinToString(", ").length > 15) "..." else "",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * 任务右侧指示器 - 优先级、星标等
 */
@Composable
private fun TaskIndicators(task: Task) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 星标指示
        if (task.isStarred) {
            Icon(
                imageVector = Icons.Filled.Star,
                contentDescription = "已星标",
                tint = Color(0xFFFFB036),
                modifier = Modifier
                    .size(20.dp)
                    .padding(bottom = 4.dp)
            )
        }
        
        // 优先级指示
        if (task.priority > 0) {
            val priorityColor = when (task.priority) {
                1 -> Color(0xFFFFB036) // 低优先级
                2 -> Color(0xFFFF9800) // 中优先级
                3 -> Color(0xFFFF5147) // 高优先级
                else -> Color.Transparent
            }
            
            val priorityIcon = when (task.priority) {
                1 -> Icons.Outlined.Flag
                2 -> Icons.Filled.Flag
                3 -> Icons.Filled.Flag
                else -> Icons.Outlined.Flag
            }
            
            Icon(
                imageVector = priorityIcon,
                contentDescription = "优先级",
                tint = priorityColor,
                modifier = Modifier.size(20.dp)
            )
        }
    }
} 