package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.Wish
import kotlinx.coroutines.flow.Flow

@Dao
interface WishDao {
    
    @Query("SELECT * FROM wishes WHERE isArchived = 0 ORDER BY createdAt DESC")
    fun getAllActiveWishes(): Flow<List<Wish>>

    @Query("SELECT * FROM wishes WHERE isArchived = 1 ORDER BY archivedAt DESC")
    fun getAllArchivedWishes(): Flow<List<Wish>>

    @Query("SELECT * FROM wishes ORDER BY createdAt DESC")
    fun getAllWishes(): Flow<List<Wish>>
    
    @Query("SELECT * FROM wishes WHERE id = :wishId")
    suspend fun getWishById(wishId: String): Wish?
    
    @Query("SELECT * FROM wishes WHERE category = :category AND isArchived = 0 ORDER BY createdAt DESC")
    fun getWishesByCategory(category: String): Flow<List<Wish>>
    
    @Query("SELECT * FROM wishes WHERE priority >= :minPriority AND isArchived = 0 ORDER BY priority DESC, createdAt DESC")
    fun getWishesByPriority(minPriority: Int): Flow<List<Wish>>
    
    @Query("SELECT * FROM wishes WHERE status = :status ORDER BY createdAt DESC")
    fun getWishesByStatus(status: String): Flow<List<Wish>>
    
    @Query("SELECT DISTINCT category FROM wishes WHERE isArchived = 0")
    suspend fun getAllCategories(): List<String>
    
    @Query("SELECT COUNT(*) FROM wishes WHERE isArchived = 0")
    suspend fun getActiveWishCount(): Int
    
    @Query("SELECT COUNT(*) FROM wishes WHERE status = 'CONVERTED_TO_GOAL'")
    suspend fun getConvertedWishCount(): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWish(wish: Wish)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWishes(wishes: List<Wish>)
    
    @Update
    suspend fun updateWish(wish: Wish)
    
    @Delete
    suspend fun deleteWish(wish: Wish)
    
    @Query("DELETE FROM wishes WHERE id = :wishId")
    suspend fun deleteWishById(wishId: String)
    
    @Query("UPDATE wishes SET isArchived = 1, archivedAt = :archivedAt WHERE id = :wishId")
    suspend fun archiveWish(wishId: String, archivedAt: java.time.LocalDateTime)
    
    @Query("UPDATE wishes SET isArchived = 0, archivedAt = NULL WHERE id = :wishId")
    suspend fun unarchiveWish(wishId: String)
    
    @Query("UPDATE wishes SET status = 'CONVERTED_TO_GOAL', relatedGoalId = :goalId, updatedAt = :updatedAt WHERE id = :wishId")
    suspend fun convertWishToGoal(wishId: String, goalId: String, updatedAt: java.time.LocalDateTime)
    
    @Query("UPDATE wishes SET status = 'ACHIEVED', achievedAt = :achievedAt WHERE id = :wishId")
    suspend fun markWishAsAchieved(wishId: String, achievedAt: java.time.LocalDateTime)
    
    @Query("""
        SELECT * FROM wishes 
        WHERE isArchived = 0 
        AND (title LIKE '%' || :searchQuery || '%' 
             OR description LIKE '%' || :searchQuery || '%'
             OR tags LIKE '%' || :searchQuery || '%')
        ORDER BY createdAt DESC
    """)
    fun searchWishes(searchQuery: String): Flow<List<Wish>>
} 