# 任务循环功能实现说明

## 🎯 **功能需求**
在添加或编辑任务时增加一个循环按钮，询问用户该任务是否循环，并支持选择循环方式，参照知名app的设计。

## 🔍 **技术架构分析**

### 数据库层面支持
Task实体类中已经存在循环相关字段：
- `isRecurring: Boolean = false` - 是否循环任务
- `recurringPattern: String? = null` - 循环模式（JSON格式）

### 循环设置数据结构
```kotlin
enum class RecurrenceType(val displayName: String, val description: String) {
    NONE("不循环", "任务只执行一次"),
    DAILY("每天", "每天重复"),
    WEEKLY("每周", "每周重复"),
    MONTHLY("每月", "每月重复"),
    YEARLY("每年", "每年重复"),
    CUSTOM("自定义", "自定义循环规则")
}

@Serializable
data class RecurrenceSettings(
    val type: String = "NONE",
    val interval: Int = 1, // 间隔：每几天/周/月/年
    val endType: String = "NEVER", // 结束类型：NEVER, DATE, COUNT
    val endDate: String? = null, // 结束日期
    val endCount: Int? = null, // 重复次数
    val weekdays: List<Int> = emptyList(), // 每周的星期几
    val monthDay: Int? = null // 每月的第几天
)
```

## 🔧 **核心技术实现**

### 1. **状态管理扩展**

#### TaskEditScreen状态变量
```kotlin
// 🔧 循环相关状态
var isRecurring by remember { mutableStateOf(false) }
var recurrenceSettings by remember { mutableStateOf(RecurrenceSettings.none()) }
var selectedRecurrenceType by remember { mutableStateOf(RecurrenceType.NONE) }
var showRecurrenceDialog by remember { mutableStateOf(false) }
```

### 2. **UI组件设计**

#### 任务属性设置区域
```kotlin
// 🔧 任务属性设置区域
Surface(
    modifier = Modifier.fillMaxWidth(),
    shape = RoundedCornerShape(12.dp),
    color = Color.White,
    border = BorderStroke(1.dp, LavenderAsh.copy(alpha = 0.3f))
) {
    Column(modifier = Modifier.padding(16.dp)) {
        // 优先级设置
        DetailItem(
            icon = Icons.Default.Flag,
            label = "优先级",
            value = getTaskEditPriorityText(selectedPriority),
            onClick = { showPriorityDialog = true }
        )
        
        // 日期设置
        DetailItem(
            icon = Icons.Default.Schedule,
            label = "时间",
            value = formatDateRange(),
            onClick = { showDatePicker = true }
        )
        
        // 🔧 循环设置
        DetailItem(
            icon = Icons.Default.Repeat,
            label = "循环",
            value = if (isRecurring) selectedRecurrenceType.displayName else "不循环",
            onClick = { showRecurrenceDialog = true }
        )
        
        // 标签设置
        DetailItem(
            icon = Icons.Default.Label,
            label = "标签",
            value = if (tags.isEmpty()) "添加标签" else "${tags.size}个标签",
            onClick = { showTagDialog = true }
        )
    }
}
```

#### DetailItem通用组件
```kotlin
@Composable
private fun DetailItem(
    icon: ImageVector,
    label: String,
    value: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(imageVector = icon, contentDescription = null)
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = label, color = TextSecondary)
        Spacer(modifier = Modifier.weight(1f))
        Text(text = value, color = TextPrimary)
        Icon(imageVector = Icons.Default.KeyboardArrowRight)
    }
}
```

### 3. **循环设置对话框**

#### RecurrenceSettingsDialog主体
```kotlin
@Composable
private fun RecurrenceSettingsDialog(
    isRecurring: Boolean,
    recurrenceSettings: RecurrenceSettings,
    selectedRecurrenceType: RecurrenceType,
    onDismiss: () -> Unit,
    onConfirm: (Boolean, RecurrenceSettings, RecurrenceType) -> Unit
) {
    var tempIsRecurring by remember { mutableStateOf(isRecurring) }
    var tempRecurrenceType by remember { mutableStateOf(selectedRecurrenceType) }
    var tempSettings by remember { mutableStateOf(recurrenceSettings) }
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(shape = RoundedCornerShape(16.dp), color = Color.White) {
            Column(modifier = Modifier.padding(20.dp)) {
                // 标题
                Text("循环设置", fontSize = 18.sp, fontWeight = FontWeight.Bold)
                
                // 是否启用循环开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("启用循环", fontSize = 16.sp)
                    Switch(
                        checked = tempIsRecurring,
                        onCheckedChange = { tempIsRecurring = it }
                    )
                }
                
                // 循环类型选择
                if (tempIsRecurring) {
                    Text("循环类型", fontSize = 14.sp, color = TextSecondary)
                    
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        RecurrenceType.values().filter { it != RecurrenceType.NONE }.forEach { type ->
                            RecurrenceTypeItem(
                                type = type,
                                isSelected = tempRecurrenceType == type,
                                onClick = { 
                                    tempRecurrenceType = type
                                    tempSettings = when (type) {
                                        RecurrenceType.DAILY -> RecurrenceSettings.daily()
                                        RecurrenceType.WEEKLY -> RecurrenceSettings.weekly()
                                        RecurrenceType.MONTHLY -> RecurrenceSettings.monthly()
                                        RecurrenceType.YEARLY -> RecurrenceSettings.yearly()
                                        else -> RecurrenceSettings.none()
                                    }
                                }
                            )
                        }
                    }
                }
                
                // 确认/取消按钮
                Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
                    OutlinedButton(onClick = onDismiss) { Text("取消") }
                    Button(
                        onClick = {
                            val finalType = if (tempIsRecurring) tempRecurrenceType else RecurrenceType.NONE
                            val finalSettings = if (tempIsRecurring) tempSettings else RecurrenceSettings.none()
                            onConfirm(tempIsRecurring, finalSettings, finalType)
                        }
                    ) { Text("确定") }
                }
            }
        }
    }
}
```

#### RecurrenceTypeItem选项组件
```kotlin
@Composable
private fun RecurrenceTypeItem(
    type: RecurrenceType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent,
        border = BorderStroke(1.dp, if (isSelected) DustyLavender else LavenderAsh.copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = DustyLavender,
                    unselectedColor = LavenderAsh
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column {
                Text(
                    text = type.displayName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                Text(
                    text = type.description,
                    fontSize = 12.sp,
                    color = TextSecondary
                )
            }
        }
    }
}
```

### 4. **数据持久化**

#### 任务创建时的循环设置保存
```kotlin
// 🔧 序列化循环设置
val recurringPatternJson = if (isRecurring) {
    try {
        Json.encodeToString(recurrenceSettings)
    } catch (e: Exception) {
        Timber.e(e, "序列化循环设置失败")
        null
    }
} else {
    null
}

// 创建ModelTask对象
val modelTask = ModelTask(
    id = newTaskId,
    title = title,
    description = description,
    // ... 其他字段
    isRecurring = isRecurring, // 🔧 添加循环标记
    recurringPattern = recurringPatternJson // 🔧 添加循环设置
)
```

## 🎨 **用户体验设计**

### 1. **界面布局优化**
- **统一设置区域**: 将循环设置与优先级、日期、标签等设置统一放在一个设置卡片中
- **清晰的视觉层次**: 使用图标、分割线和一致的间距创建清晰的视觉层次
- **直观的交互反馈**: 点击设置项时显示右箭头，表明可以进一步设置

### 2. **循环设置交互**
- **开关控制**: 使用Switch组件控制是否启用循环，直观明了
- **类型选择**: 使用RadioButton进行单选，符合用户习惯
- **即时预览**: 在主界面显示当前选择的循环类型
- **智能默认**: 根据循环类型自动设置合理的默认参数

### 3. **参照知名App设计**
- **TickTick风格**: 清晰的设置项布局和图标使用
- **Todoist风格**: 简洁的对话框设计和交互流程
- **Any.do风格**: 直观的开关和选项设计

## 🛡️ **技术保障**

### 1. **数据安全性**
- **JSON序列化**: 使用Kotlinx Serialization确保数据序列化的安全性
- **异常处理**: 完善的try-catch机制处理序列化异常
- **数据验证**: 在保存前验证循环设置的合理性

### 2. **性能优化**
- **状态管理**: 使用remember和mutableStateOf进行高效的状态管理
- **组件复用**: DetailItem等组件设计为可复用，减少代码重复
- **懒加载**: 对话框内容按需渲染，提升性能

### 3. **扩展性设计**
- **枚举扩展**: RecurrenceType枚举易于扩展新的循环类型
- **设置扩展**: RecurrenceSettings数据类支持添加新的配置项
- **UI扩展**: 组件化设计便于后续功能扩展

## ✅ **验证要点**

### 功能验证
- [ ] 循环按钮正确显示在任务设置区域
- [ ] 点击循环设置弹出对话框
- [ ] 开关控制循环启用/禁用
- [ ] 循环类型选择正常工作
- [ ] 设置保存到数据库

### 用户体验验证
- [ ] 界面布局美观统一
- [ ] 交互流程直观顺畅
- [ ] 设置项显示准确
- [ ] 对话框操作便捷

### 数据验证
- [ ] 循环设置正确序列化
- [ ] 数据库字段正确保存
- [ ] 异常情况处理得当
- [ ] 数据读取正确

## 🚀 **预期效果**

### 即时价值
1. **功能完整**: 任务管理功能更加完整，支持循环任务
2. **用户体验**: 参照知名app的设计，提供熟悉的交互体验
3. **操作便捷**: 统一的设置界面，一站式完成任务配置

### 长期价值
1. **效率提升**: 循环任务减少重复创建的工作量
2. **习惯养成**: 支持日常习惯和定期任务的管理
3. **功能扩展**: 为后续的智能提醒、任务模板等功能奠定基础

---

> **实现总结**: 通过在TaskEditScreen中添加循环设置功能，成功实现了任务循环管理。用户现在可以在创建或编辑任务时轻松设置循环规则，包括每天、每周、每月、每年等常见循环类型。界面设计参照知名app，提供了直观、便捷的用户体验。🔄✨
