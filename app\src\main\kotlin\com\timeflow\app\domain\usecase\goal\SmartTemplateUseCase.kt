package com.timeflow.app.domain.usecase.goal

import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.data.repository.GoalTemplateRepository
import com.timeflow.app.data.repository.UserPreferenceRepository
import com.timeflow.app.data.repository.UserPreferences
import com.timeflow.app.utils.PagedTemplateResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import android.util.Log

/**
 * 智能模板推荐用例类
 * 根据用户历史目标完成情况、偏好和兴趣领域，智能推荐高质量目标模板
 */
class SmartTemplateUseCase @Inject constructor(
    private val goalTemplateRepository: GoalTemplateRepository,
    private val userPreferenceRepository: UserPreferenceRepository
) {
    // 缓存的分类模板映射
    private var cachedCategorizedTemplates: Map<String, List<GoalTemplate>>? = null
    private var cacheTimestamp: Long = 0
    private val CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存过期

    /**
     * 获取个性化推荐的目标模板
     * @param limit 推荐数量限制，默认为10
     * @return 推荐模板列表Flow
     */
    fun getPersonalizedTemplates(limit: Int = 10): Flow<List<GoalTemplate>> {
        // 结合用户偏好和模板库
        return combine(
            userPreferenceRepository.getUserPreferences(),
            goalTemplateRepository.getAllTemplates()
        ) { preferences, templates ->
            // 1. 筛选用户感兴趣的类别
            val filteredByCategory = if (preferences.preferredCategories.isNotEmpty()) {
                templates.filter { template -> 
                    preferences.preferredCategories.any { category -> 
                        template.category == category.toString() 
                    }
                }
            } else {
                templates
            }
            
            // 2. 按用户历史完成率排序
            val sortedByRelevance = filteredByCategory.sortedWith(
                compareByDescending<GoalTemplate> { template ->
                    // 优先考虑用户过去完成率高的模板类别
                    val category = runCatching { 
                        TemplateCategory.valueOf(template.category) 
                    }.getOrNull()
                    preferences.categoryCompletionRates[category] ?: 0.0
                }.thenByDescending { 
                    // 然后是使用次数和评分
                    it.usageCount * (preferences.templateRatings[it.id] ?: 3.0)
                }
            )
            
            // 3. 限制返回数量
            sortedByRelevance.take(limit)
        }
    }

    /**
     * 获取按场景分类的推荐模板
     * 返回格式为 Map<场景类别, 模板列表>
     * 默认实现使用缓存，减少数据库访问
     */
    fun getCategorizedTemplates(): Flow<Map<String, List<GoalTemplate>>> {
        return flow {
            // 检查缓存是否有效
            val currentTime = System.currentTimeMillis()
            if (cachedCategorizedTemplates != null && 
                currentTime - cacheTimestamp < CACHE_TTL) {
                emit(cachedCategorizedTemplates!!)
                return@flow
            }
            
            // 缓存无效，从数据库获取
            val templates = goalTemplateRepository.getAllTemplates().map { templateList ->
                if (templateList.isEmpty()) {
                    // 如果没有模板，返回空的分类Map但保留类别结构
                    mapOf(
                        "学习提升" to emptyList(),
                        "工作项目" to emptyList(),
                        "健康生活" to emptyList(),
                        "个人爱好" to emptyList(),
                        "其他目标" to emptyList()
                    )
                } else {
                    // 按场景分组
                    templateList.groupBy { template -> 
                        when (template.category) {
                            "STUDY", "RESEARCH", "LEARN" -> "学习提升"
                            "WORK", "CAREER", "PROJECT" -> "工作项目"
                            "EXERCISE", "HEALTH", "DIET" -> "健康生活"
                            "PERSONAL", "HOBBY", "SKILL" -> "个人爱好"
                            else -> "其他目标"
                        }
                    }.mapValues { (_, templateList) ->
                        // 每个类别按成功率和使用率排序
                        templateList.sortedWith(
                            compareByDescending<GoalTemplate> { it.usageCount }
                                .thenByDescending { getTemplateSuccessRate(it) }
                        ).take(5) // 每个类别最多5个
                    }
                }
            }
            
            // 收集结果并更新缓存
            templates.collect { result ->
                cachedCategorizedTemplates = result
                cacheTimestamp = currentTime
                emit(result)
            }
        }
    }
    
    /**
     * 获取分页的按场景分类的推荐模板
     * 支持指定页码和每页大小，以便按需加载更多模板
     * @param page 页码，从1开始
     * @param pageSize 每页大小
     * @return 分类模板Map的Flow
     */
    fun getCategorizedTemplatesPaged(page: Int = 1, pageSize: Int = 10): Flow<Map<String, PagedTemplateResult>> {
        return flow {
            // 获取所有分类
            val categories = listOf("STUDY", "WORK", "HEALTH", "PERSONAL")
            
            // 为每个分类获取分页数据
            val results = categories.associateWith { category ->
                try {
                    // 转换类别常量到显示名称
                    val displayCategory = when (category) {
                        "STUDY" -> "学习提升"
                        "WORK" -> "工作项目"
                        "HEALTH" -> "健康生活"
                        "PERSONAL" -> "个人爱好"
                        else -> "其他目标"
                    }
                    
                    // 获取分页数据
                    val result = goalTemplateRepository.getTemplatesByCategoryPaged(category, page, pageSize)
                    
                    // 将结果封装为分页结果
                    PagedTemplateResult(
                        templates = result.templates,
                        totalCount = result.totalCount,
                        hasNextPage = result.hasNextPage,
                        page = result.page,
                        pageSize = result.pageSize
                    )
                } catch (e: Exception) {
                    Log.e("SmartTemplateUseCase", "获取分类模板失败: ${e.message}")
                    // 发生错误时返回空结果
                    PagedTemplateResult(
                        templates = emptyList(),
                        totalCount = 0,
                        hasNextPage = false,
                        page = page,
                        pageSize = pageSize
                    )
                }
            }
            
            // 将结果按显示名称分组
            val mappedResults = mapOf(
                Pair("学习提升", results["STUDY"] ?: PagedTemplateResult(emptyList(), 0, false, page, pageSize)),
                Pair("工作项目", results["WORK"] ?: PagedTemplateResult(emptyList(), 0, false, page, pageSize)),
                Pair("健康生活", results["HEALTH"] ?: PagedTemplateResult(emptyList(), 0, false, page, pageSize)),
                Pair("个人爱好", results["PERSONAL"] ?: PagedTemplateResult(emptyList(), 0, false, page, pageSize)),
                Pair("其他目标", PagedTemplateResult(emptyList(), 0, false, page, pageSize))
            )
            
            emit(mappedResults)
        }
    }
    
    /**
     * 获取模板的成功率
     * 由于GoalTemplate没有直接的successRate字段，我们根据usageCount估算
     */
    private fun getTemplateSuccessRate(template: GoalTemplate): Double {
        // 这里是一个估算方法，使用模板的使用次数估计其成功率
        // 实际项目中应该使用更精确的成功率统计方法
        return minOf(0.5 + (template.usageCount * 0.05), 0.95)
    }
    
    /**
     * 记录模板选择
     * 当用户选择了某个模板时调用，用于更新推荐算法的数据
     */
    suspend fun recordTemplateSelection(templateId: String) {
        try {
            // 记录模板使用情况
            goalTemplateRepository.recordTemplateUsage(templateId)
            
            // 清除缓存，确保下次获取的是最新数据
            cachedCategorizedTemplates = null
        } catch (e: Exception) {
            Log.e("SmartTemplateUseCase", "记录模板选择失败: ${e.message}")
        }
    }
    
    /**
     * 清除缓存
     * 当需要强制刷新数据时调用
     */
    fun clearCache() {
        cachedCategorizedTemplates = null
        cacheTimestamp = 0
    }
} 