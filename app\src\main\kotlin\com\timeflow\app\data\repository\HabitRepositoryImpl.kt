package com.timeflow.app.data.repository

import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.timeflow.app.data.dao.HabitDao
import com.timeflow.app.data.dao.GoalDao
import com.timeflow.app.data.entity.Habit
import com.timeflow.app.data.entity.HabitRecord
import com.timeflow.app.data.entity.HabitReminder
import com.timeflow.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.json.JSONArray
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.DayOfWeek
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * 习惯管理仓库实现类
 * 使用Room数据库进行数据持久化
 */
@Singleton
class HabitRepositoryImpl @Inject constructor(
    private val habitDao: HabitDao,
    private val goalDao: GoalDao
) : HabitRepository {
    
    // 内存缓存，用于快速访问
    private val _cachedHabits = MutableStateFlow<List<HabitModel>>(emptyList())
    
    /**
     * 获取所有活跃习惯
     */
    override suspend fun getAllHabits(): List<HabitModel> {
        val entities = habitDao.getAllActiveHabits()
        // 由于这是Flow，我们需要收集第一个值
        var result: List<HabitModel> = emptyList()
        entities.collect { habitEntities ->
            result = habitEntities.map { entity ->
                mapEntityToModel(entity)
            }
            return@collect // 只取第一个值
        }
        return result
    }
    
    /**
     * 获取所有习惯的Flow
     */
    override fun getAllHabitsFlow(): Flow<List<HabitModel>> {
        return habitDao.getAllActiveHabits().map { entities ->
            entities.map { entity ->
                mapEntityToModel(entity)
            }
        }
    }
    
    /**
     * 根据ID获取习惯
     */
    override suspend fun getHabitById(habitId: String): HabitModel? {
        val entity = habitDao.getHabitById(habitId) ?: return null
        return mapEntityToModel(entity)
    }
    
    /**
     * 添加新习惯
     */
    override suspend fun addHabit(habit: HabitModel): Boolean {
        return try {
            android.util.Log.d("HabitRepository", "🔧 开始添加习惯到数据库: name=${habit.name}, customCategoryId=${habit.customCategoryId}")
            val entity = mapModelToEntity(habit)
            android.util.Log.d("HabitRepository", "🔧 转换后的实体: customCategoryId=${entity.customCategoryId}")
            habitDao.insertHabit(entity)
            android.util.Log.d("HabitRepository", "✓ 习惯成功添加到数据库: ${habit.name}")
            true
        } catch (e: Exception) {
            android.util.Log.e("HabitRepository", "❌ 添加习惯到数据库失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 更新习惯
     */
    override suspend fun updateHabit(habit: HabitModel): Boolean {
        return try {
            val entity = mapModelToEntity(habit)
            habitDao.updateHabit(entity)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 删除习惯（软删除，设置为非活跃状态）
     */
    override suspend fun deleteHabit(habitId: String): Boolean {
        return try {
            // 使用软删除，只是设置isActive=false，不真正删除数据
            habitDao.deactivateHabit(habitId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 恢复习惯（撤销删除/归档操作）
     */
    override suspend fun restoreHabit(habitId: String): Boolean {
        return try {
            // 将习惯设置为活跃状态
            habitDao.activateHabit(habitId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 标记习惯为已完成
     */
    override suspend fun markHabitAsCompleted(habitId: String, date: LocalDate, completed: Boolean): Boolean {
        return try {
            val existingRecord = habitDao.getRecordForDate(habitId, date)
            
            if (existingRecord != null) {
                // 更新现有记录
                val updatedRecord = existingRecord.copy(
                    completed = completed,
                    completedAt = if (completed) LocalDateTime.now() else null,
                    updatedAt = LocalDateTime.now()
                )
                habitDao.updateHabitRecord(updatedRecord)
            } else {
                // 创建新记录
                val newRecord = HabitRecord(
                    id = java.util.UUID.randomUUID().toString(),
                    habitId = habitId,
                    date = date,
                    completed = completed,
                    completedAt = if (completed) LocalDateTime.now() else null
                )
                habitDao.insertHabitRecord(newRecord)
            }
            
            // 更新习惯的统计信息
            updateHabitStats(habitId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 计算当前连续天数
     */
    override suspend fun calculateStreak(habitId: String): Int {
        return habitDao.getCurrentStreakForHabit(habitId)
    }
    
    /**
     * 设置习惯提醒
     */
    override suspend fun scheduleHabitReminder(habitId: String, time: String): Boolean {
        return try {
            // 这里可以集成WorkManager或AlarmManager来设置实际的提醒
            // 目前只是保存提醒设置到数据库
            val reminder = HabitReminder(
                id = java.util.UUID.randomUUID().toString(),
                habitId = habitId,
                time = time,
                days = JSONArray(DayOfWeek.values().map { it.name }).toString(),
                enabled = true
            )
            habitDao.insertHabitReminder(reminder)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 根据目标ID获取关联的习惯
     */
    override suspend fun getHabitsByGoalId(goalId: String): List<HabitModel> {
        val entities = habitDao.getHabitsByGoalId(goalId)
        return entities.map { mapEntityToModel(it) }
    }
    
    /**
     * 获取与指定目标关联的习惯列表流
     */
    override fun getHabitsByGoalIdFlow(goalId: String): Flow<List<HabitModel>> {
        return habitDao.getHabitsByGoalIdFlow(goalId).map { entities ->
            entities.map { mapEntityToModel(it) }
        }
    }
    
    /**
     * 获取习惯统计数据
     */
    suspend fun getHabitStats(): HabitStatsModel {
        val allHabits = habitDao.getAllActiveHabits()
        var totalHabits = 0
        var completedToday = 0

        allHabits.collect { habits ->
            totalHabits = habits.size
            completedToday = habitDao.getCompletedHabitsCountForDate(LocalDate.now())
            return@collect
        }

        val averageCompletionRate = habitDao.getAverageCompletionRate() ?: 0f
        val topHabits = habitDao.getTopHabitsByCompletions(5)

        return HabitStatsModel(
            totalHabits = totalHabits,
            activeHabits = totalHabits,
            completedToday = completedToday,
            averageCompletionRate = averageCompletionRate,
            topHabits = topHabits.map {
                HabitCompletionStat(it.name, it.completions)
            }
        )
    }

    override suspend fun getHabitStatistics(startDate: LocalDate, endDate: LocalDate): HabitAnalyticsData {
        return try {
            val allHabits = habitDao.getAllHabits()
            val activeHabits = allHabits.filter { it.isActive }

            // 获取今日完成的习惯数量
            val completedToday = habitDao.getCompletedHabitsCountForDate(LocalDate.now())

            // 计算平均完成率
            val averageCompletionRate = if (activeHabits.isNotEmpty()) {
                activeHabits.map { habit ->
                    val daysSinceCreated = java.time.temporal.ChronoUnit.DAYS.between(
                        habit.createdAt.toLocalDate(), LocalDate.now()
                    )
                    if (daysSinceCreated > 0) {
                        (habit.totalCompletions.toFloat() / daysSinceCreated * 100).coerceAtMost(100f)
                    } else 100f
                }.average().toFloat()
            } else 0f

            // 计算平均连续天数
            val averageStreak = if (activeHabits.isNotEmpty()) {
                activeHabits.map { it.currentStreak }.average().toFloat()
            } else 0f

            // 找出最长连续天数
            val longestStreak = activeHabits.maxOfOrNull { it.longestStreak } ?: 0

            // 按分类统计
            val habitsByCategory = activeHabits.groupBy { it.category }
                .mapValues { it.value.size }

            // 获取表现最好的习惯
            val topPerformingHabits = activeHabits.sortedByDescending { it.currentStreak }
                .take(5)
                .map { habit ->
                    val daysSinceCreated = java.time.temporal.ChronoUnit.DAYS.between(
                        habit.createdAt.toLocalDate(), LocalDate.now()
                    )
                    val completionRate = if (daysSinceCreated > 0) {
                        (habit.totalCompletions.toFloat() / daysSinceCreated * 100).coerceAtMost(100f)
                    } else 100f

                    HabitPerformanceData(
                        habitId = habit.id,
                        habitName = habit.name,
                        completionRate = completionRate,
                        currentStreak = habit.currentStreak,
                        longestStreak = habit.longestStreak
                    )
                }

            HabitAnalyticsData(
                totalHabits = allHabits.size,
                activeHabits = activeHabits.size,
                completedToday = completedToday,
                averageCompletionRate = averageCompletionRate,
                averageStreak = averageStreak,
                longestStreak = longestStreak,
                habitsByCategory = habitsByCategory,
                topPerformingHabits = topPerformingHabits
            )
        } catch (e: Exception) {
            Log.e("HabitRepository", "获取习惯统计失败", e)
            HabitAnalyticsData(0, 0, 0, 0f, 0f, 0, emptyMap(), emptyList())
        }
    }

    override suspend fun getHabitCompletionRates(startDate: LocalDate, endDate: LocalDate): Map<String, Float> {
        return try {
            val activeHabits = habitDao.getAllHabits().filter { it.isActive }
            activeHabits.associate { habit ->
                val daysSinceCreated = java.time.temporal.ChronoUnit.DAYS.between(
                    habit.createdAt.toLocalDate(), LocalDate.now()
                )
                val completionRate = if (daysSinceCreated > 0) {
                    (habit.totalCompletions.toFloat() / daysSinceCreated * 100).coerceAtMost(100f)
                } else 100f

                habit.name to completionRate
            }
        } catch (e: Exception) {
            Log.e("HabitRepository", "获取习惯完成率失败", e)
            emptyMap()
        }
    }

    override suspend fun getHabitStreakRanking(): List<HabitStreakData> {
        return try {
            val activeHabits = habitDao.getAllHabits().filter { it.isActive }
            activeHabits.sortedByDescending { it.currentStreak }
                .map { habit ->
                    HabitStreakData(
                        habitId = habit.id,
                        habitName = habit.name,
                        currentStreak = habit.currentStreak,
                        longestStreak = habit.longestStreak
                    )
                }
        } catch (e: Exception) {
            Log.e("HabitRepository", "获取习惯连续天数排行失败", e)
            emptyList()
        }
    }

    override suspend fun getDailyHabitCompletionStats(startDate: LocalDate, endDate: LocalDate): Map<LocalDate, Int> {
        return try {
            val completionStats = mutableMapOf<LocalDate, Int>()
            var currentDate = startDate

            while (!currentDate.isAfter(endDate)) {
                val completedCount = habitDao.getCompletedHabitsCountForDate(currentDate)
                completionStats[currentDate] = completedCount
                currentDate = currentDate.plusDays(1)
            }

            completionStats
        } catch (e: Exception) {
            Log.e("HabitRepository", "获取每日习惯完成统计失败", e)
            emptyMap()
        }
    }

    override suspend fun getHabitCategoryStatistics(): Map<String, Int> {
        return try {
            val activeHabits = habitDao.getAllHabits().filter { it.isActive }
            activeHabits.groupBy { it.category }
                .mapValues { it.value.size }
        } catch (e: Exception) {
            Log.e("HabitRepository", "获取习惯分类统计失败", e)
            emptyMap()
        }
    }
    
    /**
     * 更新习惯统计信息
     */
    private suspend fun updateHabitStats(habitId: String) {
        val habit = habitDao.getHabitById(habitId) ?: return
        
        // 获取精确的当前连续天数
        val currentStreak = calculateCurrentStreak(habitId)
        
        // 获取历史最长连续天数（简化版本）
        val longestStreak = habitDao.getLongestStreakForHabit(habitId) ?: 0
        val totalCompletions = habitDao.getTotalCompletionsForHabit(habitId)
        
        val updatedHabit = habit.copy(
            currentStreak = currentStreak,
            longestStreak = maxOf(longestStreak, currentStreak),
            totalCompletions = totalCompletions,
            updatedAt = LocalDateTime.now()
        )
        
        habitDao.updateHabit(updatedHabit)
    }
    
    /**
     * 计算精确的当前连续天数
     * 从今天开始向前计算连续完成的天数
     */
    private suspend fun calculateCurrentStreak(habitId: String): Int {
        val recentRecords = habitDao.getRecentRecordsForStreak(habitId)
        if (recentRecords.isEmpty()) return 0
        
        // 按日期降序排列（最近的在前面）
        val sortedRecords = recentRecords.sortedByDescending { it.date }
        val today = LocalDate.now()
        
        var currentStreak = 0
        var checkDate = today
        
        // 从今天开始向前检查
        for (record in sortedRecords) {
            if (record.date == checkDate) {
                if (record.completed) {
                currentStreak++
                    checkDate = checkDate.minusDays(1)
            } else {
                    // 遇到未完成的记录，连续天数终止
                    break
                }
            } else if (record.date < checkDate) {
                // 如果有日期间隙且当前检查的日期没有记录，连续天数终止
                break
            }
        }
        
        return currentStreak
    }
    
    /**
     * 实体转模型
     */
    private suspend fun mapEntityToModel(entity: Habit): HabitModel {
        // 解析频率天数
        val frequencyDays = try {
            val jsonArray = JSONArray(entity.frequencyDays)
            (0 until jsonArray.length()).map { 
                DayOfWeek.valueOf(jsonArray.getString(it))
            }
        } catch (e: Exception) {
            DayOfWeek.values().toList()
        }
        
        // 获取完成日期
        val records = habitDao.getCompletedRecordsForHabit(entity.id)
        val completedDates = records.map { it.date }
        
        // 获取关联目标标题
        val relatedGoalTitle = entity.relatedGoalId?.let { goalId ->
            goalDao.getGoalById(goalId)?.title
        }
        
        return HabitModel(
            id = entity.id,
            name = entity.name,
            description = entity.description,
            icon = getEmojiByName(entity.iconName), // 改为emoji
            color = Color(android.graphics.Color.parseColor(entity.colorHex)),
            category = HabitCategory.valueOf(entity.category),
            customCategoryId = entity.customCategoryId, // 🔧 修复：映射customCategoryId字段
            frequencyType = HabitFrequencyType.valueOf(entity.frequencyType),
            frequencyDays = frequencyDays,
            targetCount = entity.targetCount,
            reminderEnabled = entity.reminderEnabled,
            reminderTime = entity.reminderTime?.let { LocalTime.parse(it) },
            fixedTime = entity.fixedTime?.let { LocalTime.parse(it) },
            difficulty = HabitDifficulty.valueOf(entity.difficulty),
            relatedGoalId = entity.relatedGoalId,
            relatedGoalTitle = relatedGoalTitle,
            currentStreak = entity.currentStreak,
            longestStreak = entity.longestStreak,
            totalCompletions = entity.totalCompletions,
            completedDates = completedDates,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            isActive = entity.isActive,
            customEmoji = entity.customEmoji,
            notes = entity.notes,
            sortOrder = entity.sortOrder
        )
    }
    
    /**
     * 模型转实体
     */
    private fun mapModelToEntity(model: HabitModel): Habit {
        // 序列化频率天数
        val frequencyDaysJson = JSONArray().apply {
            model.frequencyDays.forEach { put(it.name) }
        }.toString()
        
        return Habit(
            id = model.id,
            name = model.name,
            description = model.description,
            iconName = getEmojiName(model.icon), // 改为emoji名称
            colorHex = String.format("#%06X", 0xFFFFFF and model.color.toArgb()),
            category = model.category.name,
            customCategoryId = model.customCategoryId, // 🔧 修复：映射customCategoryId字段
            frequencyType = model.frequencyType.name,
            frequencyDays = frequencyDaysJson,
            targetCount = model.targetCount,
            reminderEnabled = model.reminderEnabled,
            reminderTime = model.reminderTime?.format(DateTimeFormatter.ofPattern("HH:mm")),
            fixedTime = model.fixedTime?.format(DateTimeFormatter.ofPattern("HH:mm")),
            difficulty = model.difficulty.name,
            relatedGoalId = model.relatedGoalId,
            currentStreak = model.currentStreak,
            longestStreak = model.longestStreak,
            totalCompletions = model.totalCompletions,
            createdAt = model.createdAt,
            updatedAt = model.updatedAt,
            isActive = model.isActive,
            customEmoji = model.customEmoji,
            notes = model.notes,
            sortOrder = model.sortOrder
        )
    }
    
    /**
     * 根据图标名称获取emoji
     */
    private fun getEmojiByName(iconName: String): String {
        return when (iconName) {
            "favorite" -> "❤️"
            "water_drop" -> "💧"
            "wb_sunny" -> "☀️"
            "menu_book" -> "📖"
            "fitness_center" -> "💪"
            "self_improvement" -> "🧘‍♀️"
            "code" -> "💻"
            "work" -> "💼"
            "home" -> "🏠"
            "school" -> "🎓"
            "run" -> "🏃‍♂️"
            "coffee" -> "☕"
            "apple" -> "🍎"
            "sleep" -> "😴"
            "no_phone" -> "🚫📱"
            "art" -> "🎨"
            "music" -> "🎵"
            "plant" -> "🌱"
            "write" -> "✍️"
            "fire" -> "🔥"
            "star" -> "⭐"
            "sunrise" -> "🌅"
            "salad" -> "🥗"
            "walk" -> "🚶‍♂️"
            else -> "❤️" // 默认为心形
        }
    }
    
    /**
     * 根据emoji获取图标名称
     */
    private fun getEmojiName(emoji: String): String {
        return when (emoji) {
            "❤️" -> "favorite"
            "💧" -> "water_drop"
            "☀️" -> "wb_sunny"
            "📖" -> "menu_book"
            "💪" -> "fitness_center"
            "🧘‍♀️" -> "self_improvement"
            "💻" -> "code"
            "💼" -> "work"
            "🏠" -> "home"
            "🎓" -> "school"
            "🏃‍♂️" -> "run"
            "☕" -> "coffee"
            "🍎" -> "apple"
            "😴" -> "sleep"
            "🚫📱" -> "no_phone"
            "🎨" -> "art"
            "🎵" -> "music"
            "🌱" -> "plant"
            "✍️" -> "write"
            "🔥" -> "fire"
            "⭐" -> "star"
            "🌅" -> "sunrise"
            "🥗" -> "salad"
            "🚶‍♂️" -> "walk"
            else -> "favorite" // 默认为favorite
        }
    }
} 