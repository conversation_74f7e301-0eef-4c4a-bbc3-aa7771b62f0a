﻿package com.timeflow.app.ui.screen.task

import android.app.Activity
import android.widget.Toast
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import com.timeflow.app.data.model.RecurrenceType
import com.timeflow.app.data.model.RecurrenceSettings
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import com.timeflow.app.data.model.Task as ModelTask
import java.time.Instant
import java.time.ZoneId
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.material3.Divider

// 添加事件通知相关导入
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent
import android.util.Log
import com.timeflow.app.util.NewTaskAddedEvent

// 主题色彩 - 更新为用户指定的配色方案
private val AddTaskPrimary = DustyLavender
private val AddTaskBackground = Color(0xFFFAFAFA)
private val AddTaskCardBackground = Color(0xFFF5F5F7) // 统一卡片背景色，参考选择日期卡片
private val AddTaskBorder = Color(0xFFE5E7EB)
private val AddTaskTextPrimary = Color(0xFF1F1937) // 深色文字
private val AddTaskTextSecondary = Color(0xFF6B7280) // 灰色文字
private val AddTaskAccent = Color(0xFFF59E0B) // 暖橙色点缀
private val AddTaskIconGray = Color(0xFF9CA3AF)

// 使用与TaskDetailBottomSheet.kt相同的优先级颜色体系
private val PriorityColors = mapOf(
    Priority.URGENT to Color(0xFFB74645),  // UrgencyColors.Critical - 紧急
    Priority.HIGH to Color(0xFFFF8247),    // UrgencyColors.High - 高优先级
    Priority.MEDIUM to Color(0xFFd9b335),  // UrgencyColors.Medium - 中优先级
    Priority.LOW to Color(0xFFA8C986)      // UrgencyColors.Low - 低优先级
)

// 优先级无选择状态的颜色
private val PriorityNoneColor = Color(0xFF9CA3AF) // 灰色

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun AddTaskScreen(
    navController: NavController,
    viewModel: TaskListViewModel = hiltViewModel()
) {
    // 状态管理
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(Priority.MEDIUM) }
    var selectedStartDate by remember { mutableStateOf<LocalDate?>(null) }
    var selectedEndDate by remember { mutableStateOf<LocalDate?>(null) }
    var selectedStartTime by remember { mutableStateOf<LocalTime?>(null) }
    var selectedEndTime by remember { mutableStateOf<LocalTime?>(null) }
    var tags by remember { mutableStateOf(emptyList<String>()) }
    var subTasks by remember { mutableStateOf(emptyList<SubTask>()) }
    var isCreating by remember { mutableStateOf(false) }
    var showDateTimeRangePicker by remember { mutableStateOf(false) }
    var showTagInput by remember { mutableStateOf(false) }
    var showSubTaskInput by remember { mutableStateOf(false) }
    var showWishImportDialog by remember { mutableStateOf(false) }
    
    // 新增：提醒设置相关状态
    var reminderEnabled by remember { mutableStateOf(false) }
    var selectedReminderDate by remember { mutableStateOf<LocalDate?>(null) }
    var selectedReminderTime by remember { mutableStateOf<LocalTime?>(null) }
    var showReminderPicker by remember { mutableStateOf(false) }

    // 🔧 循环相关状态
    var isRecurring by remember { mutableStateOf(false) }
    var recurrenceSettings by remember { mutableStateOf(RecurrenceSettings.none()) }
    var selectedRecurrenceType by remember { mutableStateOf(RecurrenceType.NONE) }
    var showRecurrenceDialog by remember { mutableStateOf(false) }
    
    // 动画状态
    var isContentVisible by remember { mutableStateOf(false) }
    val contentAlpha by animateFloatAsState(
        targetValue = if (isContentVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "content_alpha"
    )

    // 键盘控制器
    val keyboardController = LocalSoftwareKeyboardController.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    LaunchedEffect(Unit) {
        delay(100)
        isContentVisible = true
    }

    // 系统栏处理
    val activity = LocalContext.current as? Activity
    LaunchedEffect(Unit) {
        activity?.window?.statusBarColor = AddTaskBackground.hashCode()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AddTaskBackground)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .alpha(contentAlpha)
                .padding(top = SystemBarManager.getFixedStatusBarHeight())
        ) {
            // 顶部导航栏
            TopNavigationBar(
                onBackClick = { navController.popBackStack() },
                onWishImportClick = { showWishImportDialog = true }
            )

            // 主内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 16.dp)
            ) {
                Spacer(modifier = Modifier.height(4.dp))

                // 任务标题输入
                TaskTitleInput(
                    title = title,
                    onTitleChange = { title = it }
                )

                Spacer(modifier = Modifier.height(20.dp))

                // 任务描述输入
                TaskDescriptionInput(
                    description = description,
                    onDescriptionChange = { description = it }
                )

                Spacer(modifier = Modifier.height(28.dp))

                // 任务选项区域 - 更新为日期范围选择
                TaskOptionsSection(
                    selectedPriority = selectedPriority,
                    selectedStartDate = selectedStartDate,
                    selectedEndDate = selectedEndDate,
                    selectedStartTime = selectedStartTime,
                    selectedEndTime = selectedEndTime,
                    onDateTimeRangeClick = { showDateTimeRangePicker = true },
                    onPrioritySelected = { priority ->
                        selectedPriority = priority
                    },
                    // 新增：提醒设置参数
                    reminderEnabled = reminderEnabled,
                    selectedReminderDate = selectedReminderDate,
                    selectedReminderTime = selectedReminderTime,
                    onReminderEnabledChange = { enabled ->
                        reminderEnabled = enabled
                        if (enabled && selectedReminderDate == null) {
                            // 默认设置为开始时间前10分钟提醒
                            selectedReminderDate = selectedStartDate ?: LocalDate.now()
                            selectedReminderTime = (selectedStartTime ?: LocalTime.of(9, 0)).minusMinutes(10)
                        }
                    },
                    onReminderClick = { showReminderPicker = true },
                    // 🔧 循环设置参数
                    isRecurring = isRecurring,
                    recurrenceSettings = recurrenceSettings,
                    selectedRecurrenceType = selectedRecurrenceType,
                    onRecurrenceClick = { showRecurrenceDialog = true }
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 标签区域
                TagsSection(
                    tags = tags,
                    onAddTag = { showTagInput = true },
                    onRemoveTag = { tagToRemove ->
                        tags = tags.filter { it != tagToRemove }
                    }
                )

                Spacer(modifier = Modifier.height(24.dp))

                // 子任务区域
                SubTasksSection(
                    subTasks = subTasks,
                    onAddSubTask = { showSubTaskInput = true },
                    onSubTaskToggle = { subTaskId ->
                        subTasks = subTasks.map { subTask ->
                            if (subTask.id == subTaskId) {
                                subTask.copy(isCompleted = !subTask.isCompleted)
                            } else {
                                subTask
                            }
                        }
                    },
                    onSubTaskRemove = { subTaskId ->
                        subTasks = subTasks.filter { it.id != subTaskId }
                    }
                )

                Spacer(modifier = Modifier.height(32.dp))

                // 创建按钮
                CreateTaskButton(
                    onClick = {
                        if (title.isNotBlank()) {
                            scope.launch {
                                isCreating = true
                                try {
                                    // 计算最终的开始和截止时间
                                    val finalStartDate = selectedStartDate?.atTime(selectedStartTime ?: LocalTime.of(9, 0))
                                    val finalEndDate = selectedEndDate?.atTime(selectedEndTime ?: LocalTime.of(18, 0))
                                    
                                    // 计算提醒时间
                                    val finalReminderTime = if (reminderEnabled) {
                                        val reminderDate = selectedReminderDate
                                        val reminderTime = selectedReminderTime
                                        if (reminderDate != null && reminderTime != null) {
                                            combineDateAndTime(reminderDate, reminderTime)
                                        } else null
                                    } else null

                                    // 🔧 序列化循环设置
                                    val recurringPatternJson = if (isRecurring) {
                                        try {
                                            Json.encodeToString(recurrenceSettings)
                                        } catch (e: Exception) {
                                            Log.e("AddTaskScreen", "序列化循环设置失败", e)
                                            null
                                        }
                                    } else {
                                        null
                                    }

                                    val newTask = ModelTask(
                                        id = UUID.randomUUID().toString(),
                                        title = title,
                                        description = description,
                                        priority = selectedPriority,
                                        startDate = finalStartDate,
                                        dueDate = finalEndDate ?: finalStartDate, // 如果没有截止日期，使用开始日期
                                        reminderTime = finalReminderTime, // 🔔 设置提醒时间
                                        tags = tags.map { TaskTag(name = it, color = DustyLavender) },
                                        isRecurring = isRecurring, // 🔧 添加循环标记
                                        recurringPattern = recurringPatternJson, // 🔧 添加循环设置
                                        createdAt = LocalDateTime.now(),
                                        updatedAt = LocalDateTime.now(),
                                        // 如果用户选择了日期，标记为手动修改
                                        dateManuallyModified = selectedStartDate != null || selectedEndDate != null
                                    )
                                    
                                    Log.d("AddTaskScreen", "=== 开始任务创建流程 ===")
                                    Log.d("AddTaskScreen", "任务详情: title=${newTask.title}, id=${newTask.id}")
                                    Log.d("AddTaskScreen", "开始日期: ${newTask.startDate}")
                                    Log.d("AddTaskScreen", "截止日期: ${newTask.dueDate}")
                                    Log.d("AddTaskScreen", "提醒时间: ${newTask.reminderTime}")
                                    Log.d("AddTaskScreen", "优先级: ${newTask.priority}")
                                    
                                    // 🚀 优化后的任务创建流程 - 移除不必要的延迟
                                    Log.d("AddTaskScreen", "=== 快速任务创建流程 ===")
                                    
                                    // Step 1: 保存主任务
                                    Log.d("AddTaskScreen", "保存主任务: ${newTask.title}")
                                    viewModel.saveTask(newTask)
                                    
                                    // Step 2: 批量保存子任务（如果有）
                                    if (subTasks.isNotEmpty()) {
                                        Log.d("AddTaskScreen", "批量保存 ${subTasks.size} 个子任务")
                                        val subTaskModels = subTasks.map { subTask ->
                                            ModelTask(
                                                id = subTask.id,
                                                title = subTask.title,
                                                description = subTask.note,
                                                priority = subTask.priority,
                                                dueDate = subTask.dueDate,
                                                parentTaskId = newTask.id,
                                                createdAt = LocalDateTime.now(),
                                                updatedAt = LocalDateTime.now(),
                                                dateManuallyModified = subTask.dueDate != null
                                            )
                                        }
                                        // 批量保存，减少数据库操作次数
                                        subTaskModels.forEach { viewModel.saveTask(it) }
                                    }
                                    
                                    // Step 3: 立即反馈用户并返回（Room Flow会自动更新UI）
                                    Log.d("AddTaskScreen", "任务创建成功，立即返回")
                                    Toast.makeText(context, "任务「${newTask.title}」创建成功", Toast.LENGTH_SHORT).show()
                                    navController.popBackStack()
                                } catch (e: Exception) {
                                    Log.e("AddTaskScreen", "创建任务失败", e)
                                    Toast.makeText(context, "创建失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                } finally {
                                    isCreating = false
                                }
                            }
                        } else {
                            Toast.makeText(context, "请输入任务标题", Toast.LENGTH_SHORT).show()
                        }
                    },
                    isLoading = isCreating,
                    enabled = title.isNotBlank()
                )

                Spacer(modifier = Modifier.height(40.dp))
            }
        }
    }

    // 日期时间范围选择器
    if (showDateTimeRangePicker) {
        // 🎯 使用当前时间作为默认值
        val now = LocalTime.now()
        val currentHour = now.hour
        val currentMinute = (now.minute / 15) * 15 // 取最接近的15分钟间隔
        val currentTime = LocalTime.of(currentHour, currentMinute)
        val endTime = currentTime.plusHours(2) // 默认结束时间为开始时间+2小时
        
        EnhancedDateTimeRangePickerDialog(
            initialStartDate = selectedStartDate ?: LocalDate.now(),
            initialEndDate = selectedEndDate ?: (selectedStartDate ?: LocalDate.now()),
            initialStartTime = selectedStartTime ?: currentTime, // 🎯 使用当前时间
            initialEndTime = selectedEndTime ?: endTime, // 🎯 使用当前时间+2小时
            onDismiss = { showDateTimeRangePicker = false },
            onDateTimeRangeSelected = { startDate, endDate, startTime, endTime ->
                selectedStartDate = startDate
                selectedEndDate = endDate
                selectedStartTime = startTime
                selectedEndTime = endTime
                showDateTimeRangePicker = false
            }
        )
    }

    // 标签输入对话框
    if (showTagInput) {
        TagInputDialog(
            onDismiss = { showTagInput = false },
            onTagAdded = { newTag ->
                if (newTag.isNotBlank() && !tags.contains(newTag)) {
                    tags = tags + newTag
                }
                showTagInput = false
            }
        )
    }

    // 子任务输入对话框
    if (showSubTaskInput) {
        SubTaskInputDialog(
            onDismiss = { showSubTaskInput = false },
            onSubTaskAdded = { newSubTask ->
                subTasks = subTasks + newSubTask
                showSubTaskInput = false
            }
        )
    }
    
    // 愿望导入对话框
    if (showWishImportDialog) {
        WishImportDialog(
            onDismiss = { showWishImportDialog = false },
            onWishSelected = { wish ->
                // 从愿望导入任务信息
                title = wish.title
                description = wish.description
                // 根据愿望难度设置优先级
                selectedPriority = when (wish.difficulty) {
                    com.timeflow.app.data.model.WishDifficulty.EASY -> Priority.LOW
                    com.timeflow.app.data.model.WishDifficulty.MEDIUM -> Priority.MEDIUM
                    com.timeflow.app.data.model.WishDifficulty.HARD -> Priority.HIGH
                    com.timeflow.app.data.model.WishDifficulty.EXTREME -> Priority.URGENT
                }
                // 添加愿望相关的标签
                if (wish.tags.isNotEmpty()) {
                    tags = wish.tags
                }
                showWishImportDialog = false
            }
        )
    }
    
    // 提醒时间选择对话框
    if (showReminderPicker) {
        ReminderTimePickerDialog(
            initialDate = selectedReminderDate ?: LocalDate.now(),
            initialTime = selectedReminderTime ?: LocalTime.of(9, 0),
            onDismiss = { showReminderPicker = false },
            onTimeSelected = { date, time ->
                selectedReminderDate = date
                selectedReminderTime = time
                showReminderPicker = false
            }
        )
    }

    // 🔧 循环设置对话框
    if (showRecurrenceDialog) {
        RecurrenceSettingsDialog(
            isRecurring = isRecurring,
            recurrenceSettings = recurrenceSettings,
            selectedRecurrenceType = selectedRecurrenceType,
            onDismiss = { showRecurrenceDialog = false },
            onConfirm = { recurring, settings, type ->
                isRecurring = recurring
                recurrenceSettings = settings
                selectedRecurrenceType = type
                showRecurrenceDialog = false
            }
        )
    }
}

@Composable
private fun TopNavigationBar(
    onBackClick: () -> Unit,
    onWishImportClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = AddTaskBackground,
        shadowElevation = 0.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(36.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = AddTaskTextPrimary,
                    modifier = Modifier.size(22.dp)
                )
            }

            // 标题
            Text(
                text = "新建任务",
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = AddTaskTextPrimary
                )
            )

            // 从愿望导入按钮
            Surface(
                modifier = Modifier
                    .clickable { onWishImportClick() },
                color = Color(0xFFFFD700).copy(alpha = 0.1f), // 淡金色背景
                shape = RoundedCornerShape(18.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = "从愿望导入",
                        tint = Color(0xFFFFD700), // 金色
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "愿望",
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFFFFD700) // 金色
                        )
                    )
                }
            }
        }
    }
}

@Composable
private fun TaskTitleInput(
    title: String,
    onTitleChange: (String) -> Unit
) {
    Column {
        // 使用Surface包装，统一背景设计，取消阴影
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = AddTaskCardBackground, // 使用统一的卡片背景色
            shadowElevation = 0.dp, // 取消阴影
            shape = RoundedCornerShape(16.dp)
        ) {
            BasicTextField(
                value = title,
                onValueChange = onTitleChange,
                textStyle = TextStyle(
                    fontSize = 22.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = AddTaskTextPrimary,
                    letterSpacing = 0.2.sp // 增加字母间距
                ),
                cursorBrush = SolidColor(AddTaskAccent), // 使用暖橙色光标
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 16.dp), // 增加内边距
                decorationBox = { innerTextField ->
                    if (title.isEmpty()) {
                        Text(
                            text = "任务标题",
                            style = TextStyle(
                                fontSize = 22.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = AddTaskTextSecondary.copy(alpha = 0.4f), // 降低占位符透明度
                                letterSpacing = 0.2.sp
                            )
                        )
                    }
                    innerTextField()
                }
            )
        }
    }
}

@Composable
private fun TaskDescriptionInput(
    description: String,
    onDescriptionChange: (String) -> Unit
) {
    Column {
        // 使用Surface包装，取消阴影，统一背景
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(140.dp), // 固定高度
            color = AddTaskCardBackground, // 使用统一的卡片背景色
            shadowElevation = 0.dp, // 取消阴影
            shape = RoundedCornerShape(16.dp)
        ) {
            BasicTextField(
                value = description,
                onValueChange = onDescriptionChange,
                textStyle = TextStyle(
                    fontSize = 15.sp,
                    color = AddTaskTextPrimary,
                    lineHeight = 24.sp,
                    letterSpacing = 0.1.sp
                ),
                cursorBrush = SolidColor(AddTaskAccent), // 使用暖橙色光标
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp), // 增加内边距
                decorationBox = { innerTextField ->
                    if (description.isEmpty()) {
                        Text(
                            text = "添加备注，让任务更清晰...", // 更友好的占位符文字
                            style = TextStyle(
                                fontSize = 15.sp,
                                color = AddTaskTextSecondary.copy(alpha = 0.5f),
                                lineHeight = 24.sp,
                                letterSpacing = 0.1.sp
                            )
                        )
                    }
                    innerTextField()
                }
            )
        }
    }
}

@Composable
private fun TaskOptionsSection(
    selectedPriority: Priority,
    selectedStartDate: LocalDate?,
    selectedEndDate: LocalDate?,
    selectedStartTime: LocalTime?,
    selectedEndTime: LocalTime?,
    onDateTimeRangeClick: () -> Unit,
    onPrioritySelected: (Priority) -> Unit,
    // 新增：提醒设置参数
    reminderEnabled: Boolean,
    selectedReminderDate: LocalDate?,
    selectedReminderTime: LocalTime?,
    onReminderEnabledChange: (Boolean) -> Unit,
    onReminderClick: () -> Unit,
    // 🔧 循环设置参数
    isRecurring: Boolean,
    recurrenceSettings: RecurrenceSettings,
    selectedRecurrenceType: RecurrenceType,
    onRecurrenceClick: () -> Unit
) {
    Column {
        // 日期时间选择
        DateTimeRangeOptionCard(
            selectedStartDate = selectedStartDate,
            selectedEndDate = selectedEndDate,
            selectedStartTime = selectedStartTime,
            selectedEndTime = selectedEndTime,
            onClick = onDateTimeRangeClick
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 优先级选择
        PriorityOptionCard(
            selectedPriority = selectedPriority,
            onPrioritySelected = onPrioritySelected
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 提醒设置
        ReminderOptionCard(
            reminderEnabled = reminderEnabled,
            selectedReminderDate = selectedReminderDate,
            selectedReminderTime = selectedReminderTime,
            onReminderEnabledChange = onReminderEnabledChange,
            onReminderClick = onReminderClick
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 🔧 循环设置
        RecurrenceOptionCard(
            isRecurring = isRecurring,
            selectedRecurrenceType = selectedRecurrenceType,
            onRecurrenceClick = onRecurrenceClick
        )
    }
}

@Composable
private fun DateTimeRangeOptionCard(
    selectedStartDate: LocalDate?,
    selectedEndDate: LocalDate?,
    selectedStartTime: LocalTime?,
    selectedEndTime: LocalTime?,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .clickable { onClick() },
        color = AddTaskCardBackground, // 使用统一的卡片背景色
        shadowElevation = 0.dp, // 取消阴影
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                brush = if (selectedStartDate != null) {
                                    Brush.radialGradient(
                                        colors = listOf(
                                            AddTaskAccent.copy(alpha = 0.15f),
                                            AddTaskAccent.copy(alpha = 0.08f)
                                        )
                                    )
                                } else {
                                    Brush.radialGradient(
                                        colors = listOf(
                                            AddTaskIconGray.copy(alpha = 0.12f),
                                            AddTaskIconGray.copy(alpha = 0.06f)
                                        )
                                    )
                                },
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = null,
                            tint = if (selectedStartDate != null) AddTaskAccent else AddTaskIconGray.copy(alpha = 0.8f),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column {
                        Text(
                            text = "选择日期",
                            style = TextStyle(
                                fontSize = 15.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = AddTaskTextPrimary.copy(alpha = 0.9f),
                                letterSpacing = 0.3.sp
                            )
                        )

                        Spacer(modifier = Modifier.height(6.dp))

                        Text(
                            text = formatSelectedDateTimeRange(selectedStartDate, selectedEndDate, selectedStartTime, selectedEndTime),
                            style = TextStyle(
                                fontSize = 13.sp,
                                color = if (selectedStartDate != null) AddTaskAccent else AddTaskTextSecondary.copy(alpha = 0.7f),
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }
                }

                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = AddTaskIconGray.copy(alpha = 0.6f),
                    modifier = Modifier.size(22.dp)
                )
            }
        }
    }
}

@Composable
private fun PriorityOptionCard(
    selectedPriority: Priority,
    onPrioritySelected: (Priority) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .clickable { },
        color = AddTaskCardBackground, // 使用统一的卡片背景色
        shadowElevation = 0.dp, // 取消阴影
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 左侧：标题和当前选择
                Column {
                    Text(
                        text = "优先级",
                        style = TextStyle(
                            fontSize = 15.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = AddTaskTextPrimary.copy(alpha = 0.9f),
                            letterSpacing = 0.3.sp
                        )
                    )

                    Spacer(modifier = Modifier.height(6.dp))

                    Text(
                        text = getPriorityDisplayText(selectedPriority),
                        style = TextStyle(
                            fontSize = 13.sp,
                            color = PriorityColors[selectedPriority] ?: AddTaskTextSecondary,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }
            
            // 分隔线
            Spacer(modifier = Modifier.height(16.dp))
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(AddTaskTextSecondary.copy(alpha = 0.1f))
            )
            Spacer(modifier = Modifier.height(16.dp))

            // 优先级选择器 - 横排圆形图标，包含紧急选项，使用首字标签
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly, // 改为均匀分布
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 低优先级
                PriorityCircleIcon(
                    priority = Priority.LOW,
                    isSelected = selectedPriority == Priority.LOW,
                    onClick = { onPrioritySelected(Priority.LOW) },
                    label = "低"
                )
                
                // 中优先级
                PriorityCircleIcon(
                    priority = Priority.MEDIUM,
                    isSelected = selectedPriority == Priority.MEDIUM,
                    onClick = { onPrioritySelected(Priority.MEDIUM) },
                    label = "中"
                )
                
                // 高优先级
                PriorityCircleIcon(
                    priority = Priority.HIGH,
                    isSelected = selectedPriority == Priority.HIGH,
                    onClick = { onPrioritySelected(Priority.HIGH) },
                    label = "高"
                )
                
                // 紧急
                PriorityCircleIcon(
                    priority = Priority.URGENT,
                    isSelected = selectedPriority == Priority.URGENT,
                    onClick = { onPrioritySelected(Priority.URGENT) },
                    label = "紧"
                )
            }
        }
    }
}

@Composable
private fun PriorityCircleIcon(
    priority: Priority?,
    isSelected: Boolean,
    onClick: () -> Unit,
    label: String
) {
    val color = priority?.let { PriorityColors[it] } ?: PriorityNoneColor
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }
    ) {
        // 圆形图标
        Box(
            modifier = Modifier
                .size(44.dp)
                .background(
                    color = if (isSelected) color else color.copy(alpha = 0.2f),
                    shape = CircleShape
                )
                .border(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = if (isSelected) color else color.copy(alpha = 0.4f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Flag,
                contentDescription = null,
                tint = if (isSelected) Color.White else color,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 标签文字
        Text(
            text = label,
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                color = if (isSelected) color else AddTaskTextSecondary
            )
        )
    }
}

// 现代化滑动式日期时间范围选择器
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EnhancedDateTimeRangePickerDialog(
    initialStartDate: LocalDate,
    initialEndDate: LocalDate,
    initialStartTime: LocalTime,
    initialEndTime: LocalTime,
    onDismiss: () -> Unit,
    onDateTimeRangeSelected: (LocalDate, LocalDate, LocalTime, LocalTime) -> Unit
) {
    var selectedStartDate by remember { mutableStateOf(initialStartDate) }
    var selectedEndDate by remember { mutableStateOf(initialEndDate) }
    var selectedStartTime by remember { mutableStateOf(initialStartTime) }
    var selectedEndTime by remember { mutableStateOf(initialEndTime) }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(0.dp)
            ) {
                // 简洁的顶部栏
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    AddTaskPrimary.copy(alpha = 0.08f),
                                    Color.Transparent
                                )
                            )
                        )
                        .padding(horizontal = 20.dp, vertical = 16.dp)
                ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                            text = "设定时间",
                        style = TextStyle(
                                fontSize = 17.sp,
                            fontWeight = FontWeight.SemiBold,
                                color = AddTaskTextPrimary
                        )
                    )

                    IconButton(
                        onClick = onDismiss,
                            modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                                tint = AddTaskTextSecondary.copy(alpha = 0.7f),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }

                // 紧凑的选择区域
                    Column(
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 12.dp)
                    ) {
                    // 开始时间选择
                    CompactDateTimeSelector(
                        title = "开始时间",
                        icon = Icons.Default.PlayArrow,
                        iconColor = AddTaskPrimary,
                        selectedDate = selectedStartDate,
                        selectedTime = selectedStartTime,
                        onDateSelected = { selectedStartDate = it },
                        onTimeSelected = { selectedStartTime = it }
                    )

                    // 分割线
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                    ) {
                        Divider(
                            color = AddTaskBorder.copy(alpha = 0.5f),
                            thickness = 0.5.dp
                        )
                        
                        // 中间的连接图标
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .background(Color.White, CircleShape)
                                .align(Alignment.Center),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.ArrowDownward,
                                contentDescription = null,
                                tint = AddTaskIconGray.copy(alpha = 0.6f),
                                modifier = Modifier.size(14.dp)
                            )
                        }
                    }

                    // 结束时间选择
                    CompactDateTimeSelector(
                        title = "结束时间",
                        icon = Icons.Default.Stop,
                        iconColor = AddTaskAccent,
                        selectedDate = selectedEndDate,
                        selectedTime = selectedEndTime,
                        onDateSelected = { selectedEndDate = it },
                        onTimeSelected = { selectedEndTime = it }
                    )
                }

                // 底部操作区
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFFAFBFC),
                    shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { onDismiss() },
                            color = Color.Transparent,
                            shape = RoundedCornerShape(12.dp),
                            border = BorderStroke(1.dp, AddTaskBorder.copy(alpha = 0.6f))
                        ) {
                            Box(
                                modifier = Modifier.padding(vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                            Text(
                                    text = "取消",
                                style = TextStyle(
                                        fontSize = 15.sp,
                                    fontWeight = FontWeight.Medium,
                                        color = AddTaskTextSecondary
                                )
                            )
                        }
                        }

                        // 确认按钮
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable {
                                    onDateTimeRangeSelected(selectedStartDate, selectedEndDate, selectedStartTime, selectedEndTime)
                                },
                            color = AddTaskPrimary,
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Box(
                                modifier = Modifier.padding(vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "确定",
                                    style = TextStyle(
                                        fontSize = 15.sp,
                                        fontWeight = FontWeight.SemiBold,
                                        color = Color.White
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

// 紧凑的日期时间选择组件
@Composable
private fun CompactDateTimeSelector(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    iconColor: Color,
    selectedDate: LocalDate,
    selectedTime: LocalTime,
    onDateSelected: (LocalDate) -> Unit,
    onTimeSelected: (LocalTime) -> Unit
) {
    Column {
        // 标题行
                        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 12.dp)
                        ) {
                            Icon(
                imageVector = icon,
                                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                text = title,
                                style = TextStyle(
                                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = AddTaskTextPrimary
                                )
                            )
                        }

        // 日期和时间选择器行
                Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 滑动日期选择器
            ModernDatePicker(
                selectedDate = selectedDate,
                onDateSelected = onDateSelected,
                        modifier = Modifier.weight(1f)
                    )

            // 滑动时间选择器
            ModernTimePicker(
                selectedTime = selectedTime,
                onTimeSelected = onTimeSelected,
                        modifier = Modifier.weight(1f)
                    )
                }
    }
}

// 现代化滑动日期选择器
@Composable
private fun ModernDatePicker(
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    val today = LocalDate.now()
    val dates = remember {
        (0..30).map { today.plusDays(it.toLong()) }
    }
    
    // 🎯 添加LazyListState用于自动滚动
    val dateListState = rememberLazyListState()
    
    // 🎯 自动滚动到选中的日期
    LaunchedEffect(selectedDate) {
        val dateIndex = dates.indexOf(selectedDate)
        if (dateIndex >= 0) {
            // 滚动到选中的日期，让其在视图中居中
            dateListState.animateScrollToItem(
                index = maxOf(0, dateIndex - 1), // 稍微向上偏移，让选中项更居中
                scrollOffset = 0
            )
        }
    }

    Surface(
        modifier = modifier,
        color = AddTaskCardBackground,
        shape = RoundedCornerShape(12.dp)
    ) {
        LazyColumn(
            state = dateListState, // 🎯 使用状态控制滚动
            modifier = Modifier
                .height(120.dp)
                .padding(vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            contentPadding = PaddingValues(vertical = 40.dp)
        ) {
            items(dates) { date ->
                val isSelected = date == selectedDate
                val isToday = date == today
                val isTomorrow = date == today.plusDays(1)

                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onDateSelected(date) }
                        .padding(horizontal = 12.dp),
                    color = if (isSelected) AddTaskPrimary.copy(alpha = 0.12f) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(vertical = 8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = when {
                                isToday -> "今天"
                                isTomorrow -> "明天"
                                else -> date.format(DateTimeFormatter.ofPattern("MM/dd"))
                            },
                            style = TextStyle(
                                fontSize = 13.sp,
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                            )
                        )
                        
                        if (!isToday && !isTomorrow) {
                            Text(
                                text = when (date.dayOfWeek.value) {
                                    1 -> "周一"
                                    2 -> "周二"
                                    3 -> "周三"
                                    4 -> "周四"
                                    5 -> "周五"
                                    6 -> "周六"
                                    7 -> "周日"
                                    else -> ""
                                },
                                style = TextStyle(
                                    fontSize = 10.sp,
                                    fontWeight = FontWeight.Normal,
                                    color = if (isSelected) AddTaskPrimary.copy(alpha = 0.8f) else AddTaskTextSecondary.copy(alpha = 0.7f)
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}

// 现代化滑动时间选择器
@Composable
private fun ModernTimePicker(
    selectedTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    modifier: Modifier = Modifier
) {
    val hours = remember { (0..23).toList() }
    val minutes = remember { (0..59 step 15).toList() } // 15分钟间隔
    
    // 🎯 添加LazyListState用于自动滚动
    val hourListState = rememberLazyListState()
    val minuteListState = rememberLazyListState()
    
    // 🎯 自动滚动到当前时间
    LaunchedEffect(selectedTime) {
        // 计算当前小时在列表中的索引位置
        val hourIndex = hours.indexOf(selectedTime.hour)
        if (hourIndex >= 0) {
            // 滚动到选中的小时，让其在视图中居中
            hourListState.animateScrollToItem(
                index = maxOf(0, hourIndex - 1), // 稍微向上偏移，让选中项更居中
                scrollOffset = 0
            )
        }
        
        // 找到最接近的分钟（因为是15分钟间隔）
        val currentMinute = selectedTime.minute
        val closestMinute = minutes.minByOrNull { kotlin.math.abs(it - currentMinute) } ?: 0
        val minuteIndex = minutes.indexOf(closestMinute)
        if (minuteIndex >= 0) {
            // 滚动到选中的分钟，让其在视图中居中
            minuteListState.animateScrollToItem(
                index = maxOf(0, minuteIndex - 1), // 稍微向上偏移，让选中项更居中
                scrollOffset = 0
            )
        }
    }

    Surface(
        modifier = modifier,
        color = AddTaskCardBackground,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier.height(120.dp)
        ) {
            // 小时选择
            LazyColumn(
                state = hourListState, // 🎯 使用状态控制滚动
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                contentPadding = PaddingValues(vertical = 40.dp)
            ) {
                items(hours) { hour ->
                    val isSelected = hour == selectedTime.hour
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onTimeSelected(selectedTime.withHour(hour))
                            }
                            .padding(horizontal = 8.dp),
                        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.12f) else Color.Transparent,
                        shape = RoundedCornerShape(6.dp)
                    ) {
                        Text(
                            text = String.format("%02d", hour),
                            modifier = Modifier.padding(vertical = 8.dp),
                            textAlign = TextAlign.Center,
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                                )
                            )
                        }
                    }
                }

            // 分隔符
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .fillMaxHeight()
                    .background(AddTaskBorder.copy(alpha = 0.3f))
            )

            // 分钟选择
            LazyColumn(
                state = minuteListState, // 🎯 使用状态控制滚动
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                contentPadding = PaddingValues(vertical = 40.dp)
            ) {
                items(minutes) { minute ->
                    // 🎯 改进分钟选择逻辑，找到最接近的15分钟间隔
                    val currentMinute = selectedTime.minute
                    val closestMinute = minutes.minByOrNull { kotlin.math.abs(it - currentMinute) } ?: 0
                    val isSelected = minute == closestMinute
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onTimeSelected(selectedTime.withMinute(minute))
                            }
                            .padding(horizontal = 8.dp),
                        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.12f) else Color.Transparent,
                        shape = RoundedCornerShape(6.dp)
                    ) {
                        Text(
                            text = String.format("%02d", minute),
                            modifier = Modifier.padding(vertical = 8.dp),
                            textAlign = TextAlign.Center,
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TabButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable { onClick() },
        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.1f) else Color.Transparent,
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                color = if (isSelected) AddTaskPrimary else AddTaskTextSecondary
            ),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun CreateTaskButton(
    onClick: () -> Unit,
    isLoading: Boolean,
    enabled: Boolean
) {
    // 使用Surface替代Button，获得更好的控制
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .height(52.dp) // 稍微增加高度
            .clip(RoundedCornerShape(26.dp)) // 更圆润的圆角
            .clickable(enabled = enabled && !isLoading) { onClick() },
        color = Color.Transparent, // 透明背景，使用渐变
        shadowElevation = if (enabled && !isLoading) 8.dp else 2.dp, // 动态阴影
        shape = RoundedCornerShape(26.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    color = if (enabled && !isLoading) {
                        // 活跃状态：纯色背景，取消渐变
                        AddTaskPrimary
                    } else {
                        // 禁用状态：纯色背景，取消渐变
                        AddTaskPrimary.copy(alpha = 0.4f)
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(22.dp), // 稍微增大
                        color = Color.White,
                        strokeWidth = 2.5.dp
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                } else {
                    // 使用更现代的图标或形状
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .background(
                                Color.White.copy(alpha = 0.2f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                }

                Text(
                    text = if (isLoading) "创建中..." else "创建任务",
                    style = TextStyle(
                        fontSize = 16.sp, // 稍微增大字体
                        fontWeight = FontWeight.SemiBold,
                        color = Color.White,
                        letterSpacing = 0.5.sp // 增加字母间距
                    )
                )
            }
        }
    }
}

// 辅助函数
private fun formatSelectedDateTimeRange(startDate: LocalDate?, endDate: LocalDate?, startTime: LocalTime?, endTime: LocalTime?): String {
    return when {
        startDate == null && endDate == null && startTime == null && endTime == null -> "选择的日期"
        startDate != null && endDate != null && startTime != null && endTime != null -> {
            // 🎯 修复重复显示日期的问题
            if (startDate.isEqual(endDate)) {
                // 同一天，只显示一次日期，用逗号分隔
                "${formatDisplayDate(startDate)}, ${startTime.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${endTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
            } else {
                // 不同日期，显示完整的日期时间范围，用逗号分隔
                "${formatDisplayDate(startDate)}, ${startTime.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${formatDisplayDate(endDate)}, ${endTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
            }
        }
        startDate != null && endDate != null -> {
            // 只有日期没有时间的情况
            if (startDate.isEqual(endDate)) {
                // 同一天，只显示一次日期
                formatDisplayDate(startDate)
            } else {
                // 不同日期，显示日期范围，用逗号分隔
                "${formatDisplayDate(startDate)}, ${formatDisplayDate(endDate)}"
            }
        }
        startDate != null && startTime != null -> {
            // 只有开始日期和时间，用逗号分隔
            "${formatDisplayDate(startDate)}, ${startTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
        }
        startDate != null -> {
            // 只有开始日期
            formatDisplayDate(startDate)
        }
        else -> "选择的日期"
    }
}

private fun formatDisplayDate(date: LocalDate): String {
    val today = LocalDate.now()
    return when {
        date.isEqual(today) -> "今天"
        date.isEqual(today.plusDays(1)) -> "明天"
        date.isEqual(today.plusDays(2)) -> "后天"
        ChronoUnit.DAYS.between(today, date) < 7 -> {
            when (date.dayOfWeek.value) {
                1 -> "周一"; 2 -> "周二"; 3 -> "周三"; 4 -> "周四"
                5 -> "周五"; 6 -> "周六"; 7 -> "周日"
                else -> date.format(DateTimeFormatter.ofPattern("M.d日"))
            }
        }
        date.year == today.year -> date.format(DateTimeFormatter.ofPattern("M.d日"))
        else -> date.format(DateTimeFormatter.ofPattern("yyyy.M.d"))
    }
}

private fun getPriorityDisplayText(priority: Priority): String {
    return when (priority) {
        Priority.LOW -> "低优先级"
        Priority.MEDIUM -> "普通优先级"
        Priority.HIGH -> "高优先级"
        Priority.URGENT -> "紧急"
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun TagsSection(
    tags: List<String>,
    onAddTag: () -> Unit,
    onRemoveTag: (String) -> Unit
) {
    Column {
        // 美化标签标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 添加装饰性图标
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    AddTaskPrimary.copy(alpha = 0.15f),
                                    AddTaskPrimary.copy(alpha = 0.05f)
                                )
                            ),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Label,
                        contentDescription = null,
                        tint = AddTaskPrimary.copy(alpha = 0.8f),
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = "标签",
                    style = TextStyle(
                        fontSize = 16.sp, // 增大字体
                        fontWeight = FontWeight.SemiBold, // 加粗
                        color = AddTaskTextPrimary.copy(alpha = 0.9f),
                        letterSpacing = 0.3.sp
                    )
                )
            }

            // 美化添加按钮 - 取消阴影
            Surface(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .clickable { onAddTag() },
                color = AddTaskAccent.copy(alpha = 0.1f), // 使用暖橙色
                shadowElevation = 0.dp, // 取消阴影
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加标签",
                        tint = AddTaskAccent, // 使用暖橙色
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // 标签显示区域
        if (tags.isNotEmpty()) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(10.dp), // 增加间距
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                tags.forEach { tag ->
                    TagChip(
                        tag = tag,
                        onRemove = { onRemoveTag(tag) }
                    )
                }
            }
        } else {
            // 优化空状态提示 - 取消阴影，使用统一背景
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp)) // 与其他卡片保持一致
                    .clickable { onAddTag() },
                color = AddTaskCardBackground, // 使用统一的卡片背景色
                shadowElevation = 0.dp, // 取消阴影
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier.padding(20.dp), // 增加内边距
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .background(
                                AddTaskTextSecondary.copy(alpha = 0.1f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Label,
                            contentDescription = null,
                            tint = AddTaskTextSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "添加标签",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = AddTaskTextSecondary.copy(alpha = 0.8f)
                            )
                        )
                        Text(
                            text = "为任务分类，便于管理",
                            style = TextStyle(
                                fontSize = 12.sp,
                                color = AddTaskTextSecondary.copy(alpha = 0.6f)
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TagChip(
    tag: String,
    onRemove: () -> Unit
) {
    Surface(
        color = Color.Transparent, // 透明背景，使用渐变
        shape = RoundedCornerShape(20.dp), // 更圆润
        shadowElevation = 0.dp // 取消阴影
    ) {
        Box(
            modifier = Modifier.background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        AddTaskAccent.copy(alpha = 0.12f), // 使用暖橙色
                        AddTaskAccent.copy(alpha = 0.08f)
                    )
                ),
                shape = RoundedCornerShape(20.dp)
            )
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp), // 增加内边距
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = tag,
                    style = TextStyle(
                        fontSize = 13.sp, // 稍微减小字体
                        color = AddTaskAccent.copy(alpha = 0.9f), // 使用暖橙色
                        fontWeight = FontWeight.SemiBold, // 加粗
                        letterSpacing = 0.2.sp
                    )
                )
                Spacer(modifier = Modifier.width(6.dp))
                
                // 美化删除按钮
                Surface(
                    modifier = Modifier
                        .size(18.dp)
                        .clip(CircleShape)
                        .clickable { onRemove() },
                    color = AddTaskAccent.copy(alpha = 0.15f), // 使用暖橙色
                    shape = CircleShape
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "删除标签",
                            tint = AddTaskAccent.copy(alpha = 0.8f), // 使用暖橙色
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SubTasksSection(
    subTasks: List<SubTask>,
    onAddSubTask: () -> Unit,
    onSubTaskToggle: (String) -> Unit,
    onSubTaskRemove: (String) -> Unit
) {
    Column {
        // 美化子任务标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 添加装饰性图标
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    AddTaskPrimary.copy(alpha = 0.15f),
                                    AddTaskPrimary.copy(alpha = 0.05f)
                                )
                            ),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.List,
                        contentDescription = null,
                        tint = AddTaskPrimary.copy(alpha = 0.8f),
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = "子任务",
                    style = TextStyle(
                        fontSize = 16.sp, // 增大字体
                        fontWeight = FontWeight.SemiBold, // 加粗
                        color = AddTaskTextPrimary.copy(alpha = 0.9f),
                        letterSpacing = 0.3.sp
                    )
                )

                if (subTasks.isNotEmpty()) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Surface(
                        color = AddTaskPrimary.copy(alpha = 0.12f),
                        shape = RoundedCornerShape(12.dp), // 更大圆角
                        shadowElevation = 1.dp // 轻微阴影
                    ) {
                        Text(
                            text = "${subTasks.count { it.isCompleted }}/${subTasks.size}",
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 增加内边距
                            style = TextStyle(
                                fontSize = 12.sp, // 稍微增大
                                fontWeight = FontWeight.SemiBold,
                                color = AddTaskPrimary
                            )
                        )
                    }
                }
            }

            // 美化添加按钮 - 取消阴影
            Surface(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .clickable { onAddSubTask() },
                color = AddTaskAccent.copy(alpha = 0.1f), // 使用暖橙色
                shadowElevation = 0.dp, // 取消阴影
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加子任务",
                        tint = AddTaskAccent, // 使用暖橙色
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // 子任务列表
        if (subTasks.isNotEmpty()) {
            Column(
                verticalArrangement = Arrangement.spacedBy(10.dp) // 增加间距
            ) {
                subTasks.forEach { subTask ->
                    SubTaskItem(
                        subTask = subTask,
                        onToggle = { onSubTaskToggle(subTask.id) },
                        onRemove = { onSubTaskRemove(subTask.id) }
                    )
                }
            }
        } else {
            // 优化空状态提示 - 取消阴影，使用统一背景
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp)) // 与其他卡片保持一致
                    .clickable { onAddSubTask() },
                color = AddTaskCardBackground, // 使用统一的卡片背景色
                shadowElevation = 0.dp, // 取消阴影
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier.padding(20.dp), // 增加内边距
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .background(
                                AddTaskTextSecondary.copy(alpha = 0.1f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.List,
                            contentDescription = null,
                            tint = AddTaskTextSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "添加子任务",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = AddTaskTextSecondary.copy(alpha = 0.8f)
                            )
                        )
                        Text(
                            text = "将复杂任务分解为小步骤",
                            style = TextStyle(
                                fontSize = 12.sp,
                                color = AddTaskTextSecondary.copy(alpha = 0.6f)
                            )
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SubTaskItem(
    subTask: SubTask,
    onToggle: () -> Unit,
    onRemove: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = AddTaskCardBackground, // 使用统一的卡片背景色
        shape = RoundedCornerShape(16.dp), // 与其他卡片保持一致
        shadowElevation = 0.dp // 取消阴影
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp), // 增加内边距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 美化复选框区域 - 取消阴影
            Surface(
                modifier = Modifier.size(28.dp),
                color = if (subTask.isCompleted) 
                    AddTaskAccent.copy(alpha = 0.1f)  // 使用暖橙色
                else 
                    AddTaskTextSecondary.copy(alpha = 0.05f),
                shape = CircleShape,
                shadowElevation = 0.dp // 取消阴影
            ) {
                Checkbox(
                    checked = subTask.isCompleted,
                    onCheckedChange = { onToggle() },
                    modifier = Modifier.size(28.dp),
                    colors = CheckboxDefaults.colors(
                        checkedColor = AddTaskAccent, // 使用暖橙色
                        uncheckedColor = AddTaskTextSecondary.copy(alpha = 0.6f),
                        checkmarkColor = Color.White
                    )
                )
            }

            Spacer(modifier = Modifier.width(12.dp)) // 增加间距

            // 子任务内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = subTask.title,
                    style = TextStyle(
                        fontSize = 14.sp, // 稍微增大字体
                        color = if (subTask.isCompleted) 
                            AddTaskTextSecondary.copy(alpha = 0.7f) 
                        else 
                            AddTaskTextPrimary.copy(alpha = 0.9f),
                        fontWeight = if (subTask.isCompleted) FontWeight.Normal else FontWeight.Medium,
                        textDecoration = if (subTask.isCompleted) TextDecoration.LineThrough else TextDecoration.None,
                        letterSpacing = 0.1.sp
                    )
                )
                
                if (subTask.note.isNotBlank()) {
                    Spacer(modifier = Modifier.height(4.dp)) // 增加间距
                    Text(
                        text = subTask.note,
                        style = TextStyle(
                            fontSize = 12.sp, // 稍微增大字体
                            color = AddTaskTextSecondary.copy(alpha = 0.6f),
                            lineHeight = 16.sp
                        )
                    )
                }
            }

            // 美化删除按钮
            Surface(
                modifier = Modifier
                    .size(32.dp)
                    .clip(CircleShape)
                    .clickable { onRemove() },
                color = AddTaskTextSecondary.copy(alpha = 0.06f),
                shape = CircleShape
            ) {
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除子任务",
                        tint = AddTaskTextSecondary.copy(alpha = 0.5f),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun TagInputDialog(
    onDismiss: () -> Unit,
    onTagAdded: (String) -> Unit
) {
    var inputText by remember { mutableStateOf("") }
    val commonTags = listOf("工作", "生活", "学习", "重要", "紧急", "项目", "会议", "阅读", "健身")

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "添加标签",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = AddTaskTextPrimary
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 输入框
                OutlinedTextField(
                    value = inputText,
                    onValueChange = { inputText = it },
                    placeholder = { Text("输入标签名称") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = AddTaskPrimary,
                        cursorColor = AddTaskPrimary
                    ),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (inputText.isNotBlank()) {
                                onTagAdded(inputText.trim())
                            }
                        }
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 常用标签
                Text(
                    text = "常用标签",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = AddTaskTextSecondary
                    )
                )

                Spacer(modifier = Modifier.height(6.dp))

                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    commonTags.forEach { tag ->
                        Surface(
                            modifier = Modifier.clickable {
                                onTagAdded(tag)
                            },
                            color = AddTaskPrimary.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ) {
                            Text(
                                text = tag,
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 5.dp),
                                style = TextStyle(
                                    fontSize = 11.sp,
                                    color = AddTaskPrimary
                                )
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消", color = AddTaskTextSecondary)
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = {
                            if (inputText.isNotBlank()) {
                                onTagAdded(inputText.trim())
                            }
                        },
                        enabled = inputText.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AddTaskPrimary
                        )
                    ) {
                        Text("添加", color = Color.White)
                    }
                }
            }
        }
    }
}

@Composable
private fun SubTaskInputDialog(
    onDismiss: () -> Unit,
    onSubTaskAdded: (SubTask) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var note by remember { mutableStateOf("") }
    var priority by remember { mutableStateOf(Priority.MEDIUM) }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "添加子任务",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = AddTaskTextPrimary
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 子任务标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    placeholder = { Text("子任务标题") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = AddTaskPrimary,
                        cursorColor = AddTaskPrimary
                    )
                )

                Spacer(modifier = Modifier.height(10.dp))

                // 子任务备注输入
                OutlinedTextField(
                    value = note,
                    onValueChange = { note = it },
                    placeholder = { Text("备注 (可选)") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = AddTaskPrimary,
                        cursorColor = AddTaskPrimary
                    )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 优先级选择
                Text(
                    text = "优先级",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = AddTaskTextSecondary
                    )
                )

                Spacer(modifier = Modifier.height(6.dp))

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Priority.values().forEach { priorityOption ->
                        val isSelected = priority == priorityOption
                        val priorityColor = PriorityColors[priorityOption] ?: AddTaskIconGray

                        Surface(
                            modifier = Modifier
                                .clickable { priority = priorityOption },
                            color = if (isSelected) priorityColor.copy(alpha = 0.2f) else Color.Transparent,
                            shape = RoundedCornerShape(8.dp),
                            border = BorderStroke(
                                1.dp,
                                if (isSelected) priorityColor else AddTaskBorder
                            )
                        ) {
                            Text(
                                text = getPriorityDisplayText(priorityOption),
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 5.dp),
                                style = TextStyle(
                                    fontSize = 11.sp,
                                    color = if (isSelected) priorityColor else AddTaskTextSecondary
                                )
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消", color = AddTaskTextSecondary)
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                val newSubTask = SubTask(
                                    id = UUID.randomUUID().toString(),
                                    title = title.trim(),
                                    note = note.trim(),
                                    priority = priority,
                                    isCompleted = false
                                )
                                onSubTaskAdded(newSubTask)
                            }
                        },
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AddTaskPrimary
                        )
                    ) {
                        Text("添加", color = Color.White)
                    }
                }
            }
        }
    }
} 

// 愿望导入对话框
@Composable
private fun WishImportDialog(
    onDismiss: () -> Unit,
    onWishSelected: (com.timeflow.app.data.model.WishModel) -> Unit
) {
    val wishListViewModel: com.timeflow.app.ui.viewmodel.WishListViewModel = hiltViewModel()
    val wishUiState by wishListViewModel.uiState.collectAsState()
    
    // 获取活跃愿望
    val activeWishes = remember(wishUiState.wishes) {
        wishUiState.wishes.filter { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE }
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(0.dp)
            ) {
                // 头部
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFFFD700).copy(alpha = 0.1f),
                                    Color.Transparent
                                )
                            )
                        )
                        .padding(horizontal = 20.dp, vertical = 16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = null,
                                tint = Color(0xFFFFD700),
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "从愿望导入",
                                style = TextStyle(
                                    fontSize = 17.sp,
                                    fontWeight = FontWeight.SemiBold,
                                    color = AddTaskTextPrimary
                                )
                            )
                        }

                        IconButton(
                            onClick = onDismiss,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = AddTaskTextSecondary.copy(alpha = 0.7f),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }

                // 愿望列表
                if (activeWishes.isNotEmpty()) {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 400.dp),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(activeWishes) { wish ->
                            WishImportItem(
                                wish = wish,
                                onClick = { onWishSelected(wish) }
                            )
                        }
                    }
                } else {
                    // 空状态
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = Color.LightGray,
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            text = "暂无可导入的愿望",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "先去愿望池添加一些愿望吧",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                    }
                }

                // 底部操作栏
                if (activeWishes.isNotEmpty()) {
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        color = Color(0xFFFAFBFC),
                        shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 20.dp, vertical = 16.dp),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            TextButton(
                                onClick = onDismiss,
                                colors = ButtonDefaults.textButtonColors(
                                    contentColor = AddTaskTextSecondary
                                )
                            ) {
                                Text("取消")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun WishImportItem(
    wish: com.timeflow.app.data.model.WishModel,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        color = Color(0xFFF8F8F8),
        shape = RoundedCornerShape(12.dp),
        shadowElevation = 1.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 类别emoji
            Text(
                text = wish.category.emoji,
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 愿望内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = wish.title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = Color.DarkGray
                )
                
                if (wish.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = wish.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 显示难度和标签
                if (wish.tags.isNotEmpty() || wish.difficulty != com.timeflow.app.data.model.WishDifficulty.MEDIUM) {
                    Spacer(modifier = Modifier.height(6.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 难度标识
                        if (wish.difficulty != com.timeflow.app.data.model.WishDifficulty.MEDIUM) {
                            Surface(
                                color = when (wish.difficulty) {
                                    com.timeflow.app.data.model.WishDifficulty.EASY -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                                    com.timeflow.app.data.model.WishDifficulty.MEDIUM -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                    com.timeflow.app.data.model.WishDifficulty.HARD -> Color(0xFFF44336).copy(alpha = 0.1f)
                                    com.timeflow.app.data.model.WishDifficulty.EXTREME -> Color(0xFF9C27B0).copy(alpha = 0.1f)
                                },
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = wish.difficulty.displayName,
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                    style = MaterialTheme.typography.labelSmall,
                                    color = when (wish.difficulty) {
                                        com.timeflow.app.data.model.WishDifficulty.EASY -> Color(0xFF4CAF50)
                                        com.timeflow.app.data.model.WishDifficulty.MEDIUM -> Color(0xFFFF9800)
                                        com.timeflow.app.data.model.WishDifficulty.HARD -> Color(0xFFF44336)
                                        com.timeflow.app.data.model.WishDifficulty.EXTREME -> Color(0xFF9C27B0)
                                    }
                                )
                            }
                        }
                        
                        // 标签
                        if (wish.tags.isNotEmpty()) {
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = wish.tags.take(2).joinToString(", "),
                                style = MaterialTheme.typography.labelSmall,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
            
            // 优先级星星
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row {
                    repeat(wish.priority) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = Color(0xFFFFD700),
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = "导入",
                    tint = AddTaskPrimary,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

// 辅助函数：组合日期和时间
private fun combineDateAndTime(date: LocalDate, time: LocalTime): LocalDateTime {
    return LocalDateTime.of(date, time)
}

@Composable
private fun ReminderOptionCard(
    reminderEnabled: Boolean,
    selectedReminderDate: LocalDate?,
    selectedReminderTime: LocalTime?,
    onReminderEnabledChange: (Boolean) -> Unit,
    onReminderClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp)),
        color = AddTaskCardBackground,
        shadowElevation = 0.dp,
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                brush = if (reminderEnabled) {
                                    Brush.radialGradient(
                                        colors = listOf(
                                            AddTaskPrimary.copy(alpha = 0.15f),
                                            AddTaskPrimary.copy(alpha = 0.08f)
                                        )
                                    )
                                } else {
                                    Brush.radialGradient(
                                        colors = listOf(
                                            AddTaskIconGray.copy(alpha = 0.12f),
                                            AddTaskIconGray.copy(alpha = 0.06f)
                                        )
                                    )
                                },
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = if (reminderEnabled) Icons.Default.NotificationsActive else Icons.Default.NotificationsOff,
                            contentDescription = null,
                            tint = if (reminderEnabled) AddTaskPrimary else AddTaskIconGray.copy(alpha = 0.8f),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column {
                        Text(
                            text = "提醒设置",
                            style = TextStyle(
                                fontSize = 15.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = AddTaskTextPrimary.copy(alpha = 0.9f),
                                letterSpacing = 0.3.sp
                            )
                        )

                        Spacer(modifier = Modifier.height(6.dp))

                        Text(
                            text = if (reminderEnabled && selectedReminderDate != null && selectedReminderTime != null) {
                                "${selectedReminderDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} ${selectedReminderTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
                            } else "点击设置提醒时间",
                            style = TextStyle(
                                fontSize = 13.sp,
                                color = if (reminderEnabled) AddTaskPrimary else AddTaskTextSecondary.copy(alpha = 0.7f),
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }
                }

                // 开关
                Switch(
                    checked = reminderEnabled,
                    onCheckedChange = onReminderEnabledChange,
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = AddTaskPrimary,
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = AddTaskIconGray.copy(alpha = 0.3f)
                    )
                )
            }
            
            // 如果启用了提醒，显示可点击的提醒时间设置区域
            if (reminderEnabled) {
                Spacer(modifier = Modifier.height(16.dp))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(AddTaskTextSecondary.copy(alpha = 0.1f))
                )
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(8.dp))
                        .clickable { onReminderClick() }
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.AccessTime,
                            contentDescription = null,
                            tint = AddTaskPrimary,
                            modifier = Modifier.size(20.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "设置具体提醒时间",
                            style = TextStyle(
                                fontSize = 14.sp,
                                color = AddTaskPrimary,
                                fontWeight = FontWeight.Medium
                            )
                        )
                    }
                    
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = null,
                        tint = AddTaskIconGray.copy(alpha = 0.6f),
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun ReminderTimePickerDialog(
    initialDate: LocalDate,
    initialTime: LocalTime,
    onDismiss: () -> Unit,
    onTimeSelected: (LocalDate, LocalTime) -> Unit
) {
    var selectedDate by remember { mutableStateOf(initialDate) }
    var selectedTime by remember { mutableStateOf(initialTime) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // 标题
                Text(
                    text = "设置提醒时间",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = AddTaskTextPrimary
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 日期选择
                Text(
                    text = "提醒日期",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = AddTaskTextSecondary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = AddTaskCardBackground,
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        // 今天
                        val today = LocalDate.now()
                        DateOptionButton(
                            text = "今天",
                            date = today.format(DateTimeFormatter.ofPattern("MM/dd")),
                            isSelected = selectedDate == today,
                            onClick = { selectedDate = today }
                        )
                        
                        // 明天
                        val tomorrow = LocalDate.now().plusDays(1)
                        DateOptionButton(
                            text = "明天",
                            date = tomorrow.format(DateTimeFormatter.ofPattern("MM/dd")),
                            isSelected = selectedDate == tomorrow,
                            onClick = { selectedDate = tomorrow }
                        )
                        
                        // 后天
                        val dayAfterTomorrow = LocalDate.now().plusDays(2)
                        DateOptionButton(
                            text = "后天",
                            date = dayAfterTomorrow.format(DateTimeFormatter.ofPattern("MM/dd")),
                            isSelected = selectedDate == dayAfterTomorrow,
                            onClick = { selectedDate = dayAfterTomorrow }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 时间选择
                Text(
                    text = "提醒时间",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = AddTaskTextSecondary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 使用简化的时间选择器
                TimePickerWheel(
                    selectedTime = selectedTime,
                    onTimeSelected = { selectedTime = it },
                    modifier = Modifier.height(120.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = AddTaskTextSecondary
                        )
                    ) {
                        Text("取消")
                    }
                    
                    // 确认按钮
                    Button(
                        onClick = { onTimeSelected(selectedDate, selectedTime) },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AddTaskPrimary
                        )
                    ) {
                        Text("确认", color = Color.White)
                    }
                }
            }
        }
    }
}

@Composable
private fun DateOptionButton(
    text: String,
    date: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .wrapContentSize()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onClick() },
        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.1f) else Color.Transparent,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = text,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                    color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                )
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = date,
                style = TextStyle(
                    fontSize = 12.sp,
                    color = if (isSelected) AddTaskPrimary else AddTaskTextSecondary
                )
            )
        }
    }
}

@Composable
private fun TimePickerWheel(
    selectedTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    modifier: Modifier
) {
    val hours = remember { (0..23).toList() }
    val minutes = remember { (0..59 step 15).toList() } // 15分钟间隔
    
    // 🎯 添加LazyListState用于自动滚动
    val hourListState = rememberLazyListState()
    val minuteListState = rememberLazyListState()
    
    // 🎯 自动滚动到当前时间
    LaunchedEffect(selectedTime) {
        // 计算当前小时在列表中的索引位置
        val hourIndex = hours.indexOf(selectedTime.hour)
        if (hourIndex >= 0) {
            // 滚动到选中的小时，让其在视图中居中
            hourListState.animateScrollToItem(
                index = maxOf(0, hourIndex - 1), // 稍微向上偏移，让选中项更居中
                scrollOffset = 0
            )
        }
        
        // 找到最接近的分钟（因为是15分钟间隔）
        val currentMinute = selectedTime.minute
        val closestMinute = minutes.minByOrNull { kotlin.math.abs(it - currentMinute) } ?: 0
        val minuteIndex = minutes.indexOf(closestMinute)
        if (minuteIndex >= 0) {
            // 滚动到选中的分钟，让其在视图中居中
            minuteListState.animateScrollToItem(
                index = maxOf(0, minuteIndex - 1), // 稍微向上偏移，让选中项更居中
                scrollOffset = 0
            )
        }
    }

    Surface(
        modifier = modifier,
        color = AddTaskCardBackground,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier.height(120.dp)
        ) {
            // 小时选择
            LazyColumn(
                state = hourListState, // 🎯 使用状态控制滚动
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                contentPadding = PaddingValues(vertical = 40.dp)
            ) {
                items(hours) { hour ->
                    val isSelected = hour == selectedTime.hour
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onTimeSelected(selectedTime.withHour(hour))
                            }
                            .padding(horizontal = 8.dp),
                        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.12f) else Color.Transparent,
                        shape = RoundedCornerShape(6.dp)
                    ) {
                        Text(
                            text = String.format("%02d", hour),
                            modifier = Modifier.padding(vertical = 8.dp),
                            textAlign = TextAlign.Center,
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                                )
                            )
                        }
                    }
                }

            // 分隔符
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .fillMaxHeight()
                    .background(AddTaskBorder.copy(alpha = 0.3f))
            )

            // 分钟选择
            LazyColumn(
                state = minuteListState, // 🎯 使用状态控制滚动
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
                contentPadding = PaddingValues(vertical = 40.dp)
            ) {
                items(minutes) { minute ->
                    // 🎯 改进分钟选择逻辑，找到最接近的15分钟间隔
                    val currentMinute = selectedTime.minute
                    val closestMinute = minutes.minByOrNull { kotlin.math.abs(it - currentMinute) } ?: 0
                    val isSelected = minute == closestMinute
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onTimeSelected(selectedTime.withMinute(minute))
                            }
                            .padding(horizontal = 8.dp),
                        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.12f) else Color.Transparent,
                        shape = RoundedCornerShape(6.dp)
                    ) {
                        Text(
                            text = String.format("%02d", minute),
                            modifier = Modifier.padding(vertical = 8.dp),
                            textAlign = TextAlign.Center,
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                color = if (isSelected) AddTaskPrimary else AddTaskTextPrimary
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 循环设置选项卡片
 */
@Composable
private fun RecurrenceOptionCard(
    isRecurring: Boolean,
    selectedRecurrenceType: RecurrenceType,
    onRecurrenceClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .clickable { onRecurrenceClick() },
        color = AddTaskCardBackground,
        border = BorderStroke(1.dp, AddTaskBorder),
        shadowElevation = 0.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 左侧：标题和当前选择
                Column {
                    Text(
                        text = "循环",
                        style = TextStyle(
                            fontSize = 15.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = AddTaskTextPrimary.copy(alpha = 0.9f),
                            letterSpacing = 0.3.sp
                        )
                    )

                    Spacer(modifier = Modifier.height(6.dp))

                    Text(
                        text = if (isRecurring) selectedRecurrenceType.displayName else "不循环",
                        style = TextStyle(
                            fontSize = 13.sp,
                            color = if (isRecurring) AddTaskPrimary else AddTaskTextSecondary,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }

                // 右侧：循环图标
                Icon(
                    imageVector = Icons.Default.Repeat,
                    contentDescription = "循环设置",
                    tint = if (isRecurring) AddTaskPrimary else AddTaskIconGray,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 循环设置对话框
 */
@Composable
private fun RecurrenceSettingsDialog(
    isRecurring: Boolean,
    recurrenceSettings: RecurrenceSettings,
    selectedRecurrenceType: RecurrenceType,
    onDismiss: () -> Unit,
    onConfirm: (Boolean, RecurrenceSettings, RecurrenceType) -> Unit
) {
    var tempIsRecurring by remember { mutableStateOf(isRecurring) }
    var tempRecurrenceType by remember { mutableStateOf(selectedRecurrenceType) }
    var tempSettings by remember { mutableStateOf(recurrenceSettings) }

    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "循环设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = AddTaskTextPrimary
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 是否启用循环
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "启用循环",
                        fontSize = 16.sp,
                        color = AddTaskTextPrimary
                    )
                    Switch(
                        checked = tempIsRecurring,
                        onCheckedChange = { tempIsRecurring = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = AddTaskPrimary,
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = AddTaskIconGray
                        )
                    )
                }

                // 循环类型选择
                if (tempIsRecurring) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "循环类型",
                        fontSize = 14.sp,
                        color = AddTaskTextSecondary,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // 循环类型选项
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        RecurrenceType.values().filter { it != RecurrenceType.NONE }.forEach { type ->
                            RecurrenceTypeItem(
                                type = type,
                                isSelected = tempRecurrenceType == type,
                                onClick = {
                                    tempRecurrenceType = type
                                    tempSettings = when (type) {
                                        RecurrenceType.DAILY -> RecurrenceSettings.daily()
                                        RecurrenceType.WEEKLY -> RecurrenceSettings.weekly()
                                        RecurrenceType.MONTHLY -> RecurrenceSettings.monthly()
                                        RecurrenceType.YEARLY -> RecurrenceSettings.yearly()
                                        else -> RecurrenceSettings.none()
                                    }
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = AddTaskTextSecondary
                        ),
                        border = BorderStroke(1.dp, AddTaskBorder)
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            val finalType = if (tempIsRecurring) tempRecurrenceType else RecurrenceType.NONE
                            val finalSettings = if (tempIsRecurring) tempSettings else RecurrenceSettings.none()
                            onConfirm(tempIsRecurring, finalSettings, finalType)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AddTaskPrimary
                        )
                    ) {
                        Text("确定", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 循环类型选项组件
 */
@Composable
private fun RecurrenceTypeItem(
    type: RecurrenceType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick),
        color = if (isSelected) AddTaskPrimary.copy(alpha = 0.1f) else Color.Transparent,
        border = BorderStroke(
            1.dp,
            if (isSelected) AddTaskPrimary else AddTaskBorder
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = AddTaskPrimary,
                    unselectedColor = AddTaskIconGray
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column {
                Text(
                    text = type.displayName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = AddTaskTextPrimary
                )
                Text(
                    text = type.description,
                    fontSize = 12.sp,
                    color = AddTaskTextSecondary
                )
            }
        }
    }
}

