package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 时间洞察小组件 - 显示当日时间分布和统计
 */
class TimeInsightWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.widget_time_insight)
        
        // 设置当前日期
        val today = LocalDate.now()
        val dateFormatter = DateTimeFormatter.ofPattern("M月d日")
        views.setTextViewText(R.id.widget_date, today.format(dateFormatter))
        
        // 设置静态数据
        views.setTextViewText(R.id.widget_focus_time, "8h 55m")
        views.setTextViewText(R.id.widget_session_count, "7次")
        views.setTextViewText(R.id.widget_avg_session, "50min")
        views.setTextViewText(R.id.widget_task_stats, "4/6")
        views.setTextViewText(R.id.widget_completion_rate, "67%")
        views.setProgressBar(R.id.widget_progress_bar, 100, 67, false)
        
        // 设置时间分布
        views.setProgressBar(R.id.widget_morning_bar, 100, 51, false)
        views.setProgressBar(R.id.widget_afternoon_bar, 100, 77, false)
        views.setProgressBar(R.id.widget_evening_bar, 100, 100, false)
        
        // 设置点击事件
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "analytics")
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
    }
}
