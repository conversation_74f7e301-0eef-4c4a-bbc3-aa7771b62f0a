package com.timeflow.app.ui.task.components.common

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.dp

/**
 * 任务卡片的主题配置
 * 
 * 定义了任务卡片的各种样式选项，包括颜色、形状、优先级指示器风格等。
 * 可以通过切换主题快速更改整个应用的任务卡片外观。
 * 
 * @param backgroundColor 卡片背景色
 * @param completedBackgroundColor 已完成任务的背景色
 * @param shape 卡片形状
 * @param priorityIndicatorVariant 优先级指示器样式
 * @param elevationActive 活动状态的卡片阴影高度
 * @param elevationCompleted 已完成任务的卡片阴影高度
 * @param cornerRadius 卡片圆角半径（如果形状是RoundedCornerShape）
 */
data class TaskCardTheme(
    val backgroundColor: Color,
    val completedBackgroundColor: Color,
    val shape: Shape,
    val priorityIndicatorVariant: PriorityVariant,
    val elevationActive: Int,
    val elevationCompleted: Int,
    val cornerRadius: Int
)

/**
 * 任务卡片主题预设
 */
object TaskCardThemes {
    /**
     * 默认主题 - 简洁现代风格
     */
    @Composable
    fun Default(): TaskCardTheme {
        return TaskCardTheme(
            backgroundColor = MaterialTheme.colorScheme.surface,
            completedBackgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f),
            shape = MaterialTheme.shapes.medium,
            priorityIndicatorVariant = PriorityVariant.DOT,
            elevationActive = 2,
            elevationCompleted = 1,
            cornerRadius = 8
        )
    }
    
    /**
     * 浅色主题 - 米色纸张风格
     */
    @Composable
    fun Light(): TaskCardTheme {
        return TaskCardTheme(
            backgroundColor = Color(0xFFF8F8F5), // 浅米色
            completedBackgroundColor = Color(0xFFF2F2F2), // 浅灰色
            shape = RoundedCornerShape(16.dp),
            priorityIndicatorVariant = PriorityVariant.TAG,
            elevationActive = 3,
            elevationCompleted = 1,
            cornerRadius = 16
        )
    }
    
    /**
     * 现代卡片主题 - 内容优先
     */
    @Composable
    fun Modern(): TaskCardTheme {
        return TaskCardTheme(
            backgroundColor = MaterialTheme.colorScheme.surface,
            completedBackgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
            shape = RoundedCornerShape(12.dp),
            priorityIndicatorVariant = PriorityVariant.ALERT,
            elevationActive = 0,
            elevationCompleted = 0,
            cornerRadius = 12
        )
    }
    
    /**
     * 商务主题 - 更加正式的外观
     */
    @Composable
    fun Business(): TaskCardTheme {
        return TaskCardTheme(
            backgroundColor = Color.White,
            completedBackgroundColor = Color(0xFFEEEEEE),
            shape = RoundedCornerShape(4.dp),
            priorityIndicatorVariant = PriorityVariant.OUTLINED,
            elevationActive = 1,
            elevationCompleted = 0,
            cornerRadius = 4
        )
    }
    
    /**
     * 富彩主题 - 强调优先级的彩色卡片
     */
    @Composable
    fun Colorful(): TaskCardTheme {
        return TaskCardTheme(
            backgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
            completedBackgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
            shape = RoundedCornerShape(20.dp),
            priorityIndicatorVariant = PriorityVariant.ICON,
            elevationActive = 4,
            elevationCompleted = 1,
            cornerRadius = 20
        )
    }
} 