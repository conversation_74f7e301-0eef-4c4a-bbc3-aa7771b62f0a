package com.timeflow.app.ui.task.components.common.util

import android.util.Log
import kotlinx.coroutines.delay
import kotlin.random.Random

/**
 * 重试工具类
 * 提供各种重试策略，用于网络请求或数据库操作的重试
 */
object RetryUtil {
    private const val TAG = "RetryUtil"
    
    /**
     * 使用指数退避策略进行重试
     * 
     * @param maxAttempts 最大尝试次数
     * @param initialDelayMs 初始延迟(毫秒)
     * @param maxDelayMs 最大延迟(毫秒)
     * @param factor 延迟增长因子
     * @param block 要执行的操作
     * @return 操作结果
     * @throws Exception 如果所有尝试都失败，抛出最后一次操作的异常
     */
    suspend fun <T> withRetry(
        maxAttempts: Int = 3,
        initialDelayMs: Long = 100,
        maxDelayMs: Long = 3000,
        factor: Double = 2.0,
        block: suspend () -> T
    ): T {
        var currentDelay = initialDelayMs
        var lastException: Exception? = null
        
        for (attempt in 1..maxAttempts) {
            try {
                // 尝试执行操作
                return block().also {
                    if (attempt > 1) {
                        Log.d(TAG, "操作在第 $attempt 次尝试成功")
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "操作失败，尝试 $attempt/$maxAttempts: ${e.message}")
                lastException = e
                
                // 如果已经到达最大尝试次数，抛出异常
                if (attempt == maxAttempts) {
                    Log.e(TAG, "达到最大尝试次数 $maxAttempts，放弃重试")
                    throw e
                }
                
                // 计算下一次延迟，加入随机因子以防止同时重试
                val randomFactor = 1.0 + Random.nextDouble(-0.1, 0.1)
                currentDelay = (currentDelay * factor * randomFactor)
                    .toLong()
                    .coerceAtMost(maxDelayMs)
                
                Log.d(TAG, "等待 $currentDelay ms 后重试...")
                delay(currentDelay)
            }
        }
        
        // 这行代码理论上永远不会执行，因为最后一次尝试失败会抛出异常
        throw lastException ?: IllegalStateException("所有重试尝试均失败，但没有捕获到异常")
    }
    
    /**
     * 使用固定间隔策略进行重试
     * 
     * @param maxAttempts 最大尝试次数
     * @param delayMs 重试间隔(毫秒)
     * @param block 要执行的操作
     * @return 操作结果
     * @throws Exception 如果所有尝试都失败，抛出最后一次操作的异常
     */
    suspend fun <T> withFixedRetry(
        maxAttempts: Int = 3,
        delayMs: Long = 1000,
        block: suspend () -> T
    ): T {
        var lastException: Exception? = null
        
        for (attempt in 1..maxAttempts) {
            try {
                // 尝试执行操作
                return block().also {
                    if (attempt > 1) {
                        Log.d(TAG, "操作在第 $attempt 次尝试成功")
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "操作失败，尝试 $attempt/$maxAttempts: ${e.message}")
                lastException = e
                
                // 如果已经到达最大尝试次数，抛出异常
                if (attempt == maxAttempts) {
                    Log.e(TAG, "达到最大尝试次数 $maxAttempts，放弃重试")
                    throw e
                }
                
                Log.d(TAG, "等待 $delayMs ms 后重试...")
                delay(delayMs)
            }
        }
        
        // 这行代码理论上永远不会执行，因为最后一次尝试失败会抛出异常
        throw lastException ?: IllegalStateException("所有重试尝试均失败，但没有捕获到异常")
    }
    
    /**
     * 重试直到成功或达到最大尝试次数
     * 
     * @param maxAttempts 最大尝试次数，-1表示无限重试
     * @param delayMs 重试间隔(毫秒)
     * @param shouldRetry 判断是否应该根据异常类型重试的函数，默认总是重试
     * @param block 要执行的操作
     * @return 操作结果
     * @throws Exception 如果所有尝试都失败，抛出最后一次操作的异常
     */
    suspend fun <T> retryUntilSuccess(
        maxAttempts: Int = -1,
        delayMs: Long = 1000,
        shouldRetry: (Exception) -> Boolean = { true },
        block: suspend () -> T
    ): T {
        var attempt = 1
        var lastException: Exception? = null
        
        while (maxAttempts == -1 || attempt <= maxAttempts) {
            try {
                // 尝试执行操作
                return block().also {
                    if (attempt > 1) {
                        Log.d(TAG, "操作在第 $attempt 次尝试成功")
                    }
                }
            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "操作失败，尝试 $attempt: ${e.message}")
                
                // 检查是否应该重试
                if (!shouldRetry(e)) {
                    Log.e(TAG, "异常类型不符合重试条件，放弃重试: ${e.javaClass.simpleName}")
                    throw e
                }
                
                // 如果已经到达最大尝试次数，抛出异常
                if (maxAttempts != -1 && attempt == maxAttempts) {
                    Log.e(TAG, "达到最大尝试次数 $maxAttempts，放弃重试")
                    throw e
                }
                
                Log.d(TAG, "等待 $delayMs ms 后重试...")
                delay(delayMs)
                attempt++
            }
        }
        
        // 这行代码理论上永远不会执行，因为无限重试或最后一次尝试失败会抛出异常
        throw lastException ?: IllegalStateException("所有重试尝试均失败，但没有捕获到异常")
    }
} 