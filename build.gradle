// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        compose_version = '1.6.1'
        compose_compiler_version = '1.5.8'
        kotlin_version = '1.9.22'
        agp_version = '8.9.0'
        
        // 应用版本信息
        app_version_code = 8 // 2024-05-26: Compose库升级更新
        app_version_name = "0.5.3" // Compose库升级与现代API支持
        
        // 版本变更记录
        version_history = [
            // Format: [version_name, version_code, description, date]
            ["0.5.3", 8, "Compose库升级与现代API支持", "2024-05-26"],
            ["0.5.2", 7, "UI组件一致性优化与Bug修复", "2024-05-25"],
            ["0.5.1", 6, "移除废弃UI组件", "2024-05-24"],
            ["0.5.0", 5, "任务模型重构和看板功能优化", "2024-05-23"]
        ]
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.48'
        classpath 'com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:1.9.22-1.0.16'
    }
}

plugins {
    id 'com.android.application' version "$agp_version" apply false
    id 'com.android.library' version "$agp_version" apply false
    id 'org.jetbrains.kotlin.android' version "$kotlin_version" apply false
    id 'org.jetbrains.kotlin.plugin.serialization' version "$kotlin_version" apply false
    id 'com.google.dagger.hilt.android' version '2.50' apply false
    id 'com.google.devtools.ksp' version '1.9.22-1.0.16' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
} 