package com.timeflow.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.*

/**
 * 图片处理工具类
 * 提供图片复制、压缩、旋转等功能
 */
object ImageUtils {
    
    private const val TAG = "ImageUtils"
    private const val MAX_WIDTH = 1024
    private const val MAX_HEIGHT = 1024
    private const val QUALITY = 85
    
    /**
     * 复制URI图片到应用内部存储并压缩
     * @param context 上下文
     * @param sourceUri 源图片URI
     * @param targetFileName 目标文件名（不含后缀）
     * @return 内部存储文件的URI，失败返回null
     */
    suspend fun copyAndCompressImage(
        context: Context,
        sourceUri: Uri,
        targetFileName: String
    ): Uri? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始复制并压缩图片: $sourceUri -> $targetFileName")
            
            // 创建内部存储目录
            val internalDir = File(context.filesDir, "user_images")
            if (!internalDir.exists()) {
                internalDir.mkdirs()
                Log.d(TAG, "创建内部存储目录: ${internalDir.absolutePath}")
            }
            
            // 目标文件
            val targetFile = File(internalDir, "$targetFileName.jpg")
            
            // 读取源图片
            val inputStream: InputStream? = context.contentResolver.openInputStream(sourceUri)
            inputStream?.use { stream ->
                // 解码图片
                val bitmap = BitmapFactory.decodeStream(stream)
                if (bitmap == null) {
                    Log.e(TAG, "无法解码图片: $sourceUri")
                    return@withContext null
                }
                
                Log.d(TAG, "原始图片尺寸: ${bitmap.width} x ${bitmap.height}")
                
                // 处理图片旋转
                val rotatedBitmap = correctImageOrientation(context, sourceUri, bitmap)
                
                // 压缩图片
                val compressedBitmap = compressBitmap(rotatedBitmap, MAX_WIDTH, MAX_HEIGHT)
                
                Log.d(TAG, "压缩后图片尺寸: ${compressedBitmap.width} x ${compressedBitmap.height}")
                
                // 保存到内部存储
                FileOutputStream(targetFile).use { fos ->
                    compressedBitmap.compress(Bitmap.CompressFormat.JPEG, QUALITY, fos)
                    fos.flush()
                }
                
                // 清理bitmap
                if (bitmap != rotatedBitmap) {
                    bitmap.recycle()
                }
                if (rotatedBitmap != compressedBitmap) {
                    rotatedBitmap.recycle()
                }
                compressedBitmap.recycle()
                
                Log.d(TAG, "图片保存成功: ${targetFile.absolutePath}")
                Log.d(TAG, "文件大小: ${targetFile.length()} bytes")
                
                return@withContext Uri.fromFile(targetFile)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "复制并压缩图片失败", e)
        }
        
        return@withContext null
    }
    
    /**
     * 压缩Bitmap到指定尺寸
     */
    private fun compressBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // 计算缩放比例
        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight, 1.0f) // 不放大图片
        
        if (scale >= 1.0f) {
            return bitmap // 无需压缩
        }
        
        // 创建缩放矩阵
        val matrix = Matrix()
        matrix.postScale(scale, scale)
        
        // 创建压缩后的bitmap
        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true)
    }
    
    /**
     * 修正图片方向
     */
    private suspend fun correctImageOrientation(
        context: Context,
        uri: Uri,
        bitmap: Bitmap
    ): Bitmap = withContext(Dispatchers.IO) {
        try {
            // 读取EXIF信息
            val inputStream = context.contentResolver.openInputStream(uri)
            inputStream?.use { stream ->
                val exif = ExifInterface(stream)
                val orientation = exif.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL
                )
                
                val matrix = Matrix()
                when (orientation) {
                    ExifInterface.ORIENTATION_ROTATE_90 -> matrix.postRotate(90f)
                    ExifInterface.ORIENTATION_ROTATE_180 -> matrix.postRotate(180f)
                    ExifInterface.ORIENTATION_ROTATE_270 -> matrix.postRotate(270f)
                    ExifInterface.ORIENTATION_FLIP_HORIZONTAL -> matrix.postScale(-1f, 1f)
                    ExifInterface.ORIENTATION_FLIP_VERTICAL -> matrix.postScale(1f, -1f)
                    else -> return@withContext bitmap // 无需旋转
                }
                
                Log.d(TAG, "检测到图片方向: $orientation，进行旋转修正")
                return@withContext Bitmap.createBitmap(
                    bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取图片方向信息失败", e)
        }
        
        return@withContext bitmap
    }
    
    /**
     * 删除内部存储的图片文件
     */
    suspend fun deleteImage(context: Context, fileName: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val internalDir = File(context.filesDir, "user_images")
            val file = File(internalDir, "$fileName.jpg")
            if (file.exists()) {
                val deleted = file.delete()
                Log.d(TAG, "删除图片文件: ${file.absolutePath}, 结果: $deleted")
                return@withContext deleted
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除图片文件失败", e)
        }
        return@withContext false
    }
    
    /**
     * 清理所有用户图片
     */
    suspend fun clearAllUserImages(context: Context): Boolean = withContext(Dispatchers.IO) {
        try {
            val internalDir = File(context.filesDir, "user_images")
            if (internalDir.exists()) {
                val files = internalDir.listFiles()
                files?.forEach { file ->
                    file.delete()
                }
                val deleted = internalDir.delete()
                Log.d(TAG, "清理所有用户图片，结果: $deleted")
                return@withContext deleted
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理用户图片失败", e)
        }
        return@withContext false
    }
    
    /**
     * 获取图片文件大小（字节）
     */
    fun getImageFileSize(context: Context, fileName: String): Long {
        return try {
            val internalDir = File(context.filesDir, "user_images")
            val file = File(internalDir, "$fileName.jpg")
            if (file.exists()) file.length() else 0L
        } catch (e: Exception) {
            Log.e(TAG, "获取图片文件大小失败", e)
            0L
        }
    }
    
    /**
     * 检查图片文件是否存在
     */
    fun imageExists(context: Context, fileName: String): Boolean {
        return try {
            val internalDir = File(context.filesDir, "user_images")
            val file = File(internalDir, "$fileName.jpg")
            file.exists()
        } catch (e: Exception) {
            Log.e(TAG, "检查图片文件存在性失败", e)
            false
        }
    }
    
    /**
     * 生成唯一的文件名
     */
    fun generateUniqueFileName(prefix: String = "img"): String {
        return "${prefix}_${System.currentTimeMillis()}_${UUID.randomUUID().toString().substring(0, 8)}"
    }
} 