package com.timeflow.app.ui.viewmodel

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timeflow.app.data.ai.model.AiConfig
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

// 创建DataStore实例
val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "ai_config_preferences")

// 定义DataStore的key
private val AI_CONFIGS_KEY = stringPreferencesKey("ai_configs")
private val SELECTED_CONFIG_ID_KEY = stringPreferencesKey("selected_config_id")

/**
 * AI配置ViewModel，用于管理AI配置的增删改查
 */
@HiltViewModel
class AiConfigViewModel @Inject constructor() : ViewModel() {
    
    // 存储所有AI配置的列表
    private val _configurations = MutableStateFlow<List<AiConfig>>(emptyList())
    val configurations: StateFlow<List<AiConfig>> = _configurations.asStateFlow()
    
    // 当前选中的配置ID
    private val _selectedConfigId = MutableStateFlow<String?>(null)
    val selectedConfigId: StateFlow<String?> = _selectedConfigId.asStateFlow()
    
    // 加载所有配置
    fun loadConfigurations(context: Context) {
        viewModelScope.launch {
            val storedConfigs = context.dataStore.data.firstOrNull()?.get(AI_CONFIGS_KEY)
            if (!storedConfigs.isNullOrEmpty()) {
                val type = object : TypeToken<List<AiConfig>>() {}.type
                val configs = Gson().fromJson<List<AiConfig>>(storedConfigs, type)
                _configurations.value = configs
            } else {
                // 如果没有存储的配置，创建默认配置
                val defaultConfig = AiConfig(
                    id = UUID.randomUUID().toString(),
                    name = "DeepSeek默认配置",
                    provider = "DeepSeek",
                    modelName = "deepseek-chat",
                    serverUrl = "https://api.deepseek.com/v1",
                    isDefault = true
                )
                _configurations.value = listOf(defaultConfig)
                saveConfigurations(context)
            }
            
            // 加载选中的配置ID
            val selectedId = context.dataStore.data.firstOrNull()?.get(SELECTED_CONFIG_ID_KEY)
            _selectedConfigId.value = selectedId ?: _configurations.value.firstOrNull()?.id
        }
    }
    
    // 保存所有配置
    private fun saveConfigurations(context: Context) {
        viewModelScope.launch {
            val configsJson = Gson().toJson(_configurations.value)
            context.dataStore.edit { preferences ->
                preferences[AI_CONFIGS_KEY] = configsJson
            }
        }
    }
    
    // 添加新配置
    fun addConfiguration(context: Context, config: AiConfig) {
        viewModelScope.launch {
            val newConfig = config.copy(id = UUID.randomUUID().toString())
            val currentConfigs = _configurations.value.toMutableList()
            currentConfigs.add(newConfig)
            _configurations.value = currentConfigs
            saveConfigurations(context)
            
            // 如果是第一个配置，自动选中
            if (currentConfigs.size == 1 || _selectedConfigId.value == null) {
                selectConfig(context, newConfig.id)
            }
        }
    }
    
    // 更新配置
    fun updateConfiguration(context: Context, config: AiConfig) {
        viewModelScope.launch {
            val currentConfigs = _configurations.value.toMutableList()
            val index = currentConfigs.indexOfFirst { it.id == config.id }
            if (index != -1) {
                currentConfigs[index] = config
                _configurations.value = currentConfigs
                saveConfigurations(context)
            }
        }
    }
    
    // 删除配置
    fun deleteConfiguration(context: Context, configId: String) {
        viewModelScope.launch {
            val currentConfigs = _configurations.value.toMutableList()
            val removedConfig = currentConfigs.find { it.id == configId }
            
            if (removedConfig != null) {
                currentConfigs.remove(removedConfig)
                _configurations.value = currentConfigs
                
                // 如果删除的是当前选中的配置，重新选择第一个配置
                if (_selectedConfigId.value == configId) {
                    _selectedConfigId.value = currentConfigs.firstOrNull()?.id
                    context.dataStore.edit { preferences ->
                        preferences[SELECTED_CONFIG_ID_KEY] = _selectedConfigId.value ?: ""
                    }
                }
                
                saveConfigurations(context)
            }
        }
    }
    
    // 选择配置
    fun selectConfig(context: Context, configId: String) {
        viewModelScope.launch {
            if (_configurations.value.any { it.id == configId }) {
                _selectedConfigId.value = configId
                context.dataStore.edit { preferences ->
                    preferences[SELECTED_CONFIG_ID_KEY] = configId
                }
            }
        }
    }
    
    // 获取当前选中的配置
    fun getSelectedConfig(): AiConfig? {
        val selectedId = _selectedConfigId.value
        return _configurations.value.find { it.id == selectedId }
    }
} 