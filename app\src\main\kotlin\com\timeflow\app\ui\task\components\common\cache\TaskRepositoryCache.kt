package com.timeflow.app.ui.task.components.common.cache

import android.util.Log
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.repository.TaskRepository
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.data.repository.TaskStatistics
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 为TaskRepository添加缓存层，减少数据库访问频率
 */
class TaskRepositoryCache(private val taskRepository: TaskRepository) : TaskRepository {
    private val TAG = "TaskRepositoryCache"
    
    // 是否启用缓存
    private var cacheEnabled = true
    
    // 单个任务缓存，5分钟过期
    private val taskCache = ExpiringCache<String, ModelTask>(
        maxSize = 100,
        expirationTimeMs = TimeUnit.MINUTES.toMillis(5)
    )
    
    // 子任务列表缓存，2分钟过期
    private val subTaskCache = ExpiringCache<String, List<ModelTask>>(
        maxSize = 50,
        expirationTimeMs = TimeUnit.MINUTES.toMillis(2)
    )
    
    // 变更追踪器
    private val changeTracker = ChangeTracker()
    
    // 缓存统计
    private var taskCacheHits = 0
    private var taskCacheMisses = 0
    private var subTaskCacheHits = 0
    private var subTaskCacheMisses = 0
    
    /**
     * 获取单个任务
     * 
     * @param taskId 任务ID
     * @return 任务实体，不存在则返回null
     */
    override suspend fun getTaskById(taskId: String): ModelTask? {
        if (!cacheEnabled || changeTracker.needsRefresh(
                ChangeTracker.EntityType.TASK,
                taskId,
                ChangeTracker.DEFAULT_TASK_MAX_AGE
            )
        ) {
            Log.d(TAG, "Cache miss (refresh needed) for task $taskId")
            taskCacheMisses++
            
            // 从数据库获取任务
            val task = taskRepository.getTaskById(taskId)
            
            // 更新缓存
            if (task != null) {
                taskCache.put(taskId, task)
                changeTracker.markFetched(ChangeTracker.EntityType.TASK, taskId)
                Log.d(TAG, "Updated cache for task $taskId: $task")
            } else {
                Log.d(TAG, "Task $taskId not found in database")
            }
            
            return task
        }
        
        // 尝试从缓存获取
        val cachedTask = taskCache.get(taskId)
        if (cachedTask != null) {
            taskCacheHits++
            Log.d(TAG, "Cache hit for task $taskId")
            return cachedTask
        }
        
        // 缓存未命中，从数据库获取
        Log.d(TAG, "Cache miss for task $taskId")
        taskCacheMisses++
        val task = taskRepository.getTaskById(taskId)
        
        // 更新缓存
        if (task != null) {
            taskCache.put(taskId, task)
            changeTracker.markFetched(ChangeTracker.EntityType.TASK, taskId)
            Log.d(TAG, "Updated cache for task $taskId")
        }
        
        return task
    }
    
    /**
     * 获取子任务列表
     * 
     * @param parentTaskId 父任务ID
     * @return 子任务列表
     */
    override suspend fun getSubTasks(parentTaskId: String): List<ModelTask> {
        if (!cacheEnabled || changeTracker.needsRefresh(
                ChangeTracker.EntityType.SUBTASKS,
                parentTaskId,
                ChangeTracker.DEFAULT_SUBTASKS_MAX_AGE
            )
        ) {
            Log.d(TAG, "Cache miss (refresh needed) for subtasks of $parentTaskId")
            subTaskCacheMisses++
            
            // 从数据库获取子任务
            val subTasks = taskRepository.getSubTasks(parentTaskId)
            
            // 更新缓存
            subTaskCache.put(parentTaskId, subTasks)
            changeTracker.markFetched(ChangeTracker.EntityType.SUBTASKS, parentTaskId)
            Log.d(TAG, "Updated cache for subtasks of $parentTaskId: ${subTasks.size} items")
            
            return subTasks
        }
        
        // 尝试从缓存获取
        val cachedSubTasks = subTaskCache.get(parentTaskId)
        if (cachedSubTasks != null) {
            subTaskCacheHits++
            Log.d(TAG, "Cache hit for subtasks of $parentTaskId: ${cachedSubTasks.size} items")
            return cachedSubTasks
        }
        
        // 缓存未命中，从数据库获取
        Log.d(TAG, "Cache miss for subtasks of $parentTaskId")
        subTaskCacheMisses++
        val subTasks = taskRepository.getSubTasks(parentTaskId)
        
        // 更新缓存
        subTaskCache.put(parentTaskId, subTasks)
        changeTracker.markFetched(ChangeTracker.EntityType.SUBTASKS, parentTaskId)
        Log.d(TAG, "Updated cache for subtasks of $parentTaskId: ${subTasks.size} items")
        
        return subTasks
    }
    
    /**
     * 更新任务
     * 
     * @param task 要更新的任务
     */
    override suspend fun updateTask(task: ModelTask) {
        // 更新数据库
        taskRepository.updateTask(task)
        
        // 更新缓存
        taskCache.put(task.id, task)
        
        // 标记任务已变更
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, task.id)
        
        // 如果任务有父任务，也标记父任务的子任务列表已变更
        task.parentTaskId?.let { parentId ->
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
        }
        
        Log.d(TAG, "Updated task ${task.id} in database and cache")
    }
    
    /**
     * 添加任务
     * 
     * @param task 要添加的任务
     */
    override suspend fun insertTask(task: ModelTask) {
        // 添加到数据库
        taskRepository.insertTask(task)
        
        // 更新缓存
        taskCache.put(task.id, task)
        
        // 标记相关数据已变更
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, task.id)
        
        // 如果任务有父任务，标记父任务的子任务列表已变更
        task.parentTaskId?.let { parentId ->
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
            // 同时使子任务缓存失效
            subTaskCache.invalidate(parentId)
        }
        
        Log.d(TAG, "Added task ${task.id} to database and cache via insertTask")
    }
    
    /**
     * 保存任务 - 优先更新，不存在则插入
     * 
     * @param task 要保存的任务
     */
    override suspend fun saveTask(task: ModelTask) {
         // First, try to update cache
        taskCache.put(task.id, task)
        // Mark changed
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, task.id)
        task.parentTaskId?.let { parentId ->
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
            // Invalidate subtask cache on save as well
            subTaskCache.invalidate(parentId) 
        }
        // Then save to underlying repository
        taskRepository.saveTask(task)
        Log.d(TAG, "Saved task ${task.id} to database and cache via saveTask")
    }
    
    /**
     * 删除任务
     * 
     * @param task 任务对象
     */
    override suspend fun deleteTask(task: ModelTask) {
        deleteTaskInternal(task.id, task.parentTaskId)
    }

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     */
    override suspend fun deleteTask(taskId: String) {
         // We need the parentId to invalidate cache, so try to get it first
        val task = getTaskById(taskId) // Use cached version if possible
        val parentId = task?.parentTaskId
        deleteTaskInternal(taskId, parentId)
    }

    // Internal delete logic to avoid code duplication
    private suspend fun deleteTaskInternal(taskId: String, parentTaskId: String?) {
        // 从数据库删除
        taskRepository.deleteTask(taskId) // Use the ID version of delete
        
        // 从缓存删除
        taskCache.invalidate(taskId)
        
        // 标记相关数据已变更
        parentTaskId?.let {
            // 标记父任务的子任务列表已变更
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, it)
            // 使子任务缓存失效
            subTaskCache.invalidate(it)
        }
        
        Log.d(TAG, "Deleted task $taskId from database and cache")
    }

    // --- Implement missing methods required by TaskRepository interface ---
    // Most will just pass through to the underlying repository and potentially invalidate cache

    override suspend fun getAllTasks(): List<ModelTask> {
        // Simple pass-through for now, could add caching later if needed
        Log.d(TAG, "getAllTasks: Passing through to repository")
        return taskRepository.getAllTasks()
    }

    override fun observeAllTasks(): Flow<List<ModelTask>> {
        // Pass-through Flow, cache invalidation handled by write operations
        Log.d(TAG, "observeAllTasks: Passing through to repository")
        return taskRepository.observeAllTasks()
    }

    override fun observeTaskById(taskId: String): Flow<ModelTask?> {
         // Basic flow implementation that checks cache first, then repository
         // Note: This doesn't actively observe repository changes reflected in cache easily
        Log.d(TAG, "observeTaskById: Basic cache check + repository flow for $taskId")
         return flow {
            val cached = getTaskById(taskId) // Use our cached get
            if (cached != null) emit(cached)
            // Then emit from the underlying repository's flow
            taskRepository.observeTaskById(taskId).collect { emit(it) }
        }
    }

    override suspend fun insertTasks(tasks: List<ModelTask>) {
        taskRepository.insertTasks(tasks)
        tasks.forEach { task ->
            taskCache.put(task.id, task)
            changeTracker.markChanged(ChangeTracker.EntityType.TASK, task.id)
            task.parentTaskId?.let { parentId ->
                changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
                subTaskCache.invalidate(parentId)
            }
        }
        Log.d(TAG, "Inserted ${tasks.size} tasks into database and cache")
    }

    override fun getTasksByPriority(): Flow<List<ModelTask>> {
        Log.d(TAG, "getTasksByPriority: Passing through to repository")
        return taskRepository.getTasksByPriority()
    }

    override fun getTasksByTags(tags: List<TaskTag>): Flow<List<ModelTask>> {
        Log.d(TAG, "getTasksByTags: Passing through to repository")
        return taskRepository.getTasksByTags(tags)
    }

    override fun getChildTasks(parentId: String): Flow<List<ModelTask>> {
        // Use cached version for initial check, then repository flow
        Log.d(TAG, "getChildTasks: Basic cache check + repository flow for $parentId")
        return flow {
            val cached = subTaskCache.get(parentId)
            if (cached != null) emit(cached)
            taskRepository.getChildTasks(parentId).collect { emit(it) }
        }
    }
    
    override fun getRootTasks(): Flow<List<ModelTask>> {
        Log.d(TAG, "getRootTasks: Passing through to repository")
        return taskRepository.getRootTasks()
    }

    override suspend fun updateTaskStatus(taskId: String, newStatus: String) {
        taskRepository.updateTaskStatus(taskId, newStatus)
        taskCache.invalidate(taskId) // Invalidate single task cache
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        // Check if parent needs subtask list update (status change might affect lists)
        getTaskById(taskId)?.parentTaskId?.let { parentId ->
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
             // Invalidate subtask list cache? Maybe not needed just for status change.
             // subTaskCache.invalidate(parentId) 
        }
         Log.d(TAG, "Updated task status for $taskId and invalidated cache")
    }

    override suspend fun updateTaskCompletion(taskId: String, isCompleted: Boolean) {
        taskRepository.updateTaskCompletion(taskId, isCompleted)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        getTaskById(taskId)?.parentTaskId?.let { parentId ->
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
             // Invalidate parent's subtask list on completion change
             subTaskCache.invalidate(parentId) 
        }
        Log.d(TAG, "Updated task completion for $taskId and invalidated cache")
    }

    override suspend fun updateTaskPriority(taskId: String, priority: Priority) {
        taskRepository.updateTaskPriority(taskId, priority)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
         getTaskById(taskId)?.parentTaskId?.let { parentId ->
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
             // Priority change could affect sorted lists
             subTaskCache.invalidate(parentId)
        }
        Log.d(TAG, "Updated task priority for $taskId and invalidated cache")
    }

    override suspend fun updateTaskHasSubtasks(taskId: String, hasSubtasks: Boolean) {
        taskRepository.updateTaskHasSubtasks(taskId, hasSubtasks)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        Log.d(TAG, "Updated task hasSubtasks flag for $taskId and invalidated cache")
    }

    override suspend fun updateTaskOrder(taskId: String, orderPosition: Int) {
        taskRepository.updateTaskOrder(taskId, orderPosition)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        getTaskById(taskId)?.parentTaskId?.let { parentId ->
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
             subTaskCache.invalidate(parentId) // Order change invalidates list
        }
         Log.d(TAG, "Updated task order for $taskId and invalidated cache")
    }

    override suspend fun updateTasksOrder(updates: List<Pair<String, Int>>) {
        taskRepository.updateTasksOrder(updates)
        updates.forEach { (taskId, _) ->
            taskCache.invalidate(taskId)
            changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
             getTaskById(taskId)?.parentTaskId?.let { parentId ->
                changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
                subTaskCache.invalidate(parentId)
            }
        }
         Log.d(TAG, "Updated task orders for ${updates.size} tasks and invalidated caches")
    }

    override suspend fun updateTaskParent(taskId: String, parentId: String?, depth: Int) {
        val oldParentId = getTaskById(taskId)?.parentTaskId
        taskRepository.updateTaskParent(taskId, parentId, depth)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        // Invalidate both old and new parent's subtask lists
        oldParentId?.let { 
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, it)
             subTaskCache.invalidate(it) 
        }
        parentId?.let { 
             changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, it)
             subTaskCache.invalidate(it) 
        }
        Log.d(TAG, "Updated task parent for $taskId and invalidated caches")
    }

    override suspend fun updateTaskProgress(taskId: String, progress: Float) {
        taskRepository.updateTaskProgress(taskId, progress)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        Log.d(TAG, "Updated task progress for $taskId and invalidated cache")
    }

    override fun getAllTags(): Flow<List<TaskTag>> {
        Log.d(TAG, "getAllTags: Passing through to repository")
        // Caching tags might be complex due to source, pass through for now
        return taskRepository.getAllTags()
    }

    override fun getTasksByGroup(groupId: String): Flow<List<ModelTask>> {
         Log.d(TAG, "getTasksByGroup: Passing through to repository")
        return taskRepository.getTasksByGroup(groupId)
    }

    override fun getTasksByGroupType(groupType: String): Flow<List<ModelTask>> {
         Log.d(TAG, "getTasksByGroupType: Passing through to repository")
        return taskRepository.getTasksByGroupType(groupType)
    }

    override suspend fun updateTaskGroup(taskId: String, groupId: String?, newOrder: Int) {
        taskRepository.updateTaskGroup(taskId, groupId, newOrder)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        // Invalidate subtask list of parent if order within group changed? Less direct impact.
         Log.d(TAG, "Updated task group for $taskId and invalidated cache")
    }

    override suspend fun getMaxOrderInGroup(groupId: String): Int {
        // Simple query, likely not worth caching
        return taskRepository.getMaxOrderInGroup(groupId)
    }
    
    override suspend fun generateSampleDataIfNeeded(force: Boolean) {
        taskRepository.generateSampleDataIfNeeded(force)
        // Clear all caches if sample data is generated/forced
        if (force) { // Or based on repository logic if it indicates data changed
             clearCache()
        }
    }

    override suspend fun getConflictTasks(): List<ModelTask> {
         Log.d(TAG, "getConflictTasks: Passing through to repository")
        // This might change frequently, pass through for now
        return taskRepository.getConflictTasks()
    }

    override fun observeTaskConflicts(): Flow<List<ModelTask>> {
         Log.d(TAG, "observeTaskConflicts: Passing through to repository")
        return taskRepository.observeTaskConflicts()
    }

    override suspend fun getDefaultColumnId(): String {
        // Likely static, but call repository anyway
        return taskRepository.getDefaultColumnId()
    }

    override suspend fun getRecentlyUpdatedTasks(): List<ModelTask> {
         Log.d(TAG, "getRecentlyUpdatedTasks: Passing through to repository")
        // Definition of 'recent' changes, pass through
        return taskRepository.getRecentlyUpdatedTasks()
    }

    override suspend fun updateTaskDueDate(taskId: String, dueDate: LocalDateTime) {
        taskRepository.updateTaskDueDate(taskId, dueDate)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
         Log.d(TAG, "Updated task due date for $taskId and invalidated cache")
    }

    override suspend fun addSubtasksInTransaction(parentTaskId: String, subtasks: List<ModelTask>) {
        taskRepository.addSubtasksInTransaction(parentTaskId, subtasks)
        // Invalidate parent's subtask cache after transaction
        subTaskCache.invalidate(parentTaskId)
        changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentTaskId)
        // Add new subtasks to task cache
        subtasks.forEach { 
            taskCache.put(it.id, it) 
            changeTracker.markChanged(ChangeTracker.EntityType.TASK, it.id)
        }
         Log.d(TAG, "Added ${subtasks.size} subtasks in transaction for $parentTaskId and invalidated/updated caches")
    }

    override suspend fun getTasksReferencingParent(parentId: String): List<ModelTask> {
        // This is essentially getSubTasks, use the cached version
        return getSubTasks(parentId)
    }

    override suspend fun forceCleanupTaskReferences(taskId: String) {
        // This is a destructive operation, clear relevant caches
        val task = getTaskById(taskId)
        taskRepository.forceCleanupTaskReferences(taskId)
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId) // Mark as changed though it's deleted
        task?.parentTaskId?.let { parentId ->
            subTaskCache.invalidate(parentId)
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
        }
        Log.w(TAG, "Forced cleanup for task $taskId and cleared/invalidated caches")
    }
    
    /**
     * 更新任务颜色
     *
     * @param taskId 任务ID
     * @param color 颜色值
     */
    override suspend fun updateTaskColor(taskId: String, color: Long) {
        // 获取当前任务缓存
        val cachedTask = taskCache.get(taskId)

        // 更新数据库（委托给底层仓库）
        taskRepository.updateTaskColor(taskId, color)

        // 如果有缓存，更新缓存
        if (cachedTask != null) {
            // 修复：清理描述中可能存在的颜色信息，避免UI显示问题
            val cleanedDescription = cachedTask.description
                .replace(Regex("\\s*\\{\"color\":\\d+\\}\\s*"), "") // 移除颜色信息
                .trim()

            // 创建更新后的任务并更新缓存（不在描述中存储颜色信息）
            val updatedTask = cachedTask.copy(
                description = cleanedDescription,
                updatedAt = LocalDateTime.now()
            )
            taskCache.put(taskId, updatedTask)
        }

        // 标记任务已变更
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)

        Log.d(TAG, "更新任务颜色: taskId=$taskId, color=${color.toString(16)} (颜色信息不再存储在描述中)")
    }

    /**
     * 清理所有任务描述中的颜色信息
     * 修复历史数据中可能存在的颜色信息显示问题
     */
    suspend fun cleanupTaskDescriptions() {
        try {
            Log.d(TAG, "开始清理任务描述中的颜色信息")

            // 从数据库获取所有任务
            val allTasks: List<ModelTask> = taskRepository.getAllTasks()
            var cleanedCount = 0

            // 明确指定类型并处理每个任务
            for (task in allTasks) {
                if (task.description.contains("\"color\":")) {
                    val cleanedDescription = task.description
                        .replace(Regex("\\s*\\{\"color\":\\d+\\}\\s*"), "")
                        .trim()

                    if (cleanedDescription != task.description) {
                        val updatedTask = task.copy(
                            description = cleanedDescription,
                            updatedAt = LocalDateTime.now()
                        )

                        // 更新缓存
                        taskCache.put(task.id, updatedTask)

                        // 更新数据库 - 需要在协程中调用
                        try {
                            taskRepository.updateTask(updatedTask)
                            cleanedCount++
                        } catch (updateException: Exception) {
                            Log.e(TAG, "更新任务失败: ${task.id}", updateException)
                        }
                    }
                }
            }

            Log.d(TAG, "清理完成，共清理了 $cleanedCount 个任务的描述")
        } catch (e: Exception) {
            Log.e(TAG, "清理任务描述失败", e)
        }
    }

    override suspend fun updateTaskCompletionStatus(taskId: String, isCompleted: Boolean) {
        // 调用底层仓库的方法
        taskRepository.updateTaskCompletionStatus(taskId, isCompleted)
        
        // 使相关缓存失效
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        
        // 🔧 关键修复：立即从数据库重新获取最新数据并更新缓存
        val updatedTask = taskRepository.getTaskById(taskId)
        if (updatedTask != null) {
            taskCache.put(taskId, updatedTask)
            changeTracker.markFetched(ChangeTracker.EntityType.TASK, taskId)
            Log.d(TAG, "Updated task completion status for $taskId and invalidated cache")
        } else {
            Log.e(TAG, "Failed to refresh cache for task $taskId after completion status update")
        }
        
        // 如果有父任务，也需要更新父任务的子任务列表缓存
        updatedTask?.parentTaskId?.let { parentId ->
            changeTracker.markChanged(ChangeTracker.EntityType.SUBTASKS, parentId)
            // 完成状态改变时使父任务的子任务列表缓存失效
            subTaskCache.invalidate(parentId)
        }
        
        Log.d(TAG, "Updated task completion status for $taskId and invalidated cache")
    }

    override suspend fun autoRescheduleOverdueTasks(): Int {
        // 委托给底层repository执行自动重新调度
        val rescheduledCount = taskRepository.autoRescheduleOverdueTasks()
        
        // 清理所有任务缓存，因为可能有多个任务的日期被修改
        taskCache.clear()
        
        // 标记所有任务数据可能已变更
        changeTracker.clearAll()
        
        Log.d(TAG, "Auto-rescheduled $rescheduledCount overdue tasks, cleared cache")
        return rescheduledCount
    }
    
    override suspend fun updateTaskDateManuallyModified(taskId: String, isManuallyModified: Boolean) {
        // 委托给底层repository
        taskRepository.updateTaskDateManuallyModified(taskId, isManuallyModified)
        
        // 清理该任务的缓存
        taskCache.invalidate(taskId)
        
        // 标记变更
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        
        Log.d(TAG, "Updated task date manually modified flag for $taskId: isManuallyModified=$isManuallyModified")
    }
    
    override suspend fun updateTaskDateTime(taskId: String, startDate: LocalDateTime?, dueDate: LocalDateTime?) {
        // 委托给底层repository
        taskRepository.updateTaskDateTime(taskId, startDate, dueDate)
        
        // 清理该任务的缓存
        taskCache.invalidate(taskId)
        
        // 标记变更
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
        
        Log.d(TAG, "Updated task datetime for $taskId: startDate=$startDate, dueDate=$dueDate")
    }

    // 🆕 浮动任务相关方法实现 - 直接代理到底层repository
    override suspend fun getFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<ModelTask> {
        return taskRepository.getFloatingTasksInWeek(weekStart, weekEnd)
    }
    
    override fun observeFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): Flow<List<ModelTask>> {
        return taskRepository.observeFloatingTasksInWeek(weekStart, weekEnd)
    }
    
    override suspend fun getUnscheduledFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<ModelTask> {
        return taskRepository.getUnscheduledFloatingTasksInWeek(weekStart, weekEnd)
    }
    
    override suspend fun scheduleFloatingTask(taskId: String, scheduledDate: LocalDateTime) {
        taskRepository.scheduleFloatingTask(taskId, scheduledDate)
        // 清除相关缓存
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
    }
    
    override suspend fun unscheduleFloatingTask(taskId: String) {
        taskRepository.unscheduleFloatingTask(taskId)
        // 清除相关缓存
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
    }
    
    override suspend fun updateFloatingTaskOrder(taskId: String, newOrder: Int) {
        taskRepository.updateFloatingTaskOrder(taskId, newOrder)
        // 清除相关缓存
        taskCache.invalidate(taskId)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, taskId)
    }
    
    override suspend fun createFloatingTask(
        title: String,
        description: String,
        priority: Priority?,
        weekStart: LocalDateTime,
        weekEnd: LocalDateTime,
        estimatedMinutes: Int
    ): ModelTask {
        val task = taskRepository.createFloatingTask(title, description, priority, weekStart, weekEnd, estimatedMinutes)
        // 缓存新创建的任务
        taskCache.put(task.id, task)
        changeTracker.markChanged(ChangeTracker.EntityType.TASK, task.id)
        return task
    }

    /**
     * 根据目标ID获取关联的任务
     */
    override suspend fun getTasksByGoalId(goalId: String): List<ModelTask> {
        // 目标关联任务通常不需要缓存，直接透传到repository
        Log.d(TAG, "getTasksByGoalId: Passing through to repository for goalId=$goalId")
        return taskRepository.getTasksByGoalId(goalId)
    }
    
    /**
     * 观察指定目标ID的任务变化
     */
    override fun observeTasksByGoalId(goalId: String): Flow<List<ModelTask>> {
        // 直接透传到repository，由于是观察模式，不适合缓存
        Log.d(TAG, "observeTasksByGoalId: Passing through to repository for goalId=$goalId")
        return taskRepository.observeTasksByGoalId(goalId)
    }

    // --- End of implemented methods ---
    
    /**
     * 在事务中执行操作
     * 
     * @param block 在事务中执行的操作
     */
    suspend fun runInTransaction(block: suspend () -> Unit) {
        try {
            // 执行事务
            block()
            
            // 事务完成后，清除所有缓存
            taskCache.clear()
            subTaskCache.clear()
        } catch (e: Exception) {
            Log.e(TAG, "Transaction failed", e)
            throw e
        }
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        taskCache.clear()
        subTaskCache.clear()
        changeTracker.clearAll()
        resetStats()
        Log.d(TAG, "All caches cleared")
    }
    
    /**
     * 重置统计信息
     */
    fun resetStats() {
        taskCacheHits = 0
        taskCacheMisses = 0
        subTaskCacheHits = 0
        subTaskCacheMisses = 0
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 包含缓存统计信息的Map
     */
    fun getStats(): Map<String, Any> {
        val taskTotal = taskCacheHits + taskCacheMisses
        val subTaskTotal = subTaskCacheHits + subTaskCacheMisses
        
        val taskHitRate = if (taskTotal > 0) taskCacheHits.toDouble() / taskTotal else 0.0
        val subTaskHitRate = if (subTaskTotal > 0) subTaskCacheHits.toDouble() / subTaskTotal else 0.0
        
        return mapOf(
            "enabled" to cacheEnabled,
            "taskCacheSize" to taskCache.size(),
            "taskCacheHits" to taskCacheHits,
            "taskCacheMisses" to taskCacheMisses,
            "taskCacheHitRate" to taskHitRate,
            "subTaskCacheSize" to subTaskCache.size(),
            "subTaskCacheHits" to subTaskCacheHits,
            "subTaskCacheMisses" to subTaskCacheMisses,
            "subTaskCacheHitRate" to subTaskHitRate,
            "trackedEntities" to changeTracker.getTrackedEntityCount()
        )
    }
    
    /**
     * 设置缓存启用状态
     * 
     * @param enabled 是否启用缓存
     */
    fun setCacheEnabled(enabled: Boolean) {
        this.cacheEnabled = enabled
        if (!enabled) {
            clearCache()
        }
        Log.d(TAG, "Cache ${if (enabled) "enabled" else "disabled"}")
    }

    // 🔧 循环任务相关方法实现

    /**
     * 获取所有循环任务
     */
    override suspend fun getRecurringTasks(): List<ModelTask> {
        return taskRepository.getRecurringTasks()
    }

    /**
     * 根据日期范围获取任务
     */
    override suspend fun getTasksByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<ModelTask> {
        return taskRepository.getTasksByDateRange(startDate, endDate)
    }

    /**
     * 获取指定循环任务的所有实例
     */
    override suspend fun getRecurringTaskInstances(originalTaskId: String): List<ModelTask> {
        return taskRepository.getRecurringTaskInstances(originalTaskId)
    }

    /**
     * 更新循环任务设置
     */
    override suspend fun updateRecurringSettings(taskId: String, isRecurring: Boolean, recurringPattern: String?) {
        taskRepository.updateRecurringSettings(taskId, isRecurring, recurringPattern)
        // 清除相关缓存
        taskCache.invalidate(taskId)
        clearCache()
    }

    /**
     * 检查是否存在相同标题的循环任务实例
     */
    override suspend fun hasRecurringTaskInstance(title: String, dateTime: LocalDateTime): Boolean {
        return taskRepository.hasRecurringTaskInstance(title, dateTime)
    }
}

/**
 * 带过期时间的缓存实现
 */
class ExpiringCache<K, V>(
    private val maxSize: Int,
    private val expirationTimeMs: Long
) {
    // 缓存内容
    private val cache = ConcurrentHashMap<K, CacheEntry<V>>()
    
    // 读写锁
    private val lock = ReentrantReadWriteLock()
    
    /**
     * 缓存条目，包含值和过期时间
     */
    private data class CacheEntry<V>(
        val value: V,
        val expireTime: Long
    )
    
    /**
     * 获取缓存项
     * 
     * @param key 缓存键
     * @return 缓存值，如果不存在或已过期则返回null
     */
    fun get(key: K): V? {
        return lock.read {
            val entry = cache[key]
            if (entry != null && !isExpired(entry)) {
                entry.value
            } else {
                if (entry != null) {
                    // 如果条目已过期，删除它
                    invalidate(key)
                }
                null
            }
        }
    }
    
    /**
     * 放入缓存项
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    fun put(key: K, value: V) {
        lock.write {
            cleanupIfNeeded()
            
            cache[key] = CacheEntry(
                value = value,
                expireTime = System.currentTimeMillis() + expirationTimeMs
            )
        }
    }
    
    /**
     * 使缓存项失效
     * 
     * @param key 缓存键
     */
    fun invalidate(key: K) {
        lock.write {
            cache.remove(key)
        }
    }
    
    /**
     * 清除所有缓存项
     */
    fun clear() {
        lock.write {
            cache.clear()
        }
    }
    
    /**
     * 获取缓存大小
     * 
     * @return 缓存中的项数
     */
    fun size(): Int {
        return lock.read {
            cache.size
        }
    }
    
    /**
     * 检查缓存项是否已过期
     * 
     * @param entry 缓存条目
     * @return 如果已过期则返回true
     */
    private fun isExpired(entry: CacheEntry<V>): Boolean {
        return System.currentTimeMillis() > entry.expireTime
    }
    
    /**
     * 如果缓存超过最大大小，清理部分缓存项
     */
    private fun cleanupIfNeeded() {
        if (cache.size > maxSize) {
            // 找出一半过期的或最旧的条目删除
            val now = System.currentTimeMillis()
            val keysToRemove = cache.entries
                .filter { entry -> now > entry.value.expireTime } // 先删除已过期的
                .take(cache.size - maxSize + 10)  // 至少删除一些，确保在最大大小以下
                .map { it.key }
            
            if (keysToRemove.isNotEmpty()) {
                keysToRemove.forEach { key -> cache.remove(key) }
            } else {
                // 如果没有过期条目，删除一些最早放入的条目
                val entriesToRemove = cache.entries
                    .sortedBy { it.value.expireTime }
                    .take(cache.size / 4)  // 删除1/4的条目
                    .map { it.key }
                
                entriesToRemove.forEach { key -> cache.remove(key) }
            }
        }
    }

    // 🔧 循环任务相关方法实现 - 暂时注释，待后续实现
    /*
    override suspend fun getRecurrenceGroup(taskId: String): List<com.timeflow.app.data.model.ModelTask> {
        return taskRepository.getRecurrenceGroup(taskId)
    }

    override suspend fun getCompletedRecurrenceTasks(taskId: String): List<com.timeflow.app.data.model.ModelTask> {
        return taskRepository.getCompletedRecurrenceTasks(taskId)
    }

    override suspend fun getAllRecurringTasks(): List<com.timeflow.app.data.model.ModelTask> {
        return taskRepository.getAllRecurringTasks()
    }
    */
}