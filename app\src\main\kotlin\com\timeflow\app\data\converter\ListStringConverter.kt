package com.timeflow.app.data.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * List<String>类型转换器
 * 用于将List<String>类型与数据库中的String类型相互转换
 */
class ListStringConverter {
    private val gson = Gson()
    
    /**
     * 将List<String>转换为数据库可存储的String
     */
    @TypeConverter
    fun fromList(value: List<String>?): String {
        return if (value == null || value.isEmpty()) {
            "[]"
        } else {
            gson.toJson(value)
        }
    }
    
    /**
     * 将数据库中的String转换回List<String>
     */
    @TypeConverter
    fun toList(value: String?): List<String> {
        if (value.isNullOrEmpty() || value == "[]") {
            return emptyList()
        }
        
        val listType = object : TypeToken<List<String>>() {}.type
        return try {
            gson.fromJson(value, listType)
        } catch (e: Exception) {
            emptyList()
        }
    }
} 