package com.timeflow.app.service

import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务常驻通知管理器
 * 负责管理TaskPersistentNotificationService的启动和停止
 */
@Singleton
class TaskPersistentNotificationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "TaskPersistentNotificationManager"
    }
    
    private var isServiceRunning = false
    
    /**
     * 启动任务常驻通知服务
     */
    fun startPersistentNotification() {
        Log.d(TAG, "🚀 启动任务常驻通知服务")
        
        try {
            val intent = Intent(context, TaskPersistentNotificationService::class.java).apply {
                action = TaskPersistentNotificationService.ACTION_START_PERSISTENT
            }
            
            context.startForegroundService(intent)
            isServiceRunning = true
            
            Log.d(TAG, "✅ 任务常驻通知服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动任务常驻通知服务失败", e)
        }
    }
    
    /**
     * 停止任务常驻通知服务
     */
    fun stopPersistentNotification() {
        Log.d(TAG, "🛑 停止任务常驻通知服务")
        
        try {
            val intent = Intent(context, TaskPersistentNotificationService::class.java).apply {
                action = TaskPersistentNotificationService.ACTION_STOP_PERSISTENT
            }
            
            context.startService(intent)
            isServiceRunning = false
            
            Log.d(TAG, "✅ 任务常驻通知服务停止成功")
        } catch (e: Exception) {
            Log.e(TAG, "停止任务常驻通知服务失败", e)
        }
    }
    
    /**
     * 刷新任务数据
     */
    fun refreshTaskData() {
        if (!isServiceRunning) return
        
        Log.d(TAG, "🔄 刷新任务数据")
        
        try {
            val intent = Intent(context, TaskPersistentNotificationService::class.java).apply {
                action = TaskPersistentNotificationService.ACTION_REFRESH_TASKS
            }
            
            context.startService(intent)
            Log.d(TAG, "✅ 任务数据刷新请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "刷新任务数据失败", e)
        }
    }
    
    /**
     * 完成指定任务
     */
    fun completeTask(taskId: String, taskTitle: String) {
        if (!isServiceRunning) return
        
        Log.d(TAG, "✅ 完成任务: $taskTitle")
        
        try {
            val intent = Intent(context, TaskPersistentNotificationService::class.java).apply {
                action = TaskPersistentNotificationService.ACTION_COMPLETE_TASK
                putExtra(TaskPersistentNotificationService.EXTRA_TASK_ID, taskId)
                putExtra(TaskPersistentNotificationService.EXTRA_TASK_TITLE, taskTitle)
            }
            
            context.startService(intent)
            Log.d(TAG, "✅ 任务完成请求已发送")
        } catch (e: Exception) {
            Log.e(TAG, "完成任务失败", e)
        }
    }
    
    /**
     * 检查服务是否正在运行
     */
    fun isRunning(): Boolean = isServiceRunning
    
    /**
     * 切换常驻通知状态
     */
    fun togglePersistentNotification() {
        if (isServiceRunning) {
            stopPersistentNotification()
        } else {
            startPersistentNotification()
        }
    }
}
