package com.timeflow.app.ui.screen.goal

import android.app.Activity
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.ui.components.LoadingIndicator
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.ColorUtils
import com.timeflow.app.utils.getColorFromHex
import com.timeflow.app.utils.SystemBarManager
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut

/**
 * 模板库导入界面
 * 展示公共模板库供用户浏览和导入
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalTemplateImportScreen(
    navController: NavController,
    viewModel: GoalTemplateViewModel = hiltViewModel(),
    initialCategory: String? = null
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态
    val uiState by viewModel.uiState.collectAsState()
    val recommendedTemplates by viewModel.recommendedTemplates.collectAsState()
    val popularTemplates by viewModel.popularTemplates.collectAsState()
    val categories by viewModel.availableCategories.collectAsState()
    
    // 筛选状态
    var selectedCategory by remember { mutableStateOf<String?>(initialCategory) }
    var searchQuery by remember { mutableStateOf("") }
    var showSearchBar by remember { mutableStateOf(false) }
    
    // 初始化时应用类别过滤器
    LaunchedEffect(initialCategory) {
        if (initialCategory != null) {
            viewModel.setCategoryFilter(initialCategory)
        }
    }
    
    // 模板列表
    val filteredTemplates = remember(recommendedTemplates, selectedCategory, searchQuery) {
        recommendedTemplates.filter { template ->
            (selectedCategory == null || template.category == selectedCategory) &&
            (searchQuery.isEmpty() || 
             template.name.contains(searchQuery, ignoreCase = true) ||
             template.description.contains(searchQuery, ignoreCase = true))
        }
    }
    
    // 状态栏颜色控制
    SideEffect {
        activity?.let {
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    // 提供清理机制
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            val originalStatusBarColor = window.statusBarColor
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                window.statusBarColor = originalStatusBarColor
                Log.d("GoalTemplateImportScreen", "GoalTemplateImportScreen disposed")
            }
        }
    }
    
    // 搜索回调
    val onSearch = { query: String ->
        searchQuery = query
    }

    // 处理模板选择
    val onTemplateSelected = { template: GoalTemplate ->
        // 导入模板并记录使用
        viewModel.importTemplate(template)
        viewModel.recordTemplateUsage(template.id)
        
        // 返回到添加目标页面，传递模板ID
        navController.previousBackStackEntry?.savedStateHandle?.set("selected_template_id", template.id)
        navController.popBackStack()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F9FA))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        if (showSearchBar) {
                            TextField(
                                value = searchQuery,
                                onValueChange = onSearch,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(12.dp))
                                    .shadow(elevation = 2.dp, shape = RoundedCornerShape(12.dp)),
                                placeholder = { Text("搜索模板...") },
                                singleLine = true,
                                colors = TextFieldDefaults.colors(
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedIndicatorColor = Color.White,
                                    cursorColor = Color.White
                                ),
                                shape = RoundedCornerShape(12.dp)
                            )
                        } else {
                            Text("模板库")
                        }
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = { showSearchBar = !showSearchBar }
                        ) {
                            Icon(
                                imageVector = if (showSearchBar) Icons.Default.Close else Icons.Default.Search,
                                contentDescription = if (showSearchBar) "关闭搜索" else "搜索"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = DustyLavender.copy(alpha = 0.85f),
                        titleContentColor = Color.White,
                        navigationIconContentColor = Color.White,
                        actionIconContentColor = Color.White
                    )
                )
            },
            content = { paddingValues ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    when {
                        uiState is TemplateUiState.Loading && recommendedTemplates.isEmpty() -> {
                            LoadingIndicator(isLoading = true)
                        }
                        uiState is TemplateUiState.Error && recommendedTemplates.isEmpty() -> {
                            // 错误状态
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Outlined.Error,
                                    contentDescription = null,
                                    modifier = Modifier.size(80.dp),
                                    tint = Color.Gray
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Text(
                                    text = "加载模板库失败",
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.DarkGray
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = (uiState as TemplateUiState.Error).message,
                                    fontSize = 16.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center
                                )
                                
                                Spacer(modifier = Modifier.height(24.dp))
                                
                                Button(
                                    onClick = { viewModel.loadRecommendedTemplates() },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = DustyLavender
                                    )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Refresh,
                                        contentDescription = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("重试")
                                }
                            }
                        }
                        else -> {
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp)
                            ) {
                                // 类别筛选
                                if (categories.isNotEmpty()) {
                                    CategoryFilterChips(
                                        categories = categories,
                                        selectedCategory = selectedCategory,
                                        onCategorySelected = { category ->
                                            selectedCategory = if (selectedCategory == category) null else category
                                        }
                                    )
                                    
                                    Spacer(modifier = Modifier.height(16.dp))
                                }
                                
                                // 模板网格
                                if (filteredTemplates.isEmpty()) {
                                    // 没有找到匹配的模板
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 32.dp),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Icon(
                                            imageVector = Icons.Outlined.SearchOff,
                                            contentDescription = null,
                                            modifier = Modifier.size(64.dp),
                                            tint = Color.LightGray
                                        )
                                        
                                        Spacer(modifier = Modifier.height(16.dp))
                                        
                                        Text(
                                            text = "没有找到匹配的模板",
                                            fontSize = 18.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = Color.DarkGray
                                        )
                                        
                                        if (searchQuery.isNotEmpty() || selectedCategory != null) {
                                            Spacer(modifier = Modifier.height(8.dp))
                                            
                                            Button(
                                                onClick = { 
                                                    searchQuery = ""
                                                    selectedCategory = null
                                                    showSearchBar = false
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = DustyLavender
                                                )
                                            ) {
                                                Text("清除筛选条件")
                                            }
                                        }
                                    }
                                } else {
                                    // 热门模板
                                    Text(
                                        text = "推荐模板",
                                        fontSize = 18.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = Color.DarkGray,
                                        modifier = Modifier.padding(bottom = 12.dp)
                                    )
                                    
                                    LazyVerticalGrid(
                                        columns = GridCells.Fixed(2),
                                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                                        verticalArrangement = Arrangement.spacedBy(12.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        items(filteredTemplates) { template ->
                                            TemplateCard(
                                                template = template,
                                                onClick = {
                                                    // 更新为调用上面定义的回调
                                                    onTemplateSelected(template)
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        )
    }
}

/**
 * 类别过滤芯片
 */
@Composable
private fun CategoryFilterChips(
    categories: List<String>,
    selectedCategory: String?,
    onCategorySelected: (String) -> Unit
) {
    LazyColumn {
        item {
            Column {
                Text(
                    text = "按类别筛选",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.DarkGray,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    FilterChip(
                        selected = selectedCategory == null,
                        onClick = { onCategorySelected("") },
                        label = { Text("全部") },
                        leadingIcon = if (selectedCategory == null) {
                            {
                                Icon(
                                    imageVector = Icons.Filled.Check,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                            }
                        } else null,
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = DustyLavender.copy(alpha = 0.2f),
                            selectedLabelColor = DustyLavender,
                            selectedLeadingIconColor = DustyLavender
                        )
                    )
                    
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(categories) { category ->
                            FilterChip(
                                selected = category == selectedCategory,
                                onClick = { onCategorySelected(category) },
                                label = { Text(category) },
                                leadingIcon = if (category == selectedCategory) {
                                    {
                                        Icon(
                                            imageVector = Icons.Filled.Check,
                                            contentDescription = null,
                                            modifier = Modifier.size(18.dp)
                                        )
                                    }
                                } else null,
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = DustyLavender.copy(alpha = 0.2f),
                                    selectedLabelColor = DustyLavender,
                                    selectedLeadingIconColor = DustyLavender
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 模板卡片
 */
@Composable
private fun TemplateCard(
    template: GoalTemplate,
    onClick: () -> Unit
) {
    // 使用马卡龙配色方案 - 降低饱和度的柔和色调
    val (backgroundColor, iconBackgroundColor, emoji) = when (template.category) {
        TemplateCategory.STUDY.displayName -> Triple(Color(0xFFF8EFF0), Color(0xFFF2DEE2), "💪")
        TemplateCategory.LEARNING.displayName -> Triple(Color(0xFFF0F7F9), Color(0xFFE5F0F5), "📚")
        TemplateCategory.WORK.displayName -> Triple(Color(0xFFF5EFF8), Color(0xFFECE0F0), "💼")
        TemplateCategory.HEALTH.displayName -> Triple(Color(0xFFF0F8F1), Color(0xFFE5F0E7), "🌿")
        TemplateCategory.FINANCE.displayName -> Triple(Color(0xFFF8F6F0), Color(0xFFF0EDE5), "💰")
        TemplateCategory.CREATIVE.displayName -> Triple(Color(0xFFF8EFF2), Color(0xFFF2E0E5), "🎨")
        TemplateCategory.PERSONAL.displayName -> Triple(Color(0xFFF1F0F8), Color(0xFFE6E5F0), "❤️")
        TemplateCategory.LIFESTYLE.displayName -> Triple(Color(0xFFF0F5F8), Color(0xFFE5EEF2), "📋")
        TemplateCategory.READING.displayName -> Triple(Color(0xFFF0F8F6), Color(0xFFE5F0EE), "🌱")
        else -> Triple(Color(0xFFF8F5F0), Color(0xFFF2EDE5), "🎯")
    }
    
    // 原模板颜色用作按钮颜色
    val templateColor = remember(template.colorHex) {
        getColorFromHex(template.colorHex) ?: DustyLavender
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // 顶部彩条
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .background(
                        Brush.horizontalGradient(
                            colors = listOf(
                                iconBackgroundColor,
                                backgroundColor
                            )
                        )
                    )
            )
            
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 图标和标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 图标
                    Box(
                        modifier = Modifier
                            .size(42.dp)
                            .background(
                                color = iconBackgroundColor,
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = emoji,
                            fontSize = 16.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // 标题
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = template.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        if (template.category.isNotEmpty()) {
                            Text(
                                text = template.category,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                }
                
                // 描述
                if (template.description.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = template.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 标签行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 目标类型
                    Text(
                        text = when(template.goalType.name) {
                            "BOOLEAN" -> "布尔型目标"
                            "NUMERIC" -> "数值型目标"
                            else -> "目标"
                        },
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier
                            .border(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 周期性
                    if (template.isRecurring) {
                        Text(
                            text = "周期性",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            modifier = Modifier
                                .border(
                                    width = 1.dp,
                                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                    shape = RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 导入按钮
                FilledTonalButton(
                    onClick = onClick,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.filledTonalButtonColors(
                        containerColor = templateColor.copy(alpha = 0.15f),
                        contentColor = templateColor
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.FileDownload,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("导入模板")
                }
            }
        }
    }
} 