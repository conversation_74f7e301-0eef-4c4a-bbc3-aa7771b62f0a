package com.timeflow.app.ui.screen.calendar

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.forEachGesture
import androidx.compose.foundation.gestures.tryAwaitRelease
import androidx.compose.foundation.hapticfeedback.HapticFeedback
import androidx.compose.foundation.hapticfeedback.HapticFeedbackType
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.CalendarViewDay
import androidx.compose.material.icons.filled.CalendarViewMonth
import androidx.compose.material.icons.filled.CompareArrows
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.tryAwaitRelease
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.zIndex
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.utils.SystemBarManager
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlinx.coroutines.delay

// 日历专用配色方案，使用Material3的darkColorScheme
val CalendarColors = darkColorScheme(
    primary = Color(0xFF554A60),
    onPrimary = Color(0xFF554A60),
    primaryContainer = Color(0xFFF9FBF8),
    onPrimaryContainer = Color(0xFF554A60),
    inversePrimary = Color(0xFFB1A5BD),
    secondary = Color(0xFFB1A5BD),
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFF9FBF8),
    onSecondaryContainer = Color(0xFF554A60),
    tertiary = Color(0xFF554A60),
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFF9FBF8),
    onTertiaryContainer = Color(0xFF554A60),
    background = Color(0xFFF9FBF8),
    onBackground = Color(0xFF554A60),
    surface = Color(0xFFF9FBF8),
    onSurface = Color(0xFF554A60).copy(alpha = 0.6f),
    surfaceVariant = Color(0xFFF9FBF8),
    onSurfaceVariant = Color(0xFF554A60).copy(alpha = 0.7f),
    surfaceTint = Color(0xFFF9FBF8),
    inverseSurface = Color(0xFF554A60),
    inverseOnSurface = Color(0xFFF9FBF8),
    error = Color(0xFFB00020),
    onError = Color.White,
    errorContainer = Color(0xFFFDE7E9),
    onErrorContainer = Color(0xFFB00020),
    outline = Color(0xFF554A60).copy(alpha = 0.2f),
    outlineVariant = Color(0xFF554A60).copy(alpha = 0.1f),
    scrim = Color(0xFF000000).copy(alpha = 0.3f)
)

// 任务颜色
val TaskColors = listOf(
    Color(0xFFFFF0C8), // 黄色
    Color(0xFFE8E4F3), // 淡紫色
    Color(0xFFFFD6DB), // 粉红色
    Color(0xFFD8F3E7)  // 薄荷绿
)

/**
 * 日历视图类型
 */
enum class CalendarViewType {
    MONTH,      // 月视图
    WEEK,       // 周视图（简洁版）
    WEEK_DETAIL,// 周视图（详细版）
    DAY,        // 日视图
    COMPARISON  // 对比视图
}

/**
 * 日历屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    // 状态管理
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    var currentYearMonth by remember { mutableStateOf(YearMonth.now()) }
    var currentWeek by remember { mutableStateOf(getWeekRange(LocalDate.now())) }
    var calendarViewType by remember { mutableStateOf(CalendarViewType.WEEK) } // 默认周视图
    
    // 添加触觉反馈
    val haptic = LocalHapticFeedback.current
    
    // 今日按钮动画状态
    var isTodayButtonPressed by remember { mutableStateOf(false) }
    val todayButtonScale by animateFloatAsState(
        targetValue = if (isTodayButtonPressed) 0.9f else 1f,
        animationSpec = tween(
            durationMillis = if (isTodayButtonPressed) 100 else 200,
            easing = if (isTodayButtonPressed) FastOutSlowInEasing else LinearOutSlowInEasing
        )
    )
    
    val monthFormatter = DateTimeFormatter.ofPattern("yyyy年MM月")
    val weekFormatter = DateTimeFormatter.ofPattern("yyyy年MM月")
    val weekdays = DayOfWeek.values().map { 
        it.getDisplayName(TextStyle.SHORT, Locale.CHINA) 
    }
    
    // 获取Activity引用，设置状态栏
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.setupTaskPageSystemBars(it)
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用任务页面系统栏设置
            SystemBarManager.setupTaskPageSystemBars(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("CalendarScreen", "CalendarScreen disposed")
            }
        }
    }
    
    // 获取状态栏高度
    val statusBarHeight = SystemBarManager.getFixedStatusBarHeight()
    
    // 使用日历专用颜色主题
    MaterialTheme(
        colorScheme = CalendarColors
    ) {
        Scaffold(
            containerColor = CalendarColors.background,
            topBar = {
                Column(
                    modifier = Modifier
                        .padding(top = statusBarHeight)
                        .background(CalendarColors.background)
                ) {
                    // 顶部导航栏
                    SmallTopAppBar(
                        title = { 
                            // Empty title area - removed the menu button
                            Spacer(modifier = Modifier.width(4.dp))
                        },
                        actions = {
                            // 所有按钮使用一行显示，更加紧凑和美观
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.padding(end = 8.dp)
                            ) {
                                // 按钮1：周视图切换 (移动到右侧)
                                Box(
                                    modifier = Modifier
                                        .size(40.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(
                                            if (calendarViewType == CalendarViewType.WEEK || 
                                                calendarViewType == CalendarViewType.WEEK_DETAIL)
                                            Color(0xFFE57373) else Color(0xFFF5F5F5)
                                        )
                                        .clickable {
                                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                            calendarViewType = when (calendarViewType) {
                                                CalendarViewType.WEEK -> CalendarViewType.WEEK_DETAIL
                                                CalendarViewType.WEEK_DETAIL -> CalendarViewType.WEEK
                                                else -> CalendarViewType.WEEK
                                            }
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                            imageVector = Icons.Default.CalendarViewDay,
                                            contentDescription = "周视图",
                                            tint = if (calendarViewType == CalendarViewType.WEEK || 
                                                      calendarViewType == CalendarViewType.WEEK_DETAIL)
                                                Color.White else Color(0xFF757575),
                                            modifier = Modifier.size(24.dp)
                                        )
                                }
                                
                                // 按钮2：日视图/对比切换
                                Box(
                                    modifier = Modifier
                                        .size(40.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(
                                            if (calendarViewType == CalendarViewType.DAY || 
                                                calendarViewType == CalendarViewType.COMPARISON)
                                            Color(0xFFFFB74D) else Color(0xFFF5F5F5)
                                        )
                                        .clickable {
                                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                            calendarViewType = when (calendarViewType) {
                                                CalendarViewType.DAY -> CalendarViewType.COMPARISON
                                                CalendarViewType.COMPARISON -> CalendarViewType.DAY
                                                else -> CalendarViewType.DAY
                                            }
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = if (calendarViewType == CalendarViewType.DAY) 
                                            Icons.Default.CalendarViewDay else Icons.Default.CompareArrows,
                                        contentDescription = "日/对比视图",
                                        tint = if (calendarViewType == CalendarViewType.DAY || 
                                                  calendarViewType == CalendarViewType.COMPARISON)
                                            Color.White else Color(0xFF757575),
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                                
                                // 今天按钮 - 添加缩放和触觉反馈
                                Box(
                                    modifier = Modifier
                                        .height(36.dp)
                                        .scale(todayButtonScale)
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color(0xFFF0F0F0))
                                        .pointerInput(Unit) {
                                            detectTapGestures(
                                                onPress = { 
                                                    isTodayButtonPressed = true
                                                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                                    
                                                    // 简化实现，移除 tryAwaitRelease() 调用
                                                    // 直接在 onPress 结束时重置状态
                                                    
                                                    // 跳转到今天
                                                    val today = LocalDate.now()
                                                    selectedDate = today
                                                    currentYearMonth = YearMonth.now()
                                                    currentWeek = getWeekRange(today)
                                                    
                                                    // 延迟重置按钮状态以提供视觉反馈
                                                    isTodayButtonPressed = false
                                                }
                                            )
                                        }
                                        .padding(horizontal = 12.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "今天",
                                        color = Color(0xFF554A60),
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        },
                        navigationIcon = { 
                            // Empty implementation - no navigation icon
                        },
                        colors = TopAppBarDefaults.smallTopAppBarColors(
                            containerColor = CalendarColors.background,
                            titleContentColor = CalendarColors.primary,
                            actionIconContentColor = CalendarColors.primary
                        )
                    )
                    
                    // 移动到下方的日期区域和导航按钮 (优化视觉设计)
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 12.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 前一个时间段按钮 - 添加触觉反馈
                        IconButton(
                            onClick = { 
                                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                when (calendarViewType) {
                                    CalendarViewType.MONTH -> currentYearMonth = currentYearMonth.minusMonths(1)
                                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                                        val newStartDate = currentWeek.first.minusWeeks(1)
                                        currentWeek = getWeekRange(newStartDate)
                                        selectedDate = newStartDate
                                    }
                                    else -> selectedDate = selectedDate.minusDays(1)
                                }
                            },
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFF5F5F5))
                        ) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "上一个",
                                tint = Color(0xFF554A60),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                        
                        // 日期范围文本
                        Text(
                            text = when(calendarViewType) {
                                CalendarViewType.MONTH -> currentYearMonth.format(monthFormatter)
                                CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> 
                                    "${currentWeek.first.format(DateTimeFormatter.ofPattern("MM月dd日"))} - ${currentWeek.second.format(DateTimeFormatter.ofPattern("MM月dd日"))}"
                                CalendarViewType.DAY -> selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                                CalendarViewType.COMPARISON -> "${selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}时间对比"
                            },
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF3C3C3C),
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 8.dp)
                        )
                        
                        // 后一个时间段按钮 - 添加触觉反馈
                        IconButton(
                            onClick = { 
                                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                when (calendarViewType) {
                                    CalendarViewType.MONTH -> currentYearMonth = currentYearMonth.plusMonths(1)
                                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                                        val newStartDate = currentWeek.first.plusWeeks(1)
                                        currentWeek = getWeekRange(newStartDate)
                                        selectedDate = newStartDate
                                    }
                                    else -> selectedDate = selectedDate.plusDays(1)
                                }
                            },
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFF5F5F5))
                        ) {
                            Icon(
                                imageVector = Icons.Default.ArrowForward,
                                contentDescription = "下一个",
                                tint = Color(0xFF554A60),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }
            },
            floatingActionButton = {
                // 添加任务按钮
                FloatingActionButton(
                    onClick = { 
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        navController.navigate(AppDestinations.ADD_TASK_ROUTE)
                    },
                    containerColor = Color(0xE6C3B6CF) // #c3b6cff3 带10%透明度
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加任务",
                        tint = Color.White
                    )
                }
            }
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(CalendarColors.background)
            ) {
                when (calendarViewType) {
                    CalendarViewType.MONTH -> MonthCalendarView(
                        yearMonth = currentYearMonth,
                        selectedDate = selectedDate,
                        onDateSelected = { selectedDate = it }
                    )
                    CalendarViewType.WEEK -> WeekCalendarView(
                        weekRange = currentWeek,
                        selectedDate = selectedDate,
                        onDateSelected = { selectedDate = it }
                    )
                    CalendarViewType.WEEK_DETAIL -> WeekDetailCalendarView(
                        weekRange = currentWeek,
                        selectedDate = selectedDate,
                        onDateSelected = { selectedDate = it }
                    )
                    CalendarViewType.DAY -> DayCalendarView(
                        date = selectedDate
                    )
                    CalendarViewType.COMPARISON -> ComparisonCalendarView(
                        date = selectedDate
                    )
                }
            }
        }
    }
}

// 辅助函数：获取周范围
private fun getWeekRange(date: LocalDate): Pair<LocalDate, LocalDate> {
    // 获取一周的开始（周一）和结束（周日）
    var start = date
    while (start.dayOfWeek != DayOfWeek.MONDAY) {
        start = start.minusDays(1)
    }
    
    var end = date
    while (end.dayOfWeek != DayOfWeek.SUNDAY) {
        end = end.plusDays(1)
    }
    
    return Pair(start, end)
}

/**
 * 对比视图日历 - 比较计划和实际完成情况
 */
@Composable
fun ComparisonCalendarView(
    date: LocalDate
) {
    // 小时列表
    val hours = (0..23).toList()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(CalendarColors.background)
            .padding(horizontal = 8.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 计划时间标题
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "PLAN",
                    fontSize = 16.sp,
                    color = CalendarColors.onSurface.copy(alpha = 0.7f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "03H 35M",
                    fontSize = 24.sp,
                    color = CalendarColors.primary,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "这一列是规划",
                    fontSize = 14.sp,
                    color = Color(0xFFC1BA36),
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 分隔线
            Divider(
                modifier = Modifier
                    .height(80.dp)
                    .width(1.dp)
                    .padding(horizontal = 8.dp),
                color = CalendarColors.onSurface.copy(alpha = 0.1f)
            )
            
            // 实际时间标题
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "RECORD",
                    fontSize = 16.sp,
                    color = CalendarColors.onSurface.copy(alpha = 0.7f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "00H 17M",
                    fontSize = 24.sp,
                    color = Color.Black,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "这是实际记录",
                    fontSize = 14.sp,
                    color = Color(0xFFC1BA36),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 全天事项区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 全天事项左侧
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 4.dp)
                ) {
                    // 计划的全天事项
                    Column {
                        ComparisonTaskItem(
                            title = "周报准备",
                            color = TaskColors[0],
                            isPlan = true
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        ComparisonTaskItem(
                            title = "团队活动",
                            color = TaskColors[2],
                            isPlan = true
                        )
                    }
                }
                
                // 分隔线
                Box(
                    modifier = Modifier
                        .width(1.dp)
                        .height(90.dp)
                        .background(CalendarColors.onSurface.copy(alpha = 0.1f))
                )
                
                // 全天事项右侧
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 4.dp)
                ) {
                    // 实际的全天事项
                    Column {
                        ComparisonTaskItem(
                            title = "周报准备",
                            color = TaskColors[0],
                            isPlan = false
                        )
                    }
                }
            }
        }

        Text(
            text = "规划与记录会以同色系显示",
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            textAlign = TextAlign.Center,
            fontSize = 14.sp,
            color = Color(0xFFC1BA36),
            fontWeight = FontWeight.Medium
        )
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 时间表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 80.dp) // 留出FAB的空间
        ) {
            items(hours.size) { index ->
                val hour = hours[index]
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 小时标签
                    Text(
                        text = "${hour.toString().padStart(2, '0')}",
                        fontSize = 12.sp,
                        modifier = Modifier.width(20.dp),
                        color = CalendarColors.onSurface.copy(alpha = 0.6f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 计划内容 (左侧)
                    Box(modifier = Modifier
                        .weight(1f)
                        .height(50.dp)
                        .padding(end = 4.dp)
                    ) {
                        // 模拟任务内容
                        if (hour == 8) {
                            ComparisonTaskItem(
                                title = "早会",
                                color = TaskColors[0],
                                isPlan = true
                            )
                        } else if (hour == 12) {
                            ComparisonTaskItem(
                                title = "午休",
                                color = TaskColors[1],
                                isPlan = true
                            )
                        } else if (hour == 15) {
                            ComparisonTaskItem(
                                title = "下午例会",
                                color = TaskColors[2],
                                isPlan = true
                            )
                        } else if (hour == 20) {
                            ComparisonTaskItem(
                                title = "看书",
                                color = TaskColors[3],
                                isPlan = true
                            )
                        } else if (hour == 21) {
                            ComparisonTaskItem(
                                title = "冥想",
                                color = TaskColors[1],
                                isPlan = true
                            )
                        } else if (hour == 22) {
                            ComparisonTaskItem(
                                title = "复盘",
                                color = TaskColors[2],
                                isPlan = true
                            )
                        }
                    }
                    
                    // 分隔线
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(50.dp)
                            .background(CalendarColors.onSurface.copy(alpha = 0.1f))
                    )
                    
                    // 实际内容 (右侧)
                    Box(modifier = Modifier
                        .weight(1f)
                        .height(50.dp)
                        .padding(start = 4.dp)
                    ) {
                        // 只在有记录的小时显示
                        if (hour == 8) {
                            ComparisonTaskItem(
                                title = "早会",
                                color = TaskColors[0],
                                isPlan = false
                            )
                        } else if (hour == 15) {
                            ComparisonTaskItem(
                                title = "下午例会",
                                color = TaskColors[2],
                                isPlan = false
                            )
                        } else if (hour == 21) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(40.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 显示紫色方块
                                Box(
                                    modifier = Modifier
                                        .size(14.dp)
                                        .background(Color(0xFFBF3FFF))
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                // 显示红色块
                                Box(
                                    modifier = Modifier
                                        .size(width = 20.dp, height = 14.dp)
                                        .background(Color(0xFFFF3F3F))
                                )
                            }
                        }
                    }
                }
                Divider(
                    color = CalendarColors.onSurface.copy(alpha = 0.05f),
                    thickness = 0.5.dp
                )
            }
        }
    }
}

/**
 * 对比视图中的任务项
 */
@Composable
fun ComparisonTaskItem(
    title: String,
    color: Color,
    isPlan: Boolean // 是否为计划项
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(color)
            .border(
                width = 1.dp,
                color = color.copy(alpha = 0.5f),
                shape = RoundedCornerShape(6.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = title,
            color = CalendarColors.primary,
            fontSize = 14.sp,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * 月视图日历
 */
@Composable
fun MonthCalendarView(
    yearMonth: YearMonth,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit
) {
    val daysInMonth = yearMonth.lengthOfMonth()
    val firstDayOfMonth = yearMonth.atDay(1)
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 调整为周日开始
    
    // 构建显示的日期列表
    val datesList = buildList {
        // 前一个月的填充日期
        for (i in 0 until firstDayOfWeek) {
            add(firstDayOfMonth.minusDays((firstDayOfWeek - i).toLong()))
        }
        
        // 当前月的日期
        for (i in 1..daysInMonth) {
            add(yearMonth.atDay(i))
        }
        
        // 确保总数是7的倍数
        val remainder = (firstDayOfWeek + daysInMonth) % 7
        if (remainder > 0) {
            val daysToAdd = 7 - remainder
            for (i in 1..daysToAdd) {
                add(yearMonth.atEndOfMonth().plusDays(i.toLong()))
            }
        }
    }
    
    // 使用网格布局显示日历
    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        contentPadding = PaddingValues(4.dp),
        modifier = Modifier
            .fillMaxSize()
            .background(CalendarColors.background)
    ) {
        items(datesList) { date ->
            CalendarDay(
                date = date,
                isCurrentMonth = date.month == yearMonth.month,
                isSelected = date == selectedDate,
                isToday = date == LocalDate.now(),
                onDateClick = onDateSelected
            )
        }
    }
}

/**
 * 单个日历日期项
 */
@Composable
fun CalendarDay(
    date: LocalDate,
    isCurrentMonth: Boolean,
    isSelected: Boolean,
    isToday: Boolean,
    onDateClick: (LocalDate) -> Unit
) {
    // 选中日期背景颜色
    val selectedBackgroundColor = Color(0xE6E8E4EC) // #e8e4ecfb 带10%透明度

    Box(
        modifier = Modifier
            .aspectRatio(1f) // 保持正方形
            .padding(2.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> selectedBackgroundColor
                    isToday -> CalendarColors.primary.copy(alpha = 0.1f)
                    else -> Color.Transparent
                }
            )
            .clickable { onDateClick(date) }
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(2.dp)
        ) {
            Text(
                text = date.dayOfMonth.toString(),
                color = when {
                    isSelected -> CalendarColors.onPrimary
                    isToday -> CalendarColors.primary
                    !isCurrentMonth -> CalendarColors.onSurface.copy(alpha = 0.4f)
                    else -> CalendarColors.onPrimary
                },
                fontSize = 14.sp,
                fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                overflow = TextOverflow.Clip,
                maxLines = 1
            )
            
            // 任务指示点示例
            if (isCurrentMonth && date.dayOfMonth % 3 == 0) {
                Spacer(modifier = Modifier.height(4.dp))
                Box(
                    modifier = Modifier
                        .size(4.dp)
                        .clip(CircleShape)
                        .background(
                            CalendarColors.primary.copy(alpha = 0.7f)
                        )
                )
            }
        }
    }
}

/**
 * 日视图日历
 */
@Composable
fun DayCalendarView(
    date: LocalDate
) {
    // 小时列表
    val hours = (0..23).toList()
    
    // 获取当前时间并设置自动更新
    val currentTime = remember { mutableStateOf(LocalTime.now()) }
    val currentHour = currentTime.value.hour
    val currentMinute = currentTime.value.minute
    val isToday = date == LocalDate.now()
    
    // 每分钟更新当前时间
    LaunchedEffect(key1 = Unit) {
        while(true) {
            delay(60000) // 每分钟更新一次
            currentTime.value = LocalTime.now()
        }
    }
    
    // 任务更新逻辑
    val handleTaskDrag = { title: String, hour: Int, dayOffset: Int, hourOffset: Int ->
        // 这里应该更新任务数据，这个示例只打印日志
        Log.d("DayCalendarView", "任务'$title'调整：日期偏移 $dayOffset 天，时间偏移 $hourOffset 小时")
        // 在实际应用中，这里应该调用 ViewModel 的方法更新任务数据
        true // 返回更新是否成功
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(CalendarColors.background)
    ) {
        // 日期显示
        Text(
            text = date.format(DateTimeFormatter.ofPattern("EEEE", Locale.CHINA)),
            style = MaterialTheme.typography.titleMedium,
            color = CalendarColors.primary,
            modifier = Modifier.padding(16.dp),
            overflow = TextOverflow.Ellipsis
        )
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 时间表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 80.dp) // 留出FAB的空间
        ) {
            items(hours.size) { index ->
                val hour = hours[index]
                Box(modifier = Modifier.fillMaxWidth()) {
                    // 当前时间指示线 (只在是当天且小时匹配时显示)
                    if (isToday && hour == currentHour) {
                        Box(
                            modifier = Modifier
                                .padding(start = 50.dp) // 对齐小时标签右侧
                                .offset(y = ((currentMinute / 60f) * 60).dp)
                        ) {
                            CurrentTimeIndicator(currentHour, currentMinute)
                        }
                    }
                    
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                ) {
                    // 小时标签
                    Box(
                        modifier = Modifier
                            .width(50.dp)
                            .padding(end = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "$hour:00",
                            fontSize = 12.sp,
                                color = CalendarColors.onSurface.copy(alpha = 0.6f),
                                overflow = TextOverflow.Clip
                        )
                    }
                    
                    // 内容区域
                    Column(modifier = Modifier.weight(1f)) {
                        // 模拟任务内容
                        if (hour == 9 || hour == 14 || hour == 19) {
                                val title = when (hour) {
                                    9 -> "晨会"
                                    14 -> "项目进度汇报"
                                    19 -> "健身"
                                    else -> "任务"
                                }
                                val time = "$hour:00 - ${hour+1}:00"
                                
                                DraggableTaskEventItem(
                                    title = title,
                                    time = time,
                                    color = CalendarColors.primary.copy(alpha = 0.1f),
                                    onDragCompleted = { dayOffset, hourOffset -> 
                                        handleTaskDrag(title, hour, dayOffset, hourOffset)
                                    }
                                )
                            }
                            Divider(
                                color = CalendarColors.onSurface.copy(alpha = 0.1f),
                                modifier = Modifier.padding(top = if (hour == 9 || hour == 14 || hour == 19) 8.dp else 0.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 任务事件项
 */
@Composable
fun TaskEventItem(
    title: String,
    time: String,
    color: Color
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(color)
            .padding(8.dp)
    ) {
        Column {
            Text(
                text = title,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = CalendarColors.primary,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = time,
                fontSize = 12.sp,
                color = CalendarColors.onSurface,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

// 简洁周视图 (类似图1)
@Composable
fun WeekCalendarView(
    weekRange: Pair<LocalDate, LocalDate>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit
) {
    // 创建当前周的日期列表
    val datesList = mutableListOf<LocalDate>()
    var currentDate = weekRange.first
    while (!currentDate.isAfter(weekRange.second)) {
        datesList.add(currentDate)
        currentDate = currentDate.plusDays(1)
    }
    
    // 获取星期几的本地化名称
    val weekdays = DayOfWeek.values().map { 
        it.getDisplayName(TextStyle.SHORT, Locale.CHINA) 
    }
    
    // 任务拖拽处理逻辑
    val handleTaskDrag = { title: String, dayOffset: Int, hourOffset: Int ->
        // 这里应该更新任务数据，这个示例只打印日志
        Log.d("WeekCalendarView", "任务'$title'调整：日期偏移 $dayOffset 天，时间偏移 $hourOffset 小时")
        // 在实际应用中，这里应该调用 ViewModel 的方法更新任务数据
        true // 返回更新是否成功
    }
    
    // 显示一周日期
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(CalendarColors.background)
            .padding(horizontal = 8.dp)
    ) {
        // 星期标题行 (放在数字上方)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            weekdays.forEach { day ->
                Text(
                    text = day,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center,
                    color = CalendarColors.onSurface.copy(alpha = 0.6f),
                    fontSize = 14.sp
                )
            }
        }
        
        // 显示日期行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            datesList.forEach { date ->
                val isToday = date == LocalDate.now()
                val isSelected = date == selectedDate
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 4.dp)
                ) {
                    // 日期数字
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(
                                when {
                                    isSelected -> Color(0xE6E8E4EC) // 选中状态背景
                                    isToday -> CalendarColors.primary.copy(alpha = 0.1f)
                                    else -> Color.Transparent
                                }
                            )
                            .clickable { onDateSelected(date) }
                            .padding(4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            fontSize = 16.sp,
                            fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                            color = when {
                                isSelected -> CalendarColors.primary
                                isToday -> CalendarColors.primary
                                else -> CalendarColors.onPrimary
                            }
                        )
                    }
                    
                    // 任务指示点(示例: 每3天显示一个点)
                    if (date.dayOfMonth % 3 == 0) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Box(
                            modifier = Modifier
                                .size(4.dp)
                                .clip(CircleShape)
                                .background(CalendarColors.primary.copy(alpha = 0.7f))
                        )
                    }
                }
            }
        }
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 任务详情显示区域 - 显示选中日期的任务
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 EEEE", Locale.CHINA)),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = CalendarColors.primary,
            modifier = Modifier.padding(start = 16.dp, bottom = 16.dp)
        )
        
        // 示例任务列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 80.dp) // 留出FAB的空间
        ) {
            // 示例任务 - 实际应用中应从数据源获取
            item {
                val taskColor = TaskColors[selectedDate.dayOfMonth % TaskColors.size]
                
                DraggableTaskEventItem(
                    title = "晨会",
                    time = "09:00 - 10:00",
                    color = taskColor,
                    onDragCompleted = { dayOffset, hourOffset -> 
                        handleTaskDrag("晨会", dayOffset, hourOffset)
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                DraggableTaskEventItem(
                    title = "项目进度汇报",
                    time = "14:00 - 15:00",
                    color = TaskColors[(selectedDate.dayOfMonth + 1) % TaskColors.size],
                    onDragCompleted = { dayOffset, hourOffset -> 
                        handleTaskDrag("项目进度汇报", dayOffset, hourOffset)
                    }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                DraggableTaskEventItem(
                    title = "健身",
                    time = "19:00 - 20:00",
                    color = TaskColors[(selectedDate.dayOfMonth + 2) % TaskColors.size],
                    onDragCompleted = { dayOffset, hourOffset -> 
                        handleTaskDrag("健身", dayOffset, hourOffset)
                    }
                )
            }
        }
    }
}

// 详细周视图 (类似图2)
@Composable
fun WeekDetailCalendarView(
    weekRange: Pair<LocalDate, LocalDate>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit
) {
    // 创建当前周的日期列表
    val datesList = mutableListOf<LocalDate>()
    var currentDate = weekRange.first
    while (!currentDate.isAfter(weekRange.second)) {
        datesList.add(currentDate)
        currentDate = currentDate.plusDays(1)
    }
    
    // 显示一周的任务情况
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(CalendarColors.background)
            .padding(horizontal = 4.dp, vertical = 8.dp)
    ) {
        // 顶部周历
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            listOf("一", "二", "三", "四", "五", "六", "日").forEachIndexed { index, day ->
                val date = datesList.getOrNull(index)
                date?.let {
                    val isToday = it == LocalDate.now()
                    val isSelected = it == selectedDate
                    val hasEvents = it.dayOfMonth % 3 == 0 // 示例：每3天有任务
                    
                    Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier
                                .widthIn(max = 40.dp)
                                .padding(2.dp)
                        ) {
                            // 周几
                            Text(
                                text = "周$day",
                                fontSize = 12.sp,
                                color = if (isSelected) CalendarColors.primary else CalendarColors.onSurface.copy(alpha = 0.6f)
                            )
                            
                            // 日期
                            Box(
                                modifier = Modifier
                                    .padding(vertical = 4.dp)
                                    .size(28.dp)
                                    .clip(CircleShape)
                                    .background(
                                        when {
                                            isSelected -> Color(0xE6E8E4EC)
                                            isToday -> CalendarColors.primary.copy(alpha = 0.1f)
                                            else -> Color.Transparent
                                        }
                                    )
                                    .clickable { onDateSelected(it) },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = it.dayOfMonth.toString(),
                                    fontSize = 14.sp,
                                    fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                                    color = when {
                                        isSelected -> CalendarColors.primary
                                        isToday -> CalendarColors.primary
                                        else -> CalendarColors.onPrimary
                                    }
                                )
                            }
                            
                            // 任务指示器
                            if (hasEvents) {
                                Box(
                                    modifier = Modifier
                                        .padding(top = 2.dp)
                                        .size(4.dp)
                                        .clip(CircleShape)
                                        .background(
                                            if (isSelected) CalendarColors.primary
                                            else CalendarColors.primary.copy(alpha = 0.5f)
                                        )
                                )
                            }
                        }
                    }
                }
            }
        }
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 日期任务详情区 (显示一周的任务)
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 8.dp)
        ) {
            // 通过LazyColumn展示内容
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(bottom = 80.dp)
            ) {
                datesList.forEach { date ->
                    item {
                        val isSelected = date == selectedDate
                        val isToday = date == LocalDate.now()
                        val dayOfWeek = when (date.dayOfWeek) {
                            DayOfWeek.MONDAY -> "周一"
                            DayOfWeek.TUESDAY -> "周二"
                            DayOfWeek.WEDNESDAY -> "周三"
                            DayOfWeek.THURSDAY -> "周四"
                            DayOfWeek.FRIDAY -> "周五"
                            DayOfWeek.SATURDAY -> "周六"
                            DayOfWeek.SUNDAY -> "周日"
                        }
                        
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                                .clickable { onDateSelected(date) }
                        ) {
                            // 日期标题
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "$dayOfWeek ${date.dayOfMonth}",
                                    fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                                    fontSize = 16.sp,
                                    color = if (isSelected) CalendarColors.primary else CalendarColors.onPrimary
                                )
                                
                                if (isToday) {
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "今天",
                                        fontSize = 12.sp,
                                        color = CalendarColors.primary,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                            
                            // 示例任务 - 每天随机1-3个任务
                            val taskCount = (1..3).random()
                            for (i in 0 until taskCount) {
                                if (date.dayOfMonth % 4 != i) { // 使任务分布不均匀
                                    val taskColor = TaskColors[(date.dayOfMonth + i) % TaskColors.size]
                                    
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp, horizontal = 8.dp)
                                            .clip(RoundedCornerShape(6.dp))
                                            .background(taskColor)
                                            .border(
                                                width = 1.dp,
                                                color = taskColor.copy(alpha = 0.5f),
                                                shape = RoundedCornerShape(6.dp)
                                            )
                                            .padding(horizontal = 12.dp, vertical = 8.dp)
                                    ) {
                                        Column {
                                            Text(
                                                text = when (i) {
                                                    0 -> "早会"
                                                    1 -> "项目进度汇报"
                                                    else -> "健身"
                                                },
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium,
                                                color = CalendarColors.primary
                                            )
                                            
                                            Text(
                                                text = when (i) {
                                                    0 -> "09:00 - 10:00"
                                                    1 -> "14:00 - 15:00"
                                                    else -> "19:00 - 20:00"
                                                },
                                                fontSize = 12.sp,
                                                color = CalendarColors.onSurface
                                            )
                                        }
                                    }
                                }
                            }
                            
                            if (taskCount == 0 || date.dayOfMonth % 5 == 0) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "无任务",
                                        fontSize = 14.sp,
                                        color = CalendarColors.onSurface.copy(alpha = 0.5f)
                                    )
                                }
                            }
                        }
                        
                        // 日期分隔线
                        if (date != datesList.last()) {
                            Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
                        }
                    }
                }
            }
        }
    }
}

/**
 * 可拖拽任务事件项
 */
@Composable
fun DraggableTaskEventItem(
    title: String,
    time: String,
    color: Color,
    onDragCompleted: (dayOffset: Int, hourOffset: Int) -> Unit = { _, _ -> }
) {
    // 状态管理
    var isDragging by remember { mutableStateOf(false) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = if (isDragging) 1.05f else 1f, 
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.8f else 1f,
        animationSpec = tween(durationMillis = 100)
    )
    
    // 触觉反馈
    val haptic = LocalHapticFeedback.current
    
    // 时间和日期偏移指示器状态
    var showXIndicator by remember { mutableStateOf(false) }
    var showYIndicator by remember { mutableStateOf(false) }
    var dayOffset by remember { mutableStateOf(0) }
    var hourOffset by remember { mutableStateOf(0) }
    
    // 主要拖拽方向
    var dragDirection by remember { mutableStateOf<DragDirection?>(null) }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp)
    ) {
        // 如果正在拖拽，显示引导线
        if (isDragging) {
            // 水平引导线 - 横向拖拽时显示
            if (dragDirection == DragDirection.HORIZONTAL && abs(offsetX) > 50f) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .offset(y = offsetY.dp)
                        .background(color = Color(0xFFE57373).copy(alpha = 0.7f))
                )
                
                // 显示日期提示
                Box(
                    modifier = Modifier
                        .offset(x = (offsetX + if (offsetX > 0) 50 else -50).dp, y = offsetY.dp)
                        .background(
                            color = Color(0xFFE57373).copy(alpha = 0.9f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = if (dayOffset > 0) "后移 $dayOffset 天" else "前移 ${abs(dayOffset)} 天",
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }
            }
            
            // 垂直引导线 - 纵向拖拽时显示
            if (dragDirection == DragDirection.VERTICAL && abs(offsetY) > 30f) {
                Box(
                    modifier = Modifier
                        .width(1.dp)
                        .fillMaxHeight()
                        .offset(x = offsetX.dp)
                        .background(color = Color(0xFF4CAF50).copy(alpha = 0.7f))
                )
                
                // 显示时间提示
                Box(
                    modifier = Modifier
                        .offset(x = offsetX.dp, y = (offsetY + if (offsetY > 0) 30 else -30).dp)
                        .background(
                            color = Color(0xFF4CAF50).copy(alpha = 0.9f),
                            shape = RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = if (hourOffset > 0) "延后 $hourOffset 小时" else "提前 ${abs(hourOffset)} 小时",
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }
            }
        }
        
        // 任务卡片
        Box(
            modifier = Modifier
                .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                .zIndex(if (isDragging) 1f else 0f)
                .scale(scale)
                .clip(RoundedCornerShape(6.dp))
                .background(color.copy(alpha = alpha))
                .border(
                    width = if (isDragging) 1.5.dp else 1.dp,
                    color = if (isDragging) {
                        when (dragDirection) {
                            DragDirection.HORIZONTAL -> Color(0xFFE57373)
                            DragDirection.VERTICAL -> Color(0xFF4CAF50)
                            else -> CalendarColors.primary.copy(alpha = 0.7f)
                        }
                    } else color.copy(alpha = 0.5f),
                    shape = RoundedCornerShape(6.dp)
                )
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { 
                            isDragging = true
                            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        },
                        onDragEnd = {
                            isDragging = false
                            showXIndicator = false
                            showYIndicator = false
                            dragDirection = null
                            
                            // 检查是否有有效的拖拽
                            if (dayOffset != 0 || hourOffset != 0) {
                                // 触发微动效反馈
                                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                
                                // 调用回调函数更新任务
                                onDragCompleted(dayOffset, hourOffset)
                            }
                            
                            // 重置状态
                            offsetX = 0f
                            offsetY = 0f
                            dayOffset = 0
                            hourOffset = 0
                        },
                        onDrag = { change, dragAmount ->
                            change.consume()
                            offsetX += dragAmount.x
                            offsetY += dragAmount.y
                            
                            // 确定主要拖拽方向
                            if (dragDirection == null) {
                                dragDirection = if (abs(offsetX) > abs(offsetY)) {
                                    DragDirection.HORIZONTAL
                                } else {
                                    DragDirection.VERTICAL
                                }
                            }
                            
                            // 根据主要方向更新偏移
                            if (dragDirection == DragDirection.HORIZONTAL) {
                                // 只更新水平偏移，锁定垂直方向
                                offsetY = 0f
                                
                                // 计算日期偏移 (每100dp视为一天)
                                val newDayOffset = (offsetX / 100).roundToInt()
                                if (newDayOffset != dayOffset) {
                                    dayOffset = newDayOffset
                                    haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    showXIndicator = true
                                }
                            } else {
                                // 只更新垂直偏移，锁定水平方向
                                offsetX = 0f
                                
                                // 计算时间偏移 (每60dp视为一小时)
                                val newHourOffset = (offsetY / 60).roundToInt()
                                if (newHourOffset != hourOffset) {
                                    hourOffset = newHourOffset
                                    haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    showYIndicator = true
                                }
                            }
                        }
                    )
                }
                .padding(horizontal = 12.dp, vertical = 8.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Column {
                Text(
                    text = title,
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                    color = CalendarColors.primary,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(2.dp))
                
                // 显示时间，如果正在拖拽时显示调整后的时间
                val displayTime = if (isDragging && hourOffset != 0) {
                    // 简单模拟时间调整，实际应用中应该解析time字符串并正确调整时间
                    val indicatorText = if (hourOffset > 0) "⬇ 延后" else "⬆ 提前"
                    "$time ($indicatorText ${abs(hourOffset)}小时)"
                } else {
                    time
                }
                
                Text(
                    text = displayTime,
                    fontSize = 12.sp,
                    color = CalendarColors.onSurface,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 显示日期偏移
                if (isDragging && dayOffset != 0) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = if (dayOffset > 0) "➡ 后移${dayOffset}天" else "⬅ 前移${abs(dayOffset)}天",
                        fontSize = 12.sp,
                        color = CalendarColors.primary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

// 拖拽方向枚举
enum class DragDirection {
    HORIZONTAL,
    VERTICAL
}

// 添加当前时间线组件
@Composable
fun CurrentTimeIndicator(
    hour: Int,
    minute: Int
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(2.dp)
            .background(Color(0xFFE57373))
    )
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(start = 4.dp)
    ) {
        // 时间点
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(Color(0xFFE57373))
        )
        
        // 时间文本
        Text(
            text = String.format("%02d:%02d", hour, minute),
            fontSize = 10.sp,
            color = Color(0xFFE57373),
            modifier = Modifier.padding(start = 4.dp)
        )
    }
}