package com.timeflow.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.timeflow.app.service.FocusTimerService
import dagger.hilt.android.AndroidEntryPoint

/**
 * 专注计时操作广播接收器
 * 处理来自通知栏的计时器操作
 */
@AndroidEntryPoint
class FocusTimerActionReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "FocusTimerActionReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "接收到操作: ${intent.action}")
        
        // 将操作转发给FocusTimerService
        val serviceIntent = Intent(context, FocusTimerService::class.java).apply {
            action = intent.action
        }
        
        try {
            context.startForegroundService(serviceIntent)
        } catch (e: Exception) {
            Log.e(TAG, "启动前台服务失败", e)
        }
    }
} 