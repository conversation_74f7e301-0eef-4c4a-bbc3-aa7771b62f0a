package com.timeflow.app.utils

import android.util.Log
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 安全导航工具类，用于处理所有导航相关的安全操作
 */
object SafeNavigation {
    private const val TAG = "SafeNavigation"
    private var lastNavigationTime = 0L
    private val MIN_NAVIGATION_INTERVAL = 600L
    private var currentProcessingRoute: String? = null
    
    /**
     * 安全导航到指定路由
     * @param navController 导航控制器
     * @param route 目标路由
     * @param scope 协程作用域
     * @param popUpToRoute 要弹出到的路由，默认为null
     * @param inclusive 是否包含popUpToRoute
     * @param saveState 是否保存状态
     * @param launchSingleTop 是否启动单一顶部实例
     * @param restoreState 是否恢复状态
     * @param onNavigationComplete 导航完成后的回调
     * @param onError 错误回调
     * @return 导航是否成功
     */
    suspend fun safeNavigate(
        navController: NavController,
        route: String,
        scope: CoroutineScope,
        popUpToRoute: String? = null,
        inclusive: Boolean = false,
        saveState: Boolean = true,
        launchSingleTop: Boolean = true,
        restoreState: Boolean = true,
        onNavigationComplete: (() -> Unit)? = null,
        onError: ((Exception) -> Unit)? = null
    ): Boolean {
        // 检查是否重复导航
        if (currentProcessingRoute == route) {
            Log.d(TAG, "重复的导航请求: $route, 忽略")
            return false
        }
        
        // 节流控制
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastNavigationTime < MIN_NAVIGATION_INTERVAL) {
            Log.d(TAG, "导航频率过高: $route, 忽略")
            return false
        }
        
        // 检查当前路由是否已经是目标路由
        if (navController.currentDestination?.route == route) {
            Log.d(TAG, "当前已在路由 $route, 无需导航")
            return true
        }
        
        // 准备导航
        currentProcessingRoute = route
        lastNavigationTime = currentTime
        
        var success = false
        
        try {
            withContext(Dispatchers.Main) {
                Log.d(TAG, "开始安全导航到: $route")
                
                // 构建导航选项
                val navOptions = NavOptions.Builder().apply {
                    if (popUpToRoute != null) {
                        setPopUpTo(popUpToRoute, inclusive, saveState)
                    }
                    setLaunchSingleTop(launchSingleTop)
                    setRestoreState(restoreState)
                }.build()
                
                // 执行导航
                navController.navigate(route, navOptions)
                success = true
                
                // 导航成功后，延迟释放当前处理的路由
                delay(500)
                Log.d(TAG, "导航完成: $route")
                
                // 调用完成回调
                onNavigationComplete?.invoke()
            }
        } catch (e: Exception) {
            Log.e(TAG, "导航失败: ${e.message}", e)
            onError?.invoke(e)
            success = false
        } finally {
            // 无论成功与否，都释放当前处理的路由
            currentProcessingRoute = null
        }
        
        return success
    }
    
    /**
     * 安全地处理底部导航栏的导航
     */
    fun handleBottomNavigation(
        navController: NavController, 
        route: String,
        scope: CoroutineScope,
        onComplete: (() -> Unit)? = null
    ) {
        Log.d(TAG, "处理底部导航: $route")
        
        // 获取当前导航图的起始目的地
        val startDestinationId = try {
            navController.graph.findStartDestination().id
        } catch (e: Exception) {
            Log.e(TAG, "获取起始目的地ID失败: ${e.message}")
            0
        }
        
        scope.launch {
            try {
                // 检查当前是否在任务详情页等非底部导航页面
                val currentRoute = navController.currentDestination?.route
                Log.d(TAG, "当前路由: $currentRoute, 目标路由: $route")
                
                if (currentRoute != null && !currentRoute.equals(route, ignoreCase = true)) {
                    // 如果在详情页，先延迟一下确保UI稳定
                    delay(50)
                    
                    // 安全导航
                    val success = safeNavigate(
                        navController = navController,
                        route = route,
                        scope = this,
                        popUpToRoute = startDestinationId.toString(),
                        saveState = true,
                        launchSingleTop = true,
                        restoreState = true,
                        onNavigationComplete = onComplete,
                        onError = { e ->
                            Log.e(TAG, "底部导航失败: ${e.message}")
                        }
                    )
                    
                    if (success) {
                        Log.d(TAG, "底部导航成功: $route")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理底部导航异常: ${e.message}", e)
            }
        }
    }
}