package com.timeflow.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.Instant

/**
 * 时间会话数据模型
 * 参考Toggl、RescueTime等知名时间管理应用的会话管理设计
 */
@Entity(tableName = "time_sessions")
data class TimeSession(
    @PrimaryKey
    val id: String,
    
    // 关联的任务ID
    val taskId: String,
    
    // 任务名称（快照，避免因任务删除而丢失记录）
    val taskName: String,
    
    // 会话开始时间
    val startTime: Instant,
    
    // 会话结束时间（null表示进行中）
    val endTime: Instant? = null,
    
    // 会话持续时长（秒）
    val duration: Long,
    
    // 计时器类型（NORMAL、POMODORO）
    val timerType: String,
    
    // 是否已完成
    val isCompleted: Boolean = false,
    
    // 会话备注
    val notes: String = "",
    
    // 创建时间
    val createdAt: Instant,
    
    // 更新时间
    val updatedAt: Instant = Instant.now(),
    
    // 标签（可用于分类）
    val tags: List<String> = emptyList(),
    
    // 专注评分（1-5分）
    val focusRating: Int? = null,
    
    // 生产力评分（1-5分）
    val productivityRating: Int? = null
) 