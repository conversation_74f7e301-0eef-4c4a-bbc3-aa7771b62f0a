package com.timeflow.app.utils

import androidx.compose.ui.graphics.Color
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity
import com.timeflow.app.data.analytics.TrendDirection

/**
 * 生理周期相关的工具函数
 */
object CycleUtils {
    
    /**
     * 获取规律性文本描述
     */
    fun getRegularityText(regularity: CycleRegularity): String {
        return when (regularity) {
            CycleRegularity.VERY_REGULAR -> "非常规律"
            CycleRegularity.REGULAR -> "比较规律"
            CycleRegularity.SOMEWHAT_IRREGULAR -> "不太规律"
            CycleRegularity.IRREGULAR -> "很不规律"
            CycleRegularity.INSUFFICIENT_DATA -> "数据不足"
        }
    }
    
    /**
     * 获取规律性颜色
     */
    fun getRegularityColor(regularity: CycleRegularity): Color {
        return when (regularity) {
            CycleRegularity.VERY_REGULAR -> Color(0xFF4CAF50)
            CycleRegularity.REGULAR -> Color(0xFF8BC34A)
            CycleRegularity.SOMEWHAT_IRREGULAR -> Color(0xFFFF9800)
            CycleRegularity.IRREGULAR -> Color(0xFFE91E63)
            CycleRegularity.INSUFFICIENT_DATA -> Color.Gray
        }
    }
    
    /**
     * 获取趋势文本描述
     */
    fun getTrendText(trend: TrendDirection): String {
        return when (trend) {
            TrendDirection.INCREASING -> "延长趋势"
            TrendDirection.DECREASING -> "缩短趋势"
            TrendDirection.STABLE -> "稳定"
        }
    }
    
    /**
     * 获取评分颜色
     */
    fun getScoreColor(score: Int): Color {
        return when {
            score >= 90 -> Color(0xFF4CAF50)
            score >= 80 -> Color(0xFF8BC34A)
            score >= 70 -> Color(0xFFFF9800)
            score >= 60 -> Color(0xFFFF5722)
            else -> Color(0xFFE91E63)
        }
    }
    
    /**
     * 获取变异性描述
     */
    fun getVariabilityDescription(variability: Double): String {
        return when {
            variability < 0.05 -> "非常稳定"
            variability < 0.1 -> "比较稳定"
            variability < 0.15 -> "轻度波动"
            variability < 0.2 -> "中度波动"
            else -> "波动较大"
        }
    }
}
