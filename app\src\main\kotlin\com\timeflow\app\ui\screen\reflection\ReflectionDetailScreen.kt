package com.timeflow.app.ui.screen.reflection

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccessTime
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AddReaction
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.DeleteOutline
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Fingerprint
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Label
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.OpenInNew
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.PriorityHigh
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.TagFaces
import androidx.compose.material.icons.filled.WorkspacePremium
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material.icons.outlined.Image
import androidx.compose.material.icons.outlined.Share
import androidx.compose.material.icons.outlined.Tag
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.AssistChip
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.toPixelMap
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.core.app.ShareCompat
import androidx.core.content.FileProvider
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import coil.compose.AsyncImage
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.clickable
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.navigation.TimeFlowNavigator
import com.timeflow.app.ui.navigation.asTimeFlowNavigator
import com.timeflow.app.ui.screen.reflection.ChipGroup
import com.timeflow.app.ui.screen.reflection.ReflectionType
import com.timeflow.app.ui.screen.reflection.formatDateTime
import com.timeflow.app.ui.screen.reflection.components.ShareOptionsDialog
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.ReflectionShareManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter as JavaDateTimeFormatter
import java.util.Locale
import java.util.UUID

/**
 * 感想详情页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun ReflectionDetailScreen(
    reflectionId: String,
    navController: NavController,
    viewModel: ReflectionDetailViewModel = hiltViewModel()
) {
    val navigator: TimeFlowNavigator = navController.asTimeFlowNavigator()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val isEditMode by viewModel.isEditMode.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    val scope = rememberCoroutineScope()
    
    // 表单状态
    val title by viewModel.title.collectAsStateWithLifecycle()
    val content by viewModel.content.collectAsStateWithLifecycle()
    val selectedMood by viewModel.selectedMood.collectAsStateWithLifecycle()
    val selectedTags by viewModel.selectedTags.collectAsStateWithLifecycle()
    val selectedType by viewModel.selectedType.collectAsStateWithLifecycle()
    
    // 用于捕获卡片视图引用，以便生成图片
    val cardView = remember { ComposeView(context) }
    
    // 显示操作结果的Toast信息
    var toastMessage by remember { mutableStateOf<String?>(null) }
    
    // 图片查看器状态
    var showImageViewer by remember { mutableStateOf(false) }
    var selectedImageModel by remember { mutableStateOf<Any?>(null) }
    
    // 分享对话框状态
    var showShareDialog by remember { mutableStateOf(false) }
    
    // 显示Toast消息
    LaunchedEffect(toastMessage) {
        toastMessage?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            toastMessage = null
        }
    }
    
    // 监听保存成功事件
    LaunchedEffect(uiState.saveSuccessful) {
        if (uiState.saveSuccessful) {
            toastMessage = "感想已保存"
            
            // 如果是新建感想，保存后返回列表页
            if (uiState.isNewReflection) {
                navigator.navigateBack()
            }
        }
    }
    
    LaunchedEffect(reflectionId) {
        viewModel.loadReflection(reflectionId)
    }
    
    val scrollState = rememberScrollState()
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明黑色状态栏
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("ReflectionDetailScreen", "ReflectionDetailScreen disposed")
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface) // 设置页面背景色
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加状态栏高度内边距
    ) {
        // 顶部标题栏，包含返回按钮和保存按钮
        CenterAlignedTopAppBar(
            title = { 
                if (uiState.isNewReflection) {
                    Text("新建感想")
                } else if (isEditMode) {
                    Text("编辑感想")
                } else if (uiState.reflection != null) {
                    Text(uiState.reflection?.title ?: "", maxLines = 1, overflow = TextOverflow.Ellipsis)
                } else {
                    Text("加载中...")
                }
            },
            navigationIcon = {
                IconButton(onClick = { navigator.navigateBack() }) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            },
            actions = {
                if (isEditMode) {
                    // 编辑模式下显示保存按钮
                    IconButton(
                        onClick = { viewModel.saveReflection(context) }
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Save,
                            contentDescription = "保存",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                } else if (!uiState.isNewReflection && uiState.reflection != null) {
                    // 查看模式下显示编辑按钮
                    IconButton(
                        onClick = { viewModel.enterEditMode() }
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "编辑",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,
                titleContentColor = MaterialTheme.colorScheme.primary
            )
        )
        
        // 主内容区域
        if (uiState.isLoading) {
            // 加载状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (uiState.errorMessage != null) {
            // 错误状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = uiState.errorMessage ?: "未知错误",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        } else if (isEditMode) {
            // 🎨 现代化编辑模式
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(0.dp)
            ) {
                item {
                    ModernReflectionEditForm(
                        title = title,
                        content = content,
                        selectedMood = selectedMood,
                        selectedTags = selectedTags,
                        selectedType = selectedType,
                        selectedImages = viewModel.selectedImages.collectAsState().value,
                        onTitleChange = viewModel::updateTitle,
                        onContentChange = viewModel::updateContent,
                        onMoodChange = viewModel::updateMood,
                        onTagsChange = viewModel::updateTags,
                        onTypeChange = viewModel::updateType,
                        onImageAdd = viewModel::addImage,
                        onImageRemove = viewModel::removeImage,
                        onSave = { viewModel.saveReflection(context) }
                    )
                }
            }
        } else if (uiState.reflection != null) {
            // 🎨 轻盈卡片风格 - 参考图2、图3设计
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp, 0.dp, 16.dp, 100.dp)
            ) {
                item {
                    // 🎨 主卡片 - 轻盈现代设计
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                            .combinedClickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null,
                                onClick = { /* 单击不做任何操作 */ },
                                onLongClick = {
                                                                            scope.launch {
                                            val result = ReflectionShareManager.saveReflectionCardToGallery(
                                                context = context,
                                                reflection = uiState.reflection!!
                                            )
                                            // Toast消息已在ReflectionShareManager中处理
                                        }
                                }
                            ),
                        shape = RoundedCornerShape(20.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        ),
                        elevation = CardDefaults.cardElevation(
                            defaultElevation = 3.dp,
                            pressedElevation = 1.dp
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(20.dp)
                        ) {
                            // 🎨 头部信息区
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 心情圆形图标
                                Surface(
                                    shape = CircleShape,
                                    color = Color(0xFF837A93).copy(alpha = 0.1f),
                                    modifier = Modifier.size(44.dp)
                                ) {
                                    Box(contentAlignment = Alignment.Center) {
                                        Text(
                                            text = uiState.reflection?.mood?.emoji ?: "😊",
                                            fontSize = 20.sp
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.width(12.dp))
                                
                                Column(modifier = Modifier.weight(1f)) {
                                    // 类型标签 + 时间
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = uiState.reflection?.type?.displayName ?: "生活",
                                            style = MaterialTheme.typography.labelMedium,
                                            color = Color(0xFF837A93),
                                            fontWeight = FontWeight.SemiBold
                                        )
                                        
                                        Spacer(modifier = Modifier.width(8.dp))
                                        
                                        Text(
                                            text = "•",
                                            style = MaterialTheme.typography.labelMedium,
                                            color = Color(0xFF999999)
                                        )
                                        
                                        Spacer(modifier = Modifier.width(8.dp))
                                        
                                        Text(
                                            text = formatDateTime(uiState.reflection?.date),
                                            style = MaterialTheme.typography.labelMedium,
                                            color = Color(0xFF999999)
                                        )
                                    }
                                }
                            }
                            
                            // 分隔线
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(1.dp)
                                    .background(Color(0xFF837A93).copy(alpha = 0.1f))
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // 🎨 内容区域
                            Column {
                                // 标题（如果不为空且不是默认的）
                                if (!uiState.reflection?.title.isNullOrBlank() && 
                                    uiState.reflection?.title != "感想记录") {
                                    Text(
                                        text = uiState.reflection?.title ?: "",
                                        style = MaterialTheme.typography.titleMedium.copy(
                                            fontWeight = FontWeight.Bold,
                                            lineHeight = 24.sp
                                        ),
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 16.dp)
                                    )
                                }
                                
                                // 主要内容
                                if (uiState.reflection?.richContent?.isNotEmpty() == true) {
                                    // 使用Column来正确排列富文本内容，避免重叠
                                    Column(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalArrangement = Arrangement.spacedBy(12.dp)
                                    ) {
                                        uiState.reflection?.richContent?.forEach { contentBlock ->
                                            when (contentBlock.type) {
                                                "text" -> {
                                                    SelectionContainer {
                                                        Text(
                                                            text = contentBlock.value,
                                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                                lineHeight = 22.sp,
                                                                letterSpacing = 0.1.sp
                                                            ),
                                                            color = Color(0xFF2D2D2D),
                                                            modifier = Modifier.fillMaxWidth()
                                                        )
                                                    }
                                                }
                                                "image" -> {
                                                    Card(
                                                        shape = RoundedCornerShape(16.dp),
                                                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                                                        modifier = Modifier
                                                            .fillMaxWidth()
                                                            .clickable {
                                                                selectedImageModel = if (contentBlock.value.startsWith("/")) {
                                                                    java.io.File(contentBlock.value)
                                                                } else {
                                                                    contentBlock.value
                                                                }
                                                                showImageViewer = true
                                                            }
                                                    ) {
                                                        AsyncImage(
                                                            model = if (contentBlock.value.startsWith("/")) {
                                                                // 文件路径，使用File加载
                                                                java.io.File(contentBlock.value)
                                                            } else {
                                                                // URI字符串，直接使用
                                                                contentBlock.value
                                                            },
                                                            contentDescription = "感想图片",
                                                            contentScale = ContentScale.Crop,
                                                            modifier = Modifier
                                                                .fillMaxWidth()
                                                                .heightIn(min = 200.dp, max = 400.dp)
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    // 没有富文本内容时，显示普通文本
                                    SelectionContainer {
                                        Text(
                                            text = uiState.reflection?.content ?: "",
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                lineHeight = 18.sp,
                                                letterSpacing = 0.1.sp
                                            ),
                                            color = Color(0xFF2D2D2D),
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                                
                                // 标签区域（如果有）
                                if (uiState.reflection?.tags?.isNotEmpty() == true) {
                                    Spacer(modifier = Modifier.height(20.dp))
                                    
                                    LazyRow(
                                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        items(uiState.reflection?.tags ?: emptyList()) { tag ->
                                            Surface(
                                                shape = RoundedCornerShape(10.dp),
                                                color = Color(0xFF837A93).copy(alpha = 0.08f),
                                                modifier = Modifier.clickable { }
                                            ) {
                                                Text(
                                                    text = "#$tag",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = Color(0xFF837A93),
                                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 任务信息（如果是任务反馈）
                            if (uiState.reflection?.title?.startsWith("已完成:") == true || 
                                uiState.reflection?.title?.startsWith("✓") == true) {
                                Spacer(modifier = Modifier.height(20.dp))
                                
                                Surface(
                                    shape = RoundedCornerShape(12.dp),
                                    color = Color(0xFF4CAF50).copy(alpha = 0.08f),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Row(
                                        modifier = Modifier.padding(12.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Filled.CheckCircle,
                                            contentDescription = "任务完成",
                                            tint = Color(0xFF4CAF50),
                                            modifier = Modifier.size(18.dp)
                                        )
                                        
                                        Spacer(modifier = Modifier.width(8.dp))
                                        
                                        Text(
                                            text = "已完成任务",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = Color(0xFF4CAF50),
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                            
                            // 操作按钮区域
                            Spacer(modifier = Modifier.height(32.dp))
                            
                            // 分隔线
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(1.dp)
                                    .background(Color(0xFF837A93).copy(alpha = 0.1f))
                            )
                            
                            Spacer(modifier = Modifier.height(20.dp))
                            
                            // 🎨 轻盈操作按钮
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // 分享按钮
                                Surface(
                                    modifier = Modifier
                                        .weight(1f)
                                        .clickable {
                                            showShareDialog = true
                                        },
                                    shape = RoundedCornerShape(10.dp),
                                    color = Color.Transparent
                                ) {
                                    Row(
                                        modifier = Modifier.padding(vertical = 8.dp),
                                        horizontalArrangement = Arrangement.Center,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            Icons.Outlined.Share, 
                                            contentDescription = "分享",
                                            modifier = Modifier.size(16.dp),
                                            tint = Color(0xFF666666)
                                        )
                                        // 删除"分享"文字，只保留图标
                                    }
                                }
                                
                                // 收藏按钮
                                Surface(
                                    modifier = Modifier
                                        .weight(1f)
                                        .clickable { 
                                            viewModel.addToMotivationWall()
                                            toastMessage = "已收藏"
                                        },
                                    shape = RoundedCornerShape(10.dp),
                                    color = Color.Transparent
                                ) {
                                    Row(
                                        modifier = Modifier.padding(vertical = 8.dp),
                                        horizontalArrangement = Arrangement.Center,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            Icons.Outlined.FavoriteBorder, 
                                            contentDescription = "收藏",
                                            modifier = Modifier.size(16.dp),
                                            tint = Color(0xFF666666)
                                        )
                                        Spacer(modifier = Modifier.width(6.dp))
                                        Text(
                                            "收藏", 
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = Color(0xFF666666)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            // 处理 reflection 为 null 的情况 (例如加载失败但没有错误消息)
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Text("无法加载感想详情")
            }
        }
    }
    
            // 图片查看器对话框
        if (showImageViewer && selectedImageModel != null) {
            ImageViewerDialog(
                imageModel = selectedImageModel!!,
                onDismiss = { 
                    showImageViewer = false
                    selectedImageModel = null
                }
            )
        }
        
        // 分享选项对话框
        if (showShareDialog && uiState.reflection != null) {
            ShareOptionsDialog(
                onDismiss = { showShareDialog = false },
                onShareOnly = {
                    scope.launch {
                        ReflectionShareManager.shareReflectionCard(
                            context = context,
                            reflection = uiState.reflection!!,
                            saveToGallery = false
                        )
                    }
                },
                onSaveOnly = {
                    scope.launch {
                        ReflectionShareManager.saveReflectionCardToGallery(
                            context = context,
                            reflection = uiState.reflection!!
                        )
                    }
                },
                onShareAndSave = {
                    scope.launch {
                        ReflectionShareManager.shareReflectionCard(
                            context = context,
                            reflection = uiState.reflection!!,
                            saveToGallery = true
                        )
                    }
                }
            )
        }
}

/**
 * 捕获卡片内容并保存为图片
 */
private suspend fun captureAndSaveCardImage(
    context: Context,
    card: Reflection?,
    saveToGallery: Boolean = false,
    shareImage: Boolean = false
): Boolean {
    if (card == null) return false
    
    return withContext(Dispatchers.IO) {
        try {
            // 创建位图
            val cardWidth = 1080 // 假设宽度为1080像素
            val cardHeight = 1920 // 假设高度为1920像素
            val bitmap = Bitmap.createBitmap(cardWidth, cardHeight, Bitmap.Config.ARGB_8888)
            val canvas = android.graphics.Canvas(bitmap)
            
            // 绘制白色背景
            canvas.drawColor(android.graphics.Color.WHITE)
            
            // 绘制标题
            val paint = android.graphics.Paint().apply {
                color = android.graphics.Color.BLACK
                textSize = 60f
                isFakeBoldText = true
            }
            canvas.drawText(card.title, 50f, 120f, paint)
            
            // 绘制日期
            paint.apply {
                textSize = 40f
                isFakeBoldText = false
                color = android.graphics.Color.GRAY
            }
            val dateText = formatDateTime(card.date)
            canvas.drawText(dateText, 50f, 200f, paint)
            
            // 绘制内容
            paint.apply {
                textSize = 50f
                color = android.graphics.Color.BLACK
            }
            val content = card.content
            val contentLines = breakTextIntoLines(content, paint, cardWidth - 100)
            var y = 300f
            for (line in contentLines) {
                canvas.drawText(line, 50f, y, paint)
                y += 70f
            }
            
            // 绘制标签
            paint.apply {
                textSize = 40f
                color = android.graphics.Color.rgb(165, 165, 196)
            }
            var tagX = 50f
            if (card.tags.isNotEmpty()) {
                y += 50f
                for (tag in card.tags) {
                    val tagText = "#$tag"
                    canvas.drawText(tagText, tagX, y, paint)
                    tagX += paint.measureText(tagText) + 30f
                    if (tagX > cardWidth - 100) {
                        tagX = 50f
                        y += 60f
                    }
                }
            }
            
            // 绘制水印
            paint.apply {
                textSize = 30f
                color = android.graphics.Color.LTGRAY
                textAlign = android.graphics.Paint.Align.RIGHT
            }
            canvas.drawText("来自TimeFlow", cardWidth - 50f, cardHeight - 50f, paint)
            
            // 根据不同操作处理bitmap
            if (saveToGallery) {
                // 保存到相册
                saveImageToGallery(context, bitmap)
            }
            
            if (shareImage) {
                // 分享图片
                withContext(Dispatchers.Main) {
                    shareImageFromBitmap(context, bitmap, card.title)
                }
            }
            
            true
        } catch (e: Exception) {
            Log.e("ReflectionDetailScreen", "Error capturing/saving image", e)
            false
        }
    }
}

/**
 * 将文本分行显示
 */
private fun breakTextIntoLines(text: String, paint: android.graphics.Paint, maxWidth: Int): List<String> {
    val lines = mutableListOf<String>()
    val words = text.split(" ")
    var currentLine = StringBuilder()
    
    for (word in words) {
        val testLine = if (currentLine.isEmpty()) word else "${currentLine} $word"
        val width = paint.measureText(testLine)
        
        if (width <= maxWidth) {
            currentLine.append(if (currentLine.isEmpty()) word else " $word")
        } else {
            lines.add(currentLine.toString())
            currentLine = StringBuilder(word)
        }
    }
    
    if (currentLine.isNotEmpty()) {
        lines.add(currentLine.toString())
    }
    
    return lines
}

/**
 * 保存图片到相册
 */
private fun saveImageToGallery(context: Context, bitmap: Bitmap): Uri? {
    val filename = "TimeFlow_${UUID.randomUUID()}.jpg"
    var fos: OutputStream? = null
    var imageUri: Uri? = null
    
    try {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, filename)
                put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
            }
            
            context.contentResolver.also { resolver ->
                imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                fos = imageUri?.let { resolver.openOutputStream(it) }
            }
        } else {
            val imagesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
            val image = File(imagesDir, filename)
            fos = FileOutputStream(image)
            imageUri = Uri.fromFile(image)
        }
        
        fos?.use {
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, it)
        }
    } catch (e: Exception) {
        Log.e("ReflectionDetailScreen", "Error saving image to gallery", e)
    }
    
    return imageUri
}

/**
 * 分享图片
 */
private fun shareImageFromBitmap(context: Context, bitmap: Bitmap, title: String) {
    try {
        // 保存bitmap到缓存目录
        val cachePath = File(context.cacheDir, "images")
        cachePath.mkdirs()
        val file = File(cachePath, "shared_image.jpg")
        
        FileOutputStream(file).use { 
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, it)
            it.flush()
        }
        
        // 获取FileProvider Uri
        val fileUri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            file
        )
        
        // 创建分享Intent
        val intent = ShareCompat.IntentBuilder(context as Activity)
            .setType("image/jpeg")
            .setText("分享我的感想：$title")
            .setStream(fileUri)
            .intent
            .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        
        // 启动分享
        context.startActivity(Intent.createChooser(intent, "分享感想"))
    } catch (e: Exception) {
        Log.e("ReflectionDetailScreen", "Error sharing image", e)
        Toast.makeText(context, "分享失败，请重试", Toast.LENGTH_SHORT).show()
    }
}

/**
 * 任务信息部分 - 显示任务相关信息
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun TaskInfoSection(reflection: com.timeflow.app.ui.screen.reflection.Reflection?) {
    if (reflection == null) return
    
    // 🔧 修复：从metrics中获取真实的任务信息，而不是解析标题
    val taskId = reflection.metrics["taskId"] ?: reflection.id
    val taskTitle = reflection.metrics["taskTitle"] ?: run {
        // 如果metrics中没有，则从标题中解析（向后兼容）
        val titleParts = reflection.title.split(":", limit = 2)
        if (titleParts.size > 1) titleParts[1].trim() else "未知任务"
    }
    val taskDescription = reflection.metrics["taskDescription"] ?: ""
    val taskPriority = reflection.metrics["taskPriority"] ?: "未设置"
    val taskTagsString = reflection.metrics["taskTags"] ?: ""
    val taskTags = if (taskTagsString.isNotBlank()) {
        taskTagsString.split(",").filter { it.isNotBlank() }
    } else {
        emptyList()
    }
    
    // 使用反思的日期作为完成时间
    val completedAt = formatDateTime(reflection.date)
    
    android.util.Log.d("TaskInfoSection", "显示任务信息: 标题=$taskTitle, 标签=${taskTags.joinToString(",")}, 优先级=$taskPriority")
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.2f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "任务信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 任务标题
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Filled.CheckCircle,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = taskTitle,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 🔧 修复：显示任务描述（如果有）
            if (taskDescription.isNotBlank()) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(verticalAlignment = Alignment.Top) {
                    Icon(
                        imageVector = Icons.Filled.Description,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = taskDescription,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 🔧 修复：显示任务优先级
            if (taskPriority != "未设置") {
                Spacer(modifier = Modifier.height(4.dp))
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = Icons.Filled.PriorityHigh,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "优先级: $taskPriority",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 🔧 修复：显示任务标签
            if (taskTags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.Top,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Filled.Label,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Column {
                        Text(
                            text = "任务标签:",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        // 显示任务标签
                        FlowRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            taskTags.forEach { tag ->
                                Surface(
                                    shape = RoundedCornerShape(8.dp),
                                    color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.7f),
                                    modifier = Modifier.height(24.dp)
                                ) {
                                    Text(
                                        text = "#$tag",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer,
                                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 完成时间
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Filled.Schedule,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(16.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "完成时间: ${completedAt}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 任务ID（调试信息）
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Filled.Fingerprint,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(16.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "任务ID: ${taskId.take(8)}...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 跳转到任务详情的按钮
            OutlinedButton(
                onClick = {
                    // 此处应该使用navigator跳转到任务详情页
                    // 暂时不做任何操作，因为我们没有保存真正的taskId
                },
                modifier = Modifier.align(Alignment.End),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.OpenInNew,
                    contentDescription = "查看任务",
                    modifier = Modifier.size(ButtonDefaults.IconSize)
                )
                Spacer(modifier = Modifier.width(ButtonDefaults.IconSpacing))
                Text("查看任务")
            }
        }
    }
}

/**
 * 格式化日期时间
 */
private fun formatDateTime(date: java.time.Instant): String {
    return try {
        val dateTime = date.atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
        val formatter = JavaDateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")
        dateTime.format(formatter)
    } catch (e: Exception) {
        "日期格式错误"
    }
}

/**
 * 感想编辑表单
 */
@Composable
fun ReflectionEditForm(
    title: String,
    content: String,
    selectedMood: MoodType,
    selectedTags: List<String>,
    selectedType: ReflectionType,
    selectedImages: List<Uri> = emptyList(),
    selectedDate: Instant = Instant.now(),
    selectedGoalId: String? = null,
    availableGoals: List<GoalSummary> = emptyList(),
    onTitleChange: (String) -> Unit,
    onContentChange: (String) -> Unit,
    onMoodChange: (MoodType) -> Unit,
    onTagsChange: (List<String>) -> Unit,
    onTypeChange: (ReflectionType) -> Unit,
    onImageAdd: (Uri) -> Unit,
    onImageRemove: (Uri) -> Unit,
    onDateChange: (Instant) -> Unit,
    onGoalSelect: (String?) -> Unit,
    onSave: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showImagePicker by remember { mutableStateOf(false) }
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris ->
        uris.forEach { uri ->
            onImageAdd(uri)
        }
    }
    
    // 日期选择相关状态
    var showDatePicker by remember { mutableStateOf(false) }
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = selectedDate.toEpochMilli()
    )
    val formattedDate = remember(selectedDate) {
        val dateTime = selectedDate.atZone(ZoneId.systemDefault()).toLocalDateTime()
        val formatter = JavaDateTimeFormatter.ofPattern("yyyy年MM月dd日 E", Locale.CHINESE)
        dateTime.format(formatter)
    }
    
    // 目标选择相关状态
    var showGoalSelector by remember { mutableStateOf(false) }
    val selectedGoalName = remember(selectedGoalId, availableGoals) {
        availableGoals.find { it.id == selectedGoalId }?.title ?: "无"
    }
    
    // 日期选择器对话框
    if (showDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            val newDate = Instant.ofEpochMilli(millis)
                            onDateChange(newDate)
                        }
                        showDatePicker = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color(0xFF837A93) // 🎯 确认按钮使用主色调
                    )
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("取消")
                }
            }
        ) {
            DatePicker(
                state = datePickerState,
                showModeToggle = false,
                colors = DatePickerDefaults.colors(
                    containerColor = Color.White,
                    titleContentColor = Color(0xFF837A93), // 🎯 主色调
                    selectedDayContainerColor = Color(0xFF837A93), // 🎯 选中日期背景
                    selectedDayContentColor = Color.White, // 🎯 选中日期文字颜色
                    todayContentColor = Color(0xFF837A93), // 🎯 今天日期文字颜色
                    todayDateBorderColor = Color(0xFF837A93), // 🎯 今天日期边框颜色
                    dayContentColor = Color(0xFF333333), // 🎯 普通日期文字颜色
                    weekdayContentColor = Color(0xFF666666) // 🎯 星期标题文字颜色
                )
            )
        }
    }
    
    // 目标选择器对话框
    if (showGoalSelector) {
        AlertDialog(
            onDismissRequest = { showGoalSelector = false },
            title = { Text("选择关联目标") },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    // 添加"无"选项
                    ListItem(
                        headlineContent = { Text("无") },
                        leadingContent = {
                            RadioButton(
                                selected = selectedGoalId == null,
                                onClick = { 
                                    onGoalSelect(null)
                                    showGoalSelector = false
                                }
                            )
                        },
                        modifier = Modifier.clickable {
                            onGoalSelect(null)
                            showGoalSelector = false
                        }
                    )
                    
                    Divider()
                    
                    availableGoals.forEach { goal ->
                        ListItem(
                            headlineContent = { Text(goal.title) },
                            leadingContent = {
                                RadioButton(
                                    selected = selectedGoalId == goal.id,
                                    onClick = { 
                                        onGoalSelect(goal.id)
                                        showGoalSelector = false
                                    }
                                )
                            },
                            modifier = Modifier.clickable {
                                onGoalSelect(goal.id)
                                showGoalSelector = false
                            }
                        )
                        
                        if (goal.id != availableGoals.lastOrNull()?.id) {
                            Divider()
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showGoalSelector = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    Column(
        modifier = modifier.padding(16.dp)
    ) {
        // 标题输入
        OutlinedTextField(
            value = title,
            onValueChange = onTitleChange,
            label = { Text("标题") },
            placeholder = { Text("输入感想标题") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 内容编辑区
        OutlinedTextField(
            value = content,
            onValueChange = onContentChange,
            label = { Text("感想内容") },
            placeholder = { Text("记录你的感想...") },
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            textStyle = MaterialTheme.typography.bodyLarge,
            colors = TextFieldDefaults.outlinedTextFieldColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 图片选择和预览部分
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Outlined.Image,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = if (selectedImages.isEmpty()) "添加图片" else "已添加 ${selectedImages.size} 张图片",
                    style = MaterialTheme.typography.titleSmall
                )
                Spacer(modifier = Modifier.weight(1f))
                TextButton(
                    onClick = { 
                        // 启动图片选择器
                        imagePickerLauncher.launch("image/*")
                    }
                ) {
                    Icon(Icons.Filled.Add, contentDescription = "添加")
                    Text("添加")
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 图片预览区域
            if (selectedImages.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(selectedImages) { imageUri ->
                        Box(
                            modifier = Modifier
                                .size(100.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.surfaceVariant)
                        ) {
                            // 图片预览
                            AsyncImage(
                                model = imageUri,
                                contentDescription = "已选图片",
                                contentScale = ContentScale.Crop,
                                modifier = Modifier.fillMaxSize()
                            )
                            
                            // 删除按钮
                            IconButton(
                                onClick = { onImageRemove(imageUri) },
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .size(32.dp)
                                    .padding(4.dp)
                                    .background(
                                        color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f),
                                        shape = CircleShape
                                    )
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.Close,
                                    contentDescription = "移除图片",
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 日期选择器
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "日期",
                style = MaterialTheme.typography.titleSmall,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            OutlinedCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showDatePicker = true },
                colors = CardDefaults.outlinedCardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.CalendarToday,
                        contentDescription = "选择日期",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Text(
                        text = formattedDate,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    Icon(
                        imageVector = Icons.Filled.KeyboardArrowRight,
                        contentDescription = "展开",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 目标选择
        Spacer(modifier = Modifier.height(16.dp))
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "关联目标",
                style = MaterialTheme.typography.titleSmall,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            OutlinedCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showGoalSelector = true },
                colors = CardDefaults.outlinedCardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.Flag,
                        contentDescription = "选择目标",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Text(
                        text = selectedGoalName,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    Icon(
                        imageVector = Icons.Filled.KeyboardArrowRight,
                        contentDescription = "展开",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 心情选择器
        Text(
            text = "选择心情",
            style = MaterialTheme.typography.titleSmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(MoodType.values()) { mood ->
                MoodChip(
                    mood = mood,
                    selected = selectedMood == mood,
                    onClick = { onMoodChange(mood) }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 类型选择器
        Text(
            text = "选择类型",
            style = MaterialTheme.typography.titleSmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            ReflectionType.values().forEach { type ->
                FilterChip(
                    selected = selectedType == type,
                    onClick = { onTypeChange(type) },
                    label = { Text(type.displayName) },
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = MaterialTheme.colorScheme.primary,
                        selectedLabelColor = MaterialTheme.colorScheme.onPrimary
                    )
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 标签选择/编辑
        TagEditor(
            tags = selectedTags,
            onTagsChange = onTagsChange,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 保存按钮
        Button(
            onClick = onSave,
            modifier = Modifier.align(Alignment.End)
        ) {
            Icon(Icons.Filled.Save, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("保存感想")
        }
    }
}

/**
 * 心情选择芯片
 */
@Composable
fun MoodChip(
    mood: MoodType,
    selected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        shape = CircleShape,
        color = if (selected) mood.color.copy(alpha = 0.8f) else mood.color.copy(alpha = 0.3f),
        modifier = Modifier
            .size(52.dp)
            .clickable(onClick = onClick)
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = mood.emoji,
                style = MaterialTheme.typography.titleLarge
            )
        }
    }
}

/**
 * 标签编辑器
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TagEditor(
    tags: List<String>,
    onTagsChange: (List<String>) -> Unit,
    modifier: Modifier = Modifier
) {
    var newTag by remember { mutableStateOf("") }
    
    Column(modifier = modifier) {
        Text(
            text = "标签",
            style = MaterialTheme.typography.titleSmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 标签输入
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = newTag,
                onValueChange = { newTag = it },
                label = { Text("添加标签") },
                placeholder = { Text("输入标签并点击添加") },
                singleLine = true,
                modifier = Modifier.weight(1f)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(
                onClick = {
                    if (newTag.isNotBlank() && !tags.contains(newTag)) {
                        onTagsChange(tags + newTag)
                        newTag = ""
                    }
                }
            ) {
                Icon(Icons.Filled.Add, contentDescription = "添加标签")
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 已选标签
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            maxItemsInEachRow = 4
        ) {
            tags.forEach { tag ->
                AssistChip(
                    onClick = { 
                        onTagsChange(tags - tag)
                    },
                    label = { Text(tag) },
                    leadingIcon = {
                        Icon(
                            Icons.Filled.Close,
                            contentDescription = "移除标签",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                )
            }
        }
    }
}

data class GoalSummary(
    val id: String,
    val title: String
)

/**
 * 🎨 现代化感想编辑表单
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ModernReflectionEditForm(
    title: String,
    content: String,
    selectedMood: MoodType,
    selectedTags: List<String>,
    selectedType: ReflectionType,
    selectedImages: List<Uri>,
    onTitleChange: (String) -> Unit,
    onContentChange: (String) -> Unit,
    onMoodChange: (MoodType) -> Unit,
    onTagsChange: (List<String>) -> Unit,
    onTypeChange: (ReflectionType) -> Unit,
    onImageAdd: (Uri) -> Unit,
    onImageRemove: (Uri) -> Unit,
    onSave: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 心情 + 类型 合并行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 心情选择
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "心情",
                    style = MaterialTheme.typography.labelMedium,
                    color = Color(0xFF666666),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    items(MoodType.values()) { mood ->
                        Surface(
                            shape = CircleShape,
                            color = if (selectedMood == mood) 
                                Color(0xFF837A93).copy(alpha = 0.1f) 
                            else Color.Transparent,
                            modifier = Modifier
                                .size(40.dp)
                                .clickable { onMoodChange(mood) }
                                .border(
                                    width = if (selectedMood == mood) 2.dp else 1.dp,
                                    color = if (selectedMood == mood) 
                                        Color(0xFF837A93) 
                                    else Color(0xFFE0E0E0),
                                    shape = CircleShape
                                )
                        ) {
                            Box(contentAlignment = Alignment.Center) {
                                Text(
                                    text = mood.emoji,
                                    fontSize = 16.sp
                                )
                            }
                        }
                    }
                }
            }
            
            // 类型选择
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "类型",
                    style = MaterialTheme.typography.labelMedium,
                    color = Color(0xFF666666),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    ReflectionType.values().forEach { type ->
                        Surface(
                            shape = RoundedCornerShape(12.dp),
                            color = if (selectedType == type) 
                                Color(0xFF837A93).copy(alpha = 0.1f)
                            else Color.Transparent,
                            modifier = Modifier.clickable { onTypeChange(type) },
                            border = BorderStroke(
                                width = 1.dp,
                                color = if (selectedType == type) 
                                    Color(0xFF837A93) 
                                else Color(0xFFE0E0E0)
                            )
                        ) {
                            Text(
                                text = type.displayName,
                                style = MaterialTheme.typography.labelSmall,
                                color = if (selectedType == type) 
                                    Color(0xFF837A93) 
                                else Color(0xFF666666),
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            }
        }
        
        // 标题输入
        Column {
            Text(
                text = "标题",
                style = MaterialTheme.typography.labelMedium,
                color = Color(0xFF666666),
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            OutlinedTextField(
                value = title,
                onValueChange = onTitleChange,
                placeholder = { Text("输入感想标题...") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF837A93),
                    unfocusedBorderColor = Color(0xFFE0E0E0)
                )
            )
        }
        
        // 内容输入
        Column {
            Text(
                text = "内容",
                style = MaterialTheme.typography.labelMedium,
                color = Color(0xFF666666),
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            OutlinedTextField(
                value = content,
                onValueChange = onContentChange,
                placeholder = { Text("分享你的想法...") },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF837A93),
                    unfocusedBorderColor = Color(0xFFE0E0E0)
                ),
                maxLines = 6
            )
        }
        
        // 图片上传区域
        ModernImageSelector(
            selectedImages = selectedImages,
            onImageAdd = onImageAdd,
            onImageRemove = onImageRemove,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 标签编辑
        ModernTagEditor(
            tags = selectedTags,
            onTagsChange = onTagsChange,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 保存按钮
        Button(
            onClick = onSave,
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF837A93)
            )
        ) {
            Text("保存", color = Color.White)
        }
    }
}

/**
 * 🎨 现代化心情选择组件
 */
@Composable
private fun ModernMoodChip(
    mood: MoodType,
    selected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        shape = CircleShape,
        color = if (selected) mood.color.copy(alpha = 0.2f) else Color.Transparent,
        modifier = Modifier
            .size(60.dp)
            .clickable(onClick = onClick)
            .border(
                width = if (selected) 2.dp else 1.dp,
                color = if (selected) mood.color else Color(0xFFE0E0E0),
                shape = CircleShape
            )
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = mood.emoji,
                    style = MaterialTheme.typography.titleMedium,
                    fontSize = 20.sp
                )
            }
        }
    }
}

/**
 * 🎨 现代化类型选择组件
 */
@Composable
private fun ModernTypeChip(
    type: ReflectionType,
    selected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        shape = RoundedCornerShape(20.dp),
        color = if (selected) Color(0xFF837A93).copy(alpha = 0.1f) else Color(0xFFF5F5F5),
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .clickable(onClick = onClick)
            .border(
                width = if (selected) 2.dp else 0.dp,
                color = if (selected) Color(0xFF837A93) else Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
    ) {
        Text(
            text = type.displayName,
            style = MaterialTheme.typography.labelMedium.copy(
                fontWeight = if (selected) FontWeight.SemiBold else FontWeight.Medium
            ),
            color = if (selected) Color(0xFF837A93) else Color(0xFF666666),
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 10.dp)
        )
    }
}

/**
 * 🎨 现代化标签编辑器
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun ModernTagEditor(
    tags: List<String>,
    onTagsChange: (List<String>) -> Unit,
    modifier: Modifier = Modifier
) {
    var newTag by remember { mutableStateOf("") }
    
    Column(modifier = modifier) {
        Text(
            text = "标签",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.SemiBold
            ),
            color = Color(0xFF2D2D2D),
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        // 标签输入
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = newTag,
                onValueChange = { newTag = it },
                placeholder = { Text("添加标签", color = Color(0xFF999999)) },
                singleLine = true,
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(16.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF837A93),
                    unfocusedBorderColor = Color(0xFFE0E0E0),
                    cursorColor = Color(0xFF837A93)
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Surface(
                shape = CircleShape,
                color = Color(0xFF837A93),
                modifier = Modifier
                    .size(40.dp)
                    .clickable {
                        if (newTag.isNotBlank() && !tags.contains(newTag)) {
                            onTagsChange(tags + newTag)
                            newTag = ""
                        }
                    }
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Icon(
                        Icons.Filled.Add,
                        contentDescription = "添加标签",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
        
        if (tags.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // 已选标签
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                tags.forEach { tag ->
                    Surface(
                        shape = RoundedCornerShape(16.dp),
                        color = Color(0xFF837A93).copy(alpha = 0.1f),
                        modifier = Modifier
                            .clip(RoundedCornerShape(16.dp))
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            Text(
                                text = tag,
                                style = MaterialTheme.typography.labelMedium,
                                color = Color(0xFF837A93),
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            
                            Surface(
                                shape = CircleShape,
                                color = Color(0xFF837A93).copy(alpha = 0.2f),
                                modifier = Modifier
                                    .size(20.dp)
                                    .clickable { onTagsChange(tags - tag) }
                            ) {
                                Box(contentAlignment = Alignment.Center) {
                                    Icon(
                                        Icons.Filled.Close,
                                        contentDescription = "移除标签",
                                        tint = Color(0xFF837A93),
                                        modifier = Modifier.size(12.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 🎨 现代化任务信息卡片组件
@Composable
private fun ModernTaskInfoCard(
    reflection: Reflection?,
    modifier: Modifier = Modifier
) {
    reflection?.let {
        Card(
            modifier = modifier,
            shape = RoundedCornerShape(20.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF8F9FF) // 淡蓝背景
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题行
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Surface(
                        shape = CircleShape,
                        color = Color(0xFF4CAF50).copy(alpha = 0.1f),
                        modifier = Modifier.size(36.dp)
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            Icon(
                                imageVector = Icons.Filled.CheckCircle,
                                contentDescription = "任务完成",
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "任务完成",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = Color(0xFF2D2D2D)
                        )
                        Text(
                            text = "已完成一项任务",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF666666)
                        )
                    }
                    
                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = Color(0xFF4CAF50).copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = "✓ 完成",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color(0xFF4CAF50),
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 分割线
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(Color(0xFFE0E0E0))
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 任务详情
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "任务标题",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color(0xFF999999)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = reflection.title.removePrefix("已完成:").removePrefix("✓").trim(),
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color(0xFF2D2D2D),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column(horizontalAlignment = Alignment.End) {
                        Text(
                            text = "完成时间",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color(0xFF999999)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = formatDateTime(reflection.date),
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color(0xFF2D2D2D)
                        )
                    }
                }
                
                // 如果有内容描述，显示任务详情
                if (reflection.content.isNotBlank()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = Color.White,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "任务详情",
                                style = MaterialTheme.typography.labelMedium,
                                color = Color(0xFF999999)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = reflection.content,
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color(0xFF2D2D2D),
                                lineHeight = 20.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 🎨 图片放大查看对话框
 */
@Composable
fun ImageViewerDialog(
    imageModel: Any,
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.9f))
                .clickable { onDismiss() },
            contentAlignment = Alignment.Center
        ) {
            AsyncImage(
                model = imageModel,
                contentDescription = "放大查看",
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(32.dp)
            )
            
            // 关闭按钮
            IconButton(
                onClick = onDismiss,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .background(
                        Color.White.copy(alpha = 0.2f),
                        CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Filled.Close,
                    contentDescription = "关闭",
                    tint = Color.White
                )
            }
        }
    }
}

/**
 * 🎨 现代化图片选择组件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ModernImageSelector(
    selectedImages: List<Uri>,
    onImageAdd: (Uri) -> Unit,
    onImageRemove: (Uri) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { onImageAdd(it) }
    }
    
    Column(modifier = modifier) {
        // 标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "添加图片",
                style = MaterialTheme.typography.labelMedium,
                color = Color(0xFF666666)
            )
            
            if (selectedImages.size < 9) { // 最多9张图片
                Surface(
                    modifier = Modifier
                        .size(32.dp)
                        .clickable { imagePickerLauncher.launch("image/*") },
                    shape = CircleShape,
                    color = Color(0xFF837A93).copy(alpha = 0.1f)
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Filled.Add,
                            contentDescription = "添加图片",
                            tint = Color(0xFF837A93),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 图片预览网格
        if (selectedImages.isNotEmpty()) {
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                selectedImages.forEach { imageUri ->
                    ImagePreviewCard(
                        imageUri = imageUri,
                        onRemove = { onImageRemove(imageUri) }
                    )
                }
            }
        } else {
            // 空状态提示
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { imagePickerLauncher.launch("image/*") },
                shape = RoundedCornerShape(12.dp),
                color = Color(0xFFF3F2F4),
                border = BorderStroke(
                    width = 1.dp,
                    color = Color(0xFFE0E0E0)
                )
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Image,
                        contentDescription = "添加图片",
                        tint = Color(0xFF999999),
                        modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "点击添加图片",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF999999)
                    )
                    Text(
                        text = "支持最多9张图片",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFCCCCCC)
                    )
                }
            }
        }
    }
}

/**
 * 图片预览卡片
 */
@Composable
private fun ImagePreviewCard(
    imageUri: Uri,
    onRemove: () -> Unit
) {
    Box(
        modifier = Modifier.size(80.dp)
    ) {
        // 图片
        Card(
            modifier = Modifier.fillMaxSize(),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            AsyncImage(
                model = imageUri,
                contentDescription = "预览图片",
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 删除按钮
        Surface(
            modifier = Modifier
                .size(24.dp)
                .align(Alignment.TopEnd)
                .offset(x = 8.dp, y = (-8).dp)
                .clickable { onRemove() },
            shape = CircleShape,
            color = Color.White,
            shadowElevation = 2.dp
        ) {
            Box(contentAlignment = Alignment.Center) {
                Icon(
                    imageVector = Icons.Filled.Close,
                    contentDescription = "删除",
                    tint = Color(0xFF666666),
                    modifier = Modifier.size(14.dp)
                )
            }
        }
    }
}


