package com.timeflow.app.ui.task.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.timeflow.app.data.entity.Task
import com.timeflow.app.ui.task.TaskListOptimizer

/**
 * 优化的任务列表视图 - 专为高性能设计
 * 特性：
 * 1. 性能优化 - 避免HwcComposer getLuts错误
 * 2. 内存优化 - 高效处理大量任务
 * 3. 支持任务状态变更和触觉反馈
 * 4. 简洁的设计风格
 */
@Composable
fun OptimizedTaskListView(
    tasks: List<TaskItem>,
    onTaskClick: (Long) -> Unit,
    onCheckChange: (Long, Boolean) -> Unit,
    modifier: Modifier = Modifier,
    emptyStateMessage: String = "暂无任务",
    taskCardStyle: TaskCardStyle = TaskCardStyle.COMPACT
) {
    val listState = rememberLazyListState()
    val hapticFeedback = LocalHapticFeedback.current
    
    // 如果任务列表为空，显示空状态
    if (tasks.isEmpty()) {
        EmptyView(message = emptyStateMessage)
        return
    }
    
    // 大量任务时启用高性能模式
    LaunchedEffect(tasks.size) {
        if (tasks.size > 40) {
            TaskListOptimizer.enableHighPerformanceMode()
        } else {
            TaskListOptimizer.disableHighPerformanceMode()
        }
    }
    
    LazyColumn(
        state = listState,
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 12.dp),
        contentPadding = PaddingValues(vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = tasks,
            key = { task -> task.id }
        ) { task ->
            TaskCard(
                task = task,
                onClick = {
                    onTaskClick(task.id)
                },
                onCheckChange = { isCompleted ->
                    // 触发触觉反馈
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    onCheckChange(task.id, isCompleted)
                },
                style = taskCardStyle
            )
        }
    }
}

/**
 * 任务卡片风格
 */
enum class TaskCardStyle {
    COMPACT,    // 紧凑风格
    STANDARD,   // 标准风格
    DETAILED    // 详细风格
}

/**
 * 简化的任务数据类，用于优化渲染性能
 */
data class TaskItem(
    val id: Long,
    val title: String,
    val description: String = "",
    val isCompleted: Boolean = false,
    val dueDate: String? = null,
    val priority: Int = 0
)

/**
 * 基础任务卡片 - 高性能实现
 */
@Composable
private fun TaskCard(
    task: TaskItem,
    onClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    style: TaskCardStyle = TaskCardStyle.COMPACT
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .defaultMinSize(minHeight = 56.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
        ),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = task.isCompleted,
                onCheckedChange = { onCheckChange(it) }
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (task.isCompleted) 
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                
                if (task.description.isNotEmpty() && style != TaskCardStyle.COMPACT) {
                    // 🔧 清理描述中的JSON颜色信息
                    val cleanDescription = remember(task.description) {
                        val colorPattern = """\{"color":(\d+)\}""".toRegex()
                        task.description.replace(colorPattern, "").trim()
                    }
                    
                    if (cleanDescription.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = cleanDescription,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            maxLines = if (style == TaskCardStyle.DETAILED) 3 else 1
                        )
                    }
                }
                
                if (task.dueDate != null && style == TaskCardStyle.DETAILED) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = task.dueDate,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 优先级指示器
            if (task.priority > 0 && style != TaskCardStyle.COMPACT) {
                val priorityColor = when (task.priority) {
                    1 -> Color(0xFFFFA114) // 中等优先级
                    2 -> Color(0xFFFF9500) // 高优先级
                    3 -> Color(0xFFFF3B30) // 紧急
                    else -> Color.Transparent
                }
                
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(priorityColor, RoundedCornerShape(4.dp))
                )
            }
        }
    }
}

/**
 * 空状态视图
 */
@Composable
fun EmptyView(
    message: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
} 