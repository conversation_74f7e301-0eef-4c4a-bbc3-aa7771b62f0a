package com.timeflow.app.ui.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.TaskTime
import com.timeflow.app.data.model.TaskTimeUpdateEvent
import com.timeflow.app.domain.usecase.TaskTimeUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 任务时间ViewModel
 * 负责UI层的任务时间状态管理和业务逻辑调用
 */
@HiltViewModel
class TaskTimeViewModel @Inject constructor(
    private val taskTimeUseCase: TaskTimeUseCase
) : ViewModel() {
    
    companion object {
        private const val TAG = "TaskTimeViewModel"
    }
    
    // UI状态
    private val _uiState = MutableStateFlow(TaskTimeUiState())
    val uiState: StateFlow<TaskTimeUiState> = _uiState.asStateFlow()
    
    // 时间更新事件
    private val _timeUpdateEvents = MutableSharedFlow<TaskTimeUpdateEvent>()
    val timeUpdateEvents: SharedFlow<TaskTimeUpdateEvent> = _timeUpdateEvents.asSharedFlow()
    
    // 任务时间缓存
    private val _taskTimes = MutableStateFlow<Map<String, TaskTime>>(emptyMap())
    val taskTimes: StateFlow<Map<String, TaskTime>> = _taskTimes.asStateFlow()
    
    init {
        // 监听UseCase的时间更新事件 - 主要的事件源
        viewModelScope.launch {
            taskTimeUseCase.timeUpdateEvents
                .onEach { event: TaskTimeUpdateEvent ->
                    Log.d(TAG, "收到UseCase时间更新事件: $event")
                    
                    // 更新本地缓存
                    updateLocalTaskTime(event.newTime)
                    
                    // 转发事件给UI层
                    _timeUpdateEvents.emit(event)
                    
                    // 更新UI状态
                    updateUiState { it.copy(lastUpdatedTaskId = event.taskId) }
                }
                .catch { e: Throwable ->
                    Log.e(TAG, "监听UseCase时间更新事件失败", e)
                    updateUiState { it.copy(error = e.message) }
                }
                .collect()
        }
        
        // 监听UseCase的直接时间更新流
        viewModelScope.launch {
            taskTimeUseCase.observeTimeUpdates()
                .onEach { taskTime: TaskTime ->
                    Log.d(TAG, "收到UseCase直接时间更新: $taskTime")
                    updateLocalTaskTime(taskTime)
                }
                .catch { e: Throwable ->
                    Log.e(TAG, "监听UseCase直接时间更新失败", e)
                }
                .collect()
        }
    }
    
    /**
     * 更新任务时间
     */
    fun updateTaskTime(
        taskId: String,
        startTime: LocalDateTime? = null,
        endTime: LocalDateTime? = null,
        dueDate: LocalDateTime? = null,
        source: String = "UI"
    ) {
        Log.d(TAG, "UI请求更新任务时间: taskId=$taskId, source=$source")
        
        viewModelScope.launch {
            updateUiState { it.copy(isLoading = true, error = null) }
            
            try {
                val result = taskTimeUseCase.updateTaskTime(
                    taskId = taskId,
                    startTime = startTime,
                    endTime = endTime,
                    dueDate = dueDate,
                    source = source
                )
                
                if (result.isSuccess) {
                    val taskTime = result.getOrNull()!!
                    Log.d(TAG, "任务时间更新成功: $taskTime")
                    updateUiState { it.copy(isLoading = false, lastUpdatedTaskId = taskId) }
                } else {
                    val error = result.exceptionOrNull()
                    Log.e(TAG, "任务时间更新失败", error)
                    updateUiState { it.copy(isLoading = false, error = error?.message) }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "更新任务时间异常", e)
                updateUiState { it.copy(isLoading = false, error = e.message) }
            }
        }
    }
    
    /**
     * 快速设置截止时间
     */
    fun setDueDate(taskId: String, dueDate: LocalDateTime) {
        Log.d(TAG, "快速设置截止时间: taskId=$taskId, dueDate=$dueDate")
        updateTaskTime(taskId = taskId, dueDate = dueDate, source = "QuickDueDate")
    }
    
    /**
     * 设置时间范围
     */
    fun setTimeRange(taskId: String, startTime: LocalDateTime, endTime: LocalDateTime) {
        Log.d(TAG, "设置时间范围: taskId=$taskId, startTime=$startTime, endTime=$endTime")
        updateTaskTime(
            taskId = taskId,
            startTime = startTime,
            endTime = endTime,
            dueDate = endTime,
            source = "TimeRange"
        )
    }
    
    /**
     * 清除任务时间
     */
    fun clearTaskTime(taskId: String) {
        Log.d(TAG, "清除任务时间: taskId=$taskId")
        updateTaskTime(
            taskId = taskId,
            startTime = null,
            endTime = null,
            dueDate = null,
            source = "Clear"
        )
    }
    
    /**
     * 获取任务时间
     */
    fun getTaskTime(taskId: String): TaskTime? {
        return _taskTimes.value[taskId]
    }
    
    /**
     * 加载任务时间
     */
    fun loadTaskTime(taskId: String) {
        Log.d(TAG, "加载任务时间: taskId=$taskId")
        
        viewModelScope.launch {
            try {
                val taskTime = taskTimeUseCase.getTaskTime(taskId)
                if (taskTime != null) {
                    updateLocalTaskTime(taskTime)
                    Log.d(TAG, "任务时间加载成功: $taskTime")
                } else {
                    Log.d(TAG, "任务时间不存在: taskId=$taskId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载任务时间失败", e)
                updateUiState { it.copy(error = e.message) }
            }
        }
    }
    
    /**
     * 批量加载任务时间
     */
    fun loadTaskTimes(taskIds: List<String>) {
        Log.d(TAG, "批量加载任务时间: ${taskIds.size}个任务")
        
        viewModelScope.launch {
            try {
                val taskTimes = taskTimeUseCase.getTaskTimes(taskIds)
                
                val currentTimes = _taskTimes.value.toMutableMap()
                currentTimes.putAll(taskTimes)
                _taskTimes.value = currentTimes
                
                Log.d(TAG, "批量加载完成: 加载了${taskTimes.size}个任务时间")
                
            } catch (e: Exception) {
                Log.e(TAG, "批量加载任务时间失败", e)
                updateUiState { it.copy(error = e.message) }
            }
        }
    }
    
    /**
     * 刷新所有任务时间
     */
    fun refreshAllTaskTimes() {
        Log.d(TAG, "刷新所有任务时间")
        
        viewModelScope.launch {
            updateUiState { it.copy(isLoading = true) }
            
            try {
                // 获取当前所有任务ID
                val taskIds = _taskTimes.value.keys.toList()
                if (taskIds.isNotEmpty()) {
                    loadTaskTimes(taskIds)
                }
                
                updateUiState { it.copy(isLoading = false) }
                
            } catch (e: Exception) {
                Log.e(TAG, "刷新任务时间失败", e)
                updateUiState { it.copy(isLoading = false, error = e.message) }
            }
        }
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        updateUiState { it.copy(error = null) }
    }
    
    /**
     * 更新本地任务时间缓存
     */
    private fun updateLocalTaskTime(taskTime: TaskTime) {
        val currentTimes = _taskTimes.value.toMutableMap()
        currentTimes[taskTime.taskId] = taskTime
        _taskTimes.value = currentTimes
        
        Log.d(TAG, "本地缓存已更新: ${taskTime.taskId}")
    }
    
    /**
     * 更新UI状态
     */
    private fun updateUiState(update: (TaskTimeUiState) -> TaskTimeUiState) {
        _uiState.value = update(_uiState.value)
    }
    
    /**
     * 强制刷新任务时间
     */
    fun refreshTaskTime(taskId: String) {
        Log.d(TAG, "强制刷新任务时间: taskId=$taskId")
        loadTaskTime(taskId)
    }
}

/**
 * 任务时间UI状态
 */
data class TaskTimeUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val lastUpdatedTaskId: String? = null
) 