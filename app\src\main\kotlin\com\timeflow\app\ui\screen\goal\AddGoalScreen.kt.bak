package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.CalendarMonth
import androidx.compose.material.icons.outlined.Numbers
import androidx.compose.material.icons.outlined.Upload
import androidx.compose.material.icons.outlined.Check
import androidx.compose.material.icons.outlined.Notifications
import androidx.compose.material.icons.outlined.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import androidx.compose.ui.platform.LocalContext
import android.app.Activity
import android.util.Log
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.ui.screen.goal.components.RecurringSettingsPanel
import com.timeflow.app.ui.screen.goal.components.ReminderSettingsPanel
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.utils.getColorFromHex
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.outlined.Assignment
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.ListAlt
import androidx.compose.material.icons.filled.List
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.DateRange
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import com.timeflow.app.ui.viewmodel.GoalCreationViewModel
import com.timeflow.app.ui.components.goal.GoalAssistantButtons
import com.timeflow.app.ui.components.goal.SmartTemplateList
import androidx.compose.material3.Slider
import androidx.compose.material.icons.filled.SmartToy

/**
 * 添加目标屏幕
 * 提供表单用于创建新的目标
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddGoalScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel(),
    templateViewModel: GoalTemplateViewModel = hiltViewModel(),
    goalCreationViewModel: GoalCreationViewModel = hiltViewModel()
) {
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态变量
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var startDate by remember { mutableStateOf<LocalDateTime?>(LocalDateTime.now()) }
    var dueDate by remember { mutableStateOf<LocalDateTime?>(LocalDateTime.now().plusMonths(1)) }
    var selectedPriority by remember { mutableStateOf(GoalPriority.MEDIUM) }
    var selectedCategory by remember { mutableStateOf<String?>(null) }
    val categories = listOf("工作", "学习", "健康", "个人成长", "其他")
    
    // 目标量化相关状态
    var goalType by remember { mutableStateOf(GoalType.BOOLEAN) }
    var currentValue by remember { mutableStateOf("0") }
    var targetValue by remember { mutableStateOf("100") }
    var unit by remember { mutableStateOf("") }
    var isRecurring by remember { mutableStateOf(false) }
    var recurringSettings by remember { mutableStateOf<RecurringSettings?>(null) }
    
    // 提醒设置状态
    var reminderSettings = remember { mutableStateListOf<ReminderSetting>() }
    
    // 附件状态
    var attachments = remember { mutableStateListOf<String>() }

    // 模板相关状态
    var showTemplateDialog by remember { mutableStateOf(false) }
    var showTemplateLibraryDialog by remember { mutableStateOf(false) }
    var selectedTemplate by remember { mutableStateOf<GoalTemplate?>(null) }
    
    // AI助手相关状态
    var showAiAssistantDialog by remember { mutableStateOf(false) }
    var showAiAssistantCard by remember { mutableStateOf(true) }
    
    // 监听UI状态变化
    val uiState by viewModel.uiState.collectAsState()
    val creationUiState by goalCreationViewModel.uiState.collectAsState()
    
    // 监听模板数据
    val recentTemplates by templateViewModel.recentTemplates.collectAsState()
    val popularTemplates by templateViewModel.popularTemplates.collectAsState()
    val recommendedTemplates by templateViewModel.recommendedTemplates.collectAsState()
    val templateCategories by templateViewModel.availableCategories.collectAsState()
    
    // LaunchedEffect加载模板数据
    LaunchedEffect(Unit) {
        templateViewModel.loadRecentTemplates(3)
        templateViewModel.loadPopularTemplates(3)
        templateViewModel.loadRecommendedTemplates()
        templateViewModel.updateAvailableCategories()
    }
    
    // 添加日志输出当前UI状态
    LaunchedEffect(uiState) {
        when(uiState) {
            is GoalUiState.Idle -> android.util.Log.d("AddGoalScreen", "UI状态: Idle")
            is GoalUiState.Loading -> android.util.Log.d("AddGoalScreen", "UI状态: Loading")
            is GoalUiState.Success -> android.util.Log.d("AddGoalScreen", "UI状态: Success")
            is GoalUiState.Error -> android.util.Log.d("AddGoalScreen", "UI状态: Error - ${(uiState as GoalUiState.Error).message}")
        }
    }
    
    // 监听从模板导入界面返回的模板ID
    LaunchedEffect(Unit) {
        val templateId = navController.currentBackStackEntry?.savedStateHandle?.get<String>("selected_template_id")
        if (templateId != null) {
            // 清除savedStateHandle中的数据，防止重复加载
            navController.currentBackStackEntry?.savedStateHandle?.remove<String>("selected_template_id")
            
            // 加载模板
            templateViewModel.loadTemplateDetail(templateId)
            // 等待加载完成后设置为当前选中模板
            templateViewModel.currentTemplate.collect { template ->
                if (template != null) {
                    selectedTemplate = template
                }
            }
        }
    }
    
    // 当模板被选中时，填充表单
    LaunchedEffect(selectedTemplate) {
        selectedTemplate?.let { template ->
            // 填充表单
            if (title.isEmpty()) {
                title = template.defaultTitle
            }
            description = template.defaultDescription
            selectedPriority = template.defaultPriority
            goalType = template.goalType
            
            if (template.defaultTargetValue != null) {
                targetValue = template.defaultTargetValue.toString()
            }
            
            if (template.defaultUnit != null) {
                unit = template.defaultUnit
            }
            
            if (template.defaultDurationDays != null) {
                dueDate = LocalDateTime.now().plusDays(template.defaultDurationDays.toLong())
            }
            
            isRecurring = template.isRecurring
            recurringSettings = template.recurringSettings
            
            // 复制提醒设置
            reminderSettings.clear()
            reminderSettings.addAll(template.defaultReminderSettings)
        }
    }

    // 修改：保存后自动返回逻辑
    // 使用一个布尔值记录是否已尝试过保存操作
    var hasSaveAttempted = remember { mutableStateOf(false) }
    
    LaunchedEffect(uiState) {
        // 只有当曾经尝试过保存，且当前状态为Success时才返回
        if (hasSaveAttempted.value && uiState is GoalUiState.Success) {
            android.util.Log.d("AddGoalScreen", "目标创建成功，返回上级页面")
            navController.popBackStack()
        }
    }
    
    // 处理智能模板推荐显示
    if (creationUiState is GoalCreationViewModel.GoalCreationUiState.TemplateRecommendation) {
        val templateState = creationUiState as GoalCreationViewModel.GoalCreationUiState.TemplateRecommendation
        SmartTemplateList(
            categorizedTemplates = templateState.categorizedTemplates,
            onTemplateSelected = { templateId ->
                goalCreationViewModel.selectTemplate(templateId)
                // 导航到模板详情或者直接使用该模板
                templateViewModel.loadTemplateDetail(templateId)
            },
            onDismiss = {
                goalCreationViewModel.switchToNormalMode()
            },
            isLoading = templateState.isLoading,
            error = templateState.error,
            modifier = Modifier.fillMaxSize()
        )
    } else {
        // 正常的目标添加界面
        
        // 使用更安全的状态栏实现
        SideEffect {
            activity?.let { 
                SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明状态栏
            }
        }
        
        activity?.let { act ->
            DisposableEffect(key1 = Unit) {
                val window = act.window
                
                // 保存原始值以在dispose时恢复
                val originalStatusBarColor = window.statusBarColor
                
                // 应用不透明状态栏设置
                SystemBarManager.forceOpaqueStatusBar(act)
                
                onDispose {
                    // 恢复原始状态栏颜色
                    window.statusBarColor = originalStatusBarColor
                    Log.d("AddGoalScreen", "AddGoalScreen disposed")
                }
            }
        }
        
        // 模板选择对话框
        if (showTemplateDialog) {
            TemplateSelectionDialog(
                onDismiss = { showTemplateDialog = false },
                onTemplateSelected = { template ->
                    selectedTemplate = template
                    showTemplateDialog = false
                },
                recentTemplates = recentTemplates,
                popularTemplates = popularTemplates,
                onBrowseAllTemplates = {
                    showTemplateDialog = false
                    navController.navigate(AppDestinations.GOAL_TEMPLATE_LIST)
                }
            )
        }
        
        // 模板库选择对话框 - 使用全屏导航替代
        LaunchedEffect(showTemplateLibraryDialog) {
            if (showTemplateLibraryDialog) {
                showTemplateLibraryDialog = false
                navController.navigate(AppDestinations.GOAL_TEMPLATE_IMPORT) {
                    launchSingleTop = true
                }
            }
        }
        
        // 保存函数
        fun saveGoalFunction() {
            Log.d("AddGoalScreen", "保存目标: $title")
            
            // 验证输入
            if (title.isBlank()) {
                // 显示错误
                Log.d("AddGoalScreen", "目标标题不能为空")
                return
            }
            
            // 设置hasSaveAttempted为true
            hasSaveAttempted.value = true
            
            // 创建目标对象
            val goal = Goal(
                title = title,
                description = description,
                startDate = startDate,
                dueDate = dueDate,
                priority = selectedPriority,
                tags = listOfNotNull(selectedCategory),
                goalType = goalType,
                currentValue = if (currentValue.isNotBlank()) currentValue.toDoubleOrNull() ?: 0.0 else 0.0,
                targetValue = if (targetValue.isNotBlank()) targetValue.toDoubleOrNull() else null,
                unit = if (unit.isNotBlank()) unit else null,
                isRecurring = isRecurring,
                recurringSettings = recurringSettings,
                reminderSettings = reminderSettings.toList(),
                progress = 0f,
                status = "进行中", // 默认状态为"进行中"
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
            
            // 保存目标
            viewModel.createGoal(
                title = goal.title,
                description = goal.description,
                startDate = goal.startDate,
                dueDate = goal.dueDate,
                priority = goal.priority,
                goalType = goal.goalType,
                currentValue = goal.currentValue,
                targetValue = goal.targetValue,
                unit = goal.unit,
                reminderSettings = goal.reminderSettings,
                attachments = goal.attachments,
                isRecurring = goal.isRecurring,
                recurringSettings = goal.recurringSettings
            )
        }

        // 主界面
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF7F9FC))
                .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距，匹配ProfileScreen
        ) {
            // 顶部标题栏
            TopAppBar(
                title = {
                    Text(
                        text = "设定新目标",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    if (!showAiAssistantCard) {
                        IconButton(onClick = { showAiAssistantCard = true }) {
                            Icon(
                                imageVector = Icons.Default.SmartToy,
                                contentDescription = "显示AI助手",
                                tint = DustyLavender
                            )
                        }
                    }
                    
                    TextButton(
                        onClick = { saveGoalFunction() }
                    ) {
                        Text(
                            text = "保存",
                            fontWeight = FontWeight.Medium,
                            color = DustyLavender
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFF7F9FC), // 匹配背景色
                    titleContentColor = Color(0xFF333333)
                )
            )
            
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF7F9FC))
            ) {
                // 表单内容
                val scrollState = rememberScrollState()
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // AI助手区域 (使用我们的组件代替原来的实现)
                    AnimatedVisibility(visible = showAiAssistantCard) {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color.White
                            ),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                // 标题和关闭按钮
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "AI助手",
                                        fontSize = 16.sp,
                            text = template.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 分类标签
                        if (template.category.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .clip(RoundedCornerShape(4.dp))
                                    .background(templateColor.copy(alpha = 0.15f))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = template.category,
                                    fontSize = 12.sp,
                                    color = templateColor,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = template.description,
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 导入按钮
                IconButton(onClick = onImport) {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = "导入模板",
                        tint = Color(0xFF4285F4)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 目标详情预览
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(0xFFF8F9FA))
                    .padding(8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "目标标题",
                        fontSize = 12.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        text = template.defaultTitle,
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    )
                }
                
                if (template.defaultDurationDays != null) {
                    Column {
                        Text(
                            text = "预期天数",
                            fontSize = 12.sp,
                            color = Color(0xFF666666)
                        )
                        Text(
                            text = "${template.defaultDurationDays}天",
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        )
                    }
                }
                
                if (template.goalType == GoalType.NUMERIC && template.defaultTargetValue != null) {
                    Column {
                        Text(
                            text = "目标数值",
                            fontSize = 12.sp,
                            color = Color(0xFF666666)
                        )
                        Text(
                            text = "${template.defaultTargetValue}${template.defaultUnit ?: ""}",
                            fontSize = 14.sp,
                            color = Color(0xFF333333)
                        )
                    }
                }
            }

            // 使用GoalAssistantButtons组件
            GoalAssistantButtons(
                onTemplateClick = { 
                    goalCreationViewModel.showSmartTemplates() 
                },
                onBreakdownClick = {
                    // 如果有足够信息，导航到目标拆解页面
                    if (title.isNotBlank()) {
                        navController.navigate("${AppDestinations.GOAL_BREAKDOWN}?title=${title}&description=${description}")
                    } else {
                        // 否则提示用户先填写基本信息
                        // 这里可以显示一个snackbar或dialog提示用户
                    }
                },
                onAnalyzeClick = {
                    // 开始AI智能分析
                    // 这里可以启动分析流程或显示分析对话框
                    showAiAssistantDialog = true
                }
            )
        }
    }
}

/**
 * 新增：分类选择芯片
 */
@Composable
private fun TemplateCategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) DustyLavender else Color(0xFFF0F0F0),
        border = if (isSelected) null else BorderStroke(1.dp, Color(0xFFE0E0E0))
    ) {
        Text(
            text = category,
            color = if (isSelected) Color.White else Color(0xFF666666),
            fontSize = 14.sp,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
} 