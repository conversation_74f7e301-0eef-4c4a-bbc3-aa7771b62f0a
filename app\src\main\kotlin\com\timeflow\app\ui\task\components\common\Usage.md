# 任务组件使用指南

## 共享组件目录 (`components/common`)

本目录包含可重用的任务相关UI组件，遵循了最佳实践和性能优化原则。

### 核心组件

- `EnhancedTaskCard` - 高性能的任务卡片组件，支持拖拽、滑动、延迟加载等高级特性
- `TaskList` - 基础任务列表组件，优化了大型列表的渲染性能

### 工具组件

- `RetryUtil` - 提供可靠的网络请求重试机制
- `ManagedState` - 优化的组件状态管理系统
- `EventBus` - 组件间通信机制

## 主要任务视图

应用主要使用以下任务视图组件：

- `OptimizedTaskListView` - 高性能任务列表，支持虚拟滚动和懒加载
- `DraggableKanbanBoard` - 可拖拽看板视图，适合项目和工作流管理

## 组件约定

1. 所有共享组件必须使用`ManagedState`管理状态
2. 所有组件必须实现内存优化和懒加载支持
3. 动画和交互应考虑低端设备性能

## 最近优化

- 移除了重复的TaskConverter实现
- 移除了过时的DraggableTaskList和TaskListHeader重复实现
- 统一使用TimeFlowNavHost作为主导航控制器
- 优化了TaskViewModel的性能和内存使用
