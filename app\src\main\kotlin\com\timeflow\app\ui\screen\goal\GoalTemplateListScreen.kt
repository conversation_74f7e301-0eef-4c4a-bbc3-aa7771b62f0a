package com.timeflow.app.ui.screen.goal

import android.app.Activity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.ui.components.LoadingIndicator
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.DateTimeUtils
import com.timeflow.app.utils.ColorUtils
import com.timeflow.app.utils.ComposeStatusBarEffect
import com.timeflow.app.utils.SystemBarManager
import android.util.Log
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.BorderStroke
import com.timeflow.app.utils.getColorFromHex
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import kotlin.experimental.ExperimentalTypeInference

/**
 * 全局颜色定义
 */
private val PrimaryColor = Color(0xFFb9b1c1)
private val SecondaryColor = Color(0xFFf1eff3)
private val AccentColor = Color(0xFFd7bbca)
private val TextPrimaryColor = Color(0xFF2D3748)
private val TextSecondaryColor = Color(0xFF718096)
private val DividerColor = Color(0xFFE2E8F0)

// 添加缺失的颜色定义
private val BackgroundColor = Color(0xFFF8F8F9)
private val CardBackground = Color(0xFFFFFFFF)
private val BorderColor = Color(0xFFE5E5EA)
private val TextPrimary = TextPrimaryColor
private val TextSecondary = TextSecondaryColor 
private val TextTertiary = Color(0xFFAEAEB2)

/**
 * 缩小后的间距系统 - 提高信息密度
 */
private val XXS = 2.dp
private val XS = 4.dp
private val S = 6.dp
private val M = 8.dp
private val L = 12.dp
private val XL = 16.dp
private val XXL = 20.dp

// 导入ViewModel中定义的类型
// TemplateFilterState 和 TemplateSortOrder 已在ViewModel中定义

/**
 * 现代化目标模板库
 * 采用iOS风格设计，信息密度更高，操作更直观
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalTemplateListScreen(
    navController: NavController,
    viewModel: GoalTemplateViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态收集
    val uiState by viewModel.uiState.collectAsState()
    val templates by viewModel.templates.collectAsState()
    val recentTemplates by viewModel.recentTemplates.collectAsState()
    val popularTemplates by viewModel.popularTemplates.collectAsState()
    val filterState by viewModel.filterState.collectAsState()
    val availableCategories by viewModel.availableCategories.collectAsState()
    val recommendedTemplates by viewModel.recommendedTemplates.collectAsState()
    
    // UI状态
    var showFilterDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var templateToDelete by remember { mutableStateOf<GoalTemplate?>(null) }
    var showCreateDialog by remember { mutableStateOf(false) }
    var showTemplateLibrary by remember { mutableStateOf(false) }
    var selectedTemplate by remember { mutableStateOf<GoalTemplate?>(null) }
    var customTitle by remember { mutableStateOf("") }
    var searchText by remember { mutableStateOf("") }
    var showSearch by remember { mutableStateOf(false) }
    
    // 状态栏设置
    ComposeStatusBarEffect(BackgroundColor, isDarkContent = true)
    
    // 加载推荐模板
    LaunchedEffect(Unit) {
        viewModel.loadRecommendedTemplates()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(BackgroundColor)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 现代化顶部导航栏
        ModernTopBar(
            title = if (showSearch) null else "模板库",
            showSearch = showSearch,
            searchText = searchText,
            onSearchTextChange = { 
                searchText = it
                viewModel.setSearchQuery(it)
            },
            onBackClick = { navController.popBackStack() },
            onSearchToggle = { showSearch = !showSearch },
            onFilterClick = { showFilterDialog = true },
            onAddClick = { navController.navigate(AppDestinations.GOAL_TEMPLATE_EDIT) },
            onImportClick = { showTemplateLibrary = true }
        )
        
        // 分类筛选栏 - 移到顶部固定位置
        if (availableCategories.isNotEmpty() && !showSearch && filterState.searchQuery.isEmpty()) {
            ModernCategoryFilter(
                categories = availableCategories,
                selectedCategory = filterState.selectedCategory,
                onCategorySelected = { category ->
                    viewModel.setCategoryFilter(
                        if (filterState.selectedCategory == category) null else category
                    )
                }
            )
        }
        
        // 内容区域 - 不再使用Box，避免层级遮挡
                    when {
                        uiState is TemplateUiState.Loading && templates.isEmpty() -> {
                ModernLoadingView()
                        }
                        uiState is TemplateUiState.Error && templates.isEmpty() -> {
                ModernErrorView(
                    message = (uiState as TemplateUiState.Error).message,
                    onRetry = { viewModel.loadRecommendedTemplates() }
                )
            }
            templates.isEmpty() && filterState.searchQuery.isEmpty() -> {
                ModernEmptyView(
                    onCreateNew = { navController.navigate(AppDestinations.GOAL_TEMPLATE_EDIT) },
                    onImport = { showTemplateLibrary = true }
                )
                        }
                        templates.isEmpty() -> {
                ModernNoResultsView(
                    onClearFilter = {
                                viewModel.setSearchQuery("")
                                viewModel.setCategoryFilter(null)
                                searchText = ""
                        showSearch = false
                    }
                )
                        }
                        else -> {
                ModernTemplateContent(
                                templates = templates,
                                recentTemplates = recentTemplates,
                                popularTemplates = popularTemplates,
                    showSections = filterState.searchQuery.isEmpty() && filterState.selectedCategory == null,
                                onTemplateClick = { template ->
                                    selectedTemplate = template
                                    customTitle = template.defaultTitle
                        showCreateDialog = true
                                },
                                onEditTemplate = { template ->
                                    navController.navigate("${AppDestinations.GOAL_TEMPLATE_EDIT}/${template.id}")
                                },
                                onDeleteTemplate = { template ->
                                    templateToDelete = template
                        showDeleteDialog = true
                    }
                )
            }
        }
        
        // 对话框组件
        DialogComponents(
            showFilterDialog = showFilterDialog,
            onDismissFilter = { showFilterDialog = false },
            filterState = filterState,
            availableCategories = availableCategories,
                onApplyFilter = { category, sortOrder ->
                    viewModel.setCategoryFilter(category)
                if (sortOrder != null) viewModel.setSortOrder(sortOrder)
                    showFilterDialog = false
                },
            
            showDeleteDialog = showDeleteDialog,
            templateToDelete = templateToDelete,
            onConfirmDelete = {
                templateToDelete?.let { viewModel.deleteTemplate(it.id) }
                showDeleteDialog = false
                            templateToDelete = null
            },
            onDismissDelete = {
                showDeleteDialog = false
                            templateToDelete = null
            },
            
            showCreateDialog = showCreateDialog,
            selectedTemplate = selectedTemplate,
            customTitle = customTitle,
            onTitleChange = { customTitle = it },
            onConfirmCreate = {
                            if (customTitle.isNotBlank()) {
                                selectedTemplate?.let { template ->
                                    viewModel.createGoalFromTemplate(template.id, customTitle)
                        showCreateDialog = false
                                    selectedTemplate = null
                                    customTitle = ""
                                    navController.navigate(AppDestinations.GOAL_MANAGEMENT)
                                }
                            }
                        },
            onDismissCreate = {
                showCreateDialog = false
                            selectedTemplate = null
                            customTitle = ""
            },
            
            showTemplateLibrary = showTemplateLibrary,
            recommendedTemplates = recommendedTemplates,
            onSelectTemplate = { template ->
                viewModel.importTemplate(template)
                showTemplateLibrary = false
            },
            onDismissLibrary = { showTemplateLibrary = false },
            predefinedCategories = viewModel.getPredefinedCategories()
        )
    }
}

/**
 * 现代化顶部导航栏 - 简洁高效
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ModernTopBar(
    title: String?,
    showSearch: Boolean,
    searchText: String,
    onSearchTextChange: (String) -> Unit,
    onBackClick: () -> Unit,
    onSearchToggle: () -> Unit,
    onFilterClick: () -> Unit,
    onAddClick: () -> Unit,
    onImportClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = CardBackground,
        shadowElevation = if (showSearch) 0.dp else 0.5.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = XL, vertical = L),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = TextPrimary,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(L))
            
            if (showSearch) {
                // 搜索输入框
                OutlinedTextField(
                    value = searchText,
                    onValueChange = onSearchTextChange,
                    modifier = Modifier.weight(1f),
                    placeholder = { 
                        Text(
                            text = "搜索模板...",
                            fontSize = 14.sp,
                            color = TextTertiary
                        )
                    },
                    singleLine = true,
                    shape = RoundedCornerShape(L),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = PrimaryColor,
                        unfocusedBorderColor = BorderColor,
                        cursorColor = PrimaryColor,
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary
                    ),
                    textStyle = androidx.compose.ui.text.TextStyle(fontSize = 14.sp)
                )
            } else {
                // 标题
                title?.let {
                    Text(
                        text = it,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(L))
            
            // 操作按钮组 - 紧凑排列
            Row(
                horizontalArrangement = Arrangement.spacedBy(S),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 搜索按钮
                ModernActionButton(
                    icon = if (showSearch) Icons.Default.Close else Icons.Default.Search,
                    contentDescription = if (showSearch) "关闭" else "搜索",
                    onClick = onSearchToggle
                )
                
                // 筛选按钮
                ModernActionButton(
                    icon = Icons.Default.FilterList,
                    contentDescription = "筛选",
                    onClick = onFilterClick
                )
                
                // 导入按钮
                ModernActionButton(
                    icon = Icons.Default.CloudDownload,
                    contentDescription = "导入",
                    onClick = onImportClick,
                    tint = SecondaryColor
                )
                
                // 添加按钮 - 主要操作
                ModernActionButton(
                    icon = Icons.Default.Add,
                    contentDescription = "创建",
                    onClick = onAddClick,
                    tint = AccentColor,
                    backgroundColor = AccentColor.copy(alpha = 0.1f)
                )
            }
        }
    }
}

/**
 * 现代化操作按钮
 */
@Composable
private fun ModernActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    contentDescription: String,
    onClick: () -> Unit,
    tint: Color = TextSecondary,
    backgroundColor: Color = Color.Transparent
) {
    Surface(
        modifier = Modifier
            .size(36.dp)
            .clickable { onClick() },
        shape = CircleShape,
        color = backgroundColor
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                tint = tint,
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

/**
 * 现代化模板列表
 */
@Composable
private fun ModernTemplateList(
    templates: List<GoalTemplate>,
    recentTemplates: List<GoalTemplate>,
    popularTemplates: List<GoalTemplate>,
    showSections: Boolean,
    onTemplateClick: (GoalTemplate) -> Unit,
    onEditTemplate: (GoalTemplate) -> Unit,
    onDeleteTemplate: (GoalTemplate) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = XL, vertical = M),
        verticalArrangement = Arrangement.spacedBy(M)
    ) {
        if (showSections) {
            // 最近使用 - 如果有数据
            if (recentTemplates.isNotEmpty()) {
            item {
                    ModernSectionHeader(
                        title = "最近使用",
                        subtitle = "${recentTemplates.size}个模板"
                    )
                }
                items(recentTemplates.take(3)) { template ->
                    CompactTemplateCard(
                            template = template,
                        isRecent = true,
                            onClick = { onTemplateClick(template) },
                        onEdit = { onEditTemplate(template) },
                        onDelete = { onDeleteTemplate(template) }
                    )
                }
                
                item { Spacer(modifier = Modifier.height(L)) }
            }
            
            // 热门模板 - 如果有数据
            if (popularTemplates.isNotEmpty()) {
            item {
                    ModernSectionHeader(
                        title = "热门模板",
                        subtitle = "${popularTemplates.size}个模板"
                    )
                }
                items(popularTemplates.take(3)) { template ->
                    CompactTemplateCard(
                            template = template,
                        isPopular = true,
                            onClick = { onTemplateClick(template) },
                        onEdit = { onEditTemplate(template) },
                        onDelete = { onDeleteTemplate(template) }
                    )
                }
                
                item { Spacer(modifier = Modifier.height(L)) }
        }
        
        // 所有模板
        item {
                ModernSectionHeader(
                    title = "所有模板",
                    subtitle = "${templates.size}个模板"
                )
            }
        }
        
        // 模板列表
        items(templates) { template ->
            CompactTemplateCard(
                template = template,
                onClick = { onTemplateClick(template) },
                onEdit = { onEditTemplate(template) },
                onDelete = { onDeleteTemplate(template) }
            )
        }
    }
}

/**
 * 现代化章节标题
 */
@Composable
private fun ModernSectionHeader(
    title: String,
    subtitle: String? = null
) {
    Column(
            modifier = Modifier
                .fillMaxWidth()
            .padding(vertical = S)
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = TextPrimary
        )
        subtitle?.let {
            Text(
                text = it,
                fontSize = 12.sp,
                color = TextSecondary,
                modifier = Modifier.padding(top = XXS)
            )
        }
    }
}

/**
 * 紧凑型模板卡片 - 大幅缩小，信息密度更高
 */
@Composable
private fun CompactTemplateCard(
    template: GoalTemplate,
    isRecent: Boolean = false,
    isPopular: Boolean = false,
    onClick: () -> Unit,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    // 分类配色
    val categoryColor = remember(template.category) {
        when (template.category.lowercase()) {
            "健身", "运动", "健康" -> Color(0xFF34C759)
            "学习", "教育" -> Color(0xFF007AFF) 
            "工作", "事业" -> Color(0xFF5856D6)
            "阅读" -> Color(0xFFFF9500)
            "财务", "理财" -> Color(0xFFFFD60A)
            "个人成长" -> Color(0xFF32D74B)
            "生活习惯" -> Color(0xFFAF52DE)
            else -> Color(0xFF8E8E93)
        }
    }
    
    val categoryEmoji = remember(template.category) {
        when (template.category.lowercase()) {
            "健身", "运动", "健康" -> "💪"
            "学习", "教育" -> "📚"
            "工作", "事业" -> "💼"
            "阅读" -> "📖"
            "财务", "理财" -> "💰"
            "个人成长" -> "🌱"
            "生活习惯" -> "⏰"
            else -> "🎯"
        }
    }
    
    // 原模板颜色用作按钮颜色
    val templateColor = remember(template.colorHex) {
        getColorFromHex(template.colorHex) ?: DustyLavender
    }
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(L),
        color = CardBackground,
        shadowElevation = 1.dp
    ) {
        Column {
            // 顶部状态条
            if (isRecent || isPopular) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                        .height(XXS)
                    .background(
                            if (isRecent) AccentColor else SecondaryColor
                            )
                        )
            }
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(L),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                // 分类图标
                    Box(
                        modifier = Modifier
                        .size(32.dp)
                        .background(
                            categoryColor.copy(alpha = 0.1f),
                            CircleShape
                        ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                        text = categoryEmoji,
                        fontSize = 14.sp
                    )
                }
                
                Spacer(modifier = Modifier.width(L))
                
                // 主要信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 模板名称
                        Text(
                            text = template.name,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = TextPrimary,
                                maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                            )
                        
                        // 分类标签
                        if (template.category.isNotEmpty()) {
                            Surface(
                                shape = RoundedCornerShape(S),
                                color = categoryColor.copy(alpha = 0.1f)
                            ) {
                            Text(
                                text = template.category,
                                    fontSize = 10.sp,
                                    color = categoryColor,
                                    modifier = Modifier.padding(horizontal = S, vertical = XXS)
                                )
                            }
                        }
                    }
                    
                    // 目标标题
                    if (template.defaultTitle.isNotEmpty()) {
                    Text(
                            text = template.defaultTitle,
                            fontSize = 12.sp,
                            color = TextSecondary,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(top = XXS)
                        )
                    }
                    
                    // 统计信息
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = XS)
                    ) {
                        // 使用次数
                    Text(
                            text = "${template.usageCount}次使用",
                            fontSize = 10.sp,
                            color = TextTertiary
                        )
                        
                        if (template.subTaskTemplates.isNotEmpty()) {
                        Text(
                                text = " • ${template.subTaskTemplates.size}个子任务",
                                fontSize = 10.sp,
                                color = TextTertiary
                            )
                        }
                        
                        if (template.isRecurring) {
                        Text(
                                text = " • 周期性",
                                fontSize = 10.sp,
                                color = TextTertiary
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(M))
                
                // 操作按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(XS)
                ) {
                    // 编辑按钮
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "编辑",
                            tint = TextSecondary,
                            modifier = Modifier.size(14.dp)
                        )
                    }
                    
                    // 删除按钮
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFFF3B30),
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 现代化分类筛选
 */
@Composable
private fun ModernCategoryFilter(
    categories: List<String>,
    selectedCategory: String?,
    onCategorySelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = CardBackground,
        shadowElevation = 0.5.dp
    ) {
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(horizontal = XL, vertical = M),
            horizontalArrangement = Arrangement.spacedBy(S)
        ) {
            items(categories) { category ->
                ModernCategoryChip(
                    category = category,
                    isSelected = category == selectedCategory,
                    onClick = { onCategorySelected(category) }
                )
            }
        }
    }
}

/**
 * 现代化分类标签
 */
@Composable
private fun ModernCategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable { onClick() },
        shape = RoundedCornerShape(L),
        color = if (isSelected) PrimaryColor else Color.Transparent,
        border = if (!isSelected) BorderStroke(0.5.dp, BorderColor) else null
                ) {
                    Text(
            text = category,
            fontSize = 12.sp,
            color = if (isSelected) Color.White else TextSecondary,
            modifier = Modifier.padding(horizontal = L, vertical = S)
        )
    }
}

/**
 * 现代化空状态视图
 */
@Composable
private fun ModernEmptyView(
    onCreateNew: () -> Unit,
    onImport: () -> Unit
) {
                Column(
                    modifier = Modifier
            .fillMaxSize()
            .padding(XXL),
        horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
        // 插图
                    Text(
            text = "📋",
            fontSize = 48.sp
        )
        
        Spacer(modifier = Modifier.height(XL))
        
        Text(
            text = "还没有模板",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = TextPrimary
        )
                    
                    Text(
            text = "创建模板可以快速生成相似目标",
            fontSize = 14.sp,
            color = TextSecondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = S)
        )
        
        Spacer(modifier = Modifier.height(XXL))
        
        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(L)
        ) {
            // 导入按钮
            OutlinedButton(
                onClick = onImport,
                shape = RoundedCornerShape(L),
                border = BorderStroke(1.dp, BorderColor)
                    ) {
                        Icon(
                    imageVector = Icons.Default.CloudDownload,
                            contentDescription = null,
                    modifier = Modifier.size(16.dp)
                        )
                Spacer(modifier = Modifier.width(S))
                        Text(
                    text = "导入模板",
                    fontSize = 14.sp
                )
            }
            
            // 创建按钮
            Button(
                onClick = onCreateNew,
                shape = RoundedCornerShape(L),
                colors = ButtonDefaults.buttonColors(
                    containerColor = AccentColor
                )
                ) {
                    Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(S))
                Text(
                    text = "创建模板",
                    fontSize = 14.sp
                )
            }
        }
    }
}

/**
 * 现代化加载视图
 */
@Composable
private fun ModernLoadingView() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                color = PrimaryColor,
                strokeWidth = 2.dp,
                modifier = Modifier.size(32.dp)
            )
            Spacer(modifier = Modifier.height(L))
            Text(
                text = "加载模板中...",
                fontSize = 14.sp,
                color = TextSecondary
            )
        }
    }
}

/**
 * 现代化错误视图
 */
@Composable
private fun ModernErrorView(
    message: String,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(XXL),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "⚠️",
            fontSize = 48.sp
        )
        
        Spacer(modifier = Modifier.height(XL))
        
        Text(
            text = "加载失败",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = TextPrimary
        )
        
        Text(
            text = message,
            fontSize = 14.sp,
            color = TextSecondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = S)
        )
        
        Spacer(modifier = Modifier.height(XXL))
        
        Button(
            onClick = onRetry,
            shape = RoundedCornerShape(L),
            colors = ButtonDefaults.buttonColors(
                containerColor = PrimaryColor
            )
        ) {
            Text(
                text = "重试",
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 现代化无结果视图
 */
@Composable
private fun ModernNoResultsView(
    onClearFilter: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(XXL),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "🔍",
            fontSize = 48.sp
        )
        
        Spacer(modifier = Modifier.height(XL))
        
        Text(
            text = "没有找到模板",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = TextPrimary
        )
        
        Text(
            text = "尝试调整搜索条件或清除筛选",
            fontSize = 14.sp,
            color = TextSecondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = S)
        )
        
        Spacer(modifier = Modifier.height(XXL))
        
        OutlinedButton(
            onClick = onClearFilter,
            shape = RoundedCornerShape(L),
            border = BorderStroke(1.dp, BorderColor)
        ) {
        Text(
                text = "清除筛选",
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 对话框组件集合
 */
@Composable
private fun DialogComponents(
    // 筛选对话框
    showFilterDialog: Boolean,
    onDismissFilter: () -> Unit,
    filterState: TemplateFilterState,
    availableCategories: List<String>,
    onApplyFilter: (String?, TemplateSortOrder?) -> Unit,
    
    // 删除对话框
    showDeleteDialog: Boolean,
    templateToDelete: GoalTemplate?,
    onConfirmDelete: () -> Unit,
    onDismissDelete: () -> Unit,
    
    // 创建对话框
    showCreateDialog: Boolean,
    selectedTemplate: GoalTemplate?,
    customTitle: String,
    onTitleChange: (String) -> Unit,
    onConfirmCreate: () -> Unit,
    onDismissCreate: () -> Unit,
    
    // 模板库对话框
    showTemplateLibrary: Boolean,
    recommendedTemplates: List<GoalTemplate>,
    onSelectTemplate: (GoalTemplate) -> Unit,
    onDismissLibrary: () -> Unit,
    predefinedCategories: List<String>
) {
    // 筛选对话框
    if (showFilterDialog) {
        FilterDialog(
            currentFilter = filterState,
            onDismiss = onDismissFilter,
            onApplyFilter = onApplyFilter,
            availableCategories = availableCategories,
            predefinedCategories = predefinedCategories
        )
    }
    
    // 删除确认对话框
    if (showDeleteDialog && templateToDelete != null) {
        AlertDialog(
            onDismissRequest = onDismissDelete,
            title = { 
                Text(
                    text = "删除模板",
            fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = { 
                Text(
                    text = "确定要删除模板「${templateToDelete.name}」吗？此操作不可撤销。",
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                TextButton(onClick = onConfirmDelete) {
                    Text(
                        text = "删除",
                        color = Color(0xFFFF3B30),
                        fontSize = 14.sp
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismissDelete) {
                    Text(
                        text = "取消",
                        fontSize = 14.sp
                    )
                }
            }
        )
    }
    
    // 创建目标对话框
    if (showCreateDialog && selectedTemplate != null) {
        AlertDialog(
            onDismissRequest = onDismissCreate,
            title = { 
                Text(
                    text = "从模板创建目标",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Column {
                    Text(
                        text = "基于模板「${selectedTemplate.name}」创建新目标：",
                        fontSize = 14.sp
                    )
                    
                    Spacer(modifier = Modifier.height(L))
                    
                    OutlinedTextField(
                        value = customTitle,
                        onValueChange = onTitleChange,
                        label = { 
                            Text(
                                text = "目标名称",
                                fontSize = 12.sp
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        textStyle = androidx.compose.ui.text.TextStyle(fontSize = 14.sp)
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = onConfirmCreate,
                    enabled = customTitle.isNotBlank()
                ) {
                    Text(
                        text = "创建",
                        fontSize = 14.sp
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismissCreate) {
                    Text(
                        text = "取消",
                        fontSize = 14.sp
                    )
                }
            }
        )
    }
    
    // 模板库对话框
    if (showTemplateLibrary) {
        TemplateLibraryDialog(
            onDismiss = onDismissLibrary,
            onTemplateSelected = onSelectTemplate,
            recommendedTemplates = recommendedTemplates,
            categories = predefinedCategories
        )
    }
}

/**
 * 过滤对话框
 */
@Composable
private fun FilterDialog(
    currentFilter: TemplateFilterState,
    onDismiss: () -> Unit,
    onApplyFilter: (String?, TemplateSortOrder?) -> Unit,
    availableCategories: List<String>,
    predefinedCategories: List<String>
) {
    val allCategories = remember(availableCategories, predefinedCategories) {
        (availableCategories + predefinedCategories).distinct().sorted()
    }
    
    var selectedCategory by remember { mutableStateOf(currentFilter.selectedCategory) }
    var selectedSortOrder by remember { mutableStateOf(currentFilter.sortOrder) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("筛选模板") },
        text = {
            Column {
                Text(
                    text = "分类",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 全部分类选项
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { selectedCategory = null }
                        .padding(vertical = 8.dp)
                ) {
                    RadioButton(
                        selected = selectedCategory == null,
                        onClick = { selectedCategory = null }
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text("全部分类")
                }
                
                // 分类选项列表
                allCategories.forEach { category ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { selectedCategory = category }
                            .padding(vertical = 8.dp)
                    ) {
                        RadioButton(
                            selected = selectedCategory == category,
                            onClick = { selectedCategory = category }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(category)
                    }
                }
                
                Divider(modifier = Modifier.padding(vertical = 16.dp))
                
                Text(
                    text = "排序",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 排序选项
                val sortOptions = listOf(
                    Pair(TemplateSortOrder.NAME_ASC, "名称 (A-Z)"),
                    Pair(TemplateSortOrder.NAME_DESC, "名称 (Z-A)"),
                    Pair(TemplateSortOrder.USAGE_COUNT, "使用次数"),
                    Pair(TemplateSortOrder.RECENTLY_USED, "最近使用"),
                    Pair(TemplateSortOrder.RECENTLY_CREATED, "最近创建")
                )
                
                sortOptions.forEach { (sortOrder, label) ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { selectedSortOrder = sortOrder }
                            .padding(vertical = 8.dp)
                    ) {
                        RadioButton(
                            selected = selectedSortOrder == sortOrder,
                            onClick = { selectedSortOrder = sortOrder }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(label)
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onApplyFilter(selectedCategory, selectedSortOrder) }
            ) {
                Text("应用")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text("取消")
            }
        }
    )
}

/**
 * 新增：模板库对话框
 */
@Composable
private fun TemplateLibraryDialog(
    onDismiss: () -> Unit,
    onTemplateSelected: (GoalTemplate) -> Unit,
    recommendedTemplates: List<GoalTemplate>,
    categories: List<String>
) {
    var selectedCategory by remember { mutableStateOf<String?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    
    // 过滤模板
    val filteredTemplates = remember(recommendedTemplates, selectedCategory, searchQuery) {
        recommendedTemplates.filter { template ->
            (selectedCategory == null || template.category == selectedCategory) &&
            (searchQuery.isEmpty() || 
             template.name.contains(searchQuery, ignoreCase = true) ||
             template.description.contains(searchQuery, ignoreCase = true))
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text(
                    text = "模板库",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "浏览并导入精选模板",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 500.dp)
            ) {
                // 搜索栏
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("搜索模板...") },
                    leadingIcon = { Icon(Icons.Default.Search, contentDescription = null) },
                    singleLine = true,
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = DustyLavender,
                        unfocusedBorderColor = Color(0xFFE2E8F0)
                    )
                )
                
                // 分类筛选横向滚动
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    item {
                        ListCategoryChip(
                            category = "全部",
                            isSelected = selectedCategory == null,
                            onClick = { selectedCategory = null }
                        )
                    }
                    
                    items(categories) { category ->
                        ListCategoryChip(
                            category = category,
                            isSelected = selectedCategory == category,
                            onClick = { selectedCategory = category }
                        )
                    }
                }
                
                // 模板列表
                Text(
                    text = if (selectedCategory != null) "「${selectedCategory}」分类模板" else "推荐模板",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                if (filteredTemplates.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "没有找到符合条件的模板",
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        items(filteredTemplates) { template ->
                            CompactTemplateCard(
                                template = template,
                                onClick = { onTemplateSelected(template) },
                                onEdit = { /* 不做任何操作 */ },
                                onDelete = { /* 不做任何操作 */ }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        },
        properties = DialogProperties(dismissOnClickOutside = true),
        containerColor = Color(0xFFF4F2F4) // 设置模板库对话框的背景色为#f4f2f4
    )
}

/**
 * 新增：分类选择芯片
 */
@Composable
private fun ListCategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        color = if (isSelected) DustyLavender else Color(0xFFF0F0F0),
        border = if (isSelected) null else BorderStroke(1.dp, Color(0xFFE0E0E0))
    ) {
        Text(
            text = category,
            color = if (isSelected) Color.White else Color(0xFF666666),
            fontSize = 14.sp,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

/**
 * 新增：模板库项目
 */
@Composable
private fun TemplateLibraryItem(
    template: GoalTemplate,
    onImport: () -> Unit
) {
    // 使用马卡龙配色方案 - 与TemplateCardCompact保持一致
    val templateColor = remember(template.category.lowercase()) {
        when (template.category.lowercase()) {
            "健身", "运动", "健康" -> Color(0xFFB5E5D0) // 薄荷绿
            "学习" -> Color(0xFFC3E2F7) // 浅蓝
            "工作" -> Color(0xFFD6CDEA) // 淡紫
            "阅读" -> Color(0xFFF6D5CA) // 淡橙
            "财务", "理财" -> Color(0xFFE5D8BE) // 淡黄
            "习惯" -> Color(0xFFD4E2C8) // 淡绿
            "个人成长" -> Color(0xFFD1E1E0) // 淡青
            "心理健康" -> Color(0xFFE2CFDE) // 浅紫红
            "生活习惯" -> Color(0xFFE5E1D9) // 浅灰米
            else -> Color(0xFFD8D8EB) // 默认淡蓝紫
        }
    }
    
    // emoji图标
    val emoji = when (template.category.lowercase()) {
        "健身", "运动", "健康" -> "💪"
        "学习" -> "📚"
        "工作" -> "💼"
        "阅读" -> "📖"
        "财务", "理财" -> "💰"
        "习惯" -> "⏰"
        "个人成长" -> "🌱"
        "心理健康" -> "🧠"
        "生活习惯" -> "🏠"
        else -> "✔️"
    }

    // 卡片
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable(onClick = onImport),
        shape = RoundedCornerShape(M),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // 顶部条
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .background(
                        Brush.horizontalGradient(
                            colors = listOf(
                                templateColor,
                                templateColor.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 图标
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(templateColor.copy(alpha = 0.3f)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = emoji, fontSize = 20.sp)
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 信息
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 名称
                        Text(
                            text = template.name,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 分类
                        if (template.category.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = templateColor.copy(alpha = 0.3f),
                                        shape = RoundedCornerShape(S)
                                    )
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = template.category,
                                    fontSize = 12.sp,
                                    color = Color(0xFF505050)
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 描述
                    if (template.description.isNotEmpty()) {
                        Text(
                            text = template.description,
                            fontSize = 14.sp,
                            color = Color.Gray,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    // 目标预览
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            if (template.defaultTitle.isNotEmpty()) {
                                Text(
                                    text = "目标：${template.defaultTitle}",
                                    fontSize = 12.sp,
                                    color = Color.DarkGray,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                            
                            if (template.defaultDurationDays != null && template.defaultDurationDays > 0) {
                                Text(
                                    text = "周期：${template.defaultDurationDays}天",
                                    fontSize = 12.sp,
                                    color = Color.DarkGray
                                )
                            }
                            
                            if (template.defaultTargetValue != null && template.defaultTargetValue > 0) {
                                Text(
                                    text = "目标值：${template.defaultTargetValue}",
                                    fontSize = 12.sp,
                                    color = Color.DarkGray
                                )
                            }
                            
                            if (template.subTaskTemplates.isNotEmpty()) {
                                Text(
                                    text = "子任务：${template.subTaskTemplates.size}项",
                                    fontSize = 12.sp,
                                    color = Color.DarkGray
                                )
                            }
                        }
                        
                        // 导入按钮
                        Button(
                            onClick = onImport,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = templateColor
                            ),
                            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 6.dp)
                        ) {
                            Text(
                                text = "导入",
                                fontSize = 14.sp,
                                color = Color(0xFF505050)
                            )
                        }
                    }
                }
            }
        }
    }
} 

/**
 * 现代化模板内容区域
 */
@Composable
private fun ModernTemplateContent(
    templates: List<GoalTemplate>,
    recentTemplates: List<GoalTemplate>,
    popularTemplates: List<GoalTemplate>,
    showSections: Boolean,
    onTemplateClick: (GoalTemplate) -> Unit,
    onEditTemplate: (GoalTemplate) -> Unit,
    onDeleteTemplate: (GoalTemplate) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(
            start = XL,
            end = XL,
            top = M,
            bottom = XXL
        ),
        verticalArrangement = Arrangement.spacedBy(L)
    ) {
        if (showSections) {
            // 最近使用的模板
            if (recentTemplates.isNotEmpty()) {
                item {
                    ModernSectionHeader(
                        title = "最近使用",
                        subtitle = "${recentTemplates.size}个模板"
                    )
                }
                
                items(recentTemplates) { template ->
                    CompactTemplateCard(
                        template = template,
                        isRecent = true,
                        onClick = { onTemplateClick(template) },
                        onEdit = { onEditTemplate(template) },
                        onDelete = { onDeleteTemplate(template) }
                    )
                }
                
                item { Spacer(modifier = Modifier.height(M)) }
            }
            
            // 热门模板
            if (popularTemplates.isNotEmpty()) {
                item {
                    ModernSectionHeader(
                        title = "热门模板",
                        subtitle = "${popularTemplates.size}个模板"
                    )
                }
                
                items(popularTemplates) { template ->
                    CompactTemplateCard(
                        template = template,
                        isPopular = true,
                        onClick = { onTemplateClick(template) },
                        onEdit = { onEditTemplate(template) },
                        onDelete = { onDeleteTemplate(template) }
                    )
                }
                
                item { Spacer(modifier = Modifier.height(M)) }
            }
            
            // 所有模板
            if (templates.isNotEmpty()) {
                item {
                    ModernSectionHeader(
                        title = "所有模板",
                        subtitle = "${templates.size}个模板"
                    )
                }
            }
        }
        
        // 模板列表
        items(templates) { template ->
            CompactTemplateCard(
                template = template,
                onClick = { onTemplateClick(template) },
                onEdit = { onEditTemplate(template) },
                onDelete = { onDeleteTemplate(template) }
            )
        }
    }
}

/**
 * 现代化段落标题
 */
@Composable
private fun ModernSectionHeader(
    title: String,
    subtitle: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = S),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = TextPrimary
            )
            
            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = TextSecondary
            )
        }
    }
}

// 重复的函数已删除 - 使用上面已定义的版本