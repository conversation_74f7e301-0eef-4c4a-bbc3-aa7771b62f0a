package com.timeflow.app.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.ui.screen.settings.AutoBackupFrequency
import com.timeflow.app.ui.screen.settings.BackupInfo
import com.timeflow.app.utils.DatabaseBackupManager
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 备份设置ViewModel
 * 用于管理数据库备份设置和操作
 */
@HiltViewModel
class BackupSettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : ViewModel() {
    
    private val backupManager = DatabaseBackupManager(context)
    
    // 备份设置状态
    private val _backupSettings = MutableStateFlow(BackupSettings())
    val backupSettings: StateFlow<BackupSettings> = _backupSettings.asStateFlow()
    
    // 备份信息状态
    private val _backupInfo = MutableStateFlow(BackupInfo())
    val backupInfo: StateFlow<BackupInfo> = _backupInfo.asStateFlow()
    
    // 备份文件列表
    private val _backupFiles = MutableStateFlow<List<DatabaseBackupManager.BackupFileInfo>>(emptyList())
    val backupFiles: StateFlow<List<DatabaseBackupManager.BackupFileInfo>> = _backupFiles.asStateFlow()
    
    // 恢复操作状态
    private val _isRestoring = MutableStateFlow(false)
    val isRestoring: StateFlow<Boolean> = _isRestoring.asStateFlow()
    
    // 备份操作状态
    private val _isBackingUp = MutableStateFlow(false)
    val isBackingUp: StateFlow<Boolean> = _isBackingUp.asStateFlow()
    
    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    init {
        // 初始化时加载备份设置和信息
        loadBackupSettings()
        refreshBackupInfo()
    }
    
    /**
     * 加载备份设置
     */
    private fun loadBackupSettings() {
        viewModelScope.launch {
            try {
                val settings = backupManager.getBackupSettings()
                _backupSettings.value = BackupSettings(
                    isAutoBackupEnabled = settings.isAutoBackupEnabled,
                    autoBackupFrequency = when (settings.autoBackupFrequency) {
                        "daily" -> AutoBackupFrequency.DAILY
                        "weekly" -> AutoBackupFrequency.WEEKLY
                        "monthly" -> AutoBackupFrequency.MONTHLY
                        else -> AutoBackupFrequency.WEEKLY
                    },
                    maxBackupCount = settings.maxBackupCount
                )
            } catch (e: Exception) {
                _errorMessage.value = "加载备份设置失败: ${e.message}"
            }
        }
    }
    
    /**
     * 刷新备份信息
     */
    private fun refreshBackupInfo() {
        viewModelScope.launch {
            try {
                val info = backupManager.getBackupInfo()
                _backupInfo.value = BackupInfo(
                    backupCount = info.backupCount,
                    totalSizeBytes = info.totalSizeBytes,
                    lastBackupTime = info.lastBackupTime
                )
                _backupFiles.value = info.backupFiles.sortedByDescending { it.timestamp }
            } catch (e: Exception) {
                _errorMessage.value = "获取备份信息失败: ${e.message}"
            }
        }
    }
    
    /**
     * 执行手动备份
     */
    fun manualBackup() {
        viewModelScope.launch {
            _isBackingUp.value = true
            try {
                val success = backupManager.backupDatabase()
                if (success) {
                    refreshBackupInfo()
                } else {
                    _errorMessage.value = "备份失败"
                }
            } catch (e: Exception) {
                _errorMessage.value = "备份失败: ${e.message}"
            } finally {
                _isBackingUp.value = false
            }
        }
    }
    
    /**
     * 切换自动备份开关
     */
    fun toggleAutoBackup(enabled: Boolean) {
        viewModelScope.launch {
            try {
                val currentSettings = _backupSettings.value
                val newSettings = currentSettings.copy(isAutoBackupEnabled = enabled)
                _backupSettings.value = newSettings
                
                backupManager.updateBackupSettings(
                    isAutoBackupEnabled = enabled,
                    autoBackupFrequency = when (newSettings.autoBackupFrequency) {
                        AutoBackupFrequency.DAILY -> "daily"
                        AutoBackupFrequency.WEEKLY -> "weekly"
                        AutoBackupFrequency.MONTHLY -> "monthly"
                    },
                    maxBackupCount = newSettings.maxBackupCount
                )
            } catch (e: Exception) {
                _errorMessage.value = "更新自动备份设置失败: ${e.message}"
            }
        }
    }
    
    /**
     * 更新自动备份频率
     */
    fun updateAutoBackupFrequency(frequency: AutoBackupFrequency) {
        viewModelScope.launch {
            try {
                val currentSettings = _backupSettings.value
                val newSettings = currentSettings.copy(autoBackupFrequency = frequency)
                _backupSettings.value = newSettings
                
                backupManager.updateBackupSettings(
                    isAutoBackupEnabled = newSettings.isAutoBackupEnabled,
                    autoBackupFrequency = when (frequency) {
                        AutoBackupFrequency.DAILY -> "daily"
                        AutoBackupFrequency.WEEKLY -> "weekly"
                        AutoBackupFrequency.MONTHLY -> "monthly"
                    },
                    maxBackupCount = newSettings.maxBackupCount
                )
            } catch (e: Exception) {
                _errorMessage.value = "更新备份频率失败: ${e.message}"
            }
        }
    }
    
    /**
     * 更新最大备份数量
     */
    fun updateMaxBackupCount(count: Int) {
        viewModelScope.launch {
            try {
                val currentSettings = _backupSettings.value
                val newSettings = currentSettings.copy(maxBackupCount = count)
                _backupSettings.value = newSettings
                
                backupManager.updateBackupSettings(
                    isAutoBackupEnabled = newSettings.isAutoBackupEnabled,
                    autoBackupFrequency = when (newSettings.autoBackupFrequency) {
                        AutoBackupFrequency.DAILY -> "daily"
                        AutoBackupFrequency.WEEKLY -> "weekly"
                        AutoBackupFrequency.MONTHLY -> "monthly"
                    },
                    maxBackupCount = count
                )
                
                // 更新后刷新备份信息
                refreshBackupInfo()
            } catch (e: Exception) {
                _errorMessage.value = "更新最大备份数量失败: ${e.message}"
            }
        }
    }
    
    /**
     * 删除指定备份文件
     */
    fun deleteBackup(fileName: String) {
        viewModelScope.launch {
            try {
                val success = backupManager.deleteBackup(fileName)
                if (success) {
                    refreshBackupInfo()
                } else {
                    _errorMessage.value = "删除备份失败"
                }
            } catch (e: Exception) {
                _errorMessage.value = "删除备份失败: ${e.message}"
            }
        }
    }
    
    /**
     * 恢复到指定备份版本
     */
    fun restoreBackup(fileName: String) {
        viewModelScope.launch {
            _isRestoring.value = true
            try {
                val success = backupManager.restoreBackup(fileName)
                if (success) {
                    _errorMessage.value = "恢复备份成功，请重启应用以应用更改"
                } else {
                    _errorMessage.value = "恢复备份失败"
                }
            } catch (e: Exception) {
                _errorMessage.value = "恢复备份失败: ${e.message}"
            } finally {
                _isRestoring.value = false
            }
        }
    }
    
    /**
     * 恢复到上一个版本的数据
     */
    fun restoreToPreviousVersion() {
        viewModelScope.launch {
            _isRestoring.value = true
            try {
                // 获取所有备份文件
                val backupFiles = _backupFiles.value
                if (backupFiles.isEmpty()) {
                    _errorMessage.value = "没有可用的备份文件"
                    return@launch
                }
                
                // 获取当前版本
                val currentVersion = backupFiles.firstOrNull()?.version ?: 0
                
                // 查找上一个版本的备份文件
                val previousVersionBackup = backupFiles
                    .filter { it.version < currentVersion }
                    .maxByOrNull { it.version }
                
                if (previousVersionBackup == null) {
                    _errorMessage.value = "没有找到上一个版本的备份"
                    return@launch
                }
                
                // 恢复到上一个版本
                val success = backupManager.restoreBackup(previousVersionBackup.fileName)
                if (success) {
                    _errorMessage.value = "已恢复到上一个版本(${previousVersionBackup.version})，请重启应用以应用更改"
                } else {
                    _errorMessage.value = "恢复到上一个版本失败"
                }
            } catch (e: Exception) {
                _errorMessage.value = "恢复到上一个版本失败: ${e.message}"
            } finally {
                _isRestoring.value = false
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

/**
 * 备份设置数据类
 */
data class BackupSettings(
    val isAutoBackupEnabled: Boolean = false,
    val autoBackupFrequency: AutoBackupFrequency = AutoBackupFrequency.WEEKLY,
    val maxBackupCount: Int = 5
)