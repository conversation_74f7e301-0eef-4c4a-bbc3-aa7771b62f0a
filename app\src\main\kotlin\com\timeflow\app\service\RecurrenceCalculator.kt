package com.timeflow.app.service

import android.util.Log
import com.timeflow.app.data.model.RecurrenceSettings
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 循环任务时间计算器
 * 负责计算下一个循环任务的时间
 */
@Singleton
class RecurrenceCalculator @Inject constructor() {
    
    companion object {
        private const val TAG = "RecurrenceCalculator"
    }

    /**
     * 计算下一次出现的时间
     */
    fun calculateNextOccurrence(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime? {
        return try {
            when (settings.type) {
                "DAILY" -> calculateDailyNext(baseDateTime, settings)
                "WEEKLY" -> calculateWeeklyNext(baseDateTime, settings)
                "MONTHLY" -> calculateMonthlyNext(baseDateTime, settings)
                "YEARLY" -> calculateYearlyNext(baseDateTime, settings)
                "CUSTOM" -> calculateCustomNext(baseDateTime, settings)
                else -> null
            }
        } catch (e: Exception) {
            Log.e(TAG, "计算下一次循环时间失败: ${settings.type}", e)
            null
        }
    }

    /**
     * 计算每日循环的下一次时间
     */
    private fun calculateDailyNext(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        return baseDateTime.plusDays(settings.interval.toLong())
    }

    /**
     * 计算每周循环的下一次时间
     */
    private fun calculateWeeklyNext(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        return if (settings.weekdays.isNotEmpty()) {
            // 指定了星期几
            calculateNextWeekday(baseDateTime, settings)
        } else {
            // 没有指定星期几，按周间隔
            baseDateTime.plusWeeks(settings.interval.toLong())
        }
    }

    /**
     * 计算下一个指定星期几的时间
     */
    private fun calculateNextWeekday(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        val currentDayOfWeek = baseDateTime.dayOfWeek.value
        val targetWeekdays = settings.weekdays.sorted()
        
        // 查找本周内下一个目标星期几
        val nextWeekdayInCurrentWeek = targetWeekdays.find { it > currentDayOfWeek }
        
        return if (nextWeekdayInCurrentWeek != null) {
            // 本周内有下一个目标日
            val daysToAdd = nextWeekdayInCurrentWeek - currentDayOfWeek
            baseDateTime.plusDays(daysToAdd.toLong())
        } else {
            // 本周内没有，找下一周的第一个目标日
            val firstTargetWeekday = targetWeekdays.first()
            val daysToNextWeek = 7 - currentDayOfWeek + firstTargetWeekday
            baseDateTime.plusDays(daysToNextWeek.toLong())
                .plusWeeks((settings.interval - 1).toLong())
        }
    }

    /**
     * 计算每月循环的下一次时间
     */
    private fun calculateMonthlyNext(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        return if (settings.monthDay != null) {
            // 指定了每月的第几天
            calculateNextMonthDay(baseDateTime, settings)
        } else {
            // 没有指定日期，按月间隔
            baseDateTime.plusMonths(settings.interval.toLong())
        }
    }

    /**
     * 计算下一个指定月份日期的时间
     */
    private fun calculateNextMonthDay(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        val targetDay = settings.monthDay!!
        val currentMonth = baseDateTime.toLocalDate()
        
        // 尝试在当前月份设置目标日期
        val targetDateInCurrentMonth = try {
            currentMonth.withDayOfMonth(targetDay)
        } catch (e: Exception) {
            // 当前月份没有这一天（如2月30日），跳到下个月
            null
        }
        
        return if (targetDateInCurrentMonth != null && 
                   targetDateInCurrentMonth.isAfter(currentMonth)) {
            // 当前月份的目标日期还未到
            baseDateTime.withDayOfMonth(targetDay)
        } else {
            // 当前月份的目标日期已过或不存在，计算下个月
            val nextMonth = baseDateTime.plusMonths(settings.interval.toLong())
            try {
                nextMonth.withDayOfMonth(targetDay)
            } catch (e: Exception) {
                // 下个月也没有这一天，使用月末
                nextMonth.with(TemporalAdjusters.lastDayOfMonth())
            }
        }
    }

    /**
     * 计算每年循环的下一次时间
     */
    private fun calculateYearlyNext(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        val nextYear = baseDateTime.plusYears(settings.interval.toLong())
        
        return try {
            // 尝试保持相同的月日
            nextYear.withMonth(baseDateTime.monthValue)
                .withDayOfMonth(baseDateTime.dayOfMonth)
        } catch (e: Exception) {
            // 如果日期不存在（如2月29日在非闰年），使用月末
            nextYear.withMonth(baseDateTime.monthValue)
                .with(TemporalAdjusters.lastDayOfMonth())
        }
    }

    /**
     * 计算自定义循环的下一次时间
     */
    private fun calculateCustomNext(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): LocalDateTime {
        // 自定义循环逻辑，可以根据需要扩展
        return when {
            settings.weekdays.isNotEmpty() -> {
                // 基于星期几的自定义循环
                calculateNextWeekday(baseDateTime, settings)
            }
            settings.monthDay != null -> {
                // 基于月份日期的自定义循环
                calculateNextMonthDay(baseDateTime, settings)
            }
            else -> {
                // 默认按天间隔
                baseDateTime.plusDays(settings.interval.toLong())
            }
        }
    }

    /**
     * 计算循环任务的所有未来实例（用于预览）
     */
    fun calculateFutureOccurrences(
        baseDateTime: LocalDateTime,
        settings: RecurrenceSettings,
        maxCount: Int = 10
    ): List<LocalDateTime> {
        val occurrences = mutableListOf<LocalDateTime>()
        var currentDateTime = baseDateTime
        
        repeat(maxCount) {
            val nextDateTime = calculateNextOccurrence(currentDateTime, settings)
            if (nextDateTime != null) {
                // 检查是否超过结束条件
                if (isAfterEndCondition(nextDateTime, settings)) {
                    return@repeat
                }
                
                occurrences.add(nextDateTime)
                currentDateTime = nextDateTime
            } else {
                return@repeat
            }
        }
        
        return occurrences
    }

    /**
     * 检查是否超过结束条件
     */
    private fun isAfterEndCondition(
        dateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): Boolean {
        return when (settings.endType) {
            "DATE" -> {
                val endDate = settings.endDate?.let { LocalDateTime.parse(it) }
                endDate != null && dateTime.isAfter(endDate)
            }
            "COUNT" -> {
                // 这里需要额外的上下文来判断已经生成的次数
                // 在实际使用中，应该从外部传入当前计数
                false
            }
            else -> false // NEVER
        }
    }

    /**
     * 计算两个时间之间的循环次数
     */
    fun calculateOccurrencesBetween(
        startDateTime: LocalDateTime,
        endDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): Int {
        var count = 0
        var currentDateTime = startDateTime
        
        while (currentDateTime.isBefore(endDateTime)) {
            val nextDateTime = calculateNextOccurrence(currentDateTime, settings)
            if (nextDateTime == null || nextDateTime.isAfter(endDateTime)) {
                break
            }
            count++
            currentDateTime = nextDateTime
        }
        
        return count
    }

    /**
     * 验证循环设置是否有效
     */
    fun validateRecurrenceSettings(settings: RecurrenceSettings): Boolean {
        return try {
            when (settings.type) {
                "DAILY" -> settings.interval > 0
                "WEEKLY" -> settings.interval > 0 && 
                           (settings.weekdays.isEmpty() || 
                            settings.weekdays.all { it in 1..7 })
                "MONTHLY" -> settings.interval > 0 && 
                            (settings.monthDay == null || 
                             settings.monthDay in 1..31)
                "YEARLY" -> settings.interval > 0
                "CUSTOM" -> true // 自定义规则的验证可以更复杂
                else -> false
            }
        } catch (e: Exception) {
            Log.e(TAG, "验证循环设置失败", e)
            false
        }
    }
}
