package com.timeflow.app.data.repository

import android.util.Log
import com.timeflow.app.data.dao.CycleDao
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 历史经期数据管理仓库
 * 专门处理历史数据的导入、验证、冲突解决和统计分析
 */
@Singleton
class HistoryPeriodRepository @Inject constructor(
    private val cycleDao: CycleDao
) {
    companion object {
        private const val TAG = "HistoryPeriodRepository"
    }

    /**
     * 批量导入历史经期数据
     */
    suspend fun batchImportHistoryPeriods(
        historyRecords: List<HistoryPeriodModel>,
        conflictStrategy: ConflictResolutionStrategy = ConflictResolutionStrategy.ASK_USER
    ): BatchImportResult {
        Log.d(TAG, "开始批量导入 ${historyRecords.size} 条历史记录")
        
        val results = mutableListOf<HistoryPeriodModel>()
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        var successCount = 0
        var failCount = 0
        var conflictCount = 0
        
        // 获取现有数据
        val existingCycles = cycleDao.getAllCycles().first()
        
        for (historyRecord in historyRecords) {
            try {
                // 验证数据
                val validation = historyRecord.validate()
                if (!validation.isValid) {
                    errors.addAll(validation.errors.map { "${historyRecord.getDisplayText()}: $it" })
                    failCount++
                    continue
                }
                
                warnings.addAll(validation.warnings.map { "${historyRecord.getDisplayText()}: $it" })
                
                // 检查冲突
                val conflicts = detectConflicts(historyRecord, existingCycles)
                
                if (conflicts.isNotEmpty()) {
                    conflictCount++
                    when (conflictStrategy) {
                        ConflictResolutionStrategy.SKIP_CONFLICTING -> {
                            warnings.add("跳过冲突记录: ${historyRecord.getDisplayText()}")
                            continue
                        }
                        ConflictResolutionStrategy.REPLACE_EXISTING -> {
                            // 删除冲突的现有记录
                            for (conflict in conflicts) {
                                cycleDao.deleteCycle(conflict.existingRecord)
                            }
                        }
                        ConflictResolutionStrategy.MERGE_DATA -> {
                            // 合并数据逻辑
                            val mergedRecord = mergeConflictingData(historyRecord, conflicts)
                            if (mergedRecord != null) {
                                insertHistoryRecord(mergedRecord)
                                results.add(mergedRecord)
                                successCount++
                                continue
                            }
                        }
                        ConflictResolutionStrategy.ASK_USER -> {
                            // 标记为需要用户决策
                            warnings.add("需要用户决策的冲突: ${historyRecord.getDisplayText()}")
                            continue
                        }
                    }
                }
                
                // 插入记录
                insertHistoryRecord(historyRecord)
                results.add(historyRecord)
                successCount++
                
            } catch (e: Exception) {
                Log.e(TAG, "导入记录失败: ${historyRecord.getDisplayText()}", e)
                errors.add("导入失败: ${historyRecord.getDisplayText()} - ${e.message}")
                failCount++
            }
        }
        
        Log.d(TAG, "批量导入完成: 成功 $successCount, 失败 $failCount, 冲突 $conflictCount")
        
        return BatchImportResult(
            totalRecords = historyRecords.size,
            successfulImports = successCount,
            failedImports = failCount,
            conflictResolutions = conflictCount,
            warnings = warnings,
            errors = errors,
            importedRecords = results
        )
    }
    
    /**
     * 检测数据冲突
     */
    private fun detectConflicts(
        newRecord: HistoryPeriodModel,
        existingCycles: List<CycleRecord>
    ): List<DataConflict> {
        val conflicts = mutableListOf<DataConflict>()
        
        for (existing in existingCycles) {
            val existingStart = existing.startDate
            val existingEnd = existing.endDate ?: existingStart.plusDays(
                (existing.periodLength ?: 5).toLong() - 1
            )
            
            // 检查日期重叠
            val hasOverlap = !(newRecord.endDate.isBefore(existingStart) || 
                              newRecord.startDate.isAfter(existingEnd))
            
            if (hasOverlap) {
                val conflictType = when {
                    newRecord.startDate == existingStart && newRecord.endDate == existingEnd -> 
                        ConflictType.EXACT_DUPLICATE
                    newRecord.startDate == existingStart || newRecord.endDate == existingEnd -> 
                        ConflictType.PARTIAL_OVERLAP
                    else -> ConflictType.DATE_OVERLAP
                }
                
                conflicts.add(DataConflict(
                    newRecord = newRecord,
                    existingRecord = existing,
                    conflictType = conflictType,
                    description = "日期范围冲突: 新记录 ${newRecord.getDisplayText()} 与现有记录重叠"
                ))
            }
        }
        
        return conflicts
    }
    
    /**
     * 合并冲突数据
     */
    private fun mergeConflictingData(
        newRecord: HistoryPeriodModel,
        conflicts: List<DataConflict>
    ): HistoryPeriodModel? {
        // 简单的合并策略：取日期范围的并集
        if (conflicts.size == 1) {
            val existing = conflicts.first().existingRecord
            val mergedStart = minOf(newRecord.startDate, existing.startDate)
            val mergedEnd = maxOf(newRecord.endDate, existing.endDate ?: existing.startDate)
            
            return newRecord.copy(
                startDate = mergedStart,
                endDate = mergedEnd,
                notes = "${newRecord.notes ?: ""} [合并: ${existing.notes ?: ""}]".trim()
            )
        }
        
        return null
    }
    
    /**
     * 插入历史记录
     */
    private suspend fun insertHistoryRecord(historyRecord: HistoryPeriodModel) {
        val cycleRecord = CycleRecord(
            startDate = historyRecord.startDate,
            endDate = historyRecord.endDate,
            periodLength = historyRecord.periodLength,
            notes = buildString {
                append(historyRecord.notes ?: "")
                if (historyRecord.source != HistoryDataSource.MANUAL_INPUT) {
                    append(" [${historyRecord.source.displayName}]")
                }
            }.trim().takeIf { it.isNotEmpty() },
            createdAt = historyRecord.createdAt
        )
        
        cycleDao.insertCycle(cycleRecord)
        Log.d(TAG, "插入历史记录: ${historyRecord.getDisplayText()}")
    }
    
    /**
     * 分析历史数据并生成统计报告
     */
    suspend fun analyzeHistoryData(): HistoryAnalysisResult {
        val cycles = cycleDao.getAllCycles().first()
        
        if (cycles.isEmpty()) {
            return HistoryAnalysisResult(
                totalPeriods = 0,
                averageCycleLength = 0.0,
                averagePeriodLength = 0.0,
                cycleRegularity = CycleRegularityLevel.INSUFFICIENT_DATA,
                longestCycle = 0,
                shortestCycle = 0,
                longestPeriod = 0,
                shortestPeriod = 0,
                dataQualityScore = 0.0,
                recommendations = listOf("请添加更多经期记录以获得准确的分析结果")
            )
        }
        
        // 计算周期长度
        val cycleLengths = calculateCycleLengths(cycles)
        val periodLengths = cycles.mapNotNull { it.periodLength }
        
        // 基本统计
        val avgCycleLength = cycleLengths.average()
        val avgPeriodLength = periodLengths.average()
        
        // 规律性分析
        val cycleVariability = if (cycleLengths.size > 1) {
            val variance = cycleLengths.map { (it - avgCycleLength) * (it - avgCycleLength) }.average()
            kotlin.math.sqrt(variance)
        } else 0.0
        
        val regularity = when {
            cycleLengths.size < 3 -> CycleRegularityLevel.INSUFFICIENT_DATA
            cycleVariability < 3 -> CycleRegularityLevel.VERY_REGULAR
            cycleVariability < 7 -> CycleRegularityLevel.REGULAR
            cycleVariability < 14 -> CycleRegularityLevel.SOMEWHAT_IRREGULAR
            else -> CycleRegularityLevel.IRREGULAR
        }
        
        // 数据质量评分
        val dataQuality = calculateDataQualityScore(cycles, cycleLengths, periodLengths)
        
        // 生成建议
        val recommendations = generateRecommendations(cycles, regularity, dataQuality)
        
        return HistoryAnalysisResult(
            totalPeriods = cycles.size,
            averageCycleLength = avgCycleLength,
            averagePeriodLength = avgPeriodLength,
            cycleRegularity = regularity,
            longestCycle = cycleLengths.maxOrNull() ?: 0,
            shortestCycle = cycleLengths.minOrNull() ?: 0,
            longestPeriod = periodLengths.maxOrNull() ?: 0,
            shortestPeriod = periodLengths.minOrNull() ?: 0,
            dataQualityScore = dataQuality,
            recommendations = recommendations
        )
    }
    
    /**
     * 计算周期长度
     */
    private fun calculateCycleLengths(cycles: List<CycleRecord>): List<Int> {
        val sortedCycles = cycles.sortedBy { it.startDate }
        val cycleLengths = mutableListOf<Int>()
        
        for (i in 1 until sortedCycles.size) {
            val currentCycle = sortedCycles[i]
            val previousCycle = sortedCycles[i - 1]
            
            val cycleLength = ChronoUnit.DAYS.between(
                previousCycle.startDate,
                currentCycle.startDate
            ).toInt()
            
            // 只包含合理的周期长度
            if (cycleLength in 15..50) {
                cycleLengths.add(cycleLength)
            }
        }
        
        return cycleLengths
    }
    
    /**
     * 计算数据质量评分
     */
    private fun calculateDataQualityScore(
        cycles: List<CycleRecord>,
        cycleLengths: List<Int>,
        periodLengths: List<Int>
    ): Double {
        var score = 0.0
        
        // 数据完整性 (40%)
        val completenessScore = when {
            cycles.size >= 12 -> 1.0  // 一年以上数据
            cycles.size >= 6 -> 0.8   // 半年以上数据
            cycles.size >= 3 -> 0.6   // 三个月以上数据
            else -> 0.3
        }
        score += completenessScore * 0.4
        
        // 数据一致性 (30%)
        val consistencyScore = if (cycleLengths.isNotEmpty()) {
            val variance = cycleLengths.map { it - cycleLengths.average() }.map { it * it }.average()
            val cv = kotlin.math.sqrt(variance) / cycleLengths.average()
            when {
                cv < 0.1 -> 1.0
                cv < 0.2 -> 0.8
                cv < 0.3 -> 0.6
                else -> 0.3
            }
        } else 0.0
        score += consistencyScore * 0.3
        
        // 记录详细程度 (30%)
        val detailScore = cycles.count { it.endDate != null && it.periodLength != null }.toDouble() / cycles.size
        score += detailScore * 0.3
        
        return score.coerceIn(0.0, 1.0)
    }
    
    /**
     * 生成个性化建议
     */
    private fun generateRecommendations(
        cycles: List<CycleRecord>,
        regularity: CycleRegularityLevel,
        dataQuality: Double
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于数据质量的建议
        when {
            dataQuality < 0.5 -> {
                recommendations.add("建议补充更多历史数据以提高分析准确性")
                recommendations.add("记录时请包含完整的开始和结束日期")
            }
            dataQuality < 0.8 -> {
                recommendations.add("数据质量良好，继续保持记录习惯")
            }
            else -> {
                recommendations.add("数据质量优秀，预测准确性很高")
            }
        }
        
        // 基于规律性的建议
        when (regularity) {
            CycleRegularityLevel.IRREGULAR -> {
                recommendations.add("周期不规律，建议咨询妇科医生")
                recommendations.add("记录更多症状信息有助于找出影响因素")
            }
            CycleRegularityLevel.SOMEWHAT_IRREGULAR -> {
                recommendations.add("周期轻度不规律，注意生活方式的影响")
            }
            CycleRegularityLevel.VERY_REGULAR -> {
                recommendations.add("周期非常规律，身体状况良好")
            }
            else -> {}
        }
        
        return recommendations
    }
}
