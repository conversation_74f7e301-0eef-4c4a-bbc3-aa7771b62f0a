package com.timeflow.app.ui.theme

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.timeflow.app.R

/**
 * 动态主题助手类 - 用于管理应用的 Material3 动态主题
 */
object DynamicThemeHelper {
    // 默认亮色主题
    private val LightColors = lightColorScheme(
        primary = Color(0xFF6A45C9),
        onPrimary = Color(0xFFFFFFFF),
        primaryContainer = Color(0xFFF0EBFF),
        onPrimaryContainer = Color(0xFF22005D),
        
        secondary = Color(0xFF03DAC5),
        onSecondary = Color(0xFF000000),
        secondaryContainer = Color(0xFFB3F1EA),
        onSecondaryContainer = Color(0xFF002018),
        
        tertiary = Color(0xFF4A2F8F),
        onTertiary = Color(0xFFFFFFFF),
        
        background = Color(0xFFFFFFFF),
        onBackground = Color(0xFF000000),
        surface = Color(0xFFFFFBFF),
        onSurface = Color(0xFF1C1B1E)
    )
    
    // 默认暗色主题
    private val DarkColors = darkColorScheme(
        primary = Color(0xFFF0EBFF),
        onPrimary = Color(0xFF381E72),
        primaryContainer = Color(0xFF4A2F8F),
        onPrimaryContainer = Color(0xFFE9DDFF),
        
        secondary = Color(0xFFB3F1EA),
        onSecondary = Color(0xFF00382F),
        secondaryContainer = Color(0xFF018786),
        onSecondaryContainer = Color(0xFFA2FCF0),
        
        tertiary = Color(0xFFD6BAFF),
        onTertiary = Color(0xFF332D41),
        
        background = Color(0xFF1C1B1E),
        onBackground = Color(0xFFE6E1E6),
        surface = Color(0xFF1C1B1E),
        onSurface = Color(0xFFE6E1E6)
    )
    
    /**
     * 检查是否支持动态主题
     * @return 如果系统支持动态主题，则返回 true
     */
    fun supportsDynamicTheming() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    
    /**
     * 检查当前是否是暗色模式
     * @param context 上下文
     * @return 如果当前是暗色模式，则返回 true
     */
    fun isDarkMode(context: Context): Boolean {
        val uiMode = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return uiMode == Configuration.UI_MODE_NIGHT_YES
    }
    
    /**
     * 获取应用的颜色方案
     * @param context 上下文
     * @param isDark 是否是暗色模式
     * @param useDynamicTheming 是否使用动态主题
     * @return 颜色方案
     */
    @Composable
    fun getAppColorScheme(
        context: Context = LocalContext.current,
        isDark: Boolean = isSystemInDarkTheme(),
        useDynamicTheming: Boolean = true
    ): ColorScheme {
        // 如果支持动态主题并且用户选择使用动态主题
        val supportsDynamic = supportsDynamicTheming() && useDynamicTheming
        
        return when {
            supportsDynamic && isDark -> dynamicDarkColorScheme(context)
            supportsDynamic && !isDark -> dynamicLightColorScheme(context)
            isDark -> DarkColors
            else -> LightColors
        }
    }
}

/**
 * 应用 Material3 主题
 */
@Composable
fun AppTheme(
    isDark: Boolean = isSystemInDarkTheme(),
    useDynamicTheming: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = DynamicThemeHelper.getAppColorScheme(
        isDark = isDark,
        useDynamicTheming = useDynamicTheming
    )
    
    MaterialTheme(
        colorScheme = colorScheme,
        content = content
    )
} 