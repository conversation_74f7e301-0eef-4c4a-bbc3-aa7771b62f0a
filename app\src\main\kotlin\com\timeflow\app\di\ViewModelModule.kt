package com.timeflow.app.di

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.timeflow.app.ui.screen.task.TaskDetailViewModel
import com.timeflow.app.ui.task.KanbanViewModel
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.timetracking.TimeTrackingViewModel
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityRetainedComponent
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ViewModelScoped
import dagger.multibindings.IntoMap

/**
 * ViewModel注入模块 - 使用Hilt的标准方式注入ViewModel
 * 
 * 注意：不需要手动创建ViewModel，Hilt会自动处理
 * ViewModel类需要使用@HiltViewModel注解，并使用@Inject标注构造函数
 */
@Module
@InstallIn(ViewModelComponent::class)
abstract class ViewModelModule {

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(TaskListViewModel::class)
    abstract fun bindTaskListViewModel(viewModel: TaskListViewModel): ViewModel

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(TaskDetailViewModel::class)
    abstract fun bindTaskDetailViewModel(viewModel: TaskDetailViewModel): ViewModel

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(KanbanViewModel::class)
    abstract fun bindKanbanViewModel(viewModel: KanbanViewModel): ViewModel

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(TimeFlowViewModel::class)
    abstract fun bindTimeFlowViewModel(viewModel: TimeFlowViewModel): ViewModel

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(TimeTrackingViewModel::class)
    abstract fun bindTimeTrackingViewModel(viewModel: TimeTrackingViewModel): ViewModel

    @Binds
    @ViewModelScoped
    @IntoMap
    @ViewModelKey(AiAssistantViewModel::class)
    abstract fun bindAiAssistantViewModel(viewModel: AiAssistantViewModel): ViewModel
} 