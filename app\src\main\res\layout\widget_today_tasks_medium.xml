<?xml version="1.0" encoding="utf-8"?>
<!-- 今日待办小组件 (2x2) - 参考图片重新设计 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_card_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 顶部区域：日期和emoji -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp">

        <!-- 左侧日期信息 -->
        <LinearLayout
            android:id="@+id/widget_date_section"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_alignParentStart="true">

            <TextView
                android:id="@+id/widget_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="22"
                android:textSize="32sp"
                android:textStyle="bold"
                android:textColor="@color/widget_today_text_primary_light"
                android:fontFamily="sans-serif-light" />

            <TextView
                android:id="@+id/widget_weekday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="周四"
                android:textSize="14sp"
                android:textColor="@color/widget_today_text_secondary_light"
                android:fontFamily="sans-serif-medium"
                android:layout_marginStart="6dp"
                android:layout_marginTop="8dp" />

        </LinearLayout>

        <!-- 右侧心情emoji -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/widget_emoji_background"
            android:padding="8dp">

            <TextView
                android:id="@+id/widget_mood_emoji"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="😊"
                android:textSize="18sp" />

        </LinearLayout>

    </RelativeLayout>

    <!-- 任务列表区域 -->
    <LinearLayout
        android:id="@+id/widget_tasks_section"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 任务1 -->
        <LinearLayout
            android:id="@+id/widget_task_container_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- 复选框 -->
            <View
                android:id="@+id/widget_checkbox_1"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="@drawable/widget_checkbox_unchecked"
                android:layout_marginEnd="8dp" />

            <!-- 优先级颜色条 -->
            <View
                android:id="@+id/widget_priority_bar_1"
                android:layout_width="3dp"
                android:layout_height="16dp"
                android:background="@color/widget_priority_high"
                android:layout_marginEnd="8dp" />

            <!-- 任务文本 -->
            <TextView
                android:id="@+id/widget_task_1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="小组件设计"
                android:textSize="14sp"
                android:textColor="@color/widget_today_text_primary_light"
                android:maxLines="1"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <!-- 分隔线1 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/widget_divider_light"
            android:layout_marginBottom="8dp"
            android:alpha="0.3" />

        <!-- 任务2 -->
        <LinearLayout
            android:id="@+id/widget_task_container_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- 复选框 -->
            <View
                android:id="@+id/widget_checkbox_2"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="@drawable/widget_checkbox_unchecked"
                android:layout_marginEnd="8dp" />

            <!-- 优先级颜色条 -->
            <View
                android:id="@+id/widget_priority_bar_2"
                android:layout_width="3dp"
                android:layout_height="16dp"
                android:background="@color/widget_priority_medium"
                android:layout_marginEnd="8dp" />

            <!-- 任务文本 -->
            <TextView
                android:id="@+id/widget_task_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="《哈利波特》"
                android:textSize="14sp"
                android:textColor="@color/widget_today_text_primary_light"
                android:maxLines="1"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

        <!-- 分隔线2 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/widget_divider_light"
            android:layout_marginBottom="8dp"
            android:alpha="0.3" />

        <!-- 任务3 -->
        <LinearLayout
            android:id="@+id/widget_task_container_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 复选框 -->
            <View
                android:id="@+id/widget_checkbox_3"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="@drawable/widget_checkbox_unchecked"
                android:layout_marginEnd="8dp" />

            <!-- 优先级颜色条 -->
            <View
                android:id="@+id/widget_priority_bar_3"
                android:layout_width="3dp"
                android:layout_height="16dp"
                android:background="@color/widget_priority_low"
                android:layout_marginEnd="8dp" />

            <!-- 任务文本 -->
            <TextView
                android:id="@+id/widget_task_3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="每天回答3道面"
                android:textSize="14sp"
                android:textColor="@color/widget_today_text_primary_light"
                android:maxLines="1"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_horizontal"
        android:layout_marginTop="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="今日任务"
            android:textSize="14sp"
            android:textColor="@color/widget_today_text_secondary_light"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2 × 2"
            android:textSize="12sp"
            android:textColor="@color/widget_today_text_tertiary_light"
            android:fontFamily="sans-serif"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 隐藏的兼容性元素 -->
    <TextView
        android:id="@+id/widget_task_count"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_task_4"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_pending_count"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_completed_count"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/widget_skipped_count"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</LinearLayout>
