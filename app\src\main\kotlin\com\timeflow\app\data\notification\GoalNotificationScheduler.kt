package com.timeflow.app.data.notification

import android.content.Context
import androidx.work.*
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.repository.GoalRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 目标通知调度器
 * 负责安排和管理各种类型的通知
 */
@Singleton
class GoalNotificationScheduler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val goalRepository: GoalRepository,
    private val notificationManager: GoalNotificationManager
) {
    companion object {
        private const val DAILY_REMINDER_WORK = "daily_reminder_work"
        private const val PROGRESS_CHECK_WORK = "progress_check_work"
        private const val DEADLINE_CHECK_WORK = "deadline_check_work"
        private const val SMART_SUGGESTION_WORK = "smart_suggestion_work"
    }
    
    private val workManager = WorkManager.getInstance(context)
    
    /**
     * 初始化所有通知调度
     */
    fun initializeNotificationSchedules() {
        scheduleDailyReminders()
        scheduleProgressChecks()
        scheduleDeadlineChecks()
        scheduleSmartSuggestions()
    }
    
    /**
     * 安排每日提醒
     */
    fun scheduleDailyReminders(reminderTime: LocalTime = LocalTime.of(9, 0)) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(false)
            .build()
        
        val dailyReminderRequest = PeriodicWorkRequestBuilder<DailyReminderWorker>(1, TimeUnit.DAYS)
            .setConstraints(constraints)
            .setInitialDelay(calculateInitialDelay(reminderTime), TimeUnit.MILLISECONDS)
            .addTag(DAILY_REMINDER_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            DAILY_REMINDER_WORK,
            ExistingPeriodicWorkPolicy.REPLACE,
            dailyReminderRequest
        )
    }
    
    /**
     * 安排进度检查
     */
    fun scheduleProgressChecks() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val progressCheckRequest = PeriodicWorkRequestBuilder<ProgressCheckWorker>(6, TimeUnit.HOURS)
            .setConstraints(constraints)
            .addTag(PROGRESS_CHECK_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            PROGRESS_CHECK_WORK,
            ExistingPeriodicWorkPolicy.KEEP,
            progressCheckRequest
        )
    }
    
    /**
     * 安排截止日期检查
     */
    fun scheduleDeadlineChecks() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val deadlineCheckRequest = PeriodicWorkRequestBuilder<DeadlineCheckWorker>(12, TimeUnit.HOURS)
            .setConstraints(constraints)
            .addTag(DEADLINE_CHECK_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            DEADLINE_CHECK_WORK,
            ExistingPeriodicWorkPolicy.KEEP,
            deadlineCheckRequest
        )
    }
    
    /**
     * 安排智能建议
     */
    fun scheduleSmartSuggestions() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val smartSuggestionRequest = PeriodicWorkRequestBuilder<SmartSuggestionWorker>(1, TimeUnit.DAYS)
            .setConstraints(constraints)
            .setInitialDelay(2, TimeUnit.HOURS) // 延迟2小时后开始
            .addTag(SMART_SUGGESTION_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            SMART_SUGGESTION_WORK,
            ExistingPeriodicWorkPolicy.KEEP,
            smartSuggestionRequest
        )
    }
    
    /**
     * 立即发送成就通知
     */
    fun sendImmediateAchievementNotification(goal: Goal, achievementType: AchievementType) {
        val achievementWorkRequest = OneTimeWorkRequestBuilder<AchievementNotificationWorker>()
            .setInputData(
                workDataOf(
                    "goal_id" to goal.id,
                    "achievement_type" to achievementType.name
                )
            )
            .build()
        
        workManager.enqueue(achievementWorkRequest)
    }
    
    /**
     * 安排特定目标的截止日期提醒
     */
    fun scheduleGoalDeadlineReminder(goal: Goal) {
        goal.dueDate?.let { dueDate ->
            val now = LocalDateTime.now()
            val reminderTimes = listOf(
                dueDate.minusDays(7), // 一周前
                dueDate.minusDays(3), // 三天前
                dueDate.minusDays(1), // 一天前
                dueDate // 当天
            ).filter { it.isAfter(now) }
            
            reminderTimes.forEach { reminderTime ->
                val delay = java.time.Duration.between(now, reminderTime).toMillis()
                
                val reminderRequest = OneTimeWorkRequestBuilder<GoalDeadlineReminderWorker>()
                    .setInitialDelay(delay, TimeUnit.MILLISECONDS)
                    .setInputData(
                        workDataOf(
                            "goal_id" to goal.id,
                            "reminder_time" to reminderTime.toString()
                        )
                    )
                    .addTag("goal_deadline_${goal.id}")
                    .build()
                
                workManager.enqueue(reminderRequest)
            }
        }
    }
    
    /**
     * 取消目标的所有提醒
     */
    fun cancelGoalReminders(goalId: String) {
        workManager.cancelAllWorkByTag("goal_deadline_$goalId")
    }
    
    /**
     * 取消所有通知调度
     */
    fun cancelAllSchedules() {
        workManager.cancelAllWorkByTag(DAILY_REMINDER_WORK)
        workManager.cancelAllWorkByTag(PROGRESS_CHECK_WORK)
        workManager.cancelAllWorkByTag(DEADLINE_CHECK_WORK)
        workManager.cancelAllWorkByTag(SMART_SUGGESTION_WORK)
    }
    
    /**
     * 计算到指定时间的初始延迟
     */
    private fun calculateInitialDelay(targetTime: LocalTime): Long {
        val now = LocalDateTime.now()
        val today = now.toLocalDate()
        var targetDateTime = today.atTime(targetTime)
        
        // 如果今天的目标时间已过，则安排到明天
        if (targetDateTime.isBefore(now)) {
            targetDateTime = targetDateTime.plusDays(1)
        }
        
        return java.time.Duration.between(now, targetDateTime).toMillis()
    }
}

/**
 * 每日提醒Worker
 */
class DailyReminderWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * 进度检查Worker
 */
class ProgressCheckWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * 截止日期检查Worker
 */
class DeadlineCheckWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * 智能建议Worker
 */
class SmartSuggestionWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * 成就通知Worker
 */
class AchievementNotificationWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
}

/**
 * 目标截止日期提醒Worker
 */
class GoalDeadlineReminderWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            // 简化实现，实际应用中需要注入依赖
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
}

// 扩展函数
private fun Goal.hasReachedMilestone(milestone: Float): Boolean {
    // 这里应该检查目标是否已经达到过这个里程碑
    // 需要在Goal数据类中添加相应的字段来跟踪
    return false
}

private fun Goal.daysSinceCreated(): Int {
    // 这里应该计算目标创建以来的天数
    // 需要在Goal数据类中添加创建时间字段
    return 0
}
