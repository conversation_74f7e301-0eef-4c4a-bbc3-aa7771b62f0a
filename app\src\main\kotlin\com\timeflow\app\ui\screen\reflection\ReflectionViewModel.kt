package com.timeflow.app.ui.screen.reflection

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.TimeFilter
import com.timeflow.app.ui.screen.reflection.model.TimeDimension
import com.timeflow.app.ui.screen.reflection.model.DayViewData
import com.timeflow.app.ui.screen.reflection.model.MonthViewData
import java.util.UUID
import kotlin.random.Random
import android.net.Uri
import com.timeflow.app.ui.screen.reflection.GoalSummary
import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * 感想列表界面状态
 */
data class ReflectionListState(
    val reflections: List<Reflection> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val errorMessage: String? = null,
    val isSearchVisible: Boolean = false,
    val isFilterVisible: Boolean = false,
    val isFilterPanelVisible: Boolean = false,
    val searchQuery: String = "",
    val selectedCategories: Set<String> = emptySet(),
    val selectedTimeFilter: TimeFilter = TimeFilter.THIS_MONTH,
    val timeFilter: TimeFilter = TimeFilter.THIS_MONTH,
    val selectedTags: Set<String> = emptySet(),
    val onlyFavorites: Boolean = false,
    val sortOrder: SortOrder = SortOrder.NEWEST_FIRST,
    val selectedMoods: List<String> = emptyList(),
    val selectedTypes: List<String> = emptyList(),
    val selectedMoodTypes: Set<MoodType> = emptySet(),
    val selectedReflectionTypes: Set<ReflectionType> = emptySet(),
    val advancedFilters: Map<String, Boolean> = emptyMap(),
    val recentSearches: List<String> = emptyList(),
    val popularTags: List<String> = emptyList(),
    val searchSuggestions: List<String> = emptyList(),
    val extendedSuggestions: List<String> = emptyList(),
    // 新增用于多时间维度视图的状态
    val timeDimension: TimeDimension = TimeDimension.WEEK,
    val weekDays: List<DayViewData> = emptyList(),
    val monthDays: List<DayViewData> = emptyList(),
    val yearMonths: List<MonthViewData> = emptyList(),
    val selectedDay: DayViewData? = null,
    val currentDate: LocalDate = LocalDate.now(),
    val currentWeek: Pair<LocalDate, LocalDate> = Pair(LocalDate.now(), LocalDate.now()),
    val currentMonth: Pair<LocalDate, LocalDate> = Pair(LocalDate.now(), LocalDate.now()),
    val currentYear: Int = LocalDate.now().year,
    // 删除功能相关状态
    val showDeleteDialog: Boolean = false,
    val reflectionToDelete: Reflection? = null,
    val showUndoSnackbar: Boolean = false,
    val deletedReflection: Reflection? = null,
    val undoTimeRemaining: Int = 10 // 撤销倒计时秒数
)

/**
 * 排序方式
 */
enum class SortOrder {
    NEWEST_FIRST,
    OLDEST_FIRST,
    TITLE_ASC,
    TITLE_DESC
}

/**
 * 感想页面视图模型
 */
@HiltViewModel
class ReflectionViewModel @Inject constructor(
    private val reflectionRepository: ReflectionRepository,
    private val searchSuggestionService: SearchSuggestionService
) : ViewModel() {

    private val _uiState = MutableStateFlow(ReflectionListState(isLoading = true))
    val uiState: StateFlow<ReflectionListState> = _uiState.asStateFlow()
    
    // 自定义日期范围
    private val _startDate = MutableStateFlow<LocalDate?>(null)
    val startDate: StateFlow<LocalDate?> = _startDate.asStateFlow()
    


    var showSearchPanel by mutableStateOf(false)
        private set
    
    var showFilterPanel by mutableStateOf(false)
        private set

    // 所有可能的标签
    private val allTags = mutableListOf<String>()
    
    // 所有可能的分类
    private val allCategories = mutableListOf<String>()

    // 🎯 修复：添加数据加载状态管理
    private var _hasInitialized = false
    private var _lastRefreshTime = 0L
    private val REFRESH_COOLDOWN = 2000L // 2秒冷却时间

    init {
        // 不在init中立即加载数据，由页面控制
        initializeTimeViews()

        // 监听感想创建事件
        viewModelScope.launch {
            com.timeflow.app.util.NotificationCenter.events.collect { event ->
                if (event is ReflectionCreatedEvent) {
                    android.util.Log.d("ReflectionViewModel", "📨 收到感想创建事件: ${event.reflectionId}")
                    // 直接添加到列表，避免重新加载
                    if (!_uiState.value.showUndoSnackbar) {
                        addNewReflectionToList(event.reflection)
                    }
                }
            }
        }
    }

    /**
     * 加载感想列表
     */
    fun loadReflections() {
        // 如果正在显示撤销删除的Snackbar，跳过加载以避免恢复已删除的记录
        if (_uiState.value.showUndoSnackbar) {
            android.util.Log.d("ReflectionViewModel", "正在显示撤销删除选项，跳过感想加载")
            return
        }

        viewModelScope.launch {
            // 🎯 修复：只在首次加载或数据为空时显示loading
            val shouldShowLoading = _uiState.value.reflections.isEmpty()
            if (shouldShowLoading) {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            }

            try {
                // 🔧 添加超时机制，避免无限等待
                val realReflections = withTimeout(10000) { // 10秒超时
                    reflectionRepository.getRecentReflections()
                }

                // 🎯 修复：移除模拟数据逻辑，避免删除后重新出现
                val reflections = realReflections
                android.util.Log.d("ReflectionViewModel", "加载到${reflections.size}条感想记录")

                // 提取所有标签和分类
                allTags.clear()
                allCategories.clear()
                reflections.forEach { reflection ->
                    allTags.addAll(reflection.tags)
                    if (reflection.type != null) {
                        allCategories.add(reflection.type.displayName)
                    }
                }

                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    reflections = reflections,
                    isLoading = false
                )

                // 初始化时间视图数据
                updateTimeViewData(reflections)

            } catch (e: Exception) {
                android.util.Log.e("ReflectionViewModel", "加载感想失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error"
                )
            }
        }
    }
    
    /**
     * 更新时间视图数据
     */
    private fun updateTimeViewData(reflections: List<Reflection>) {
        viewModelScope.launch {
            try {
                // 根据当前选择的时间维度更新相应的数据
                when (_uiState.value.timeDimension) {
                    TimeDimension.WEEK -> updateWeekViewData(reflections)
                    TimeDimension.MONTH -> updateMonthViewData(reflections)
                    TimeDimension.YEAR -> updateYearViewData(reflections)
                }
            } catch (e: Exception) {
                android.util.Log.e("ReflectionViewModel", "更新时间视图数据失败", e)
            }
        }
    }
    
    /**
     * 生成模拟感想数据
     */
    private fun generateMockReflections(): List<Reflection> {
        val moodOptions = listOf(
            MoodType.HAPPY,
            MoodType.CALM,
            MoodType.SAD,
            MoodType.ANGRY,
            MoodType.ANXIOUS
        )
        
        val types = listOf(
            ReflectionType.EXERCISE,
            ReflectionType.STUDY,
            ReflectionType.WORK,
            ReflectionType.CREATIVITY,
            ReflectionType.LIFE
        )
        
        val tags = listOf(
            "重要", "灵感", "目标", "进步", "挑战", "感恩",
            "阅读", "写作", "健身", "冥想", "社交", "家庭",
            "编程", "设计", "音乐", "电影", "美食", "自然"
        )
        
        val reflectionTitles = listOf(
            "今天的思考",
            "突然的感悟",
            "生活中的小确幸",
            "工作心得",
            "关于未来的计划",
            "令人难忘的瞬间",
            "情绪整理",
            "遇到的难题与解决",
            "今日总结",
            "关于读书的体会",
            "旅行中的所见所闻",
            "人际关系的反思"
        )
        
        val reflectionContents = listOf(
            "今天过得很充实，完成了许多计划中的任务，感到很有成就感。希望明天也能保持这样的状态。",
            "在阅读过程中有了新的想法，或许可以尝试一些不同的方法来解决当前的问题。",
            "生活中的小确幸往往在不经意间出现，学会感恩和珍惜每一个美好的瞬间。",
            "今天的会议很有成效，团队的合作越来越默契，项目进展顺利。",
            "规划了未来半年的目标，需要在时间管理上更加高效，减少无意义的事情。",
            "今天的日落特别美，停下来欣赏这样的美景让我感到内心平静。",
            "最近情绪有些波动，需要通过冥想来调整自己的心态，保持积极向上。",
            "遇到了技术难题，经过几个小时的探索终于找到了解决方案，过程虽然辛苦但很充实。",
            "今天完成了周计划中的大部分任务，还需要调整一些细节，总体上还算顺利。",
            "这本书给了我很多启发，作者的思维方式和观点值得我深入思考。",
            "这次旅行让我看到了不同的文化和生活方式，开阔了视野，也让我对自己的生活有了新的思考。",
            "与朋友的深入交流让我意识到沟通的重要性，需要学会表达自己的同时也要倾听他人。"
        )
        
        val now = LocalDateTime.now()
        
        return List(30) { index ->
            val randomDaysAgo = Random.nextInt(60)
            val creationTime = now.minus(randomDaysAgo.toLong(), ChronoUnit.DAYS)
                .minusHours(Random.nextLong(24))
                .minusMinutes(Random.nextLong(60))
            
            val tagCount = Random.nextInt(1, 4)
            val selectedTags = tags.shuffled().take(tagCount)
            
            Reflection(
                id = UUID.randomUUID().toString(),
                title = reflectionTitles[Random.nextInt(reflectionTitles.size)],
                content = reflectionContents[Random.nextInt(reflectionContents.size)],
                date = creationTime.atZone(ZoneId.systemDefault()).toInstant(),
                rating = Random.nextInt(1, 6),
                tags = selectedTags,
                type = types[Random.nextInt(types.size)],
                mood = moodOptions[Random.nextInt(moodOptions.size)]
            )
        }.sortedByDescending { it.date }  // 默认按创建时间倒序排列
    }
    
    /**
     * 获取筛选后的感想列表
     */
    fun getFilteredReflections(): List<Reflection> {
        val currentState = _uiState.value
        return currentState.reflections.filter { reflection ->
            // 搜索过滤
            val matchesSearch = currentState.searchQuery.isEmpty() ||
                    reflection.title.contains(currentState.searchQuery, ignoreCase = true) ||
                    reflection.content.contains(currentState.searchQuery, ignoreCase = true) ||
                    reflection.tags.any { it.contains(currentState.searchQuery, ignoreCase = true) }
            
            // 分类过滤
            val matchesCategory = currentState.selectedCategories.isEmpty() ||
                    currentState.selectedCategories.contains(reflection.type.displayName)
            
            // 标签过滤
            val matchesTags = currentState.selectedTags.isEmpty() ||
                    reflection.tags.any { currentState.selectedTags.contains(it) }
            
            // 收藏过滤 (Reflection类没有isFavorite属性，先忽略)
            val matchesFavorite = true
            
            // 时间过滤
            val matchesTimeFilter = when (currentState.selectedTimeFilter) {
                TimeFilter.THIS_WEEK -> {
                    val weekAgo = Instant.now().minus(7, ChronoUnit.DAYS)
                    reflection.date.isAfter(weekAgo)
                }
                TimeFilter.THIS_MONTH -> {
                    val monthAgo = Instant.now().minus(30, ChronoUnit.DAYS)
                    reflection.date.isAfter(monthAgo)
                }
                TimeFilter.CUSTOM -> true // 自定义时间范围暂不实现
            }
            
            matchesSearch && matchesCategory && matchesTags && matchesFavorite && matchesTimeFilter
        }.let { filtered ->
            // 排序
            when (currentState.sortOrder) {
                SortOrder.NEWEST_FIRST -> filtered.sortedByDescending { it.date }
                SortOrder.OLDEST_FIRST -> filtered.sortedBy { it.date }
                SortOrder.TITLE_ASC -> filtered.sortedBy { it.title }
                SortOrder.TITLE_DESC -> filtered.sortedByDescending { it.title }
            }
        }
    }
    
    /**
     * 切换搜索面板可见性
     */
    fun toggleSearchPanel() {
        _uiState.value = _uiState.value.copy(
            isSearchVisible = !_uiState.value.isSearchVisible,
            isFilterVisible = false
        )
    }
    
    /**
     * 切换筛选面板可见性
     */
    fun toggleFilterPanel() {
        _uiState.value = _uiState.value.copy(
            isFilterPanelVisible = !_uiState.value.isFilterPanelVisible,
            isFilterVisible = !_uiState.value.isFilterVisible,
            isSearchVisible = false
        )
    }
    
    /**
     * 更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
    }
    
    /**
     * 清除搜索查询
     */
    fun clearSearch() {
        _uiState.value = _uiState.value.copy(searchQuery = "")
    }
    
    /**
     * 切换时间过滤器
     */
    fun setTimeFilter(timeFilter: TimeFilter) {
        _uiState.value = _uiState.value.copy(selectedTimeFilter = timeFilter)
    }
    
    /**
     * 切换类别选择
     */
    fun toggleCategoryFilter(category: String) {
        val currentCategories = _uiState.value.selectedCategories.toMutableSet()
        if (currentCategories.contains(category)) {
            currentCategories.remove(category)
        } else {
            currentCategories.add(category)
        }
        _uiState.value = _uiState.value.copy(selectedCategories = currentCategories)
    }
    
    /**
     * 切换标签选择
     */
    fun toggleTagFilter(tag: String) {
        val currentTags = _uiState.value.selectedTags.toMutableSet()
        if (currentTags.contains(tag)) {
            currentTags.remove(tag)
        } else {
            currentTags.add(tag)
        }
        _uiState.value = _uiState.value.copy(selectedTags = currentTags)
    }
    
    /**
     * 切换收藏筛选
     */
    fun toggleFavoriteFilter() {
        _uiState.value = _uiState.value.copy(onlyFavorites = !_uiState.value.onlyFavorites)
    }
    
    /**
     * 设置排序方式
     */
    fun setSortOrder(sortOrder: SortOrder) {
        _uiState.value = _uiState.value.copy(sortOrder = sortOrder)
    }
    
    /**
     * 清除所有筛选条件
     */
    fun clearAllFilters() {
        _uiState.value = _uiState.value.copy(
            searchQuery = "",
            selectedCategories = emptySet(),
            selectedTags = emptySet(),
            selectedTimeFilter = TimeFilter.THIS_MONTH,
            onlyFavorites = false,
            sortOrder = SortOrder.NEWEST_FIRST
        )
    }
    
    /**
     * 获取所有可用的标签
     */
    fun getAllTags(): List<String> {
        return allTags.distinct().sorted()
    }
    
    /**
     * 获取所有可用的分类
     */
    fun getAllCategories(): List<String> {
        return allCategories.distinct().sorted()
    }
    
    /**
     * 格式化日期时间
     */
    fun formatDateTime(dateTime: LocalDateTime): String {
        val now = LocalDateTime.now()
        val today = now.toLocalDate()
        val yesterday = today.minusDays(1)
        
        return when {
            dateTime.toLocalDate() == today -> "今天 ${dateTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
            dateTime.toLocalDate() == yesterday -> "昨天 ${dateTime.format(DateTimeFormatter.ofPattern("HH:mm"))}"
            dateTime.year == now.year -> dateTime.format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm"))
            else -> dateTime.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm"))
        }
    }
    
    /**
     * 切换收藏状态
     */
    fun toggleFavorite(reflectionId: String) {
        // Reflection类型没有isFavorite字段，此方法需要重新实现或暂时移除
        // 临时修改为日志输出
        android.util.Log.d("ReflectionViewModel", "toggleFavorite方法未实现，reflectionId: $reflectionId")
    }
    
    /**
     * 初始加载数据 - 只在首次加载时调用
     */
    fun loadInitialData() {
        if (_hasInitialized) {
            android.util.Log.d("ReflectionViewModel", "数据已初始化，跳过重复加载")
            return
        }

        // 如果正在显示撤销删除的Snackbar，跳过加载以避免恢复已删除的记录
        if (_uiState.value.showUndoSnackbar) {
            android.util.Log.d("ReflectionViewModel", "正在显示撤销删除选项，跳过数据加载")
            return
        }

        android.util.Log.d("ReflectionViewModel", "开始初始化数据加载")
        _hasInitialized = true

        viewModelScope.launch {
            try {
                // 🔧 添加超时机制，先尝试加载真实数据
                withTimeout(15000) { // 15秒超时，给初始化更多时间
                    loadReflections()
                }

                // 如果数据为空且是首次使用，询问是否需要示例数据
                if (_uiState.value.reflections.isEmpty()) {
                    android.util.Log.d("ReflectionViewModel", "数据库为空，可考虑初始化示例数据")
                    // 这里可以添加用户选择逻辑，暂时不自动创建示例数据
                }
            } catch (e: Exception) {
                android.util.Log.e("ReflectionViewModel", "初始化数据加载失败", e)
                // 🔧 超时或其他错误时，停止loading状态
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "数据加载超时，请检查网络连接或重试"
                )
            }
        }
    }

    /**
     * 手动初始化示例数据（仅在用户明确需要时调用）
     */
    fun initializeSampleData() {
        viewModelScope.launch {
            try {
                android.util.Log.d("ReflectionViewModel", "手动初始化示例数据")
                reflectionRepository.initializeSampleDataIfNeeded()
                // 重新加载数据
                loadReflections()
            } catch (e: Exception) {
                android.util.Log.e("ReflectionViewModel", "初始化示例数据失败", e)
            }
        }
    }

    /**
     * 检查是否需要刷新数据
     */
    fun refreshIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - _lastRefreshTime < REFRESH_COOLDOWN) {
            android.util.Log.d("ReflectionViewModel", "刷新冷却中，跳过刷新")
            return
        }

        if (_uiState.value.showUndoSnackbar) {
            android.util.Log.d("ReflectionViewModel", "正在显示撤销删除选项，跳过刷新")
            return
        }

        android.util.Log.d("ReflectionViewModel", "执行条件刷新")
        _lastRefreshTime = currentTime
        loadReflections()
    }

    /**
     * 添加新感想到列表（避免重新加载）
     */
    fun addNewReflection(reflection: com.timeflow.app.ui.screen.reflection.Reflection) {
        addNewReflectionToList(reflection)
    }

    /**
     * 内部方法：添加新感想到列表
     */
    private fun addNewReflectionToList(reflection: com.timeflow.app.ui.screen.reflection.Reflection) {
        val currentReflections = _uiState.value.reflections.toMutableList()

        // 检查是否已存在
        if (currentReflections.any { it.id == reflection.id }) {
            android.util.Log.d("ReflectionViewModel", "感想已存在，跳过添加: ${reflection.id}")
            return
        }

        // 添加到列表开头
        currentReflections.add(0, reflection)

        // 按日期排序
        val sortedReflections = currentReflections.sortedByDescending { it.date }

        _uiState.value = _uiState.value.copy(reflections = sortedReflections)

        android.util.Log.d("ReflectionViewModel", "新感想已添加到列表: ${reflection.title}")
    }
    
    /**
     * 更新开始日期
     */
    fun updateStartDate(date: LocalDate) {
        _startDate.value = date
    }
    

    
    /**
     * 执行搜索
     */
    fun performSearch() {
        // 基于当前的搜索条件执行搜索
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            delay(300) // 模拟搜索延迟
            _uiState.value = _uiState.value.copy(isLoading = false)
            // 实际搜索逻辑已经在getFilteredReflections中实现
        }
    }
    
    /**
     * 更新时间筛选器
     */
    fun updateTimeFilter(timeFilter: TimeFilter) {
        _uiState.value = _uiState.value.copy(selectedTimeFilter = timeFilter)
    }
    
    /**
     * 更新心情筛选器
     */
    fun updateMoodFilter(mood: String, isSelected: Boolean) {
        val currentMoods = _uiState.value.selectedMoods.toMutableList()
        if (isSelected && !currentMoods.contains(mood)) {
            currentMoods.add(mood)
        } else if (!isSelected && currentMoods.contains(mood)) {
            currentMoods.remove(mood)
        }
        _uiState.value = _uiState.value.copy(selectedMoods = currentMoods)
    }
    
    /**
     * 更新类型筛选器
     */
    fun updateTypeFilter(type: String, isSelected: Boolean) {
        val currentTypes = _uiState.value.selectedTypes.toMutableList()
        if (isSelected && !currentTypes.contains(type)) {
            currentTypes.add(type)
        } else if (!isSelected && currentTypes.contains(type)) {
            currentTypes.remove(type)
        }
        _uiState.value = _uiState.value.copy(selectedTypes = currentTypes)
    }
    
    /**
     * 更新高级筛选器
     */
    fun updateAdvancedFilter(filter: String, value: Boolean) {
        val updatedFilters = _uiState.value.advancedFilters.toMutableMap()
        updatedFilters[filter] = value
        _uiState.value = _uiState.value.copy(advancedFilters = updatedFilters)
    }
    
    /**
     * 清除自定义日期范围
     */
    fun clearCustomDateRange() {
        _startDate.value = null
    }
    
    /**
     * 应用自定义日期范围
     */
    fun applyCustomDateRange() {
        // 应用日期范围筛选
        _uiState.value = _uiState.value.copy(selectedTimeFilter = TimeFilter.CUSTOM)
    }
    
    /**
     * 选择搜索建议
     */
    fun selectSearchSuggestion(suggestion: String) {
        _uiState.value = _uiState.value.copy(searchQuery = suggestion)
        performSearch()
    }

    /**
     * 初始化时间维度视图数据
     */
    private fun initializeTimeViews() {
        updateCurrentDate(LocalDate.now())
    }

    /**
     * 更新当前日期并刷新时间维度视图
     */
    fun updateCurrentDate(date: LocalDate) {
        viewModelScope.launch {
            val now = LocalDate.now()
            
            // 更新当前日期
            _uiState.update { it.copy(currentDate = date) }
            
            // 更新当前周范围
            val weekStart = date.with(java.time.DayOfWeek.MONDAY).minusDays(1) // 从周日开始
            val weekEnd = weekStart.plusDays(6) // 到周六结束
            _uiState.update { it.copy(currentWeek = Pair(weekStart, weekEnd)) }
            
            // 更新当前月范围
            val monthStart = date.withDayOfMonth(1)
            val monthEnd = monthStart.plusMonths(1).minusDays(1)
            _uiState.update { it.copy(currentMonth = Pair(monthStart, monthEnd)) }
            
            // 更新当前年份
            _uiState.update { it.copy(currentYear = date.year) }
            
            // 刷新时间视图数据
            refreshTimeViewData()
        }
    }
    
    /**
     * 刷新时间视图数据
     */
    private fun refreshTimeViewData() {
        viewModelScope.launch {
            // 生成周视图数据
            generateWeekViewData()
            
            // 生成月视图数据
            generateMonthViewData()
            
            // 生成年视图数据
            generateYearViewData()
        }
    }
    
    /**
     * 生成周视图数据
     */
    private fun generateWeekViewData() {
        val currentState = _uiState.value
        val weekStart = currentState.currentWeek.first
        val weekEnd = currentState.currentWeek.second
        val today = LocalDate.now()
        
        // 生成一周的日期数据
        val weekDays = (0..6).map { dayOffset ->
            val date = weekStart.plusDays(dayOffset.toLong())
            val reflectionsCount = countReflectionsForDate(date)
            
            DayViewData(
                date = date,
                timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant(),
                isCurrentMonth = date.month == currentState.currentDate.month,
                isToday = date == today,
                reflectionCount = reflectionsCount
            )
        }
        
        // 更新周视图数据
        _uiState.update { currentState ->
            val newSelectedDay = when {
                // 🎯 如果当前日期就是今天（比如点击了"今天"按钮），强制选中今天
                currentState.currentDate == today -> weekDays.find { day -> day.isToday }
                // 如果之前没有选中的日期，选中今天或周内第一天
                currentState.selectedDay == null -> weekDays.find { day -> day.isToday } ?: weekDays.firstOrNull()
                // 保持之前选中的日期（如果它在当前周内）
                else -> weekDays.find { day -> day.date == currentState.selectedDay?.date } ?: currentState.selectedDay
            }
            
            currentState.copy(
                weekDays = weekDays,
                selectedDay = newSelectedDay
            )
        }
    }
    
    /**
     * 生成月视图数据
     */
    private fun generateMonthViewData() {
        val currentState = _uiState.value
        val monthStart = currentState.currentMonth.first
        val today = LocalDate.now()
        
        // 获取本月第一天是星期几 (周日为0，周一为1...)
        val firstDayOfWeekValue = monthStart.dayOfWeek.value % 7
        
        // 计算需要显示的前一个月的天数
        val daysFromPrevMonth = firstDayOfWeekValue
        
        // 计算本月的总天数
        val daysInMonth = monthStart.month.length(monthStart.isLeapYear)
        
        // 计算日历网格中的总天数 (6周，每周7天)
        val totalDays = 42
        
        val monthDays = mutableListOf<DayViewData>()
        
        // 添加上个月的日期
        for (i in daysFromPrevMonth downTo 1) {
            val date = monthStart.minusDays(i.toLong())
            monthDays.add(DayViewData(
                date = date,
                timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant(),
                isCurrentMonth = false,
                isToday = date == today,
                reflectionCount = countReflectionsForDate(date)
            ))
        }
        
        // 添加本月的日期
        for (i in 1..daysInMonth) {
            val date = monthStart.withDayOfMonth(i)
            monthDays.add(DayViewData(
                date = date,
                timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant(),
                isCurrentMonth = true,
                isToday = date == today,
                reflectionCount = countReflectionsForDate(date)
            ))
        }
        
        // 添加下个月的日期
        val remainingDays = totalDays - monthDays.size
        for (i in 1..remainingDays) {
            val date = monthStart.plusMonths(1).withDayOfMonth(i)
            monthDays.add(DayViewData(
                date = date,
                timestamp = date.atStartOfDay(ZoneId.systemDefault()).toInstant(),
                isCurrentMonth = false,
                isToday = date == today,
                reflectionCount = countReflectionsForDate(date)
            ))
        }
        
        // 更新月视图数据
        _uiState.update { it.copy(monthDays = monthDays) }
    }
    
    /**
     * 生成年视图数据
     */
    private fun generateYearViewData() {
        val currentState = _uiState.value
        val currentYear = currentState.currentYear
        val today = LocalDate.now()
        
        // 生成12个月的数据
        val yearMonths = (1..12).map { month ->
            // 计算该月的感想数量
            val startOfMonth = LocalDate.of(currentYear, month, 1)
            val endOfMonth = startOfMonth.plusMonths(1).minusDays(1)
            val reflectionsCount = countReflectionsBetweenDates(startOfMonth, endOfMonth)
            
            MonthViewData(
                year = currentYear,
                month = month,
                reflectionCount = reflectionsCount,
                isCurrentMonth = today.year == currentYear && today.monthValue == month
            )
        }
        
        // 更新年视图数据
        _uiState.update { it.copy(yearMonths = yearMonths) }
    }
    
    /**
     * 统计指定日期的感想数量
     */
    private fun countReflectionsForDate(date: LocalDate): Int {
        val startOfDay = date.atStartOfDay(ZoneId.systemDefault()).toInstant()
        val endOfDay = date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1)
        
        return _uiState.value.reflections.count { reflection ->
            reflection.date >= startOfDay && reflection.date <= endOfDay
        }
    }
    
    /**
     * 统计指定日期范围的感想数量
     */
    private fun countReflectionsBetweenDates(startDate: LocalDate, endDate: LocalDate): Int {
        val startInstant = startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()
        val endInstant = endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1)
        
        return _uiState.value.reflections.count { reflection ->
            reflection.date >= startInstant && reflection.date <= endInstant
        }
    }
    
    /**
     * 设置时间维度
     */
    fun setTimeDimension(dimension: TimeDimension) {
        _uiState.update { it.copy(timeDimension = dimension) }
    }
    
    /**
     * 选择日期
     */
    fun selectDay(day: DayViewData) {
        _uiState.update { it.copy(selectedDay = day) }
        
        // 如果选择的是非当前月的日期，更新当前日期到该日期
        if (!day.isCurrentMonth) {
            updateCurrentDate(day.date)
        }
    }
    
    /**
     * 选择月份
     */
    fun selectMonth(monthData: MonthViewData) {
        val newDate = LocalDate.of(monthData.year, monthData.month, 1)
        updateCurrentDate(newDate)
        setTimeDimension(TimeDimension.MONTH)
    }
    
    /**
     * 切换到前一周/月/年
     */
    fun navigatePrevious() {
        val currentState = _uiState.value
        when (currentState.timeDimension) {
            TimeDimension.WEEK -> {
                val newDate = currentState.currentDate.minusWeeks(1)
                updateCurrentDate(newDate)
            }
            TimeDimension.MONTH -> {
                val newDate = currentState.currentDate.minusMonths(1)
                updateCurrentDate(newDate)
            }
            TimeDimension.YEAR -> {
                val newDate = currentState.currentDate.minusYears(1)
                updateCurrentDate(newDate)
            }
        }
    }
    
    /**
     * 切换到后一周/月/年
     */
    fun navigateNext() {
        val currentState = _uiState.value
        when (currentState.timeDimension) {
            TimeDimension.WEEK -> {
                val newDate = currentState.currentDate.plusWeeks(1)
                updateCurrentDate(newDate)
            }
            TimeDimension.MONTH -> {
                val newDate = currentState.currentDate.plusMonths(1)
                updateCurrentDate(newDate)
            }
            TimeDimension.YEAR -> {
                val newDate = currentState.currentDate.plusYears(1)
                updateCurrentDate(newDate)
            }
        }
    }
    
    /**
     * 回到今天
     */
    fun navigateToToday() {
        val today = LocalDate.now()
        android.util.Log.d("ReflectionViewModel", "===== 点击今天按钮 =====")
        android.util.Log.d("ReflectionViewModel", "今天日期: $today")
        android.util.Log.d("ReflectionViewModel", "当前时间维度: ${_uiState.value.timeDimension}")
        
        // 🎯 关键：更新当前日期为今天，generateWeekViewData会自动选中今天
        updateCurrentDate(today)
        
        // 🎯 同时更新startDate，确保日期选择器也聚焦到今天
        _startDate.value = today
        
        android.util.Log.d("ReflectionViewModel", "✓ 今天按钮功能执行完成，generateWeekViewData会自动选中今天")
    }
    
    /**
     * 获取当前时间范围的标题
     */
    fun getCurrentTimeRangeTitle(): String {
        val currentState = _uiState.value
        return when (currentState.timeDimension) {
            TimeDimension.WEEK -> {
                val weekStart = currentState.currentWeek.first
                val weekEnd = currentState.currentWeek.second
                val weekNumber = weekStart.plusDays(3).get(java.time.temporal.WeekFields.ISO.weekOfYear())
                
                // 判断是否跨月
                if (weekStart.month == weekEnd.month) {
                    "${weekStart.year}年${weekStart.monthValue}月 第${weekNumber}周"
                } else {
                    "${weekStart.monthValue}/${weekStart.dayOfMonth}-${weekEnd.monthValue}/${weekEnd.dayOfMonth}"
                }
            }
            TimeDimension.MONTH -> {
                "${currentState.currentDate.year}年${currentState.currentDate.monthValue}月"
            }
            TimeDimension.YEAR -> {
                "${currentState.currentDate.year}年"
            }
        }
    }
    
    /**
     * 根据当前视图模式获取可见的感想列表
     */
    fun getVisibleReflections(): List<Reflection> {
        val currentState = _uiState.value
        return when (currentState.timeDimension) {
            TimeDimension.WEEK -> {
                // 周视图，返回当前选中日期的感想
                currentState.selectedDay?.let { selectedDay ->
                    val startOfDay = selectedDay.date.atStartOfDay(ZoneId.systemDefault()).toInstant()
                    val endOfDay = selectedDay.date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1)
                    
                    currentState.reflections.filter { reflection ->
                        reflection.date >= startOfDay && reflection.date <= endOfDay
                    }
                } ?: emptyList()
            }
            TimeDimension.MONTH -> {
                // 月视图，返回当前月的感想
                val monthStart = currentState.currentMonth.first
                val monthEnd = currentState.currentMonth.second
                val startInstant = monthStart.atStartOfDay(ZoneId.systemDefault()).toInstant()
                val endInstant = monthEnd.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1)
                
                currentState.reflections.filter { reflection ->
                    reflection.date >= startInstant && reflection.date <= endInstant
                }
            }
            TimeDimension.YEAR -> {
                // 年视图，返回当前年的感想
                val yearStart = LocalDate.of(currentState.currentYear, 1, 1)
                val yearEnd = LocalDate.of(currentState.currentYear, 12, 31)
                val startInstant = yearStart.atStartOfDay(ZoneId.systemDefault()).toInstant()
                val endInstant = yearEnd.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1)
                
                currentState.reflections.filter { reflection ->
                    reflection.date >= startInstant && reflection.date <= endInstant
                }
            }
        }
    }

    /**
     * 更新周视图数据
     */
    private fun updateWeekViewData(reflections: List<Reflection>) {
        viewModelScope.launch {
            val currentState = _uiState.value
            val weekStart = currentState.currentWeek.first
            val weekEnd = currentState.currentWeek.second
            
            // 更新周视图数据...
            generateWeekViewData()
        }
    }

    /**
     * 更新月视图数据
     */
    private fun updateMonthViewData(reflections: List<Reflection>) {
        viewModelScope.launch {
            val currentState = _uiState.value
            val monthStart = currentState.currentMonth.first
            val monthEnd = currentState.currentMonth.second
            
            // 更新月视图数据...
            generateMonthViewData()
        }
    }

    /**
     * 更新年视图数据
     */
    private fun updateYearViewData(reflections: List<Reflection>) {
        viewModelScope.launch {
            val currentState = _uiState.value
            val currentYear = currentState.currentYear
            
            // 更新年视图数据...
            generateYearViewData()
        }
    }

    fun addToMotivationWall() {
        // 实现添加到激励墙逻辑
    }
    
    // ========== 删除功能相关方法 ==========
    
    /**
     * 显示删除确认对话框
     */
    fun showDeleteDialog(reflection: Reflection) {
        _uiState.value = _uiState.value.copy(
            showDeleteDialog = true,
            reflectionToDelete = reflection
        )
    }
    
    /**
     * 隐藏删除确认对话框
     */
    fun hideDeleteDialog() {
        _uiState.value = _uiState.value.copy(
            showDeleteDialog = false,
            reflectionToDelete = null
        )
    }
    
    /**
     * 确认删除感想
     */
    fun confirmDeleteReflection() {
        val reflectionToDelete = _uiState.value.reflectionToDelete
        if (reflectionToDelete != null) {
            viewModelScope.launch {
                try {
                    android.util.Log.d("ReflectionViewModel", "===== 开始删除感想 =====")
                    android.util.Log.d("ReflectionViewModel", "删除感想ID: ${reflectionToDelete.id}")
                    android.util.Log.d("ReflectionViewModel", "删除感想标题: ${reflectionToDelete.title}")

                    // 🎯 关键修复：立即从数据库删除，不使用延迟删除机制
                    reflectionRepository.deleteReflection(reflectionToDelete.id)
                    android.util.Log.d("ReflectionViewModel", "✓ 数据库删除成功")

                    // 从UI列表中移除
                    val updatedReflections = _uiState.value.reflections.filter {
                        it.id != reflectionToDelete.id
                    }
                    android.util.Log.d("ReflectionViewModel", "✓ UI列表更新，剩余${updatedReflections.size}条记录")

                    // 更新UI状态，显示撤销Snackbar
                    _uiState.value = _uiState.value.copy(
                        reflections = updatedReflections,
                        showDeleteDialog = false,
                        reflectionToDelete = null,
                        showUndoSnackbar = true,
                        deletedReflection = reflectionToDelete,
                        undoTimeRemaining = 10
                    )

                    // 启动倒计时
                    startUndoCountdown()

                    android.util.Log.d("ReflectionViewModel", "✓ 删除完成，显示撤销选项")

                } catch (e: Exception) {
                    android.util.Log.e("ReflectionViewModel", "删除感想失败", e)
                    _uiState.value = _uiState.value.copy(
                        error = e.message ?: "删除失败",
                        showDeleteDialog = false,
                        reflectionToDelete = null
                    )
                }
            }
        }
    }
    
    /**
     * 撤销删除操作
     */
    fun undoDeleteReflection() {
        val deletedReflection = _uiState.value.deletedReflection
        if (deletedReflection != null) {
            viewModelScope.launch {
                try {
                    android.util.Log.d("ReflectionViewModel", "===== 开始撤销删除 =====")
                    android.util.Log.d("ReflectionViewModel", "撤销感想ID: ${deletedReflection.id}")

                    // 重新保存到数据库
                    android.util.Log.d("ReflectionViewModel", "开始重新保存到数据库...")
                    reflectionRepository.saveReflection(deletedReflection)
                    android.util.Log.d("ReflectionViewModel", "✓ 数据库保存成功")

                    // 将感想重新添加到UI列表中
                    val updatedReflections = (_uiState.value.reflections + deletedReflection)
                        .sortedByDescending { it.date }
                    android.util.Log.d("ReflectionViewModel", "✓ UI列表恢复完成，现有${updatedReflections.size}条记录")

                    _uiState.value = _uiState.value.copy(
                        reflections = updatedReflections,
                        showUndoSnackbar = false,
                        deletedReflection = null
                    )

                    android.util.Log.d("ReflectionViewModel", "✓ 撤销删除完成")

                } catch (e: Exception) {
                    android.util.Log.e("ReflectionViewModel", "撤销删除失败", e)
                    _uiState.value = _uiState.value.copy(
                        error = e.message ?: "撤销失败",
                        showUndoSnackbar = false,
                        deletedReflection = null
                    )
                }
            }
        }
    }
    
    /**
     * 隐藏撤销Snackbar
     */
    fun hideUndoSnackbar() {
        android.util.Log.d("ReflectionViewModel", "隐藏撤销Snackbar")
        _uiState.value = _uiState.value.copy(
            showUndoSnackbar = false,
            deletedReflection = null
        )
    }
    
    /**
     * 启动撤销倒计时
     */
    private fun startUndoCountdown() {
        viewModelScope.launch {
            repeat(10) { second ->
                delay(1000)
                if (_uiState.value.showUndoSnackbar) {
                    _uiState.value = _uiState.value.copy(
                        undoTimeRemaining = 10 - second - 1
                    )
                } else {
                    return@launch // 如果Snackbar已隐藏，停止倒计时
                }
            }

            // 10秒后自动隐藏撤销选项
            if (_uiState.value.showUndoSnackbar) {
                android.util.Log.d("ReflectionViewModel", "撤销时间到期，隐藏撤销选项")
                _uiState.value = _uiState.value.copy(
                    showUndoSnackbar = false,
                    deletedReflection = null
                )
            }
        }
    }
    
    // 🆕 保存图片到内部存储的方法
    private suspend fun saveImageToInternalStorage(context: Context, imageUri: Uri): String? {
        return withContext(Dispatchers.IO) {
            try {
                val inputStream: InputStream? = context.contentResolver.openInputStream(imageUri)
                inputStream?.use { input ->
                    val fileName = "reflection_${System.currentTimeMillis()}_${UUID.randomUUID()}.jpg"
                    val file = File(context.filesDir, "reflection_images")
                    if (!file.exists()) {
                        file.mkdirs()
                    }
                    val imageFile = File(file, fileName)
                    
                    FileOutputStream(imageFile).use { output ->
                        input.copyTo(output)
                    }
                    
                    imageFile.absolutePath
                }
            } catch (e: Exception) {
                android.util.Log.e("ReflectionDetailViewModel", "保存图片失败", e)
                null
            }
        }
    }
    
    // 🆕 批量保存图片的方法（占位方法，实际在ReflectionDetailViewModel中实现）
    private suspend fun saveSelectedImages(context: Context): List<String> {
        // 这个方法在ReflectionViewModel中是占位方法
        // 实际的图片保存逻辑在ReflectionDetailViewModel中
        return emptyList()
    }

    /**
     * 强制刷新感想数据 - 用于确保数据实时更新
     */
    fun forceRefresh() {
        android.util.Log.d("ReflectionViewModel", "🔄 强制刷新感想数据")

        // 如果正在显示撤销删除的Snackbar，跳过刷新以避免恢复已删除的记录
        if (_uiState.value.showUndoSnackbar) {
            android.util.Log.d("ReflectionViewModel", "正在显示撤销删除选项，跳过数据刷新")
            return
        }

        // 检查冷却时间
        val currentTime = System.currentTimeMillis()
        if (currentTime - _lastRefreshTime < REFRESH_COOLDOWN) {
            android.util.Log.d("ReflectionViewModel", "刷新冷却中，跳过强制刷新")
            return
        }

        _lastRefreshTime = currentTime

        viewModelScope.launch {
            // 🎯 修复：避免频繁显示loading状态导致闪烁
            val wasEmpty = _uiState.value.reflections.isEmpty()
            if (wasEmpty) {
                _uiState.value = _uiState.value.copy(isLoading = true)
            }

            try {
                // 🔧 添加超时机制，强制从数据库重新获取数据
                val realReflections = withTimeout(10000) { // 10秒超时
                    reflectionRepository.getRecentReflections()
                }
                android.util.Log.d("ReflectionViewModel", "✓ 强制刷新获取到${realReflections.size}条感想记录")

                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    reflections = realReflections,
                    isLoading = false,
                    error = null
                )

                // 更新时间视图数据
                updateTimeViewData(realReflections)

            } catch (e: Exception) {
                android.util.Log.e("ReflectionViewModel", "强制刷新失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "刷新失败"
                )
            }
        }
    }

    // 🎯 新增：心情筛选状态管理
    fun toggleMoodFilter(mood: MoodType) {
        val currentMoods = _uiState.value.selectedMoodTypes.toMutableSet()
        if (currentMoods.contains(mood)) {
            currentMoods.remove(mood)
        } else {
            currentMoods.add(mood)
        }
        _uiState.update { 
            it.copy(selectedMoodTypes = currentMoods) 
        }
    }
    
    // 🎯 新增：反思类型筛选状态管理
    fun toggleReflectionTypeFilter(type: ReflectionType) {
        val currentTypes = _uiState.value.selectedReflectionTypes.toMutableSet()
        if (currentTypes.contains(type)) {
            currentTypes.remove(type)
        } else {
            currentTypes.add(type)
        }
        _uiState.update { 
            it.copy(selectedReflectionTypes = currentTypes) 
        }
    }
    
    // 🎯 新增：应用筛选
    fun applyFilters() {
        // 应用当前的所有筛选条件
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            // 这里可以添加实际的筛选逻辑
            // 目前只是模拟延迟并关闭筛选面板
            delay(300)
            
            _uiState.update { 
                it.copy(
                    isLoading = false,
                    isFilterPanelVisible = false
                ) 
            }
        }
    }
}

/**
 * 感想详情页视图模型
 */
@HiltViewModel
class ReflectionDetailViewModel @Inject constructor(
    private val reflectionRepository: ReflectionRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ReflectionDetailState())
    val uiState: StateFlow<ReflectionDetailState> = _uiState.asStateFlow()
    
    // 用于编辑模式的状态
    private val _isEditMode = MutableStateFlow(false)
    val isEditMode: StateFlow<Boolean> = _isEditMode.asStateFlow()
    
    // 表单数据状态
    private val _title = MutableStateFlow("")
    val title: StateFlow<String> = _title.asStateFlow()
    
    private val _content = MutableStateFlow("")
    val content: StateFlow<String> = _content.asStateFlow()
    
    private val _selectedMood = MutableStateFlow(MoodType.CALM)
    val selectedMood: StateFlow<MoodType> = _selectedMood.asStateFlow()
    
    private val _selectedTags = MutableStateFlow<List<String>>(emptyList())
    val selectedTags: StateFlow<List<String>> = _selectedTags.asStateFlow()
    
    private val _selectedType = MutableStateFlow(ReflectionType.PERSONAL)
    val selectedType: StateFlow<ReflectionType> = _selectedType.asStateFlow()

    // 图片状态
    private val _selectedImages = MutableStateFlow<List<Uri>>(emptyList())
    val selectedImages: StateFlow<List<Uri>> = _selectedImages.asStateFlow()
 
    // 日期相关状态
    private val _selectedDate = MutableStateFlow(Instant.now())
    val selectedDate: StateFlow<Instant> = _selectedDate.asStateFlow()

    // 目标相关状态
    private val _selectedGoalId = MutableStateFlow<String?>(null)
    val selectedGoalId: StateFlow<String?> = _selectedGoalId.asStateFlow()

    private val _availableGoals = MutableStateFlow<List<GoalSummary>>(emptyList())
    val availableGoals: StateFlow<List<GoalSummary>> = _availableGoals.asStateFlow()

    // 初始化
    init {
        loadAvailableGoals()
    }

    // 添加更新日期的方法
    fun updateDate(newDate: Instant) {
        _selectedDate.value = newDate
    }

    // 添加图片管理相关的方法
    fun updateImages(newImages: List<Uri>) {
        _selectedImages.value = newImages
    }

    fun addImage(imageUri: Uri) {
        _selectedImages.value = _selectedImages.value + imageUri
    }

    fun removeImage(imageUri: Uri) {
        _selectedImages.value = _selectedImages.value.filter { it != imageUri }
    }

    // 更新目标选择的方法
    fun updateGoalId(goalId: String?) {
        _selectedGoalId.value = goalId
    }

    // 添加加载可用目标的方法
    private fun loadAvailableGoals() {
        viewModelScope.launch {
            try {
                // 这里应该是从GoalRepository加载数据
                // 现在使用模拟数据
                _availableGoals.value = generateMockGoals()
            } catch (e: Exception) {
                android.util.Log.e("ReflectionDetailViewModel", "加载目标失败", e)
            }
        }
    }

    // 生成模拟目标数据方法
    private fun generateMockGoals(): List<GoalSummary> {
        return listOf(
            GoalSummary("1", "完成Android应用开发"),
            GoalSummary("2", "每天锻炼30分钟"),
            GoalSummary("3", "学习Kotlin进阶知识"),
            GoalSummary("4", "每周读一本书"),
            GoalSummary("5", "半马训练计划")
        )
    }

    // 进入编辑模式
    fun enterEditMode() {
        _isEditMode.value = true
    }

    // 修改loadReflection方法，加载关联目标
    fun loadReflection(reflectionId: String) {
        if (reflectionId.isBlank()) {
            // 空ID，表示新建感想
            initNewReflection()
            return
        }
        
        // 有ID，加载已有感想
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                
                val reflection = reflectionRepository.getReflectionById(reflectionId)
                
                // 更新表单状态
                _title.value = reflection.title
                _content.value = reflection.content
                _selectedMood.value = reflection.mood
                _selectedTags.value = reflection.tags
                _selectedType.value = reflection.type
                _selectedDate.value = reflection.date
                
                // 加载关联目标ID (假设在metrics中有goalId字段)
                _selectedGoalId.value = reflection.metrics["goalId"]
                
                // 提取图片URI
                val imageUris = reflection.richContent
                    .filter { it.type == "image" }
                    .mapNotNull { 
                        try { 
                            if (it.value.startsWith("/")) {
                                // 文件路径，转换为URI
                                Uri.fromFile(java.io.File(it.value))
                            } else {
                                Uri.parse(it.value)
                            }
                        } catch (e: Exception) { null } 
                    }
                _selectedImages.value = imageUris
                
                _uiState.update { 
                    it.copy(
                        reflection = reflection,
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        errorMessage = e.message ?: "加载感想详情失败",
                        isLoading = false
                    )
                }
            }
        }
    }
    
    // 修改初始化新感想的方法，使用选定日期
    private fun initNewReflection() {
        // 设置为编辑模式
        _isEditMode.value = true
        
        // 设置默认值
        _title.value = ""
        _content.value = ""
        _selectedMood.value = MoodType.CALM
        _selectedTags.value = listOf("日常")
        _selectedType.value = ReflectionType.LIFE
        _selectedDate.value = Instant.now() // 默认使用当前时间
        
        // 创建空感想对象
        val newReflection = Reflection(
            id = UUID.randomUUID().toString(),
            title = "",
            content = "",
            richContent = emptyList(),
            date = _selectedDate.value, // 使用选定日期
            rating = 3,
            tags = _selectedTags.value,
            type = _selectedType.value,
            mood = _selectedMood.value,
            plans = emptyList(),
            backgroundImage = null
        )
        
        _uiState.update { 
            it.copy(
                reflection = newReflection,
                isLoading = false,
                isNewReflection = true
            )
        }
    }
    
    // 修改saveReflection方法，保存关联目标和图片
    fun saveReflection(context: Context) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSaving = true) }
                
                val currentState = _uiState.value
                val reflection = currentState.reflection
                
                if (reflection != null) {
                    // 🆕 先保存图片到内部存储
                    val savedImagePaths = saveSelectedImages(context)
                    
                    // 构建富文本内容列表，包含文本和图片
                    val richContentBlocks = mutableListOf<ContentBlock>()
                    
                    // 添加文本内容块
                    if (_content.value.isNotEmpty()) {
                        richContentBlocks.add(ContentBlock("text", _content.value))
                    }
                    
                    // 添加图片内容块（使用保存后的路径）
                    for (imagePath in savedImagePaths) {
                        richContentBlocks.add(ContentBlock("image", imagePath))
                    }
                    
                    // 构建metrics，包含goalId
                    val metrics = reflection.metrics.toMutableMap()
                    if (_selectedGoalId.value != null) {
                        metrics["goalId"] = _selectedGoalId.value!!
                    } else {
                        metrics.remove("goalId")
                    }
                    
                    // 创建更新后的感想对象
                    val updatedReflection = reflection.copy(
                        title = _title.value,
                        content = _content.value,
                        richContent = richContentBlocks,
                        tags = _selectedTags.value,
                        type = _selectedType.value,
                        mood = _selectedMood.value,
                        date = _selectedDate.value,
                        metrics = metrics
                    )
                    
                    // 保存到仓库
                    reflectionRepository.saveReflection(updatedReflection)
                    
                    // 更新UI状态
                    _uiState.update { 
                        it.copy(
                            reflection = updatedReflection,
                            isSaving = false,
                            isEditMode = false,
                            saveSuccessful = true
                        )
                    }
                    
                    // 退出编辑模式
                    _isEditMode.value = false
                    
                    // 清空已选图片（因为已保存到richContent中）
                    _selectedImages.value = emptyList()
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        errorMessage = e.message ?: "保存感想失败",
                        isSaving = false
                    )
                }
            }
        }
    }
    
    // 更新表单字段
    fun updateTitle(newTitle: String) {
        _title.value = newTitle
    }
    
    fun updateContent(newContent: String) {
        _content.value = newContent
    }
    
    fun updateMood(newMood: MoodType) {
        _selectedMood.value = newMood
    }
    
    fun updateTags(newTags: List<String>) {
        _selectedTags.value = newTags
    }
    
    fun updateType(newType: ReflectionType) {
        _selectedType.value = newType
    }
    
    fun shareReflection() {
        // 实现分享逻辑
    }

    fun addToMotivationWall() {
        // 实现添加到激励墙逻辑
    }
    
    // 🆕 保存图片到内部存储的方法
    private suspend fun saveImageToInternalStorage(context: Context, imageUri: Uri): String? {
        return withContext(Dispatchers.IO) {
            try {
                val inputStream: InputStream? = context.contentResolver.openInputStream(imageUri)
                inputStream?.use { input ->
                    val fileName = "reflection_${System.currentTimeMillis()}_${UUID.randomUUID()}.jpg"
                    val file = File(context.filesDir, "reflection_images")
                    if (!file.exists()) {
                        file.mkdirs()
                    }
                    val imageFile = File(file, fileName)
                    
                    FileOutputStream(imageFile).use { output ->
                        input.copyTo(output)
                    }
                    
                    imageFile.absolutePath
                }
            } catch (e: Exception) {
                android.util.Log.e("ReflectionDetailViewModel", "保存图片失败", e)
                null
            }
        }
    }
    
    // 🆕 批量保存图片的方法
    private suspend fun saveSelectedImages(context: Context): List<String> {
        val savedPaths = mutableListOf<String>()
        for (uri in _selectedImages.value) {
            val savedPath = saveImageToInternalStorage(context, uri)
            if (savedPath != null) {
                savedPaths.add(savedPath)
            }
        }
        return savedPaths
    }
}

/**
 * 感想详情页状态类
 */
data class ReflectionDetailState(
    val reflection: Reflection? = null,
    val isLoading: Boolean = true,
    val isSaving: Boolean = false,
    val errorMessage: String? = null,
    val isNewReflection: Boolean = false,
    val isEditMode: Boolean = false,
    val saveSuccessful: Boolean = false
) 