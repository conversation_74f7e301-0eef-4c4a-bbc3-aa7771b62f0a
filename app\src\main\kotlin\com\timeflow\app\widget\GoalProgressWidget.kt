package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity

/**
 * 目标进度小组件 - 显示习惯和目标的完成进度
 */
class GoalProgressWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.widget_goal_progress)
        
        // 设置总体进度
        views.setTextViewText(R.id.widget_overall_progress, "65%")
        views.setProgressBar(R.id.widget_overall_progress_bar, 100, 65, false)
        
        // 设置习惯1
        views.setViewVisibility(R.id.widget_habit_1_container, android.view.View.VISIBLE)
        views.setTextViewText(R.id.widget_habit_1_name, "Maybe you should...")
        views.setTextViewText(R.id.widget_habit_1_progress, "81%")
        views.setProgressBar(R.id.widget_habit_1_bar, 100, 81, false)
        views.setTextViewText(R.id.widget_habit_1_stats, "81→100")
        
        // 设置习惯2
        views.setViewVisibility(R.id.widget_habit_2_container, android.view.View.VISIBLE)
        views.setTextViewText(R.id.widget_habit_2_name, "whaddaya say")
        views.setTextViewText(R.id.widget_habit_2_progress, "48%")
        views.setProgressBar(R.id.widget_habit_2_bar, 100, 48, false)
        views.setTextViewText(R.id.widget_habit_2_stats, "19→40")
        
        // 隐藏空状态
        views.setViewVisibility(R.id.widget_empty_goals, android.view.View.GONE)
        
        // 设置点击事件
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "habits")
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
    }
}
