package com.timeflow.app.ui.screen.goal

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.viewmodel.GoalViewModel
import com.timeflow.app.data.model.*
import com.timeflow.app.data.model.getDefaultGoalCategories
import com.timeflow.app.data.model.getTimeBasedCategories
import com.timeflow.app.data.model.getThemeBasedCategories
import com.timeflow.app.ui.components.goal.SmartCategoryRecommendation
import com.timeflow.app.ai.GoalCategoryClassifier

/**
 * 目标分类管理界面 - 参照Calflow设计
 * 支持颜色编码和自定义分类
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalCategoryManagementScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel()
) {
    var showAddDialog by remember { mutableStateOf(false) }
    var editingCategory by remember { mutableStateOf<GoalCategory?>(null) }
    
    val timeBasedCategories = remember { getTimeBasedCategories() }
    val themeBasedCategories = remember { getThemeBasedCategories() }
    val allCategories = remember { getDefaultGoalCategories().toMutableList() }
    val predefinedColors = remember { getPredefinedColors() }
    val predefinedIcons = remember { getPredefinedIcons() }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "分类管理",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { showAddDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加分类"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        },
        containerColor = Color(0xFFF8F9FA)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 分类统计
            item {
                CategoryStatisticsCard(
                    timeBasedCount = timeBasedCategories.size,
                    themeBasedCount = themeBasedCategories.size,
                    customCount = 0 // TODO: 从数据库获取自定义分类数量
                )
            }

            // 智能分类测试区域
            item {
                SmartCategoryTestSection()
            }

            // 时间维度分类
            item {
                CategorySectionHeader(
                    title = "时间维度分类",
                    subtitle = "按时间框架组织目标",
                    icon = Icons.Default.Schedule
                )
            }

            items(timeBasedCategories) { category ->
                CategoryCard(
                    category = category,
                    onEdit = { editingCategory = it },
                    onDelete = null, // 默认分类不能删除
                    isCustom = false
                )
            }

            // 主题维度分类
            item {
                CategorySectionHeader(
                    title = "主题维度分类",
                    subtitle = "按内容主题组织目标",
                    icon = Icons.Default.Category
                )
            }

            items(themeBasedCategories) { category ->
                CategoryCard(
                    category = category,
                    onEdit = { editingCategory = it },
                    onDelete = null, // 默认分类不能删除
                    isCustom = false
                )
            }
            
            // 添加新分类提示
            item {
                AddCategoryPrompt(
                    onClick = { showAddDialog = true }
                )
            }
        }
    }
    
    // 添加/编辑分类对话框
    if (showAddDialog || editingCategory != null) {
        CategoryEditDialog(
            category = editingCategory,
            predefinedColors = predefinedColors,
            predefinedIcons = predefinedIcons,
            onDismiss = { 
                showAddDialog = false
                editingCategory = null
            },
            onSave = { newCategory ->
                if (editingCategory != null) {
                    // TODO: 实现编辑分类功能
                } else {
                    // TODO: 实现添加自定义分类到数据库
                }
                showAddDialog = false
                editingCategory = null
            }
        )
    }
}

/**
 * 分类区域标题
 */
@Composable
fun CategorySectionHeader(
    title: String,
    subtitle: String,
    icon: ImageVector
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(vertical = 8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color(0xFF6B7280),
            modifier = Modifier.size(20.dp)
        )

        Column {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F2937)
            )
            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = Color(0xFF6B7280)
            )
        }
    }
}

/**
 * 分类统计卡片
 */
@Composable
fun CategoryStatisticsCard(
    timeBasedCount: Int,
    themeBasedCount: Int,
    customCount: Int
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "分类概览",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1F2937)
                )
                
                Icon(
                    imageVector = Icons.Default.Category,
                    contentDescription = null,
                    tint = Color(0xFF6366F1),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "时间维度",
                    value = "$timeBasedCount",
                    color = Color(0xFF3B82F6)
                )

                StatisticItem(
                    label = "主题维度",
                    value = "$themeBasedCount",
                    color = Color(0xFF10B981)
                )

                StatisticItem(
                    label = "自定义",
                    value = "$customCount",
                    color = Color(0xFF8B5CF6)
                )
            }
        }
    }
}

/**
 * 统计项
 */
@Composable
fun StatisticItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 分类卡片
 */
@Composable
fun CategoryCard(
    category: GoalCategory,
    onEdit: (GoalCategory) -> Unit,
    onDelete: ((GoalCategory) -> Unit)?,
    isCustom: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = category.color.copy(alpha = 0.1f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = category.icon,
                    contentDescription = null,
                    tint = category.color,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 内容
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = category.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1F2937)
                    )
                    
                    if (!isCustom) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "预设",
                            fontSize = 10.sp,
                            color = Color(0xFF6B7280),
                            modifier = Modifier
                                .background(
                                    color = Color(0xFFF3F4F6),
                                    shape = RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 4.dp, vertical = 2.dp)
                        )
                    }
                }
                
                Text(
                    text = category.description,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
            
            // 操作按钮
            Row {
                IconButton(
                    onClick = { onEdit(category) },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑",
                        tint = Color(0xFF6B7280),
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                if (onDelete != null) {
                    IconButton(
                        onClick = { onDelete(category) },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFEF4444),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 添加分类提示
 */
@Composable
fun AddCategoryPrompt(
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F9FF)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        border = BorderStroke(1.dp, Color(0xFF6366F1).copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                tint = Color(0xFF6366F1),
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "添加新分类",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF6366F1)
            )
        }
    }
}

/**
 * 分类编辑对话框
 */
@Composable
fun CategoryEditDialog(
    category: GoalCategory?,
    predefinedColors: List<Color>,
    predefinedIcons: List<ImageVector>,
    onDismiss: () -> Unit,
    onSave: (GoalCategory) -> Unit
) {
    var name by remember { mutableStateOf(category?.name ?: "") }
    var description by remember { mutableStateOf(category?.description ?: "") }
    var selectedColor by remember { mutableStateOf(category?.color ?: predefinedColors.first()) }
    var selectedIcon by remember { mutableStateOf(category?.icon ?: predefinedIcons.first()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = if (category == null) "添加分类" else "编辑分类",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 名称输入
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("分类名称") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        focusedLabelColor = selectedColor
                    )
                )

                // 描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = selectedColor,
                        focusedLabelColor = selectedColor
                    )
                )

                // 颜色选择
                Text(
                    text = "选择颜色",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937)
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(predefinedColors) { color ->
                        ColorOption(
                            color = color,
                            isSelected = selectedColor == color,
                            onClick = { selectedColor = color }
                        )
                    }
                }

                // 图标选择
                Text(
                    text = "选择图标",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937)
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(predefinedIcons) { icon ->
                        IconOption(
                            icon = icon,
                            color = selectedColor,
                            isSelected = selectedIcon == icon,
                            onClick = { selectedIcon = icon }
                        )
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (name.isNotBlank()) {
                        val newCategory = GoalCategory(
                            id = category?.id ?: "custom_${System.currentTimeMillis()}",
                            name = name,
                            description = description,
                            icon = selectedIcon,
                            color = selectedColor
                        )
                        onSave(newCategory)
                    }
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = selectedColor
                )
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            OutlinedButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 颜色选项
 */
@Composable
fun ColorOption(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(32.dp)
            .background(
                color = color,
                shape = CircleShape
            )
            .clickable { onClick() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        width = 3.dp,
                        color = Color.White,
                        shape = CircleShape
                    )
                } else Modifier
            ),
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 图标选项
 */
@Composable
fun IconOption(
    icon: ImageVector,
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .background(
                color = if (isSelected) color.copy(alpha = 0.2f) else Color(0xFFF3F4F6),
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onClick() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        width = 2.dp,
                        color = color,
                        shape = RoundedCornerShape(8.dp)
                    )
                } else Modifier
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = if (isSelected) color else Color(0xFF6B7280),
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * 获取预定义颜色
 */
fun getPredefinedColors(): List<Color> {
    return listOf(
        Color(0xFF6366F1), // 蓝色
        Color(0xFFEF4444), // 红色
        Color(0xFF10B981), // 绿色
        Color(0xFFF59E0B), // 橙色
        Color(0xFF8B5CF6), // 紫色
        Color(0xFFEC4899), // 粉色
        Color(0xFF06B6D4), // 青色
        Color(0xFF84CC16), // 黄绿色
        Color(0xFFF97316), // 深橙色
        Color(0xFF6B7280)  // 灰色
    )
}

/**
 * 获取预定义图标
 */
fun getPredefinedIcons(): List<ImageVector> {
    return listOf(
        Icons.Default.FitnessCenter,
        Icons.Default.School,
        Icons.Default.Work,
        Icons.Default.AttachMoney,
        Icons.Default.People,
        Icons.Default.Palette,
        Icons.Default.Home,
        Icons.Default.Person,
        Icons.Default.Star,
        Icons.Default.Favorite,
        Icons.Default.Build,
        Icons.Default.Flight,
        Icons.Default.Restaurant,
        Icons.Default.MusicNote,
        Icons.Default.Camera,
        Icons.Default.Book
    )
}

/**
 * 智能分类测试区域
 */
@Composable
private fun SmartCategoryTestSection() {
    var testTitle by remember { mutableStateOf("") }
    var testDescription by remember { mutableStateOf("") }
    var selectedCategoryId by remember { mutableStateOf("personal_development") }
    var isExpanded by remember { mutableStateOf(false) }

    val classifier = remember { GoalCategoryClassifier() }

    // 预设测试用例
    val quickTestCases = remember {
        listOf(
            "每天跑步30分钟" to "提高身体素质，减重5公斤",
            "学习Python编程" to "掌握基础语法，完成项目",
            "读完10本书" to "提升知识面，培养阅读习惯",
            "存钱买房" to "每月存款5000元，攒够首付",
            "提升工作技能" to "学习项目管理，争取升职"
        )
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题和展开按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { isExpanded = !isExpanded },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = null,
                        tint = Color(0xFF8B5CF6),
                        modifier = Modifier.size(20.dp)
                    )

                    Column {
                        Text(
                            text = "智能分类测试",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF1F2937)
                        )
                        Text(
                            text = "测试AI分类功能的准确性",
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                Icon(
                    imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "收起" else "展开",
                    tint = Color(0xFF6B7280),
                    modifier = Modifier.size(20.dp)
                )
            }

            // 展开的测试内容
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically() + fadeIn(),
                exit = shrinkVertically() + fadeOut()
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 快速测试按钮
                    Text(
                        text = "快速测试",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF374151)
                    )

                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        contentPadding = PaddingValues(horizontal = 4.dp)
                    ) {
                        items(quickTestCases) { (title, description) ->
                            QuickTestButton(
                                title = title,
                                onClick = {
                                    testTitle = title
                                    testDescription = description
                                }
                            )
                        }
                    }

                    Divider(color = Color(0xFFE5E7EB))

                    // 自定义测试
                    Text(
                        text = "自定义测试",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF374151)
                    )

                    OutlinedTextField(
                        value = testTitle,
                        onValueChange = { testTitle = it },
                        label = { Text("目标标题") },
                        modifier = Modifier.fillMaxWidth(),
                        placeholder = { Text("输入目标标题...") }
                    )

                    OutlinedTextField(
                        value = testDescription,
                        onValueChange = { testDescription = it },
                        label = { Text("目标描述") },
                        modifier = Modifier.fillMaxWidth(),
                        placeholder = { Text("详细描述目标...") },
                        maxLines = 3
                    )

                    // 智能推荐结果
                    if (testTitle.isNotBlank() || testDescription.isNotBlank()) {
                        SmartCategoryRecommendation(
                            title = testTitle,
                            description = testDescription,
                            selectedCategoryId = selectedCategoryId,
                            onCategorySelected = { selectedCategoryId = it }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 快速测试按钮
 */
@Composable
private fun QuickTestButton(
    title: String,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier.clickable { onClick() },
        color = Color(0xFFF3F4F6),
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(1.dp, Color(0xFFE5E7EB))
    ) {
        Text(
            text = title,
            fontSize = 12.sp,
            color = Color(0xFF374151),
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
        )
    }
}
