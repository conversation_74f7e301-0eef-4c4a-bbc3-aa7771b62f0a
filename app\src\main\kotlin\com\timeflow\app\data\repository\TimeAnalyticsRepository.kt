package com.timeflow.app.data.repository

import com.timeflow.app.data.model.AppUsageData
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * 时间分析仓库接口
 */
interface TimeAnalyticsRepository {
    /**
     * 获取指定日期的应用使用数据
     */
    fun getAppUsageByDate(date: LocalDate): Flow<List<AppUsageData>>
    
    /**
     * 保存应用使用数据
     */
    suspend fun saveAppUsage(appUsage: AppUsageData, date: LocalDate)
    
    /**
     * 批量保存应用使用数据
     */
    suspend fun saveAppUsageBatch(appUsages: List<AppUsageData>, date: LocalDate)
    
    /**
     * 获取指定日期范围的应用使用数据
     */
    fun getAppUsageByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<Map<LocalDate, List<AppUsageData>>>
    
    /**
     * 获取应用类别统计
     */
    fun getCategoryStatsByDate(date: LocalDate): Flow<Map<String, kotlin.time.Duration>>
    
    /**
     * 获取生产力和分心时间统计
     */
    fun getProductivityStatsByDate(date: LocalDate): Flow<Pair<kotlin.time.Duration, kotlin.time.Duration>>
    
    /**
     * 获取最近的应用使用趋势（每日总使用时间）
     */
    fun getRecentUsageTrend(days: Int): Flow<Map<LocalDate, kotlin.time.Duration>>
} 