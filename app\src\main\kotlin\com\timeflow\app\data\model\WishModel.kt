package com.timeflow.app.data.model

import java.time.LocalDateTime

/**
 * 愿望业务模型
 */
data class WishModel(
    val id: String,
    val title: String,
    val description: String = "",
    val category: WishCategory = WishCategory.OTHER,
    val priority: Int = 3, // 1-5星级
    val inspirationItems: List<String> = emptyList(),
    val imageUris: List<String> = emptyList(), // 图片URI列表
    val estimatedCost: Float? = null,
    val targetTimePeriod: String = "",
    val tags: List<String> = emptyList(),
    val status: WishStatus = WishStatus.ACTIVE,
    val relatedGoalId: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val achievedAt: LocalDateTime? = null,
    val archivedAt: LocalDateTime? = null,
    val isArchived: Boolean = false,
    val difficulty: WishDifficulty = WishDifficulty.MEDIUM,
    val motivation: String = "",
    val prerequisites: List<String> = emptyList(),
    val notes: String = ""
)

/**
 * 愿望类别
 */
enum class WishCategory(val displayName: String, val emoji: String) {
    TRAVEL("旅行", "🗻"),
    SHOPPING("购物", "🛒"),
    LEARNING("学习", "📚"),
    CAREER("事业", "💼"),
    LIFESTYLE("生活", "🌱"),
    HEALTH("健康", "💪"),
    HOBBY("爱好", "🎨"),
    RELATIONSHIP("关系", "👥"),
    OTHER("其他", "⭐")
}

/**
 * 愿望状态
 */
enum class WishStatus(val displayName: String) {
    ACTIVE("活跃"),
    CONVERTED_TO_GOAL("已转目标"),
    ARCHIVED("已归档"),
    ACHIEVED("已实现")
}

/**
 * 愿望难度
 */
enum class WishDifficulty(val displayName: String, val level: Int) {
    EASY("简单", 1),
    MEDIUM("中等", 2),
    HARD("困难", 3),
    EXTREME("极难", 4)
} 