package com.timeflow.app.utils

import com.timeflow.app.BuildConfig
import timber.log.Timber

/**
 * 日志配置工具类
 * 根据是否为生产环境优化日志输出
 */
object LogConfig {
    
    // 是否输出详细日志
    private var isVerboseLogging = BuildConfig.DEBUG
    
    /**
     * 初始化日志系统
     */
    fun initLogging() {
        if (BuildConfig.DEBUG) {
            // 开发模式：使用完整日志树
            Timber.plant(Timber.DebugTree())
        } else {
            // 生产模式：使用精简日志树，只记录警告和错误
            Timber.plant(ReleaseTree())
        }
    }
    
    /**
     * 设置是否输出详细日志
     */
    fun setVerboseLogging(verbose: Boolean) {
        isVerboseLogging = verbose
    }
    
    /**
     * 检查是否允许输出详细日志
     */
    fun isVerboseLoggingEnabled(): Boolean {
        return isVerboseLogging
    }
    
    /**
     * 生产环境日志树
     * 只记录警告和错误级别的日志
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            // 只记录警告和错误级别的日志
            if (priority >= android.util.Log.WARN) {
                // 在生产环境中，可以将日志发送到崩溃报告服务或分析服务
                android.util.Log.println(priority, tag, message)
                
                // 错误日志可以收集到崩溃报告服务
                if (priority == android.util.Log.ERROR && t != null) {
                    // 在实际应用中，可以调用崩溃报告SDK上报错误
                    // 例如：Crashlytics.logException(t)
                }
            }
        }
    }
} 