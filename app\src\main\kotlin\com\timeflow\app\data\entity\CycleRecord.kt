package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.timeflow.app.data.converter.LocalDateConverter
import java.time.LocalDate

/**
 * 生理周期记录实体类
 */
@Entity(tableName = "cycle_records")
@TypeConverters(LocalDateConverter::class)
data class CycleRecord(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,
    val startDate: LocalDate,
    val endDate: LocalDate? = null, // 可为空表示正在进行中
    val startTime: String? = null,   // 经期开始时间 (HH:mm格式)
    val endTime: String? = null,     // 经期结束时间 (HH:mm格式)
    val cycleLength: Int? = null,   // 整个周期长度，可计算
    val periodLength: Int? = null,   // 经期长度，可计算
    val notes: String? = null,       // 备注信息
    val createdAt: LocalDate = LocalDate.now() // 创建时间
) 