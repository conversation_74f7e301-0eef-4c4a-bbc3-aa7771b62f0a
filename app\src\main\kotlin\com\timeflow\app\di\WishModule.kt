package com.timeflow.app.di

import com.timeflow.app.data.dao.WishDao
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.data.repository.WishRepository
import com.timeflow.app.data.repository.WishRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object WishModule {

    @Provides
    @Singleton
    fun provideWishDao(database: AppDatabase): WishDao {
        return database.wishDao()
    }

    @Provides
    @Singleton
    fun provideWishRepository(wishDao: WishDao): WishRepository {
        return WishRepositoryImpl(wishDao)
    }
} 