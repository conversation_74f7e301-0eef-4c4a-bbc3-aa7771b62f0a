plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("kotlin-parcelize")
    // id("org.jetbrains.kotlin.kapt") // 注释掉kapt，使用KSP替代
    id("com.google.devtools.ksp") version "1.9.22-1.0.17" // 更新KSP版本以匹配Kotlin 1.9.22
    id("com.google.dagger.hilt.android")
    id("org.jetbrains.kotlin.plugin.serialization")
    // Temporarily commented out Firebase plugins until google-services.json is available
    // id("com.google.firebase.crashlytics")
    // id("com.google.gms.google-services")
}

android {
    namespace = "com.timeflow.app"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.timeflow.app"
        minSdk = 26
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    // 配置源代码目录，只使用kotlin目录
    sourceSets {
        getByName("main") {
            java.srcDirs("src/main/kotlin")
            // 不包含java目录
            java.setSrcDirs(listOf("src/main/kotlin"))
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    // 禁用构建缓存，解决jlink和JDK相关错误
    buildOptions {
        // 完全禁用构建缓存，解决jlink错误
        buildCache {
            enabled = false
        }
    }
    
    // 解决JDK 21兼容性问题
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }
    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs += listOf(
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
            "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
            "-opt-in=kotlin.ExperimentalStdlibApi",
            "-opt-in=kotlin.experimental.ExperimentalTypeInference",
            "-opt-in=kotlin.ExperimentalUnsignedTypes",
            "-Xallow-kotlin-package",
            "-Xcontext-receivers",
            "-Xopt-in=kotlin.RequiresOptIn",
            "-Xallow-unstable-dependencies"
        )
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.7"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    // Core Android dependencies
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.activity:activity-compose:1.8.2")
    
    // 添加javax.inject依赖，解决@ApplicationContext注解问题
    implementation("javax.inject:javax.inject:1")
    
    // 添加Android Desugaring库，解决JDK兼容性问题
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")

    // Performance Monitoring
    implementation("androidx.metrics:metrics-performance:1.0.0-beta01")
    
    // Gson for JSON processing
    implementation("com.google.code.gson:gson:2.10.1")

    // Material Design
    implementation("com.google.android.material:material:1.11.0")
    
    // Compose
    val composeBomVersion = "2023.10.01"
    implementation(platform("androidx.compose:compose-bom:$composeBomVersion"))
    implementation("androidx.compose.ui:ui")
    implementation("androidx.compose.ui:ui-graphics")
    implementation("androidx.compose.ui:ui-tooling-preview")
    implementation("androidx.compose.material3:material3:1.2.0")
    implementation("androidx.compose.material:material-icons-extended")
    implementation("androidx.constraintlayout:constraintlayout-compose:1.0.1")
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0")
    implementation("androidx.navigation:navigation-compose:2.7.6")
    
    // Accompanist
    val accompanistVersion = "0.32.0"
    implementation("com.google.accompanist:accompanist-systemuicontroller:$accompanistVersion")
    implementation("com.google.accompanist:accompanist-permissions:$accompanistVersion")
    
    // Room Database
    val roomVersion = "2.6.1"
    implementation("androidx.room:room-runtime:$roomVersion")
    // kapt("androidx.room:room-compiler:$roomVersion")
    ksp("androidx.room:room-compiler:$roomVersion")
    implementation("androidx.room:room-ktx:$roomVersion")
    
    // Hilt Dependency Injection
    val hiltVersion = "2.50"
    implementation("com.google.dagger:hilt-android:$hiltVersion")
    // kapt("com.google.dagger:hilt-android-compiler:$hiltVersion")
    ksp("com.google.dagger:hilt-android-compiler:$hiltVersion")
    implementation("androidx.hilt:hilt-navigation-compose:1.1.0")
    implementation("androidx.hilt:hilt-work:1.1.0")
    // kapt("androidx.hilt:hilt-compiler:1.1.0")
    ksp("androidx.hilt:hilt-compiler:1.1.0")
    
    // Kotlin Coroutines and Flow
    val coroutinesVersion = "1.7.3"
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutinesVersion")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion")
    
    // JSON Serialization
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.2")
    
    // WorkManager for background tasks
    val workVersion = "2.9.0"
    implementation("androidx.work:work-runtime-ktx:$workVersion")
    
    // ML Kit for AI features
    implementation("com.google.mlkit:text-recognition:16.0.0")
    
    // DateTime
    implementation("org.jetbrains.kotlinx:kotlinx-datetime:0.5.0")
    
    // Coil for image loading
    implementation("io.coil-kt:coil-compose:2.6.0")
    
    // App Startup
    implementation("androidx.startup:startup-runtime:1.1.1")
    
    // 日志库
    implementation("com.jakewharton.timber:timber:5.0.1")
    
    // 支付SDK依赖
    // 支付宝SDK (真实项目需要从支付宝开放平台下载)
    // implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar", "*.jar"))))
    // 微信支付SDK (真实项目需要从微信开放平台下载)
    // implementation("com.tencent.mm.opensdk:wechat-sdk-android:6.8.0")
    
    // HTTP网络请求库 (用于支付回调验证)
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    
    // 加密库 (用于支付签名)
    implementation("org.bouncycastle:bcprov-jdk15on:1.70")
    
    // 内存泄漏检测（仅在Debug构建中启用）
    debugImplementation("com.squareup.leakcanary:leakcanary-android:2.12")
    
    // Reorderable for drag-and-drop
    implementation("org.burnoutcrew.composereorderable:reorderable:0.9.6")
    
    // Testing
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
    androidTestImplementation(platform("androidx.compose:compose-bom:$composeBomVersion"))
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    debugImplementation("androidx.compose.ui:ui-tooling")
    debugImplementation("androidx.compose.ui:ui-test-manifest")

    // Firebase
    // Temporarily commented out Firebase dependencies until google-services.json is available
    // implementation(platform("com.google.firebase:firebase-bom:32.7.2"))
    // implementation("com.google.firebase:firebase-analytics")
    // implementation("com.google.firebase:firebase-crashlytics")
    
    // Alternative crash reporting (simple local implementation)
    // No external dependencies needed
}