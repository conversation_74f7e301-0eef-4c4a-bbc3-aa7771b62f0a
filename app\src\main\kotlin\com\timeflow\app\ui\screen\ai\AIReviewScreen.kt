package com.timeflow.app.ui.screen.ai

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.Locale
import androidx.compose.foundation.clickable
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Canvas
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import com.timeflow.app.ui.theme.DustyLavender
import kotlin.math.cos
import kotlin.math.sin
import com.timeflow.app.ui.viewmodel.AiConfigViewModel

// 在文件顶部添加扩展属性
val WorkingStyle.displayName: String
    get() = when (this) {
        WorkingStyle.DEEP_WORKER -> "深度工作"
        WorkingStyle.MULTITASKER -> "多任务处理"
        WorkingStyle.STRUCTURED -> "结构化工作"
        WorkingStyle.FLEXIBLE -> "灵活工作"
        WorkingStyle.COLLABORATIVE -> "协作型工作"
    }

/**
 * AI智能复盘页面 - 基于复盘原则的数据驱动版本
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIReviewScreen(
    navController: NavController,
    viewModel: AIReviewViewModel = hiltViewModel(),
    aiConfigViewModel: AiConfigViewModel = hiltViewModel()
) {
    // 温柔可爱的主题颜色
    val primaryColor = Color(0xFF87787a) // 浅粉色
    val secondaryColor = Color(0xFFa585a5) // 淡紫色
    val accentColor = Color(0xFF867482) // 粉红色
    val backgroundColor = Color(0xFFE5DDD5) // 用户指定的背景色
    val cardColor = Color(0xFFf5f1ed) // 更新为用户指定的卡片背景色
    val textColor = Color(0xFF554f47) // 温和的深灰色
    val positiveColor = Color(0xFF5c8241) // 淡绿色
    
    // 状态
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val timeFrames = listOf("日", "周", "月", "年")
    
    // 从ViewModel获取状态
    val selectedTimeFrame by viewModel.selectedTimeFrame.collectAsState()
    val reviewData by viewModel.reviewData.collectAsState()
    val isGeneratingAnalysis by viewModel.isGeneratingAnalysis.collectAsState()
    
    // 加载AI配置
    LaunchedEffect(Unit) {
        aiConfigViewModel.loadConfigurations(context)
    }
    
    // 基于复盘原则的模型映射
    val reviewModelMap = mapOf(
        "日" to "✨ 每日成长记录",
        "周" to "🌱 一周收获总结", 
        "月" to "🌸 月度进步回顾",
        "年" to "🎉 年度成就庆典"
    )
    
    // 复盘原则说明
    val reviewPrincipleMap = mapOf(
        "日" to "KISS原则 - 保持简单，专注核心",
        "周" to "PDCA循环 - 计划→执行→检查→改进",
        "月" to "GRAI框架 - 目标→结果→分析→洞察",
        "年" to "KPT模型 - 保持→改进→尝试"
    )
    
    Scaffold(
        topBar = {
            // 温柔的顶部应用栏
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(backgroundColor, backgroundColor.copy(alpha = 0.9f))
                        )
                    )
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
            ) {
                TopAppBar(
                    title = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                        Text(
                                text = "🌸",
                                fontSize = 18.sp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "AI复盘",
                            style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.SemiBold,
                                    fontSize = 16.sp // 缩小字体
                            ),
                            color = textColor
                        )
                        }
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.Outlined.ArrowBack,
                                contentDescription = "返回",
                                tint = textColor,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent,
                        titleContentColor = textColor
                    )
                )
            }
        },
        containerColor = backgroundColor
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp) // 减小间距
        ) {
            // 温柔的时间范围选择器
            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                            text = "🕐",
                            fontSize = 14.sp
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "选择回顾时光",
                            fontSize = 13.sp, // 缩小字体
                        fontWeight = FontWeight.Medium,
                        color = textColor,
                            modifier = Modifier.padding(vertical = 6.dp)
                    )
                    }
                    
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(timeFrames) { timeFrame ->
                            val isSelected = timeFrame == selectedTimeFrame
                            
                            CuteTimeFrameChip(
                                timeFrame = timeFrame,
                                isSelected = isSelected,
                                primaryColor = primaryColor,
                                accentColor = accentColor,
                                onClick = { 
                                    viewModel.setTimeFrame(timeFrame)
                                }
                            )
                        }
                    }
                }
            }
            
            // 复盘原则说明卡片
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = cardColor
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 1.dp
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 时间范围信息
                        Text(
                            text = reviewData.displayTimeRange,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = textColor
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        // 复盘模型信息
                            Text(
                                text = reviewModelMap[selectedTimeFrame] ?: "",
                            fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = primaryColor
                            )
                        
                        Spacer(modifier = Modifier.height(2.dp))
                        
                        // 复盘原则说明
                        Text(
                            text = reviewPrincipleMap[selectedTimeFrame] ?: "",
                            fontSize = 10.sp,
                            color = textColor.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
            
            // 可爱的加载指示器
            item {
                AnimatedVisibility(
                    visible = reviewData.isLoading,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(
                                color = primaryColor,
                                modifier = Modifier.size(36.dp),
                                strokeWidth = 3.dp
                            )
                            
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            Text(
                                text = "🤖 AI小助手正在用心分析您的${selectedTimeFrame}度成长...",
                                fontSize = 12.sp,
                                color = textColor.copy(alpha = 0.7f),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
            
            // 基于复盘原则的数据分析卡片
            item {
                AnimatedVisibility(
                    visible = !reviewData.isLoading,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    when (selectedTimeFrame) {
                        "日" -> KISSReviewCard(reviewData, primaryColor, cardColor, textColor, positiveColor)
                        "周" -> PDCAReviewCard(reviewData, primaryColor, cardColor, textColor, positiveColor)
                        "月" -> GRAIReviewCard(reviewData, primaryColor, cardColor, textColor, positiveColor)
                        "年" -> KPTReviewCard(reviewData, primaryColor, cardColor, textColor, positiveColor)
                    }
                }
            }
            
            // 温柔的生产力得分卡片
            item {
                CuteProductivityScoreCard(
                    score = reviewData.productivityScore,
                    trendDirection = reviewData.trendAnalysis.trendDirection,
                    primaryColor = primaryColor,
                    positiveColor = positiveColor,
                    cardColor = cardColor
                )
            }
            
            // 可爱的趋势分析图表
            item {
                CuteTrendAnalysisCard(
                    trendData = reviewData.trendAnalysis,
                    timeFrame = selectedTimeFrame,
                    primaryColor = primaryColor,
                    cardColor = cardColor
                )
            }
            
            // 温柔的效率模式分析
            item {
                CuteEfficiencyPatternsCard(
                    patterns = reviewData.efficiencyPatterns,
                    primaryColor = primaryColor,
                    cardColor = cardColor
                )
            }
            
            // 积极的个性化洞察
            item {
                PositivePersonalizedInsightsCard(
                    insights = reviewData.personalizedInsights,
                    primaryColor = primaryColor,
                    positiveColor = positiveColor,
                    cardColor = cardColor
                )
            }
            
            // 温柔的对比分析
            item {
                CuteComparisonAnalysisCard(
                    comparison = reviewData.comparisonAnalysis,
                    timeFrame = selectedTimeFrame,
                    primaryColor = primaryColor,
                    cardColor = cardColor
                )
            }
            
            // AI分析生成按钮
            item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(20.dp),
                        colors = CardDefaults.cardColors(
                        containerColor = cardColor
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 1.dp
                        )
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                        // 可爱的顶部标题与图标
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Box(
                                    modifier = Modifier
                                    .size(40.dp)
                                        .background(
                                            brush = Brush.linearGradient(
                                                colors = listOf(primaryColor, secondaryColor)
                                            ),
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                Text(
                                    text = "🌟",
                                    fontSize = 18.sp
                                    )
                                }
                                
                            Spacer(modifier = Modifier.width(12.dp))
                                
                                Column {
                                    Text(
                                    text = "智能分析",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.SemiBold,
                                        color = textColor
                                    )
                                    
                                    Text(
                                    text = "基于${reviewPrincipleMap[selectedTimeFrame]}",
                                    fontSize = 10.sp,
                                        color = textColor.copy(alpha = 0.6f)
                                    )
                                }
                            }
                            
                        Spacer(modifier = Modifier.height(12.dp))
                            
                        // 温柔的数据统计卡片
                        CuteDataStatsCard(
                            reviewData = reviewData,
                            primaryColor = primaryColor,
                            textColor = textColor,
                            positiveColor = positiveColor
                        )
                            
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // AI分析功能说明
                        Text(
                            text = "📊 调用您配置的AI模型，基于${reviewPrincipleMap[selectedTimeFrame]}深度分析效率数据，生成专业洞察和改进建议",
                            fontSize = 10.sp,
                            color = textColor.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                            
                        Spacer(modifier = Modifier.height(12.dp))
                            
                        // 智能AI分析按钮 - 根据状态显示不同内容
                        val hasAnalysis by remember { derivedStateOf { viewModel.hasAIAnalysisForCurrentTimeFrame() } }
                        
                        Button(
                            onClick = { viewModel.generateAIAnalysis() },
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (hasAnalysis) primaryColor else secondaryColor
                            ),
                            enabled = !isGeneratingAnalysis
                        ) {
                            if (isGeneratingAnalysis) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "🤖 AI正在思考中...",
                                        fontSize = 12.sp,
                                        color = Color.White
                                    )
                                }
                            } else {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = if (hasAnalysis) "🔄" else "🧠",
                                        fontSize = 14.sp
                                    )
                                    Spacer(modifier = Modifier.width(6.dp))
                                    Text(
                                        text = if (hasAnalysis) "重新AI深度分析" else "专业AI深度分析",
                                        fontSize = 12.sp,
                                        color = Color.White,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                        
                        // 如果已有分析结果，显示上次分析时间和清除按钮
                        if (hasAnalysis && !isGeneratingAnalysis) {
                            Spacer(modifier = Modifier.height(6.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "✅ 已完成AI分析",
                                    fontSize = 10.sp,
                                    color = positiveColor,
                                    fontWeight = FontWeight.Medium
                                )
                                
                                // 清除缓存按钮
                                TextButton(
                                    onClick = { viewModel.clearAIAnalysisCache() },
                                    modifier = Modifier.height(24.dp),
                                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                                ) {
                                    Text(
                                        text = "🗑️ 清除",
                                        fontSize = 9.sp,
                                        color = textColor.copy(alpha = 0.7f)
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // AI配置状态指示
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            val aiConfig = aiConfigViewModel.getSelectedConfig()
                            if (aiConfig != null && aiConfig.apiKey.isNotBlank()) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "AI已配置",
                                    tint = positiveColor,
                                    modifier = Modifier.size(12.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "AI已配置：${aiConfig.modelName}",
                                    fontSize = 9.sp,
                                    color = positiveColor,
                                    fontWeight = FontWeight.Medium
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.Warning,
                                    contentDescription = "AI未配置",
                                    tint = Color(0xFFFF9800),
                                    modifier = Modifier.size(12.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "未配置AI，将使用智能模拟分析",
                                    fontSize = 9.sp,
                                    color = Color(0xFFFF9800),
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }
            
            // AI分析结果
            item {
                // 添加调试日志
                LaunchedEffect(reviewData.insights, reviewData.recommendations) {
                    Log.d("AIReviewScreen", "=== UI状态更新 ===")
                    Log.d("AIReviewScreen", "洞察数量: ${reviewData.insights.size}")
                    Log.d("AIReviewScreen", "建议数量: ${reviewData.recommendations.size}")
                    reviewData.insights.forEachIndexed { index, insight ->
                        Log.d("AIReviewScreen", "洞察${index + 1}: ${insight.title} - ${insight.description.take(50)}...")
                    }
                    reviewData.recommendations.forEachIndexed { index, recommendation ->
                        Log.d("AIReviewScreen", "建议${index + 1}: ${recommendation.title} - ${recommendation.description.take(50)}...")
                    }
                }
                
                AnimatedVisibility(
                    visible = reviewData.insights.isNotEmpty() || reviewData.recommendations.isNotEmpty(),
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // AI洞察卡片
                        if (reviewData.insights.isNotEmpty()) {
                            PositiveAIInsightsCard(
                                insights = reviewData.insights,
                                primaryColor = primaryColor,
                                positiveColor = positiveColor,
                                cardColor = cardColor
                            )
                        }
                        
                        // AI建议卡片
                        if (reviewData.recommendations.isNotEmpty()) {
                            PositiveAIRecommendationsCard(
                                recommendations = reviewData.recommendations,
                                primaryColor = primaryColor,
                                positiveColor = positiveColor,
                                cardColor = cardColor
                            )
                        }
                    }
                }
            }
            
            // 底部间距
            item {
                Spacer(modifier = Modifier.height(20.dp))
            }
        }
    }
}

// KISS原则复盘卡片 - 日复盘
@Composable
fun KISSReviewCard(
    reviewData: AIReviewData,
    primaryColor: Color,
    cardColor: Color,
    textColor: Color,
    positiveColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "✨", fontSize = 16.sp)
                Spacer(modifier = Modifier.width(8.dp))
                        Text(
                    text = "KISS原则 - 今日简单回顾",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = textColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Keep - 保持的好习惯
            ReviewSection(
                title = "Keep - 保持",
                icon = "✅",
                items = generateKeepItems(reviewData),
                color = positiveColor
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Improve - 需要改进的
            ReviewSection(
                title = "Improve - 改进", 
                icon = "🔧",
                items = generateImproveItems(reviewData),
                color = primaryColor
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Start - 开始尝试的
            ReviewSection(
                title = "Start - 开始",
                icon = "🚀", 
                items = generateStartItems(reviewData),
                color = Color(0xFFFFD700)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Stop - 停止的习惯
            ReviewSection(
                title = "Stop - 停止",
                icon = "🛑",
                items = generateStopItems(reviewData),
                color = Color(0xFFFF6B6B)
            )
        }
    }
}

// PDCA循环复盘卡片 - 周复盘
@Composable
fun PDCAReviewCard(
    reviewData: AIReviewData,
    primaryColor: Color,
    cardColor: Color,
    textColor: Color,
    positiveColor: Color
) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "🌱", fontSize = 16.sp)
                Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                    text = "PDCA循环 - 本周成长回顾",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                                            color = textColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Plan - 计划执行情况
            ReviewSection(
                title = "Plan - 计划",
                icon = "📋",
                items = generatePlanItems(reviewData),
                color = Color(0xFF4CAF50)
                                        )
                                        
                                        Spacer(modifier = Modifier.height(8.dp))
                                        
            // Do - 执行结果
            ReviewSection(
                title = "Do - 执行",
                icon = "⚡",
                items = generateDoItems(reviewData),
                color = Color(0xFF2196F3)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Check - 检查分析
            ReviewSection(
                title = "Check - 检查",
                icon = "🔍",
                items = generateCheckItems(reviewData),
                color = Color(0xFFFF9800)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Action - 改进行动
            ReviewSection(
                title = "Action - 改进",
                icon = "🎯",
                items = generateActionItems(reviewData),
                color = Color(0xFF9C27B0)
            )
        }
    }
}

// GRAI框架复盘卡片 - 月复盘
@Composable
fun GRAIReviewCard(
    reviewData: AIReviewData,
    primaryColor: Color,
    cardColor: Color,
    textColor: Color,
    positiveColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "🌸", fontSize = 16.sp)
                Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                    text = "GRAI框架 - 本月深度回顾",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                                            color = textColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Goal - 目标回顾
            ReviewSection(
                title = "Goal - 目标",
                icon = "🎯",
                items = generateGoalItems(reviewData),
                color = Color(0xFF4CAF50)
                                        )
                                        
                                        Spacer(modifier = Modifier.height(8.dp))
                                        
            // Result - 结果评估
            ReviewSection(
                title = "Result - 结果",
                icon = "📊",
                items = generateResultItems(reviewData),
                color = Color(0xFF2196F3)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Analysis - 原因分析
            ReviewSection(
                title = "Analysis - 分析",
                icon = "🔬",
                items = generateAnalysisItems(reviewData),
                color = Color(0xFFFF9800)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Insight - 洞察总结
            ReviewSection(
                title = "Insight - 洞察",
                icon = "💡",
                items = generateInsightItems(reviewData),
                color = Color(0xFF9C27B0)
            )
        }
    }
}

// KPT模型复盘卡片 - 年复盘
@Composable
fun KPTReviewCard(
    reviewData: AIReviewData,
    primaryColor: Color,
    cardColor: Color,
    textColor: Color,
    positiveColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "🎉", fontSize = 16.sp)
                Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                    text = "KPT模型 - 年度成就回顾",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                                            color = textColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Keep - 继续保持
            ReviewSection(
                title = "Keep - 保持",
                icon = "🏆",
                items = generateYearKeepItems(reviewData),
                color = positiveColor
                                        )
                                        
                                        Spacer(modifier = Modifier.height(8.dp))
                                        
            // Problem - 遇到的问题
            ReviewSection(
                title = "Problem - 问题",
                icon = "🤔",
                items = generateProblemItems(reviewData),
                color = Color(0xFFFF9800)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Try - 新的尝试
            ReviewSection(
                title = "Try - 尝试",
                icon = "🌟",
                items = generateTryItems(reviewData),
                color = Color(0xFF9C27B0)
            )
        }
    }
}

// 复盘区块组件
@Composable
fun ReviewSection(
    title: String,
    icon: String,
    items: List<String>,
    color: Color
) {
    Column {
        // 标题行
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = icon, fontSize = 12.sp)
            Spacer(modifier = Modifier.width(6.dp))
                                        Text(
                text = title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = color
                                        )
        }
                                        
        Spacer(modifier = Modifier.height(4.dp))
                                        
        // 内容列表
        items.forEach { item ->
            Row(
                modifier = Modifier.padding(start = 18.dp, bottom = 2.dp)
            ) {
                                        Text(
                    text = "• $item",
                    fontSize = 10.sp,
                    color = Color(0xFF666666),
                    lineHeight = 14.sp
                                        )
                                    }
                                }
                            }
                        }

// 数据驱动的内容生成函数
fun generateKeepItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于任务完成率
    if (reviewData.taskStats.completionRate > 0.7f) {
        items.add("任务完成率${(reviewData.taskStats.completionRate * 100).toInt()}%，执行力很棒")
    }
    
    // 基于专注时间
    if (reviewData.timeStats.totalFocusedMinutes > 120) {
        items.add("专注时间${reviewData.timeStats.totalFocusedMinutes}分钟，专注力不错")
    }
    
    // 基于高效时段
    if (reviewData.efficiencyPatterns.peakHours.isNotEmpty()) {
        val peakHour = reviewData.efficiencyPatterns.peakHours.first()
        items.add("${peakHour}:00时段效率最高，继续利用黄金时间")
    }
    
    return items.ifEmpty { listOf("今天的努力都值得肯定") }
}

fun generateImproveItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于完成率
    if (reviewData.taskStats.completionRate < 0.5f) {
        items.add("任务完成率偏低，可以适当减少任务数量")
    }
    
    // 基于中断次数
    if (reviewData.efficiencyPatterns.interruptionPattern.averageRecoveryTime > 5) {
        items.add("中断次数较多，尝试关闭通知提升专注")
    }
    
    // 基于专注时长
    if (reviewData.timeStats.averageSessionDuration < 25) {
        items.add("专注时长较短，可以尝试番茄工作法")
    }
    
    return items.ifEmpty { listOf("保持现有节奏，持续优化") }
}

fun generateStartItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于工作风格
    when (reviewData.personalizedInsights.workingStyle) {
        WorkingStyle.DEEP_WORKER -> items.add("尝试安排更长的深度工作时段")
        WorkingStyle.MULTITASKER -> items.add("尝试任务批处理，减少切换成本")
        WorkingStyle.STRUCTURED -> items.add("建立更详细的日程规划")
        else -> items.add("探索适合自己的工作节奏")
    }
    
    // 基于效率模式
    if (reviewData.efficiencyPatterns.lowEnergyPeriods.isNotEmpty()) {
        items.add("在低效时段安排简单任务")
    }
    
    return items.ifEmpty { listOf("尝试新的时间管理方法") }
}

fun generateStopItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于中断模式
    if (reviewData.efficiencyPatterns.interruptionPattern.averageRecoveryTime > 8) {
        items.add("减少不必要的任务切换")
    }
    
    // 基于压力指标
    if (reviewData.personalizedInsights.stressIndicators.isNotEmpty()) {
        items.add("避免过度安排任务")
    }
    
    return items.ifEmpty { listOf("停止自我苛责，给自己更多鼓励") }
}

// PDCA相关生成函数
fun generatePlanItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    items.add("本周计划任务${reviewData.taskStats.totalTasks}个")
    if (reviewData.goalStats.totalGoals > 0) {
        items.add("设定目标${reviewData.goalStats.totalGoals}个")
    }
    
    return items
}

fun generateDoItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    items.add("实际完成任务${reviewData.taskStats.completedTasks}个")
    items.add("累计专注时间${reviewData.timeStats.totalFocusedMinutes}分钟")
    
    return items
}

fun generateCheckItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    val completionRate = (reviewData.taskStats.completionRate * 100).toInt()
    items.add("任务完成率$completionRate%")
    
    when (reviewData.trendAnalysis.trendDirection) {
        TrendDirection.IMPROVING -> items.add("效率呈上升趋势")
        TrendDirection.DECLINING -> items.add("效率有所下降")
        TrendDirection.STABLE -> items.add("效率保持稳定")
        TrendDirection.FLUCTUATING -> items.add("效率波动较大")
            }
    
    return items
}

fun generateActionItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于对比分析提供改进建议
    if (reviewData.comparisonAnalysis.vsLastPeriod.productivityChange < 0) {
        items.add("下周重点提升任务执行效率")
    }
    
    if (reviewData.efficiencyPatterns.peakHours.isNotEmpty()) {
        items.add("优化高效时段的任务安排")
    }
    
    return items.ifEmpty { listOf("继续保持当前良好状态") }
}

// GRAI相关生成函数
fun generateGoalItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    items.add("本月设定目标${reviewData.goalStats.totalGoals}个")
    if (reviewData.goalStats.goalCompletionRate > 0) {
        val rate = (reviewData.goalStats.goalCompletionRate * 100).toInt()
        items.add("目标完成率$rate%")
    }
    
    return items
}

fun generateResultItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    items.add("完成任务${reviewData.taskStats.completedTasks}个")
    items.add("累计专注${reviewData.timeStats.totalFocusedMinutes / 60}小时")
    items.add("生产力得分${reviewData.productivityScore}分")
    
    return items
}

fun generateAnalysisItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 分析工作风格
    items.add("工作风格：${reviewData.personalizedInsights.workingStyle.displayName}")
    
    // 分析效率模式
    if (reviewData.efficiencyPatterns.peakHours.isNotEmpty()) {
        items.add("高效时段：${reviewData.efficiencyPatterns.peakHours.joinToString(", ")}点")
    }
    
    return items
}

fun generateInsightItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于个人优势
    reviewData.personalizedInsights.strengths.take(2).forEach { strength ->
        items.add("优势：$strength")
    }
    
    // 基于改进领域
    reviewData.personalizedInsights.improvementAreas.take(1).forEach { area ->
        items.add("成长点：$area")
    }
    
    return items.ifEmpty { listOf("持续成长，值得肯定") }
}

// KPT相关生成函数
fun generateYearKeepItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于年度数据
    items.add("年度完成任务${reviewData.taskStats.completedTasks}个")
    items.add("保持${reviewData.personalizedInsights.workingStyle.displayName}的工作风格")
    
    reviewData.personalizedInsights.strengths.take(2).forEach { strength ->
        items.add("继续发挥${strength}的优势")
    }
    
    return items
}

fun generateProblemItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于改进领域
    reviewData.personalizedInsights.improvementAreas.take(2).forEach { area ->
        items.add(area)
    }
    
    // 基于压力指标
    reviewData.personalizedInsights.stressIndicators.take(1).forEach { stress ->
        items.add("压力来源：$stress")
    }
    
    return items.ifEmpty { listOf("整体表现良好，无明显问题") }
}

fun generateTryItems(reviewData: AIReviewData): List<String> {
    val items = mutableListOf<String>()
    
    // 基于工作风格建议新尝试
    when (reviewData.personalizedInsights.workingStyle) {
        WorkingStyle.DEEP_WORKER -> items.add("尝试更长时间的深度工作")
        WorkingStyle.MULTITASKER -> items.add("尝试时间块管理方法")
        WorkingStyle.STRUCTURED -> items.add("尝试更灵活的工作安排")
        WorkingStyle.FLEXIBLE -> items.add("尝试建立固定的工作节奏")
        WorkingStyle.COLLABORATIVE -> items.add("尝试独立工作时间")
    }
    
    // 基于效率模式
    if (reviewData.efficiencyPatterns.focusSessionPatterns.optimalSessionLength < 45) {
        items.add("尝试延长专注时间")
    }
    
    return items.ifEmpty { listOf("尝试新的成长挑战") }
}

// 可爱的时间范围选择芯片
@Composable
fun CuteTimeFrameChip(
    timeFrame: String,
    isSelected: Boolean,
    primaryColor: Color,
    accentColor: Color,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .clickable { onClick() },
        color = if (isSelected) primaryColor.copy(alpha = 0.15f) else Color.Transparent,
        shape = RoundedCornerShape(16.dp),
        border = if (isSelected) BorderStroke(1.dp, primaryColor) else BorderStroke(1.dp, Color.Gray.copy(alpha = 0.3f))
        ) {
            Text(
                text = timeFrame,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            fontSize = 12.sp,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
            color = if (isSelected) primaryColor else Color.Gray
        )
    }
}

// 可爱的生产力得分卡片
@Composable
fun CuteProductivityScoreCard(
    score: Int,
    trendDirection: TrendDirection,
    primaryColor: Color,
    positiveColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "🌟 生产力得分",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF4A4A4A)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "$score",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = primaryColor
            )
            
            Text(
                text = "/100",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            val trendIcon = when (trendDirection) {
                TrendDirection.IMPROVING -> "📈"
                TrendDirection.DECLINING -> "📉"
                TrendDirection.STABLE -> "➡️"
                TrendDirection.FLUCTUATING -> "📊"
            }
            
            Text(
                text = "$trendIcon ${trendDirection.name.lowercase()}",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

// 可爱的趋势分析卡片
@Composable
fun CuteTrendAnalysisCard(
    trendData: TrendAnalysis,
    timeFrame: String,
    primaryColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "📈", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "${timeFrame}度趋势分析",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "趋势强度: ${(trendData.trendStrength * 100).toInt()}%",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

// 可爱的效率模式卡片
@Composable
fun CuteEfficiencyPatternsCard(
    patterns: EfficiencyPatterns,
    primaryColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "⚡", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "效率模式分析",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (patterns.peakHours.isNotEmpty()) {
                Text(
                    text = "🌟 高效时段: ${patterns.peakHours.joinToString(", ")}点",
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
            
            Text(
                text = "⏱️ 最佳专注时长: ${patterns.focusSessionPatterns.optimalSessionLength}分钟",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

// 积极的个性化洞察卡片
@Composable
fun PositivePersonalizedInsightsCard(
    insights: PersonalizedInsights,
    primaryColor: Color,
    positiveColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "💡", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
            Text(
                    text = "个性化洞察",
                fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "🎯 工作风格: ${insights.workingStyle.displayName}",
                fontSize = 10.sp,
                color = Color.Gray
            )
            
            if (insights.strengths.isNotEmpty()) {
                Text(
                    text = "💪 优势: ${insights.strengths.take(2).joinToString(", ")}",
                    fontSize = 10.sp,
                    color = positiveColor
            )
        }
        }
    }
}

// 可爱的对比分析卡片
@Composable
fun CuteComparisonAnalysisCard(
    comparison: ComparisonAnalysis,
    timeFrame: String,
    primaryColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "📊", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "对比分析",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val productivityChange = comparison.vsLastPeriod.productivityChange
            val changeIcon = if (productivityChange > 0) "📈" else if (productivityChange < 0) "📉" else "➡️"
            val changeText = if (productivityChange > 0) "提升" else if (productivityChange < 0) "下降" else "持平"
            
            Text(
                text = "$changeIcon 较上${timeFrame}: 生产力${changeText}${kotlin.math.abs(productivityChange * 100).toInt()}%",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

// 可爱的数据统计卡片
@Composable
fun CuteDataStatsCard(
    reviewData: AIReviewData,
    primaryColor: Color,
    textColor: Color,
    positiveColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Text(
                text = "📊 数据概览",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                color = textColor
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "任务完成",
                    fontSize = 10.sp,
                    color = textColor
                )
                Text(
                    text = "${reviewData.taskStats.completedTasks}/${reviewData.taskStats.totalTasks}",
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = primaryColor
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "专注时间",
                    fontSize = 10.sp,
                    color = textColor
                )
                Text(
                    text = "${reviewData.timeStats.totalFocusedMinutes}分钟",
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = primaryColor
                )
            }
        }
    }
}

// 积极的AI洞察卡片
@Composable
fun PositiveAIInsightsCard(
    insights: List<AIInsight>,
    primaryColor: Color,
    positiveColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "🤖", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
            Text(
                    text = "AI智能洞察",
                fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (insights.isEmpty()) {
                Text(
                    text = "正在生成智能洞察...",
                    fontSize = 11.sp,
                    color = Color.Gray,
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
            } else {
                insights.take(3).forEachIndexed { index, insight ->
                    val (formattedTitle, formattedDescription) = com.timeflow.app.util.MarkdownFormatter.formatInsightContent(
                        insight.title,
                        insight.description
                    )
                    
                    Column(
                        modifier = Modifier.padding(bottom = if (index < insights.size - 1) 10.dp else 0.dp)
                    ) {
                        Text(
                            text = formattedTitle,
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = primaryColor,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        // 如果描述很长，使用智能分割
                        val paragraphs = com.timeflow.app.util.MarkdownFormatter.smartParagraphSplit(formattedDescription)
                        
                        paragraphs.forEachIndexed { pIndex, paragraph ->
                            Text(
                                text = paragraph,
                                fontSize = 10.sp,
                                color = Color.Gray,
                                lineHeight = 14.sp,
                                modifier = Modifier.padding(bottom = if (pIndex < paragraphs.size - 1) 2.dp else 0.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

// 积极的AI建议卡片
@Composable
fun PositiveAIRecommendationsCard(
    recommendations: List<AIRecommendation>,
    primaryColor: Color,
    positiveColor: Color,
    cardColor: Color
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = "💡", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI智能建议",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF4A4A4A)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (recommendations.isEmpty()) {
                Text(
                    text = "正在生成智能建议...",
                    fontSize = 11.sp,
                    color = Color.Gray,
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
            } else {
                recommendations.take(3).forEachIndexed { index, recommendation ->
                    val (formattedTitle, formattedDescription) = com.timeflow.app.util.MarkdownFormatter.formatRecommendationContent(
                        recommendation.title,
                        recommendation.description
                    )
                    
                    Column(
                        modifier = Modifier.padding(bottom = if (index < recommendations.size - 1) 10.dp else 0.dp)
                    ) {
                        Text(
                            text = formattedTitle,
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = primaryColor,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        // 如果描述很长，使用智能分割
                        val paragraphs = com.timeflow.app.util.MarkdownFormatter.smartParagraphSplit(formattedDescription)
                        
                        paragraphs.forEachIndexed { pIndex, paragraph ->
                            Text(
                                text = paragraph,
                                fontSize = 10.sp,
                                color = Color.Gray,
                                lineHeight = 14.sp,
                                modifier = Modifier.padding(bottom = if (pIndex < paragraphs.size - 1) 2.dp else 0.dp)
                            )
                        }
                        
                        if (recommendation.estimatedImpact.isNotBlank()) {
                            val formattedImpact = com.timeflow.app.util.MarkdownFormatter.formatMarkdownText(recommendation.estimatedImpact)
                            Text(
                                text = "🎯 预期效果: $formattedImpact",
                                fontSize = 9.sp,
                                color = positiveColor,
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
} 