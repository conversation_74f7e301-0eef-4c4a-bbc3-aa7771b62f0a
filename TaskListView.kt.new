﻿package com.timeflow.app.ui.task.components

import androidx.compose.animation.*
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.RadioButtonUnchecked
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.toMutableStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.zIndex
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.model.TaskGroup
import com.timeflow.app.ui.task.TaskViewModel
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.Instant
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarResult
import androidx.compose.material3.SnackbarDuration
import androidx.compose.ui.platform.LocalContext
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.View
import android.os.VibratorManager
import android.view.HapticFeedbackConstants
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.task.components.common.BaseTaskCard
import com.timeflow.app.ui.task.components.common.EnhancedTaskCard
import com.timeflow.app.ui.task.components.common.TaskConverter
import com.timeflow.app.ui.task.components.common.TaskCardThemes
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.hapticfeedback.LocalHapticFeedback
import kotlin.math.roundToInt

// å®šä¹‰ä»»åŠ¡å¡ç‰‡é«˜åº¦ï¼ˆéœ€æ ¹æ®å®žé™…å¸ƒå±€è°ƒæ•´ï¼‰
private val taskCardHeight = 120.dp

// æ‰©å±•å‡½æ•°ï¼šå°† Dp è½¬æ¢ä¸ºåƒç´ 
@Composable
fun Dp.toPx(): Float {
    return with(LocalDensity.current) { <EMAIL>() }
}

/**
 * ä»»åŠ¡åˆ—è¡¨è§†å›¾ï¼Œæ˜¾ç¤ºå·²åˆ†ç»„çš„ä»»åŠ¡
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskListView(
    viewModel: TaskViewModel = hiltViewModel(),
    onTaskClick: (String) -> Unit,
    onTaskStatusChange: (String, String) -> Unit,
    onTaskDelete: (String) -> Unit,
    onTaskRestore: (Task) -> Unit,
    modifier: Modifier = Modifier
) {
    // ä½¿ç”¨GroupedTaskListViewæ›¿ä»£åŽŸæ¥çš„ä»»åŠ¡åˆ—è¡¨
    val taskGroups by viewModel.taskGroups.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    var deletedTask by remember { mutableStateOf<Task?>(null) }
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Handle undo functionality
    LaunchedEffect(deletedTask) {
        deletedTask?.let { task ->
            val result = snackbarHostState.showSnackbar(
                message = "ä»»åŠ¡å·²åˆ é™¤",
                actionLabel = "æ’¤é”€",
                duration = SnackbarDuration.Short
            )
            if (result == SnackbarResult.ActionPerformed) {
                onTaskRestore(task)
            }
            deletedTask = null
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        // æ˜¾ç¤ºåˆ†ç»„ä»»åŠ¡åˆ—è¡¨
        GroupedTaskListView(
            groups = taskGroups,
            onTaskClick = onTaskClick,
            onTaskStatusChange = onTaskStatusChange,
            onTaskDelete = { taskId ->
                // æ‰¾åˆ°è¦åˆ é™¤çš„ä»»åŠ¡ä»¥ä¾¿æ’¤é”€
                val task = uiState.tasks.find { it.id == taskId }
                task?.let { deletedTask = it }
                onTaskDelete(taskId)
            },
            // å¤„ç†ä»»åŠ¡é‡æŽ’åº
            onTaskReorder = { groupId, oldTaskIndex, newTaskIndex ->
                viewModel.updateTasksOrder(groupId, taskGroups.find { it.id == groupId }?.tasks?.let {
                    val reorderedTasks = it.toMutableList()
                    val task = reorderedTasks.removeAt(oldTaskIndex)
                    reorderedTasks.add(newTaskIndex, task)
                    reorderedTasks.map { task -> task.id }
                } ?: emptyList())
            }
        )
        
        // æ’¤é”€æ“ä½œçš„Snackbar
        SnackbarHost(
            hostState = snackbarHostState,
                modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 16.dp)
        )
    }
    
    // æ˜¾ç¤ºå†²çªè§£å†³å¯¹è¯æ¡†
    if (uiState.showConflictResolutionDialog) {
        uiState.conflictResolutionTask?.let { task ->
            uiState.suggestedTimeSlot?.let { suggestedTime ->
                ConflictResolutionDialog(
                    task = task,
                    suggestedTime = suggestedTime,
                    onAccept = { viewModel.resolveTimeConflict(true) },
                    onDismiss = { viewModel.dismissConflictResolutionDialog() }
                )
            }
        }
    }
}

/**
 * åˆ†ç»„ä»»åŠ¡åˆ—è¡¨è§†å›¾
 * æ”¯æŒåˆ†ç»„å±•å¼€/æ”¶èµ·åŠŸèƒ½
 */
@Composable
fun GroupedTaskListView(
    groups: List<TaskGroup>,
    onTaskClick: (String) -> Unit,
    onTaskStatusChange: (String, String) -> Unit,
    onTaskDelete: (String) -> Unit,
    modifier: Modifier = Modifier,
    // ä¿®æ”¹ä»»åŠ¡é‡æŽ’åºæŽ¥å£
    onTaskReorder: (String, Int, Int) -> Unit = { _, _, _ -> }
) {
    // ä½¿ç”¨LazyColumnçš„æ›´é«˜æ•ˆçŠ¶æ€ç®¡ç†
    val lazyListState = rememberLazyListState()
    
    // ä¼˜åŒ–çš„LazyColumné…ç½®
    LazyColumn(
        state = lazyListState,
        modifier = modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(
            items = groups,
            key = { group -> group.id },
            contentType = { "group" }
        ) { group ->
            // è®°ä½å±•å¼€çŠ¶æ€ä»¥é¿å…çŠ¶æ€ä¸¢å¤±
            var isExpanded by remember(group.id) { mutableStateOf(true) }
            
            // æ•´ä¸ªåˆ†ç»„å¡ç‰‡
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = .1f),
                border = BorderStroke(0.5.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)),
                shadowElevation = 1.dp,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    // ç»„æ ‡é¢˜
                    TaskGroupHeader(
                        group = group,
                        isExpanded = isExpanded,
                        onToggleExpand = { isExpanded = !isExpanded }
                    )
                    
                    // ä»»åŠ¡åˆ—è¡¨ - ä»…åœ¨å±•å¼€æ—¶æ˜¾ç¤º
                    AnimatedVisibility(
                        visible = isExpanded,
                        enter = expandVertically() + fadeIn(),
                        exit = shrinkVertically() + fadeOut()
                    ) {
                        if (group.tasks.isEmpty()) {
                            EmptyGroupPlaceholder()
                        } else {
                            // ä½¿ç”¨Columnä»£æ›¿LazyColumnåµŒå¥—
                            Column(
                                verticalArrangement = Arrangement.spacedBy(12.dp),
                                modifier = Modifier.padding(top = 8.dp)
                            ) {
                                // é™åˆ¶æœ€å¤§æ˜¾ç¤ºæ•°é‡
                                val tasksToShow = if (group.tasks.size > 20) group.tasks.take(20) else group.tasks
                                
                                // è½¬æ¢ä¸ºentity.Taskåˆ—è¡¨ï¼Œä¿®å¤ç±»åž‹ä¸åŒ¹é…é—®é¢˜
                                val entityTasks = remember(tasksToShow) {
                                    tasksToShow.map { TaskConverter.toEntityTask(it) }
                                }
                                
                                entityTasks.forEachIndexed { index, task ->
                                    key(task.id) {
                                        // ä½¿ç”¨æ–°çš„ImprovedTaskCardå®žçŽ°
                                        ImprovedTaskCard(
                                            task = task,
                                            onTaskClick = onTaskClick,
                                            onTaskCompleted = { taskId, status ->
                                                onTaskStatusChange(taskId, status)
                                            },
                                            onTaskDrag = { oldIndex, newIndex ->
                                                // è°ƒç”¨æŽ’åºå›žè°ƒ
                                                onTaskReorder(group.id, oldIndex, newIndex)
                                            },
                                            groupTasks = entityTasks,
                                            groupTaskIndex = index,
                                            modifier = Modifier.animateItemPlacement(
                                                animationSpec = spring(
                                                    dampingRatio = 0.8f,
                                                    stiffness = 300f
                                                )
                                            )
                                        )
                                    }
                                }
                                
                                // å¦‚æžœä»»åŠ¡è¿‡å¤šï¼Œæ˜¾ç¤º"åŠ è½½æ›´å¤š"æŒ‰é’®
                                if (group.tasks.size > 20) {
                                    TextButton(
                                        onClick = { /* å®žçŽ°åŠ è½½æ›´å¤šé€»è¾‘ */ },
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        Text("æ˜¾ç¤ºå‰©ä½™ ${group.tasks.size - 20} ä¸ªä»»åŠ¡")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * ä»»åŠ¡åˆ†ç»„æ ‡é¢˜ç»„ä»¶
 * å¯ç‚¹å‡»åˆ‡æ¢å±•å¼€/æ”¶èµ·çŠ¶æ€
 */
@Composable
fun TaskGroupHeader(
    group: TaskGroup,
    isExpanded: Boolean,
    onToggleExpand: () -> Unit,
    modifier: Modifier = Modifier
) {
    val rotationState by animateFloatAsState(
        targetValue = if (isExpanded) 0f else -90f,
        animationSpec = spring(
            dampingRatio = 0.8f,
            stiffness = 300f
        ),
        label = "Expansion arrow rotation"
    )
    
    // é¢„å…ˆè®¡ç®—å›¾æ ‡é€‰æ‹©
    val icon: ImageVector
    val iconDesc: String
    
    when {
        group.title.contains("åˆ°æœŸ") -> {
            icon = Icons.Default.Bolt
            iconDesc = group.title
        }
        group.title.contains("æ˜Žå¤©") -> {
            icon = Icons.Default.CalendarToday
            iconDesc = group.title
        }
        group.title.contains("æŽ¥ä¸‹æ¥") -> {
            icon = Icons.Default.DateRange
            iconDesc = group.title
        }
        group.title.contains("æ—¥æœŸ") -> {
            icon = Icons.Default.Event
            iconDesc = group.title
        }
        else -> {
            icon = Icons.Default.Folder
            iconDesc = group.title
        }
    }
    
    // æ ‡é¢˜è¡Œå¸ƒå±€ - ä½¿ç”¨æ‰å¹³è®¾è®¡è€Œä¸æ˜¯å¡ç‰‡ï¼Œä¸Žå›¾2ä¸€è‡´
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onToggleExpand)
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // å·¦ä¾§: åˆ†ç»„å›¾æ ‡å’Œæ ‡é¢˜
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // åˆ†ç»„å›¾æ ‡ - ä½¿ç”¨æ›´æ˜Žæ˜¾çš„å›¾æ ‡å®¹å™¨ï¼Œä¸Žå›¾2é£Žæ ¼ä¸€è‡´
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = iconDesc,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            // åˆ†ç»„æ ‡é¢˜ - å¢žå¼ºæ–‡å­—å±‚çº§
            Column {
            Text(
                text = group.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                // ä»»åŠ¡æ•°é‡è¯´æ˜Ž - ä½¿ç”¨æ›´æ¸…æ™°çš„è¡¨è¿°
                Text(
                    text = "${group.tasks.size}ä¸ªä»»åŠ¡",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
        }
        
        // å³ä¾§: å±•å¼€/æ”¶èµ·æŒ‡ç¤ºå™¨
        Icon(
            imageVector = Icons.Default.KeyboardArrowDown,
            contentDescription = if (isExpanded) "æ”¶èµ·" else "å±•å¼€",
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier
                .size(24.dp)
                .rotate(rotationState)
        )
    }
}

/**
 * æ”¹è¿›çš„ä»»åŠ¡å¡ç‰‡ç»„ä»¶
 * æ”¯æŒæ‹–æ‹½æŽ’åºåŠŸèƒ½
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun ImprovedTaskCard(
    task: Task,
    onTaskClick: (String) -> Unit,
    onTaskCompleted: (String, String) -> Unit,
    onTaskDrag: (oldIndex: Int, newIndex: Int) -> Unit, // æ–°å¢žæ‹–åŠ¨æŽ’åºå›žè°ƒ
    groupTasks: List<Task>, // ä¼ å…¥åˆ†ç»„å†…ä»»åŠ¡åˆ—è¡¨
    groupTaskIndex: Int, // ä»»åŠ¡åœ¨åˆ†ç»„å†…çš„ç´¢å¼•
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val hapticFeedback = LocalHapticFeedback.current
    val localView = LocalView.current
    
    // æ‹–æ‹½çŠ¶æ€
    var isDragging by remember { mutableStateOf(false) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var originalIndex by remember { mutableStateOf(groupTaskIndex) }
    
    // ä½¿ç”¨rememberè®°ä½å…³é”®çŠ¶æ€é¿å…ä¸å¿…è¦é‡æ–°è®¡ç®—
    val isCompleted = remember(task.completedAt) { task.completedAt != null }
    var isPressed by remember { mutableStateOf(false) }
    
    // å½“é•¿æŒ‰çŠ¶æ€å˜åŒ–æ—¶é‡ç½®å®ƒ
    LaunchedEffect(isPressed) {
        if (isPressed) {
            kotlinx.coroutines.delay(300)
            isPressed = false
        }
    }
    
    // ä½¿ç”¨springåŠ¨ç”»æ›¿ä»£tweenä»¥èŽ·å¾—æ›´å¥½çš„æ€§èƒ½
    val scale by animateFloatAsState(
        targetValue = if (isPressed || isDragging) 0.97f else 1f,
        animationSpec = spring(
            dampingRatio = 0.7f,
            stiffness = 400f
        ),
        label = "Scale Animation"
    )
    
    // ç¼“å­˜è®¡ç®—çš„ä½ç½®å’Œåé¦ˆæ ‡å¿—
    val dragData = remember { mutableStateOf(0) }
    
    // æ‹–æ‹½çŠ¶æ€å¤„ç†å™¨
    val dragState = rememberDraggableState { delta ->
        if (isDragging) {
            dragOffset = dragOffset.copy(y = dragOffset.y + delta)
            
            // è®¡ç®—å½“å‰æ‹–æ‹½ä½ç½®å¯¹åº”çš„ä»»åŠ¡ç´¢å¼•
            val newIndexOffset = (dragOffset.y / taskCardHeight.toPx()).roundToInt()
            val newIndex = (originalIndex + newIndexOffset).coerceIn(0, groupTasks.size - 1)
            
            // ä»…åœ¨ç´¢å¼•å˜åŒ–æ—¶æä¾›è§¦è§‰åé¦ˆ
            if (newIndex != dragData.value) {
                dragData.value = newIndex
                localView.performHapticFeedback(HapticFeedbackConstants.CLOCK_TICK)
            }
        }
    }
    
    // é¢„å…ˆè®¡ç®—å’Œç¼“å­˜æ ¼å¼åŒ–çš„æ—¥æœŸ
    val dateFormat = remember { DateTimeFormatter.ofPattern("MM-dd HH:mm") }
    val formattedDueDate = remember(task.dueDate) {
        task.dueDate?.format(dateFormat)
    }
    
    // èŽ·å–ä¼˜å…ˆçº§ç›¸å…³é¢œè‰²å’Œå›¾æ ‡
    val tagColor = when (task.priority) {
        1 -> Color(0xFFFFC107) // ä¸­ - é»„è‰²
        2 -> Color(0xFFFF9800) // é«˜ - æ©™è‰²
        3 -> Color(0xFFF44336) // ç´§æ€¥ - çº¢è‰²
        else -> Color(0xFF4CAF50) // ä½Ž - ç»¿è‰²
    }
    
    val tagText = when (task.priority) {
        1 -> "ä¸­ç­‰"
        2 -> "é‡è¦"
        3 -> "ç´§æ€¥"
        else -> "å¸¸è§„"
    }
    
    // å¤–å±‚ä¸Žå¡ç‰‡è®¾è®¡
    Box(
        modifier = modifier
            .fillMaxWidth()
            .zIndex(if (isDragging) 1f else 0f)
            .graphicsLayer {
                // ä»…åœ¨æ‹–åŠ¨æ—¶åº”ç”¨åž‚ç›´å¹³ç§»
                translationY = if (isDragging) dragOffset.y else 0f
                // æ‹–åŠ¨æ—¶é™ä½Žé€æ˜Žåº¦
                alpha = if (isDragging) 0.9f else 1f
            }
    ) {
        // æ‹–åŠ¨æ—¶æ˜¾ç¤ºæŒ‡ç¤ºå™¨
        if (isDragging) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(8.dp)
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.DragIndicator,
                    contentDescription = "æ‹–åŠ¨æŽ’åº",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // å¡ç‰‡ä¸»ä½“
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .scale(scale)
                .shadow(
                    elevation = if (isDragging) 8.dp else 2.dp,
                    shape = RoundedCornerShape(12.dp),
                    spotColor = if (isDragging) 
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.2f) 
                    else 
                        tagColor.copy(alpha = 0.1f)
                )
                .clip(RoundedCornerShape(12.dp))
                .border(
                    width = if (isDragging) 2.dp else 0.5.dp,
                    color = if (isDragging) 
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    else 
                        tagColor.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(12.dp)
                )
                .combinedClickable(
                    onClick = { onTaskClick(task.id) },
                    onLongClick = { 
                        isPressed = true
                        localView.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                        // å¯åŠ¨æ‹–æ‹½
                        isDragging = true
                        originalIndex = groupTaskIndex
                    }
                )
                .draggable(
                    state = dragState,
                    orientation = Orientation.Vertical,
                    enabled = true,
                    onDragStarted = {
                        // ä»…é€šè¿‡é•¿æŒ‰å¯åŠ¨æ‹–æ‹½
                    },
                    onDragStopped = { velocity ->
                        if (isDragging) {
                            // è®¡ç®—æœ€ç»ˆç›®æ ‡ç´¢å¼•
                            val finalOffset = dragOffset.y
                            val newIndexOffset = (finalOffset / taskCardHeight.toPx()).roundToInt()
                            val newIndex = (originalIndex + newIndexOffset).coerceIn(0, groupTasks.size - 1)
                            
                            // ä»…å½“ä½ç½®å˜åŒ–æ—¶è§¦å‘é‡æŽ’åº
                            if (newIndex != originalIndex) {
                                onTaskDrag(originalIndex, newIndex)
                                localView.performHapticFeedback(HapticFeedbackConstants.CONFIRM)
                            }
                            
                            // é‡ç½®æ‹–æ‹½çŠ¶æ€
                            isDragging = false
                            dragOffset = Offset.Zero
                        }
                    }
                ),
            shape = RoundedCornerShape(12.dp),
            color = MaterialTheme.colorScheme.surface
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.Top,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // ä»»åŠ¡çŠ¶æ€æŒ‡ç¤ºå™¨ - ä½¿ç”¨æ–¹å½¢å½©è‰²è¾¹æ¡†ä¸Žå›¾2ä¸€è‡´
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .border(
                            width = 2.dp,
                            color = tagColor,
                            shape = RoundedCornerShape(4.dp)
                        )
                        .background(if (isCompleted) tagColor else Color.Transparent)
                        .clickable(enabled = !isDragging) { // å¦‚æžœæ­£åœ¨æ‹–åŠ¨åˆ™ç¦ç”¨ç‚¹å‡»
                            val newStatus = if (isCompleted) "å¾…åŠž" else "å·²å®Œæˆ"
                            onTaskCompleted(task.id, newStatus)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    if (isCompleted) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "å·²å®Œæˆ",
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
                
                // ä»»åŠ¡å†…å®¹åŒº
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // ä»»åŠ¡æ ‡é¢˜
                    Text(
                        text = task.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = if (isCompleted) 
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        else 
                            MaterialTheme.colorScheme.onSurface,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    // ä»»åŠ¡æè¿°
                    if (!task.description.isNullOrEmpty()) {
                        Text(
                            text = task.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    // åº•éƒ¨ä¿¡æ¯åŒºï¼šæ ‡ç­¾å’Œæ—¶é—´
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // æ ‡ç­¾åŒº
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // ä¼˜å…ˆçº§æ ‡ç­¾
                            Surface(
                                shape = RoundedCornerShape(4.dp),
                                color = tagColor.copy(alpha = 0.15f)
                            ) {
                                Text(
                                    text = tagText,
                                    style = MaterialTheme.typography.labelSmall,
                                    color = tagColor,
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                            
                            // æ¨¡æ‹Ÿ"ç”Ÿæ´»"ç±»åž‹æ ‡ç­¾ï¼Œä¸Žå›¾2ä¸€è‡´
                            if (task.id.hashCode() % 3 == 0) {
                                Surface(
                                    shape = RoundedCornerShape(4.dp),
                                    color = Color(0xFFE0F7FA).copy(alpha = 0.7f)
                                ) {
                                    Text(
                                        text = "ç”Ÿæ´»",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = Color(0xFF00838F),
                                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                    )
                                }
                            }
                        }
                        
                        // æ—¶é—´ä¿¡æ¯
                        if (formattedDueDate != null) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.AccessTime,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                                    modifier = Modifier.size(12.dp)
                                )
                                
                                Text(
                                    text = formattedDueDate,
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun TaskTag(
    text: String,
    isCompleted: Boolean
) {
    Surface(
        shape = RoundedCornerShape(4.dp),
        color = if (isCompleted)
            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        else
            MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.7f)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = if (isCompleted)
                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
            else
                MaterialTheme.colorScheme.onTertiaryContainer,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

@Composable
private fun PriorityIndicator(
    priority: Int?,
    modifier: Modifier = Modifier
) {
    val color = when (priority) {
        0 -> Color(0xFF4CAF50) // ä½Žä¼˜å…ˆçº§ - ç»¿è‰²
        1 -> Color(0xFFFFC107) // ä¸­ä¼˜å…ˆçº§ - é»„è‰²
        2 -> Color(0xFFFF9800) // é«˜ä¼˜å…ˆçº§ - æ©™è‰²
        3 -> Color(0xFFF44336) // ç´§æ€¥ - çº¢è‰²
        else -> Color(0xFFBDBDBD) // é»˜è®¤ - ç°è‰²
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(2.dp))
            .background(color)
    )
}

// ç§»é™¤ä¾èµ–äºŽComposableå‡½æ•°çš„ç‰ˆæœ¬
private fun getDynamicTimeText(dueDate: Instant?, isCompleted: Boolean): String {
    if (dueDate == null) return ""
    
    val currentTime = Instant.now()
    val duration = Duration.between(currentTime, dueDate)
    
    return when {
        duration.isNegative -> "å·²è¶…æ—¶"
        duration.toHours() < 24 -> "${duration.toHours()}å°æ—¶åŽ"
        duration.toDays() < 7 -> "${duration.toDays()}å¤©åŽ"
        else -> dueDate.toString()
    }
}

private fun performHapticFeedback(view: View, feedbackType: Int) {
    when (feedbackType) {
        HapticFeedbackConstants.LONG_PRESS -> {
            view.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
        }
        HapticFeedbackConstants.CLOCK_TICK -> {
            view.performHapticFeedback(HapticFeedbackConstants.CLOCK_TICK)
        }
        else -> {
            view.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
        }
    }
}

/**
 * æ˜¾ç¤ºä»»åŠ¡ç»„ä¸ºç©ºæ—¶çš„å ä½æç¤º
 * ä¼˜åŒ–ï¼šä½¿ç”¨ä¸€ä¸ªç‹¬ç«‹ç»„ä»¶å‡å°‘é‡ç»„èŒƒå›´
 */
@Composable
private fun EmptyGroupPlaceholder() {
    // é¢„å…ˆè®¡ç®—é¢œè‰²ï¼Œé¿å…åœ¨rememberå—ä¸­ä½¿ç”¨MaterialTheme
    val placeholderColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
    val iconTint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
    val textColor = MaterialTheme.colorScheme.onSurfaceVariant
    val secondaryTextColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        shape = RoundedCornerShape(16.dp),
        color = placeholderColor
    ) {
        Column(
            modifier = Modifier
                .padding(24.dp)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.AddTask,
                contentDescription = null,
                tint = iconTint,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "è¯¥åˆ†ç»„æ²¡æœ‰ä»»åŠ¡",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = textColor
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "ç‚¹å‡»"+"æŒ‰é’®æ·»åŠ æ–°ä»»åŠ¡",
                style = MaterialTheme.typography.bodyMedium,
                color = secondaryTextColor,
                textAlign = TextAlign.Center
            )
        }
    }
}

// å†²çªè§£å†³å¯¹è¯æ¡†
@Composable
fun ConflictResolutionDialog(
    task: Task,
    suggestedTime: LocalDateTime,
    onAccept: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("æ—¶é—´å†²çªæç¤º") },
        text = {
            Column {
                Text("ä»»åŠ¡ "${task.title}" ä¸ŽçŽ°æœ‰ä»»åŠ¡æ—¶é—´å†²çªã€‚")
                Spacer(modifier = Modifier.height(8.dp))
                Text("å»ºè®®è°ƒæ•´åˆ°: ${suggestedTime.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"))}")
            }
        },
        confirmButton = {
            TextButton(onClick = onAccept) {
                Text("æŽ¥å—å»ºè®®")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("å–æ¶ˆ")
            }
            }
        }
    )
}