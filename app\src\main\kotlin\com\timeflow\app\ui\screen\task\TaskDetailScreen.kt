package com.timeflow.app.ui.screen.task

import android.util.Log
import android.widget.Toast
import android.app.Activity
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.*
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.timeflow.app.data.entity.Task as EntityTask
import com.timeflow.app.data.entity.Priority as EntityPriority
import com.timeflow.app.ui.task.TaskListViewModel as TaskListViewModel
import com.timeflow.app.ui.theme.*
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.runtime.DisposableEffect
import java.time.LocalDate
import java.time.format.TextStyle
import java.util.Locale
import com.timeflow.app.ui.components.CalendarGrid
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import com.timeflow.app.data.converter.TaskConverter as DataTaskConverter
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.getSampleTasks
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.Label
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.data.model.Priority
import kotlinx.coroutines.delay
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.timeflow.app.util.AIAssistantTaskCreatedEvent
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import androidx.lifecycle.viewModelScope
import com.timeflow.app.ui.screen.task.TaskViewModel
import com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState

// 主题色彩常量
private val PrimaryPurple = DustyLavender
private val LightPurple = Color(0xFFE8D1F2)
private val VeryLightPurple = Color(0xFFF3E8F8)
private val TaskHeaderBackground = Color(0xFFF9FAFB)
private val SectionTitleColor = Color(0xFF6B7280)
private val StatusBadgeRed = Color(0xFFEF4444)
private val StatusBadgeRedBg = Color(0xFFFEF2F2)
private val ProgressPurple = Color(0xFF7C3AED)

@Composable
fun TaskDetailScreen(
    taskId: String,
    viewModel: TaskViewModel = hiltViewModel(),
    onEditClick: () -> Unit,
    onDismiss: () -> Unit,
    navController: NavController
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    // 动画控制
    var isContentVisible by remember { mutableStateOf(false) }
    val contentAlpha by animateFloatAsState(
        targetValue = if (isContentVisible) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "content_alpha"
    )

    LaunchedEffect(Unit) {
        viewModel.loadTask(taskId)
        delay(100)
        isContentVisible = true
    }

    // 系统栏管理
    val activity = LocalContext.current as? Activity
    DisposableEffect(Unit) {
        activity?.let { SystemBarManager.setupTaskPageSystemBars(activity) }
        onDispose {
            activity?.let { SystemBarManager.setupStandardPageSystemBars(activity) }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F5))
    ) {
        when (val currentState = uiState) {
            is TaskUiState.Loading -> {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(48.dp)
                        .align(Alignment.Center),
                    color = PrimaryPurple
                )
            }
            
            is TaskUiState.Error -> {
                ErrorContent(
                    message = currentState.message,
                    onRetry = { viewModel.loadTask(taskId) },
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            
            is TaskUiState.Success -> {
                ModernTaskDetailContent(
                    task = currentState.task,
                    onDismiss = onDismiss,
                    onEditClick = onEditClick,
                    viewModel = viewModel,
                    contentAlpha = contentAlpha
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ModernTaskDetailContent(
    task: TaskData,
    onDismiss: () -> Unit,
    onEditClick: () -> Unit,
    viewModel: TaskViewModel,
    contentAlpha: Float
) {
    // 🔧 新增：清理描述中的JSON颜色信息的方法
    fun cleanDescriptionForEdit(rawDescription: String): String {
        val colorPattern = """\{"color":(\d+)\}""".toRegex()
        return rawDescription.replace(colorPattern, "").trim()
    }
    
    // 编辑模式状态
    var isEditMode by remember { mutableStateOf(false) }
    var editedTitle by remember { mutableStateOf(task.title) }
    var editedDescription by remember { mutableStateOf(cleanDescriptionForEdit(task.description)) } // 🔧 初始化时就清理描述
    
    // 进入编辑模式时重置编辑状态
    LaunchedEffect(isEditMode) {
        if (isEditMode) {
            editedTitle = task.title
            editedDescription = cleanDescriptionForEdit(task.description) // 🔧 清理描述
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .graphicsLayer(alpha = contentAlpha)
    ) {
        // 顶部导航栏
        TaskDetailTopBar(
            onDismiss = onDismiss,
            onEditClick = { 
                if (isEditMode) {
                    // 保存编辑（确保描述是清理过的）
                    val cleanedDescription = cleanDescriptionForEdit(editedDescription)
                    viewModel.updateTaskDetails(task.id, editedTitle, cleanedDescription)
                    isEditMode = false
                } else {
                    // 进入编辑模式
                    isEditMode = true
                }
            },
            isEditMode = isEditMode
        )
        
        // 滚动内容
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 100.dp)
        ) {
            item {
                // 任务头部信息
                TaskHeaderSection(
                    task = task,
                    isEditMode = isEditMode,
                    editedTitle = editedTitle,
                    onTitleChange = { editedTitle = it }
                )
            }
            
            item {
                // 任务描述
                TaskDescriptionSection(
                    description = task.description,
                    isEditMode = isEditMode,
                    editedDescription = editedDescription,
                    onDescriptionChange = { newDesc -> 
                        // 🔧 确保输入的描述也被清理
                        editedDescription = cleanDescriptionForEdit(newDesc)
                    }
                )
            }
            
            item {
                // 进度条
                TaskProgressSection(task = task)
            }
            
            item {
                // 子任务列表
                if (task.subTasks.isNotEmpty()) {
                    TaskChecklistSection(
                        subTasks = task.subTasks,
                        onSubTaskToggle = { subTaskId, isCompleted ->
                            viewModel.toggleSubTaskCompletion(subTaskId, isCompleted)
                        }
                    )
                }
            }
            
            item {
                // 分割线
                Divider(
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 24.dp),
                    thickness = 0.5.dp,
                    color = Color.Black.copy(alpha = 0.08f)
                )
            }
            
            item {
                // 详细信息
                TaskDetailsSection(task = task)
            }
        }
        
        // 底部操作按钮
        BottomActionButton(
            task = task,
            onComplete = {
                viewModel.markTaskCompleted(task.id)
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TaskDetailTopBar(
    onDismiss: () -> Unit,
    onEditClick: () -> Unit,
    isEditMode: Boolean = false
) {
    Surface(
        color = TaskHeaderBackground,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // 状态栏间距
            Spacer(modifier = Modifier.height(SystemBarManager.getFixedStatusBarHeight()))
            
            // 导航栏内容
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF6B7280)
                    )
                }
                
                // 编辑/保存按钮
                IconButton(
                    onClick = onEditClick,
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = if (isEditMode) Icons.Default.Check else Icons.Default.Edit,
                        contentDescription = if (isEditMode) "保存" else "编辑",
                        tint = if (isEditMode) PrimaryPurple else Color(0xFF6B7280)
                    )
                }
            }
        }
    }
}

@Composable
private fun TaskHeaderSection(
    task: TaskData,
    isEditMode: Boolean = false,
    editedTitle: String = task.title,
    onTitleChange: (String) -> Unit = {}
) {
    Surface(
        color = TaskHeaderBackground,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .padding(bottom = 24.dp)
        ) {
            // TODAY'S TASK 标签
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "TODAY'S TASK",
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 1.2.sp
                    ),
                    color = SectionTitleColor
                )
                
                Text(
                    text = "Nov 15, 2023",
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = Color(0xFF9CA3AF)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 任务标题
            if (isEditMode) {
                val focusRequester = remember { FocusRequester() }
                
                OutlinedTextField(
                    value = editedTitle,
                    onValueChange = onTitleChange,
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                    textStyle = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 24.sp
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = PrimaryPurple,
                        unfocusedBorderColor = Color(0xFFE5E7EB),
                        focusedTextColor = Color(0xFF111827),
                        unfocusedTextColor = Color(0xFF111827)
                    ),
                    placeholder = {
                        Text("输入任务标题...")
                    },
                    singleLine = false,
                    maxLines = 3
                )
                
                LaunchedEffect(Unit) {
                    focusRequester.requestFocus()
                }
            } else {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 24.sp
                    ),
                    color = Color(0xFF111827)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 状态标签
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 优先级标签
                StatusBadge(
                    text = getTaskPriorityText(task.priority),
                    backgroundColor = StatusBadgeRedBg,
                    textColor = StatusBadgeRed,
                    icon = Icons.Default.Circle
                )
                
                // 时间标签
                task.dueDate?.let { dueDate ->
                    StatusBadge(
                        text = formatDueTime(dueDate),
                        backgroundColor = Color(0xFFF3F4F6),
                        textColor = Color(0xFF6B7280),
                        icon = Icons.Default.Schedule
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusBadge(
    text: String,
    backgroundColor: Color,
    textColor: Color,
    icon: ImageVector
) {
    Surface(
        color = backgroundColor,
        shape = RoundedCornerShape(20.dp),
        modifier = Modifier.padding(0.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = textColor,
                modifier = Modifier.size(6.dp)
            )
            
            Text(
                text = text,
                style = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp
                ),
                color = textColor
            )
        }
    }
}

@Composable
private fun TaskDescriptionSection(
    description: String,
    isEditMode: Boolean = false,
    editedDescription: String = description,
    onDescriptionChange: (String) -> Unit = {}
) {
    // 🔧 新增：清理描述中的JSON颜色信息
    fun cleanDescription(rawDescription: String): String {
        val colorPattern = """\{"color":(\d+)\}""".toRegex()
        return rawDescription.replace(colorPattern, "").trim()
    }
    
    // 清理原始描述和编辑描述
    val cleanedDescription = cleanDescription(description)
    val cleanedEditedDescription = cleanDescription(editedDescription)
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 16.dp)
    ) {
        SectionHeader(title = "DESCRIPTION")
        
        Spacer(modifier = Modifier.height(12.dp))
        
        if (isEditMode) {
            OutlinedTextField(
                value = cleanedEditedDescription,
                onValueChange = onDescriptionChange,
                modifier = Modifier.fillMaxWidth(),
                textStyle = MaterialTheme.typography.bodyLarge.copy(
                    lineHeight = 24.sp
                ),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = PrimaryPurple,
                    unfocusedBorderColor = Color(0xFFE5E7EB),
                    focusedTextColor = Color(0xFF374151),
                    unfocusedTextColor = Color(0xFF374151)
                ),
                placeholder = {
                    Text("输入任务描述...")
                },
                minLines = 3,
                maxLines = 6
            )
        } else if (cleanedDescription.isNotEmpty() || cleanedEditedDescription.isNotEmpty()) {
            Text(
                text = if (cleanedDescription.isNotEmpty()) cleanedDescription else cleanedEditedDescription,
                style = MaterialTheme.typography.bodyLarge.copy(
                    lineHeight = 24.sp
                ),
                color = Color(0xFF374151)
            )
        } else {
            Text(
                text = "暂无描述",
                style = MaterialTheme.typography.bodyLarge.copy(
                    lineHeight = 24.sp
                ),
                color = Color(0xFF9CA3AF),
                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
            )
        }
    }
}

@Composable
private fun TaskProgressSection(task: TaskData) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            SectionHeader(title = "PROGRESS")
            
            Text(
                text = "${calculateProgress(task)}%",
                style = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp
                ),
                color = SectionTitleColor
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 进度条
        LinearProgressIndicator(
            progress = calculateProgress(task) / 100f,
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .clip(RoundedCornerShape(3.dp)),
            color = ProgressPurple,
            trackColor = Color(0xFFF3F4F6)
        )
    }
}

@Composable
private fun TaskChecklistSection(
    subTasks: List<SubTask>,
    onSubTaskToggle: (String, Boolean) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp, vertical = 16.dp)
    ) {
        SectionHeader(title = "CHECKLIST (${subTasks.size})")
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            subTasks.forEach { subTask ->
                ModernChecklistItem(
                    subTask = subTask,
                    onToggle = { isCompleted ->
                        onSubTaskToggle(subTask.id, isCompleted)
                    }
                )
            }
        }
    }
}

@Composable
private fun ModernChecklistItem(
    subTask: SubTask,
    onToggle: (Boolean) -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onToggle(!subTask.isCompleted) },
        color = Color.Transparent
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 自定义复选框
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .border(
                        width = 2.dp,
                        color = if (subTask.isCompleted) ProgressPurple else Color(0xFFD1D5DB),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .background(
                        if (subTask.isCompleted) ProgressPurple else Color.Transparent
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (subTask.isCompleted) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = subTask.title,
                style = MaterialTheme.typography.bodyMedium,
                color = if (subTask.isCompleted) 
                    Color(0xFF9CA3AF) else Color(0xFF1F2937),
                textDecoration = if (subTask.isCompleted) 
                    TextDecoration.LineThrough else TextDecoration.None
            )
        }
    }
}

@Composable
private fun TaskDetailsSection(task: TaskData) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
    ) {
        SectionHeader(title = "DETAILS")
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 截止日期
            task.dueDate?.let { dueDate ->
                DetailItem(
                    icon = Icons.Default.DateRange,
                    label = "截止日期",
                    value = dueDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                )
            }
            
            // 状态
            DetailItem(
                icon = Icons.Default.Flag,
                label = "状态",
                value = if (task.status == "已完成") "已完成" else "进行中"
            )
            
            // 优先级
            DetailItem(
                icon = Icons.Default.PriorityHigh,
                label = "优先级",
                value = getTaskPriorityText(task.priority)
            )
            
            // 标签
            if (task.tags.isNotEmpty()) {
                DetailItem(
                    icon = Icons.Default.Label,
                    label = "标签",
                    value = task.tags.joinToString(", ")
                )
            }
        }
    }
}

@Composable
private fun DetailItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier.width(24.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = Color(0xFF9CA3AF),
                modifier = Modifier.size(16.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelSmall.copy(
                    fontSize = 12.sp
                ),
                color = SectionTitleColor
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF1F2937)
            )
        }
    }
}

@Composable
private fun SectionHeader(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.labelSmall.copy(
            fontWeight = FontWeight.Medium,
            letterSpacing = 1.2.sp,
            fontSize = 11.sp
        ),
        color = SectionTitleColor
    )
}

@Composable
private fun BottomActionButton(
    task: TaskData,
    onComplete: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White,
        shadowElevation = 8.dp
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 12.dp)
                .navigationBarsPadding(),
            contentAlignment = Alignment.Center
        ) {
            Button(
                onClick = onComplete,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = ProgressPurple,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Text(
                        text = if (task.status == "已完成") "标记为未完成" else "标记为完成",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }
        }
    }
}

@Composable
private fun ErrorContent(
    message: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "加载失败：$message",
            color = MaterialTheme.colorScheme.error,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = onRetry,
            colors = ButtonDefaults.buttonColors(
                containerColor = PrimaryPurple
            )
        ) {
            Text("重试")
        }
    }
}

// 工具函数
private fun getTaskPriorityText(priority: Priority): String {
    return when (priority) {
        Priority.HIGH -> "高优先级"
        Priority.MEDIUM -> "普通优先级"
        Priority.LOW -> "低优先级"
        else -> "普通优先级"
    }
}

private fun formatDueTime(dueDate: LocalDateTime): String {
    val formatter = DateTimeFormatter.ofPattern("今天 HH:mm")
    return formatter.format(dueDate)
}

private fun calculateProgress(task: TaskData): Int {
    if (task.subTasks.isEmpty()) return if (task.status == "已完成") 100 else 0
    
    val completedCount = task.subTasks.count { it.isCompleted }
    return ((completedCount.toFloat() / task.subTasks.size) * 100).toInt()
}