package com.timeflow.app.ui.screen.calendar

import android.app.Activity
import android.os.Build
import android.text.format.DateFormat
import android.util.Log
import android.widget.Toast
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.ui.input.pointer.consumeDownChange
import androidx.compose.ui.input.pointer.consumePositionChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.calendar.DimensionUtils
import com.timeflow.app.ui.screen.health.HabitData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.theme.AppTheme
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.enableHardwareAcceleration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.*
import kotlin.math.abs

// 日历专用配色方案，使用Material3的darkColorScheme
val CalendarColors = darkColorScheme(
    primary = Color(0xFF554A60),
    onPrimary = Color(0xFF554A60),
    primaryContainer = Color(0xFFFFFFFF),
    onPrimaryContainer = Color(0xFF554A60),
    inversePrimary = Color(0xFFB1A5BD),
    secondary = Color(0xFFB1A5BD),
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFFFFFFF),
    onSecondaryContainer = Color(0xFF554A60),
    tertiary = Color(0xFF554A60),
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFFFFFFF),
    onTertiaryContainer = Color(0xFF554A60),
    background = Color(0xFFFFFFFF),
    onBackground = Color(0xFF554A60),
    surface = Color(0xFFFFFFFF),
    onSurface = Color(0xFF554A60).copy(alpha = 0.6f),
    surfaceVariant = Color(0xFFFFFFFF),
    onSurfaceVariant = Color(0xFF554A60).copy(alpha = 0.7f),
    surfaceTint = Color(0xFFFFFFFF),
    inverseSurface = Color(0xFF554A60),
    inverseOnSurface = Color(0xFFFFFFFF),
    error = Color(0xFFB00020),
    onError = Color.White,
    errorContainer = Color(0xFFFDE7E9),
    onErrorContainer = Color(0xFFB00020),
    outline = Color(0xFF554A60).copy(alpha = 0.2f),
    outlineVariant = Color(0xFF554A60).copy(alpha = 0.1f),
    scrim = Color(0xFF000000).copy(alpha = 0.3f)
)

// 更新任务颜色，确保所有颜色都有80%透明度
val TaskColors = listOf(
    Color(0xCCFFF0C8), // 黄色 (80%不透明度，CC=204/255≈0.8)
    Color(0xCCE8E4F3), // 淡紫色 (80%不透明度)
    Color(0xCCFFD6DB), // 粉红色 (80%不透明度)
    Color(0xCCD8F3E7)  // 薄荷绿 (80%不透明度)
)

/**
 * 日历视图类型
 */
enum class CalendarViewType {
    MONTH,      // 月视图
    WEEK,       // 周视图（简洁版）
    WEEK_DETAIL,// 周视图（详细版）
    DAY,        // 日视图
    COMPARISON  // 对比视图
}

// 优化 NoRippleInteractionSource 实现
private object NoRippleInteractionSource : MutableInteractionSource {
    override val interactions: Flow<Interaction> = emptyFlow()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction): Boolean = false
}

// 提供一个函数，返回记住的无涟漪交互源
@Composable
private fun rememberNoRippleInteractionSource(): MutableInteractionSource {
    return remember { NoRippleInteractionSource }
}

/**
 * 日历屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    viewModel: CalendarViewModel = hiltViewModel(),
    navController: NavController,
    statusBarHeight: Dp = 24.dp
) {
    // 获取视图引用和上下文
    val view = LocalView.current
    val context = LocalContext.current
    
    // 优化：合并硬件加速设置逻辑到单个LaunchedEffect
    LaunchedEffect(Unit) {
        try {
            // 为当前View启用硬件加速
            view.enableHardwareAcceleration()
            
            // 如果上下文是Activity，直接设置窗口标志
            (context as? Activity)?.let { activity ->
                // 检查是否已经启用了硬件加速
                if (activity.window.attributes.flags and 
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED == 0
                ) {
                    activity.window.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )
                }
            }
        } catch (e: Exception) {
            // 安全处理异常，避免崩溃
            Log.e("CalendarScreen", "启用硬件加速失败", e)
        }
    }
    
    val selectedDateState = viewModel.selectedDate.collectAsStateWithLifecycle()
    var selectedDate by remember { mutableStateOf(selectedDateState.value) }

    val colorPreferences = viewModel.userColorPreference.collectAsStateWithLifecycle()
    
    // 减少日志记录频率
    LaunchedEffect(colorPreferences.value.calendarPageBackground) {
        Log.d("CalendarScreen", "获取到的用户颜色偏好: calendar=${colorPreferences.value.calendarPageBackground.toString(16)}")
    }
    
    // 优化背景色计算 - 使用派生状态以提高性能
    val colorValue = colorPreferences.value.calendarPageBackground
    val calendarBackgroundColor by derivedStateOf {
        try {
            // 从Long值提取ARGB分量
            val a = ((colorValue shr 24) and 0xFFL).toInt()
            val r = ((colorValue shr 16) and 0xFFL).toInt()
            val g = ((colorValue shr 8) and 0xFFL).toInt()
            val b = (colorValue and 0xFFL).toInt()
            
            // 创建Color对象
            Color(r, g, b, a)
        } catch (e: Exception) {
            Log.e("CalendarScreen", "颜色转换失败", e)
            Color.White // 失败时使用默认白色
        }
    }
    
    // Update selected date when viewModel state changes
    LaunchedEffect(selectedDateState.value) {
        selectedDate = selectedDateState.value
    }
    
    // 日历视图类型状态
    var calendarViewType by remember { mutableStateOf(CalendarViewType.WEEK) }

    // 当前年月状态，用于月视图
    val currentYearMonthState = viewModel.currentYearMonth.collectAsState()
    var currentYearMonth by remember { mutableStateOf(currentYearMonthState.value) }
    
    // 当前周状态，用于周视图
    var currentWeek by remember { 
        mutableStateOf(
            Pair(
                selectedDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)), 
                selectedDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
            )
        ) 
    }
    
    // 当前周的开始日期
    var currentWeekStart by remember { mutableStateOf(currentWeek.first) }
    
    // 更新年月状态
    LaunchedEffect(currentYearMonthState.value) {
        currentYearMonth = currentYearMonthState.value
    }
    
    // 滚动到当前时间的状态
    var scrollToCurrentTime by remember { mutableStateOf(false) }
    
    // 缓存上下文和活动引用
    val activity = remember { context as? Activity }
    
    // 优化系统栏设置
    DisposableEffect(Unit) {
        activity?.let { act -> 
            SystemBarManager.setupTaskPageSystemBars(act)
        }
        onDispose {}
    }

    // 优化事件处理
    LaunchedEffect(Unit) {
        viewModel.subscribeToEvents()
        
        viewModel.eventFlow.collectLatest { event ->
            when (event) {
                is CalendarViewModel.CalendarUiEvent.Error -> {
                    Log.e("CalendarScreen", "Calendar event error: ${event.message}")
                }
                is CalendarViewModel.CalendarUiEvent.EventsUpdated -> {
                    Log.d("CalendarScreen", "Calendar events updated")
                }
            }
        }
    }
    
    // 选择日期时更新ViewModel
    val handleDateSelection: (LocalDate) -> Unit = { date ->
        selectedDate = date
        viewModel.setSelectedDate(date)
    }

    // 长按创建状态
    var isCreatingNewEvent by remember { mutableStateOf(false) }
    var creationStartTime by remember { mutableStateOf<LocalDateTime?>(null) }
    var creationEndTime by remember { mutableStateOf<LocalDateTime?>(null) }
    var showCreateEventDialog by remember { mutableStateOf(false) }
    var newEventStartTime by remember { mutableStateOf<LocalDateTime?>(null) }
    var newEventEndTime by remember { mutableStateOf<LocalDateTime?>(null) }

    // 获取HabitViewModel
    val habitViewModel: HabitViewModel = hiltViewModel()
    
    // 主布局使用 Box 包装以提高性能
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(calendarBackgroundColor)
    ) {
        Scaffold(
            containerColor = calendarBackgroundColor,
            topBar = {
                CalendarTopBar(
                    statusBarHeight = statusBarHeight,
                    calendarViewType = calendarViewType,
                    onCalendarViewTypeChange = { newType -> calendarViewType = newType },
                    selectedDate = selectedDate,
                    currentYearMonth = currentYearMonth,
                    currentWeek = currentWeek,
                    onDateSelected = handleDateSelection,
                    onYearMonthChanged = { newYM -> 
                        currentYearMonth = newYM
                        viewModel.setCurrentYearMonth(newYM)
                    },
                    onWeekStartChanged = { newStart ->
                        val weekEnd = newStart.plusDays(6)
                        currentWeek = Pair(newStart, weekEnd)
                        currentWeekStart = newStart
                    },
                    onScrollToCurrentTime = { scrollToCurrentTime = true },
                    backgroundColor = calendarBackgroundColor // 传递背景颜色
                )
            }
        ) { innerPadding ->
            // 使用 key 优化重组控制
            key(calendarViewType) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding)
                ) {
                    when (calendarViewType) {
                        CalendarViewType.MONTH -> {
                            // 使用 LazyVerticalGrid 优化月视图性能
                            MonthCalendarView(
                                yearMonth = currentYearMonth,
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                backgroundColor = calendarBackgroundColor,
                                viewModel = viewModel,
                                habitViewModel = habitViewModel
                            )
                        }
                        CalendarViewType.WEEK -> {
                            WeekCalendarView(
                                weekRange = Pair(currentWeekStart, currentWeekStart.plusDays(6)),
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                viewModel = viewModel,
                                habitViewModel = habitViewModel,
                                backgroundColor = calendarBackgroundColor
                            )
                        }
                        CalendarViewType.WEEK_DETAIL -> {
                            // TODO: DetailedWeekCalendarView未实现，暂时使用普通WeekCalendarView替代
                            WeekCalendarView(
                                weekRange = Pair(currentWeekStart, currentWeekStart.plusDays(6)),
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                viewModel = viewModel,
                                backgroundColor = calendarBackgroundColor
                            )
                            // 原代码暂时注释掉，等实现完成后再启用
                            /*
                            DetailedWeekCalendarView(
                                startDate = currentWeekStart,
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                events = viewModel.allEvents.collectAsState().value,
                                modifier = Modifier.fillMaxSize()
                            )
                            */
                        }
                        CalendarViewType.DAY -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = 8.dp)
                            ) {
                                DayCalendarView(
                                    date = selectedDate,
                                    viewModel = viewModel,
                                    navController = navController,
                                    scrollToCurrentTime = scrollToCurrentTime,
                                    backgroundColor = calendarBackgroundColor
                                )
                            }
                        }
                        CalendarViewType.COMPARISON -> {
                            ComparisonCalendarView(
                                date = selectedDate,
                                viewModel = viewModel,
                                backgroundColor = calendarBackgroundColor
                            )
                        }
                    }
                    
                    // 创建事件对话框
                    if (showCreateEventDialog) {
                        // TODO: CreateEventDialog未实现，待完善
                        /*
                        CreateEventDialog(
                            startTime = newEventStartTime,
                            endTime = newEventEndTime,
                            onDismiss = { showCreateEventDialog = false },
                            onCreateEvent = { title, description ->
                                newEventStartTime?.let { start ->
                                    newEventEndTime?.let { end ->
                                        viewModel.createEvent(title, description, start, end)
                                        showCreateEventDialog = false
                                    }
                                }
                            }
                        )
                        */
                        // 暂时使用简单对话框
                        AlertDialog(
                            onDismissRequest = { showCreateEventDialog = false },
                            title = { Text("创建新事件") },
                            text = { Text("此功能尚未完全实现，请稍后再试。") },
                            confirmButton = {
                                TextButton(onClick = { showCreateEventDialog = false }) {
                                    Text("确定")
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 日历顶部栏，包含导航和操作按钮
 */
@Composable
fun CalendarTopBar(
    statusBarHeight: Dp,
    calendarViewType: CalendarViewType,
    onCalendarViewTypeChange: (CalendarViewType) -> Unit,
    selectedDate: LocalDate,
    currentYearMonth: YearMonth,
    currentWeek: Pair<LocalDate, LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onYearMonthChanged: (YearMonth) -> Unit,
    onWeekStartChanged: (LocalDate) -> Unit,
    onScrollToCurrentTime: () -> Unit,
    backgroundColor: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = statusBarHeight)
            .background(backgroundColor)
            .padding(vertical = 8.dp)
    ) {
        // 日历操作按钮
        CalendarActions(
            calendarViewType = calendarViewType,
            onCalendarViewTypeChange = onCalendarViewTypeChange,
            onScrollToCurrentTime = onScrollToCurrentTime,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )
        
        // 日历导航
        CalendarNavigation(
            calendarViewType = calendarViewType,
            selectedDate = selectedDate,
            currentYearMonth = currentYearMonth,
            currentWeek = currentWeek,
            onYearMonthChanged = onYearMonthChanged,
            onWeekStartChanged = onWeekStartChanged,
            onDateSelected = onDateSelected,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )
    }
}

/**
 * 日历操作按钮
 */
@Composable
fun CalendarActions(
    calendarViewType: CalendarViewType,
    onCalendarViewTypeChange: (CalendarViewType) -> Unit,
    onScrollToCurrentTime: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 月/周视图切换
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (calendarViewType == CalendarViewType.MONTH) 
                    Color(0xFFbecabb) else Color(0xFFF5F5F5)
                )
                .clickable(
                    interactionSource = rememberNoRippleInteractionSource(),
                    indication = null
                ) {
                    onCalendarViewTypeChange(
                        if (calendarViewType == CalendarViewType.MONTH) CalendarViewType.WEEK 
                        else CalendarViewType.MONTH
                    )
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = if (calendarViewType == CalendarViewType.MONTH) 
                    Icons.Default.CalendarViewMonth else Icons.Default.CalendarViewWeek,
                contentDescription = "月/周视图",
                tint = if (calendarViewType == CalendarViewType.MONTH) 
                    Color.White else Color(0xFF757575),
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 周视图切换（详细/简洁）
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (calendarViewType == CalendarViewType.WEEK_DETAIL) 
                    Color(0xFFbecabb) else Color(0xFFF5F5F5)
                )
                .clickable(
                    interactionSource = rememberNoRippleInteractionSource(),
                    indication = null
                ) {
                    onCalendarViewTypeChange(
                        if (calendarViewType == CalendarViewType.WEEK_DETAIL) 
                        CalendarViewType.WEEK else CalendarViewType.WEEK_DETAIL
                    )
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = if (calendarViewType == CalendarViewType.WEEK_DETAIL) 
                    Icons.Default.ViewWeek else Icons.Default.ViewDay,
                contentDescription = "周视图详细/简洁",
                tint = if (calendarViewType == CalendarViewType.WEEK_DETAIL) 
                    Color.White else Color(0xFF757575),
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 日/对比视图切换
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (calendarViewType == CalendarViewType.DAY || 
                        calendarViewType == CalendarViewType.COMPARISON)
                    Color(0xFFbecabb) else Color(0xFFF5F5F5)
                )
                .clickable(
                    interactionSource = rememberNoRippleInteractionSource(),
                    indication = null
                ) {
                    onCalendarViewTypeChange(
                        when (calendarViewType) {
                            CalendarViewType.DAY -> CalendarViewType.COMPARISON
                            CalendarViewType.COMPARISON -> CalendarViewType.DAY
                            else -> CalendarViewType.DAY
                        }
                    )
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = if (calendarViewType == CalendarViewType.DAY) 
                    Icons.Default.CalendarViewDay else Icons.Default.CompareArrows,
                contentDescription = "日/对比视图",
                tint = if (calendarViewType == CalendarViewType.DAY || 
                          calendarViewType == CalendarViewType.COMPARISON)
                    Color.White else Color(0xFF757575),
                modifier = Modifier.size(24.dp)
            )
        }
        
        // Today button
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(Color(0xFFF5F5F5))
                .clickable(
                    interactionSource = rememberNoRippleInteractionSource(),
                    indication = null
                ) {
                    onScrollToCurrentTime()
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Today,
                contentDescription = "今天",
                tint = Color(0xFF757575),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

// Add CalendarNavigation composable
@Composable
private fun CalendarNavigation(
    calendarViewType: CalendarViewType,
    selectedDate: LocalDate,
    currentYearMonth: YearMonth,
    currentWeek: Pair<LocalDate, LocalDate>,
    onYearMonthChanged: (YearMonth) -> Unit,
    onWeekStartChanged: (LocalDate) -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(CalendarColors.background) // 应用背景颜色
            .padding(horizontal = 20.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Previous button
        IconButton(
            onClick = {
                when (calendarViewType) {
                    CalendarViewType.MONTH -> onYearMonthChanged(currentYearMonth.minusMonths(1))
                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                        val newStartDate = currentWeek.first.minusWeeks(1)
                        onWeekStartChanged(newStartDate)
                        // 选择周视图中的第一天来确保事件正确显示
                        onDateSelected(newStartDate)
                    }
                    else -> onDateSelected(selectedDate.minusDays(1))
                }
            },
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(Color(0xFFFBFCFC))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "上一个",
                tint = Color(0xFF554A60),
                modifier = Modifier.size(18.dp)
            )
        }

        // Date range text
        Text(
            text = when(calendarViewType) {
                CalendarViewType.MONTH -> currentYearMonth.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
                CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> 
                    "${currentWeek.first.format(DateTimeFormatter.ofPattern("MM月dd日"))} - ${currentWeek.second.format(DateTimeFormatter.ofPattern("MM月dd日"))}"
                CalendarViewType.DAY -> selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
                CalendarViewType.COMPARISON -> "${selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}时间对比"
            },
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF3C3C3C),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 8.dp)
        )

        // Next button
        IconButton(
            onClick = {
                when (calendarViewType) {
                    CalendarViewType.MONTH -> onYearMonthChanged(currentYearMonth.plusMonths(1))
                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                        val newStartDate = currentWeek.first.plusWeeks(1)
                        onWeekStartChanged(newStartDate)
                        // 选择周视图中的第一天来确保事件正确显示
                        onDateSelected(newStartDate)
                    }
                    else -> onDateSelected(selectedDate.plusDays(1))
                }
            },
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(Color(0xFFFBFCFC))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "下一个",
                tint = Color(0xFF554A60),
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

// 辅助函数：获取周范围
private fun getWeekRange(date: LocalDate): Pair<LocalDate, LocalDate> {
    // 获取一周的开始（周一）和结束（周日）
    var start = date
    while (start.dayOfWeek != DayOfWeek.MONDAY) {
        start = start.minusDays(1)
    }
    
    var end = date
    while (end.dayOfWeek != DayOfWeek.SUNDAY) {
        end = end.plusDays(1)
    }
    
    return Pair(start, end)
}

/**
 * 对比视图日历 - 比较计划和实际完成情况
 */
@Composable
fun ComparisonCalendarView(
    date: LocalDate,
    viewModel: CalendarViewModel = hiltViewModel(),
    backgroundColor: Color = CalendarColors.background
) {
    // 获取当天的事件
    val events = viewModel.getEventsForDate(date)
    
    // 计算计划时间和实际时间
    val (plannedTime, actualTime) = remember(events) {
        events.fold(Pair(0L, 0L)) { acc, event ->
            val planned = if (event.end != null) {
                ChronoUnit.MINUTES.between(event.start, event.end)
            } else {
                60L // 默认1小时
            }
            
            // 计算实际时间：如果任务已完成，使用计划时间作为实际时间
            val actual = if (event.isCompleted) planned else 0L
            
            Pair(acc.first + planned, acc.second + actual)
        }
    }
    
    // 格式化显示时间
    val formatDuration = { minutes: Long ->
        val hours = minutes / 60
        val mins = minutes % 60
        "${hours}H ${mins}M"
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor) // 使用传入的背景色
            .padding(horizontal = 8.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 计划时间标题
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "PLAN",
                    fontSize = 16.sp,
                    color = CalendarColors.onSurface.copy(alpha = 0.7f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = formatDuration(plannedTime),
                    fontSize = 24.sp,
                    color = CalendarColors.primary,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // 分隔线
            Divider(
                modifier = Modifier
                    .height(80.dp)
                    .width(1.dp)
                    .padding(horizontal = 8.dp),
                color = CalendarColors.onSurface.copy(alpha = 0.1f)
            )
            
            // 实际时间标题
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "RECORD",
                    fontSize = 16.sp,
                    color = CalendarColors.onSurface.copy(alpha = 0.7f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = formatDuration(actualTime),
                    fontSize = 24.sp,
                    color = Color.Black,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        
        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 全天事项区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 全天事项左侧 - 计划
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 4.dp)
                ) {
                    Column {
                        events.filter { it.isAllDay == true }.forEach { event ->
                            ComparisonTaskItem(
                                title = event.title,
                                color = event.color?.let { Color(it) } ?: 
                                    TaskColors[event.urgency.ordinal % TaskColors.size],
                                isPlan = true
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
                
                // 分隔线
                Box(
                    modifier = Modifier
                        .width(1.dp)
                        .height(90.dp)
                        .background(CalendarColors.onSurface.copy(alpha = 0.1f))
                )
                
                // 全天事项右侧 - 实际
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 4.dp)
                ) {
                    Column {
                        events.filter { it.isAllDay == true && it.isCompleted == true }
                            .forEach { event ->
                                ComparisonTaskItem(
                                    title = event.title,
                                    color = event.color?.let { Color(it) } ?: 
                                        TaskColors[event.urgency.ordinal % TaskColors.size],
                                    isPlan = false
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                    }
                }
            }
        }

        Divider(color = CalendarColors.onSurface.copy(alpha = 0.1f))
        
        // 时间表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 80.dp)
        ) {
            items((0..23).toList()) { hour ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min)
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.Top
                ) {
                    // 小时标签
                    Text(
                        text = "${hour.toString().padStart(2, '0')}",
                        fontSize = 12.sp,
                        modifier = Modifier.width(20.dp),
                        color = CalendarColors.onSurface.copy(alpha = 0.6f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 计划内容 (左侧)
                    Box(modifier = Modifier
                        .weight(1f)
                        .height(50.dp)
                        .padding(end = 4.dp)
                    ) {
                        // 显示该小时的事件
                        events.filter { !it.isAllDay && it.start.hour == hour }
                            .forEach { event ->
                                ComparisonTaskItem(
                                    title = event.title,
                                    color = event.color?.let { Color(it) } ?: 
                                        TaskColors[event.urgency.ordinal % TaskColors.size],
                                    isPlan = true
                                )
                            }
                    }
                    
                    // 分隔线
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(50.dp)
                            .background(CalendarColors.onSurface.copy(alpha = 0.1f))
                    )
                    
                    // 实际内容 (右侧)
                    Box(modifier = Modifier
                        .weight(1f)
                        .height(50.dp)
                        .padding(start = 4.dp)
                    ) {
                        // 显示该小时已完成的事件
                        events.filter { !it.isAllDay && it.start.hour == hour && it.isCompleted }
                            .forEach { event ->
                                ComparisonTaskItem(
                                    title = event.title,
                                    color = event.color?.let { Color(it) } ?: 
                                        TaskColors[event.urgency.ordinal % TaskColors.size],
                                    isPlan = false
                                )
                            }
                    }
                }
                Divider(
                    color = CalendarColors.onSurface.copy(alpha = 0.05f),
                    thickness = 0.5.dp
                )
            }
        }
    }
}

/**
 * 对比视图中的任务项
 */
@Composable
fun ComparisonTaskItem(
    title: String,
    color: Color,
    isPlan: Boolean // 是否为计划项
) {
    // 从基础颜色派生不同透明度的颜色
    val backgroundColor = color.copy(alpha = 0.75f) // 卡片背景: 75%不透明度
    val borderColor = color.copy(alpha = 0.85f)     // 卡片边框: 85%不透明度
    val textColor = CalendarColors.primary         // 文本使用100%不透明度确保可读性

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(backgroundColor)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(6.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Text(
            text = title,
            color = textColor,
            fontSize = 14.sp,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * 月视图日历
 */
@Composable
fun MonthCalendarView(
    yearMonth: YearMonth,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    backgroundColor: Color = CalendarColors.background,
    viewModel: CalendarViewModel = hiltViewModel(),
    habitViewModel: HabitViewModel = hiltViewModel()
) {
    val daysInMonth = yearMonth.lengthOfMonth()
    val firstDayOfMonth = yearMonth.atDay(1)
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 调整为周日开始
    
    // 构建显示的日期列表
    val datesList = buildList {
        // 前一个月的填充日期
        for (i in 0 until firstDayOfWeek) {
            add(firstDayOfMonth.minusDays((firstDayOfWeek - i).toLong()))
        }
        
        // 当前月的日期
        for (i in 1..daysInMonth) {
            add(yearMonth.atDay(i))
        }
        
        // 确保总数是7的倍数
        val remainder = (firstDayOfWeek + daysInMonth) % 7
        if (remainder > 0) {
            val daysToAdd = 7 - remainder
            for (i in 1..daysToAdd) {
                add(yearMonth.atEndOfMonth().plusDays(i.toLong()))
            }
        }
    }
    
    // 获取月份的所有事件与习惯
    val calendarState by viewModel.calendarState.collectAsStateWithLifecycle()
    val habits = habitViewModel.habits.collectAsState().value
    
    // 为每个日期预计算其事件和习惯
    val dateEventsMap = remember(calendarState.events, yearMonth) {
        calendarState.events
            .filter { it.start.toLocalDate().year == yearMonth.year && it.start.toLocalDate().month == yearMonth.month }
            .groupBy { it.start.toLocalDate() }
    }
    
    val dateHabitsMap = remember(habits, yearMonth) {
        habits.flatMap { habit ->
            habit.completedDates.filter { date -> 
                date.year == yearMonth.year && date.month == yearMonth.month
            }.map { date -> date to habit }
        }.groupBy({ it.first }, { it.second })
    }
    
    // 添加习惯打卡功能
    val context = LocalContext.current
    
    // 处理习惯点击的函数
    val handleHabitClick = { habit: HabitData, date: LocalDate ->
        // 检查是否已完成
        val isCompleted = habit.completedDates.contains(date)
        
        if (isCompleted) {
            // 取消打卡
            habitViewModel.removeCompletedDate(habit.id, date)
            // 提供反馈
            Toast.makeText(context, "已取消「${habit.name}」打卡", Toast.LENGTH_SHORT).show()
        } else {
            // 添加打卡
            habitViewModel.addCompletedDate(habit.id, date)
            // 提供反馈
            Toast.makeText(context, "「${habit.name}」打卡成功", Toast.LENGTH_SHORT).show()
        }
    }

    // 习惯对话框状态
    var showHabitsDialog by remember { mutableStateOf(false) }

    // 点击日期时的处理，如果当前日期等于选中日期，显示该日期的习惯列表
    val onDateClicked: (LocalDate) -> Unit = { date ->
        onDateSelected(date)
        
        // 如果点击的是当前已选中的日期，则显示习惯列表对话框
        if (date == selectedDate && date.month == yearMonth.month) {
            val habitsForDate = dateHabitsMap[date] ?: emptyList()
            if (habitsForDate.isNotEmpty()) {
                // 显示习惯列表对话框，允许用户点击打卡
                showHabitsDialog = true
            }
        }
    }
    
    // 显示习惯打卡对话框
    if (showHabitsDialog) {
        val habitsForSelectedDate = dateHabitsMap[selectedDate] ?: emptyList()
        
        AlertDialog(
            onDismissRequest = { showHabitsDialog = false },
            title = {
                Text(
                    text = "${selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}习惯打卡",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Column {
                    habitsForSelectedDate.forEach { habit ->
                        val isCompleted = habit.completedDates.contains(selectedDate)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                                .clickable {
                                    handleHabitClick(habit, selectedDate)
                                    // 延迟关闭对话框，让用户看到反馈效果
                                    showHabitsDialog = false
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 图标
                            if (habit.customEmoji.isNotEmpty()) {
                                Text(
                                    text = habit.customEmoji,
                                    fontSize = 20.sp,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = habit.icon,
                                    contentDescription = null,
                                    tint = habit.color,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .padding(end = 4.dp)
                                )
                            }
                            
                            // 习惯名称
                            Text(
                                text = habit.name,
                                fontSize = 16.sp,
                                color = Color.Black,
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 打卡状态
                            if (isCompleted) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "已完成",
                                    tint = Color(0xFF4CAF50),
                                    modifier = Modifier.size(24.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.RadioButtonUnchecked,
                                    contentDescription = "未完成",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showHabitsDialog = false }) {
                    Text("关闭")
                }
            }
        )
    }
    
    // 使用网格布局显示日历
    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        contentPadding = PaddingValues(4.dp),
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        // 添加星期几的标题行
        items(listOf("日", "一", "二", "三", "四", "五", "六")) { dayName ->
            Text(
                text = dayName,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = if(dayName == "日" || dayName == "六") Color(0xFFE57373).copy(alpha = 0.8f) else CalendarColors.onSurface.copy(alpha = 0.6f)
            )
        }
        
        items(datesList) { date ->
            MonthCalendarDay(
                date = date,
                isCurrentMonth = date.month == yearMonth.month,
                isSelected = date == selectedDate,
                isToday = date == LocalDate.now(),
                events = dateEventsMap[date] ?: emptyList(),
                habits = dateHabitsMap[date] ?: emptyList(),
                onDateClick = onDateClicked
            )
        }
    }
}

/**
 * 月视图的单个日期单元格，增强显示事件和习惯
 */
@Composable
fun MonthCalendarDay(
    date: LocalDate,
    isCurrentMonth: Boolean,
    isSelected: Boolean,
    isToday: Boolean,
    events: List<CalendarEvent>,
    habits: List<HabitData>,
    onDateClick: (LocalDate) -> Unit
) {
    // 习惯对话框状态
    var showHabitsDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val habitViewModel: HabitViewModel = hiltViewModel()

    // 缓存颜色和修饰符
    val backgroundColor = remember(isSelected, isToday) {
        when {
            isSelected -> Color(0xE6E8E4EC)
            isToday -> CalendarColors.primary.copy(alpha = 0.1f)
            else -> Color.Transparent
        }
    }
    
    val textColor = remember(isSelected, isToday, isCurrentMonth) {
        when {
            isSelected -> CalendarColors.onPrimary
            isToday -> CalendarColors.primary
            !isCurrentMonth -> CalendarColors.onSurface.copy(alpha = 0.4f)
            else -> CalendarColors.onPrimary
        }
    }
    
    val fontWeight = remember(isToday, isSelected) {
        if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal
    }

    // 习惯对话框
    if (showHabitsDialog && habits.isNotEmpty()) {
        AlertDialog(
            onDismissRequest = { showHabitsDialog = false },
            title = {
                Text(
                    text = "${date.format(DateTimeFormatter.ofPattern("MM月dd日"))}习惯打卡",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Column {
                    habits.forEach { habit ->
                        val isCompleted = habit.completedDates.contains(date)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                                .clickable {
                                    if (isCompleted) {
                                        habitViewModel.removeCompletedDate(habit.id, date)
                                        Toast.makeText(context, "已取消「${habit.name}」打卡", Toast.LENGTH_SHORT).show()
                                    } else {
                                        habitViewModel.addCompletedDate(habit.id, date)
                                        Toast.makeText(context, "「${habit.name}」打卡成功", Toast.LENGTH_SHORT).show()
                                    }
                                    showHabitsDialog = false
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 图标
                            if (habit.customEmoji.isNotEmpty()) {
                                Text(
                                    text = habit.customEmoji,
                                    fontSize = 20.sp,
                                    modifier = Modifier.padding(end = 8.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = habit.icon,
                                    contentDescription = null,
                                    tint = habit.color,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .padding(end = 4.dp)
                                )
                            }
                            
                            // 习惯名称
                            Text(
                                text = habit.name,
                                fontSize = 16.sp,
                                color = Color.Black,
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 打卡状态
                            if (isCompleted) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "已完成",
                                    tint = Color(0xFF4CAF50),
                                    modifier = Modifier.size(24.dp)
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.RadioButtonUnchecked,
                                    contentDescription = "未完成",
                                    tint = Color.Gray,
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showHabitsDialog = false }) {
                    Text("关闭")
                }
            }
        )
    }

    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable(
                interactionSource = rememberNoRippleInteractionSource(),
                indication = null
            ) {
                onDateClick(date)
            }
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(1.dp)
        ) {
            // 日期数字
            Text(
                text = date.dayOfMonth.toString(),
                color = textColor,
                fontSize = 14.sp,
                fontWeight = fontWeight,
                overflow = TextOverflow.Clip,
                maxLines = 1
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            // 事件和习惯指示
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 习惯和事件指示器
                if ((habits.isNotEmpty() || events.isNotEmpty()) && isCurrentMonth) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 2.dp)
                            .clickable(
                                enabled = habits.isNotEmpty(),
                                interactionSource = rememberNoRippleInteractionSource(),
                                indication = null
                            ) {
                                if (habits.isNotEmpty()) {
                                    showHabitsDialog = true
                                }
                            },
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 显示习惯指示点
                        if (habits.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .size(5.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF81C784).copy(alpha = 0.8f))
                            )
                        }
                        
                        // 如果同时有事件和习惯，添加间距
                        if (habits.isNotEmpty() && events.isNotEmpty()) {
                            Spacer(modifier = Modifier.width(3.dp))
                        }
                        
                        // 显示事件指示点
                        if (events.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .size(5.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFFAB47BC).copy(alpha = 0.8f))
                            )
                        }
                        
                        // 显示总数
                        val totalCount = habits.size + events.size
                        if (totalCount > 2) {
                            Spacer(modifier = Modifier.width(3.dp))
                            Text(
                                text = "+${totalCount - 2}",
                                fontSize = 8.sp,
                                color = textColor.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 修改现有的日视图使用新的增强时间轴
 */
@Composable
fun DayCalendarView(
    date: LocalDate,
    viewModel: CalendarViewModel = hiltViewModel(),
    navController: NavController,
    scrollToCurrentTime: Boolean = false,
    backgroundColor: Color = CalendarColors.background
) {
    // 使用collectAsStateWithLifecycle优化状态收集
    val calendarState by viewModel.calendarState.collectAsStateWithLifecycle()
    
    // 日期相关的事件
    val dateEvents = remember(calendarState, date) {
        calendarState.events.filter { it.start.toLocalDate() == date }
    }
    
    // 使用Box包装器增加底部内边距，确保内容不被导航栏遮挡
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor) // 使用传入的背景色
    ) {
        // 使用新的增强垂直时间轴实现
        EnhancedDayTimelineView(
            viewModel = viewModel,
            selectedDate = date,
            events = dateEvents,
            onEventClick = { /* 已在组件内部处理事件点击 */ },
            scrollToCurrentTime = scrollToCurrentTime,
            backgroundColor = backgroundColor // 传递背景色给子组件
        )
    }
}

/**
 * 增强版日视图垂直时间轴
 */
@Composable
fun EnhancedDayTimelineView(
    viewModel: CalendarViewModel,
    modifier: Modifier = Modifier,
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    hourHeightDp: Dp = 60.dp,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    onEventClick: (CalendarEvent) -> Unit = {},
    scrollToCurrentTime: Boolean = false,
    backgroundColor: Color = CalendarColors.background // 添加背景色参数
) {
    val density = LocalDensity.current
    val hourHeightPx = with(density) { hourHeightDp.toPx() }
    val state = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val dayStartHour = 0
    val dayEndHour = 24
    
    // 状态变量
    var isDragging by remember { mutableStateOf(false) }
    var draggedEventId by remember { mutableStateOf<String?>(null) }
    var dragStartY by remember { mutableStateOf(0f) }
    var dragCurrentY by remember { mutableStateOf(0f) }
    
    // 创建事件对话框状态
    var showCreateEventDialog by remember { mutableStateOf(false) }
    var newEventStartTime by remember { mutableStateOf<LocalTime?>(null) }
    var newEventTitle by remember { mutableStateOf("") }
    var newEventDuration by remember { mutableStateOf(60) } // 默认1小时
    
    // 颜色选择器状态
    var showColorPicker by remember { mutableStateOf(false) }
    var selectedColor by remember { mutableStateOf<Color?>(null) }
    var currentRed by remember { mutableStateOf(205f) }   // 默认R值
    var currentGreen by remember { mutableStateOf(86f) }  // 默认G值
    var currentBlue by remember { mutableStateOf(84f) }   // 默认B值
    var currentAlpha by remember { mutableStateOf(0.2f) } // 默认透明度20%
    
    // 编辑模式状态
    var isEditMode by remember { mutableStateOf(false) }
    var editingEventId by remember { mutableStateOf<String?>(null) }
    
    // 监听UI事件
    LaunchedEffect(viewModel) {
        viewModel.eventFlow.collect { event ->
            when (event) {
                is CalendarViewModel.CalendarUiEvent.EventsUpdated -> {
                    Log.d("EnhancedDayTimelineView", "事件更新通知已接收")
                }
                is CalendarViewModel.CalendarUiEvent.Error -> {
                    Log.e("EnhancedDayTimelineView", "事件错误: ${event.message}")
                }
            }
        }
    }
    
    // 对事件进行时间重叠分析，获取每个事件的布局信息
    val eventsLayoutInfo = remember(events) {
        Log.d("EnhancedDayTimelineView", "计算事件布局, 事件数量: ${events.size}")
        calculateEventsLayout(events)
    }
    
    // 滚动到当前时间的函数
    val scrollToCurrentTimePosition = {
        val currentHour = LocalTime.now().hour
        val currentMinute = LocalTime.now().minute
        
        // 计算滚动位置，考虑小时和分钟，更精确地滚动到当前时间点
        // 如果想要滚动到稍微靠上的位置，可以减去一个偏移量
        val scrollPosition = (currentHour - 0.5f).coerceAtLeast(0f)
        
        coroutineScope.launch {
            // 使用带有偏移量的animateScrollToItem可以提供更平滑的滚动体验
            state.animateScrollToItem(currentHour.coerceAtLeast(0), 
                (currentMinute.toFloat() / 60f * hourHeightPx).toInt())
        }
    }
    
    // 默认滚动到当前时间附近
    LaunchedEffect(Unit) {
        scrollToCurrentTimePosition()
    }
    
    // 响应scrollToCurrentTime状态的变化
    LaunchedEffect(scrollToCurrentTime) {
        if (scrollToCurrentTime) {
            scrollToCurrentTimePosition()
        }
    }
    
    Box(modifier = modifier.fillMaxSize().background(backgroundColor)) { // 使用传入的背景色
        LazyColumn(
            state = state,
            contentPadding = contentPadding,
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor) // 使用传入的背景色
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { offset ->
                            // 计算点击位置对应的时间
                            val hourIndex = (offset.y / hourHeightPx).toInt()
                            val minute = ((offset.y % hourHeightPx) / hourHeightPx * 60).toInt()
                            val clickTime = LocalTime.of(
                                (dayStartHour + hourIndex).coerceIn(0, 23), 
                                (minute / 5 * 5).coerceIn(0, 59)
                            )
                            
                            // 显示创建事件对话框
                            newEventStartTime = clickTime
                            showCreateEventDialog = true
                            
                            // 重置颜色选择器，使用默认颜色
                            currentRed = 205f
                            currentGreen = 86f
                            currentBlue = 84f
                            selectedColor = null
                        }
                    )
                }
        ) {
            items(24) { hour ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(hourHeightDp)
                ) {
                    // 时间轴的小时标记和网格
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight()
                    ) {
                        // 时间标签
                        Box(
                            modifier = Modifier
                                .width(50.dp)
                                .fillMaxHeight(),
                            contentAlignment = Alignment.TopEnd
                        ) {
                            Text(
                                text = String.format("%02d:00", hour),
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray,
                                modifier = Modifier.padding(top = 4.dp, end = 8.dp)
                            )
                        }
                        
                        // 分隔线
                        Box(
                            modifier = Modifier
                                .width(1.dp)
                                .fillMaxHeight()
                                .background(Color.LightGray.copy(alpha = 0.5f))
                        )
                        
                        // 时间块内容区
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        ) {
                            // 渲染当前小时的事件
                            eventsLayoutInfo.forEach { (event, layout) ->
                                // 当前事件的开始时间和结束时间
                                val eventStartHour = event.start.hour
                                val eventEndHour = event.end?.hour ?: (event.start.hour + 1)
                                
                                // 只处理跨越当前小时的事件
                                if (hour in eventStartHour..eventEndHour) {
                                    // 计算在当前小时内的起始位置
                                    val startMinute = if (eventStartHour == hour) event.start.minute else 0
                                    val endMinute = if (eventEndHour == hour) (event.end?.minute ?: 0) else 60
                                    
                                    // 计算位置和高度比例
                                    val topOffsetRatio = startMinute.toFloat() / 60f
                                    val heightRatio = (endMinute - startMinute).toFloat() / 60f
                                    
                                    // 转换为dp值
                                    val topOffset = hourHeightDp * topOffsetRatio
                                    val height = (hourHeightDp * heightRatio).coerceAtLeast(30.dp) // 最小高度30dp
                                    
                                    // 格式化时间显示
                                    val startTimeStr = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                                    val endTimeStr = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: 
                                        event.start.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))
                                    
                                    // 只在事件开始的小时显示完整事件
                                    if (event.start.hour == hour) {
                                        // 判断是否正在拖拽当前事件
                                        val isDraggingThis = isDragging && draggedEventId == event.id
                                        
                                        // 事件块颜色 - 使用事件颜色或默认颜色，确保80%透明度
                                        val bgColor = event.color?.let { 
                                            // 从Long值中提取颜色通道值，包括透明度
                                            val a = (it shr 24 and 0xFFL).toFloat() / 255f
                                            val r = (it shr 16 and 0xFFL).toFloat() / 255f
                                            val g = (it shr 8 and 0xFFL).toFloat() / 255f
                                            val b = (it and 0xFFL).toFloat() / 255f
                                            
                                            Color(r, g, b, a)
                                        } ?: getEventColorByUrgency(event.urgency)
                                        
                                        // 事件块 - 使用计算的水平位置和宽度
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth(layout.widthFraction) // 使用计算的宽度比例
                                                .height(height)
                                                .offset(
                                                    x = layout.xOffsetFraction.times(LocalDensity.current.density).dp, // 横向偏移
                                                    y = topOffset
                                                )
                                                .padding(start = 4.dp, end = 4.dp, bottom = 2.dp)
                                                .clip(RoundedCornerShape(8.dp))
                                                .background(bgColor)
                                                .clickable { 
                                                    // 打开编辑对话框而不是导航
                                                    isEditMode = true
                                                    editingEventId = event.id
                                                    
                                                    // 预填充事件信息
                                                    newEventStartTime = event.start.toLocalTime()
                                                    newEventTitle = event.title
                                                    
                                                    // 计算事件持续时间（分钟）
                                                    val durationMinutes = if (event.end != null) {
                                                        ChronoUnit.MINUTES.between(event.start, event.end).toInt()
                                                    } else {
                                                        60 // 默认1小时
                                                    }
                                                    newEventDuration = durationMinutes
                                                    
                                                    // 设置颜色
                                                    event.color?.let { colorLong ->
                                                        val a = (colorLong shr 24 and 0xFFL).toFloat() / 255f
                                                        val r = (colorLong shr 16 and 0xFFL).toFloat() / 255f
                                                        val g = (colorLong shr 8 and 0xFFL).toFloat() / 255f
                                                        val b = (colorLong and 0xFFL).toFloat() / 255f
                                                        
                                                        selectedColor = Color(r, g, b, a)
                                                        currentRed = r * 255f
                                                        currentGreen = g * 255f
                                                        currentBlue = b * 255f
                                                        currentAlpha = a
                                                    }
                                                    
                                                    // 显示对话框
                                                    showCreateEventDialog = true
                                                }
                                                .pointerInput(event.id) {
                                                    detectDragGestures(
                                                        onDragStart = { offset ->
                                                            isDragging = true
                                                            draggedEventId = event.id
                                                            dragStartY = offset.y
                                                            dragCurrentY = offset.y
                                                        },
                                                        onDrag = { change, dragAmount ->
                                                            change.consumePositionChange()
                                                            dragCurrentY += dragAmount.y
                                                        },
                                                        onDragEnd = {
                                                            // 计算拖拽的时间偏移
                                                            val dragDelta = dragCurrentY - dragStartY
                                                            val minutesDelta = ((dragDelta / hourHeightPx) * 60).toInt()
                                                            
                                                            // 如果拖拽超过5分钟，更新事件时间
                                                            if (abs(minutesDelta) >= 5) {
                                                                val newStartTime = event.start.plusMinutes(minutesDelta.toLong())
                                                                val newEndTime = event.end?.plusMinutes(minutesDelta.toLong())
                                                                viewModel.updateTaskTime(event.id, newStartTime, newEndTime ?: newStartTime.plusHours(1))
                                                            }
                                                            
                                                            isDragging = false
                                                            draggedEventId = null
                                                        },
                                                        onDragCancel = {
                                                            isDragging = false
                                                            draggedEventId = null
                                                        }
                                                    )
                                                }
                                                .then(
                                                    if (isDraggingThis) {
                                                        Modifier.shadow(8.dp, RoundedCornerShape(8.dp))
                                                    } else {
                                                        Modifier
                                                    }
                                                ),
                                            contentAlignment = Alignment.CenterStart
                                        ) {
                                            Column(
                modifier = Modifier
                    .fillMaxSize()
                                                    .padding(horizontal = 6.dp, vertical = 4.dp)
                                            ) {
                                                Text(
                                                    text = event.title,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    fontWeight = FontWeight.Medium,
                                                    fontSize = 12.sp,
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                                
                                                Spacer(modifier = Modifier.height(2.dp))
                                                
                                                Text(
                                                    text = "$startTimeStr - $endTimeStr",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    fontSize = 10.sp,
                                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 小时分隔线
                if (hour < 23) {
                    Divider(
                        color = Color.LightGray.copy(alpha = 0.3f),
                        modifier = Modifier.padding(start = 50.dp)
                    )
                }
            }
        }
        
        // 当前时间指示线
        val currentTime = LocalTime.now()
        if (selectedDate.isEqual(LocalDate.now())) {
            val minutesFromMidnight = currentTime.hour * 60 + currentTime.minute
            val offsetY = with(density) { (minutesFromMidnight / 60f) * hourHeightDp.toPx() }
            
            Canvas(modifier = Modifier.fillMaxWidth()) {
                drawLine(
                    color = Color.Red,
                    start = Offset(50f, offsetY),
                    end = Offset(size.width, offsetY),
                    strokeWidth = 2f
                )
                
                // 时间指示点
                drawCircle(
                    color = Color.Red,
                    radius = 6f,
                    center = Offset(50f, offsetY)
                )
            }
        }
    }
    
    // 颜色选择器对话框
    if (showColorPicker) {
        AlertDialog(
            onDismissRequest = { showColorPicker = false },
            title = { Text("选择颜色") },
            text = {
                Column(
                        modifier = Modifier
                            .fillMaxWidth()
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 颜色预览
                    val previewColor = Color(
                        red = currentRed / 255f,
                        green = currentGreen / 255f,
                        blue = currentBlue / 255f,
                        alpha = currentAlpha  // 使用自定义透明度
                    )
                    
                    // 显示颜色预览和十六进制值
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(48.dp)
                                .background(previewColor, RoundedCornerShape(8.dp))
                                .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp))
                        )
                        
                        // 显示颜色的十六进制值和透明度百分比
                        val hexValue = String.format(
                            "#%02X%02X%02X (%d%%)",
                            currentRed.toInt(),
                            currentGreen.toInt(),
                            currentBlue.toInt(),
                            (currentAlpha * 100).toInt()
                        )
                        Text(text = hexValue, style = MaterialTheme.typography.bodyMedium)
                    }
                    
                    // R值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("R: ${currentRed.toInt()}")
                        }
                        Slider(
                            value = currentRed,
                            onValueChange = { currentRed = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Red,
                                activeTrackColor = Color.Red.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // G值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("G: ${currentGreen.toInt()}")
                        }
                        Slider(
                            value = currentGreen,
                            onValueChange = { currentGreen = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Green,
                                activeTrackColor = Color.Green.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // B值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("B: ${currentBlue.toInt()}")
                        }
                        Slider(
                            value = currentBlue,
                            onValueChange = { currentBlue = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Blue,
                                activeTrackColor = Color.Blue.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // 透明度滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("透明度: ${(currentAlpha * 100).toInt()}%")
                        }
                        Slider(
                            value = currentAlpha,
                            onValueChange = { currentAlpha = it },
                            valueRange = 0.1f..1.0f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.DarkGray,
                                activeTrackColor = Color.DarkGray.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // 透明度说明
                    Row(
                        modifier = Modifier
            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "透明度信息",
                            tint = Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            "透明度可调整，数值越小越透明",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                    
                    // 颜色推荐
                    Text("推荐颜色", modifier = Modifier.padding(top = 8.dp))
                    
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(getRecommendedColors()) { color ->
        Box(
            modifier = Modifier
                                    .size(36.dp)
                                    .background(color, RoundedCornerShape(8.dp))
                                    .border(
                                        width = if (isColorSimilar(color, previewColor)) 2.dp else 1.dp,
                                        color = if (isColorSimilar(color, previewColor)) Color.Black else Color.LightGray,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .clickable {
                                        // 设置当前RGB值为推荐颜色的值，包括透明度
                                        currentRed = color.red * 255f
                                        currentGreen = color.green * 255f
                                        currentBlue = color.blue * 255f
                                        currentAlpha = color.alpha
                                    }
                            )
                        }
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 使用当前RGB值和用户自定义透明度创建新的Color对象
                        val newColor = Color(
                            red = currentRed / 255f,
                            green = currentGreen / 255f,
                            blue = currentBlue / 255f,
                            alpha = currentAlpha  // 使用自定义透明度
                        )
                        
                        selectedColor = newColor
                        showColorPicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showColorPicker = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 创建事件对话框
    if (showCreateEventDialog && newEventStartTime != null) {
        AlertDialog(
            onDismissRequest = { 
                showCreateEventDialog = false
                isEditMode = false
                editingEventId = null
            },
            title = { Text(if (isEditMode) "编辑事件" else "创建新事件") },
            text = {
        Column(
                    modifier = Modifier.padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text("时间: ${newEventStartTime?.format(DateTimeFormatter.ofPattern("HH:mm"))}")
                    
                    OutlinedTextField(
                        value = newEventTitle,
                        onValueChange = { newEventTitle = it },
                        label = { Text("事件标题") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("持续时间: ")
                        Slider(
                            value = newEventDuration.toFloat(),
                            onValueChange = { newEventDuration = it.toInt() },
                            valueRange = 15f..180f,
                            steps = 11,
                    modifier = Modifier.weight(1f)
                )
                        Text("${newEventDuration}分钟")
                    }
                    
                    // 颜色选择
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("事件颜色: ")
                        
                        // 显示当前选中的颜色
                        Box(
                            modifier = Modifier
                                .size(36.dp)
                                .background(
                                    selectedColor ?: Color(0xCCE57373), // 默认红色，80%透明度
                                    RoundedCornerShape(8.dp)
                                )
                                .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp))
                                .clickable { showColorPicker = true }
                        )
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        if (newEventTitle.isNotBlank() && newEventStartTime != null) {
                            val startDateTime = LocalDateTime.of(selectedDate, newEventStartTime!!)
                            val endDateTime = startDateTime.plusMinutes(newEventDuration.toLong())
                            
                            // 将选中的颜色转换为Long类型存储（使用用户自定义的透明度）
                            val colorValue = selectedColor?.let { color ->
                                // 转换为Long格式
                                val r = (color.red * 255).toLong()
                                val g = (color.green * 255).toLong()
                                val b = (color.blue * 255).toLong()
                                val a = (color.alpha * 255).toLong()
                                
                                (a shl 24) or (r shl 16) or (g shl 8) or b
                            }
                            
                            if (isEditMode && editingEventId != null) {
                                // 更新现有事件
                                Log.d("EnhancedDayTimelineView", "更新事件: $newEventTitle, ID: $editingEventId")
                                
                                // 更新事件时间
                                viewModel.updateTaskTime(editingEventId!!, startDateTime, endDateTime)
                                
                                // 更新事件颜色
                                colorValue?.let {
                                    viewModel.updateTaskColor(editingEventId!!, it)
                                    Log.d("EnhancedDayTimelineView", "更新事件颜色: ID=${editingEventId}, color=${it.toString(16)}")
                                }
                                
                                // 注意：此处不再使用删除后重新创建的方式，而是使用专门的更新方法
                            } else {
                                // 创建新事件
                                Log.d("EnhancedDayTimelineView", "创建新事件: $newEventTitle, 时间: $startDateTime")
                                
                                viewModel.createEvent(
                                    title = newEventTitle,
                                    start = startDateTime,
                                    end = endDateTime,
                                    color = colorValue
                                )
                            }
                            
                            // 重置并关闭对话框
                            newEventTitle = ""
                            selectedColor = null
                            showCreateEventDialog = false
                            isEditMode = false
                            editingEventId = null
                        }
                    }
                ) {
                    Text(if (isEditMode) "保存" else "创建")
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    showCreateEventDialog = false
                    newEventTitle = ""
                    selectedColor = null
                    isEditMode = false
                    editingEventId = null
                }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 判断两个颜色是否相似（忽略alpha值）
 */
private fun isColorSimilar(color1: Color, color2: Color): Boolean {
    val threshold = 0.05f  // 颜色相似度阈值
    return abs(color1.red - color2.red) < threshold &&
           abs(color1.green - color2.green) < threshold &&
           abs(color1.blue - color2.blue) < threshold
}

/**
 * 根据事件紧急程度获取颜色（带80%透明度）
 */
private fun getEventColorByUrgency(urgency: TaskUrgency): Color {
    val baseColor = when (urgency) {
        TaskUrgency.CRITICAL -> Color(0xFFE57373) // 红色
        TaskUrgency.HIGH -> Color(0xFFFFB74D)   // 橙色
        TaskUrgency.MEDIUM -> Color(0xFF4FC3F7) // 蓝色
        TaskUrgency.LOW -> Color(0xFF81C784)    // 绿色
        else -> Color(0xFF9E9E9E) // 默认灰色
    }
    
    // 应用80%透明度
    return baseColor.copy(alpha = 0.8f)
}

/**
 * 根据事件紧急程度获取推荐的事件颜色列表，供用户选择
 */
private fun getRecommendedColors(): List<Color> {
    return listOf(
        // 不同颜色，透明度固定为80%
        Color(0xFFE57373).copy(alpha = 0.8f), // 红色带80%透明度  
        Color(0xFFFFB74D).copy(alpha = 0.8f), // 橙色带80%透明度
        Color(0xFF4FC3F7).copy(alpha = 0.8f), // 蓝色带80%透明度
        Color(0xFF81C784).copy(alpha = 0.8f), // 绿色带80%透明度
        
        // 相同颜色，不同透明度
        Color(0xFFAB47BC).copy(alpha = 0.9f), // 紫色带90%透明度
        Color(0xFFAB47BC).copy(alpha = 0.7f), // 紫色带70%透明度
        Color(0xFFAB47BC).copy(alpha = 0.5f), // 紫色带50%透明度
        Color(0xFFAB47BC).copy(alpha = 0.3f)  // 紫色带30%透明度
    )
}

/**
 * 事件布局信息，包含水平位置偏移和宽度比例
 */
data class EventLayoutInfo(
    val widthFraction: Float,  // 事件宽度占比
    val xOffsetFraction: Float // 横向偏移占比
)

/**
 * 计算事件的布局信息，解决重叠问题
 * 实现并排显示同一时间段内的事件
 */
private fun calculateEventsLayout(events: List<CalendarEvent>): Map<CalendarEvent, EventLayoutInfo> {
    // 如果没有事件或只有一个事件，直接返回简单布局
    if (events.isEmpty()) {
        return emptyMap()
    }
    
    if (events.size == 1) {
        return mapOf(events.first() to EventLayoutInfo(0.95f, 0f))
    }
    
    // 创建结果Map
    val result = mutableMapOf<CalendarEvent, EventLayoutInfo>()
    
    // 将事件按照开始时间分组
    val eventsByStartTime = events.groupBy { 
        LocalTime.of(it.start.hour, it.start.minute / 15 * 15) // 按15分钟为间隔分组
    }
    
    // 创建时间段冲突图
    val overlapGroups = mutableListOf<MutableSet<CalendarEvent>>()
    
    // 寻找重叠的事件组
    for (event in events) {
        val eventStart = event.start
        val eventEnd = event.end ?: event.start.plusHours(1)
        
        // 查找此事件是否与已有分组重叠
        var foundGroup = false
        for (group in overlapGroups) {
            // 检查是否与组内任何事件重叠
            val overlapsWithGroup = group.any { existingEvent ->
                val existingStart = existingEvent.start
                val existingEnd = existingEvent.end ?: existingStart.plusHours(1)
                
                // 检测时间重叠
                (eventStart.isBefore(existingEnd) && eventEnd.isAfter(existingStart))
            }
            
            if (overlapsWithGroup) {
                group.add(event)
                foundGroup = true
                break
            }
        }
        
        // 如果没找到重叠的组，创建新组
        if (!foundGroup) {
            overlapGroups.add(mutableSetOf(event))
        }
    }
    
    // 合并可能有交叉的组
    var i = 0
    while (i < overlapGroups.size - 1) {
        var j = i + 1
        var merged = false
        
        while (j < overlapGroups.size) {
            // 检查两个组是否有共同事件
            if (overlapGroups[i].intersect(overlapGroups[j]).isNotEmpty()) {
                // 合并组
                overlapGroups[i].addAll(overlapGroups[j])
                overlapGroups.removeAt(j)
                merged = true
            } else {
                j++
            }
        }
        
        if (!merged) {
            i++
        }
    }
    
    // 对每个重叠组计算布局
    for (group in overlapGroups) {
        val eventsInGroup = group.toList()
        val columnCount = eventsInGroup.size // 该组中的事件数量决定每个事件的宽度
        
        // 按开始时间排序
        val sortedEvents = eventsInGroup.sortedBy { it.start }
        
        // 计算每个事件的宽度和位置
        val width = 0.95f / columnCount // 95%的宽度平均分配
        
        sortedEvents.forEachIndexed { index, event ->
            val xOffset = index * width // 水平偏移
            result[event] = EventLayoutInfo(width, xOffset)
        }
    }
    
    return result
}

// 简洁周视图 (类似图1)
@Composable
fun WeekCalendarView(
    weekRange: Pair<LocalDate, LocalDate>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    viewModel: CalendarViewModel = hiltViewModel(),
    habitViewModel: HabitViewModel = hiltViewModel(),
    backgroundColor: Color = CalendarColors.background
) {
    // 创建当前周的日期列表
    val datesList = mutableListOf<LocalDate>()
    var currentDate = weekRange.first
    while (!currentDate.isAfter(weekRange.second)) {
        datesList.add(currentDate)
        currentDate = currentDate.plusDays(1)
    }
    
    // 从ViewModel获取当前周的事件
    val weekEvents = viewModel.getEventsForWeek(weekRange.first, weekRange.second)
    
    // 获取习惯数据
    val habits = habitViewModel.habits.collectAsState().value
    
    // 计算每个日期的习惯完成情况
    val dateHabitsMap = remember(habits, datesList) {
        habits.flatMap { habit ->
            habit.completedDates.filter { date -> 
                date >= weekRange.first && date <= weekRange.second 
            }.map { date -> date to habit }
        }.groupBy({ it.first }, { it.second })
    }
    
    // 获取选中日期的事件
    val selectedDateEvents = viewModel.getEventsForDate(selectedDate)
    
    // 获取选中日期的习惯
    val selectedDateHabits = dateHabitsMap[selectedDate] ?: emptyList()
    
    // 显示一周日期
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor) // 使用传入的背景色
            .padding(horizontal = 8.dp)
    ) {
        // 显示日期数字行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            datesList.forEach { date ->
                val isToday = date == LocalDate.now()
                val isSelected = date == selectedDate
                val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
                
                // 当天的事件和习惯
                val eventsForDay = weekEvents.filter { it.start.toLocalDate() == date }
                val habitsForDay = dateHabitsMap[date] ?: emptyList()
                val hasEvents = eventsForDay.isNotEmpty()
                val hasHabits = habitsForDay.isNotEmpty()
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 4.dp)
                ) {
                    // 日期数字
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(
                                when {
                                    isSelected -> Color(0xE6E8E4EC)
                                    isToday -> CalendarColors.primary.copy(alpha = 0.1f)
                                    else -> Color.Transparent
                                }
                            )
                            .clickable(
                                interactionSource = rememberNoRippleInteractionSource(),
                                indication = null
                            ) {
                                onDateSelected(date)
                            }
                            .padding(4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            fontSize = 16.sp,
                            fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                            color = when {
                                isSelected -> CalendarColors.primary
                                isToday -> CalendarColors.primary
                                isWeekend -> Color(0xFFE57373) // 周末使用红色
                                else -> CalendarColors.onPrimary
                            }
                        )
                    }
                    
                    // 事件和习惯指示器
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier.padding(top = 4.dp)
                    ) {
                        // 习惯指示点
                        if (hasHabits) {
                            Box(
                                modifier = Modifier
                                    .size(4.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF81C784).copy(alpha = 0.7f))
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                        }
                        
                        // 事件指示点
                        if (hasEvents) {
                            Box(
                                modifier = Modifier
                                    .size(4.dp)
                                    .clip(CircleShape)
                                    .background(CalendarColors.primary.copy(alpha = 0.7f))
                            )
                        }
                    }
                }
            }
        }
        
        // 星期几标题行 (放在数字下方)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            datesList.forEach { date ->
                val dayText = when (date.dayOfWeek) {
                    DayOfWeek.MONDAY -> "周一"
                    DayOfWeek.TUESDAY -> "周二"
                    DayOfWeek.WEDNESDAY -> "周三"
                    DayOfWeek.THURSDAY -> "周四"
                    DayOfWeek.FRIDAY -> "周五"
                    DayOfWeek.SATURDAY -> "周六"
                    DayOfWeek.SUNDAY -> "周日"
                }
                val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
                
                Text(
                    text = dayText,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center,
                    color = if (isWeekend) Color(0xFFE57373).copy(alpha = 0.8f) else CalendarColors.onSurface.copy(alpha = 0.6f),
                    fontSize = 14.sp
                )
            }
        }
        
        // 任务详情显示区域 - 显示选中日期的任务
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 EEEE", Locale.CHINA)),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = CalendarColors.primary,
            modifier = Modifier.padding(start = 16.dp, bottom = 16.dp)
        )

        // 显示习惯区域
        if (selectedDateHabits.isNotEmpty()) {
            // 获取上下文
            val context = LocalContext.current
            
            Text(
                text = "今日习惯",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = CalendarColors.primary,
                modifier = Modifier.padding(start = 16.dp, bottom = 8.dp, top = 8.dp)
            )
            
            LazyRow(
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                selectedDateHabits.forEach { habit ->
                    item {
                        HabitChip(
                            habit = habit,
                            date = selectedDate,  // 使用选中的日期而不是今天
                            onClick = { clickedHabit ->
                                // 判断选中日期是否已经完成，如果已完成则取消，如果未完成则标记为完成
                                val isCompleted = clickedHabit.completedDates.contains(selectedDate)
                                
                                if (isCompleted) {
                                    // 取消打卡
                                    habitViewModel.removeCompletedDate(clickedHabit.id, selectedDate)
                                    // 提供反馈
                                    Toast.makeText(context, "已取消「${clickedHabit.name}」打卡", Toast.LENGTH_SHORT).show()
                                } else {
                                    // 添加打卡
                                    habitViewModel.addCompletedDate(clickedHabit.id, selectedDate)
                                    // 提供反馈
                                    Toast.makeText(context, "「${clickedHabit.name}」打卡成功", Toast.LENGTH_SHORT).show()
                                }
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // 显示当日任务标题
        Text(
            text = "今日任务",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = CalendarColors.primary,
            modifier = Modifier.padding(start = 16.dp, bottom = 8.dp)
        )

        // 显示当日任务列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 80.dp) // 留出FAB的空间
        ) {
            if (selectedDateEvents.isEmpty()) {
                // 没有任务时显示提示
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "今日无任务",
                            fontSize = 16.sp,
                            color = CalendarColors.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            } else {
                // 显示任务列表
                items(selectedDateEvents.size) { index ->
                    val event = selectedDateEvents[index]
                    
                    // 计算事件持续时间文本
                    val durationText = if (event.end != null) {
                        val hours = event.start.until(event.end, java.time.temporal.ChronoUnit.HOURS)
                        val minutes = event.start.until(event.end, java.time.temporal.ChronoUnit.MINUTES) % 60
                        
                        if (hours > 0) {
                            if (minutes > 0) "${hours}小时${minutes}分钟" else "${hours}小时"
                        } else {
                            "${minutes}分钟"
                        }
                    } else {
                        "1小时" // 默认1小时
                    }
                    
                    // 事件时间范围文本
                    val timeText = "${event.start.hour}:${event.start.minute.toString().padStart(2, '0')} - " +
                            (event.end?.let { "${it.hour}:${it.minute.toString().padStart(2, '0')}" } ?: "")
                    
                    WeekViewTaskItem(
                        title = event.title,
                        time = timeText,
                        duration = durationText,
                        color = event.color?.let { Color(it) } ?: TaskColors[event.urgency.ordinal % TaskColors.size]
                    )
                    
                    if (index < selectedDateEvents.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
    }
}

/**
 * 习惯芯片组件，添加完成状态显示
 */
@Composable
fun HabitChip(
    habit: HabitData,
    onClick: (HabitData) -> Unit = {},
    date: LocalDate = LocalDate.now() // 默认为今天
) {
    // 判断是否已完成
    val isCompleted = habit.completedDates.contains(date)
    
    Surface(
        modifier = Modifier
            .height(32.dp)
            .clickable { onClick(habit) },
        color = if (isCompleted) habit.color.copy(alpha = 0.4f) else habit.color.copy(alpha = 0.2f),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 显示自定义emoji或默认图标
            if (habit.customEmoji.isNotEmpty()) {
                Text(
                    text = habit.customEmoji,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(end = 4.dp)
                )
            } else {
                Icon(
                    imageVector = habit.icon,
                    contentDescription = null,
                    tint = habit.color,
                    modifier = Modifier
                        .size(16.dp)
                        .padding(end = 4.dp)
                )
            }
            
            Text(
                text = habit.name,
                fontSize = 12.sp,
                color = Color.DarkGray,
                modifier = Modifier.weight(1f)
            )
            
            // 显示完成状态
            if (isCompleted) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "已完成",
                    tint = habit.color,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 周视图中的任务项
 */
@Composable
fun WeekViewTaskItem(
    title: String,
    time: String,
    duration: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    // 从基础颜色派生不同透明度的颜色
    val backgroundColor = color.copy(alpha = 0.1f) // 降低背景透明度使其更柔和
    val indicatorColor = color.copy(alpha = 0.8f)  // 左侧指示条使用较高透明度
    val textColor = CalendarColors.primary         // 文本使用主色调
    val secondaryTextColor = CalendarColors.onSurface.copy(alpha = 0.7f) // 次要文本使用较低透明度

    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .padding(start = 2.dp) // 减小左侧padding以便放置指示条
    ) {
        // 左侧指示条
        Box(
            modifier = Modifier
                .width(4.dp)
                .fillMaxHeight()
                .clip(RoundedCornerShape(2.dp))
                .background(indicatorColor)
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 12.dp, end = 12.dp, top = 8.dp, bottom = 8.dp)
        ) {
            // 标题和时长
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                    color = textColor,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = duration,
                    fontSize = 12.sp,
                    color = textColor.copy(alpha = 0.8f),
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 时间
            Text(
                text = time,
                fontSize = 12.sp,
                color = secondaryTextColor,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Composable
private fun CalendarEventList(
    events: List<CalendarEvent>,
    onEventClick: (CalendarEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    // 使用key保证列表状态稳定性，减少重组
    val listState = rememberLazyListState()
    
    LazyColumn(
        state = listState,
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = events,
            key = { it.id } // 使用稳定的键以提高性能
        ) { event ->
            CalendarEventItem(
                event = event,
                onClick = { onEventClick(event) },
                modifier = Modifier.animateItemPlacement() // 平滑动画
            )
        }
        
        // 如果没有事件，显示空状态
        if (events.isEmpty()) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "没有事件",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarEventItem(
    event: CalendarEvent,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 计算背景颜色和文字颜色 - 避免在remember中使用Composable函数
    val color = event.color
    val backgroundColor = if (color != null) {
        Color(color)
    } else {
        MaterialTheme.colorScheme.surface
    }
    
    // 使用Surface避免多层嵌套提高性能
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        color = backgroundColor.copy(alpha = 0.12f),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 优化文本布局避免重组
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = event.title,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                // 避免在remember中使用Composable函数
                val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
                val startTime = event.start.format(timeFormatter)
                val endTime = event.end?.format(timeFormatter) ?: "?"
                val formattedTime = "$startTime - $endTime"
                
                Text(
                    text = formattedTime,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            // 完成状态指示器
            if (event.isCompleted) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = Color.Green.copy(alpha = 0.8f),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun CalendarTopBar(
    currentYearMonth: YearMonth,
    onPreviousClick: () -> Unit,
    onNextClick: () -> Unit,
    onMonthClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(CalendarColors.surface)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onPreviousClick) {
                Icon(
                    imageVector = Icons.Default.ChevronLeft,
                    contentDescription = "上个月",
                    tint = CalendarColors.onSurface
                )
            }
            
            Row(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onMonthClick() },
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = currentYearMonth.format(DateTimeFormatter.ofPattern("yyyy年MM月")),
                    style = MaterialTheme.typography.titleLarge,
                    color = CalendarColors.onSurface,
                    fontWeight = FontWeight.Bold
                )
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = "选择月份",
                    tint = CalendarColors.onSurface
                )
            }
            
            IconButton(onClick = onNextClick) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "下个月",
                    tint = CalendarColors.onSurface
                )
            }
        }
    }
}