package com.timeflow.app.di

import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import com.timeflow.app.ui.screen.reflection.SearchSuggestionService
import com.timeflow.app.ui.screen.reflection.data.ReflectionRepositoryImpl
import com.timeflow.app.ui.screen.reflection.data.SearchSuggestionServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 反思模块的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class ReflectionModule {
    
    @Binds
    @Singleton
    abstract fun bindReflectionRepository(
        repositoryImpl: ReflectionRepositoryImpl
    ): ReflectionRepository
    
    @Binds
    @Singleton
    abstract fun bindSearchSuggestionService(
        serviceImpl: SearchSuggestionServiceImpl
    ): SearchSuggestionService
} 