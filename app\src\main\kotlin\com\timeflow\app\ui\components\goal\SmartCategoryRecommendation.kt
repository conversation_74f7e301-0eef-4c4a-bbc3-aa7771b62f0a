package com.timeflow.app.ui.components.goal

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.ai.GoalCategoryClassifier
import com.timeflow.app.data.model.GoalCategory

/**
 * 智能分类推荐组件
 * 基于文本内容自动推荐合适的分类
 */
@Composable
fun SmartCategoryRecommendation(
    title: String,
    description: String,
    selectedCategoryId: String,
    onCategorySelected: (String) -> Unit,
    modifier: Modifier = Modifier,
    classifier: GoalCategoryClassifier = remember { GoalCategoryClassifier() }
) {
    var recommendations by remember { mutableStateOf<List<GoalCategoryClassifier.CategoryRecommendation>>(emptyList()) }
    var isAnalyzing by remember { mutableStateOf(false) }
    var showRecommendations by remember { mutableStateOf(false) }
    
    // 当文本内容变化时重新分析
    LaunchedEffect(title, description) {
        if (title.isNotBlank() || description.isNotBlank()) {
            isAnalyzing = true
            try {
                val newRecommendations = classifier.classifyGoal(title, description)
                recommendations = newRecommendations
                showRecommendations = newRecommendations.isNotEmpty()
            } finally {
                isAnalyzing = false
            }
        } else {
            recommendations = emptyList()
            showRecommendations = false
        }
    }
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 智能推荐标题
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Psychology,
                contentDescription = null,
                tint = Color(0xFF8B5CF6),
                modifier = Modifier.size(20.dp)
            )
            
            Text(
                text = "智能分类推荐",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF374151)
            )
            
            if (isAnalyzing) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = Color(0xFF8B5CF6)
                )
            }
        }
        
        // 推荐结果
        AnimatedVisibility(
            visible = showRecommendations && !isAnalyzing,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                recommendations.take(3).forEach { recommendation ->
                    RecommendationCard(
                        recommendation = recommendation,
                        isSelected = selectedCategoryId == recommendation.category.id,
                        onSelect = { onCategorySelected(recommendation.category.id) }
                    )
                }
                
                // 提示信息
                if (recommendations.isNotEmpty()) {
                    Text(
                        text = "💡 AI根据您的文本内容智能推荐，点击选择或手动调整",
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
            }
        }
        
        // 无推荐时的提示
        if (!isAnalyzing && !showRecommendations && (title.isNotBlank() || description.isNotBlank())) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFFF9FAFB)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = Color(0xFF6B7280),
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Text(
                        text = "暂无智能推荐，请手动选择分类",
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280)
                    )
                }
            }
        }
    }
}

/**
 * 推荐卡片组件
 */
@Composable
private fun RecommendationCard(
    recommendation: GoalCategoryClassifier.CategoryRecommendation,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    val category = recommendation.category
    val confidence = recommendation.confidence
    val confidenceText = when {
        confidence >= 0.8 -> "高度匹配"
        confidence >= 0.6 -> "较为匹配"
        confidence >= 0.4 -> "可能匹配"
        else -> "低匹配度"
    }
    
    val confidenceColor = when {
        confidence >= 0.8 -> Color(0xFF10B981)
        confidence >= 0.6 -> Color(0xFF3B82F6)
        confidence >= 0.4 -> Color(0xFFF59E0B)
        else -> Color(0xFF6B7280)
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelect() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) category.color.copy(alpha = 0.1f) else Color.White
        ),
        shape = RoundedCornerShape(8.dp),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(2.dp, category.color)
        } else {
            androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFE5E7EB))
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 分类图标
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .background(
                        color = category.color.copy(alpha = 0.2f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = category.icon,
                    contentDescription = null,
                    tint = category.color,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            // 分类信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = category.name,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF1F2937)
                    )
                    
                    // 置信度标签
                    Surface(
                        color = confidenceColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(4.dp)
                    ) {
                        Text(
                            text = confidenceText,
                            fontSize = 10.sp,
                            color = confidenceColor,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                
                Text(
                    text = recommendation.reason,
                    fontSize = 12.sp,
                    color = Color(0xFF6B7280),
                    maxLines = 2
                )
                
                // 匹配关键词
                if (recommendation.matchedKeywords.isNotEmpty()) {
                    Text(
                        text = "关键词：${recommendation.matchedKeywords.take(3).joinToString("、")}",
                        fontSize = 11.sp,
                        color = Color(0xFF9CA3AF),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            }
            
            // 置信度进度条
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                CircularProgressIndicator(
                    progress = confidence.toFloat(),
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 3.dp,
                    color = confidenceColor,
                    trackColor = Color(0xFFE5E7EB)
                )
                
                Text(
                    text = "${(confidence * 100).toInt()}%",
                    fontSize = 10.sp,
                    color = confidenceColor,
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 选中标识
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已选中",
                    tint = category.color,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 快速智能分类按钮
 */
@Composable
fun QuickSmartCategoryButton(
    title: String,
    description: String,
    onCategorySelected: (String) -> Unit,
    modifier: Modifier = Modifier,
    classifier: GoalCategoryClassifier = remember { GoalCategoryClassifier() }
) {
    var isAnalyzing by remember { mutableStateOf(false) }
    
    OutlinedButton(
        onClick = {
            if (title.isNotBlank() || description.isNotBlank()) {
                isAnalyzing = true
                try {
                    val category = classifier.quickClassify(title, description)
                    onCategorySelected(category.id)
                } finally {
                    isAnalyzing = false
                }
            }
        },
        modifier = modifier,
        enabled = !isAnalyzing && (title.isNotBlank() || description.isNotBlank()),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = Color(0xFF8B5CF6)
        ),
        border = androidx.compose.foundation.BorderStroke(1.dp, Color(0xFF8B5CF6))
    ) {
        if (isAnalyzing) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = Color(0xFF8B5CF6)
            )
        } else {
            Icon(
                imageVector = Icons.Default.AutoAwesome,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = if (isAnalyzing) "分析中..." else "智能分类",
            fontSize = 12.sp
        )
    }
}
