package com.timeflow.app.data.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 向导模板数据类
 * 参照Project50和Calflow的模板设计
 */
data class WizardTemplate(
    val id: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val color: Color,
    val category: GoalCategory,
    val estimatedDays: Int,
    val difficulty: Difficulty,
    val suggestions: List<String>,
    val milestones: List<String> = emptyList(),
    val habits: List<String> = emptyList()
)



/**
 * 时间框架
 */
data class TimeFrame(
    val id: String,
    val name: String,
    val description: String,
    val days: Int,
    val icon: ImageVector,
    val color: Color
)

/**
 * 难度等级
 */
enum class Difficulty(
    val displayName: String,
    val description: String,
    val color: Color,
    val icon: ImageVector
) {
    EASY(
        displayName = "简单",
        description = "轻松达成，适合新手",
        color = Color(0xFF10B981),
        icon = Icons.Default.SentimentSatisfied
    ),
    MEDIUM(
        displayName = "中等",
        description = "需要一定努力和坚持",
        color = Color(0xFFF59E0B),
        icon = Icons.Default.SentimentNeutral
    ),
    HARD(
        displayName = "困难",
        description = "具有挑战性，需要全力以赴",
        color = Color(0xFFEF4444),
        icon = Icons.Default.SentimentDissatisfied
    )
}

/**
 * 获取预设的目标模板
 */
fun getGoalTemplates(): List<WizardTemplate> {
    return listOf(
        WizardTemplate(
            id = "fitness_weight_loss",
            title = "减重目标",
            description = "健康减重，塑造理想身材",
            icon = Icons.Default.FitnessCenter,
            color = Color(0xFFEF4444),
            category = getDefaultGoalCategories().first { it.id == "health_fitness" },
            estimatedDays = 90,
            difficulty = Difficulty.MEDIUM,
            suggestions = listOf(
                "设定合理的减重目标（每周0.5-1kg）",
                "制定运动计划，每周至少3次",
                "控制饮食，减少高热量食物",
                "记录体重变化和饮食日记"
            ),
            milestones = listOf("第1周：建立运动习惯", "第4周：减重2-4kg", "第8周：减重4-8kg", "第12周：达成目标"),
            habits = listOf("每日称重", "运动打卡", "饮食记录")
        ),
        
        WizardTemplate(
            id = "learning_language",
            title = "学习新语言",
            description = "掌握一门新的外语技能",
            icon = Icons.Default.Language,
            color = Color(0xFF3B82F6),
            category = getDefaultGoalCategories().first { it.id == "learning_growth" },
            estimatedDays = 180,
            difficulty = Difficulty.HARD,
            suggestions = listOf(
                "每天至少学习30分钟",
                "使用多种学习资源（app、书籍、视频）",
                "找语言交换伙伴练习口语",
                "设定阶段性测试目标"
            ),
            milestones = listOf("第1月：掌握基础词汇", "第3月：简单对话", "第6月：流利交流"),
            habits = listOf("每日单词学习", "听力练习", "口语练习")
        ),
        
        WizardTemplate(
            id = "reading_books",
            title = "阅读计划",
            description = "培养阅读习惯，增长知识",
            icon = Icons.Default.MenuBook,
            color = Color(0xFF8B5CF6),
            category = getDefaultGoalCategories().first { it.id == "learning_growth" },
            estimatedDays = 365,
            difficulty = Difficulty.EASY,
            suggestions = listOf(
                "设定年度阅读目标（如12本书）",
                "选择感兴趣的书籍类型",
                "每天固定阅读时间",
                "记录读书笔记和感悟"
            ),
            milestones = listOf("第1月：1本书", "第3月：3本书", "第6月：6本书", "第12月：12本书"),
            habits = listOf("每日阅读", "读书笔记", "书评分享")
        ),
        
        WizardTemplate(
            id = "save_money",
            title = "储蓄计划",
            description = "建立理财习惯，积累财富",
            icon = Icons.Default.Savings,
            color = Color(0xFF10B981),
            category = getDefaultGoalCategories().first { it.id == "finance_money" },
            estimatedDays = 365,
            difficulty = Difficulty.MEDIUM,
            suggestions = listOf(
                "设定明确的储蓄目标金额",
                "制定月度储蓄计划",
                "减少不必要的开支",
                "寻找额外收入来源"
            ),
            milestones = listOf("第1月：建立储蓄习惯", "第3月：达成25%目标", "第6月：达成50%目标", "第12月：完成目标"),
            habits = listOf("记录支出", "定期储蓄", "投资学习")
        ),
        
        WizardTemplate(
            id = "career_skill",
            title = "职业技能提升",
            description = "学习新技能，提升职业竞争力",
            icon = Icons.Default.Work,
            color = Color(0xFFF59E0B),
            category = getDefaultGoalCategories().first { it.id == "career_work" },
            estimatedDays = 120,
            difficulty = Difficulty.MEDIUM,
            suggestions = listOf(
                "选择与职业相关的技能",
                "制定学习计划和时间表",
                "实践项目巩固所学",
                "寻求导师指导"
            ),
            milestones = listOf("第1月：基础学习", "第2月：实践应用", "第3月：项目完成", "第4月：技能认证"),
            habits = listOf("每日学习", "实践练习", "技能分享")
        ),
        
        WizardTemplate(
            id = "social_network",
            title = "扩展人脉",
            description = "建立有价值的社交网络",
            icon = Icons.Default.People,
            color = Color(0xFFEC4899),
            category = getDefaultGoalCategories().first { it.id == "relationships" },
            estimatedDays = 180,
            difficulty = Difficulty.MEDIUM,
            suggestions = listOf(
                "参加行业活动和聚会",
                "主动联系老朋友",
                "加入专业社群",
                "提供价值给他人"
            ),
            milestones = listOf("第1月：参加2个活动", "第3月：建立10个新联系", "第6月：深化5个关系"),
            habits = listOf("社交活动", "联系维护", "价值分享")
        )
    )
}

/**
 * 获取目标分类
 * 参考知名应用的分类设计，包括时间维度和主题维度
 * @deprecated 使用 com.timeflow.app.data.model.getDefaultGoalCategories() 代替
 */
@Deprecated("使用 getDefaultGoalCategories() 代替")
fun getGoalCategories(): List<GoalCategory> {
    return getDefaultGoalCategories()
}

/**
 * 获取时间框架选项
 */
fun getTimeFrames(): List<TimeFrame> {
    return listOf(
        TimeFrame(
            id = "week",
            name = "一周内",
            description = "短期目标，快速见效",
            days = 7,
            icon = Icons.Default.Today,
            color = Color(0xFF10B981)
        ),
        TimeFrame(
            id = "month",
            name = "一个月",
            description = "中短期目标，建立习惯",
            days = 30,
            icon = Icons.Default.CalendarMonth,
            color = Color(0xFF3B82F6)
        ),
        TimeFrame(
            id = "quarter",
            name = "三个月",
            description = "季度目标，显著改变",
            days = 90,
            icon = Icons.Default.DateRange,
            color = Color(0xFFF59E0B)
        ),
        TimeFrame(
            id = "half_year",
            name = "半年",
            description = "中期目标，深度发展",
            days = 180,
            icon = Icons.Default.Event,
            color = Color(0xFF8B5CF6)
        ),
        TimeFrame(
            id = "year",
            name = "一年",
            description = "长期目标，重大成就",
            days = 365,
            icon = Icons.Default.EventNote,
            color = Color(0xFFEF4444)
        )
    )
}

/**
 * 获取难度选项
 */
fun getDifficulties(): List<Difficulty> {
    return Difficulty.values().toList()
}
