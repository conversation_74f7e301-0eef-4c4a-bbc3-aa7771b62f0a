package com.timeflow.app.ui.screen.goal

import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.RecurringSettings
import com.timeflow.app.data.model.ReminderSetting
import com.timeflow.app.data.model.TimeSlotInfo
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.AiTaskRepositoryImpl
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.data.repository.GoalStats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collect
import java.time.LocalDateTime
import javax.inject.Inject
import com.timeflow.app.data.ai.model.AiSettings
import com.timeflow.app.data.repository.AiTaskRepository
import com.timeflow.app.ui.viewmodel.aiSettingsDataStore
import androidx.datastore.preferences.core.stringPreferencesKey
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import org.json.JSONArray
import java.util.concurrent.TimeUnit
import com.timeflow.app.data.ai.model.AiConfig
import android.app.Application
import com.google.gson.reflect.TypeToken
import android.content.Context
import com.timeflow.app.ui.viewmodel.dataStore
import kotlinx.coroutines.flow.firstOrNull
import java.time.temporal.ChronoUnit
import timber.log.Timber
import java.util.UUID
import java.util.regex.Pattern
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.delay

// 添加TAG常量
private const val TAG = "GoalViewModel"

@HiltViewModel
class GoalViewModel @Inject constructor(
    application: Application,
    private val goalRepository: GoalRepository,
    private val aiTaskRepository: AiTaskRepository
) : AndroidViewModel(application) {

    // UI状态 - 更改初始状态为Idle，不会触发自动返回
    private val _uiState = mutableStateOf<GoalUiState>(GoalUiState.Idle)
    val uiState: State<GoalUiState> = _uiState

    // 当前选中的目标状态过滤
    private val _selectedStatus = mutableStateOf("全部")
    val selectedStatus: State<String> = _selectedStatus

    // 当前选中的分类过滤
    private val _selectedCategory = mutableStateOf<String?>(null)
    val selectedCategory: State<String?> = _selectedCategory

    // 目标列表
    private val _goals = mutableStateOf<List<Goal>>(emptyList())
    val goals: State<List<Goal>> = _goals

    // 统计数据
    private val _stats = mutableStateOf(GoalStats(0, 0, 0, 0))
    val stats: State<GoalStats> = _stats

    // 当前目标详情
    private val _currentGoal = mutableStateOf<Goal?>(null)
    val currentGoal: State<Goal?> = _currentGoal

    // 当前目标的子任务
    private val _subTasks = mutableStateOf<List<GoalSubTask>>(emptyList())
    val subTasks: State<List<GoalSubTask>> = _subTasks
    
    // 为主页显示预先计算和缓存的排序过滤目标列表
    // 这样可以避免在UI线程中进行排序和过滤计算
    private val _homePageGoals = mutableStateOf<List<Goal>>(emptyList())
    
    // 对外暴露的已排序过滤的目标Flow
    val homePageGoals: State<List<Goal>> = _homePageGoals
    
    // 转换为StateFlow进行处理
    private val _goalsFlow = MutableStateFlow<List<Goal>>(emptyList())
    
    // 对外暴露的顶部优先目标Flow，限制为最多3个
    val topPriorityGoals = _goalsFlow
        .map { allGoals ->
            allGoals.asSequence()
                .filter { goal -> goal.progress < 1.0f && goal.dueDate != null }
                .sortedWith(
                    compareBy<Goal> { it.dueDate }
                    .thenByDescending { it.priority.value }
                )
                .take(3)
                .toList()
        }
        .stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5000),
            emptyList()
        )

    // 新增：拆解状态
    private val _breakdownState = mutableStateOf<BreakdownState>(BreakdownState.Idle)
    val breakdownState: State<BreakdownState> = _breakdownState

    // 定义DataStore的key
    private val AI_SETTINGS_KEY = stringPreferencesKey("ai_settings")
    private val AI_CONFIGS_KEY = stringPreferencesKey("ai_configs")
    private val SELECTED_CONFIG_ID_KEY = stringPreferencesKey("selected_config_id")
    
    // 🚀 HTTP客户端 - 与AI对话页面保持一致的超时配置
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(8, TimeUnit.SECONDS)   // 连接超时8s，与AI对话一致
        .writeTimeout(15, TimeUnit.SECONDS)    // 写入超时15s，与AI对话一致
        .readTimeout(25, TimeUnit.SECONDS)     // 读取超时25s，与AI对话一致
        .build()
    
    // Gson实例
    private val gson = Gson()

    // 对外暴露所有目标的StateFlow（用于目标选择组件）
    private val _allGoals = MutableStateFlow<List<Goal>>(emptyList())
    val allGoals: StateFlow<List<Goal>> = _allGoals.asStateFlow()

    init {
        // 初始化时加载全部目标
        loadGoals()
        // 加载统计数据
        loadStats()
        
        // 设置目标列表变化的监听，更新主页排序过滤后的目标
        viewModelScope.launch {
            // 监听内部状态更新StateFlow
            _goals.value.let { 
                _goalsFlow.value = it
                updateHomePageGoals(it)
            }
        }
    }
    
    // 更新主页显示的目标列表
    private fun updateHomePageGoals(allGoals: List<Goal>) {
        _homePageGoals.value = allGoals.asSequence()
            .filter { goal -> goal.progress < 1.0f && goal.dueDate != null }
            .sortedWith(
                compareBy<Goal> { it.dueDate }
                .thenByDescending { it.priority.value }
            )
            .take(3)
            .toList()
            
        // 同时更新StateFlow
        _goalsFlow.value = allGoals
    }

    fun loadGoals() {
        viewModelScope.launch {
            _uiState.value = GoalUiState.Loading
            try {
                val categoryId = _selectedCategory.value
                val status = _selectedStatus.value

                when {
                    // 有分类筛选且有状态筛选
                    categoryId != null && status != "全部" -> {
                        when (status) {
                            "进行中" -> goalRepository.getGoalsByCategoryAndStatus(categoryId, "进行中").collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "已完成" -> goalRepository.getGoalsByCategoryAndStatus(categoryId, "已完成").collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "待完成" -> goalRepository.getGoalsByCategoryAndStatus(categoryId, "待完成").collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            else -> goalRepository.getGoalsByCategory(categoryId).collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                        }
                    }
                    // 只有分类筛选
                    categoryId != null -> {
                        goalRepository.getGoalsByCategory(categoryId).collect { goals ->
                            _goals.value = goals
                            _uiState.value = GoalUiState.Success
                        }
                    }
                    // 只有状态筛选
                    else -> {
                        when (status) {
                            "全部" -> goalRepository.getAllGoals().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "进行中" -> goalRepository.getGoalsByStatus("进行中").collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "已完成" -> goalRepository.getCompletedGoals().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "待完成" -> goalRepository.getGoalsByStatus("待完成").collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "需要AI建议" -> goalRepository.getGoalsWithoutAiBreakdown().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "待拆解" -> goalRepository.getGoalsWithoutAiBreakdown().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            "待复盘" -> goalRepository.getGoalsNeedingReview().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                            else -> goalRepository.getAllGoals().collect { goals ->
                                _goals.value = goals
                                _uiState.value = GoalUiState.Success
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "加载目标失败")
            }
        }
    }

    fun loadStats() {
        viewModelScope.launch {
            try {
                _stats.value = goalRepository.getGoalStats()
            } catch (e: Exception) {
                // 统计数据加载失败不影响主界面显示
                _stats.value = GoalStats(0, 0, 0, 0)
            }
        }
    }

    fun updateStatusFilter(status: String) {
        _selectedStatus.value = status
        loadGoals()
    }

    fun updateCategoryFilter(categoryId: String?) {
        _selectedCategory.value = categoryId
        loadGoals()
    }

    fun clearFilters() {
        _selectedStatus.value = "全部"
        _selectedCategory.value = null
        loadGoals()
    }

    /**
     * 加载所有目标（用于目标选择组件）
     */
    fun loadAllGoals() {
        viewModelScope.launch {
            try {
                goalRepository.getAllGoals().collect { goals ->
                    _allGoals.value = goals
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载所有目标失败", e)
            }
        }
    }

    fun loadGoalDetail(goalId: String) {
        viewModelScope.launch {
            _uiState.value = GoalUiState.Loading
            try {
                val goal = goalRepository.getGoalById(goalId)
                if (goal != null) {
                    _currentGoal.value = goal
                    
                    // 加载子任务
                    val subTasks = goalRepository.getSubTasksForGoal(goalId)
                    _subTasks.value = subTasks
                    
                    _uiState.value = GoalUiState.Success
                } else {
                    _uiState.value = GoalUiState.Error("目标不存在")
                }
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "加载目标详情失败")
            }
        }
    }

    fun createGoal(
        title: String,
        description: String,
        startDate: LocalDateTime? = null,
        dueDate: LocalDateTime? = null,
        priority: GoalPriority,
        categoryId: String = "personal_development",
        goalType: GoalType = GoalType.BOOLEAN,
        currentValue: Double? = null,
        targetValue: Double? = null,
        unit: String? = null,
        reminderSettings: List<ReminderSetting> = emptyList(),
        attachments: List<String> = emptyList(),
        isRecurring: Boolean = false,
        recurringSettings: RecurringSettings? = null
    ) {
        if (title.isBlank()) {
            _uiState.value = GoalUiState.Error("目标标题不能为空")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = GoalUiState.Loading
            try {
                val calculatedProgress = if (goalType == GoalType.NUMERIC) {
                    try {
                        val current = currentValue ?: 0.0
                        val target = targetValue ?: 1.0
                        if (target == 0.0) 0f else (current / target).toFloat().coerceIn(0f, 1f)
                    } catch (e: Exception) {
                        0f
                    }
                } else {
                    0f
                }
                
                val newGoal = Goal(
                    title = title,
                    description = description,
                    startDate = startDate,
                    dueDate = dueDate,
                    priority = priority,
                    categoryId = categoryId,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    goalType = goalType,
                    currentValue = currentValue,
                    targetValue = targetValue,
                    unit = unit,
                    progress = calculatedProgress,
                    reminderSettings = reminderSettings,
                    attachments = attachments,
                    isRecurring = isRecurring,
                    recurringSettings = recurringSettings
                )
                
                goalRepository.saveGoal(newGoal)
                loadGoals()
                loadStats()
                
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "创建目标失败")
            }
        }
    }

    fun updateGoal(goal: Goal) {
        viewModelScope.launch {
            _uiState.value = GoalUiState.Loading
            try {
                Log.d(TAG, "更新目标: ${goal.id} - ${goal.title}")
                
                // 更新数据库中的目标
                goalRepository.updateGoal(goal)
                
                // 更新当前目标状态
                if (_currentGoal.value?.id == goal.id) {
                    _currentGoal.value = goal
                }
                
                // 刷新目标列表
                loadGoals()
                
                Log.d(TAG, "✓ 目标更新成功")
            } catch (e: Exception) {
                Log.e(TAG, "更新目标失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "更新目标失败")
            }
        }
    }

    fun deleteGoal(goalId: String) {
        viewModelScope.launch {
            _uiState.value = GoalUiState.Loading
            try {
                goalRepository.deleteGoal(goalId)
                loadGoals()
                loadStats()
                
                _currentGoal.value = null
                _subTasks.value = emptyList()
                
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "删除目标失败")
            }
        }
    }

    fun markGoalCompleted(goalId: String, completed: Boolean) {
        viewModelScope.launch {
            try {
                val goal = goalRepository.getGoalById(goalId) ?: return@launch
                
                val updatedGoal = if (completed) {
                    goal.copy(
                        completedAt = LocalDateTime.now(),
                        progress = 1.0f,
                        status = "已完成",
                        updatedAt = LocalDateTime.now()
                    )
                } else {
                    goal.copy(
                        completedAt = null,
                        progress = if (goal.progress >= 1.0f) 0.99f else goal.progress,
                        status = "进行中",
                        updatedAt = LocalDateTime.now()
                    )
                }
                
                goalRepository.updateGoal(updatedGoal)
                
                // 如果当前正在查看该目标，则更新当前目标
                if (_currentGoal.value?.id == goalId) {
                    _currentGoal.value = updatedGoal
                }
                
                loadGoals()
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "更新目标状态失败")
            }
        }
    }

    fun updateGoalProgress(goalId: String, progress: Float) {
        viewModelScope.launch {
            try {
                val goal = goalRepository.getGoalById(goalId) ?: return@launch
                val normalizedProgress = progress.coerceIn(0f, 1f)
                
                val updatedGoal = goal.copy(
                    progress = normalizedProgress,
                    completedAt = if (normalizedProgress >= 1.0f) LocalDateTime.now() else null,
                    status = when {
                        normalizedProgress >= 1.0f -> "已完成"
                        normalizedProgress > 0f -> "进行中"
                        else -> "待完成"
                    },
                    updatedAt = LocalDateTime.now()
                )
                
                goalRepository.updateGoal(updatedGoal)
                
                // 如果当前正在查看该目标，则更新当前目标
                if (_currentGoal.value?.id == goalId) {
                    _currentGoal.value = updatedGoal
                }
                
                loadGoals()
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "更新目标进度失败")
            }
        }
    }

    fun addSubTask(goalId: String, title: String, description: String = "", estimatedDays: Int = 0) {
        if (title.isBlank()) {
            _uiState.value = GoalUiState.Error("子任务标题不能为空")
            return
        }
        
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始添加子目标: goalId=$goalId, title=$title")
                
                val subTask = GoalSubTask(
                    goalId = goalId,
                    title = title,
                    description = description,
                    estimatedDurationDays = estimatedDays,
                    createdAt = LocalDateTime.now()
                )
                
                // 保存子任务到数据库
                goalRepository.saveSubTask(subTask)
                Log.d(TAG, "✓ 子目标保存成功: ${subTask.id}")
                
                // 🔧 强制刷新子任务列表
                if (_currentGoal.value?.id == goalId) {
                    val refreshedSubTasks = goalRepository.getSubTasksForGoal(goalId)
                    _subTasks.value = refreshedSubTasks
                    Log.d(TAG, "✓ 子目标列表已刷新，当前数量: ${refreshedSubTasks.size}")
                    
                    // 🔧 确保UI状态为成功，触发重组
                    _uiState.value = GoalUiState.Success
                }
                
                Log.d(TAG, "✓ 子目标添加完成")
            } catch (e: Exception) {
                Log.e(TAG, "添加子目标失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "添加子任务失败")
            }
        }
    }

    fun markSubTaskCompleted(subTaskId: String, completed: Boolean) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始更新子目标状态: subTaskId=$subTaskId, completed=$completed")
                
                // 找到子任务
                val subTask = _subTasks.value.find { it.id == subTaskId } ?: return@launch
                
                // 更新子任务状态
                val updatedSubTask = if (completed) {
                    subTask.copy(
                        completedAt = LocalDateTime.now(),
                        status = "已完成"
                    )
                } else {
                    subTask.copy(
                        completedAt = null,
                        status = "进行中"
                    )
                }
                
                // 保存到数据库
                goalRepository.updateSubTask(updatedSubTask)
                Log.d(TAG, "✓ 子目标状态已保存: ${updatedSubTask.title}")
                
                // 🔧 强制刷新子任务列表
                val currentGoalId = _currentGoal.value?.id ?: return@launch
                val refreshedSubTasks = goalRepository.getSubTasksForGoal(currentGoalId)
                _subTasks.value = refreshedSubTasks
                Log.d(TAG, "✓ 子目标列表已刷新，当前数量: ${refreshedSubTasks.size}")
                
                // 更新目标进度
                val allSubTasks = refreshedSubTasks
                if (allSubTasks.isNotEmpty()) {
                    val completedCount = allSubTasks.count { it.completedAt != null }
                    val newProgress = completedCount.toFloat() / allSubTasks.size.toFloat()
                    updateGoalProgress(currentGoalId, newProgress)
                    Log.d(TAG, "✓ 目标进度已更新: $newProgress")
                }
                
                // 🔧 确保UI状态为成功，触发重组
                _uiState.value = GoalUiState.Success
                
            } catch (e: Exception) {
                Log.e(TAG, "更新子目标状态失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "更新子任务状态失败")
            }
        }
    }

    /**
     * 请求AI拆解目标
     */
    fun requestAiBreakdown(goalId: String) {
        viewModelScope.launch {
            Log.d(TAG, "========== AI拆解开始 ==========")
            Log.d(TAG, "目标ID: $goalId")
                
                _breakdownState.value = BreakdownState.Processing(false)
            Log.d(TAG, "✓ 状态设置为Processing")
            
            try {
                // 🔧 Step 1: 获取目标信息
                Log.d(TAG, "Step 1: 获取目标信息...")
                val goal = goalRepository.getGoalById(goalId)
                if (goal == null) {
                    Log.e(TAG, "❌ 目标不存在: $goalId")
                    _breakdownState.value = BreakdownState.Error("目标不存在")
                    return@launch
                }
                Log.d(TAG, "✓ 目标信息获取成功: ${goal.title}")
                Log.d(TAG, "目标详情: 标题=${goal.title}, 描述=${goal.description}, 优先级=${goal.priority}")
                
                // 🔧 Step 2: 获取AI配置
                Log.d(TAG, "Step 2: 获取AI配置...")
                val aiConfig = getAiConfig(getApplication())

                // 🔧 检查API密钥是否有效
                if (aiConfig.apiKey.isBlank()) {
                    Log.e(TAG, "❌ API密钥为空")
                    _breakdownState.value = BreakdownState.Error("API密钥未设置，请先在设置中配置AI服务的API密钥")
                    return@launch
                }
                Log.d(TAG, "✓ AI配置获取成功: ${aiConfig.name}")
                Log.d(TAG, "AI配置详情: provider=${aiConfig.provider}, model=${aiConfig.modelName}")
                Log.d(TAG, "服务器URL: ${aiConfig.serverUrl}")
                Log.d(TAG, "API密钥: ${aiConfig.apiKey.take(8)}...")
                
                // 🔧 Step 3: 直接调用AI对话页面的成功方法
                Log.d(TAG, "Step 3: 直接调用AI对话页面的aiSplitTaskToSubtasks方法...")
                val subTasks = callAiDialogMethod(goal, aiConfig)
                Log.d(TAG, "✓ AI对话方法调用完成，生成了 ${subTasks.size} 个子目标")
                
                // 🔧 Step 4: 保存子任务到数据库
                Log.d(TAG, "Step 4: 保存子目标到数据库...")
                subTasks.forEachIndexed { index, subTask ->
                    Log.d(TAG, "保存子目标 ${index + 1}: ${subTask.title}")
                    goalRepository.saveSubTask(subTask)
                }
                Log.d(TAG, "✓ 所有子目标保存完成")
                
                // 🔧 Step 5: 标记目标为已AI拆解
                Log.d(TAG, "Step 5: 标记目标为已AI拆解...")
                markGoalAsAiBreakdown(goalId)
                Log.d(TAG, "✓ 目标标记完成")
                
                // 🔧 Step 6: 刷新子任务列表
                Log.d(TAG, "Step 6: 刷新子任务列表...")
                val refreshedSubTasks = goalRepository.getSubTasksForGoal(goalId)
                _subTasks.value = refreshedSubTasks
                Log.d(TAG, "✓ 子任务列表刷新完成，当前数量: ${refreshedSubTasks.size}")
                
                // 🔧 Step 7: 设置显示结果状态，让UI显示生成的子任务
                Log.d(TAG, "Step 7: 设置显示结果状态...")
                _breakdownState.value = BreakdownState.ShowingResult(false)
                Log.d(TAG, "✓ AI拆解结果可供显示")
                
                // 延迟后设置为完成状态
                delay(1000)
                Log.d(TAG, "Step 8: 设置完成状态...")
                _breakdownState.value = BreakdownState.Completed
                Log.d(TAG, "✓ AI拆解流程完成")
                Log.d(TAG, "========== AI拆解成功结束 ==========")
                
                    } catch (e: Exception) {
                Log.e(TAG, "❌ AI拆解过程中出现异常", e)
                Log.e(TAG, "异常详情: ${e.message}")
                Log.e(TAG, "异常类型: ${e.javaClass.simpleName}")
                e.printStackTrace()

                // 🚀 禁用降级策略：只使用AI拆解
                Log.e(TAG, "❌ AI拆解失败，不使用本地模板")
                _breakdownState.value = BreakdownState.Error("AI拆解失败: ${e.message}")

                /* 降级策略已禁用
                try {
                    Log.d(TAG, "🔄 启动降级策略：使用本地模板拆解")

                    // 重新获取目标信息用于降级策略
                    val goal = goalRepository.getGoalById(goalId)
                    if (goal == null) {
                        Log.e(TAG, "❌ 降级策略失败：目标不存在")
                        throw Exception("目标不存在")
                    }

                    val fallbackSubTasks = generateFallbackSubTasks(goal)

                    if (fallbackSubTasks.isNotEmpty()) {
                        Log.d(TAG, "✅ 降级策略成功生成 ${fallbackSubTasks.size} 个子目标")

                        // 保存降级生成的子目标
                        fallbackSubTasks.forEach { subTask ->
                            goalRepository.saveSubTask(subTask)
                        }

                        // 更新状态为成功
                        _breakdownState.value = BreakdownState.Completed

                        Log.d(TAG, "✓ 降级拆解流程完成")
                        Log.d(TAG, "========== 降级拆解成功结束 ==========")
                        return@launch
                    }
                } catch (fallbackException: Exception) {
                    Log.e(TAG, "❌ 降级策略也失败了: ${fallbackException.message}")
                }
                */
            }
        }
    }
    
    /**
     * 生成降级子目标 - 当AI调用失败时使用本地模板
     */
    private fun generateFallbackSubTasks(goal: Goal): List<GoalSubTask> {
        Log.d(TAG, "开始生成降级子目标...")

        val subTasks = mutableListOf<GoalSubTask>()
        val goalTitle = goal.title.lowercase()
        val goalDescription = goal.description?.lowercase() ?: ""

        // 根据目标类型生成不同的模板
        val templates = when {
            // 学习类目标
            goalTitle.contains("学习") || goalTitle.contains("掌握") || goalTitle.contains("学会") -> {
                listOf(
                    "制定学习计划" to "分析学习内容，制定详细的学习时间表和阶段目标",
                    "基础知识学习" to "学习核心概念和基础理论，建立知识框架",
                    "实践练习" to "通过练习和实操加深理解，巩固所学知识",
                    "项目实战" to "应用所学知识完成实际项目，验证学习效果",
                    "总结提升" to "回顾学习过程，总结经验，制定进一步提升计划"
                )
            }
            // 健康类目标
            goalTitle.contains("健身") || goalTitle.contains("减肥") || goalTitle.contains("锻炼") || goalTitle.contains("健康") -> {
                listOf(
                    "制定健身计划" to "评估当前身体状况，制定适合的运动计划",
                    "基础训练" to "从基础动作开始，建立正确的运动习惯",
                    "强化训练" to "逐步增加训练强度，提升身体素质",
                    "饮食调整" to "配合运动调整饮食结构，优化营养摄入",
                    "效果评估" to "定期检测身体指标，调整训练和饮食方案"
                )
            }
            // 工作类目标
            goalTitle.contains("工作") || goalTitle.contains("项目") || goalTitle.contains("完成") || goalTitle.contains("开发") -> {
                listOf(
                    "需求分析" to "明确目标要求，分析具体需求和约束条件",
                    "方案设计" to "制定详细的实施方案和时间计划",
                    "分步实施" to "按计划逐步执行，定期检查进度",
                    "测试优化" to "测试结果质量，优化改进不足之处",
                    "总结汇报" to "整理成果，总结经验，向相关人员汇报"
                )
            }
            // 恋爱/感情类目标
            goalTitle.contains("恋爱") || goalTitle.contains("对象") || goalTitle.contains("感情") || goalTitle.contains("结婚") -> {
                listOf(
                    "自我提升" to "提升个人魅力和内在修养，为遇到合适的人做准备",
                    "扩展社交圈" to "参加各种活动，认识更多志同道合的朋友",
                    "明确标准" to "理清自己的择偶标准和期望，避免盲目交往",
                    "积极交往" to "主动参与社交活动，勇敢表达自己的想法",
                    "深入了解" to "与合适的人深入交流，建立稳定的感情关系"
                )
            }
            // 通用目标模板
            else -> {
                listOf(
                    "目标分析" to "深入分析目标要求，明确具体的实现路径",
                    "计划制定" to "制定详细的行动计划和时间安排",
                    "开始行动" to "按计划开始执行，保持持续的行动力",
                    "进度检查" to "定期检查进度，及时调整策略和方法",
                    "目标达成" to "完成最终目标，总结经验和收获"
                )
            }
        }

        // 生成子目标
        templates.forEachIndexed { index, (title, description) ->
            val subTask = GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goal.id,
                title = title,
                description = description,
                estimatedDurationDays = when (index) {
                    0 -> 3  // 第一个任务通常是计划阶段
                    templates.size - 1 -> 2  // 最后一个任务通常是总结阶段
                    else -> 7  // 中间的执行任务
                },
                completedAt = null,
                createdAt = LocalDateTime.now(),
                aiRecommendation = "建议专注执行，保持耐心和持续性",
                status = "待开始"
            )
            subTasks.add(subTask)
        }

        Log.d(TAG, "降级策略生成了 ${subTasks.size} 个子目标")
        return subTasks
    }

    /**
     * 检查AI配置状态 - 增强版本
     */
    suspend fun checkAiConfigStatus(): String {
        return try {
            Log.d(TAG, "开始检查AI配置状态...")

            val aiConfig = getAiConfig(getApplication())
            when {
                aiConfig.apiKey.isBlank() -> {
                    Log.w(TAG, "AI配置API密钥为空")
                    "❌ AI配置缺少API密钥，请在设置中完善配置"
                }
                aiConfig.serverUrl.isBlank() -> {
                    Log.w(TAG, "AI配置服务器URL为空")
                    "❌ AI配置缺少服务器地址，请在设置中完善配置"
                }
                else -> {
                    Log.d(TAG, "AI配置检查通过: ${aiConfig.name} (${aiConfig.provider})")
                    "✅ AI配置正常 - ${aiConfig.name} (${aiConfig.provider})"
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "AI配置检查失败", e)
            "❌ AI配置检查失败: ${e.message}"
        }
    }

    /**
     * 测试AI连接 - 新增功能
     */
    suspend fun testAiConnection(): String {
        return try {
            Log.d(TAG, "开始测试AI连接...")

            val aiConfig = getAiConfig(getApplication())

            if (aiConfig.apiKey.isBlank()) {
                return "❌ API密钥未设置，请在设置中配置API密钥"
            }

            // 创建一个简单的测试目标
            val testGoal = Goal(
                id = "test",
                title = "学习编程",
                description = "学习Python编程基础",
                priority = GoalPriority.MEDIUM,
                createdAt = LocalDateTime.now()
            )

            // 尝试调用AI服务
            val subTasks = callAiService(testGoal, aiConfig)

            if (subTasks.isNotEmpty()) {
                Log.d(TAG, "AI连接测试成功，生成了${subTasks.size}个子目标")
                "✅ AI连接测试成功，生成了${subTasks.size}个子目标"
            } else {
                "⚠️ AI连接成功但未生成有效结果"
            }

        } catch (e: Exception) {
            Log.e(TAG, "AI连接测试失败", e)
            "❌ AI连接测试失败: ${e.message}"
        }
    }

    /**
     * 🔧 获取AI配置 - 修复版本，参考AI对话页面的成功实现
     */
    private suspend fun getAiConfig(context: Context): AiConfig {
        return try {
            withContext(Dispatchers.IO) {
                Log.d(TAG, "开始获取AI配置...")

                // 获取选中的配置ID
                val selectedConfigId = context.dataStore.data.firstOrNull()?.get(SELECTED_CONFIG_ID_KEY)
                if (selectedConfigId.isNullOrBlank()) {
                    Log.w(TAG, "未选择AI配置，使用默认配置")
                    return@withContext getDefaultAiConfig()
                }

                // 获取所有配置
                val configsJson = context.dataStore.data.firstOrNull()?.get(AI_CONFIGS_KEY)
                if (configsJson.isNullOrBlank()) {
                    Log.w(TAG, "未找到AI配置列表，使用默认配置")
                    return@withContext getDefaultAiConfig()
                }

                // 解析配置列表
                val configsType = object : TypeToken<List<AiConfig>>() {}.type
                val configs: List<AiConfig> = gson.fromJson(configsJson, configsType)

                // 查找选中的配置
                val selectedConfig = configs.find { it.id == selectedConfigId }
                if (selectedConfig == null) {
                    Log.w(TAG, "找不到ID为 $selectedConfigId 的配置，使用默认配置")
                    return@withContext getDefaultAiConfig()
                }

                // 验证配置完整性
                if (selectedConfig.apiKey.isBlank()) {
                    Log.w(TAG, "AI配置的API密钥为空，使用默认配置")
                    return@withContext getDefaultAiConfig()
                }

                if (selectedConfig.serverUrl.isBlank()) {
                    Log.w(TAG, "AI配置的服务器URL为空，使用默认配置")
                    return@withContext getDefaultAiConfig()
                }

                Log.d(TAG, "🚀 成功获取AI配置: ${selectedConfig.name} (${selectedConfig.provider})")
                selectedConfig
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取AI配置失败，使用默认配置", e)
            getDefaultAiConfig()
        }
    }

    /**
     * 🔧 获取默认AI配置 - 新增方法
     */
    private fun getDefaultAiConfig(): AiConfig {
        return AiConfig(
            id = "default-deepseek",
            name = "DeepSeek默认配置",
            provider = "DeepSeek",
            modelName = "deepseek-chat",
            serverUrl = "https://api.deepseek.com/v1",
            apiKey = "", // 需要用户在设置中配置
            maxTokens = 2048,
            temperature = 0.7f,
            isDefault = true
        )
    }
    
    /**
     * 计算已完成目标的平均持续天数
     * 用于AI上下文提供数据参考
     */
    private suspend fun calculateAverageGoalDuration(): Double {
        return withContext(Dispatchers.IO) {
            try {
                // 先收集Flow数据，然后对List进行过滤
                val allGoals = goalRepository.getAllGoals().firstOrNull() ?: emptyList()
                val completedGoals = allGoals.filter { goal -> goal.completedAt != null }
                
                if (completedGoals.isEmpty()) return@withContext 7.0 // 默认7天
                
                val totalDurationDays = completedGoals.sumOf { goal -> goal.getDurationDays() }
                (totalDurationDays.toDouble() / completedGoals.size).coerceAtLeast(1.0)
            } catch (e: Exception) {
                Log.e(TAG, "计算目标平均持续时间失败", e)
                7.0 // 默认7天
            }
        }
    }

    /**
     * 获取用户的历史目标数据用于AI分析
     */
    private suspend fun getUserHistoryContext(): String {
        return withContext(Dispatchers.IO) {
            try {
                val allGoals = goalRepository.getAllGoals().firstOrNull() ?: emptyList()
                val completedGoals = allGoals.filter { it.completedAt != null }
                val totalGoals = allGoals.size
                val completedCount = completedGoals.size
                val completionRate = if (totalGoals > 0) (completedCount * 100 / totalGoals) else 0
                
                // 分析目标类型分布
                val goalTypes = allGoals.map { goal ->
                    when {
                        goal.title.contains("学习") || goal.title.contains("培训") -> "学习"
                        goal.title.contains("项目") || goal.title.contains("开发") -> "项目"
                        goal.title.contains("健康") || goal.title.contains("运动") -> "健康"
                        goal.title.contains("工作") || goal.title.contains("职业") -> "工作"
                        else -> "其他"
                    }
                }.groupingBy { it }.eachCount()
                
                val avgDuration = calculateAverageGoalDuration()
                
                """
                用户历史数据分析：
                - 总目标数：$totalGoals 个
                - 完成目标数：$completedCount 个
                - 完成率：$completionRate%
                - 平均完成时间：${avgDuration.toInt()} 天
                - 目标类型分布：${goalTypes.entries.joinToString(", ") { "${it.key}:${it.value}个" }}
                """.trimIndent()
            } catch (e: Exception) {
                Log.e(TAG, "获取用户历史数据失败", e)
                "用户历史数据：暂无足够数据进行分析"
            }
        }
    }

    /**
     * 直接调用AI对话页面的成功方法
     */
    private suspend fun callAiDialogMethod(goal: Goal, aiConfig: AiConfig): List<GoalSubTask> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "🚀 直接调用AI对话页面的aiSplitTaskToSubtasks方法")

                // 直接使用已注入的aiTaskRepository

                // 创建Task对象用于AI拆解
                val task = Task(
                    id = UUID.randomUUID().toString(),
                    title = goal.title,
                    description = goal.description ?: "",
                    type = TaskType.NORMAL,
                    priority = Priority.MEDIUM,
                    dueDate = goal.dueDate,
                    startDate = goal.startDate,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    isCompleted = false,
                    completedAt = null,
                    tags = emptyList<TaskTag>(),
                    estimatedTimeMinutes = 60,
                    actualTimeMinutes = 0,
                    reminderTime = null,
                    goalId = goal.id
                )

                // 直接调用AI对话页面的成功方法
                val aiSubTasks = aiTaskRepository.aiSplitTaskToSubtasks(task)

                Log.d(TAG, "AI对话方法返回 ${aiSubTasks.size} 个子任务")

                // 转换为GoalSubTask对象
                val subTasks = aiSubTasks.mapIndexed { index: Int, title: String ->
                    GoalSubTask(
                        id = UUID.randomUUID().toString(),
                        goalId = goal.id,
                        title = title,
                        description = "AI智能拆解生成的子目标",
                        estimatedDurationDays = 3 + index,
                        completedAt = null,
                        createdAt = LocalDateTime.now(),
                        aiRecommendation = "建议按顺序完成此子目标",
                        status = "待开始"
                    )
                }

                Log.d(TAG, "✅ 成功转换为 ${subTasks.size} 个GoalSubTask对象")
                return@withContext subTasks

            } catch (e: Exception) {
                Log.e(TAG, "❌ AI对话方法调用失败: ${e.message}", e)
                throw Exception("AI拆解失败: ${e.message}")
            }
        }
    }

    /**
     * 调用AI服务API进行目标拆解 - 增强版本
     */
    private suspend fun callAiService(goal: Goal, aiConfig: AiConfig): List<GoalSubTask> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始调用AI API进行目标拆解")
                
                // 🚀 完全复制AI对话页面executeApiRequest的实现方式
                val prompt = """将"${goal.title}"拆解为3-5个子目标，每个不超过12字，格式：
1. 子目标名
2. 子目标名
3. 子目标名

只返回编号列表，无需其他说明。""".trimIndent()

                // 🚀 使用与AI对话页面完全相同的Map构建和JSONObject序列化
                val request = mapOf(
                    "model" to aiConfig.modelName,
                    "messages" to listOf(
                        mapOf("role" to "user", "content" to prompt)
                    ),
                    "temperature" to 0.3,
                    "max_tokens" to 200,
                    "stream" to false
                )

                // 🚀 使用与AI对话页面完全相同的序列化方式
                val jsonBody = org.json.JSONObject(request).toString()
                Log.d(TAG, "使用JSONObject序列化，请求体: $jsonBody")
                
                // 🚀 完全复制AI对话页面executeApiRequest的HTTP实现
                val maxRetries = 1  // 与AI对话页面一致
                var retryCount = 0
                var lastException: Exception? = null

                while (retryCount < maxRetries) {
                    try {
                        val client = OkHttpClient.Builder()
                            .connectTimeout(8, TimeUnit.SECONDS)
                            .writeTimeout(15, TimeUnit.SECONDS)
                            .readTimeout(25, TimeUnit.SECONDS)
                            .build()

                        val requestBody = jsonBody.toRequestBody("application/json; charset=utf-8".toMediaType())

                        // 🚀 使用与AI对话页面相同的URL构建方式
                        val sanitizedUrl = if (aiConfig.serverUrl.endsWith("/")) {
                            "${aiConfig.serverUrl}chat/completions"
                        } else {
                            "${aiConfig.serverUrl}/chat/completions"
                        }

                        Log.d(TAG, "API请求URL (尝试${retryCount+1}/$maxRetries): $sanitizedUrl")
                        Log.d(TAG, "🚀 完全复制executeApiRequest实现: JSONObject序列化, 独立HTTP客户端")

                        val httpRequest = Request.Builder()
                            .url(sanitizedUrl)
                            .addHeader("Content-Type", "application/json")
                            .addHeader("Authorization", "Bearer ${aiConfig.apiKey}")
                            .post(requestBody)
                            .build()

                        Log.d(TAG, "发送API请求 (尝试${retryCount+1}/$maxRetries)")
                        val response = client.newCall(httpRequest).execute()

                        // 🚀 完全复制AI对话页面的响应处理
                        if (!response.isSuccessful) {
                            val errorBody = response.body?.string() ?: "无响应内容"
                            val errorMsg = when (response.code) {
                                401 -> "API密钥验证失败 (401)"
                                403 -> "API访问被拒绝 (403)"
                                404 -> "API端点未找到 (404)，请检查服务器URL: $sanitizedUrl"
                                429 -> "API请求过多 (429)，请求频率过高或超出配额"
                                500, 502, 503, 504 -> "AI服务器错误 (${response.code})"
                                else -> "API调用失败 (${response.code}): ${response.message}"
                            }
                            Log.e(TAG, "API请求失败: $errorMsg")
                            throw Exception(errorMsg)
                        }

                        val responseBody = response.body?.string() ?: throw Exception("空响应")
                        Log.d(TAG, "响应内容预览: ${responseBody.take(200)}...")

                        // 🚀 使用与AI对话页面相同的JSON解析
                        val jsonResponse = JSONObject(responseBody)
                        val choices = jsonResponse.getJSONArray("choices")
                        val content = choices.getJSONObject(0)
                            .getJSONObject("message")
                            .getString("content")

                        Log.d(TAG, "AI生成内容: $content")

                        // 🚀 使用与AI对话页面相同的简单列表解析
                        val subTaskTitles = content.lines()
                            .asSequence()  // 使用序列提高性能
                            .mapNotNull { line ->
                                // 匹配"数字. 内容"格式
                                val match = Regex("^\\s*(\\d+)\\s*[.．]\\s*(.+)$").find(line.trim())
                                match?.groupValues?.getOrNull(2)?.trim()?.let { task ->
                                    if (task.length <= 12) task else task.take(12)  // 限制长度
                                }
                            }
                            .filter { it.isNotBlank() && it.length >= 2 }  // 过滤太短的结果
                            .take(5)  // 最多5个
                            .toList()

                        // 转换为GoalSubTask对象
                        val subTasks = subTaskTitles.mapIndexed { index, title ->
                            GoalSubTask(
                                id = UUID.randomUUID().toString(),
                                goalId = goal.id,
                                title = title,
                                description = "AI智能拆解生成的子目标",
                                estimatedDurationDays = 3 + index, // 简单的天数分配
                                completedAt = null,
                                createdAt = LocalDateTime.now(),
                                aiRecommendation = "建议按顺序完成此子目标",
                                status = "待开始"
                            )
                        }

                        // 验证结果质量
                        if (subTasks.isEmpty()) {
                            throw Exception("AI未能生成有效的子目标")
                        }

                        Log.d(TAG, "AI成功生成 ${subTasks.size} 个子目标: ${subTaskTitles}")
                        return@withContext subTasks

                    } catch (e: java.net.SocketTimeoutException) {
                        lastException = Exception("网络请求超时，请检查网络连接后重试")
                        Log.e(TAG, "❌ 请求超时 (尝试${retryCount+1}/$maxRetries): ${e.message}")
                    } catch (e: java.net.ConnectException) {
                        lastException = Exception("无法连接到AI服务器，请检查网络连接")
                        Log.e(TAG, "❌ 连接失败 (尝试${retryCount+1}/$maxRetries): ${e.message}")
                    } catch (e: Exception) {
                        lastException = Exception("网络请求失败: ${e.message}")
                        Log.e(TAG, "❌ HTTP请求异常 (尝试${retryCount+1}/$maxRetries): ${e.message}", e)
                    }

                    retryCount++
                    if (retryCount < maxRetries) {
                        Log.d(TAG, "等待2秒后重试...")
                        kotlinx.coroutines.delay(2000)
                    }
                }

                // 所有重试都失败了，抛出最后一个异常
                throw lastException ?: Exception("未知网络错误")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ AI目标拆解API调用失败", e)
                Log.e(TAG, "API调用失败详情: ${e.message}")
                Log.e(TAG, "失败的目标: ${goal.title}")

                // 🔧 改进：不使用备选方案，直接抛出异常让上层处理
                throw Exception("AI API调用失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 解析AI响应到子目标列表 - 增强版本
     */
    private fun parseAiResponseToSubTasks(response: String, goalId: String): List<GoalSubTask> {
        try {
            Log.d(TAG, "开始解析AI响应，响应长度: ${response.length}")
            
            // 清理响应文本，移除markdown代码块标记
            var cleanedResponse = response.trim()
                .replace("```json", "")
                .replace("```JSON", "")
                    .replace("```", "")
                    .trim()
            
            // 尝试多种JSON提取策略
            val jsonStr = when {
                // 策略1：直接尝试解析整个响应
                cleanedResponse.startsWith("{") && cleanedResponse.endsWith("}") -> {
                    cleanedResponse
                }
                
                // 策略2：查找包含subTasks的JSON对象
                cleanedResponse.contains("\"subTasks\"") -> {
                    val startIndex = cleanedResponse.indexOf("{")
                    val endIndex = cleanedResponse.lastIndexOf("}") + 1
                    if (startIndex >= 0 && endIndex > startIndex) {
                        cleanedResponse.substring(startIndex, endIndex)
                    } else {
                        throw Exception("无法找到有效的JSON结构")
                    }
                }
                
                // 策略3：使用正则表达式提取JSON
                else -> {
                    val jsonPattern = Pattern.compile("\\{[^{}]*\"subTasks\"[^{}]*\\[.*?\\][^{}]*\\}", Pattern.DOTALL)
                    val matcher = jsonPattern.matcher(cleanedResponse)
                    if (matcher.find()) {
                        matcher.group(0)
                    } else {
                        throw Exception("响应中未找到有效的JSON格式")
                    }
                }
            }
            
            Log.d(TAG, "提取的JSON字符串: $jsonStr")
            
            // 解析JSON
            val json = JSONObject(jsonStr)
            val subTasksArray = json.getJSONArray("subTasks")
            
            if (subTasksArray.length() == 0) {
                throw Exception("AI返回的子目标列表为空")
            }
            
            val subTasks = mutableListOf<GoalSubTask>()
            
            for (i in 0 until subTasksArray.length()) {
                try {
                val subTaskObj = subTasksArray.getJSONObject(i)
                
                    // 验证必要字段
                    val title = subTaskObj.optString("title", "").trim()
                    if (title.isBlank()) {
                        Log.w(TAG, "跳过标题为空的子目标")
                        continue
                    }
                    
                    val description = subTaskObj.optString("description", "AI生成的子目标").trim()
                    val estimatedDays = subTaskObj.optInt("estimatedDurationDays", 1).coerceIn(1, 30)
                    val aiRecommendation = subTaskObj.optString("aiRecommendation", "")
                
                val subTask = GoalSubTask(
                        id = UUID.randomUUID().toString(),
                    goalId = goalId,
                        title = title,
                        description = description,
                        estimatedDurationDays = estimatedDays,
                    createdAt = LocalDateTime.now(),
                    status = "待开始",
                        aiRecommendation = aiRecommendation.takeIf { it.isNotBlank() }
                )
                
                subTasks.add(subTask)
                    Log.d(TAG, "成功解析子目标 ${i + 1}: $title")
                    
                } catch (e: Exception) {
                    Log.e(TAG, "解析第 ${i + 1} 个子目标失败", e)
                    // 继续处理其他子目标
                }
            }
            
            if (subTasks.isEmpty()) {
                throw Exception("没有成功解析任何有效的子目标")
            }
            
            Log.d(TAG, "AI解析成功，共生成 ${subTasks.size} 个子目标")
            return subTasks
            
        } catch (e: Exception) {
            Log.e(TAG, "解析AI响应失败: ${e.message}", e)
            Log.d(TAG, "失败的响应内容: $response")
            
            // 解析失败时返回基础子目标
            return createFallbackSubTasks(goalId, "AI拆解解析失败，请手动添加子目标或重试AI拆解")
        }
    }

    /**
     * 创建备用子目标（当AI解析失败时使用）
     */
    private fun createFallbackSubTasks(goalId: String, reason: String): List<GoalSubTask> {
            return listOf(
                GoalSubTask(
                    id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "分析和规划",
                description = "详细分析目标要求，制定执行计划和时间安排",
                    estimatedDurationDays = 1,
                    createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "建议先明确目标的具体要求和成功标准"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "准备工作",
                description = "收集所需资源、工具和材料，做好充分准备",
                estimatedDurationDays = 2,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "充分的准备工作能提高后续执行效率"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "核心执行",
                description = "按计划执行目标的核心任务和关键环节",
                estimatedDurationDays = 3,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "建议将核心任务进一步细分为更小的步骤"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "检查优化",
                description = "检查完成质量，进行必要的调整和优化",
                estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "定期检查进度有助于及时发现和解决问题"
            )
        )
    }

    fun generateAiAnalysis(goalId: String) {
        viewModelScope.launch {
            try {
                val goal = goalRepository.getGoalById(goalId) ?: return@launch
                
                // 获取应用上下文
                val context = getApplication<Application>().applicationContext
                
                // 获取AI配置
                val aiConfig = getAiConfig(context)

                // 决定使用真实API还是模拟数据
                if (aiConfig.apiKey.isNotEmpty() && aiConfig.serverUrl.isNotEmpty()) {
                    // 调用真实的AI API服务获取分析结果
                    callAiAnalysisService(goal, aiConfig)
                } else {
                    // 使用模拟数据
                    generateMockAiAnalysis(goal)
                }
                
                // 刷新当前数据
                if (_currentGoal.value?.id == goalId) {
                    _currentGoal.value = goalRepository.getGoalById(goalId)
                }
                
                loadGoals()
                
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "生成AI分析失败")
            }
        }
    }
    
    /**
     * 调用AI服务API进行目标分析
     */
    private suspend fun callAiAnalysisService(goal: Goal, aiConfig: AiConfig) {
        return withContext(Dispatchers.IO) {
            try {
                // 准备请求体
                val requestJson = JSONObject().apply {
                    put("model", aiConfig.modelName)
                    put("temperature", aiConfig.temperature)
                    put("max_tokens", aiConfig.maxTokens)
                    
                    // 构建消息
                    val messagesArray = JSONArray().apply {
                        // 系统消息
                        put(JSONObject().apply {
                            put("role", "system")
                            put("content", "你是TimeFlow应用中的AI助手，负责分析用户的目标，提供有效的效率建议，" + 
                                "并基于常见的时间管理理论给出最佳工作时段推荐。你的回答应该结构化，便于解析。")
                        })
                        
                        // 用户消息
                        put(JSONObject().apply {
                            put("role", "user")
                            put("content", "请分析以下目标，提供高效完成的建议：\n" +
                                "标题：${goal.title}\n" +
                                "描述：${goal.description ?: ""}\n" +
                                "目标期限：${goal.dueDate ?: "未设置"}\n" +
                                "难度：中等\n" +
                                "历史目标平均完成天数：${calculateAverageGoalDuration()}天\n\n" +
                                "请给出如下信息：\n" +
                                "1. 给出3条具体的提高效率的建议（以'推荐:'开头）\n" +
                                "2. 提供3个效率指标（格式为'指标名:数值'，数值范围0.0-2.0）\n" +
                                "3. 推荐3个最佳工作时段（格式：'周几,开始时间-结束时间,效率系数,描述'）")
                        })
                    }
                    put("messages", messagesArray)
                }
                
                // 🚀 构建HTTP请求 - 修复端点URL
                val endpoint = if (aiConfig.serverUrl.endsWith("/")) {
                    "${aiConfig.serverUrl}chat/completions"
                } else {
                    "${aiConfig.serverUrl}/chat/completions"
                }

                val mediaType = "application/json; charset=utf-8".toMediaType()
                val requestBody = requestJson.toString().toRequestBody(mediaType)
                val request = Request.Builder()
                    .url(endpoint)
                    .addHeader("Authorization", "Bearer ${aiConfig.apiKey}")
                    .addHeader("Content-Type", "application/json")
                    .post(requestBody)
                    .build()
                
                // 执行请求
                val response = httpClient.newCall(request).execute()
                
                if (!response.isSuccessful) {
                    throw Exception("API调用失败: ${response.code}")
                }
                
                // 解析响应
                val responseBody = response.body?.string() ?: throw Exception("返回空响应")
                val jsonResponse = JSONObject(responseBody)
                
                // 提取结果
                val choices = jsonResponse.getJSONArray("choices")
                if (choices.length() == 0) {
                    throw Exception("API返回结果为空")
                }
                
                val message = choices.getJSONObject(0).getJSONObject("message")
                val content = message.getString("content")
                
                // 解析内容并更新目标
                val (recommendations, metrics, timeSlots) = parseAiAnalysisResponse(content)
                
                // 更新目标
                val updatedGoal = goal.copy(
                    hasAiAnalysis = true,
                    updatedAt = LocalDateTime.now(),
                    aiRecommendations = recommendations,
                    metrics = metrics,
                    bestTimeSlots = timeSlots
                )
                
                goalRepository.updateGoal(updatedGoal)
                
            } catch (e: Exception) {
                // 发生异常时记录日志并使用模拟数据
                Log.e("GoalViewModel", "调用AI分析服务失败", e)
                generateMockAiAnalysis(goal)
            }
        }
    }
    
    /**
     * 解析AI分析响应
     * 返回三元组：推荐列表，指标映射，时间段列表
     */
    private fun parseAiAnalysisResponse(content: String): Triple<List<String>, Map<String, Float>, List<TimeSlotInfo>> {
        val recommendations = mutableListOf<String>()
        val metrics = mutableMapOf<String, Float>()
        val timeSlots = mutableListOf<TimeSlotInfo>()
        
        val lines = content.split("\n")
        
        for (line in lines) {
            val trimmedLine = line.trim()
            
            // 解析推荐
            if (trimmedLine.startsWith("推荐:") || trimmedLine.startsWith("推荐：") || 
                trimmedLine.contains("建议") || trimmedLine.contains("应该")) {
                val recommendation = trimmedLine.substringAfter(':').trim()
                    .takeIf { it.isNotBlank() } 
                    ?: trimmedLine
                recommendations.add(recommendation)
            }
            
            // 解析指标 (格式: "指标名: 数值" 或 "指标名 数值")
            else if (trimmedLine.contains(":") && !trimmedLine.startsWith("推荐") && 
                    !trimmedLine.contains("时段") && !trimmedLine.contains("-")) {
                try {
                    val parts = trimmedLine.split(":")
                    if (parts.size >= 2) {
                        val metricName = parts[0].trim()
                        // 尝试提取数值
                        val valueText = parts[1].trim()
                        val valueMatch = Regex("[0-9]+([.][0-9]+)?").find(valueText)
                        if (valueMatch != null) {
                            val value = valueMatch.value.toFloatOrNull() ?: continue
                            metrics[metricName] = value
                        }
                    }
                } catch (e: Exception) {
                    // 解析失败，跳过
                    continue
                }
            }
            
            // 解析时间段 (格式: "周几,开始时间-结束时间,效率系数,描述")
            else if ((trimmedLine.contains("周") || trimmedLine.contains("星期")) && 
                     trimmedLine.contains("-") && 
                     (trimmedLine.contains("点") || trimmedLine.contains(":"))) {
                try {
                    // 尝试提取时间段信息
                    val dayOfWeekMatch = Regex("周[一二三四五六日天]|星期[一二三四五六日天]").find(trimmedLine)
                    val timeRangeMatch = Regex("([0-9]{1,2})[点:]?-([0-9]{1,2})[点:]?").find(trimmedLine)
                    val efficiencyMatch = Regex("([0-9]+([.][0-9]+)?)%?").find(trimmedLine)
                    
                    if (dayOfWeekMatch != null && timeRangeMatch != null) {
                        val dayOfWeek = dayOfWeekMatch.value
                        val startHour = timeRangeMatch.groupValues[1].toIntOrNull() ?: continue
                        val endHour = timeRangeMatch.groupValues[2].toIntOrNull() ?: continue
                        
                        // 提取效率系数，如果没有则默认为0.25
                        val efficiency = if (efficiencyMatch != null) {
                            val value = efficiencyMatch.groupValues[1].toFloatOrNull() ?: 0.25f
                            if (trimmedLine.contains("%")) value / 100 else value.coerceIn(0f, 1f)
                        } else {
                            0.25f
                        }
                        
                        // 提取描述，默认为时段类型
                        val description = when {
                            trimmedLine.contains("高效") || trimmedLine.contains("效率高") -> "工作效率高峰期"
                            trimmedLine.contains("创意") || trimmedLine.contains("灵感") -> "创意思维活跃时段"
                            trimmedLine.contains("学习") || trimmedLine.contains("专注") -> "学习效率最佳时段"
                            else -> "高效工作时段"
                        }
                        
                        timeSlots.add(
                            TimeSlotInfo(
                                dayOfWeek = dayOfWeek,
                                startHour = startHour,
                                endHour = endHour,
                                efficiency = efficiency,
                                description = description
                            )
                        )
                    }
                } catch (e: Exception) {
                    // 解析失败，跳过
                    continue
                }
            }
        }
        
        // 确保有至少一些内容，否则使用默认值
        if (recommendations.isEmpty()) {
            recommendations.add("根据您的工作习惯，建议将核心任务安排在每天上午9-11点，这段时间您的效率比平均值高25%")
            recommendations.add("建议尝试「番茄工作法」：25分钟专注工作+5分钟休息，可提升理解力20%")
            recommendations.add("周日晚上20-22点是您的最佳学习时段，应该重点利用这段时间")
        }
        
        if (metrics.isEmpty()) {
            metrics["阅读速度"] = 1.15f
            metrics["专注度"] = 0.85f
            metrics["完成效率"] = 0.75f
        }
        
        if (timeSlots.isEmpty()) {
            timeSlots.add(
                TimeSlotInfo(
                    dayOfWeek = "周三",
                    startHour = 9,
                    endHour = 11,
                    efficiency = 0.25f,
                    description = "工作效率高峰期"
                )
            )
            timeSlots.add(
                TimeSlotInfo(
                    dayOfWeek = "周五", 
                    startHour = 14,
                    endHour = 16,
                    efficiency = 0.20f,
                    description = "创意思维活跃时段"
                )
            )
            timeSlots.add(
                TimeSlotInfo(
                    dayOfWeek = "周日",
                    startHour = 20,
                    endHour = 22,
                    efficiency = 0.30f,
                    description = "学习效率最佳时段"
                )
            )
        }
        
        return Triple(recommendations.take(3), metrics.entries.take(3).associate { it.key to it.value }, timeSlots.take(3))
    }
    
    /**
     * 生成模拟AI分析数据
     */
    private suspend fun generateMockAiAnalysis(goal: Goal) {
        // TODO: 这里应该调用AI服务进行目标分析
        // 目前先模拟AI分析，生成一些示例分析结果
        
        val recommendations = listOf(
            "根据您的工作习惯，建议将核心任务安排在每天上午9-11点，这段时间您的效率比平均值高25%",
            "建议尝试「番茄工作法」：25分钟专注工作+5分钟休息，可提升理解力20%",
            "周日晚上20-22点是您的最佳学习时段，应该重点利用这段时间"
        )
        
        val metrics = mapOf(
            "阅读速度" to 1.15f,
            "专注度" to 0.85f,
            "完成效率" to 0.75f
        )
        
        val timeSlots = listOf(
            TimeSlotInfo(
                dayOfWeek = "周三",
                startHour = 9,
                endHour = 11,
                efficiency = 0.25f,
                description = "工作效率高峰期"
            ),
            TimeSlotInfo(
                dayOfWeek = "周五",
                startHour = 14,
                endHour = 16,
                efficiency = 0.20f,
                description = "创意思维活跃时段"
            ),
            TimeSlotInfo(
                dayOfWeek = "周日",
                startHour = 20,
                endHour = 22,
                efficiency = 0.30f,
                description = "学习效率最佳时段"
            )
        )
        
        // 更新目标
        val updatedGoal = goal.copy(
            hasAiAnalysis = true,
            updatedAt = LocalDateTime.now(),
            aiRecommendations = recommendations,
            metrics = metrics,
            bestTimeSlots = timeSlots
        )
        
        goalRepository.updateGoal(updatedGoal)
    }

    // 添加供SaveGoalAsTemplateScreen直接调用的方法
    suspend fun getGoalById(goalId: String): Goal? {
        return goalRepository.getGoalById(goalId)
    }

    fun resetBreakdownState() {
        _breakdownState.value = BreakdownState.Idle
    }
    
    /**
     * 更新目标拆解状态
     * @param state 新的拆解状态
     */
    fun updateBreakdownState(state: BreakdownState) {
        _breakdownState.value = state
    }
    
    /**
     * 加载目标的子任务列表
     * @param goalId 目标ID
     */
    fun loadSubTasks(goalId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = GoalUiState.Loading
                
                // 从仓库获取子任务列表
                val subTasksList = goalRepository.getSubTasksForGoal(goalId)
                _subTasks.value = subTasksList
                
                // 更新目标的AI拆解状态
                val goal = goalRepository.getGoalById(goalId)
                if (goal != null && !goal.hasAiBreakdown && subTasksList.isNotEmpty()) {
                    // 更新目标，标记为已拆解
                    val updatedGoal = goal.copy(
                        hasAiBreakdown = true,
                        updatedAt = LocalDateTime.now()
                    )
                    goalRepository.updateGoal(updatedGoal)
                    
                    // 如果当前正在查看该目标，则更新当前目标
                    if (_currentGoal.value?.id == goalId) {
                        _currentGoal.value = updatedGoal
                    }
                }
                
                _uiState.value = GoalUiState.Success
            } catch (e: Exception) {
                _uiState.value = GoalUiState.Error(e.message ?: "加载子任务失败")
            }
        }
    }
    
    // 旧的breakdownGoal方法已删除，现在使用requestAiBreakdown

    /**
     * 智能生成备选子目标（基于目标内容分析）
     */
    private suspend fun createSmartMockSubTasks(goal: Goal): List<GoalSubTask> {
        Log.d(TAG, "开始智能生成备选子目标")
        
        // 分析目标类型和关键词
        val title = goal.title.lowercase()
        val description = goal.description?.lowercase() ?: ""
        val content = "$title $description"
        
        // 估算总体时间
        val totalDays = if (goal.dueDate != null && goal.startDate != null) {
            ChronoUnit.DAYS.between(goal.startDate, goal.dueDate).toInt().coerceAtLeast(3)
        } else {
            calculateAverageGoalDuration().toInt().coerceAtLeast(7)
        }
        
        // 根据目标内容智能分类和生成
        val subTasks = when {
            // 学习类目标
            content.contains("学习") || content.contains("掌握") || content.contains("学会") ||
            content.contains("培训") || content.contains("课程") || content.contains("技能") -> {
                generateLearningSubTasks(goal.id, title, totalDays)
            }
            
            // 项目类目标
            content.contains("项目") || content.contains("开发") || content.contains("设计") ||
            content.contains("制作") || content.contains("构建") || content.contains("实现") -> {
                generateProjectSubTasks(goal.id, title, totalDays)
            }
            
            // 健康类目标
            content.contains("健康") || content.contains("运动") || content.contains("锻炼") ||
            content.contains("减肥") || content.contains("健身") || content.contains("体重") -> {
                generateHealthSubTasks(goal.id, title, totalDays)
            }
            
            // 工作职业类目标
            content.contains("工作") || content.contains("职业") || content.contains("升职") ||
            content.contains("面试") || content.contains("求职") || content.contains("简历") -> {
                generateCareerSubTasks(goal.id, title, totalDays)
            }
            
            // 阅读写作类目标
            content.contains("阅读") || content.contains("读书") || content.contains("写作") ||
            content.contains("文章") || content.contains("书籍") || content.contains("写") -> {
                generateReadingWritingSubTasks(goal.id, title, totalDays)
            }
            
            // 财务类目标
            content.contains("存钱") || content.contains("理财") || content.contains("投资") ||
            content.contains("财务") || content.contains("预算") || content.contains("消费") -> {
                generateFinancialSubTasks(goal.id, title, totalDays)
            }
            
            // 通用目标
            else -> {
                generateGenericSubTasks(goal.id, title, totalDays)
            }
        }
        
        Log.d(TAG, "智能生成了 ${subTasks.size} 个${getGoalCategory(content)}类子目标")
        return subTasks
    }
    
    /**
     * 获取目标分类
     */
    private fun getGoalCategory(content: String): String {
        return when {
            content.contains("学习") || content.contains("培训") -> "学习"
            content.contains("项目") || content.contains("开发") -> "项目"
            content.contains("健康") || content.contains("运动") -> "健康"
            content.contains("工作") || content.contains("职业") -> "职业"
            content.contains("阅读") || content.contains("写作") -> "阅读写作"
            content.contains("财务") || content.contains("理财") -> "财务"
            else -> "通用"
        }
    }

    /**
     * 生成学习类子目标
     */
    private fun generateLearningSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        val phaseTime = (totalDays / 4.0).toInt().coerceAtLeast(1)
        
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "学习计划制定",
                description = "分析学习目标，制定详细的学习计划和时间安排",
                    estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "建议制定每日学习计划，设定可衡量的学习目标"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                    title = "基础知识学习",
                description = "学习相关的基础理论知识和核心概念",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "先掌握基础概念，为后续深入学习打好根基"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "实践练习",
                description = "通过练习题、案例分析等方式巩固所学知识",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "理论结合实践，多做练习题增强理解"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "进阶学习",
                description = "深入学习高级内容和复杂应用场景",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "循序渐进，将知识点串联成完整的知识体系"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "总结评估",
                description = "总结学习成果，进行自我评估和查漏补缺",
                estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "定期回顾总结，确保学习效果达到预期目标"
            )
        )
    }
    
    /**
     * 生成项目类子目标
     */
    private fun generateProjectSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        val phaseTime = (totalDays / 5.0).toInt().coerceAtLeast(1)
        
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                    title = "需求分析",
                description = "明确项目需求，分析功能要求和技术方案",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "详细的需求分析是项目成功的基础"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "设计规划",
                description = "设计项目架构，制定详细的实施计划",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "好的设计能有效提高开发效率和代码质量"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "核心开发",
                description = "实现项目的核心功能和主要模块",
                estimatedDurationDays = phaseTime * 2,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "先完成核心功能，再逐步完善细节功能"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "测试调试",
                description = "全面测试项目功能，修复发现的问题",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "充分的测试能确保项目质量和稳定性"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "完善部署",
                description = "完善项目文档，部署上线并进行最终验收",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "完整的文档和部署流程便于后续维护"
            )
        )
    }
    
    /**
     * 生成健康类子目标
     */
    private fun generateHealthSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "健康评估",
                description = "了解当前健康状况，制定个性化的健康计划",
                    estimatedDurationDays = 2,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "建议先进行体检，了解身体基础状况"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "运动计划",
                description = "制定适合的运动方案，逐步提高运动强度",
                estimatedDurationDays = (totalDays * 0.6).toInt(),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "循序渐进，避免运动伤害，保持规律性"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "饮食调整",
                description = "调整饮食结构，培养健康的饮食习惯",
                estimatedDurationDays = (totalDays * 0.3).toInt(),
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "均衡营养，控制热量摄入，多吃蔬菜水果"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "效果评估",
                description = "定期评估健康改善效果，调整计划方案",
                    estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "记录身体变化，持续优化健康计划"
            )
        )
    }
    
    /**
     * 生成职业类子目标
     */
    private fun generateCareerSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "自我评估",
                description = "分析个人技能、经验和职业发展方向",
                estimatedDurationDays = 2,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "客观评估自身优势和不足，明确发展目标"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "技能提升",
                description = "学习相关技能，提升专业能力和竞争力",
                estimatedDurationDays = (totalDays * 0.5).toInt(),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "重点关注行业热门技能和核心竞争力"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "机会准备",
                description = "完善简历，建立人脉，寻找合适的机会",
                estimatedDurationDays = (totalDays * 0.3).toInt(),
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "主动出击，多渠道寻找机会和资源"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "目标达成",
                description = "把握机会，实现职业发展目标",
                estimatedDurationDays = (totalDays * 0.2).toInt(),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "保持积极心态，坚持努力直到成功"
            )
        )
    }
    
    /**
     * 生成阅读写作类子目标
     */
    private fun generateReadingWritingSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "计划制定",
                description = "制定阅读或写作计划，选择合适的材料和主题",
                    estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "明确目标，制定可执行的日程安排"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "内容积累",
                description = "按计划进行阅读或收集写作素材",
                estimatedDurationDays = (totalDays * 0.6).toInt(),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "保持规律性，做好笔记和思考总结"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "深度思考",
                description = "深入思考内容要点，形成自己的见解和观点",
                estimatedDurationDays = (totalDays * 0.3).toInt(),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "不仅要读，更要思考和理解"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "成果输出",
                description = "总结心得体会，完成写作或分享成果",
                estimatedDurationDays = (totalDays * 0.1).toInt().coerceAtLeast(1),
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "输出是最好的学习和巩固方式"
            )
        )
    }
    
    /**
     * 生成财务类子目标
     */
    private fun generateFinancialSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "财务分析",
                description = "分析当前财务状况，制定理财目标",
                estimatedDurationDays = 3,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "详细记录收支情况，了解资金流向"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "预算规划",
                description = "制定详细的预算计划和储蓄方案",
                    estimatedDurationDays = 2,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "合理分配收入，优先保证必要支出"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "执行监控",
                description = "严格执行预算计划，定期监控财务状况",
                estimatedDurationDays = totalDays - 7,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "坚持记账，培养良好的消费习惯"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "目标达成",
                description = "评估财务目标完成情况，制定下一步计划",
                estimatedDurationDays = 2,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "总结经验，为长期财务规划做准备"
            )
        )
    }
    
    /**
     * 生成通用类子目标
     */
    private fun generateGenericSubTasks(goalId: String, title: String, totalDays: Int): List<GoalSubTask> {
        val phaseTime = (totalDays / 4.0).toInt().coerceAtLeast(1)
        
        return listOf(
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "目标分析",
                description = "深入分析目标要求，制定详细的执行策略",
                    estimatedDurationDays = 1,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "明确成功标准，将大目标分解为小步骤"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "准备阶段",
                description = "收集必要资源，做好充分的前期准备工作",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                    status = "待开始",
                aiRecommendation = "充分的准备是成功的一半"
            ),
                GoalSubTask(
                id = UUID.randomUUID().toString(),
                    goalId = goalId,
                title = "核心执行",
                description = "按计划执行目标的核心任务和关键环节",
                estimatedDurationDays = phaseTime * 2,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "保持专注，逐步推进，及时调整策略"
            ),
            GoalSubTask(
                id = UUID.randomUUID().toString(),
                goalId = goalId,
                title = "完善优化",
                description = "检查完成质量，进行最后的完善和优化",
                estimatedDurationDays = phaseTime,
                createdAt = LocalDateTime.now(),
                status = "待开始",
                aiRecommendation = "注重细节，确保目标高质量完成"
            )
        )
    }

    /**
     * 将子任务添加到指定目标
     * @param goalId 目标ID
     * @param subTask 子任务对象
     */
    fun addSubTaskToGoal(goalId: String, subTask: GoalSubTask) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "添加子任务到目标: goalId=$goalId, subTask=${subTask.title}")
                
                // 保存子任务到数据库
                goalRepository.saveSubTask(subTask)
                
                // 🔧 强制刷新子任务列表
                if (_currentGoal.value?.id == goalId) {
                    val refreshedSubTasks = goalRepository.getSubTasksForGoal(goalId)
                    _subTasks.value = refreshedSubTasks
                    Log.d(TAG, "✓ 子目标列表已刷新，当前数量: ${refreshedSubTasks.size}")
                    
                    // 🔧 确保UI状态为成功，触发重组
                    _uiState.value = GoalUiState.Success
                }
                
                Log.d(TAG, "✓ 子任务添加成功")
            } catch (e: Exception) {
                Log.e(TAG, "添加子任务失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "添加子任务失败")
            }
        }
    }

    /**
     * 删除子目标
     */
    fun deleteSubTask(subTaskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始删除子目标: subTaskId=$subTaskId")
                
                // 删除子任务
                goalRepository.deleteSubTask(subTaskId)
                Log.d(TAG, "✓ 子目标删除成功: $subTaskId")
                
                // 🔧 强制刷新子任务列表
                val currentGoalId = _currentGoal.value?.id ?: return@launch
                val refreshedSubTasks = goalRepository.getSubTasksForGoal(currentGoalId)
                _subTasks.value = refreshedSubTasks
                Log.d(TAG, "✓ 子目标列表已刷新，当前数量: ${refreshedSubTasks.size}")
                
                // 更新目标进度
                val allSubTasks = refreshedSubTasks
                if (allSubTasks.isNotEmpty()) {
                    val completedCount = allSubTasks.count { it.completedAt != null }
                    val newProgress = completedCount.toFloat() / allSubTasks.size.toFloat()
                    updateGoalProgress(currentGoalId, newProgress)
                    Log.d(TAG, "✓ 目标进度已更新: $newProgress")
                }
                
                // 🔧 确保UI状态为成功，触发重组
                _uiState.value = GoalUiState.Success
                
            } catch (e: Exception) {
                Log.e(TAG, "删除子目标失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "删除子目标失败")
            }
        }
    }

    /**
     * 更新子目标
     */
    fun updateSubTask(subTask: GoalSubTask) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始更新子目标: subTaskId=${subTask.id}, title=${subTask.title}")
                
                // 更新子任务
                goalRepository.updateSubTask(subTask)
                Log.d(TAG, "✓ 子目标更新成功: ${subTask.title}")
                
                // 🔧 强制刷新子任务列表
                val currentGoalId = _currentGoal.value?.id ?: return@launch
                val refreshedSubTasks = goalRepository.getSubTasksForGoal(currentGoalId)
                _subTasks.value = refreshedSubTasks
                Log.d(TAG, "✓ 子目标列表已刷新，当前数量: ${refreshedSubTasks.size}")
                
                // 🔧 确保UI状态为成功，触发重组
                _uiState.value = GoalUiState.Success
                
            } catch (e: Exception) {
                Log.e(TAG, "更新子目标失败", e)
                _uiState.value = GoalUiState.Error(e.message ?: "更新子目标失败")
            }
        }
    }

    /**
     * 标记目标为已AI拆解
     */
    private suspend fun markGoalAsAiBreakdown(goalId: String) {
        try {
            Log.d(TAG, "标记目标为已AI拆解: goalId=$goalId")
            
            val goal = goalRepository.getGoalById(goalId)
            if (goal != null) {
                val updatedGoal = goal.copy(
                    hasAiBreakdown = true,
                    updatedAt = LocalDateTime.now()
                )
                goalRepository.updateGoal(updatedGoal)
                
                // 如果是当前目标，更新ViewModel状态
                if (_currentGoal.value?.id == goalId) {
                    _currentGoal.value = updatedGoal
                }
                
                Log.d(TAG, "✓ 目标标记为已AI拆解成功")
            } else {
                Log.e(TAG, "❌ 无法找到目标: $goalId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "标记目标为AI拆解失败", e)
            throw e
        }
    }
}

// UI状态
sealed class GoalUiState {
    object Idle : GoalUiState()
    object Loading : GoalUiState()
    object Success : GoalUiState()
    data class Error(val message: String) : GoalUiState()
}

// 拆解状态
sealed class BreakdownState {
    object Idle : BreakdownState()
    
    // 处理中状态带有是否备选模式的标志
    data class Processing(val isBackupMode: Boolean = false) : BreakdownState()
    
    // 显示结果状态带有备选模式标志
    data class ShowingResult(val isBackupMode: Boolean = false) : BreakdownState()
    
    object Completed : BreakdownState()
    data class Error(val message: String) : BreakdownState()
} 