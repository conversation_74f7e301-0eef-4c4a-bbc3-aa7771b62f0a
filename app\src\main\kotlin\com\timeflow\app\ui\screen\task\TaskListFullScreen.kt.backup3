package com.timeflow.app.ui.screen.task

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.regex.Pattern
import kotlin.random.Random
import kotlin.math.max
import kotlin.math.min
import kotlinx.coroutines.*
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.flow.launchIn
import androidx.compose.ui.graphics.graphicsLayer

// 添加导入ThemeManager以访问主题颜色
import com.timeflow.app.ui.theme.ThemeManager
import com.timeflow.app.ui.theme.getPageBackgroundColor

// 添加SwipeToDismiss相关导入
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FractionalThreshold
import androidx.compose.material.SwipeToDismiss
import androidx.compose.material.rememberDismissState

// 添加编辑任务标题相关导入
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable

// 添加TaskTimeManager导入
import com.timeflow.app.ui.screen.task.TaskTimeManager
import com.timeflow.app.ui.screen.task.TaskTimeUpdateEvent

// 添加新的TaskTimeViewModel导入
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel
import com.timeflow.app.ui.screen.task.convertModelTaskDataToUITaskData
import com.timeflow.app.ui.screen.task.convertUITaskDataToModelTaskData
import com.timeflow.app.ui.screen.task.transformUIToModelSubTask

// 添加NotificationCenter和事件相关导入
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent

// Add NoRippleInteractionSource implementation
private object NoRippleInteractionSource : MutableInteractionSource {
    override val interactions: Flow<Interaction> = emptyFlow()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction): Boolean = false
}

// 添加下拉刷新相关导入
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateIntAsState

@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class, ExperimentalComposeApi::class, ExperimentalFoundationApi::class, ExperimentalTypeConversion::class, ExperimentalMaterialApi::class)
@Composable
fun TaskListFullScreen(
    navController: NavController,
    viewModel: TaskListViewModel = hiltViewModel(),
    taskTimeViewModel: TaskTimeViewModel = hiltViewModel() // 使用新的TaskTimeViewModel
) {
    // 获取View和Activity，用于设置窗口属性
    val view = LocalView.current
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 创建TaskTimeManager实例
    val taskTimeManager = remember { TaskTimeManager() }
    
    // 添加缺失的状态变量
    val scaffoldState = rememberScaffoldState()
    var isShowingLoadingDialog by remember { mutableStateOf(false) }
    var loadingMessage by remember { mutableStateOf<String?>(null) }
    var snackbarMessage by remember { mutableStateOf<String?>(null) }
    
    // 使用更安全的状态栏实现 - 移除 SideEffect
    /*
    SideEffect {
        activity?.let { 
            SystemBarManager.setupTaskPageSystemBars(it)
        }
    }
    */
    
    // activity?.let { act -> // 移除 DisposableEffect
    //     DisposableEffect(key1 = Unit) {
    //         val window = act.window
    //         
    //         // 不再修改decorFitsSystemWindows，使用SystemBarManager的标准设置
    //         
    //         // 保存原始值以在dispose时恢复
    //         val originalStatusBarColor = window.statusBarColor
    //         
    //         // 应用任务页面系统栏设置
    //         SystemBarManager.setupTaskPageSystemBars(act)
    //         
    //         onDispose {
    //             // 恢复原始状态栏颜色
    //             window.statusBarColor = originalStatusBarColor
    //             Log.d("TaskListFullScreen", "TaskListFullScreen disposed")
    //         }
    //     }
    // }

    var selectedFilter by remember { mutableStateOf("全部") }
    val filters = listOf("全部", "今天", "明天", "本周", "未定期")
    
    // 添加任务完成状态筛选模式
    var completionFilterMode by remember { mutableStateOf(CompletionFilterMode.SHOW_ALL) }
    // 添加排序方式
    var sortMode by remember { mutableStateOf(TaskSortMode.NONE) }
    // 添加下拉菜单显示状态
    var showMenu by remember { mutableStateOf(false) }
    
    // 底部弹出层状态
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false)
    var showBottomSheet by remember { mutableStateOf(false) }
    var selectedTask by remember { mutableStateOf<ModelTaskData?>(null) }

    // 直接从ViewModel获取任务列表状态
    val taskListState by viewModel.taskListState.collectAsState()
    // 使用MutableStateList以便能够修改列表内容 - REMOVED
    // val taskList = remember { mutableStateListOf<ModelTaskData>() }
    
    // 添加加载状态 - 从ViewModel状态获取
    // var isLoading by remember { mutableStateOf(true) } - REMOVED, use taskListState.isLoading
    val coroutineScope = rememberCoroutineScope()
    
    // 从ThemeManager获取背景色 - 修复不正确的单例获取方法
    // 直接使用ThemeManager对象，无需调用getInstance()
    // 监听主题偏好变化，确保主题更改时界面会重组
    val themePreference = ThemeManager.userThemePreference.collectAsState()
    // 使用getPageBackgroundColor获取页面专属背景色，确保与主页保持一致
    val backgroundColor = getPageBackgroundColor("home")
    
    // 添加页面进入动画状态
    var pageAnimationStarted by remember { mutableStateOf(false) }
    var showContent by remember { mutableStateOf(false) }
    
    // 页面进入动画
    LaunchedEffect(Unit) {
        pageAnimationStarted = true
        delay(50) // 短暂延迟让动画开始
        showContent = true
    }
    
    // 添加内容透明度动画
    val contentAlpha by animateFloatAsState(
        targetValue = if (showContent) 1f else 0f,
        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing),
        label = "contentAlpha"
    )
        
    // 添加列表项进入动画的延迟状态
    var itemAnimationTrigger by remember { mutableStateOf(0) }
    
    // 💡 美化的刷新状态管理 - 参考知名app设计
    var isRefreshing by remember { mutableStateOf(false) }
    var lastRefreshTime by remember { mutableStateOf(0L) }
    var refreshDebounceJob: Job? by remember { mutableStateOf(null) }
    val minimumRefreshInterval = 800L // 减少刷新间隔，提升响应速度
    
    // 💡 优雅的刷新动画 - 类似微信的下拉刷新
    val refreshAnimationDuration = 600
    val refreshIndicatorColor by animateColorAsState(
        targetValue = if (isRefreshing) MaterialTheme.colorScheme.primary else Color.Gray.copy(alpha = 0.6f),
        animationSpec = tween(refreshAnimationDuration / 2, easing = FastOutSlowInEasing),
        label = "refreshIndicatorColor"
    )
    
    // 💡 微动画状态 - 参考Notion的流畅过渡
    val listAlpha = animateFloatAsState(
        targetValue = if (isRefreshing) 0.85f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "listAlpha"
    )
    
    val listScale = animateFloatAsState(
        targetValue = if (isRefreshing) 0.98f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "listScale"
    )
    
    // 💡 智能防抖刷新函数 - 优化用户体验
    fun performRefresh(reason: String = "用户下拉") {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastRefresh = currentTime - lastRefreshTime
        
        Log.d("SmartRefresh", "🔄 刷新请求: $reason, 距离上次: ${timeSinceLastRefresh}ms")
        
        // 取消之前的防抖任务
        refreshDebounceJob?.cancel()
        
        if (timeSinceLastRefresh >= minimumRefreshInterval) {
            // 立即刷新，添加优雅的UI反馈
            lastRefreshTime = currentTime
            isRefreshing = true
            
            coroutineScope.launch {
                try {
                    Log.d("SmartRefresh", "✨ 开始执行刷新: $reason")
                    viewModel.refreshTasks()
                    
                    // 💡 确保最小刷新时间，避免闪烁
                    delay(600) // 最小刷新动画持续时间
                    
                    Log.d("SmartRefresh", "✅ 刷新完成: $reason")
                } catch (e: Exception) {
                    Log.e("SmartRefresh", "❌ 刷新失败: $reason", e)
                } finally {
                    isRefreshing = false
                }
            }
        } else {
            // 防抖延迟，提升用户体验
            val delayTime = minimumRefreshInterval - timeSinceLastRefresh
            Log.d("SmartRefresh", "⏳ 防抖延迟刷新: ${delayTime}ms")
            
            refreshDebounceJob = coroutineScope.launch {
                delay(delayTime)
                lastRefreshTime = System.currentTimeMillis()
                isRefreshing = true
                
                try {
                    Log.d("SmartRefresh", "🔄 防抖刷新执行: $reason")
                    viewModel.refreshTasks()
                    delay(600)
                } catch (e: Exception) {
                    Log.e("SmartRefresh", "❌ 防抖刷新失败", e)
                } finally {
                    isRefreshing = false
                }
            }
        }
    }
    
    // 💡 下拉刷新状态 - 参考TickTick的交互设计
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing,
        onRefresh = { performRefresh("用户下拉刷新") }
    )

    // 启动协程处理异步任务和定时刷新
    LaunchedEffect(Unit) {
        // 当任务加载完成后触发列表项动画
        launch {
            viewModel.taskListState.collect { state ->
                Log.d("TaskListFullScreen", "收到任务列表状态更新: isLoading=${state.isLoading}, 任务数量=${state.tasks.size}")
                
                // 💡 同步ViewModel的loading状态到UI刷新状态
                if (!state.isLoading && isRefreshing) {
                    Log.d("TaskListFullScreen", "✅ ViewModel加载完成，结束UI刷新状态")
                    isRefreshing = false
                }
                
                if (!state.isLoading && state.tasks.isNotEmpty()) {
                    // 数据加载完成，触发列表项动画
                    itemAnimationTrigger = state.tasks.size
                }
                if (!state.isLoading && state.tasks.isEmpty()) {
                    Log.w("TaskListFullScreen", "任务列表为空，检查数据源")
                }
            }
        }

        // 💡 优化定时刷新 - 延长间隔，减少频繁刷新
        while(true) {
            delay(60000) // 改为60秒，减少刷新频率
            Log.d("TaskListFullScreen", "执行定时刷新")
            smartRefresh("定时刷新")
        }
    }

    // 优化列表渲染性能
    val listState = rememberLazyListState()
    
    // 使用 derivedStateOf 优化过滤和排序计算 - 直接使用 taskListState.tasks
    val filteredTasks by remember(selectedFilter, taskListState.tasks, completionFilterMode, sortMode) {
        derivedStateOf {
            // 添加日志记录初始任务数量
            val initialTaskCount = taskListState.tasks.size
            Log.d("TaskListFullScreen", "过滤前任务总数: $initialTaskCount")
            
            // 检查是否有AI生成的任务
            val aiTasks = taskListState.tasks.filter { task -> 
                task.aiGenerated == true 
            }
            if (aiTasks.isNotEmpty()) {
                Log.d("TaskListFullScreen", "检测到 ${aiTasks.size} 个AI生成的任务")
                aiTasks.forEach { task ->
                    Log.d("TaskListFullScreen", "AI任务: id=${task.id}, 标题=${task.title}, 父任务ID=${task.parentTaskId}, 显示=${task.displayInTaskList}")
                }
            }
            
            // 第一步过滤：基于displayInTaskList属性，但添加例外情况
            val tasksAfterDisplayFilter = taskListState.tasks.filter { task -> 
                // 基本过滤条件
                val shouldDisplay = task.displayInTaskList
                
                // 对于AI生成的任务，如果是主任务（没有父任务），总是显示
                val isAiTask = task.aiGenerated == true
                val isParentTask = task.parentTaskId == null
                
                // 最终决定是否显示该任务
                val finalDecision = shouldDisplay || (isAiTask && isParentTask)
                
                // 记录被过滤掉的AI任务
                if (!finalDecision && isAiTask) {
                    Log.d("TaskListFullScreen", "AI任务 ${task.id} (${task.title}) 被过滤掉，显示设置=${task.displayInTaskList}")
                }
                
                finalDecision
            }
            
            Log.d("TaskListFullScreen", "displayInTaskList过滤后任务数: ${tasksAfterDisplayFilter.size}")
            
            val tasksAfterCompletionFilter = tasksAfterDisplayFilter.filter { task ->
                val passesCompletionFilter = when (completionFilterMode) {
                    CompletionFilterMode.HIDE_COMPLETED -> !task.isCompleted
                    CompletionFilterMode.SHOW_ONLY_COMPLETED -> task.isCompleted
                    else -> true // SHOW_ALL
                }
                if (!passesCompletionFilter) {
                    Log.d("TaskListFullScreen", "任务 ${task.id} (${task.title}) 被完成状态过滤掉, 当前过滤模式: $completionFilterMode, 任务完成状态: ${task.isCompleted}")
                }
                passesCompletionFilter
            }
            
            Log.d("TaskListFullScreen", "完成状态过滤后任务数: ${tasksAfterCompletionFilter.size}")
            
            val tasksAfterDateFilter = tasksAfterCompletionFilter.filter { task ->
                val passesDateFilter = when (selectedFilter) {
                    "今天" -> isTaskForToday(task)
                    "明天" -> isTaskForTomorrow(task)
                    "本周" -> isTaskForThisWeek(task)
                    "未定期" -> isTaskUpcoming(task)
                    else -> true
                }
                if (!passesDateFilter) {
                    Log.d("TaskListFullScreen", "任务 ${task.id} (${task.title}) 被日期过滤掉, 当前过滤: $selectedFilter, 任务天数: ${task.daysLeft}")
                }
                passesDateFilter
            }
            
            Log.d("TaskListFullScreen", "日期过滤后任务数: ${tasksAfterDateFilter.size}, 当前日期过滤: $selectedFilter")
            
            // 确保AI生成的任务在过滤后仍然存在
            val aiTasksAfterFilter = tasksAfterDateFilter.filter { it.aiGenerated == true }
            if (aiTasks.size != aiTasksAfterFilter.size) {
                Log.w("TaskListFullScreen", "警告: 过滤前有 ${aiTasks.size} 个AI任务，过滤后只剩 ${aiTasksAfterFilter.size} 个")
                val missingTasks = aiTasks.filter { aiTask -> !tasksAfterDateFilter.any { it.id == aiTask.id } }
                missingTasks.forEach { task ->
                    Log.w("TaskListFullScreen", "被过滤掉的AI任务: id=${task.id}, 标题=${task.title}, 完成状态=${task.isCompleted}")
                }
            }
            
            val sortedTasks = when (sortMode) {
                TaskSortMode.PRIORITY_HIGH_TO_LOW -> tasksAfterDateFilter.sortedByDescending { 
                    when(it.urgency) {
                        TaskUrgency.CRITICAL -> 4
                        TaskUrgency.HIGH -> 3
                        TaskUrgency.MEDIUM -> 2
                        TaskUrgency.LOW -> 1
                    }
                }
                TaskSortMode.TIME_RECENT_TO_EARLIEST -> tasksAfterDateFilter.sortedBy { it.daysLeft }
                else -> tasksAfterDateFilter // Keep original order if no sort applied or NONE
            }
            
            Log.d("TaskListFullScreen", "最终显示的任务数: ${sortedTasks.size}")
            sortedTasks
        }
    }
    
    // 处理标签更新 - 💡 优化版
    val handleTagsUpdated: (String, List<String>) -> Unit = { taskId, updatedTags ->
        Log.d("TaskListFullScreen", "开始更新任务标签: taskId=$taskId, tags=$updatedTags")
        
        coroutineScope.launch {
            try {
                // 调用TaskListViewModel的updateTaskTags方法保存标签
                viewModel.updateTaskTags(taskId, updatedTags)
                
                Log.d("TaskListFullScreen", "任务标签更新成功: $taskId")
                
                // **关键修复**: 立即更新selectedTask的标签，确保UI即时显示
                if (selectedTask?.id == taskId) {
                    selectedTask = selectedTask?.copy(customTags = updatedTags)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask的标签显示")
                }
                
                // **新增**: 强制清除缓存，确保下次获取最新数据
                try {
                    // 清除TaskRepository缓存
                    viewModel.clearTaskCache(taskId)
                    Log.d("TaskListFullScreen", "已清除任务缓存")
                } catch (e: Exception) {
                    Log.w("TaskListFullScreen", "清除缓存失败，但继续执行", e)
                }
                
                // 💡 使用智能防抖刷新，延迟降低
                delay(150) // 减少延迟时间
                smartRefresh("标签更新-$taskId")
                
                // 发送全局刷新事件
                NotificationCenter.post(TaskRefreshEvent(taskId))
                
                Log.d("TaskListFullScreen", "标签更新完成，已请求智能刷新")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新任务标签失败: taskId=$taskId", e)
                snackbarMessage = "更新任务标签失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理紧急程度更新 - 强化数据库保存机制
    val handleUrgencyUpdated: (String, TaskUrgency) -> Unit = { taskId: String, updatedUrgency: TaskUrgency ->
        Log.d("PriorityUpdate", "[TaskListFullScreen] ===== 开始处理紧急程度更新 =====")
        Log.d("PriorityUpdate", "[TaskListFullScreen] 任务ID: $taskId")
        Log.d("PriorityUpdate", "[TaskListFullScreen] 新紧急程度: $updatedUrgency")
        
        coroutineScope.launch {
            try {
                // 将紧急程度转换为优先级
                val priority = when(updatedUrgency) {
                    TaskUrgency.CRITICAL -> Priority.URGENT
                    TaskUrgency.HIGH -> Priority.HIGH
                    TaskUrgency.MEDIUM -> Priority.MEDIUM
                    TaskUrgency.LOW -> Priority.LOW
                }
                Log.d("PriorityUpdate", "[TaskListFullScreen] 转换后的优先级: $priority")
                
                // 立即更新selectedTask的显示
                val taskIndex = taskListState.tasks.indexOfFirst { it.id == taskId }
                if (taskIndex >= 0) {
                    if (selectedTask?.id == taskId) {
                        val task = taskListState.tasks[taskIndex]
                        selectedTask = task.copy(urgency = updatedUrgency)
                        Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ selectedTask显示已立即更新")
                    }
                }
                
                // 调用TaskListViewModel保存到数据库
                Log.d("PriorityUpdate", "[TaskListFullScreen] 开始调用ViewModel保存到数据库...")
                try {
                    viewModel.updateTaskPriority(taskId, priority)
                    Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ ViewModel.updateTaskPriority调用成功")
                } catch (e: Exception) {
                    Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ ViewModel.updateTaskPriority调用失败", e)
                    throw e
                }
                
                // 延迟刷新确保数据库更新完成
                Log.d("PriorityUpdate", "[TaskListFullScreen] 等待数据库更新完成...")
                delay(250)
                
                // 强制刷新任务列表
                Log.d("PriorityUpdate", "[TaskListFullScreen] 开始刷新任务列表...")
                try {
                    viewModel.refreshTasks()
                    Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ 任务列表刷新完成")
                } catch (e: Exception) {
                    Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ 任务列表刷新失败", e)
                }
                
                // 发送刷新事件通知其他组件
                Log.d("PriorityUpdate", "[TaskListFullScreen] 发送全局刷新事件...")
                NotificationCenter.post(TaskRefreshEvent(taskId))
                Log.d("PriorityUpdate", "[TaskListFullScreen] ✓ 全局刷新事件已发送")
                
                Log.d("PriorityUpdate", "[TaskListFullScreen] ===== 优先级更新流程完成 =====")
                
            } catch (e: Exception) {
                Log.e("PriorityUpdate", "[TaskListFullScreen] ✗ 优先级更新流程失败", e)
                snackbarMessage = "更新优先级失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 创建优先级更新处理函数 - 增强处理逻辑
    val handlePriorityUpdate: (String, Priority) -> Unit = { id, priority ->
        Log.d("PriorityUpdate", "接收到优先级更新请求: taskId=$id, priority=$priority")
        
        val urgency = when(priority) {
            Priority.HIGH -> TaskUrgency.HIGH
            Priority.MEDIUM -> TaskUrgency.MEDIUM
            Priority.LOW -> TaskUrgency.LOW
            Priority.URGENT -> TaskUrgency.CRITICAL
            else -> TaskUrgency.MEDIUM
        }
        
        // 直接调用紧急程度更新处理
        handleUrgencyUpdated(id, urgency)
    }
    
    // 创建子任务添加处理函数
    val handleSubTaskAdd: (String, com.timeflow.app.ui.screen.task.SubTask) -> Unit = { parentId, uiSubTask ->
        val modelSubTask = transformUIToModelSubTask(parentId, uiSubTask)
        Log.d("TaskListFullScreen", "开始添加子任务: parentId=$parentId, title=${modelSubTask.title}")
        
        coroutineScope.launch {
            try {
                viewModel.addSubTask(parentId, modelSubTask.title)
                Log.d("TaskListFullScreen", "子任务添加成功: ${modelSubTask.title}")
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                Log.d("TaskListFullScreen", "子任务添加完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "添加子任务失败: parentId=$parentId", e)
                snackbarMessage = "添加子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理子任务更新 - 修复实现
    val handleSubTaskUpdated: (String, com.timeflow.app.ui.screen.task.SubTask) -> Unit = { parentId, updatedSubTask ->
        Log.d("TaskListFullScreen", "开始更新子任务: parentId=$parentId, subTaskId=${updatedSubTask.id}, isCompleted=${updatedSubTask.isCompleted}")
        
        coroutineScope.launch {
            try {
                viewModel.updateTaskStatus(updatedSubTask.id, updatedSubTask.isCompleted)
                Log.d("TaskListFullScreen", "子任务状态更新成功: ${updatedSubTask.id}")
                
                // **关键修复**: 立即更新selectedTask中的对应子任务
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val updatedSubTasks = currentSubTasks.map { subTask ->
                        if (subTask.id == updatedSubTask.id) {
                            subTask.copy(
                                title = updatedSubTask.title,
                                isCompleted = updatedSubTask.isCompleted,
                                priority = updatedSubTask.priority ?: Priority.MEDIUM
                            )
                        } else {
                            subTask
                        }
                    }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask中的子任务状态")
                }
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务更新已同步")
                }
                
                Log.d("TaskListFullScreen", "子任务更新完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新子任务失败: parentId=$parentId, subTaskId=${updatedSubTask.id}", e)
                snackbarMessage = "更新子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 创建任务完成处理函数
    val handleTaskComplete: (String) -> Unit = { taskId ->
        coroutineScope.launch {
            try {
                viewModel.updateTaskStatus(taskId, true)
                Log.d("TaskListFullScreen", "请求更新任务状态: taskId=$taskId, isCompleted=true")
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新任务状态请求失败: taskId=$taskId", e)
                snackbarMessage = "更新任务状态失败，请重试"
            }
        }
    }
    
    // 创建任务时间更新处理函数
    val handleTaskTimeUpdate: (String, LocalDateTime?, LocalDateTime?) -> Unit = { taskId, startDateTime, endDateTime ->
        Log.d("TimeSync", "=== 开始处理时间更新 ===")
        Log.d("TimeSync", "任务ID: $taskId")
        Log.d("TimeSync", "开始时间: $startDateTime")
        Log.d("TimeSync", "结束时间: $endDateTime")
        
        coroutineScope.launch {
            try {
                if (selectedTask?.id == taskId && startDateTime != null) {
                    selectedTask = selectedTask?.copy(
                        dueDate = startDateTime,
                        daysLeft = calculateDaysLeft(startDateTime)
                    )
                    Log.d("TimeSync", "✓ 已更新选中任务显示")
                }
                
                if (startDateTime != null) {
                    viewModel.updateTaskTime(taskId, startDateTime)
                    Log.d("TimeSync", "✓ 已调用TaskListViewModel更新")
                    
                    delay(100)
                    
                    taskTimeViewModel.updateTaskTime(
                        taskId = taskId,
                        startTime = startDateTime,
                        endTime = endDateTime,
                        dueDate = startDateTime,
                        source = "TaskListFullScreen_Sync"
                    )
                    Log.d("TimeSync", "✓ 已调用TaskTimeViewModel同步")
                }
                
                delay(150)
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TimeSync", "时间更新处理失败", e)
            }
        }
    }
    
    // 创建任务更新处理函数 - 💡 优化版
    val handleTaskUpdate: (TaskData) -> Unit = { updatedTaskData ->
        val updatedModelTask = convertUITaskDataToModelTaskData(updatedTaskData)
        selectedTask = updatedModelTask
        Log.d("TaskSync", "[TaskListFullScreen] 已更新selectedTask: ${updatedModelTask.title}")
        
        // 💡 使用智能防抖刷新替代立即刷新
        smartRefresh("任务更新-${updatedModelTask.id}")
    }
    
    // 创建任务反馈提交处理函数
    val handleFeedbackSubmitted: (String, com.timeflow.app.ui.screen.task.model.FeedbackData) -> Unit = { taskId, feedback ->
        viewModel.submitTaskFeedback(taskId, feedback)
        android.util.Log.d("TaskListFullScreen", "任务反馈已提交: taskId=$taskId, comment=${feedback.comment?.take(20)}...")
    }
    
    // 显示反馈对话框的状态
    var showFeedbackDialog by remember { mutableStateOf(false) }
    var feedbackTaskId by remember { mutableStateOf<String?>(null) }
    var feedbackTaskTitle by remember { mutableStateOf("") }
    
    // 处理子任务添加 - 修复实现
    val handleSubTaskAdded: (String, SubTask) -> Unit = { parentId, subTask ->
        Log.d("TaskListFullScreen", "开始添加子任务: parentId=$parentId, title=${subTask.title}")
        
        coroutineScope.launch {
            try {
                // 调用TaskListViewModel的addSubTask方法保存子任务
                viewModel.addSubTask(parentId, subTask.title)
                
                Log.d("TaskListFullScreen", "子任务添加成功: ${subTask.title}")
                
                // **关键修复**: 立即更新selectedTask的子任务列表，确保UI即时显示
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val newSubTask = SubTask(
                        id = subTask.id,
                        title = subTask.title,
                        isCompleted = subTask.isCompleted,
                        priority = subTask.priority ?: Priority.MEDIUM
                    )
                    val updatedSubTasks = currentSubTasks.toMutableList().apply { add(newSubTask) }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask的子任务列表")
                }
                
                // 延迟一小段时间确保数据库写入完成
                delay(100)
                
                // 强制刷新任务列表以显示新的子任务
                viewModel.refreshTasks()
                
                // 发送全局刷新事件
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 等待任务列表刷新完成后，再次确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务已更新为最新数据")
                }
                
                Log.d("TaskListFullScreen", "子任务添加完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "添加子任务失败: parentId=$parentId", e)
                snackbarMessage = "添加子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理子任务删除 - 修复实现
    val handleSubTaskDeleted: (String, String) -> Unit = { parentId, subTaskId ->
        Log.d("TaskListFullScreen", "开始删除子任务: parentId=$parentId, subTaskId=$subTaskId")
        
        coroutineScope.launch {
            try {
                // 调用ViewModel删除子任务
                viewModel.deleteSubTask(parentId, subTaskId)
                Log.d("TaskListFullScreen", "子任务删除成功: $subTaskId")
                
                // **关键修复**: 立即更新selectedTask中的子任务列表
                if (selectedTask?.id == parentId) {
                    val currentSubTasks = selectedTask?.subTasks ?: emptyList()
                    val updatedSubTasks = currentSubTasks.filter { it.id != subTaskId }
                    selectedTask = selectedTask?.copy(subTasks = updatedSubTasks)
                    Log.d("TaskListFullScreen", "已立即更新selectedTask，移除已删除的子任务")
                }
                
                delay(100)
                viewModel.refreshTasks()
                
                NotificationCenter.post(TaskRefreshEvent(parentId))
                
                // **额外的同步步骤**: 确保selectedTask是最新的
                delay(200)
                val refreshedTask = viewModel.taskListState.value.tasks.find { it.id == parentId }
                if (refreshedTask != null && selectedTask?.id == parentId) {
                    selectedTask = refreshedTask
                    Log.d("TaskListFullScreen", "二次同步完成，selectedTask的子任务删除已同步")
                }
                
                Log.d("TaskListFullScreen", "子任务删除完成，已刷新任务列表")
                
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "删除子任务失败: parentId=$parentId, subTaskId=$subTaskId", e)
                snackbarMessage = "删除子任务失败: ${e.localizedMessage ?: "未知错误"}"
            }
        }
    }
    
    // 处理任务状态更新（包括子任务状态联动） - 只调用ViewModel
    val handleTaskStatusChanged: (String, Boolean) -> Unit = { taskId, isCompleted ->
        // REMOVE direct list modification

        // JUST call the ViewModel method:
        coroutineScope.launch {
            try {
                viewModel.updateTaskStatus(taskId, isCompleted)
                Log.d("TaskListFullScreen", "请求更新任务状态: taskId=$taskId, isCompleted=$isCompleted")
            } catch (e: Exception) {
                Log.e("TaskListFullScreen", "更新任务状态请求失败: taskId=$taskId", e)
                 snackbarMessage = "更新任务状态失败，请重试"
            }
        }
        // REMOVE the logic for updating subtasks/parent tasks locally.
    }
    
    // 处理任务反馈提交 - 同上
    val handleFeedbackSubmitted_duplicate: (String, com.timeflow.app.ui.screen.task.model.FeedbackData) -> Unit = { taskId, feedback ->
        // 调用ViewModel的submitTaskFeedback方法
        viewModel.submitTaskFeedback(taskId, feedback)
        android.util.Log.d("TaskListFullScreen", "任务反馈已提交: taskId=$taskId, comment=${feedback.comment?.take(20)}...")
    }
    
    // 处理任务时间更新 - 彻底修复的时间更新处理机制
    val handleTaskTimeUpdated: (String, LocalDateTime?, LocalDateTime?) -> Unit = { taskId, startDateTime, endDateTime ->
        Log.d("TimeSync", "=== 开始处理时间更新 ===")
        Log.d("TimeSync", "任务ID: $taskId")
        Log.d("TimeSync", "开始时间: $startDateTime")
        Log.d("TimeSync", "结束时间: $endDateTime")
        
        coroutineScope.launch {
            try {
                // 1. 立即更新选中任务的显示（UI层面的即时反馈）
                if (selectedTask?.id == taskId && startDateTime != null) {
                    selectedTask = selectedTask?.copy(
                        dueDate = startDateTime,
                        daysLeft = calculateDaysLeft(startDateTime)
                    )
                    Log.d("TimeSync", "✓ 已更新选中任务显示")
                }
                
                // 2. 关键修复：确保TaskRepository也被更新
                if (startDateTime != null) {
                    // 先调用TaskListViewModel更新主数据源
                    viewModel.updateTaskTime(taskId, startDateTime)
                    Log.d("TimeSync", "✓ 已调用TaskListViewModel更新")
                    
                    // 等待数据库更新完成
                    delay(100)
                    
                    // 然后调用TaskTimeViewModel确保时间管理系统同步
                    taskTimeViewModel.updateTaskTime(
                        taskId = taskId,
                        startTime = startDateTime,
                        endTime = endDateTime,
                        dueDate = startDateTime,
                        source = "TaskListFullScreen_Sync"
                    )
                    Log.d("TimeSync", "✓ 已调用TaskTimeViewModel同步")
                }
                
                // 3. 强制刷新任务列表（确保UI同步）
                delay(150) // 增加延迟确保数据库更新完成
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已刷新任务列表")
                
                // 4. 额外的强制刷新机制
                delay(200)
                viewModel.refreshTasks()
                Log.d("TimeSync", "✓ 已进行第二次刷新确保同步")
                
                // 5. 发送全局事件确保其他组件也能收到更新
                try {
                    com.timeflow.app.util.NotificationCenter.post(
                        com.timeflow.app.util.TaskRefreshEvent(taskId)
                    )
                    Log.d("TimeSync", "✓ 已发送全局刷新事件")
                } catch (e: Exception) {
                    Log.e("TimeSync", "发送全局事件失败", e)
                }
                
                Log.d("TimeSync", "=== 时间更新处理完成 ===")
                
            } catch (e: Exception) {
                Log.e("TimeSync", "时间更新处理失败", e)
            }
        }
    }
    
    // 监听TaskListViewModel的任务列表变化 - 主要的数据同步机制
    LaunchedEffect(Unit) {
        viewModel.taskListState.collect { state ->
            if (!state.isLoading && state.tasks.isNotEmpty()) {
                // 检查选中任务是否需要更新
                selectedTask?.let { currentSelected ->
                    val updatedTask = state.tasks.find { it.id == currentSelected.id }
                    if (updatedTask != null && updatedTask.dueDate != currentSelected.dueDate) {
                        selectedTask = updatedTask
                        Log.d("TimeSync", "从任务列表同步更新了选中任务时间: ${updatedTask.dueDate}")
                    }
                }
            }
        }
    }
    
    // 监听TaskTimeViewModel的时间更新事件 - 立即响应机制
    LaunchedEffect(Unit) {
        taskTimeViewModel.timeUpdateEvents.collect { event ->
            Log.d("TimeSync", "收到TaskTimeViewModel时间更新事件: taskId=${event.taskId}, 新时间=${event.newTime.effectiveTime}")
            
            // 立即更新选中任务的时间显示
            if (selectedTask?.id == event.taskId) {
                selectedTask = selectedTask?.copy(
                    dueDate = event.newTime.effectiveTime,
                    daysLeft = event.newTime.daysLeft
                )
                Log.d("TimeSync", "已更新选中任务时间显示: ${event.newTime.effectiveTime}")
            }
            
            // 立即强制刷新任务列表以显示最新时间
            viewModel.refreshTasks()
            Log.d("TimeSync", "已强制刷新任务列表响应时间更新")
            
            // 额外的延迟刷新确保数据同步
            delay(300)
            viewModel.refreshTasks()
            Log.d("TimeSync", "已进行延迟刷新确保数据完全同步")
        }
    }
    
    // 监听TaskListViewModel的状态变化 - 确保UI及时更新
    LaunchedEffect(Unit) {
        viewModel.taskListState.collect { state ->
            Log.d("TaskListFullScreen", "收到任务列表状态更新: isLoading=${state.isLoading}, 任务数量=${state.tasks.size}")
            
            // 如果有选中的任务，检查是否需要更新其时间显示
            selectedTask?.let { currentSelected ->
                val updatedTask = state.tasks.find { it.id == currentSelected.id }
                if (updatedTask != null && updatedTask.dueDate != currentSelected.dueDate) {
                    selectedTask = updatedTask
                    Log.d("TimeSync", "从任务列表状态更新中同步了选中任务时间: 旧时间=${currentSelected.dueDate}, 新时间=${updatedTask.dueDate}")
                }
            }
        }
    }
    
    // 💡 优化全局任务刷新事件处理 - 使用防抖和平滑动画
    LaunchedEffect(Unit) {
        com.timeflow.app.util.NotificationCenter.events.collect { event ->
            if (event is com.timeflow.app.util.TaskRefreshEvent) {
                Log.d("TaskSync", "[TaskListFullScreen] 收到全局任务刷新事件: ${event.taskId}")
                
                try {
                    // 💡 使用智能防抖刷新，而不是立即刷新
                    Log.d("TaskSync", "[TaskListFullScreen] 开始智能刷新任务列表...")
                    smartRefresh("全局事件-${event.taskId}")
                    Log.d("TaskSync", "[TaskListFullScreen] ✓ 任务列表刷新请求已提交")
                    
                    // 如果是当前选中的任务，使用更温和的更新方式
                    if (selectedTask?.id == event.taskId) {
                        Log.d("TaskSync", "[TaskListFullScreen] 检测到当前选中任务的更新事件")
                        
                        // 💡 添加微动画过渡效果
                        coroutineScope.launch {
                            // 延迟获取更新，减少抖动
                            delay(300) // 给刷新时间
                            
                            val updatedTask = viewModel.taskListState.value.tasks.find { it.id == event.taskId }
                            if (updatedTask != null) {
                                selectedTask = updatedTask
                                Log.d("TaskSync", "[TaskListFullScreen] ✓ 平滑更新selectedTask: ${updatedTask.title}")
                                Log.d("TaskSync", "[TaskListFullScreen] 新的紧急程度: ${updatedTask.urgency}")
                            } else {
                                Log.w("TaskSync", "[TaskListFullScreen] ⚠ 未找到更新后的任务")
                            }
                        }
                    }
                    
                    Log.d("TaskSync", "[TaskListFullScreen] 全局刷新事件处理完成")
                } catch (e: Exception) {
                    Log.e("TaskSync", "[TaskListFullScreen] 处理全局刷新事件失败", e)
                }
            }
        }
    }
    
    // 💡 优化任务状态监听 - 减少不必要的重组
    LaunchedEffect(viewModel.taskListState) {
        viewModel.taskListState.collect { state ->
            // 💡 只在关键状态变化时记录日志，减少日志噪音
            if (state.isLoading) {
                Log.d("TaskListFullScreen", "任务列表开始加载...")
            } else {
                Log.d("TaskListFullScreen", "任务列表加载完成: ${state.tasks.size}个任务")
            }
            
            // 如果有选中的任务，确保它是最新的
            selectedTask?.let { currentSelected ->
                val updatedTask = state.tasks.find { it.id == currentSelected.id }
                if (updatedTask != null && updatedTask != currentSelected) {
                    selectedTask = updatedTask
                    Log.d("TaskSync", "[TaskListFullScreen] selectedTask已静默更新")
                }
            }
        }
    }
    
    // 显示任务详情底部弹出层
    if (showBottomSheet && selectedTask != null) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissBottomSheet() {
            showBottomSheet = false
        }
        
        fun navigateToEdit() {
            showBottomSheet = false
            navController.navigate(AppDestinations.taskEditRoute(selectedTask!!.id))
        }
        
        ModalBottomSheet(
            onDismissRequest = ::dismissBottomSheet,
            sheetState = sheetState,
            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
            containerColor = Color.White
        ) {
            TaskDetailBottomSheet(
                task = convertModelTaskDataToUITaskData(selectedTask!!),
                onEditClick = ::navigateToEdit,
                onDismiss = ::dismissBottomSheet,
                onTagsUpdated = handleTagsUpdated, // 处理标签更新
                onPriorityUpdated = handlePriorityUpdate,
                onSubTaskAdded = handleSubTaskAdd,
                onSubTaskUpdated = handleSubTaskUpdated,
                onSubTaskDeleted = handleSubTaskDeleted, // 处理子任务删除
                onTaskCompleted = handleTaskComplete,
                onTaskTimeUpdated = handleTaskTimeUpdate,
                navController = navController, // 添加导航控制器参数
                onTaskUpdated = handleTaskUpdate // 使用函数引用避免实验性功能问题
            )
        }
    }
    
    // 显示反馈对话框
    if (showFeedbackDialog && feedbackTaskId != null) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissFeedbackDialog() {
            showFeedbackDialog = false 
            feedbackTaskId = null
        }
        
        fun submitFeedback(feedback: com.timeflow.app.ui.screen.task.model.FeedbackData) {
            feedbackTaskId?.let { taskId ->
                handleFeedbackSubmitted(taskId, feedback)
            }
            showFeedbackDialog = false
            feedbackTaskId = null
        }
        
        TaskFeedbackDialog(
            onDismiss = ::dismissFeedbackDialog,
            onSubmit = ::submitFeedback,
            taskTitle = feedbackTaskTitle
        )
    }

    // 添加语音输入底部弹窗状态
    var showVoiceInputSheet by remember { mutableStateOf(false) }

    // 将语音识别相关变量提升到父作用域
    var isListening by remember { mutableStateOf(false) }
    var speechRecognizer by remember { mutableStateOf<SpeechRecognizer?>(null) }
    var recognizedText by remember { mutableStateOf("") }
    var audioLevel by remember { mutableStateOf(0f) }
    var speechListenerJob: Job? by remember { mutableStateOf(null) }
    var isHolding by remember { mutableStateOf(false) }
    var pendingVoiceText by remember { mutableStateOf<String?>(null) }
    
    // 模拟音量波动的动画值
    val waveAmplitudes = remember { List(50) { mutableStateOf(0.1f) } }
    
    // 语音图标状态动画
    val micScale = animateFloatAsState(
        targetValue = if (isListening) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "micScale"
    )
    
    // 处理待处理的语音文本
    LaunchedEffect(pendingVoiceText) {
        pendingVoiceText?.let { text ->
            if (text.isNotBlank()) {
                // 延迟一帧，确保UI已更新
                delay(16) // 等待约一帧的时间
                
                // 设置标志表示有文本需要处理
                showVoiceInputSheet = false // 先关闭语音输入界面
                
                // 不直接调用Composable函数，而是设置状态让组合函数处理
                // 处理过程仍然继续，但不直接从这里调用Composable
            }
        }
    }

    // 底部弹出层显示语音输入界面
    if (showVoiceInputSheet) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissVoiceInputSheet() {
            showVoiceInputSheet = false
            speechRecognizer?.destroy()
            speechRecognizer = null
            speechListenerJob?.cancel()
            recognizedText = ""
        }
        
        fun closeVoiceInput() {
            showVoiceInputSheet = false
            speechRecognizer?.destroy()
            speechRecognizer = null
            speechListenerJob?.cancel()
            recognizedText = ""
        }
        
        ModalBottomSheet(
            onDismissRequest = ::dismissVoiceInputSheet,
            sheetState = rememberModalBottomSheetState(),
            shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
            containerColor = Color.White,
            dragHandle = {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Box(
                        modifier = Modifier
                            .width(40.dp)
                            .height(4.dp)
                            .background(
                                color = Color(0xFFE0E0E0),
                                shape = RoundedCornerShape(2.dp)
                            )
                    )
                }
            }
        ) {
            // 创建所有setter的显式函数
            fun setIsListening(value: Boolean) { isListening = value }
            fun setRecognizedText(value: String) { recognizedText = value }
            fun setAudioLevel(value: Float) { audioLevel = value }
            fun setSpeechRecognizer(value: SpeechRecognizer?) { speechRecognizer = value }
            fun setSpeechListenerJob(value: Job?) { speechListenerJob = value }
            fun setIsHolding(value: Boolean) { isHolding = value }
            fun setPendingVoiceText(value: String?) { pendingVoiceText = value }
            
            VoiceInputContent(
                onClose = ::closeVoiceInput,
                isListening = isListening,
                setIsListening = ::setIsListening,
                recognizedText = recognizedText,
                setRecognizedText = ::setRecognizedText,
                audioLevel = audioLevel,
                setAudioLevel = ::setAudioLevel,
                speechRecognizer = speechRecognizer,
                setSpeechRecognizer = ::setSpeechRecognizer,
                speechListenerJob = speechListenerJob,
                setSpeechListenerJob = ::setSpeechListenerJob,
                isHolding = isHolding,
                setIsHolding = ::setIsHolding,
                pendingVoiceText = pendingVoiceText,
                setPendingVoiceText = ::setPendingVoiceText,
                waveAmplitudes = waveAmplitudes,
                navController = navController,
                viewModel = viewModel
            )
        }
    }
    
    // 当有待处理的语音文本时显示解析UI
    pendingVoiceText?.let { text ->
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissParseVoiceInput() {
            pendingVoiceText = null
        }
        
        fun dismissLoadingDialog() {
            isShowingLoadingDialog = false
        }
        
        ParseVoiceInputUI(
            text = text,
            navController = navController,
            viewModel = viewModel,
            onDismiss = ::dismissParseVoiceInput
        )
    }
    
    // 显示正在加载对话框
    if (isShowingLoadingDialog) {
        
        // 创建显式函数避免lambda类型推断问题
        fun dismissLoadingDialog() {
            isShowingLoadingDialog = false
        }
        
        LoadingDialog(
            message = loadingMessage ?: "处理中...",
            onDismiss = ::dismissLoadingDialog
        )
    }

    // 显示成功或错误消息
    if (snackbarMessage != null) {
        LaunchedEffect(snackbarMessage) {
            val result = scaffoldState.snackbarHostState.showSnackbar(
                message = snackbarMessage ?: "",
                actionLabel = "关闭",
                duration = SnackbarDuration.Short
            )
            
            if (result == SnackbarResult.Dismissed || result == SnackbarResult.ActionPerformed) {
                snackbarMessage = null
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor) // 使用从ThemeManager获取的背景色替换AppBackground
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 优化的顶部栏
        CenterAlignedTopAppBar(
            title = { 
                Text(
                    text = "所有",
                    style = MaterialTheme.typography.titleLarge,
                    fontSize = SystemBarManager.getStandardTitleTextSize(),
                    fontWeight = FontWeight.SemiBold, // 调整粗细
                    color = Color(0xFF474947), // 设置为指定颜色
                    modifier = Modifier // 移除顶部内边距
                )
            },
            navigationIcon = {
                IconButton(
                    onClick = { 
                        // 简单地返回上一页
                        navController.popBackStack()
                    },
                    modifier = Modifier.padding(8.dp)
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                IconButton(onClick = { /* 搜索 */ }) {
                    Icon(
                        Icons.Default.Search,
                        contentDescription = "搜索",
                        tint = TextPrimary
                    )
                }
                Box {
                    // 创建显式函数避免lambda类型推断问题
                    fun toggleMenu() { showMenu = !showMenu }
                    fun hideMenu() { showMenu = false }
                    fun showAllTasks() {
                        completionFilterMode = CompletionFilterMode.SHOW_ALL
                        sortMode = TaskSortMode.NONE
                        showMenu = false
                    }
                    fun hideCompleted() {
                        completionFilterMode = CompletionFilterMode.HIDE_COMPLETED
                        sortMode = TaskSortMode.NONE
                        showMenu = false
                    }
                    fun showOnlyCompleted() {
                        completionFilterMode = CompletionFilterMode.SHOW_ONLY_COMPLETED
                        sortMode = TaskSortMode.NONE
                        showMenu = false
                    }
                    fun sortByPriority() {
                        sortMode = TaskSortMode.PRIORITY_HIGH_TO_LOW
                        showMenu = false
                    }
                    fun sortByTime() {
                        sortMode = TaskSortMode.TIME_RECENT_TO_EARLIEST
                        showMenu = false
                    }
                    
                    IconButton(onClick = ::toggleMenu) {
                        Icon(
                            Icons.Default.MoreVert,
                            contentDescription = "更多",
                            tint = TextPrimary
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = ::hideMenu,
                        offset = DpOffset(0.dp, 10.dp),
                        modifier = Modifier.background(
                            color = Color.White,
                            shape = RoundedCornerShape(8.dp)
                        )
                    ) {
                        // 自定义菜单项样式
                        Column(
                            modifier = Modifier
                                .width(150.dp)
                                .padding(vertical = 6.dp)
                        ) {
                            // 省略菜单标题，直接进入选项
                            
                            // 筛选选项分组
                            Divider(
                                color = LavenderAsh.copy(alpha = 0.2f),
                                thickness = 1.dp,
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 3.dp)
                            )
                            
                            // 显示所有任务选项
                            FilterMenuItem(
                                title = "显示所有任务",
                                isSelected = completionFilterMode == CompletionFilterMode.SHOW_ALL && sortMode == TaskSortMode.NONE,
                                onClick = ::showAllTasks
                            )
                            
                            // 隐藏已完成任务选项
                            FilterMenuItem(
                                title = "隐藏已完成",
                                isSelected = completionFilterMode == CompletionFilterMode.HIDE_COMPLETED && sortMode == TaskSortMode.NONE,
                                onClick = ::hideCompleted
                            )
                            
                            // 只显示已完成任务选项
                            FilterMenuItem(
                                title = "显示已完成",
                                isSelected = completionFilterMode == CompletionFilterMode.SHOW_ONLY_COMPLETED && sortMode == TaskSortMode.NONE,
                                onClick = ::showOnlyCompleted
                            )
                            
                            // 排序选项分组
                            Divider(
                                color = LavenderAsh.copy(alpha = 0.2f),
                                thickness = 1.dp,
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                            )
                            
                            // 按优先级排序
                            FilterMenuItem(
                                title = "优先级排序",
                                isSelected = sortMode == TaskSortMode.PRIORITY_HIGH_TO_LOW,
                                onClick = ::sortByPriority
                            )
                            
                            // 按时间排序
                            FilterMenuItem(
                                title = "按时间排序",
                                isSelected = sortMode == TaskSortMode.TIME_RECENT_TO_EARLIEST,
                                onClick = ::sortByTime
                            )
                        }
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent,
                titleContentColor = TextPrimary
            ),
            modifier = Modifier, // 移除固定高度设置
            windowInsets = WindowInsets(0) // 保持windowInsets为0以避免额外边距
        )

        // 过滤器标签
        ScrollableTabRow(
            selectedTabIndex = filters.indexOf(selectedFilter),
            edgePadding = 16.dp,
            containerColor = Color.Transparent,
            divider = {},
            indicator = { }, // 设置空指示器移除默认的蓝色下划线
            modifier = Modifier
                .padding(top = 8.dp, bottom = 4.dp)
                .height(48.dp) // 设置固定高度
        ) {
            filters.forEachIndexed { index, filter ->
                
                // 创建显式函数避免lambda类型推断问题
                fun selectFilter() { selectedFilter = filter }
                
                Tab(
                    selected = selectedFilter == filter,
                    onClick = ::selectFilter,
                    modifier = Modifier.padding(horizontal = 6.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(vertical = 4.dp)
                    ) {
                        Text(
                            text = filter,
                            fontSize = 14.sp,
                            fontWeight = if (selectedFilter == filter) FontWeight.Bold else FontWeight.Normal,
                            color = if (selectedFilter == filter) DustyLavender else TextSecondary,
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                        if (selectedFilter == filter) {
                            Box(
                                Modifier
                                    .width(24.dp)
                                    .height(2.dp)
                                    .background(
                                        color = DustyLavender,
                                        shape = MaterialTheme.shapes.small
                                    )
                            )
                        }
                    }
                }
            }
        }

        // 内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pullRefresh(pullRefreshState) // 添加下拉刷新手势
                .graphicsLayer(
                    alpha = contentAlpha,
                    scaleX = listScale.value,
                    scaleY = listScale.value
                ), // 添加平滑的缩放动画
            contentAlignment = Alignment.TopCenter
        ) {
            // 💡 主内容区域 - 美化的下拉刷新设计
            if (taskListState.isLoading && taskListState.tasks.isEmpty()) {
                // 💡 使用骨架屏替代空白loading，减少视觉跳跃
                SkeletonTaskList()
            } else {
                // 原始的任务列表显示逻辑保持不变
                if (!taskListState.isLoading) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 16.dp)
                            // 💡 添加平滑的透明度过渡动画
                            .alpha(listAlpha.value),
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        contentPadding = PaddingValues(vertical = 12.dp)
                    ) {
                        itemsIndexed(
                            items = filteredTasks,
                            key = { _, task -> task.id },
                            contentType = { _, task -> task.isCompleted }
                        ) { index, task ->
                            // 创建显式函数避免lambda类型推断问题
                            fun onTaskClick() {
                                selectedTask = task
                                showBottomSheet = true
                            }
                            
                            fun onCheckChange(isChecked: Boolean) {
                                if (isChecked) {
                                    feedbackTaskId = task.id
                                    feedbackTaskTitle = task.title
                                    showFeedbackDialog = true
                                }
                                handleTaskStatusChanged(task.id, isChecked)
                            }
                            
                            fun onDeleteTask(taskId: String) {
                                coroutineScope.launch {
                                    try {
                                        viewModel.deleteTask(taskId)
                                        snackbarMessage = "任务已删除"
                                    } catch (e: Exception) {
                                        snackbarMessage = "删除失败: ${e.localizedMessage}"
                                        Log.e("TaskListFullScreen", "删除任务失败", e)
                                    }
                                }
                            }
                            
                            // 添加列表项进入动画
                            androidx.compose.animation.AnimatedVisibility(
                                visible = itemAnimationTrigger > 0,
                                enter = fadeIn(
                                    animationSpec = tween(
                                        durationMillis = 300,
                                        delayMillis = (index * 50).coerceAtMost(500), // 每个项目延迟50ms，最多500ms
                                        easing = FastOutSlowInEasing
                                    )
                                ) + slideInVertically(
                                    initialOffsetY = { it / 4 },
                                    animationSpec = tween(
                                        durationMillis = 400,
                                        delayMillis = (index * 50).coerceAtMost(500),
                                        easing = FastOutSlowInEasing
                                    )
                                )
                            ) {
                            AnimatedTaskItem(
                                task = task,
                                onTaskClick = ::onTaskClick,
                                onCheckChange = ::onCheckChange,
                                onDeleteTask = ::onDeleteTask
                            )
                            }
                            
                            if (index < filteredTasks.size - 1) {
                            Spacer(modifier = Modifier.height(2.dp))
                            }
                        }

                        // 显示空状态 - 也添加动画
                        if (filteredTasks.isEmpty()) {
                            item {
                                androidx.compose.animation.AnimatedVisibility(
                                    visible = !taskListState.isLoading,
                                    enter = fadeIn(
                                        animationSpec = tween(durationMillis = 500, easing = FastOutSlowInEasing)
                                    ) + scaleIn(
                                        initialScale = 0.8f,
                                        animationSpec = tween(durationMillis = 500, easing = FastOutSlowInEasing)
                                    )
                                ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Task,
                                            contentDescription = null,
                                            tint = TextSecondary.copy(alpha = 0.5f),
                                            modifier = Modifier.size(48.dp)
                                        )
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Text(
                                            text = "暂无任务",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = TextSecondary.copy(alpha = 0.7f)
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Text(
                                            text = "点击右下角按钮添加新任务",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = TextSecondary.copy(alpha = 0.7f),
                                            fontSize = 14.sp
                                        )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 💡 现代化下拉刷新指示器 - 参考微信设计
            PullRefreshIndicator(
                refreshing = isRefreshing,
                state = pullRefreshState,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 8.dp),
                backgroundColor = Color.White,
                contentColor = refreshIndicatorColor,
                scale = true // 启用缩放动画
            )
            
            // 💡 额外的刷新状态提示 - 参考Notion的优雅设计
            AnimatedVisibility(
                visible = isRefreshing,
                enter = slideInVertically(
                    initialOffsetY = { -it },
                    animationSpec = tween(400, easing = FastOutSlowInEasing)
                ) + fadeIn(animationSpec = tween(300)),
                exit = slideOutVertically(
                    targetOffsetY = { -it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeOut(animationSpec = tween(200)),
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 60.dp)
            ) {
                Card(
                    modifier = Modifier
                        .padding(horizontal = 16.dp),
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.95f)
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        // 🔄 旋转的刷新图标
                        val rotation by animateFloatAsState(
                            targetValue = if (isRefreshing) 360f else 0f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1000, easing = LinearEasing),
                                repeatMode = RepeatMode.Restart
                            ),
                            label = "refreshRotation"
                        )
                        
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "刷新中",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .size(18.dp)
                                .rotate(rotation)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "正在同步最新任务...",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }

    // 悬浮添加按钮 - 添加长按语音识别功能
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomEnd
    ) {
        // 使用ViewModel的状态判断是否加载完成
        if (!taskListState.isLoading) {
            // 检查麦克风权限 - 暂不请求
            val hasRecordPermission = true  // 暂时直接假设有权限
            
            // 初始化语音识别器
            LaunchedEffect(context) {
                if (hasRecordPermission && speechRecognizer == null) {
                    speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
                    
                    speechRecognizer?.setRecognitionListener(object : RecognitionListener {
                        override fun onReadyForSpeech(params: Bundle?) {}
                        override fun onBeginningOfSpeech() {}
                        override fun onRmsChanged(rmsdB: Float) {
                            // 将RMS值映射到0-1范围用于波形动画
                            audioLevel = (rmsdB + 20) / 40f // 通常RMS值在-20到20之间
                            audioLevel = max(0f, min(1f, audioLevel))
                            
                            // 更新波形振幅
                            waveAmplitudes.forEachIndexed { index, state ->
                                state.value = audioLevel * (0.5f + Random.nextFloat() * 0.5f)
                            }
                        }
                        override fun onBufferReceived(buffer: ByteArray?) {}
                        override fun onEndOfSpeech() {
                            isListening = false
                        }
                        override fun onError(error: Int) {
                            isListening = false
                            if (error != SpeechRecognizer.ERROR_NO_MATCH) {
                                recognizedText = "无法识别，请重试"
                            }
                        }
                        override fun onResults(results: Bundle?) {
                            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                            val text = matches?.get(0) ?: ""
                            recognizedText = text
                            
                            // 设置待处理文本
                            pendingVoiceText = text
                            
                            // 延迟关闭语音界面，给用户看结果的时间
                            coroutineScope.launch {
                                delay(2000)
                                showVoiceInputSheet = false
                            }
                        }
                        override fun onPartialResults(partialResults: Bundle?) {
                            val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                            if (matches != null && matches.isNotEmpty()) {
                                recognizedText = matches[0]
                            }
                        }
                        override fun onEvent(eventType: Int, params: Bundle?) {}
                    })
                }
            }
            
            // 长按悬浮按钮开始语音输入
            val interactionSource = remember { MutableInteractionSource() }
            val isPressed by interactionSource.collectIsPressedAsState()
            val haptic = LocalHapticFeedback.current
            
            // 根据按压状态更新语音识别
            LaunchedEffect(isPressed) {
                if (isPressed && hasRecordPermission) {
                    isHolding = true
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    delay(300) // 长按0.3秒后开始语音识别
                    
                    if (isHolding) {
                        // 开始语音识别流程
                        isListening = true
                        showVoiceInputSheet = true // 显示底部弹出框
                        recognizedText = "说出你要做的事..."
                        
                        // 再次触发轻微触觉反馈，指示识别开始
                        haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        
                        // 开始语音听写
                        val recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
                            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN") // 设置为中文
                        }
                        
                        // 启动语音监听并安排波形动画
                        speechRecognizer?.startListening(recognizerIntent)
                        
                        // 为波形条启动异步动画
                        speechListenerJob = launch {
                            while (isListening) {
                                waveAmplitudes.forEachIndexed { index, state ->
                                    launch {
                                        // 根据当前音量和随机因子生成目标值
                                        val targetHeight = state.value * Random.nextFloat() * 0.5f + 0.5f
                                        
                                        // 使用不同持续时间为每个条创建交错动画
                                        state.value = targetHeight
                                    }
                                }
                                delay(150) // 控制动画更新频率
                            }
                        }
                    }
                } else if (!isPressed && isHolding) {
                    isHolding = false
                    
                    // 如果正在识别，停止识别
                    if (isListening) {
                        isListening = false
                        speechRecognizer?.stopListening()
                        speechListenerJob?.cancel()
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    }
                }
            }
            
            // 浮动按钮
            FloatingActionButton(
                onClick = {
                    // 短按直接导航到任务编辑页面
                    if (!isListening) {
                        navController.navigate(AppDestinations.ADD_TASK_ROUTE)
                    }
                },
                modifier = Modifier
                    .padding(end = 16.dp, bottom = 100.dp)
                    .size(56.dp)
                    .scale(micScale.value),
                interactionSource = interactionSource,
                containerColor = Color(0xFF8BAA80),
                contentColor = Color.White,
                shape = CircleShape,
                elevation = FloatingActionButtonDefaults.elevation(
                    defaultElevation = 2.dp,
                    pressedElevation = 4.dp
                )
            ) {
                Icon(
                    imageVector = if (isListening) Icons.Default.Mic else Icons.Default.Add, 
                    contentDescription = if (isListening) "正在录音" else "添加任务",
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }

    // 注释掉旧的TaskTimeManager监听，使用新的TaskTimeViewModel
    /*
    // 监听TaskTimeManager的时间更新事件
    DisposableEffect(Unit) {
        val timeUpdateJob = coroutineScope.launch {
            taskTimeManager.timeUpdateEvents.collect { event ->
                Log.d("TaskListFullScreen", "收到时间更新事件: taskId=${event.taskId}, 新时间=${event.newDateTime}")
                
                // 更新选中任务的时间显示
                if (selectedTask?.id == event.taskId) {
                    selectedTask = selectedTask?.copy(
                        dueDate = event.newDateTime,
                        daysLeft = event.newDateTime?.let { calculateDaysLeft(it) } ?: Int.MAX_VALUE
                    )
                    Log.d("TaskListFullScreen", "已更新选中任务时间显示")
                }
                
                // 强制刷新任务列表以显示最新时间
                delay(100) // 短暂延迟确保数据同步
                viewModel.refreshTasks()
                Log.d("TaskListFullScreen", "已刷新任务列表")
            }
        }
        
        onDispose {
            timeUpdateJob.cancel()
            Log.d("TaskListFullScreen", "已取消时间更新事件监听")
        }
    }
    */

    // 额外监听TaskTimeViewModel的UI状态变化
    LaunchedEffect(Unit) {
        taskTimeViewModel.uiState.collect { state ->
            if (state.lastUpdatedTaskId != null) {
                Log.d("TaskListFullScreen", "检测到任务时间状态变化: ${state.lastUpdatedTaskId}")
                
                // 强制刷新任务列表
                delay(50) // 短暂延迟确保数据同步
                viewModel.refreshTasks()
            }
        }
    }

    // 智能防抖刷新函数
    fun smartRefresh(reason: String = "unknown") {
        performRefresh(reason) // 使用新的 performRefresh 函数
    }

    // 显式函数，避免实验性功能问题
    fun handleTaskUpdateFunction(updatedTaskData: TaskData) {
        handleTaskUpdate(updatedTaskData)
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun AnimatedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDeleteTask: (String) -> Unit = {}
) {
    // 添加悬停状态
    var isPressed by remember { mutableStateOf(false) }
    
    // 悬停动画
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "itemScale"
    )
    
    val elevation by animateFloatAsState(
        targetValue = if (isPressed) 0.5f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "itemElevation"
    )
    
    Box(
        modifier = Modifier
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale
            )
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onTaskClick() }
                )
            }
    ) {
    EnhancedTaskItem(
        task = task,
        onTaskClick = onTaskClick,
        onCheckChange = onCheckChange,
            onDeleteTask = onDeleteTask,
            elevation = elevation
    )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun EnhancedTaskItem(
    task: ModelTaskData,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDeleteTask: (String) -> Unit = {}, // 添加删除任务的回调函数
    elevation: Float
) {
    // 使用remember缓存计算值，避免不必要的重新计算
    val isChecked = remember(task.id, task.isCompleted) { task.isCompleted }
    val textDecoration = remember(isChecked) { 
        if (isChecked) TextDecoration.LineThrough else TextDecoration.None 
    }
    val textColor = remember(isChecked) { 
        if (isChecked) TextSecondary.copy(alpha = 0.6f) else TextPrimary 
    }
    val urgencyColor = remember(task.urgency) { getUrgencyColor(task.urgency) }
    val timeText = remember(task.dueDate, task.daysLeft) {
        // 优先使用dueDate，如果有的话
        if (task.dueDate != null) {
            formatDetailedTimeRangeFromDate(task.dueDate)
        } else if (task.daysLeft != Int.MAX_VALUE) {
            // 只有当daysLeft有效时才使用它
            formatDetailedTimeRange(task.daysLeft)
        } else {
            "未设置时间" // 添加默认显示
        }
    }
    
    // 添加标题编辑状态
    var isEditing by remember { mutableStateOf(false) }
    var editedTitle by remember(task.title) { mutableStateOf(task.title) }
    
    // 触觉反馈
    val haptic = LocalHapticFeedback.current
    val viewModel: TaskListViewModel = hiltViewModel()
    
    // 保存标题变更的函数
    val saveTitle = {
        if (isEditing && editedTitle.isNotBlank() && editedTitle != task.title) {
            viewModel.updateTaskTitle(task.id, editedTitle)
        }
        isEditing = false
    }
    
    // 添加滑动状态
    var isDeleting by remember { mutableStateOf(false) }
    var isCompleting by remember { mutableStateOf(false) }
    
    val dismissState = rememberDismissState(
        initialValue = DismissValue.Default,
        confirmStateChange = { value ->
            when (value) {
                DismissValue.DismissedToStart -> {
                    // 左滑删除
                    isDeleting = true
                    onDeleteTask(task.id)
                    true
                }
                DismissValue.DismissedToEnd -> {
                    // 右滑完成
                    isCompleting = true
                    onCheckChange(!isChecked)
                    true
                }
                else -> false
            }
        }
    )
    
    val isDismissed = dismissState.isDismissed(DismissDirection.EndToStart) || 
                    dismissState.isDismissed(DismissDirection.StartToEnd)
    
    // 计算滑动进度 (0.0-1.0)
    val dismissProgressLeft = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToStart -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    val dismissProgressRight = remember(dismissState.progress) {
        when (dismissState.progress.to) {
            DismissValue.DismissedToEnd -> dismissState.progress.fraction
            else -> 0f
        }
    }
    
    // 图标旋转动画
    val deleteIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToStart) -45f else 0f,
        label = "deleteIconRotation"
    )
    
    val completeIconRotation by animateFloatAsState(
        targetValue = if (dismissState.targetValue == DismissValue.DismissedToEnd) 45f else 0f,
        label = "completeIconRotation" 
    )

    // 当滑动超过阈值时触发触觉反馈
    LaunchedEffect(dismissState.targetValue) {
        if (dismissState.targetValue == DismissValue.DismissedToStart || 
            dismissState.targetValue == DismissValue.DismissedToEnd) {
            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
        }
    }
    
    // 使用外层Box包裹，控制整体布局
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp) // 减小水平内边距，增加卡片宽度
    ) {
        // 滑动删除组件
        SwipeToDismiss(
            state = dismissState,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 2.dp),
            directions = setOf(DismissDirection.EndToStart, DismissDirection.StartToEnd), // 添加右滑方向
            dismissThresholds = { direction -> FractionalThreshold(0.15f) }, // 降低滑动阈值，轻微滑动就触发
            background = {
                Row(
                    modifier = Modifier.fillMaxSize(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 右滑完成背景 (左侧)
                    if (dismissProgressRight > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(start = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFF4CAF50)), // 绿色完成按钮
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "完成",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(completeIconRotation)
                                )
                            }
                        }
                    }
                    
                    // 占位Spacer
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 左滑删除背景 (右侧)
                    if (dismissProgressLeft > 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(80.dp)
                                .padding(end = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(44.dp)
                                    .clip(CircleShape)
                                    .background(Color(0xFFEF5350)), // 红色删除按钮
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .rotate(deleteIconRotation)
                                )
                            }
                        }
                    }
                }
            },
            dismissContent = {
                // 任务项内容 - 使用Card替代Box增强视觉效果
                val cardScale by animateFloatAsState(
                    targetValue = 1f - (max(dismissProgressLeft, dismissProgressRight) * 0.03f), // 滑动时卡片轻微缩小
                    label = "cardScale"
                )
                val cardElevation by animateFloatAsState(
                    targetValue = 1f + (max(dismissProgressLeft, dismissProgressRight) * 2f), // 滑动时阴影增大
                    label = "cardElevation"
                )
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .scale(cardScale),
                    shape = RoundedCornerShape(12.dp), // 减小圆角
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFfcfcfc) // 确保背景色为rgb(247, 247, 246)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = elevation.dp
                    ),
                    onClick = { 
                        // 如果处于编辑状态，点击卡片其他区域时保存更改并退出编辑模式
                        val isEditingTitle = isEditing
                        if (isEditingTitle) {
                            saveTitle()
                        } else {
                            onTaskClick()
                        }
                    }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 使用静态颜色替代动态计算
                        Box(
                            modifier = Modifier
                                .size(width = 4.dp, height = 36.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(urgencyColor)
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        // 优化复选框，移除动画效果
                        Checkbox(
                            checked = isChecked,
                            onCheckedChange = onCheckChange,
                            colors = CheckboxDefaults.colors(
                                checkedColor = DustyLavender,
                                uncheckedColor = LavenderAsh.copy(alpha = 0.7f)
                            ),
                            modifier = Modifier
                                .size(20.dp)
                                .padding(end = 4.dp),
                            interactionSource = NoRippleInteractionSource
                        )

                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 8.dp)
                        ) {
                            // 替换原有的Text组件为可编辑的标题组件
                            
                            if (isEditing) {
                                // 编辑模式 - 显示TextField
                                val focusRequester = remember { FocusRequester() }
                                TextField(
                                    value = editedTitle,
                                    onValueChange = { editedTitle = it },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 0.dp)
                                        .focusRequester(focusRequester),
                                    textStyle = MaterialTheme.typography.bodyLarge.copy(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    ),
                                    colors = TextFieldDefaults.colors(
                                        focusedContainerColor = Color.Transparent,
                                        unfocusedContainerColor = Color.Transparent,
                                        disabledContainerColor = Color.Transparent,
                                        focusedIndicatorColor = DustyLavender,
                                        unfocusedIndicatorColor = Color.Transparent
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(
                                        imeAction = ImeAction.Done
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = { 
                                            if (editedTitle.isNotBlank()) {
                                                saveTitle()
                                            }
                                        }
                                    )
                                )
                                
                                // 自动获取焦点
                                LaunchedEffect(Unit) {
                                    delay(100)
                                    try {
                                        focusRequester.requestFocus()
                                    } catch (e: Exception) {
                                        Log.e("TaskListFullScreen", "请求焦点失败", e)
                                    }
                                }
                                
                                // 监听点击外部事件，保存更改并退出编辑模式
                                DisposableEffect(Unit) {
                                    onDispose {
                                        saveTitle()
                                    }
                                }
                            } else {
                                // 非编辑模式 - 显示普通文本，支持长按编辑
                                Text(
                                    text = task.title,
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontSize = 16.sp,
                                    fontWeight = if (!isChecked) FontWeight.Medium else FontWeight.Normal,
                                    textDecoration = textDecoration,
                                    color = textColor,
                                    maxLines = 2,
                                    overflow = TextOverflow.Ellipsis,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .combinedClickable(
                                            onClick = { /* 短按由外层Card处理 */ },
                                            onLongClick = { 
                                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                                isEditing = true 
                                            }
                                        )
                                )
                            }

                            Spacer(modifier = Modifier.height(6.dp))

                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 使用Box替代Surface以提高性能
                                Box(
                                    modifier = Modifier
                                        .clip(RoundedCornerShape(4.dp))
                                        .background(LavenderAsh.copy(alpha = 0.2f))
                                        .padding(horizontal = 6.dp, vertical = 2.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Schedule,
                                            contentDescription = null,
                                            tint = DustyLavender.copy(alpha = 0.8f),
                                            modifier = Modifier.size(12.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        Text(
                                            text = timeText,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontSize = 12.sp,
                                            color = DustyLavender.copy(alpha = 0.8f)
                                        )
                                    }
                                }

                                task.tag?.let {
                                    Spacer(modifier = Modifier.width(8.dp))
                                    OptimizedTagChip(it, isPrimary = true)
                                }
                            }
                        }
                    }
                }
            }
        )
    }
}

@Composable
private fun OptimizedTagChip(tag: String, isPrimary: Boolean = false) {
    // 使用remember缓存颜色值和样式
    val backgroundColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFFF7F3DE) // 主标签使用淡黄色背景
        else 
            LavenderAsh.copy(alpha = 0.15f) // 次要标签使用淡紫色背景
    }
        
    val textColor = remember(isPrimary) {
        if (isPrimary) 
            Color(0xFF8B7E4B) // 主标签文字颜色
        else 
            TextSecondary // 次要标签文字颜色
    }

    val fontWeight = remember(isPrimary) {
        if (isPrimary) FontWeight.Medium else FontWeight.Normal
    }

    // 使用Box替代Surface以提高性能
    Box(
        modifier = Modifier
            .height(22.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
    ) {
        Text(
            text = tag,
            fontSize = 11.sp,
            color = textColor,
            fontWeight = fontWeight,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
        )
    }
}

// 判断任务是否为今天的任务
private fun isTaskForToday(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    
    return when {
        // 如果有具体的dueDate日期，则直接与今天比较
        task.dueDate != null -> {
            task.dueDate.toLocalDate().isEqual(today)
        }
        // 通过daysLeft计算，如果是0天，则表示今天
        task.daysLeft == 0 -> true
        else -> false
    }
}

// 判断任务是否为明天的任务
private fun isTaskForTomorrow(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    val tomorrow = today.plusDays(1)
    
    return when {
        // 如果有具体的dueDate日期，则直接与明天比较
        task.dueDate != null -> {
            task.dueDate.toLocalDate().isEqual(tomorrow)
        }
        // 通过daysLeft计算，如果正好是1天，则表示明天
        task.daysLeft == 1 -> true
        // 或者通过daysLeft计算出来的日期与明天相等
        task.daysLeft > 0 -> {
            val dueDate = today.plusDays(task.daysLeft.toLong())
            dueDate.isEqual(tomorrow)
        }
        else -> false
    }
}

// 判断任务是否为本周的任务
private fun isTaskForThisWeek(task: ModelTaskData): Boolean {
    val today = LocalDate.now()
    val endOfWeek = today.plusDays(7 - today.dayOfWeek.value.toLong())
    
    return when {
        // 如果有具体的dueDate日期，则判断是否在本周内
        task.dueDate != null -> {
            val dueDate = task.dueDate.toLocalDate()
            !dueDate.isAfter(endOfWeek) && !dueDate.isBefore(today)
        }
        // 通过daysLeft计算，判断是否在本周内（0-6天）
        task.daysLeft >= 0 && task.daysLeft <= 6 -> true
        // 或者通过daysLeft计算出来的日期在本周内
        task.daysLeft > 0 -> {
            val dueDate = today.plusDays(task.daysLeft.toLong())
            !dueDate.isAfter(endOfWeek) && !dueDate.isBefore(today)
        }
        else -> false
    }
}

// 判断任务是否为未定期任务（没有设置任务时间）
private fun isTaskUpcoming(task: ModelTaskData): Boolean {
    return when {
        // 如果没有设置具体的dueDate日期，则属于未定期任务
        task.dueDate == null -> true
        // 或者daysLeft设置为Int.MAX_VALUE，表示未定期任务
        task.daysLeft == Int.MAX_VALUE -> true
        else -> false
    }
}

// 筛选模式枚举
enum class CompletionFilterMode {
    SHOW_ALL,          // 显示所有任务
    HIDE_COMPLETED,    // 隐藏已完成任务
    SHOW_ONLY_COMPLETED // 只显示已完成任务
}

// 排序模式枚举
enum class TaskSortMode {
    NONE,                    // 不排序
    PRIORITY_HIGH_TO_LOW,    // 优先级从高到低
    TIME_RECENT_TO_EARLIEST  // 时间从最近到最早
}

// 自定义菜单项组件
@Composable
private fun FilterMenuItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            // 使用 NoRippleInteractionSource 来避免菜单项的 RippleDrawable 动画
            .clickable(
                interactionSource = NoRippleInteractionSource,
                indication = null,
                onClick = onClick
            )
            .background(if (isSelected) DustyLavender.copy(alpha = 0.1f) else Color.Transparent)
            .padding(horizontal = 16.dp, vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isSelected) DustyLavender else TextPrimary,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            fontSize = 14.sp
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = DustyLavender,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// 语音输入内容组件
@Composable
private fun VoiceInputContent(
    onClose: () -> Unit,
    isListening: Boolean,
    setIsListening: (Boolean) -> Unit,
    recognizedText: String,
    setRecognizedText: (String) -> Unit,
    audioLevel: Float,
    setAudioLevel: (Float) -> Unit,
    speechRecognizer: SpeechRecognizer?,
    setSpeechRecognizer: (SpeechRecognizer?) -> Unit,
    speechListenerJob: Job?,
    setSpeechListenerJob: (Job?) -> Unit,
    isHolding: Boolean,
    setIsHolding: (Boolean) -> Unit,
    pendingVoiceText: String?,
    setPendingVoiceText: (String?) -> Unit,
    waveAmplitudes: List<MutableState<Float>>,
    navController: NavController,
    viewModel: TaskListViewModel
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val hapticFeedback = LocalHapticFeedback.current
    val primaryColor = Color(0xFFB6AFC7) // 使用指定的按钮颜色
    
    // 初始化语音识别器
    LaunchedEffect(context) {
        if (speechRecognizer == null) {
            setSpeechRecognizer(SpeechRecognizer.createSpeechRecognizer(context))
        }
    }
    
    // 处理语音识别回调
    LaunchedEffect(speechRecognizer) {
        speechRecognizer?.setRecognitionListener(object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                setIsListening(true)
            }
            
            override fun onBeginningOfSpeech() {}
            
            override fun onRmsChanged(rmsdB: Float) {
                // 将RMS转换为0-1之间的值
                val normalizedLevel = (rmsdB + 100) / 100
                setAudioLevel(normalizedLevel.coerceIn(0f, 1f))
                
                // 使波形产生随机波动
                coroutineScope.launch {
                    waveAmplitudes.forEach { amplitude ->
                        val currentValue = amplitude.value
                        val randomFactor = Random.nextFloat() * 0.4f - 0.2f
                        val newValue = (normalizedLevel + randomFactor).coerceIn(0.05f, 1f)
                        amplitude.value = newValue
                    }
                }
            }
            
            override fun onBufferReceived(buffer: ByteArray?) {}
            
            override fun onEndOfSpeech() {
                setIsListening(false)
            }
            
            override fun onError(error: Int) {
                setIsListening(false)
                Log.e("VoiceInput", "Speech recognition error: $error")
            }
            
            override fun onResults(results: Bundle?) {
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    val recognizedText = matches[0]
                    setRecognizedText(recognizedText)
                    
                    // 设置待处理文本
                    setPendingVoiceText(recognizedText)
                    
                    // 关闭语音输入
                    onClose()
                }
            }
            
            override fun onPartialResults(partialResults: Bundle?) {
                val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    setRecognizedText(matches[0])
                }
            }
            
            override fun onEvent(eventType: Int, params: Bundle?) {}
        })
    }
    
    // 启动语音识别
    LaunchedEffect(Unit) {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, "zh-CN")
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }
        
        speechRecognizer?.startListening(intent)
    }
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题
        Text(
            text = "说出你要做的事",
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
            ),
            color = Color.Black,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 识别结果显示区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color(0xFFF6F5F9),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(vertical = 24.dp, horizontal = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            if (recognizedText.isNotEmpty()) {
                Text(
                    text = recognizedText,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.DarkGray,
                    textAlign = TextAlign.Center
                )
            } else {
            Text(
                    text = "无法识别，请重试",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.Gray,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 音量波形可视化
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                waveAmplitudes.forEach { amplitude ->
                    Box(
                        modifier = Modifier
                            .width(3.dp)
                            .height((50 * amplitude.value).dp + 5.dp)
                            .background(
                                primaryColor.copy(alpha = 0.7f + 0.3f * amplitude.value),
                                RoundedCornerShape(1.5.dp)
                            )
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 确认按钮
        Box(
            modifier = Modifier
                .size(72.dp)
                .background(primaryColor, CircleShape)
                .clickable {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    if (recognizedText.isNotEmpty()) {
                        setPendingVoiceText(recognizedText)
                        onClose()
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "确认",
                tint = Color.White,
                modifier = Modifier.size(36.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 底部提示
            Text(
                text = "上滑可取消输入",
                style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray,
            textAlign = TextAlign.Center
            )
    }
}

/**
 * 格式化剩余时间为详细格式，包含具体时间段
 * 例如: "后天，3月31日, 17:25 - 18:25"
 */
private fun formatDetailedTimeRange(daysLeft: Int): String {
    val today = LocalDateTime.now()
    val dueDate = today.plusDays(daysLeft.toLong())
    
    // 添加日志辅助调试
    Log.d("TimeFormatting", "从天数($daysLeft)计算日期: $dueDate, 今天: $today")
    
    // 复用formatDetailedTimeRangeFromDate函数保持一致性
    return formatDetailedTimeRangeFromDate(dueDate)
}

/**
 * 格式化剩余时间为详细格式，使用实际的日期而不是计算，确保和详情页面一致
 * 例如: "后天，3月31日, 17:25 - 18:25"
 */
private fun formatDetailedTimeRangeFromDate(dueDate: LocalDateTime): String {
    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val inputDate = dueDate.toLocalDate()
    val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
    val endTime = dueDate.plusHours(1).format(DateTimeFormatter.ofPattern("HH:mm"))
    
    // 添加日志辅助调试
    Log.d("TimeFormatting", "格式化日期: $dueDate, 今天: $now")
    
    return when {
        // 今天
        inputDate.isEqual(today) -> {
            "今天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 明天
        inputDate.isEqual(today.plusDays(1)) -> {
            "明天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 后天
        inputDate.isEqual(today.plusDays(2)) -> {
            "后天，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 本周内 (显示星期几)
        ChronoUnit.DAYS.between(today, inputDate) < 7 -> {
            val dayOfWeek = when (inputDate.dayOfWeek.value) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> ""
            }
            "$dayOfWeek，${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 本年内 (显示月日)
        inputDate.year == today.year -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("M月d日"))}, $time - $endTime"
        }
        // 其他年份 (显示年月日)
        else -> {
            "${inputDate.format(DateTimeFormatter.ofPattern("yyyy年M月d日"))}, $time - $endTime"
        }
    }
}

// 将 SubTask 转换为 TaskModel
private fun convertModelSubTaskToModelTaskData(subTask: SubTask): ModelTaskData {
    return ModelTaskData(
        id = subTask.id,
        title = subTask.title,
        description = subTask.note, // 使用note字段代替description
        isCompleted = subTask.isCompleted,
        daysLeft = 0,  // 子任务通常没有独立的截止日期
        urgency = TaskUrgency.MEDIUM,  // 默认为中等优先级
        displayInTaskList = false,  // 子任务通常不在主列表显示
        parentTaskId = subTask.parentTaskId
    )
}

// 检查所有子任务是否完成
private fun areAllModelSubTasksCompleted(subTasks: List<SubTask>): Boolean {
    return subTasks.isNotEmpty() && subTasks.all { it.isCompleted }
}

// 修改 transformSubTaskToLocalModel 函数，处理正确的 SubTask 类型
private fun transformSubTaskToLocalModel(subTask: SubTask): SubTask {
    // 我们现在直接使用来自 model 包的 SubTask
    return subTask
}

// 添加功能层转换工具函数
private fun addSubTaskToParentTask(parentTask: ModelTaskData, subTask: SubTask): ModelTaskData {
    val updatedSubTasks = parentTask.subTasks.toMutableList()
    updatedSubTasks.add(subTask)
    return parentTask.copy(subTasks = updatedSubTasks)
}

// 修改实验特性警告
@OptIn(ExperimentalTypeConversion::class, ExperimentalMaterial3Api::class)
@Composable
private fun ParseVoiceInputUI(
    text: String,
    navController: NavController,
    viewModel: TaskListViewModel,
    onDismiss: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val primaryColor = Color(0xFFB6AFC7) // 使用指定的按钮颜色
    val backgroundColor = Color(0xFFF6F5F9) // 使用相同的背景色
    val hapticFeedback = LocalHapticFeedback.current
    
    // 解析任务标题、描述、时间等属性
    val taskTitle = remember { mutableStateOf(text) }
    val taskDescription = remember { mutableStateOf("") }
    val taskPriority = remember { mutableStateOf(TaskUrgency.MEDIUM) }
    val taskTag = remember { mutableStateOf<String?>(null) }
    val taskDate = remember { mutableStateOf<LocalDate?>(null) }
    
    // 解析文本中的时间信息
    LaunchedEffect(text) {
        // 这里可以添加更复杂的NLP解析逻辑
        parseVoiceInput(
            text = text,
            taskTitle = taskTitle,
            taskDescription = taskDescription,
            taskPriority = taskPriority,
            taskTag = taskTag,
            taskDate = taskDate
        )
    }
    
    // 创建任务的函数
    @OptIn(ExperimentalTypeConversion::class)
    val createTask: () -> Unit = {
        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
        
        // 创建任务对象
        val newTask = ModelTaskData(
            id = UUID.randomUUID().toString(),
            title = taskTitle.value,
            description = taskDescription.value,
            daysLeft = calculateDaysLeft(taskDate.value),
            isCompleted = false,
            urgency = taskPriority.value,
            tag = taskTag.value,
            displayInTaskList = true
        )
        
        // 保存任务
        coroutineScope.launch {
            viewModel.saveTask(newTask)
            onDismiss()
        }
    }
    
    // 使用ModalBottomSheet显示界面
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = rememberModalBottomSheetState(),
        shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
        containerColor = Color.White,
        dragHandle = {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Box(
            modifier = Modifier
                        .width(40.dp)
                        .height(4.dp)
                        .background(
                            color = Color(0xFFE0E0E0),
                            shape = RoundedCornerShape(2.dp)
                        )
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Text(
                text = "创建任务",
                style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.SemiBold
                ),
                color = Color.Black,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 解析结果区域
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = backgroundColor,
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(16.dp)
            ) {
                // 任务标题
                Text(
                    text = "标题",
                    style = MaterialTheme.typography.labelLarge,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                OutlinedTextField(
                    value = taskTitle.value,
                    onValueChange = { taskTitle.value = it },
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = primaryColor,
                        unfocusedBorderColor = Color.LightGray,
                        cursorColor = primaryColor
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 任务描述
                Text(
                    text = "描述",
                    style = MaterialTheme.typography.labelLarge,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                OutlinedTextField(
                    value = taskDescription.value,
                    onValueChange = { taskDescription.value = it },
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = primaryColor,
                        unfocusedBorderColor = Color.LightGray,
                        cursorColor = primaryColor
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 优先级和标签
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // 优先级选择
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "优先级",
                            style = MaterialTheme.typography.labelLarge,
                            color = Color.Gray,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 8.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color.LightGray,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        ) {
                            Text(
                                text = when(taskPriority.value) {
                                    TaskUrgency.LOW -> "低"
                                    TaskUrgency.MEDIUM -> "中"
                                    TaskUrgency.HIGH -> "高"
                                    TaskUrgency.CRITICAL -> "紧急"
                                    else -> "中"
                                },
                                color = Color.DarkGray
                            )
                        }
                    }
                    
                    // 标签选择
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "标签",
                            style = MaterialTheme.typography.labelLarge,
                            color = Color.Gray,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 8.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color.LightGray,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp)
                        ) {
                            Text(
                                text = taskTag.value ?: "未分类",
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 取消按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.LightGray.copy(alpha = 0.5f),
                        contentColor = Color.DarkGray
                    )
                ) {
                    Text("取消")
                }
                
                // 创建按钮
                Button(
                    onClick = createTask,
            modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    shape = RoundedCornerShape(24.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = primaryColor,
                        contentColor = Color.White
                    )
                ) {
                    Text("创建任务")
                }
            }
        }
    }
}

// 辅助函数：解析语音输入
@OptIn(ExperimentalTypeConversion::class)
private fun parseVoiceInput(
    text: String,
    taskTitle: MutableState<String>,
    taskDescription: MutableState<String>,
    taskPriority: MutableState<TaskUrgency>,
    taskTag: MutableState<String?>,
    taskDate: MutableState<LocalDate?>
) {
    // 简单的语音解析逻辑，可以替换为更复杂的NLP处理
    
    // 如果包含"明天"，设置为明天
    if (text.contains("明天")) {
        taskDate.value = LocalDate.now().plusDays(1)
    } else if (text.contains("今天")) {
        taskDate.value = LocalDate.now()
    } else if (text.contains("后天")) {
        taskDate.value = LocalDate.now().plusDays(2)
    } else if (text.contains("下周")) {
        taskDate.value = LocalDate.now().plusDays(7)
    }
    
    // 检测优先级
    if (text.contains("紧急") || text.contains("重要")) {
        taskPriority.value = TaskUrgency.HIGH
    } else if (text.contains("非常紧急") || text.contains("极其重要")) {
        taskPriority.value = TaskUrgency.CRITICAL
    } else if (text.contains("不紧急") || text.contains("低优先级")) {
        taskPriority.value = TaskUrgency.LOW
    }
    
    // 检测标签
    val tagPattern = Pattern.compile("标签(:|：|是)?\\s*([^,，。；;]+)")
    val tagMatcher = tagPattern.matcher(text)
    if (tagMatcher.find()) {
        taskTag.value = tagMatcher.group(2)?.trim()
    } else {
        // 常见类别检测
        when {
            text.contains("工作") -> taskTag.value = "工作"
            text.contains("学习") -> taskTag.value = "学习"
            text.contains("个人") -> taskTag.value = "个人"
            text.contains("家庭") -> taskTag.value = "家庭"
            text.contains("健康") -> taskTag.value = "健康"
        }
    }
    
    // 检测描述
    val descPattern = Pattern.compile("描述(:|：|是)?\\s*([^。；;]+)")
    val descMatcher = descPattern.matcher(text)
    if (descMatcher.find()) {
        taskDescription.value = descMatcher.group(2)?.trim() ?: ""
    }
    
    // 处理标题，尝试提取主要任务内容
    // 如果找不到明确的标题模式，就使用整个文本作为标题
    val titlePattern = Pattern.compile("^([^,，。；;]+)")
    val titleMatcher = titlePattern.matcher(text)
    if (titleMatcher.find()) {
        var title = titleMatcher.group(1)?.trim() ?: text
        // 移除可能包含的时间和优先级词
        val timeWords = listOf("今天", "明天", "后天", "下周")
        val priorityWords = listOf("紧急", "重要", "非常紧急", "极其重要", "不紧急", "低优先级")
        
        for (word in timeWords + priorityWords) {
            title = title.replace(word, "").trim()
        }
        
        if (title.isNotEmpty()) {
            taskTitle.value = title
        } else {
            taskTitle.value = text
        }
    } else {
        taskTitle.value = text
    }
}

// 辅助函数：计算离deadline还有多少天
private fun calculateDaysLeft(date: LocalDate?): Int {
    if (date == null) return 1 // 默认为明天
    
    val today = LocalDate.now()
    return ChronoUnit.DAYS.between(today, date).toInt()
}

// 辅助函数：将UI层的SubTask转换为model.SubTask
private fun transformUIToModelSubTask(parentId: String, uiSubTask: com.timeflow.app.ui.screen.task.SubTask): SubTask {
    return SubTask(
        id = uiSubTask.id,
        title = uiSubTask.title,
        isCompleted = uiSubTask.isCompleted,
        parentTaskId = parentId,
        note = uiSubTask.note // 使用note字段
    )
}

@OptIn(ExperimentalMaterial3Api::class)
annotation class ExperimentalComposeApi

@OptIn(ExperimentalMaterial3Api::class)
annotation class ExperimentalTypeConversion

@OptIn(ExperimentalTypeConversion::class)
private fun getUrgencyColor(urgency: TaskUrgency): Color {
    return when (urgency) {
        TaskUrgency.CRITICAL -> Color(0xFFE57373)
        TaskUrgency.HIGH -> Color(0xFFFFB74D)
        TaskUrgency.MEDIUM -> Color(0xFFFFF176)
        TaskUrgency.LOW -> Color(0xFF81C784)
    }
}

@OptIn(ExperimentalTypeConversion::class)
private fun createDismissHandler(): () -> Unit {
    return { /* 处理关闭事件 */ }
}

// 添加 LoadingDialog 组件
@Composable
private fun LoadingDialog(message: String, onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("处理中") },
        text = { Text(message) },
        confirmButton = { }
    )
}

// 修复 rememberScaffoldState 函数
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun rememberScaffoldState(): ScaffoldState {
    return remember { 
        ScaffoldState(
            snackbarHostState = SnackbarHostState(),
            drawerState = DrawerState(DrawerValue.Closed)
        ) 
    }
}

// 添加类型别名以处理命名冲突
private typealias ComposeRoundedCornerShape = RoundedCornerShape

// 添加所需的状态类
@OptIn(ExperimentalMaterial3Api::class)
private class ScaffoldState(
    val snackbarHostState: SnackbarHostState,
    val drawerState: DrawerState
)

private enum class DrawerValue {
    Closed,
    Open
}

@OptIn(ExperimentalMaterial3Api::class)
private class DrawerState(initialValue: DrawerValue) {
    var currentValue = initialValue
        private set

    fun open() {
        currentValue = DrawerValue.Open
    }

    fun close() {
        currentValue = DrawerValue.Closed
    }
} 

/**
 * 计算给定日期时间与今天的天数差（重载版本）
 * 支持LocalDateTime类型的参数
 */
private fun calculateDaysLeft(dateTime: LocalDateTime?): Int {
    // 如果日期时间为null，返回Int.MAX_VALUE表示无期限
    if (dateTime == null) return Int.MAX_VALUE
    
    // 转换为LocalDate并调用原有的calculateDaysLeft函数
    return calculateDaysLeft(dateTime.toLocalDate())
}

// 💡 现代化骨架屏组件 - 参考微信、TickTick等app的设计
@Composable
private fun SkeletonTaskList() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(vertical = 12.dp)
    ) {
        items(8) { index ->
            SkeletonTaskItem(
                modifier = Modifier.animateItemPlacement(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                )
            )
        }
    }
}

@Composable
private fun SkeletonTaskItem(modifier: Modifier = Modifier) {
    // 💡 脉动动画 - 类似Notion的加载效果
    val infiniteTransition = rememberInfiniteTransition(label = "skeletonAnimation")
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.2f,
        targetValue = 0.7f,
        animationSpec = infiniteRepeatable(
            animation = tween(1200, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "skeletonAlpha"
    )
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(84.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 💡 圆形复选框骨架
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(
                        color = Color.Gray.copy(alpha = alpha),
                        shape = CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                // 💡 标题骨架 - 随机宽度增加真实感
                val titleWidth = remember { (0.6f..0.9f).random() }
                Box(
                    modifier = Modifier
                        .fillMaxWidth(titleWidth)
                        .height(18.dp)
                        .background(
                            color = Color.Gray.copy(alpha = alpha),
                            shape = RoundedCornerShape(4.dp)
                        )
                )
                
                // 💡 描述骨架
                val descWidth = remember { (0.4f..0.7f).random() }
                Box(
                    modifier = Modifier
                        .fillMaxWidth(descWidth)
                        .height(14.dp)
                        .background(
                            color = Color.Gray.copy(alpha = alpha * 0.7f),
                            shape = RoundedCornerShape(4.dp)
                        )
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 💡 优先级标记骨架
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = Color.Gray.copy(alpha = alpha),
                        shape = CircleShape
                    )
            )
        }
    }
}
