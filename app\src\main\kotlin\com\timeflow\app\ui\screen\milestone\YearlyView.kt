package com.timeflow.app.ui.screen.milestone

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.format.DateTimeFormatter

/**
 * 年度视图 - 按年份折叠的时间轴
 */
@Composable
fun YearlyView(
    milestones: List<Milestone>,
    onMilestoneClick: (Milestone) -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 按年份分组
    val groupedByYear = remember(milestones) {
        milestones.groupBy { it.date.year }
            .toSortedMap(compareByDescending { it })
    }
    
    // 记录每个年份的展开状态
    val expandedYears = remember { mutableStateMapOf<Int, Boolean>() }
    
    // 默认展开当前年份
    LaunchedEffect(groupedByYear.keys) {
        val currentYear = java.time.LocalDate.now().year
        groupedByYear.keys.forEach { year ->
            expandedYears[year] = year == currentYear
        }
    }
    
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp)
    ) {
        groupedByYear.forEach { (year, yearMilestones) ->
            item(key = "year_$year") {
                YearHeader(
                    year = year,
                    count = yearMilestones.size,
                    isExpanded = expandedYears[year] ?: false,
                    onToggle = { expandedYears[year] = !(expandedYears[year] ?: false) }
                )
            }
            
            if (expandedYears[year] == true) {
                items(
                    items = yearMilestones.sortedByDescending { it.date },
                    key = { "milestone_${it.id}" }
                ) { milestone ->
                    YearlyMilestoneItem(
                        milestone = milestone,
                        onClick = { onMilestoneClick(milestone) }
                    )
                }
            }
        }
        
        // 底部空间
        item { Spacer(modifier = Modifier.height(80.dp)) }
    }
}

/**
 * 年份标题组件
 */
@Composable
fun YearHeader(
    year: Int,
    count: Int,
    isExpanded: Boolean,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    val rotationAngle by animateFloatAsState(
        targetValue = if (isExpanded) 0f else -90f,
        label = "Arrow Rotation"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .shadow(elevation = 2.dp, shape = RoundedCornerShape(16.dp))
            .clickable(onClick = onToggle),
        shape = RoundedCornerShape(16.dp),
        color = Color.White
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 年份和数量
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 年份图标
                Card(
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    ),
                    modifier = Modifier.size(48.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Text(
                            text = year.toString(),
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                    }
                }
                
                Column {
                    // 年份标题
                    Text(
                        text = "$year" + "年",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onBackground
                    )
                    
                    // 里程碑数量
                    Text(
                        text = "共" + "$count" + "个里程碑",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f)
                    )
                }
            }
            
            // 展开/折叠箭头
            Icon(
                imageVector = if (isExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                contentDescription = if (isExpanded) "折叠" else "展开",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .size(32.dp)
                    .rotate(rotationAngle)
            )
        }
    }
}

/**
 * 年度视图里程碑项
 */
@Composable
fun YearlyMilestoneItem(
    milestone: Milestone,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 8.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 日期卡片
                Card(
                    shape = RoundedCornerShape(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = milestone.category.color.copy(alpha = 0.1f)
                    ),
                    border = ButtonDefaults.outlinedButtonBorder.copy(
                        width = 1.dp,
                        brush = androidx.compose.ui.graphics.SolidColor(milestone.category.color.copy(alpha = 0.3f))
                    ),
                    modifier = Modifier.width(80.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text(
                            text = milestone.date.format(DateTimeFormatter.ofPattern("MM月")),
                            fontSize = 12.sp,
                            color = milestone.category.color,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = milestone.date.format(DateTimeFormatter.ofPattern("dd日")),
                            fontSize = 16.sp,
                            color = milestone.category.color,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 内容区域
                Column(modifier = Modifier.weight(1f)) {
                    // 标题
                    Text(
                        text = milestone.title,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onBackground,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 类别标签
                    Surface(
                        color = milestone.category.color.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(4.dp),
                        modifier = Modifier.wrapContentWidth()
                    ) {
                        Text(
                            text = milestone.category.displayName,
                            fontSize = 12.sp,
                            color = milestone.category.color,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 类别图标
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(milestone.category.color.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = milestone.category.icon,
                        contentDescription = null,
                        tint = milestone.category.color,
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            // 添加描述文本
            if (milestone.description.isNotBlank()) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = milestone.description,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    lineHeight = 20.sp
                )
            }
            
            // 里程碑类型标记（如果不是普通类型）
            if (milestone.milestoneType != MilestoneType.REGULAR) {
                Spacer(modifier = Modifier.height(8.dp))
                val (typeColor, typeName) = when(milestone.milestoneType) {
                    MilestoneType.DECISION -> Pair(Color(0xFF9C27B0), "决策型事件") // 紫色
                    MilestoneType.OPPORTUNITY -> Pair(Color(0xFF4CAF50), "机遇型事件") // 绿色
                    MilestoneType.IMPACT -> Pair(Color(0xFFE91E63), "冲击型事件") // 粉色
                    else -> Pair(Color.Gray, "")
                }
                
                Surface(
                    color = typeColor.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(6.dp)
                                .background(typeColor, CircleShape)
                        )
                        Text(
                            text = typeName,
                            fontSize = 12.sp,
                            color = typeColor,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
} 