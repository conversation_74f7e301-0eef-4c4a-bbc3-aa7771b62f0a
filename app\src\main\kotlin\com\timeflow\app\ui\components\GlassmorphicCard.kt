package com.timeflow.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 磨玻璃效果卡片组件
 * 
 * 提供现代感的半透明磨砂玻璃效果，适用于创建高级UI元素。
 * 可以自定义圆角大小、透明度和渐变效果。
 * 
 * @param modifier Modifier 应用于卡片的修饰符
 * @param cornerRadius Dp 卡片圆角大小，默认16dp
 * @param containerAlpha Float 卡片背景透明度，默认0.7f
 * @param gradientColors 渐变色列表，默认使用主题色的浅色渐变
 * @param content @Composable BoxScope.() -> Unit 卡片内容
 */
@Composable
fun GlassmorphicCard(
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 16.dp,
    containerAlpha: Float = 0.7f,
    gradientColors: List<Color> = listOf(
        MaterialTheme.colorScheme.surface.copy(alpha = 0.3f),
        MaterialTheme.colorScheme.surface.copy(alpha = 0.15f)
    ),
    elevation: Dp = 0.dp,
    content: @Composable BoxScope.() -> Unit
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = containerAlpha)
        ),
        shape = RoundedCornerShape(cornerRadius),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation)
    ) {
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(cornerRadius))
                .background(
                    brush = Brush.verticalGradient(
                        colors = gradientColors
                    )
                )
                .graphicsLayer {
                    this.alpha = 0.99f // 轻微调整透明度以激活渲染层
                }
                .blur(radius = 0.5.dp),
            content = content
        )
    }
} 