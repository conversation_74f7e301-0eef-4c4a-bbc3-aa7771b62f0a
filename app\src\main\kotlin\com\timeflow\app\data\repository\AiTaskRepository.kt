package com.timeflow.app.data.repository

import com.timeflow.app.data.ai.model.*
import com.timeflow.app.data.model.Task
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

/**
 * AI任务仓库接口 - 处理所有与AI任务功能相关的数据操作
 */
interface AiTaskRepository {

    /**
     * 获取任务的AI拆分结果
     * @param taskId 任务ID
     * @return 任务拆分结果Flow
     */
    fun getTaskDecomposition(taskId: String): Flow<AiTaskDecomposition?>
    
    /**
     * 为任务创建新的AI拆分
     * @param taskId 原始任务ID
     * @param subTasks 拆分后的子任务列表
     * @param aiRecommendation AI推荐说明
     * @param reasonForDecomposition 拆分理由
     * @return 创建的任务拆分结果
     */
    suspend fun createTaskDecomposition(
        taskId: String,
        subTasks: List<AiSubTask>,
        aiRecommendation: String? = null,
        reasonForDecomposition: String? = null
    ): AiTaskDecomposition
    
    /**
     * 更新任务的AI拆分结果
     * @param decomposition 更新后的任务拆分
     * @return 更新是否成功
     */
    suspend fun updateTaskDecomposition(decomposition: AiTaskDecomposition): Boolean
    
    /**
     * 获取任务的AI时间预估
     * @param taskId 任务ID
     * @return 时间预估结果Flow
     */
    fun getTimeEstimation(taskId: String): Flow<AiTimeEstimation?>
    
    /**
     * 创建任务的AI时间预估
     * @param taskId 任务ID
     * @param estimatedMinutes 预估时长（分钟）
     * @param confidence 置信度
     * @param basis 预估依据
     * @param similarTasks 类似任务列表
     * @param adjustmentFactors 调整因素列表
     * @return 创建的时间预估结果
     */
    suspend fun createTimeEstimation(
        taskId: String,
        estimatedMinutes: Int,
        confidence: Float,
        basis: String,
        similarTasks: List<SimilarTaskReference> = emptyList(),
        adjustmentFactors: List<AdjustmentFactor> = emptyList()
    ): AiTimeEstimation
    
    /**
     * 获取用户的工作效率数据
     * @param userId 用户ID
     * @return 用户效率数据Flow
     */
    fun getUserEfficiencyData(userId: String): Flow<UserEfficiencyData?>
    
    /**
     * 更新用户的工作效率数据
     * @param efficiencyData 更新后的效率数据
     * @return 更新是否成功
     */
    suspend fun updateUserEfficiencyData(efficiencyData: UserEfficiencyData): Boolean
    
    /**
     * 获取任务的AI洞察
     * @param taskId 任务ID
     * @return 任务洞察结果Flow
     */
    fun getTaskInsight(taskId: String): Flow<AiTaskInsight?>
    
    /**
     * 创建或更新任务的AI洞察
     * @param insight 任务洞察数据
     * @return 创建或更新后的洞察结果
     */
    suspend fun saveTaskInsight(insight: AiTaskInsight): AiTaskInsight
    
    /**
     * 获取推荐的任务时间段
     * @param taskId 任务ID
     * @param fromDate 开始日期时间
     * @param toDate 结束日期时间
     * @return 推荐时间段列表Flow
     */
    fun getRecommendedTimeSlots(
        taskId: String,
        fromDate: LocalDateTime,
        toDate: LocalDateTime
    ): Flow<List<TimeSlotRecommendation>>
    
    /**
     * 为用户创建一个新的AI对话
     * @param userId 用户ID
     * @return 创建的对话
     */
    suspend fun createConversation(userId: String): AiConversation
    
    /**
     * 获取用户的所有对话列表
     * @param userId 用户ID
     * @return 对话列表Flow
     */
    fun getUserConversations(userId: String): Flow<List<AiConversation>>
    
    /**
     * 获取特定对话的消息
     * @param conversationId 对话ID
     * @return 消息列表Flow
     */
    fun getConversationMessages(conversationId: String): Flow<List<ConversationMessage>>
    
    /**
     * 向对话添加新消息
     * @param conversationId 对话ID
     * @param message 新消息
     * @return 添加后的对话
     */
    suspend fun addMessage(conversationId: String, message: ConversationMessage): AiConversation
    
    /**
     * 通过自然语言分析创建任务
     * @param userId 用户ID
     * @param naturalLanguageInput 自然语言输入
     * @return 创建的任务
     */
    suspend fun createTaskFromNaturalLanguage(userId: String, naturalLanguageInput: String): Task
    
    /**
     * 智能拆分任务
     * @param taskId 要拆分的任务ID
     * @return 拆分后的子任务列表
     */
    suspend fun breakdownTask(taskId: String): List<Task>
    
    /**
     * 生成并应用最佳任务计划
     * @param userId 用户ID
     * @param date 日期
     * @return 生成的任务计划
     */
    suspend fun generateOptimalTaskSchedule(userId: String, date: LocalDateTime): List<Task>
    
    /**
     * 处理用户消息并获取AI响应
     * @param conversationId 会话ID
     * @param userMessage 用户消息内容
     * @param messageHistory 消息历史记录
     * @param relatedTaskId 相关任务ID（如果有）
     * @param relatedTaskTitle 相关任务标题（如果有）
     * @return 更新后的会话
     */
    suspend fun processUserMessage(
        conversationId: String,
        userMessage: String,
        messageHistory: List<ConversationMessage>,
        relatedTaskId: String? = null,
        relatedTaskTitle: String? = null
    ): AiConversation

    /**
     * AI智能任务拆解 - 将任务拆解为子任务标题列表
     * @param task 要拆解的任务
     * @return 子任务标题列表
     */
    suspend fun aiSplitTaskToSubtasks(task: Task): List<String>
} 