package com.timeflow.app.data.repository

import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskTag
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 任务仓库接口
 */
interface TaskRepository {
    /**
     * 获取所有任务
     */
    suspend fun getAllTasks(): List<Task>
    
    /**
     * 观察所有任务变化
     */
    fun observeAllTasks(): Flow<List<Task>>
    
    /**
     * 根据ID获取任务
     */
    suspend fun getTaskById(taskId: String): Task?
    
    /**
     * 观察指定ID的任务变化
     */
    fun observeTaskById(taskId: String): Flow<Task?>
    
    /**
     * 插入任务
     */
    suspend fun insertTask(task: Task)
    
    /**
     * 保存任务（根据ID决定插入或更新）
     */
    suspend fun saveTask(task: Task)
    
    /**
     * 插入多个任务
     */
    suspend fun insertTasks(tasks: List<Task>)
    
    /**
     * 更新任务
     */
    suspend fun updateTask(task: Task)
    
    /**
     * 删除任务
     */
    suspend fun deleteTask(task: Task)
    
    /**
     * 获取按优先级排序的任务
     */
    fun getTasksByPriority(): Flow<List<Task>>
    
    /**
     * 获取包含特定标签的任务
     */
    fun getTasksByTags(tags: List<TaskTag>): Flow<List<Task>>
    
    /**
     * 获取子任务
     */
    fun getChildTasks(parentId: String): Flow<List<Task>>
    
    /**
     * 获取子任务（立即返回而非Flow）
     */
    suspend fun getSubTasks(parentId: String): List<Task>
    
    /**
     * 获取根任务（没有父任务的任务）
     */
    fun getRootTasks(): Flow<List<Task>>
    
    /**
     * 更新任务状态
     */
    suspend fun updateTaskStatus(taskId: String, newStatus: String)
    
    /**
     * 更新任务完成状态
     */
    suspend fun updateTaskCompletion(taskId: String, isCompleted: Boolean)
    
    /**
     * 更新任务优先级
     */
    suspend fun updateTaskPriority(taskId: String, priority: Priority)
    
    /**
     * 更新任务是否有子任务标志
     */
    suspend fun updateTaskHasSubtasks(taskId: String, hasSubtasks: Boolean)
    
    /**
     * 更新任务排序
     */
    suspend fun updateTaskOrder(taskId: String, orderPosition: Int)
    
    /**
     * 批量更新任务顺序
     */
    suspend fun updateTasksOrder(updates: List<Pair<String, Int>>)
    
    /**
     * 更新任务的父任务
     */
    suspend fun updateTaskParent(taskId: String, parentId: String?, depth: Int)
    
    /**
     * 更新任务进度
     */
    suspend fun updateTaskProgress(taskId: String, progress: Float)
    
    /**
     * 获取所有任务标签
     */
    fun getAllTags(): Flow<List<TaskTag>>
    
    /**
     * 按分组获取任务
     */
    fun getTasksByGroup(groupId: String): Flow<List<Task>>
    
    /**
     * 按分组类型获取任务
     */
    fun getTasksByGroupType(groupType: String): Flow<List<Task>>
    
    /**
     * 更新任务分组和顺序
     */
    suspend fun updateTaskGroup(taskId: String, groupId: String?, newOrder: Int)
    
    /**
     * 获取分组内最大顺序值
     */
    suspend fun getMaxOrderInGroup(groupId: String): Int
    
    /**
     * 删除任务
     */
    suspend fun deleteTask(taskId: String)
    
    /**
     * 生成示例数据，只在应用首次启动或强制刷新时执行
     */
    suspend fun generateSampleDataIfNeeded(force: Boolean = false)
    
    /**
     * 获取任务冲突列表
     */
    suspend fun getConflictTasks(): List<Task>
    
    /**
     * 观察任务冲突
     */
    fun observeTaskConflicts(): Flow<List<Task>>
    
    /**
     * 获取默认列ID（替代Kanban列ID）
     */
    suspend fun getDefaultColumnId(): String
    
    /**
     * 获取最近更新的任务
     * @return 最近修改过的任务列表
     */
    suspend fun getRecentlyUpdatedTasks(): List<Task>
    
    /**
     * 更新任务截止时间
     * @param taskId 任务ID
     * @param dueDate 新的截止时间
     */
    suspend fun updateTaskDueDate(taskId: String, dueDate: LocalDateTime)
    
    /**
     * 在事务中添加多个子任务
     * @param parentTaskId 父任务ID
     * @param subtasks 子任务列表
     */
    suspend fun addSubtasksInTransaction(parentTaskId: String, subtasks: List<Task>)
    
    /**
     * 获取引用指定任务作为父任务的所有任务
     * @param parentId 父任务ID
     * @return 引用该父任务的所有任务列表
     */
    suspend fun getTasksReferencingParent(parentId: String): List<Task>
    
    /**
     * 强制清理任务的所有相关引用（在外键约束错误时使用）
     * @param taskId 任务ID
     * @return 操作是否成功
     */
    suspend fun forceCleanupTaskReferences(taskId: String)
    
    /**
     * 更新任务颜色
     * @param taskId 任务ID
     * @param color 颜色值 (ARGB格式)
     */
    suspend fun updateTaskColor(taskId: String, color: Long)
    
    /**
     * 更新任务完成状态（新方法）
     * @param taskId 任务ID
     * @param isCompleted 是否已完成
     */
    suspend fun updateTaskCompletionStatus(taskId: String, isCompleted: Boolean)
    
    /**
     * 自动顺延过期的未完成任务到今天
     * 只会顺延那些日期未被用户手动修改过的任务
     * @return 被顺延的任务数量
     */
    suspend fun autoRescheduleOverdueTasks(): Int
    
    /**
     * 更新任务日期手动修改标记
     * @param taskId 任务ID
     * @param isManuallyModified 是否被手动修改
     */
    suspend fun updateTaskDateManuallyModified(taskId: String, isManuallyModified: Boolean)
    
    /**
     * 更新任务的日期时间，并标记为手动修改
     * 这个方法专门用于用户手动修改任务日期的场景
     * @param taskId 任务ID
     * @param startDate 开始日期时间（可为null）
     * @param dueDate 截止日期时间（可为null）
     */
    suspend fun updateTaskDateTime(taskId: String, startDate: LocalDateTime?, dueDate: LocalDateTime?)
    
    /**
     * 🆕 获取指定周范围内的浮动任务
     */
    suspend fun getFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task>
    
    /**
     * 🆕 观察指定周范围内的浮动任务变化
     */
    fun observeFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): Flow<List<Task>>
    
    /**
     * 🆕 获取未安排的浮动任务
     */
    suspend fun getUnscheduledFloatingTasksInWeek(weekStart: LocalDateTime, weekEnd: LocalDateTime): List<Task>
    
    /**
     * 🆕 将浮动任务安排到具体日期
     */
    suspend fun scheduleFloatingTask(taskId: String, scheduledDate: LocalDateTime)
    
    /**
     * 🆕 取消浮动任务的具体日期安排
     */
    suspend fun unscheduleFloatingTask(taskId: String)
    
    /**
     * 🆕 更新浮动任务顺序
     */
    suspend fun updateFloatingTaskOrder(taskId: String, newOrder: Int)
    
    /**
     * 🆕 创建浮动任务
     */
    suspend fun createFloatingTask(
        title: String,
        description: String = "",
        priority: Priority? = null,
        weekStart: LocalDateTime,
        weekEnd: LocalDateTime,
        estimatedMinutes: Int = 0
    ): Task
    
    /**
     * 根据目标ID获取关联的任务
     */
    suspend fun getTasksByGoalId(goalId: String): List<Task>
    
    /**
     * 观察指定目标ID的任务变化
     */
    fun observeTasksByGoalId(goalId: String): Flow<List<Task>>

    // 🔧 循环任务相关方法 - 暂时注释，待后续实现
    /*
    /**
     * 获取循环任务组（包括原始任务和所有生成的循环任务）
     */
    suspend fun getRecurrenceGroup(taskId: String): List<ModelTask>

    /**
     * 获取已完成的循环任务数量
     */
    suspend fun getCompletedRecurrenceTasks(taskId: String): List<ModelTask>

    /**
     * 获取所有循环任务
     */
    suspend fun getAllRecurringTasks(): List<ModelTask>
    */

    // TODO: 暂时注释掉这些方法，等TaskRepositoryCache问题解决后再启用
    /*
    /**
     * 获取任务统计数据
     */
    suspend fun getTaskStatistics(startDate: LocalDate, endDate: LocalDate): TaskStatistics

    /**
     * 获取任务完成率统计
     */
    suspend fun getCompletionRateByDateRange(startDate: LocalDate, endDate: LocalDate): Float

    /**
     * 获取任务分类统计
     */
    suspend fun getTaskCategoryStatistics(startDate: LocalDate, endDate: LocalDate): Map<String, Int>

    /**
     * 获取每日任务完成统计
     */
    suspend fun getDailyCompletionStats(startDate: LocalDate, endDate: LocalDate): Map<LocalDate, Int>

    /**
     * 获取任务优先级分布
     */
    suspend fun getTaskPriorityDistribution(startDate: LocalDate, endDate: LocalDate): Map<String, Int>
    */

    // 🔧 循环任务相关方法

    /**
     * 获取所有循环任务
     */
    suspend fun getRecurringTasks(): List<Task>

    /**
     * 根据日期范围获取任务
     */
    suspend fun getTasksByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Task>

    /**
     * 获取指定循环任务的所有实例
     */
    suspend fun getRecurringTaskInstances(originalTaskId: String): List<Task>

    /**
     * 更新循环任务设置
     */
    suspend fun updateRecurringSettings(taskId: String, isRecurring: Boolean, recurringPattern: String?)

    /**
     * 检查是否存在相同标题的循环任务实例
     */
    suspend fun hasRecurringTaskInstance(title: String, dateTime: LocalDateTime): Boolean
}

/**
 * 任务统计数据
 */
data class TaskStatistics(
    val totalTasks: Int,
    val completedTasks: Int,
    val pendingTasks: Int,
    val overdueTasks: Int,
    val completionRate: Float,
    val averageCompletionTime: Long, // 平均完成时间（毫秒）
    val mostProductiveHour: Int?, // 最高效的小时
    val tasksByCategory: Map<String, Int>,
    val tasksByPriority: Map<String, Int>
)