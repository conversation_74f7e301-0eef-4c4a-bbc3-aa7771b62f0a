package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import java.time.LocalDateTime
import java.util.UUID

/**
 * 感想记录数据模型
 */
data class Reflection(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val content: String,
    val createdTime: LocalDateTime = LocalDateTime.now(),
    val modifiedTime: LocalDateTime = LocalDateTime.now(),
    val moodEmoji: String, // 使用emoji表示心情
    val moodColor: Color, // 心情对应的颜色
    val tags: List<String> = emptyList(),
    val category: String = "", // 分类
    val isFavorite: Boolean = false,
    val isPrivate: Boolean = false,
    val metadata: Map<String, Any> = emptyMap() // 额外元数据，如位置信息、天气等
)

/**
 * 时间过滤器
 */
enum class TimeFilter {
    TODAY,
    YESTERDAY,
    THIS_WEEK,
    THIS_MONTH,
    CUSTOM_RANGE
}

/**
 * AI分析结果
 */
data class ReflectionAIAnalysis(
    val reflectionId: String,
    val sentimentScore: Float, // 情感分析分数，-1到1之间
    val keywords: List<String>, // 关键词
    val suggestedTags: List<String>, // 建议的标签
    val summary: String, // 摘要
    val insights: String // 洞察
)

/**
 * 感想统计数据
 */
data class ReflectionStats(
    val totalCount: Int,
    val favoriteCount: Int,
    val moodDistribution: Map<String, Int>, // 心情分布
    val categoryDistribution: Map<String, Int>, // 分类分布
    val dailyCount: Map<LocalDateTime, Int>, // 每日数量
    val weeklyCount: Map<Int, Int>, // 每周数量
    val monthlyCount: Map<Int, Int>, // 每月数量
    val averageLength: Int, // 平均长度
    val longestReflection: String, // 最长感想ID
    val mostUsedTags: List<Pair<String, Int>> // 最常用标签及其使用次数
) 