package com.timeflow.app.ui.screen.analytics

import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*
import javax.inject.Inject
import kotlin.random.Random
import com.timeflow.app.data.repository.TimeAnalyticsRepository
import com.timeflow.app.data.repository.TaskRepository
import kotlinx.coroutines.flow.collectLatest
import kotlin.time.Duration

/**
 * 任务数据类
 */
data class CompletedTaskData(
    val title: String,
    val duration: Long, // 以分钟为单位
    val pomos: Int
)

/**
 * 数据分析视图模型
 * 负责处理统计数据的获取、处理和提供
 */
@HiltViewModel
class AnalyticsViewModel @Inject constructor(
    private val analyticsDataService: AnalyticsDataService,
    private val timeAnalyticsRepository: TimeAnalyticsRepository,
    private val taskRepository: TaskRepository,
    private val analyticsInsightService: AnalyticsInsightService
) : ViewModel() {
    
    companion object {
        private const val TAG = "AnalyticsViewModel"
    }

    // 时间范围选项
    enum class TimeRange {
        DAY, WEEK, MONTH, YEAR
    }

    // 当前选中的时间范围
    private val _selectedTimeRange = MutableStateFlow(TimeRange.WEEK)
    val selectedTimeRange: StateFlow<TimeRange> = _selectedTimeRange.asStateFlow()
    
    // 生产力数据
    private val _productivityScore = MutableStateFlow(0)
    val productivityScore: StateFlow<Int> = _productivityScore.asStateFlow()
    
    private val _completionRate = MutableStateFlow(0f)
    val completionRate: StateFlow<Float> = _completionRate.asStateFlow()
    
    private val _interruptionCount = MutableStateFlow(0)
    val interruptionCount: StateFlow<Int> = _interruptionCount.asStateFlow()
    
    private val _comparisonData = MutableStateFlow("")
    val comparisonData: StateFlow<String> = _comparisonData.asStateFlow()
    
    // 时间分配数据
    private val _timeDistribution = MutableStateFlow<Map<String, Float>>(emptyMap())
    val timeDistribution: StateFlow<Map<String, Float>> = _timeDistribution.asStateFlow()
    
    // 效率趋势数据
    private val _efficiencyData = MutableStateFlow<List<Pair<String, Float>>>(emptyList())
    val efficiencyData: StateFlow<List<Pair<String, Float>>> = _efficiencyData.asStateFlow()
    
    // 目标统计数据
    private val _goalStatistics = MutableStateFlow<GoalStatistics?>(null)
    val goalStatistics: StateFlow<GoalStatistics?> = _goalStatistics.asStateFlow()
    
    // 习惯统计数据
    private val _habitStatistics = MutableStateFlow<HabitStatistics?>(null)
    val habitStatistics: StateFlow<HabitStatistics?> = _habitStatistics.asStateFlow()
    
    // 时间分布数据
    private val _timeDistributionData = MutableStateFlow<TimeDistributionData?>(null)
    val timeDistributionData: StateFlow<TimeDistributionData?> = _timeDistributionData.asStateFlow()
    
    // 任务标签数据
    private val _taskTags = MutableStateFlow<Map<String, Int>>(emptyMap())
    val taskTags: StateFlow<Map<String, Int>> = _taskTags.asStateFlow()
    
    // 热力图数据
    private val _heatmapData = MutableStateFlow<List<List<Float>>>(emptyList())
    val heatmapData: StateFlow<List<List<Float>>> = _heatmapData.asStateFlow()

    // 洞察数据
    private val _dailyInsights = MutableStateFlow<List<String>>(emptyList())
    val dailyInsights: StateFlow<List<String>> = _dailyInsights.asStateFlow()

    private val _weeklyInsights = MutableStateFlow<List<String>>(emptyList())
    val weeklyInsights: StateFlow<List<String>> = _weeklyInsights.asStateFlow()

    private val _actionSuggestions = MutableStateFlow<List<String>>(emptyList())
    val actionSuggestions: StateFlow<List<String>> = _actionSuggestions.asStateFlow()

    private val _yearlyAchievements = MutableStateFlow<List<String>>(emptyList())
    val yearlyAchievements: StateFlow<List<String>> = _yearlyAchievements.asStateFlow()

    // 活动详细数据
    private val _activityDetailData = MutableStateFlow<Map<String, com.timeflow.app.ui.screen.analytics.ActivityData>>(emptyMap())
    val activityDetailData: StateFlow<Map<String, com.timeflow.app.ui.screen.analytics.ActivityData>> = _activityDetailData.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误信息
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 已完成任务列表数据
    private val _completedTasks = MutableStateFlow<List<CompletedTaskData>>(emptyList())
    val completedTasks: StateFlow<List<CompletedTaskData>> = _completedTasks.asStateFlow()
    
    init {
        // 初始化时加载数据
        loadDataForRange(TimeRange.WEEK)
    }
    
    /**
     * 设置选定的时间范围
     */
    fun setTimeRange(range: TimeRange) {
        _selectedTimeRange.value = range
        loadDataForRange(range)
    }
    
    /**
     * 根据时间范围加载相应的数据
     */
    private fun loadDataForRange(range: TimeRange) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                Log.d(TAG, "开始加载${range.name}数据")
                
                // 并行加载所有数据
                val productivityDataDeferred = async { analyticsDataService.getProductivityScoreData(range) }
                val activityDistributionDeferred = async { analyticsDataService.getActivityDistributionData(range) }
                val goalStatisticsDeferred = async { analyticsDataService.getGoalStatistics(range) }
                val habitStatisticsDeferred = async { analyticsDataService.getHabitStatistics(range) }
                val timeDistributionDeferred = async { analyticsDataService.getTimeDistributionData(range) }
                val efficiencyTrendDeferred = async { analyticsDataService.getEfficiencyTrendData(range) }
                val activityDetailDeferred = async { analyticsDataService.getActivityDetailData(range) }
                
                // 等待所有数据加载完成
                val productivityData = productivityDataDeferred.await()
                val activityDistribution = activityDistributionDeferred.await()
                val goalStats = goalStatisticsDeferred.await()
                val habitStats = habitStatisticsDeferred.await()
                val timeDistData = timeDistributionDeferred.await()
                val efficiencyTrend = efficiencyTrendDeferred.await()
                val activityDetail = activityDetailDeferred.await()
                
                // 更新UI状态
                _productivityScore.value = productivityData.score
                _completionRate.value = productivityData.completionRate
                _interruptionCount.value = productivityData.interruptionCount
                _comparisonData.value = productivityData.comparisonData
                
                _timeDistribution.value = timeDistData // 现在直接是Map<String, Float>
                _goalStatistics.value = goalStats
                _habitStatistics.value = habitStats
                _efficiencyData.value = efficiencyTrend
                _activityDetailData.value = activityDetail
                
                // 加载完成的任务数据
                loadCompletedTasksData(range)
                
                // 生成热力图数据
                generateHeatmapData(range)

                // 加载洞察数据
                val dailyInsightsDeferred = async { analyticsInsightService.generateDailyInsights(range) }
                val weeklyInsightsDeferred = async { analyticsInsightService.generateWeeklyInsights() }
                val actionSuggestionsDeferred = async { analyticsInsightService.generateActionSuggestions() }
                val yearlyAchievementsDeferred = async { analyticsInsightService.generateYearlyAchievements() }

                _dailyInsights.value = dailyInsightsDeferred.await()
                _weeklyInsights.value = weeklyInsightsDeferred.await()
                _actionSuggestions.value = actionSuggestionsDeferred.await()
                _yearlyAchievements.value = yearlyAchievementsDeferred.await()

                Log.d(TAG, "数据加载完成: 生产力得分=${productivityData.score}, 完成率=${productivityData.completionRate}%")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载数据失败", e)
                _error.value = "数据加载失败: ${e.message}"
                
                // 加载失败时使用默认数据
                loadFallbackData()
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载已完成任务数据
     */
    private suspend fun loadCompletedTasksData(range: TimeRange) {
        try {
            val tasks = taskRepository.getAllTasks()
            val (startDate, endDate) = getDateRange(range)
            
            val completedTasksInRange = tasks.filter { task ->
                task.isCompleted && task.completedAt?.let { completedAt ->
                    val completedDate = completedAt.toLocalDate()
                    !completedDate.isBefore(startDate) && !completedDate.isAfter(endDate)
                } == true
            }
            
            val completedTaskData = completedTasksInRange.map { task ->
                CompletedTaskData(
                    title = task.title,
                    duration = task.actualTimeMinutes.toLong(),
                    pomos = (task.actualTimeMinutes / 25).coerceAtLeast(1) // 假设25分钟一个番茄钟
                )
            }
            
            _completedTasks.value = completedTaskData
            Log.d(TAG, "加载了${completedTaskData.size}个已完成任务")
            
        } catch (e: Exception) {
            Log.e(TAG, "加载已完成任务失败", e)
            _completedTasks.value = emptyList()
        }
    }
    
    /**
     * 生成热力图数据
     */
    private suspend fun generateHeatmapData(range: TimeRange) {
        try {
            // 根据时间范围生成不同的热力图
            val heatmapData = when (range) {
                TimeRange.DAY -> generateDailyHeatmap()
                TimeRange.WEEK -> generateWeeklyHeatmap()
                TimeRange.MONTH -> generateMonthlyHeatmap()
                TimeRange.YEAR -> generateYearlyHeatmap()
            }
            
            _heatmapData.value = heatmapData
            
        } catch (e: Exception) {
            Log.e(TAG, "生成热力图数据失败", e)
            // 使用默认热力图数据
            _heatmapData.value = generateDefaultHeatmap()
        }
    }
    
    /**
     * 生成每日热力图 (24小时)
     */
    private suspend fun generateDailyHeatmap(): List<List<Float>> {
        // 简化实现：生成单日24小时的热力图
        return listOf(
            (0..23).map { hour ->
                // 根据时间段模拟效率值
                when (hour) {
                    in 9..11 -> 0.7f + kotlin.random.Random.nextFloat() * 0.2f  // 上午高效
                    in 14..16 -> 0.6f + kotlin.random.Random.nextFloat() * 0.2f // 下午较高效
                    in 19..21 -> 0.4f + kotlin.random.Random.nextFloat() * 0.2f // 晚上中等
                    else -> 0.1f + kotlin.random.Random.nextFloat() * 0.2f      // 其他时间较低
                }
            }
        )
    }
    
    /**
     * 生成每周热力图 (7天x24小时)
     */
    private suspend fun generateWeeklyHeatmap(): List<List<Float>> {
        return List(7) { day ->
            List(24) { hour ->
                val isWeekend = day >= 5
                val isDaytime = hour in 9..17
                val baseValue = if (isDaytime && !isWeekend) 0.7f else 0.3f
                val randomFactor = (kotlin.random.Random.nextFloat() * 0.3f)
                (baseValue + randomFactor).coerceIn(0f, 1f)
            }
        }
    }
    
    /**
     * 生成每月热力图
     */
    private suspend fun generateMonthlyHeatmap(): List<List<Float>> {
        // 简化实现：生成30天的数据
        return List(30) { day ->
            List(24) { hour ->
                // 基于真实数据或模拟数据生成
                kotlin.random.Random.nextFloat()
            }
        }
    }
    
    /**
     * 生成每年热力图
     */
    private suspend fun generateYearlyHeatmap(): List<List<Float>> {
        // 简化实现：生成12个月的数据
        return List(12) { month ->
            List(30) { day ->
                kotlin.random.Random.nextFloat()
            }
        }
    }
    
    /**
     * 生成默认热力图数据
     */
    private fun generateDefaultHeatmap(): List<List<Float>> {
        return List(7) { day ->
            List(24) { hour ->
                val isWeekend = day >= 5
                val isDaytime = hour in 9..17
                val baseValue = if (isDaytime && !isWeekend) 0.5f else 0.2f
                (baseValue + kotlin.random.Random.nextFloat() * 0.3f).coerceIn(0f, 1f)
            }
        }
    }
    
    /**
     * 加载失败时的后备数据
     */
    private fun loadFallbackData() {
        Log.w(TAG, "使用后备数据")
        
        _productivityScore.value = 75
        _completionRate.value = 70f
        _interruptionCount.value = 5
        _comparisonData.value = "+5.0%"
        
        _timeDistribution.value = mapOf(
            "工作" to 40f,
            "学习" to 30f,
            "娱乐" to 20f,
            "其他" to 10f
        )
        
        _efficiencyData.value = listOf(
            "周一" to 70f,
            "周二" to 75f,
            "周三" to 60f,
            "周四" to 80f,
            "周五" to 85f,
            "周六" to 65f,
            "周日" to 55f
        )
        
        _heatmapData.value = generateDefaultHeatmap()
    }
    
    /**
     * 获取日期范围
     */
    private fun getDateRange(timeRange: TimeRange): Pair<LocalDate, LocalDate> {
        val now = LocalDate.now()
        return when (timeRange) {
            TimeRange.DAY -> now to now
            TimeRange.WEEK -> {
                val startOfWeek = now.minusDays(now.dayOfWeek.value - 1L)
                startOfWeek to startOfWeek.plusDays(6)
            }
            TimeRange.MONTH -> {
                val startOfMonth = now.withDayOfMonth(1)
                startOfMonth to now
            }
            TimeRange.YEAR -> {
                val startOfYear = now.withDayOfYear(1)
                startOfYear to now
            }
        }
    }
    
    /**
     * 预加载数据
     */
    fun preloadData() {
        loadDataForRange(_selectedTimeRange.value)
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadDataForRange(_selectedTimeRange.value)
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }

    // 保留一些原有的方法以保持向后兼容
    private fun loadDailyData() {
        Log.d(TAG, "加载每日数据")
        // 现在由loadDataForRange统一处理
    }
    
    private fun loadWeeklyData() {
        Log.d(TAG, "加载每周数据")
        // 现在由loadDataForRange统一处理
    }
    
    private fun loadMonthlyData() {
        Log.d(TAG, "加载每月数据")
        // 现在由loadDataForRange统一处理
    }
    
    private fun loadYearlyData() {
        Log.d(TAG, "加载每年数据")
        // 现在由loadDataForRange统一处理
    }
    
    // 为了兼容性保留的方法
    private fun loadRealTaskDistribution() {
        // 现在由AnalyticsDataService处理
    }
    
    private fun loadTaskDistributionForDate(date: LocalDate) {
        // 现在由AnalyticsDataService处理
    }
    
    private fun loadTaskDistributionForDateRange(startDate: LocalDate, endDate: LocalDate) {
        // 现在由AnalyticsDataService处理
    }
    
    private fun loadCompletedTasksForDate(date: LocalDate) {
        // 现在由loadCompletedTasksData处理
    }
    
    private fun loadTimeRangeData(range: TimeRange) {
        // 现在由loadDataForRange处理
    }
    
    private fun loadMockCompletedTasks() {
        // 现在由loadCompletedTasksData处理真实数据
    }
} 