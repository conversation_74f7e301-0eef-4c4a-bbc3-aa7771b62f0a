package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import androidx.room.ForeignKey
import androidx.room.Index
import com.timeflow.app.data.converter.DateTimeConverter
import com.timeflow.app.data.converter.StringListConverter
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.LocalTime

/**
 * 习惯实体类
 */
@Entity(
    tableName = "habits",
    foreignKeys = [
        ForeignKey(
            entity = Goal::class,
            parentColumns = ["id"],
            childColumns = ["relatedGoalId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [Index("relatedGoalId")]
)
@TypeConverters(DateTimeConverter::class, StringListConverter::class)
@Serializable
data class Habit(
    @PrimaryKey
    val id: String,
    
    // 基本信息
    val name: String,
    val description: String = "",
    val iconName: String = "favorite", // 图标名称
    val colorHex: String = "#f5c4c4", // 颜色十六进制值
    val category: String = "HEALTH", // 习惯类别
    val customCategoryId: String? = null, // 🔧 新增：自定义分类ID
    
    // 频率设置
    val frequencyType: String = "DAILY", // DAILY, WEEKLY, CUSTOM
    val frequencyDays: String = "", // JSON格式的星期列表，如 ["MONDAY", "WEDNESDAY", "FRIDAY"]
    val targetCount: Int = 1, // 目标次数（每天/每周）
    
    // 时间设置
    val reminderEnabled: Boolean = false,
    val reminderTime: String? = null, // HH:MM格式
    val fixedTime: String? = null, // 固定时间，HH:MM格式
    
    // 难度和目标关联
    val difficulty: String = "MEDIUM", // EASY, MEDIUM, HARD, EXTREME
    val relatedGoalId: String? = null, // 关联的目标ID
    
    // 统计信息
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val totalCompletions: Int = 0,
    
    // 时间戳
    @Contextual
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Contextual
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val isActive: Boolean = true,
    
    // 自定义设置
    val customEmoji: String = "",
    val notes: String = "",
    val sortOrder: Int = 0
)

/**
 * 习惯记录实体类
 */
@Entity(
    tableName = "habit_records",
    foreignKeys = [
        ForeignKey(
            entity = Habit::class,
            parentColumns = ["id"],
            childColumns = ["habitId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("habitId"), Index("date")]
)
@TypeConverters(DateTimeConverter::class)
@Serializable
data class HabitRecord(
    @PrimaryKey
    val id: String,
    
    val habitId: String,
    @Contextual
    val date: LocalDate,
    val completed: Boolean = false,
    @Contextual
    val completedAt: LocalDateTime? = null,
    
    // 完成详情
    val completionCount: Int = 1, // 当天完成次数
    val notes: String = "", // 当天备注
    val skipReason: String = "", // 跳过原因（如"生病"）
    val mood: String = "", // 完成时的心情
    
    // 时间戳
    @Contextual
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Contextual
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 习惯提醒设置实体类
 */
@Entity(
    tableName = "habit_reminders",
    foreignKeys = [
        ForeignKey(
            entity = Habit::class,
            parentColumns = ["id"],
            childColumns = ["habitId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("habitId")]
)
@TypeConverters(DateTimeConverter::class)
data class HabitReminder(
    @PrimaryKey
    val id: String,
    
    val habitId: String,
    val time: String, // HH:MM格式
    val days: String, // JSON格式的星期列表
    val enabled: Boolean = true,
    val message: String = "", // 自定义提醒消息
    
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) 