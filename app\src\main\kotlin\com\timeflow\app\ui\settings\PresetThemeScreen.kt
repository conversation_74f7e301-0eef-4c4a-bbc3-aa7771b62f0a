package com.timeflow.app.ui.settings

import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 预设主题ViewModel
 */
@HiltViewModel
class PresetThemeViewModel @Inject constructor() : ViewModel() {
    
    private val presetThemeManager = PresetThemeManager
    
    val presetThemeState = presetThemeManager.presetThemeState
    
    /**
     * 应用预设主题
     */
    fun applyPresetTheme(presetId: String) {
        viewModelScope.launch {
            try {
                val result = presetThemeManager.applyPresetTheme(presetId)
                if (result.isSuccess) {
                    Log.d("PresetThemeViewModel", "✅ 成功应用预设主题: $presetId")
                } else {
                    Log.e("PresetThemeViewModel", "❌ 应用预设主题失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("PresetThemeViewModel", "应用预设主题异常", e)
            }
        }
    }
    
    /**
     * 保存当前主题为预设
     */
    fun saveCurrentThemeAsPreset(name: String, description: String = "") {
        viewModelScope.launch {
            try {
                val result = presetThemeManager.saveCurrentThemeAsPreset(name, description)
                if (result.isSuccess) {
                    Log.d("PresetThemeViewModel", "✅ 成功保存预设主题: $name")
                } else {
                    Log.e("PresetThemeViewModel", "❌ 保存预设主题失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("PresetThemeViewModel", "保存预设主题异常", e)
            }
        }
    }
    
    /**
     * 删除预设主题
     */
    fun deletePresetTheme(presetId: String) {
        viewModelScope.launch {
            try {
                val result = presetThemeManager.deletePresetTheme(presetId)
                if (result.isSuccess) {
                    Log.d("PresetThemeViewModel", "✅ 成功删除预设主题: $presetId")
                } else {
                    Log.e("PresetThemeViewModel", "❌ 删除预设主题失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("PresetThemeViewModel", "删除预设主题异常", e)
            }
        }
    }
    
    /**
     * 更新预设主题
     */
    fun updatePresetTheme(presetId: String, name: String? = null, description: String? = null) {
        viewModelScope.launch {
            try {
                val result = presetThemeManager.updatePresetTheme(presetId, name, description)
                if (result.isSuccess) {
                    Log.d("PresetThemeViewModel", "✅ 成功更新预设主题: $presetId")
                } else {
                    Log.e("PresetThemeViewModel", "❌ 更新预设主题失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("PresetThemeViewModel", "更新预设主题异常", e)
            }
        }
    }
}

/**
 * 预设主题选择页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PresetThemeScreen(
    navController: NavController,
    onClose: () -> Unit = {},
    viewModel: PresetThemeViewModel = hiltViewModel()
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    
    // 状态管理
    val presetThemeState by viewModel.presetThemeState.collectAsState()
    var showSaveDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf<PresetTheme?>(null) }
    var showEditDialog by remember { mutableStateOf<PresetTheme?>(null) }
    
    // 全屏显示预设主题页面
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF9F9F9))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部导航栏
        TopAppBar(
            title = {
                Text(
                    text = "预设主题",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onClose) {
                    Icon(
                        imageVector = Icons.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = { showSaveDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Filled.Add,
                        contentDescription = "保存当前主题",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFFF9F9F9)
            )
        )
        
        // 预设主题网格
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            contentPadding = PaddingValues(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            items(presetThemeState.presets) { preset ->
                PresetThemeCard(
                    preset = preset,
                    isSelected = presetThemeState.currentPresetId == preset.id,
                    onApply = { viewModel.applyPresetTheme(preset.id) },
                    onDelete = if (!preset.isBuiltIn) {{ showDeleteDialog = preset }} else null,
                    onEdit = if (!preset.isBuiltIn) {{ showEditDialog = preset }} else null
                )
            }
        }
    }
    
    // 保存主题对话框
    if (showSaveDialog) {
        SaveThemeDialog(
            onDismiss = { showSaveDialog = false },
            onSave = { name, description ->
                viewModel.saveCurrentThemeAsPreset(name, description)
                showSaveDialog = false
            }
        )
    }
    
    // 删除确认对话框
    showDeleteDialog?.let { preset ->
        AlertDialog(
            onDismissRequest = { showDeleteDialog = null },
            title = { 
                Text(
                    text = "删除预设主题",
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text("确定要删除 \"${preset.name}\" 主题吗？此操作无法撤销。") 
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deletePresetTheme(preset.id)
                        showDeleteDialog = null
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = null }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 编辑主题对话框
    showEditDialog?.let { preset ->
        EditThemeDialog(
            preset = preset,
            onDismiss = { showEditDialog = null },
            onSave = { name, description ->
                viewModel.updatePresetTheme(preset.id, name, description)
                showEditDialog = null
            }
        )
    }
}

/**
 * 预设主题卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PresetThemeCard(
    preset: PresetTheme,
    isSelected: Boolean,
    onApply: () -> Unit,
    onDelete: (() -> Unit)? = null,
    onEdit: (() -> Unit)? = null
) {
    var showMenu by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(180.dp)
            .clickable { onApply() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                preset.previewColor.copy(alpha = 0.1f)
            } else {
                Color.White
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        ),
        border = if (isSelected) {
            BorderStroke(2.dp, preset.previewColor)
        } else null
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 主题预览区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(
                            Brush.horizontalGradient(
                                colors = preset.generateThumbnailColors().take(3)
                            )
                        )
                ) {
                    // 预览色块
                    Row(
                        modifier = Modifier.fillMaxSize(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        preset.generateThumbnailColors().take(3).forEach { color ->
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight()
                                    .background(color)
                            )
                        }
                    }
                    
                    // 选中指示器
                    if (isSelected) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Check,
                                contentDescription = "已选中",
                                tint = Color.White,
                                modifier = Modifier
                                    .size(32.dp)
                                    .background(
                                        Color.Black.copy(alpha = 0.5f),
                                        RoundedCornerShape(50)
                                    )
                                    .padding(6.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 主题信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = preset.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        if (preset.description.isNotEmpty()) {
                            Text(
                                text = preset.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        
                        // 内置/自定义标签
                        Text(
                            text = if (preset.isBuiltIn) "内置" else "自定义",
                            style = MaterialTheme.typography.labelSmall,
                            color = if (preset.isBuiltIn) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.secondary
                            },
                            modifier = Modifier
                                .background(
                                    if (preset.isBuiltIn) {
                                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                                    } else {
                                        MaterialTheme.colorScheme.secondary.copy(alpha = 0.1f)
                                    },
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                    
                    // 菜单按钮（仅自定义主题）
                    if (!preset.isBuiltIn) {
                        Box {
                            IconButton(
                                onClick = { showMenu = true },
                                modifier = Modifier.size(24.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.MoreVert,
                                    contentDescription = "更多选项",
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                            
                            DropdownMenu(
                                expanded = showMenu,
                                onDismissRequest = { showMenu = false }
                            ) {
                                onEdit?.let {
                                    DropdownMenuItem(
                                        text = { Text("编辑") },
                                        onClick = {
                                            it()
                                            showMenu = false
                                        },
                                        leadingIcon = {
                                            Icon(Icons.Filled.Edit, contentDescription = "编辑")
                                        }
                                    )
                                }
                                
                                onDelete?.let {
                                    DropdownMenuItem(
                                        text = { Text("删除", color = MaterialTheme.colorScheme.error) },
                                        onClick = {
                                            it()
                                            showMenu = false
                                        },
                                        leadingIcon = {
                                            Icon(
                                                Icons.Filled.Delete, 
                                                contentDescription = "删除",
                                                tint = MaterialTheme.colorScheme.error
                                            )
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 保存主题对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SaveThemeDialog(
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "保存当前主题",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("主题名称") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            if (name.isNotBlank()) {
                                onSave(name.trim(), description.trim())
                            }
                        },
                        enabled = name.isNotBlank()
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
}

/**
 * 编辑主题对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditThemeDialog(
    preset: PresetTheme,
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit
) {
    var name by remember { mutableStateOf(preset.name) }
    var description by remember { mutableStateOf(preset.description) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                Text(
                    text = "编辑主题",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("主题名称") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            if (name.isNotBlank()) {
                                onSave(name.trim(), description.trim())
                            }
                        },
                        enabled = name.isNotBlank()
                    ) {
                        Text("保存")
                    }
                }
            }
        }
    }
} 