package com.timeflow.app.service

import android.content.Context
import android.util.Log
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timeflow.app.data.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

// 通知配置DataStore
private val Context.notificationConfigDataStore by preferencesDataStore(name = "notification_config")

/**
 * 通知配置管理器
 * 负责管理动态通知配置，支持从配置文件或数据库读取设置
 */
@Singleton
class NotificationConfigManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "NotificationConfigManager"
        private val CONFIG_KEY = stringPreferencesKey("notification_config")
    }
    
    private val gson = Gson()
    
    /**
     * 获取通知配置
     */
    fun getNotificationConfig(): Flow<NotificationConfig> {
        return context.notificationConfigDataStore.data
            .map { preferences ->
                val configJson = preferences[CONFIG_KEY]
                if (configJson != null) {
                    try {
                        gson.fromJson(configJson, NotificationConfig::class.java)
                    } catch (e: Exception) {
                        Log.e(TAG, "解析通知配置失败，使用默认配置", e)
                        getDefaultConfig()
                    }
                } else {
                    getDefaultConfig()
                }
            }
    }
    
    /**
     * 保存通知配置
     */
    suspend fun saveNotificationConfig(config: NotificationConfig) {
        try {
            val configJson = gson.toJson(config)
            context.notificationConfigDataStore.edit { preferences ->
                preferences[CONFIG_KEY] = configJson
            }
            Log.d(TAG, "通知配置已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存通知配置失败", e)
        }
    }
    
    /**
     * 获取默认配置
     */
    private fun getDefaultConfig(): NotificationConfig {
        return NotificationConfig()
    }
    
    /**
     * 根据ID获取通知分类
     */
    suspend fun getCategoryById(categoryId: String): NotificationCategory? {
        return getNotificationConfig().map { config ->
            config.categories.find { it.id == categoryId }
        }.let { flow ->
            // 这里简化处理，实际应该使用first()
            null
        }
    }
    
    /**
     * 根据ID获取通知项目
     */
    suspend fun getItemById(itemId: String): NotificationItem? {
        return getNotificationConfig().map { config ->
            config.categories.flatMap { it.items }.find { it.id == itemId }
        }.let { flow ->
            // 这里简化处理，实际应该使用first()
            null
        }
    }
    
    /**
     * 更新通知项目状态
     */
    suspend fun updateItemEnabled(itemId: String, enabled: Boolean) {
        getNotificationConfig().map { config ->
            val updatedCategories = config.categories.map { category ->
                category.copy(
                    items = category.items.map { item ->
                        if (item.id == itemId) {
                            item.copy(enabled = enabled)
                        } else {
                            item
                        }
                    }
                )
            }
            config.copy(categories = updatedCategories)
        }.let { flow ->
            // 这里需要实际的实现来保存更新后的配置
            Log.d(TAG, "通知项目 $itemId 状态已更新为: $enabled")
        }
    }
    
    /**
     * 获取提醒时间选项
     */
    fun getReminderTimeOptions(): Flow<List<ReminderTimeOption>> {
        return getNotificationConfig().map { it.reminderTimeOptions }
    }
    
    /**
     * 获取用药提醒时间选项
     */
    fun getMedicationReminderTimeOptions(): Flow<List<ReminderTimeOption>> {
        return getNotificationConfig().map { it.medicationReminderTimeOptions }
    }
    
    /**
     * 从资源文件加载配置（可选功能）
     */
    private fun loadConfigFromAssets(): NotificationConfig? {
        return try {
            val inputStream = context.assets.open("notification_config.json")
            val configJson = inputStream.bufferedReader().use { it.readText() }
            gson.fromJson(configJson, NotificationConfig::class.java)
        } catch (e: Exception) {
            Log.w(TAG, "从assets加载配置失败，使用默认配置", e)
            null
        }
    }
    
    /**
     * 重置为默认配置
     */
    suspend fun resetToDefault() {
        val defaultConfig = getDefaultConfig()
        saveNotificationConfig(defaultConfig)
        Log.d(TAG, "通知配置已重置为默认设置")
    }
    
    /**
     * 验证配置完整性
     */
    private fun validateConfig(config: NotificationConfig): Boolean {
        return try {
            // 检查必要的分类是否存在
            val requiredCategories = listOf(
                "task_management",
                "health_management",
                "notification_style"
            )
            
            val existingCategories = config.categories.map { it.id }
            val hasAllRequired = requiredCategories.all { it in existingCategories }
            
            if (!hasAllRequired) {
                Log.w(TAG, "配置缺少必要的分类")
                return false
            }
            
            // 检查每个分类是否有有效的项目
            config.categories.forEach { category ->
                if (category.items.isEmpty()) {
                    Log.w(TAG, "分类 ${category.id} 没有配置项目")
                }
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "配置验证失败", e)
            false
        }
    }
    
    /**
     * 导出配置为JSON
     */
    suspend fun exportConfigAsJson(): String {
        return getNotificationConfig().map { config ->
            gson.toJson(config)
        }.let { flow ->
            // 这里简化处理，实际应该使用first()
            gson.toJson(getDefaultConfig())
        }
    }
    
    /**
     * 从JSON导入配置
     */
    suspend fun importConfigFromJson(configJson: String): Boolean {
        return try {
            val config = gson.fromJson(configJson, NotificationConfig::class.java)
            if (validateConfig(config)) {
                saveNotificationConfig(config)
                Log.d(TAG, "配置导入成功")
                true
            } else {
                Log.e(TAG, "导入的配置无效")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "导入配置失败", e)
            false
        }
    }
    
    /**
     * 获取配置统计信息
     */
    suspend fun getConfigStats(): ConfigStats {
        return getNotificationConfig().map { config ->
            val totalCategories = config.categories.size
            val totalItems = config.categories.sumOf { it.items.size }
            val enabledItems = config.categories.flatMap { it.items }.count { it.enabled }
            
            ConfigStats(
                totalCategories = totalCategories,
                totalItems = totalItems,
                enabledItems = enabledItems,
                disabledItems = totalItems - enabledItems
            )
        }.let { flow ->
            // 这里简化处理，实际应该使用first()
            ConfigStats(0, 0, 0, 0)
        }
    }
}

/**
 * 配置统计信息
 */
data class ConfigStats(
    val totalCategories: Int,
    val totalItems: Int,
    val enabledItems: Int,
    val disabledItems: Int
)
