package com.timeflow.app.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// 关联愿望主题配色
private val WishLinkPrimary = Color(0xFFFFD93D) // 金黄色
private val WishLinkSecondary = Color(0xFFFFF3C4) // 浅金色
private val WishLinkAccent = Color(0xFFFF6B9D) // 粉色
private val WishLinkBackground = Color(0xFFFFFBE6) // 极浅金色

/**
 * ✨ 关联愿望标记组件
 * 用于目标卡片上显示是否关联了愿望
 */
@Composable
fun WishLinkedBadge(
    isLinked: Boolean,
    wishTitle: String? = null,
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = isLinked,
        enter = scaleIn() + fadeIn(),
        exit = scaleOut() + fadeOut()
    ) {
        Surface(
            modifier = modifier
                .clip(RoundedCornerShape(12.dp))
                .clickable { onClick() },
            color = Color.Transparent
        ) {
            Box(
                modifier = Modifier.background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            WishLinkPrimary.copy(alpha = 0.2f),
                            WishLinkSecondary.copy(alpha = 0.3f)
                        )
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 闪烁星星效果
                    val infiniteTransition = rememberInfiniteTransition(label = "star_twinkle")
                    val starAlpha by infiniteTransition.animateFloat(
                        initialValue = 0.6f,
                        targetValue = 1f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(1500, easing = EaseInOutSine),
                            repeatMode = RepeatMode.Reverse
                        ),
                        label = "star_alpha"
                    )
                    
                    Icon(
                        imageVector = Icons.Default.AutoAwesome,
                        contentDescription = "关联愿望",
                        tint = WishLinkPrimary.copy(alpha = starAlpha),
                        modifier = Modifier.size(14.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = wishTitle?.take(8)?.plus("...") ?: "愿望",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = WishLinkPrimary
                    )
                }
            }
        }
    }
}

/**
 * 🌟 关联愿望详情卡片
 * 在目标详情页面展示完整的愿望信息
 */
@Composable
fun LinkedWishDetailCard(
    wish: com.timeflow.app.data.model.WishModel?,
    onUnlink: () -> Unit = {},
    onViewWish: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    wish?.let { wishData ->
        Surface(
            modifier = modifier
                .fillMaxWidth()
                .shadow(
                    elevation = 8.dp,
                    shape = RoundedCornerShape(20.dp),
                    spotColor = WishLinkPrimary.copy(alpha = 0.2f)
                ),
            shape = RoundedCornerShape(20.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 头部标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.AutoAwesome,
                            contentDescription = null,
                            tint = WishLinkPrimary,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "关联愿望",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color.DarkGray
                        )
                    }
                    
                    // 操作按钮
                    Row {
                        IconButton(
                            onClick = onViewWish,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Visibility,
                                contentDescription = "查看愿望",
                                tint = Color.Gray,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        
                        IconButton(
                            onClick = onUnlink,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.LinkOff,
                                contentDescription = "取消关联",
                                tint = Color.Gray,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 愿望主要信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.Top
                ) {
                    // 类别图标
                    Surface(
                        color = getCategoryColor(wishData.category).copy(alpha = 0.15f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = wishData.category.emoji,
                            fontSize = 20.sp,
                            modifier = Modifier.padding(8.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    // 愿望内容
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = wishData.title,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.DarkGray,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        if (wishData.description.isNotBlank()) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = wishData.description,
                                fontSize = 14.sp,
                                color = Color.Gray,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 优先级星星
                        Row {
                            repeat(wishData.priority) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null,
                                    tint = WishLinkPrimary,
                                    modifier = Modifier.size(12.dp)
                                )
                            }
                        }
                    }
                }
                
                // 愿望状态和标签
                if (wishData.tags.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        wishData.tags.take(3).forEach { tag ->
                            Surface(
                                color = WishLinkSecondary.copy(alpha = 0.5f),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "#$tag",
                                    fontSize = 10.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = WishLinkPrimary,
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                        
                        if (wishData.tags.size > 3) {
                            Surface(
                                color = Color.Gray.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "+${wishData.tags.size - 3}",
                                    fontSize = 10.sp,
                                    color = Color.Gray,
                                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 转化提示
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = WishLinkBackground,
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.TipsAndUpdates,
                            contentDescription = null,
                            tint = WishLinkPrimary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "此目标由愿望「${wishData.title}」转化而来",
                            fontSize = 12.sp,
                            color = Color.DarkGray,
                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                        )
                    }
                }
            }
        }
    }
}

/**
 * 🔗 快速关联愿望按钮
 * 用于在目标编辑页面快速关联愿望
 */
@Composable
fun QuickLinkWishButton(
    isLinked: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .clickable { onClick() },
        color = if (isLinked) WishLinkPrimary.copy(alpha = 0.1f) else Color.Gray.copy(alpha = 0.1f),
        shape = RoundedCornerShape(12.dp),
        border = BorderStroke(
            1.dp,
            if (isLinked) WishLinkPrimary.copy(alpha = 0.5f) else Color.Gray.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (isLinked) Icons.Default.Link else Icons.Default.LinkOff,
                contentDescription = if (isLinked) "已关联愿望" else "关联愿望",
                tint = if (isLinked) WishLinkPrimary else Color.Gray,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (isLinked) "已关联愿望" else "关联愿望",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = if (isLinked) WishLinkPrimary else Color.Gray
            )
            
            if (isLinked) {
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Default.AutoAwesome,
                    contentDescription = null,
                    tint = WishLinkPrimary,
                    modifier = Modifier.size(12.dp)
                )
            }
        }
    }
}

/**
 * 📋 愿望关联选择对话框
 * 让用户选择要关联的愿望
 */
@Composable
fun WishLinkSelectionDialog(
    availableWishes: List<com.timeflow.app.data.model.WishModel>,
    currentLinkedWishId: String?,
    onDismiss: () -> Unit,
    onWishSelected: (com.timeflow.app.data.model.WishModel?) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.AutoAwesome,
                    contentDescription = null,
                    tint = WishLinkPrimary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "关联愿望",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        },
        text = {
            Column {
                if (availableWishes.isEmpty()) {
                    Text(
                        text = "暂无可关联的愿望\n去愿望池添加一些愿望吧！",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                } else {
                    Text(
                        text = "选择要关联的愿望:",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    // 取消关联选项
                    if (currentLinkedWishId != null) {
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable { onWishSelected(null) },
                            color = Color.Gray.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.LinkOff,
                                    contentDescription = null,
                                    tint = Color.Gray,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "取消关联",
                                    fontSize = 14.sp,
                                    color = Color.Gray
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    
                    // 愿望列表
                    availableWishes.forEach { wish ->
                        val isSelected = wish.id == currentLinkedWishId
                        
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable { onWishSelected(wish) },
                            color = if (isSelected) 
                                WishLinkPrimary.copy(alpha = 0.2f) 
                            else 
                                Color.Transparent,
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = wish.category.emoji,
                                    fontSize = 16.sp
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = wish.title,
                                        fontSize = 14.sp,
                                        fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                                        color = if (isSelected) WishLinkPrimary else Color.DarkGray,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis
                                    )
                                    if (wish.description.isNotBlank()) {
                                        Text(
                                            text = wish.description,
                                            fontSize = 12.sp,
                                            color = Color.Gray,
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                    }
                                }
                                
                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.CheckCircle,
                                        contentDescription = "已选择",
                                        tint = WishLinkPrimary,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("完成", color = WishLinkPrimary)
            }
        }
    )
}

// 🎨 辅助函数
private fun getCategoryColor(category: com.timeflow.app.data.model.WishCategory): Color {
    return when (category) {
        com.timeflow.app.data.model.WishCategory.TRAVEL -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.SHOPPING -> Color(0xFFFF9800)
        com.timeflow.app.data.model.WishCategory.LEARNING -> Color(0xFF2196F3)
        com.timeflow.app.data.model.WishCategory.CAREER -> Color(0xFF9C27B0)
        com.timeflow.app.data.model.WishCategory.LIFESTYLE -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.HEALTH -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.HOBBY -> Color(0xFFFF5722)
        com.timeflow.app.data.model.WishCategory.RELATIONSHIP -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.OTHER -> Color(0xFF607D8B)
    }
} 