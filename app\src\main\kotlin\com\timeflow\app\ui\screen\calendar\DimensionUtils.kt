package com.timeflow.app.ui.screen.calendar

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp

/**
 * 单位转换工具类
 */
object DimensionUtils {
    /**
     * 将dp值转换为像素值
     */
    @Composable
    fun dpToPx(dp: Float): Float {
        return with(LocalDensity.current) { dp.toDp().toPx() }
    }

    /**
     * 将像素值转换为dp值
     */
    @Composable
    fun pxToDp(px: Float): Dp {
        return with(LocalDensity.current) { px.toDp() }
    }
} 