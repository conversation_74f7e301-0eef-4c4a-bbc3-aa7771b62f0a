package com.timeflow.app.data.repository

import android.util.Log
import androidx.room.RoomDatabase
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.reflect.KClass

/**
 * 仓库基类
 * 提供统一的数据访问模式和缓存机制
 * @param T 实体类型
 * @param ID 实体ID类型
 */
abstract class BaseRepository<T : Any, ID : Any> {
    
    // 缓存配置
    protected val cacheEnabled: Boolean = true
    protected val cacheTTLMs: Long = 60000 // 缓存有效期，默认1分钟
    
    // 查询缓存 - 缓存ID -> 实体对象
    private val entityCache = ConcurrentHashMap<ID, Pair<T, Long>>()
    
    // 查询缓存 - 查询签名 -> 列表结果
    private val queryCache = ConcurrentHashMap<String, Pair<List<T>, Long>>()
    
    // 缓存统计
    private val cacheHits = AtomicLong(0)
    private val cacheMisses = AtomicLong(0)
    
    // 数据库调度器 - 默认使用IO调度器
    protected val databaseDispatcher: CoroutineDispatcher = Dispatchers.IO
    
    /**
     * 从缓存获取单个实体
     * @param id 实体ID
     * @return 缓存的实体或null
     */
    protected fun getFromCache(id: ID): T? {
        if (!cacheEnabled) return null
        
        val cached = entityCache[id]
        val now = System.currentTimeMillis()
        
        return if (cached != null && now - cached.second < cacheTTLMs) {
            // 缓存命中
            cacheHits.incrementAndGet()
            cached.first
        } else {
            // 缓存未命中或已过期
            cacheMisses.incrementAndGet()
            if (cached != null) {
                // 清理过期缓存
                entityCache.remove(id)
            }
            null
        }
    }
    
    /**
     * 将实体添加到缓存
     * @param id 实体ID
     * @param entity 实体对象
     */
    protected fun addToCache(id: ID, entity: T) {
        if (!cacheEnabled) return
        
        entityCache[id] = Pair(entity, System.currentTimeMillis())
    }
    
    /**
     * 从缓存获取查询结果
     * @param querySignature 查询签名
     * @return 缓存的查询结果或null
     */
    protected fun getQueryFromCache(querySignature: String): List<T>? {
        if (!cacheEnabled) return null
        
        val cached = queryCache[querySignature]
        val now = System.currentTimeMillis()
        
        return if (cached != null && now - cached.second < cacheTTLMs) {
            // 缓存命中
            cacheHits.incrementAndGet()
            cached.first
        } else {
            // 缓存未命中或已过期
            cacheMisses.incrementAndGet()
            if (cached != null) {
                // 清理过期缓存
                queryCache.remove(querySignature)
            }
            null
        }
    }
    
    /**
     * 将查询结果添加到缓存
     * @param querySignature 查询签名
     * @param results 查询结果
     */
    protected fun addQueryToCache(querySignature: String, results: List<T>) {
        if (!cacheEnabled) return
        
        queryCache[querySignature] = Pair(results, System.currentTimeMillis())
    }
    
    /**
     * 创建查询签名
     * @param queryName 查询名称
     * @param params 查询参数
     * @return 查询签名
     */
    protected fun createQuerySignature(queryName: String, vararg params: Any?): String {
        return "$queryName:${params.joinToString(",") { it.toString() }}"
    }
    
    /**
     * 清除特定实体的缓存
     * @param id 实体ID
     */
    protected fun invalidateCache(id: ID) {
        entityCache.remove(id)
    }
    
    /**
     * 清除所有缓存
     */
    protected fun invalidateAllCache() {
        entityCache.clear()
        queryCache.clear()
    }
    
    /**
     * 安全执行数据库操作，在IO线程上执行并提供统一的异常处理
     * @param action 数据库操作
     * @return 操作结果
     */
    protected suspend fun <R> executeSafely(action: suspend () -> R): Result<R> {
        return try {
            withContext(databaseDispatcher) {
                Result.success(action())
            }
        } catch (e: CancellationException) {
            // 协程取消不视为错误，直接重新抛出
            throw e
        } catch (e: Exception) {
            Timber.e(e, "数据库操作失败")
            Result.failure(e)
        }
    }
    
    /**
     * 创建一个安全的Flow，在IO线程上执行并提供统一的异常处理
     * @param action 数据库操作
     * @return 包含操作结果的Flow
     */
    protected fun <R> flowSafely(action: suspend () -> R): Flow<Result<R>> = flow {
        try {
            val result = withContext(databaseDispatcher) {
                action()
            }
            emit(Result.success(result))
        } catch (e: CancellationException) {
            // 协程取消不视为错误，直接重新抛出
            throw e
        } catch (e: Exception) {
            Timber.e(e, "数据库操作失败")
            emit(Result.failure(e))
        }
    }.flowOn(databaseDispatcher)
    
    /**
     * 获取缓存统计信息
     * @return 缓存命中率信息
     */
    fun getCacheStats(): String {
        val hits = cacheHits.get()
        val misses = cacheMisses.get()
        val total = hits + misses
        val hitRate = if (total > 0) (hits.toDouble() / total) * 100 else 0.0
        
        return "缓存命中率: %.2f%% (命中: %d, 未命中: %d)".format(hitRate, hits, misses)
    }
    
    /**
     * 预热缓存
     * 子类可以重写此方法来预加载常用数据
     */
    open suspend fun warmupCache() {
        // 基类中为空实现，子类可以重写
    }
} 