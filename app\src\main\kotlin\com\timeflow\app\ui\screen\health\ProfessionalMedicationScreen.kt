package com.timeflow.app.ui.screen.health

import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.material3.LocalTextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*
import com.timeflow.app.utils.TimeZoneUtils
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.launch
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.material3.ButtonDefaults
import androidx.compose.foundation.lazy.rememberLazyListState

// 莫兰迪配色方案 - 柔和低饱和度色彩
private val MorandiSage = Color(0xFF9CAF88)          // 鼠尾草绿
private val MorandiBeige = Color(0xFFD4C4B0)         // 奶茶米色
private val MorandiGrayGreen = Color(0xFFA8B5A0)     // 灰绿色
private val MorandiLavender = Color(0xFFB5A7C7)      // 薰衣草灰
private val MorandiPink = Color(0xFFD4B5B0)          // 灰粉色
private val MorandiCream = Color(0xFFF5F3F0)         // 奶油白
private val MorandiWarm = Color(0xFFEFEBE7)          // 暖灰白
private val MorandiDarkGray = Color(0xFF6B6B6B)      // 深灰色
private val MorandiMediumGray = Color(0xFF8E8E8E)    // 中灰色
private val MorandiLightGray = Color(0xFFBBBBBB)     // 浅灰色

/**
 * 简洁用药记录页面 - 莫兰迪风格
 * 温和低饱和度配色，简约舒适的视觉体验
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfessionalMedicationScreen(
    navController: NavController,
    viewModel: ProfessionalMedicationViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val undoSnackbarState by viewModel.showUndoSnackbar.collectAsStateWithLifecycle()
    val deleteUndoSnackbarState by viewModel.showDeleteUndoSnackbar.collectAsStateWithLifecycle()
    val editDialogState by viewModel.showEditDialog.collectAsStateWithLifecycle()
    val heatmapDialogState by viewModel.showHeatmapDialog.collectAsStateWithLifecycle()
    val medicationActionsState by viewModel.showMedicationActions.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val snackbarHostState = remember { SnackbarHostState() }
    val deleteSnackbarHostState = remember { SnackbarHostState() }
    val scrollState = rememberLazyListState()
    
    // 状态栏处理
    DisposableEffect(Unit) {
        val activity = context as? android.app.Activity
        activity?.let { SystemBarManager.forceOpaqueStatusBar(it, false) }
        onDispose { }
    }
    
    var showAddDialog by remember { mutableStateOf(false) }
    var showTemplateSelector by remember { mutableStateOf(false) }
    var showAddTemplateDialog by remember { mutableStateOf(false) }
    
    // 模板状态管理
    var medicationTemplates by remember { mutableStateOf(getDefaultMedicationTemplates()) }
    
    // 🔧 新增：收集热力图数据
    val heatmapData by viewModel.getHeatmapData(
        heatmapDialogState?.medication?.id ?: "",
        heatmapDialogState?.selectedPeriod ?: HeatmapPeriod.WEEK
    ).collectAsState(initial = null)
    
    // 处理撤销Snackbar显示
    LaunchedEffect(undoSnackbarState?.medicationId) {
        if (undoSnackbarState != null) {
            snackbarHostState.showSnackbar(
                message = "占位消息", // 实际消息在UndoSnackbar中显示
                duration = SnackbarDuration.Indefinite
            )
        } else {
            snackbarHostState.currentSnackbarData?.dismiss()
        }
    }
    
    // 处理删除撤销Snackbar显示
    LaunchedEffect(deleteUndoSnackbarState?.medicationId) {
        if (deleteUndoSnackbarState != null) {
            deleteSnackbarHostState.showSnackbar(
                message = "占位消息", // 实际消息在DeleteUndoSnackbar中显示
                duration = SnackbarDuration.Indefinite
            )
        } else {
            deleteSnackbarHostState.currentSnackbarData?.dismiss()
        }
    }
    
    // 🔧 确保页面恢复时数据刷新
    LaunchedEffect(Unit) {
        viewModel.refreshData()
        viewModel.logCurrentPersistenceState()
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MorandiWarm)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight()),
            contentPadding = PaddingValues(bottom = 100.dp),
            state = scrollState
        ) {
            // 简洁顶部栏
            item {
                SimpleTopBar(
                    onBackClick = { navController.navigateUp() },
                    onRefreshClick = { 
                        viewModel.refreshData()
                        viewModel.logCurrentPersistenceState()
                    },
                    modifier = Modifier.padding(14.dp)
                )
            }
            

            
            // 今日用药概览
            item {
                TodayOverviewCard(
                    totalMedications = state.todayMedications.size,
                    completedCount = state.todayMedications.count { it.isCompleted },
                    modifier = Modifier.padding(horizontal = 14.dp, vertical = 6.dp)
                )
            }
            
            // 快速模板区域
            item {
                QuickTemplatesSection(
                    templates = medicationTemplates,
                    onTemplateClick = { template ->
                        // 从模板创建药物
                        val medication = ProfessionalMedication(
                            id = "template_${System.currentTimeMillis()}",
                            name = template.name,
                            dosage = template.dosage,
                            frequency = template.frequency,
                            scheduledTime = LocalTime.now(),
                            category = template.category
                        )
                        viewModel.addMedication(medication)
                    },
                    onAddTemplateClick = { showAddTemplateDialog = true },
                    onDeleteTemplate = { templateId ->
                        medicationTemplates = medicationTemplates.filter { it.id != templateId }
                    },
                    modifier = Modifier.padding(horizontal = 14.dp, vertical = 6.dp)
                )
            }
            
            // 今日用药列表
            item {
                Text(
                    text = "今日用药",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MorandiDarkGray,
                    modifier = Modifier.padding(horizontal = 14.dp, vertical = 6.dp)
                )
            }
            
            if (state.todayMedications.isEmpty()) {
                item {
                    EmptyStateCard(
                        onAddClick = { showAddDialog = true },
                        onTemplateClick = { showTemplateSelector = true },
                        modifier = Modifier.padding(horizontal = 14.dp, vertical = 6.dp)
                    )
                }
            } else {
                items(state.todayMedications.sortedBy { it.scheduledTime }) { medication ->
                    SwipeableMedicationCard(
                        medication = medication,
                        onMarkTaken = { viewModel.markMedicationTaken(it) },
                        onCardClick = { viewModel.showHeatmapDialog(it) },
                        onEditClick = { viewModel.showMedicationActions(it) },
                        onArchiveClick = { viewModel.archiveMedication(it.id) },
                        onDeleteClick = { viewModel.deleteMedication(it) },
                        modifier = Modifier.padding(horizontal = 14.dp, vertical = 3.dp)
                    )
                }
            }
            
            // 智能功能区域
            item {
                Spacer(modifier = Modifier.height(16.dp))
                SmartFeaturesSection(
                    state = state,
                    viewModel = viewModel,
                    modifier = Modifier.padding(horizontal = 14.dp)
                )
            }
        }
        
        // 浮动添加按钮
        FloatingActionButton(
            onClick = { showAddDialog = true },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(18.dp),
            containerColor = MorandiSage,
            contentColor = MorandiCream
        ) {
            Icon(
                imageVector = Icons.Filled.Add,
                contentDescription = "添加用药",
                modifier = Modifier.size(20.dp)
            )
        }
        
                // Snackbar Host
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        ) { snackbarData ->
            UndoSnackbar(
                snackbarData = snackbarData,
                undoState = undoSnackbarState,
                onUndo = {
                    undoSnackbarState?.let { viewModel.undoMedicationTaken(it.medicationId) }
                },
                onDismiss = { viewModel.dismissUndoSnackbar() }
            )
        }
        
        // 删除撤销 Snackbar Host
        SnackbarHost(
            hostState = deleteSnackbarHostState,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = if (undoSnackbarState != null) 80.dp else 0.dp) // 避免重叠
        ) { snackbarData ->
            DeleteUndoSnackbar(
                snackbarData = snackbarData,
                undoState = deleteUndoSnackbarState,
                onUndo = {
                    deleteUndoSnackbarState?.let { viewModel.undoDeleteMedication(it.medicationId) }
                },
                onDismiss = { viewModel.dismissDeleteUndoSnackbar() }
            )
        }
    }
    
    // 添加用药对话框
    if (showAddDialog) {
        AddMedicationDialog(
            onDismiss = { showAddDialog = false },
            onAddMedication = { medication ->
                viewModel.addMedication(medication)
                showAddDialog = false
            }
        )
    }
    
    // 模板选择器
    if (showTemplateSelector) {
        TemplateSelectionDialog(
            templates = medicationTemplates,
            onDismiss = { showTemplateSelector = false },
            onTemplateSelected = { template ->
                val medication = ProfessionalMedication(
                    id = "template_${System.currentTimeMillis()}",
                    name = template.name,
                    dosage = template.dosage,
                    frequency = template.frequency,
                    scheduledTime = LocalTime.now(),
                    category = template.category
                )
                viewModel.addMedication(medication)
                showTemplateSelector = false
            }
        )
    }
    
    // 药物操作菜单
    medicationActionsState?.let { medication ->
        MedicationActionsBottomSheet(
            medication = medication,
            onDismiss = { viewModel.hideMedicationActions() },
            onEditClick = { viewModel.showEditDialog(medication) },
            onArchiveClick = { viewModel.archiveMedication(medication.id) }
        )
    }
    
    // 编辑药物对话框
    editDialogState?.let { medication ->
        EditMedicationDialog(
            medication = medication,
            onDismiss = { viewModel.hideEditDialog() },
            onUpdateMedication = { id: String, name: String, dosage: String, frequency: String, time: LocalTime, category: String ->
                viewModel.updateMedication(id, name, dosage, frequency, time, category)
            }
        )
    }
    
    // 显示热力图对话框
    if (heatmapDialogState != null && heatmapData != null) {
        MedicationHeatmapDialog(
            dialogState = heatmapDialogState!!,
            heatmapData = heatmapData!!,
            onDismiss = { viewModel.hideHeatmapDialog() },
            onPeriodChange = { viewModel.changeHeatmapPeriod(it) }
        )
    }
    
    // 添加模板对话框
    if (showAddTemplateDialog) {
        AddTemplateDialog(
            onDismiss = { showAddTemplateDialog = false },
            onCreateTemplate = { template ->
                // 添加到模板列表
                medicationTemplates = medicationTemplates + template
                showAddTemplateDialog = false
            }
        )
    }
}

/**
 * 简洁顶部栏 - 莫兰迪风格
 */
@Composable
private fun SimpleTopBar(
    onBackClick: () -> Unit,
    onRefreshClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onBackClick) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = MorandiDarkGray,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(6.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "用药记录",
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
            Text(
                text = "记录您的日常用药",
                fontSize = 12.sp,
                color = MorandiMediumGray
            )
        }
        
        // 🔧 添加刷新按钮
        if (onRefreshClick != null) {
            IconButton(
                onClick = onRefreshClick,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新数据",
                    tint = MorandiSage,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}

/**
 * 今日概览卡片 - 莫兰迪风格
 */
@Composable
private fun TodayOverviewCard(
    totalMedications: Int,
    completedCount: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成进度
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "$completedCount/$totalMedications",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    color = MorandiSage
                )
                Text(
                    text = "已完成",
                    fontSize = 11.sp,
                    color = MorandiMediumGray
                )
            }
            
            // 分隔线
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .height(32.dp)
                    .background(MorandiBeige)
            )
            
            // 完成率
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val rate = if (totalMedications > 0) {
                    (completedCount * 100 / totalMedications)
                } else 0
                Text(
                    text = "$rate%",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    color = MorandiGrayGreen
                )
                Text(
                    text = "完成率",
                    fontSize = 11.sp,
                    color = MorandiMediumGray
                )
            }
        }
    }
}

/**
 * 快速模板区域 - 优化版：缩小字体、分列显示、一键隐藏
 */
@Composable
private fun QuickTemplatesSection(
    templates: List<MedicationTemplate>,
    onTemplateClick: (MedicationTemplate) -> Unit,
    onAddTemplateClick: () -> Unit,
    onDeleteTemplate: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var isTemplatesVisible by remember { mutableStateOf(true) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 标题行，包含隐藏/显示按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "常用模板",
                        fontSize = 13.sp, // 稍微增大字体到13sp
                        fontWeight = FontWeight.Medium,
                        color = MorandiDarkGray
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Text(
                        text = "(${templates.size}个)",
                        fontSize = 11.sp, // 稍微增大辅助文字到11sp
                        color = MorandiMediumGray
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    // 一键隐藏/显示按钮
                    TextButton(
                        onClick = { isTemplatesVisible = !isTemplatesVisible },
                        contentPadding = PaddingValues(horizontal = 6.dp, vertical = 2.dp),
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = MorandiSage
                        ),
                        modifier = Modifier.height(24.dp)
                    ) {
                        Text(
                            text = if (isTemplatesVisible) "隐藏" else "显示",
                            fontSize = 11.sp, // 稍微增大按钮文字
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.width(3.dp))
                        Icon(
                            imageVector = if (isTemplatesVisible) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                            contentDescription = if (isTemplatesVisible) "隐藏模板" else "显示模板",
                            modifier = Modifier.size(12.dp) // 缩小图标
                        )
                    }
                    
                    Icon(
                        imageVector = Icons.Default.Medication,
                        contentDescription = "添加模板",
                        tint = MorandiSage,
                        modifier = Modifier
                            .size(14.dp)
                            .clickable { onAddTemplateClick() }
                    )
                }
            }
            
            // 模板内容区域，带动画显示/隐藏
            AnimatedVisibility(
                visible = isTemplatesVisible,
                enter = expandVertically(animationSpec = tween(300)) + fadeIn(animationSpec = tween(300)),
                exit = shrinkVertically(animationSpec = tween(300)) + fadeOut(animationSpec = tween(300))
            ) {
                Column {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 使用LazyVerticalGrid分列显示
                    val rowCount = (templates.size + 1) / 2 // 计算行数，向上取整
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2), // 分2列显示
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        modifier = Modifier.height((rowCount * 28).dp) // 动态计算高度，更紧凑
                    ) {
                        // 现有模板
                        items(templates) { template ->
                            CompactTemplateChip(
                                template = template,
                                onClick = { onTemplateClick(template) },
                                onDelete = { onDeleteTemplate(template.id) }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 模板芯片 - 莫兰迪风格（保留原版本）
 */
@Composable
private fun TemplateChip(
    template: MedicationTemplate,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        color = template.color.copy(alpha = 0.2f),
        border = BorderStroke(1.dp, template.color.copy(alpha = 0.4f))
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = template.icon,
                contentDescription = template.name,
                tint = template.color,
                modifier = Modifier.size(14.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = template.name,
                fontSize = 12.sp,
                color = template.color,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 紧凑型模板芯片 - 用于分列显示，支持删除功能
 */
@Composable
private fun CompactTemplateChip(
    template: MedicationTemplate,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(24.dp) // 固定高度，更紧凑
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp), // 更小的圆角
        color = template.color.copy(alpha = 0.15f), // 更淡的背景
        border = BorderStroke(0.5.dp, template.color.copy(alpha = 0.3f)) // 更细的边框
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 6.dp, vertical = 2.dp), // 更小的内边距
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Icon(
                imageVector = template.icon,
                contentDescription = template.name,
                tint = template.color,
                modifier = Modifier.size(10.dp) // 更小的图标
            )
            Spacer(modifier = Modifier.width(3.dp)) // 更小的间距
            Text(
                text = template.name,
                fontSize = 10.sp, // 稍微增大字体
                color = template.color,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis, // 防止文字溢出
                modifier = Modifier.weight(1f)
            )
            
            // 删除按钮 - 只对自定义模板显示
            if (!template.id.startsWith("default_")) {
                Spacer(modifier = Modifier.width(2.dp))
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除模板",
                    tint = MorandiMediumGray,
                    modifier = Modifier
                        .size(12.dp)
                        .clickable { onDelete() }
                )
            }
        }
    }
}


/**
 * 用药记录卡片 - 莫兰迪风格
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun MedicationRecordCard(
    medication: ProfessionalMedication,
    onMarkTaken: (ProfessionalMedication) -> Unit,
    onCardClick: (ProfessionalMedication) -> Unit = {},
    onEditClick: (ProfessionalMedication) -> Unit = {},
    onArchiveClick: (ProfessionalMedication) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = { onCardClick(medication) },
                onLongClick = { 
                    // 长按显示编辑和归档选项
                    onEditClick(medication)
                }
            ),
        shape = RoundedCornerShape(10.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (medication.isCompleted)
                MorandiSage.copy(alpha = 0.1f) else Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        border = if (medication.isCompleted) 
            BorderStroke(1.dp, MorandiSage.copy(alpha = 0.3f)) else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成状态按钮
            IconButton(
                onClick = { if (!medication.isCompleted) onMarkTaken(medication) },
                modifier = Modifier.size(32.dp)
            ) {
                if (medication.isCompleted) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "已完成",
                        tint = MorandiSage,
                        modifier = Modifier.size(20.dp)
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.RadioButtonUnchecked,
                        contentDescription = "标记完成",
                        tint = MorandiLightGray,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 药物信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = medication.name,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (medication.isCompleted) MorandiSage else MorandiDarkGray
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = medication.dosage,
                        fontSize = 11.sp,
                        color = MorandiMediumGray
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Text(
                        text = "•",
                        fontSize = 11.sp,
                        color = MorandiMediumGray
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Text(
                        text = medication.frequency,
                        fontSize = 11.sp,
                        color = MorandiMediumGray
                    )
                }
            }
            
            // 时间
            Text(
                text = medication.scheduledTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                fontSize = 12.sp,
                color = if (medication.isCompleted) MorandiSage else MorandiMediumGray,
                fontWeight = if (medication.isCompleted) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

/**
 * 空状态卡片 - 莫兰迪风格
 */
@Composable
private fun EmptyStateCard(
    onAddClick: () -> Unit,
    onTemplateClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Medication,
                contentDescription = "暂无用药",
                tint = MorandiLightGray,
                modifier = Modifier.size(36.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "今日还没有添加用药记录",
                fontSize = 14.sp,
                color = MorandiMediumGray,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                OutlinedButton(
                    onClick = onTemplateClick,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MorandiGrayGreen
                    ),
                    border = BorderStroke(1.dp, MorandiGrayGreen)
                ) {
                    Icon(
                        imageVector = Icons.Default.Speed,
                        contentDescription = "快速模板",
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("快速模板", fontSize = 12.sp)
                }
                
                Button(
                    onClick = onAddClick,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MorandiSage
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加用药",
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("添加用药", fontSize = 12.sp)
                }
            }
        }
    }
}

// 数据模型
data class MedicationTemplate(
    val id: String = java.util.UUID.randomUUID().toString(),
    val name: String,
    val dosage: String,
    val frequency: String,
    val icon: ImageVector,
    val color: Color,
    val category: String
)

/**
 * 获取常用药物模板 - 莫兰迪配色
 */
private fun getDefaultMedicationTemplates(): List<MedicationTemplate> {
    return listOf(
        MedicationTemplate(
            id = "default_1",
            name = "胰岛素",
            dosage = "按医嘱",
            frequency = "每日三次",
            icon = Icons.Default.Bloodtype,
            color = MorandiPink,
            category = "处方药"
        ),
        MedicationTemplate(
            id = "default_2",
            name = "维生素D",
            dosage = "1000IU",
            frequency = "每日一次",
            icon = Icons.Default.WbSunny,
            color = MorandiBeige,
            category = "保健品"
        ),
        MedicationTemplate(
            id = "default_3",
            name = "鱼油",
            dosage = "1粒",
            frequency = "每日一次",
            icon = Icons.Default.Waves,
            color = MorandiGrayGreen,
            category = "保健品"
        ),
        MedicationTemplate(
            id = "default_4",
            name = "维生素C",
            dosage = "500mg",
            frequency = "每日一次",
            icon = Icons.Default.LocalFlorist,
            color = MorandiSage,
            category = "保健品"
        ),
        MedicationTemplate(
            id = "default_5",
            name = "美白丸",
            dosage = "2粒",
            frequency = "每日一次",
            icon = Icons.Default.FaceRetouchingNatural,
            color = MorandiLavender,
            category = "美容保健"
        ),
        MedicationTemplate(
            id = "default_6",
            name = "钙片",
            dosage = "600mg",
            frequency = "每日一次",
            icon = Icons.Default.SevereCold,
            color = MorandiPink.copy(alpha = 0.8f),
            category = "保健品"
        )
    )
}

// 简化的对话框组件 - 莫兰迪风格
@Composable
private fun AddMedicationDialog(
    onDismiss: () -> Unit,
    onAddMedication: (ProfessionalMedication) -> Unit
) {
    var medicationName by remember { mutableStateOf("") }
    var dosage by remember { mutableStateOf("") }
    var frequency by remember { mutableStateOf("每日一次") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                "添加用药记录",
                fontSize = 16.sp,
                color = MorandiDarkGray
            ) 
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                OutlinedTextField(
                    value = medicationName,
                    onValueChange = { medicationName = it },
                    label = { Text("药物名称", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage
                    )
                )
                OutlinedTextField(
                    value = dosage,
                    onValueChange = { dosage = it },
                    label = { Text("剂量", fontSize = 12.sp) },
                    placeholder = { Text("如：100mg、1粒", fontSize = 11.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage
                    )
                )
                OutlinedTextField(
                    value = frequency,
                    onValueChange = { frequency = it },
                    label = { Text("频次", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage
                    )
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (medicationName.isNotBlank() && dosage.isNotBlank()) {
                        val medication = ProfessionalMedication(
                            id = "manual_${System.currentTimeMillis()}",
                            name = medicationName,
                            dosage = dosage,
                            frequency = frequency,
                            scheduledTime = LocalTime.now(),
                            category = "自定义"
                        )
                        onAddMedication(medication)
                    }
                },
                enabled = medicationName.isNotBlank() && dosage.isNotBlank()
            ) {
                Text("添加", fontSize = 12.sp, color = MorandiSage)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 12.sp, color = MorandiMediumGray)
            }
        },
        containerColor = Color.White
    )
}

@Composable
private fun TemplateSelectionDialog(
    templates: List<MedicationTemplate>,
    onDismiss: () -> Unit,
    onTemplateSelected: (MedicationTemplate) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                "选择药物模板",
                fontSize = 16.sp,
                color = MorandiDarkGray
            ) 
        },
        text = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(6.dp),
                modifier = Modifier.height(250.dp)
            ) {
                items(templates) { template ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onTemplateSelected(template) },
                        colors = CardDefaults.cardColors(
                            containerColor = template.color.copy(alpha = 0.15f)
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(10.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = template.icon,
                                contentDescription = template.name,
                                tint = template.color,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(10.dp))
                            Column {
                                Text(
                                    text = template.name,
                                    fontWeight = FontWeight.Medium,
                                    color = template.color,
                                    fontSize = 13.sp
                                )
                                Text(
                                    text = "${template.dosage} • ${template.frequency}",
                                    fontSize = 10.sp,
                                    color = MorandiMediumGray
                                )
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", fontSize = 12.sp, color = MorandiMediumGray)
            }
        },
        containerColor = Color.White
    )
}

/**
 * 智能功能区域组件
 */
@Composable
private fun SmartFeaturesSection(
    state: ProfessionalMedicationState,
    viewModel: ProfessionalMedicationViewModel,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 依从性分析卡片
        AdherenceAnalysisCard(
            analysis = state.adherenceAnalysis,
            weeklyChart = state.weeklyAdherenceChart,
            onViewDetails = { /* 查看详细分析 */ }
        )
        
        Spacer(modifier = Modifier.height(10.dp))
        
        // 个性化建议卡片
        if (state.personalizedSuggestions.isNotEmpty()) {
            PersonalizedSuggestionsCard(
                suggestions = state.personalizedSuggestions,
                onCompleteSuggestion = { viewModel.markSuggestionCompleted(it) }
            )
            
            Spacer(modifier = Modifier.height(10.dp))
        }
        
        // 智能提醒设置卡片
        SmartReminderCard(
            reminders = state.smartReminders,
            onSettingsClick = { /* 打开提醒设置 */ }
        )
        
        // 漏服原因分析卡片
        if (state.missedDosesByReason.isNotEmpty()) {
            Spacer(modifier = Modifier.height(10.dp))
            MissedDoseAnalysisCard(
                missedReasons = state.missedDosesByReason,
                onRecordMissedReason = { medicationId, reason, note -> 
                    viewModel.recordMissedDoseReason(medicationId, reason, note)
                }
            )
        }
    }
}

/**
 * 依从性分析卡片
 */
@Composable
private fun AdherenceAnalysisCard(
    analysis: AdherenceAnalysis,
    weeklyChart: List<AdherenceChartPoint>,
    onViewDetails: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(14.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "依从性分析",
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 16.sp),
                    fontWeight = FontWeight.Medium,
                    color = MorandiDarkGray
                )
                
                Text(
                    text = when (analysis.riskLevel) {
                        AdherenceRiskLevel.LOW -> "优秀"
                        AdherenceRiskLevel.MODERATE -> "良好"
                        AdherenceRiskLevel.HIGH -> "需改善"
                        AdherenceRiskLevel.CRITICAL -> "需关注"
                    },
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                    color = when (analysis.riskLevel) {
                        AdherenceRiskLevel.LOW -> MorandiSage
                        AdherenceRiskLevel.MODERATE -> MorandiBeige
                        AdherenceRiskLevel.HIGH -> Color(0xFFE6A875)
                        AdherenceRiskLevel.CRITICAL -> MorandiPink
                    },
                    modifier = Modifier
                        .background(
                            when (analysis.riskLevel) {
                                AdherenceRiskLevel.LOW -> MorandiSage.copy(alpha = 0.1f)
                                AdherenceRiskLevel.MODERATE -> MorandiBeige.copy(alpha = 0.1f)
                                AdherenceRiskLevel.HIGH -> Color(0xFFE6A875).copy(alpha = 0.1f)
                                AdherenceRiskLevel.CRITICAL -> MorandiPink.copy(alpha = 0.1f)
                            },
                            shape = RoundedCornerShape(6.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 3.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 依从率显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "${analysis.overallScore.toInt()}%",
                        style = MaterialTheme.typography.headlineMedium.copy(fontSize = 20.sp),
                        fontWeight = FontWeight.Medium,
                        color = when (analysis.riskLevel) {
                            AdherenceRiskLevel.LOW -> MorandiSage
                            AdherenceRiskLevel.MODERATE -> MorandiBeige
                            AdherenceRiskLevel.HIGH -> Color(0xFFE6A875)
                            AdherenceRiskLevel.CRITICAL -> MorandiPink
                        }
                    )
                    Text(
                        text = "总体依从率",
                        style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                        color = MorandiMediumGray
                    )
                }
                
                Column {
                    Text(
                        text = "${analysis.streak}天",
                        style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
                        fontWeight = FontWeight.Medium,
                        color = MorandiDarkGray
                    )
                    Text(
                        text = "连续服药",
                        style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                        color = MorandiMediumGray
                    )
                }
                
                Column {
                    Text(
                        text = "${analysis.totalMissedDoses}次",
                        style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp),
                        fontWeight = FontWeight.Medium,
                        color = if (analysis.totalMissedDoses > 0) MorandiPink else MorandiSage
                    )
                    Text(
                        text = "漏服次数",
                        style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                        color = MorandiMediumGray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(10.dp))
            
            // 趋势指示
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = when (analysis.improvementTrend) {
                        TrendDirection.IMPROVING -> Icons.Default.TrendingUp
                        TrendDirection.DECLINING -> Icons.Default.TrendingDown
                        TrendDirection.STABLE -> Icons.Default.TrendingFlat
                        TrendDirection.FLUCTUATING -> Icons.Default.ShowChart
                    },
                    contentDescription = null,
                    tint = when (analysis.improvementTrend) {
                        TrendDirection.IMPROVING -> MorandiSage
                        TrendDirection.DECLINING -> MorandiPink
                        TrendDirection.STABLE -> MorandiLavender
                        TrendDirection.FLUCTUATING -> MorandiBeige
                    },
                    modifier = Modifier.size(14.dp)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                Text(
                    text = when (analysis.improvementTrend) {
                        TrendDirection.IMPROVING -> "依从性正在改善"
                        TrendDirection.DECLINING -> "依从性有所下降"
                        TrendDirection.STABLE -> "依从性保持稳定"
                        TrendDirection.FLUCTUATING -> "依从性存在波动"
                    },
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                    color = MorandiMediumGray
                )
            }
        }
    }
}

/**
 * 个性化建议卡片
 */
@Composable
private fun PersonalizedSuggestionsCard(
    suggestions: List<PersonalizedSuggestion>,
    onCompleteSuggestion: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(14.dp)
        ) {
            Text(
                text = "个性化建议",
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 16.sp),
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
            
            Spacer(modifier = Modifier.height(10.dp))
            
            suggestions.take(2).forEach { suggestion ->
                if (!suggestion.isCompleted) {
                    SuggestionItem(
                        suggestion = suggestion,
                        onComplete = { onCompleteSuggestion(suggestion.id) }
                    )
                    
                    if (suggestion != suggestions.last()) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
    }
}

/**
 * 建议项目组件
 */
@Composable
private fun SuggestionItem(
    suggestion: PersonalizedSuggestion,
    onComplete: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        // 优先级指示器
        Box(
            modifier = Modifier
                .size(6.dp)
                .background(
                    when (suggestion.priority) {
                        SuggestionPriority.URGENT -> MorandiPink
                        SuggestionPriority.HIGH -> Color(0xFFE6A875)
                        SuggestionPriority.MEDIUM -> MorandiBeige
                        SuggestionPriority.LOW -> MorandiLavender
                    },
                    shape = CircleShape
                )
                .align(Alignment.Top)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = suggestion.title,
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
            
            Text(
                text = suggestion.description,
                style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                color = MorandiMediumGray,
                lineHeight = 14.sp
            )
            
            if (suggestion.actionText != null) {
                Spacer(modifier = Modifier.height(4.dp))
                
                TextButton(
                    onClick = onComplete,
                    contentPadding = PaddingValues(horizontal = 6.dp, vertical = 2.dp)
                ) {
                    Text(
                        text = suggestion.actionText,
                        style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                        color = MorandiSage
                    )
                }
            }
        }
    }
}

/**
 * 智能提醒卡片
 */
@Composable
private fun SmartReminderCard(
    reminders: List<SmartReminder>,
    onSettingsClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(14.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "智能提醒",
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 16.sp),
                    fontWeight = FontWeight.Medium,
                    color = MorandiDarkGray
                )
                
                IconButton(
                    onClick = onSettingsClick,
                    modifier = Modifier.size(20.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = "提醒设置",
                        tint = MorandiLavender,
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(6.dp))
            
            if (reminders.isNotEmpty()) {
                Text(
                    text = "已设置 ${reminders.count { it.isActive }} 个活跃提醒",
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                    color = MorandiMediumGray
                )
                
                Spacer(modifier = Modifier.height(6.dp))
                
                // 显示最近的提醒
                reminders.filter { it.isActive }.take(1).forEach { reminder ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = when (reminder.type) {
                                ReminderType.MEDICATION_TIME -> Icons.Default.AccessTime
                                ReminderType.MISSED_DOSE -> Icons.Default.Warning
                                ReminderType.REFILL_REMINDER -> Icons.Default.Refresh
                                else -> Icons.Default.Notifications
                            },
                            contentDescription = null,
                            tint = when (reminder.priority) {
                                ReminderPriority.URGENT -> MorandiPink
                                ReminderPriority.HIGH -> Color(0xFFE6A875)
                                ReminderPriority.NORMAL -> MorandiSage
                                ReminderPriority.LOW -> MorandiLavender
                            },
                            modifier = Modifier.size(14.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(6.dp))
                        
                        Column {
                            Text(
                                text = reminder.scheduledTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                                fontWeight = FontWeight.Medium,
                                color = MorandiDarkGray
                            )
                            Text(
                                text = reminder.message,
                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                                color = MorandiMediumGray
                            )
                        }
                    }
                }
            } else {
                Text(
                    text = "暂无活跃提醒",
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 11.sp),
                    color = MorandiMediumGray
                )
            }
        }
    }
}

/**
 * 漏服原因分析卡片
 */
@Composable
private fun MissedDoseAnalysisCard(
    missedReasons: Map<MissedDoseReason, Int>,
    onRecordMissedReason: (String, MissedDoseReason, String?) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(14.dp)
        ) {
            Text(
                text = "漏服原因分析",
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 16.sp),
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
            
            Spacer(modifier = Modifier.height(10.dp))
            
            missedReasons.entries.sortedByDescending { it.value }.take(3).forEach { (reason, count) ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = when (reason) {
                            MissedDoseReason.FORGOT -> "忘记服药"
                            MissedDoseReason.TOO_BUSY -> "太忙了"
                            MissedDoseReason.SIDE_EFFECTS -> "副作用"
                            MissedDoseReason.FEELING_BETTER -> "感觉好了"
                            MissedDoseReason.COST_CONCERNS -> "费用问题"
                            MissedDoseReason.TRAVEL -> "出行"
                            MissedDoseReason.RAN_OUT -> "药品用完"
                            MissedDoseReason.OTHER -> "其他原因"
                        },
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                        color = MorandiDarkGray
                    )
                    
                    Text(
                        text = "${count}次",
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                        fontWeight = FontWeight.Medium,
                        color = MorandiPink
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

/**
 * 可滑动删除的药物卡片
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
private fun SwipeableMedicationCard(
    medication: ProfessionalMedication,
    onMarkTaken: (ProfessionalMedication) -> Unit,
    onCardClick: (ProfessionalMedication) -> Unit,
    onEditClick: (ProfessionalMedication) -> Unit,
    onArchiveClick: (ProfessionalMedication) -> Unit,
    onDeleteClick: (ProfessionalMedication) -> Unit,
    modifier: Modifier = Modifier
) {
    val hapticFeedback = LocalHapticFeedback.current
    val scope = rememberCoroutineScope()
    
    // 滑动状态
    val swipeToDismissState = rememberSwipeToDismissBoxState(
        confirmValueChange = { dismissValue ->
            when (dismissValue) {
                SwipeToDismissBoxValue.StartToEnd -> {
                    // 右滑删除
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    onDeleteClick(medication)
                    false // 不自动关闭，由删除逻辑处理
                }
                SwipeToDismissBoxValue.EndToStart -> {
                    // 左滑归档
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    onArchiveClick(medication)
                    false // 不自动关闭，由归档逻辑处理
                }
                SwipeToDismissBoxValue.Settled -> false
            }
        }
    )
    
    SwipeToDismissBox(
        state = swipeToDismissState,
        modifier = modifier,
        backgroundContent = {
            // 滑动背景内容
            SwipeBackgroundContent(
                dismissDirection = swipeToDismissState.dismissDirection
            )
        },
        content = {
            // 原有的药物卡片内容
            MedicationRecordCard(
                medication = medication,
                onMarkTaken = onMarkTaken,
                onCardClick = onCardClick,
                onEditClick = onEditClick,
                onArchiveClick = onArchiveClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    )
}

/**
 * 滑动背景内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SwipeBackgroundContent(
    dismissDirection: SwipeToDismissBoxValue
) {
    val backgroundColor = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Color(0xFFFF5252) // 删除 - 红色
        SwipeToDismissBoxValue.EndToStart -> Color(0xFFFF9800) // 归档 - 橙色
        else -> Color.Transparent
    }
    
    val icon = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Icons.Default.Delete
        SwipeToDismissBoxValue.EndToStart -> Icons.Default.Archive
        else -> Icons.Default.Delete
    }
    
    val text = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> "删除"
        SwipeToDismissBoxValue.EndToStart -> "归档"
        else -> ""
    }
    
    val alignment = when (dismissDirection) {
        SwipeToDismissBoxValue.StartToEnd -> Alignment.CenterStart
        SwipeToDismissBoxValue.EndToStart -> Alignment.CenterEnd
        else -> Alignment.Center
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor, RoundedCornerShape(16.dp))
            .padding(horizontal = 24.dp),
        contentAlignment = alignment
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = Color.White,
                modifier = Modifier.size(22.dp)
            )
            Text(
                text = text,
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 自定义撤销Snackbar - 莫兰迪风格
 */
@Composable
private fun UndoSnackbar(
    snackbarData: SnackbarData,
    undoState: UndoSnackbarState?,
    onUndo: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 14.dp, vertical = 8.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = MorandiSage),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = MorandiCream,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Column {
                    Text(
                        text = undoState?.let { "已完成 ${it.medicationName}" } ?: "已完成用药",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MorandiCream
                    )
                    
                    Text(
                        text = undoState?.let { "${it.remainingSeconds}秒内可撤销" } ?: "可撤销",
                        fontSize = 11.sp,
                        color = MorandiCream.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 右侧按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 撤销按钮
                TextButton(
                    onClick = onUndo,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MorandiCream
                    )
                ) {
                    Text(
                        text = "撤销",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // 关闭按钮
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MorandiCream,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 药物操作底部菜单 - 莫兰迪风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MedicationActionsBottomSheet(
    medication: ProfessionalMedication,
    onDismiss: () -> Unit,
    onEditClick: () -> Unit,
    onArchiveClick: () -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState()
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState,
        containerColor = Color.White
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Text(
                text = medication.name,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 编辑选项
            ActionItem(
                icon = Icons.Default.Edit,
                title = "编辑药物",
                subtitle = "修改用药信息",
                onClick = {
                    onEditClick()
                    onDismiss()
                }
            )
            
            // 归档选项
            ActionItem(
                icon = Icons.Default.Archive,
                title = "归档药物",
                subtitle = "暂时停止使用",
                onClick = {
                    onArchiveClick()
                    onDismiss()
                }
            )
            
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
}

/**
 * 操作项组件
 */
@Composable
private fun ActionItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MorandiSage,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = MorandiMediumGray
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "进入",
            tint = MorandiLightGray,
            modifier = Modifier.size(16.dp)
        )
    }
}

/**
 * 编辑药物对话框 - 莫兰迪风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditMedicationDialog(
    medication: ProfessionalMedication,
    onDismiss: () -> Unit,
    onUpdateMedication: (String, String, String, String, LocalTime, String) -> Unit
) {
    var name by remember { mutableStateOf(medication.name) }
    var dosage by remember { mutableStateOf(medication.dosage) }
    var frequency by remember { mutableStateOf(medication.frequency) }
    var selectedTime by remember { mutableStateOf(medication.scheduledTime) }
    var category by remember { mutableStateOf(medication.category) }
    var showTimePicker by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        title = {
            Text(
                text = "编辑用药",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 药物名称
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("药物名称", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    )
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 用量
                OutlinedTextField(
                    value = dosage,
                    onValueChange = { dosage = it },
                    label = { Text("用量", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    )
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 频次
                OutlinedTextField(
                    value = frequency,
                    onValueChange = { frequency = it },
                    label = { Text("频次", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    )
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 时间选择
                OutlinedTextField(
                    value = selectedTime.format(DateTimeFormatter.ofPattern("HH:mm")),
                    onValueChange = { },
                    label = { Text("服用时间", fontSize = 12.sp) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showTimePicker = true },
                    enabled = false,
                    colors = OutlinedTextFieldDefaults.colors(
                        disabledBorderColor = MorandiSage,
                        disabledLabelColor = MorandiSage,
                        disabledTextColor = MorandiDarkGray
                    ),
                    trailingIcon = {
                        Icon(
                            imageVector = Icons.Default.AccessTime,
                            contentDescription = "选择时间",
                            tint = MorandiSage,
                            modifier = Modifier.clickable { showTimePicker = true }
                        )
                    }
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 分类
                OutlinedTextField(
                    value = category,
                    onValueChange = { category = it },
                    label = { Text("分类", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    )
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    onUpdateMedication(medication.id, name, dosage, frequency, selectedTime, category)
                },
                colors = ButtonDefaults.buttonColors(containerColor = MorandiSage)
            ) {
                Text("保存", color = MorandiCream, fontSize = 14.sp)
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(contentColor = MorandiMediumGray)
            ) {
                Text("取消", fontSize = 14.sp)
            }
        }
    )
    
    // 时间选择器
    if (showTimePicker) {
        TimePickerDialog(
            initialTime = selectedTime,
            onTimeSelected = { time ->
                selectedTime = time
                showTimePicker = false
            },
            onDismiss = { showTimePicker = false }
        )
    }
}

/**
 * 热力图对话框 - 莫兰迪风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MedicationHeatmapDialog(
    dialogState: HeatmapDialogState,
    heatmapData: HeatmapData,
    onDismiss: () -> Unit,
    onPeriodChange: (HeatmapPeriod) -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 顶部标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${dialogState.medication.name} 服药记录",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = MorandiDarkGray
                    )
                    
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MorandiMediumGray
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 周期选择器
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    HeatmapPeriod.values().forEach { period ->
                        FilterChip(
                            onClick = { onPeriodChange(period) },
                            label = {
                                Text(
                                    text = when (period) {
                                        HeatmapPeriod.WEEK -> "本周"
                                        HeatmapPeriod.MONTH -> "本月"
                                        HeatmapPeriod.YEAR -> "本年"
                                    },
                                    fontSize = 12.sp
                                )
                            },
                            selected = dialogState.selectedPeriod == period,
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = MorandiSage,
                                selectedLabelColor = MorandiCream,
                                containerColor = MorandiBeige,
                                labelColor = MorandiDarkGray
                            )
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 统计信息 - 紧凑布局
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = MorandiBeige.copy(alpha = 0.3f)),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    HeatmapStatistics(
                        data = heatmapData,
                        period = dialogState.selectedPeriod,
                        modifier = Modifier.padding(12.dp)
                    )


                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 热力图 - 放在卡片中
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                    shape = RoundedCornerShape(8.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    MedicationHeatmapGrid(
                        data = when (dialogState.selectedPeriod) {
                            HeatmapPeriod.WEEK -> heatmapData.weekData
                            HeatmapPeriod.MONTH -> heatmapData.monthData
                            HeatmapPeriod.YEAR -> heatmapData.yearData
                        },
                        period = dialogState.selectedPeriod,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 热力图统计信息
 */
@Composable
private fun HeatmapStatistics(
    data: HeatmapData,
    period: HeatmapPeriod,
    modifier: Modifier = Modifier
) {
    val average = when (period) {
        HeatmapPeriod.WEEK -> data.weeklyAverage
        HeatmapPeriod.MONTH -> data.monthlyAverage
        HeatmapPeriod.YEAR -> data.yearlyAverage
    }
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        StatisticItem(
            title = "完成率",
            value = "${(average * 100).toInt()}%",
            color = MorandiSage
        )
        
        StatisticItem(
            title = "总次数",
            value = "${data.completedDoses}/${data.totalDoses}",
            color = MorandiGrayGreen
        )
        
        StatisticItem(
            title = "准时率",
            value = "${if (data.completedDoses > 0) (data.onTimeDoses * 100 / data.completedDoses) else 0}%",
            color = MorandiLavender
        )
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatisticItem(
    title: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Medium,
            color = color
        )
        Text(
            text = title,
            fontSize = 12.sp,
            color = MorandiMediumGray
        )
    }
}

/**
 * 热力图网格
 */
@Composable
private fun MedicationHeatmapGrid(
    data: List<HeatmapDataPoint>,
    period: HeatmapPeriod,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        when (period) {
            HeatmapPeriod.WEEK -> WeekHeatmapGrid(data)
            HeatmapPeriod.MONTH -> MonthHeatmapGrid(data)
            HeatmapPeriod.YEAR -> YearHeatmapGrid(data)
        }
    }
}

/**
 * 周热力图 - 紧凑版本
 */
@Composable
private fun WeekHeatmapGrid(data: List<HeatmapDataPoint>) {
    // 基本日志记录
    Log.d("WeekHeatmap", "周热力图渲染，数据点数量: ${data.size}")

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 🔧 修复：动态生成星期标签，确保与实际日期对应
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(3.dp)
        ) {
            data.take(7).forEach { point ->
                val dayLabel = TimeZoneUtils.getDayOfWeekString(point.date) // 🔧 使用统一时区处理

                Text(
                    text = dayLabel,
                    fontSize = 10.sp,
                    color = MorandiMediumGray,
                    modifier = Modifier.width(24.dp),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )


            }
        }

        Spacer(modifier = Modifier.height(6.dp))

        // 热力图格子 - 紧凑布局
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(3.dp)
        ) {
            data.take(7).forEach { point ->
                val isToday = TimeZoneUtils.isToday(point.date) // 🔧 使用统一时区处理

                HeatmapCell(
                    value = point.value,
                    date = point.date?.dayOfMonth?.toString() ?: "",
                    modifier = Modifier.size(24.dp),
                    isToday = isToday // 传递今天标识
                )


            }
        }
    }
}

/**
 * 月热力图 - 日历式布局
 */
@Composable
private fun MonthHeatmapGrid(data: List<HeatmapDataPoint>) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 星期标签
        Row(
            modifier = Modifier.padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(3.dp)
        ) {
            listOf("一", "二", "三", "四", "五", "六", "日").forEach { day ->
                Text(
                    text = day,
                    fontSize = 10.sp,
                    color = MorandiMediumGray,
                    modifier = Modifier.width(20.dp),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 日历网格 - 显示完整月份
        val weeks = data.chunked(7).take(6) // 最多6周显示完整月份
        
        Column(
            modifier = Modifier.padding(horizontal = 8.dp),
            verticalArrangement = Arrangement.spacedBy(3.dp)
        ) {
            weeks.forEach { week ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(3.dp)
                ) {
                    // 确保每行都有7个格子
                    repeat(7) { dayIndex ->
                        val dayData = week.getOrNull(dayIndex)
                        if (dayData != null && dayData.date != null) {
                            val isToday = TimeZoneUtils.isToday(dayData.date) // 🔧 使用统一时区处理

                            HeatmapCell(
                                value = dayData.value,
                                date = dayData.date.dayOfMonth.toString(),
                                modifier = Modifier.size(20.dp),
                                showDate = true,
                                isToday = isToday
                            )
                        } else {
                            // 空白格子用于对齐
                            Box(
                                modifier = Modifier
                                    .size(20.dp)
                                    .background(
                                        color = Color.Transparent,
                                        shape = RoundedCornerShape(3.dp)
                                    )
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 图例
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "少",
                fontSize = 8.sp,
                color = MorandiMediumGray
            )
            Spacer(modifier = Modifier.width(4.dp))
            
            // 颜色图例
            Row(
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                listOf(0f, 0.25f, 0.5f, 0.75f, 1f).forEach { value ->
                    Box(
                        modifier = Modifier
                            .size(10.dp)
                            .background(
                                color = getHeatmapColor(value),
                                shape = RoundedCornerShape(1.dp)
                            )
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "多",
                fontSize = 8.sp,
                color = MorandiMediumGray
            )
        }
    }
}

/**
 * 年热力图 - 紧凑版本
 */
@Composable
private fun YearHeatmapGrid(data: List<HeatmapDataPoint>) {
    // 月度数据汇总
    val monthlyData = data.chunked(30).map { month ->
        month.map { it.value }.average().toFloat()
    }
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "月度完成情况",
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = MorandiDarkGray,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        // 4x3网格显示12个月 - 更紧凑
        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            modifier = Modifier
                .height(150.dp)
                .padding(horizontal = 8.dp),
            verticalArrangement = Arrangement.spacedBy(3.dp),
            horizontalArrangement = Arrangement.spacedBy(3.dp)
        ) {
            items(12) { index ->
                val monthValue = monthlyData.getOrElse(index) { 0f }
                Card(
                    modifier = Modifier.aspectRatio(1.2f),
                    colors = CardDefaults.cardColors(
                        containerColor = getHeatmapColor(monthValue)
                    ),
                    shape = RoundedCornerShape(4.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "${index + 1}月",
                                fontSize = 8.sp,
                                color = if (monthValue > 0.5f) Color.White else MorandiDarkGray,
                                fontWeight = FontWeight.Medium
                            )
                            if (monthValue > 0f) {
                                Text(
                                    text = "${(monthValue * 100).toInt()}%",
                                    fontSize = 6.sp,
                                    color = if (monthValue > 0.5f) Color.White.copy(alpha = 0.8f) else MorandiMediumGray
                                )
                            }
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 图例
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "低",
                fontSize = 8.sp,
                color = MorandiMediumGray
            )
            Spacer(modifier = Modifier.width(4.dp))
            
            // 颜色图例
            Row(
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                listOf(0f, 0.25f, 0.5f, 0.75f, 1f).forEach { value ->
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = getHeatmapColor(value),
                                shape = RoundedCornerShape(1.dp)
                            )
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "高",
                fontSize = 8.sp,
                color = MorandiMediumGray
            )
        }
    }
}

/**
 * 热力图单元格 - 增强版本
 */
@Composable
private fun HeatmapCell(
    value: Float,
    modifier: Modifier = Modifier,
    date: String = "",
    showDate: Boolean = false,
    isToday: Boolean = false // 🔧 新增：是否为今天
) {
    Box(
        modifier = modifier
            .background(
                color = getHeatmapColor(value),
                shape = RoundedCornerShape(3.dp)
            )
            .then(
                // 🔧 为今天添加特殊边框
                if (isToday) {
                    Modifier.border(
                        width = 1.dp,
                        color = Color(0xFF4A90E2),
                        shape = RoundedCornerShape(3.dp)
                    )
                } else Modifier
            ),
        contentAlignment = Alignment.Center
    ) {
        if (showDate && date.isNotEmpty() && value > 0f) {
            Text(
                text = date,
                fontSize = 7.sp,
                color = if (value > 0.5f) Color.White else Color(0xFF2A2A2A),
                fontWeight = FontWeight.Medium
            )
        }

        // 🔧 为今天显示特殊标识
        if (isToday && !showDate) {
            Text(
                text = date,
                fontSize = 6.sp,
                color = if (value > 0.5f) Color.White else Color(0xFF2A2A2A),
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * 根据值获取热力图颜色 - 莫兰迪绿色系
 */
private fun getHeatmapColor(value: Float): Color {
    return when {
        value >= 0.9f -> Color(0xFF7A8B7A) // 深莫兰迪绿 - 优秀
        value >= 0.7f -> Color(0xFF8FA08F) // 中深莫兰迪绿 - 良好  
        value >= 0.5f -> Color(0xFFA4B5A4) // 中浅莫兰迪绿 - 一般
        value >= 0.3f -> Color(0xFFB9CAB9) // 浅莫兰迪绿 - 较差
        value > 0f -> Color(0xFFCEDFCE) // 很浅莫兰迪绿 - 很差
        else -> Color(0xFFF0F0F0) // 浅灰 - 无数据
    }
}

/**
 * 删除撤销Snackbar - 莫兰迪风格
 */
@Composable
private fun DeleteUndoSnackbar(
    snackbarData: SnackbarData,
    undoState: DeleteUndoSnackbarState?,
    onUndo: () -> Unit,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFF5252)), // 删除主题红色
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：删除图标和信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = Color.White,
                    modifier = Modifier
                        .size(20.dp)
                        .padding(end = 8.dp)
                )
                
                Column {
                    Text(
                        text = undoState?.let { "已删除 ${it.medicationName}" } ?: "已删除药物",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                    Text(
                        text = undoState?.let { "${it.remainingSeconds}秒内可撤销" } ?: "可撤销",
                        fontSize = 12.sp,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
            
            // 撤销按钮
            TextButton(
                onClick = onUndo,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color.White
                )
            ) {
                Text(
                    text = "撤销",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

/**
 * 时间选择器对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimePickerDialog(
    initialTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    onDismiss: () -> Unit
) {
    val timePickerState = rememberTimePickerState(
        initialHour = initialTime.hour,
        initialMinute = initialTime.minute
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        title = {
            Text(
                text = "选择时间",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MorandiDarkGray
            )
        },
        text = {
            TimePicker(
                state = timePickerState,
                colors = TimePickerDefaults.colors(
                    timeSelectorSelectedContainerColor = MorandiSage,
                    timeSelectorSelectedContentColor = MorandiCream
                )
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    val selectedTime = LocalTime.of(timePickerState.hour, timePickerState.minute)
                    onTimeSelected(selectedTime)
                },
                colors = ButtonDefaults.buttonColors(containerColor = MorandiSage)
            ) {
                Text("确定", color = MorandiCream, fontSize = 14.sp)
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(contentColor = MorandiMediumGray)
            ) {
                Text("取消", fontSize = 14.sp)
            }
        }
    )
}

/**
 * 添加模板对话框 - 用于创建自定义模板
 */
@Composable
private fun AddTemplateDialog(
    onDismiss: () -> Unit,
    onCreateTemplate: (MedicationTemplate) -> Unit
) {
    var templateName by remember { mutableStateOf("") }
    var dosage by remember { mutableStateOf("") }
    var frequency by remember { mutableStateOf("") }
    var category by remember { mutableStateOf("自定义") }
    
    val categories = listOf("处方药", "保健品", "美容保健", "自定义")
    var showCategoryDropdown by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加模板",
                    tint = MorandiSage,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    "创建模板",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MorandiDarkGray
                )
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 模板名称
                OutlinedTextField(
                    value = templateName,
                    onValueChange = { templateName = it },
                    label = { 
                        Text(
                            "模板名称", 
                            fontSize = 12.sp,
                            color = MorandiMediumGray
                        ) 
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    ),
                    textStyle = LocalTextStyle.current.copy(fontSize = 13.sp)
                )
                
                // 剂量
                OutlinedTextField(
                    value = dosage,
                    onValueChange = { dosage = it },
                    label = { 
                        Text(
                            "剂量", 
                            fontSize = 12.sp,
                            color = MorandiMediumGray
                        ) 
                    },
                    placeholder = { 
                        Text(
                            "如：500mg、1片",
                            fontSize = 12.sp,
                            color = MorandiLightGray
                        ) 
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    ),
                    textStyle = LocalTextStyle.current.copy(fontSize = 13.sp)
                )
                
                // 频次
                OutlinedTextField(
                    value = frequency,
                    onValueChange = { frequency = it },
                    label = { 
                        Text(
                            "频次", 
                            fontSize = 12.sp,
                            color = MorandiMediumGray
                        ) 
                    },
                    placeholder = { 
                        Text(
                            "如：每日一次、每日三次",
                            fontSize = 12.sp,
                            color = MorandiLightGray
                        ) 
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MorandiSage,
                        focusedLabelColor = MorandiSage,
                        cursorColor = MorandiSage
                    ),
                    textStyle = LocalTextStyle.current.copy(fontSize = 13.sp)
                )
                
                // 分类选择
                ExposedDropdownMenuBox(
                    expanded = showCategoryDropdown,
                    onExpandedChange = { showCategoryDropdown = !showCategoryDropdown }
                ) {
                    OutlinedTextField(
                        value = category,
                        onValueChange = { },
                        readOnly = true,
                        label = { 
                            Text(
                                "分类", 
                                fontSize = 12.sp,
                                color = MorandiMediumGray
                            ) 
                        },
                        trailingIcon = {
                            Icon(
                                imageVector = if (showCategoryDropdown) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                contentDescription = "选择分类",
                                tint = MorandiSage,
                                modifier = Modifier.size(20.dp)
                            )
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MorandiSage,
                            focusedLabelColor = MorandiSage
                        ),
                        textStyle = LocalTextStyle.current.copy(fontSize = 13.sp)
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showCategoryDropdown,
                        onDismissRequest = { showCategoryDropdown = false }
                    ) {
                        categories.forEach { categoryOption ->
                            DropdownMenuItem(
                                text = { 
                                    Text(
                                        categoryOption,
                                        fontSize = 13.sp,
                                        color = MorandiDarkGray
                                    ) 
                                },
                                onClick = {
                                    category = categoryOption
                                    showCategoryDropdown = false
                                }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (templateName.isNotBlank() && dosage.isNotBlank() && frequency.isNotBlank()) {
                        // 根据分类选择图标和颜色
                        val (icon, color) = when (category) {
                            "处方药" -> Icons.Default.Bloodtype to MorandiPink
                            "保健品" -> Icons.Default.LocalFlorist to MorandiSage
                            "美容保健" -> Icons.Default.FaceRetouchingNatural to MorandiLavender
                            "自定义" -> Icons.Default.Medication to MorandiGrayGreen
                            else -> Icons.Default.Medication to MorandiSage
                        }
                        
                        val template = MedicationTemplate(
                            name = templateName,
                            dosage = dosage,
                            frequency = frequency,
                            icon = icon,
                            color = color,
                            category = category
                        )
                        onCreateTemplate(template)
                    }
                },
                enabled = templateName.isNotBlank() && dosage.isNotBlank() && frequency.isNotBlank()
            ) {
                Text(
                    "创建",
                    fontSize = 12.sp,
                    color = if (templateName.isNotBlank() && dosage.isNotBlank() && frequency.isNotBlank()) 
                        MorandiSage else MorandiLightGray,
                    fontWeight = FontWeight.Medium
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    "取消",
                    fontSize = 12.sp,
                    color = MorandiMediumGray,
                    fontWeight = FontWeight.Medium
                )
            }
        },
        containerColor = Color.White,
        shape = RoundedCornerShape(16.dp)
    )
}

 