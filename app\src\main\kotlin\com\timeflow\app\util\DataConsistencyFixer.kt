package com.timeflow.app.util

import android.util.Log
import com.timeflow.app.data.dao.TaskDao
import com.timeflow.app.data.repository.TaskRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据一致性修复工具
 * 用于检测和修复数据库中的数据不一致问题
 */
@Singleton
class DataConsistencyFixer @Inject constructor(
    private val taskDao: TaskDao,
    private val taskRepository: TaskRepository
) {
    
    companion object {
        private const val TAG = "DataConsistencyFixer"
    }
    
    /**
     * 检查并修复所有任务的hasSubtasks字段
     * @return 修复的任务数量
     */
    suspend fun fixAllTasksHasSubtasksFlag(): Int = withContext(Dispatchers.IO) {
        Log.i(TAG, "开始检查和修复所有任务的hasSubtasks字段...")
        
        var fixedCount = 0
        
        try {
            // 获取所有任务
            val allTasks = taskDao.getAllTasks()
            Log.d(TAG, "检查 ${allTasks.size} 个任务的数据一致性")
            
            for (task in allTasks) {
                try {
                    // 查询该任务的实际子任务数量
                    val actualSubTasks = taskDao.getTasksByParentId(task.id)
                    val actualHasSubtasks = actualSubTasks.isNotEmpty()
                    
                    // 检查是否存在不一致
                    if (task.hasSubtasks != actualHasSubtasks) {
                        Log.w(TAG, "发现数据不一致: 任务 ${task.id}(${task.title}) hasSubtasks=${task.hasSubtasks}, 但实际子任务数量=${actualSubTasks.size}")
                        
                        // 修复不一致的数据
                        val updatedAt = LocalDateTime.now()
                        val updateResult = taskDao.updateTaskHasSubtasksFlag(
                            taskId = task.id,
                            hasSubtasks = actualHasSubtasks,
                            updateTime = updatedAt
                        )
                        
                        if (updateResult > 0) {
                            fixedCount++
                            Log.i(TAG, "✓ 已修复任务 ${task.id}: hasSubtasks 从 ${task.hasSubtasks} 更新为 ${actualHasSubtasks}")
                        } else {
                            Log.e(TAG, "✗ 修复任务 ${task.id} 失败: 数据库更新返回0行")
                        }
                    } else {
                        Log.v(TAG, "任务 ${task.id} 数据一致性正常: hasSubtasks=${task.hasSubtasks}")
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "检查任务 ${task.id} 时发生错误", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "修复过程中发生严重错误", e)
        }
        
        Log.i(TAG, "数据一致性修复完成: 共修复 $fixedCount 个任务")
        fixedCount
    }
    
    /**
     * 检查特定任务的数据一致性
     * @param taskId 任务ID
     * @return 是否修复了数据不一致
     */
    suspend fun checkAndFixTaskConsistency(taskId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "检查任务 $taskId 的数据一致性...")
            
            val task = taskDao.getTaskById(taskId)
            if (task == null) {
                Log.w(TAG, "任务 $taskId 不存在")
                return@withContext false
            }
            
            // 查询实际子任务
            val actualSubTasks = taskDao.getTasksByParentId(taskId)
            val actualHasSubtasks = actualSubTasks.isNotEmpty()
            
            if (task.hasSubtasks != actualHasSubtasks) {
                Log.w(TAG, "发现数据不一致: 任务 $taskId hasSubtasks=${task.hasSubtasks}, 实际子任务数量=${actualSubTasks.size}")
                
                // 修复数据
                val updateResult = taskDao.updateTaskHasSubtasksFlag(
                    taskId = taskId,
                    hasSubtasks = actualHasSubtasks,
                    updateTime = LocalDateTime.now()
                )
                
                if (updateResult > 0) {
                    Log.i(TAG, "✓ 已修复任务 $taskId: hasSubtasks 从 ${task.hasSubtasks} 更新为 $actualHasSubtasks")
                    return@withContext true
                } else {
                    Log.e(TAG, "✗ 修复任务 $taskId 失败")
                    return@withContext false
                }
            } else {
                Log.v(TAG, "任务 $taskId 数据一致性正常")
                return@withContext false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查任务 $taskId 数据一致性时发生错误", e)
            return@withContext false
        }
    }
    
    /**
     * 生成数据一致性报告
     * @return 包含问题任务信息的报告
     */
    suspend fun generateConsistencyReport(): ConsistencyReport = withContext(Dispatchers.IO) {
        val report = ConsistencyReport()
        
        try {
            val allTasks = taskDao.getAllTasks()
            
            for (task in allTasks) {
                try {
                    val actualSubTasks = taskDao.getTasksByParentId(task.id)
                    val actualHasSubtasks = actualSubTasks.isNotEmpty()
                    
                    if (task.hasSubtasks != actualHasSubtasks) {
                        report.inconsistentTasks.add(
                            InconsistentTask(
                                taskId = task.id,
                                title = task.title,
                                recordedHasSubtasks = task.hasSubtasks,
                                actualSubtaskCount = actualSubTasks.size
                            )
                        )
                    }
                    
                } catch (e: Exception) {
                    report.errorTasks.add(
                        ErrorTask(
                            taskId = task.id,
                            title = task.title,
                            error = e.message ?: "未知错误"
                        )
                    )
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "生成一致性报告时发生错误", e)
        }
        
        Log.i(TAG, "一致性报告生成完成: ${report.inconsistentTasks.size} 个不一致任务, ${report.errorTasks.size} 个错误任务")
        report
    }
    
    /**
     * 数据一致性报告
     */
    data class ConsistencyReport(
        val inconsistentTasks: MutableList<InconsistentTask> = mutableListOf(),
        val errorTasks: MutableList<ErrorTask> = mutableListOf()
    ) {
        val totalInconsistentTasks: Int get() = inconsistentTasks.size
        val totalErrorTasks: Int get() = errorTasks.size
        val hasIssues: Boolean get() = inconsistentTasks.isNotEmpty() || errorTasks.isNotEmpty()
    }
    
    /**
     * 不一致的任务信息
     */
    data class InconsistentTask(
        val taskId: String,
        val title: String,
        val recordedHasSubtasks: Boolean,
        val actualSubtaskCount: Int
    )
    
    /**
     * 检查时出错的任务信息
     */
    data class ErrorTask(
        val taskId: String,
        val title: String,
        val error: String
    )
} 