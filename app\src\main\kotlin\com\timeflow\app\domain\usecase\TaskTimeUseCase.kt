package com.timeflow.app.domain.usecase

import android.util.Log
import com.timeflow.app.data.model.TaskTime
import com.timeflow.app.data.model.TaskTimeUpdateEvent
import com.timeflow.app.data.repository.TaskTimeRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onEach
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务时间业务用例
 * 封装任务时间相关的业务逻辑和规则
 */
@Singleton
class TaskTimeUseCase @Inject constructor(
    private val taskTimeRepository: TaskTimeRepository
) {
    
    companion object {
        private const val TAG = "TaskTimeUseCase"
    }
    
    // 时间更新事件流
    private val _timeUpdateEvents = MutableSharedFlow<TaskTimeUpdateEvent>()
    val timeUpdateEvents: Flow<TaskTimeUpdateEvent> = _timeUpdateEvents.asSharedFlow()
    
    /**
     * 更新任务时间
     * 这是主要的时间更新入口，包含完整的业务逻辑
     */
    suspend fun updateTaskTime(
        taskId: String,
        startTime: LocalDateTime? = null,
        endTime: LocalDateTime? = null,
        dueDate: LocalDateTime? = null,
        source: String = "Unknown"
    ): Result<TaskTime> {
        Log.d(TAG, "开始更新任务时间: taskId=$taskId, source=$source")
        
        try {
            // 1. 获取当前时间数据
            val oldTime = taskTimeRepository.getTaskTime(taskId)
            
            // 2. 验证时间数据
            val validationResult = validateTimeData(startTime, endTime, dueDate)
            if (validationResult.isFailure) {
                // 修复类型不匹配：将验证错误转换为TaskTime的Result
                return Result.failure(validationResult.exceptionOrNull()!!)
            }
            
            // 3. 更新时间数据
            val updateResult = taskTimeRepository.updateTaskTime(
                taskId = taskId,
                startTime = startTime,
                endTime = endTime,
                dueDate = dueDate,
                source = source
            )
            
            if (updateResult.isSuccess) {
                val newTime = updateResult.getOrNull()!!
                
                // 4. 发送更新事件
                val event = TaskTimeUpdateEvent(
                    taskId = taskId,
                    oldTime = oldTime,
                    newTime = newTime,
                    source = source
                )
                _timeUpdateEvents.emit(event)
                
                Log.d(TAG, "任务时间更新成功: $newTime")
            }
            
            return updateResult
            
        } catch (e: Exception) {
            Log.e(TAG, "更新任务时间失败", e)
            return Result.failure(e)
        }
    }
    
    /**
     * 快速设置任务截止时间
     */
    suspend fun setTaskDueDate(
        taskId: String,
        dueDate: LocalDateTime,
        source: String = "QuickSet"
    ): Result<TaskTime> {
        return updateTaskTime(
            taskId = taskId,
            dueDate = dueDate,
            source = source
        )
    }
    
    /**
     * 设置任务时间范围
     */
    suspend fun setTaskTimeRange(
        taskId: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime,
        source: String = "TimeRange"
    ): Result<TaskTime> {
        return updateTaskTime(
            taskId = taskId,
            startTime = startTime,
            endTime = endTime,
            dueDate = endTime, // 将结束时间作为截止时间
            source = source
        )
    }
    
    /**
     * 清除任务时间
     */
    suspend fun clearTaskTime(
        taskId: String,
        source: String = "Clear"
    ): Result<TaskTime> {
        return updateTaskTime(
            taskId = taskId,
            startTime = null,
            endTime = null,
            dueDate = null,
            source = source
        )
    }
    
    /**
     * 获取任务时间
     */
    suspend fun getTaskTime(taskId: String): TaskTime? {
        return taskTimeRepository.getTaskTime(taskId)
    }
    
    /**
     * 批量获取任务时间
     */
    suspend fun getTaskTimes(taskIds: List<String>): Map<String, TaskTime> {
        return taskTimeRepository.getTaskTimes(taskIds)
    }
    
    /**
     * 监听时间更新
     */
    fun observeTimeUpdates(): Flow<TaskTime> {
        return taskTimeRepository.timeUpdates
            .onEach { taskTime ->
                Log.d(TAG, "观察到时间更新: $taskTime")
            }
            .catch { e ->
                Log.e(TAG, "时间更新观察失败", e)
            }
    }
    
    /**
     * 获取今天到期的任务
     */
    suspend fun getTodayDueTasks(): List<TaskTime> {
        val allTimes = taskTimeRepository.timeCache
        // 这里需要实现获取今天到期任务的逻辑
        // TODO: 实现具体逻辑
        return emptyList()
    }
    
    /**
     * 获取过期的任务
     */
    suspend fun getOverdueTasks(): List<TaskTime> {
        val allTimes = taskTimeRepository.timeCache
        // 这里需要实现获取过期任务的逻辑
        // TODO: 实现具体逻辑
        return emptyList()
    }
    
    /**
     * 验证时间数据
     */
    private fun validateTimeData(
        startTime: LocalDateTime?,
        endTime: LocalDateTime?,
        dueDate: LocalDateTime?
    ): Result<Unit> {
        
        // 验证开始时间和结束时间的逻辑关系
        if (startTime != null && endTime != null) {
            if (startTime.isAfter(endTime)) {
                return Result.failure(IllegalArgumentException("开始时间不能晚于结束时间"))
            }
        }
        
        // 验证截止时间不能是过去时间（可选验证）
        if (dueDate != null && dueDate.isBefore(LocalDateTime.now().minusHours(1))) {
            Log.w(TAG, "截止时间设置为过去时间: $dueDate")
            // 这里可以选择是否允许过去时间，目前只记录警告
        }
        
        // 验证时间范围合理性
        if (startTime != null && dueDate != null) {
            if (startTime.isAfter(dueDate)) {
                return Result.failure(IllegalArgumentException("开始时间不能晚于截止时间"))
            }
        }
        
        return Result.success(Unit)
    }
    
    /**
     * 智能时间建议
     * 根据任务历史和用户习惯提供时间建议
     */
    suspend fun suggestTaskTime(taskId: String): TaskTime? {
        // TODO: 实现智能时间建议逻辑
        // 可以基于：
        // 1. 用户的工作习惯
        // 2. 类似任务的历史时间
        // 3. 当前日程安排
        Log.d(TAG, "为任务提供时间建议: taskId=$taskId")
        return null
    }
    
    /**
     * 时间冲突检测
     */
    suspend fun checkTimeConflicts(taskTime: TaskTime): List<String> {
        // TODO: 实现时间冲突检测
        Log.d(TAG, "检查时间冲突: $taskTime")
        return emptyList()
    }
} 