package com.timeflow.app.ui.screen.task

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskTag
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.task.TaskListViewModel
import java.time.format.DateTimeFormatter
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.zIndex

/**
 * 任务快速详情底部弹出框
 * 显示任务的基本信息，包括标题、子任务和标签
 * 点击可进入完整的任务详情页面
 * 
 * 性能优化版本：
 * 1. 使用derivedStateOf计算派生状态
 * 2. 使用LazyColumn高效渲染子任务
 * 3. 避免不必要的重组
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskQuickDetailSheet(
    taskId: String,
    navController: NavController,
    viewModel: TaskListViewModel
) {
    // 使用State来存储从ViewModel获取的ModelTask
    var selectedTask by remember { mutableStateOf<Task?>(null) }
    val subTasks = viewModel.subTasks.value
    val isLoading = viewModel.isLoading.value
    val error = viewModel.error.value
    
    // 避免在重组时重复请求数据
    LaunchedEffect(Unit) {
        viewModel.loadTask(taskId)
        viewModel.loadSubTasks(taskId)
        
        // 获取ModelTask
        viewModel.getModelTaskById(taskId) { task ->
            selectedTask = task
        }
    }
    
    // 底部弹出框
    ModalBottomSheet(
        onDismissRequest = { navController.popBackStack() },
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = false),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        containerColor = MaterialTheme.colorScheme.surface,
        dragHandle = { BottomSheetDefaults.DragHandle() }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 300.dp, max = 500.dp)
                .padding(bottom = 16.dp)
        ) {
            when {
                isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                error != null -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text("加载失败: ${error}")
                    }
                }
                selectedTask == null -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text("找不到任务")
                    }
                }
                else -> {
                    // 使用key强制在任务变化时重组，但避免不必要的子任务重组
                    // 创建一个本地不可变副本避免智能转换问题
                    val taskToDisplay = selectedTask
                    if (taskToDisplay != null) {
                        key(taskToDisplay.id) {
                            TaskQuickDetailContent(
                                task = taskToDisplay,
                                subTasks = subTasks,
                                onEditClick = {
                                    // 导航到完整的编辑页面
                                    navController.popBackStack()
                                    navController.navigate(AppDestinations.taskDetailRoute(taskId))
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun TaskQuickDetailContent(
    task: Task?,
    subTasks: List<Task>,
    onEditClick: () -> Unit
) {
    if (task == null) return
    
    // 使用remember缓存计算值
    val dateFormatter = remember { DateTimeFormatter.ofPattern("yyyy-MM-dd") }
    
    // 预计算派生状态，避免重组时重复计算
    val completedSubtasks = remember(subTasks) {
        derivedStateOf { subTasks.count { it.completedAt != null } }
    }
    
    // 状态颜色映射表
    val statusColors = remember {
        mapOf(
            "待办" to Pair(Color(0xFFE0F7FA), Color(0xFF00ACC1)),
            "进行中" to Pair(Color(0xFFE8F5E9), Color(0xFF43A047)),
            "已完成" to Pair(Color(0xFFE8EAF6), Color(0xFF3F51B5)),
        )
    }
    
    // 获取当前任务状态颜色
    val (bgColor, textColor) = statusColors[task.status] ?: Pair(Color(0xFFF5F5F5), Color.Gray)
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // 任务标题和状态 - 使用clickable modifier
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
                .clickable(onClick = onEditClick),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f)
            )
            
            // 状态标签
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = bgColor
            ) {
                Text(
                    text = task.status,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = textColor
                )
            }
        }
        
        // 分隔线
        HorizontalDivider()
        
        // 子任务区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "子任务",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                Text(
                    text = "${completedSubtasks.value}/${subTasks.size}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 使用LazyColumn高效渲染子任务
            if (subTasks.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无子任务",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 120.dp)
                ) {
                    LazyColumn {
                        items(
                            items = subTasks.take(5),
                            key = { it.id }
                        ) { subTask ->
                            SubTaskItem(subTask = subTask)
                        }
                        
                        // 显示更多子任务的提示
                        if (subTasks.size > 5) {
                            item {
                                Text(
                                    text = "还有 ${subTasks.size - 5} 个子任务...",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp, horizontal = 28.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // 分隔线
        HorizontalDivider()
        
        // 标签和截止日期区域
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 截止日期部分
            if (task.dueDate != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Event,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = task.dueDate.format(dateFormatter),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            // 优先级显示
            if (task.priority != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val priorityColors = listOf(
                        Color(0xFF4CAF50), // 低
                        Color(0xFFFFC107), // 中
                        Color(0xFFFF9800), // 高
                        Color(0xFFF44336)  // 紧急
                    )
                    
                    // 根据优先级枚举获取索引
                    val priorityIndex = when(task.priority) {
                        Priority.LOW -> 0
                        Priority.MEDIUM -> 1
                        Priority.HIGH -> 2
                        Priority.URGENT -> 3
                        else -> 0
                    }
                    
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = priorityColors.getOrElse(priorityIndex) { Color.Gray },
                                shape = CircleShape
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = when(task.priority) {
                            Priority.LOW -> "低"
                            Priority.MEDIUM -> "中"
                            Priority.HIGH -> "高"
                            Priority.URGENT -> "紧急"
                            else -> "未知"
                        },
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        // 操作按钮区域 - 使用更现代的Button样式
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 编辑按钮
            FilledTonalButton(
                onClick = onEditClick,
                shape = RoundedCornerShape(8.dp),
                colors = ButtonDefaults.filledTonalButtonColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("编辑详情")
            }
            
            // 添加子任务按钮
            OutlinedButton(
                onClick = onEditClick,
                shape = RoundedCornerShape(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("添加子任务")
            }
        }
    }
}

/**
 * 子任务项 - 抽取为独立可组合项，提高重用性
 */
@Composable
private fun SubTaskItem(subTask: Task) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = subTask.completedAt != null,
            onCheckedChange = null,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = subTask.title,
            style = MaterialTheme.typography.bodyMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun TagChip(tag: TaskTag) {
    Surface(
        modifier = Modifier.height(24.dp),
        shape = RoundedCornerShape(12.dp),
        color = Color(0xFFF5F5F5)
    ) {
        Text(
            text = tag.name ?: "",
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
} 