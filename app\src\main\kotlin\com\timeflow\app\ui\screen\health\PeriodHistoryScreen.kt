package com.timeflow.app.ui.screen.health

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel
import com.timeflow.app.ui.viewmodel.PeriodStatistics
import com.timeflow.app.utils.CycleUtils
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/**
 * 经期历史管理界面
 * 显示所有历史经期记录，支持编辑、删除、补记功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PeriodHistoryScreen(
    viewModel: MenstrualCycleViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val historicalPeriods by viewModel.getHistoricalPeriods().collectAsState(initial = emptyList())
    val statistics by viewModel.getPeriodStatistics().collectAsState(initial = PeriodStatistics())
    
    var showAddDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var selectedCycle by remember { mutableStateOf<CycleRecord?>(null) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "经期历史",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { showAddDialog = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加历史记录",
                            tint = Color(0xFF880E4F)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White
                )
            )
        },
        containerColor = Color(0xFFFFF9FA)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 统计信息卡片
            item {
                StatisticsCard(statistics = statistics)
            }
            
            // 历史记录列表
            if (historicalPeriods.isNotEmpty()) {
                item {
                    Text(
                        text = "历史记录 (${historicalPeriods.size}条)",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.DarkGray,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
                
                items(historicalPeriods.sortedByDescending { it.startDate }) { cycle ->
                    PeriodHistoryItem(
                        cycle = cycle,
                        onEdit = {
                            selectedCycle = cycle
                            showEditDialog = true
                        },
                        onDelete = {
                            selectedCycle = cycle
                            showDeleteDialog = true
                        }
                    )
                }
            } else {
                item {
                    EmptyHistoryView(
                        onAddFirst = { showAddDialog = true }
                    )
                }
            }
        }
    }
    
    // 添加历史记录对话框
    if (showAddDialog) {
        AddPeriodDialog(
            onDismiss = { showAddDialog = false },
            onConfirm = { startDate, endDate, notes ->
                viewModel.addHistoricalPeriod(startDate, endDate, notes)
                showAddDialog = false
            }
        )
    }
    
    // 编辑记录对话框
    if (showEditDialog && selectedCycle != null) {
        EditPeriodDialog(
            cycle = selectedCycle!!,
            onDismiss = { 
                showEditDialog = false
                selectedCycle = null
            },
            onConfirm = { startDate, endDate, notes ->
                viewModel.editPeriodRecord(selectedCycle!!.id, startDate, endDate, notes)
                showEditDialog = false
                selectedCycle = null
            }
        )
    }
    
    // 删除确认对话框
    if (showDeleteDialog && selectedCycle != null) {
        DeleteConfirmDialog(
            cycle = selectedCycle!!,
            onDismiss = {
                showDeleteDialog = false
                selectedCycle = null
            },
            onConfirm = {
                viewModel.deletePeriodRecord(selectedCycle!!.id)
                showDeleteDialog = false
                selectedCycle = null
            }
        )
    }
}

/**
 * 统计信息卡片
 */
@Composable
fun StatisticsCard(statistics: PeriodStatistics) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "统计概览",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "记录周期",
                    value = "${statistics.totalCycles}个",
                    icon = Icons.Default.EventNote
                )
                
                StatisticItem(
                    label = "平均周期",
                    value = "${statistics.averageCycleLength.toInt()}天",
                    icon = Icons.Default.DateRange
                )
                
                StatisticItem(
                    label = "平均经期",
                    value = "${statistics.averagePeriodLength.toInt()}天",
                    icon = Icons.Default.Favorite
                )
            }
            
            if (statistics.totalCycles >= 3) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "最短周期",
                        value = "${statistics.shortestCycle}天",
                        icon = Icons.Default.TrendingDown
                    )
                    
                    StatisticItem(
                        label = "最长周期",
                        value = "${statistics.longestCycle}天",
                        icon = Icons.Default.TrendingUp
                    )
                    
                    StatisticItem(
                        label = "规律性",
                        value = CycleUtils.getRegularityText(statistics.cycleRegularity),
                        icon = Icons.Default.CheckCircle
                    )
                }
            }
        }
    }
}

/**
 * 统计项
 */
@Composable
fun StatisticItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = Color(0xFF880E4F),
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color.DarkGray
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 空历史记录视图
 */
@Composable
fun EmptyHistoryView(onAddFirst: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.EventNote,
            contentDescription = null,
            tint = Color.Gray,
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "暂无历史记录",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Gray
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "开始记录您的经期历史，\n获得更准确的预测",
            fontSize = 14.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onAddFirst,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF880E4F)
            )
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("添加第一条记录")
        }
    }
}

/**
 * 历史记录项
 */
@Composable
fun PeriodHistoryItem(
    cycle: CycleRecord,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
    val periodLength = cycle.periodLength ?:
        cycle.endDate?.let { ChronoUnit.DAYS.between(cycle.startDate, it).toInt() + 1 } ?: 0

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = cycle.startDate.format(dateFormatter),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF880E4F)
                    )

                    if (cycle.endDate != null) {
                        Text(
                            text = "至 ${cycle.endDate.format(dateFormatter)}",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "编辑",
                            tint = Color(0xFF2196F3),
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFE91E63),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 详细信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                if (periodLength > 0) {
                    InfoChip(
                        label = "经期",
                        value = "${periodLength}天",
                        color = Color(0xFFFFEBEE)
                    )
                }

                cycle.cycleLength?.let { length ->
                    InfoChip(
                        label = "周期",
                        value = "${length}天",
                        color = Color(0xFFE3F2FD)
                    )
                }
            }

            // 备注信息
            cycle.notes?.let { notes ->
                if (notes.isNotBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = notes,
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = Color(0xFFF5F5F5),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 信息标签
 */
@Composable
fun InfoChip(
    label: String,
    value: String,
    color: Color
) {
    Row(
        modifier = Modifier
            .background(
                color = color,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 12.dp, vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )

        Spacer(modifier = Modifier.width(4.dp))

        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = Color.DarkGray
        )
    }
}

// 🔧 删除：重复的工具函数已移动到CycleUtils
