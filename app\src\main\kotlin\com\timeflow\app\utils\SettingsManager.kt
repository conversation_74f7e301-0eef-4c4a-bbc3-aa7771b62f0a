package com.timeflow.app.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.preference.PreferenceManager

/**
 * 设置管理器
 * 用于读取和保存应用设置
 */
class SettingsManager private constructor() {
    private var sharedPreferences: SharedPreferences? = null
    
    companion object {
        @Volatile
        private var instance: SettingsManager? = null
        
        fun getInstance(): SettingsManager {
            return instance ?: synchronized(this) {
                instance ?: SettingsManager().also { instance = it }
            }
        }
        
        fun init(context: Context) {
            getInstance().sharedPreferences = PreferenceManager.getDefaultSharedPreferences(context)
        }
    }
    
    fun getStringPreference(key: String, defaultValue: String): String {
        return sharedPreferences?.getString(key, defaultValue) ?: defaultValue
    }
    
    fun setStringPreference(key: String, value: String) {
        sharedPreferences?.edit()?.putString(key, value)?.apply()
    }
    
    fun getIntPreference(key: String, defaultValue: Int): Int {
        return sharedPreferences?.getInt(key, defaultValue) ?: defaultValue
    }
    
    fun setIntPreference(key: String, value: Int) {
        sharedPreferences?.edit()?.putInt(key, value)?.apply()
    }
    
    fun getBooleanPreference(key: String, defaultValue: Boolean): Boolean {
        return sharedPreferences?.getBoolean(key, defaultValue) ?: defaultValue
    }
    
    fun setBooleanPreference(key: String, value: Boolean) {
        sharedPreferences?.edit()?.putBoolean(key, value)?.apply()
    }
} 