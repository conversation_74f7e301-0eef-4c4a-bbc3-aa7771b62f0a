package com.timeflow.app.utils

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 事件总线，用于应用内组件间通信
 */
@Singleton
class EventBus @Inject constructor() {
    
    companion object {
        // 防抖间隔常量
        private const val DEBOUNCE_INTERVAL_MS = 50L // 50ms防抖间隔
    }
    
    // 事件流
    private val _events = MutableSharedFlow<Any>(
        replay = 0,
        extraBufferCapacity = 10
    )
    val events: SharedFlow<Any> = _events.asSharedFlow()
    
    // 事件去重记录
    private val recentEventHashes = mutableMapOf<Int, Long>()
    
    /**
     * 发送事件
     */
    suspend fun emit(event: Any) {
        // 记录发送的事件
        Timber.d("emit event: ${event.javaClass.simpleName}")
        _events.emit(event)
    }
    
    /**
     * 尝试发送事件，不抛出异常
     */
    fun tryEmit(event: Any): Boolean {
        // 事件防抖
        val eventHash = event.hashCode()
        val currentTime = System.currentTimeMillis()
        val lastEmitTime = recentEventHashes[eventHash]
        
        if (lastEmitTime != null && currentTime - lastEmitTime < DEBOUNCE_INTERVAL_MS) {
            // 在防抖时间内，忽略重复事件
            Timber.v("忽略重复事件: ${event.javaClass.simpleName} (${currentTime - lastEmitTime}ms < ${DEBOUNCE_INTERVAL_MS}ms)")
            return false
        }
        
        // 更新事件时间戳
        recentEventHashes[eventHash] = currentTime
        
        // 清理旧记录
        if (recentEventHashes.size > 100) {
            // 清理5秒前的记录
            val cutoffTime = currentTime - 5000L
            recentEventHashes.entries.removeIf { it.value < cutoffTime }
        }
        
        // 记录发送的事件
        Timber.d("tryEmit event: ${event.javaClass.simpleName}, success=${_events.tryEmit(event)}")
        return _events.tryEmit(event)
    }
} 