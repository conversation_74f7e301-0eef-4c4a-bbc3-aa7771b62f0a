package com.timeflow.app.di

import android.content.Context
import android.graphics.Bitmap
import coil.ImageLoader
import coil.decode.BitmapFactoryDecoder
import coil.memory.MemoryCache
import coil.util.DebugLogger
import com.timeflow.app.BuildConfig
import com.timeflow.app.utils.SafeImageLoader
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import timber.log.Timber
import javax.inject.Singleton

/**
 * Hilt模块 - 图像加载配置
 * 提供安全的ImageLoader来解决硬件位图问题
 */
@Module
@InstallIn(SingletonComponent::class)
object ImageModule {
    
    /**
     * 提供全局安全的ImageLoader实例
     */
    @Provides
    @Singleton
    fun provideImageLoader(
        @ApplicationContext context: Context
    ): ImageLoader {
        return ImageLoader.Builder(context)
            .memoryCache {
                MemoryCache.Builder(context)
                    .maxSizePercent(0.25) // 使用25%内存作为图像缓存
                    .build()
            }
            .components {
                // 使用BitmapFactoryDecoder确保位图兼容性
                add(BitmapFactoryDecoder.Factory())
            }
            // 关键设置：禁用硬件位图以避免软件渲染冲突
            .allowHardware(false)
            .allowRgb565(true) // 允许RGB565格式以节省内存
            .bitmapConfig(Bitmap.Config.RGB_565) // 默认使用RGB565
            .crossfade(300) // 300ms淡入淡出动画
            .respectCacheHeaders(false)
            // 只在调试模式下启用日志
            .logger(if (BuildConfig.DEBUG && Timber.treeCount > 0) DebugLogger() else null)
            .build()
    }
    
    /**
     * 提供SafeImageLoader工具类
     */
    @Provides
    @Singleton
    fun provideSafeImageLoader(
        @ApplicationContext context: Context
    ): SafeImageLoader {
        return SafeImageLoader(context)
    }
} 