package com.timeflow.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.graphics.FilterQuality
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.ImageLoader
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import coil.decode.BitmapFactoryDecoder
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.request.ImageRequest
import coil.util.DebugLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 安全的图像加载工具类
 * 解决硬件位图与软件渲染冲突问题
 */
@Singleton
class SafeImageLoader @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "SafeImageLoader"
        private const val MEMORY_CACHE_SIZE_PERCENT = 0.25 // 使用25%内存作为图像缓存
        private const val MAX_BITMAP_SIZE = 2048 // 最大位图尺寸
    }
    
    /**
     * 创建安全的ImageLoader配置
     */
    fun createSafeImageLoader(): ImageLoader {
        return ImageLoader.Builder(context)
            .memoryCache {
                MemoryCache.Builder(context)
                    .maxSizePercent(MEMORY_CACHE_SIZE_PERCENT)
                    .build()
            }
            .components {
                // 使用BitmapFactoryDecoder并配置为不使用硬件位图
                add(BitmapFactoryDecoder.Factory())
            }
            .crossfade(300) // 300ms淡入淡出动画
            .respectCacheHeaders(false)
            .allowRgb565(true) // 允许RGB565格式以节省内存
            .bitmapConfig(Bitmap.Config.RGB_565) // 默认使用RGB565
            .logger(if (timber.log.Timber.treeCount > 0) DebugLogger() else null)
            .build()
    }
    
    /**
     * 创建安全的图像请求
     */
    fun createSafeImageRequest(
        data: Any?,
        context: Context = this.context,
        crossfadeEnabled: Boolean = true,
        size: coil.size.Size = coil.size.Size.ORIGINAL
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(data)
            .size(size)
            .memoryCachePolicy(CachePolicy.ENABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .networkCachePolicy(CachePolicy.ENABLED)
            .crossfade(if (crossfadeEnabled) 300 else 0)
            // 关键：禁用硬件位图
            .allowHardware(false)
            // 设置最大尺寸限制
            .size(MAX_BITMAP_SIZE, MAX_BITMAP_SIZE)
            // 设置位图配置
            .bitmapConfig(Bitmap.Config.RGB_565)
            .build()
    }
}

/**
 * 安全的异步图像Painter
 * 自动处理硬件位图问题
 */
@Composable
fun rememberSafeAsyncImagePainter(
    model: Any?,
    imageLoader: ImageLoader? = null,
    transform: (AsyncImagePainter.State) -> AsyncImagePainter.State = AsyncImagePainter.DefaultTransform,
    onState: ((AsyncImagePainter.State) -> Unit)? = null,
    contentScale: ContentScale = ContentScale.Fit,
    filterQuality: FilterQuality = FilterQuality.Low
): AsyncImagePainter {
    val context = LocalContext.current
    
    // 创建安全的ImageRequest
    val safeRequest = remember(model) {
        ImageRequest.Builder(context)
            .data(model)
            .crossfade(300)
            // 关键：禁用硬件位图
            .allowHardware(false)
            .bitmapConfig(Bitmap.Config.RGB_565)
            .size(2048, 2048) // 限制最大尺寸
            .build()
    }
    
    // 使用安全的ImageLoader（如果提供）
    val safeImageLoader = imageLoader ?: remember {
        ImageLoader.Builder(context)
            .components {
                add(BitmapFactoryDecoder.Factory())
            }
            .allowRgb565(true)
            .bitmapConfig(Bitmap.Config.RGB_565)
            .crossfade(300)
            .build()
    }
    
    return rememberAsyncImagePainter(
        model = safeRequest,
        imageLoader = safeImageLoader,
        transform = transform,
        onState = { state ->
            when (state) {
                is AsyncImagePainter.State.Error -> {
                    Timber.e("图像加载失败: ${state.result.throwable.message}")
                }
                is AsyncImagePainter.State.Success -> {
                    Timber.d("图像加载成功")
                }
                else -> {}
            }
            onState?.invoke(state)
        },
        contentScale = contentScale,
        filterQuality = filterQuality
    )
}

/**
 * 安全的图像组件
 * 自动处理加载状态和错误
 */
@Composable
fun SafeAsyncImage(
    model: Any?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    fallback: Painter? = error,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    filterQuality: FilterQuality = FilterQuality.Low,
    showLoadingIndicator: Boolean = true
) {
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    
    val painter = rememberSafeAsyncImagePainter(
        model = model,
        contentScale = contentScale,
        filterQuality = filterQuality,
        onState = { state ->
            when (state) {
                is AsyncImagePainter.State.Loading -> {
                    isLoading = true
                    hasError = false
                    onLoading?.invoke(state)
                }
                is AsyncImagePainter.State.Success -> {
                    isLoading = false
                    hasError = false
                    onSuccess?.invoke(state)
                }
                is AsyncImagePainter.State.Error -> {
                    isLoading = false
                    hasError = true
                    onError?.invoke(state)
                    Timber.e("SafeAsyncImage加载失败: ${state.result.throwable.message}")
                }
                else -> {
                    isLoading = false
                    hasError = false
                }
            }
        }
    )
    
    Box(modifier = modifier) {
        Image(
            painter = when {
                hasError && error != null -> error
                hasError && fallback != null -> fallback
                isLoading && placeholder != null -> placeholder
                else -> painter
            },
            contentDescription = contentDescription,
            modifier = Modifier.matchParentSize(),
            alignment = alignment,
            contentScale = contentScale,
            alpha = alpha,
            colorFilter = colorFilter
        )
        
        // 显示加载指示器
        if (isLoading && showLoadingIndicator && placeholder == null) {
            CircularProgressIndicator(
                modifier = Modifier
                    .size(24.dp)
                    .align(Alignment.Center),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 2.dp
            )
        }
    }
}

/**
 * 预加载图像到缓存
 */
suspend fun preloadImage(
    context: Context,
    imageLoader: ImageLoader,
    data: Any?
) {
    try {
        val request = ImageRequest.Builder(context)
            .data(data)
            .allowHardware(false)
            .bitmapConfig(Bitmap.Config.RGB_565)
            .build()
        
        imageLoader.execute(request)
        Timber.d("图像预加载成功: $data")
    } catch (e: Exception) {
        Timber.e(e, "图像预加载失败: $data")
    }
} 