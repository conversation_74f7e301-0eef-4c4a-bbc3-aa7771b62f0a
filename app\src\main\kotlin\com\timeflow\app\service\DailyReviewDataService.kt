package com.timeflow.app.service

import android.content.Context
import android.util.Log
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.HabitRepository
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.model.Priority
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 每日回顾数据服务
 * 负责收集和分析用户的每日数据，生成个性化的回顾内容
 */
@Singleton
class DailyReviewDataService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository,
    private val habitRepository: HabitRepository,
    private val timeSessionRepository: TimeSessionRepository,
    private val reflectionRepository: ReflectionRepository,
    private val goalRepository: GoalRepository
) {
    companion object {
        private const val TAG = "DailyReviewDataService"
    }

    /**
     * 生成每日回顾数据
     */
    suspend fun generateDailyReviewData(date: LocalDate = LocalDate.now()): DailyReviewData {
        Log.d(TAG, "开始生成每日回顾数据: $date")
        
        return try {
            val taskStats = generateTaskStats(date)
            val habitStats = generateHabitStats(date)
            val focusStats = generateFocusStats(date)
            val reflectionStats = generateReflectionStats(date)
            val goalStats = generateGoalStats(date)
            
            val overallScore = calculateOverallScore(taskStats, habitStats, focusStats, reflectionStats, goalStats)
            val insights = generateInsights(taskStats, habitStats, focusStats, reflectionStats, goalStats)
            val recommendations = generateRecommendations(taskStats, habitStats, focusStats, reflectionStats, goalStats)
            
            DailyReviewData(
                date = date,
                taskStats = taskStats,
                habitStats = habitStats,
                focusStats = focusStats,
                reflectionStats = reflectionStats,
                goalStats = goalStats,
                overallScore = overallScore,
                insights = insights,
                recommendations = recommendations
            )
        } catch (e: Exception) {
            Log.e(TAG, "生成每日回顾数据失败", e)
            createEmptyReviewData(date)
        }
    }

    /**
     * 生成任务统计
     */
    private suspend fun generateTaskStats(date: LocalDate): TaskStats {
        val allTasks = taskRepository.getAllTasks()
        val todayTasks = allTasks.filter { task ->
            (task.startDate?.toLocalDate() == date) || 
            (task.dueDate?.toLocalDate() == date) ||
            (task.createdAt.toLocalDate() == date)
        }
        
        val completedTasks = todayTasks.count { it.isCompleted }
        val totalTasks = todayTasks.size
        val completionRate = if (totalTasks > 0) (completedTasks * 100 / totalTasks) else 0
        
        val highPriorityTasks = todayTasks.count {
            it.priority == com.timeflow.app.data.model.Priority.HIGH ||
            it.priority == com.timeflow.app.data.model.Priority.URGENT
        }
        val completedHighPriorityTasks = todayTasks.count {
            (it.priority == com.timeflow.app.data.model.Priority.HIGH ||
             it.priority == com.timeflow.app.data.model.Priority.URGENT) &&
            it.isCompleted
        }
        
        return TaskStats(
            totalTasks = totalTasks,
            completedTasks = completedTasks,
            completionRate = completionRate,
            highPriorityTasks = highPriorityTasks,
            completedHighPriorityTasks = completedHighPriorityTasks,
            overdueTasks = todayTasks.count { !it.isCompleted && it.dueDate?.toLocalDate()?.isBefore(date) == true }
        )
    }

    /**
     * 生成习惯统计
     */
    private suspend fun generateHabitStats(date: LocalDate): HabitStats {
        val allHabits = habitRepository.getAllHabits()
        val activeHabits = allHabits.filter { it.isActive } // 使用isActive而不是isArchived

        // 这里需要根据实际的习惯完成记录来计算
        // 暂时使用模拟数据，实际实现需要查询习惯完成记录
        val completedHabits = activeHabits.count {
            // 模拟：假设有50%的概率完成
            (it.id.hashCode() + date.dayOfYear) % 2 == 0
        }

        return HabitStats(
            totalHabits = activeHabits.size,
            completedHabits = completedHabits,
            completionRate = if (activeHabits.isNotEmpty()) (completedHabits * 100 / activeHabits.size) else 0,
            longestStreak = activeHabits.maxOfOrNull { it.longestStreak } ?: 0 // 使用实际的最长连续天数
        )
    }

    /**
     * 生成专注统计
     */
    private suspend fun generateFocusStats(date: LocalDate): FocusStats {
        val sessions = timeSessionRepository.getSessionsByDate(date).first()
        val totalDuration = sessions.sumOf { it.duration }
        val sessionCount = sessions.size
        val averageDuration = if (sessionCount > 0) totalDuration / sessionCount else 0
        
        return FocusStats(
            totalFocusTime = totalDuration,
            sessionCount = sessionCount,
            averageSessionDuration = averageDuration,
            longestSession = sessions.maxOfOrNull { it.duration } ?: 0
        )
    }

    /**
     * 生成感想统计
     */
    private suspend fun generateReflectionStats(date: LocalDate): ReflectionStats {
        val allReflections = reflectionRepository.getRecentReflections()
        val todayReflections = allReflections.filter {
            it.date.toEpochMilli() >= date.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli() &&
            it.date.toEpochMilli() < date.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli()
        }
        
        val averageRating = if (todayReflections.isNotEmpty()) {
            todayReflections.map { it.rating }.average()
        } else 0.0
        
        return ReflectionStats(
            reflectionCount = todayReflections.size,
            averageRating = averageRating,
            moodDistribution = todayReflections.groupBy { it.mood?.name ?: "未知" }
                .mapValues { it.value.size }
        )
    }

    /**
     * 生成目标统计
     */
    private suspend fun generateGoalStats(date: LocalDate): GoalStats {
        val allGoals = goalRepository.getAllGoalsList()
        val activeGoals = allGoals.filter { it.status == "进行中" }
        val completedToday = allGoals.filter { 
            it.status == "已完成" && 
            it.completedAt?.toLocalDate() == date
        }
        
        return GoalStats(
            totalActiveGoals = activeGoals.size,
            completedTodayGoals = completedToday.size,
            averageProgress = if (activeGoals.isNotEmpty()) {
                activeGoals.map { it.progress }.average()
            } else 0.0
        )
    }

    /**
     * 计算总体评分
     */
    private fun calculateOverallScore(
        taskStats: TaskStats,
        habitStats: HabitStats,
        focusStats: FocusStats,
        reflectionStats: ReflectionStats,
        goalStats: GoalStats
    ): Int {
        val taskScore = taskStats.completionRate * 0.3
        val habitScore = habitStats.completionRate * 0.25
        val focusScore = minOf(focusStats.totalFocusTime / 3600.0 * 100, 100.0) * 0.25 // 1小时=100分
        val reflectionScore = minOf(reflectionStats.reflectionCount * 25.0, 100.0) * 0.1 // 4条感想=100分
        val goalScore = goalStats.averageProgress * 100 * 0.1
        
        return (taskScore + habitScore + focusScore + reflectionScore + goalScore).toInt()
    }

    /**
     * 生成洞察
     */
    private fun generateInsights(
        taskStats: TaskStats,
        habitStats: HabitStats,
        focusStats: FocusStats,
        reflectionStats: ReflectionStats,
        goalStats: GoalStats
    ): List<String> {
        val insights = mutableListOf<String>()
        
        // 任务相关洞察
        when {
            taskStats.completionRate >= 90 -> insights.add("🎯 任务执行力超强！今天的效率让人印象深刻")
            taskStats.completionRate >= 70 -> insights.add("💪 任务完成得不错，保持这种节奏")
            taskStats.completionRate >= 50 -> insights.add("📈 任务完成过半，还有提升空间")
            else -> insights.add("🤔 今天的任务完成较少，可能需要调整计划")
        }
        
        // 专注时间洞察
        when {
            focusStats.totalFocusTime >= 7200 -> insights.add("⏰ 专注时间充足，深度工作做得很好")
            focusStats.totalFocusTime >= 3600 -> insights.add("🎯 有一定的专注时间，可以再增加一些")
            focusStats.totalFocusTime > 0 -> insights.add("⏱️ 专注时间较少，建议增加深度工作时间")
            else -> insights.add("💡 今天没有记录专注时间，试试番茄工作法")
        }
        
        // 习惯相关洞察
        if (habitStats.totalHabits > 0) {
            when {
                habitStats.completionRate >= 80 -> insights.add("🔄 习惯坚持得很好，自律性很强")
                habitStats.completionRate >= 50 -> insights.add("📅 部分习惯完成了，继续保持")
                else -> insights.add("⚡ 习惯执行需要加强，从小习惯开始")
            }
        }
        
        return insights
    }

    /**
     * 生成建议
     */
    private fun generateRecommendations(
        taskStats: TaskStats,
        habitStats: HabitStats,
        focusStats: FocusStats,
        reflectionStats: ReflectionStats,
        goalStats: GoalStats
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于任务完成情况的建议
        if (taskStats.completionRate < 50) {
            recommendations.add("明天可以尝试减少任务数量，专注于重要任务")
        }
        
        if (taskStats.overdueTasks > 0) {
            recommendations.add("优先处理逾期任务，避免积压")
        }
        
        // 基于专注时间的建议
        if (focusStats.totalFocusTime < 3600) {
            recommendations.add("建议明天安排至少1小时的专注时间")
        }
        
        // 基于习惯的建议
        if (habitStats.completionRate < 70 && habitStats.totalHabits > 0) {
            recommendations.add("选择1-2个最重要的习惯重点坚持")
        }
        
        // 基于感想的建议
        if (reflectionStats.reflectionCount == 0) {
            recommendations.add("记录今天的感想和收获，有助于成长")
        }
        
        return recommendations
    }

    /**
     * 创建空的回顾数据
     */
    private fun createEmptyReviewData(date: LocalDate): DailyReviewData {
        return DailyReviewData(
            date = date,
            taskStats = TaskStats(0, 0, 0, 0, 0, 0),
            habitStats = HabitStats(0, 0, 0, 0),
            focusStats = FocusStats(0, 0, 0, 0),
            reflectionStats = ReflectionStats(0, 0.0, emptyMap()),
            goalStats = GoalStats(0, 0, 0.0),
            overallScore = 0,
            insights = listOf("今天的数据较少，明天继续努力！"),
            recommendations = listOf("开始记录你的任务和目标")
        )
    }
}

/**
 * 每日回顾数据
 */
data class DailyReviewData(
    val date: LocalDate,
    val taskStats: TaskStats,
    val habitStats: HabitStats,
    val focusStats: FocusStats,
    val reflectionStats: ReflectionStats,
    val goalStats: GoalStats,
    val overallScore: Int,
    val insights: List<String>,
    val recommendations: List<String>
)

/**
 * 任务统计
 */
data class TaskStats(
    val totalTasks: Int,
    val completedTasks: Int,
    val completionRate: Int,
    val highPriorityTasks: Int,
    val completedHighPriorityTasks: Int,
    val overdueTasks: Int
)

/**
 * 习惯统计
 */
data class HabitStats(
    val totalHabits: Int,
    val completedHabits: Int,
    val completionRate: Int,
    val longestStreak: Int
)

/**
 * 专注统计
 */
data class FocusStats(
    val totalFocusTime: Long, // 秒
    val sessionCount: Int,
    val averageSessionDuration: Long, // 秒
    val longestSession: Long // 秒
)

/**
 * 感想统计
 */
data class ReflectionStats(
    val reflectionCount: Int,
    val averageRating: Double,
    val moodDistribution: Map<String, Int>
)

/**
 * 目标统计
 */
data class GoalStats(
    val totalActiveGoals: Int,
    val completedTodayGoals: Int,
    val averageProgress: Double
)
