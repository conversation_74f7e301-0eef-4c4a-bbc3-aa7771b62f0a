# 📱 TimeFlow 数据恢复指南

## 🔍 数据丢失原因分析

在版本迁移过程中，数据可能因以下原因丢失：
1. **数据库版本升级** - 从版本17升级到版本18时的迁移问题
2. **破坏性迁移** - 如果迁移脚本有问题，可能导致数据重置
3. **存储权限变更** - Android系统权限变化导致数据访问问题

## 🛠️ 数据恢复方法

### 方法一：使用应用内置的数据恢复功能 ⭐ 推荐

您的应用已经内置了完整的备份恢复系统！

#### 步骤：
1. **打开应用设置**
   - 进入应用主界面
   - 点击右下角的"设置"或"我的"
   - 找到"数据恢复"选项

2. **访问数据恢复界面**
   - 点击"数据恢复"进入恢复界面
   - 系统会自动扫描可用的备份文件

3. **选择备份进行恢复**
   - 查看备份列表，显示备份时间和文件大小
   - 选择最近的备份文件（通常是数据最完整的）
   - 点击"恢复此备份"

4. **重启应用**
   - 恢复完成后，应用会提示重启
   - 点击"重启应用"完成恢复过程

### 方法二：通过备份设置页面恢复

1. **进入备份设置**
   - 设置 → 备份设置
   - 查看"备份列表"部分

2. **使用快速恢复**
   - 点击"使用上一版本"按钮
   - 这会自动恢复到最近的可用备份

3. **手动选择备份**
   - 在备份列表中找到合适的备份文件
   - 点击恢复图标 🔄 进行恢复

## 📂 备份文件位置

如果应用内恢复不可用，您可以手动查找备份文件：

### Android 文件路径：
```
/data/data/com.timeflow.app/files/database_backups/
```

### 备份文件命名格式：
```
timeflow_database_v[版本号]_[时间戳].db
例如：timeflow_database_v17_20250709_143022.db
```

## 🔧 高级恢复选项

### 如果自动恢复失败：

1. **检查存储权限**
   - 确保应用有存储权限
   - Android设置 → 应用 → TimeFlow → 权限

2. **手动复制备份文件**
   - 使用文件管理器找到备份文件
   - 复制到应用数据目录

3. **清除应用数据后恢复**
   - 设置 → 应用 → TimeFlow → 存储 → 清除数据
   - 重新打开应用，进入数据恢复界面

## 📋 恢复后验证清单

恢复完成后，请检查以下数据是否正确：

### ✅ 用药记录
- [ ] 药物列表是否完整
- [ ] 用药历史记录是否存在
- [ ] 热力图数据是否正确显示

### ✅ 任务数据
- [ ] 待办事项列表
- [ ] 已完成任务历史
- [ ] 任务分类和标签

### ✅ 习惯追踪
- [ ] 习惯列表
- [ ] 打卡记录
- [ ] 统计数据

### ✅ 其他数据
- [ ] 目标设置
- [ ] 愿望清单
- [ ] 时间追踪记录
- [ ] 反思日记

## 🚨 紧急恢复步骤

如果常规方法都无法恢复数据：

### 1. 检查应用日志
在LogCat中搜索以下关键词：
- `DatabaseBackupManager`
- `backup_time`
- `emergency_backup`

### 2. 查找紧急备份
应用在每次恢复前都会创建紧急备份：
```
emergency_backup_[时间戳].db
```

### 3. 联系技术支持
如果以上方法都无效，请提供：
- 应用版本号
- 设备型号和Android版本
- 错误日志截图
- 数据丢失的具体时间

## 💡 预防措施

为避免将来数据丢失：

### 1. 启用自动备份
- 设置 → 备份设置
- 开启"自动备份"
- 设置备份频率（建议每日）

### 2. 手动备份重要数据
- 在重要操作前手动创建备份
- 定期检查备份文件是否正常

### 3. 云端同步（如果可用）
- 设置 → 同步设置
- 启用云端数据同步

## 📞 获取帮助

如果遇到问题：
1. 查看应用内帮助文档
2. 检查设置页面的故障排除指南
3. 查看LogCat日志获取详细错误信息

---

**重要提醒：** 
- 恢复过程中请保持应用在前台运行
- 确保设备有足够的存储空间
- 恢复前建议关闭其他应用以释放内存
- 恢复完成后务必重启应用以确保数据正确加载
