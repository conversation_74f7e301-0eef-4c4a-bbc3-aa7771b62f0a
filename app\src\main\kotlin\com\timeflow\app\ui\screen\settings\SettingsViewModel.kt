package com.timeflow.app.ui.screen.settings

import android.content.Context
import android.net.Uri
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import android.util.Log
import com.timeflow.app.utils.ImageUtils
import com.timeflow.app.service.PaymentManager
import com.timeflow.app.data.model.MembershipPlan
import javax.inject.Inject

// 为设置创建DataStore
private val Context.settingsDataStore by preferencesDataStore(name = "user_settings")

// 会员类型枚举
enum class MembershipType {
    NONE,           // 无会员
    MONTHLY,        // 月度会员
    LIFETIME        // 终身会员
}

// 用户信息数据类
data class UserInfo(
    val nickname: String = "",
    val email: String = "",
    val isEmailVerified: Boolean = false,
    val wechatBound: Boolean = false,
    val wechatNickname: String = "",
    val membershipType: MembershipType = MembershipType.NONE,
    val membershipExpiry: Long? = null, // 会员到期时间戳（月度会员用）
    val avatarUri: String? = null,
    val cardBackgroundUri: String? = null,
    val isLoggedIn: Boolean = false // 🔧 新增：登录状态
)

@HiltViewModel
class SettingsViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val paymentManager: PaymentManager
) : ViewModel() {
    
    // 定义DataStore键
    private object PreferencesKeys {
        val CARD_BACKGROUND_URI = stringPreferencesKey("card_background_uri")
        val AVATAR_URI = stringPreferencesKey("avatar_uri")
        val NICKNAME = stringPreferencesKey("user_nickname")
        val EMAIL = stringPreferencesKey("user_email")
        val EMAIL_VERIFIED = stringPreferencesKey("email_verified")
        val WECHAT_BOUND = stringPreferencesKey("wechat_bound")
        val WECHAT_NICKNAME = stringPreferencesKey("wechat_nickname")
        val MEMBERSHIP_TYPE = stringPreferencesKey("membership_type")
        val MEMBERSHIP_EXPIRY = longPreferencesKey("membership_expiry")
    }
    
    // 卡片背景URI状态
    private val _cardBackgroundUri = MutableStateFlow<Uri?>(null)
    val cardBackgroundUri: StateFlow<Uri?> = _cardBackgroundUri
    
    // 头像URI状态
    private val _avatarUri = MutableStateFlow<Uri?>(null)
    val avatarUri: StateFlow<Uri?> = _avatarUri
    
    // 用户信息状态
    private val _userInfo = MutableStateFlow(UserInfo())
    val userInfo: StateFlow<UserInfo> = _userInfo
    
    // 添加图片处理状态
    private val _isProcessingImage = MutableStateFlow(false)
    val isProcessingImage: StateFlow<Boolean> = _isProcessingImage
    
    // 添加处理状态消息
    private val _processingMessage = MutableStateFlow("")
    val processingMessage: StateFlow<String> = _processingMessage
    
    // 支付相关状态
    private val _showPaymentDialog = MutableStateFlow(false)
    val showPaymentDialog: StateFlow<Boolean> = _showPaymentDialog.asStateFlow()
    
    private val _selectedMembershipPlan = MutableStateFlow<MembershipPlan?>(null)
    val selectedMembershipPlan: StateFlow<MembershipPlan?> = _selectedMembershipPlan.asStateFlow()
    
    // 暴露PaymentManager的支付状态
    val paymentState = paymentManager.paymentState
    
    init {
        // 加载保存的背景URI
        viewModelScope.launch {
            context.settingsDataStore.data
                .map { preferences ->
                    preferences[PreferencesKeys.CARD_BACKGROUND_URI]?.let { uriString ->
                        try {
                            Uri.parse(uriString)
                        } catch (e: Exception) {
                            Log.e("SettingsViewModel", "解析背景URI失败: $uriString", e)
                            null
                        }
                    }
                }
                .collect { uri ->
                    _cardBackgroundUri.value = uri
                    // 同时更新用户信息中的背景URI
                    _userInfo.value = _userInfo.value.copy(cardBackgroundUri = uri?.toString())
                }
        }
        
        // 加载保存的头像URI
        viewModelScope.launch {
            context.settingsDataStore.data
                .map { preferences ->
                    preferences[PreferencesKeys.AVATAR_URI]?.let { uriString ->
                        try {
                            Uri.parse(uriString)
                        } catch (e: Exception) {
                            Log.e("SettingsViewModel", "解析头像URI失败: $uriString", e)
                            null
                        }
                    }
                }
                .collect { uri ->
                    _avatarUri.value = uri
                    // 同时更新用户信息中的头像URI
                    _userInfo.value = _userInfo.value.copy(avatarUri = uri?.toString())
                }
        }
        
        // 加载完整的用户信息
        viewModelScope.launch {
            context.settingsDataStore.data
                .map { preferences ->
                    val savedNickname = preferences[PreferencesKeys.NICKNAME]
                    val isLoggedIn = !savedNickname.isNullOrEmpty()
                    UserInfo(
                        nickname = if (isLoggedIn) savedNickname!! else "未登录",
                        email = preferences[PreferencesKeys.EMAIL] ?: "",
                        isEmailVerified = preferences[PreferencesKeys.EMAIL_VERIFIED]?.toBoolean() ?: false,
                        wechatBound = preferences[PreferencesKeys.WECHAT_BOUND]?.toBoolean() ?: false,
                        wechatNickname = preferences[PreferencesKeys.WECHAT_NICKNAME] ?: "",
                        membershipType = when(preferences[PreferencesKeys.MEMBERSHIP_TYPE]) {
                            "MONTHLY" -> MembershipType.MONTHLY
                            "LIFETIME" -> MembershipType.LIFETIME
                            else -> MembershipType.NONE
                        },
                        membershipExpiry = preferences[PreferencesKeys.MEMBERSHIP_EXPIRY],
                        avatarUri = preferences[PreferencesKeys.AVATAR_URI],
                        cardBackgroundUri = preferences[PreferencesKeys.CARD_BACKGROUND_URI],
                        isLoggedIn = isLoggedIn
                    )
                }
                .collect { userInfo ->
                    _userInfo.value = userInfo
                    Log.d("SettingsViewModel", "用户信息已加载: $userInfo")
                }
        }
    }
    
    /**
     * 保存卡片背景URI - 支持持久化存储
     */
    fun saveCardBackgroundUri(uri: Uri?) {
        viewModelScope.launch {
            try {
                if (uri != null) {
                    // 设置处理状态
                    _isProcessingImage.value = true
                    _processingMessage.value = "正在保存背景图片..."
                    
                    // 复制图片到内部存储并压缩
                    val fileName = ImageUtils.generateUniqueFileName("card_bg")
                    val internalUri = ImageUtils.copyAndCompressImage(context, uri, fileName)
                    
                    if (internalUri != null) {
                        // 删除旧的背景文件
                        val oldFileName = extractFileNameFromUri(_cardBackgroundUri.value)
                        if (oldFileName != null) {
                            ImageUtils.deleteImage(context, oldFileName)
                        }
                        
                        // 保存新的URI到DataStore
                        context.settingsDataStore.edit { preferences ->
                            preferences[PreferencesKeys.CARD_BACKGROUND_URI] = internalUri.toString()
                        }
                        _cardBackgroundUri.value = internalUri
                        _userInfo.value = _userInfo.value.copy(cardBackgroundUri = internalUri.toString())
                        
                        Log.d("SettingsViewModel", "卡片背景已保存到内部存储: $internalUri")
                        Log.d("SettingsViewModel", "文件大小: ${ImageUtils.getImageFileSize(context, fileName)} bytes")
                    } else {
                        Log.e("SettingsViewModel", "复制卡片背景到内部存储失败")
                    }
                    
                    // 清除处理状态
                    _isProcessingImage.value = false
                    _processingMessage.value = ""
                } else {
                    // 删除背景
                    val oldFileName = extractFileNameFromUri(_cardBackgroundUri.value)
                    if (oldFileName != null) {
                        ImageUtils.deleteImage(context, oldFileName)
                    }
                    
                    context.settingsDataStore.edit { preferences ->
                        preferences.remove(PreferencesKeys.CARD_BACKGROUND_URI)
                    }
                    _cardBackgroundUri.value = null
                    _userInfo.value = _userInfo.value.copy(cardBackgroundUri = null)
                    
                    Log.d("SettingsViewModel", "卡片背景已移除")
                }
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "保存卡片背景URI失败", e)
                // 清除处理状态
                _isProcessingImage.value = false
                _processingMessage.value = ""
            }
        }
    }
    
    /**
     * 保存头像URI - 支持持久化存储
     */
    fun saveAvatarUri(uri: Uri?) {
        viewModelScope.launch {
            try {
                if (uri != null) {
                    // 设置处理状态
                    _isProcessingImage.value = true
                    _processingMessage.value = "正在保存头像..."
                    
                    // 复制图片到内部存储并压缩
                    val fileName = ImageUtils.generateUniqueFileName("avatar")
                    val internalUri = ImageUtils.copyAndCompressImage(context, uri, fileName)
                    
                    if (internalUri != null) {
                        // 删除旧的头像文件
                        val oldFileName = extractFileNameFromUri(_avatarUri.value)
                        if (oldFileName != null) {
                            ImageUtils.deleteImage(context, oldFileName)
                        }
                        
                        // 保存新的URI到DataStore
                        context.settingsDataStore.edit { preferences ->
                            preferences[PreferencesKeys.AVATAR_URI] = internalUri.toString()
                        }
                        _avatarUri.value = internalUri
                        _userInfo.value = _userInfo.value.copy(avatarUri = internalUri.toString())
                        
                        Log.d("SettingsViewModel", "头像已保存到内部存储: $internalUri")
                        Log.d("SettingsViewModel", "文件大小: ${ImageUtils.getImageFileSize(context, fileName)} bytes")
                    } else {
                        Log.e("SettingsViewModel", "复制头像到内部存储失败")
                    }
                    
                    // 清除处理状态
                    _isProcessingImage.value = false
                    _processingMessage.value = ""
                } else {
                    // 删除头像
                    val oldFileName = extractFileNameFromUri(_avatarUri.value)
                    if (oldFileName != null) {
                        ImageUtils.deleteImage(context, oldFileName)
                    }
                    
                    context.settingsDataStore.edit { preferences ->
                    preferences.remove(PreferencesKeys.AVATAR_URI)
                }
                    _avatarUri.value = null
                    _userInfo.value = _userInfo.value.copy(avatarUri = null)
                    
                    Log.d("SettingsViewModel", "头像已移除")
                }
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "保存头像URI失败", e)
                // 清除处理状态
                _isProcessingImage.value = false
                _processingMessage.value = ""
            }
        }
    }
    
    /**
     * 更新用户昵称
     */
    fun updateNickname(nickname: String) {
        viewModelScope.launch {
            try {
                context.settingsDataStore.edit { preferences ->
                    preferences[PreferencesKeys.NICKNAME] = nickname
            }
                _userInfo.value = _userInfo.value.copy(nickname = nickname)
                Log.d("SettingsViewModel", "昵称已更新: $nickname")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "更新昵称失败", e)
            }
        }
    }
    
    /**
     * 更新邮箱地址
     */
    fun updateEmail(email: String) {
        viewModelScope.launch {
            try {
                context.settingsDataStore.edit { preferences ->
                    preferences[PreferencesKeys.EMAIL] = email
                    // 新邮箱默认未验证
                    preferences[PreferencesKeys.EMAIL_VERIFIED] = "false"
                }
                _userInfo.value = _userInfo.value.copy(email = email, isEmailVerified = false)
                Log.d("SettingsViewModel", "邮箱已更新: $email")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "更新邮箱失败", e)
            }
        }
    }
    
    /**
     * 设置邮箱验证状态
     */
    fun setEmailVerified(verified: Boolean) {
        viewModelScope.launch {
            try {
                context.settingsDataStore.edit { preferences ->
                    preferences[PreferencesKeys.EMAIL_VERIFIED] = verified.toString()
                }
                _userInfo.value = _userInfo.value.copy(isEmailVerified = verified)
                Log.d("SettingsViewModel", "邮箱验证状态已更新: $verified")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "更新邮箱验证状态失败", e)
            }
        }
    }
    
    /**
     * 绑定微信账户
     */
    fun bindWechat(nickname: String) {
        viewModelScope.launch {
            try {
                context.settingsDataStore.edit { preferences ->
                    preferences[PreferencesKeys.WECHAT_BOUND] = "true"
                    preferences[PreferencesKeys.WECHAT_NICKNAME] = nickname
                }
                _userInfo.value = _userInfo.value.copy(wechatBound = true, wechatNickname = nickname)
                Log.d("SettingsViewModel", "微信已绑定: $nickname")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "绑定微信失败", e)
            }
        }
    }
    
    /**
     * 解绑微信账户
     */
    fun unbindWechat() {
        viewModelScope.launch {
            try {
                context.settingsDataStore.edit { preferences ->
                    preferences[PreferencesKeys.WECHAT_BOUND] = "false"
                    preferences[PreferencesKeys.WECHAT_NICKNAME] = ""
                }
                _userInfo.value = _userInfo.value.copy(wechatBound = false, wechatNickname = "")
                Log.d("SettingsViewModel", "微信已解绑")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "解绑微信失败", e)
            }
        }
    }
    
    /**
     * 购买月度会员 - 显示支付对话框
     */
    fun purchaseMonthlyMembership() {
        _selectedMembershipPlan.value = MembershipPlan.MONTHLY
        _showPaymentDialog.value = true
    }
    
    /**
     * 购买终身会员 - 显示支付对话框
     */
    fun purchaseLifetimeMembership() {
        _selectedMembershipPlan.value = MembershipPlan.LIFETIME
        _showPaymentDialog.value = true
    }
    
    /**
     * 检查会员是否过期（仅对月度会员有效）
     */
    fun checkMembershipExpiry(): Boolean {
        val userInfo = _userInfo.value
        return when (userInfo.membershipType) {
            MembershipType.LIFETIME -> false // 终身会员永不过期
            MembershipType.MONTHLY -> {
                val expiry = userInfo.membershipExpiry
                expiry != null && System.currentTimeMillis() > expiry
            }
            MembershipType.NONE -> false // 无会员不存在过期问题
        }
    }
    
    /**
     * 获取会员状态描述
     */
    fun getMembershipStatus(): String {
        val userInfo = _userInfo.value
        return when (userInfo.membershipType) {
            MembershipType.LIFETIME -> "终身会员"
            MembershipType.MONTHLY -> {
                val expiry = userInfo.membershipExpiry
                if (expiry != null && System.currentTimeMillis() < expiry) {
                    val daysLeft = (expiry - System.currentTimeMillis()) / (24 * 60 * 60 * 1000)
                    "月度会员（剩余${daysLeft}天）"
                } else {
                    "会员已过期"
                }
            }
            MembershipType.NONE -> "非会员"
        }
    }
    

    
    /**
     * 从URI中提取文件名（用于删除旧文件）
     */
    private fun extractFileNameFromUri(uri: Uri?): String? {
        return try {
            uri?.lastPathSegment?.let { segment ->
                // 移除文件扩展名
                if (segment.contains(".")) {
                    segment.substring(0, segment.lastIndexOf("."))
                } else {
                    segment
                }
            }
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "提取文件名失败", e)
            null
        }
    }
    
    /**
     * 处理支付方法选择
     */
    fun onPaymentMethodSelected(
        activity: android.app.Activity,
        paymentMethod: com.timeflow.app.data.model.PaymentMethod
    ) {
        viewModelScope.launch {
            try {
                val membershipPlan = _selectedMembershipPlan.value
                if (membershipPlan == null) {
                    Log.e("SettingsViewModel", "未选择会员套餐")
                    return@launch
                }
                
                // 创建订单
                val order = paymentManager.createPaymentOrder(
                    membershipPlan = membershipPlan,
                    userId = "user_${System.currentTimeMillis()}" // 实际项目中应使用真实用户ID
                )
                
                Log.d("SettingsViewModel", "开始支付: ${order.orderId}")
                
                // 发起支付
                val result = paymentManager.startPayment(
                    activity = activity,
                    order = order,
                    paymentMethod = paymentMethod,
                    userId = "user_${System.currentTimeMillis()}"
                )
                
                result.onSuccess { paymentResult ->
                    if (paymentResult.status == com.timeflow.app.data.model.PaymentStatus.SUCCESS) {
                        // 支付成功，激活会员
                        activateMembership(membershipPlan, paymentResult)
                    }
                }.onFailure { throwable ->
                    Log.e("SettingsViewModel", "支付失败", throwable)
                }
                
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "处理支付失败", e)
            }
        }
    }
    
    /**
     * 激活会员权益
     */
    private suspend fun activateMembership(
        membershipPlan: MembershipPlan,
        paymentResult: com.timeflow.app.data.model.PaymentResult
    ) {
        try {
            when (membershipPlan) {
                MembershipPlan.MONTHLY -> {
                    val expiryTime = System.currentTimeMillis() + (30L * 24 * 60 * 60 * 1000) // 30天
                    context.settingsDataStore.edit { preferences ->
                        preferences[PreferencesKeys.MEMBERSHIP_TYPE] = "MONTHLY"
                        preferences[PreferencesKeys.MEMBERSHIP_EXPIRY] = expiryTime
                    }
                    _userInfo.value = _userInfo.value.copy(
                        membershipType = MembershipType.MONTHLY,
                        membershipExpiry = expiryTime
                    )
                    Log.d("SettingsViewModel", "月度会员激活成功，订单: ${paymentResult.orderId}")
                }
                MembershipPlan.LIFETIME -> {
                    context.settingsDataStore.edit { preferences ->
                        preferences[PreferencesKeys.MEMBERSHIP_TYPE] = "LIFETIME"
                        preferences.remove(PreferencesKeys.MEMBERSHIP_EXPIRY) // 终身会员无到期时间
                    }
                    _userInfo.value = _userInfo.value.copy(
                        membershipType = MembershipType.LIFETIME,
                        membershipExpiry = null
                    )
                    Log.d("SettingsViewModel", "终身会员激活成功，订单: ${paymentResult.orderId}")
                }
            }
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "激活会员失败", e)
        }
    }
    
    /**
     * 关闭支付对话框
     */
    fun dismissPaymentDialog() {
        _showPaymentDialog.value = false
        _selectedMembershipPlan.value = null
        paymentManager.clearPaymentState()
    }
    
    /**
     * 清理所有用户数据（退出登录时调用）
     */
    fun clearAllUserData() {
        viewModelScope.launch {
            try {
                // 清理图片文件
                ImageUtils.clearAllUserImages(context)
                
                // 清理DataStore
                context.settingsDataStore.edit { preferences ->
                    preferences.clear()
                }
                
                // 重置状态
                _cardBackgroundUri.value = null
                _avatarUri.value = null
                _userInfo.value = UserInfo()
                
                Log.d("SettingsViewModel", "所有用户数据已清理")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "清理用户数据失败", e)
            }
        }
    }
} 