# TimeFlow 应用开发规则

本文档定义了 TimeFlow 应用开发过程中必须遵循的规则和最佳实践，以确保代码质量、性能和可维护性。

## 1. 技术栈规范

### 1.1 基础技术
- **编程语言**: 必须使用 Kotlin，禁止使用 Java
- **UI 框架**: 必须使用 Jetpack Compose 构建 UI
- **架构模式**: 严格遵循 MVVM + Clean Architecture
- **设计规范**: 遵循 Material Design 3 设计原则
- **异步处理**: 使用 Kotlin Coroutines 和 Flow，禁止使用传统回调
- **本地存储**: 使用 Room 数据库，禁止直接使用 SQLite
- **依赖注入**: 使用 Hilt 进行依赖管理

### 1.2 版本要求
- Kotlin: 1.8.0 或更高版本
- Compose: 1.4.0 或更高版本
- Android 最低支持版本: API 26 (Android 8.0)
- 目标 Android 版本: 最新稳定版

## 2. 代码组织规范

### 2.1 包结构
```
com.timeflow.app/
├── data/               # 数据层
│   ├── local/          # 本地数据源 (Room)
│   ├── remote/         # 远程数据源 (API)
│   ├── repository/     # 仓库实现
│   └── model/          # 数据模型
├── domain/             # 领域层
│   ├── repository/     # 仓库接口
│   ├── usecase/        # 用例类
│   └── model/          # 领域模型
├── di/                 # 依赖注入
│   ├── module/         # Hilt 模块
│   └── qualifier/      # 限定符
├── presentation/       # 表示层
│   ├── common/         # 公共组件
│   ├── theme/          # 主题相关
│   └── features/       # 功能模块
│       ├── task/       # 任务管理功能
│       ├── time/       # 时间追踪功能
│       ├── stats/      # 统计分析功能
│       └── settings/   # 设置功能
├── util/               # 工具类
└── core/               # 核心功能
    ├── extension/      # Kotlin 扩展
    ├── performance/    # 性能优化
    └── navigation/     # 导航组件
```

### 2.2 命名规范
- **类名**: 使用 PascalCase，如 `TaskViewModel`
- **函数名**: 使用 camelCase，如 `createTask()`
- **变量名**: 使用 camelCase，如 `taskList`
- **常量**: 使用 UPPER_SNAKE_CASE，如 `MAX_TASK_COUNT`
- **Compose 函数**: 使用 PascalCase，如 `TaskItem()`
- **布局变量**: 使用 camelCase 并添加类型后缀，如 `taskListScreen`

### 2.3 文件组织
- 每个文件应专注于单一职责
- 单个文件不应超过 400 行
- 相关功能应放在同一个包中
- Compose UI 组件和 ViewModel 应分开放在不同文件中

## 3. 架构规范

### 3.1 UI 层 (Presentation)
- **模式**: 必须使用单向数据流 (UDF)
- **状态管理**:
  - 使用不可变 `State<T>` 或 `StateFlow<T>` 表示 UI 状态
  - 使用 sealed class 定义 UI 状态和 UI 事件
  - 通过 `collectAsStateWithLifecycle()` 收集状态
- **组件复用**:
  - 基础 UI 组件应放在 `common` 包中便于复用
  - 页面级组件应该接受 ViewModel 作为参数，而不是直接创建
- **导航**:
  - 使用统一的导航组件 `TimeFlowNavHost`
  - 导航参数必须使用类型安全的方式传递

### 3.2 视图模型层 (ViewModel)
- 每个屏幕对应一个 ViewModel
- ViewModel 只能依赖 Use Case 或其他 ViewModel
- ViewModel 必须继承 `androidx.lifecycle.ViewModel`
- 使用 `viewModelScope` 管理协程生命周期
- 通过 `StateFlow<UiState>` 暴露 UI 状态
- 通过函数暴露 UI 事件处理

### 3.3 领域层 (Domain)
- Use Case 应遵循单一职责原则
- 每个 Use Case 只执行一个业务操作
- 返回值应使用 `Flow<T>` 或 `suspend fun`
- 使用 `Result<T>` 封装操作结果，处理异常情况

### 3.4 数据层 (Data)
- Repository 实现必须实现领域层定义的接口
- 数据库操作必须在 IO 线程中执行
- 数据模型转换应在 Repository 中进行
- 必须实现缓存策略，优化数据访问性能
- 网络请求应实现重试和错误处理机制

## 4. Compose UI 规范

### 4.1 组件设计
- 组件应接受明确的参数，而不是依赖外部状态
- 使用 `remember` 和 `derivedStateOf` 优化重组
- 复杂界面应拆分为多个小型可组合函数
- 避免在组合函数中进行复杂计算或 IO 操作

### 4.2 性能优化
- 使用 `key` 参数识别列表项，优化重组
- 避免不必要的 lambda 创建，使用 `remember { ... }`
- 大型列表必须使用 `LazyColumn` 或 `LazyRow`
- 使用 `graphicsLayer` 优化动画性能
- 实现 RippleDrawable 优化，避免渲染问题

### 4.3 主题和样式
- 使用 Material 3 主题系统，支持动态颜色
- 所有颜色必须从主题中获取，禁止硬编码颜色值
- 字体大小必须使用主题中定义的文字样式
- 支持亮色和暗色主题，确保适当的对比度
- 支持用户自定义主题色

## 5. 性能优化规范

### 5.1 启动优化
- 实现冷启动优化，减少主线程工作
- 使用 `LaunchedEffect` 延迟加载非关键组件
- 实现数据库预热机制，避免首次查询延迟
- 监控并优化首屏渲染时间

### 5.2 渲染优化
- 使用轻量级动画代替默认动画
- 避免深层嵌套视图结构
- 优化 RippleDrawable 处理，减少渲染问题
- 使用硬件加速提升 UI 渲染性能

### 5.3 内存优化
- 实现内存泄漏检测与防护
- 优化图片加载和缓存策略
- 添加低内存情况下的资源释放机制
- 优化 Compose 重组，减少不必要的对象创建

### 5.4 数据库优化
- 实现数据库查询缓存，减少重复查询
- 使用 Flow 提高数据库响应性
- 确保耗时操作在 IO 线程执行
- 实现批量操作，避免多次数据库访问

## 6. 测试规范

### 6.1 单元测试
- 每个 Repository 和 Use Case 必须有对应的单元测试
- 使用 MockK 进行依赖模拟
- 测试覆盖率目标: 最低 80%
- 使用参数化测试覆盖边界情况

### 6.2 UI 测试
- 为关键用户流程编写 UI 测试
- 使用 Compose 测试 API 验证 UI 行为
- 模拟不同设备尺寸测试 UI 适配

### 6.3 性能测试
- 实现启动时间基准测试
- 实现关键操作性能基准测试
- 使用 Jetpack Benchmark 进行测量

## 7. 代码质量规范

### 7.1 代码审查
- 所有代码必须经过至少一次代码审查
- 遵循标准 Kotlin 编码风格
- 使用 ktlint 或 detekt 进行静态代码分析

### 7.2 文档
- 公共 API 必须有 KDoc 文档
- 复杂的业务逻辑必须添加详细注释
- 使用 TODO 和 FIXME 标记需要改进的代码

### 7.3 版本控制
- 使用 Git Flow 工作流
- 提交信息必须遵循约定式提交规范
- 功能分支必须通过测试才能合并

## 8. 安全规范

### 8.1 数据安全
- 敏感数据必须加密存储
- 避免将敏感信息写入日志
- 使用 Android Keystore 管理密钥

### 8.2 网络安全
- 所有网络请求必须使用 HTTPS
- 实现证书固定 (Certificate Pinning)
- 添加请求头保护，防止常见网络攻击

### 8.3 代码安全
- 启用 R8 代码混淆
- 移除调试信息和日志
- 实现运行时完整性检查

## 9. 发布和持续集成

### 9.1 构建变体
- 定义 debug、stage 和 release 构建变体
- 每个变体使用不同的应用 ID 后缀
- 为不同环境配置不同的后端 API 端点

### 9.2 持续集成
- 使用 GitHub Actions 或 Jenkins 自动构建
- 每次提交自动运行单元测试
- 定期运行 UI 测试和性能测试

### 9.3 发布流程
- 使用 App Bundle 格式发布
- 实现内部测试和 beta 测试渠道
- 发布前进行预发布检查清单验证

## 10. 功能特定规范

### 10.1 AI 任务拆分
- 实现离线任务拆分支持
- 提供手动调整 AI 拆分结果的功能
- 保存历史拆分记录供学习改进

### 10.2 时间追踪
- 支持后台计时，屏幕关闭仍能工作
- 实现番茄工作法和自定义工作/休息模式
- 提供时间使用报告和数据导出功能

### 10.3 情绪和生理期追踪
- 确保数据存储安全，实现端到端加密
- 提供数据备份和恢复功能
- 实现私密模式，隐藏敏感信息

### 10.4 统计分析
- 优化图表渲染性能
- 支持多种数据聚合和筛选方式
- 提供详细的数据导出功能

## 11. 异常处理和日志

### 11.1 异常处理
- 使用统一的异常处理机制
- 避免在 UI 线程抛出未捕获异常
- 为用户提供友好的错误提示

### 11.2 日志
- 使用结构化日志记录关键事件
- 在 release 版本中禁用调试日志
- 实现日志级别控制，方便调试

## 12. 辅助功能和国际化

### 12.1 辅助功能
- 支持 TalkBack 屏幕阅读器
- 确保所有可交互元素有足够的点击区域
- 提供高对比度主题选项

### 12.2 国际化
- 使用资源文件实现多语言支持
- 考虑不同语言文本长度差异的 UI 布局
- 支持 RTL (从右到左) 布局

## 附录: 技术债务处理

- 每个 Sprint 分配 20% 时间处理技术债务
- 维护技术债务列表，按影响程度排序
- 优先修复性能问题和用户体验问题 