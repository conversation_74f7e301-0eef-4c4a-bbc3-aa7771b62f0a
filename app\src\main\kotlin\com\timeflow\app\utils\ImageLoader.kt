package com.timeflow.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.LruCache
import android.widget.ImageView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 简单的图片加载工具类
 * 支持内存缓存管理和异步加载
 */
class ImageLoader private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: ImageLoader? = null
        
        fun getInstance(): ImageLoader =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: ImageLoader().also { INSTANCE = it }
            }
    }
    
    // 图片内存缓存
    private val memoryCache: LruCache<String, Bitmap>
    
    // 加载中的请求
    private val loadingRequests = ConcurrentHashMap<String, WeakReference<ImageView>>()
    
    init {
        // 计算可用内存的1/8作为缓存大小
        val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        val cacheSize = maxMemory / 8
        
        memoryCache = object : LruCache<String, Bitmap>(cacheSize) {
            override fun sizeOf(key: String, bitmap: Bitmap): Int {
                // 缓存大小计算方式：每像素占用字节数 * 宽度 * 高度 / 1024
                return bitmap.byteCount / 1024
            }
        }
    }
    
    /**
     * 清除内存缓存
     */
    fun clearMemoryCache() {
        try {
            memoryCache.evictAll()
        } catch (e: Exception) {
            // 忽略清理过程中的错误
        }
    }
    
    /**
     * 获取缓存的Bitmap
     */
    fun getBitmapFromCache(key: String): Bitmap? {
        return memoryCache.get(key)
    }
    
    /**
     * 将Bitmap添加到缓存
     */
    fun addBitmapToCache(key: String, bitmap: Bitmap) {
        if (getBitmapFromCache(key) == null) {
            memoryCache.put(key, bitmap)
        }
    }
    
    /**
     * 取消加载图片
     */
    fun cancelRequest(imageView: ImageView) {
        loadingRequests.entries.removeIf { it.value.get() == imageView }
    }
    
    /**
     * 监控内存使用情况
     * @return 当前缓存使用百分比
     */
    fun getMemoryCacheUsage(): Float {
        val size = memoryCache.size()
        val maxSize = memoryCache.maxSize()
        return if (maxSize > 0) size.toFloat() / maxSize else 0f
    }
} 