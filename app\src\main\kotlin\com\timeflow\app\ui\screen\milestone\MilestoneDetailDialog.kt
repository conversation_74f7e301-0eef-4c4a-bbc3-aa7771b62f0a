package com.timeflow.app.ui.screen.milestone

import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import com.timeflow.app.ui.screen.milestone.Milestone
import com.timeflow.app.ui.screen.milestone.MilestoneCategory
import com.timeflow.app.ui.screen.milestone.MonetMorandiColors
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt

/**
 * 里程碑详情对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MilestoneDetailDialog(
    milestone: Milestone,
    onDismiss: () -> Unit,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    onImageAdd: (List<String>) -> Unit,
    onImageRemove: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val hapticFeedback = LocalHapticFeedback.current
    val context = LocalContext.current
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetMultipleContents()
    ) { uris: List<Uri> ->
        if (uris.isNotEmpty()) {
            // 将URI转换为字符串并保存到内部存储
            val savedImagePaths = mutableListOf<String>()
            uris.forEach { uri ->
                try {
                    val savedPath = saveImageToInternalStorage(context, uri)
                    savedPath?.let { savedImagePaths.add(it) }
                } catch (e: Exception) {
                    // 处理保存失败的情况
                }
            }
            if (savedImagePaths.isNotEmpty()) {
                onImageAdd(savedImagePaths)
            }
        }
    }
    
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    
    val categoryColor = when (milestone.category) {
        MilestoneCategory.CAREER -> MonetMorandiColors.Career
        MilestoneCategory.EDUCATION -> MonetMorandiColors.Education
        MilestoneCategory.LOVE -> MonetMorandiColors.Love
        MilestoneCategory.LIFE -> MonetMorandiColors.Life
        MilestoneCategory.TRAVEL -> MonetMorandiColors.Travel
        MilestoneCategory.HEALTH -> MonetMorandiColors.Health
        else -> MonetMorandiColors.Life
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(20.dp)),
            colors = CardDefaults.cardColors(
                containerColor = MonetMorandiColors.Surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 头部：类别图标、标题和操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 类别图标
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(RoundedCornerShape(12.dp))
                            .background(categoryColor.copy(alpha = 0.15f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = milestone.category.icon,
                            contentDescription = milestone.category.displayName,
                            tint = categoryColor,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // 标题和类别
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = milestone.title,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = MonetMorandiColors.OnSurface
                        )
                        
                        Text(
                            text = milestone.category.displayName,
                            fontSize = 12.sp,
                            color = categoryColor,
                            modifier = Modifier
                                .background(
                                    categoryColor.copy(alpha = 0.15f),
                                    RoundedCornerShape(6.dp)
                                )
                                .padding(horizontal = 8.dp, vertical = 2.dp)
                        )
                    }
                    
                    // 操作按钮
                    Row {
                        IconButton(
                            onClick = {
                                hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.TextHandleMove)
                                onEdit()
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = "编辑",
                                tint = MonetMorandiColors.Primary
                            )
                        }
                        
                        IconButton(
                            onClick = {
                                hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.LongPress)
                                showDeleteConfirmation = true
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "删除",
                                tint = MonetMorandiColors.Error
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 日期和进度
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "日期",
                            fontSize = 12.sp,
                            color = MonetMorandiColors.OnSurfaceVariant
                        )
                        Text(
                            text = milestone.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = MonetMorandiColors.OnSurface
                        )
                    }
                    
                    Column(
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "完成度",
                            fontSize = 12.sp,
                            color = MonetMorandiColors.OnSurfaceVariant
                        )
                        
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "${milestone.completionPercentage.roundToInt()}%",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = MonetMorandiColors.OnSurface
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Box(
                                modifier = Modifier.size(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    progress = { milestone.completionPercentage / 100f },
                                    modifier = Modifier.size(28.dp),
                                    color = categoryColor,
                                    trackColor = categoryColor.copy(alpha = 0.2f),
                                    strokeWidth = 3.dp
                                )
                                
                                if (milestone.completionPercentage >= 100f) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已完成",
                                        tint = categoryColor,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 描述
                if (milestone.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "描述",
                        fontSize = 12.sp,
                        color = MonetMorandiColors.OnSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = milestone.description,
                        fontSize = 14.sp,
                        color = MonetMorandiColors.OnSurface,
                        lineHeight = 20.sp
                    )
                }
                
                // 图片区域
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "图片 (${milestone.imageUris.size})",
                        fontSize = 12.sp,
                        color = MonetMorandiColors.OnSurfaceVariant
                    )
                    
                    TextButton(
                        onClick = {
                            hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.TextHandleMove)
                            imagePickerLauncher.launch("image/*")
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加图片",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(text = "添加图片")
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (milestone.imageUris.isNotEmpty()) {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(milestone.imageUris) { imageUri ->
                            Box {
                                Card(
                                    modifier = Modifier
                                        .size(80.dp)
                                        .clip(RoundedCornerShape(8.dp)),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                                ) {
                                    Image(
                                        painter = rememberAsyncImagePainter(imageUri.toUri()),
                                        contentDescription = "里程碑图片",
                                        modifier = Modifier.fillMaxSize(),
                                        contentScale = ContentScale.Crop
                                    )
                                }
                                
                                // 删除按钮
                                IconButton(
                                    onClick = {
                                        hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.TextHandleMove)
                                        onImageRemove(imageUri)
                                    },
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .size(24.dp)
                                        .background(
                                            MonetMorandiColors.Error.copy(alpha = 0.8f),
                                            CircleShape
                                        )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "删除图片",
                                        tint = Color.White,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }
                } else {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp)
                            .clickable {
                                hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.TextHandleMove)
                                imagePickerLauncher.launch("image/*")
                            },
                        colors = CardDefaults.cardColors(
                            containerColor = MonetMorandiColors.Primary.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "添加图片",
                                    tint = MonetMorandiColors.Primary,
                                    modifier = Modifier.size(24.dp)
                                )
                                
                                Spacer(modifier = Modifier.height(4.dp))
                                
                                Text(
                                    text = "点击添加图片",
                                    fontSize = 12.sp,
                                    color = MonetMorandiColors.Primary
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 底部关闭按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MonetMorandiColors.Primary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "关闭",
                        fontSize = 16.sp,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = {
                Text(
                    text = "确认删除",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(text = "确定要删除里程碑「${milestone.title}」吗？此操作不可撤销。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.LongPress)
                        showDeleteConfirmation = false
                        onDelete()
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MonetMorandiColors.Error
                    )
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteConfirmation = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 保存图片到内部存储
 */
private fun saveImageToInternalStorage(context: Context, uri: Uri): String? {
    return try {
        val inputStream = context.contentResolver.openInputStream(uri)
        val fileName = "milestone_${System.currentTimeMillis()}.jpg"
        val file = java.io.File(context.filesDir, fileName)
        
        inputStream?.use { input ->
            file.outputStream().use { output ->
                input.copyTo(output)
            }
        }
        
        file.absolutePath
    } catch (e: Exception) {
        null
    }
} 