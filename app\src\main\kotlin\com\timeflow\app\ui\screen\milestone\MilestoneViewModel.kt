package com.timeflow.app.ui.screen.milestone

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import java.time.LocalDate
import javax.inject.Inject

/**
 * 里程碑UI状态
 */
data class MilestoneUiState(
    val milestones: List<Milestone> = emptyList(),
    val filteredMilestones: List<Milestone> = emptyList(),
    val selectedCategory: MilestoneCategory? = null,
    val searchQuery: String = "",
    val isLoading: Boolean = false,
    val selectedYearFilter: Int? = null,
    val showAnalytics: Boolean = false,
    val insights: MilestoneInsights = MilestoneInsights(),
    val error: String? = null,
    val selectedMilestone: Milestone? = null
)

/**
 * 里程碑视图模型
 */
@HiltViewModel
class MilestoneViewModel @Inject constructor(
    // 此处应注入里程碑仓库
    // private val milestoneRepository: MilestoneRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MilestoneUiState())
    val uiState: StateFlow<MilestoneUiState> = _uiState.asStateFlow()

    private val _viewMode = MutableStateFlow(ViewMode.TIMELINE)
    val viewMode: StateFlow<ViewMode> = _viewMode.asStateFlow()
    
    // 保存预处理完成的数据分析结果
    private var preparedInsights: MilestoneInsights? = null

    // 模拟数据 - 实际应用中应从数据库加载
    private val demoMilestones = listOf(
        Milestone(
            id = "1",
            title = "大学毕业典礼",
            description = "在樱花礼堂接过计算机科学学士学位证书，与导师和同学们合影留念。",
            date = LocalDate.of(2019, 6, 15),
            category = MilestoneCategory.EDUCATION,
            tags = listOf("毕业", "里程碑"),
            completionPercentage = 100f,
            milestoneType = MilestoneType.DECISION
        ),
        Milestone(
            id = "2",
            title = "遇见人生伴侣",
            description = "在东京塔下的咖啡馆初次约会，聊到打烊仍意犹未尽。",
            date = LocalDate.of(2022, 3, 15),
            category = MilestoneCategory.LOVE,
            tags = listOf("爱情", "初遇"),
            completionPercentage = 100f,
            milestoneType = MilestoneType.OPPORTUNITY
        ),
        Milestone(
            id = "3",
            title = "加入科技公司",
            description = "成为XYZ科技的前端开发工程师，负责核心产品的前端架构设计。",
            date = LocalDate.of(2021, 4, 1),
            category = MilestoneCategory.CAREER,
            rating = 4.5f,
            completionPercentage = 100f,
            milestoneType = MilestoneType.OPPORTUNITY
        ),
        Milestone(
            id = "4", 
            title = "独自环球旅行",
            description = "历时3个月，游历12个国家，完成了人生第一次独自环球旅行。",
            date = LocalDate.of(2023, 8, 1),
            category = MilestoneCategory.TRAVEL,
            completionPercentage = 100f,
            milestoneType = MilestoneType.DECISION
        ),
        Milestone(
            id = "5",
            title = "购买第一套房",
            description = "在城市中心购买了第一套属于自己的房子，开始新的生活。",
            date = LocalDate.of(2024, 2, 10),
            category = MilestoneCategory.LIFE,
            completionPercentage = 90f,
            milestoneType = MilestoneType.DECISION
        ),
        // 添加更多样例数据
        Milestone(
            id = "6",
            title = "学习人工智能",
            description = "完成了为期6个月的人工智能与机器学习专业课程，获得证书。",
            date = LocalDate.of(2022, 10, 5),
            category = MilestoneCategory.EDUCATION,
            completionPercentage = 100f,
            milestoneType = MilestoneType.DECISION
        ),
        Milestone(
            id = "7",
            title = "晋升技术主管",
            description = "在公司内部晋升为技术团队主管，领导10人团队开发核心产品。",
            date = LocalDate.of(2023, 2, 20),
            category = MilestoneCategory.CAREER,
            rating = 5f,
            completionPercentage = 100f,
            milestoneType = MilestoneType.OPPORTUNITY
        )
    )

    init {
        loadMilestones()
        // 启动后台任务预计算分析数据
        precomputeInsights()
    }

    /**
     * 加载里程碑数据
     */
    private fun loadMilestones() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                
                // 模拟网络加载
                // val milestones = milestoneRepository.getMilestones()
                val milestones = demoMilestones
                
                // 更新状态
                _uiState.update { 
                    it.copy(
                        milestones = milestones,
                        filteredMilestones = milestones,
                        isLoading = false
                    ) 
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        error = "加载里程碑数据失败: ${e.message}",
                        isLoading = false
                    ) 
                }
            }
        }
    }
    
    /**
     * 预计算分析数据（后台任务）
     */
    private fun precomputeInsights() {
        viewModelScope.launch(Dispatchers.Default) {
            val milestones = _uiState.value.milestones
            preparedInsights = calculateInsights(milestones)
        }
    }

    /**
     * 计算里程碑分析数据
     */
    private fun calculateInsights(milestones: List<Milestone>): MilestoneInsights {
        val categoryDistribution = milestones.groupBy { it.category }
            .mapValues { it.value.size }
        
        val yearlyDistribution = milestones.groupBy { it.date.year }
            .mapValues { it.value.size }
        
        return MilestoneInsights(
            totalMilestones = milestones.size,
            completedMilestones = milestones.count { it.completionPercentage >= 100f },
            categoryDistribution = categoryDistribution,
            turnedPointsCount = milestones.count { it.milestoneType != MilestoneType.REGULAR },
            yearlyDistribution = yearlyDistribution
        )
    }

    /**
     * 设置视图模式
     */
    fun setViewMode(mode: ViewMode) {
        _viewMode.value = mode
    }

    /**
     * 设置过滤条件
     */
    fun setFilter(category: MilestoneCategory? = null, year: Int? = null, query: String = "") {
        viewModelScope.launch {
            val searchQuery = query.lowercase().trim()
            
            // 在后台线程进行过滤
            val filtered = withContext(Dispatchers.Default) {
                _uiState.value.milestones.filter { milestone ->
                    val matchesCategory = category?.let { milestone.category == it } ?: true
                    val matchesYear = year?.let { milestone.date.year == it } ?: true
                    val matchesQuery = searchQuery.isEmpty() || 
                                      milestone.title.lowercase().contains(searchQuery) ||
                                      milestone.description.lowercase().contains(searchQuery) ||
                                      milestone.tags.any { it.lowercase().contains(searchQuery) }
                    
                    matchesCategory && matchesYear && matchesQuery
                }
            }
            
            _uiState.update { 
                it.copy(
                    filteredMilestones = filtered,
                    selectedCategory = category,
                    selectedYearFilter = year,
                    searchQuery = query
                ) 
            }
        }
    }

    /**
     * 切换数据分析对话框显示状态
     */
    fun toggleAnalytics(show: Boolean) {
        viewModelScope.launch {
            if (show) {
                // 如果已经预计算了分析数据则直接使用
                val insights = preparedInsights ?: withContext(Dispatchers.Default) {
                    calculateInsights(_uiState.value.milestones)
                }
                
                _uiState.update { 
                    it.copy(
                        showAnalytics = true,
                        insights = insights
                    ) 
                }
            } else {
                _uiState.update { it.copy(showAnalytics = false) }
            }
        }
    }
    
    /**
     * 预加载里程碑详情
     * 在导航前预先加载和处理数据，减少页面转换时的卡顿
     */
    fun prefetchMilestoneDetails(milestoneId: String) {
        viewModelScope.launch(Dispatchers.Default) {
            val milestone = _uiState.value.milestones.find { it.id == milestoneId }
            withContext(Dispatchers.Main) {
                _uiState.update { it.copy(selectedMilestone = milestone) }
            }
        }
    }

    /**
     * 添加新里程碑
     */
    fun addMilestone(milestone: Milestone) {
        viewModelScope.launch {
            try {
                // 模拟保存到数据库
                // milestoneRepository.addMilestone(milestone)
                
                // 更新本地数据
                val updatedMilestones = _uiState.value.milestones + milestone
                
                // 在后台线程计算新的分析数据
                launch(Dispatchers.Default) {
                    preparedInsights = calculateInsights(updatedMilestones)
                }
                
                _uiState.update { 
                    it.copy(
                        milestones = updatedMilestones,
                        filteredMilestones = applyCurrentFilters(updatedMilestones)
                    ) 
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "添加里程碑失败: ${e.message}") }
            }
        }
    }

    /**
     * 更新里程碑
     */
    fun updateMilestone(milestone: Milestone) {
        viewModelScope.launch {
            try {
                // 模拟更新到数据库
                // milestoneRepository.updateMilestone(milestone)
                
                // 更新本地数据
                val updatedMilestones = _uiState.value.milestones.map { 
                    if (it.id == milestone.id) milestone else it 
                }
                
                // 在后台线程计算新的分析数据
                launch(Dispatchers.Default) {
                    preparedInsights = calculateInsights(updatedMilestones)
                }
                
                _uiState.update { 
                    it.copy(
                        milestones = updatedMilestones,
                        filteredMilestones = applyCurrentFilters(updatedMilestones),
                        selectedMilestone = null // 清除已选中的里程碑
                    ) 
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "更新里程碑失败: ${e.message}") }
            }
        }
    }
    
    /**
     * 删除里程碑
     */
    fun deleteMilestone(milestoneId: String) {
        viewModelScope.launch {
            try {
                // 模拟从数据库删除
                // milestoneRepository.deleteMilestone(milestoneId)
                
                // 更新本地数据
                val updatedMilestones = _uiState.value.milestones.filter { it.id != milestoneId }
                
                // 在后台线程计算新的分析数据
                launch(Dispatchers.Default) {
                    preparedInsights = calculateInsights(updatedMilestones)
                }
                
                _uiState.update { 
                    it.copy(
                        milestones = updatedMilestones,
                        filteredMilestones = applyCurrentFilters(updatedMilestones),
                        selectedMilestone = null // 清除已选中的里程碑
                    ) 
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "删除里程碑失败: ${e.message}") }
            }
        }
    }
    
    /**
     * 应用当前筛选条件
     */
    private fun applyCurrentFilters(milestones: List<Milestone>): List<Milestone> {
        val state = _uiState.value
        val category = state.selectedCategory
        val year = state.selectedYearFilter
        val query = state.searchQuery.lowercase().trim()
        
        return milestones.filter { milestone ->
            val matchesCategory = category?.let { milestone.category == it } ?: true
            val matchesYear = year?.let { milestone.date.year == it } ?: true
            val matchesQuery = query.isEmpty() || 
                            milestone.title.lowercase().contains(query) ||
                            milestone.description.lowercase().contains(query) ||
                            milestone.tags.any { it.lowercase().contains(query) }
            
            matchesCategory && matchesYear && matchesQuery
        }
    }

    /**
     * 导航到添加里程碑页面
     */
    fun navigateToAddMilestone() {
        // 实际应用中应使用导航组件
        // NavController.navigate(...)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
} 