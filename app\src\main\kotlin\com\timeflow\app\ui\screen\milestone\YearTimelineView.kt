package com.timeflow.app.ui.screen.milestone

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt

/**
 * 年时间轴视图 - 垂直时间轴展示人生关键时刻
 */
@Composable
fun YearTimelineView(
    milestones: List<Milestone>,
    onMilestoneClick: (Milestone) -> Unit,
    onMilestoneLongPress: (Milestone) -> Unit,
    modifier: Modifier = Modifier
) {
    // 按年份分组并排序（从最新到最旧）
    val groupedByYear = remember(milestones) {
        milestones.groupBy { it.date.year }
            .toSortedMap(compareByDescending { it })
    }
    
    // 当前年份
    val currentYear = remember { LocalDate.now().year }
    
    // 年份范围
    val yearRange = remember(groupedByYear) {
        if (groupedByYear.isEmpty()) {
            currentYear to currentYear
        } else {
            val minYear = groupedByYear.keys.minOrNull() ?: currentYear
            val maxYear = groupedByYear.keys.maxOrNull() ?: currentYear
            minYear to maxYear
        }
    }
    
    // 生成年份列表（包含空年份以显示连续的时间轴）
    val allYears = remember(yearRange) {
        (yearRange.second downTo yearRange.first).toList()
    }
    
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        // 时间轴标题
        item {
            TimelineHeader(
                totalYears = allYears.size,
                totalMilestones = milestones.size,
                yearRange = yearRange
            )
        }
        
        // 为每一年生成一个时间轴项
        items(
            items = allYears,
            key = { year -> year }
        ) { year ->
            val yearMilestones = groupedByYear[year] ?: emptyList()
            val isCurrentYear = year == currentYear
            val hasEvents = yearMilestones.isNotEmpty()
            val isDecadeStart = year % 10 == 0
            
            YearTimelineItem(
                year = year,
                milestones = yearMilestones,
                isCurrentYear = isCurrentYear,
                hasEvents = hasEvents,
                isDecadeStart = isDecadeStart,
                isFirst = year == allYears.first(),
                isLast = year == allYears.last(),
                onMilestoneClick = onMilestoneClick,
                onMilestoneLongPress = onMilestoneLongPress
            )
        }
        
        // 底部空间
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * 时间轴标题组件
 */
@Composable
fun TimelineHeader(
    totalYears: Int,
    totalMilestones: Int,
    yearRange: Pair<Int, Int>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MonetMorandiColors.Primary.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column {
                Text(
                    text = "人生时间轴",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MonetMorandiColors.Primary
                )
                Text(
                    text = "${yearRange.first} - ${yearRange.second}",
                    fontSize = 12.sp,
                    color = MonetMorandiColors.OnSurfaceVariant
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "$totalMilestones 个里程碑",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MonetMorandiColors.OnSurface
                )
                Text(
                    text = "跨越 $totalYears 年",
                    fontSize = 12.sp,
                    color = MonetMorandiColors.OnSurfaceVariant
                )
            }
        }
    }
}

/**
 * 年份时间轴项组件
 */
@Composable
fun YearTimelineItem(
    year: Int,
    milestones: List<Milestone>,
    isCurrentYear: Boolean,
    hasEvents: Boolean,
    isDecadeStart: Boolean,
    isFirst: Boolean,
    isLast: Boolean,
    onMilestoneClick: (Milestone) -> Unit,
    onMilestoneLongPress: (Milestone) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 左侧：年份标签和时间轴线
        TimelineIndicator(
            year = year,
            milestones = milestones,
            isCurrentYear = isCurrentYear,
            hasEvents = hasEvents,
            isDecadeStart = isDecadeStart,
            isFirst = isFirst,
            isLast = isLast
        )
        
        // 右侧：事件内容
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(bottom = if (isLast) 0.dp else 24.dp)
        ) {
            // 年代分隔线
            if (isDecadeStart && !isFirst) {
                DecadeSeparator(year)
            }
            
            if (hasEvents) {
                // 按日期排序里程碑（从早到晚）
                val sortedMilestones = milestones.sortedBy { it.date }
                
                sortedMilestones.forEachIndexed { index, milestone ->
                    TimelineMilestoneCard(
                        milestone = milestone,
                        onClick = { onMilestoneClick(milestone) },
                        onLongPress = { onMilestoneLongPress(milestone) },
                        isLast = index == sortedMilestones.size - 1
                    )
                }
            } else if (isCurrentYear) {
                // 当年没有事件时显示占位内容
                EmptyYearPlaceholder()
            }
        }
    }
}

/**
 * 年代分隔线
 */
@Composable
fun DecadeSeparator(
    year: Int,
    modifier: Modifier = Modifier
) {
    val decade = (year / 10) * 10
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                MonetMorandiColors.Primary.copy(alpha = 0.3f)
                            )
                        )
                    )
            )
            
            Surface(
                color = MonetMorandiColors.Primary.copy(alpha = 0.15f),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.padding(horizontal = 8.dp)
            ) {
                Text(
                    text = "${decade}s",
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    color = MonetMorandiColors.Primary,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
            
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                MonetMorandiColors.Primary.copy(alpha = 0.3f),
                                Color.Transparent
                            )
                        )
                    )
            )
        }
    }
}

/**
 * 时间轴指示器
 */
@Composable
fun TimelineIndicator(
    year: Int,
    milestones: List<Milestone>,
    isCurrentYear: Boolean,
    hasEvents: Boolean,
    isDecadeStart: Boolean,
    isFirst: Boolean,
    isLast: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.width(80.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        // 增强的时间轴线
        Canvas(
            modifier = Modifier
                .width(6.dp)
                .height(140.dp)
                .offset(y = 40.dp)
        ) {
            val strokeWidth = if (hasEvents) 6.dp.toPx() else 3.dp.toPx()
            val lineColor = when {
                isCurrentYear -> MonetMorandiColors.Accent.copy(alpha = 0.8f)
                hasEvents -> MonetMorandiColors.Primary.copy(alpha = 0.7f)
                else -> MonetMorandiColors.OnSurfaceVariant.copy(alpha = 0.2f)
            }
            
            // 渐变线条效果
            val gradient = Brush.verticalGradient(
                colors = if (hasEvents) {
                    listOf(
                        lineColor.copy(alpha = 0.8f),
                        lineColor.copy(alpha = 0.4f)
                    )
                } else {
                    listOf(lineColor, lineColor)
                }
            )
            
            // 绘制连接线
            if (!isFirst) {
                drawLine(
                    brush = gradient,
                    start = Offset(size.width / 2, 0f),
                    end = Offset(size.width / 2, size.height / 2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
            
            if (!isLast) {
                drawLine(
                    brush = gradient,
                    start = Offset(size.width / 2, size.height / 2),
                    end = Offset(size.width / 2, size.height),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }
        }
        
        // 年份圆点和标签
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.offset(y = 32.dp)
        ) {
            // 增强的年份圆点
            Box(
                modifier = Modifier
                    .size(
                        when {
                            isCurrentYear -> 24.dp
                            hasEvents -> 18.dp
                            isDecadeStart -> 14.dp
                            else -> 10.dp
                        }
                    )
                    .clip(CircleShape)
                    .background(
                        when {
                            isCurrentYear -> MonetMorandiColors.Accent
                            hasEvents -> MonetMorandiColors.Primary
                            isDecadeStart -> MonetMorandiColors.Secondary
                            else -> MonetMorandiColors.OnSurfaceVariant.copy(alpha = 0.3f)
                        }
                    )
                    .border(
                        width = if (isCurrentYear) 4.dp else if (hasEvents) 2.dp else 0.dp,
                        color = Color.White,
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                when {
                    isCurrentYear -> {
                        // 当前年份的脉冲效果
                        val infiniteTransition = rememberInfiniteTransition()
                        val pulseScale by infiniteTransition.animateFloat(
                            initialValue = 0.8f,
                            targetValue = 1.2f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1000),
                                repeatMode = RepeatMode.Reverse
                            )
                        )
                        
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .graphicsLayer(scaleX = pulseScale, scaleY = pulseScale)
                                .clip(CircleShape)
                                .background(Color.White)
                        )
                    }
                    hasEvents -> {
                        Text(
                            text = milestones.size.toString(),
                            fontSize = 8.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 增强的年份标签
            Text(
                text = year.toString(),
                fontSize = when {
                    isCurrentYear -> 18.sp
                    hasEvents -> 15.sp
                    isDecadeStart -> 13.sp
                    else -> 12.sp
                },
                fontWeight = when {
                    isCurrentYear -> FontWeight.Bold
                    hasEvents || isDecadeStart -> FontWeight.SemiBold
                    else -> FontWeight.Medium
                },
                color = when {
                    isCurrentYear -> MonetMorandiColors.Accent
                    hasEvents -> MonetMorandiColors.OnSurface
                    isDecadeStart -> MonetMorandiColors.Secondary
                    else -> MonetMorandiColors.OnSurfaceVariant
                },
                textAlign = TextAlign.Center
            )
            
            // 事件统计信息
            if (hasEvents) {
                Text(
                    text = "${milestones.size}个事件",
                    fontSize = 9.sp,
                    color = MonetMorandiColors.OnSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                
                // 显示主要类别
                val mainCategory = milestones.groupBy { it.category }
                    .maxByOrNull { it.value.size }?.key
                mainCategory?.let { category ->
                    Text(
                        text = category.displayName,
                        fontSize = 8.sp,
                        color = when (category) {
                            MilestoneCategory.CAREER -> MonetMorandiColors.Career
                            MilestoneCategory.EDUCATION -> MonetMorandiColors.Education
                            MilestoneCategory.LOVE -> MonetMorandiColors.Love
                            MilestoneCategory.LIFE -> MonetMorandiColors.Life
                            MilestoneCategory.TRAVEL -> MonetMorandiColors.Travel
                            MilestoneCategory.HEALTH -> MonetMorandiColors.Health
                            else -> MonetMorandiColors.OnSurfaceVariant
                        },
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            }
        }
    }
}

/**
 * 时间轴里程碑卡片
 */
@Composable
fun TimelineMilestoneCard(
    milestone: Milestone,
    onClick: () -> Unit,
    onLongPress: () -> Unit,
    isLast: Boolean,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(dampingRatio = 0.8f)
    )
    
    val categoryColor = when (milestone.category) {
        MilestoneCategory.CAREER -> MonetMorandiColors.Career
        MilestoneCategory.EDUCATION -> MonetMorandiColors.Education
        MilestoneCategory.LOVE -> MonetMorandiColors.Love
        MilestoneCategory.LIFE -> MonetMorandiColors.Life
        MilestoneCategory.TRAVEL -> MonetMorandiColors.Travel
        MilestoneCategory.HEALTH -> MonetMorandiColors.Health
        else -> MonetMorandiColors.Life
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(scaleX = scale, scaleY = scale)
            .clickable { 
                isPressed = false
                onClick() 
            }
            .background(Color.Transparent)
            .padding(bottom = if (isLast) 0.dp else 12.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MonetMorandiColors.Surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 1.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 类别图标
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .background(categoryColor.copy(alpha = 0.15f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = milestone.category.icon,
                        contentDescription = milestone.category.displayName,
                        tint = categoryColor,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 内容区域
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 上方：标题和日期
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top
                    ) {
                        Text(
                            text = milestone.title,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = MonetMorandiColors.OnSurface,
                            modifier = Modifier.weight(1f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        Text(
                            text = milestone.date.format(DateTimeFormatter.ofPattern("MM/dd")),
                            fontSize = 10.sp,
                            color = MonetMorandiColors.OnSurfaceVariant,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 下方：描述和标签
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (milestone.description.isNotBlank()) {
                            Text(
                                text = milestone.description,
                                fontSize = 11.sp,
                                color = MonetMorandiColors.OnSurfaceVariant,
                                modifier = Modifier.weight(1f),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        
                        // 类别标签
                        Surface(
                            color = categoryColor.copy(alpha = 0.15f),
                            shape = RoundedCornerShape(6.dp)
                        ) {
                            Text(
                                text = milestone.category.displayName,
                                fontSize = 9.sp,
                                color = categoryColor,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
                
                // 右侧进度指示器
                if (milestone.completionPercentage > 0f) {
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .padding(start = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            progress = { milestone.completionPercentage / 100f },
                            modifier = Modifier.size(24.dp),
                            color = categoryColor,
                            trackColor = categoryColor.copy(alpha = 0.2f),
                            strokeWidth = 2.dp
                        )
                        
                        if (milestone.completionPercentage >= 100f) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "已完成",
                                tint = categoryColor,
                                modifier = Modifier.size(12.dp)
                            )
                        } else {
                            Text(
                                text = "${milestone.completionPercentage.roundToInt()}",
                                fontSize = 7.sp,
                                color = categoryColor,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
            
            // 图片显示区域
            if (milestone.imageUris.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(milestone.imageUris.take(3)) { imageUri ->
                        Card(
                            modifier = Modifier
                                .size(60.dp)
                                .clip(RoundedCornerShape(8.dp)),
                            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                        ) {
                            Image(
                                painter = rememberAsyncImagePainter(imageUri.toUri()),
                                contentDescription = "里程碑图片",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                        }
                    }
                    
                    // 如果有更多图片，显示数量提示
                    if (milestone.imageUris.size > 3) {
                        item {
                            Card(
                                modifier = Modifier
                                    .size(60.dp)
                                    .clip(RoundedCornerShape(8.dp)),
                                colors = CardDefaults.cardColors(
                                    containerColor = MonetMorandiColors.Primary.copy(alpha = 0.1f)
                                )
                            ) {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "+${milestone.imageUris.size - 3}",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = MonetMorandiColors.Primary
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 空年份占位组件
 */
@Composable
fun EmptyYearPlaceholder(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MonetMorandiColors.SurfaceVariant.copy(alpha = 0.5f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.CalendarToday,
                    contentDescription = "空年份",
                    tint = MonetMorandiColors.OnSurfaceVariant.copy(alpha = 0.6f),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "今年等待你的精彩",
                    fontSize = 12.sp,
                    color = MonetMorandiColors.OnSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

 