package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.viewmodel.BackupRestoreViewModel
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 数据恢复界面
 * 用于在应用迁移后恢复丢失的数据
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataRecoveryScreen(
    viewModel: BackupRestoreViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    var showRestartDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.getBackupFiles()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("数据恢复") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { padding ->
        Box(modifier = Modifier.padding(padding)) {
            if (uiState.isLoading) {
                CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
            } else if (uiState.backupFiles.isEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "未找到可用的数据备份",
                        style = MaterialTheme.typography.titleMedium,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "应用可能没有创建过备份，或备份文件已被删除",
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "找到 ${uiState.backupFiles.size} 个数据备份",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "选择一个备份进行恢复，恢复后应用将自动重启",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    LazyColumn {
                        items(uiState.backupFiles) { backupFile ->
                            RecoveryBackupItem(
                                backupFile = backupFile,
                                onRestore = {
                                    viewModel.restoreBackup(backupFile)
                                    showRestartDialog = true
                                }
                            )
                        }
                    }
                }
            }

            if (uiState.error != null) {
                AlertDialog(
                    onDismissRequest = {},
                    title = { Text("恢复失败") },
                    text = { Text(uiState.error ?: "未知错误") },
                    confirmButton = {
                        TextButton(onClick = {
                            viewModel.getBackupFiles()
                        }) {
                            Text("确定")
                        }
                    }
                )
            }

            if (showRestartDialog || uiState.restoreCompleted) {
                AlertDialog(
                    onDismissRequest = {},
                    title = { Text("恢复成功") },
                    text = { Text("数据已恢复，请重启应用以加载恢复的数据") },
                    confirmButton = {
                        TextButton(onClick = {
                            // 重启应用
                            val packageManager = context.packageManager
                            val intent = packageManager.getLaunchIntentForPackage(context.packageName)
                            intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                            context.startActivity(intent)
                            android.os.Process.killProcess(android.os.Process.myPid())
                        }) {
                            Text("重启应用")
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun RecoveryBackupItem(
    backupFile: File,
    onRestore: () -> Unit
) {
    val viewModel: BackupRestoreViewModel = hiltViewModel()
    val formattedDate = viewModel.formatDate(backupFile.lastModified())
    val fileSizeMB = String.format("%.2f", backupFile.length() / (1024.0 * 1024.0))
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "备份时间: $formattedDate",
                style = MaterialTheme.typography.titleSmall
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "文件大小: ${fileSizeMB}MB",
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "文件名: ${backupFile.name}",
                style = MaterialTheme.typography.bodySmall
            )
            Spacer(modifier = Modifier.height(8.dp))
            Button(
                onClick = onRestore,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("恢复此备份")
            }
        }
    }
}