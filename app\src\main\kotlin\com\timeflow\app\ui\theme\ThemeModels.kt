package com.timeflow.app.ui.theme

import androidx.compose.ui.graphics.Color

/**
 * 定义主题模式
 */
enum class ThemeMode {
    LIGHT, 
    DARK, 
    FOLLOW_SYSTEM
}

/**
 * 用户主题偏好设置数据类
 */
data class UserThemePreference(
    val isDarkMode: Boolean = false,
    val primaryColor: Color = Color(0xFFf5f4f6),
    val secondaryColor: Color = Color(0xFF03DAC6),
    val backgroundColor: Color = Color(0xFFF5F5F5),
    val surfaceColor: Color = Color(0xFFFFFFFF),
    val errorColor: Color = Color(0xFFB00020),
    val homePageColor: Color = Color(0xFFf5f4f6),
    val calendarPageColor: Color = Color(0xFF03DAC6),
    val statisticsPageColor: Color = Color(0xFF009688),
    val profilePageColor: Color = Color(0xFF795548), // 🔧 添加profile页面背景色字段
    val settingsPageColor: Color = Color(0xFF607D8B),
    val useSystemDarkMode: Boolean = true,
    val useUnifiedColors: Boolean = false
) 

/**
 * 预设主题数据类
 */
data class PresetTheme(
    val id: String,
    val name: String,
    val description: String = "",
    val previewColor: Color, // 用于预览的主色调
    val themePreference: UserThemePreference,
    val isBuiltIn: Boolean = false, // 是否为内置主题
    val createdAt: Long = System.currentTimeMillis(),
    val thumbnailColors: List<Color> = emptyList() // 用于显示的缩略图颜色
) {
    // 生成缩略图颜色
    fun generateThumbnailColors(): List<Color> {
        return listOf(
            themePreference.primaryColor,
            themePreference.secondaryColor,
            themePreference.homePageColor,
            themePreference.calendarPageColor,
            themePreference.statisticsPageColor
        )
    }
}

/**
 * 预设主题管理状态
 */
data class PresetThemeState(
    val presets: List<PresetTheme> = emptyList(),
    val currentPresetId: String? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 内置预设主题
 */
object BuiltInPresets {
    
    // 莫兰迪·雾霾蓝主题
    val defaultTheme = PresetTheme(
        id = "default",
        name = "雾霾蓝",
        description = "莫兰迪配色·宁静优雅的雾霾蓝调",
        previewColor = Color(0xFF9BB0C1),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFF9BB0C1),
            secondaryColor = Color(0xFFB5C4D1),
            backgroundColor = Color(0xFFF7F8FA),
            homePageColor = Color(0xFFF2F4F7),
            calendarPageColor = Color(0xFFF0F3F6),
            statisticsPageColor = Color(0xFFEDF1F4),
            profilePageColor = Color(0xFFF1F4F7),
            settingsPageColor = Color(0xFFF2F4F7)
        )
    )
    
    // 莫兰迪·灰粉主题
    val dustyRoseTheme = PresetTheme(
        id = "dusty_rose",
        name = "灰粉",
        description = "莫兰迪配色·温柔细腻的灰粉色调",
        previewColor = Color(0xFFB8A5A0),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFFB8A5A0),
            secondaryColor = Color(0xFFC4B1AC),
            backgroundColor = Color(0xFFFAF8F7),
            homePageColor = Color(0xFFF7F4F3),
            calendarPageColor = Color(0xFFF5F2F1),
            statisticsPageColor = Color(0xFFF8F5F4),
            profilePageColor = Color(0xFFF6F3F2),
            settingsPageColor = Color(0xFFF7F4F3)
        )
    )
    
    // 莫奈·睡莲池主题
    val monetWaterLily = PresetTheme(
        id = "monet_water_lily",
        name = "睡莲池",
        description = "莫奈印象派·如诗如画的睡莲池色调",
        previewColor = Color(0xFF7A9B8E),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFF7A9B8E),
            secondaryColor = Color(0xFF8FA69B),
            backgroundColor = Color(0xFFF4F7F5),
            homePageColor = Color(0xFFEFF3F0),
            calendarPageColor = Color(0xFFEBF0ED),
            statisticsPageColor = Color(0xFFF1F5F2),
            profilePageColor = Color(0xFFEDF2EF),
            settingsPageColor = Color(0xFFEFF3F0)
        )
    )
    
    // 莫兰迪·薰衣草主题
    val lavenderGrayTheme = PresetTheme(
        id = "lavender_gray",
        name = "薰衣草灰",
        description = "莫兰迪配色·梦幻浪漫的薰衣草灰调",
        previewColor = Color(0xFFA5A0B8),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFFA5A0B8),
            secondaryColor = Color(0xFFB1ACC4),
            backgroundColor = Color(0xFFF8F7FA),
            homePageColor = Color(0xFFF4F3F7),
            calendarPageColor = Color(0xFFF2F1F5),
            statisticsPageColor = Color(0xFFF5F4F8),
            profilePageColor = Color(0xFFF3F2F6),
            settingsPageColor = Color(0xFFF4F3F7)
        )
    )
    
    // 莫奈·日出印象主题
    val monetSunrise = PresetTheme(
        id = "monet_sunrise",
        name = "日出印象",
        description = "莫奈印象派·温暖柔和的日出色调",
        previewColor = Color(0xFFD4A574),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFFD4A574),
            secondaryColor = Color(0xFFDDB385),
            backgroundColor = Color(0xFFFBF8F4),
            homePageColor = Color(0xFFF9F5F0),
            calendarPageColor = Color(0xFFF7F3EE),
            statisticsPageColor = Color(0xFFFAF6F1),
            profilePageColor = Color(0xFFF8F4EF),
            settingsPageColor = Color(0xFFF9F5F0)
        )
    )
    
    // 莫兰迪·雾绿主题
    val sageGreenTheme = PresetTheme(
        id = "sage_green",
        name = "雾绿",
        description = "莫兰迪配色·清新淡雅的雾绿色调",
        previewColor = Color(0xFF8FA08F),
        isBuiltIn = true,
        themePreference = UserThemePreference(
            primaryColor = Color(0xFF8FA08F),
            secondaryColor = Color(0xFF9BAC9B),
            backgroundColor = Color(0xFFF6F8F6),
            homePageColor = Color(0xFFF2F5F2),
            calendarPageColor = Color(0xFFF0F3F0),
            statisticsPageColor = Color(0xFFF3F6F3),
            profilePageColor = Color(0xFFF1F4F1),
            settingsPageColor = Color(0xFFF2F5F2)
        )
    )
    
    // 获取所有内置主题
    fun getAllBuiltInThemes(): List<PresetTheme> {
        return listOf(
            defaultTheme,
            dustyRoseTheme,
            monetWaterLily,
            lavenderGrayTheme,
            monetSunrise,
            sageGreenTheme
        )
    }
} 