package com.timeflow.app.data.model

import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 历史经期补记数据模型
 * 用于批量添加和管理历史经期数据
 */
data class HistoryPeriodModel(
    val id: String = java.util.UUID.randomUUID().toString(),
    val startDate: LocalDate,
    val endDate: LocalDate,
    val periodLength: Int = calculatePeriodLength(startDate, endDate),
    val notes: String? = null,
    val source: HistoryDataSource = HistoryDataSource.MANUAL_INPUT,
    val confidence: DataConfidence = DataConfidence.HIGH,
    val createdAt: LocalDate = LocalDate.now(),
    val isValidated: Boolean = false,
    val conflictsWith: List<String> = emptyList() // 冲突的记录ID列表
) {
    companion object {
        private fun calculatePeriodLength(startDate: LocalDate, endDate: LocalDate): Int {
            return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
        }
    }
    
    /**
     * 验证数据的合理性
     */
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        
        // 基本日期验证
        if (endDate.isBefore(startDate)) {
            errors.add("结束日期不能早于开始日期")
        }
        
        // 经期长度验证
        when {
            periodLength < 1 -> errors.add("经期长度不能少于1天")
            periodLength > 10 -> warnings.add("经期长度超过10天，请确认是否正确")
            periodLength > 7 -> warnings.add("经期长度较长，建议咨询医生")
        }
        
        // 日期范围验证
        val today = LocalDate.now()
        if (startDate.isAfter(today)) {
            errors.add("不能记录未来的经期")
        }
        
        val twoYearsAgo = today.minusYears(2)
        if (startDate.isBefore(twoYearsAgo)) {
            warnings.add("记录时间较久远，可能影响预测准确性")
        }
        
        return ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }
    
    /**
     * 格式化显示
     */
    fun getDisplayText(): String {
        val formatter = DateTimeFormatter.ofPattern("MM月dd日")
        return "${startDate.format(formatter)} - ${endDate.format(formatter)} (${periodLength}天)"
    }
}

/**
 * 历史数据来源
 */
enum class HistoryDataSource(val displayName: String) {
    MANUAL_INPUT("手动输入"),
    IMPORTED_FROM_APP("从其他应用导入"),
    IMPORTED_FROM_FILE("从文件导入"),
    CALENDAR_SYNC("日历同步"),
    HEALTH_APP_SYNC("健康应用同步")
}

/**
 * 数据可信度
 */
enum class DataConfidence(val displayName: String, val score: Int) {
    LOW("低", 1),
    MEDIUM("中", 2),
    HIGH("高", 3),
    VERIFIED("已验证", 4)
}

/**
 * 验证结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String>
) {
    val hasWarnings: Boolean get() = warnings.isNotEmpty()
    val hasErrors: Boolean get() = errors.isNotEmpty()
}

/**
 * 批量导入结果
 */
data class BatchImportResult(
    val totalRecords: Int,
    val successfulImports: Int,
    val failedImports: Int,
    val conflictResolutions: Int,
    val warnings: List<String>,
    val errors: List<String>,
    val importedRecords: List<HistoryPeriodModel>
) {
    val successRate: Double get() = if (totalRecords > 0) successfulImports.toDouble() / totalRecords else 0.0
}

/**
 * 冲突解决策略
 */
enum class ConflictResolutionStrategy {
    SKIP_CONFLICTING,      // 跳过冲突记录
    REPLACE_EXISTING,      // 替换现有记录
    MERGE_DATA,           // 合并数据
    ASK_USER              // 询问用户
}

/**
 * 数据冲突信息
 */
data class DataConflict(
    val newRecord: HistoryPeriodModel,
    val existingRecord: com.timeflow.app.data.entity.CycleRecord,
    val conflictType: ConflictType,
    val description: String
)

/**
 * 冲突类型
 */
enum class ConflictType(val displayName: String) {
    DATE_OVERLAP("日期重叠"),
    EXACT_DUPLICATE("完全重复"),
    PARTIAL_OVERLAP("部分重叠"),
    ADJACENT_DATES("相邻日期")
}

/**
 * 统计分析结果
 */
data class HistoryAnalysisResult(
    val totalPeriods: Int,
    val averageCycleLength: Double,
    val averagePeriodLength: Double,
    val cycleRegularity: CycleRegularityLevel,
    val longestCycle: Int,
    val shortestCycle: Int,
    val longestPeriod: Int,
    val shortestPeriod: Int,
    val dataQualityScore: Double,
    val recommendations: List<String>
)

/**
 * 周期规律性级别
 */
enum class CycleRegularityLevel(val displayName: String, val description: String) {
    VERY_REGULAR("非常规律", "周期变化小于3天"),
    REGULAR("规律", "周期变化在3-7天"),
    SOMEWHAT_IRREGULAR("轻度不规律", "周期变化在7-14天"),
    IRREGULAR("不规律", "周期变化超过14天"),
    INSUFFICIENT_DATA("数据不足", "需要更多数据进行分析")
}
