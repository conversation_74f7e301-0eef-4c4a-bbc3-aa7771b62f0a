# 任务常驻通知功能测试指南 📋

## 🎯 **功能概述**

根据您的需求，我们实现了参考TickTick设计的任务常驻通知功能：

### 核心特性
- **📋 常驻通知栏显示**：在通知栏持续显示今日主任务和待办事项
- **🔒 无法滑动删除**：除非手动关闭，否则通知不会被清除
- **⚡ 实时更新**：任务状态变化时通知内容自动更新
- **🎯 智能筛选**：优先显示今日主任务、高优先级任务和过期任务
- **👆 交互操作**：支持完成任务、查看详情等操作
- **🎯 只显示主任务**：不显示子待办，保持通知简洁清晰

## 📋 **详细测试步骤**

### 测试场景1：启动任务常驻通知 ⭐ 核心测试
```bash
# 1. 启动应用并监控服务日志
adb logcat -c
adb logcat -s TaskPersistentNotificationService TaskPersistentNotificationManager

# 2. 操作步骤：
# - 打开应用设置页面
# - 进入"通知设置"
# - 找到"任务常驻通知"开关并启用
# - 观察通知栏是否出现任务通知

# 3. 预期结果：
# ✅ 通知栏出现"📋 今日主任务 (X)"或"📋 待办主任务 (X)"通知
# ✅ 通知无法通过滑动删除
# ✅ 点击通知可以打开应用任务页面
# ✅ 通知显示今日主任务的标题和开始时间信息
# ✅ 不显示子待办，只显示主任务
```

### 测试场景2：五个主任务显示验证 📋 新增测试
```bash
# 1. 监控任务数量显示
adb logcat -s TaskPersistentNotificationService | grep -E "(任务数据|更新)"

# 2. 操作步骤：
# - 确保常驻通知已启动
# - 创建6-8个今日主任务（确保超过5个）
# - 设置不同优先级：紧急、高、中、低
# - 展开通知查看显示效果

# 3. 预期结果：
# ✅ 通知展开后显示前5个主任务
# ✅ 如果超过5个，显示"... 还有 X 个主任务"
# ✅ 任务按优先级排序：紧急 > 高 > 中 > 低
# ✅ 每个任务显示优先级图标和开始时间信息
# ✅ 其他主任务部分显示最多3个
```

### 测试场景3：通知内容和交互 🎯 重要测试
```bash
# 1. 监控通知更新日志
adb logcat -s TaskPersistentNotificationService | grep -E "(通知|任务|更新)"

# 2. 操作步骤：
# - 确保常驻通知已启动
# - 在应用中创建6-8个今日主任务和子任务
# - 设置不同的优先级和截止时间
# - 观察通知内容变化（应该只显示主任务，最多5个）

# 3. 预期结果：
# ✅ 通知标题显示主任务数量
# ✅ 通知内容显示第一个主任务的标题和开始时间
# ✅ 展开通知显示最多5个主任务列表（不包含子任务，不重复标题）
# ✅ 高优先级主任务用红色/橙色圆点标识
# ✅ 过期主任务优先显示
```

### 测试场景4：任务完成操作 ✅ 交互测试
```bash
# 1. 监控任务操作日志
adb logcat -s TaskPersistentNotificationActionReceiver TaskRepository

# 2. 操作步骤：
# - 在通知中点击"完成"按钮
# - 观察任务状态变化
# - 检查通知内容是否更新

# 3. 预期结果：
# ✅ 点击"完成"按钮后任务状态变为已完成
# ✅ 通知内容立即更新，移除已完成的任务
# ✅ 如果所有任务完成，显示"🎉 所有任务已完成！"
```

### 测试场景5：服务持久性 🔒 稳定性测试
```bash
# 1. 监控服务生命周期
adb logcat -s TaskPersistentNotificationService | grep -E "(启动|停止|销毁)"

# 2. 操作步骤：
# - 启动常驻通知
# - 切换到其他应用
# - 清理后台应用
# - 重启应用
# - 检查通知是否仍然存在

# 3. 预期结果：
# ✅ 服务在后台持续运行
# ✅ 清理后台后服务自动重启
# ✅ 应用重启后根据设置自动启动服务
# ✅ 通知在各种情况下都保持显示
```

## 🔧 **调试功能使用**

在DEBUG模式下，设置页面底部会显示调试功能区域：

### 调试按钮说明
1. **启动任务常驻通知**：手动启动服务进行测试
2. **停止任务常驻通知**：手动停止服务
3. **刷新任务数据**：强制刷新通知中的任务数据
4. **切换服务状态**：在启动/停止之间切换
5. **检查服务状态**：查看当前服务运行状态
6. **直接启动服务**：绕过管理器直接启动前台服务

### 使用方法
```bash
# 1. 确保应用为DEBUG版本
# 2. 进入设置 -> 通知设置
# 3. 滚动到底部找到"🔧 调试功能"区域
# 4. 点击相应按钮进行测试
# 5. 观察logcat输出验证功能
```

## 📱 **用户设置选项**

### 设置位置
- **路径**：设置 -> 通知设置 -> 任务提醒 -> 任务常驻通知
- **描述**：在通知栏常驻显示今日任务，参考TickTick设计
- **默认状态**：启用

### 设置行为
- **启用时**：立即启动任务常驻通知服务
- **禁用时**：停止服务并清除通知
- **应用启动时**：根据用户设置决定是否自动启动

## 🎨 **通知设计特点**

### 视觉设计
- **图标**：使用应用主图标
- **颜色**：应用主题色
- **样式**：低重要性，避免打扰用户
- **展开内容**：显示任务列表，支持优先级标识

### 交互设计
- **主要操作**：点击打开应用任务页面
- **次要操作**：完成任务、查看全部
- **持久性**：setOngoing(true)，无法滑动删除
- **更新频率**：每30秒自动更新一次

## ✅ **验证标准**

### 功能验证
- [ ] 通知正确显示在通知栏
- [ ] 通知无法被滑动删除
- [ ] 任务数据实时更新
- [ ] 交互操作正常工作
- [ ] 服务在后台稳定运行

### 性能验证
- [ ] 服务启动时间 < 2秒
- [ ] 内存占用合理（< 50MB）
- [ ] 电池消耗低
- [ ] 不影响应用主要功能

### 用户体验验证
- [ ] 设置开关响应及时
- [ ] 通知内容清晰易懂
- [ ] 操作反馈明确
- [ ] 不会过度打扰用户

## 🐛 **常见问题排查**

### 通知不显示
1. **检查权限**：确保应用有通知权限
2. **检查设置**：确认任务常驻通知已启用
3. **检查服务**：查看服务是否正常启动
4. **检查数据**：确认有可显示的任务

### 通知内容不更新
1. **检查数据源**：确认任务数据有变化
2. **检查更新机制**：查看定时更新是否正常
3. **检查观察者**：确认数据观察者正常工作

### 服务被系统杀死
1. **检查前台服务**：确认使用前台服务
2. **检查重启机制**：START_STICKY标志
3. **检查电池优化**：提示用户关闭电池优化

---

## 📝 **测试记录模板**

```
测试日期：____
测试版本：____
测试设备：____

功能测试结果：
□ 启动通知 - 通过/失败
□ 内容显示 - 通过/失败  
□ 交互操作 - 通过/失败
□ 服务持久性 - 通过/失败

问题记录：
1. ________________
2. ________________

改进建议：
1. ________________
2. ________________
```
