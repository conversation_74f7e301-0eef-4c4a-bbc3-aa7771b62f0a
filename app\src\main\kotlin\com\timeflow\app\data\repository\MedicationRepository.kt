package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.MedicationRecordDao
import com.timeflow.app.data.entity.MedicationRecord
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

interface MedicationRepository {
    suspend fun addMedicationRecord(record: MedicationRecord)
    suspend fun deleteMedicationRecord(medicationId: String, date: LocalDate)
    fun getMedicationRecords(medicationId: String, startDate: LocalDate, endDate: LocalDate): Flow<List<MedicationRecord>>
}

@Singleton
class MedicationRepositoryImpl @Inject constructor(
    private val medicationRecordDao: MedicationRecordDao
) : MedicationRepository {

    override suspend fun addMedicationRecord(record: MedicationRecord) {
        medicationRecordDao.insertRecord(record)
    }

    override suspend fun deleteMedicationRecord(medicationId: String, date: LocalDate) {
        medicationRecordDao.deleteRecord(medicationId, date)
    }

    override fun getMedicationRecords(medicationId: String, startDate: LocalDate, endDate: LocalDate): Flow<List<MedicationRecord>> {
        return medicationRecordDao.getRecordsForMedication(medicationId, startDate, endDate)
    }
} 