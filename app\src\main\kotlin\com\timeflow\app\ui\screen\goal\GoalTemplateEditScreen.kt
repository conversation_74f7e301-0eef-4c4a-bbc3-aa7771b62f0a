package com.timeflow.app.ui.screen.goal

import android.app.Activity
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTaskTemplate
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.data.model.GoalType
import com.timeflow.app.data.model.TemplateCategory
import com.timeflow.app.ui.components.LoadingIndicator
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.getColorFromHex
import java.time.LocalDateTime
import java.util.UUID
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import com.timeflow.app.utils.ColorUtils
import com.timeflow.app.utils.ComposeStatusBarEffect
import kotlin.experimental.ExperimentalTypeInference

/**
 * 目标模板编辑屏幕
 * 用于创建新模板或编辑现有模板
 * @param navController 导航控制器
 * @param templateId 要编辑的模板ID，为null时表示创建新模板
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalTypeInference::class)
@Composable
fun GoalTemplateEditScreen(
    navController: NavController,
    templateId: String? = null,
    viewModel: GoalTemplateViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val currentTemplate by viewModel.currentTemplate.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    val isEditMode = templateId != null
    
    // 如果是编辑模式，加载模板数据
    LaunchedEffect(templateId) {
        if (templateId != null) {
            viewModel.loadTemplateDetail(templateId)
        }
    }
    
    // 状态
    var name by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var category by remember { mutableStateOf("") }
    var colorHex by remember { mutableStateOf("#9370DB") } // 默认为DustyLavender
    var defaultTitle by remember { mutableStateOf("") }
    var defaultDescription by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf(GoalPriority.MEDIUM) }
    var defaultTags by remember { mutableStateOf("") }
    var defaultDurationDays by remember { mutableStateOf("") }
    var goalType by remember { mutableStateOf(GoalType.BOOLEAN) }
    var defaultTargetValue by remember { mutableStateOf("") }
    var defaultUnit by remember { mutableStateOf("") }
    var isRecurring by remember { mutableStateOf(false) }
    
    // 子任务相关
    var subTaskTemplates by remember { mutableStateOf<List<GoalSubTaskTemplate>>(emptyList()) }
    var newSubTaskTitle by remember { mutableStateOf("") }
    var newSubTaskDescription by remember { mutableStateOf("") }
    var newSubTaskDurationDays by remember { mutableStateOf("") }
    
    // 对话框显示控制
    var showCategoryDialog by remember { mutableStateOf(false) }
    var showColorDialog by remember { mutableStateOf(false) }
    var showConfirmDeleteDialog by remember { mutableStateOf(false) }
    
    // 根据当前模板更新UI
    LaunchedEffect(currentTemplate) {
        currentTemplate?.let { template ->
            name = template.name
            description = template.description
            category = template.category
            colorHex = template.colorHex
            defaultTitle = template.defaultTitle
            defaultDescription = template.defaultDescription
            selectedPriority = template.defaultPriority
            defaultTags = template.defaultTags.joinToString(", ")
            defaultDurationDays = template.defaultDurationDays?.toString() ?: ""
            goalType = template.goalType
            defaultTargetValue = template.defaultTargetValue?.toString() ?: ""
            defaultUnit = template.defaultUnit ?: ""
            isRecurring = template.isRecurring
            subTaskTemplates = template.subTaskTemplates
        }
    }
    
    // 状态栏颜色控制
    val templateColor = getColorFromHex(colorHex) ?: ColorUtils.DustyLavender

    // 使用ComposeStatusBarEffect来管理状态栏颜色
    ComposeStatusBarEffect(templateColor.copy(alpha = 0.85f))

    // 保存模板逻辑
    val saveTemplate = {
        // 处理标签
        val tagsList = defaultTags.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        
        // 处理目标值
        val targetValue = defaultTargetValue.toDoubleOrNull()
        
        // 创建模板对象
        val template = GoalTemplate(
            id = currentTemplate?.id ?: "",
            name = name,
            description = description,
            category = category,
            colorHex = colorHex,
            usageCount = currentTemplate?.usageCount ?: 0,
            lastUsed = currentTemplate?.lastUsed,
            createdAt = currentTemplate?.createdAt ?: LocalDateTime.now(),
            defaultTitle = defaultTitle,
            defaultDescription = defaultDescription,
            defaultPriority = selectedPriority,
            defaultTags = tagsList,
            defaultDurationDays = defaultDurationDays.toIntOrNull(),
            goalType = goalType,
            defaultTargetValue = targetValue,
            defaultUnit = if (defaultUnit.isNotEmpty()) defaultUnit else null,
            isRecurring = isRecurring,
            recurringSettings = null, // 简化版不处理
            defaultReminderSettings = emptyList(), // 简化版不处理
            subTaskTemplates = subTaskTemplates
        )
        
        if (isEditMode) {
            viewModel.updateTemplate(template)
        } else {
            viewModel.createTemplate(template)
        }
    }
    
    // 添加子任务
    val addSubTask: () -> Unit = {
        val duration = newSubTaskDurationDays.toIntOrNull() ?: 0
        
        if (newSubTaskTitle.isNotEmpty()) {
            val newSubTask = GoalSubTaskTemplate(
                id = UUID.randomUUID().toString(),
                title = newSubTaskTitle,
                description = newSubTaskDescription,
                estimatedDurationDays = duration,
                orderIndex = subTaskTemplates.size
            )
            
            subTaskTemplates = subTaskTemplates + newSubTask
            
            // 清空输入
            newSubTaskTitle = ""
            newSubTaskDescription = ""
            newSubTaskDurationDays = ""
        } else {
            Toast.makeText(context, "子任务标题不能为空", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 处理UI状态
    LaunchedEffect(uiState) {
        when (uiState) {
            is TemplateUiState.Success -> {
                val message = (uiState as? TemplateUiState.Success)?.message
                if (!message.isNullOrEmpty()) {
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    if (!isEditMode) {
                        navController.popBackStack()
                    }
                }
            }
            is TemplateUiState.Error -> {
                val errorMessage = (uiState as TemplateUiState.Error).message
                Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT).show()
            }
            else -> { /* do nothing */ }
        }
    }
    
    // 主要UI结构
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text(if (isEditMode) "编辑模板" else "创建模板") },
                    navigationIcon = {
                        IconButton(
                            onClick = {
                                if (name.isNotBlank()) {
                                    showConfirmDeleteDialog = true
                                } else {
                                    navController.popBackStack()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    actions = {
                        if (isEditMode) {
                            IconButton(
                                onClick = { showConfirmDeleteDialog = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除"
                                )
                            }
                        }
                        
                        IconButton(
                            onClick = { saveTemplate() },
                            enabled = name.isNotBlank() && defaultTitle.isNotBlank()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Save,
                                contentDescription = "保存"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.White,
                        titleContentColor = MaterialTheme.colorScheme.primary,
                        navigationIconContentColor = MaterialTheme.colorScheme.primary,
                        actionIconContentColor = MaterialTheme.colorScheme.primary
                    )
                )
            },
            content = { paddingValues ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    when {
                        uiState is TemplateUiState.Loading && templateId != null && currentTemplate == null -> {
                            LoadingIndicator(isLoading = true, message = "加载模板...")
                        }
                        uiState is TemplateUiState.Error && templateId != null && currentTemplate == null -> {
                            ErrorMessage(message = (uiState as TemplateUiState.Error).message)
                        }
                        else -> {
                            TemplateEditContent(
                                name = name,
                                onNameChange = { name = it },
                                description = description,
                                onDescriptionChange = { description = it },
                                category = category,
                                onCategoryChange = { category = it },
                                onShowCategoryDialog = { showCategoryDialog = true },
                                colorHex = colorHex,
                                onColorChange = { colorHex = it },
                                onShowColorDialog = { showColorDialog = true },
                                defaultTitle = defaultTitle,
                                onDefaultTitleChange = { defaultTitle = it },
                                defaultDescription = defaultDescription,
                                onDefaultDescriptionChange = { defaultDescription = it },
                                selectedPriority = selectedPriority,
                                onPriorityChange = { selectedPriority = it },
                                defaultTags = defaultTags,
                                onDefaultTagsChange = { defaultTags = it },
                                defaultDurationDays = defaultDurationDays,
                                onDefaultDurationDaysChange = { defaultDurationDays = it },
                                goalType = goalType,
                                onGoalTypeChange = { goalType = it },
                                defaultTargetValue = defaultTargetValue,
                                onDefaultTargetValueChange = { defaultTargetValue = it },
                                defaultUnit = defaultUnit,
                                onDefaultUnitChange = { defaultUnit = it },
                                isRecurring = isRecurring,
                                onIsRecurringChange = { isRecurring = it },
                                subTaskTemplates = subTaskTemplates,
                                onAddSubTask = { title, description, durationDays ->
                                    if (title.isNotBlank()) {
                                        val newSubTask = GoalSubTaskTemplate(
                                            title = title,
                                            description = description,
                                            estimatedDurationDays = durationDays.toIntOrNull() ?: 0,
                                            orderIndex = subTaskTemplates.size
                                        )
                                        subTaskTemplates = subTaskTemplates + newSubTask
                                        newSubTaskTitle = ""
                                        newSubTaskDescription = ""
                                        newSubTaskDurationDays = ""
                                    }
                                },
                                onRemoveSubTask = { subTask ->
                                    subTaskTemplates = subTaskTemplates.filter { it != subTask }
                                },
                                onMoveSubTaskUp = { index ->
                                    if (index > 0) {
                                        val mutableList = subTaskTemplates.toMutableList()
                                        val item = mutableList.removeAt(index)
                                        mutableList.add(index - 1, item)
                                        
                                        // 更新顺序索引
                                        subTaskTemplates = mutableList.mapIndexed { i, subTask ->
                                            subTask.copy(orderIndex = i)
                                        }
                                    }
                                },
                                onMoveSubTaskDown = { index ->
                                    if (index < subTaskTemplates.size - 1) {
                                        val mutableList = subTaskTemplates.toMutableList()
                                        val item = mutableList.removeAt(index)
                                        mutableList.add(index + 1, item)
                                        
                                        // 更新顺序索引
                                        subTaskTemplates = mutableList.mapIndexed { i, subTask ->
                                            subTask.copy(orderIndex = i)
                                        }
                                    }
                                },
                                newSubTaskTitle = newSubTaskTitle,
                                onNewSubTaskTitleChange = { newSubTaskTitle = it },
                                newSubTaskDescription = newSubTaskDescription,
                                onNewSubTaskDescriptionChange = { newSubTaskDescription = it },
                                newSubTaskDurationDays = newSubTaskDurationDays,
                                onNewSubTaskDurationDaysChange = { newSubTaskDurationDays = it }
                            )
                        }
                    }
                }
            }
        )
    }
    
    // 颜色选择对话框
    if (showColorDialog) {
        ColorPickerDialog(
            currentColor = colorHex,
            onColorSelected = { 
                colorHex = it
                showColorDialog = false
            },
            onDismiss = { showColorDialog = false }
        )
    }
    
    // 分类选择对话框
    if (showCategoryDialog) {
        CategorySelectionDialog(
            currentCategory = category,
            onCategorySelected = { 
                category = it
                showCategoryDialog = false
            },
            onDismiss = { showCategoryDialog = false },
            predefinedCategories = viewModel.getPredefinedCategories()
        )
    }
    
    // 确认放弃对话框
    if (showConfirmDeleteDialog && isEditMode) {
        AlertDialog(
            onDismissRequest = { showConfirmDeleteDialog = false },
            title = { Text("删除模板") },
            text = { Text("确定要删除模板「${name}」吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        templateId?.let { id ->
                            viewModel.deleteTemplate(id)
                            navController.popBackStack()
                        }
                        showConfirmDeleteDialog = false
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showConfirmDeleteDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 模板编辑内容
 */
@Composable
private fun TemplateEditContent(
    name: String,
    onNameChange: (String) -> Unit,
    description: String,
    onDescriptionChange: (String) -> Unit,
    category: String,
    onCategoryChange: (String) -> Unit,
    onShowCategoryDialog: () -> Unit,
    colorHex: String,
    onColorChange: (String) -> Unit,
    onShowColorDialog: () -> Unit,
    defaultTitle: String,
    onDefaultTitleChange: (String) -> Unit,
    defaultDescription: String,
    onDefaultDescriptionChange: (String) -> Unit,
    selectedPriority: GoalPriority,
    onPriorityChange: (GoalPriority) -> Unit,
    defaultTags: String,
    onDefaultTagsChange: (String) -> Unit,
    defaultDurationDays: String,
    onDefaultDurationDaysChange: (String) -> Unit,
    goalType: GoalType,
    onGoalTypeChange: (GoalType) -> Unit,
    defaultTargetValue: String,
    onDefaultTargetValueChange: (String) -> Unit,
    defaultUnit: String,
    onDefaultUnitChange: (String) -> Unit,
    isRecurring: Boolean,
    onIsRecurringChange: (Boolean) -> Unit,
    subTaskTemplates: List<GoalSubTaskTemplate>,
    onAddSubTask: (String, String, String) -> Unit,
    onRemoveSubTask: (GoalSubTaskTemplate) -> Unit,
    onMoveSubTaskUp: (Int) -> Unit,
    onMoveSubTaskDown: (Int) -> Unit,
    newSubTaskTitle: String,
    onNewSubTaskTitleChange: (String) -> Unit,
    newSubTaskDescription: String,
    onNewSubTaskDescriptionChange: (String) -> Unit,
    newSubTaskDurationDays: String,
    onNewSubTaskDurationDaysChange: (String) -> Unit
) {
    val templateColor = remember(colorHex) {
        getColorFromHex(colorHex) ?: DustyLavender
    }
    
    // 卡片背景色
    val cardBackgroundColor = Color(0xFFF8F8F9)
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 模板基本信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "模板基本信息",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 模板名称
                OutlinedTextField(
                    value = name,
                    onValueChange = onNameChange,
                    label = { Text("模板名称") },
                    placeholder = { Text("例如：每日阅读目标") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 模板描述
                OutlinedTextField(
                    value = description,
                    onValueChange = onDescriptionChange,
                    label = { Text("模板描述") },
                    placeholder = { Text("例如：用于创建日常阅读目标的模板") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 类别和颜色选择部分
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 分类选择
                    OutlinedTextField(
                        value = category,
                        onValueChange = onCategoryChange,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        label = { Text("分类") },
                        readOnly = true,
                        singleLine = true,
                        trailingIcon = {
                            IconButton(onClick = onShowCategoryDialog) {
                                Icon(
                                    imageVector = Icons.Default.ArrowDropDown,
                                    contentDescription = "选择分类"
                                )
                            }
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color.Transparent,
                            unfocusedBorderColor = Color.Transparent,
                            unfocusedLabelColor = Color(0xFF83728D),
                            unfocusedTextColor = Color(0xFF83728D),
                            focusedContainerColor = Color(0xFFF4F2F4),
                            unfocusedContainerColor = Color(0xFFF4F2F4)
                        )
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 颜色选择按钮
                    Button(
                        onClick = onShowColorDialog,
                        modifier = Modifier
                            .height(48.dp)
                            .width(90.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF9370DB)
                        ),
                        shape = RoundedCornerShape(4.dp)
                    ) {
                        Text(
                            text = "颜色",
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
        
        // 目标默认信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "目标默认信息",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 默认标题
                OutlinedTextField(
                    value = defaultTitle,
                    onValueChange = onDefaultTitleChange,
                    label = { Text("默认标题") },
                    placeholder = { Text("创建目标时的默认标题") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 默认描述
                OutlinedTextField(
                    value = defaultDescription,
                    onValueChange = onDefaultDescriptionChange,
                    label = { Text("默认描述") },
                    placeholder = { Text("创建目标时的默认描述") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 默认优先级
                Text(
                    text = "默认优先级",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    PriorityOption(
                        priority = GoalPriority.LOW,
                        selectedPriority = selectedPriority,
                        onSelect = { onPriorityChange(GoalPriority.LOW) },
                        color = templateColor
                    )
                    PriorityOption(
                        priority = GoalPriority.MEDIUM,
                        selectedPriority = selectedPriority,
                        onSelect = { onPriorityChange(GoalPriority.MEDIUM) },
                        color = templateColor
                    )
                    PriorityOption(
                        priority = GoalPriority.HIGH,
                        selectedPriority = selectedPriority,
                        onSelect = { onPriorityChange(GoalPriority.HIGH) },
                        color = templateColor
                    )
                    PriorityOption(
                        priority = GoalPriority.URGENT,
                        selectedPriority = selectedPriority,
                        onSelect = { onPriorityChange(GoalPriority.URGENT) },
                        color = templateColor
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 默认标签
                OutlinedTextField(
                    value = defaultTags,
                    onValueChange = onDefaultTagsChange,
                    label = { Text("默认标签") },
                    placeholder = { Text("用逗号分隔，例如：阅读,习惯,每日") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 默认持续时间
                OutlinedTextField(
                    value = defaultDurationDays,
                    onValueChange = onDefaultDurationDaysChange,
                    label = { Text("默认持续天数") },
                    placeholder = { Text("例如：30") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
            }
        }
        
        // 目标类型卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "目标类型设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 目标类型选择
                Text(
                    text = "目标类型",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    GoalTypeOption(
                        type = GoalType.BOOLEAN,
                        selectedType = goalType,
                        onSelect = { onGoalTypeChange(GoalType.BOOLEAN) },
                        modifier = Modifier.weight(1f),
                        color = templateColor
                    )
                    GoalTypeOption(
                        type = GoalType.NUMERIC,
                        selectedType = goalType,
                        onSelect = { onGoalTypeChange(GoalType.NUMERIC) },
                        modifier = Modifier.weight(1f),
                        color = templateColor
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 数值型目标设置
                if (goalType == GoalType.NUMERIC) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = defaultTargetValue,
                            onValueChange = onDefaultTargetValueChange,
                            label = { Text("目标值") },
                            placeholder = { Text("例如：10000") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            modifier = Modifier.weight(1f),
                            singleLine = true,
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color.Transparent,
                                unfocusedBorderColor = Color.Transparent,
                                focusedLabelColor = templateColor,
                                unfocusedLabelColor = Color(0xFF83728D),
                                focusedContainerColor = Color(0xFFF4F2F4),
                                unfocusedContainerColor = Color(0xFFF4F2F4)
                            )
                        )
                        
                        OutlinedTextField(
                            value = defaultUnit,
                            onValueChange = onDefaultUnitChange,
                            label = { Text("单位") },
                            placeholder = { Text("例如：步/米") },
                            modifier = Modifier.weight(1f),
                            singleLine = true,
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color.Transparent,
                                unfocusedBorderColor = Color.Transparent,
                                focusedLabelColor = templateColor,
                                unfocusedLabelColor = Color(0xFF83728D),
                                focusedContainerColor = Color(0xFFF4F2F4),
                                unfocusedContainerColor = Color(0xFFF4F2F4)
                            )
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 周期性设置
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "是否为周期性目标模板",
                        fontSize = 16.sp,
                        color = Color(0xFF333333),
                        modifier = Modifier.weight(1f)
                    )
                    
                    Switch(
                        checked = isRecurring,
                        onCheckedChange = onIsRecurringChange,
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = templateColor,
                            checkedTrackColor = templateColor.copy(alpha = 0.5f)
                        )
                    )
                }
                
                // 周期性提示
                if (isRecurring) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "使用此模板创建的目标将被标记为周期性目标",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
        
        // 子任务模板卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = cardBackgroundColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "子任务模板",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 子任务列表
                if (subTaskTemplates.isNotEmpty()) {
                    Text(
                        text = "已添加的子任务",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    subTaskTemplates.forEachIndexed { index, subTask ->
                        SubTaskTemplateItem(
                            subTask = subTask,
                            index = index,
                            onRemove = { onRemoveSubTask(subTask) },
                            onMoveUp = { onMoveSubTaskUp(index) },
                            onMoveDown = { onMoveSubTaskDown(index) },
                            isFirst = index == 0,
                            isLast = index == subTaskTemplates.size - 1,
                            color = templateColor
                        )
                        
                        if (index < subTaskTemplates.size - 1) {
                            Divider(modifier = Modifier.padding(vertical = 8.dp))
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // 添加新子任务
                Text(
                    text = "添加子任务模板",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = newSubTaskTitle,
                    onValueChange = onNewSubTaskTitleChange,
                    label = { Text("子任务标题") },
                    placeholder = { Text("输入子任务标题") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = newSubTaskDescription,
                    onValueChange = onNewSubTaskDescriptionChange,
                    label = { Text("子任务描述") },
                    placeholder = { Text("输入子任务描述") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = newSubTaskDurationDays,
                    onValueChange = onNewSubTaskDurationDaysChange,
                    label = { Text("预计天数") },
                    placeholder = { Text("完成此子任务预计需要的天数") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedLabelColor = templateColor,
                        unfocusedLabelColor = Color(0xFF83728D),
                        focusedContainerColor = Color(0xFFF4F2F4),
                        unfocusedContainerColor = Color(0xFFF4F2F4)
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { 
                        onAddSubTask(
                            newSubTaskTitle,
                            newSubTaskDescription,
                            newSubTaskDurationDays
                        )
                    },
                    enabled = newSubTaskTitle.isNotBlank(),
                    modifier = Modifier.align(Alignment.End),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = templateColor
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("添加子任务")
                }
            }
        }
    }
}

/**
 * 优先级选项组件
 */
@Composable
private fun PriorityOption(
    priority: GoalPriority,
    selectedPriority: GoalPriority,
    onSelect: () -> Unit,
    color: Color
) {
    val isSelected = priority == selectedPriority
    
    val priorityColor = when (priority) {
        GoalPriority.LOW -> Color(0xFF10B981)
        GoalPriority.MEDIUM -> Color(0xFFF59E0B)
        GoalPriority.HIGH -> Color(0xFFEF4444)
        GoalPriority.URGENT -> Color(0xFFDC2626)
    }
    
    val priorityText = when (priority) {
        GoalPriority.LOW -> "低"
        GoalPriority.MEDIUM -> "中"
        GoalPriority.HIGH -> "高"
        GoalPriority.URGENT -> "紧急"
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onSelect)
            .background(
                if (isSelected) priorityColor.copy(alpha = 0.1f) else Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (isSelected) priorityColor else Color(0xFFE2E8F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = priorityText,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
            color = if (isSelected) priorityColor else Color(0xFF666666)
        )
    }
}

/**
 * 目标类型选项
 */
@Composable
private fun GoalTypeOption(
    type: GoalType,
    selectedType: GoalType,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier,
    color: Color
) {
    val isSelected = type == selectedType
    
    val typeText = when (type) {
        GoalType.NUMERIC -> "数值型"
        GoalType.BOOLEAN -> "布尔型"
    }
    
    val typeIcon = when (type) {
        GoalType.NUMERIC -> Icons.Outlined.Numbers
        GoalType.BOOLEAN -> Icons.Outlined.Check
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onSelect)
            .background(
                if (isSelected) color.copy(alpha = 0.1f) else Color(0xFFF8FAFC)
            )
            .border(
                width = 1.dp,
                color = if (isSelected) color else Color(0xFFE2E8F0),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = typeIcon,
                contentDescription = null,
                tint = if (isSelected) color else Color(0xFF666666),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = typeText,
                fontSize = 14.sp,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                color = if (isSelected) color else Color(0xFF666666)
            )
        }
    }
}

/**
 * 子任务模板项
 */
@Composable
private fun SubTaskTemplateItem(
    subTask: GoalSubTaskTemplate,
    index: Int,
    onRemove: () -> Unit,
    onMoveUp: () -> Unit,
    onMoveDown: () -> Unit,
    isFirst: Boolean,
    isLast: Boolean,
    color: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 序号
            Box(
                modifier = Modifier
                    .size(28.dp)
                    .clip(CircleShape)
                    .background(color.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "${index + 1}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 子任务信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = subTask.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                
                if (subTask.description.isNotBlank()) {
                    Text(
                        text = subTask.description,
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
                
                if (subTask.estimatedDurationDays > 0) {
                    Text(
                        text = "预计${subTask.estimatedDurationDays}天",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }
            
            // 按钮
            Row {
                if (!isFirst) {
                    IconButton(
                        onClick = onMoveUp,
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowUpward,
                            contentDescription = "上移",
                            tint = Color.Gray,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                if (!isLast) {
                    IconButton(
                        onClick = onMoveDown,
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowDownward,
                            contentDescription = "下移",
                            tint = Color.Gray,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(36.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

/**
 * 颜色选择对话框
 */
@Composable
private fun ColorPickerDialog(
    currentColor: String,
    onColorSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    val predefinedColors = listOf(
        "#9370DB", // DustyLavender
        "#6200EE", // 紫色
        "#3700B3", // 深紫色
        "#03DAC6", // 绿松石色
        "#018786", // 青色
        "#FF5722", // 深橙色
        "#E91E63", // 粉红色
        "#F44336", // 红色
        "#FF9800", // 橙色
        "#FFC107", // 琥珀色
        "#FFEB3B", // 黄色
        "#4CAF50", // 绿色
        "#8BC34A", // 浅绿色
        "#2196F3", // 蓝色
        "#03A9F4", // 浅蓝色
        "#795548", // 棕色
        "#607D8B"  // 蓝灰色
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择模板颜色") },
        text = {
            Column {
                // 颜色网格
                val rows = predefinedColors.chunked(5)
                rows.forEach { rowColors ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        rowColors.forEach { colorHex ->
                            val color = getColorFromHex(colorHex) ?: Color.Gray
                            val isSelected = colorHex == currentColor
                            
                            Box(
                                modifier = Modifier
                                    .size(48.dp)
                                    .clip(CircleShape)
                                    .background(color)
                                    .border(
                                        width = if (isSelected) 3.dp else 0.dp,
                                        color = Color.Black,
                                        shape = CircleShape
                                    )
                                    .clickable { onColorSelected(colorHex) },
                                contentAlignment = Alignment.Center
                            ) {
                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = null,
                                        tint = Color.White,
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("完成")
            }
        }
    )
}

/**
 * 分类选择对话框
 */
@Composable
private fun CategorySelectionDialog(
    currentCategory: String,
    onCategorySelected: (String) -> Unit,
    onDismiss: () -> Unit,
    predefinedCategories: List<String>
) {
    var customCategory by remember { mutableStateOf("") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择分类") },
        text = {
            Column {
                // 预定义分类列表
                LazyColumn(
                    modifier = Modifier
                        .heightIn(max = 250.dp)
                        .padding(bottom = 16.dp)
                ) {
                    items(predefinedCategories) { category ->
                        val isSelected = category == currentCategory
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onCategorySelected(category) }
                                .padding(vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = isSelected,
                                onClick = { onCategorySelected(category) }
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = category,
                                fontSize = 16.sp
                            )
                        }
                    }
                }
                
                Divider(modifier = Modifier.padding(vertical = 8.dp))
                
                // 自定义分类
                Text(
                    text = "或创建新分类",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = customCategory,
                    onValueChange = { customCategory = it },
                    label = { Text("分类名称") },
                    placeholder = { Text("输入自定义分类") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = {
                        if (customCategory.isNotBlank()) {
                            onCategorySelected(customCategory)
                        } else {
                            onDismiss()
                        }
                    },
                    modifier = Modifier.align(Alignment.End),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF9370DB)
                    ),
                    enabled = customCategory.isNotBlank()
                ) {
                    Text("添加自定义分类")
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        containerColor = Color(0xFFF4F2F4)
    )
}

/**
 * 错误消息显示
 */
@Composable
private fun ErrorMessage(message: String) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Outlined.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "出错了",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.DarkGray
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = message,
            fontSize = 16.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }
} 