package com.timeflow.app.data.model

import java.time.DayOfWeek
import java.time.LocalDate

/**
 * 周期性设置数据类，用于定义目标的重复规则
 */
data class RecurringSettings(
    // 基本设置
    val frequency: RecurringPeriod = RecurringPeriod.WEEKLY,  // 频率：每日/每周/每月/每年
    val interval: Int = 1,  // 间隔：每几(天/周/月/年)
    
    // 每周设置
    val weeklyDays: Set<DayOfWeek> = emptySet(),  // 每周重复的星期几
    
    // 每月设置
    val monthlyType: MonthlyType = MonthlyType.DAY_OF_MONTH,  // 每月类型：固定日期或相对日期
    val dayOfMonth: Int? = null,  // 每月第几天（如每月15号）
    val weekOfMonth: WeekOfMonth? = null,  // 每月第几周（第一周、第二周...）
    val dayOfWeek: DayOfWeek? = null,  // 每月的星期几（如每月的周五）
    
    // 结束条件
    val endCondition: EndCondition = EndCondition.Never,
    val endDate: LocalDate? = null,  // 截止日期
    val endCount: Int? = null,  // 重复次数
    
    // 例外日期
    val exceptions: List<LocalDate> = emptyList(),  // 例外日期列表
    
    // 高级设置
    val skipHolidays: Boolean = false,  // 是否跳过节假日
    val skipWeekends: Boolean = false,  // 是否跳过周末
    
    // 自定义RRule（iCalendar规则）
    val customRRule: String? = null  // 自定义RRule字符串
)

/**
 * 每月重复类型
 */
enum class MonthlyType {
    DAY_OF_MONTH,  // 每月固定日期（如每月15号）
    DAY_OF_WEEK    // 每月相对日期（如每月第三个周五）
}

/**
 * 每月第几周
 */
enum class WeekOfMonth {
    FIRST,      // 第一周
    SECOND,     // 第二周
    THIRD,      // 第三周
    FOURTH,     // 第四周
    LAST        // 最后一周
}

/**
 * 结束条件
 */
sealed class EndCondition {
    object Never : EndCondition()               // 永不结束
    data class ByDate(val date: LocalDate) : EndCondition()  // 指定日期结束
    data class ByCount(val count: Int) : EndCondition()      // 指定次数结束
} 