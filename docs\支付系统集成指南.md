# TimeFlow 支付系统集成指南

## 概述

TimeFlow应用已集成完整的支付系统，支持支付宝、微信支付和模拟支付。当前实现包含完整的支付流程，可以在测试模式下运行，也可以轻松集成真实的支付SDK。

## 功能特性

### ✅ 已实现功能

1. **完整的支付流程**
   - 订单创建和管理
   - 支付方式选择（支付宝、微信、模拟支付）
   - 支付状态跟踪
   - 支付结果处理

2. **优美的用户界面**
   - 支付方式选择对话框
   - 支付处理中动画
   - 支付成功/失败界面
   - 会员权益展示

3. **智能状态管理**
   - 支付状态实时更新
   - 自动会员权益激活
   - 支付重试机制
   - 错误处理和用户提示

4. **测试支持**
   - 模拟支付（Debug模式可见）
   - 可配置成功/失败概率
   - 详细的支付日志

## 使用方法

### 用户操作流程

1. **购买会员**
   - 进入"账户"页面
   - 点击"购买月度会员"或"购买终身会员"按钮
   - 选择支付方式（支付宝/微信/模拟支付）
   - 完成支付流程

2. **支付方式选择**
   - **支付宝**：推荐使用，支付快速安全
   - **微信支付**：便捷支付，一键完成
   - **模拟支付**：仅在Debug模式显示，测试使用

3. **支付状态跟踪**
   - 支付处理中：显示加载动画和进度提示
   - 支付成功：显示成功界面和订单详情
   - 支付失败：显示错误信息和重试选项

## 技术架构

### 核心组件

1. **PaymentManager** - 支付管理器
   - 统一的支付接口
   - 支持多种支付方式
   - 自动订单生成
   - 支付状态管理

2. **PaymentDialog** - 支付界面组件
   - 支付方式选择
   - 支付流程展示
   - 状态反馈界面

3. **SettingsViewModel** - 状态管理
   - 支付状态跟踪
   - 会员权益激活
   - 用户界面更新

### 数据模型

```kotlin
// 支付方式
enum class PaymentMethod {
    ALIPAY,     // 支付宝
    WECHAT,     // 微信支付
    MOCK        // 模拟支付
}

// 支付状态
enum class PaymentStatus {
    PENDING,    // 待支付
    PROCESSING, // 支付中
    SUCCESS,    // 支付成功
    FAILED,     // 支付失败
    CANCELLED,  // 支付取消
    REFUNDED    // 已退款
}

// 会员套餐
enum class MembershipPlan {
    MONTHLY,    // 月度会员 ¥9.90/月
    LIFETIME    // 终身会员 ¥99.90
}
```

## 集成真实支付SDK

### 支付宝支付集成

1. **申请支付宝开发者账号**
   - 访问 [支付宝开放平台](https://open.alipay.com/)
   - 创建应用并获得AppId
   - 配置RSA密钥对

2. **下载并集成SDK**
   ```kotlin
   // 在app/build.gradle.kts中添加
   implementation(files("libs/alipaySdk-15.8.11-20220428205124.aar"))
   ```

3. **配置支付宝支付**
   ```kotlin
   // 在PaymentManager.kt中更新配置
   private val paymentConfig = PaymentConfig(
       isTestMode = false, // 切换到生产模式
       alipayAppId = "您的支付宝AppId",
       alipayPrivateKey = "您的RSA私钥",
       alipayPublicKey = "支付宝RSA公钥"
   )
   ```

4. **实现真实支付流程**
   ```kotlin
   // 在processRealAlipay方法中实现
   val payInfo = OrderInfoUtil2_0.buildOrderParamMap(
       appId = paymentConfig.alipayAppId,
       method = "alipay.trade.app.pay",
       outTradeNo = request.order.orderId,
       totalAmount = request.order.amount,
       subject = request.order.title,
       notifyUrl = paymentConfig.notifyUrl
   )
   
   val alipay = PayTask(activity)
   val result = alipay.payV2(payInfo, true)
   ```

### 微信支付集成

1. **申请微信支付商户号**
   - 访问 [微信支付商户平台](https://pay.weixin.qq.com/)
   - 获得商户号、AppId、API密钥

2. **下载并集成SDK**
   ```kotlin
   // 在app/build.gradle.kts中添加
   implementation("com.tencent.mm.opensdk:wechat-sdk-android:6.8.0")
   ```

3. **配置微信支付**
   ```kotlin
   // 在PaymentManager.kt中更新配置
   private val paymentConfig = PaymentConfig(
       isTestMode = false,
       wechatAppId = "您的微信AppId",
       wechatMchId = "您的商户号",
       wechatKey = "您的API密钥"
   )
   ```

4. **实现微信支付流程**
   ```kotlin
   // 在processRealWechatPay方法中实现
   val api = WXAPIFactory.createWXAPI(context, paymentConfig.wechatAppId)
   val request = PayReq().apply {
       appId = paymentConfig.wechatAppId
       partnerId = paymentConfig.wechatMchId
       prepayId = "预支付交易会话ID"
       nonceStr = "随机字符串"
       timeStamp = System.currentTimeMillis().toString()
       packageValue = "Sign=WXPay"
       sign = "签名"
   }
   api.sendReq(request)
   ```

## 安全考虑

### 1. 签名验证
- 所有支付请求必须包含签名
- 使用RSA2048或更高级别的加密算法
- 定期更换密钥

### 2. 订单验证
- 服务端验证订单金额
- 检查订单状态防止重复支付
- 记录所有支付日志

### 3. 网络安全
- 使用HTTPS加密传输
- 实现接口防重放攻击
- 设置适当的超时时间

## 测试指南

### 模拟支付测试

1. **启用Debug模式**
   - 确保应用在Debug模式下编译
   - 支付对话框中会显示"模拟支付"选项

2. **测试场景**
   - 选择"模拟支付"立即成功
   - 支付宝模拟：90%成功率，10%失败率
   - 微信模拟：85%成功率，15%取消率

3. **验证功能**
   - 检查会员状态是否正确更新
   - 验证支付成功后的UI变化
   - 测试支付失败的重试功能

### 生产环境测试

1. **沙盒测试**
   - 使用支付宝/微信提供的沙盒环境
   - 测试完整的支付流程
   - 验证回调处理

2. **真实测试**
   - 使用小额真实支付测试
   - 验证资金到账情况
   - 检查订单状态同步

## 常见问题

### Q: 支付宝SDK集成失败？
A: 确保下载了正确版本的SDK，并且在AndroidManifest.xml中添加了必要的权限和配置。

### Q: 微信支付回调没有收到？
A: 检查微信支付的回调URL配置，确保服务器可以接收到微信的支付通知。

### Q: 模拟支付无法看到？
A: 模拟支付只在Debug版本中显示，Release版本会自动隐藏。

### Q: 支付成功但会员状态没有更新？
A: 检查SettingsViewModel中的activateMembership方法，确保DataStore正确保存了会员信息。

### Q: 支付金额与显示不符？
A: 确保PaymentManager中的金额与UI显示的金额一致，并且服务端验证使用的也是相同金额。

## 后续开发计划

1. **服务端集成**
   - 实现支付回调验证
   - 订单状态同步
   - 会员权益管理

2. **功能扩展**
   - 支持更多支付方式
   - 订阅管理功能
   - 退款处理

3. **数据分析**
   - 支付成功率统计
   - 用户支付偏好分析
   - 收入报表生成

## 联系支持

如果在集成过程中遇到问题，请：

1. 查看支付平台官方文档
2. 检查应用日志中的错误信息
3. 确认支付配置是否正确
4. 验证网络连接和权限设置

---

**注意**: 在正式上线前，请务必在沙盒环境中充分测试所有支付功能，确保支付流程的稳定性和安全性。