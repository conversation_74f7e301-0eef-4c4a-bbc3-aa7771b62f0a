package com.timeflow.app.ui.components

import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.timeflow.app.ui.navigation.BottomNavItem
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.ui.graphics.graphicsLayer
import com.timeflow.app.utils.RenderOptimizer
import androidx.compose.ui.platform.LocalView
import com.timeflow.app.utils.enableHardwareAcceleration
import androidx.compose.foundation.clickable
import androidx.compose.ui.composed

@Composable
fun BottomNavBar(
    navController: NavController,
    items: List<BottomNavItem>,
    modifier: Modifier = Modifier
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    
    // 防止重复导航的状态
    val isNavigating = remember { mutableStateOf(false) }
    
    // 创建Handler用于延迟重置导航状态
    val handler = remember { Handler(Looper.getMainLooper()) }
    
    // 获取当前视图并启用硬件加速
    val view = LocalView.current
    LaunchedEffect(Unit) {
        view.enableHardwareAcceleration()
    }
    
    // 在组件销毁时清理Handler
    DisposableEffect(Unit) {
        onDispose {
            // 移除所有待处理的回调，避免内存泄漏
            handler.removeCallbacksAndMessages(null)
        }
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(80.dp)
            .background(Color.White.copy(alpha = 0.95f)) // 增加不透明度减少渲染负担
            .graphicsLayer(renderEffect = null, shadowElevation = 0f) // 优化渲染性能
    ) {
        // 添加顶部分隔线
        Divider(
            color = Color.LightGray.copy(alpha = 0.3f),
            thickness = 1.dp,
            modifier = Modifier.align(Alignment.TopCenter)
        )
        
        // 导航项
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            items.forEach { item ->
                val selected = currentDestination?.hierarchy?.any { it.route == item.route } == true
                
                // 使用记忆化的交互源和涟漪效果
                val interactionSource = remember { MutableInteractionSource() }
                val ripple = rememberRipple(
                    bounded = false,
                    radius = 24.dp,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 自定义导航项 - 优化点击处理和硬件加速
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight()
                        .padding(4.dp)
                        .wrapContentSize(Alignment.Center)
                        .clip(CircleShape)
                        .graphicsLayer {
                            // 优化图层渲染性能
                            this.renderEffect = null
                            this.alpha = 1f
                            this.shadowElevation = 0f
                        }
                        .indication(interactionSource, ripple)
                        .background(Color.Transparent)
                        // 使用indication和interactionSource代替原始的clickable
                        // 处理点击事件
                        .let { columnModifier ->
                            if (!isNavigating.value && !selected) {
                                columnModifier.then(
                                    Modifier.noRippleClickable {
                                        Log.d("BottomNavBar", "点击底部导航项: ${item.route}")
                                        isNavigating.value = true
                                        
                                        try {
                                            // 简化导航调用，不使用协程包装
                                            navController.navigate(item.route) {
                                                popUpTo(navController.graph.startDestinationRoute!!) {
                                                    saveState = true
                                                }
                                                launchSingleTop = true
                                                restoreState = true
                                            }
                                            
                                            // 使用Handler延迟重置状态，避免协程作用域问题
                                            handler.postDelayed({
                                                isNavigating.value = false
                                            }, 300) // 缩短延迟时间，提高响应速度
                                            
                                            // 超时保护 - 确保在1秒后一定会重置导航状态
                                            handler.postDelayed({
                                                if (isNavigating.value) {
                                                    isNavigating.value = false
                                                }
                                            }, 1000) // 缩短超时时间
                                            
                                        } catch (e: Exception) {
                                            Log.e("BottomNavBar", "导航错误: ${e.message}")
                                            isNavigating.value = false
                                        }
                                    }
                                )
                            } else {
                                columnModifier
                            }
                        }
                ) {
                    // 图标区域 - 简化渲染
                    Icon(
                        imageVector = if (selected) item.selectedIcon else item.unselectedIcon,
                        contentDescription = stringResource(id = item.titleResId),
                        tint = if (selected) MaterialTheme.colorScheme.primary else Color.Gray,
                        modifier = Modifier
                            .size(24.dp)
                            .padding(vertical = 2.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(2.dp))
                    
                    // 文本 - 使用基本Text，减少自定义样式
                    Text(
                        text = stringResource(id = item.titleResId),
                        fontSize = 11.sp,
                        fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
                        color = if (selected) MaterialTheme.colorScheme.primary else Color.Gray,
                        maxLines = 1,
                    )
                }
            }
        }
    }
}

// 无涟漪点击修饰符 - 替代原始clickable但不使用RippleDrawable
@Composable
private fun Modifier.noRippleClickable(onClick: () -> Unit): Modifier = composed {
    clickable(
        interactionSource = remember { MutableInteractionSource() },
        indication = null, // 不使用涟漪效果
        onClick = onClick
    )
} 