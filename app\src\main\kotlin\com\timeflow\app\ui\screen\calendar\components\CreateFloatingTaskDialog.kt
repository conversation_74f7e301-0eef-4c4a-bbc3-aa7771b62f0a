package com.timeflow.app.ui.screen.calendar.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.timeflow.app.data.model.Priority
import java.time.LocalDateTime

/**
 * 创建浮动任务对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateFloatingTaskDialog(
    weekStart: LocalDateTime,
    weekEnd: LocalDateTime,
    onDismiss: () -> Unit,
    onCreateTask: (title: String, description: String, priority: Priority?, estimatedMinutes: Int) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf<Priority?>(Priority.MEDIUM) }
    var estimatedMinutes by remember { mutableStateOf(60) }
    var estimatedHours by remember { mutableStateOf(1) }
    var estimatedMins by remember { mutableStateOf(0) }
    var isCreating by remember { mutableStateOf(false) } // 🔧 防重复创建状态
    
    val titleFocusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // 计算总分钟数
    LaunchedEffect(estimatedHours, estimatedMins) {
        estimatedMinutes = estimatedHours * 60 + estimatedMins
    }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "添加本周任务",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1F2937)
                    )
                    
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = Color(0xFF6B7280),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 任务标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("任务标题") },
                    placeholder = { Text("输入任务名称...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(titleFocusRequester),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Next
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                        focusedLabelColor = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                    )
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 任务描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述 (可选)") },
                    placeholder = { Text("添加任务描述...") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = { keyboardController?.hide() }
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                        focusedLabelColor = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                    )
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 优先级选择
                Text(
                    text = "优先级",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF374151),
                    modifier = Modifier.padding(bottom = 12.dp)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Priority.values().forEach { priority ->
                        PriorityChip(
                            priority = priority,
                            isSelected = selectedPriority == priority,
                            onClick = { selectedPriority = priority },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 预估时间
                Text(
                    text = "预估时间",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF374151),
                    modifier = Modifier.padding(bottom = 12.dp)
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 小时选择器
                    TimePickerWheel(
                        label = "小时",
                        value = estimatedHours,
                        onValueChange = { estimatedHours = it },
                        range = 0..23,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 分钟选择器
                    TimePickerWheel(
                        label = "分钟",
                        value = estimatedMins,
                        onValueChange = { estimatedMins = it },
                        range = 0..59,
                        step = 15,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 周期信息显示
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFC4B5D4).copy(alpha = 0.1f) // 莫兰迪薰衣草紫
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = null,
                            tint = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                            modifier = Modifier.size(20.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "本周内任意时间完成",
                            fontSize = 13.sp,
                            color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF6B7280)
                        ),
                        border = ButtonDefaults.outlinedButtonBorder.copy(
                            brush = androidx.compose.ui.graphics.SolidColor(Color(0xFFD1D5DB))
                        )
                    ) {
                        Text("取消")
                    }
                    
                    // 创建按钮
                    Button(
                        onClick = {
                            if (title.isNotBlank() && !isCreating) {
                                isCreating = true
                                onCreateTask(title.trim(), description.trim(), selectedPriority, estimatedMinutes)
                            }
                        },
                        enabled = title.isNotBlank() && !isCreating,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                            disabledContainerColor = Color(0xFFC4B5D4).copy(alpha = 0.5f)
                        )
                    ) {
                        if (isCreating) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                androidx.compose.material3.CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    color = Color.White,
                                    strokeWidth = 2.dp
                                )
                                Text("创建中...", color = Color.White)
                            }
                        } else {
                            Text("创建", color = Color.White)
                        }
                    }
                }
            }
        }
    }
    
    // 自动聚焦标题输入框
    LaunchedEffect(Unit) {
        titleFocusRequester.requestFocus()
    }
}

/**
 * 优先级选择芯片
 */
@Composable
private fun PriorityChip(
    priority: Priority,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (priority) {
        Priority.URGENT -> Color(0xFFEF4444) to "紧急"
        Priority.HIGH -> Color(0xFFF59E0B) to "高"
        Priority.MEDIUM -> Color(0xFF10B981) to "中"
        Priority.LOW -> Color(0xFF6B7280) to "低"
    }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(
                color = if (isSelected) color.copy(alpha = 0.15f) else Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (isSelected) color else Color(0xFFE5E7EB),
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(color = color, shape = CircleShape)
            )
            
            Spacer(modifier = Modifier.width(6.dp))
            
            Text(
                text = text,
                fontSize = 12.sp,
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                color = if (isSelected) color else Color(0xFF6B7280)
            )
        }
    }
}

/**
 * 时间选择器轮盘
 */
@Composable
private fun TimePickerWheel(
    label: String,
    value: Int,
    onValueChange: (Int) -> Unit,
    range: IntRange,
    step: Int = 1,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF6B7280),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 减少按钮
            IconButton(
                onClick = {
                    val newValue = (value - step).coerceAtLeast(range.first)
                    onValueChange(newValue)
                },
                modifier = Modifier.size(32.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(28.dp)
                        .background(
                            color = Color(0xFFF3F4F6),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Remove,
                        contentDescription = "减少",
                        tint = Color(0xFF6B7280),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            // 数值显示
            Surface(
                modifier = Modifier.width(50.dp),
                color = Color(0xFFF9FAFB),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = value.toString(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937),
                    modifier = Modifier.padding(vertical = 8.dp),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
            }
            
            // 增加按钮
            IconButton(
                onClick = {
                    val newValue = (value + step).coerceAtMost(range.last)
                    onValueChange(newValue)
                },
                modifier = Modifier.size(32.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(28.dp)
                        .background(
                            color = Color(0xFFF3F4F6),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "增加",
                        tint = Color(0xFF6B7280),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
} 