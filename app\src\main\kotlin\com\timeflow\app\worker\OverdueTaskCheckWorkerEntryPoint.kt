package com.timeflow.app.worker

import com.timeflow.app.service.TaskReminderScheduler
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * 逾期任务检查Worker的EntryPoint
 * 用于在Worker中获取依赖注入的对象
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface OverdueTaskCheckWorkerEntryPoint {
    fun taskReminderScheduler(): TaskReminderScheduler
} 