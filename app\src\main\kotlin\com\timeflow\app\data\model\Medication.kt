package com.timeflow.app.data.model

import java.time.LocalTime

/**
 * 用药信息数据模型
 */
data class Medication(
    val id: String,
    val name: String,
    val dosage: String,
    val frequency: String,
    val reminderTimes: List<String> = emptyList(),
    val category: String = "",
    val isActive: Boolean = true,
    val startDate: String = "",
    val endDate: String? = null,
    val notes: String = ""
) 