package com.timeflow.app.data.model

import java.io.Serializable

/**
 * AI服务配置数据模型
 */
data class AiConfigModel(
    val id: String = "",              // 配置的唯一标识符
    val name: String = "",            // 配置名称
    val apiKey: String = "",          // API密钥
    val apiServer: String = "",       // API服务器地址
    val modelName: String = "",       // 模型名称
    val provider: String = "DeepSeek", // 提供商
    val isDefault: Boolean = false    // 是否为默认配置
) : Serializable 