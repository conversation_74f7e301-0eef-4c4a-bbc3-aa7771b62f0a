package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 周统计小组件 - 显示一周的专注时间统计图表
 */
class WeeklyStatsWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        val views = RemoteViews(context.packageName, R.layout.widget_weekly_stats)
        
        // 设置标题
        views.setTextViewText(R.id.widget_title, "统计")
        views.setTextViewText(R.id.widget_subtitle, "每日趋势")
        
        // 设置日期范围
        val today = LocalDate.now()
        val weekStart = today.minusDays(6)
        val startDate = weekStart.format(DateTimeFormatter.ofPattern("M/d"))
        val endDate = today.format(DateTimeFormatter.ofPattern("M/d"))
        views.setTextViewText(R.id.widget_date_range, "$startDate - $endDate")
        
        // 设置统计数据
        views.setTextViewText(R.id.widget_total_time, "12h 30m")
        views.setTextViewText(R.id.widget_avg_time, "107min")
        views.setTextViewText(R.id.widget_trend, "📈 上升")
        
        // 设置图表数据
        views.setProgressBar(R.id.widget_bar_1, 100, 30, false)
        views.setProgressBar(R.id.widget_bar_2, 100, 45, false)
        views.setProgressBar(R.id.widget_bar_3, 100, 25, false)
        views.setProgressBar(R.id.widget_bar_4, 100, 60, false)
        views.setProgressBar(R.id.widget_bar_5, 100, 80, false)
        views.setProgressBar(R.id.widget_bar_6, 100, 70, false)
        views.setProgressBar(R.id.widget_bar_7, 100, 90, false)
        
        // 设置日期标签
        views.setTextViewText(R.id.widget_day_1, weekStart.format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_2, weekStart.plusDays(1).format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_3, weekStart.plusDays(2).format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_4, weekStart.plusDays(3).format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_5, weekStart.plusDays(4).format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_6, weekStart.plusDays(5).format(DateTimeFormatter.ofPattern("d")))
        views.setTextViewText(R.id.widget_day_7, today.format(DateTimeFormatter.ofPattern("d")))
        
        // 设置点击事件
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "analytics")
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)
        
        appWidgetManager.updateAppWidget(appWidgetId, views)
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
    }
}
