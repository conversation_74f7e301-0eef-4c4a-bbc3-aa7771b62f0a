package com.timeflow.app.ui.screen.milestone

import android.app.Activity
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.CalendarMonth
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.*
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.time.format.DateTimeFormatter
import androidx.compose.ui.window.DialogProperties

/**
 * 莫奈+莫兰迪配色方案
 */
object MonetMorandiColors {
    // 莫奈主色调 - 柔和且充满活力
    val Primary = Color(0xFF9BB5FF) // 柔和蓝紫色
    val PrimaryVariant = Color(0xFF7C9AE8) // 深一点的蓝紫色
    val Secondary = Color(0xFFB8E6B8) // 薄荷绿
    val Accent = Color(0xFFFFB8D6) // 樱花粉
    
    // 莫兰迪中性色调 - 低饱和度，高级感
    val Background = Color(0xFFF8F6F0) // 米白色背景
    val Surface = Color(0xFFFFFBF7) // 卡片表面
    val SurfaceVariant = Color(0xFFF2F0EA) // 变体表面
    
    // 文字颜色 - 柔和不刺眼
    val OnSurface = Color(0xFF4A4A4A) // 主要文字
    val OnSurfaceVariant = Color(0xFF6B6B6B) // 次要文字  
    val OnSurfaceTertiary = Color(0xFF8B8B8B) // 三级文字
    
    // 类别色彩 - 莫兰迪风格低饱和度（加深版本）
    val Career = Color(0xFF9BA8C0) // 职业 - 深雾霾蓝
    val Education = Color(0xFFB0C0A0) // 教育 - 深鼠尾草绿
    val Love = Color(0xFFD4A8A8) // 爱情 - 深玫瑰灰
    val Life = Color(0xFFB8C8B8) // 生活 - 深薄荷灰
    val Travel = Color(0xFFBFA880) // 旅行 - 深暖米色
    val Health = Color(0xFFC8B8E0) // 健康 - 深淡紫灰
    
    // 功能色彩
    val Success = Color(0xFFB8D8B8) // 成功 - 柔和绿
    val Warning = Color(0xFFE8D5A3) // 警告 - 温和黄
    val Error = Color(0xFFE8B8B8) // 错误 - 温和红
    val Info = Color(0xFFB8D8E8) // 信息 - 温和蓝
}

/**
 * 长按处理器
 */
class LongPressHandler {
    private var isLongPressing = false
    
    suspend fun handleLongPress(onLongPress: () -> Unit) {
        if (!isLongPressing) {
            isLongPressing = true
            delay(600) // 长按阈值
            onLongPress()
            isLongPressing = false
        }
    }
    
    fun cancelLongPress() {
        isLongPressing = false
    }
}

/**
 * 里程碑页面 - 莫奈+莫兰迪美化版
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MilestoneScreen(
    navController: NavController,
    viewModel: MilestoneViewModel = hiltViewModel()
) {
    // 设置状态栏
    val context = LocalContext.current
    DisposableEffect(Unit) {
        (context as? Activity)?.let {
            SystemBarManager.forceOpaqueStatusBar(it, lightStatusBar = true)
        }
        onDispose {}
    }
    
    // 获取状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val coroutineScope = rememberCoroutineScope()
    val hapticFeedback = LocalHapticFeedback.current
    
    // 视图模式状态
    var viewMode by remember { mutableStateOf(ViewMode.GRID) }
    
    // 编辑状态
    var editingMilestone by remember { mutableStateOf<Milestone?>(null) }
    var showingDetailMilestone by remember { mutableStateOf<Milestone?>(null) }
    
    // 计算总体完成度
    val totalProgress = remember(uiState.filteredMilestones) {
        if (uiState.filteredMilestones.isEmpty()) 0f
        else uiState.filteredMilestones.sumOf { it.completionPercentage.toDouble() }.toFloat() / 
             uiState.filteredMilestones.size / 100f
    }
    
    // 主布局
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MonetMorandiColors.Background,
                        MonetMorandiColors.SurfaceVariant
                    )
                )
            )
    ) {
        Column(
                        modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight())
        ) {
            // 顶部导航栏
            MonetTopBar(
                totalProgress = totalProgress,
                viewMode = viewMode,
                onViewModeChange = { newMode -> viewMode = newMode },
                onBackClick = { navController.popBackStack() }
            )
            
            // 内容区域 - 根据视图模式显示不同内容
            when (viewMode) {
                ViewMode.GRID -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 里程碑卡片
                        items(
                            items = uiState.filteredMilestones,
                            key = { it.id }
                        ) { milestone ->
                            MonetMilestoneCard(
                                milestone = milestone,
                                onClick = { 
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    showingDetailMilestone = milestone 
                                },
                                onLongPress = { 
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                    editingMilestone = milestone 
                                }
                    )
                }
                        
                        // 底部空间
                        item {
                            Spacer(modifier = Modifier.height(80.dp))
                        }
                    }
                }
                ViewMode.TIMELINE -> {
                    YearTimelineView(
                        milestones = uiState.filteredMilestones,
                        onMilestoneClick = { milestone ->
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                            showingDetailMilestone = milestone
                        },
                        onMilestoneLongPress = { milestone ->
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            editingMilestone = milestone
                    },
                    modifier = Modifier.fillMaxSize()
                        )
                }
                ViewMode.YEARLY -> {
                    // 年度视图 - 与时间轴视图相同
                    YearTimelineView(
                            milestones = uiState.filteredMilestones,
                        onMilestoneClick = { milestone ->
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                            showingDetailMilestone = milestone
                        },
                        onMilestoneLongPress = { milestone ->
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            editingMilestone = milestone
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                    }
            }
            
        // 浮动操作按钮
            FloatingActionButton(
                    onClick = { 
                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                editingMilestone = Milestone(
                                id = "new_${System.currentTimeMillis()}",
                                title = "",
                                description = "",
                                category = MilestoneCategory.LIFE,
                    date = java.time.LocalDate.now(),
                                completionPercentage = 0f,
                    milestoneType = MilestoneType.REGULAR,
                    imageUris = emptyList(),
                    tags = emptyList(),
                    rating = 0f
                )
            },
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = MonetMorandiColors.Primary,
            contentColor = Color.White
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                contentDescription = "添加里程碑"
            )
        }
    }
    
    // 编辑对话框
        editingMilestone?.let { milestone ->
            EditMilestoneDialog(
                milestone = milestone,
                onSave = { updatedMilestone ->
                coroutineScope.launch {
                    if (updatedMilestone.id.startsWith("new_")) {
                        viewModel.addMilestone(updatedMilestone)
                    } else {
                        viewModel.updateMilestone(updatedMilestone)
                    }
                    editingMilestone = null
                }
            },
            onDismiss = { editingMilestone = null }
            )
    }
    
    // 详情对话框
    showingDetailMilestone?.let { milestone ->
        MilestoneDetailDialog(
            milestone = milestone,
            onEdit = { 
                showingDetailMilestone = null
                editingMilestone = milestone
            },
            onDismiss = { showingDetailMilestone = null },
            onDelete = {
                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                viewModel.deleteMilestone(milestone.id)
                showingDetailMilestone = null
            },
            onImageAdd = { imagePaths ->
                val updatedMilestone = milestone.copy(
                    imageUris = milestone.imageUris + imagePaths
                )
                viewModel.updateMilestone(updatedMilestone)
            },
            onImageRemove = { imagePath ->
                val updatedMilestone = milestone.copy(
                    imageUris = milestone.imageUris - imagePath
                )
                viewModel.updateMilestone(updatedMilestone)
                // 删除本地文件
                try {
                    java.io.File(imagePath).delete()
                } catch (e: Exception) {
                    // 处理删除失败
                }
            }
            )
    }
}

/**
 * 莫奈风格顶部导航栏
 */
@Composable
fun MonetTopBar(
    totalProgress: Float,
    viewMode: ViewMode,
    onViewModeChange: (ViewMode) -> Unit,
    onBackClick: () -> Unit
) {
    Column {
    Row(
        modifier = Modifier
            .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 返回按钮
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                    .size(40.dp)
                .clip(CircleShape)
                    .background(MonetMorandiColors.Surface)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                    tint = MonetMorandiColors.OnSurface,
                    modifier = Modifier.size(20.dp)
                        )
            }
                    
            Spacer(modifier = Modifier.width(12.dp))
            
            // 标题区域
            Column(modifier = Modifier.weight(1f)) {
                    Text(
                    text = "人生里程碑",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MonetMorandiColors.OnSurface
                )
                Text(
                    text = "记录生命中的重要时刻",
                    fontSize = 11.sp,
                    color = MonetMorandiColors.OnSurfaceVariant
                )
        }
        
        // 进度指示器
            Box(
                modifier = Modifier
                    .size(44.dp)
                    .clip(CircleShape)
                    .background(MonetMorandiColors.Surface),
                contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                progress = { totalProgress },
                    modifier = Modifier.size(32.dp),
                    color = MonetMorandiColors.Primary,
                    trackColor = MonetMorandiColors.Primary.copy(alpha = 0.2f),
                strokeWidth = 3.dp
            )
                            Text(
                text = "${(totalProgress * 100).toInt()}%",
                    fontSize = 8.sp,
                                fontWeight = FontWeight.Medium,
                    color = MonetMorandiColors.Primary
            )
            }
        }
        
        // 视图切换按钮
        Row(
                            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            ViewModeButton(
                isSelected = viewMode == ViewMode.GRID,
                icon = Icons.Default.ViewList,
                text = "列表",
                onClick = { onViewModeChange(ViewMode.GRID) }
            )
            
            ViewModeButton(
                isSelected = viewMode == ViewMode.TIMELINE,
                icon = Icons.Default.Timeline,
                text = "年时间轴",
                onClick = { onViewModeChange(ViewMode.TIMELINE) }
            )
        }
    }
}

/**
 * 莫奈风格里程碑卡片
 */
@Composable
fun MonetMilestoneCard(
    milestone: Milestone,
    onClick: () -> Unit,
    onLongPress: () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(dampingRatio = 0.8f)
    )
    
    val categoryColor = when (milestone.category) {
        MilestoneCategory.CAREER -> MonetMorandiColors.Career
        MilestoneCategory.EDUCATION -> MonetMorandiColors.Education
        MilestoneCategory.LOVE -> MonetMorandiColors.Love
        MilestoneCategory.LIFE -> MonetMorandiColors.Life
        MilestoneCategory.TRAVEL -> MonetMorandiColors.Travel
        MilestoneCategory.HEALTH -> MonetMorandiColors.Health
        else -> MonetMorandiColors.Life
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer(scaleX = scale, scaleY = scale)
            .pointerInput(milestone.id) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        val released = try {
                            tryAwaitRelease()
                        } catch (e: Exception) {
                            false
                        }
                        isPressed = false
                        released
                    },
                    onTap = { onClick() },
                    onLongPress = { onLongPress() }
                )
            },
                        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MonetMorandiColors.Surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
                    Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                .padding(16.dp)
                                    ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧图标
                                    Box(
                                        modifier = Modifier
                        .size(48.dp)
                                            .clip(RoundedCornerShape(12.dp))
                        .background(categoryColor.copy(alpha = 0.2f)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                        imageVector = milestone.category.icon,
                        contentDescription = null,
                        tint = categoryColor,
                                        modifier = Modifier.size(24.dp)
                                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 中间内容
                Column(modifier = Modifier.weight(1f)) {
                                    Text(
                        text = milestone.title,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = MonetMorandiColors.OnSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(2.dp))
                    
                    Text(
                        text = milestone.description,
                        fontSize = 11.sp,
                        color = MonetMorandiColors.OnSurfaceVariant,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 类别标签
                        Surface(
                            color = categoryColor.copy(alpha = 0.15f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = milestone.category.displayName,
                                fontSize = 9.sp,
                                color = categoryColor,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                    
                        Spacer(modifier = Modifier.width(8.dp))
                    
                        // 日期
                    Text(
                            text = milestone.date.format(DateTimeFormatter.ofPattern("MM/dd")),
                            fontSize = 9.sp,
                            color = MonetMorandiColors.OnSurfaceTertiary
                        )
                    }
                }
                
                // 右侧进度和状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 完成度圆环
                                        Box(
                        modifier = Modifier.size(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            progress = { milestone.completionPercentage / 100f },
                            modifier = Modifier.fillMaxSize(),
                            color = categoryColor,
                            trackColor = categoryColor.copy(alpha = 0.2f),
                            strokeWidth = 2.dp
                        )
                Text(
                            text = "${milestone.completionPercentage.toInt()}%",
                            fontSize = 8.sp,
                    fontWeight = FontWeight.Medium,
                            color = categoryColor
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                
                    // 里程碑类型指示器
                    if (milestone.milestoneType != MilestoneType.REGULAR) {
                        val typeIcon = when (milestone.milestoneType) {
                            MilestoneType.DECISION -> Icons.Default.Psychology
                            MilestoneType.OPPORTUNITY -> Icons.Default.TrendingUp
                            MilestoneType.IMPACT -> Icons.Default.Bolt
                            else -> Icons.Default.Circle
                        }
                        
                                Icon(
                            imageVector = typeIcon,
                                    contentDescription = null,
                            tint = categoryColor,
                            modifier = Modifier.size(12.dp)
                                )
                    }
                }
            }
            
            // 图片显示区域
            if (milestone.imageUris.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
        
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                    items(milestone.imageUris.take(4)) { imageUri ->
                        Card(
                            modifier = Modifier
                                .size(50.dp)
                                .clip(RoundedCornerShape(8.dp)),
                            elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                        ) {
                            Image(
                                painter = rememberAsyncImagePainter(imageUri.toUri()),
                                contentDescription = "里程碑图片",
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.Crop
                            )
                        }
}

                    // 如果有更多图片，显示数量提示
                    if (milestone.imageUris.size > 4) {
                        item {
                Card(
                    modifier = Modifier
                                    .size(50.dp)
                                    .clip(RoundedCornerShape(8.dp)),
                    colors = CardDefaults.cardColors(
                                    containerColor = MonetMorandiColors.Primary.copy(alpha = 0.1f)
                    )
                ) {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                    ) {
                        Text(
                                        text = "+${milestone.imageUris.size - 4}",
                                        fontSize = 10.sp,
                            fontWeight = FontWeight.Medium,
                                        color = MonetMorandiColors.Primary
                        )
                    }
                }
                        }
                            }
                        }
                    }
        }
    }
}

/**
 * 详情信息行
 */
@Composable
fun DetailInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
            text = label,
            fontSize = 11.sp,
            color = MonetMorandiColors.OnSurfaceVariant,
            fontWeight = FontWeight.Medium
        )
                                Text(
            text = value,
            fontSize = 11.sp,
            color = MonetMorandiColors.OnSurface,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 编辑里程碑对话框 - 简化版
 */
@Composable
fun EditMilestoneDialog(
    milestone: Milestone,
    onSave: (Milestone) -> Unit,
    onDismiss: () -> Unit
) {
    var title by remember { mutableStateOf(milestone.title) }
    var description by remember { mutableStateOf(milestone.description) }
    var category by remember { mutableStateOf(milestone.category) }
    var date by remember { mutableStateOf(milestone.date) }
    var showDatePicker by remember { mutableStateOf(false) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = MonetMorandiColors.Surface,
        title = {
            Text(
                text = if (milestone.id.startsWith("new_")) "添加里程碑" else "编辑里程碑",
                fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                color = MonetMorandiColors.OnSurface
            )
        },
        text = {
                    Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("标题", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MonetMorandiColors.Primary,
                        unfocusedBorderColor = MonetMorandiColors.Primary.copy(alpha = 0.3f)
                    )
                )
                
                // 描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("描述", fontSize = 12.sp) },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3,
                    maxLines = 5,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MonetMorandiColors.Primary,
                        unfocusedBorderColor = MonetMorandiColors.Primary.copy(alpha = 0.3f)
                    )
                )
                
                // 类别选择
                    Text(
                    text = "类别",
                    fontSize = 12.sp,
                    color = MonetMorandiColors.OnSurfaceVariant
                )
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(MilestoneCategory.values()) { cat ->
                        FilterChip(
                            selected = category == cat,
                            onClick = { category = cat },
                            label = { Text(cat.displayName, fontSize = 12.sp) },
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = MonetMorandiColors.Primary,
                                selectedLabelColor = Color.White
                            )
                        )
                    }
                }
                
                // 日期选择
                Text(
                    text = "日期",
                    fontSize = 12.sp,
                    color = MonetMorandiColors.OnSurfaceVariant
                )
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp))
                        .clickable { showDatePicker = true },
                    color = MonetMorandiColors.Surface,
                    border = BorderStroke(1.dp, MonetMorandiColors.Primary.copy(alpha = 0.3f))
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "日期",
                                tint = MonetMorandiColors.Primary
                            )
                            Text(
                                text = date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                                fontSize = 14.sp,
                                color = MonetMorandiColors.OnSurface
                            )
                        }
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowRight,
                            contentDescription = "选择",
                            tint = MonetMorandiColors.OnSurfaceVariant
                        )
                    }
                }
            }
        },
        confirmButton = {
                    TextButton(
                onClick = {
                    onSave(milestone.copy(
                                title = title,
                                description = description,
                        category = category,
                        date = date
                    ))
                },
                enabled = title.isNotBlank()
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
    
    // 日期选择器对话框
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = date.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
            TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            val selectedDate = java.time.Instant.ofEpochMilli(millis)
                                .atZone(java.time.ZoneId.systemDefault())
                                .toLocalDate()
                            date = selectedDate
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDatePicker = false }
                ) {
                    Text("取消")
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                usePlatformDefaultWidth = false
            ),
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .wrapContentHeight(),
            colors = DatePickerDefaults.colors(
                containerColor = Color.White
            )
        ) {
            DatePicker(
                state = datePickerState,
                showModeToggle = false,
                title = null,
                headline = null,
                colors = DatePickerDefaults.colors(
                    containerColor = MonetMorandiColors.Surface,
                    titleContentColor = MonetMorandiColors.OnSurface,
                    headlineContentColor = MonetMorandiColors.OnSurface,
                    weekdayContentColor = MonetMorandiColors.OnSurfaceVariant,
                    subheadContentColor = MonetMorandiColors.OnSurfaceVariant,
                    yearContentColor = MonetMorandiColors.OnSurface,
                    currentYearContentColor = MonetMorandiColors.Primary,
                    selectedYearContainerColor = MonetMorandiColors.Primary.copy(alpha = 0.1f),
                    selectedYearContentColor = MonetMorandiColors.Primary,
                    dayContentColor = MonetMorandiColors.OnSurface,
                    selectedDayContainerColor = MonetMorandiColors.Primary,
                    selectedDayContentColor = Color.White,
                    todayDateBorderColor = MonetMorandiColors.Primary,
                    todayContentColor = MonetMorandiColors.Primary
                ),
                modifier = Modifier
                    .padding(top = 4.dp)  // 减少顶部内边距
                    .padding(horizontal = 8.dp)  // 减少水平内边距
            )
        }
    }
}