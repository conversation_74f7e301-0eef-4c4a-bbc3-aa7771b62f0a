package com.timeflow.app.data.model

import androidx.compose.ui.graphics.Color
import java.time.LocalTime

/**
 * 视图层时间槽数据模型
 */
data class ViewTimeSlot(
    val id: String,
    val title: String,
    val description: String = "",
    val startTime: LocalTime,
    val endTime: LocalTime,
    val category: ViewTimeCategory,
    
    // UI渲染相关属性，用于存储Canvas绘制信息
    var startX: Float = 0f,
    var startY: Float = 0f,
    var width: Float = 0f,
    var height: Float = 0f
)

/**
 * 视图层时间槽类别
 */
data class ViewTimeCategory(
    val id: String,
    val name: String,
    val color: Color
) 