package com.timeflow.app.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 安全的ViewModel基类
 * 提供内存安全和异常处理机制
 */
abstract class SafeViewModel : ViewModel() {
    
    /**
     * 视图状态 - 表示UI状态：加载中、错误、成功等
     */
    val viewState = MutableStateFlow<ViewState>(ViewState.Idle)
    
    /**
     * 协程异常处理器，用于捕获ViewModel作用域中的协程异常
     */
    private val errorHandler = CoroutineExceptionHandler { _, exception ->
        if (exception !is CancellationException) {
            Timber.e(exception, "ViewModel协程异常")
            viewState.value = ViewState.Error(exception.message ?: "未知错误")
        }
    }
    
    /**
     * 安全地启动协程，内置错误处理
     */
    protected fun safeLaunch(block: suspend CoroutineScope.() -> Unit) {
        viewModelScope.launch(errorHandler) {
            // 设置为加载状态
            viewState.value = ViewState.Loading
            
            try {
                block()
                // 仅当当前状态仍为加载中时，设置为成功状态
                // 避免覆盖block中可能设置的其他状态
                if (viewState.value is ViewState.Loading) {
                    viewState.value = ViewState.Success
                }
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    viewState.value = ViewState.Error(e.message ?: "未知错误")
                    Timber.e(e, "safeLaunch异常")
                } else {
                    throw e // 重新抛出CancellationException，以便正确传播取消操作
                }
            }
        }
    }
    
    /**
     * 处理ViewModel清理工作，取消所有未完成的协程
     */
    override fun onCleared() {
        // viewModelScope会在onCleared时自动取消所有协程
        super.onCleared()
    }
}

/**
 * 表示视图状态的密封类
 */
sealed class ViewState {
    // 初始空闲状态
    object Idle : ViewState()
    
    // 加载中状态
    object Loading : ViewState()
    
    // 成功状态，可以包含可选的结果数据
    object Success : ViewState()
    
    // 错误状态，包含错误信息
    data class Error(val message: String) : ViewState()
} 