package com.timeflow.app.ui.screen.goal

import android.app.Activity
import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.material.ripple.rememberRipple
import com.timeflow.app.data.repository.GoalStats
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.ui.components.goal.GoalCategorySelector
import com.timeflow.app.data.model.getDefaultGoalCategories
import com.timeflow.app.data.model.getCategoryById
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.delay

// 现代化设计色彩系统 - 参考苹果设计规范
private val BackgroundColor = Color(0xFFF2F2F7)  // 苹果标准背景色
private val CardBackgroundColor = Color.White
private val PrimaryColor = Color(0xFF877b85)      // 紫色
private val SecondaryColor = Color(0xFF5856D6)    // 苹果紫
private val AccentColor = Color(0xFF34C759)       // 苹果绿
private val WarningColor = Color(0xFFFF9500)      // 苹果橙
private val DangerColor = Color(0xFFFF3B30)       // 苹果红
private val TextPrimary = Color(0xFF1C1C1E)       // 深色文字
private val TextSecondary = Color(0xFF8E8E93)     // 浅色文字
private val Separator = Color(0xFFE5E5EA)         // 分隔线

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalManagementScreen(
    navController: NavController,
    viewModel: GoalViewModel = hiltViewModel()
) {
    val uiState = viewModel.uiState.value
    val goals = viewModel.goals.value
    val selectedStatus = viewModel.selectedStatus.value
    val selectedCategory = viewModel.selectedCategory.value
    val stats = viewModel.stats.value
    
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态栏处理
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }

    // 动画状态
    var isVisible by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        delay(100)
        isVisible = true
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(BackgroundColor)
    ) {
        // 主内容区域
        AnimatedVisibility(
            visible = isVisible,
            enter = fadeIn(tween(400)) + slideInVertically(
                initialOffsetY = { it / 10 },
                animationSpec = tween(500, easing = FastOutSlowInEasing)
            )
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight()),
                contentPadding = PaddingValues(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)  // 从12dp缩小到10dp
            ) {
                // 顶部标题和操作区域
                item {
                    ModernHeaderSection(navController)
                }
                
                // 统计概览卡片
                item {
                    ModernStatsCard(stats, goals, navController)
                }

                // 分类筛选器
                item {
                    CategoryFilterSection(
                        selectedCategoryId = selectedCategory,
                        onCategorySelected = { categoryId ->
                            viewModel.updateCategoryFilter(categoryId)
                        },
                        onClearFilter = {
                            viewModel.updateCategoryFilter(null)
                        }
                    )
                }

                // 筛选标签
                item {
                    ModernFilterTabs(
                        selectedTab = selectedStatus,
                        onTabSelected = { viewModel.updateStatusFilter(it) }
                    )
                }
                
                // 目标列表
                when (uiState) {
                    is GoalUiState.Loading -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = PrimaryColor,
                                    strokeWidth = 2.dp,
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                    }
                    is GoalUiState.Success -> {
                        if (goals.isEmpty()) {
                            item { ModernEmptyState(navController) }
                        } else {
                            items(goals) { goal ->
                                ModernGoalCard(
                                    goal = goal,
                                    onGoalClick = { 
                                        navController.navigate(AppDestinations.goalDetailRoute(goal.id))
                                    },
                                    onGoalCompleteToggle = { isCompleted -> 
                                        viewModel.markGoalCompleted(goal.id, isCompleted)
                                    },
                                    onEditClick = {
                                        navController.navigate(AppDestinations.editGoalRoute(goal.id))
                                    },
                                    onDeleteClick = {
                                        viewModel.deleteGoal(goal.id)
                                    }
                                )
                            }
                        }
                    }
                    is GoalUiState.Error -> {
                        item {
                            ModernErrorState(
                                error = (uiState as GoalUiState.Error).message,
                                onRetry = { /* 重试逻辑 */ }
                            )
                        }
                    }
                    else -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(120.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "准备就绪",
                                    fontSize = 13.sp,
                                    color = TextSecondary
                                )
                            }
                        }
                    }
                }
                
                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }
    }
}

@Composable
private fun ModernHeaderSection(navController: NavController) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp)
    ) {
        // 主标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "目标",
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    color = TextPrimary,
                    letterSpacing = (-0.5).sp
                )
                
                Text(
                    text = "专注当下，成就未来",
                    fontSize = 13.sp,
                    color = TextSecondary,
                    modifier = Modifier.padding(top = 2.dp)
                )
            }
            
            // 操作按钮组
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // AI助手按钮
                Surface(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .clickable { 
                            navController.navigate(AppDestinations.AI_ASSISTANT_ROUTE)
                        },
                    color = SecondaryColor.copy(alpha = 0.1f),
                    shape = CircleShape
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = "AI助手",
                            tint = SecondaryColor,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                
                // 模板按钮
                Surface(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .clickable { 
                            navController.navigate(AppDestinations.GOAL_TEMPLATE_LIST)
                        },
                    color = WarningColor.copy(alpha = 0.1f),
                    shape = CircleShape
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Outlined.AutoAwesome,
                            contentDescription = "目标模板",
                            tint = WarningColor,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                


                // 添加目标按钮
                Surface(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .clickable {
                            navController.navigate(AppDestinations.ADD_GOAL_ROUTE)
                        },
                    color = PrimaryColor,
                    shape = CircleShape
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加目标",
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ModernStatsCard(stats: GoalStats?, goals: List<Goal>, navController: NavController) {
    if (stats == null) return
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = CardBackgroundColor,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 0.dp
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "概览",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = TextPrimary,
                modifier = Modifier.padding(bottom = 10.dp)
            )
            
            // 统计数据网格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 总目标
                StatItem(
                    icon = Icons.Default.Flag,
                    iconColor = PrimaryColor,
                    title = "目标",
                    value = stats.totalGoals.toString(),
                    subtitle = "总计",
                    modifier = Modifier.weight(1f)
                )
                
                // 进行中
                StatItem(
                    icon = Icons.Default.TrendingUp,
                    iconColor = AccentColor,
                    title = "进行中",
                    value = goals.count { it.completedAt == null }.toString(),
                    subtitle = "活跃",
                    modifier = Modifier.weight(1f)
                )
                
                // AI优化
                StatItem(
                    icon = Icons.Default.Psychology,
                    iconColor = SecondaryColor,
                    title = "AI助手",
                    value = stats.aiBreakdownGoals.toString(),
                    subtitle = "已优化",
                    modifier = Modifier.weight(1f),
                    onClick = {
                        navController.navigate(AppDestinations.goalCompletionAnalysisRoute())
                    }
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    icon: ImageVector,
    iconColor: Color,
    title: String,
    value: String,
    subtitle: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    Surface(
        modifier = modifier
            .then(if (onClick != null) Modifier.clickable { onClick() } else Modifier),
        color = iconColor.copy(alpha = 0.05f),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(10.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(iconColor.copy(alpha = 0.1f), CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(12.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(6.dp))
            
            Text(
                text = value,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = TextPrimary
            )
            
            Text(
                text = title,
                fontSize = 10.sp,
                color = TextSecondary
            )
        }
    }
}

/**
 * 分类筛选区域
 */
@Composable
private fun CategoryFilterSection(
    selectedCategoryId: String?,
    onCategorySelected: (String) -> Unit,
    onClearFilter: () -> Unit
) {
    var showCategorySelector by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 分类筛选标题和清除按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "按分类筛选",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )

            if (selectedCategoryId != null) {
                TextButton(
                    onClick = onClearFilter,
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = "清除",
                        fontSize = 12.sp,
                        color = PrimaryColor
                    )
                }
            }
        }

        // 当前选中的分类显示或选择按钮
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showCategorySelector = !showCategorySelector },
            color = CardBackgroundColor,
            shape = RoundedCornerShape(12.dp),
            border = BorderStroke(1.dp, if (selectedCategoryId != null) PrimaryColor else Separator)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                if (selectedCategoryId != null) {
                    val category = getCategoryById(selectedCategoryId)
                    if (category != null) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = category.icon,
                                contentDescription = null,
                                tint = category.color,
                                modifier = Modifier.size(20.dp)
                            )
                            Text(
                                text = category.name,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = TextPrimary
                            )
                        }
                    } else {
                        Text(
                            text = "选择分类",
                            fontSize = 14.sp,
                            color = TextSecondary
                        )
                    }
                } else {
                    Text(
                        text = "选择分类",
                        fontSize = 14.sp,
                        color = TextSecondary
                    )
                }

                Icon(
                    imageVector = if (showCategorySelector) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = null,
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }

        // 分类选择器
        AnimatedVisibility(
            visible = showCategorySelector,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = CardBackgroundColor,
                shape = RoundedCornerShape(12.dp)
            ) {
                GoalCategorySelector(
                    selectedCategoryId = selectedCategoryId ?: "",
                    onCategorySelected = { categoryId ->
                        onCategorySelected(categoryId)
                        showCategorySelector = false
                    },
                    compactMode = true,
                    showAllCategories = true,
                    modifier = Modifier.padding(12.dp)
                )
            }
        }
    }
}

@Composable
private fun ModernFilterTabs(
    selectedTab: String,
    onTabSelected: (String) -> Unit
) {
    val tabs = listOf("全部", "进行中", "已完成", "待完成", "AI建议")
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = CardBackgroundColor,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            tabs.forEach { tab ->
                val isSelected = selectedTab == tab
                
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clip(RoundedCornerShape(8.dp))
                        .clickable { onTabSelected(tab) },
                    color = if (isSelected) Color(0xFF877b85) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = tab,
                        fontSize = 12.sp,
                        fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
                        color = if (isSelected) Color.White else TextSecondary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun ModernGoalCard(
    goal: Goal,
    onGoalClick: () -> Unit,
    onGoalCompleteToggle: (Boolean) -> Unit,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // 删除确认对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = "删除目标",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            },
            text = {
                Text(
                    text = "确定要删除「${goal.title}」吗？此操作无法撤销。",
                    fontSize = 13.sp,
                    color = TextSecondary
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        onDeleteClick()
                    }
                ) {
                    Text("删除", color = DangerColor, fontSize = 13.sp)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("取消", fontSize = 13.sp)
                }
            }
        )
    }
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onGoalClick() },
        color = CardBackgroundColor,
        shape = RoundedCornerShape(14.dp),  // 从16dp缩小到14dp
        shadowElevation = 0.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp)  // 从20dp缩小到16dp
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    // 标题
                    Text(
                        text = goal.title,
                        fontSize = 15.sp,  // 从16sp缩小到15sp
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    // 描述
                    if (goal.description.isNotEmpty()) {
                        Text(
                            text = goal.description,
                            fontSize = 11.sp,  // 从12sp缩小到11sp
                            color = TextSecondary,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(top = 3.dp)  // 从4dp缩小到3dp
                        )
                    }
                    
                    // 标签行
                    Row(
                        modifier = Modifier.padding(top = 6.dp),  // 从8dp缩小到6dp
                        horizontalArrangement = Arrangement.spacedBy(5.dp)  // 从6dp缩小到5dp
                    ) {
                        // 优先级标签
                        PriorityChip(priority = goal.priority)
                        
                        // AI标签
                        if (goal.hasAiBreakdown || goal.hasAiAnalysis) {
                            Surface(
                                color = SecondaryColor.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(6.dp)  // 从8dp缩小到6dp
                            ) {
                                Text(
                                    text = "AI",
                                    fontSize = 9.sp,  // 从10sp缩小到9sp
                                    fontWeight = FontWeight.Medium,
                                    color = SecondaryColor,
                                    modifier = Modifier.padding(horizontal = 5.dp, vertical = 2.dp)  // 从6dp缩小到5dp
                                )
                            }
                        }
                    }
                }
                
                // 操作按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(3.dp)  // 从4dp缩小到3dp
                ) {
                    // 编辑按钮
                    Surface(
                        modifier = Modifier
                            .size(26.dp)  // 从28dp缩小到26dp
                            .clip(CircleShape)
                            .clickable { onEditClick() },
                        color = TextSecondary.copy(alpha = 0.1f),
                        shape = CircleShape
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = "编辑",
                                tint = TextSecondary,
                                modifier = Modifier.size(13.dp)  // 从14dp缩小到13dp
                            )
                        }
                    }
                    
                    // 删除按钮
                    Surface(
                        modifier = Modifier
                            .size(26.dp)  // 从28dp缩小到26dp
                            .clip(CircleShape)
                            .clickable { showDeleteDialog = true },
                        color = DangerColor.copy(alpha = 0.1f),
                        shape = CircleShape
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "删除",
                                tint = DangerColor,
                                modifier = Modifier.size(13.dp)  // 从14dp缩小到13dp
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))  // 从16dp缩小到12dp
            
            // 🎨 美化的进度区域 - 参照知名app设计
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "进度 ${(goal.progress * 100).toInt()}%",
                        fontSize = 12.sp, // 增大字体
                        fontWeight = FontWeight.Medium,
                        color = TextPrimary
                    )

                    // 截止时间
                    goal.dueDate?.let { dueDate ->
                        val days = ChronoUnit.DAYS.between(LocalDateTime.now(), dueDate).toInt()
                        val (text, color) = when {
                            days < 0 -> "已逾期${-days}天" to DangerColor
                            days == 0 -> "今日截止" to WarningColor
                            days <= 3 -> "剩余${days}天" to WarningColor
                            else -> "${days}天后" to TextSecondary
                        }

                        Text(
                            text = text,
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = color
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp)) // 增加间距

                // 🎨 增大的进度条 - 莫兰迪/莫奈色系
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp) // 从3dp增大到8dp
                        .background(
                            Color(0xFFF5F5F5), // 莫兰迪灰色背景
                            RoundedCornerShape(4.dp)
                        )
                ) {
                    // 进度条主体 - 使用莫兰迪/莫奈色系
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(goal.progress)
                            .fillMaxHeight()
                            .background(
                                brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                                    colors = getProgressColors(goal.priority)
                                ),
                                shape = RoundedCornerShape(4.dp)
                            )
                    )

                    // 🌟 进度条光泽效果
                    if (goal.progress > 0.1f) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(goal.progress * 0.6f)
                                .fillMaxHeight()
                                .background(
                                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.White.copy(alpha = 0.3f),
                                            Color.Transparent
                                        )
                                    ),
                                    shape = RoundedCornerShape(4.dp)
                                )
                        )
                    }
                }

                // 🎯 进度里程碑指示器
                if (goal.progress > 0f) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        listOf(0.25f, 0.5f, 0.75f, 1.0f).forEach { milestone ->
                            val isReached = goal.progress >= milestone
                            Box(
                                modifier = Modifier
                                    .size(6.dp)
                                    .background(
                                        color = if (isReached) {
                                            getProgressColors(goal.priority)[1]
                                        } else {
                                            Color(0xFFE0E0E0)
                                        },
                                        shape = CircleShape
                                    )
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 🎨 获取莫兰迪/莫奈色系的进度条颜色
 */
private fun getProgressColors(priority: GoalPriority): List<Color> {
    return when (priority) {
        GoalPriority.LOW -> listOf(
            Color(0xFF9BB5A6), // 莫兰迪薄荷绿
            Color(0xFFB8CDB8)  // 淡雅绿
        )
        GoalPriority.MEDIUM -> listOf(
            Color(0xFFD4A574), // 莫兰迪暖橙
            Color(0xFFE6C2A6)  // 淡雅橙
        )
        GoalPriority.HIGH -> listOf(
            Color(0xFFD4A5A5), // 莫兰迪玫瑰粉
            Color(0xFFE6C2C2)  // 淡雅粉
        )
        GoalPriority.URGENT -> listOf(
            Color(0xFFB5A6A6), // 莫兰迪紫灰
            Color(0xFFD1C4C4)  // 淡雅紫
        )
    }
}

/**
 * 🎨 获取莫奈色系的进度条颜色（备选方案）
 */
private fun getMonetProgressColors(priority: GoalPriority): List<Color> {
    return when (priority) {
        GoalPriority.LOW -> listOf(
            Color(0xFF81C784), // 莫奈绿
            Color(0xFFA5D6A7)  // 淡莫奈绿
        )
        GoalPriority.MEDIUM -> listOf(
            Color(0xFFFFB74D), // 莫奈橙
            Color(0xFFFFCC80)  // 淡莫奈橙
        )
        GoalPriority.HIGH -> listOf(
            Color(0xFFE57373), // 莫奈红
            Color(0xFFEF9A9A)  // 淡莫奈红
        )
        GoalPriority.URGENT -> listOf(
            Color(0xFF9575CD), // 莫奈紫
            Color(0xFFB39DDB)  // 淡莫奈紫
        )
    }
}

@Composable
private fun PriorityChip(priority: GoalPriority) {
    val (text, color) = when (priority) {
        GoalPriority.LOW -> "低" to AccentColor
        GoalPriority.MEDIUM -> "中" to WarningColor
        GoalPriority.HIGH -> "高" to DangerColor
        GoalPriority.URGENT -> "紧急" to DangerColor
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = RoundedCornerShape(6.dp)  // 从8dp缩小到6dp
    ) {
        Text(
            text = text,
            fontSize = 9.sp,  // 从10sp缩小到9sp
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier.padding(horizontal = 5.dp, vertical = 2.dp)  // 从6dp缩小到5dp
        )
    }
}

@Composable
private fun ModernEmptyState(navController: NavController) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = CardBackgroundColor,
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(40.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(64.dp)
                    .background(
                        PrimaryColor.copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Flag,
                    contentDescription = null,
                    tint = PrimaryColor,
                    modifier = Modifier.size(32.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "还没有目标",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = TextPrimary
            )
            
            Text(
                text = "创建第一个目标，开始你的成长之旅",
                fontSize = 12.sp,
                color = TextSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(20.dp))
            
            Surface(
                modifier = Modifier
                    .clip(RoundedCornerShape(20.dp))
                    .clickable { 
                        navController.navigate(AppDestinations.ADD_GOAL_ROUTE)
                    },
                color = PrimaryColor,
                shape = RoundedCornerShape(20.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 20.dp, vertical = 10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    Text(
                        text = "创建目标",
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }
    }
}

@Composable
private fun ModernErrorState(
    error: String,
    onRetry: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = DangerColor.copy(alpha = 0.05f),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = null,
                tint = DangerColor,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "出现问题",
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = TextPrimary
            )
            
            Text(
                text = error,
                fontSize = 12.sp,
                color = TextSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Surface(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .clickable { onRetry() },
                color = PrimaryColor,
                shape = RoundedCornerShape(16.dp)
            ) {
                Text(
                    text = "重试",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }
    }
} 