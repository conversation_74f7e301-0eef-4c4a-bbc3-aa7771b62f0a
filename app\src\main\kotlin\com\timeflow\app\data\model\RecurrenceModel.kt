package com.timeflow.app.data.model

import kotlinx.serialization.Serializable

/**
 * 循环类型枚举
 */
enum class RecurrenceType(val displayName: String, val description: String) {
    NONE("不循环", "任务只执行一次"),
    DAILY("每天", "每天重复"),
    WEEKLY("每周", "每周重复"),
    MONTHLY("每月", "每月重复"),
    YEARLY("每年", "每年重复"),
    CUSTOM("自定义", "自定义循环规则")
}

/**
 * 循环设置数据类
 */
@Serializable
data class RecurrenceSettings(
    val type: String = "NONE", // 对应RecurrenceType
    val interval: Int = 1, // 间隔：每几天/周/月/年
    val endType: String = "NEVER", // 结束类型：NEVER, DATE, COUNT
    val endDate: String? = null, // 结束日期 (ISO格式)
    val endCount: Int? = null, // 重复次数
    val weekdays: List<Int> = emptyList(), // 每周的星期几 (1=周一, 7=周日)
    val monthDay: Int? = null // 每月的第几天
) {
    companion object {
        fun none() = RecurrenceSettings()
        
        fun daily(interval: Int = 1) = RecurrenceSettings(
            type = "DAILY",
            interval = interval
        )
        
        fun weekly(interval: Int = 1, weekdays: List<Int> = emptyList()) = RecurrenceSettings(
            type = "WEEKLY",
            interval = interval,
            weekdays = weekdays
        )
        
        fun monthly(interval: Int = 1, monthDay: Int? = null) = RecurrenceSettings(
            type = "MONTHLY",
            interval = interval,
            monthDay = monthDay
        )
        
        fun yearly(interval: Int = 1) = RecurrenceSettings(
            type = "YEARLY",
            interval = interval
        )
    }
}
