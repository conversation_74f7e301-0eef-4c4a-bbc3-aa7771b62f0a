package com.timeflow.app.util

import com.timeflow.app.data.entity.Task
import java.time.LocalDateTime
import java.time.Duration

object TaskUtils {
    /**
     * Calculate task progress considering weights of subtasks
     */
    fun calculateTaskProgress(task: Task, subtasks: List<Task>): Float {
        if (subtasks.isEmpty()) return if (task.completedAt != null) 1f else 0f
        
        // 由于 Task 实体没有 weight 字段，我们为每个任务使用默认权重 1.0
        val totalWeight = subtasks.size.toDouble()
        val completedWeight = subtasks.count { it.completedAt != null }.toDouble()
        
        if (totalWeight == 0.0) return 0f
        return (completedWeight / totalWeight).toFloat()
    }
    
    /**
     * Calculate task progress for a list of subtasks
     */
    fun calculateTaskProgress(subtasks: List<Task>): Float {
        if (subtasks.isEmpty()) return 0f
        
        // 由于 Task 实体没有 weight 字段，我们为每个任务使用默认权重 1.0
        val totalWeight = subtasks.size.toDouble()
        val completedWeight = subtasks.count { it.completedAt != null }.toDouble()
        
        if (totalWeight == 0.0) return 0f
        return (completedWeight / totalWeight).toFloat()
    }

    /**
     * Validate task weights to ensure they sum up to approximately 100
     */
    fun validateTaskWeights(subtasks: List<Task>): Boolean {
        // 由于我们不使用权重，这个验证总是通过
        return true
    }

    /**
     * Validate task hierarchy to prevent circular dependencies
     */
    fun validateTaskHierarchy(task: Task, parentId: String, getParentTask: (String) -> Task?): Boolean {
        val visited = mutableSetOf<String>()
        var current: Task? = task
        
        while (current != null) {
            if (visited.contains(current.id)) return false
            visited.add(current.id)
            current = current.parentTaskId?.let { getParentTask(it) }
        }
        return true
    }

    /**
     * Detect time conflicts between tasks
     */
    fun detectTimeConflicts(tasks: List<Task>): Map<String, List<String>> {
        val sortedTasks = tasks.sortedBy { it.createdAt }
        val conflictMap = mutableMapOf<String, MutableList<String>>()
        
        var accumulator: List<Task> = emptyList()
        
        sortedTasks.forEachIndexed { index, task ->
            val overlaps = accumulator.filter { 
                task.dueDate?.isAfter(it.createdAt) == true && 
                task.createdAt.isBefore(it.dueDate ?: LocalDateTime.MAX)
            }
            
            overlaps.forEach { overlapped ->
                conflictMap.getOrPut(task.id) { mutableListOf() }.add(overlapped.id)
                conflictMap.getOrPut(overlapped.id) { mutableListOf() }.add(task.id)
            }
            
            accumulator = accumulator + task
        }
        
        return conflictMap
    }

    /**
     * Calculate task duration in minutes
     */
    fun calculateTaskDuration(task: Task): Long {
        val start = task.createdAt
        val end = task.dueDate ?: return 60 // 如果没有截止日期，默认为60分钟
        
        // 确保结束时间晚于开始时间
        if (end.isBefore(start)) {
            return 60 // 如果结束时间早于开始时间，返回默认值
        }
        
        return Duration.between(start, end).toMinutes().coerceAtLeast(15) // 至少15分钟
    }

    /**
     * Check if a task is overdue
     */
    fun isTaskOverdue(task: Task): Boolean {
        val now = LocalDateTime.now()
        return task.completedAt == null && task.dueDate?.isBefore(now) == true
    }

    /**
     * Check for progress anomalies
     */
    fun checkProgressAnomalies(task: Task, subtasks: List<Task>): List<String> {
        val anomalies = mutableListOf<String>()
        val progress = calculateTaskProgress(task, subtasks)
        
        when {
            progress > 1f -> anomalies.add("进度超过100%")
            progress < 0f -> anomalies.add("进度为负数")
            // 不检查权重总和，因为我们不使用权重
        }
        
        return anomalies
    }

    /**
     * Auto-resolve time conflicts
     * 根据任务优先级和持续时间自动调整，将低优先级任务推迟
     */
    fun autoResolveTimeConflicts(
        tasks: List<Task>,
        conflictGroups: List<List<Task>>
    ): List<Task> {
        if (conflictGroups.isEmpty()) return tasks
        
        val updatedTasks = tasks.toMutableList()
        
        for (conflictGroup in conflictGroups) {
            // 按优先级排序冲突任务（优先级更高的任务保持不变）
            val sortedConflicts = conflictGroup.sortedByDescending { it.priority }
            
            // 获取优先级最高的任务，其时间保持不变
            val highestPriorityTask = sortedConflicts.first()
            
            // 如果当前任务优先级高于所有冲突任务，则保持原样
            if ((highestPriorityTask.priority ?: 0) > (sortedConflicts.drop(1).maxOfOrNull { it.priority ?: 0 } ?: -1)) {
                // 对其他冲突任务进行时间调整
                for (task in sortedConflicts.drop(1)) {
                    // 高优先级任务结束后安排低优先级任务
                    val newStartTime = highestPriorityTask.dueDate
                    val taskDuration = calculateTaskDuration(task)
                    val newEndTime = newStartTime?.plusMinutes(taskDuration.toLong())
                    
                    // 更新任务时间
                    val updatedTask = task.copy(
                        createdAt = newStartTime ?: task.createdAt,
                        dueDate = newEndTime
                    )
                    
                    // 替换原任务
                    val index = updatedTasks.indexOfFirst { it.id == task.id }
                    if (index != -1) {
                        updatedTasks[index] = updatedTask
                    }
                }
            }
        }
        
        return updatedTasks
    }

    /**
     * Calculate task completion time estimate
     */
    fun estimateCompletionTime(task: Task, subtasks: List<Task>): LocalDateTime? {
        if (subtasks.isEmpty()) return null
        
        val totalDuration = subtasks.sumOf { calculateTaskDuration(it) }
        val completedDuration = subtasks.filter { it.completedAt != null }
            .sumOf { calculateTaskDuration(it) }
        
        if (totalDuration == 0L) return null
        
        val progress = completedDuration.toFloat() / totalDuration
        val remainingDuration = (totalDuration * (1 - progress)).toLong()
        
        return LocalDateTime.now().plusMinutes(remainingDuration)
    }

    /**
     * Validate task dependencies
     */
    fun validateDependencies(task: Task, dependencies: List<Task>): Boolean {
        return dependencies.all { dependency ->
            dependency.completedAt != null || 
            dependency.dueDate?.isBefore(task.createdAt) == true
        }
    }

    /**
     * 获取与指定任务有时间冲突的任务
     */
    fun getTasksConflictingWith(task: Task, allTasks: List<Task>): List<Task> {
        return allTasks.filter { other ->
            if (other.id == task.id) return@filter false
            if (task.dueDate == null || other.dueDate == null) return@filter false
            
            task.dueDate.isAfter(other.createdAt) && 
            task.createdAt.isBefore(other.dueDate)
        }
    }
    
    /**
     * 为任务建议一个新的时间段，避免与冲突任务重叠
     */
    fun suggestNewTimeSlot(task: Task, conflictingTasks: List<Task>): LocalDateTime {
        val taskDuration = calculateTaskDuration(task)
        val now = LocalDateTime.now()
        
        // 从当前时间开始，每30分钟检查一次时间槽
        var currentTime = now
        while (currentTime.isBefore(now.plusDays(7))) { // 最多查找7天
            val endTime = currentTime.plusMinutes(taskDuration)
            val isSlotAvailable = conflictingTasks.none { conflict ->
                (currentTime.isBefore(conflict.dueDate ?: LocalDateTime.MAX) && 
                endTime.isAfter(conflict.createdAt))
            }
            
            if (isSlotAvailable) {
                return currentTime
            }
            
            currentTime = currentTime.plusMinutes(30)
        }
        
        // 如果无法找到合适的时间槽，返回7天后的时间
        return now.plusDays(7)
    }
} 