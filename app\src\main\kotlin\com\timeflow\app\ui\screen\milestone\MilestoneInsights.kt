package com.timeflow.app.ui.screen.milestone

/**
 * 里程碑数据分析
 */
data class MilestoneInsights(
    val totalMilestones: Int = 0,                                // 总里程碑数量
    val completedMilestones: Int = 0,                            // 已完成里程碑数量
    val categoryDistribution: Map<MilestoneCategory, Int> = emptyMap(), // 类别分布
    val yearlyDistribution: Map<Int, Int> = emptyMap(),          // 年度分布
    val turnedPointsCount: Int = 0,                              // 转折点数量
    val completionRate: Float = 0f,                              // 完成率
    val averageRating: Float = 0f                                // 平均评分
) 