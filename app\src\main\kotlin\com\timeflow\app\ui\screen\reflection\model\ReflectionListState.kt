package com.timeflow.app.ui.screen.reflection.model

import com.timeflow.app.data.model.TimeFilter
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.SortOrder
import com.timeflow.app.ui.screen.reflection.TimeFilter as UITimeFilter
import java.time.LocalDate

/**
 * 感想列表页面状态
 */
data class ReflectionListState(
    val reflections: List<Reflection> = emptyList(),
    val filteredReflections: List<Reflection> = emptyList(),
    val searchQuery: String = "",
    val selectedCategories: Set<String> = emptySet(),
    val selectedTags: Set<String> = emptySet(),
    val selectedMoods: List<String> = emptyList(),
    val selectedTypes: List<String> = emptyList(),
    val advancedFilters: Map<String, Boolean> = emptyMap(),
    val selectedTimeFilter: TimeFilter = TimeFilter.THIS_MONTH,
    val timeFilter: UITimeFilter = UITimeFilter.THIS_WEEK,
    val sortOrder: SortOrder = SortOrder.NEWEST_FIRST,
    val onlyFavorites: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isSearchVisible: Boolean = false,
    val isFilterVisible: Boolean = false,
    val recentSearches: List<String> = emptyList(),
    val popularTags: List<String> = emptyList(),
    val searchSuggestions: List<String> = emptyList(),
    
    // 时间相关字段
    val timeDimension: TimeDimension = TimeDimension.WEEK,
    val currentDate: LocalDate = LocalDate.now(),
    val currentWeek: Pair<LocalDate, LocalDate> = Pair(LocalDate.now(), LocalDate.now()),
    val currentMonth: Pair<LocalDate, LocalDate> = Pair(LocalDate.now(), LocalDate.now()),
    val currentYear: Int = LocalDate.now().year,
    val weekDays: List<DayViewData> = emptyList(),
    val monthDays: List<DayViewData> = emptyList(),
    val yearMonths: List<MonthViewData> = emptyList(),
    val selectedDay: DayViewData? = null
) 