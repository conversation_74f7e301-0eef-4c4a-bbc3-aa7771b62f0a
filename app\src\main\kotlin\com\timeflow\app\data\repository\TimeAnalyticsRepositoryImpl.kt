package com.timeflow.app.data.repository

import com.timeflow.app.data.model.AppUsageData
import com.timeflow.app.data.entity.AppUsageEntity
import com.timeflow.app.data.dao.AppUsageDao
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 时间分析仓库实现
 */
class TimeAnalyticsRepositoryImpl @Inject constructor(
    private val appUsageDao: AppUsageDao
) : TimeAnalyticsRepository {

    override fun getAppUsageByDate(date: LocalDate): Flow<List<AppUsageData>> {
        return appUsageDao.getAppUsageByDate(date)
            .map { entities -> entities.map { it.toAppUsageData() } }
    }

    override suspend fun saveAppUsage(appUsage: AppUsageData, date: LocalDate) {
        withContext(Dispatchers.IO) {
            val entity = appUsage.toAppUsageEntity(date)
            // 先检查是否已存在记录
            val existingEntity = appUsageDao.getAppUsageByPackageAndDate(appUsage.packageName, date)
            
            if (existingEntity != null) {
                // 更新已有记录
                val updatedEntity = existingEntity.copy(
                    durationMs = existingEntity.durationMs + appUsage.duration.inWholeMilliseconds,
                    launchCount = existingEntity.launchCount + appUsage.launchCount
                )
                appUsageDao.update(updatedEntity)
            } else {
                // 插入新记录
                appUsageDao.insert(entity)
            }
        }
    }

    override suspend fun saveAppUsageBatch(appUsages: List<AppUsageData>, date: LocalDate) {
        withContext(Dispatchers.IO) {
            // 获取当天的所有记录
            val existingEntities = appUsageDao.getAppUsageByDateSync(date)
            val existingMap = existingEntities.associateBy { it.packageName }
            
            // 处理每个应用使用数据
            val entitiesToUpdate = mutableListOf<AppUsageEntity>()
            val entitiesToInsert = mutableListOf<AppUsageEntity>()
            
            appUsages.forEach { appUsage ->
                val existing = existingMap[appUsage.packageName]
                
                if (existing != null) {
                    // 更新已有记录
                    entitiesToUpdate.add(existing.copy(
                        durationMs = existing.durationMs + appUsage.duration.inWholeMilliseconds,
                        launchCount = existing.launchCount + appUsage.launchCount
                    ))
                } else {
                    // 创建新记录
                    entitiesToInsert.add(appUsage.toAppUsageEntity(date))
                }
            }
            
            // 批量更新和插入
            if (entitiesToUpdate.isNotEmpty()) {
                appUsageDao.updateAll(entitiesToUpdate)
            }
            
            if (entitiesToInsert.isNotEmpty()) {
                appUsageDao.insertAll(entitiesToInsert)
            }
        }
    }

    override fun getAppUsageByDateRange(
        startDate: LocalDate,
        endDate: LocalDate
    ): Flow<Map<LocalDate, List<AppUsageData>>> {
        return appUsageDao.getAppUsageByDateRange(startDate, endDate)
            .map { entities ->
                entities.groupBy { it.date }
                    .mapValues { (_, entityList) -> entityList.map { it.toAppUsageData() } }
            }
    }

    override fun getCategoryStatsByDate(date: LocalDate): Flow<Map<String, Duration>> {
        return appUsageDao.getAppUsageByDate(date)
            .map { entities ->
                entities.groupBy { it.category }
                    .mapValues { (_, entityList) -> 
                        entityList.sumOf { it.durationMs }.milliseconds 
                    }
            }
    }

    override fun getProductivityStatsByDate(date: LocalDate): Flow<Pair<Duration, Duration>> {
        return appUsageDao.getAppUsageByDate(date)
            .map { entities ->
                val productiveTime = entities
                    .filter { it.isProductivity }
                    .sumOf { it.durationMs }
                    .milliseconds
                
                val distractingTime = entities
                    .filter { !it.isProductivity }
                    .sumOf { it.durationMs }
                    .milliseconds
                
                productiveTime to distractingTime
            }
    }

    override fun getRecentUsageTrend(days: Int): Flow<Map<LocalDate, Duration>> {
        val endDate = LocalDate.now()
        val startDate = endDate.minusDays(days.toLong() - 1)
        
        return appUsageDao.getAppUsageByDateRange(startDate, endDate)
            .map { entities ->
                entities.groupBy { it.date }
                    .mapValues { (_, entityList) -> 
                        entityList.sumOf { it.durationMs }.milliseconds 
                    }
            }
    }
    
    // 扩展函数：将实体转换为模型
    private fun AppUsageEntity.toAppUsageData(): AppUsageData {
        return AppUsageData(
            packageName = this.packageName,
            duration = this.durationMs.milliseconds,
            launchCount = this.launchCount,
            category = this.category,
            isProductivity = this.isProductivity
        )
    }
    
    // 扩展函数：将模型转换为实体
    private fun AppUsageData.toAppUsageEntity(date: LocalDate): AppUsageEntity {
        return AppUsageEntity(
            id = 0, // 自动生成
            packageName = this.packageName,
            date = date,
            durationMs = this.duration.inWholeMilliseconds,
            launchCount = this.launchCount,
            category = this.category,
            isProductivity = this.isProductivity
        )
    }
} 