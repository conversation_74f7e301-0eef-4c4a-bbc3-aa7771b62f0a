package com.timeflow.app.ui.screen.reflection.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.ContentBlock
import com.timeflow.app.ui.screen.reflection.formatDateTime
import java.io.File

/**
 * 🎨 可分享的感想卡片组件
 * 专门用于生成分享图片的美观卡片设计
 */
@Composable
fun ShareableReflectionCard(
    reflection: Reflection,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current
    
    // 计算卡片尺寸（适合分享的长图格式）
    val cardWidth = with(density) { (configuration.screenWidthDp * 0.9f).dp }
    val cardMaxHeight = with(density) { (configuration.screenHeightDp * 0.8f).dp }
    
    // 渐变背景色
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFF8F9FA),
            Color(0xFFF1F3F4)
        )
    )
    
    // 主要内容容器
    Box(
        modifier = modifier
            .width(cardWidth)
            .heightIn(max = cardMaxHeight)
            .background(
                brush = backgroundGradient,
                shape = RoundedCornerShape(24.dp)
            )
            .border(
                width = 1.dp,
                color = Color(0xFFE8EAED),
                shape = RoundedCornerShape(24.dp)
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(28.dp)
        ) {
            // 🎨 头部区域
            ShareableCardHeader(
                reflection = reflection,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 🎨 内容区域
            ShareableCardContent(
                reflection = reflection,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 🎨 底部区域
            ShareableCardFooter(
                reflection = reflection,
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 🎨 装饰元素
        Box(
            modifier = Modifier
                .size(120.dp)
                .align(Alignment.TopEnd)
                .offset(x = 40.dp, y = (-40).dp)
        ) {
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .background(
                        color = Color(0xFF837A93).copy(alpha = 0.1f),
                        shape = CircleShape
                    )
                    .align(Alignment.Center)
            )
        }
    }
}

/**
 * 🎨 可分享卡片头部
 */
@Composable
private fun ShareableCardHeader(
    reflection: Reflection,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 心情图标
        Surface(
            shape = CircleShape,
            color = Color(0xFF837A93).copy(alpha = 0.15f),
            modifier = Modifier.size(56.dp)
        ) {
            Box(contentAlignment = Alignment.Center) {
                Text(
                    text = reflection.mood?.emoji ?: "😊",
                    fontSize = 24.sp
                )
            }
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            // 类型标签
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = Color(0xFF837A93).copy(alpha = 0.1f),
                modifier = Modifier.wrapContentWidth()
            ) {
                Text(
                    text = reflection.type?.displayName ?: "生活感悟",
                    style = MaterialTheme.typography.labelMedium,
                    color = Color(0xFF837A93),
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 时间
            Text(
                text = formatDateTime(reflection.date),
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF666666),
                fontSize = 12.sp
            )
        }
    }
}

/**
 * 🎨 可分享卡片内容
 */
@Composable
private fun ShareableCardContent(
    reflection: Reflection,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 标题
        if (!reflection.title.isNullOrBlank() && reflection.title != "感想记录") {
            Text(
                text = reflection.title,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold,
                    lineHeight = 28.sp
                ),
                color = Color(0xFF1A1A1A),
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        // 内容区域（限制最大高度）
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 400.dp)
        ) {
            if (reflection.richContent?.isNotEmpty() == true) {
                // 富文本内容
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    reflection.richContent?.take(5)?.forEach { contentBlock ->
                        when (contentBlock.type) {
                            "text" -> {
                                Text(
                                    text = contentBlock.value.take(500), // 限制文本长度
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        lineHeight = 24.sp,
                                        letterSpacing = 0.1.sp
                                    ),
                                    color = Color(0xFF2D2D2D),
                                    maxLines = 15 // 限制最大行数
                                )
                            }
                            "image" -> {
                                ShareableImageBlock(
                                    imagePath = contentBlock.value,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .heightIn(max = 150.dp)
                                )
                            }
                        }
                    }
                }
            } else {
                // 普通文本内容
                Text(
                    text = reflection.content.take(800), // 限制文本长度
                    style = MaterialTheme.typography.bodyLarge.copy(
                        lineHeight = 24.sp,
                        letterSpacing = 0.1.sp
                    ),
                    color = Color(0xFF2D2D2D),
                    maxLines = 20 // 限制最大行数
                )
            }
        }
    }
}

/**
 * 🎨 可分享卡片底部
 */
@Composable
private fun ShareableCardFooter(
    reflection: Reflection,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 分隔线
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .background(Color(0xFFE8EAED))
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 标签
            if (reflection.tags.isNotEmpty()) {
                Row(
                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    reflection.tags.take(3).forEach { tag ->
                        Surface(
                            shape = RoundedCornerShape(50),
                            color = Color(0xFF837A93).copy(alpha = 0.1f),
                            modifier = Modifier.height(24.dp)
                        ) {
                            Text(
                                text = "#$tag",
                                style = MaterialTheme.typography.labelSmall,
                                color = Color(0xFF837A93),
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp)
                            )
                        }
                    }
                    
                    if (reflection.tags.size > 3) {
                        Surface(
                            shape = RoundedCornerShape(50),
                            color = Color(0xFF837A93).copy(alpha = 0.1f),
                            modifier = Modifier.height(24.dp)
                        ) {
                            Text(
                                text = "+${reflection.tags.size - 3}",
                                style = MaterialTheme.typography.labelSmall,
                                color = Color(0xFF837A93),
                                modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
            }
            
            // 水印
            Text(
                text = "来自 TimeFlow",
                style = MaterialTheme.typography.labelSmall,
                color = Color(0xFF999999),
                fontSize = 10.sp
            )
        }
    }
}

/**
 * 🎨 可分享的图片块
 */
@Composable
private fun ShareableImageBlock(
    imagePath: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    Card(
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        modifier = modifier
    ) {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(
                    if (imagePath.startsWith("/")) {
                        File(imagePath)
                    } else {
                        imagePath
                    }
                )
                .crossfade(true)
                .build(),
            contentDescription = "感想图片",
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
        )
    }
} 