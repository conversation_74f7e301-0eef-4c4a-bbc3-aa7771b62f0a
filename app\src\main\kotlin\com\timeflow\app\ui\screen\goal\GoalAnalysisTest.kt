package com.timeflow.app.ui.screen.goal

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.data.model.GoalSubTask
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.GoalStats
import com.timeflow.app.data.repository.GoalWithSubtasks as RepoGoalWithSubtasks
import com.timeflow.app.data.repository.AiApiResult
import com.timeflow.app.ui.viewmodel.GoalViewModel
import com.timeflow.app.ui.viewmodel.GoalUiState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.snapshots.SnapshotStateMap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.compose.runtime.State
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.YearMonth

/**
 * GoalViewModel的模拟实现，用于预览和测试
 * 不继承GoalViewModel，而是创建一个独立类，但提供相同的API
 */
class MockGoalViewModel : ViewModel() {
    // UI状态
    private val _uiState = mutableStateOf<GoalUiState>(GoalUiState.Idle)
    val uiState: State<GoalUiState> = _uiState

    // 目标完成分析数据
    private val _totalCompletedGoals = mutableStateOf(23)
    val totalCompletedGoals: State<Int> = _totalCompletedGoals

    private val _inProgressGoals = mutableStateOf(7)
    val inProgressGoals: State<Int> = _inProgressGoals

    private val _avgCompletionRate = mutableStateOf(0.78f)
    val avgCompletionRate: State<Float> = _avgCompletionRate

    private val _goalsByTimeRange = mutableStateOf<Map<String, Int>>(
        mapOf(
            "Q1" to 6,
            "Q2" to 4,
            "Q3" to 8,
            "Q4" to 5
        )
    )
    val goalsByTimeRange: State<Map<String, Int>> = _goalsByTimeRange

    private val _goalsByDomain = mutableStateOf<Map<String, Int>>(
        mapOf(
            "学习" to 8,
            "工作" to 6,
            "健康" to 5,
            "个人" to 4
        )
    )
    val goalsByDomain: State<Map<String, Int>> = _goalsByDomain

    private val _goalsByDifficulty = mutableStateOf<Map<String, Int>>(
        mapOf(
            "简单" to 7,
            "一般" to 9,
            "中等" to 5,
            "困难" to 2
        )
    )
    val goalsByDifficulty: State<Map<String, Int>> = _goalsByDifficulty

    // 当前目标和子任务 - 测试数据
    private val _currentGoal = mutableStateOf<Goal?>(null)
    val currentGoal: State<Goal?> = _currentGoal
    
    private val _subTasks = mutableStateOf<List<GoalSubTask>>(emptyList())
    val subTasks: State<List<GoalSubTask>> = _subTasks
    
    private val _availableModels = mutableStateOf(listOf<String>())
    val availableModels: State<List<String>> = _availableModels

    // 选择的时间范围
    private val _currentTimeRange = mutableStateOf("年")
    val currentTimeRange: State<String> = _currentTimeRange

    // 模拟加载数据
    fun loadCompletedGoals() {
        _uiState.value = GoalUiState.Loading
        
        // 模拟网络延迟
        viewModelScope.launch {
            delay(800)
            _uiState.value = GoalUiState.Success
        }
    }

    // 模拟根据时间范围加载数据
    fun loadCompletedGoalsByTimeRange(timeRange: String) {
        _uiState.value = GoalUiState.Loading
        _currentTimeRange.value = timeRange
        
        viewModelScope.launch {
            delay(500)
            
            _goalsByTimeRange.value = when (timeRange) {
                "年" -> mapOf(
                    "Q1" to 6,
                    "Q2" to 4,
                    "Q3" to 8,
                    "Q4" to 5
                )
                "季度" -> {
                    val currentQuarter = (LocalDateTime.now().monthValue - 1) / 3 + 1
                    when (currentQuarter) {
                        1 -> mapOf("1月" to 3, "2月" to 2, "3月" to 1)
                        2 -> mapOf("4月" to 2, "5月" to 1, "6月" to 3)
                        3 -> mapOf("7月" to 4, "8月" to 2, "9月" to 2)
                        else -> mapOf("10月" to 2, "11月" to 1, "12月" to 3)
                    }
                }
                "月" -> {
                    mapOf(
                        "第1周" to 2,
                        "第2周" to 1,
                        "第3周" to 3,
                        "第4周" to 2
                    )
                }
                else -> emptyMap()
            }
            
            _uiState.value = GoalUiState.Success
        }
    }
    
    // 额外添加空实现以匹配接口
    fun loadGoalDetail(goalId: String) {
        // 空实现
    }
    
    fun resetAnalysisData() {
        // 空实现
    }
}

/**
 * 模拟的GoalRepository，用于测试
 */
class MockGoalRepository : GoalRepository {
    // 当前已完成的目标
    override suspend fun getCompletedGoalsList(): List<Goal> {
        return emptyList()
    }
    
    // 已完成目标的Flow
    override fun getCompletedGoals(): Flow<List<Goal>> {
        return flowOf(emptyList())
    }
    
    // 按时间维度统计
    override suspend fun getCompletedGoalsByTimeRange(): Map<String, Int> {
        return emptyMap()
    }
    
    // 按月统计已完成目标
    override suspend fun getCompletedGoalsByMonth(): Map<String, Int> {
        return emptyMap()
    }
    
    // 按季度统计已完成目标
    override suspend fun getCompletedGoalsByQuarter(): Map<String, Int> {
        return emptyMap()
    }
    
    // 按年统计已完成目标
    override suspend fun getCompletedGoalsByYear(): Map<String, Int> {
        return emptyMap()
    }
    
    // 按领域统计
    override suspend fun getCompletedGoalsByDomain(): Map<String, Int> {
        return emptyMap()
    }
    
    // 按难度统计
    override suspend fun getCompletedGoalsByDifficulty(): Map<String, Int> {
        return emptyMap()
    }
    
    // 获取进行中的目标数量
    override suspend fun getInProgressGoalsCount(): Int {
        return 0
    }
    
    // 活跃目标列表Flow
    override fun getActiveGoals(): Flow<List<Goal>> {
        return flowOf(emptyList())
    }
    
    // 所有目标Flow
    override fun getAllGoals(): Flow<List<Goal>> {
        return flowOf(emptyList())
    }
    
    // 所有目标列表（suspend版本）
    override suspend fun getAllGoalsList(): List<Goal> {
        return emptyList()
    }

    // 通过状态获取目标
    override fun getGoalsByStatus(status: String): Flow<List<Goal>> {
        return flowOf(emptyList())
    }
    
    // 获取没有AI分解的目标
    override fun getGoalsWithoutAiBreakdown(): Flow<List<Goal>> {
        return flowOf(emptyList())
    }
    
    // 获取需要复习的目标
    override fun getGoalsNeedingReview(): Flow<List<Goal>> {
        return flowOf(emptyList())
    }

    // 按分类获取目标
    override fun getGoalsByCategory(categoryId: String): Flow<List<Goal>> {
        return flowOf(emptyList())
    }

    // 按分类和状态获取目标
    override fun getGoalsByCategoryAndStatus(categoryId: String, status: String): Flow<List<Goal>> {
        return flowOf(emptyList())
    }

    // 获取所有使用过的分类
    override suspend fun getAllUsedCategories(): List<String> {
        return emptyList()
    }
    
    // 获取目标及其子任务
    override suspend fun getGoalWithSubtasksById(goalId: String): RepoGoalWithSubtasks? {
        return null
    }
    
    // 通过ID获取目标
    override suspend fun getGoalById(goalId: String): Goal? {
        return null
    }
    
    // 实现缺失的getGoalSubTasks方法
    override suspend fun getGoalSubTasks(goalId: String): List<GoalSubTask> {
        return emptyList()
    }
    
    // 实现缺失的callEnhancedAiApi方法
    override suspend fun callEnhancedAiApi(
        endpoint: String,
        requestBody: String,
        retryCount: Int,
        useBackupModel: Boolean
    ): AiApiResult {
        // 模拟返回成功的AI响应
        return AiApiResult(
            isSuccess = true,
            data = """
                {
                    "success": true,
                    "data": {
                        "subTasks": [
                            {
                                "title": "模拟子任务1",
                                "description": "这是一个测试用的模拟子任务",
                                "estimatedDurationDays": 3
                            },
                            {
                                "title": "模拟子任务2",
                                "description": "这是另一个测试用的模拟子任务",
                                "estimatedDurationDays": 2
                            }
                        ]
                    }
                }
            """,
            usedBackupModel = useBackupModel
        )
    }
    
    // 实现缺失的callAiApi方法
    override suspend fun callAiApi(endpoint: String, requestBody: String): String {
        return """
            {
                "success": true,
                "data": {
                    "subTasks": [
                        {
                            "title": "模拟子任务1",
                            "description": "这是一个测试用的模拟子任务",
                            "estimatedDurationDays": 3
                        },
                        {
                            "title": "模拟子任务2",
                            "description": "这是另一个测试用的模拟子任务",
                            "estimatedDurationDays": 2
                        }
                    ]
                }
            }
        """.trimIndent()
    }
    
    // 保存目标
    override suspend fun saveGoal(goal: Goal): String {
        return ""
    }
    
    // 更新目标
    override suspend fun updateGoal(goal: Goal) {
        // 空实现
    }
    
    // 删除目标
    override suspend fun deleteGoal(goalId: String) {
        // 空实现
    }
    
    // 保存子任务
    override suspend fun saveSubTask(subTask: GoalSubTask) {
        // 空实现
    }
    
    // 更新子任务
    override suspend fun updateSubTask(subTask: GoalSubTask) {
        // 空实现
    }
    
    // 删除子任务
    override suspend fun deleteSubTask(subTaskId: String) {
        // 空实现
    }
    
    // 获取目标所有子任务
    override suspend fun getSubTasksForGoal(goalId: String): List<GoalSubTask> {
        return emptyList()
    }

    // 获取目标统计数据
    override suspend fun getGoalStats(): GoalStats {
        return GoalStats(0, 0, 0, 0)
    }

    // 获取最近完成的目标
    override suspend fun getRecentCompletedGoals(limit: Int): List<Goal> {
        return emptyList()
    }

    // 获取有AI分析的目标
    override suspend fun getGoalsWithAiAnalysis(limit: Int): List<Goal> {
        return emptyList()
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
fun GoalAnalysisPreview() {
    val mockViewModel = remember { MockGoalViewModel() }
    val navController = rememberNavController()
    
    GoalCompletionAnalysisScreen(
        navController = navController,
        viewModel = mockViewModel
    )
} 