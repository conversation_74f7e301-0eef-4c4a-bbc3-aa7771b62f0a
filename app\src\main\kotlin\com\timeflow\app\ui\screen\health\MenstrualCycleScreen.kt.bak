package com.timeflow.app.ui.screen.health

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontStyle
import com.timeflow.app.R
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import java.time.Instant
import java.time.ZoneId
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import androidx.compose.foundation.Canvas

/**
 * 自定义虚线边框修饰符
 */
fun Modifier.dashedBorder(width: Dp, color: Color, shape: Shape) = drawWithCache {
    val outline = shape.createOutline(size, layoutDirection, this)
    onDrawBehind {
        drawOutline(
            outline = outline,
            color = color,
            style = Stroke(
                width = width.toPx(),
                pathEffect = PathEffect.dashPathEffect(floatArrayOf(3f, 3f), 0f)
            )
        )
    }
}

/**
 * 周期类型枚举 - 完整定义月经周期的四个主要阶段
 */
enum class CycleType {
    PERIOD,             // 月经期 (Menstrual Phase) - 出血阶段
    FOLLICULAR,         // 卵泡期 (Follicular Phase) - 卵泡发育阶段
    OVULATION,          // 排卵期 (Ovulatory Phase) - 排卵日及前后
    LUTEAL,             // 黄体期 (Luteal Phase) - 排卵后至下次月经前
    FERTILE,            // 易孕期 (Fertile Window) - 排卵前后最易受孕时期
    PREDICTED,          // 预测月经期 - 算法预测的下次月经
    PREDICTED_PERIOD,   // 预测月经期 - 仅用虚线显示
    PREDICTED_FOLLICULAR, // 预测卵泡期 - 仅用虚线显示
    PREDICTED_OVULATION,  // 预测排卵期 - 仅用虚线显示
    PREDICTED_LUTEAL,     // 预测黄体期 - 仅用虚线显示
    PREDICTED_FERTILE,    // 预测易孕期 - 仅用虚线显示
    PCOS,               // PCOS症状期 - 多囊卵巢综合征症状期
    NONE                // 未分类 - 默认状态
}

/**
 * 定义周期类型对应的颜色方案 - 使用科学和用户友好的颜色系统
 */
private object CycleColors {
    // 月经周期四个主要阶段的基础色 - 根据用户需求优化后的颜色
    val PERIOD = Color(0xFFE57F9B).copy(alpha = 0.4f)      // 红色系 - 象征月经期
    val FOLLICULAR = Color(0xFF9E68BF).copy(alpha = 0.4f)  // 紫色系 - 象征卵泡发育
    val OVULATION = Color(0xFF9ABF68).copy(alpha = 0.4f)   // 绿色系 - 象征排卵日
    val LUTEAL = Color(0xFFCFC432).copy(alpha = 0.4f)      // 黄色系 - 象征黄体期
    
    // 辅助阶段颜色
    val FERTILE = Color(0xFF90CAF9)     // 蓝色系 - 象征易孕期
    val PREDICTED = Color.Transparent   // 透明 - 预测的月经期只使用边框
    val PCOS = Color(0xFFCE93D8)        // 紫色系 - PCOS特殊标记
    
    // 边框颜色 - 更深的色调增强可视性
    val PERIOD_BORDER = Color.Transparent     // 月经期不使用边框
    val FOLLICULAR_BORDER = Color(0xFF8E24AA) // 卵泡期边框
    val OVULATION_BORDER = Color(0xFF7CB342)  // 排卵期边框
    val LUTEAL_BORDER = Color(0xFFFFB300)     // 黄体期边框
    val FERTILE_BORDER = Color(0xFF1976D2)    // 易孕期边框
    val PREDICTED_BORDER = Color(0xFFD81B60)  // 预测月经期边框
    val PCOS_BORDER = Color(0xFF7B1FA2)       // PCOS边框
    
    // 文字颜色 - 确保高对比度和可读性
    val PERIOD_TEXT = Color(0xFF880E4F)      // 月经期文字
    val FOLLICULAR_TEXT = Color(0xFF4A148C)  // 卵泡期文字
    val OVULATION_TEXT = Color(0xFF33691E)   // 排卵期文字
    val LUTEAL_TEXT = Color(0xFF827717)      // 黄体期文字
    val FERTILE_TEXT = Color(0xFF0D47A1)     // 易孕期文字
    val PREDICTED_TEXT = Color(0xFFD81B60)   // 预测月经期文字
    val PCOS_TEXT = Color(0xFF4A148C)        // PCOS文字
    
    // 其他辅助颜色
    val CLOUD = Color(0xFFEEEEEE)            // 云背景色
    val DROP_LIGHT = Color(0xFFFFCDD2)       // 轻度血滴
    val DROP_DARK = Color(0xFFE57373)        // 重度血滴
    val PAIN_FILL = Color(0xFFFFCDD2)        // 疼痛指示
    val BACKGROUND = Color(0xFFFFF9FA)       // 整体背景色
    
    // 辅助功能 - 获取指定周期类型的颜色
    fun getColorForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD
            CycleType.FOLLICULAR -> FOLLICULAR
            CycleType.OVULATION -> OVULATION
            CycleType.LUTEAL -> LUTEAL
            CycleType.FERTILE -> FERTILE
            CycleType.PREDICTED -> PREDICTED
            CycleType.PREDICTED_PERIOD -> Color.Transparent
            CycleType.PREDICTED_FOLLICULAR -> Color.Transparent
            CycleType.PREDICTED_OVULATION -> Color.Transparent
            CycleType.PREDICTED_LUTEAL -> Color.Transparent
            CycleType.PREDICTED_FERTILE -> Color.Transparent
            CycleType.PCOS -> PCOS
            CycleType.NONE -> Color.Transparent
        }
    }
    
    // 获取周期类型对应的边框颜色
    fun getBorderForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD_BORDER
            CycleType.FOLLICULAR -> FOLLICULAR_BORDER
            CycleType.OVULATION -> OVULATION_BORDER
            CycleType.LUTEAL -> LUTEAL_BORDER
            CycleType.FERTILE -> FERTILE_BORDER
            CycleType.PREDICTED -> PREDICTED_BORDER
            CycleType.PREDICTED_PERIOD -> Color(0xFFE57F9B)
            CycleType.PREDICTED_FOLLICULAR -> Color(0xFF9E68BF)
            CycleType.PREDICTED_OVULATION -> Color(0xFF9ABF68)
            CycleType.PREDICTED_LUTEAL -> Color(0xFFCFC432)
            CycleType.PREDICTED_FERTILE -> Color(0xFF90CAF9)
            CycleType.PCOS -> PCOS_BORDER
            CycleType.NONE -> Color.LightGray
        }
    }
    
    // 获取周期类型对应的文字颜色
    fun getTextColorForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD_TEXT
            CycleType.FOLLICULAR -> FOLLICULAR_TEXT
            CycleType.OVULATION -> OVULATION_TEXT
            CycleType.LUTEAL -> LUTEAL_TEXT
            CycleType.FERTILE -> FERTILE_TEXT
            CycleType.PREDICTED -> PREDICTED_TEXT
            CycleType.PREDICTED_PERIOD -> PERIOD_TEXT
            CycleType.PREDICTED_FOLLICULAR -> FOLLICULAR_TEXT
            CycleType.PREDICTED_OVULATION -> OVULATION_TEXT
            CycleType.PREDICTED_LUTEAL -> LUTEAL_TEXT
            CycleType.PREDICTED_FERTILE -> FERTILE_TEXT
            CycleType.PCOS -> PCOS_TEXT
            CycleType.NONE -> Color.DarkGray
        }
    }
    
    // 判断是否为预测类型
    fun isPredictedType(type: CycleType): Boolean {
        return type == CycleType.PREDICTED ||
               type == CycleType.PREDICTED_PERIOD ||
               type == CycleType.PREDICTED_FOLLICULAR ||
               type == CycleType.PREDICTED_OVULATION ||
               type == CycleType.PREDICTED_LUTEAL ||
               type == CycleType.PREDICTED_FERTILE
    }
}

/**
 * 日期数据类
 */
data class CycleDay(
    val date: LocalDate,
    val type: CycleType,
    val isToday: Boolean = false,
    val isSelected: Boolean = false,
    val dayOfCycle: Int = 0,  // 当前周期的第几天
    val isPcosAffected: Boolean = false // 是否受PCOS影响
)

/**
 * 绘制缓存Canvas - 优化频繁重绘
 * 
 * 注意：onDraw 回调不能包含任何 @Composable 函数的调用，
 * 也不能使用 CompositionLocal 值（如 LocalDensity.current）。
 * 请在调用此函数前先计算所有需要的尺寸值。
 */
@Composable
fun CachedCanvas(
    modifier: Modifier = Modifier,
    onDraw: DrawScope.() -> Unit
) {
    Canvas(
        modifier = modifier.drawWithCache {
            onDrawBehind {
                onDraw()
            }
        }
    ) {
        // Empty canvas body, the actual drawing happens in drawWithCache
    }
}

/**
 * 生理周期页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenstrualCycleScreen(
    onBackClick: () -> Unit,
    viewModel: MenstrualCycleViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 使用SystemBarManager来设置状态栏，确保一致性
    DisposableEffect(Unit) {
        var originalStatusBarColor: Int = 0
        var originalNavigationBarColor: Int = 0
        
        activity?.let { act ->
            val window = act.window
            
            try {
                // 保存原始状态
                originalStatusBarColor = window.statusBarColor
                originalNavigationBarColor = window.navigationBarColor
                
                // 使用SystemBarManager设置不透明的状态栏
                SystemBarManager.forceOpaqueStatusBar(act)
            } catch (e: Exception) {
                Log.e("MenstrualCycleScreen", "设置状态栏出错: ${e.message}")
            }
        }
        
        onDispose {
            activity?.let { act ->
                try {
                    // 恢复原始颜色
                    act.window.statusBarColor = originalStatusBarColor
                    act.window.navigationBarColor = originalNavigationBarColor
                } catch (e: Exception) {
                    Log.e("MenstrualCycleScreen", "恢复状态栏出错: ${e.message}")
                }
            }
        }
    }
    
    // 从ViewModel获取状态 - Flow类型使用collectAsState
    val selectedDate by viewModel.selectedDate.collectAsState()
    val selectedMonth by viewModel.selectedMonth.collectAsState()
    val pcosMode by viewModel.pcosMode.collectAsState()
    val cycleData by viewModel.cycleData.collectAsState(initial = emptyList())
    val selectedDayData by viewModel.selectedDayData.collectAsState()
    val currentCycle by viewModel.currentCycle.collectAsState(initial = null)
    
    // 获取新增的状态
    val isFirstTimeUse by viewModel.isFirstTimeUse.collectAsState()
    val healthDataPermissionGranted by viewModel.healthDataPermissionGranted.collectAsState()
    val isSyncing by viewModel.isSyncing.collectAsState()
    val hasDataConflict by viewModel.hasDataConflict.collectAsState()
    
    // 直接获取非Flow类型的值
    val averageCycleLength = viewModel.getAverageCycleLength()
    val averagePeriodLength = viewModel.getAveragePeriodLength()
    val currentCycleStatusText = viewModel.getCurrentCycleStatusText()
    
    // 为选中日期记录经期的函数
    val recordPeriodStartForSelectedDate = {
        viewModel.recordPeriodStartForDate(selectedDate)
    }
    
    val recordPeriodEndForSelectedDate = {
        viewModel.recordPeriodEndForDate(selectedDate)
    }
    
    // 控制引导对话框显示
    var showTutorial by remember { mutableStateOf(isFirstTimeUse) }
    
    // 控制冲突解决对话框显示
    var showConflictDialog by remember { mutableStateOf(hasDataConflict) }
    
    // 控制详情浮层显示
    var showDetailOverlay by remember { mutableStateOf(false) }
    
    // 长按选中的日期
    var longPressedDate by remember { mutableStateOf<LocalDate?>(null) }
    
    // 更新变量
    var showEditDialog by remember { mutableStateOf(false) }
    var editDialogDate by remember { mutableStateOf(LocalDate.now()) }
    
    // 检测同步状态变化和冲突状态变化
    LaunchedEffect(isSyncing, hasDataConflict) {
        if (!isSyncing && hasDataConflict) {
            showConflictDialog = true
        }
    }
    
    // 首次使用引导对话框
    if (showTutorial) {
        TutorialDialog(
            onDismiss = { showTutorial = false }
        )
    }
    
    // 数据冲突解决对话框
    if (showConflictDialog) {
        ConflictResolutionDialog(
            options = viewModel.getConflictResolutionOptions(),
            onOptionSelected = { option ->
                viewModel.resolveDataConflict(option)
                showConflictDialog = false
            },
            onDismiss = { 
                viewModel.resolveDataConflict(0) // 默认选择第一个选项
                showConflictDialog = false 
            }
        )
    }
    
    // 详情浮层
    if (showDetailOverlay) {
        CycleDayDetailOverlay(
            cycleDay = selectedDayData,
            onDismiss = { showDetailOverlay = false }
        )
    }
    
    // 长按显示的编辑对话框
    if (showEditDialog) {
        CycleRecordEditDialog(
            date = editDialogDate,
            viewModel = viewModel,
            onDismiss = { showEditDialog = false }
        )
    }
    
    Scaffold(
        containerColor = CycleColors.BACKGROUND,
        topBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
                    .height(56.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 返回按钮
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = CycleColors.PERIOD_TEXT
                        )
                    }
                    
                    // BloomCycle 标题
                    Text(
                        text = "BloomCycle",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.PERIOD_TEXT,
                        maxLines = 1,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 同步状态指示器
                    if (isSyncing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = CycleColors.PERIOD_TEXT,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    
                    // PCOS模式开关
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .padding(end = 12.dp)
                            .border(
                                width = 1.5.dp,
                                color = Color(0xFFCAB8D6),
                                shape = RoundedCornerShape(16.dp)
                            )
                            .padding(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        // 可爱小图标
                        Canvas(
                            modifier = Modifier
                                .size(18.dp)
                                .padding(end = 2.dp)
                        ) {
                            // 基础圆形
                            drawCircle(
                                color = Color(0xFFCAB8D6),
                                radius = size.minDimension / 2.5f
                            )
                            
                            // 手绘效果轮廓
                            drawCircle(
                                color = Color(0xFFCAB8D6).copy(alpha = 0.4f),
                                radius = size.minDimension / 2.2f,
                                style = Stroke(
                                    width = 2f,
                                    pathEffect = PathEffect.cornerPathEffect(4f)
                                )
                            )
                            
                            // 小花装饰
                            val flowerCenter = Offset(size.width / 2, size.height / 2)
                            val petalRadius = size.minDimension / 8f
                            
                            for (angle in 0 until 360 step 60) {
                                val angleRad = angle * Math.PI / 180
                                val petalCenter = Offset(
                                    x = flowerCenter.x + (petalRadius * 2.2f * cos(angleRad)).toFloat(),
                                    y = flowerCenter.y + (petalRadius * 2.2f * sin(angleRad)).toFloat()
                                )
                                
                                drawCircle(
                                    color = Color.White,
                                    radius = petalRadius,
                                    center = petalCenter
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(6.dp))
                        
                        Text(
                            text = "PCOS模式",
                            fontSize = 13.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFFCAB8D6),
                            modifier = Modifier.padding(end = 6.dp)
                        )
                        
                        // 自定义开关
                        val switchWidth = 36.dp
                        val switchHeight = 20.dp
                        val thumbSize = 16.dp
                        
                        Box(
                            modifier = Modifier
                                .width(switchWidth)
                                .height(switchHeight)
                                .clip(RoundedCornerShape(switchHeight / 2))
                                .background(
                                    if (pcosMode) Color(0xFFCAB8D6) 
                                    else Color(0xFFCAB8D6).copy(alpha = 0.3f)
                                )
                                .clickable { viewModel.togglePcosMode() }
                                .padding(2.dp)
                        ) {
                            // 使用animateContentSize代替手动计算偏移量
                            Box(
                                modifier = Modifier
                                    .size(thumbSize)
                                    .align(if (pcosMode) Alignment.CenterEnd else Alignment.CenterStart)
                                    .clip(CircleShape)
                                    .background(Color.White)
                                    .border(
                                        width = 0.5.dp,
                                        color = Color(0xFFCAB8D6).copy(alpha = 0.5f),
                                        shape = CircleShape
                                    )
                            )
                        }
                    }
                }
            }
        }
    ) { paddingValues ->
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(CycleColors.BACKGROUND)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            // 添加顶部额外安全区域
            Spacer(modifier = Modifier.height(8.dp))
            
            // 当前周期状态卡片
            CurrentCycleStatusCard(
                selectedDayData = selectedDayData,
                cycleLength = averageCycleLength,
                pcosMode = pcosMode,
                onPeriodStart = recordPeriodStartForSelectedDate,
                onPeriodEnd = recordPeriodEndForSelectedDate
            )
            
            // 月份选择与日历视图
            BloomCalendarView(
                selectedMonth = selectedMonth,
                onPreviousMonth = { viewModel.updateSelectedMonth(selectedMonth.minusMonths(1)) },
                onNextMonth = { viewModel.updateSelectedMonth(selectedMonth.plusMonths(1)) },
                cycleData = cycleData.filter { cycleDay -> 
                    cycleDay.date.month == selectedMonth.month && 
                    cycleDay.date.year == selectedMonth.year 
                },
                selectedDate = selectedDate,
                onDateSelected = { date -> 
                    viewModel.updateSelectedDate(date)
                    // 不再显示详情浮层
                },
                onDateLongPressed = { date ->
                    longPressedDate = date
                    if (viewModel.isValidDateForHistory(date)) {
                        // 显示编辑对话框而不是直接调用函数
                        editDialogDate = date
                        showEditDialog = true
                    } else {
                        // 显示无效日期提示
                        showInvalidDateToast(context)
                    }
                },
                pcosMode = pcosMode
            )
            
            // 医学提示卡片
            MedicalInsightCard(selectedDayData = selectedDayData, pcosMode = pcosMode)
            
            // 症状记录区域
            SymptomRecordSection(
                pcosMode = pcosMode,
                onSymptomRecord = { symptom -> viewModel.recordSymptom(symptom) }
            )
            
            // 底部间距
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 首次使用引导对话框
 */
@Composable
fun TutorialDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "欢迎使用BloomCycle",
                fontWeight = FontWeight.Bold,
                color = CycleColors.PERIOD_TEXT
            )
        },
        text = {
            Column {
                Text(
                    text = "BloomCycle帮助您追踪生理周期，记录症状，预测下一次月经和排卵期。",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "使用指南:",
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 使用指南列表
                listOf(
                    "长按日期记录经期开始/结束",
                    "在日历上查看周期预测",
                    "记录症状以获得更准确的预测"
                ).forEach { instruction ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(vertical = 4.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = instruction,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "开始使用",
                    color = CycleColors.PERIOD_TEXT,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    )
}

/**
 * 冲突解决对话框
 */
@Composable
fun ConflictResolutionDialog(
    options: List<String>,
    onOptionSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "数据冲突",
                fontWeight = FontWeight.Bold,
                color = CycleColors.PERIOD_TEXT
            )
        },
        text = {
            Column {
                Text(
                    text = "检测到本地数据与云端数据存在冲突，请选择解决方案:",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 选项列表
                options.forEachIndexed { index, option ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onOptionSelected(index) }
                            .padding(vertical = 12.dp)
                    ) {
                        RadioButton(
                            selected = false,
                            onClick = { onOptionSelected(index) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = CycleColors.PERIOD_TEXT
                            )
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = option,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "取消",
                    color = CycleColors.PERIOD_TEXT
                )
            }
        }
    )
}

/**
 * 周期日详情浮层
 * 增强版本：支持四个主要周期阶段的详细信息
 */
@Composable
fun CycleDayDetailOverlay(
    cycleDay: CycleDay,
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 日期标题
                Text(
                    text = cycleDay.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.getTextColorForType(cycleDay.type)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 周期类型
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = CycleColors.getColorForType(cycleDay.type),
                                shape = CircleShape
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = when(cycleDay.type) {
                            CycleType.PERIOD -> "月经期"
                            CycleType.FOLLICULAR -> "卵泡期"
                            CycleType.OVULATION -> "排卵期"
                            CycleType.LUTEAL -> "黄体期"
                            CycleType.FERTILE -> "易孕期"
                            CycleType.PREDICTED -> "预测月经期"
                            CycleType.PREDICTED_PERIOD -> "预测月经期"
                            CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期"
                            CycleType.PREDICTED_OVULATION -> "预测排卵期"
                            CycleType.PREDICTED_LUTEAL -> "预测黄体期"
                            CycleType.PREDICTED_FERTILE -> "预测易孕期"
                            CycleType.PCOS -> "PCOS症状期"
                            CycleType.NONE -> "未分类日期"
                        },
                        fontSize = 14.sp,
                        color = Color.DarkGray
                    )
                }
                
                if (cycleDay.dayOfCycle > 0 || cycleDay.type == CycleType.PERIOD) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 确保月经第一天显示为"周期第1天"
                    val displayDayOfCycle = if (cycleDay.dayOfCycle == 0) 1 else cycleDay.dayOfCycle
                    
                    Text(
                        text = "周期第${displayDayOfCycle}天",
                        fontSize = 14.sp,
                        color = Color.DarkGray
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 阶段描述
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = CycleColors.getColorForType(cycleDay.type).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = CycleColors.getBorderForType(cycleDay.type).copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Text(
                        text = getCyclePhaseBriefDescription(cycleDay.type),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = CycleColors.getTextColorForType(cycleDay.type)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 详细信息
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFFF9F9F9),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Text(
                        text = getCyclePhaseDetailedInfo(cycleDay.type),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = Color.DarkGray
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.End),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = CycleColors.getBorderForType(cycleDay.type)
                    )
                ) {
                    Text("关闭")
                }
            }
        }
    }
}

/**
 * 获取周期阶段简要描述
 */
fun getCyclePhaseBriefDescription(type: CycleType): String {
    return when(type) {
        CycleType.PERIOD -> "月经期是经血从子宫内膜脱落并从阴道排出的时期。"
        CycleType.FOLLICULAR -> "卵泡期是卵泡在卵巢中生长发育的时期，雌激素水平开始上升。"
        CycleType.OVULATION -> "排卵期是成熟卵子从卵巢释放的时期，此时最易受孕。"
        CycleType.LUTEAL -> "黄体期是排卵后卵泡转变为黄体的时期，分泌孕激素为受精卵着床做准备。"
        CycleType.FERTILE -> "排卵前后是受孕几率最高的时期，包括排卵日及其前5天。"
        CycleType.PREDICTED -> "预测月经期是基于您的历史数据算法预测的下次月经开始时间。"
        CycleType.PREDICTED_PERIOD -> "预测月经期是基于您的历史数据算法预测的下次月经开始时间。"
        CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期是基于您历史数据预测的卵泡发育阶段。"
        CycleType.PREDICTED_OVULATION -> "预测排卵期是基于您历史数据预测的排卵时间，通常在周期中段。"
        CycleType.PREDICTED_LUTEAL -> "预测黄体期是基于您历史数据预测的排卵后阶段。"
        CycleType.PREDICTED_FERTILE -> "排卵前后是基于您历史数据预测的受孕几率较高时期。"
        CycleType.PCOS -> "PCOS症状期可能出现多囊卵巢综合征相关症状，如不规则出血、痤疮或多毛等。"
        CycleType.NONE -> "这是月经周期中的一般日期。"
    }
}

/**
 * 获取周期阶段详细信息
 */
fun getCyclePhaseDetailedInfo(type: CycleType): String {
    return when(type) {
        CycleType.PERIOD -> "月经期建议:\n• 保持充分休息和睡眠\n• 多喝温水补充流失液体\n• 避免剧烈运动和寒冷刺激\n• 如有剧烈疼痛，可咨询医生使用止痛药\n• 保持会阴部清洁，勤换卫生用品"
        CycleType.FOLLICULAR -> "卵泡期特点:\n• 雌激素水平开始上升\n• 子宫内膜开始重建\n• 情绪通常较为积极\n• 能量水平逐渐提高\n• 适合开始运动和活动计划"
        CycleType.OVULATION -> "排卵期提示:\n• 这是受孕几率最高的时期\n• 可能会感觉到轻微腹痛(排卵痛)\n• 宫颈粘液变得更加透明和有弹性\n• 基础体温会略有上升\n• 性欲可能会增强"
        CycleType.LUTEAL -> "黄体期变化:\n• 孕激素水平上升\n• 可能出现轻微乳房胀痛\n• 情绪可能波动\n• 能量水平逐渐下降\n• 如果卵子未受精，黄体会萎缩，激素水平下降，为下次月经做准备"
        CycleType.FERTILE -> "排卵前后注意事项:\n• 这是受孕几率最高的时期\n• 如希望避孕，应采取额外的避孕措施\n• 如希望怀孕，这是最佳时机\n• 排卵前性交受孕几率更高\n• 宫颈粘液变化可帮助判断排卵时间"
        CycleType.PREDICTED -> "预测期提示:\n• 这是基于您历史周期数据的预测结果\n• 可能会有1-2天的误差\n• 提前做好经期准备\n• 如实际月经未按预期到来，可能需要更新记录或咨询医生"
        CycleType.PREDICTED_PERIOD -> "预测月经期提示:\n• 这是基于您历史数据预测的月经期\n• 可能会有1-2天的误差\n• 提前准备卫生用品\n• 如有必要，可准备止痛药物\n• 请记得及时记录实际经期开始时间"
        CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期提示:\n• 这是基于您历史数据预测的卵泡发育期\n• 雌激素水平可能开始上升\n• 此阶段通常精力较为充沛\n• 适合开始新的运动或项目\n• 实际时间可能与预测有所偏差"
        CycleType.PREDICTED_OVULATION -> "预测排卵期提示:\n• 这是基于您历史数据预测的排卵日期\n• 如计划怀孕，这是较佳时机\n• 如避孕，应注意采取额外措施\n• 可能会有轻微腹部不适\n• 请关注身体其他排卵信号"
        CycleType.PREDICTED_LUTEAL -> "预测黄体期提示:\n• 这是基于您历史数据预测的黄体期\n• 孕激素水平可能上升\n• 可能会出现情绪波动或轻微不适\n• 注意休息，避免过度疲劳\n• 关注身体变化，保持记录"
        CycleType.PREDICTED_FERTILE -> "预测排卵前后提示:\n• 这是基于您历史数据预测的生育力较高时期\n• 如希望避孕，请采取可靠措施\n• 如计划怀孕，可增加性生活频率\n• 关注身体变化和宫颈粘液\n• 实际时间可能与预测有所偏差"
        CycleType.PCOS -> "PCOS注意事项:\n• 保持健康生活方式\n• 控制体重有助于减轻症状\n• 规律运动有助于改善胰岛素敏感性\n• 遵循医生建议的治疗方案\n• 需要时寻求专业医疗帮助"
        CycleType.NONE -> "日常健康建议:\n• 保持均衡饮食\n• 规律作息和睡眠\n• 适量运动\n• 注意压力管理\n• 定期进行妇科检查"
    }
}

/**
 * 打开日期上下文菜单（长按日期）
 */
private fun openDateContextMenu(
    date: LocalDate,
    onPeriodStart: () -> Unit,
    onPeriodEnd: () -> Unit
) {
    // 注意：这个函数在实际应用中应该使用平台特定的上下文菜单
    // 在这里我们简化为直接调用操作函数
    onPeriodStart()
    
    // 在实际实现中，我们会显示带有两个选项的菜单：
    // 1. 记录经期开始
    // 2. 记录经期结束
    // 然后根据用户选择调用相应的操作
}

/**
 * 显示无效日期提示
 */
private fun showInvalidDateToast(context: Context) {
    val today = LocalDate.now()
    val sixMonthsAgo = today.minusMonths(6).withDayOfMonth(1)
    val dateRange = "${sixMonthsAgo.format(DateTimeFormatter.ofPattern("yyyy年MM月"))}至${today.format(DateTimeFormatter.ofPattern("yyyy年MM月"))}"
    
    Toast.makeText(
        context, 
        "只能编辑${dateRange}范围内的日期记录", 
        Toast.LENGTH_LONG
    ).show()
}

/**
 * 当前周期状态卡片
 */
@Composable
fun CurrentCycleStatusCard(
    selectedDayData: CycleDay,
    cycleLength: Int,
    pcosMode: Boolean,
    onPeriodStart: () -> Unit = {},
    onPeriodEnd: () -> Unit = {}
) {
    // 添加日志输出，帮助调试
    Log.d("CycleDebug", "选中日期: ${selectedDayData.date}, 类型: ${selectedDayData.type}, 周期天数: ${selectedDayData.dayOfCycle}")
    
    // 计算周期阶段文字 - 确保月经第一天显示为"第1天"而非"第0天"
    val displayDayOfCycle = if (selectedDayData.dayOfCycle == 0) 1 else selectedDayData.dayOfCycle
    
    // 确保文本正确显示当前选择的日期的周期天数
    val phaseText = when (selectedDayData.type) {
        CycleType.PERIOD -> "第${displayDayOfCycle}天 · 月经期"
        CycleType.FOLLICULAR -> "第${displayDayOfCycle}天 · 卵泡期"
        CycleType.OVULATION -> "第${displayDayOfCycle}天 · 排卵期"
        CycleType.LUTEAL -> "第${displayDayOfCycle}天 · 黄体期"
        CycleType.FERTILE -> "第${displayDayOfCycle}天 · 排卵前期"
        CycleType.PREDICTED -> "第${displayDayOfCycle}天 · 预测月经期"
        CycleType.PREDICTED_PERIOD -> "第${displayDayOfCycle}天 · 预测月经期"
        CycleType.PREDICTED_FOLLICULAR -> "第${displayDayOfCycle}天 · 预测卵泡期"
        CycleType.PREDICTED_OVULATION -> "第${displayDayOfCycle}天 · 预测排卵期"
        CycleType.PREDICTED_LUTEAL -> "第${displayDayOfCycle}天 · 预测黄体期"
        CycleType.PREDICTED_FERTILE -> "第${displayDayOfCycle}天 · 预测排卵前期"
        CycleType.PCOS -> "第${displayDayOfCycle}天 · PCOS症状期"
        CycleType.NONE -> if (displayDayOfCycle < 14) "第${displayDayOfCycle}天 · 卵泡期" else "第${displayDayOfCycle}天 · 黄体期"
    }
    
    // 计算下次周期天数 - 使用调整后的周期天数
    val adjustedDayOfCycle = if (selectedDayData.dayOfCycle < 1) 1 else selectedDayData.dayOfCycle
    val daysUntilNextPeriod = cycleLength - adjustedDayOfCycle + 1
    
    // 控制弹出菜单的状态
    var showMenu by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部标题和图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "当前周期",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.PERIOD_TEXT
                    )
                    
                    // 使用更明确的文案，确保用户了解显示的是什么
                    val endText = when {
                        selectedDayData.type == CycleType.PERIOD -> " · 预测${daysUntilNextPeriod}天后结束" 
                        selectedDayData.type == CycleType.PREDICTED_PERIOD -> " · 预测周期"
                        else -> ""
                    }
                    
                    Text(
                        text = "$phaseText$endText",
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                // 根据周期类型显示按钮
                if (selectedDayData.type == CycleType.PERIOD) {
                    Button(
                        onClick = onPeriodEnd,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = CycleColors.PERIOD_TEXT
                        ),
                        modifier = Modifier.height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text("记录经期结束", color = Color.White, fontSize = 12.sp)
                    }
                } else {
                    Button(
                        onClick = onPeriodStart,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = CycleColors.PERIOD
                        ),
                        modifier = Modifier.height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text("记录经期开始", color = Color.White, fontSize = 12.sp)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 关键日期信息卡片
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 上次经期开始日期
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = CycleColors.PERIOD.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = CycleColors.PERIOD.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = null,
                                tint = CycleColors.PERIOD_TEXT,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "上次开始",
                                fontSize = 12.sp,
                                color = CycleColors.PERIOD_TEXT
                            )
                        }
                        Text(
                            text = "5月12日",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
                
                // 预测排卵日
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = if (pcosMode) CycleColors.PCOS.copy(alpha = 0.2f) 
                                    else CycleColors.OVULATION.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = if (pcosMode) CycleColors.PCOS.copy(alpha = 0.3f) 
                                    else CycleColors.OVULATION.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.FiberManualRecord,
                                contentDescription = null,
                                tint = if (pcosMode) CycleColors.PCOS_TEXT else CycleColors.OVULATION_TEXT,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "预测排卵日",
                                fontSize = 12.sp,
                                color = if (pcosMode) CycleColors.PCOS_TEXT else CycleColors.OVULATION_TEXT
                            )
                        }
                        Text(
                            text = "5月22日",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 医学提示信息
            if (pcosMode || selectedDayData.type == CycleType.FERTILE || selectedDayData.type == CycleType.PERIOD) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = when {
                                pcosMode -> CycleColors.PCOS_BORDER.copy(alpha = 0.3f)
                                selectedDayData.type == CycleType.PERIOD -> CycleColors.PERIOD_BORDER.copy(alpha = 0.3f)
                                selectedDayData.type == CycleType.FERTILE -> CycleColors.FERTILE_BORDER.copy(alpha = 0.3f)
                                else -> Color.LightGray.copy(alpha = 0.3f)
                            },
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = when {
                                pcosMode -> CycleColors.PCOS_TEXT
                                selectedDayData.type == CycleType.PERIOD -> CycleColors.PERIOD_TEXT
                                selectedDayData.type == CycleType.FERTILE -> CycleColors.FERTILE_TEXT
                                else -> Color.Gray
                            },
                            modifier = Modifier.size(16.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = when {
                                pcosMode -> "根据您的记录，本次周期预测排卵期为5月22日。生育窗口期通常为排卵前5天至排卵后1天。"
                                selectedDayData.type == CycleType.PERIOD -> "月经期间注意保暖，避免过度疲劳和受凉，保持充足睡眠和液体摄入。"
                                selectedDayData.type == CycleType.FERTILE -> "排卵前期是受孕几率较高的阶段，卵泡发育成熟准备排卵。观察宫颈粘液变化可帮助判断排卵时间。"
                                else -> "月经周期的这一阶段是相对安全期，但也不是绝对安全的避孕时段。"
                            },
                            fontSize = 12.sp,
                            color = Color.DarkGray,
                            lineHeight = 16.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 顶部周期信息头部
 */
@Composable
fun CycleInfoHeader(
    selectedDayData: CycleDay,
    cycleLength: Int,
    floatOffset: Float
) {
    // 计算周期阶段文字 - 确保月经第一天显示为"第1天"
    val displayDayOfCycle = if (selectedDayData.dayOfCycle == 0) 1 else selectedDayData.dayOfCycle
    
    val phaseText = when {
        selectedDayData.type == CycleType.PERIOD -> "🌸 第${displayDayOfCycle}天 · 月经期"
        selectedDayData.type == CycleType.FERTILE -> "🌺 第${displayDayOfCycle}天 · 排卵前期"
        selectedDayData.type == CycleType.PREDICTED -> "🌷 第${displayDayOfCycle}天 · 预测月经期"
        else -> if (displayDayOfCycle < 14) "🌿 第${displayDayOfCycle}天 · 卵泡期" else "🌿 第${displayDayOfCycle}天 · 黄体期"
    }
    
    // 计算下次周期天数 - 使用调整后的周期天数
    val adjustedDayOfCycle = if (selectedDayData.dayOfCycle < 1) 1 else selectedDayData.dayOfCycle
    val daysUntilNextPeriod = cycleLength - adjustedDayOfCycle + 1
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 周期阶段进度指示器
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(32.dp)
                    .padding(bottom = 8.dp)
            ) {
                // 针织毛线进度条（渐变背景）
                // 使用调整后的进度值确保不为0
                val progress = adjustedDayOfCycle.toFloat() / cycleLength
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFFEEEEEE))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(progress)
                            .fillMaxHeight()
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFFF48FB1), // 粉色
                                        Color(0xFFFFAB91), // 橙色
                                        Color(0xFFFFF3E0)  // 米白色
                                    )
                                )
                            )
                    )
                    
                    // Get density in the composable context before drawing
                    val density = LocalDensity.current
                    val strokeWidthPx = with(density) { 1.dp.toPx() }
                    
                    // 针织毛线效果 - 使用绘制缓存优化
                    CachedCanvas(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 1.dp)
                    ) {
                        val width = size.width * progress
                        val height = size.height
                        
                        // 绘制针织线条效果
                        val pathEffect = PathEffect.dashPathEffect(floatArrayOf(3f, 3f), 0f)
                        for (y in 0..height.toInt() step 3) {
                            drawLine(
                                color = Color(0xFFEEEEEE),
                                start = Offset(0f, y.toFloat()),
                                end = Offset(width, y.toFloat()),
                                strokeWidth = strokeWidthPx,
                                pathEffect = pathEffect
                            )
                        }
                    }
                }
                
                // 当前周期文字
                Text(
                    text = phaseText,
                    color = Color(0xFF554A60),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.align(Alignment.TopEnd)
                )
            }
            
            // 预测提示
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                // 云朵雨伞图标（带浮动动画）
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .offset(y = floatOffset.dp)
                ) {
                    // 绘制云朵
                    CachedCanvas(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        val cloudColor = CycleColors.CLOUD
                        val cloudShadowColor = Color(0xFF90CAF9)
                        
                        // 绘制主云朵
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.3f,
                            center = Offset(size.width * 0.3f, size.height * 0.5f)
                        )
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.25f,
                            center = Offset(size.width * 0.6f, size.height * 0.45f)
                        )
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.2f,
                            center = Offset(size.width * 0.75f, size.height * 0.55f)
                        )
                        
                        // 雨滴
                        val dropColor = CycleColors.DROP_DARK
                        drawCircle(
                            color = dropColor,
                            radius = size.width * 0.08f,
                            center = Offset(size.width * 0.3f, size.height * 0.8f)
                        )
                        drawCircle(
                            color = dropColor,
                            radius = size.width * 0.08f,
                            center = Offset(size.width * 0.6f, size.height * 0.85f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 预测文字
                Text(
                    text = "下次周期约在 $daysUntilNextPeriod 天后",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

/**
 * 月份选择器
 */
@Composable
fun MonthSelector(
    selectedMonth: YearMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val formatter = DateTimeFormatter.ofPattern("yyyy年M月")
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowLeft,
                contentDescription = "上个月",
                tint = Color(0xFF554A60)
            )
        }
        
        Text(
            text = selectedMonth.format(formatter),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(horizontal = 20.dp)
        )
        
        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "下个月",
                tint = Color(0xFF554A60)
            )
        }
    }
}

/**
 * BloomCalendarView - 新的美观日历视图
 */
@Composable
fun BloomCalendarView(
    selectedMonth: YearMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    cycleData: List<CycleDay>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDateLongPressed: (LocalDate) -> Unit,
    pcosMode: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 月份导航
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 月份标题
                Text(
                    text = selectedMonth.format(DateTimeFormatter.ofPattern("yyyy年M月")),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.PERIOD_TEXT,
                    modifier = Modifier.padding(horizontal = 8.dp)
                )
                
                // 月份导航按钮
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 上个月按钮
                    IconButton(
                        onClick = onPreviousMonth,
                        modifier = Modifier
                            .size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowLeft,
                            contentDescription = "上个月",
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    
                    // 下个月按钮
                    IconButton(
                        onClick = onNextMonth,
                        modifier = Modifier
                            .size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowRight,
                            contentDescription = "下个月",
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 星期标题
            Row(
                modifier = Modifier.fillMaxWidth()
            ) {
                listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = day,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = if (day == "日" || day == "六") CycleColors.PERIOD_TEXT else Color.DarkGray,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 日期网格 - 基于当前月份计算
            val firstDayOfMonth = selectedMonth.atDay(1)
            val daysInMonth = selectedMonth.lengthOfMonth()
            val startDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 调整为周日为0
            
            // 创建显示天数的网格
            val totalDays = startDayOfWeek + daysInMonth
            val totalWeeks = (totalDays + 6) / 7 // 计算总周数，向上取整
            
            // 按周显示日期
            for (week in 0 until totalWeeks) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    for (day in 0 until 7) {
                        val dayNumber = week * 7 + day - startDayOfWeek + 1
                        
                        Box(
                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            if (dayNumber in 1..daysInMonth) {
                                // 当前月的天
                                val date = firstDayOfMonth.plusDays((dayNumber - 1).toLong())
                                val cycleDay = cycleData.find { it.date == date } ?: 
                                    // 如果找不到对应的CycleDay数据，创建一个默认的
                                    CycleDay(
                                        date = date,
                                        type = CycleType.NONE,
                                        isToday = date == LocalDate.now(),
                                        isSelected = date == selectedDate,
                                        dayOfCycle = 0
                                    )
                                
                                BloomDayCell(
                                    cycleDay = cycleDay,
                                    isSelected = date == selectedDate,
                                    onDateSelected = onDateSelected,
                                    onDateLongPressed = onDateLongPressed,
                                    pcosMode = pcosMode
                                )
                            } else {
                                // 前一个月或下一个月的天 - 空占位符
                                EmptyDayCell(dayNumber)
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 图例行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp, horizontal = 4.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 月经期图例
                LegendItem(
                    color = CycleColors.PERIOD_TEXT,
                    backgroundColor = CycleColors.PERIOD,
                    text = "月经期"
                )
                
                // 卵泡期图例
                LegendItem(
                    color = CycleColors.FOLLICULAR_TEXT,
                    backgroundColor = CycleColors.FOLLICULAR,
                    text = "卵泡期"
                )
                
                // 排卵期图例
                LegendItem(
                    color = CycleColors.OVULATION_TEXT,
                    backgroundColor = CycleColors.OVULATION,
                    text = "排卵期"
                )
                
                // 黄体期图例
                LegendItem(
                    color = CycleColors.LUTEAL_TEXT,
                    backgroundColor = CycleColors.LUTEAL,
                    text = "黄体期"
                )
            }
            
            // 第二行图例 - 辅助状态
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp, horizontal = 4.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 预测月经期图例
                LegendItem(
                    color = CycleColors.PREDICTED_TEXT,
                    backgroundColor = CycleColors.PREDICTED,
                    text = "预测经期",
                    useDashedBorder = true
                )
                
                // PCOS图例 - 仅在PCOS模式下显示
                if (pcosMode) {
                    LegendItem(
                        color = CycleColors.PCOS_TEXT,
                        backgroundColor = CycleColors.PCOS,
                        text = "PCOS症状期"
                    )
                }
            }
        }
    }
}

/**
 * EmptyDayCell - 非当前月日期单元格
 */
@Composable
fun EmptyDayCell(dayNumber: Int) {
    if (dayNumber <= 0) {
        // Completely empty cell for invalid days
        Box(
            modifier = Modifier
                .size(40.dp)
                .padding(2.dp)
        ) {
            // Empty content
        }
        return
    }
    
    Box(
        modifier = Modifier
            .size(40.dp) // 固定大小确保一致性
            .padding(2.dp)
            .clip(CircleShape)
            .background(Color(0xFFF9F9F9))
            .clickable { /* 可以在这里添加处理相邻月份日期的逻辑 */ }
    ) {
        Text(
            text = dayNumber.toString(),
            fontSize = 12.sp,
            color = Color.LightGray,
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * BloomDayCell - 新的日期单元格
 * 增强版本：使用新的颜色系统和辅助方法，提高可访问性和可维护性
 */
@Composable
fun BloomDayCell(
    cycleDay: CycleDay,
    isSelected: Boolean,
    onDateSelected: (LocalDate) -> Unit,
    onDateLongPressed: (LocalDate) -> Unit,
    pcosMode: Boolean
) {
    // 添加日志输出，帮助调试
    Log.d("CycleDebug", "日期: ${cycleDay.date}, 类型: ${cycleDay.type}, 周期天数: ${cycleDay.dayOfCycle}")
    
    // 根据PCOS模式和周期类型决定显示类型
    val displayType = when {
        // 如果开启了PCOS模式且日期受PCOS影响
        pcosMode && cycleDay.isPcosAffected -> CycleType.PCOS
        // 如果开启了PCOS模式且日期原本就是PCOS类型
        pcosMode && cycleDay.type == CycleType.PCOS -> CycleType.PCOS
        // 否则使用原始类型
        else -> cycleDay.type
    }
    
    // 使用辅助方法获取颜色，提高代码可维护性
    val backgroundColor = CycleColors.getColorForType(displayType)
    val textColor = CycleColors.getTextColorForType(displayType)
    val borderColor = CycleColors.getBorderForType(displayType)
    
    // 检测长按手势
    val context = LocalContext.current
    val haptic = LocalHapticFeedback.current
    
    // 长按检测状态
    var isLongPressed by remember { mutableStateOf(false) }
    
    // 内容描述 - 提高可访问性
    val contentDescription = remember(displayType, cycleDay.date) {
        val dateStr = cycleDay.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
        val typeStr = when(displayType) {
            CycleType.PERIOD, CycleType.PREDICTED_PERIOD -> "月经期"
            CycleType.FOLLICULAR, CycleType.PREDICTED_FOLLICULAR -> "卵泡期"
            CycleType.OVULATION, CycleType.PREDICTED_OVULATION -> "排卵期"
            CycleType.LUTEAL, CycleType.PREDICTED_LUTEAL -> "黄体期"
            CycleType.FERTILE, CycleType.PREDICTED_FERTILE -> "易孕期"
            CycleType.PREDICTED -> "预测月经期"
            CycleType.PCOS -> "PCOS症状期"
            CycleType.NONE -> ""
        }
        val predictedStr = if (CycleColors.isPredictedType(displayType)) "预测" else ""
        if (typeStr.isNotEmpty()) "$dateStr $predictedStr$typeStr" else dateStr
    }
    
    // 判断是否为预测类型
    val isPredictedType = CycleColors.isPredictedType(displayType)
    
    // 根据日期类型决定边框样式
    val borderModifier = when {
        isSelected -> {
            // 选中状态，使用粗边框
            Modifier.border(
                width = 2.dp,
                color = if (displayType == CycleType.PERIOD) Color(0xFFD32F2F) else borderColor,
                shape = CircleShape
            )
        }
        cycleDay.isToday -> {
            // 今天，使用细边框
            Modifier.border(
                width = 1.dp,
                color = if (displayType == CycleType.PERIOD) Color(0xFFD32F2F) else borderColor,
                shape = CircleShape
            )
        }
        isPredictedType || displayType == CycleType.PREDICTED -> {
            // 预测类型使用虚线边框
            Modifier.dashedBorder(
                width = 1.dp,
                color = borderColor,
                shape = CircleShape
            )
        }
        else -> Modifier
    }
    
    // 圆形日期单元格 - 优化性能和可访问性
    Box(
        modifier = Modifier
            .size(36.dp)
            .clip(CircleShape)
            .then(
                // 只有当displayType不是NONE和不是预测类型时才添加背景色
                if (displayType != CycleType.NONE && !isPredictedType && displayType != CycleType.PREDICTED) {
                    Modifier.background(backgroundColor)
                } else Modifier
            )
            .then(borderModifier)
            // 同时处理点击和长按
            .pointerInput(cycleDay.date) {
                detectTapGestures(
                    onTap = { onDateSelected(cycleDay.date) },
                    onLongPress = { 
                        Log.d("CycleDebug", "长按触发: ${cycleDay.date}")
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        isLongPressed = true
                        onDateLongPressed(cycleDay.date) 
                    }
                )
            }
            .semantics {
                // 添加语义属性，提高可访问性
                this.contentDescription = contentDescription
                this.role = androidx.compose.ui.semantics.Role.Button
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = cycleDay.date.dayOfMonth.toString(),
            fontSize = 14.sp,
            fontWeight = if (cycleDay.isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
            color = textColor,
            textAlign = TextAlign.Center
        )
        
        // 如果当前日期被选中，添加更明显的选中指示
        if (isSelected) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .border(
                        width = 2.dp,
                        color = Color(0xFFD32F2F),
                        shape = CircleShape
                    )
            )
        }
    }
    
    // 如果长按被触发，则添加调试日志
    LaunchedEffect(isLongPressed) {
        if (isLongPressed) {
            Log.d("CycleDebug", "长按效果已触发: ${cycleDay.date}")
            isLongPressed = false // 重置状态
        }
    }
}

/**
 * 花朵图标 - 表示易孕期
 */
@Composable
fun FlowerIcon(
    modifier: Modifier = Modifier,
    contentColor: Color = Color(0xFF2196F3)
) {
    CachedCanvas(modifier = modifier) {
        val center = Offset(size.width / 2, size.height / 2)
        val petalRadius = size.width * 0.25f
        val centerRadius = size.width * 0.15f
        
        // 绘制花瓣
        for (i in 0 until 6) {
            val angle = i * 60f * (PI.toFloat() / 180f)
            val petalCenter = Offset(
                center.x + cos(angle) * petalRadius,
                center.y + sin(angle) * petalRadius
            )
            
            drawCircle(
                color = contentColor.copy(alpha = 0.6f),
                radius = petalRadius * 0.7f,
                center = petalCenter
            )
        }
        
        // 绘制花心
        drawCircle(
            color = contentColor,
            radius = centerRadius,
            center = center
        )
        
        // 绘制花蕊点缀
        drawCircle(
            color = Color.White,
            radius = centerRadius * 0.4f,
            center = Offset(center.x + centerRadius * 0.3f, center.y - centerRadius * 0.3f)
        )
    }
}

/**
 * LegendItem - 图例项
 */
@Composable
fun LegendItem(
    color: Color,
    backgroundColor: Color,
    text: String,
    useDashedBorder: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .then(
                    if (backgroundColor != Color.Transparent) {
                        Modifier.background(backgroundColor)
                    } else Modifier
                )
                .then(
                    if (useDashedBorder) {
                        Modifier.dashedBorder(
                            width = 0.5.dp,
                            color = color,
                            shape = CircleShape
                        )
                    } else if (color != Color.Transparent) {
                        Modifier.border(
                            width = 0.5.dp,
                            color = color,
                            shape = CircleShape
                        )
                    } else Modifier
                )
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = text,
            fontSize = 12.sp,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 周期视图
 */
@Composable
fun CycleView(
    selectedDayData: CycleDay,
    cycleLength: Int
) {
    val cycleRange = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("M月d日"))}-${selectedDayData.date.plusDays((cycleLength - selectedDayData.dayOfCycle).toLong()).format(DateTimeFormatter.ofPattern("M月d日"))}"
    
    // 添加density引用
    val density = LocalDensity.current
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = cycleRange,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 预先计算所有需要的px值
        val strokeWidthPx = with(density) { 30.dp.toPx() }
        val paddingPx = with(density) { 20.dp.toPx() }
        val dotRadius15Px = with(density) { 15.dp.toPx() }
        val dotRadius12Px = with(density) { 12.dp.toPx() }
        
        // 周期环形视图
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.size(280.dp)
        ) {
            // 环形背景
            CachedCanvas(modifier = Modifier.fillMaxSize()) {
                // 使用预先计算的px值
                val circleRadius = size.minDimension / 2 - paddingPx
                
                // 绘制外圈灰色环
                drawCircle(
                    color = Color(0xFFEEEEEE),
                    radius = circleRadius,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 计算当前日期在周期中的角度
                val dayProgress = selectedDayData.dayOfCycle.toFloat() / cycleLength.toFloat()
                val sweepAngle = dayProgress * 360f
                
                // 绘制经期部分（红色，约占周期的前20%）
                drawArc(
                    color = Color(0xFFE91E63),
                    startAngle = -90f,
                    sweepAngle = 0.2f * 360f,
                    useCenter = false,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 绘制易孕期部分（蓝色，约占周期的中间30%）
                drawArc(
                    color = Color(0xFF2196F3),
                    startAngle = -90f + 0.5f * 360f,
                    sweepAngle = 0.3f * 360f,
                    useCenter = false,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 当前日期指示器
                val angle = -90f + dayProgress * 360f
                val x = center.x + cos(angle * PI.toFloat() / 180f) * circleRadius
                val y = center.y + sin(angle * PI.toFloat() / 180f) * circleRadius
                
                drawCircle(
                    color = Color.White,
                    radius = dotRadius15Px,
                    center = Offset(x, y)
                )
                
                drawCircle(
                    color = Color(0xFFE91E63),
                    radius = dotRadius12Px,
                    center = Offset(x, y)
                )
            }
            
            // 中心信息 - 移动到Canvas外部
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth(0.7f)
            ) {
                Text(
                    text = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("M月d日"))} ${getDayOfWeek(selectedDayData.date)}",
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
                
                val statusText = when (selectedDayData.type) {
                    CycleType.PERIOD -> "经期第 ${selectedDayData.dayOfCycle} 天"
                    CycleType.PREDICTED -> "预测经期第 ${selectedDayData.dayOfCycle - cycleLength} 天"
                    CycleType.FERTILE -> "易孕期"
                    else -> "周期第 ${selectedDayData.dayOfCycle} 天"
                }
                
                Text(
                    text = statusText,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF554A60),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Text(
                    text = "周期共 ${cycleLength} 天",
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

/**
 * 当日周期信息
 */
@Composable
fun CurrentDayInfo(
    selectedDayData: CycleDay
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 日期和星期
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .background(
                            color = Color(0xFFFCE4EC),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = selectedDayData.date.dayOfMonth.toString(),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFE91E63)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("yyyy年M月"))} ${getDayOfWeek(selectedDayData.date)}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF554A60)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 周期状态
            val statusText = when (selectedDayData.type) {
                CycleType.PERIOD -> "经期第 ${selectedDayData.dayOfCycle} 天"
                CycleType.PREDICTED -> "预测经期第 1 天"
                CycleType.FERTILE -> "易孕期"
                else -> "周期第 ${selectedDayData.dayOfCycle} 天"
            }
            
            val statusColor = when (selectedDayData.type) {
                CycleType.PERIOD -> Color(0xFFE91E63)
                CycleType.PREDICTED -> Color(0xFFE91E63).copy(alpha = 0.7f)
                CycleType.FERTILE -> Color(0xFF2196F3)
                else -> Color(0xFF554A60)
            }
            
            // 状态图标和文本
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                when (selectedDayData.type) {
                    CycleType.PERIOD -> {
                        BleedingCloudIcon(
                            modifier = Modifier.size(40.dp),
                            level = 2
                        )
                    }
                    CycleType.PREDICTED -> {
                        BleedingCloudIcon(
                            modifier = Modifier.size(40.dp),
                            level = 1,
                            isPredicted = true
                        )
                    }
                    CycleType.FERTILE -> {
                        // 使用另一种方式创建FlowerIcon
                        Box(
                            modifier = Modifier.size(40.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Filled.BlurOn,
                                contentDescription = null,
                                tint = Color(0xFF2196F3),
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                    else -> {
                        Box(
                            modifier = Modifier.size(40.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.CheckCircle,
                                contentDescription = null,
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = statusText,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = statusColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 周期提示信息
            val tipText = when (selectedDayData.type) {
                CycleType.PERIOD -> "经期期间注意保暖和休息，避免剧烈运动。"
                CycleType.PREDICTED -> "根据您的平均周期，这天可能是下次经期开始的日子。"
                CycleType.FERTILE -> "这是您的易孕期，此时排卵的可能性较高。"
                else -> "今天是您周期的正常日子。"
            }
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = when (selectedDayData.type) {
                        CycleType.PERIOD -> Color(0xFFFCE4EC)
                        CycleType.PREDICTED -> Color(0xFFFFF3E0)
                        CycleType.FERTILE -> Color(0xFFE3F2FD)
                        else -> Color(0xFFF1F8E9)
                    }
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Info,
                        contentDescription = null,
                        tint = when (selectedDayData.type) {
                            CycleType.PERIOD -> Color(0xFFE91E63)
                            CycleType.PREDICTED -> Color(0xFFFF9800)
                            CycleType.FERTILE -> CycleColors.FERTILE_TEXT
                            else -> Color(0xFF4CAF50)
                        },
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = tipText,
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                }
            }
        }
    }
}

/**
 * 血量云朵图标
 */
@Composable
fun BleedingCloudIcon(
    modifier: Modifier = Modifier,
    level: Int = 2, // 1-4级流量
    isPredicted: Boolean = false
) {
    // 避免无限循环动画导致的渲染问题
    val scale = if (isPredicted) 1f else 1.05f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        val cloudRadius = size.width * 0.35f
        val cloudColor = if (isPredicted) CycleColors.CLOUD else Color(0xFFFFCDD2)
        val dropColor = if (isPredicted) CycleColors.DROP_DARK else Color(0xFFE91E63).copy(alpha = 0.7f)
        
        // 云朵主体
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * scale,
            center = Offset(size.width * 0.3f, size.height * 0.4f)
        )
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * 0.8f * scale,
            center = Offset(size.width * 0.6f, size.height * 0.35f)
        )
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * 0.7f * scale,
            center = Offset(size.width * 0.75f, size.height * 0.45f)
        )
        
        // 绘制滴血程度 - 根据级别调整数量
        val dropCount = level.coerceIn(1, 4)
        val dropRadius = size.width * 0.08f
        val dropSpacing = size.width * 0.2f
        
        for (i in 0 until dropCount) {
            val dropXOffset = size.width * 0.3f + dropSpacing * i
            drawCircle(
                color = dropColor,
                radius = dropRadius,
                center = Offset(dropXOffset.coerceAtMost(size.width * 0.8f), size.height * 0.8f)
            )
        }
        
        // 如果是预测模式，绘制虚线边框
        if (isPredicted) {
            drawCircle(
                color = Color(0xFFE91E63).copy(alpha = 0.3f),
                radius = size.width * 0.45f,
                center = Offset(size.width / 2, size.height / 2),
                style = Stroke(
                    width = size.width * 0.03f,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                )
            )
        }
        
        // 云朵眼睛 - 表情
        val eyeRadius = size.width * 0.05f
        val eyeDistance = size.width * 0.15f
        
        // 左眼
        drawCircle(
            color = Color.White,
            radius = eyeRadius,
            center = Offset(size.width * 0.4f - eyeDistance / 2, size.height * 0.4f)
        )
        drawCircle(
            color = Color.Black,
            radius = eyeRadius * 0.5f,
            center = Offset(size.width * 0.4f - eyeDistance / 2, size.height * 0.4f)
        )
        
        // 右眼
        drawCircle(
            color = Color.White,
            radius = eyeRadius,
            center = Offset(size.width * 0.4f + eyeDistance / 2, size.height * 0.4f)
        )
        drawCircle(
            color = Color.Black,
            radius = eyeRadius * 0.5f,
            center = Offset(size.width * 0.4f + eyeDistance / 2, size.height * 0.4f)
        )
    }
}

/**
 * 月亮枕头图标 - 表示经期开始/结束
 */
@Composable
fun MoonPillowIcon(
    modifier: Modifier = Modifier,
    contentColor: Color = Color(0xFFE91E63)
) {
    CachedCanvas(modifier = modifier) {
        // 枕头形状
        val pillowPath = Path().apply {
            val width = size.width
            val height = size.height
            
            moveTo(width * 0.2f, height * 0.3f)
            quadraticBezierTo(width * 0.1f, height * 0.5f, width * 0.2f, height * 0.7f)
            lineTo(width * 0.8f, height * 0.7f)
            quadraticBezierTo(width * 0.9f, height * 0.5f, width * 0.8f, height * 0.3f)
            close()
        }
        
        // 绘制枕头
        drawPath(
            path = pillowPath,
            color = contentColor.copy(alpha = 0.3f)
        )
        
        // 绘制枕头边缘
        drawPath(
            path = pillowPath,
            color = contentColor,
            style = Stroke(width = size.width * 0.05f)
        )
        
        // 绘制月亮
        val moonCenter = Offset(size.width * 0.6f, size.height * 0.4f)
        val moonRadius = size.width * 0.15f
        
        drawCircle(
            color = contentColor,
            radius = moonRadius,
            center = moonCenter
        )
        
        // 月亮阴影部分
        drawCircle(
            color = contentColor.copy(alpha = 0.3f),
            radius = moonRadius * 0.8f,
            center = Offset(moonCenter.x + moonRadius * 0.3f, moonCenter.y - moonRadius * 0.1f)
        )
    }
}

/**
 * 状态记录部分
 */
@Composable
fun StatusRecordSection(pcosMode: Boolean) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // 标题
        Text(
            text = "今日记录",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(vertical = 12.dp)
        )
        
        // 经期开始状态
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    MoonPillowIcon(
                        modifier = Modifier.size(32.dp),
                        contentColor = Color(0xFFE91E63)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = stringResource(R.string.period_started),
                        fontSize = 16.sp,
                        color = Color(0xFF554A60)
                    )
                }
                
                Switch(
                    checked = false,
                    onCheckedChange = { /* 更新状态 */ },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = Color(0xFFE91E63),
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color(0xFFE0E0E0)
                    )
                )
            }
        }
        
        // 流血量记录
        SymptomRecordItem(
            icon = {
                BleedingCloudIcon(
                    modifier = Modifier.size(32.dp),
                    level = 2
                )
            },
            title = "流血量",
            tint = Color(0xFFE91E63)
        )
        
        // 腹痛记录
        SymptomRecordItem(
            icon = {
                StomachPainIcon(
                    modifier = Modifier.size(32.dp)
                )
            },
            title = "腹痛",
            tint = Color(0xFFFF9800)
        )
        
        // 疲倦记录
        SymptomRecordItem(
            icon = {
                TiredStarIcon(
                    modifier = Modifier.size(32.dp)
                )
            },
            title = "疲倦",
            tint = Color(0xFF9C27B0)
        )
    }
}

/**
 * 症状记录项
 */
@Composable
fun SymptomRecordItem(
    icon: @Composable () -> Unit,
    title: String,
    tint: Color
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable { /* 打开详情 */ },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier.size(40.dp),
                    contentAlignment = Alignment.Center
                ) {
                    icon()
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = title,
                    fontSize = 16.sp,
                    color = Color(0xFF554A60)
                )
            }
            
            // 添加可选择的程度指示器
            Row {
                repeat(4) { index ->
                    val isSelected = index == 0
                    Box(
                        modifier = Modifier
                            .size(20.dp)
                            .padding(2.dp)
                            .clip(CircleShape)
                            .background(
                                if (isSelected) tint else Color(0xFFEEEEEE)
                            )
                            .clickable { /* 选择程度 */ }
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "查看详情",
                    tint = Color(0xFF999999)
                )
            }
        }
    }
}

/**
 * 腹痛图标
 */
@Composable
fun StomachPainIcon(
    modifier: Modifier = Modifier
) {
    // 避免动画导致的渲染问题
    val scale = 1.05f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        // 腹部
        val stomachColor = Color(0xFFFFCC80)
        val painColor = Color(0xFFFF9800)
        
        // 椭圆腹部
        drawOval(
            color = stomachColor,
            size = Size(size.width * 0.8f, size.height * 0.7f) * scale,
            topLeft = Offset(size.width * 0.1f, size.height * 0.15f)
        )
        
        // 痛苦表情
        val faceCenter = Offset(size.width * 0.5f, size.height * 0.4f)
        val eyeOffset = size.width * 0.15f
        val eyeSize = size.width * 0.1f
        
        // 眉毛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x - eyeOffset - eyeSize, faceCenter.y - eyeSize),
            end = Offset(faceCenter.x - eyeOffset + eyeSize, faceCenter.y - eyeSize * 0.5f),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x + eyeOffset - eyeSize, faceCenter.y - eyeSize * 0.5f),
            end = Offset(faceCenter.x + eyeOffset + eyeSize, faceCenter.y - eyeSize),
            strokeWidth = size.width * 0.03f
        )
        
        // 眼睛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x - eyeOffset - eyeSize * 0.5f, faceCenter.y),
            end = Offset(faceCenter.x - eyeOffset + eyeSize * 0.5f, faceCenter.y),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x + eyeOffset - eyeSize * 0.5f, faceCenter.y),
            end = Offset(faceCenter.x + eyeOffset + eyeSize * 0.5f, faceCenter.y),
            strokeWidth = size.width * 0.03f
        )
        
        // 痛苦嘴巴
        val mouthPath = Path().apply {
            moveTo(faceCenter.x - eyeSize, faceCenter.y + eyeSize * 1.2f)
            cubicTo(
                faceCenter.x - eyeSize * 0.5f, faceCenter.y + eyeSize * 0.8f,
                faceCenter.x + eyeSize * 0.5f, faceCenter.y + eyeSize * 0.8f,
                faceCenter.x + eyeSize, faceCenter.y + eyeSize * 1.2f
            )
        }
        
        drawPath(
            path = mouthPath,
            color = Color(0xFF795548),
            style = Stroke(width = size.width * 0.03f)
        )
        
        // 疼痛标记
        drawCircle(
            color = CycleColors.PAIN_FILL,
            radius = size.width * 0.15f * scale,
            center = Offset(size.width * 0.7f, size.height * 0.55f)
        )
        
        drawCircle(
            color = CycleColors.PAIN_FILL,
            radius = size.width * 0.12f * (2 - scale),
            center = Offset(size.width * 0.3f, size.height * 0.6f)
        )
    }
}

/**
 * 疲倦星星图标
 */
@Composable
fun TiredStarIcon(
    modifier: Modifier = Modifier
) {
    // 避免动画导致的渲染问题
    val yawnSize = 0.85f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        val starColor = Color(0xFFCE93D8)  // 浅紫色
        val starCenter = Offset(size.width / 2, size.height / 2)
        val outerRadius = size.width * 0.45f
        val innerRadius = size.width * 0.2f
        
        val path = Path()
        val angleStep = 36f
        
        // 创建星形路径
        for (i in 0 until 10) {
            val angle = i * angleStep * PI.toFloat() / 180
            val radius = if (i % 2 == 0) outerRadius else innerRadius
            val x = starCenter.x + cos(angle) * radius
            val y = starCenter.y + sin(angle) * radius
            
            if (i == 0) {
                path.moveTo(x, y)
            } else {
                path.lineTo(x, y)
            }
        }
        path.close()
        
        // 绘制星星
        drawPath(
            path = path,
            color = starColor
        )
        
        // 疲惫的眼睛 - 闭眼
        val eyeDistance = size.width * 0.2f
        val eyeY = starCenter.y - size.height * 0.05f
        
        // 左眼
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(starCenter.x - eyeDistance - size.width * 0.08f, eyeY),
            end = Offset(starCenter.x - eyeDistance + size.width * 0.08f, eyeY),
            strokeWidth = size.width * 0.03f
        )
        
        // 右眼
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(starCenter.x + eyeDistance - size.width * 0.08f, eyeY),
            end = Offset(starCenter.x + eyeDistance + size.width * 0.08f, eyeY),
            strokeWidth = size.width * 0.03f
        )
        
        // 打哈欠的嘴巴
        drawArc(
            color = Color(0xFF673AB7),
            startAngle = 180f,
            sweepAngle = 180f * yawnSize,
            useCenter = false,
            topLeft = Offset(
                starCenter.x - size.width * 0.15f,
                starCenter.y + size.height * 0.05f
            ),
            size = Size(
                size.width * 0.3f,
                size.height * 0.2f * yawnSize
            ),
            style = Stroke(width = size.width * 0.03f)
        )
        
        // 困倦的Z字符
        val zStartX = starCenter.x + size.width * 0.35f
        val zStartY = starCenter.y - size.height * 0.3f
        val zSize = size.width * 0.15f
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX, zStartY),
            end = Offset(zStartX + zSize, zStartY),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX + zSize, zStartY),
            end = Offset(zStartX, zStartY + zSize),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX, zStartY + zSize),
            end = Offset(zStartX + zSize, zStartY + zSize),
            strokeWidth = size.width * 0.03f
        )
    }
}

/**
 * 获取星期几的文本
 */
private fun getDayOfWeek(date: LocalDate): String {
    val dayOfWeek = date.dayOfWeek.value
    return when (dayOfWeek) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> ""
    }
} 

/**
 * 医学提示卡片
 */
@Composable
fun MedicalInsightCard(
    selectedDayData: CycleDay,
    pcosMode: Boolean
) {
    // 只在特定周期类型或PCOS模式下显示
    if (selectedDayData.type == CycleType.NONE && !pcosMode && !selectedDayData.isPcosAffected) {
        return
    }
    
    val cardBackgroundColor = when {
        pcosMode || selectedDayData.type == CycleType.PCOS || selectedDayData.isPcosAffected -> 
            CycleColors.PCOS.copy(alpha = 0.1f)
        selectedDayData.type == CycleType.PERIOD -> 
            CycleColors.PERIOD.copy(alpha = 0.1f)
        selectedDayData.type == CycleType.FERTILE -> 
            CycleColors.FERTILE.copy(alpha = 0.1f)
        selectedDayData.type == CycleType.OVULATION -> 
            CycleColors.OVULATION.copy(alpha = 0.1f)
        else -> Color.White
    }
    
    val accentColor = when {
        pcosMode || selectedDayData.type == CycleType.PCOS || selectedDayData.isPcosAffected -> 
            CycleColors.PCOS_TEXT
        selectedDayData.type == CycleType.PERIOD -> 
            CycleColors.PERIOD_TEXT
        selectedDayData.type == CycleType.FERTILE -> 
            CycleColors.FERTILE_TEXT
        selectedDayData.type == CycleType.OVULATION -> 
            CycleColors.OVULATION_TEXT
        else -> Color.Gray
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 信息图标
            Icon(
                imageVector = Icons.Default.Info,
                contentDescription = null,
                tint = accentColor,
                modifier = Modifier
                    .size(24.dp)
                    .background(Color.White, CircleShape)
                    .padding(4.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 医学建议内容
            Column {
                Text(
                    text = when {
                        pcosMode && (selectedDayData.type == CycleType.PCOS || selectedDayData.isPcosAffected) -> 
                            "PCOS症状监测"
                        pcosMode -> 
                            "PCOS月经周期管理"
                        selectedDayData.type == CycleType.PERIOD -> 
                            "月经期健康管理"
                        selectedDayData.type == CycleType.FERTILE -> 
                            "排卵前期提示"
                        selectedDayData.type == CycleType.OVULATION -> 
                            "排卵期管理"
                        else -> 
                            "月经周期健康提示"
                    },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = accentColor
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = when {
                        pcosMode && (selectedDayData.type == CycleType.PCOS || selectedDayData.isPcosAffected) -> 
                            "今日可能出现雄激素水平升高的症状，建议记录多毛、痤疮、情绪变化等。维持低血糖指数(GI)饮食有助于稳定胰岛素敏感性，降低血清雄激素水平。"
                        pcosMode -> 
                            "多囊卵巢综合征通常表现为稀发排卵或无排卵导致的月经周期不规律，伴雄激素水平升高。规律运动和低GI饮食有助于改善胰岛素抵抗。"
                        selectedDayData.type == CycleType.PERIOD -> 
                            "月经期子宫内膜脱落出血，注意保暖避免寒冷刺激导致子宫收缩加剧。补充含铁丰富的食物如深绿色蔬菜、红肉等，有助于预防缺铁性贫血。"
                        selectedDayData.type == CycleType.FERTILE -> 
                            "排卵前期是生育窗口期的开始，宫颈粘液变得更加透明、拉丝，适合精子通过。每日测量基础体温(BBT)有助于精确判断排卵时间。"
                        selectedDayData.type == CycleType.OVULATION -> 
                            "排卵期可能出现轻微侧腹痛(中间痛)或少量排卵期出血，属于正常生理现象。排卵后基础体温会有0.3-0.5°C的明显上升，持续至下次月经。"
                        selectedDayData.dayOfCycle < 14 -> 
                            "卵泡期(月经结束至排卵前)是卵泡发育的阶段，雌激素水平逐渐上升。此阶段适合规律运动和均衡饮食，有助于促进卵泡健康发育。"
                        else -> 
                            "黄体期(排卵后至下次月经前)黄体分泌孕激素，使子宫内膜为可能的妊娠做准备。保持良好作息，减少压力有助于维持激素水平平衡。"
                    },
                    fontSize = 14.sp,
                    color = Color.DarkGray,
                    lineHeight = 20.sp
                )
                
                // PCOS特殊医学数据
                if (pcosMode) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.White.copy(alpha = 0.6f), RoundedCornerShape(8.dp))
                            .padding(8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        // LH/FSH比值
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "LH/FSH",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = "2.8",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = CycleColors.PCOS_TEXT
                            )
                            Text(
                                text = "偏高",
                                fontSize = 10.sp,
                                color = Color.Red
                            )
                        }
                        
                        // 雄激素水平
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "睾酮",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = "75 ng/dL",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = CycleColors.PCOS_TEXT
                            )
                            Text(
                                text = "正常范围",
                                fontSize = 10.sp,
                                color = Color.Green
                            )
                        }
                        
                        // 胰岛素水平
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "胰岛素",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = "15 μU/mL",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = CycleColors.PCOS_TEXT
                            )
                            Text(
                                text = "轻度升高",
                                fontSize = 10.sp,
                                color = Color(0xFFFFA000)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 症状记录区域
 */
@Composable
fun SymptomRecordSection(
    pcosMode: Boolean,
    onSymptomRecord: (String) -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Text(
                text = "今日症状",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = CycleColors.PERIOD_TEXT,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 常规症状图标grid
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 流量
                SymptomIconItem(
                    backgroundColor = CycleColors.PERIOD.copy(alpha = 0.2f),
                    icon = {
                        BleedingCloudIcon(
                            modifier = Modifier.size(24.dp),
                            level = 2
                        )
                    },
                    text = "流量",
                    textColor = CycleColors.PERIOD_TEXT
                )
                
                // 痛感
                SymptomIconItem(
                    backgroundColor = Color(0xFFE3F2FD),
                    icon = {
                        StomachPainIcon(
                            modifier = Modifier.size(24.dp)
                        )
                    },
                    text = "痛感",
                    textColor = Color(0xFF1976D2)
                )
                
                // 情绪
                SymptomIconItem(
                    backgroundColor = Color(0xFFFFF8E1),
                    icon = {
                        Icon(
                            imageVector = Icons.Default.SentimentSatisfied,
                            contentDescription = null,
                            tint = Color(0xFFFFA000),
                            modifier = Modifier.size(24.dp)
                        )
                    },
                    text = "情绪",
                    textColor = Color(0xFFFFA000)
                )
                
                // 睡眠
                SymptomIconItem(
                    backgroundColor = Color(0xFFE8F5E9),
                    icon = {
                        Icon(
                            imageVector = Icons.Default.NightsStay,
                            contentDescription = null,
                            tint = Color(0xFF2E7D32),
                            modifier = Modifier.size(24.dp)
                        )
                    },
                    text = "睡眠",
                    textColor = Color(0xFF2E7D32)
                )
            }
            
            // PCOS专属症状记录
            if (pcosMode) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // PCOS专属标题
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(CycleColors.PCOS.copy(alpha = 0.1f), RoundedCornerShape(8.dp))
                        .border(
                            width = 1.dp,
                            color = CycleColors.PCOS.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 小图标
                            Box(
                                modifier = Modifier
                                    .size(20.dp)
                                    .clip(CircleShape)
                                    .background(CycleColors.PCOS.copy(alpha = 0.3f)),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null,
                                    tint = CycleColors.PCOS_TEXT,
                                    modifier = Modifier.size(12.dp)
                                )
                            }
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "PCOS专属记录",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = CycleColors.PCOS_TEXT
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "已记录：雄激素水平 ↑ 胰岛素抵抗指标 →",
                            fontSize = 12.sp,
                            color = CycleColors.PCOS_TEXT.copy(alpha = 0.8f)
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // PCOS特定症状图标
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            // 多毛症
                            PcosSymptomItem(
                                icon = Icons.Default.ContentCut,
                                text = "多毛",
                                selected = true
                            )
                            
                            // 痤疮
                            PcosSymptomItem(
                                icon = Icons.Default.Face,
                                text = "痤疮",
                                selected = false
                            )
                            
                            // 胰岛素
                            PcosSymptomItem(
                                icon = Icons.Default.BarChart,
                                text = "血糖",
                                selected = true
                            )
                            
                            // 不规律周期
                            PcosSymptomItem(
                                icon = Icons.Default.Schedule,
                                text = "周期",
                                selected = false
                            )
                        }
                    }
                }
                
                // 激素水平图表（简化版）
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "激素水平趋势",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = CycleColors.PCOS_TEXT,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // 简化的图表
                SimpleHormoneChart()
            }
            
            // 添加症状记录按钮
            IconButton(
                onClick = { onSymptomRecord("症状") },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加症状",
                    tint = Color(0xFF4CAF50)
                )
            }
        }
    }
}

/**
 * 症状图标项
 */
@Composable
fun SymptomIconItem(
    backgroundColor: Color,
    icon: @Composable () -> Unit,
    text: String,
    textColor: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(backgroundColor)
                .clickable { /* 记录症状 */ },
            contentAlignment = Alignment.Center
        ) {
            icon()
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = text,
            fontSize = 12.sp,
            color = textColor
        )
    }
}

/**
 * PCOS症状项
 */
@Composable
fun PcosSymptomItem(
    icon: ImageVector,
    text: String,
    selected: Boolean
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(
                    if (selected) CycleColors.PCOS.copy(alpha = 0.3f)
                    else Color.LightGray.copy(alpha = 0.3f)
                )
                .border(
                    width = 1.dp,
                    color = if (selected) CycleColors.PCOS_TEXT else Color.Gray.copy(alpha = 0.5f),
                    shape = CircleShape
                )
                .clickable { /* 切换症状 */ },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = if (selected) CycleColors.PCOS_TEXT else Color.Gray,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = text,
            fontSize = 10.sp,
            color = if (selected) CycleColors.PCOS_TEXT else Color.Gray
        )
    }
}

/**
 * 简化的激素水平图表
 */
@Composable
fun SimpleHormoneChart() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFF5F5F5))
            .padding(8.dp)
    ) {
        // 图表内容
        Row(
            modifier = Modifier.fillMaxSize(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.Bottom
        ) {
            // 修改调用方式，确保每个Column都有weight
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .width(24.dp)
                        .height(60.dp)
                        .background(CycleColors.PCOS_TEXT.copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "LH",
                    fontSize = 10.sp,
                    color = Color.DarkGray
                )
            }
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .width(24.dp)
                        .height(40.dp)
                        .background(CycleColors.FERTILE_TEXT.copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "FSH",
                    fontSize = 10.sp,
                    color = Color.DarkGray
                )
            }
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .width(24.dp)
                        .height(55.dp)
                        .background(CycleColors.PERIOD_TEXT.copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "睾酮",
                    fontSize = 10.sp,
                    color = Color.DarkGray
                )
            }
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.weight(1f)
            ) {
                Box(
                    modifier = Modifier
                        .width(24.dp)
                        .height(70.dp)
                        .background(Color(0xFFFFA000).copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "胰岛素",
                    fontSize = 10.sp,
                    color = Color.DarkGray
                )
            }
        }
    }
}

/**
 * 图表柱状条
 */
@Composable
fun ChartBar(
    height: Int,
    color: Color,
    label: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .width(24.dp)
                .height(height.dp)
                .background(color.copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.DarkGray
        )
    }
}

// 判断是否为预测类型
fun isPredictedType(type: CycleType): Boolean {
    return type == CycleType.PREDICTED ||
           type == CycleType.PREDICTED_PERIOD ||
           type == CycleType.PREDICTED_FOLLICULAR ||
           type == CycleType.PREDICTED_OVULATION ||
           type == CycleType.PREDICTED_LUTEAL ||
           type == CycleType.PREDICTED_FERTILE
} 

/**
 * 日期编辑对话框 - 用于补录历史经期数据
 */
@Composable
fun CycleRecordEditDialog(
    date: LocalDate,
    viewModel: MenstrualCycleViewModel,
    onDismiss: () -> Unit
) {
    var selectedOperation by remember { mutableStateOf<String?>(null) }
    var startDate by remember { mutableStateOf(date) }
    var endDate by remember { mutableStateOf(date.plusDays(4)) } // 默认5天经期
    var showDateRangePicker by remember { mutableStateOf(false) }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }
    var showConfirmStartDialog by remember { mutableStateOf(false) }
    var showConfirmEndDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    
    // 获取平均经期长度
    val averagePeriodLength = remember { viewModel.getAveragePeriodLength() }
    
    // 标记是否可以编辑该日期
    val canEditDate = viewModel.isValidDateForHistory(date)
    
    if (!canEditDate) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("无法编辑此日期") },
            text = { 
                Column {
                    Text("只能编辑过去六个月内的日期，未来日期不可编辑。")
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示可编辑的日期范围
                    val today = LocalDate.now()
                    val sixMonthsAgo = today.minusMonths(6).withDayOfMonth(1)
                    Text(
                        text = "可编辑范围：${sixMonthsAgo.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 至 ${today.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}",
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT
                    )
                }
            },
            confirmButton = {
                TextButton(onClick = onDismiss) {
                    Text("确定", color = CycleColors.PERIOD_TEXT)
                }
            }
        )
        return
    }
    
    // 开始日期选择器
    if (showStartDatePicker) {
        CustomDatePickerDialog(
            title = "选择开始日期",
            initialDate = startDate,
            onDateSelected = { newDate -> 
                startDate = newDate
                // 确保结束日期不早于开始日期
                if (endDate.isBefore(startDate)) {
                    endDate = startDate.plusDays(averagePeriodLength.toLong() - 1)
                }
            },
            onDismiss = { showStartDatePicker = false }
        )
    }
    
    // 结束日期选择器
    if (showEndDatePicker) {
        CustomDatePickerDialog(
            title = "选择结束日期",
            initialDate = endDate,
            onDateSelected = { newDate -> 
                // 确保结束日期不早于开始日期
                if (!newDate.isBefore(startDate)) {
                    endDate = newDate
                } else {
                    Toast.makeText(context, "结束日期不能早于开始日期", Toast.LENGTH_SHORT).show()
                }
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
    
    // 日期范围选择界面
    if (showDateRangePicker) {
        AlertDialog(
            onDismissRequest = { showDateRangePicker = false },
            title = { Text("选择日期范围") },
            text = { 
                Column {
                    Text("请选择月经期的起止日期")
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 开始日期选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "开始日期: ",
                            fontSize = 14.sp,
                            modifier = Modifier.width(80.dp)
                        )
                        Text(
                            text = startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 14.sp,
                            color = CycleColors.PERIOD_TEXT,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { showStartDatePicker = true }
                        )
                        IconButton(onClick = { showStartDatePicker = true }) {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择开始日期",
                                tint = CycleColors.PERIOD_TEXT
                            )
                        }
                    }
                    
                    // 结束日期选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "结束日期: ",
                            fontSize = 14.sp,
                            modifier = Modifier.width(80.dp)
                        )
                        Text(
                            text = endDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 14.sp,
                            color = CycleColors.PERIOD_TEXT,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { showEndDatePicker = true }
                        )
                        IconButton(onClick = { showEndDatePicker = true }) {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择结束日期",
                                tint = CycleColors.PERIOD_TEXT
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示经期长度
                    val daysCount = ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
                    Text(
                        text = "经期长度：$daysCount 天",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = CycleColors.PERIOD_TEXT
                    )
                    
                    // 医学知识提示
                    Spacer(modifier = Modifier.height(8.dp))
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "医学小知识",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "正常月经周期为21-35天，月经期通常持续3-7天，平均为5天。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showDateRangePicker = false
                        // 实际记录日期范围
                        viewModel.recordPeriodForDateRange(startDate, endDate)
                        
                        // 使用延迟刷新函数确保UI更新
                        viewModel.refreshCalendarViewWithDelay()
                        
                        Toast.makeText(context, "已记录从 ${startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 到 ${endDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 的经期", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确定", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDateRangePicker = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 确认开始经期对话框
    if (showConfirmStartDialog) {
        val predictedEndDate = date.plusDays(averagePeriodLength.toLong() - 1)
        
        AlertDialog(
            onDismissRequest = { showConfirmStartDialog = false },
            title = { Text("确认记录经期开始") },
            text = { 
                Column {
                    Text("您选择了 ${date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 作为经期开始日")
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "根据您的历史数据，系统将自动预设经期结束日为 ${predictedEndDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}（共 $averagePeriodLength 天）",
                        fontSize = 14.sp
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "您可以在经期结束时调整结束日期，或现在选择设置经期范围",
                        fontSize = 14.sp,
                        fontStyle = FontStyle.Italic
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 医学知识提示
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "月经小贴士",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "月经期间应保持良好卫生习惯，定时更换卫生用品，注意休息，避免剧烈运动，多喝温水，少食生冷辛辣食物。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showConfirmStartDialog = false
                        viewModel.recordPeriodStartForDate(date)
                        viewModel.refreshCalendarViewWithDelay()
                        Toast.makeText(context, "已记录经期开始，预计结束日期为 ${predictedEndDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确认", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                Row {
                    TextButton(
                        onClick = { 
                            showConfirmStartDialog = false
                            // 初始化日期范围选择器的日期
                            startDate = date
                            endDate = predictedEndDate
                            showDateRangePicker = true
                        }
                    ) {
                        Text("设置日期范围", color = CycleColors.PERIOD_TEXT)
                    }
                    TextButton(
                        onClick = { showConfirmStartDialog = false }
                    ) {
                        Text("取消")
                    }
                }
            }
        )
    }
    
    // 确认结束经期对话框
    if (showConfirmEndDialog) {
        AlertDialog(
            onDismissRequest = { showConfirmEndDialog = false },
            title = { Text("确认记录经期结束") },
            text = { 
                Column {
                    Text("您选择了 ${date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 作为经期结束日")
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    val latestCycle = viewModel.cycles.value.maxByOrNull { it.startDate }
                    if (latestCycle != null && latestCycle.endDate == null) {
                        val startDate = latestCycle.startDate
                        val periodLength = ChronoUnit.DAYS.between(startDate, date).toInt() + 1
                        
                        Text(
                            text = "您的本次经期从 ${startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 开始，共持续 $periodLength 天",
                            fontSize = 14.sp
                        )
                        
                        if (periodLength < 3 || periodLength > 7) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFFFFF3CD)
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Row(
                                    modifier = Modifier.padding(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Warning,
                                        contentDescription = null,
                                        tint = Color(0xFFCC9900),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "您的经期长度为 $periodLength 天，超出了正常范围(3-7天)。如果经常出现此情况，建议咨询医生。",
                                        fontSize = 12.sp,
                                        color = Color.DarkGray
                                    )
                                }
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 医学知识提示
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "月经后调理小贴士",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "月经结束后是身体恢复的重要时期，注意补充铁质食物，适量运动增强体质，保持良好的作息习惯，为下一个周期做好准备。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showConfirmEndDialog = false
                        viewModel.recordPeriodEndForDate(date)
                        viewModel.refreshCalendarViewWithDelay()
                        Toast.makeText(context, "已记录经期结束", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确认", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showConfirmEndDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 实际应用中，可以使用系统日期选择器
    // 这里简化处理
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text(
                    text = "经期记录编辑",
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.PERIOD_TEXT
                )
                
                Text(
                    text = date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = CycleColors.PERIOD_TEXT
                )
            }
        },
        text = {
            Column {
                // 显示当前日期周期状态
                val cycleType = viewModel.selectedDayData.value.type
                val cycleTypeText = when (cycleType) {
                    CycleType.PERIOD -> "月经期"
                    CycleType.FOLLICULAR -> "卵泡期"
                    CycleType.OVULATION -> "排卵期"
                    CycleType.LUTEAL -> "黄体期"
                    CycleType.FERTILE -> "排卵前期"
                    CycleType.PREDICTED, 
                    CycleType.PREDICTED_PERIOD -> "预测月经期"
                    CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期"
                    CycleType.PREDICTED_OVULATION -> "预测排卵期"
                    CycleType.PREDICTED_LUTEAL -> "预测黄体期"
                    CycleType.PREDICTED_FERTILE -> "预测排卵前期"
                    CycleType.PCOS -> "PCOS症状期" 
                    CycleType.NONE -> "无特殊记录"
                }
                
                Text(
                    text = "当前状态: $cycleTypeText",
                    fontSize = 14.sp,
                    color = CycleColors.getTextColorForType(cycleType),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Divider(
                    color = Color.LightGray.copy(alpha = 0.5f),
                    thickness = 1.dp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Text(
                    text = "请选择操作:",
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                // 操作按钮 - 记录经期开始日
                OutlinedButton(
                    onClick = { showConfirmStartDialog = true },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = CycleColors.PERIOD_TEXT
                    ),
                    border = BorderStroke(1.dp, CycleColors.PERIOD_TEXT)
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = CycleColors.PERIOD_TEXT
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "记录经期开始日",
                            fontSize = 14.sp
                        )
                    }
                }
                
                // 操作按钮 - 删除记录
                OutlinedButton(
                    onClick = { 
                        selectedOperation = "delete"
                        viewModel.deleteCycleForDate(date)
                        
                        // 确保UI更新
                        viewModel.refreshCalendarViewWithDelay()
                        
                        Toast.makeText(context, "已删除经期记录", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.Red
                    ),
                    border = BorderStroke(1.dp, Color.Red)
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = Color.Red
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "删除此日期的经期记录",
                            fontSize = 14.sp,
                            color = Color.Red
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭", color = CycleColors.PERIOD_TEXT)
            }
        }
    )
}

/**
 * 打开日期上下文菜单（长按日期）- 增强版，支持补录
 */
private fun openDateContextMenu(
    date: LocalDate,
    onPeriodStart: () -> Unit,
    onPeriodEnd: () -> Unit,
    context: Context
) {
    // 使用系统的上下文菜单
    // 注意：此功能在实际应用中需要实现完整的上下文菜单
    // 这里只是示例逻辑
    
    // 例如可以使用PopupMenu或BottomSheetDialog
    // 显示经期记录选项:
    // 1. 记录经期开始
    // 2. 记录经期结束
    // 3. 添加经期记录范围
    // 4. 删除经期记录
    
    // 简化版实现，直接显示提示并调用回调函数
    Toast.makeText(context, "长按功能: 请使用编辑对话框完成操作", Toast.LENGTH_SHORT).show()
    onPeriodStart()
}

// 在合适的位置添加日期选择器的实现
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CustomDatePickerDialog(
    title: String,
    initialDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    )
    
    val selectedDate = remember(datePickerState.selectedDateMillis) {
        datePickerState.selectedDateMillis?.let {
            Instant.ofEpochMilli(it).atZone(ZoneId.systemDefault()).toLocalDate()
        } ?: initialDate
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DatePicker(
                    state = datePickerState,
                    showModeToggle = false,
                    title = null,
                    headline = null,
                    colors = DatePickerDefaults.colors(
                        containerColor = Color.White,
                        titleContentColor = CycleColors.PERIOD_TEXT,
                        headlineContentColor = CycleColors.PERIOD_TEXT,
                        weekdayContentColor = Color.Gray,
                        subheadContentColor = Color.DarkGray,
                        yearContentColor = Color.DarkGray,
                        currentYearContentColor = CycleColors.PERIOD_TEXT,
                        selectedYearContainerColor = CycleColors.PERIOD.copy(alpha = 0.1f),
                        selectedYearContentColor = CycleColors.PERIOD_TEXT,
                        dayContentColor = Color.DarkGray,
                        selectedDayContainerColor = CycleColors.PERIOD,
                        selectedDayContentColor = Color.White,
                        todayContentColor = CycleColors.PERIOD_TEXT,
                        todayDateBorderColor = CycleColors.PERIOD
                    )
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    selectedDate?.let { onDateSelected(it) }
                    onDismiss()
                }
            ) {
                Text("确定", color = CycleColors.PERIOD_TEXT)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}