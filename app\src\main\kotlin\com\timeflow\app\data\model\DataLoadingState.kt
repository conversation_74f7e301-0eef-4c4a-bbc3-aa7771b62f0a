package com.timeflow.app.data.model

/**
 * 数据加载状态
 * 用于表示数据加载的不同状态：加载中、成功、错误
 *
 * @property isLoading 是否正在加载数据
 * @property error 错误信息，如果有错误则非空
 */
data class DataLoadingState(
    val isLoading: Boolean = false,
    val error: String? = null
) {
    val isError: Boolean
        get() = error != null

    companion object {
        /**
         * 创建加载中状态
         */
        fun loading() = DataLoadingState(isLoading = true, error = null)
        
        /**
         * 创建成功状态
         */
        fun success() = DataLoadingState(isLoading = false, error = null)
        
        /**
         * 创建错误状态
         */
        fun error(message: String) = DataLoadingState(isLoading = false, error = message)
    }
} 