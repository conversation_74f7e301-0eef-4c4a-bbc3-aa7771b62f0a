package com.timeflow.app.ui.task

import android.view.HapticFeedbackConstants
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.converter.TaskConverter
import com.timeflow.app.data.entity.Task as EntityTask
import com.timeflow.app.data.model.KanbanColumn
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.task.model.TaskStatus
import com.timeflow.app.ui.task.model.TaskStatusChange
import com.timeflow.app.ui.task.model.TasksStatistics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import java.time.LocalDateTime
import java.time.ZoneOffset
import javax.inject.Inject
import androidx.compose.ui.hapticfeedback.HapticFeedbackType

/**
 * KanbanViewModel - 处理看板视图相关逻辑的ViewModel
 * 
 * 这是一个优化版本，提供了以下功能：
 * 1. 支持自定义状态列
 * 2. 基于优先级和截止日期的智能排序
 * 3. 列限制（WIP限制）
 * 4. 任务统计
 * 5. 状态变更和撤销功能
 * 6. 触觉反馈
 */
@HiltViewModel
class KanbanViewModel @Inject constructor(
    private val taskRepository: TaskRepository
) : ViewModel() {

    // 看板列
    private val _kanbanColumns = MutableStateFlow<List<KanbanColumn>>(emptyList())
    val kanbanColumns: StateFlow<List<KanbanColumn>> = _kanbanColumns.asStateFlow()
    
    // 列状态
    private val _columnStates = MutableStateFlow<Map<String, TaskStatus>>(mapOf())
    val columnStates: StateFlow<Map<String, TaskStatus>> = _columnStates.asStateFlow()
    
    // 任务列表
    private val _tasks = MutableStateFlow<List<EntityTask>>(emptyList())
    val tasks: StateFlow<List<EntityTask>> = _tasks.asStateFlow()
    
    // 统计信息
    private val _tasksStatistics = MutableStateFlow(TasksStatistics())
    val tasksStatistics: StateFlow<TasksStatistics> = _tasksStatistics.asStateFlow()
    
    // 状态变更记录（用于撤销）
    private val _lastStatusChange = MutableStateFlow<TaskStatusChange?>(null)
    val lastStatusChange: StateFlow<TaskStatusChange?> = _lastStatusChange.asStateFlow()
    
    // 触觉反馈 - 使用Int代替HapticFeedbackType
    private val _hapticFeedback = MutableStateFlow<Int?>(null)
    val hapticFeedback: StateFlow<Int?> = _hapticFeedback.asStateFlow()
    
    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 是否加载中
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 缓存和状态锁
    private val taskStatisticsCache = HashMap<String, TasksStatistics>()
    private val statusChangeMutex = Mutex()
    
    /**
     * 初始化看板
     */
    fun initialize() {
        viewModelScope.launch {
            // 初始化列状态
            val defaultStates = TaskStatus.getDefaultStatuses()
            _columnStates.value = defaultStates.associateBy { it.id }
            
            // 加载任务
            refreshTasks()
        }
    }
    
    /**
     * 刷新任务
     */
    fun refreshTasks() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val tasks = taskRepository.getAllTasks()
                // 转换为EntityTask类型，用于更新UI状态
                val entityTasks = tasks.map { TaskConverter.toEntityTask(it) }
                _tasks.value = entityTasks
                
                // 直接使用从仓库获取的模型任务来更新看板列
                updateKanbanColumns(tasks)
            } catch (e: Exception) {
                Timber.e(e, "加载任务失败")
                _error.value = "加载任务失败: ${e.localizedMessage}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新任务看板列数据
     * 
     * @param tasks 要显示在看板上的任务列表
     * @param forceRefresh 是否强制刷新，即使数据没有变化
     * @param customStatuses 自定义状态列，如果为null则使用默认状态
     */
    private suspend fun updateKanbanColumns(
        tasks: List<ModelTask>, 
        forceRefresh: Boolean = false,
        customStatuses: List<TaskStatus>? = null
    ) {
        viewModelScope.launch {
            try {
                // 1. 类型安全检查，过滤非Task类型项目
                val safeTasks = tasks.filter { 
                    val isTask = it is ModelTask
                    if (!isTask) {
                        Timber.e("看板视图过滤出非Task类型的对象: ${it.javaClass.name}")
                    }
                    isTask
                }
    
                // 2. 获取显示的状态列
                val statusesToShow = customStatuses ?: TaskStatus.getDefaultStatuses()
                val columns = mutableListOf<KanbanColumn>()
                
                // 3. 高效处理 - 一次性分组而不是多次过滤
                val tasksByStatus = safeTasks.groupBy { it.status }
                
                // 4. 为每个状态创建列
                for (status in statusesToShow) {
                    // 获取此状态的任务
                    val statusTasks = tasksByStatus[status.name] ?: emptyList()
                    
                    // 基于任务的优先级和截止日期进行智能排序
                    val sortedTasks = statusTasks.sortedWith(compareByDescending<ModelTask> { 
                        it.priority?.ordinal ?: 0 
                    }.thenBy { 
                        it.dueDate 
                    })
                    
                    // 应用列限制（如果设置了）
                    val limitedTasks = if (status.limit > 0 && sortedTasks.size > status.limit) {
                        sortedTasks.take(status.limit)
                    } else {
                        sortedTasks
                    }
                    
                    // 创建看板列并添加到列表
                    val column = KanbanColumn(
                        id = status.id,
                        boardId = "default_board",
                        title = status.name,
                        tasks = limitedTasks,
                        color = status.color,
                        isCollapsed = _columnStates.value[status.id]?.isCollapsed ?: false,
                        taskLimit = _columnStates.value[status.id]?.limit ?: 0
                    )
                    columns.add(column)
                }
                
                // 5. 在状态变化或强制刷新时更新
                if (_kanbanColumns.value != columns || forceRefresh) {
                    _kanbanColumns.value = columns
                    
                    // 更新统计信息
                    updateTaskStatistics(tasks)
                    
                    Timber.d("看板列加载成功: ${columns.size}列, 共${safeTasks.size}个任务")
                }
            } catch (e: Exception) {
                Timber.e(e, "更新看板列失败")
                _error.value = "加载看板视图失败: ${e.localizedMessage}"
            }
        }
    }
    
    /**
     * 更新任务统计信息
     */
    private fun updateTaskStatistics(tasks: List<ModelTask>) {
        val cacheKey = "stats_${tasks.hashCode()}"
        
        // 尝试从缓存获取
        taskStatisticsCache[cacheKey]?.let { cached ->
            _tasksStatistics.value = cached
            return
        }
        
        // 计算统计信息
        // 转换为EntityTask类型来计算统计
        val entityTasks = TaskConverter.toEntityTasks(tasks)
        val stats = TasksStatistics.fromTasks(entityTasks)
        
        // 更新状态和缓存
        _tasksStatistics.value = stats
        taskStatisticsCache[cacheKey] = stats
        
        // 缓存清理 - 保持缓存在合理大小
        if (taskStatisticsCache.size > 10) {
            taskStatisticsCache.entries.firstOrNull()?.let {
                taskStatisticsCache.remove(it.key)
            }
        }
    }
    
    /**
     * 移动任务到新状态
     */
    fun moveTaskToStatus(taskId: String, newStatus: String) {
        viewModelScope.launch {
            try {
                // 1. 获取当前任务
                val task = taskRepository.getTaskById(taskId) ?: return@launch
                val oldStatus = task.status
                
                // 2. 如果状态没变，不需要操作
                if (oldStatus == newStatus) return@launch
                
                // 3. 更新任务状态
                val updatedTask = task.copy(status = newStatus)
                taskRepository.updateTask(updatedTask)
                
                // 4. 记录状态变更
                statusChangeMutex.withLock {
                    _lastStatusChange.value = TaskStatusChange(
                        taskId = taskId,
                        oldStatus = oldStatus,
                        newStatus = newStatus
                    )
                }
                
                // 5. 触发触觉反馈
                _hapticFeedback.value = HapticFeedbackConstants.LONG_PRESS
                
                // 6. 刷新看板视图
                refreshTasks()
            } catch (e: Exception) {
                Timber.e(e, "移动任务失败")
                _error.value = "移动任务失败: ${e.localizedMessage}"
            }
        }
    }
    
    /**
     * 撤销最后一次状态变更
     */
    fun undoLastStatusChange() {
        viewModelScope.launch {
            val lastChange = _lastStatusChange.value ?: return@launch
            
            try {
                // 1. 获取任务
                val task = taskRepository.getTaskById(lastChange.taskId) ?: return@launch
                
                // 2. 恢复到原来的状态
                val restoredTask = task.copy(status = lastChange.oldStatus)
                taskRepository.updateTask(restoredTask)
                
                // 3. 清除最后变更记录
                statusChangeMutex.withLock {
                    _lastStatusChange.value = null
                }
                
                // 4. 刷新看板视图
                refreshTasks()
                
                Timber.d("撤销任务状态变更: ${lastChange.taskId}, ${lastChange.newStatus} -> ${lastChange.oldStatus}")
            } catch (e: Exception) {
                Timber.e(e, "撤销任务状态变更失败")
                _error.value = "撤销操作失败: ${e.localizedMessage}"
            }
        }
    }
    
    /**
     * 切换列折叠状态
     */
    fun toggleColumnCollapsed(statusId: String) {
        viewModelScope.launch {
            val currentColumns = _kanbanColumns.value
            val updatedColumns = currentColumns.map { column ->
                if (column.id == statusId) {
                    column.copy(isCollapsed = !column.isCollapsed)
                } else {
                    column
                }
            }
            _kanbanColumns.value = updatedColumns
        }
    }
    
    /**
     * 设置自定义状态列表
     */
    fun setCustomStatuses(statuses: List<TaskStatus>) {
        viewModelScope.launch {
            val modelTasks = TaskConverter.toModelTasks(_tasks.value)
            updateKanbanColumns(modelTasks, true, statuses)
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 清除触觉反馈状态（在UI响应后）
     */
    fun clearHapticFeedback() {
        _hapticFeedback.value = null
    }
} 