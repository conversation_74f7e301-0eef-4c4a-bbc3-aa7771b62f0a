package com.timeflow.app.utils

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.core.content.FileProvider
import androidx.core.app.ShareCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import android.util.Log
import com.timeflow.app.ui.screen.reflection.Reflection
import com.timeflow.app.ui.screen.reflection.components.ShareableReflectionCard
import java.time.ZoneId

/**
 * 🎨 感想分享管理器
 * 处理感想卡片的截图、保存和分享功能
 */
object ReflectionShareManager {
    
    private const val TAG = "ReflectionShareManager"
    
    /**
     * 分享感想卡片
     * @param context 上下文
     * @param reflection 感想对象
     * @param saveToGallery 是否同时保存到相册
     * @return 操作结果
     */
    suspend fun shareReflectionCard(
        context: Context,
        reflection: Reflection,
        saveToGallery: Boolean = false
    ): Boolean {
        return try {
            Log.d(TAG, "开始分享感想卡片: ${reflection.title}")
            
            // 1. 截取卡片UI为图片
            val bitmap = captureReflectionCard(context, reflection)
            
            // 2. 保存到相册（如果需要）
            if (saveToGallery) {
                val savedUri = saveImageToGallery(context, bitmap, reflection.title)
                if (savedUri != null) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(context, "已保存到相册", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Log.w(TAG, "保存到相册失败")
                }
            }
            
            // 3. 分享图片
            shareImage(context, bitmap, reflection.title)
            
            Log.d(TAG, "分享感想卡片完成")
            true
        } catch (e: Exception) {
            Log.e(TAG, "分享感想卡片失败", e)
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "分享失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
            false
        }
    }
    
    /**
     * 仅保存感想卡片到相册
     * @param context 上下文
     * @param reflection 感想对象
     * @return 操作结果
     */
    suspend fun saveReflectionCardToGallery(
        context: Context,
        reflection: Reflection
    ): Boolean {
        return try {
            Log.d(TAG, "开始保存感想卡片到相册: ${reflection.title}")
            
            // 1. 截取卡片UI为图片
            val bitmap = captureReflectionCard(context, reflection)
            
            // 2. 保存到相册
            val savedUri = saveImageToGallery(context, bitmap, reflection.title)
            
            if (savedUri != null) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "已保存到相册", Toast.LENGTH_SHORT).show()
                }
                Log.d(TAG, "保存感想卡片到相册完成")
                true
            } else {
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "保存失败", Toast.LENGTH_SHORT).show()
                }
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存感想卡片到相册失败", e)
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
            false
        }
    }
    
    /**
     * 截取感想卡片为Bitmap
     * @param context 上下文
     * @param reflection 感想对象
     * @return Bitmap图片
     */
    private suspend fun captureReflectionCard(
        context: Context,
        reflection: Reflection
    ): Bitmap {
        val (width, height) = ComposeScreenshotUtils.getReflectionCardImageSize(context)
        
        Log.d(TAG, "开始截图感想卡片 - 尺寸: ${width}x${height}")
        
        return try {
            // 首先尝试使用Picture API方法
            Log.d(TAG, "尝试使用Picture API方法截图")
            val bitmap = ComposeScreenshotUtils.captureComposableWithPicture(
                context = context,
                width = width,
                height = height
            ) {
                ShareableReflectionCard(
                    reflection = reflection
                )
            }
            Log.d(TAG, "Picture API方法截图成功")
            bitmap
        } catch (e: Exception) {
            Log.w(TAG, "Picture API方法失败，尝试备用方法", e)
            try {
                // 备用方法：使用简单的View方法
                Log.d(TAG, "尝试使用View方法截图")
                val bitmap = ComposeScreenshotUtils.captureComposable(
                    context = context,
                    width = width,
                    height = height
                ) {
                    ShareableReflectionCard(
                        reflection = reflection
                    )
                }
                Log.d(TAG, "View方法截图成功")
                bitmap
            } catch (e2: Exception) {
                Log.w(TAG, "备用方法也失败，使用手动绘制", e2)
                // 最后的回退方案：手动绘制
                Log.d(TAG, "尝试使用手动绘制")
                val bitmap = createFallbackBitmap(context, reflection, width, height)
                Log.d(TAG, "手动绘制完成")
                bitmap
            }
        }
    }
    
    /**
     * 创建回退方案的Bitmap（手动绘制）
     * @param context 上下文
     * @param reflection 感想对象
     * @param width 宽度
     * @param height 高度
     * @return 手动绘制的Bitmap
     */
    private suspend fun createFallbackBitmap(
        context: Context,
        reflection: Reflection,
        width: Int,
        height: Int
    ): Bitmap = withContext(Dispatchers.IO) {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(bitmap)
        
        // 绘制白色背景
        canvas.drawColor(android.graphics.Color.WHITE)
        
        // 创建画笔
        val titlePaint = android.graphics.Paint().apply {
            color = android.graphics.Color.BLACK
            textSize = 48f
            isFakeBoldText = true
            isAntiAlias = true
        }
        
        val contentPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.DKGRAY
            textSize = 36f
            isAntiAlias = true
        }
        
        val metaPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.GRAY
            textSize = 28f
            isAntiAlias = true
        }
        
        val tagPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.rgb(131, 122, 147)
            textSize = 32f
            isAntiAlias = true
        }
        
        var y = 100f
        val margin = 50f
        val maxWidth = width - margin * 2
        
        // 绘制标题
        if (reflection.title.isNotBlank() && reflection.title != "感想记录") {
            val titleLines = breakTextIntoLines(reflection.title, titlePaint, maxWidth.toInt())
            titleLines.forEach { line ->
                canvas.drawText(line, margin, y, titlePaint)
                y += 60f
            }
            y += 20f
        }
        
        // 绘制日期和类型
        val dateText = try {
            java.text.SimpleDateFormat("yyyy年MM月dd日 HH:mm", java.util.Locale.getDefault())
                .format(java.util.Date.from(reflection.date.atZone(java.time.ZoneId.systemDefault()).toInstant()))
        } catch (e: Exception) {
            "今天"
        }
        
        val typeText = reflection.type?.displayName ?: "生活感悟"
        canvas.drawText("$typeText • $dateText", margin, y, metaPaint)
        y += 50f
        
        // 绘制分隔线
        val linePaint = android.graphics.Paint().apply {
            color = android.graphics.Color.LTGRAY
            strokeWidth = 2f
        }
        canvas.drawLine(margin, y, width - margin, y, linePaint)
        y += 30f
        
        // 绘制内容
        val content = if (reflection.richContent?.isNotEmpty() == true) {
            reflection.richContent?.filter { it.type == "text" }?.joinToString("\n") { it.value } ?: reflection.content
        } else {
            reflection.content
        }
        
        if (content.isNotBlank()) {
            val contentLines = breakTextIntoLines(content, contentPaint, maxWidth.toInt())
            contentLines.forEach { line ->
                if (y < height - 150f) { // 确保不超出边界
                    canvas.drawText(line, margin, y, contentPaint)
                    y += 50f
                }
            }
            y += 20f
        }
        
        // 绘制标签
        if (reflection.tags.isNotEmpty() && y < height - 100f) {
            var tagX = margin
            reflection.tags.take(5).forEach { tag ->
                val tagText = "#$tag"
                val tagWidth = tagPaint.measureText(tagText)
                if (tagX + tagWidth < width - margin) {
                    canvas.drawText(tagText, tagX, y, tagPaint)
                    tagX += tagWidth + 30f
                }
            }
            y += 50f
        }
        
        // 绘制水印
        val watermarkPaint = android.graphics.Paint().apply {
            color = android.graphics.Color.LTGRAY
            textSize = 24f
            textAlign = android.graphics.Paint.Align.RIGHT
            isAntiAlias = true
        }
        canvas.drawText("来自 TimeFlow", width - margin, height - 30f, watermarkPaint)
        
        Log.d(TAG, "手动绘制图片完成")
        bitmap
    }
    
    /**
     * 将文本分行显示
     */
    private fun breakTextIntoLines(text: String, paint: android.graphics.Paint, maxWidth: Int): List<String> {
        val lines = mutableListOf<String>()
        val words = text.split(" ")
        var currentLine = StringBuilder()
        
        for (word in words) {
            val testLine = if (currentLine.isEmpty()) word else "${currentLine} $word"
            val width = paint.measureText(testLine)
            
            if (width <= maxWidth) {
                currentLine.append(if (currentLine.isEmpty()) word else " $word")
            } else {
                if (currentLine.isNotEmpty()) {
                    lines.add(currentLine.toString())
                    currentLine = StringBuilder(word)
                } else {
                    // 如果单个词太长，强制断行
                    lines.add(word)
                }
            }
        }
        
        if (currentLine.isNotEmpty()) {
            lines.add(currentLine.toString())
        }
        
        return lines
    }
    
    /**
     * 保存图片到系统相册
     * @param context 上下文
     * @param bitmap 图片
     * @param title 图片标题
     * @return 保存后的Uri
     */
    private suspend fun saveImageToGallery(
        context: Context,
        bitmap: Bitmap,
        title: String
    ): Uri? = withContext(Dispatchers.IO) {
        try {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename = "TimeFlow_${title.take(20)}_$timeStamp.jpg"
            
            var fos: OutputStream? = null
            var imageUri: Uri? = null
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用 MediaStore API
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, filename)
                    put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                    put(MediaStore.MediaColumns.RELATIVE_PATH, "${Environment.DIRECTORY_PICTURES}/TimeFlow")
                }
                
                context.contentResolver.also { resolver ->
                    imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                    fos = imageUri?.let { resolver.openOutputStream(it) }
                }
            } else {
                // Android 9及以下使用传统文件系统
                val imagesDir = File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                    "TimeFlow"
                )
                if (!imagesDir.exists()) {
                    imagesDir.mkdirs()
                }
                
                val imageFile = File(imagesDir, filename)
                fos = FileOutputStream(imageFile)
                imageUri = Uri.fromFile(imageFile)
            }
            
            fos?.use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
            }
            
            Log.d(TAG, "图片保存成功: $imageUri")
            imageUri
        } catch (e: Exception) {
            Log.e(TAG, "保存图片到相册失败", e)
            null
        }
    }
    
    /**
     * 分享图片
     * @param context 上下文
     * @param bitmap 图片
     * @param title 分享标题
     */
    private suspend fun shareImage(
        context: Context,
        bitmap: Bitmap,
        title: String
    ) = withContext(Dispatchers.IO) {
        try {
            // 保存到缓存目录
            val cachePath = File(context.cacheDir, "shared_images")
            if (!cachePath.exists()) {
                cachePath.mkdirs()
            }
            
            val filename = "shared_reflection_${UUID.randomUUID().toString().take(8)}.jpg"
            val imageFile = File(cachePath, filename)
            
            FileOutputStream(imageFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            }
            
            // 获取FileProvider Uri
            val fileUri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                imageFile
            )
            
            // 创建分享Intent
            withContext(Dispatchers.Main) {
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "image/jpeg"
                    putExtra(Intent.EXTRA_STREAM, fileUri)
                    putExtra(Intent.EXTRA_TEXT, "分享我的感想：$title - 来自TimeFlow")
                    putExtra(Intent.EXTRA_SUBJECT, "TimeFlow 感想分享")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                val chooserIntent = Intent.createChooser(shareIntent, "分享感想")
                chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                
                context.startActivity(chooserIntent)
            }
            
            Log.d(TAG, "图片分享成功")
        } catch (e: Exception) {
            Log.e(TAG, "分享图片失败", e)
            withContext(Dispatchers.Main) {
                Toast.makeText(context, "分享失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 显示分享选项对话框
     * @param context 上下文
     * @param reflection 感想对象
     * @param onShareOnly 仅分享回调
     * @param onSaveOnly 仅保存回调
     * @param onShareAndSave 分享并保存回调
     */
    suspend fun showShareOptions(
        context: Context,
        reflection: Reflection,
        onShareOnly: (() -> Unit)? = null,
        onSaveOnly: (() -> Unit)? = null,
        onShareAndSave: (() -> Unit)? = null
    ) {
        withContext(Dispatchers.Main) {
            val options = arrayOf(
                "分享到其他应用",
                "保存到相册",
                "分享并保存"
            )
            
            if (context is ComponentActivity) {
                // 这里可以显示一个选择对话框
                // 由于这是一个工具类，我们简化处理，直接调用对应的方法
                shareReflectionCard(context, reflection, saveToGallery = true)
            }
        }
    }
    
    /**
     * 检查是否有存储权限
     * @param context 上下文
     * @return 是否有权限
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 不需要存储权限
            true
        } else {
            // Android 9及以下需要检查权限
            context.checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == 
                android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 清理缓存的分享图片
     * @param context 上下文
     */
    fun cleanupSharedImages(context: Context) {
        try {
            val cachePath = File(context.cacheDir, "shared_images")
            if (cachePath.exists() && cachePath.isDirectory) {
                cachePath.listFiles()?.forEach { file ->
                    if (file.isFile && file.name.startsWith("shared_reflection_")) {
                        file.delete()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理缓存图片失败", e)
        }
    }
} 