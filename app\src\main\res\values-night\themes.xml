<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- 动态颜色主题 (Android 12+) -->
    <style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
    
    <!-- 静态主题颜色备用 -->
    <style name="Theme.TimeFlow.StaticColors">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryContainer">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorSecondaryContainer">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Tertiary colors -->
        <item name="colorTertiary">@color/primary_light</item>
        <item name="colorOnTertiary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
</resources>