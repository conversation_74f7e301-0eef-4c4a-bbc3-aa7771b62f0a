package com.timeflow.app.data.entity

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import java.time.LocalDate

/**
 * 用药记录实体类
 *
 * @property id 记录的唯一ID
 * @property medicationId 关联的药物ID
 * @property recordDate 记录日期
 * @property completedAt 完成时间戳（毫秒）
 * @property status 记录状态 (TAKEN, SKIPPED)
 * @property notes 备注
 */
@Entity(
    tableName = "medication_records",
    indices = [Index(value = ["medicationId", "recordDate"])]
)
data class MedicationRecord(
    @PrimaryKey val id: String,
    val medicationId: String,
    val recordDate: LocalDate,
    val completedAt: Long,
    val status: String, // e.g., "TAKEN", "SKIPPED"
    val notes: String? = null
) 