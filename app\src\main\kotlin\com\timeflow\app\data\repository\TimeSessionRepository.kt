package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.TimeSessionDao
import com.timeflow.app.data.dao.SessionStats
import com.timeflow.app.data.dao.TaskTimeStats
import com.timeflow.app.data.dao.TaskTimeStatsWithTags
import com.timeflow.app.data.model.TimeSession
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 时间会话仓库
 * 提供时间会话相关的数据操作方法
 * 参考知名时间管理应用的数据架构设计
 */
@Singleton
class TimeSessionRepository @Inject constructor(
    private val timeSessionDao: TimeSessionDao
) {
    
    /**
     * 插入新的时间会话
     */
    suspend fun insertSession(session: TimeSession) {
        timeSessionDao.insertSession(session)
    }
    
    /**
     * 更新时间会话
     */
    suspend fun updateSession(session: TimeSession) {
        timeSessionDao.updateSession(session.copy(updatedAt = java.time.Instant.now()))
    }
    
    /**
     * 删除时间会话
     */
    suspend fun deleteSession(session: TimeSession) {
        timeSessionDao.deleteSession(session)
    }
    
    /**
     * 根据ID获取时间会话
     */
    suspend fun getSessionById(sessionId: String): TimeSession? {
        return timeSessionDao.getSessionById(sessionId)
    }
    
    /**
     * 获取所有时间会话
     */
    fun getAllSessions(): Flow<List<TimeSession>> {
        return timeSessionDao.getAllSessions()
    }
    
    /**
     * 获取指定日期的时间会话
     */
    fun getSessionsByDate(date: LocalDate): Flow<List<TimeSession>> {
        val dateString = date.toString()
        return timeSessionDao.getSessionsByDate(dateString)
    }
    
    /**
     * 获取指定任务的所有时间会话
     */
    fun getSessionsByTaskId(taskId: String): Flow<List<TimeSession>> {
        return timeSessionDao.getSessionsByTaskId(taskId)
    }
    
    /**
     * 获取指定任务的总计时时长
     */
    fun getTaskTotalTime(taskId: String): Flow<Long> {
        return timeSessionDao.getTaskTotalTime(taskId)
    }
    
    /**
     * 获取今日总计时时长
     */
    fun getTodayTotalTime(): Flow<Long> {
        return timeSessionDao.getTodayTotalTime()
    }
    
    /**
     * 获取本周总计时时长
     */
    fun getWeekTotalTime(): Flow<Long> {
        val now = LocalDate.now()
        val weekStart = now.minusDays(now.dayOfWeek.value - 1L)
        val weekEnd = weekStart.plusDays(6)
        
        val weekStartEpoch = weekStart.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000
        val weekEndEpoch = weekEnd.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000
        
        return timeSessionDao.getWeekTotalTime(weekStartEpoch, weekEndEpoch)
    }
    
    /**
     * 获取本月总计时时长
     */
    fun getMonthTotalTime(): Flow<Long> {
        return timeSessionDao.getMonthTotalTime()
    }
    
    /**
     * 获取进行中的会话（用于恢复计时状态）
     */
    suspend fun getActiveSession(): TimeSession? {
        return timeSessionDao.getActiveSession()
    }
    
    /**
     * 获取指定时间范围内的会话统计
     */
    suspend fun getSessionStats(startDate: LocalDate, endDate: LocalDate): SessionStats {
        val startEpoch = startDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000
        val endEpoch = endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000
        
        return timeSessionDao.getSessionStats(startEpoch, endEpoch)
    }
    
    /**
     * 获取任务时间统计
     */
    fun getTaskTimeStats(startDate: LocalDate, endDate: LocalDate): Flow<List<TaskTimeStats>> {
        val startEpoch = startDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000
        val endEpoch = endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000
        
        return timeSessionDao.getTaskTimeStats(startEpoch, endEpoch)
    }
    
    /**
     * 获取包含标签信息的任务时间统计
     */
    fun getTaskTimeStatsWithTags(startDate: LocalDate, endDate: LocalDate): Flow<List<TaskTimeStatsWithTags>> {
        val startEpoch = startDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000
        val endEpoch = endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000
        
        return timeSessionDao.getTaskTimeStatsWithTags(startEpoch, endEpoch)
    }
    
    /**
     * 清理旧的会话记录（保留最近30天）
     */
    suspend fun cleanupOldSessions(daysToKeep: Int = 30) {
        val cutoffDate = LocalDate.now().minusDays(daysToKeep.toLong())
        val cutoffEpoch = cutoffDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000
        timeSessionDao.deleteOldSessions(cutoffEpoch)
    }
    
    /**
     * 获取最近的会话记录
     */
    fun getRecentSessions(limit: Int = 10): Flow<List<TimeSession>> {
        return timeSessionDao.getRecentSessions(limit)
    }
    
    /**
     * 获取今日会话摘要
     */
    suspend fun getTodaySessionSummary(): SessionSummary {
        val today = LocalDate.now()
        val todayStats = getSessionStats(today, today)
        val todaySessions = getSessionsByDate(today)
        
        return SessionSummary(
            totalSessions = todayStats.sessionCount,
            totalDuration = todayStats.totalDuration,
            averageDuration = todayStats.avgDuration,
            date = today
        )
    }
    
    /**
     * 获取本周会话摘要
     */
    suspend fun getWeekSessionSummary(): SessionSummary {
        val now = LocalDate.now()
        val weekStart = now.minusDays(now.dayOfWeek.value - 1L)
        val weekEnd = weekStart.plusDays(6)
        
        val weekStats = getSessionStats(weekStart, weekEnd)
        
        return SessionSummary(
            totalSessions = weekStats.sessionCount,
            totalDuration = weekStats.totalDuration,
            averageDuration = weekStats.avgDuration,
            date = weekStart
        )
    }
    
    /**
     * 获取专注力趋势数据（最近7天）
     */
    suspend fun getFocusTrendData(): List<DailyFocusData> {
        val focusData = mutableListOf<DailyFocusData>()
        val today = LocalDate.now()
        
        for (i in 6 downTo 0) {
            val date = today.minusDays(i.toLong())
            val dayStats = getSessionStats(date, date)
            
            focusData.add(
                DailyFocusData(
                    date = date,
                    totalTime = dayStats.totalDuration,
                    sessionCount = dayStats.sessionCount,
                    averageSession = dayStats.avgDuration
                )
            )
        }
        
        return focusData
    }
}

/**
 * 会话摘要数据类
 */
data class SessionSummary(
    val totalSessions: Int,
    val totalDuration: Long,
    val averageDuration: Double,
    val date: LocalDate
)

/**
 * 每日专注数据类
 */
data class DailyFocusData(
    val date: LocalDate,
    val totalTime: Long,
    val sessionCount: Int,
    val averageSession: Double
) 