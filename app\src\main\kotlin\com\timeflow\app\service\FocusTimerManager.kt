package com.timeflow.app.service

import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 专注计时管理器
 * 负责管理FocusTimerService的启动和停止
 */
@Singleton
class FocusTimerManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "FocusTimerManager"
    }
    
    private var isServiceRunning = false
    
    /**
     * 启动专注计时前台服务
     */
    fun startFocusTimerService(taskName: String) {
        Log.d(TAG, "🚀 启动专注计时服务: $taskName")
        
        try {
            val intent = Intent(context, FocusTimerService::class.java).apply {
                action = FocusTimerService.ACTION_START_TIMER
                putExtra(FocusTimerService.EXTRA_TASK_NAME, taskName)
            }
            
            context.startForegroundService(intent)
            isServiceRunning = true
            
            Log.d(TAG, "✅ 专注计时服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动专注计时服务失败", e)
        }
    }
    
    /**
     * 停止专注计时前台服务
     */
    fun stopFocusTimerService() {
        Log.d(TAG, "⏹️ 停止专注计时服务")
        
        try {
            val intent = Intent(context, FocusTimerService::class.java).apply {
                action = FocusTimerService.ACTION_STOP_TIMER
            }
            
            context.startService(intent)
            isServiceRunning = false
            
            Log.d(TAG, "✅ 专注计时服务停止成功")
        } catch (e: Exception) {
            Log.e(TAG, "停止专注计时服务失败", e)
        }
    }
    
    /**
     * 检查服务是否正在运行
     */
    fun isServiceRunning(): Boolean = isServiceRunning
    
    /**
     * 更新任务名称
     */
    fun updateTaskName(taskName: String) {
        if (isServiceRunning) {
            Log.d(TAG, "📝 更新任务名称: $taskName")
            try {
                val intent = Intent(context, FocusTimerService::class.java).apply {
                    action = FocusTimerService.ACTION_START_TIMER
                    putExtra(FocusTimerService.EXTRA_TASK_NAME, taskName)
                }
                
                context.startService(intent)
            } catch (e: Exception) {
                Log.e(TAG, "更新任务名称失败", e)
            }
        }
    }
} 