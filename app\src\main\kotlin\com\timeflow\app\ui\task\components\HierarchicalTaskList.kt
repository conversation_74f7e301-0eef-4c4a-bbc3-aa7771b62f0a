package com.timeflow.app.ui.task.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.border
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.ChevronRight
import androidx.compose.material.icons.outlined.Circle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.screen.task.TaskViewModel
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.task.components.common.BaseTaskCard
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.max
import timber.log.Timber
import com.timeflow.app.ui.task.components.TaskGroupHeader

/**
 * 树形任务列表 - 更好地体现Notion风格的树形结构
 * 
 * 特性:
 * 1. Notion风格的任务卡片
 * 2. 清晰的层级缩进视觉效果
 * 3. 平滑的展开/折叠动画
 * 4. 子任务进度统计
 * 5. 优先级颜色编码
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun HierarchicalTaskList(
    tasks: List<TaskData>,
    viewModel: TaskViewModel,
    onTaskClick: (TaskData) -> Unit,
    onSubTaskClick: (SubTask) -> Unit
) {
    val density = LocalDensity.current
    val coroutineScope = rememberCoroutineScope()
    val listState = rememberLazyListState()
    
    // 展开的任务ID集合
    val expandedTasks = remember { mutableStateOf(setOf<String>()) }
    
    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize(),
        contentPadding = PaddingValues(bottom = 80.dp) // 保留底部空间，避免FAB遮挡
    ) {
        tasks.forEach { task ->
            val isExpanded = expandedTasks.value.contains(task.id)
            // 分组标题
            item(key = "header_$task.id") {
                TaskGroupHeader(
                    title = task.title,
                    count = task.subTasks.size,
                    isExpanded = isExpanded,
                    onClick = {
                        // 原先的onToggleExpand逻辑
                        expandedTasks.value = if (expandedTasks.value.contains(task.id)) {
                            expandedTasks.value - task.id
                        } else {
                            expandedTasks.value + task.id
                        }
                    }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 分组中的任务
            items(
                items = task.subTasks,
                key = { subTask -> subTask.id }
            ) { subTask ->
                // 使用安全调用和默认值处理子任务的属性
                val hasChildren = false // 子任务暂不支持嵌套
                
                // 主任务
                HierarchicalTaskItem(
                    task = subTask,
                    isExpanded = isExpanded,
                    hasSubtasks = hasChildren,
                    onTaskClick = { onSubTaskClick(subTask) },
                    onExpandClick = {
                        if (hasChildren) {
                            val isCurrentlyExpanded = expandedTasks.value.contains(subTask.id)
                            if (isCurrentlyExpanded) {
                                expandedTasks.value = expandedTasks.value - subTask.id
                            } else {
                                expandedTasks.value = expandedTasks.value + subTask.id
                            }
                        }
                    },
                    onStatusChange = { isChecked ->
                        // 更新子任务状态的逻辑
                        viewModel.toggleSubTaskCompletion(subTask.id, isChecked)
                    }
                )
            }
            
            // 分组底部边距
            item(key = "footer_$task.id") {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * Header displaying optimization status
 */
@Composable
private fun OptimizationHeader(
    isOptimizing: Boolean,
    totalTasks: Int,
    visibleTasks: Int,
    onToggleOptimize: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.7f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Speed,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.secondary
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = if (isOptimizing) "Performance Mode" else "Full View Mode",
                    style = MaterialTheme.typography.titleSmall
                )
                
                Text(
                    text = if (isOptimizing) 
                        "Showing $visibleTasks of $totalTasks tasks for better performance"
                    else
                        "Showing all $totalTasks tasks (may affect performance)",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Switch(
                checked = !isOptimizing,
                onCheckedChange = { onToggleOptimize() }
            )
        }
    }
}

/**
 * Button to load more tasks when in optimization mode
 */
@Composable
private fun LoadMoreButton(
    remaining: Int,
    onLoadMore: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onLoadMore,
        modifier = modifier
    ) {
        Icon(
            imageVector = Icons.Default.MoreHoriz,
            contentDescription = null
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text("Load $remaining more tasks")
    }
}

/**
 * Hierarchical task item with expandable children
 */
@Composable
private fun HierarchicalTaskItem(
    task: com.timeflow.app.ui.screen.task.SubTask, // 明确指定SubTask类型
    isExpanded: Boolean,
    hasSubtasks: Boolean,
    onTaskClick: () -> Unit,
    onExpandClick: () -> Unit,
    onStatusChange: (Boolean) -> Unit
) {
    // 使用SubTask的属性
    val isCompleted = task.isCompleted
    
    Column(modifier = Modifier.padding(1.dp)) {
        // Task item row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    if (isExpanded) MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f) 
                    else Color.Transparent
                )
                .clickable { onTaskClick() }
                .padding(
                    start = 16.dp,
                    end = 16.dp,
                    top = 8.dp,
                    bottom = 8.dp
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Expand/collapse button
            if (hasSubtasks) {
                IconButton(
                    onClick = { onExpandClick() },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = if (isExpanded) "Collapse" else "Expand",
                        modifier = Modifier
                            .size(20.dp)
                            .rotate(if (isExpanded) 0f else -90f),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                // Spacer for alignment when no children
                Spacer(modifier = Modifier.width(24.dp))
            }
            
            // Task details (simplified for better performance)
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Checkbox(
                    checked = isCompleted,
                    onCheckedChange = { checked -> onStatusChange(checked) },
                    modifier = Modifier.padding(end = 8.dp)
                )
                
                Column {
                    Text(
                        text = task.title,
                        style = MaterialTheme.typography.bodyMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = if (isCompleted) 
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        else
                            MaterialTheme.colorScheme.onSurface
                    )
                    
                    if (hasSubtasks) {
                        Text(
                            text = "包含子任务",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            fontSize = 12.sp
                        )
                    }
                }
            }
            
            // Task priority indicator if applicable
            if (task.priority != null && task.priority != Priority.LOW) {
                val priorityColor = when (task.priority) {
                    Priority.MEDIUM -> Color(0xFFFFA114) // Medium
                    Priority.HIGH -> Color(0xFFFF9500) // High
                    Priority.URGENT -> Color(0xFFFF3B30) // Urgent
                    else -> Color.Gray
                }
                
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(priorityColor, MaterialTheme.shapes.small)
                )
            }
        }
    }
}

/**
 * 子任务项组件
 */
@Composable
private fun SubTaskItem(
    task: com.timeflow.app.ui.screen.task.SubTask,
    onTaskClick: () -> Unit,
    onStatusChange: (Boolean) -> Unit
) {
    // 子任务项的UI实现
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 32.dp, end = 16.dp, top = 4.dp, bottom = 4.dp)
            .clickable { onTaskClick() },
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 状态切换按钮
        Checkbox(
            checked = task.isCompleted,
            onCheckedChange = onStatusChange
        )
        
        // 子任务标题
        Text(
            text = task.title,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(start = 8.dp)
        )
    }
}

/**
 * Notion风格任务卡片
 */
@Composable
private fun NotionStyleTaskCard(
    task: ModelTask,
    hasChildren: Boolean,
    isExpanded: Boolean,
    onExpandToggle: (String) -> Unit,
    onTaskClick: (String) -> Unit,
    onStatusChange: (Boolean) -> Unit,
    dateFormatter: DateTimeFormatter,
    level: Int
) {
    val isCompleted = task.completedAt != null
    val rotationAngle by animateFloatAsState(
        targetValue = if (isExpanded) 90f else 0f,
        label = "Rotation Animation"
    )
    
    val priorityColor = when (task.priority) {
        Priority.LOW -> Color(0xFF4CAF50) // 低优先级，绿色
        Priority.MEDIUM -> Color(0xFFFFA000) // 中优先级，橙色
        Priority.HIGH -> Color(0xFFE53935) // 高优先级，红色
        Priority.URGENT -> Color(0xFFE53935) // 紧急，红色
        null -> Color.Gray
    }
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp, horizontal = 4.dp)
            .clickable { onTaskClick(task.id) },
        color = Color(0xFFFAFAFA),
        shape = RoundedCornerShape(4.dp),
        tonalElevation = 0.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 层级缩进指示线
            if (level > 0) {
                Box(
                    modifier = Modifier
                        .width((level * 16).dp)
                        .height(24.dp)
                        .padding(end = 8.dp)
                ) {
                    // 垂直缩进线
                    for (i in 1..level) {
                        Box(
                            modifier = Modifier
                                .width(1.dp)
                                .fillMaxHeight()
                                .alpha(0.2f)
                                .background(MaterialTheme.colorScheme.outline)
                                .align(Alignment.CenterEnd)
                                .offset(x = (-16.dp * (level - i)))
                        )
                    }
                }
            } else {
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            // 展开/收起按钮
            if (hasChildren) {
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null
                        ) { onExpandToggle(task.id) },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = if (isExpanded) "收起" else "展开",
                        modifier = Modifier
                            .size(16.dp)
                            .rotate(rotationAngle),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                Spacer(modifier = Modifier.width(20.dp))
            }
            
            Spacer(modifier = Modifier.width(4.dp))
            
            // 任务完成状态复选框 - Notion风格
            Box(
                modifier = Modifier
                    .size(18.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(
                        if (isCompleted) 
                            if (task.priority == Priority.HIGH || task.priority == Priority.URGENT) priorityColor else MaterialTheme.colorScheme.primary
                        else 
                            Color.White
                    )
                    .border(
                        width = 1.dp,
                        color = if (task.priority == Priority.HIGH || task.priority == Priority.URGENT) priorityColor.copy(alpha = 0.7f) else MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                        shape = RoundedCornerShape(2.dp)
                    )
                    .clickable { onStatusChange(isCompleted) },
                contentAlignment = Alignment.Center
            ) {
                if (isCompleted) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 任务内容区域
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 任务标题 - Notion风格
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = if (isCompleted) FontWeight.Normal else FontWeight.Medium
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textDecoration = if (isCompleted) TextDecoration.LineThrough else TextDecoration.None,
                    color = if (isCompleted) 
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f) 
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                
                // 子任务计数和进度
                if (hasChildren || task.progress > 0f) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = 4.dp)
                    ) {
                        LinearProgressIndicator(
                            progress = { task.progress },
                            modifier = Modifier
                                .height(3.dp)
                                .width(60.dp)
                                .clip(RoundedCornerShape(1.5.dp)),
                            color = MaterialTheme.colorScheme.primary,
                            trackColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "${(task.progress * 100).toInt()}%",
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            
            // 日期信息
            if (task.dueDate != null) {
                val isPastDue = task.dueDate.isBefore(LocalDateTime.now()) && !isCompleted
                
                // Notion风格日期标签
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(3.dp))
                        .background(
                            color = if (isPastDue) 
                                MaterialTheme.colorScheme.error.copy(alpha = 0.1f)
                            else 
                                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                ) {
                    Text(
                        text = dateFormatter.format(task.dueDate),
                        fontSize = 11.sp,
                        color = if (isPastDue) 
                            MaterialTheme.colorScheme.error
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun TaskItemWithAnimation(
    task: ModelTask,
    isExpanded: Boolean,
    hasSubtasks: Boolean,
    onTaskClick: (String) -> Unit,
    onExpandClick: (String) -> Unit,
    onStatusChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(vertical = 4.dp)
    ) {
        // 将ModelTask转换为SubTask以匹配HierarchicalTaskItem的参数类型
        val subTask = com.timeflow.app.ui.screen.task.SubTask(
            id = task.id,
            title = task.title,
            isCompleted = task.completedAt != null,
            priority = task.priority ?: com.timeflow.app.data.model.Priority.MEDIUM,
            note = task.description,
            reminderTime = null,
            parentTaskId = task.parentTaskId,
            dueDate = task.dueDate
        )
        
        HierarchicalTaskItem(
            task = subTask,
            isExpanded = isExpanded,
            hasSubtasks = hasSubtasks,
            onTaskClick = { onTaskClick(task.id) },
            onExpandClick = { onExpandClick(task.id) },
            onStatusChange = onStatusChange
        )
    }
}

// 修复Task和SubTask类型不匹配的问题
@Composable
private fun renderSubTaskItems(
    subTask: com.timeflow.app.ui.screen.task.SubTask,
    onTaskClick: (String) -> Unit,
    onStatusChange: (Boolean) -> Unit
) {
    // 渲染子任务项
    SubTaskItem(
        task = subTask,
        onTaskClick = { onTaskClick(subTask.id) }, // 将String参数的函数包装成无参函数
        onStatusChange = { checked -> onStatusChange(checked) } // 保证参数类型正确
    )
} 