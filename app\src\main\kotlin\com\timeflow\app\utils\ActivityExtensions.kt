package com.timeflow.app.utils

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.activity.ComponentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import android.content.Context
import android.view.WindowManager
import androidx.core.view.WindowCompat
import java.io.File
import java.io.FileOutputStream
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*
import java.lang.System
import android.os.Handler
import android.os.Looper
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.timeflow.app.utils.RenderOptimizer

private const val TAG = "ActivityExt"

/**
 * 应用所有的稳定性和性能优化
 * 处理日志中显示的崩溃和性能问题
 */
fun Activity.applyAllOptimizations() {
    try {
        Log.d(TAG, "开始应用所有优化: ${this.javaClass.simpleName}")
        
        // 使用新的HwcLutsErrorFixer替代旧的修复方法
        HwcLutsErrorFixer.applyCompleteFix(this)
        
        // 修复timerslack相关错误
        RenderOptimizer.fixTimerSlackIssues()
        
        // 添加触摸事件监控，避免ANR
        setupTouchMonitoring()
        
        // 如果是ComponentActivity，添加生命周期优化
        if (this is ComponentActivity) {
            setupLifecycleOptimizations()
        }
        
        // 防止输入法挤压内容
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        
        // 启用边缘到边缘显示以获得更大显示空间
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
        
        // 应用内存优化
        applyMemoryOptimization()
        
        // 确保硬件加速启用
        setupHardwareAcceleration()
        
        // 设置专为HwcComposer错误准备的紧急恢复
        configureEmergencyRecovery(this)
        
        Log.d(TAG, "已应用所有优化: ${this.javaClass.simpleName}")
    } catch (e: Exception) {
        Log.e(TAG, "应用优化时出错: ${e.message}")
    }
}

/**
 * 设置生命周期优化
 * 防止常见的生命周期相关崩溃
 */
private fun ComponentActivity.setupLifecycleOptimizations() {
    // 应用HWC Luts错误修复
    // HwcLutsErrorFixer.applyCompleteFix(this@setupLifecycleOptimizations) // DISABLED: Performance tuning

    // 应用窗口渲染优化
    lifecycle.addObserver(object : LifecycleEventObserver {
        override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
            when (event) {
                Lifecycle.Event.ON_CREATE -> {
                    // 确保窗口配置正确
                    window?.let { window ->
                        applyWindowSafety(window)
                    }
                }
                Lifecycle.Event.ON_START -> {
                    // 清理任何可能的内存泄漏
                    // Runtime.getRuntime().gc() // Explicit GC is harmful
                    Log.d(TAG, "ON_START: 不再主动触发GC")
                }
                Lifecycle.Event.ON_STOP -> {
                    // 确保停止所有可能的后台工作
                    clearViewReferences()
                }
                else -> { /* 其他生命周期事件不需要特殊处理 */ }
            }
        }
    })
}

/**
 * 应用窗口安全设置
 * 避免渲染和输入相关的崩溃
 */
private fun applyWindowSafety(window: Window) {
    try {
        // 确保窗口触摸事件不会导致ANR
        window.decorView.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 长按触摸可能导致ANR，确保主线程不会被阻塞
                window.decorView.handler?.removeCallbacksAndMessages(null)
            }
            false // 不消费事件，继续传递
        }
        
        // 优化窗口绘制
        window.decorView.viewTreeObserver.addOnGlobalLayoutListener {
            try {
                // 减少过度绘制
                window.decorView.setWillNotDraw(false)
            } catch (e: Exception) {
                Log.e(TAG, "全局布局监听器错误: ${e.message}")
            }
        }
    } catch (e: Exception) {
        Log.e(TAG, "应用窗口安全设置时出错: ${e.message}")
    }
}

/**
 * 设置触摸监控
 * 检测UI冻结和ANR
 */
private fun Activity.setupTouchMonitoring() {
    try {
        val rootView = findViewById<ViewGroup>(android.R.id.content)
        rootView?.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // 开始监控此触摸事件的响应时间
                val touchStartTime = System.currentTimeMillis()
                
                rootView.postDelayed({
                    val responseDuration = System.currentTimeMillis() - touchStartTime
                    if (responseDuration > 200) {
                        // 如果触摸响应超过200ms，记录可能的UI冻结
                        Log.w(TAG, "UI响应延迟: ${responseDuration}ms")
                    }
                }, 300)
            }
            false // 不消费事件，继续传递
        }
    } catch (e: Exception) {
        Log.e(TAG, "设置触摸监控时出错: ${e.message}")
    }
}

/**
 * 清理视图引用
 * 防止内存泄漏
 */
private fun Activity.clearViewReferences() {
    try {
        val rootView = findViewById<ViewGroup>(android.R.id.content)
        clearViewGroupReferences(rootView)
    } catch (e: Exception) {
        Log.e(TAG, "清理视图引用时出错: ${e.message}")
    }
}

/**
 * 递归清理ViewGroup引用
 */
private fun clearViewGroupReferences(viewGroup: ViewGroup?) {
    viewGroup ?: return
    
    try {
        // 清理所有子视图
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)
            if (child is ViewGroup) {
                clearViewGroupReferences(child)
            } else {
                // 清理普通View的引用
                child?.let { view ->
                    view.setOnClickListener(null)
                    view.setOnTouchListener(null)
                    view.setTag(null)
                }
            }
        }
        
        // 清理ViewGroup自身
        viewGroup.setOnHierarchyChangeListener(null)
        
    } catch (e: Exception) {
        Log.e(TAG, "清理ViewGroup引用时出错: ${e.message}")
    }
}

/**
 * 安全启动Activity
 * 防止Intent相关的崩溃
 */
fun Activity.safeStartActivity(intent: Intent) {
    try {
        // 验证Intent
        if (intent.resolveActivity(packageManager) != null) {
            startActivity(intent)
        } else {
            Log.e(TAG, "无法解析Intent: ${intent.action}")
        }
    } catch (e: Exception) {
        Log.e(TAG, "启动Activity失败: ${e.message}")
    }
}

/**
 * 安全启动Activity并传递Bundle
 * 使用SafeParcelHelper防止Parcel相关错误
 */
fun Activity.safeStartActivity(intent: Intent, options: Bundle?) {
    try {
        // 验证Bundle
        val safeOptions = if (options != null && SafeParcelHelper.validateBundle(options)) {
            options
        } else {
            Log.w(TAG, "Bundle验证失败，使用空Bundle")
            Bundle()
        }
        
        // 验证Intent
        if (intent.resolveActivity(packageManager) != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                startActivity(intent, safeOptions)
            } else {
                startActivity(intent)
            }
        } else {
            Log.e(TAG, "无法解析Intent: ${intent.action}")
        }
    } catch (e: Exception) {
        Log.e(TAG, "启动Activity失败: ${e.message}")
        
        // 简化后的Intent重试
        try {
            val simpleIntent = Intent(intent.action)
            startActivity(simpleIntent)
        } catch (e2: Exception) {
            Log.e(TAG, "简化Intent重试也失败: ${e2.message}")
        }
    }
}

/**
 * 应用紧急恢复
 * 在检测到不稳定状态时恢复Activity
 */
fun Activity.doEmergencyRecovery() {
    try {
        // 根据不同的错误情况应用相应的修复
        val errorState = HwcLutsErrorFixer.getCurrentErrorState()
        
        if (errorState == HwcLutsErrorFixer.ErrorState.CRITICAL) {
            // 已经在关键模式下，尝试重新初始化视图
            refreshAllViews()
        } else {
            // 升级到更严格的修复级别
            if (errorState == HwcLutsErrorFixer.ErrorState.BASIC_FIX) {
                HwcLutsErrorFixer.applyStrictModeFix(window)
            } else {
                HwcLutsErrorFixer.applyCriticalModeFix(window)
            }
        }
        
        // 清理引用
        clearViewReferences()
        
        // 通知GC
        // Runtime.getRuntime().gc() // Explicit GC is harmful
        Log.d(TAG, "紧急恢复: 不再主动触发GC")
        
        // 刷新UI
        refreshUI()
        
        Log.w(TAG, "已应用紧急恢复措施，当前错误状态: $errorState")
    } catch (e: Exception) {
        Log.e(TAG, "紧急恢复失败: ${e.message}")
        
        // 最后手段：尝试重启Activity
        try {
            restartActivity()
        } catch (e2: Exception) {
            Log.e(TAG, "重启Activity失败: ${e2.message}")
        }
    }
}

/**
 * 刷新所有视图
 * 在严重渲染问题时重新创建视图层次结构
 */
private fun Activity.refreshAllViews() {
    try {
        val contentView = findViewById<ViewGroup>(android.R.id.content)
        contentView?.let { root ->
            // 临时移除所有子视图
            val childCount = root.childCount
            val children = Array<View?>(childCount) { null }
            
            // 先保存引用
            for (i in 0 until childCount) {
                children[i] = root.getChildAt(i)
            }
            
            // 清空视图容器
            root.removeAllViews()
            
            // 短暂延迟后重新添加
            Handler(Looper.getMainLooper()).postDelayed({
                for (i in 0 until childCount) {
                    children[i]?.let { child ->
                        if (child.parent == null) {
                            try {
                                root.addView(child)
                            } catch (e: Exception) {
                                Log.e(TAG, "重新添加视图失败: ${e.message}")
                            }
                        }
                    }
                }
                Log.d(TAG, "已刷新所有视图")
            }, 100)
        }
    } catch (e: Exception) {
        Log.e(TAG, "刷新视图失败: ${e.message}")
    }
}

/**
 * 重启Activity
 * 在紧急情况下安全重启当前活动
 */
private fun Activity.restartActivity() {
    try {
        val intent = intent
        finish()
        startActivity(intent)
        Log.w(TAG, "Activity已紧急重启")
    } catch (e: Exception) {
        Log.e(TAG, "重启Activity失败: ${e.message}")
    }
}

/**
 * 刷新UI
 * 仅刷新UI而不重新创建视图层次结构
 */
private fun Activity.refreshUI() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && !isDestroyed) {
        val rootView = findViewById<View>(android.R.id.content)
        rootView?.invalidate()
    }
}

/**
 * 配置应用程序崩溃时的紧急恢复机制
 */
fun configureEmergencyRecovery(activity: Activity) {
    val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    
    Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
        try {
            // 针对渲染相关错误的特殊处理
            if (isRenderingError(throwable)) {
                Log.e(TAG, "检测到渲染错误，尝试应用紧急修复", throwable)
                
                // 尝试在主线程应用紧急修复
                Handler(Looper.getMainLooper()).post {
                    try {
                        // 使用HwcLutsErrorFixer来处理可能的HWC相关错误
                        if (throwable.message?.contains("HwcComposer") == true || 
                            throwable.message?.contains("getLuts") == true) {
                            HwcLutsErrorFixer.applyCriticalModeFix(activity.window)
                            activity.refreshUI()
                        } else {
                            // 其他渲染错误使用doEmergencyRecovery
                            activity.doEmergencyRecovery()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "应用紧急恢复失败", e)
                    }
                }
                
                // 允许应用继续运行，而不是崩溃
                return@setDefaultUncaughtExceptionHandler
            }
            
            // 对于其他错误，使用默认处理器
            defaultHandler?.uncaughtException(thread, throwable)
        } catch (e: Exception) {
            // 如果恢复过程失败，使用默认处理器
            defaultHandler?.uncaughtException(thread, throwable)
        }
    }
}

/**
 * 检查是否为渲染错误
 */
private fun isRenderingError(throwable: Throwable): Boolean {
    val message = throwable.message?.toLowerCase() ?: ""
    val stackTrace = throwable.stackTraceToString().toLowerCase()
    
    return message.contains("hwcomposer") || 
           message.contains("surfaceflinger") || 
           message.contains("lut") || 
           message.contains("opengl") ||
           message.contains("egl") ||
           stackTrace.contains("android.view.hwrenderer") ||
           throwable is android.opengl.GLException
}

/**
 * 设置硬件加速
 * 确保硬件加速正确配置
 */
private fun Activity.setupHardwareAcceleration() {
    try {
        // 获取当前HwcLuts错误状态，决定如何设置硬件加速
        val errorState = HwcLutsErrorFixer.getCurrentErrorState()
        
        when (errorState) {
            HwcLutsErrorFixer.ErrorState.CRITICAL -> {
                // 已经在关键模式下，使用软件渲染
                Log.w(TAG, "检测到关键渲染状态，使用软件渲染以确保稳定性")
                window.setFlags(
                    0,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
            }
            HwcLutsErrorFixer.ErrorState.STRICT -> {
                // 严格模式，混合渲染
                Log.w(TAG, "检测到严格渲染状态，使用部分硬件加速")
                window.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
                window.decorView.findViewById<View>(android.R.id.content)?.apply {
                    setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                }
            }
            else -> {
                // 正常或基本修复状态，启用完全硬件加速
                window.setFlags(
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                )
            }
        }
    } catch (e: Exception) {
        Log.e(TAG, "设置硬件加速失败: ${e.message}")
    }
}

/**
 * 应用内存优化
 * 优化内存使用
 */
private fun Activity.applyMemoryOptimization() {
    try {
        // 为大型Bitmap强制使用硬件缓冲区
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 移除不存在的方法调用
            // android.graphics.HardwareRenderer.setRenderingHardwareBuffersEnabled(true)
        }
    } catch (e: Exception) {
        Log.e(TAG, "应用内存优化失败: ${e.message}")
    }
}

/**
 * 保存崩溃日志到文件
 */
private fun saveCrashLog(context: Context, throwable: Throwable) {
    try {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val filename = "crash_$timestamp.log"
        
        val crashDir = File(context.filesDir, "crash_logs")
        if (!crashDir.exists()) {
            crashDir.mkdirs()
        }
        
        val file = File(crashDir, filename)
        val writer = PrintWriter(FileOutputStream(file))
        
        // 写入设备信息
        writer.println("----- 设备信息 -----")
        writer.println("设备: ${Build.MANUFACTURER} ${Build.MODEL}")
        writer.println("Android 版本: ${Build.VERSION.RELEASE} (SDK ${Build.VERSION.SDK_INT})")
        writer.println("App 版本: ${context.packageManager.getPackageInfo(context.packageName, 0).versionName}")
        writer.println("")
        
        // 写入异常堆栈
        writer.println("----- 异常信息 -----")
        val sw = StringWriter()
        val pw = PrintWriter(sw)
        throwable.printStackTrace(pw)
        writer.println(sw.toString())
        
        writer.close()
        
        Log.i("CrashLogger", "崩溃日志已保存至: $file")
    } catch (e: Exception) {
        Log.e("CrashLogger", "保存崩溃日志失败: ${e.message}")
    }
}

/**
 * 监控HwcComposer getLuts错误
 */
private fun Activity.setupHwcLutsErrorMonitoring() {
    // 此方法已不再需要，由HwcLutsErrorFixer.applyCompleteFix完全替代
    // 保留此空方法以向后兼容现有代码
    Log.d(TAG, "HwcLuts错误监控已由HwcLutsErrorFixer接管")
} 