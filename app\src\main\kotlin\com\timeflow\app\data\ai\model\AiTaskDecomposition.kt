package com.timeflow.app.data.ai.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * AI任务拆分数据模型 - 表示复杂任务的智能分解结果
 */
data class AiTaskDecomposition(
    val id: String = UUID.randomUUID().toString(),
    val originalTaskId: String,
    val subTasks: List<AiSubTask> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val aiRecommendation: String? = null,
    val reasonForDecomposition: String? = null,
    val totalEstimatedMinutes: Int = 0
)

/**
 * AI子任务数据模型 - 表示拆分后的子任务
 */
data class AiSubTask(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val estimatedMinutes: Int = 0,
    val suggestedStartTime: LocalDateTime? = null,
    val order: Int = 0,
    val dependsOn: List<String> = emptyList(), // 依赖的其他子任务ID
    val recommendedTimeOfDay: TimeOfDay? = null,
    val reason: String? = null, // 为什么这样拆分的理由
    val difficulty: TaskDifficulty = TaskDifficulty.MEDIUM
)

/**
 * 任务难度级别
 */
enum class TaskDifficulty {
    EASY, MEDIUM, HARD
}

/**
 * 推荐时间段
 */
enum class TimeOfDay {
    EARLY_MORNING, // 早晨 (5:00-8:00)
    MORNING,       // 上午 (8:00-12:00)
    AFTERNOON,     // 下午 (12:00-17:00)
    EVENING,       // 傍晚 (17:00-20:00)
    NIGHT          // 晚上 (20:00-23:00)
}

/**
 * 获取时间段的显示文本
 */
fun TimeOfDay.displayText(): String {
    return when(this) {
        TimeOfDay.EARLY_MORNING -> "早晨 (5:00-8:00)"
        TimeOfDay.MORNING -> "上午 (8:00-12:00)"
        TimeOfDay.AFTERNOON -> "下午 (12:00-17:00)"
        TimeOfDay.EVENING -> "傍晚 (17:00-20:00)"
        TimeOfDay.NIGHT -> "晚上 (20:00-23:00)"
    }
} 