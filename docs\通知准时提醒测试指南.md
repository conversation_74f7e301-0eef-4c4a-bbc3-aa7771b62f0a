# 通知准时提醒功能测试指南

## 🎯 测试目标

验证修复后的通知系统能够正确遵循用户的"准时通知"设置，不再提前发送通知。

## 🔧 修复内容回顾

### 主要修复点
1. **移除硬编码提前时间**：紧急任务不再强制提前5分钟
2. **统一提醒时间计算**：所有任务都遵循用户的 `defaultReminderTime` 设置
3. **修复WorkManager延迟**：添加 `setInitialDelay()` 确保在正确时间触发
4. **修复默认值**：将默认提醒时间从15分钟改为0分钟（准时通知）

## 📋 测试场景

### 场景1：准时通知测试
**目标**：验证设置为"准时通知"时，通知在指定时间准时发送

**步骤**：
1. 进入设置 → 通知设置
2. 将"默认提醒时间"设置为"准时通知"（0分钟）
3. 创建一个任务，设置开始时间为当前时间后5分钟
4. 等待通知发送
5. **预期结果**：通知在设置的开始时间准时发送

**验证方法**：
- 检查通知发送时间是否与设置的开始时间一致
- 查看日志：应显示"使用开始时间进行准时提醒（用户设置0分钟）"

### 场景2：提前通知测试
**目标**：验证设置提前时间后，通知按设置的时间提前发送

**步骤**：
1. 将"默认提醒时间"设置为"提前15分钟"
2. 创建任务，设置开始时间为当前时间后20分钟
3. 等待通知发送
4. **预期结果**：通知在开始时间前15分钟发送

**验证方法**：
- 通知应在设置时间前15分钟收到
- 日志显示："使用开始时间前15分钟提醒（用户配置）"

### 场景3：紧急任务测试
**目标**：验证紧急任务也遵循用户设置，不再硬编码提前时间

**步骤**：
1. 设置"默认提醒时间"为"准时通知"
2. 创建紧急任务（优先级=紧急）
3. 设置开始时间
4. **预期结果**：紧急任务也准时通知，不提前5分钟

**验证方法**：
- 紧急任务的通知时间与普通任务一致
- 不再有"紧急任务提前5分钟"的特殊处理

### 场景4：截止时间任务测试
**目标**：验证只有截止时间的任务也遵循用户设置

**步骤**：
1. 设置"默认提醒时间"为"提前30分钟"
2. 创建任务，只设置截止时间（不设置开始时间）
3. **预期结果**：在截止时间前30分钟收到通知

**验证方法**：
- 通知在截止时间前30分钟发送
- 不再是硬编码的"截止时间前1小时"

### 场景5：设置持久化测试
**目标**：验证设置在应用重启后仍然有效

**步骤**：
1. 设置"默认提醒时间"为"准时通知"
2. 重启应用
3. 创建新任务
4. **预期结果**：设置保持不变，仍为准时通知

## 🔍 日志验证

### 关键日志信息

**准时通知日志**：
```
使用开始时间进行准时提醒（用户设置0分钟）: 2024-01-15T10:00
```

**提前通知日志**：
```
使用开始时间前15分钟提醒（用户配置）: 2024-01-15T10:00 -> 2024-01-15T09:45
```

**WorkManager延迟日志**：
```
提醒已调度: 测试任务 (task_start_reminder) at 2024-01-15T10:00
```

### 错误日志排查

如果出现问题，检查以下日志：
- `TaskReminderUtils`: 提醒时间计算逻辑
- `TaskReminderScheduler`: 调度逻辑
- `TaskReminderWorker`: WorkManager执行
- `TaskAlarmReceiver`: 通知发送

## 🧪 自动化测试

### 单元测试验证

可以创建以下单元测试：

```kotlin
@Test
fun testOnTimeNotification() {
    val settings = NotificationSettings(defaultReminderTime = 0)
    val task = createTestTask(startDate = LocalDateTime.now().plusMinutes(5))
    
    val reminderTime = TaskReminderUtils.calculateReminderTime(task, settings)
    
    assertEquals(task.startDate, reminderTime)
}

@Test
fun testAdvanceNotification() {
    val settings = NotificationSettings(defaultReminderTime = 15)
    val task = createTestTask(startDate = LocalDateTime.now().plusMinutes(20))
    
    val reminderTime = TaskReminderUtils.calculateReminderTime(task, settings)
    
    assertEquals(task.startDate.minusMinutes(15), reminderTime)
}
```

## 📱 用户界面验证

### 设置页面检查
1. 进入通知设置页面
2. 确认"默认提醒时间"选项包含：
   - ✅ 准时通知（0分钟）
   - ✅ 提前5分钟
   - ✅ 提前10分钟
   - ✅ 提前15分钟
   - ✅ 提前30分钟
   - ✅ 提前1小时

### 任务创建验证
1. 创建任务时设置开始时间
2. 确认系统使用用户设置的提醒时间
3. 验证不同优先级任务的处理一致性

## 🚨 常见问题排查

### 问题1：通知仍然提前发送
**可能原因**：
- 设置未正确保存
- 代码中仍有硬编码逻辑
- WorkManager缓存了旧的调度

**解决方法**：
- 检查DataStore设置保存
- 清除应用数据重新测试
- 查看日志确认使用的提醒时间

### 问题2：通知没有发送
**可能原因**：
- 通知权限未授予
- WorkManager延迟计算错误
- 系统省电模式影响

**解决方法**：
- 检查通知权限
- 验证WorkManager调度
- 关闭省电模式测试

### 问题3：设置不生效
**可能原因**：
- DataStore读取失败
- 默认值覆盖用户设置
- 缓存问题

**解决方法**：
- 检查DataStore读写日志
- 验证PreferencesKeys一致性
- 重启应用测试

## 📊 测试报告模板

### 测试结果记录

| 测试场景 | 设置值 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|--------|----------|----------|------|------|
| 准时通知 | 0分钟 | 10:00发送 | 10:00发送 | ✅ | 正常 |
| 提前15分钟 | 15分钟 | 09:45发送 | 09:45发送 | ✅ | 正常 |
| 紧急任务 | 0分钟 | 10:00发送 | 10:00发送 | ✅ | 不再提前 |
| 截止时间 | 30分钟 | 09:30发送 | 09:30发送 | ✅ | 遵循设置 |

### 性能指标

- **设置响应时间**：< 100ms
- **通知准确性**：±5秒内
- **内存使用**：无明显增加
- **电池消耗**：无异常

## 🎉 验收标准

修复被认为成功的标准：

1. ✅ "准时通知"设置真正实现准时发送
2. ✅ 所有优先级任务都遵循统一的用户设置
3. ✅ 不再有硬编码的提前时间
4. ✅ 设置在应用重启后保持有效
5. ✅ 日志输出清晰反映实际使用的提醒时间
6. ✅ 用户体验符合预期

## 📝 测试注意事项

1. **时间精度**：测试时考虑系统时间的精度限制
2. **网络环境**：确保在稳定的网络环境下测试
3. **设备差异**：在不同Android版本和设备上验证
4. **电源管理**：注意系统的电源优化设置
5. **权限状态**：确保通知权限已正确授予

通过以上测试，可以全面验证通知提前发送问题已经得到彻底解决。
