package com.timeflow.app.ui.screen.discover

import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.task.components.AiSuggestion
import com.timeflow.app.ui.task.components.AiSuggestionArea
import com.timeflow.app.ui.theme.AppBackground
import kotlinx.coroutines.launch

/**
 * 发现页面 - 集成了AI建议功能
 */
@Composable
fun DiscoverScreen(
    navController: NavController,
    viewModel: DiscoverViewModel = hiltViewModel()
) {
    // 从ViewModel获取AI建议状态
    val aiSuggestions by viewModel.aiSuggestions.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val scope = rememberCoroutineScope()
    
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = AppBackground
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            // 标题
            Text(
                text = "智能发现",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 24.dp)
            )
            
            // 加载状态
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 错误状态
            if (error != null) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    shape = MaterialTheme.shapes.medium,
                    color = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.7f)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "出错了",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = error ?: "未知错误",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Button(
                            onClick = {
                                viewModel.refreshSuggestions()
                            }
                        ) {
                            Text("重试")
                        }
                    }
                }
            }
            
            // AI建议区域
            if (!isLoading && error == null) {
                if (aiSuggestions.isNotEmpty()) {
                    Text(
                        text = "根据您的日程和习惯，AI为您提供以下任务建议",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    AiSuggestionArea(
                        suggestions = aiSuggestions,
                        onAddSuggestion = { suggestion ->
                            // 处理添加建议
                            Log.d("DiscoverScreen", "添加建议: ${suggestion.text}")
                            scope.launch {
                                try {
                                    viewModel.addTaskFromSuggestion(suggestion)
                                } catch (e: Exception) {
                                    Log.e("DiscoverScreen", "添加建议任务失败: ${e.message}", e)
                                }
                            }
                        },
                        onDismissSuggestion = { suggestion ->
                            // 处理忽略建议
                            Log.d("DiscoverScreen", "忽略建议: ${suggestion.text}")
                            viewModel.dismissSuggestion(suggestion)
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    // 无建议时的展示
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        shape = MaterialTheme.shapes.medium,
                        color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                    ) {
                        Column(
                            modifier = Modifier.padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "暂无智能建议",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "随着您使用应用的时间增加，AI将会学习您的习惯并提供更多个性化建议",
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Button(
                                onClick = {
                                    // 刷新建议
                                    Log.d("DiscoverScreen", "请求刷新AI建议")
                                    viewModel.refreshSuggestions()
                                }
                            ) {
                                Text("刷新建议")
                            }
                        }
                    }
                }
            }
            
            // 更多发现功能可以在此扩展
            Spacer(modifier = Modifier.height(32.dp))
            
            Text(
                text = "更多智能功能开发中...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
} 