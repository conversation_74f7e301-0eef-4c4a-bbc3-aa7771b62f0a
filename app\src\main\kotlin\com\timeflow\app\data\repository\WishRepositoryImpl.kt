package com.timeflow.app.data.repository

import com.timeflow.app.data.dao.WishDao
import com.timeflow.app.data.entity.Wish
import com.timeflow.app.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WishRepositoryImpl @Inject constructor(
    private val wishDao: WishDao
) : WishRepository {

    private val json = Json { ignoreUnknownKeys = true }

    override fun getAllActiveWishes(): Flow<List<WishModel>> {
        return wishDao.getAllActiveWishes().map { entities ->
            entities.map { it.toModel() }
        }
    }

    override fun getAllArchivedWishes(): Flow<List<WishModel>> {
        return wishDao.getAllArchivedWishes().map { entities ->
            entities.map { it.toModel() }
        }
    }

    override fun getAllWishes(): Flow<List<WishModel>> {
        return wishDao.getAllWishes().map { entities ->
            entities.map { it.toModel() }
        }
    }

    override suspend fun getWishById(wishId: String): WishModel? {
        return wishDao.getWishById(wishId)?.toModel()
    }

    override fun getWishesByCategory(category: String): Flow<List<WishModel>> {
        return wishDao.getWishesByCategory(category).map { entities ->
            entities.map { it.toModel() }
        }
    }

    override fun getWishesByPriority(minPriority: Int): Flow<List<WishModel>> {
        return wishDao.getWishesByPriority(minPriority).map { entities ->
            entities.map { it.toModel() }
        }
    }

    override fun getWishesByStatus(status: String): Flow<List<WishModel>> {
        return wishDao.getWishesByStatus(status).map { entities ->
            entities.map { it.toModel() }
        }
    }

    override suspend fun getAllCategories(): List<String> {
        return wishDao.getAllCategories()
    }

    override suspend fun getActiveWishCount(): Int {
        return wishDao.getActiveWishCount()
    }

    override suspend fun getConvertedWishCount(): Int {
        return wishDao.getConvertedWishCount()
    }

    override suspend fun insertWish(wish: WishModel) {
        wishDao.insertWish(wish.toEntity())
    }

    override suspend fun insertWishes(wishes: List<WishModel>) {
        wishDao.insertWishes(wishes.map { it.toEntity() })
    }

    override suspend fun updateWish(wish: WishModel) {
        wishDao.updateWish(wish.toEntity())
    }

    override suspend fun deleteWish(wish: WishModel) {
        wishDao.deleteWish(wish.toEntity())
    }

    override suspend fun deleteWishById(wishId: String) {
        wishDao.deleteWishById(wishId)
    }

    override suspend fun archiveWish(wishId: String) {
        wishDao.archiveWish(wishId, LocalDateTime.now())
    }

    override suspend fun unarchiveWish(wishId: String) {
        wishDao.unarchiveWish(wishId)
    }

    override suspend fun convertWishToGoal(wishId: String, goalId: String) {
        wishDao.convertWishToGoal(wishId, goalId, LocalDateTime.now())
    }

    override suspend fun markWishAsAchieved(wishId: String) {
        wishDao.markWishAsAchieved(wishId, LocalDateTime.now())
    }

    override fun searchWishes(searchQuery: String): Flow<List<WishModel>> {
        return wishDao.searchWishes(searchQuery).map { entities ->
            entities.map { it.toModel() }
        }
    }

    // 转换扩展函数
    private fun Wish.toModel(): WishModel {
        return WishModel(
            id = id,
            title = title,
            description = description,
            category = WishCategory.values().find { it.name == category } ?: WishCategory.OTHER,
            priority = priority,
            inspirationItems = parseStringList(inspirationItems),
            imageUris = parseStringList(imageUris),
            estimatedCost = estimatedCost,
            targetTimePeriod = targetTimePeriod,
            tags = parseStringList(tags),
            status = WishStatus.values().find { it.name == status } ?: WishStatus.ACTIVE,
            relatedGoalId = relatedGoalId,
            createdAt = createdAt,
            updatedAt = updatedAt,
            achievedAt = achievedAt,
            archivedAt = archivedAt,
            isArchived = isArchived,
            difficulty = WishDifficulty.values().find { it.name == difficulty } ?: WishDifficulty.MEDIUM,
            motivation = motivation,
            prerequisites = parseStringList(prerequisites),
            notes = notes
        )
    }

    private fun WishModel.toEntity(): Wish {
        return Wish(
            id = id,
            title = title,
            description = description,
            category = category.name,
            priority = priority,
            inspirationItems = encodeStringList(inspirationItems),
            imageUris = encodeStringList(imageUris),
            estimatedCost = estimatedCost,
            targetTimePeriod = targetTimePeriod,
            tags = encodeStringList(tags),
            status = status.name,
            relatedGoalId = relatedGoalId,
            createdAt = createdAt,
            updatedAt = updatedAt,
            achievedAt = achievedAt,
            archivedAt = archivedAt,
            isArchived = isArchived,
            difficulty = difficulty.name,
            motivation = motivation,
            prerequisites = encodeStringList(prerequisites),
            notes = notes
        )
    }

    private fun parseStringList(jsonString: String): List<String> {
        return try {
            if (jsonString.isBlank()) emptyList()
            else json.decodeFromString<List<String>>(jsonString)
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun encodeStringList(list: List<String>): String {
        return try {
            json.encodeToString(list)
        } catch (e: Exception) {
            "[]"
        }
    }
} 