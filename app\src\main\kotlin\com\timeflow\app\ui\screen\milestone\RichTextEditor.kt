package com.timeflow.app.ui.screen.milestone

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 文本格式
 */
enum class TextFormat {
    BOLD, ITALIC, UNDERLINE, HEADING
}

/**
 * 富文本编辑器组件
 */
@Composable
fun RichTextEditor(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String = "输入描述...",
    modifier: Modifier = Modifier
) {
    var textFieldValue by remember { mutableStateOf(TextFieldValue(value)) }
    var activeFormats by remember { mutableStateOf(setOf<TextFormat>()) }
    val focusRequester = remember { FocusRequester() }
    
    Column(modifier = modifier) {
        // 文本编辑区域
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 120.dp)
                    .padding(12.dp)
            ) {
                BasicTextField(
                    value = textFieldValue,
                    onValueChange = { newValue ->
                        textFieldValue = newValue
                        onValueChange(newValue.text)
                    },
                    textStyle = TextStyle(
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurface
                    ),
                    cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            // 关闭键盘
                        }
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                )
                
                // 占位符
                if (textFieldValue.text.isEmpty()) {
                    Text(
                        text = placeholder,
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 格式工具栏
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                FormatButton(
                    format = TextFormat.BOLD,
                    active = TextFormat.BOLD in activeFormats,
                    icon = Icons.Default.FormatBold,
                    onClick = { 
                        activeFormats = if (TextFormat.BOLD in activeFormats) {
                            activeFormats - TextFormat.BOLD
                        } else {
                            activeFormats + TextFormat.BOLD
                        }
                        applyFormat(textFieldValue, activeFormats, onValueChange)
                    }
                )
                
                FormatButton(
                    format = TextFormat.ITALIC,
                    active = TextFormat.ITALIC in activeFormats,
                    icon = Icons.Default.FormatItalic,
                    onClick = { 
                        activeFormats = if (TextFormat.ITALIC in activeFormats) {
                            activeFormats - TextFormat.ITALIC
                        } else {
                            activeFormats + TextFormat.ITALIC
                        }
                        applyFormat(textFieldValue, activeFormats, onValueChange)
                    }
                )
                
                FormatButton(
                    format = TextFormat.UNDERLINE,
                    active = TextFormat.UNDERLINE in activeFormats,
                    icon = Icons.Default.FormatUnderlined,
                    onClick = { 
                        activeFormats = if (TextFormat.UNDERLINE in activeFormats) {
                            activeFormats - TextFormat.UNDERLINE
                        } else {
                            activeFormats + TextFormat.UNDERLINE
                        }
                        applyFormat(textFieldValue, activeFormats, onValueChange)
                    }
                )
                
                FormatButton(
                    format = TextFormat.HEADING,
                    active = TextFormat.HEADING in activeFormats,
                    icon = Icons.Default.Title,
                    onClick = { 
                        activeFormats = if (TextFormat.HEADING in activeFormats) {
                            activeFormats - TextFormat.HEADING
                        } else {
                            activeFormats + TextFormat.HEADING
                        }
                        applyFormat(textFieldValue, activeFormats, onValueChange)
                    }
                )
                
                // 分隔线
                Box(
                    modifier = Modifier
                        .height(24.dp)
                        .width(1.dp)
                        .background(MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.2f))
                )
                
                // 段落对齐
                IconButton(
                    onClick = {
                        // 左对齐
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.FormatAlignLeft,
                        contentDescription = "左对齐",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                IconButton(
                    onClick = {
                        // 居中对齐
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.FormatAlignCenter,
                        contentDescription = "居中对齐",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                IconButton(
                    onClick = {
                        // 右对齐
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.FormatAlignRight,
                        contentDescription = "右对齐",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 应用文本格式
 */
private fun applyFormat(
    textFieldValue: TextFieldValue,
    formats: Set<TextFormat>,
    onValueChange: (String) -> Unit
) {
    // 获取选中的文本范围
    val selectionStart = textFieldValue.selection.start
    val selectionEnd = textFieldValue.selection.end
    
    // 如果没有选中文本，则返回
    if (selectionStart == selectionEnd) return
    
    // 创建带格式的文本
    val annotatedString = buildAnnotatedString {
        append(textFieldValue.text)
        
        // 应用格式
        val spanStyle = SpanStyle()
        
        if (TextFormat.BOLD in formats) {
            spanStyle.copy(fontWeight = FontWeight.Bold)
        }
        
        if (TextFormat.ITALIC in formats) {
            spanStyle.copy(fontStyle = FontStyle.Italic)
        }
        
        if (TextFormat.UNDERLINE in formats) {
            spanStyle.copy(textDecoration = TextDecoration.Underline)
        }
        
        if (TextFormat.HEADING in formats) {
            spanStyle.copy(fontSize = 20.sp)
        }
        
        addStyle(spanStyle, selectionStart, selectionEnd)
    }
    
    // 更新文本值
    onValueChange(annotatedString.toString())
}

/**
 * 格式按钮
 */
@Composable
fun FormatButton(
    format: TextFormat,
    active: Boolean,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                if (active) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                else Color.Transparent
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = format.name,
            tint = if (active) MaterialTheme.colorScheme.primary
                  else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
} 