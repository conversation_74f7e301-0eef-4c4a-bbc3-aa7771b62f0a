package com.timeflow.app.ui.task.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import kotlin.random.Random

/**
 * 语音输入对话框组件
 */
@Composable
fun VoiceInputDialog(
    onDismiss: () -> Unit,
    onVoiceInputReceived: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var offsetY by remember { mutableStateOf(0f) }
    val dismissThreshold = with(LocalDensity.current) { 100.dp.toPx() }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .offset(y = offsetY.dp)
                .pointerInput(Unit) {
                    detectVerticalDragGestures(
                        onVerticalDrag = { _, dragAmount ->
                            // 只允许上滑
                            if (dragAmount < 0) {
                                offsetY += dragAmount / density
                            }
                        },
                        onDragEnd = {
                            if (offsetY < -dismissThreshold / density) {
                                onDismiss()
                            } else {
                                // 弹回原位
                                offsetY = 0f
                            }
                        }
                    )
                },
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 关闭按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = Color.Gray
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 提示文字
                Text(
                    text = "说出你要做的事",
                    fontSize = 22.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 语音波形可视化
                VoiceWaveform(
                    isRecording = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(68.dp)
                        .padding(vertical = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 底部文字提示
                Text(
                    text = "上滑可取消输入",
                    color = Color.Gray,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 录音按钮
                RecordButton(
                    isRecording = true,
                    onClick = {
                        // 模拟语音录制结束
                        onVoiceInputReceived("记得买牛奶")
                    },
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * 语音波形可视化组件
 */
@Composable
fun VoiceWaveform(
    isRecording: Boolean,
    modifier: Modifier = Modifier
) {
    val random = remember { Random(System.currentTimeMillis()) }
    val animatedValues = List(40) { index ->
        var targetHeight by remember { mutableStateOf(0.1f + random.nextFloat() * 0.6f) }
        
        LaunchedEffect(isRecording) {
            while (isRecording) {
                targetHeight = 0.1f + random.nextFloat() * 0.6f
                delay(200 + random.nextInt(200).toLong())
            }
        }
        
        val animatedHeight by animateFloatAsState(
            targetValue = targetHeight,
            animationSpec = tween(
                durationMillis = 300,
                easing = FastOutSlowInEasing
            ),
            label = "WaveformAnimation$index"
        )
        
        animatedHeight
    }
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        animatedValues.forEachIndexed { index, height ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(height)
                    .padding(horizontal = 0.5.dp)
                    .background(
                        color = Color(0xFF8BC34A).copy(alpha = 0.7f),
                        shape = RoundedCornerShape(1.dp)
                    )
            )
        }
    }
}

/**
 * 录音按钮组件
 */
@Composable
fun RecordButton(
    isRecording: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(70.dp)
            .clip(CircleShape)
            .background(Color(0xFF8BC34A))
            .clickable(onClick = onClick)
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        // 内部白色圆形
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.2f)),
            contentAlignment = Alignment.Center
        ) {
            // 麦克风图标
            Icon(
                imageVector = Icons.Default.Mic,
                contentDescription = if (isRecording) "停止录音" else "开始录音",
                tint = Color.White,
                modifier = Modifier.size(28.dp)
            )
        }
    }
} 