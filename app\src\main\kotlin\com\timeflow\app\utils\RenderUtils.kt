package com.timeflow.app.utils

import android.app.ActivityManager
import android.content.Context
import android.graphics.Color as AndroidColor
import android.os.Build
import android.util.Log
import android.view.Window
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.layout
import kotlin.math.roundToInt

/**
 * 渲染工具类 - 针对不同设备性能调整渲染模式
 * 
 * 主要用于解决硬件合成器(HWC)相关问题，以及优化低端设备的渲染性能
 */
object RenderUtils {
    private const val TAG = "RenderUtils"
    
    // 颜色模式常量 - 从Window类定义的常量值
    private const val COLOR_MODE_DEFAULT = 0
    private const val COLOR_MODE_WIDE_COLOR_GAMUT = 1
    
    /**
     * 优化窗口渲染设置，提高性能和稳定性
     * 
     * @param window 窗口对象
     */
    fun optimizeRendering(window: Window) {
        try {
            // 设置默认颜色模式以避免HWC LUTs错误
            window.colorMode = COLOR_MODE_DEFAULT
            
            // 设置窗口透明度处理方式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(false)
            }
            
            // 配置系统栏背景
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = AndroidColor.TRANSPARENT
            window.navigationBarColor = AndroidColor.TRANSPARENT
            
            Log.d(TAG, "窗口渲染已优化")
        } catch (e: Exception) {
            Log.e(TAG, "优化窗口渲染时出错", e)
        }
    }
    
    /**
     * 设置最佳渲染模式：根据设备性能和Android版本自动调整
     * 
     * 在低性能或老旧设备上禁用高级特效，避免HWC LUTs错误
     * 
     * @param window 窗口对象
     * @param context 应用上下文
     */
    fun setOptimalRenderMode(window: Window, context: Context) {
        try {
            // 强制使用默认颜色模式以解决HWC LUTs错误
            window.colorMode = android.content.pm.ActivityInfo.COLOR_MODE_DEFAULT
            Log.i(TAG, "已设置默认颜色模式以避免HWC LUTs错误")
            
            // 低端设备处理
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.statusBarColor = AndroidColor.BLACK
                window.navigationBarColor = AndroidColor.BLACK
            }
            
            // 设置窗口标志
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // 设置窗口可以在刘海区域显示
                val layoutParams = window.attributes
                layoutParams.layoutInDisplayCutoutMode = 
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                window.attributes = layoutParams
            }
            
            Log.d(TAG, "渲染模式优化完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置渲染模式失败", e)
        }
    }
    
    /**
     * 应用低延迟模式，提高交互响应速度
     * 
     * @param window 窗口对象
     */
    fun applyLowLatencyMode(window: Window) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+支持低延迟模式
                val params = window.attributes
                params.preferredRefreshRate = 60f  // 降低至合理的刷新率以节省电量
                window.attributes = params
            }
            
            Log.d(TAG, "已应用低延迟模式")
        } catch (e: Exception) {
            Log.e(TAG, "应用低延迟模式失败", e)
        }
    }
    
    /**
     * 重置低延迟模式，优化电池使用
     * 
     * @param window 窗口对象
     */
    fun resetLowLatencyMode(window: Window) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // 重置为默认模式
                val params = window.attributes
                params.preferredRefreshRate = 0f  // 0表示系统默认
                window.attributes = params
            }
            
            Log.d(TAG, "已重置低延迟模式")
        } catch (e: Exception) {
            Log.e(TAG, "重置低延迟模式失败", e)
        }
    }
    
    /**
     * 获取优化的图层修饰符
     * 
     * 根据设备性能返回适当的图层修饰符，低端设备禁用高级效果
     * 
     * @param context 应用上下文
     * @return 优化的Modifier
     */
    @Composable
    fun getOptimizedGraphicsLayerModifier(context: Context = LocalContext.current): Modifier {
        val optimizationLevel = remember { getDeviceOptimizationLevel(context) }
        
        return when (optimizationLevel) {
            RenderOptimizationLevel.LOW -> {
                // 低端设备：禁用所有高级效果
                Modifier.graphicsLayer(
                    renderEffect = null,
                    alpha = 1f,
                    shadowElevation = 0f,
                    clip = false
                )
            }
            RenderOptimizationLevel.MEDIUM -> {
                // 中端设备：限制阴影和部分效果
                Modifier.graphicsLayer(
                    shadowElevation = 2f,  // 较低的阴影高度
                    shape = RectangleShape,
                    clip = true,
                    renderEffect = null
                )
            }
            RenderOptimizationLevel.HIGH -> {
                // 高端设备：允许所有效果
                Modifier.graphicsLayer()
            }
        }
    }
    
    /**
     * 判断设备是否支持复杂渲染效果（如模糊效果）
     * 
     * @param context 应用上下文
     * @return 是否支持复杂渲染效果
     */
    fun supportsComplexEffects(context: Context): Boolean {
        // Android 12+ 通常支持高效的RenderEffect API
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return true
        }
        
        // 检查是否为低内存设备
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        
        // 如果总内存小于3GB，认为不支持复杂效果
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        val totalMemoryGB = memoryInfo.totalMem / (1024 * 1024 * 1024f)
        
        if (totalMemoryGB < 3f) {
            Log.d(TAG, "Device has less than 3GB RAM (${totalMemoryGB.roundToInt()}GB), limiting complex effects")
            return false
        }
        
        // 对于中端设备，根据系统版本判断
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && totalMemoryGB >= 4f) {
            return true
        }
        
        return false
    }
    
    /**
     * 获取设备渲染优化级别
     * 
     * @param context 应用上下文
     * @return 优化级别枚举
     */
    private fun getDeviceOptimizationLevel(context: Context): RenderOptimizationLevel {
        // 对于可能触发HWC LUTs问题的设备，始终返回LOW
        return RenderOptimizationLevel.LOW
    }
}

/**
 * 渲染优化级别枚举
 */
enum class RenderOptimizationLevel {
    LOW,    // 低端设备：禁用大部分高级渲染效果
    MEDIUM, // 中端设备：启用部分效果
    HIGH    // 高端设备：启用全部效果
} 