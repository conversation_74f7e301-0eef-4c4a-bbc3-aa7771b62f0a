package com.timeflow.app.ui.task.components.common

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.PriorityHigh
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 任务优先级指示器组件
 * 
 * @param priority 优先级 (0=低, 1=中, 2=高, 3=紧急)
 * @param size 指示器大小
 * @param shape 指示器形状，默认为圆形
 * @param variant 指示器变体，决定显示样式
 * @param modifier 修饰符
 */
@Composable
fun PriorityIndicator(
    priority: Int,
    size: Dp = 16.dp,
    shape: Shape = CircleShape,
    variant: PriorityVariant = PriorityVariant.DOT,
    modifier: Modifier = Modifier
) {
    val priorityColor = getPriorityColor(priority)
    
    when (variant) {
        PriorityVariant.DOT -> {
            // 基础圆点样式
            Box(
                modifier = modifier
                    .size(size)
                    .clip(shape)
                    .background(priorityColor)
            )
        }
        PriorityVariant.OUTLINED -> {
            // 带边框的样式
            Box(
                modifier = modifier
                    .size(size)
                    .clip(shape)
                    .background(priorityColor.copy(alpha = 0.2f))
                    .border(1.dp, priorityColor, shape)
            )
        }
        PriorityVariant.ICON -> {
            // 图标样式
            Box(
                modifier = modifier
                    .size(size * 1.5f),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Flag,
                    contentDescription = "优先级 $priority",
                    tint = priorityColor,
                    modifier = Modifier.size(size)
                )
            }
        }
        PriorityVariant.TAG -> {
            // 标签样式
            val text = when (priority) {
                0 -> "低"
                1 -> "中"
                2 -> "高"
                3 -> "紧急"
                else -> "无"
            }
            
            Surface(
                shape = RoundedCornerShape(4.dp),
                color = priorityColor.copy(alpha = 0.15f),
                modifier = modifier.height(20.dp)
            ) {
                Row(
                    modifier = Modifier
                        .padding(horizontal = 6.dp, vertical = 2.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(CircleShape)
                            .background(priorityColor)
                    )
                    Text(
                        text = text,
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium,
                        color = priorityColor,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        PriorityVariant.ALERT -> {
            // 警示样式 - 仅用于高优先级和紧急任务
            if (priority >= 2) {
                Surface(
                    shape = CircleShape,
                    color = priorityColor,
                    modifier = modifier.size(size)
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Default.PriorityHigh,
                            contentDescription = "优先级 $priority",
                            tint = Color.White,
                            modifier = Modifier.size(size * 0.6f)
                        )
                    }
                }
            } else {
                // 低优先级使用基础圆点
                Box(
                    modifier = modifier
                        .size(size)
                        .clip(shape)
                        .background(priorityColor.copy(alpha = if (priority == 0) 0.5f else 0.7f))
                )
            }
        }
    }
}

/**
 * 优先级指示器变体
 */
enum class PriorityVariant {
    DOT,      // 圆点
    OUTLINED, // 带边框
    ICON,     // 图标
    TAG,      // 标签
    ALERT     // 警示
}

/**
 * 获取优先级颜色
 * 
 * @param priority 优先级值
 * @return 对应的颜色
 */
@Composable
fun getPriorityColor(priority: Int): Color {
    return when (priority) {
        3 -> Color(0xFFD50000)  // URGENT - 深红色
        2 -> Color(0xFFF44336)  // HIGH - 红色
        1 -> Color(0xFFFF9800)  // MEDIUM - 橙色
        0 -> Color(0xFF4CAF50)  // LOW - 绿色
        else -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f) // DEFAULT - 灰色
    }
}

/**
 * 优先级指示器 - 文本色彩版本
 * 
 * @param priority 优先级 (0=低, 1=中, 2=高, 3=紧急)
 * @param modifier 修饰符
 */
@Composable
fun PriorityColor(priority: Int): Color {
    return getPriorityColor(priority)
} 