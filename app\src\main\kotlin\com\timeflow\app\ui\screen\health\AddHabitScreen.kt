package com.timeflow.app.ui.screen.health

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.timeflow.app.data.model.*
import com.timeflow.app.utils.SystemBarManager
import android.app.Activity
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.format.DateTimeFormatter

// 新建习惯页面主题配色 - 参照愿望池的优雅紫灰色调
private val AddHabitPrimary = Color(0xFF9c7d8e) // 优雅紫灰色（主色）
private val AddHabitSecondary = Color(0xFFb899a7) // 浅紫灰色（次要色）
private val AddHabitAccent = Color(0xFFFFD93D) // 金黄色（强调色）
private val AddHabitBackground = Color(0xFFFFFBF7) // 温暖白（背景色）
private val AddHabitSurface = Color(0xFFFFF8F0) // 奶油白（卡片背景）
private val AddHabitText = Color(0xFF2D1B2E) // 深紫灰（主要文字）
private val AddHabitTextSecondary = Color(0xFF6B4C6D) // 中紫灰（次要文字）

// 按钮和组件专用颜色
private val ButtonSelectedBackground = Color(0xFFf6f5ed) // 选中按钮背景色
private val GoalCardBackground = Color(0xFFfbfaf6) // 关联目标组件背景色
private val SelectedGoalBackground = Color(0xFFeae7ea) // 选中目标背景色

// 莫兰迪色系 - 高级灰调配色
private val MorandiColors = listOf(
    Color(0xFFB7A5A6), // 莫兰迪粉
    Color(0xFF9CA3B8), // 莫兰迪蓝
    Color(0xFFA8C4A2), // 莫兰迪绿
    Color(0xFFD4C5B8), // 莫兰迪棕
    Color(0xFFB8A8D4), // 莫兰迪紫
    Color(0xFFC4B5A2), // 莫兰迪橙
    Color(0xFF9BB8C4), // 莫兰迪青
    Color(0xFFD1B8A5)  // 莫兰迪土黄
)

// 莫奈印象派配色 - 艺术色彩
private val MonetColors = listOf(
    Color(0xFF8FA8D3), // 印象蓝
    Color(0xFFB8D3A8), // 田园绿
    Color(0xFFD3B8A8), // 温暖橙
    Color(0xFFA8B8D3), // 梦幻紫
    Color(0xFFD3A8B8), // 浪漫粉
    Color(0xFFA8D3B8), // 自然青
    Color(0xFFB8A8D3), // 神秘紫
    Color(0xFFD3D3A8)  // 阳光黄
)

// 图标背景颜色预设 - 莫兰迪 + 莫奈配色合集
private val IconBackgroundColors = MorandiColors + MonetColors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddHabitScreen(
    onNavigateBack: () -> Unit,
    viewModel: AddHabitViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val goals by viewModel.goals.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    LaunchedEffect(Unit) {
        viewModel.loadGoals()
    }
    
    // 监听保存结果
    LaunchedEffect(uiState.isSaved) {
        if (uiState.isSaved) {
            onNavigateBack()
        }
    }
    
    // 处理状态栏
    DisposableEffect(Unit) {
        activity?.let { SystemBarManager.forceOpaqueStatusBar(it, false) }
        onDispose { }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        AddHabitBackground,
                        AddHabitSurface
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加状态栏高度的顶部内边距
        ) {
            // 顶部导航栏 - 使用自定义实现避免与状态栏重叠
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = Color.Transparent,
                shadowElevation = 0.dp
            ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .padding(horizontal = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 返回按钮
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack, 
                        contentDescription = "返回",
                        tint = AddHabitText
                    )
                }
                
                // 标题
                Text(
                    text = "创建习惯",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = AddHabitText,
                    modifier = Modifier.weight(1f)
                )
                
                // 保存按钮
                TextButton(
                    onClick = { viewModel.saveHabit() },
                    enabled = uiState.formState.name.isNotBlank()
                ) {
                    Text(
                        text = "保存",
                        color = if (uiState.formState.name.isNotBlank()) 
                            AddHabitPrimary 
                        else 
                            AddHabitTextSecondary.copy(alpha = 0.5f),
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }
        }
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // 基本信息
            item {
                HabitBasicInfoSection(
                    formState = uiState.formState,
                    onNameChange = viewModel::updateName,
                    onDescriptionChange = viewModel::updateDescription,
                    onCategoryChange = viewModel::updateCategory,
                    onIconChange = viewModel::updateIcon,
                    onColorChange = viewModel::updateColor
                )
            }
            
            // 频率设置
            item {
                HabitFrequencySection(
                    formState = uiState.formState,
                    onFrequencyTypeChange = viewModel::updateFrequencyType,
                    onDaysChange = viewModel::updateSelectedDays,
                    onTargetCountChange = viewModel::updateTargetCount
                )
            }
            
            // 时间设置
            item {
                HabitTimeSection(
                    formState = uiState.formState,
                    onReminderToggle = viewModel::toggleReminder,
                    onReminderTimeChange = viewModel::updateReminderTime,
                    onFixedTimeChange = viewModel::updateFixedTime
                )
            }
            
            // 难度和目标关联
            item {
                HabitAdvancedSection(
                    formState = uiState.formState,
                    goals = goals,
                    onDifficultyChange = viewModel::updateDifficulty,
                    onGoalChange = viewModel::updateRelatedGoal,
                    onNotesChange = viewModel::updateNotes
                )
            }
        }
        }
    }
}

@Composable
private fun HabitBasicInfoSection(
    formState: HabitFormState,
    onNameChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onCategoryChange: (HabitCategory) -> Unit,
    onIconChange: (String) -> Unit, // 改为String类型
    onColorChange: (Color) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "基本信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = AddHabitText
            )
            
            // 习惯名称
            OutlinedTextField(
                value = formState.name,
                onValueChange = onNameChange,
                label = { Text("习惯名称") },
                placeholder = { Text("如：每日晨跑") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // 描述
            OutlinedTextField(
                value = formState.description,
                onValueChange = onDescriptionChange,
                label = { Text("描述（可选）") },
                placeholder = { Text("简单描述这个习惯") },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 3
            )
            
            // 类别选择
            Text(
                text = "类别",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = AddHabitText
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(HabitCategory.values()) { category ->
                    CategoryChip(
                        category = category,
                        isSelected = formState.category == category,
                        onClick = { onCategoryChange(category) }
                    )
                }
            }
            
            // 图标和颜色选择
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 图标选择
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "图标",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = AddHabitText
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    IconSelector(
                        selectedIcon = formState.icon,
                        onIconSelected = onIconChange
                    )
                }
                
                // 图标背景颜色选择
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "背景色",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = AddHabitText
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    IconBackgroundColorSelector(
                        selectedColor = formState.color,
                        onColorSelected = onColorChange
                    )
                }
            }
        }
    }
}

@Composable
private fun HabitFrequencySection(
    formState: HabitFormState,
    onFrequencyTypeChange: (HabitFrequencyType) -> Unit,
    onDaysChange: (Set<DayOfWeek>) -> Unit,
    onTargetCountChange: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "频率设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = AddHabitText
            )
            
            // 频率类型选择
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                HabitFrequencyType.values().forEach { type ->
                    FilterChip(
                        selected = formState.frequencyType == type,
                        onClick = { onFrequencyTypeChange(type) },
                        label = { Text(type.displayName) },
                        modifier = Modifier.weight(1f),
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = ButtonSelectedBackground,
                            selectedLabelColor = AddHabitText
                        )
                    )
                }
            }
            
            // 星期选择（当频率不是每天时显示）
            if (formState.frequencyType != HabitFrequencyType.DAILY) {
                Text(
                    text = "选择星期",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(DayOfWeek.values()) { day ->
                        DayChip(
                            day = day,
                            isSelected = formState.selectedDays.contains(day),
                            onClick = {
                                val newDays = if (formState.selectedDays.contains(day)) {
                                    formState.selectedDays - day
                                } else {
                                    formState.selectedDays + day
                                }
                                onDaysChange(newDays)
                            }
                        )
                    }
                }
            }
            
            // 目标次数
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "目标次数",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(
                        onClick = { 
                            if (formState.targetCount > 1) {
                                onTargetCountChange(formState.targetCount - 1)
                            }
                        },
                        enabled = formState.targetCount > 1
                    ) {
                        Icon(Icons.Default.Remove, contentDescription = "减少")
                    }
                    
                    Text(
                        text = formState.targetCount.toString(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    IconButton(
                        onClick = { 
                            if (formState.targetCount < 10) {
                                onTargetCountChange(formState.targetCount + 1)
                            }
                        },
                        enabled = formState.targetCount < 10
                    ) {
                        Icon(Icons.Default.Add, contentDescription = "增加")
                    }
                }
            }
        }
    }
}

@Composable
private fun HabitTimeSection(
    formState: HabitFormState,
    onReminderToggle: (Boolean) -> Unit,
    onReminderTimeChange: (LocalTime) -> Unit,
    onFixedTimeChange: (LocalTime?) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "时间设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = AddHabitText
            )
            
            // 提醒开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "每日提醒",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "在指定时间提醒你完成习惯",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
                
                Switch(
                    checked = formState.enableReminder,
                    onCheckedChange = onReminderToggle
                )
            }
            
            // 提醒时间选择
            if (formState.enableReminder) {
                TimePickerField(
                    label = "提醒时间",
                    time = formState.reminderTime ?: LocalTime.of(8, 0),
                    onTimeChange = onReminderTimeChange
                )
            }
            
            // 固定时间（可选）
            TimePickerField(
                label = "固定时间（可选）",
                time = formState.fixedTime,
                onTimeChange = onFixedTimeChange,
                optional = true
            )
        }
    }
}

@Composable
private fun HabitAdvancedSection(
    formState: HabitFormState,
    goals: List<Goal>,
    onDifficultyChange: (HabitDifficulty) -> Unit,
    onGoalChange: (String?, String?) -> Unit,
    onNotesChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White), // 统一使用白色背景
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "高级设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = AddHabitText
            )
            
            // 难度选择
            Text(
                text = "难度",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(HabitDifficulty.values()) { difficulty ->
                    DifficultyChip(
                        difficulty = difficulty,
                        isSelected = formState.difficulty == difficulty,
                        onClick = { onDifficultyChange(difficulty) }
                    )
                }
            }
            
            // 关联目标 - 美化设计
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Flag,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = "关联目标",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    if (formState.relatedGoalId != null) {
                        Surface(
                            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = "已关联",
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp),
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    }
                }
                
                if (goals.isNotEmpty()) {
                    var expanded by remember { mutableStateOf(false) }
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(12.dp))
                            .clickable { expanded = !expanded },
                        color = if (formState.relatedGoalId != null) 
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                        else 
                            MaterialTheme.colorScheme.surface,
                        border = BorderStroke(
                            1.dp, 
                            if (formState.relatedGoalId != null) 
                                MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                            else 
                                MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Column(
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = formState.relatedGoalTitle ?: "选择目标（可选）",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (formState.relatedGoalId != null) 
                                        MaterialTheme.colorScheme.onSurface
                                    else 
                                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                    fontWeight = if (formState.relatedGoalId != null) FontWeight.Medium else FontWeight.Normal
                                )
                                if (formState.relatedGoalId != null) {
                                    Text(
                                        text = "此习惯将支撑目标的达成",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                    )
                                } else {
                                    Text(
                                        text = "选择一个目标来创建关联",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                    )
                                }
                            }
                            
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                if (formState.relatedGoalId != null) {
                                    IconButton(
                                        onClick = { onGoalChange(null, null) },
                                        modifier = Modifier.size(24.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Clear,
                                            contentDescription = "取消关联",
                                            tint = MaterialTheme.colorScheme.error,
                                            modifier = Modifier.size(16.dp)
                                        )
                                    }
                                }
                                
                                Icon(
                                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                    contentDescription = if (expanded) "收起" else "展开",
                                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                    
                    // 目标选择列表
                    if (expanded) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(containerColor = Color.White),
                            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(8.dp)
                            ) {
                                // 无关联选项
                                GoalSelectionItem(
                                    title = "无关联目标",
                                    description = "这个习惯不关联任何目标",
                                    isSelected = formState.relatedGoalId == null,
                                    icon = "❌", // 使用emoji替代
                                    onClick = {
                                        onGoalChange(null, null)
                                        expanded = false
                                    }
                                )
                                
                                if (goals.isNotEmpty()) {
                                    Divider(
                                        modifier = Modifier.padding(vertical = 4.dp),
                                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                                    )
                                }
                                
                                // 目标列表
                                goals.forEach { goal ->
                                    GoalSelectionItem(
                                        title = goal.title,
                                        description = goal.description.takeIf { it.isNotBlank() } ?: "点击关联此目标",
                                        isSelected = formState.relatedGoalId == goal.id,
                                        icon = "🎯", // 使用emoji替代
                                        onClick = {
                                            onGoalChange(goal.id, goal.title)
                                            expanded = false
                                        }
                                    )
                                }
                            }
                        }
                    }
                } else {
                    // 无目标状态
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.5f),
                        shape = RoundedCornerShape(12.dp),
                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                modifier = Modifier.size(20.dp)
                            )
                            
                            Column {
                                Text(
                                    text = "暂无可关联的目标",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )
                                Text(
                                    text = "先去创建一些目标，然后就可以将习惯关联到目标了",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                                )
                            }
                        }
                    }
                }
            }
            
            // 备注
            OutlinedTextField(
                value = formState.notes,
                onValueChange = onNotesChange,
                label = { Text("备注（可选）") },
                placeholder = { Text("添加一些备注信息") },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 3
            )
        }
    }
}

// 辅助组件
@Composable
private fun CategoryChip(
    category: HabitCategory,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = { Text(category.title) },
        leadingIcon = {
            Text(
                text = category.icon, // 显示emoji
                fontSize = 14.sp,
                modifier = Modifier
            )
        },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = ButtonSelectedBackground,
            selectedLabelColor = AddHabitText
        )
    )
}

@Composable
private fun DayChip(
    day: DayOfWeek,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val dayName = when (day) {
        DayOfWeek.MONDAY -> "一"
        DayOfWeek.TUESDAY -> "二"
        DayOfWeek.WEDNESDAY -> "三"
        DayOfWeek.THURSDAY -> "四"
        DayOfWeek.FRIDAY -> "五"
        DayOfWeek.SATURDAY -> "六"
        DayOfWeek.SUNDAY -> "日"
    }
    
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                if (isSelected) MaterialTheme.colorScheme.primary
                else MaterialTheme.colorScheme.surface
            )
            .border(
                1.dp,
                if (isSelected) MaterialTheme.colorScheme.primary
                else MaterialTheme.colorScheme.outline,
                CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = dayName,
            color = if (isSelected) Color.White else MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun DifficultyChip(
    difficulty: HabitDifficulty,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = { Text(difficulty.displayName) },
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = ButtonSelectedBackground,
            selectedLabelColor = AddHabitText
        )
    )
}

@Composable
private fun IconSelector(
    selectedIcon: String, // 改为String类型存储emoji
    onIconSelected: (String) -> Unit // 改为String参数
) {
    // 使用EMOJI图标替代Material Icons
    val emojiIcons = listOf(
        "❤️", // 心形 - 健康/爱好
        "💧", // 水滴 - 喝水
        "☀️", // 太阳 - 阳光/早起
        "📖", // 书本 - 阅读
        "💪", // 肌肉 - 健身
        "🧘‍♀️", // 冥想 - 自我提升
        "💻", // 电脑 - 编程
        "💼", // 公文包 - 工作
        "🏠", // 房子 - 家务
        "🎓", // 毕业帽 - 学习
        "🏃‍♂️", // 跑步 - 运动
        "☕", // 咖啡 - 饮品
        "🍎", // 苹果 - 健康饮食
        "😴", // 睡觉 - 睡眠
        "🚫📱", // 禁止手机 - 戒断数字设备
        "🎨", // 艺术 - 创作
        "🎵", // 音乐 - 音乐练习
        "🌱", // 植物 - 成长
        "✍️", // 写作 - 日记
        "🔥", // 火焰 - 激情/坚持
        "⭐", // 星星 - 目标
        "🌅", // 日出 - 早起
        "🥗", // 沙拉 - 健康饮食
        "🚶‍♂️" // 散步 - 日常运动
    )
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(4), // 改为4列显示
        horizontalArrangement = Arrangement.Center, // 取消间距，居中排列
        verticalArrangement = Arrangement.spacedBy(2.dp), // 最小垂直间距
        modifier = Modifier
            .heightIn(max = 120.dp) // 减少高度
            .fillMaxWidth(),
        contentPadding = PaddingValues(4.dp) // 减少内边距
    ) {
        items(emojiIcons) { emoji ->
            Box(
                modifier = Modifier
                    .size(32.dp) // 明显缩小图标大小
                    .clip(CircleShape)
                    .background(
                        if (selectedIcon == emoji) 
                            AddHabitPrimary.copy(alpha = 0.2f) // 只有选中时显示淡背景
                        else 
                            Color.Transparent // 取消默认背景色
                    )
                    .clickable { onIconSelected(emoji) },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = emoji,
                    fontSize = 16.sp, // 减小字体以适应更小的容器
                    modifier = Modifier
                )
            }
        }
    }
}

@Composable
private fun IconBackgroundColorSelector(
    selectedColor: Color,
    onColorSelected: (Color) -> Unit
) {
    var showCustomColorPicker by remember { mutableStateOf(false) }
    var currentColorSet by remember { mutableStateOf("莫兰迪") }
    
    Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
        // 配色方案选择标签
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp), // 缩小标签之间的间距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 莫兰迪色系标签
            Surface(
                modifier = Modifier.clickable { currentColorSet = "莫兰迪" },
                color = if (currentColorSet == "莫兰迪") 
                    AddHabitPrimary.copy(alpha = 0.15f) 
                else 
                    Color.Transparent,
                shape = RoundedCornerShape(12.dp) // 缩小圆角
            ) {
                Text(
                    text = "莫兰迪",
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 缩小内边距
                    style = MaterialTheme.typography.labelSmall, // 使用更小的字体
                    color = if (currentColorSet == "莫兰迪") 
                        AddHabitPrimary 
                    else 
                        AddHabitTextSecondary,
                    fontWeight = if (currentColorSet == "莫兰迪") FontWeight.SemiBold else FontWeight.Medium
                )
            }
            
            // 莫奈色系标签
            Surface(
                modifier = Modifier.clickable { currentColorSet = "莫奈" },
                color = if (currentColorSet == "莫奈") 
                    AddHabitPrimary.copy(alpha = 0.15f) 
                else 
                    Color.Transparent,
                shape = RoundedCornerShape(12.dp) // 缩小圆角
            ) {
                Text(
                    text = "莫奈",
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 缩小内边距
                    style = MaterialTheme.typography.labelSmall, // 使用更小的字体
                    color = if (currentColorSet == "莫奈") 
                        AddHabitPrimary 
                    else 
                        AddHabitTextSecondary,
                    fontWeight = if (currentColorSet == "莫奈") FontWeight.SemiBold else FontWeight.Medium
                )
            }
            
            // 自定义按钮
            Surface(
                modifier = Modifier.clickable { showCustomColorPicker = true },
                color = AddHabitAccent.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp) // 缩小圆角
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 缩小内边距
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(2.dp) // 缩小图标和文字的间距
                ) {
                    Icon(
                        imageVector = Icons.Default.Palette,
                        contentDescription = "",
                        tint = AddHabitAccent,
                        modifier = Modifier.size(12.dp) // 缩小图标
                    )
                    Text(
                        text = "",
                        style = MaterialTheme.typography.labelSmall, // 使用更小的字体
                        color = AddHabitAccent,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // 颜色预设展示
        LazyVerticalGrid(
            columns = GridCells.Fixed(4), // 增加到6列显示更多颜色，更紧凑
            horizontalArrangement = Arrangement.Center, // 取消间距，居中排列
            verticalArrangement = Arrangement.spacedBy(1.dp), // 进一步减少垂直间距
            modifier = Modifier
                .heightIn(max = 80.dp) // 进一步减少高度
                .fillMaxWidth(),
            contentPadding = PaddingValues(2.dp) // 进一步减少内边距
        ) {
            val currentColors = when (currentColorSet) {
                "莫兰迪" -> MorandiColors
                "莫奈" -> MonetColors
                else -> MorandiColors
            }
            
            items(currentColors) { color ->
                Box(
                    modifier = Modifier
                        .size(24.dp) // 进一步缩小颜色块，更紧凑
                        .clip(CircleShape)
                        .background(color)
                        .border(
                            width = if (selectedColor == color) 1.5.dp else 0.dp, // 缩小边框宽度
                            color = if (selectedColor == color) 
                                AddHabitPrimary 
                            else 
                                Color.Transparent,
                            shape = CircleShape
                        )
                        .clickable { onColorSelected(color) },
                    contentAlignment = Alignment.Center
                ) {
                    // 显示选中标记
                    if (selectedColor == color) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已选中",
                            tint = Color.White,
                            modifier = Modifier.size(10.dp) // 进一步缩小图标
                        )
                    }
                }
            }
        }
    }
    
    // 自定义颜色选择器对话框
    if (showCustomColorPicker) {
        CustomColorPickerDialog(
            onColorSelected = { color ->
                onColorSelected(color)
                showCustomColorPicker = false
            },
            onDismiss = { showCustomColorPicker = false }
        )
    }
}

@Composable
private fun TimePickerField(
    label: String,
    time: LocalTime?,
    onTimeChange: (LocalTime) -> Unit,
    optional: Boolean = false
) {
    var showTimePicker by remember { mutableStateOf(false) }
    
    OutlinedTextField(
        value = time?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: if (optional) "" else "08:00",
        onValueChange = { },
        label = { Text(label) },
        readOnly = true,
        trailingIcon = {
            IconButton(onClick = { showTimePicker = true }) {
                Icon(Icons.Default.AccessTime, contentDescription = "选择时间")
            }
        },
        modifier = Modifier.fillMaxWidth()
    )
    
    if (showTimePicker) {
        TimePickerDialog(
            initialTime = time ?: LocalTime.of(8, 0),
            onTimeSelected = { selectedTime ->
                onTimeChange(selectedTime)
                showTimePicker = false
            },
            onDismiss = { showTimePicker = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TimePickerDialog(
    initialTime: LocalTime,
    onTimeSelected: (LocalTime) -> Unit,
    onDismiss: () -> Unit
) {
    val timePickerState = rememberTimePickerState(
        initialHour = initialTime.hour,
        initialMinute = initialTime.minute
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择时间") },
        text = {
            TimePicker(state = timePickerState)
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onTimeSelected(LocalTime.of(timePickerState.hour, timePickerState.minute))
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
private fun GoalSelectionItem(
    title: String,
    description: String,
    isSelected: Boolean,
    icon: String, // 改为String类型存储emoji
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onClick() },
        color = if (isSelected) 
            SelectedGoalBackground
        else 
            Color.Transparent
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .background(
                        if (isSelected) 
                            AddHabitPrimary.copy(alpha = 0.15f)
                        else 
                            AddHabitSurface,
                        CircleShape
                    )
                    .border(
                        1.dp,
                        if (isSelected) 
                            AddHabitPrimary
                        else 
                            AddHabitPrimary.copy(alpha = 0.3f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = icon, // 显示emoji
                    fontSize = 18.sp,
                    modifier = Modifier
                )
            }
            
            // 文本内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                    color = if (isSelected) 
                        AddHabitPrimary
                    else 
                        AddHabitText
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = AddHabitTextSecondary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 选中状态指示器
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已选中",
                    tint = AddHabitPrimary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
private fun CustomColorPickerDialog(
    onColorSelected: (Color) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedColor by remember { mutableStateOf(Color.Red) }
    var redValue by remember { mutableStateOf(255f) }
    var greenValue by remember { mutableStateOf(0f) }
    var blueValue by remember { mutableStateOf(0f) }
    
    // 更新选中颜色
    LaunchedEffect(redValue, greenValue, blueValue) {
        selectedColor = Color(
            red = (redValue / 255f).coerceIn(0f, 1f),
            green = (greenValue / 255f).coerceIn(0f, 1f),
            blue = (blueValue / 255f).coerceIn(0f, 1f)
        )
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "自定义颜色",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.SemiBold,
                color = AddHabitText
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // 颜色预览
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(selectedColor)
                        .border(
                            2.dp,
                            AddHabitPrimary.copy(alpha = 0.3f),
                            RoundedCornerShape(12.dp)
                        )
                )
                
                // RGB 滑块
                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    // 红色滑块
                    ColorSlider(
                        label = "红色",
                        value = redValue,
                        onValueChange = { redValue = it },
                        color = Color.Red
                    )
                    
                    // 绿色滑块
                    ColorSlider(
                        label = "绿色",
                        value = greenValue,
                        onValueChange = { greenValue = it },
                        color = Color.Green
                    )
                    
                    // 蓝色滑块
                    ColorSlider(
                        label = "蓝色",
                        value = blueValue,
                        onValueChange = { blueValue = it },
                        color = Color.Blue
                    )
                }
                
                // 快速预设色彩
                Text(
                    text = "快速选择",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = AddHabitText
                )
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    val quickColors = listOf(
                        Color.Red, Color.Green, Color.Blue, Color.Yellow,
                        Color.Cyan, Color.Magenta, Color(0xFFFF9800), 
                        Color(0xFF9C27B0), Color(0xFF795548), Color(0xFF607D8B)
                    )
                    
                    items(quickColors) { color ->
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(color)
                                .border(1.dp, Color.Gray.copy(alpha = 0.3f), CircleShape)
                                .clickable {
                                    redValue = (color.red * 255).toInt().toFloat()
                                    greenValue = (color.green * 255).toInt().toFloat()
                                    blueValue = (color.blue * 255).toInt().toFloat()
                                }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onColorSelected(selectedColor) }
            ) {
                Text(
                    text = "确定",
                    color = AddHabitPrimary,
                    fontWeight = FontWeight.SemiBold
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "取消",
                    color = AddHabitTextSecondary
                )
            }
        }
    )
}

@Composable
private fun ColorSlider(
    label: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    color: Color
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = AddHabitText
            )
            Text(
                text = value.toInt().toString(),
                style = MaterialTheme.typography.bodySmall,
                color = AddHabitTextSecondary
            )
        }
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = 0f..255f,
            modifier = Modifier.fillMaxWidth(),
            colors = SliderDefaults.colors(
                thumbColor = color,
                activeTrackColor = color.copy(alpha = 0.7f),
                inactiveTrackColor = color.copy(alpha = 0.2f)
            )
        )
    }
} 