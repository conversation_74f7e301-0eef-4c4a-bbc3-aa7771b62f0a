package com.timeflow.app.utils

import timber.log.Timber
import java.lang.reflect.Method
import java.util.Collections
import java.util.HashSet

/**
 * 反射安全检查工具
 * 限制反射行为，只允许对白名单内的类进行反射操作
 */
object RestrictedReflection {
    
    // 允许反射的类名白名单
    private val ALLOWED_CLASSES = Collections.unmodifiableSet(
        HashSet(
            listOf(
                // 应用内允许反射的类
                "com.timeflow.app.data.model.Task",
                "com.timeflow.app.data.model.TaskCategory",
                "com.timeflow.app.data.model.UserProfile",
                
                // 用于测试的类
                "com.timeflow.app.test.ReflectionTestClass"
            )
        )
    )
    
    // 允许反射的方法名白名单
    private val ALLOWED_METHODS = Collections.unmodifiableSet(
        HashSet(
            listOf(
                "toString",
                "hashCode",
                "equals",
                "getName",
                "getSimpleName"
            )
        )
    )
    
    /**
     * 安全地获取Class对象
     * 如果类名不在白名单中，则抛出安全异常
     */
    @Throws(ClassNotFoundException::class, SecurityException::class)
    fun safeForName(className: String): Class<*> {
        if (!ALLOWED_CLASSES.contains(className)) {
            val errorMsg = "反射访问被拒绝：$className 不在允许列表中"
            Timber.e(errorMsg)
            throw SecurityException(errorMsg)
        }
        
        return Class.forName(className)
    }
    
    /**
     * 安全地获取方法
     * 如果方法名不在白名单中，则抛出安全异常
     */
    @Throws(NoSuchMethodException::class, SecurityException::class)
    fun safeGetMethod(clazz: Class<*>, methodName: String, parameterTypes: Array<Class<*>>): Method {
        if (!isClassAllowed(clazz) && !ALLOWED_METHODS.contains(methodName)) {
            val errorMsg = "反射方法访问被拒绝：${clazz.name}.$methodName 不在允许列表中"
            Timber.e(errorMsg)
            throw SecurityException(errorMsg)
        }
        
        return clazz.getMethod(methodName, *parameterTypes)
    }
    
    /**
     * 检查类是否在白名单中
     */
    private fun isClassAllowed(clazz: Class<*>): Boolean {
        return ALLOWED_CLASSES.contains(clazz.name)
    }
    
    /**
     * 安全地执行反射方法
     * 包装安全检查和异常处理
     */
    fun safeInvoke(targetClassName: String, methodName: String, parameterTypes: Array<Class<*>> = emptyArray(), args: Array<Any?> = emptyArray()): Any? {
        try {
            val clazz = safeForName(targetClassName)
            val method = safeGetMethod(clazz, methodName, parameterTypes)
            
            // 如果是静态方法，则实例为null
            val instance = if (java.lang.reflect.Modifier.isStatic(method.modifiers)) null else clazz.newInstance()
            
            return method.invoke(instance, *args)
        } catch (e: Exception) {
            Timber.e(e, "反射调用失败：$targetClassName.$methodName")
            return null
        }
    }
} 