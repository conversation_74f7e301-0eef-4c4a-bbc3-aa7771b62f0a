package com.timeflow.app.data.ai.model

import java.time.LocalDateTime
import java.util.UUID

/**
 * AI对话数据模型 - 支持自然语言任务管理
 */
data class AiConversation(
    val id: String = UUID.randomUUID().toString(),
    val userId: String,
    val messages: List<ConversationMessage> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val associatedTaskIds: List<String> = emptyList(),
    val context: ConversationContext? = null,
    val status: ConversationStatus = ConversationStatus.ACTIVE
)

/**
 * 对话消息 - 表示对话中的一条消息
 */
data class ConversationMessage(
    val id: String = UUID.randomUUID().toString(),
    val content: String,
    val sender: MessageSender,
    val timestamp: LocalDateTime = LocalDateTime.now(),
    val messageType: MessageType = MessageType.TEXT,
    val attachments: List<MessageAttachment> = emptyList(),
    val actions: List<MessageAction> = emptyList(),
    val relatedTaskId: String? = null,
    val relatedTaskTitle: String? = null,
    val intentType: MessageIntent? = null
)

/**
 * 消息附件 - 对话中包含的附件
 */
data class MessageAttachment(
    val id: String = UUID.randomUUID().toString(),
    val type: AttachmentType,
    val url: String? = null,
    val content: String? = null,
    val thumbnailUrl: String? = null,
    val name: String,
    val size: Long? = null
)

/**
 * 消息动作 - 可执行的操作按钮
 */
data class MessageAction(
    val id: String = UUID.randomUUID().toString(),
    val label: String,
    val actionType: ActionType,
    val payload: Map<String, String> = emptyMap(),
    val isExecuted: Boolean = false,
    val executedAt: LocalDateTime? = null
)

/**
 * 对话上下文 - 提供对话的背景信息
 */
data class ConversationContext(
    val activeTask: String? = null,
    val currentView: String? = null,
    val recentTasks: List<String> = emptyList(),
    val userPreferences: Map<String, String> = emptyMap(),
    val timeOfDay: TimeOfDay? = null
)

/**
 * 消息发送者
 */
enum class MessageSender {
    USER, AI
}

/**
 * 消息类型
 */
enum class MessageType {
    TEXT, IMAGE, AUDIO, FILE, TASK_CARD, SUGGESTION_CARD, ACTION,
    TASK_OPTIMIZATION, SUBTASKS, ERROR, NOTIFICATION
}

/**
 * 附件类型
 */
enum class AttachmentType {
    IMAGE, DOCUMENT, AUDIO, VIDEO, TASK_REFERENCE
}

/**
 * 动作类型
 */
enum class ActionType {
    CREATE_TASK, UPDATE_TASK, DELETE_TASK, COMPLETE_TASK,
    SCHEDULE_TASK, BREAKDOWN_TASK, ANALYZE_TIME, VIEW_INSIGHTS
}

/**
 * 消息意图类型
 */
enum class MessageIntent {
    GREETING, TASK_CREATION, TASK_QUERY, TASK_UPDATE,
    SCHEDULING, TIME_TRACKING, PRODUCTIVITY_QUERY, FEEDBACK_REQUEST,
    NOT_UNDERSTOOD, COMPLEX_ANALYSIS
}

/**
 * 对话状态
 */
enum class ConversationStatus {
    ACTIVE, PAUSED, COMPLETED, ARCHIVED
} 