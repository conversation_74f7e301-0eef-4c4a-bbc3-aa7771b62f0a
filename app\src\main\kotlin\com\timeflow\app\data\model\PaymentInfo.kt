package com.timeflow.app.data.model

import kotlinx.serialization.Serializable
import java.math.BigDecimal

/**
 * 支付方式枚举
 */
enum class PaymentMethod {
    ALIPAY,     // 支付宝
    WECHAT,     // 微信支付
    MOCK        // 模拟支付（用于测试）
}

/**
 * 支付状态枚举
 */
enum class PaymentStatus {
    PENDING,    // 待支付
    PROCESSING, // 支付中
    SUCCESS,    // 支付成功
    FAILED,     // 支付失败
    CANCELLED,  // 支付取消
    REFUNDED    // 已退款
}

/**
 * 会员套餐枚举
 */
enum class MembershipPlan {
    MONTHLY,    // 月度会员
    LIFETIME    // 终身会员
}

/**
 * 支付订单信息
 */
@Serializable
data class PaymentOrder(
    val orderId: String,                    // 订单ID
    val membershipPlan: MembershipPlan,     // 会员套餐
    val amount: String,                     // 支付金额（元）
    val currency: String = "CNY",           // 货币类型
    val title: String,                      // 订单标题
    val description: String,                // 订单描述
    val createTime: Long = System.currentTimeMillis(), // 创建时间
    val expireTime: Long = System.currentTimeMillis() + 30 * 60 * 1000 // 过期时间（30分钟）
)

/**
 * 支付请求信息
 */
data class PaymentRequest(
    val order: PaymentOrder,                // 订单信息
    val paymentMethod: PaymentMethod,       // 支付方式
    val userId: String,                     // 用户ID
    val notifyUrl: String? = null,          // 支付结果通知URL
    val returnUrl: String? = null           // 支付完成返回URL
)

/**
 * 支付结果
 */
data class PaymentResult(
    val orderId: String,                    // 订单ID
    val transactionId: String?,             // 第三方交易ID
    val status: PaymentStatus,              // 支付状态
    val paymentMethod: PaymentMethod,       // 支付方式
    val amount: String,                     // 实际支付金额
    val message: String,                    // 结果描述
    val extra: Map<String, String> = emptyMap(), // 额外信息
    val timestamp: Long = System.currentTimeMillis() // 结果时间戳
)

/**
 * 支付配置信息
 */
data class PaymentConfig(
    // 支付宝配置
    val alipayAppId: String = "",
    val alipayPrivateKey: String = "",
    val alipayPublicKey: String = "",
    val alipaySignType: String = "RSA2",
    
    // 微信支付配置
    val wechatAppId: String = "",
    val wechatMchId: String = "",
    val wechatKey: String = "",
    val wechatCertPath: String = "",
    
    // 通用配置
    val notifyUrl: String = "",
    val isTestMode: Boolean = true          // 是否测试模式
)

/**
 * 支付异常
 */
sealed class PaymentException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    class NetworkError(message: String, cause: Throwable? = null) : PaymentException(message, cause)
    class ConfigurationError(message: String) : PaymentException(message)
    class SignatureError(message: String) : PaymentException(message)
    class OrderExpired(message: String) : PaymentException(message)
    class AmountMismatch(message: String) : PaymentException(message)
    class UserCancelled(message: String) : PaymentException(message)
    class Unknown(message: String, cause: Throwable? = null) : PaymentException(message, cause)
} 