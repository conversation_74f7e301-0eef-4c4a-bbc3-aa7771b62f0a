package com.timeflow.app.ui.navigation

import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument

/**
 * 应用导航目标定义
 */
object AppDestinations {
    // 主要导航路径
    const val UNIFIED_HOME_ROUTE = "unified_home"
    const val HOME_ROUTE = "home"
    const val TASK_LIST_ROUTE = "task_list"
    const val TASK_ROUTE = "task"
    const val ANALYTICS_ROUTE = "analytics"
    const val SETTINGS_ROUTE = "settings"
    const val PROFILE_ROUTE = "profile"
    const val DISCOVER_ROUTE = "discover"
    const val TASK_LIST_FULL_ROUTE = "task_list_full"
    const val ACCOUNT_ROUTE = "account"
    
    // 目标管理相关路径
    const val GOAL_MANAGEMENT = "goal_management"
    const val GOAL_DETAIL_ROUTE = "goal_detail/{goalId}"
    const val ADD_GOAL_ROUTE = "add_goal"
    const val EDIT_GOAL_ROUTE = "edit_goal/{goalId}"
    const val GOAL_BREAKDOWN_ROUTE = "goal_breakdown/{goalId}"
    const val GOAL_REVIEW_ROUTE = "goal_review/{goalId}"
    
    // 目标模板相关路径
    const val GOAL_TEMPLATE_LIST = "goal_template_list"
    const val GOAL_TEMPLATE_EDIT = "goal_template_edit"
    const val GOAL_TEMPLATE_EDIT_WITH_ID_ROUTE = "goal_template_edit/{templateId}"
    const val GOAL_TEMPLATE_IMPORT = "goal_template_import"
    const val GOAL_TEMPLATE_IMPORT_WITH_CATEGORY = "goal_template_import?category={category}"
    const val SAVE_GOAL_AS_TEMPLATE_ROUTE = "save_goal_as_template/{goalId}"

    // 智能分类相关路径
    const val SMART_CATEGORY_TEST_ROUTE = "smart_category_test"
    const val GOAL_CATEGORY_MANAGEMENT_ROUTE = "goal_category_management"
    
    // 综合目标分析相关路径
    const val GOAL_COMPLETION_ANALYSIS_ROUTE = "goal_completion_analysis"
    const val GOAL_COMPLETION_ANALYSIS_TIME_ROUTE = "goal_completion_analysis/{timeRange}"
    
    // 里程碑相关路径
    const val MILESTONE_ROUTE = "milestone"
    
    // 任务相关路径
    const val ADD_TASK_ROUTE = "add_task"
    const val TASK_DETAIL_ROUTE = "task_detail/{taskId}"
    const val EDIT_TASK_ROUTE = "edit_task/{taskId}"
    const val TASK_EDIT_ROUTE = "task_edit/{taskId}"
    
    // 时间追踪相关路径
    const val TIME_TRACKING_ROUTE = "time_tracking"
    const val TIME_ANALYTICS_DETAIL_ROUTE = "time_analytics_detail"
    const val TIME_STATISTICS_ROUTE = "time_statistics"
    
    // 日历和调度路径
    const val CALENDAR_ROUTE = "calendar"
    const val SCHEDULE_ROUTE = "schedule"
    
    // 统计和设置路径
    const val THEME_SETTINGS_ROUTE = "theme_settings"
    const val SYNC_SETTINGS_ROUTE = "sync_settings"
    const val DATA_MANAGEMENT_ROUTE = "data_management" // 🔧 新增：统一的数据管理页面
    @Deprecated("使用 DATA_MANAGEMENT_ROUTE 替代")
    const val BACKUP_SETTINGS_ROUTE = "backup_settings"
    @Deprecated("使用 DATA_MANAGEMENT_ROUTE 替代")
    const val DATA_RECOVERY_ROUTE = "data_recovery"
    const val ABOUT_ROUTE = "about"
    const val HELP_ROUTE = "help"
    
    // AI助手相关路径
    const val AI_ASSISTANT_ROUTE = "ai_assistant"
    const val AI_ASSISTANT_WITH_TASK_ROUTE = "ai_assistant/{taskId}"
    const val AI_SETTINGS_ROUTE = "ai_settings"
    const val AI_MODEL_SETTINGS_ROUTE = "ai_model_settings"
    const val AI_MODEL_SETTINGS_WITH_ID_ROUTE = "ai_model_settings/{configId}"
    const val AI_REVIEW_ROUTE = "ai_review"
    
    // 健康相关路径
    const val MENSTRUAL_CYCLE_ROUTE = "menstrual_cycle"
    const val MENSTRUAL_CYCLE_STATS_ROUTE = "menstrual_cycle_stats" // 🔧 修复：与主AppDestinations保持一致
    const val HABIT_TRACKER_ROUTE = "habit_tracker"
    const val SYMPTOMS_DETAIL_ROUTE = "symptoms_detail"
    const val EMOTION_STATS_ROUTE = "emotion_statistics"
    const val DETAILED_EMOTION_RECORD_ROUTE = "detailed_emotion_record"
    const val EMOTION_RECORD_REVIEW_ROUTE = "emotion_record_review" // 🔧 情绪记录回顾页面
    const val EMOTION_RECORD_DETAIL_ROUTE = "emotion_record_detail" // 🔧 情绪记录详情页面
    
    // 习惯追踪相关路由
    const val HABIT_DETAIL_ROUTE = "habit_detail"
    
    // 反思相关路径
    const val REFLECTION_ROUTE = "reflection"
    const val REFLECTION_DETAIL_ROUTE = "reflection_detail/{reflectionId}"
    const val ADD_REFLECTION_ROUTE = "add_reflection"
    const val EDIT_REFLECTION_ROUTE = "edit_reflection/{reflectionId}"
    
    // 愿望池相关路由
    const val WISH_LIST_ROUTE = "wish_list"
    const val WISH_DETAIL_ROUTE = "wish_detail/{wishId}"
    const val WISH_STATISTICS_ROUTE = "wish_statistics"
    
    // 习惯追踪扩展路由
    const val ADD_HABIT_ROUTE = "add_habit"
    
    // 药物管理相关路由
    const val MEDICATION_MANAGEMENT_ROUTE = "medication_management"
    const val MEDICATION_TEMPLATE_DETAIL_ROUTE = "medication_template_detail/{templateId}"
    const val MEDICATION_HISTORY_DETAIL_ROUTE = "medication_history_detail/{date}"
    
    // 参数定义
    val goalDetailArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
        }
    )
    
    val goalBreakdownArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
        }
    )
    
    val goalReviewArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
        }
    )
    
    val goalEditArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
        }
    )
    
    // 目标模板参数
    val goalTemplateEditArguments = listOf(
        navArgument("templateId") {
            type = NavType.StringType
        }
    )
    
    val goalTemplateImportArguments = listOf(
        navArgument("category") {
            type = NavType.StringType
            nullable = true
            defaultValue = null
        }
    )
    
    val saveGoalAsTemplateArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
        }
    )
    
    val taskDetailArguments = listOf(
        navArgument("taskId") {
            type = NavType.StringType
        }
    )
    
    val taskEditArguments = listOf(
        navArgument("taskId") {
            type = NavType.StringType
        }
    )
    
    val aiAssistantWithTaskArguments = listOf(
        navArgument("taskId") {
            type = NavType.StringType
            nullable = true
            defaultValue = null
        }
    )
    
    val aiModelSettingsArguments = listOf(
        navArgument("configId") {
            type = NavType.StringType
            nullable = true
            defaultValue = null
        }
    )
    
    val habitDetailArguments = listOf(
        navArgument("habitId") {
            type = NavType.StringType
        }
    )
    
    val emotionalDetailArguments = listOf(
        navArgument("dateKey") {
            type = NavType.StringType
        },
        navArgument("emotion") {
            type = NavType.StringType
        }
    )
    
    // 反思相关参数
    val reflectionDetailArguments = listOf(
        navArgument("reflectionId") {
            type = NavType.StringType
        }
    )
    
    val reflectionEditArguments = listOf(
        navArgument("reflectionId") {
            type = NavType.StringType
        }
    )
    
    val wishDetailArguments = listOf(
        navArgument("wishId") {
            type = NavType.StringType
        }
    )
    
    // 目标路由构建
    fun goalDetailRoute(goalId: String) = "goal_detail/$goalId"
    fun goalBreakdownRoute(goalId: String) = "goal_breakdown/$goalId"
    fun goalReviewRoute(goalId: String) = "goal_review/$goalId"
    fun editGoalRoute(goalId: String) = "edit_goal/$goalId"
    fun goalCompletionAnalysisRoute() = "goal_completion_analysis"
    fun goalCompletionAnalysisRoute(timeRange: String) = "goal_completion_analysis/$timeRange"
    
    // 目标模板路由构建
    fun goalTemplateEditRoute(templateId: String) = "goal_template_edit/$templateId"
    fun saveGoalAsTemplateRoute(goalId: String) = "save_goal_as_template/$goalId"
    fun goalTemplateImportRoute(category: String? = null) = if (category != null) "goal_template_import?category=$category" else "goal_template_import"
    
    // 任务路由构建
    fun taskDetailRoute(taskId: String) = "task_detail/$taskId"
    fun taskEditRoute(taskId: String) = "task_edit/$taskId"
    fun aiAssistantWithTaskRoute(taskId: String?) = if (taskId != null) "ai_assistant/$taskId" else "ai_assistant"
    
    // AI设置路由构建
    fun aiModelSettingsRoute(configId: String? = null) = if (configId != null) "ai_model_settings/$configId" else "ai_model_settings"
    
    // 习惯路由构建
    fun habitDetailRoute(habitId: String) = "habit_detail/$habitId"
    
    // 情绪记录路由构建
    fun emotionalDetailRoute(dateKey: String, emotion: String) = "detailed_emotion_record/$dateKey/$emotion"
    
    // 反思路由构建
    fun reflectionDetailRoute(reflectionId: String) = "reflection_detail/$reflectionId"
    fun editReflectionRoute(reflectionId: String) = "edit_reflection/$reflectionId"
    
    // 愿望池路由构建
    fun wishDetailRoute(wishId: String) = "wish_detail/$wishId"
}