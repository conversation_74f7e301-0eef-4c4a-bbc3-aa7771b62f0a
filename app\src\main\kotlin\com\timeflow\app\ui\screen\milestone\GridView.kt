package com.timeflow.app.ui.screen.milestone

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.Image
import androidx.core.net.toUri
import coil.compose.rememberAsyncImagePainter
import com.timeflow.app.R
import java.time.format.DateTimeFormatter

/**
 * 网格视图 - 按类别分组展示里程碑
 */
@Composable
fun GridView(
    milestones: List<Milestone>,
    onMilestoneClick: (Milestone) -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 按类别分组
    val groupedMilestones = remember(milestones) {
        milestones.groupBy { it.category }
    }
    
    // 类别排序 (确保有内容的类别优先显示)
    val sortedCategories = remember(groupedMilestones) {
        MilestoneCategory.values()
            .sortedByDescending { category -> groupedMilestones[category]?.size ?: 0 }
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        contentPadding = PaddingValues(16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = modifier.fillMaxSize()
    ) {
        sortedCategories.forEach { category ->
            val categoryMilestones = groupedMilestones[category] ?: emptyList()
            if (categoryMilestones.isNotEmpty()) {
                // 类别标题
                item(span = { GridItemSpan(2) }) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.padding(vertical = 8.dp, horizontal = 4.dp)
                    ) {
                        Icon(
                            imageVector = category.icon,
                            contentDescription = null,
                            tint = category.color,
                            modifier = Modifier.size(24.dp)
                        )
                        
                        Text(
                            text = category.displayName,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onBackground
                        )
                        
                        Text(
                            text = "(${categoryMilestones.size})",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f),
                            modifier = Modifier.padding(start = 4.dp)
                        )
                    }
                }
                
                // 类别下的里程碑卡片
                items(
                    items = categoryMilestones.sortedByDescending { it.date },
                    key = { it.id }
                ) { milestone ->
                    MilestoneGridCard(
                        milestone = milestone,
                        onClick = { onMilestoneClick(milestone) }
                    )
                }
            }
        }
    }
}

/**
 * 网格视图里程碑卡片
 */
@Composable
fun MilestoneGridCard(
    milestone: Milestone,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 移除渐变色定义，改为使用低饱和度的单一背景色
    val backgroundColor = remember(milestone.category) {
        // 降低饱和度，保持柔和的马卡龙风格
        milestone.category.color.copy(alpha = 0.25f)
    }
    
    // 创建本地变量避免智能类型转换问题
    val hasImage = milestone.imageUri != null
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(if (hasImage) 200.dp else 160.dp) // 使用本地变量判断
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            // 背景颜色或图片
            val imageUri = milestone.imageUri // 创建本地变量避免智能类型转换问题
            if (imageUri != null) {
                // 显示图片作为背景
                Image(
                    painter = rememberAsyncImagePainter(
                        model = imageUri.toUri(),
                        error = painterResource(R.drawable.ic_image_placeholder)
                    ),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp) // 图片高度
                        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)),
                    contentScale = ContentScale.Crop
                )
            } else {
                // 使用纯色背景
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                        .background(
                            color = backgroundColor,
                            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                        )
                )
            }
            
            // 内容
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部空间，给图片留出位置或显示图标
                val imageUri2 = milestone.imageUri // 再次创建本地变量避免智能类型转换问题
                if (imageUri2 == null) {
                    // 如果没有图片，显示图标
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(12.dp))
                            .background(Color.White),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = milestone.category.icon,
                            contentDescription = null,
                            tint = milestone.category.color,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                } else {
                    // 如果有图片，添加足够的空间
                    Spacer(modifier = Modifier.height(80.dp))
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 标题
                Text(
                    text = milestone.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 描述
                Text(
                    text = milestone.description,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 日期
                Text(
                    text = milestone.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            // 完成状态指示器
            if (milestone.completionPercentage >= 100f) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(8.dp)
                        .size(24.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color.White),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "已完成",
                        tint = milestone.category.color,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
} 