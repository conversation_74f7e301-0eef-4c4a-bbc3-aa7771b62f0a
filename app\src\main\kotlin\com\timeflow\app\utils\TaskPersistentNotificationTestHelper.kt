package com.timeflow.app.utils

import android.content.Context
import android.content.Intent
import android.util.Log
import com.timeflow.app.service.TaskPersistentNotificationService
import com.timeflow.app.service.TaskPersistentNotificationManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 任务常驻通知测试助手
 * 用于测试和调试任务常驻通知功能
 */
@Singleton
class TaskPersistentNotificationTestHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskPersistentNotificationManager: TaskPersistentNotificationManager
) {
    
    companion object {
        private const val TAG = "TaskPersistentNotificationTestHelper"
    }
    
    /**
     * 测试启动任务常驻通知
     */
    fun testStartPersistentNotification() {
        Log.d(TAG, "🧪 测试启动任务常驻通知")
        try {
            taskPersistentNotificationManager.startPersistentNotification()
            Log.d(TAG, "✅ 测试启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 测试启动失败", e)
        }
    }
    
    /**
     * 测试停止任务常驻通知
     */
    fun testStopPersistentNotification() {
        Log.d(TAG, "🧪 测试停止任务常驻通知")
        try {
            taskPersistentNotificationManager.stopPersistentNotification()
            Log.d(TAG, "✅ 测试停止成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 测试停止失败", e)
        }
    }
    
    /**
     * 测试刷新任务数据
     */
    fun testRefreshTaskData() {
        Log.d(TAG, "🧪 测试刷新任务数据")
        try {
            taskPersistentNotificationManager.refreshTaskData()
            Log.d(TAG, "✅ 测试刷新成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 测试刷新失败", e)
        }
    }
    
    /**
     * 直接启动服务进行测试
     */
    fun testDirectServiceStart() {
        Log.d(TAG, "🧪 测试直接启动服务")
        try {
            val intent = Intent(context, TaskPersistentNotificationService::class.java).apply {
                action = TaskPersistentNotificationService.ACTION_START_PERSISTENT
            }
            context.startForegroundService(intent)
            Log.d(TAG, "✅ 直接启动服务成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 直接启动服务失败", e)
        }
    }
    
    /**
     * 测试完成任务操作
     */
    fun testCompleteTask(taskId: String, taskTitle: String) {
        Log.d(TAG, "🧪 测试完成任务: $taskTitle")
        try {
            taskPersistentNotificationManager.completeTask(taskId, taskTitle)
            Log.d(TAG, "✅ 测试完成任务成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 测试完成任务失败", e)
        }
    }
    
    /**
     * 检查服务运行状态
     */
    fun checkServiceStatus(): Boolean {
        val isRunning = taskPersistentNotificationManager.isRunning()
        Log.d(TAG, "🔍 服务运行状态: ${if (isRunning) "运行中" else "已停止"}")
        return isRunning
    }
    
    /**
     * 切换服务状态
     */
    fun toggleService() {
        Log.d(TAG, "🔄 切换服务状态")
        try {
            taskPersistentNotificationManager.togglePersistentNotification()
            Log.d(TAG, "✅ 切换服务状态成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 切换服务状态失败", e)
        }
    }
}
