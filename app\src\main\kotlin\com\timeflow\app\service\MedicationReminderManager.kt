package com.timeflow.app.service

import android.app.AlarmManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.timeflow.app.MainActivity
import com.timeflow.app.R
import com.timeflow.app.data.model.Medication
import com.timeflow.app.ui.screen.settings.NotificationSettings
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用药提醒管理器
 * 负责处理用药通知的调度、发送和管理
 */
@Singleton
class MedicationReminderManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "MedicationReminderManager"
        const val MEDICATION_CHANNEL_ID = "MEDICATION_REMINDERS"
        const val MISSED_DOSE_CHANNEL_ID = "MISSED_DOSE_REMINDERS"
        const val ADHERENCE_CHANNEL_ID = "ADHERENCE_REPORTS"
        
        // 通知ID范围
        private const val MEDICATION_NOTIFICATION_BASE_ID = 3000
        private const val MISSED_DOSE_NOTIFICATION_BASE_ID = 4000
        private const val ADHERENCE_NOTIFICATION_BASE_ID = 5000
        
        // Intent extras
        const val EXTRA_MEDICATION_ID = "medication_id"
        const val EXTRA_MEDICATION_NAME = "medication_name"
        const val EXTRA_MEDICATION_DOSAGE = "medication_dosage"
        const val EXTRA_REMINDER_TIME = "reminder_time"
        const val EXTRA_REPEAT_COUNT = "repeat_count"
    }
    
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    private val notificationManager = NotificationManagerCompat.from(context)
    private val vibrator = ContextCompat.getSystemService(context, Vibrator::class.java)
    
    init {
        createNotificationChannels()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    MEDICATION_CHANNEL_ID,
                    "用药提醒",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "用药时间提醒通知"
                    enableVibration(true)
                    enableLights(true)
                    setShowBadge(true)
                },
                
                NotificationChannel(
                    MISSED_DOSE_CHANNEL_ID,
                    "漏服提醒",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "漏服用药提醒通知"
                    enableVibration(true)
                    enableLights(true)
                    setShowBadge(true)
                },
                
                NotificationChannel(
                    ADHERENCE_CHANNEL_ID,
                    "用药依从性报告",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "用药依从性分析报告"
                    enableVibration(false)
                    enableLights(false)
                    setShowBadge(false)
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                systemNotificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    /**
     * 调度用药提醒
     */
    fun scheduleMedicationReminder(
        medication: Medication,
        reminderTime: Calendar,
        settings: NotificationSettings
    ) {
        if (!settings.notificationsEnabled || !settings.medicationRemindersEnabled) {
            Log.d(TAG, "用药提醒已禁用，跳过调度")
            return
        }

        try {
            // 计算实际提醒时间（根据设置提前几分钟）
            val actualReminderTime = reminderTime.clone() as Calendar
            actualReminderTime.add(Calendar.MINUTE, -settings.medicationAdvanceTime)
            
            val intent = Intent(context, MedicationReminderReceiver::class.java).apply {
                putExtra(EXTRA_MEDICATION_ID, medication.id)
                putExtra(EXTRA_MEDICATION_NAME, medication.name)
                putExtra(EXTRA_MEDICATION_DOSAGE, medication.dosage)
                putExtra(EXTRA_REMINDER_TIME, reminderTime.timeInMillis)
                putExtra(EXTRA_REPEAT_COUNT, 0)
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                getMedicationNotificationId(medication.id),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 设置闹钟
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    actualReminderTime.timeInMillis,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    actualReminderTime.timeInMillis,
                    pendingIntent
                )
            }
            
            Log.d(TAG, "用药提醒已设置: ${medication.name} at ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(actualReminderTime.time)}")
            
        } catch (e: Exception) {
            Log.e(TAG, "设置用药提醒失败: ${medication.name}", e)
        }
    }
    
    /**
     * 取消用药提醒
     */
    fun cancelMedicationReminder(medicationId: String) {
        try {
            val intent = Intent(context, MedicationReminderReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                getMedicationNotificationId(medicationId),
                intent,
                PendingIntent.FLAG_NO_CREATE or PendingIntent.FLAG_IMMUTABLE
            )
            
            pendingIntent?.let { pi ->
                alarmManager.cancel(pi)
                pi.cancel()
                Log.d(TAG, "用药提醒已取消: $medicationId")
            }
            
            // 同时取消通知
            notificationManager.cancel(getMedicationNotificationId(medicationId))
            
        } catch (e: Exception) {
            Log.e(TAG, "取消用药提醒失败: $medicationId", e)
        }
    }
    
    /**
     * 显示用药提醒通知（简化版本，使用TimeFlowNotificationManager）
     */
    suspend fun showMedicationNotification(
        medicationId: String,
        medicationName: String,
        dosage: String,
        reminderTime: Long,
        repeatCount: Int,
        settings: NotificationSettings,
        notificationManager: com.timeflow.app.utils.TimeFlowNotificationManager
    ) {
        try {
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val reminderTimeStr = timeFormat.format(Date(reminderTime))

            // 使用统一的通知管理器
            notificationManager.showMedicationReminder(
                medicationId = medicationId,
                medicationName = medicationName,
                dosage = dosage,
                reminderTime = reminderTimeStr,
                settings = settings
            )

            Log.d(TAG, "用药提醒通知已发送: $medicationName")

        } catch (e: Exception) {
            Log.e(TAG, "显示用药提醒通知失败", e)
        }
    }
    
    /**
     * 生成用药通知ID
     */
    private fun getMedicationNotificationId(medicationId: String): Int {
        return MEDICATION_NOTIFICATION_BASE_ID + medicationId.hashCode().and(0x7FFFFFFF) % 1000
    }
}

/**
 * 用药提醒广播接收器
 */
class MedicationReminderReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        val medicationId = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_ID) ?: return
        val medicationName = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_NAME) ?: return
        val dosage = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_DOSAGE) ?: ""
        val reminderTime = intent.getLongExtra(MedicationReminderManager.EXTRA_REMINDER_TIME, 0L)
        val repeatCount = intent.getIntExtra(MedicationReminderManager.EXTRA_REPEAT_COUNT, 0)
        
        // 使用协程来调用suspend函数
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            try {
                // 创建通知管理器实例
                val notificationHelper = com.timeflow.app.utils.NotificationHelper(context)
                val notificationManager = com.timeflow.app.utils.TimeFlowNotificationManager(context, notificationHelper)

                // 获取通知设置（这里简化处理，实际应该从DataStore获取）
                val settings = com.timeflow.app.ui.screen.settings.NotificationSettings()

                // 格式化时间
                val timeFormat = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault())
                val reminderTimeStr = timeFormat.format(java.util.Date(reminderTime))

                // 直接使用TimeFlowNotificationManager显示通知
                notificationManager.showMedicationReminder(
                    medicationId = medicationId,
                    medicationName = medicationName,
                    dosage = dosage,
                    reminderTime = reminderTimeStr,
                    settings = settings
                )

                Log.d("MedicationReceiver", "用药提醒通知已发送: $medicationName")
            } catch (e: Exception) {
                Log.e("MedicationReceiver", "发送用药提醒通知失败", e)
            }
        }
    }
}

/**
 * 用药动作广播接收器
 */
class MedicationActionReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        val medicationId = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_ID) ?: return
        val action = intent.action ?: return
        
        val notificationManager = NotificationManagerCompat.from(context)
        
        when (action) {
            "MEDICATION_TAKEN" -> {
                // 用户点击了"已服用"
                val notificationId = 3000 + medicationId.hashCode().and(0x7FFFFFFF) % 1000
                notificationManager.cancel(notificationId)
                Log.d("MedicationAction", "用户确认已服用: $medicationId")
                
                // 这里可以记录用药记录到数据库
                // recordMedicationTaken(medicationId, System.currentTimeMillis())
            }
            
            "MEDICATION_SNOOZE" -> {
                // 用户点击了"稍后提醒"
                val medicationName = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_NAME) ?: return
                val dosage = intent.getStringExtra(MedicationReminderManager.EXTRA_MEDICATION_DOSAGE) ?: ""
                val originalReminderTime = intent.getLongExtra(MedicationReminderManager.EXTRA_REMINDER_TIME, 0L)
                val repeatCount = intent.getIntExtra(MedicationReminderManager.EXTRA_REPEAT_COUNT, 0)
                
                // 取消当前通知
                val notificationId = 3000 + medicationId.hashCode().and(0x7FFFFFFF) % 1000
                notificationManager.cancel(notificationId)
                
                // 设置重复提醒（5分钟后）
                val reminderManager = MedicationReminderManager(context)
                val nextReminderTime = System.currentTimeMillis() + (5 * 60 * 1000) // 5分钟后
                
                val nextIntent = Intent(context, MedicationReminderReceiver::class.java).apply {
                    putExtra(MedicationReminderManager.EXTRA_MEDICATION_ID, medicationId)
                    putExtra(MedicationReminderManager.EXTRA_MEDICATION_NAME, medicationName)
                    putExtra(MedicationReminderManager.EXTRA_MEDICATION_DOSAGE, dosage)
                    putExtra(MedicationReminderManager.EXTRA_REMINDER_TIME, originalReminderTime)
                    putExtra(MedicationReminderManager.EXTRA_REPEAT_COUNT, repeatCount + 1)
                }
                
                val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    notificationId + repeatCount + 10,
                    nextIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        nextReminderTime,
                        pendingIntent
                    )
                } else {
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        nextReminderTime,
                        pendingIntent
                    )
                }
                
                Log.d("MedicationAction", "用药提醒已延后: $medicationId")
            }
        }
    }
} 