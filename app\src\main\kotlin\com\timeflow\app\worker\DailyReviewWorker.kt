package com.timeflow.app.worker

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.receiver.DailyReviewAlarmReceiver
import java.util.Calendar

/**
 * 每日回顾工作器
 * 用于设置每日回顾通知的定时提醒
 */
class DailyReviewWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "DailyReviewWorker"
        const val KEY_REVIEW_TIME = "review_time" // 格式: HH:mm
        const val DAILY_REVIEW_REQUEST_CODE = 2001
    }
    
    override suspend fun doWork(): Result {
        try {
            val reviewTime = inputData.getString(KEY_REVIEW_TIME) ?: return Result.failure()
            
            Log.d(TAG, "开始设置每日回顾提醒，时间: $reviewTime")
            
            // 设置每日回顾的闹钟
            scheduleAlarm(reviewTime)
            
            return Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "设置每日回顾提醒失败: ${e.message}", e)
            return Result.failure()
        }
    }
    
    /**
     * 设置每日重复的回顾闹钟
     */
    private fun scheduleAlarm(reviewTime: String) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        // 解析回顾时间
        val (hour, minute) = reviewTime.split(":").map { it.toInt() }
        
        // 创建广播意图
        val intent = Intent(context, DailyReviewAlarmReceiver::class.java).apply {
            putExtra(DailyReviewAlarmReceiver.EXTRA_REVIEW_TIME, reviewTime)
        }
        
        // 创建PendingIntent
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            DAILY_REVIEW_REQUEST_CODE,
            intent,
            pendingIntentFlags
        )
        
        // 设置闹钟时间
        val calendar = Calendar.getInstance().apply {
            timeInMillis = System.currentTimeMillis()
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // 如果设置时间已经过去，则设置为明天的这个时间
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_YEAR, 1)
                Log.d(TAG, "回顾时间已过，设置为明天 ${hour}:${minute}")
            } else {
                Log.d(TAG, "回顾时间设置为今天 ${hour}:${minute}")
            }
        }
        
        // 取消之前的闹钟
        alarmManager.cancel(pendingIntent)
        
        // 设置新的重复闹钟
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0及以上版本需要考虑Doze模式
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                pendingIntent
            )
            Log.d(TAG, "每日回顾提醒已设置(精确闹钟): ${calendar.time}")
        } else {
            // 低版本Android可以直接设置重复闹钟
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                AlarmManager.INTERVAL_DAY,
                pendingIntent
            )
            Log.d(TAG, "每日回顾提醒已设置(重复闹钟): ${calendar.time}")
        }
        
        Log.d(TAG, "✅ 每日回顾提醒设置成功！下次提醒时间: ${calendar.time}")
    }
    
    /**
     * 取消每日回顾提醒
     */
    fun cancelDailyReviewReminder(context: Context) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        val intent = Intent(context, DailyReviewAlarmReceiver::class.java)
        
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            DAILY_REVIEW_REQUEST_CODE,
            intent,
            pendingIntentFlags
        )
        
        // 取消闹钟
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
        
        Log.d(TAG, "每日回顾提醒已取消")
    }
} 