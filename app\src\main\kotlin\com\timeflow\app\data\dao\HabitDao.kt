package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.entity.Habit
import com.timeflow.app.data.entity.HabitRecord
import com.timeflow.app.data.entity.HabitReminder
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalDateTime

@Dao
interface HabitDao {
    
    // ==================== 习惯相关操作 ====================
    
    @Query("SELECT * FROM habits WHERE isActive = 1 ORDER BY sortOrder ASC, createdAt DESC")
    fun getAllActiveHabits(): Flow<List<Habit>>

    @Query("SELECT * FROM habits ORDER BY sortOrder ASC, createdAt DESC")
    suspend fun getAllHabits(): List<Habit>
    
    @Query("SELECT * FROM habits WHERE id = :habitId AND isActive = 1")
    suspend fun getHabitById(habitId: String): Habit?
    
    @Query("SELECT * FROM habits WHERE id = :habitId")
    suspend fun getHabitByIdIncludeInactive(habitId: String): Habit?
    
    @Query("SELECT * FROM habits WHERE category = :category AND isActive = 1 ORDER BY sortOrder ASC")
    fun getHabitsByCategory(category: String): Flow<List<Habit>>
    
    @Query("SELECT * FROM habits WHERE relatedGoalId = :goalId AND isActive = 1")
    suspend fun getHabitsByGoalId(goalId: String): List<Habit>
    
    @Query("SELECT * FROM habits WHERE relatedGoalId = :goalId AND isActive = 1")
    fun getHabitsByGoalIdFlow(goalId: String): Flow<List<Habit>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabit(habit: Habit)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabits(habits: List<Habit>)
    
    @Update
    suspend fun updateHabit(habit: Habit)
    
    @Delete
    suspend fun deleteHabit(habit: Habit)
    
    @Query("DELETE FROM habits WHERE id = :habitId")
    suspend fun deleteHabitById(habitId: String)
    
    @Query("UPDATE habits SET isActive = 0 WHERE id = :habitId")
    suspend fun deactivateHabit(habitId: String)
    
    @Query("UPDATE habits SET isActive = 1 WHERE id = :habitId")
    suspend fun activateHabit(habitId: String)
    
    @Query("UPDATE habits SET sortOrder = :sortOrder WHERE id = :habitId")
    suspend fun updateHabitSortOrder(habitId: String, sortOrder: Int)
    
    // ==================== 习惯记录相关操作 ====================
    
    @Query("SELECT * FROM habit_records WHERE habitId = :habitId ORDER BY date DESC")
    suspend fun getRecordsForHabit(habitId: String): List<HabitRecord>

    @Query("SELECT * FROM habit_records ORDER BY date DESC")
    suspend fun getAllHabitRecords(): List<HabitRecord>
    
    @Query("SELECT * FROM habit_records WHERE habitId = :habitId AND date = :date")
    suspend fun getRecordForDate(habitId: String, date: LocalDate): HabitRecord?
    
    @Query("SELECT * FROM habit_records WHERE habitId = :habitId AND date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    suspend fun getRecordsForDateRange(habitId: String, startDate: LocalDate, endDate: LocalDate): List<HabitRecord>
    
    @Query("SELECT * FROM habit_records WHERE date = :date")
    suspend fun getAllRecordsForDate(date: LocalDate): List<HabitRecord>
    
    @Query("SELECT * FROM habit_records WHERE habitId = :habitId AND completed = 1 ORDER BY date DESC")
    suspend fun getCompletedRecordsForHabit(habitId: String): List<HabitRecord>
    
    @Query("SELECT COUNT(*) FROM habit_records WHERE habitId = :habitId AND completed = 1")
    suspend fun getTotalCompletionsForHabit(habitId: String): Int
    
    @Query("SELECT COUNT(*) FROM habit_records WHERE habitId = :habitId AND completed = 1 AND date BETWEEN :startDate AND :endDate")
    suspend fun getCompletionsForDateRange(habitId: String, startDate: LocalDate, endDate: LocalDate): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabitRecord(record: HabitRecord)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabitRecords(records: List<HabitRecord>)
    
    @Update
    suspend fun updateHabitRecord(record: HabitRecord)
    
    @Delete
    suspend fun deleteHabitRecord(record: HabitRecord)
    
    @Query("DELETE FROM habit_records WHERE id = :recordId")
    suspend fun deleteHabitRecordById(recordId: String)
    
    @Query("DELETE FROM habit_records WHERE habitId = :habitId")
    suspend fun deleteAllRecordsForHabit(habitId: String)
    
    // ==================== 习惯提醒相关操作 ====================
    
    @Query("SELECT * FROM habit_reminders WHERE habitId = :habitId AND enabled = 1")
    suspend fun getRemindersForHabit(habitId: String): List<HabitReminder>
    
    @Query("SELECT * FROM habit_reminders WHERE enabled = 1")
    suspend fun getAllActiveReminders(): List<HabitReminder>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabitReminder(reminder: HabitReminder)
    
    @Update
    suspend fun updateHabitReminder(reminder: HabitReminder)
    
    @Delete
    suspend fun deleteHabitReminder(reminder: HabitReminder)
    
    @Query("DELETE FROM habit_reminders WHERE habitId = :habitId")
    suspend fun deleteAllRemindersForHabit(habitId: String)
    
    // ==================== 统计查询 ====================
    
    @Query("""
        SELECT h.*, 
               COALESCE(total_completions.count, 0) as totalCompletions,
               COALESCE(current_streak.streak, 0) as currentStreak
        FROM habits h
        LEFT JOIN (
            SELECT habitId, COUNT(*) as count 
            FROM habit_records 
            WHERE completed = 1 
            GROUP BY habitId
        ) total_completions ON h.id = total_completions.habitId
        LEFT JOIN (
            SELECT habitId, COUNT(*) as streak
            FROM habit_records 
            WHERE completed = 1 AND date >= date('now', '-30 days')
            GROUP BY habitId
        ) current_streak ON h.id = current_streak.habitId
        WHERE h.isActive = 1
        ORDER BY h.sortOrder ASC, h.createdAt DESC
    """)
    fun getHabitsWithStats(): Flow<List<Habit>>
    
    @Query("""
        SELECT COUNT(DISTINCT habitId) 
        FROM habit_records 
        WHERE completed = 1 AND date = :date
    """)
    suspend fun getCompletedHabitsCountForDate(date: LocalDate): Int
    
    @Query("""
        SELECT AVG(completion_rate) as avgCompletionRate
        FROM (
            SELECT habitId, 
                   CAST(COUNT(CASE WHEN completed = 1 THEN 1 END) AS FLOAT) / COUNT(*) as completion_rate
            FROM habit_records 
            WHERE date >= date('now', '-30 days')
            GROUP BY habitId
        )
    """)
    suspend fun getAverageCompletionRate(): Float?
    
    @Query("""
        SELECT h.name, COUNT(hr.id) as completions
        FROM habits h
        LEFT JOIN habit_records hr ON h.id = hr.habitId AND hr.completed = 1
        WHERE h.isActive = 1
        GROUP BY h.id, h.name
        ORDER BY completions DESC
        LIMIT :limit
    """)
    suspend fun getTopHabitsByCompletions(limit: Int = 5): List<HabitCompletionStat>
    
    // ==================== 连续天数计算 ====================
    
    // 获取当前连续天数（从今天向前数连续完成的天数）
    @Query("""
        SELECT COUNT(*) 
        FROM habit_records 
        WHERE habitId = :habitId 
        AND completed = 1 
        AND date >= (
            SELECT COALESCE(MAX(date), date('now', '-1000 days'))
            FROM habit_records 
            WHERE habitId = :habitId 
            AND completed = 0 
            AND date <= date('now')
        )
        AND date <= date('now')
    """)
    suspend fun getCurrentStreakForHabit(habitId: String): Int
    
    // 获取历史最长连续天数（简化版本，返回总完成天数作为近似值）
    @Query("""
        SELECT COUNT(*) 
        FROM habit_records 
        WHERE habitId = :habitId 
        AND completed = 1
    """)
    suspend fun getLongestStreakForHabit(habitId: String): Int?
    
    // 获取最近完成的记录（用于业务逻辑中计算精确的连续天数）
    @Query("""
        SELECT * 
        FROM habit_records 
        WHERE habitId = :habitId 
        AND date <= date('now')
        ORDER BY date DESC 
        LIMIT 30
    """)
    suspend fun getRecentRecordsForStreak(habitId: String): List<HabitRecord>
}

/**
 * 习惯完成统计数据类
 */
data class HabitCompletionStat(
    val name: String,
    val completions: Int
) 