package com.timeflow.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.*
import com.timeflow.app.data.repository.WishRepository
import com.timeflow.app.ui.screen.wishlist.WishStatistics
import com.timeflow.app.ui.screen.wishlist.WishHeatMapData
import com.timeflow.app.ui.screen.wishlist.WishInsight
import com.timeflow.app.ui.screen.wishlist.WishInsights
import com.timeflow.app.ui.screen.wishlist.WishAchievement
import com.timeflow.app.ui.screen.wishlist.WishFilters
import com.timeflow.app.ui.screen.wishlist.HeatMapDay
import com.timeflow.app.ui.screen.wishlist.Achievement
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

@HiltViewModel
class WishStatisticsViewModel @Inject constructor(
    private val wishRepository: WishRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(WishStatisticsUiState())
    val uiState: StateFlow<WishStatisticsUiState> = _uiState.asStateFlow()

    // 缓存策略 - 统计数据缓存
    private var lastCacheUpdate: LocalDateTime? = null
    private var cachedStatistics: WishStatistics? = null

    init {
        observeWishChanges()
    }

    /**
     * 监听愿望数据变化，实时更新统计
     */
    private fun observeWishChanges() {
        viewModelScope.launch {
            wishRepository.getAllActiveWishes()
                .combine(wishRepository.getAllArchivedWishes()) { active, archived ->
                    active + archived
                }
                .collect { allWishes ->
                    // 无论是否刷新缓存，都要更新愿望列表（用于筛选）
                    _uiState.value = _uiState.value.copy(allWishes = allWishes)
                    
                    if (shouldRefreshCache()) {
                        calculateAndCacheStatistics(allWishes)
                    }
                }
        }
    }

    /**
     * 主动加载统计数据
     */
    fun loadStatistics() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // 并行获取活跃和已归档的愿望
                val activeWishesDeferred = async { wishRepository.getAllActiveWishes().first() }
                val archivedWishesDeferred = async { wishRepository.getAllArchivedWishes().first() }
                
                val activeWishes = activeWishesDeferred.await()
                val archivedWishes = archivedWishesDeferred.await()
                val allWishes = activeWishes + archivedWishes

                // 检查缓存是否需要更新
                if (shouldRefreshCache()) {
                    calculateAndCacheStatistics(allWishes)
                } else {
                    // 使用缓存数据
                    cachedStatistics?.let { stats ->
                        _uiState.value = _uiState.value.copy(
                            statistics = stats,
                            allWishes = allWishes, // 即使使用缓存也要更新愿望列表
                            isLoading = false
                        )
                    }
                }

                // 生成其他统计数据
                generateHeatMapData(allWishes)
                generateInsights(allWishes)
                generateAchievements(allWishes)

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "统计数据加载失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    /**
     * 检查是否需要刷新缓存
     * 每日凌晨或数据发生变化时更新
     */
    private fun shouldRefreshCache(): Boolean {
        val now = LocalDateTime.now()
        val lastUpdate = lastCacheUpdate
        
        return when {
            lastUpdate == null -> true
            lastUpdate.toLocalDate() != now.toLocalDate() -> true // 跨日更新
            else -> false
        }
    }

    /**
     * 计算并缓存统计数据
     */
    private suspend fun calculateAndCacheStatistics(allWishes: List<WishModel>) {
        try {
            // 🔧 基础统计 - 已完成的愿望包括已实现和已完成且已归档的愿望
            val totalWishes = allWishes.size
            val achievedWishes = allWishes.count {
                it.status == WishStatus.ACHIEVED ||
                (it.status == WishStatus.ARCHIVED && it.achievedAt != null)
            }
            val incubatingWishes = allWishes.count { it.status == WishStatus.ACTIVE }
            val realizationRate = if (totalWishes > 0) achievedWishes.toFloat() / totalWishes else 0f

            // 类别分布统计
            val categoryDistribution = allWishes
                .groupBy { it.category }
                .mapValues { it.value.size }

            // 🔧 最近成就 - 包括已实现和已完成且已归档的愿望
            val recentAchievement = allWishes
                .filter {
                    (it.status == WishStatus.ACHIEVED ||
                     (it.status == WishStatus.ARCHIVED && it.achievedAt != null)) &&
                    it.achievedAt != null
                }
                .maxByOrNull { it.achievedAt!! }
                ?.title
                ?: "暂无"

            // 最近愿望列表（最新的5个）
            val recentWishList = allWishes
                .sortedByDescending { it.createdAt }
                .take(5)

            val statistics = WishStatistics(
                totalWishes = totalWishes,
                achievementRate = realizationRate,
                realizationRate = realizationRate,
                recentAchievement = recentAchievement,
                categoryDistribution = categoryDistribution,
                achievedWishes = achievedWishes,
                activeWishes = incubatingWishes,
                incubatingWishes = incubatingWishes,
                recentWishList = recentWishList
            )

            // 更新缓存
            cachedStatistics = statistics
            lastCacheUpdate = LocalDateTime.now()

            _uiState.value = _uiState.value.copy(
                statistics = statistics,
                allWishes = allWishes, // 保存完整的愿望列表用于筛选
                isLoading = false
            )

        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "统计计算失败: ${e.message}",
                isLoading = false
            )
        }
    }

    /**
     * 生成热力图数据
     * 显示过去365天的愿望活动强度
     */
    private fun generateHeatMapData(allWishes: List<WishModel>) {
        try {
            val today = LocalDate.now()
            val heatMapData = mutableListOf<WishHeatMapData>()

            // 生成过去365天的数据
            for (i in 364 downTo 0) {
                val date = today.minusDays(i.toLong())
                
                // 计算该日期的愿望活动强度
                val intensity = calculateDayIntensity(date, allWishes)
                
                heatMapData.add(WishHeatMapData(date = date, intensity = intensity.toDouble()))
            }

            _uiState.value = _uiState.value.copy(heatMapData = heatMapData)

        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "热力图数据生成失败: ${e.message}"
            )
        }
    }

    /**
     * 计算某一天的愿望活动强度
     */
    private fun calculateDayIntensity(date: LocalDate, allWishes: List<WishModel>): Int {
        var intensity = 0
        
        allWishes.forEach { wish ->
            // 愿望创建日
            if (wish.createdAt.toLocalDate() == date) {
                intensity += 2
            }
            
            // 愿望实现日
            if (wish.achievedAt?.toLocalDate() == date) {
                intensity += 3
            }
            
            // 愿望更新日
            if (wish.updatedAt.toLocalDate() == date && wish.updatedAt.toLocalDate() != wish.createdAt.toLocalDate()) {
                intensity += 1
            }
        }
        
        return minOf(intensity, 4) // 限制最大强度为4
    }

    /**
     * 生成智能洞察
     */
    private fun generateInsights(allWishes: List<WishModel>) {
        try {
            // 高频关键词分析
            val allKeywords = mutableListOf<String>()
            allWishes.forEach { wish ->
                // 从标题和描述中提取关键词
                allKeywords.addAll(extractKeywords(wish.title))
                allKeywords.addAll(extractKeywords(wish.description))
                allKeywords.addAll(wish.tags)
            }
            
            val frequentKeywords = allKeywords
                .groupBy { it }
                .mapValues { it.value.size }
                .filter { it.value >= 2 } // 至少出现2次
                .toList()
                .sortedByDescending { it.second }
                .take(5)
                .map { it.first }

            // 本月建议生成
            val monthlyRecommendation = generateMonthlyRecommendation(allWishes)

            // 生成洞察列表
            val insightList = frequentKeywords.map { keyword ->
                WishInsight(
                    keyword = keyword,
                    frequency = allKeywords.count { it == keyword }
                )
            }

            val insights = WishInsights(
                frequentKeywords = frequentKeywords,
                monthlyRecommendation = monthlyRecommendation,
                insights = insightList
            )

            _uiState.value = _uiState.value.copy(insights = insights.insights)

        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "洞察生成失败: ${e.message}"
            )
        }
    }

    /**
     * 从文本中提取关键词
     */
    private fun extractKeywords(text: String): List<String> {
        if (text.isBlank()) return emptyList()
        
        // 简单的关键词提取逻辑
        val commonWords = setOf("的", "了", "在", "是", "我", "你", "他", "她", "它", "和", "与", "或", "但", "然而", "因为", "所以")
        
        return text
            .replace(Regex("[^\\u4e00-\\u9fa5\\w\\s]"), " ") // 保留中文、字母、数字
            .split(Regex("\\s+"))
            .filter { it.length >= 2 && !commonWords.contains(it) }
            .distinct()
    }

    /**
     * 生成本月建议
     */
    private fun generateMonthlyRecommendation(allWishes: List<WishModel>): String {
        val activeWishes = allWishes.filter { it.status == WishStatus.ACTIVE }
        
        return when {
            activeWishes.isEmpty() -> "是时候添加一些新的愿望了！让梦想点亮前进的路。"
            
            activeWishes.size >= 10 -> "你有很多活跃的愿望！建议挑选1-2个最重要的开始行动。"
            
            activeWishes.any { it.priority >= 4 } -> {
                val highPriorityCount = activeWishes.count { it.priority >= 4 }
                "你有${highPriorityCount}个高优先级愿望，现在是实现它们的最佳时机！"
            }
            
            else -> "继续培育你的愿望吧！每一个梦想都值得被认真对待。"
        }
    }

    /**
     * 生成成就里程碑
     */
    private fun generateAchievements(allWishes: List<WishModel>) {
        try {
            val achievements = mutableListOf<WishAchievement>()
            
            val totalWishes = allWishes.size
            val achievedCount = allWishes.count { it.status == WishStatus.ACHIEVED }
            val categories = allWishes.map { it.category }.distinct()
            
            // 愿望收集家成就
            achievements.add(
                WishAchievement(
                    id = "collector_novice",
                    title = "愿望收集家",
                    description = "收集10个愿望",
                    isUnlocked = totalWishes >= 10,
                    requiredCount = 10,
                    currentCount = totalWishes,
                    category = "收集"
                )
            )
            
            // 梦想实现者成就
            achievements.add(
                WishAchievement(
                    id = "achiever_starter",
                    title = "梦想实现者",
                    description = "实现第一个愿望",
                    isUnlocked = achievedCount >= 1,
                    requiredCount = 1,
                    currentCount = achievedCount,
                    category = "实现"
                )
            )
            
            // 多元探索者成就
            achievements.add(
                WishAchievement(
                    id = "explorer",
                    title = "多元探索者",
                    description = "涉足5个不同类别",
                    isUnlocked = categories.size >= 5,
                    requiredCount = 5,
                    currentCount = categories.size,
                    category = "探索"
                )
            )
            
            // 坚持者成就
            val hasLongTermWish = allWishes.any { 
                it.createdAt.isBefore(LocalDateTime.now().minusMonths(6))
            }
            achievements.add(
                WishAchievement(
                    id = "persistent",
                    title = "长期主义者",
                    description = "维护愿望超过6个月",
                    isUnlocked = hasLongTermWish,
                    requiredCount = 1,
                    currentCount = if (hasLongTermWish) 1 else 0,
                    category = "坚持"
                )
            )

            _uiState.value = _uiState.value.copy(achievements = achievements)

        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "成就生成失败: ${e.message}"
            )
        }
    }

    /**
     * 切换筛选对话框
     */
    fun toggleFilterDialog() {
        _uiState.value = _uiState.value.copy(
            showFilterDialog = !_uiState.value.showFilterDialog
        )
    }

    /**
     * 应用筛选条件
     */
    fun applyFilters(filters: WishFilters) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                filters = filters,
                showFilterDialog = false
            )
            
            // 重新加载数据应用筛选
            loadStatistics()
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 手动刷新统计数据
     */
    fun refreshStatistics() {
        // 清除缓存强制重新计算
        cachedStatistics = null
        lastCacheUpdate = null
        loadStatistics()
    }

    /**
     * 获取愿望时间轴数据（用于甘特图）
     */
    fun getWishTimeline(): Flow<List<WishTimelineItem>> {
        return wishRepository.getAllActiveWishes()
            .combine(wishRepository.getAllArchivedWishes()) { active, archived ->
                (active + archived)
                    .filter { it.createdAt != null }
                    .map { wish ->
                        WishTimelineItem(
                            id = wish.id,
                            title = wish.title,
                            category = wish.category,
                            startDate = wish.createdAt.toLocalDate(),
                            endDate = wish.achievedAt?.toLocalDate(),
                            status = wish.status,
                            priority = wish.priority
                        )
                    }
                    .sortedBy { it.startDate }
            }
    }
}

/**
 * 愿望统计页面UI状态
 */
data class WishStatisticsUiState(
    val statistics: WishStatistics = WishStatistics(),
    val heatMapData: List<WishHeatMapData> = emptyList(),
    val insights: List<WishInsight> = emptyList(),
    val achievements: List<WishAchievement> = emptyList(),
    val allWishes: List<WishModel> = emptyList(), // 新增：用于筛选的完整愿望列表
    val filters: WishFilters = WishFilters(),
    val showFilterDialog: Boolean = false,
    val isLoading: Boolean = true,
    val error: String? = null
)

/**
 * 愿望时间轴项目（用于甘特图显示）
 */
data class WishTimelineItem(
    val id: String,
    val title: String,
    val category: WishCategory,
    val startDate: LocalDate,
    val endDate: LocalDate? = null,
    val status: WishStatus,
    val priority: Int
) 