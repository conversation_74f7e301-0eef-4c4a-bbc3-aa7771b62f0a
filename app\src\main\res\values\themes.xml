<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- 动态颜色主题 (Android 12+) -->
    <style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- 动态颜色开关，设置为 false 可以关闭动态颜色 -->
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
        <item name="android:statusBarColor">@android:color/background_light</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        
        <!-- 全屏显示设置 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" tools:targetApi="q">false</item>
    </style>
    
    <!-- 静态主题颜色备用 -->
    <style name="Theme.TimeFlow.StaticColors">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryContainer">@color/secondary_light</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <!-- Tertiary colors -->
        <item name="colorTertiary">@color/primary_variant</item>
        <item name="colorOnTertiary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
        <item name="android:statusBarColor">@android:color/background_light</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        
        <!-- 全屏显示设置 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceNavigationBarContrast" tools:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" tools:targetApi="q">false</item>
    </style>

    <style name="Theme.MyApplication" parent="Theme.TimeFlow" />
    
    <!-- 添加在manifest中引用的主题 -->
    <style name="Theme.TimeFlowApp" parent="Theme.TimeFlow" />

    <!-- 优化的主题：保留启动动画，优化其他性能 -->
    <style name="Theme.TimeFlow.OptimizedAnimation" parent="Theme.TimeFlowApp">
        <!-- 保留启动画面的美丽动画 -->
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:windowDisablePreview">false</item>
        
        <!-- 优化Activity过渡动画 - 使用更快的动画 -->
        <item name="android:windowAnimationStyle">@style/OptimizedWindowAnimation</item>
        
        <!-- 启用硬件加速以获得更流畅的动画 -->
        <item name="android:hardwareAccelerated">true</item>
        
        <!-- 优化渲染性能 -->
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">false</item>
        <item name="android:windowAllowReturnTransitionOverlap">false</item>
    </style>

    <!-- 添加一个无动画的主题 -->
    <style name="Theme.TimeFlow.NoAnimation" parent="Theme.TimeFlowApp">
        <!-- 禁用所有窗口动画 -->
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowNoDisplay">false</item>
        <item name="android:windowBackground">@android:color/white</item>
        
        <!-- 禁用启动画面 -->
        <item name="android:windowSplashScreenAnimatedIcon" tools:targetApi="s">@null</item>
        <item name="android:windowSplashScreenAnimationDuration" tools:targetApi="s">0</item>
        <item name="android:windowSplashScreenBrandingImage" tools:targetApi="s">@null</item>
        
        <!-- 禁用Activity过渡动画 -->
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>
    
    <!-- 优化的窗口动画样式 -->
    <style name="OptimizedWindowAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right_fast</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left_fast</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left_fast</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right_fast</item>
    </style>
</resources>