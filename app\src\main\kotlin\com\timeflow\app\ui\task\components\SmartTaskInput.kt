package com.timeflow.app.ui.task.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.Flag
import androidx.compose.material.icons.filled.Tag
import androidx.compose.material.icons.outlined.Mic
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.regex.Pattern

/**
 * Smart Task Input Component - Similar to TickTick's natural language recognition
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartTaskInput(
    onTaskCreated: (title: String, dueDate: LocalDateTime?, priority: Int, tags: List<String>) -> Unit,
    modifier: Modifier = Modifier
) {
    var input by remember { mutableStateOf("") }
    var isExpanded by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current
    val haptics = LocalHapticFeedback.current
    
    // Parse natural language input
    val parsedInfo = remember(input) { parseNaturalLanguage(input) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        // Input field with enhanced styling
        Surface(
            shape = RoundedCornerShape(24.dp),
            color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
            tonalElevation = 2.dp,
            shadowElevation = 1.dp,
            border = BorderStroke(
                width = 1.dp, 
                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
            ),
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 2.dp)
            ) {
                // Add task icon with animation
                val interactionSource = remember { MutableInteractionSource() }
                val isPressed by interactionSource.collectIsPressedAsState()
                
                IconButton(
                    onClick = { 
                        if (input.isNotBlank()) {
                            onTaskCreated(
                                parsedInfo?.title ?: input,
                                parsedInfo?.dueDate,
                                parsedInfo?.priority ?: 0,
                                parsedInfo?.tags ?: emptyList()
                            )
                            haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                            input = ""
                            isExpanded = false
                            keyboardController?.hide()
                        }
                    },
                    interactionSource = interactionSource,
                    modifier = Modifier
                        .size(40.dp)
                        .scale(if (isPressed) 0.9f else 1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add task",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                // Input field with improved styling
                TextField(
                    value = input,
                    onValueChange = { 
                        input = it
                        if (!isExpanded && it.isNotEmpty()) {
                            isExpanded = true
                        } else if (it.isEmpty()) {
                            isExpanded = false
                        }
                    },
                    placeholder = { 
                        Text(
                            "Add task, try 'tomorrow 9am #work !3' to recognize date and priority",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        ) 
                    },
                    modifier = Modifier
                        .weight(1f)
                        .padding(vertical = 4.dp),
                    textStyle = TextStyle(
                        fontSize = 16.sp, 
                        color = MaterialTheme.colorScheme.onSurface
                    ),
                    colors = TextFieldDefaults.textFieldColors(
                        containerColor = Color.Transparent,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent,
                        cursorColor = MaterialTheme.colorScheme.primary
                    ),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (input.isNotBlank()) {
                                onTaskCreated(
                                    parsedInfo?.title ?: input,
                                    parsedInfo?.dueDate,
                                    parsedInfo?.priority ?: 0,
                                    parsedInfo?.tags ?: emptyList()
                                )
                                haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                                input = ""
                                isExpanded = false
                                keyboardController?.hide()
                            }
                        }
                    ),
                    maxLines = 2,
                    singleLine = false
                )
                
                // Voice input button
                IconButton(
                    onClick = { /* Implement voice input */ },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Mic,
                        contentDescription = "Voice input",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // Recognition hint area with improved animation
        AnimatedVisibility(
            visible = isExpanded && parsedInfo != null,
            enter = fadeIn() + expandVertically(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            ),
            exit = fadeOut() + shrinkVertically()
        ) {
            parsedInfo?.let { info ->
                TaskInfoChips(info, modifier = Modifier.padding(top = 12.dp))
            }
        }
    }
}

/**
 * Task information chips with enhanced design
 */
@Composable
private fun TaskInfoChips(
    info: ParsedTaskInfo,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Date tag with improved styling
            if (info.dueDate != null) {
                Surface(
                    shape = RoundedCornerShape(16.dp),
                    color = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.6f),
                    modifier = Modifier
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(16.dp)
                        )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        
                        // Format date for better readability
                        val formattedDate = formatDateForDisplay(info.dueDate)
                        Text(
                            text = formattedDate,
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Priority tag with color coding
            if (info.priority > 0) {
                val priorityColor = when (info.priority) {
                    1 -> Color(0xFFFFA114) // Medium
                    2 -> Color(0xFFFF9500) // High
                    3 -> Color(0xFFFF3B30) // Urgent
                    else -> Color.Gray
                }
                
                Surface(
                    shape = RoundedCornerShape(16.dp),
                    color = priorityColor.copy(alpha = 0.15f),
                    modifier = Modifier
                        .border(
                            width = 1.dp,
                            color = priorityColor.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(16.dp)
                        )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Flag,
                            contentDescription = null,
                            tint = priorityColor,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = when (info.priority) {
                                1 -> "Medium"
                                2 -> "High"
                                3 -> "Urgent"
                                else -> "Low"
                            },
                            fontSize = 12.sp,
                            color = priorityColor,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
        
        // Tags display with enhanced styling
        if (info.tags.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                info.tags.forEach { tag ->
                    Surface(
                        shape = RoundedCornerShape(16.dp),
                        color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.6f),
                        modifier = Modifier
                            .border(
                                width = 1.dp,
                                color = MaterialTheme.colorScheme.secondary.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(16.dp)
                            )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Tag,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = tag,
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Format date in a user-friendly way for display in chips
 */
private fun formatDateForDisplay(dateTime: LocalDateTime?): String {
    if (dateTime == null) return ""
    
    val now = LocalDateTime.now()
    val today = now.toLocalDate()
    val tomorrow = today.plusDays(1)
    
    return when {
        dateTime.toLocalDate() == today -> {
            "Today, " + dateTime.format(DateTimeFormatter.ofPattern("h:mm a"))
        }
        dateTime.toLocalDate() == tomorrow -> {
            "Tomorrow, " + dateTime.format(DateTimeFormatter.ofPattern("h:mm a"))
        }
        dateTime.year == now.year -> {
            dateTime.format(DateTimeFormatter.ofPattern("MMM d, h:mm a"))
        }
        else -> {
            dateTime.format(DateTimeFormatter.ofPattern("yyyy/MM/dd h:mm a"))
        }
    }
}

/**
 * Parsed task information
 */
data class ParsedTaskInfo(
    val title: String,
    val dueDate: LocalDateTime? = null,
    val priority: Int = 0,
    val tags: List<String> = emptyList()
)

/**
 * Parse natural language input
 * Example: "Update report tomorrow 3pm #work !3"
 */
private fun parseNaturalLanguage(input: String): ParsedTaskInfo? {
    if (input.isBlank()) return null
    
    // Parse tags (#tag)
    val tagPattern = Pattern.compile("#([^\\s]+)")
    val tagMatcher = tagPattern.matcher(input)
    val tags = mutableListOf<String>()
    var processedInput = input
    
    while (tagMatcher.find()) {
        val tag = tagMatcher.group(1)
        tags.add(tag)
        processedInput = processedInput.replace("#$tag", "")
    }
    
    // Parse priority (!1, !2, !3)
    val priorityPattern = Pattern.compile("!(\\d)")
    val priorityMatcher = priorityPattern.matcher(processedInput)
    var priority = 0
    
    if (priorityMatcher.find()) {
        priority = priorityMatcher.group(1).toIntOrNull() ?: 0
        processedInput = processedInput.replace(priorityMatcher.group(), "")
    }
    
    // Parse date time expressions
    var dueDate: LocalDateTime? = null
    
    // Handle "today", "tomorrow", "day after tomorrow"
    when {
        processedInput.contains("today") -> {
            dueDate = LocalDateTime.of(LocalDate.now(), LocalTime.of(9, 0))
            processedInput = processedInput.replace("today", "")
        }
        processedInput.contains("tomorrow") -> {
            dueDate = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.of(9, 0))
            processedInput = processedInput.replace("tomorrow", "")
        }
        processedInput.contains("day after tomorrow") -> {
            dueDate = LocalDateTime.of(LocalDate.now().plusDays(2), LocalTime.of(9, 0))
            processedInput = processedInput.replace("day after tomorrow", "")
        }
    }
    
    // Handle specific time
    val timePattern = Pattern.compile("(\\d{1,2})([.:])?(\\d{0,2})\\s*(am|pm|AM|PM)?")
    val timeMatcher = timePattern.matcher(processedInput)
    
    if (timeMatcher.find()) {
        var hour = timeMatcher.group(1).toIntOrNull() ?: 0
        val minute = timeMatcher.group(3).takeIf { it != null && it.isNotEmpty() }?.toIntOrNull() ?: 0
        val ampm = timeMatcher.group(4)
        
        // Adjust hour for AM/PM
        if (ampm != null) {
            if ((ampm.equals("pm", ignoreCase = true)) && hour < 12) {
                hour += 12
            } else if ((ampm.equals("am", ignoreCase = true)) && hour == 12) {
                hour = 0
            }
        }
        
        dueDate = if (dueDate != null) {
            LocalDateTime.of(dueDate.toLocalDate(), LocalTime.of(hour, minute))
        } else {
            LocalDateTime.of(LocalDate.now(), LocalTime.of(hour, minute))
        }
        
        processedInput = processedInput.replace(timeMatcher.group(), "")
    }
    
    // Clean up the input text
    val cleanedTitle = processedInput.trim()
    
    return if (cleanedTitle.isNotEmpty() || tags.isNotEmpty() || priority > 0 || dueDate != null) {
        ParsedTaskInfo(cleanedTitle, dueDate, priority, tags)
    } else {
        null
    }
} 