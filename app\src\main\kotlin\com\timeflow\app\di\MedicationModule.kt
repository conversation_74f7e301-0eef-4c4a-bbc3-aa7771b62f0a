package com.timeflow.app.di

import com.timeflow.app.data.dao.MedicationRecordDao
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.data.repository.MedicationRepository
import com.timeflow.app.data.repository.MedicationRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 药物管理模块的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object MedicationModule {
    
    @Provides
    @Singleton
    fun provideMedicationRecordDao(appDatabase: AppDatabase): MedicationRecordDao {
        return appDatabase.medicationRecordDao()
    }

    @Provides
    @Singleton
    fun provideMedicationRepository(medicationRecordDao: MedicationRecordDao): MedicationRepository {
        return MedicationRepositoryImpl(medicationRecordDao)
    }
} 