<?xml version="1.0" encoding="utf-8"?>
<!-- 小尺寸小组件布局 (2x1) -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_card_background"
    android:padding="12dp">

    <!-- 日期显示 -->
    <LinearLayout
        android:id="@+id/widget_date_section"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_alignParentStart="true"
        android:layout_marginEnd="12dp">

        <TextView
            android:id="@+id/widget_weekday"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="周四"
            android:textSize="12sp"
            android:textColor="@color/widget_today_text_secondary_light"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/widget_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="22"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/widget_today_text_primary_light"
            android:fontFamily="sans-serif-light" />

    </LinearLayout>

    <!-- 任务计数 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_toEndOf="@id/widget_date_section"
        android:layout_alignParentEnd="true"
        android:orientation="vertical"
        android:gravity="center">

        <TextView
            android:id="@+id/widget_task_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="4"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/widget_today_accent_light" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="待办"
            android:textSize="12sp"
            android:textColor="@color/widget_today_text_secondary_light" />

    </LinearLayout>

</RelativeLayout>
