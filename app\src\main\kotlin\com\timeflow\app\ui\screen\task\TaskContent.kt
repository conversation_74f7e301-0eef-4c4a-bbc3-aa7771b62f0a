package com.timeflow.app.ui.screen.task

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.util.UUID
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.screen.task.SubTask
import com.timeflow.app.ui.screen.task.TaskData
import com.timeflow.app.ui.theme.*
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent

/**
 * 子任务输入状态
 */
internal data class SubTaskInputState(
    val text: String = "",
    val datetime: LocalDateTime? = null,
    val priority: Priority = Priority.MEDIUM,
    val note: String = "",
    val attachments: List<String> = emptyList()
)

@OptIn(ExperimentalLayoutApi::class, ExperimentalAnimationApi::class)
@Composable
fun TaskContent(
    task: TaskData,
    onTagsUpdated: (String, List<String>) -> Unit,
    onPriorityUpdated: (String, Priority) -> Unit,
    onSubTaskAdded: (String, SubTask) -> Unit,
    onSubTaskUpdated: (String, SubTask) -> Unit,
    onSubTaskDeleted: (String, String) -> Unit,
    onAllSubTasksCompleted: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 记住当前任务的子任务列表，无条件更新以确保始终显示最新数据
    var localSubTasks by remember { 
        mutableStateOf(task.subTasks) 
    }
    
    // 添加去抖动逻辑，防止频繁刷新
    LaunchedEffect(task.id, task.subTasks) {
        android.util.Log.d("TaskContent", "子任务更新: 当前${localSubTasks.size}个, 新的${task.subTasks.size}个")
        
        // 无条件更新子任务列表，确保UI同步
        localSubTasks = task.subTasks
        android.util.Log.d("TaskContent", "已更新子任务列表: ${localSubTasks.size}个")
        
        // 记录子任务详情
        localSubTasks.forEachIndexed { index, subTask ->
            android.util.Log.d("TaskContent", "子任务[$index]: id=${subTask.id}, title=${subTask.title}, 状态=${subTask.isCompleted}")
        }
    }
    
    // 监听全局刷新事件，确保在其他地方修改子任务时也能更新
    LaunchedEffect(Unit) {
        NotificationCenter.observe<TaskRefreshEvent>()
            .collect { event ->
                if (event.taskId == task.id) {
                    android.util.Log.d("TaskContent", "收到任务刷新事件: ${event.taskId}")
                    // 这里不需要执行任何操作，因为父组件会自动重新加载任务
                    // UI将通过task.subTasks的变化自动更新
                }
            }
    }
    
    // 子任务删除状态
    var taskDeleting by remember { mutableStateOf<String?>(null) }
    
    // 子任务输入面板状态控制
    var showSubTaskInput by remember { mutableStateOf(false) }
    var isEditingDescription by remember { mutableStateOf(false) }
    var descriptionText by remember { mutableStateOf(task.description) }
    
    // 添加一个防抖状态，避免短时间内多次触发相同操作
    var isProcessingOperation by remember { mutableStateOf(false) }
    
    // 监控子任务完成状态
    LaunchedEffect(localSubTasks) {
        // 如果有子任务且全部完成，触发任务完成回调
        if (localSubTasks.isNotEmpty() && localSubTasks.all { it.isCompleted }) {
            onAllSubTasksCompleted()
        }
    }
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clip(RoundedCornerShape(16.dp)),
        color = Color.White,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 0.dp // 禁用Surface自带阴影
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 18.dp, vertical = 18.dp)
        ) {
            // 子任务部分，确保不会在每次重组时触发重复刷新
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 24.dp)
            ) {
                // 子任务标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "子任务",
                            color = TextPrimary,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        
                        if (localSubTasks.isNotEmpty()) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "(${localSubTasks.count { it.isCompleted }}/${localSubTasks.size})",
                                color = TextSecondary,
                                fontSize = 14.sp
                            )
                        }
                    }
                    
                    // 添加子任务按钮
                    IconButton(
                        onClick = { 
                            // 使用防抖逻辑，避免重复点击
                            if (!isProcessingOperation) {
                                isProcessingOperation = true
                                showSubTaskInput = !showSubTaskInput
                                // 设置短延迟后重置操作状态
                                CoroutineScope(Dispatchers.Main).launch {
                                    delay(300)
                                    isProcessingOperation = false
                                }
                            }
                        },
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = if (showSubTaskInput) Icons.Default.Remove else Icons.Default.Add,
                            contentDescription = if (showSubTaskInput) "收起" else "添加子任务",
                            tint = DustyLavender
                        )
                    }
                }
                
                // 添加子任务的输入区域
                AnimatedVisibility(
                    visible = showSubTaskInput,
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    InlineSubTaskInput(
                        onAddSubTask = { title, priority ->
                            // 防止重复操作
                            if (!isProcessingOperation) {
                                isProcessingOperation = true
                                
                                // 创建新的子任务
                                val newSubTask = SubTask(
                                    id = UUID.randomUUID().toString(),
                                    title = title,
                                    isCompleted = false,
                                    priority = priority,
                                    parentTaskId = task.id
                                )
                                
                                // 通过回调添加子任务
                                onSubTaskAdded(task.id, newSubTask)
                                
                                // 立即更新本地列表以提供即时反馈
                                localSubTasks = localSubTasks + newSubTask
                                
                                // 发送刷新事件，确保其他界面也能看到新添加的子任务
                                CoroutineScope(Dispatchers.Main).launch {
                                    // 延迟300ms，确保子任务已经保存到数据库
                                    delay(300)
                                    android.util.Log.d("TaskContent", "发送TaskRefreshEvent，任务ID: ${task.id}")
                                    NotificationCenter.post(TaskRefreshEvent(task.id))
                                    isProcessingOperation = false
                                }
                            }
                        },
                        modifier = Modifier.padding(vertical = 12.dp)
                    )
                }
                
                // 子任务列表显示区域
                AnimatedVisibility(
                    visible = localSubTasks.isNotEmpty(),
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                    ) {
                        // 显示现有子任务 - 使用动画增强UI体验
                        AnimatedSubTaskList(
                            subTasks = localSubTasks,
                            onTaskClick = { /* TODO: Implement task click */ },
                            onCheckChange = { subTask, isChecked ->
                                // 防止重复操作
                                if (!isProcessingOperation) {
                                    isProcessingOperation = true
                                    
                                    // 更新子任务状态
                                    val updatedSubTask = subTask.copy(isCompleted = isChecked)
                                    onSubTaskUpdated(task.id, updatedSubTask)
                                    
                                    // 更新本地状态以提供即时反馈
                                    localSubTasks = localSubTasks.map { 
                                        if (it.id == subTask.id) updatedSubTask else it 
                                    }
                                    
                                    // 检查是否所有子任务都已完成
                                    if (isChecked && localSubTasks.all { it.id == subTask.id || it.isCompleted }) {
                                        onAllSubTasksCompleted()
                                    }
                                    
                                    // 发送刷新事件，确保其他界面也能看到更新的子任务
                                    CoroutineScope(Dispatchers.Main).launch {
                                        // 延迟300ms，确保子任务已经更新到数据库
                                        delay(300)
                                        android.util.Log.d("TaskContent", "发送TaskRefreshEvent，任务ID: ${task.id}")
                                        NotificationCenter.post(TaskRefreshEvent(task.id))
                                        isProcessingOperation = false
                                    }
                                }
                            },
                            onDelete = { subTask ->
                                // 防止重复操作
                                if (!isProcessingOperation) {
                                    isProcessingOperation = true
                                    taskDeleting = subTask.id
                                    
                                    // 使用协程和延迟创建删除动画效果
                                    CoroutineScope(Dispatchers.Main).launch {
                                        delay(300) // 等待动画完成
                                        onSubTaskDeleted(task.id, subTask.id)
                                        // 立即从本地列表中移除以提供即时反馈
                                        localSubTasks = localSubTasks.filter { it.id != subTask.id }
                                        taskDeleting = null
                                        
                                        // 删除后检查剩余子任务是否全部完成
                                        if (localSubTasks.isNotEmpty() && localSubTasks.all { it.isCompleted }) {
                                            onAllSubTasksCompleted()
                                        }
                                        
                                        // 发送刷新事件
                                        android.util.Log.d("TaskContent", "发送TaskRefreshEvent，任务ID: ${task.id}")
                                        NotificationCenter.post(TaskRefreshEvent(task.id))
                                        isProcessingOperation = false
                                    }
                                }
                            },
                            taskDeleting = taskDeleting
                        )
                    }
                }
                
                // 空状态提示 - 当没有子任务且不显示输入框时显示
                AnimatedVisibility(
                    visible = localSubTasks.isEmpty() && !showSubTaskInput,
                    enter = expandVertically() + fadeIn(),
                    exit = shrinkVertically() + fadeOut()
                ) {
                    EmptySubTaskState(
                        onAddClick = { showSubTaskInput = true }
                    )
                }
            }
        }
    }
}

// 内联子任务输入组件
@Composable
private fun InlineSubTaskInput(
    onAddSubTask: (String, Priority) -> Unit,
    modifier: Modifier = Modifier
) {
    var text by remember { mutableStateOf("") }
    var priority by remember { mutableStateOf(Priority.MEDIUM) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .border(
                width = 1.dp,
                color = if (errorMessage != null) Color.Red.copy(alpha = 0.3f) else Color.LightGray.copy(alpha = 0.3f),
                shape = RoundedCornerShape(12.dp)
            )
            .background(Color.White)
            .padding(12.dp)
    ) {
        // 子任务输入行
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedTextField(
                value = text,
                onValueChange = { text = it; errorMessage = null },
                placeholder = { Text("添加子任务...") },
                modifier = Modifier.weight(1f),
                singleLine = true,
                shape = RoundedCornerShape(8.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = DustyLavender,
                    unfocusedBorderColor = Color.LightGray.copy(alpha = 0.5f)
                ),
                textStyle = LocalTextStyle.current.copy(
                    fontSize = 14.sp
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            IconButton(
                onClick = {
                    if (text.isNotBlank()) {
                        onAddSubTask(text.trim(), priority)
                        text = ""
                        errorMessage = null
                    } else {
                        errorMessage = "请输入任务内容"
                    }
                },
                modifier = Modifier
                    .size(36.dp)
                    .background(
                        color = if (text.isNotBlank()) DustyLavender else Color.Transparent,
                        shape = CircleShape
                    ),
                enabled = text.isNotBlank()
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加子任务",
                    tint = if (text.isNotBlank()) Color.White else TextSecondary.copy(alpha = 0.3f)
                )
            }
        }
        
        // 错误信息显示
        if (errorMessage != null) {
            Text(
                text = errorMessage!!,
                color = Color.Red,
                fontSize = 12.sp,
                modifier = Modifier.padding(top = 4.dp, start = 4.dp)
            )
        }
    }
}

// 子任务列表组件
@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun AnimatedSubTaskList(
    subTasks: List<SubTask>,
    onTaskClick: () -> Unit,
    onCheckChange: (SubTask, Boolean) -> Unit,
    onDelete: (SubTask) -> Unit,
    taskDeleting: String?
) {
    if (subTasks.isEmpty()) return
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
    ) {
        subTasks.forEachIndexed { index, subTask ->
            // 子任务的显示动画
            AnimatedVisibility(
                visible = taskDeleting != subTask.id,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessLow
                    )
                ) + fadeIn(),
                exit = shrinkVertically() + fadeOut()
            ) {
                SubTaskItem(
                    subTask = subTask,
                    onTaskClick = onTaskClick,
                    onCheckChange = { isChecked -> onCheckChange(subTask, isChecked) },
                    onDelete = { onDelete(subTask) }
                )
            }
            
            // 最后一个子任务不显示分隔线
            if (index < subTasks.size - 1 && taskDeleting != subTask.id) {
                Divider(
                    color = Color.LightGray.copy(alpha = 0.2f),
                    thickness = 0.5.dp,
                    modifier = Modifier.padding(vertical = 2.dp)
                )
            }
        }
    }
}

// 子任务项组件
@Composable
private fun SubTaskItem(
    subTask: SubTask,
    onTaskClick: () -> Unit,
    onCheckChange: (Boolean) -> Unit,
    onDelete: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = subTask.isCompleted,
            onCheckedChange = onCheckChange,
            colors = CheckboxDefaults.colors(
                checkedColor = DustyLavender,
                uncheckedColor = Color.Gray.copy(alpha = 0.6f)
            )
        )
        
        Text(
            text = subTask.title,
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 8.dp),
            style = MaterialTheme.typography.bodyMedium.copy(
                textDecoration = if (subTask.isCompleted) TextDecoration.LineThrough else TextDecoration.None,
                color = if (subTask.isCompleted) Color.Gray else Color.DarkGray
            )
        )
        
        IconButton(
            onClick = onDelete,
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = "删除",
                tint = Color.Gray.copy(alpha = 0.6f),
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

// 空子任务状态组件
@Composable
private fun EmptySubTaskState(
    onAddClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "没有子任务",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            OutlinedButton(
                onClick = onAddClick,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = DustyLavender
                ),
                border = BorderStroke(1.dp, DustyLavender)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("添加子任务")
            }
        }
    }
} 