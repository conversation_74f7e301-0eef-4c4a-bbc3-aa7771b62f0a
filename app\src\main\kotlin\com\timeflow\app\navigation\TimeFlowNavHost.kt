package com.timeflow.app.navigation

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.*
import androidx.navigation.compose.*
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.navigation.AppDestinations
import com.timeflow.app.ui.screen.home.UnifiedHomeScreen
import com.timeflow.app.ui.screen.task.TaskDetailScreen
import com.timeflow.app.ui.screen.task.TaskListFullScreen
import com.timeflow.app.ui.screen.task.TaskEditScreen
import com.timeflow.app.ui.screen.wishlist.WishListScreen
import com.timeflow.app.ui.screen.settings.AboutScreen

@Composable
fun TimeFlowNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    // 监听导航状态
    val currentBackStackEntry by navController.currentBackStackEntryAsState()
    
    LaunchedEffect(currentBackStackEntry) {
        Log.d("Navigation", "Current route: ${currentBackStackEntry?.destination?.route}")
        Log.d("Navigation", "NavGraph: ${navController.graph.route}")
        Log.d("Navigation", "Current arguments: ${currentBackStackEntry?.arguments}")
        Log.d("Navigation", "Destination: ${currentBackStackEntry?.destination?.route}")
        // 使用公共API获取导航历史
        val previousBackStackEntry = navController.previousBackStackEntry
        Log.d("Navigation", "Previous route: ${previousBackStackEntry?.destination?.route}")

        // 记录导航图中所有可用的目的地
        Log.d("Navigation", "Available destinations in graph:")
        Log.d("Navigation", "Graph nodes: ${navController.graph.nodes}")
    }

    NavHost(
        navController = navController,
        startDestination = AppDestinations.HOME_ROUTE,
        modifier = modifier,
        route = "app"  // 添加根路由
    ) {
        // 主页
        composable(AppDestinations.HOME_ROUTE) {
            Log.d("Navigation", "Composing HomeScreen")
            UnifiedHomeScreen(
                navController = navController,
                timeFlowViewModel = hiltViewModel(),
                taskViewModel = hiltViewModel()
            )
        }

        // 任务列表全屏页面
        composable(AppDestinations.TASK_LIST_FULL_ROUTE) {
            Log.d("Navigation", "Composing TaskListFullScreen")
            TaskListFullScreen(navController = navController)
        }

        // 任务详情页面
        composable(
            route = AppDestinations.TASK_DETAIL_ROUTE,
            arguments = AppDestinations.taskDetailArguments
        ) { backStackEntry ->
            val taskId = backStackEntry.arguments?.getString("taskId")
            Log.d("Navigation", "Composing TaskDetailScreen with taskId: $taskId")
            Log.d("Navigation", "Arguments: ${backStackEntry.arguments}")
            Log.d("Navigation", "SavedStateHandle: ${backStackEntry.savedStateHandle}")
            Log.d("Navigation", "Route in NavHost: ${AppDestinations.TASK_DETAIL_ROUTE}")
            Log.d("Navigation", "Full destination: ${backStackEntry.destination}")
            if (taskId != null) {
                TaskDetailScreen(
                    taskId = taskId,
                    onEditClick = {
                        navController.navigate("${AppDestinations.TASK_EDIT_ROUTE}/$taskId")
                    },
                    onDismiss = {
                        navController.popBackStack()
                    },
                    navController = navController
                )
            } else {
                Log.e("Navigation", "TaskId is null!")
            }
        }

        // 任务编辑页面
        composable(
            route = "${AppDestinations.TASK_EDIT_ROUTE}/{taskId}",
            arguments = listOf(
                navArgument("taskId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            Log.d("Navigation", "Composing TaskEditScreen")
            Log.d("Navigation", "Arguments: ${backStackEntry.arguments}")
            Log.d("Navigation", "TaskId: ${backStackEntry.arguments?.getString("taskId")}")
            TaskEditScreen(
                taskId = backStackEntry.arguments?.getString("taskId"),
                navController = navController
            )
        }
        
        // 不带参数的任务编辑页面（创建新任务）
        composable(AppDestinations.TASK_EDIT_ROUTE) {
            Log.d("Navigation", "Composing TaskEditScreen (new task)")
            TaskEditScreen(
                taskId = null,
                navController = navController
            )
        }
        
        // 愿望池页面
        composable(AppDestinations.WISH_LIST_ROUTE) {
            Log.d("Navigation", "Composing WishListScreen")
            WishListScreen(navController = navController)
        }
        
        // 关于页面
        composable(AppDestinations.ABOUT_ROUTE) {
            Log.d("Navigation", "Composing AboutScreen")
            AboutScreen(
                navController = navController,
                calendarViewModel = hiltViewModel()
            )
        }
        
        // 习惯详情页面
        composable(
            route = AppDestinations.HABIT_DETAIL_ROUTE,
            arguments = AppDestinations.habitDetailArguments
        ) { backStackEntry ->
            val habitId = backStackEntry.arguments?.getString("habitId")
            Log.d("Navigation", "Composing HabitDetailScreen with habitId: $habitId")
            if (habitId != null) {
                com.timeflow.app.ui.screen.health.HabitDetailScreen(
                    habitId = habitId,
                    navController = navController
                )
            } else {
                Log.e("Navigation", "HabitId is null!")
            }
        }
    }
} 