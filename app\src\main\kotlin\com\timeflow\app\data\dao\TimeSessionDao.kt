package com.timeflow.app.data.dao

import androidx.room.*
import com.timeflow.app.data.model.TimeSession
import kotlinx.coroutines.flow.Flow
import java.time.Instant
import java.time.LocalDate

/**
 * 时间会话数据访问对象
 * 提供时间会话的数据库操作方法
 */
@Dao
interface TimeSessionDao {
    
    /**
     * 插入新的时间会话
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: TimeSession)
    
    /**
     * 更新时间会话
     */
    @Update
    suspend fun updateSession(session: TimeSession)
    
    /**
     * 删除时间会话
     */
    @Delete
    suspend fun deleteSession(session: TimeSession)
    
    /**
     * 根据ID获取时间会话
     */
    @Query("SELECT * FROM time_sessions WHERE id = :sessionId")
    suspend fun getSessionById(sessionId: String): TimeSession?
    
    /**
     * 获取所有时间会话
     */
    @Query("SELECT * FROM time_sessions ORDER BY startTime DESC")
    fun getAllSessions(): Flow<List<TimeSession>>
    
    /**
     * 获取指定日期的时间会话
     */
    @Query("""
        SELECT * FROM time_sessions 
        WHERE date(startTime / 1000, 'unixepoch') = :date 
        ORDER BY startTime DESC
    """)
    fun getSessionsByDate(date: String): Flow<List<TimeSession>>
    
    /**
     * 获取指定任务的所有时间会话
     */
    @Query("""
        SELECT * FROM time_sessions 
        WHERE taskId = :taskId 
        ORDER BY startTime DESC
    """)
    fun getSessionsByTaskId(taskId: String): Flow<List<TimeSession>>
    
    /**
     * 获取指定任务的总计时时长
     */
    @Query("""
        SELECT COALESCE(SUM(duration), 0) 
        FROM time_sessions 
        WHERE taskId = :taskId AND isCompleted = 1
    """)
    fun getTaskTotalTime(taskId: String): Flow<Long>
    
    /**
     * 获取今日总计时时长
     */
    @Query("""
        SELECT COALESCE(SUM(duration), 0) 
        FROM time_sessions 
        WHERE date(startTime / 1000, 'unixepoch') = date('now') 
        AND isCompleted = 1
    """)
    fun getTodayTotalTime(): Flow<Long>
    
    /**
     * 获取本周总计时时长
     */
    @Query("""
        SELECT COALESCE(SUM(duration), 0) 
        FROM time_sessions 
        WHERE startTime >= :weekStart AND startTime <= :weekEnd
        AND isCompleted = 1
    """)
    fun getWeekTotalTime(weekStart: Long, weekEnd: Long): Flow<Long>
    
    /**
     * 获取本月总计时时长
     */
    @Query("""
        SELECT COALESCE(SUM(duration), 0) 
        FROM time_sessions 
        WHERE strftime('%Y-%m', startTime / 1000, 'unixepoch') = strftime('%Y-%m', 'now')
        AND isCompleted = 1
    """)
    fun getMonthTotalTime(): Flow<Long>
    
    /**
     * 获取进行中的会话（用于恢复）
     */
    @Query("""
        SELECT * FROM time_sessions 
        WHERE isCompleted = 0 AND endTime IS NULL
        ORDER BY startTime DESC 
        LIMIT 1
    """)
    suspend fun getActiveSession(): TimeSession?
    
    /**
     * 获取指定时间范围内的会话统计
     */
    @Query("""
        SELECT 
            COUNT(*) as sessionCount,
            COALESCE(SUM(duration), 0) as totalDuration,
            AVG(duration) as avgDuration
        FROM time_sessions 
        WHERE startTime >= :startTime AND startTime <= :endTime
        AND isCompleted = 1
    """)
    suspend fun getSessionStats(startTime: Long, endTime: Long): SessionStats
    
    /**
     * 按任务分组的时间统计
     */
    @Query("""
        SELECT 
            taskId,
            taskName,
            COUNT(*) as sessionCount,
            COALESCE(SUM(duration), 0) as totalDuration
        FROM time_sessions 
        WHERE startTime >= :startTime AND startTime <= :endTime
        AND isCompleted = 1
        GROUP BY taskId, taskName
        ORDER BY totalDuration DESC
    """)
    fun getTaskTimeStats(startTime: Long, endTime: Long): Flow<List<TaskTimeStats>>
    
    /**
     * 按任务分组的时间统计（包含标签信息）
     */
    @Query("""
        SELECT 
            ts.taskId,
            ts.taskName,
            COUNT(*) as sessionCount,
            COALESCE(SUM(ts.duration), 0) as totalDuration,
            COALESCE(t.tagIds, '') as taskTags
        FROM time_sessions ts
        LEFT JOIN tasks t ON ts.taskId = t.id
        WHERE ts.startTime >= :startTime AND ts.startTime <= :endTime
        AND ts.isCompleted = 1
        GROUP BY ts.taskId, ts.taskName, t.tagIds
        ORDER BY totalDuration DESC
    """)
    fun getTaskTimeStatsWithTags(startTime: Long, endTime: Long): Flow<List<TaskTimeStatsWithTags>>
    
    /**
     * 删除指定日期之前的旧记录
     */
    @Query("DELETE FROM time_sessions WHERE startTime < :cutoffTime")
    suspend fun deleteOldSessions(cutoffTime: Long)
    
    /**
     * 获取最近N个会话
     */
    @Query("""
        SELECT * FROM time_sessions 
        WHERE isCompleted = 1
        ORDER BY startTime DESC 
        LIMIT :limit
    """)
    fun getRecentSessions(limit: Int): Flow<List<TimeSession>>
}

/**
 * 会话统计数据类
 */
data class SessionStats(
    val sessionCount: Int,
    val totalDuration: Long,
    val avgDuration: Double
)

/**
 * 任务时间统计数据类
 */
data class TaskTimeStats(
    val taskId: String,
    val taskName: String,
    val sessionCount: Int,
    val totalDuration: Long
)

/**
 * 包含标签信息的任务时间统计数据类
 */
data class TaskTimeStatsWithTags(
    val taskId: String,
    val taskName: String,
    val sessionCount: Int,
    val totalDuration: Long,
    val taskTags: String? = null
) {
    /**
     * 将标签字符串转换为标签列表
     */
    fun getTagsList(): List<String> {
        return if (taskTags.isNullOrBlank()) {
            emptyList()
        } else {
            taskTags.split(",").map { it.trim() }.filter { it.isNotEmpty() }
        }
    }
} 