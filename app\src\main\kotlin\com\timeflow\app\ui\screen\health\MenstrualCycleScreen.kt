@file:OptIn(ExperimentalMaterial3Api::class, kotlin.ExperimentalUnsignedTypes::class)

package com.timeflow.app.ui.screen.health

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.derivedStateOf
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.draw.scale
import androidx.core.view.WindowCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontStyle
import com.timeflow.app.R
import com.timeflow.app.data.preferences.UserPreferencesManager
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel
import com.timeflow.app.ui.viewmodel.CycleType
import com.timeflow.app.ui.viewmodel.CycleDay
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm
import com.timeflow.app.data.entity.CycleRecord
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import java.time.Instant
import java.time.ZoneId
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.viewmodel.PeriodStatistics
import com.timeflow.app.ui.viewmodel.OperationResult
import com.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity

/**
 * 定义周期类型对应的颜色方案 - 使用科学和用户友好的颜色系统
 */
private object CycleColors {
    // 月经周期四个主要阶段的基础色 - 根据用户需求优化后的颜色
    val PERIOD = Color(0xFFE57F9B).copy(alpha = 0.4f)      // 红色系 - 象征月经期
    val FOLLICULAR = Color(0xFF9E68BF).copy(alpha = 0.4f)  // 紫色系 - 象征卵泡发育
    val OVULATION = Color(0xFF9ABF68).copy(alpha = 0.4f)   // 绿色系 - 象征排卵日
    val LUTEAL = Color(0xFFCFC432).copy(alpha = 0.4f)      // 黄色系 - 象征黄体期
    
    // 辅助阶段颜色
    val FERTILE = Color(0xFF90CAF9)     // 蓝色系 - 象征易孕期
    val PREDICTED = Color.Transparent   // 透明 - 预测的月经期只使用边框

    
    // 边框颜色 - 更深的色调增强可视性
    val PERIOD_BORDER = Color.Transparent     // 月经期不使用边框
    val FOLLICULAR_BORDER = Color(0xFF8E24AA) // 卵泡期边框
    val OVULATION_BORDER = Color(0xFF7CB342)  // 排卵期边框
    val LUTEAL_BORDER = Color(0xFFFFB300)     // 黄体期边框
    val FERTILE_BORDER = Color(0xFF1976D2)    // 易孕期边框
    val PREDICTED_BORDER = Color(0xFFD81B60)  // 预测月经期边框

    
    // 文字颜色 - 确保高对比度和可读性
    val PERIOD_TEXT = Color(0xFF880E4F)      // 月经期文字
    val FOLLICULAR_TEXT = Color(0xFF4A148C)  // 卵泡期文字
    val OVULATION_TEXT = Color(0xFF33691E)   // 排卵期文字
    val LUTEAL_TEXT = Color(0xFF827717)      // 黄体期文字
    val FERTILE_TEXT = Color(0xFF0D47A1)     // 易孕期文字
    val PREDICTED_TEXT = Color(0xFFD81B60)   // 预测月经期文字

    
    // 其他辅助颜色
    val CLOUD = Color(0xFFEEEEEE)            // 云背景色
    val DROP_LIGHT = Color(0xFFFFCDD2)       // 轻度血滴
    val DROP_DARK = Color(0xFFE57373)        // 重度血滴
    val PAIN_FILL = Color(0xFFFFCDD2)        // 疼痛指示
    val BACKGROUND = Color(0xFFFFF9FA)       // 整体背景色
    
    // 辅助功能 - 获取指定周期类型的颜色
    fun getColorForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD
            // 🔧 修改：不显示卵泡期、黄体期，只显示经期和预测经期
            CycleType.FOLLICULAR -> Color.Transparent
            CycleType.OVULATION -> OVULATION
            CycleType.LUTEAL -> Color.Transparent
            CycleType.FERTILE -> FERTILE
            CycleType.PREDICTED -> PREDICTED
            // 🔧 修改：预测经期只显示边框，不显示背景
            CycleType.PREDICTED_PERIOD -> Color.Transparent
            CycleType.PREDICTED_FOLLICULAR -> Color.Transparent
            CycleType.PREDICTED_OVULATION -> Color.Transparent
            CycleType.PREDICTED_LUTEAL -> Color.Transparent
            CycleType.PREDICTED_FERTILE -> Color.Transparent
            CycleType.NONE -> Color.Transparent
        }
    }
    
    // 获取周期类型对应的边框颜色
    fun getBorderForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD_BORDER
            // 🔧 修改：不显示卵泡期、黄体期边框
            CycleType.FOLLICULAR -> Color.Transparent
            CycleType.OVULATION -> OVULATION_BORDER
            CycleType.LUTEAL -> Color.Transparent
            CycleType.FERTILE -> FERTILE_BORDER
            CycleType.PREDICTED -> PREDICTED_BORDER
            // 🔧 修改：显示预测经期边框
            CycleType.PREDICTED_PERIOD -> Color(0xFFE91E63).copy(alpha = 0.8f)
            CycleType.PREDICTED_FOLLICULAR -> Color.Transparent
            CycleType.PREDICTED_OVULATION -> OVULATION_BORDER
            CycleType.PREDICTED_LUTEAL -> Color.Transparent
            CycleType.PREDICTED_FERTILE -> FERTILE_BORDER
            CycleType.NONE -> Color.LightGray
        }
    }
    
    // 获取周期类型对应的文字颜色
    fun getTextColorForType(type: CycleType): Color {
        return when(type) {
            CycleType.PERIOD -> PERIOD_TEXT
            // 🔧 修改：不显示卵泡期、黄体期文字颜色
            CycleType.FOLLICULAR -> Color.DarkGray
            CycleType.OVULATION -> OVULATION_TEXT
            CycleType.LUTEAL -> Color.DarkGray
            CycleType.FERTILE -> FERTILE_TEXT
            CycleType.PREDICTED -> PREDICTED_TEXT
            // 🔧 修改：预测经期使用特殊颜色
            CycleType.PREDICTED_PERIOD -> Color(0xFFE91E63).copy(alpha = 0.8f)
            CycleType.PREDICTED_FOLLICULAR -> Color.DarkGray
            CycleType.PREDICTED_OVULATION -> OVULATION_TEXT
            CycleType.PREDICTED_LUTEAL -> Color.DarkGray
            CycleType.PREDICTED_FERTILE -> FERTILE_TEXT
            CycleType.NONE -> Color.DarkGray
        }
    }
    
    // 判断是否为预测类型
    fun isPredictedType(type: CycleType): Boolean {
        return type == CycleType.PREDICTED ||
               type == CycleType.PREDICTED_PERIOD ||
               type == CycleType.PREDICTED_FOLLICULAR ||
               type == CycleType.PREDICTED_OVULATION ||
               type == CycleType.PREDICTED_LUTEAL ||
               type == CycleType.PREDICTED_FERTILE
    }
}

/**
 * 绘制缓存Canvas - 优化频繁重绘
 * 
 * 注意：onDraw 回调不能包含任何 @Composable 函数的调用，
 * 也不能使用 CompositionLocal 值（如 LocalDensity.current）。
 * 请在调用此函数前先计算所有需要的尺寸值。
 */
@Composable
fun CachedCanvas(
    modifier: Modifier = Modifier,
    onDraw: DrawScope.() -> Unit
) {
    Canvas(
        modifier = modifier.drawWithCache {
            onDrawBehind {
                onDraw()
            }
        }
    ) {
        // Empty canvas body, the actual drawing happens in drawWithCache
    }
}

/**
 * 生理周期页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenstrualCycleScreen(
    onBackClick: () -> Unit,
    viewModel: MenstrualCycleViewModel = hiltViewModel(),
    navController: NavController
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 使用SystemBarManager来设置状态栏，确保一致性
    DisposableEffect(Unit) {
        var originalStatusBarColor: Int = 0
        var originalNavigationBarColor: Int = 0
        
        activity?.let { act ->
            val window = act.window
            
            try {
                // 保存原始状态
                originalStatusBarColor = window.statusBarColor
                originalNavigationBarColor = window.navigationBarColor
                
                // 使用SystemBarManager设置不透明的状态栏
                SystemBarManager.forceOpaqueStatusBar(act)
            } catch (e: Exception) {
                Log.e("MenstrualCycleScreen", "设置状态栏出错: ${e.message}")
            }
        }
        
        onDispose {
            activity?.let { act ->
                try {
                    // 恢复原始颜色
                    act.window.statusBarColor = originalStatusBarColor
                    act.window.navigationBarColor = originalNavigationBarColor
                } catch (e: Exception) {
                    Log.e("MenstrualCycleScreen", "恢复状态栏出错: ${e.message}")
                }
            }
        }
    }
    
    // 从ViewModel获取状态 - Flow类型使用collectAsState
    val selectedDate by viewModel.selectedDate.collectAsState()
    val selectedDayData by viewModel.selectedDayData.collectAsState()
    val currentCycle by viewModel.currentCycle.collectAsState(initial = null)
    val selectedMonth by viewModel.selectedMonth.collectAsState()
    val cycleData by viewModel.cycleData.collectAsState()
    
    // 获取UserPreferencesManager实例
    val userPreferencesManager = remember {
        EntryPointAccessors.fromApplication(
            context.applicationContext,
            UserPreferencesManagerEntryPoint::class.java
        ).userPreferencesManager()
    }

    // 获取新增的状态
    val healthDataPermissionGranted by viewModel.healthDataPermissionGranted.collectAsState()
    val isSyncing by viewModel.isSyncing.collectAsState()
    val hasDataConflict by viewModel.hasDataConflict.collectAsState()
    
    // 专业预测算法相关状态
    val predictionResult by viewModel.predictionResult.collectAsState()
    val cycleRegularity by viewModel.cycleRegularity.collectAsState()
    val predictionConfidence by viewModel.predictionConfidence.collectAsState()
    val healthRecommendations by viewModel.healthRecommendations.collectAsState()
    
    // 直接获取非Flow类型的值
    val averageCycleLength = viewModel.getAverageCycleLength()
    val averagePeriodLength = viewModel.getAveragePeriodLength()
    val currentCycleStatusText = viewModel.getCurrentCycleStatusText()
    
    // 获取cycles数据用于显示历史记录
    val cycles by viewModel.cycles.collectAsState()
    
    // 为选中日期记录经期开始的函数
    val recordPeriodStartForSelectedDate = {
        viewModel.recordPeriodStartForDate(selectedDate)
    }

    // 添加缺失的函数
    val showInvalidDateToast = { context: Context ->
        Toast.makeText(context, "无法编辑此日期", Toast.LENGTH_SHORT).show()
    }
    
    // 控制引导对话框显示
    var showTutorial by remember { mutableStateOf(userPreferencesManager.isFirstTimeMenstrualCycle()) }
    
    // 控制冲突解决对话框显示
    var showConflictDialog by remember { mutableStateOf(false) }
    
    // 控制详情浮层显示
    var showDetailOverlay by remember { mutableStateOf(false) }

    // 添加缺失的变量定义
    var longPressedDate by remember { mutableStateOf<LocalDate?>(null) }
    var editDialogDate by remember { mutableStateOf<LocalDate?>(null) }
    var showEditDialog by remember { mutableStateOf(false) }
    var showDateEditDialog by remember { mutableStateOf(false) }
    var showAddPeriodDialog by remember { mutableStateOf(false) }
    var editingCycle by remember { mutableStateOf<CycleRecord?>(null) }
    
    // 检测同步状态变化和冲突状态变化
    LaunchedEffect(isSyncing, hasDataConflict) {
        if (!isSyncing && hasDataConflict) {
            showConflictDialog = true
        }
    }
    
    // 首次使用引导对话框
    if (showTutorial) {
        TutorialDialog(
            onDismiss = {
                showTutorial = false
                userPreferencesManager.markMenstrualCycleAsUsed()
            }
        )
    }
    
    // 数据冲突解决对话框
    if (showConflictDialog) {
        ConflictResolutionDialog(
            options = viewModel.getConflictResolutionOptions(),
            onOptionSelected = { option ->
                viewModel.resolveDataConflict(option)
                showConflictDialog = false
            },
            onDismiss = { 
                viewModel.resolveDataConflict(0) // 默认选择第一个选项
                showConflictDialog = false 
            }
        )
    }
    
    // 详情浮层
    if (showDetailOverlay) {
        CycleDayDetailOverlay(
            cycleDay = selectedDayData,
            onDismiss = { showDetailOverlay = false }
        )
    }
    
    Scaffold(
        containerColor = CycleColors.BACKGROUND,
        topBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
                    .height(56.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 返回按钮
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = CycleColors.PERIOD_TEXT
                        )
                    }
                    
                    // BloomCycle 标题
                    Text(
                        text = "BloomCycle",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.PERIOD_TEXT,
                        maxLines = 1,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    
                    // 同步状态指示器
                    if (isSyncing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = CycleColors.PERIOD_TEXT,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    

                }
            }
        }
    ) { paddingValues ->
        // 内容区域 - 重新设计的布局
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(CycleColors.BACKGROUND)
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            // 顶部状态概览卡片 - 简化版本，突出核心信息
            CompactCycleOverviewCard(
                selectedDayData = selectedDayData,
                cycleLength = averageCycleLength,
                cycles = cycles,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 月历视图 - 恢复日历组件，这是生理周期app的核心
            BloomCalendarView(
                selectedMonth = selectedMonth,
                onPreviousMonth = { viewModel.updateSelectedMonth(selectedMonth.minusMonths(1)) },
                onNextMonth = { viewModel.updateSelectedMonth(selectedMonth.plusMonths(1)) },
                cycleData = cycleData.filter { cycleDay ->
                    cycleDay.date.month == selectedMonth.month &&
                    cycleDay.date.year == selectedMonth.year
                },
                selectedDate = selectedDate,
                onDateSelected = { date ->
                    viewModel.updateSelectedDate(date)
                },
                onDateLongPressed = { date ->
                    longPressedDate = date
                    editDialogDate = date
                    showDateEditDialog = true
                }
            )

            // 快速操作区域 - 参照知名app的设计
            QuickActionsSection(
                onRecordPeriod = recordPeriodStartForSelectedDate,
                onRecordSymptoms = {
                    navController.navigate(AppDestinations.SYMPTOMS_DETAIL_ROUTE)
                },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 今日洞察卡片 - 合并多个卡片的信息
            TodayInsightCard(
                selectedDayData = selectedDayData,
                predictionResult = predictionResult,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 历史记录快速访问卡片
            HistoryQuickAccessCard(
                viewModel = viewModel,
                onNavigateToHistory = {
                    // 🔧 修复：启用导航到历史记录页面
                    navController.navigate("period_history")
                },
                onNavigateToAnalytics = {
                    // 🔧 融合：使用统一的统计页面路由
                    navController.navigate(com.timeflow.app.navigation.AppDestinations.MENSTRUAL_CYCLE_STATS_ROUTE)
                },
                onAddHistoricalRecord = {
                    // 显示添加历史记录对话框
                    showAddPeriodDialog = true
                },
                onEditRecord = { cycle ->
                    // 显示编辑历史记录对话框
                    editingCycle = cycle
                    showEditDialog = true
                },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 症状快速记录 - 改进的症状记录界面
            EnhancedSymptomWidget(
                onNavigateToSymptomDetail = {
                    navController.navigate(AppDestinations.SYMPTOMS_DETAIL_ROUTE)
                },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // 底部间距
            Spacer(modifier = Modifier.height(16.dp))
        }
    }

    // 添加历史记录对话框
    if (showAddPeriodDialog) {
        AddPeriodDialog(
            onDismiss = { showAddPeriodDialog = false },
            onConfirm = { startDate, endDate, notes ->
                viewModel.addHistoricalPeriod(
                    startDate = startDate,
                    endDate = endDate,
                    notes = notes
                ) { result ->
                    // 处理操作结果
                    when (result) {
                        is OperationResult.Success -> {
                            Toast.makeText(context, "历史记录添加成功", Toast.LENGTH_SHORT).show()
                        }
                        is OperationResult.Warning -> {
                            Toast.makeText(context, "添加成功，但有警告：${result.message}", Toast.LENGTH_LONG).show()
                        }
                        is OperationResult.Error -> {
                            Toast.makeText(context, "添加失败：${result.message}", Toast.LENGTH_SHORT).show()
                        }
                        is OperationResult.Conflict -> {
                            Toast.makeText(context, "日期冲突，请检查输入", Toast.LENGTH_SHORT).show()
                        }
                        is OperationResult.ConflictResolved -> {
                            Toast.makeText(context, "添加成功，${result.message}", Toast.LENGTH_LONG).show()
                        }
                    }
                }
                showAddPeriodDialog = false
            }
        )
    }

    // 编辑历史记录对话框
    if (showEditDialog && editingCycle != null) {
        EditPeriodDialog(
            cycle = editingCycle!!,
            onDismiss = {
                showEditDialog = false
                editingCycle = null
            },
            onConfirm = { startDate, endDate, notes ->
                viewModel.editPeriodRecord(
                    cycleId = editingCycle!!.id,
                    startDate = startDate,
                    endDate = endDate,
                    notes = notes,
                    onResult = { result ->
                        // 处理操作结果
                        when (result) {
                            is OperationResult.Success -> {
                                Toast.makeText(context, "记录更新成功", Toast.LENGTH_SHORT).show()
                                // 🔧 修复：更新成功后刷新数据
                                viewModel.refreshData()
                                // 🔧 修复：成功时关闭对话框
                                showEditDialog = false
                                editingCycle = null
                            }
                            is OperationResult.Warning -> {
                                Toast.makeText(context, "更新成功，但有警告：${result.message}", Toast.LENGTH_LONG).show()
                                // 🔧 修复：即使有警告也要刷新数据
                                viewModel.refreshData()
                                // 🔧 修复：警告时也关闭对话框
                                showEditDialog = false
                                editingCycle = null
                            }
                            is OperationResult.ConflictResolved -> {
                                Toast.makeText(context, "记录更新成功，已自动解决日期冲突", Toast.LENGTH_LONG).show()
                                // 🔧 修复：冲突解决后刷新数据
                                viewModel.refreshData()
                                // 🔧 修复：成功时关闭对话框
                                showEditDialog = false
                                editingCycle = null
                            }
                            is OperationResult.Error -> {
                                Toast.makeText(context, "更新失败：${result.message}", Toast.LENGTH_SHORT).show()
                                // 🔧 修复：错误时不关闭对话框，让用户可以重试
                            }
                            is OperationResult.Conflict -> {
                                Toast.makeText(context, "日期冲突，请检查输入", Toast.LENGTH_SHORT).show()
                                // 🔧 修复：冲突时不关闭对话框，让用户可以修改
                            }
                        }
                    }
                )
                // 🔧 修复：移除这里的对话框关闭逻辑，改为在成功回调中处理
                // showEditDialog = false
                // editingCycle = null
            }
        )
    }

    // 日期编辑对话框（长按日历日期时显示）
    if (showDateEditDialog && editDialogDate != null) {
        DateEditDialog(
            date = editDialogDate!!,
            viewModel = viewModel,
            onDismiss = {
                showDateEditDialog = false
                editDialogDate = null
            }
        )
    }
}

/**
 * 首次使用引导对话框
 */
@Composable
fun TutorialDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "欢迎使用BloomCycle",
                fontWeight = FontWeight.Bold,
                color = CycleColors.PERIOD_TEXT
            )
        },
        text = {
            Column {
                Text(
                    text = "BloomCycle帮助您追踪生理周期，记录症状，预测下一次月经和排卵期。",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "使用指南:",
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 使用指南列表
                listOf(
                    "长按日期记录经期开始/结束",
                    "在日历上查看周期预测",
                    "记录症状以获得更准确的预测"
                ).forEach { instruction ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(vertical = 4.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = instruction,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "开始使用",
                    color = CycleColors.PERIOD_TEXT,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    )
}

/**
 * 冲突解决对话框
 */
@Composable
fun ConflictResolutionDialog(
    options: List<String>,
    onOptionSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "数据冲突",
                fontWeight = FontWeight.Bold,
                color = CycleColors.PERIOD_TEXT
            )
        },
        text = {
            Column {
                Text(
                    text = "检测到本地数据与云端数据存在冲突，请选择解决方案:",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 选项列表
                options.forEachIndexed { index, option ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onOptionSelected(index) }
                            .padding(vertical = 12.dp)
                    ) {
                        RadioButton(
                            selected = false,
                            onClick = { onOptionSelected(index) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = CycleColors.PERIOD_TEXT
                            )
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = option,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "取消",
                    color = CycleColors.PERIOD_TEXT
                )
            }
        }
    )
}

/**
 * 周期日详情浮层
 * 增强版本：支持四个主要周期阶段的详细信息
 */
@Composable
fun CycleDayDetailOverlay(
    cycleDay: CycleDay,
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 日期标题
                Text(
                    text = cycleDay.date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.getTextColorForType(cycleDay.type)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 周期类型
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = CycleColors.getColorForType(cycleDay.type),
                                shape = CircleShape
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = when(cycleDay.type) {
                            CycleType.PERIOD -> "月经期"
                            CycleType.FOLLICULAR -> "卵泡期"
                            CycleType.OVULATION -> "排卵期"
                            CycleType.LUTEAL -> "黄体期"
                            CycleType.FERTILE -> "易孕期"
                            CycleType.PREDICTED -> "预测月经期"
                            CycleType.PREDICTED_PERIOD -> "预测月经期"
                            CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期"
                            CycleType.PREDICTED_OVULATION -> "预测排卵期"
                            CycleType.PREDICTED_LUTEAL -> "预测黄体期"
                            CycleType.PREDICTED_FERTILE -> "预测易孕期"
                            CycleType.NONE -> "未分类日期"
                        },
                        fontSize = 14.sp,
                        color = Color.DarkGray
                    )
                }
                
                if (cycleDay.dayOfCycle > 0 || cycleDay.type == CycleType.PERIOD) {
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 确保月经第一天显示为"周期第1天"
                    val displayDayOfCycle = if (cycleDay.dayOfCycle == 0) 1 else cycleDay.dayOfCycle
                    
                    Text(
                        text = "周期第${displayDayOfCycle}天",
                        fontSize = 14.sp,
                        color = Color.DarkGray
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 阶段描述
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = CycleColors.getColorForType(cycleDay.type).copy(alpha = 0.1f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = CycleColors.getBorderForType(cycleDay.type).copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Text(
                        text = getCyclePhaseBriefDescription(cycleDay.type),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = CycleColors.getTextColorForType(cycleDay.type)
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 详细信息
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFFF9F9F9),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Text(
                        text = getCyclePhaseDetailedInfo(cycleDay.type),
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = Color.DarkGray
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.End),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = CycleColors.getBorderForType(cycleDay.type)
                    )
                ) {
                    Text("关闭")
                }
            }
        }
    }
}

/**
 * 获取周期阶段简要描述
 */
fun getCyclePhaseBriefDescription(type: CycleType): String {
    return when(type) {
        CycleType.PERIOD -> "月经期是经血从子宫内膜脱落并从阴道排出的时期。"
        CycleType.FOLLICULAR -> "卵泡期是卵泡在卵巢中生长发育的时期，雌激素水平开始上升。"
        CycleType.OVULATION -> "排卵期是成熟卵子从卵巢释放的时期，此时最易受孕。"
        CycleType.LUTEAL -> "黄体期是排卵后卵泡转变为黄体的时期，分泌孕激素为受精卵着床做准备。"
        CycleType.FERTILE -> "排卵前后是受孕几率最高的时期，包括排卵日及其前5天。"
        CycleType.PREDICTED -> "预测月经期是基于您的历史数据算法预测的下次月经开始时间。"
        CycleType.PREDICTED_PERIOD -> "预测月经期是基于您的历史数据算法预测的下次月经开始时间。"
        CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期是基于您历史数据预测的卵泡发育阶段。"
        CycleType.PREDICTED_OVULATION -> "预测排卵期是基于您历史数据预测的排卵时间，通常在周期中段。"
        CycleType.PREDICTED_LUTEAL -> "预测黄体期是基于您历史数据预测的排卵后阶段。"
        CycleType.PREDICTED_FERTILE -> "排卵前后是基于您历史数据预测的受孕几率较高时期。"

        CycleType.NONE -> "这是月经周期中的一般日期。"
    }
}

/**
 * 获取周期阶段详细信息
 */
fun getCyclePhaseDetailedInfo(type: CycleType): String {
    return when(type) {
        CycleType.PERIOD -> "月经期建议:\n• 保持充分休息和睡眠\n• 多喝温水补充流失液体\n• 避免剧烈运动和寒冷刺激\n• 如有剧烈疼痛，可咨询医生使用止痛药\n• 保持会阴部清洁，勤换卫生用品"
        CycleType.FOLLICULAR -> "卵泡期特点:\n• 雌激素水平开始上升\n• 子宫内膜开始重建\n• 情绪通常较为积极\n• 能量水平逐渐提高\n• 适合开始运动和活动计划"
        CycleType.OVULATION -> "排卵期提示:\n• 这是受孕几率最高的时期\n• 可能会感觉到轻微腹痛(排卵痛)\n• 宫颈粘液变得更加透明和有弹性\n• 基础体温会略有上升\n• 性欲可能会增强"
        CycleType.LUTEAL -> "黄体期变化:\n• 孕激素水平上升\n• 可能出现轻微乳房胀痛\n• 情绪可能波动\n• 能量水平逐渐下降\n• 如果卵子未受精，黄体会萎缩，激素水平下降，为下次月经做准备"
        CycleType.FERTILE -> "排卵前后注意事项:\n• 这是受孕几率最高的时期\n• 如希望避孕，应采取额外的避孕措施\n• 如希望怀孕，这是最佳时机\n• 排卵前性交受孕几率更高\n• 宫颈粘液变化可帮助判断排卵时间"
        CycleType.PREDICTED -> "预测期提示:\n• 这是基于您历史周期数据的预测结果\n• 可能会有1-2天的误差\n• 提前做好经期准备\n• 如实际月经未按预期到来，可能需要更新记录或咨询医生"
        CycleType.PREDICTED_PERIOD -> "预测月经期提示:\n• 这是基于您历史数据预测的月经期\n• 可能会有1-2天的误差\n• 提前准备卫生用品\n• 如有必要，可准备止痛药物\n• 请记得及时记录实际经期开始时间"
        CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期提示:\n• 这是基于您历史数据预测的卵泡发育期\n• 雌激素水平可能开始上升\n• 此阶段通常精力较为充沛\n• 适合开始新的运动或项目\n• 实际时间可能与预测有所偏差"
        CycleType.PREDICTED_OVULATION -> "预测排卵期提示:\n• 这是基于您历史数据预测的排卵日期\n• 如计划怀孕，这是较佳时机\n• 如避孕，应注意采取额外措施\n• 可能会有轻微腹部不适\n• 请关注身体其他排卵信号"
        CycleType.PREDICTED_LUTEAL -> "预测黄体期提示:\n• 这是基于您历史数据预测的黄体期\n• 孕激素水平可能上升\n• 可能会出现情绪波动或轻微不适\n• 注意休息，避免过度疲劳\n• 关注身体变化，保持记录"
        CycleType.PREDICTED_FERTILE -> "预测排卵前后提示:\n• 这是基于您历史数据预测的生育力较高时期\n• 如希望避孕，请采取可靠措施\n• 如计划怀孕，可增加性生活频率\n• 关注身体变化和宫颈粘液\n• 实际时间可能与预测有所偏差"

        CycleType.NONE -> "日常健康建议:\n• 保持均衡饮食\n• 规律作息和睡眠\n• 适量运动\n• 注意压力管理\n• 定期进行妇科检查"
    }
}

/**
 * BloomCalendarView - 美观的日历视图组件
 */
@Composable
fun BloomCalendarView(
    selectedMonth: YearMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    cycleData: List<CycleDay>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDateLongPressed: (LocalDate) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 月份导航
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 月份标题
                Text(
                    text = selectedMonth.format(DateTimeFormatter.ofPattern("yyyy年M月")),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.PERIOD_TEXT,
                    modifier = Modifier.padding(horizontal = 8.dp)
                )

                // 月份导航按钮
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 上个月按钮
                    IconButton(
                        onClick = onPreviousMonth,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowLeft,
                            contentDescription = "上个月",
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(20.dp)
                        )
                    }

                    // 下个月按钮
                    IconButton(
                        onClick = onNextMonth,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.KeyboardArrowRight,
                            contentDescription = "下个月",
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 星期标题
            Row(modifier = Modifier.fillMaxWidth()) {
                listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = day,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = if (day == "日" || day == "六") CycleColors.PERIOD_TEXT else Color.DarkGray,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 日期网格
            val firstDayOfMonth = selectedMonth.atDay(1)
            val daysInMonth = selectedMonth.lengthOfMonth()
            val startDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7
            val totalDays = startDayOfWeek + daysInMonth
            val totalWeeks = (totalDays + 6) / 7

            // 按周显示日期
            for (week in 0 until totalWeeks) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    for (day in 0 until 7) {
                        val dayNumber = week * 7 + day - startDayOfWeek + 1

                        Box(
                            modifier = Modifier.weight(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            if (dayNumber in 1..daysInMonth) {
                                val date = firstDayOfMonth.plusDays((dayNumber - 1).toLong())
                                val cycleDay = cycleData.find { it.date == date } ?:
                                    CycleDay(
                                        date = date,
                                        type = CycleType.NONE,
                                        isToday = date == LocalDate.now(),
                                        isSelected = date == selectedDate,
                                        dayOfCycle = 0
                                    )

                                BloomDayCell(
                                    cycleDay = cycleDay,
                                    isSelected = date == selectedDate,
                                    onDateSelected = onDateSelected,
                                    onDateLongPressed = onDateLongPressed
                                )
                            } else {
                                EmptyDayCell(dayNumber)
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 图例
            CalendarLegend()
        }
    }
}

/**
 * 空日期单元格
 */
@Composable
fun EmptyDayCell(dayNumber: Int) {
    if (dayNumber <= 0) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .padding(2.dp)
        )
        return
    }

    Box(
        modifier = Modifier
            .size(40.dp)
            .padding(2.dp)
            .clip(CircleShape)
            .background(Color(0xFFF9F9F9))
    ) {
        Text(
            text = dayNumber.toString(),
            fontSize = 12.sp,
            color = Color.LightGray,
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 增强的日期单元格 - 改进交互体验和视觉效果
 */
@Composable
fun BloomDayCell(
    cycleDay: CycleDay,
    isSelected: Boolean,
    onDateSelected: (LocalDate) -> Unit,
    onDateLongPressed: (LocalDate) -> Unit
) {
    val backgroundColor = CycleColors.getColorForType(cycleDay.type)
    val textColor = CycleColors.getTextColorForType(cycleDay.type)
    val borderColor = CycleColors.getBorderForType(cycleDay.type)

    val hapticFeedback = LocalHapticFeedback.current

    // 添加选中动画效果
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
    )

    // 今日特殊样式
    val isToday = cycleDay.isToday
    val todayRingColor = if (isToday) CycleColors.PERIOD_TEXT else Color.Transparent

    Box(
        modifier = Modifier
            .size(44.dp) // 稍微增大尺寸提升触摸体验
            .padding(2.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(backgroundColor)
            .border(
                width = when {
                    isSelected -> 3.dp
                    isToday -> 2.dp
                    cycleDay.type != CycleType.NONE -> 1.dp
                    else -> 0.dp
                },
                color = when {
                    isSelected -> CycleColors.PERIOD_TEXT
                    isToday -> todayRingColor
                    else -> borderColor
                },
                shape = CircleShape
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 移除默认的点击效果，使用自定义动画
            ) {
                onDateSelected(cycleDay.date)
            }
            .pointerInput(cycleDay.date) {
                detectTapGestures(
                    onLongPress = {
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        onDateLongPressed(cycleDay.date)
                    }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        // 🔧 修复：主要数字始终居中显示
        Text(
            text = cycleDay.date.dayOfMonth.toString(),
            fontSize = if (isToday) 16.sp else 14.sp,
            fontWeight = if (isToday) FontWeight.Bold else FontWeight.Medium,
            color = textColor,
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.Center)
        )

        // 🔧 修复：只显示排卵期标记，移除D1-Dn显示
        when {
            cycleDay.type == CycleType.OVULATION -> {
                Box(
                    modifier = Modifier
                        .size(4.dp)
                        .background(textColor, CircleShape)
                        .align(Alignment.BottomEnd)
                        .padding(end = 2.dp, bottom = 2.dp)
                )
            }
            CycleColors.isPredictedType(cycleDay.type) -> {
                Text(
                    text = "?",
                    fontSize = 7.sp,
                    color = textColor.copy(alpha = 0.6f),
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 2.dp, top = 2.dp)
                )
            }
        }

        // 今日指示器
        if (isToday && !isSelected) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(
                        color = CycleColors.PERIOD_TEXT,
                        shape = CircleShape
                    )
                    .align(Alignment.BottomCenter)
                    .offset(y = (-2).dp)
            )
        }
    }
}

/**
 * 改进的日历图例 - 只显示经期和预测经期
 */
@Composable
fun CalendarLegend() {
    // 🔧 修改：只显示经期相关的图例
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp, horizontal = 4.dp),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 经期图例
        CompactLegendItem(
            color = CycleColors.PERIOD,
            text = "经期"
        )

        // 排卵期图例
        CompactLegendItem(
            color = CycleColors.OVULATION,
            text = "排卵期"
        )

        // 易孕期图例
        CompactLegendItem(
            color = CycleColors.FERTILE,
            text = "易孕期"
        )

        // 预测经期图例
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .border(
                        width = 1.dp,
                        color = Color(0xFFE91E63).copy(alpha = 0.8f),
                        shape = CircleShape
                    )
            )
            Text(
                text = "预测经期",
                fontSize = 9.sp,
                color = Color(0xFFE91E63).copy(alpha = 0.8f)
            )
        }
    }
}

/**
 * 紧凑图例项
 */
@Composable
fun CompactLegendItem(
    color: Color,
    text: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    color = color,
                    shape = CircleShape
                )
        )

        Text(
            text = text,
            fontSize = 9.sp,
            color = Color.Gray
        )
    }
}

/**
 * 图例项
 */
@Composable
fun LegendItem(
    color: Color,
    backgroundColor: Color,
    text: String,
    useDashedBorder: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(
                    color = backgroundColor,
                    shape = CircleShape
                )
                .let { modifier ->
                    if (useDashedBorder) {
                        modifier.border(
                            width = 1.dp,
                            color = color,
                            shape = CircleShape
                        )
                    } else modifier
                }
        )

        Text(
            text = text,
            fontSize = 10.sp,
            color = Color.DarkGray
        )
    }
}

/**
 * 紧凑的周期概览卡片 - 参照知名app设计
 */
@Composable
fun CompactCycleOverviewCard(
    selectedDayData: CycleDay,
    cycleLength: Int,
    cycles: List<CycleRecord>,
    modifier: Modifier = Modifier
) {
    val currentCycle = cycles.find { cycle ->
        cycle.startDate <= selectedDayData.date &&
        (cycle.endDate == null || cycle.endDate!! >= selectedDayData.date)
    }

    val daysUntilNextPeriod = calculateDaysUntilNextPeriod(selectedDayData, cycleLength)
    val phaseText = getPhaseText(selectedDayData.type)
    val phaseColor = CycleColors.getTextColorForType(selectedDayData.type)

    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 主要状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = phaseText,
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = phaseColor
                    )

                    Text(
                        text = when {
                            selectedDayData.type == CycleType.PERIOD -> "第${selectedDayData.dayOfCycle}天"
                            daysUntilNextPeriod > 0 -> "${daysUntilNextPeriod}天后来经"
                            else -> "周期第${selectedDayData.dayOfCycle}天"
                        },
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }

                // 周期进度圆环
                CycleProgressRing(
                    currentDay = selectedDayData.dayOfCycle,
                    totalDays = cycleLength,
                    phaseColor = phaseColor
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 快速信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                QuickInfoItem(
                    label = "周期长度",
                    value = "${cycleLength}天",
                    icon = Icons.Default.DateRange
                )

                QuickInfoItem(
                    label = "平均经期",
                    value = "5天", // 可以从数据中计算
                    icon = Icons.Default.Favorite
                )

                QuickInfoItem(
                    label = "规律性",
                    value = "正常",
                    icon = Icons.Default.CheckCircle
                )
            }
        }
    }
}

/**
 * 周期进度圆环
 */
@Composable
fun CycleProgressRing(
    currentDay: Int,
    totalDays: Int,
    phaseColor: Color,
    modifier: Modifier = Modifier
) {
    val progress = if (totalDays > 0) currentDay.toFloat() / totalDays else 0f

    Box(
        modifier = modifier.size(60.dp),
        contentAlignment = Alignment.Center
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val strokeWidth = 6.dp.toPx()
            val radius = (size.minDimension - strokeWidth) / 2
            val center = Offset(size.width / 2, size.height / 2)

            // 背景圆环
            drawCircle(
                color = Color.LightGray.copy(alpha = 0.3f),
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidth)
            )

            // 进度圆环
            drawArc(
                color = phaseColor,
                startAngle = -90f,
                sweepAngle = 360f * progress,
                useCenter = false,
                style = Stroke(width = strokeWidth),
                topLeft = Offset(
                    center.x - radius,
                    center.y - radius
                ),
                size = Size(radius * 2, radius * 2)
            )
        }

        Text(
            text = currentDay.toString(),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = phaseColor
        )
    }
}

/**
 * 快速信息项
 */
@Composable
fun QuickInfoItem(
    label: String,
    value: String,
    icon: ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = Color.Gray,
            modifier = Modifier.size(20.dp)
        )

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.DarkGray
        )

        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
    }
}

/**
 * 快速操作区域 - 参照知名app的快速操作设计
 */
@Composable
fun QuickActionsSection(
    onRecordPeriod: () -> Unit,
    onRecordSymptoms: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "快速记录",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 记录经期按钮
                QuickActionButton(
                    text = "记录经期",
                    icon = Icons.Default.Favorite,
                    backgroundColor = Color(0xFFFFEBEE),
                    iconColor = Color(0xFFE91E63),
                    onClick = onRecordPeriod,
                    modifier = Modifier.weight(1f)
                )

                // 记录症状按钮
                QuickActionButton(
                    text = "记录症状",
                    icon = Icons.Default.Add,
                    backgroundColor = Color(0xFFE3F2FD),
                    iconColor = Color(0xFF2196F3),
                    onClick = onRecordSymptoms,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 快速操作按钮
 */
@Composable
fun QuickActionButton(
    text: String,
    icon: ImageVector,
    backgroundColor: Color,
    iconColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = iconColor,
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = text,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 个性化洞察卡片 - 参照Flo等知名app的个性化内容
 */
@Composable
fun TodayInsightCard(
    selectedDayData: CycleDay,
    predictionResult: PeriodPredictionAlgorithm.PredictionResult?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "个性化洞察",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            // 主要洞察
            val mainInsight = getPersonalizedInsight(selectedDayData, predictionResult)
            InsightItem(
                icon = mainInsight.icon,
                title = mainInsight.title,
                description = mainInsight.description,
                color = mainInsight.color
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 健康建议
            val healthTips = getHealthTips(selectedDayData.type)
            if (healthTips.isNotEmpty()) {
                Text(
                    text = "健康建议",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                healthTips.take(2).forEach { tip ->
                    HealthTipItem(tip = tip)
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }

            // 预测信息
            predictionResult?.let { prediction ->
                Spacer(modifier = Modifier.height(12.dp))
                PredictionInsightItem(prediction = prediction)
            }
        }
    }
}

/**
 * 洞察项
 */
@Composable
fun InsightItem(
    icon: ImageVector,
    title: String,
    description: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )

        Column {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = color
            )

            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray,
                lineHeight = 20.sp
            )
        }
    }
}

/**
 * 增强的症状记录组件 - 参照知名app设计
 */
@Composable
fun EnhancedSymptomWidget(
    onNavigateToSymptomDetail: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "症状记录",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.DarkGray
                )

                TextButton(
                    onClick = onNavigateToSymptomDetail
                ) {
                    Text(
                        text = "查看全部",
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 快速症状记录网格
            LazyVerticalGrid(
                columns = GridCells.Fixed(4),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.height(120.dp)
            ) {
                items(quickSymptoms) { symptom ->
                    QuickSymptomItem(
                        symptom = symptom,
                        onClick = {
                            // 这里可以添加快速记录逻辑
                            onNavigateToSymptomDetail()
                        }
                    )
                }
            }
        }
    }
}

/**
 * 快速症状项
 */
@Composable
fun QuickSymptomItem(
    symptom: QuickSymptom,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(symptom.backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = symptom.emoji,
                fontSize = 20.sp
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = symptom.name,
            fontSize = 10.sp,
            color = Color.DarkGray,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

/**
 * 快速症状数据类
 */
data class QuickSymptom(
    val name: String,
    val emoji: String,
    val backgroundColor: Color
)

// 预定义的快速症状列表
private val quickSymptoms = listOf(
    QuickSymptom("流量", "🩸", Color(0xFFFFEBEE)),
    QuickSymptom("痛感", "😣", Color(0xFFE3F2FD)),
    QuickSymptom("情绪", "😊", Color(0xFFFFF8E1)),
    QuickSymptom("睡眠", "😴", Color(0xFFE8F5E9)),
    QuickSymptom("食欲", "🍎", Color(0xFFF3E5F5)),
    QuickSymptom("运动", "🏃", Color(0xFFE0F2F1)),
    QuickSymptom("皮肤", "✨", Color(0xFFFCE4EC)),
    QuickSymptom("其他", "📝", Color(0xFFF5F5F5))
)

/**
 * 辅助函数 - 获取阶段文本
 */
private fun getPhaseText(type: CycleType): String {
    return when (type) {
        CycleType.PERIOD -> "月经期"
        CycleType.FOLLICULAR -> "卵泡期"
        CycleType.OVULATION -> "排卵期"
        CycleType.LUTEAL -> "黄体期"
        CycleType.FERTILE -> "易孕期"
        CycleType.PREDICTED_PERIOD -> "预测月经期"
        else -> "周期中"
    }
}

/**
 * 健康建议项
 */
@Composable
fun HealthTipItem(tip: String) {
    Row(
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(4.dp)
                .background(
                    color = Color(0xFF880E4F),
                    shape = CircleShape
                )
                .padding(top = 6.dp)
        )

        Text(
            text = tip,
            fontSize = 12.sp,
            color = Color.Gray,
            lineHeight = 16.sp
        )
    }
}

/**
 * 预测洞察项
 */
@Composable
fun PredictionInsightItem(prediction: PeriodPredictionAlgorithm.PredictionResult) {
    val nextPeriodDays = ChronoUnit.DAYS.between(LocalDate.now(), prediction.nextPeriodStart).toInt()

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = Icons.Default.TrendingUp,
            contentDescription = null,
            tint = Color(0xFF4CAF50),
            modifier = Modifier.size(16.dp)
        )

        Text(
            text = when {
                nextPeriodDays <= 0 -> "预计今天开始月经"
                nextPeriodDays == 1 -> "预计明天开始月经"
                nextPeriodDays <= 7 -> "预计${nextPeriodDays}天后开始月经"
                else -> "下次月经还有${nextPeriodDays}天"
            },
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

/**
 * 个性化洞察数据类
 */
data class PersonalizedInsight(
    val icon: ImageVector,
    val title: String,
    val description: String,
    val color: Color
)

/**
 * 获取个性化洞察
 */
private fun getPersonalizedInsight(
    selectedDayData: CycleDay,
    predictionResult: PeriodPredictionAlgorithm.PredictionResult?
): PersonalizedInsight {
    return when (selectedDayData.type) {
        CycleType.PERIOD -> PersonalizedInsight(
            icon = Icons.Default.Favorite,
            title = "月经期 - 第${selectedDayData.dayOfCycle}天",
            description = "身体正在自然更新，这是休息和自我关爱的时间",
            color = CycleColors.PERIOD_TEXT
        )
        CycleType.FOLLICULAR -> PersonalizedInsight(
            icon = Icons.Default.WbSunny,
            title = "卵泡期 - 新的开始",
            description = "雌激素水平上升，精力充沛，是开始新项目的好时机",
            color = CycleColors.FOLLICULAR_TEXT
        )
        CycleType.OVULATION -> PersonalizedInsight(
            icon = Icons.Default.Star,
            title = "排卵期 - 生育力巅峰",
            description = "受孕几率最高，体温略升，可能感到更有魅力",
            color = CycleColors.OVULATION_TEXT
        )
        CycleType.LUTEAL -> PersonalizedInsight(
            icon = Icons.Default.SelfImprovement,
            title = "黄体期 - 内省时光",
            description = "孕激素影响情绪，适合反思和规划，注意情绪管理",
            color = CycleColors.LUTEAL_TEXT
        )
        CycleType.FERTILE -> PersonalizedInsight(
            icon = Icons.Default.FavoriteBorder,
            title = "易孕期 - 生育窗口",
            description = "受孕几率较高，如有计划请注意避孕或备孕",
            color = CycleColors.FERTILE_TEXT
        )
        else -> PersonalizedInsight(
            icon = Icons.Default.Info,
            title = "周期追踪中",
            description = "继续记录症状和周期，帮助我们提供更准确的预测",
            color = Color.Gray
        )
    }
}

/**
 * 获取基于医学证据的健康建议
 * 参考：ACOG指南、WHO生殖健康建议、《Williams妇科学》
 */
private fun getHealthTips(cycleType: CycleType): List<String> {
    return when (cycleType) {
        CycleType.PERIOD -> listOf(
            "补充铁质：每日15-18mg，预防缺铁性贫血(推荐：瘦肉、菠菜、豆类)",
            "保持水分：每日2-3L温水，促进血液循环，缓解痛经",
            "适度运动：轻度有氧运动可促进内啡肽分泌，缓解疼痛",
            "热敷腹部：40-45°C热敷15-20分钟，缓解子宫收缩疼痛",
            "避免剧烈运动：防止经血逆流，降低子宫内膜异位症风险"
        )
        CycleType.FOLLICULAR -> listOf(
            "优质蛋白：每日1.2g/kg体重，支持卵泡发育(推荐：鱼类、蛋类、豆制品)",
            "维生素E：每日15mg，抗氧化保护卵子质量(推荐：坚果、植物油)",
            "叶酸补充：每日400μg，为可能的受孕做准备",
            "有氧运动：每周150分钟中等强度，改善卵巢血液循环",
            "充足睡眠：7-9小时，维持正常的激素分泌节律"
        )
        CycleType.OVULATION -> listOf(
            "监测排卵：基础体温升高0.3-0.5°C提示排卵",
            "宫颈粘液观察：透明拉丝状粘液提示排卵期",
            "备孕最佳时机：排卵前2天至排卵后1天受孕率最高",
            "抗氧化食物：维生素C、E保护卵子免受自由基损伤",
            "避免阴道冲洗：保持阴道正常pH值，利于精子存活"
        )
        CycleType.LUTEAL -> listOf(
            "镁元素补充：每日300-400mg，缓解PMS症状(推荐：深绿叶蔬菜、坚果)",
            "维生素B6：每日50-100mg，调节神经递质，改善情绪",
            "限制咖啡因：每日<200mg，减少焦虑和乳房胀痛",
            "复合碳水化合物：稳定血糖，减少情绪波动",
            "规律作息：保持褪黑素正常分泌，维持激素平衡"
        )
        CycleType.FERTILE -> listOf(
            "叶酸补充：每日400-800μg，预防神经管缺陷",
            "避免有害物质：戒烟酒，远离重金属和化学污染",
            "适宜体重：BMI 18.5-24.9，过重或过轻都影响生育力",
            "压力管理：慢性压力影响下丘脑-垂体-卵巢轴功能",
            "规律性生活：隔日一次可保证精子质量和数量"
        )
        else -> listOf(
            "均衡营养：遵循地中海饮食模式，富含Omega-3脂肪酸",
            "规律运动：每周150分钟中等强度运动，维持健康体重",
            "充足睡眠：7-9小时优质睡眠，维持内分泌平衡",
            "压力管理：瑜伽、冥想等有助于调节皮质醇水平",
            "定期检查：每年妇科检查，及早发现潜在问题"
        )
    }
}

/**
 * 历史记录快速访问卡片
 */
@Composable
fun HistoryQuickAccessCard(
    viewModel: MenstrualCycleViewModel,
    onNavigateToHistory: () -> Unit,
    onNavigateToAnalytics: () -> Unit,
    onAddHistoricalRecord: () -> Unit,
    onEditRecord: (CycleRecord) -> Unit,
    modifier: Modifier = Modifier
) {
    val historicalPeriods by viewModel.getHistoricalPeriods().collectAsState(initial = emptyList())
    val statistics by viewModel.getPeriodStatistics().collectAsState(initial = PeriodStatistics())

    Card(
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "历史记录",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.DarkGray
                )

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 添加历史记录按钮
                    IconButton(
                        onClick = onAddHistoricalRecord,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加历史记录",
                            tint = Color(0xFF4CAF50),
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    // 数据分析按钮
                    IconButton(
                        onClick = onNavigateToAnalytics,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Analytics,
                            contentDescription = "数据分析",
                            tint = Color(0xFF2196F3),
                            modifier = Modifier.size(18.dp)
                        )
                    }

                    // 查看全部按钮
                    IconButton(
                        onClick = onNavigateToHistory,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = "查看全部",
                            tint = Color(0xFF880E4F),
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            if (historicalPeriods.isNotEmpty()) {
                // 统计概览
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    HistoryStatItem(
                        label = "记录周期",
                        value = "${statistics.totalCycles}个",
                        icon = Icons.Default.EventNote,
                        color = Color(0xFF2196F3),
                        onClick = onNavigateToHistory
                    )

                    HistoryStatItem(
                        label = "平均周期",
                        value = "${statistics.averageCycleLength.toInt()}天",
                        icon = Icons.Default.DateRange,
                        color = Color(0xFF4CAF50),
                        onClick = onNavigateToAnalytics
                    )

                    HistoryStatItem(
                        label = "规律性",
                        value = getRegularityShortText(statistics.cycleRegularity),
                        icon = Icons.Default.CheckCircle,
                        color = getRegularityColor(statistics.cycleRegularity),
                        onClick = onNavigateToAnalytics
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 最近记录
                val recentCycles = historicalPeriods.sortedByDescending { it.startDate }.take(3)
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "最近记录",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Gray
                        )

                        Text(
                            text = "点击编辑",
                            fontSize = 11.sp,
                            color = Color(0xFF880E4F),
                            modifier = Modifier
                                .background(
                                    color = Color(0xFF880E4F).copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    recentCycles.forEach { cycle ->
                        RecentCycleItem(
                            cycle = cycle,
                            onEdit = onEditRecord
                        )
                    }
                }
            } else {
                // 空状态
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.EventNote,
                        contentDescription = null,
                        tint = Color.LightGray,
                        modifier = Modifier.size(32.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "暂无历史记录",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )

                    Text(
                        text = "开始记录您的经期历史",
                        fontSize = 12.sp,
                        color = Color.LightGray
                    )
                }
            }
        }
    }
}

/**
 * 历史统计项
 */
@Composable
fun HistoryStatItem(
    label: String,
    value: String,
    icon: ImageVector,
    color: Color,
    onClick: (() -> Unit)? = null
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(enabled = onClick != null) {
                onClick?.invoke()
            }
            .padding(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(16.dp)
        )

        Spacer(modifier = Modifier.height(2.dp))

        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )

        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
    }
}

/**
 * 最近周期项
 */
@Composable
fun RecentCycleItem(
    cycle: CycleRecord,
    onEdit: (CycleRecord) -> Unit = {}
) {
    val dateFormatter = DateTimeFormatter.ofPattern("MM/dd")

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onEdit(cycle) }
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 8.dp, vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(
                        color = Color(0xFF880E4F),
                        shape = CircleShape
                    )
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = cycle.startDate.format(dateFormatter),
                fontSize = 12.sp,
                color = Color.DarkGray
            )

            cycle.endDate?.let { endDate ->
                Text(
                    text = " - ${endDate.format(dateFormatter)}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            cycle.periodLength?.let { length ->
                Text(
                    text = "${length}天",
                    fontSize = 11.sp,
                    color = Color.Gray,
                    modifier = Modifier
                        .background(
                            color = Color(0xFFF5F5F5),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }

            // 编辑图标
            Icon(
                imageVector = Icons.Default.Edit,
                contentDescription = "编辑记录",
                tint = Color(0xFF880E4F),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 获取规律性简短文本
 */
private fun getRegularityShortText(regularity: CycleRegularity): String {
    return when (regularity) {
        CycleRegularity.VERY_REGULAR -> "很规律"
        CycleRegularity.REGULAR -> "规律"
        CycleRegularity.SOMEWHAT_IRREGULAR -> "一般"
        CycleRegularity.IRREGULAR -> "不规律"
        CycleRegularity.INSUFFICIENT_DATA -> "数据少"
    }
}

/**
 * 获取规律性颜色
 */
private fun getRegularityColor(regularity: CycleRegularity): Color {
    return when (regularity) {
        CycleRegularity.VERY_REGULAR -> Color(0xFF4CAF50)
        CycleRegularity.REGULAR -> Color(0xFF8BC34A)
        CycleRegularity.SOMEWHAT_IRREGULAR -> Color(0xFFFF9800)
        CycleRegularity.IRREGULAR -> Color(0xFFE91E63)
        CycleRegularity.INSUFFICIENT_DATA -> Color.Gray
    }
}

/**
 * 辅助函数 - 计算距离下次月经的天数
 */
private fun calculateDaysUntilNextPeriod(selectedDayData: CycleDay, cycleLength: Int): Int {
    return if (selectedDayData.dayOfCycle > 0) {
        cycleLength - selectedDayData.dayOfCycle
    } else {
        0
    }
}

/**
 * 当前周期状态卡片 - 已被CompactCycleOverviewCard替代，保留以兼容性
 * @deprecated 使用CompactCycleOverviewCard替代
 */
@Composable
fun CurrentCycleStatusCard(
    selectedDayData: CycleDay,
    cycleLength: Int,
    onPeriodStart: () -> Unit = {},
    cycles: List<CycleRecord> = emptyList()
) {
    // 添加日志输出，帮助调试
    Log.d("CycleDebug", "选中日期: ${selectedDayData.date}, 类型: ${selectedDayData.type}, 周期天数: ${selectedDayData.dayOfCycle}")
    
    // 计算周期阶段文字 - 确保月经第一天显示为"第1天"而非"第0天"
    val displayDayOfCycle = if (selectedDayData.dayOfCycle == 0) 1 else selectedDayData.dayOfCycle
    
    // 确保文本正确显示当前选择的日期的周期天数
    val phaseText = when (selectedDayData.type) {
        CycleType.PERIOD -> "第${displayDayOfCycle}天 · 月经期"
        CycleType.FOLLICULAR -> "第${displayDayOfCycle}天 · 卵泡期"
        CycleType.OVULATION -> "第${displayDayOfCycle}天 · 排卵期"
        CycleType.LUTEAL -> "第${displayDayOfCycle}天 · 黄体期"
        CycleType.FERTILE -> "第${displayDayOfCycle}天 · 排卵前期"
        CycleType.PREDICTED -> "第${displayDayOfCycle}天 · 预测月经期"
        CycleType.PREDICTED_PERIOD -> "第${displayDayOfCycle}天 · 预测月经期"
        CycleType.PREDICTED_FOLLICULAR -> "第${displayDayOfCycle}天 · 预测卵泡期"
        CycleType.PREDICTED_OVULATION -> "第${displayDayOfCycle}天 · 预测排卵期"
        CycleType.PREDICTED_LUTEAL -> "第${displayDayOfCycle}天 · 预测黄体期"
        CycleType.PREDICTED_FERTILE -> "第${displayDayOfCycle}天 · 预测排卵前期"

        CycleType.NONE -> if (displayDayOfCycle < 14) "第${displayDayOfCycle}天 · 卵泡期" else "第${displayDayOfCycle}天 · 黄体期"
    }
    
    // 计算下次周期天数 - 使用调整后的周期天数
    val adjustedDayOfCycle = if (selectedDayData.dayOfCycle < 1) 1 else selectedDayData.dayOfCycle
    val daysUntilNextPeriod = cycleLength - adjustedDayOfCycle + 1
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部标题和图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "当前周期",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.PERIOD_TEXT
                    )
                    
                    // 使用更明确的文案，确保用户了解显示的是什么
                    val endText = when {
                        selectedDayData.type == CycleType.PERIOD -> " · 预测${daysUntilNextPeriod}天后结束" 
                        selectedDayData.type == CycleType.PREDICTED_PERIOD -> " · 预测周期"
                        else -> ""
                    }
                    
                    Text(
                        text = "$phaseText$endText",
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                // 显示记录按钮（如果当前不是经期状态）
                if (selectedDayData.type != CycleType.PERIOD) {
                    Button(
                        onClick = onPeriodStart,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = CycleColors.PERIOD
                        ),
                        modifier = Modifier.height(36.dp),
                        shape = RoundedCornerShape(18.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(14.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("记录经期", color = Color.White, fontSize = 12.sp)
                    }
                } else {
                    // 如果是经期状态，显示经期中的提示
                    Surface(
                        color = CycleColors.PERIOD.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(18.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Circle,
                                contentDescription = null,
                                tint = CycleColors.PERIOD,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = "经期中",
                                color = CycleColors.PERIOD,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 关键日期信息卡片
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 上次经期开始日期
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = CycleColors.PERIOD.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = null,
                                tint = CycleColors.PERIOD_TEXT,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "上次开始",
                                fontSize = 12.sp,
                                color = CycleColors.PERIOD_TEXT
                            )
                        }
                        Text(
                            text = cycles.lastOrNull()?.startDate?.format(DateTimeFormatter.ofPattern("M月d日")) ?: "无记录",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
                
                // 预测排卵日
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            color = CycleColors.OVULATION.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(8.dp)
                ) {
                    Column {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.FiberManualRecord,
                                contentDescription = null,
                                tint = CycleColors.OVULATION_TEXT,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "预测排卵日",
                                fontSize = 12.sp,
                                color = CycleColors.OVULATION_TEXT
                            )
                        }
                        Text(
                            text = cycles.lastOrNull()?.let { lastCycle ->
                                lastCycle.startDate.plusDays(14).format(DateTimeFormatter.ofPattern("M月d日"))
                            } ?: "无预测",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 医学提示信息
            if (selectedDayData.type == CycleType.FERTILE || selectedDayData.type == CycleType.PERIOD) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            width = 1.dp,
                            color = when {
                                selectedDayData.type == CycleType.PERIOD -> CycleColors.PERIOD_BORDER.copy(alpha = 0.3f)
                                selectedDayData.type == CycleType.FERTILE -> CycleColors.FERTILE_BORDER.copy(alpha = 0.3f)
                                else -> Color.LightGray.copy(alpha = 0.3f)
                            },
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = when {
                                selectedDayData.type == CycleType.PERIOD -> CycleColors.PERIOD_TEXT
                                selectedDayData.type == CycleType.FERTILE -> CycleColors.FERTILE_TEXT
                                else -> Color.Gray
                            },
                            modifier = Modifier.size(16.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = when {
                                selectedDayData.type == CycleType.PERIOD -> "月经期间注意保暖，避免过度疲劳和受凉，保持充足睡眠和液体摄入。"
                                selectedDayData.type == CycleType.FERTILE -> "排卵前期是受孕几率较高的阶段，卵泡发育成熟准备排卵。观察宫颈粘液变化可帮助判断排卵时间。"
                                else -> "月经周期的这一阶段是相对安全期，但也不是绝对安全的避孕时段。"
                            },
                            fontSize = 12.sp,
                            color = Color.DarkGray,
                            lineHeight = 16.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 顶部周期信息头部
 */
@Composable
fun CycleInfoHeader(
    selectedDayData: CycleDay,
    cycleLength: Int,
    floatOffset: Float
) {
    // 计算周期阶段文字 - 确保月经第一天显示为"第1天"
    val displayDayOfCycle = if (selectedDayData.dayOfCycle == 0) 1 else selectedDayData.dayOfCycle
    
    val phaseText = when {
        selectedDayData.type == CycleType.PERIOD -> "🌸 第${displayDayOfCycle}天 · 月经期"
        selectedDayData.type == CycleType.FERTILE -> "🌺 第${displayDayOfCycle}天 · 排卵前期"
        selectedDayData.type == CycleType.PREDICTED -> "🌷 第${displayDayOfCycle}天 · 预测月经期"
        else -> if (displayDayOfCycle < 14) "🌿 第${displayDayOfCycle}天 · 卵泡期" else "🌿 第${displayDayOfCycle}天 · 黄体期"
    }
    
    // 计算下次周期天数 - 使用调整后的周期天数
    val adjustedDayOfCycle = if (selectedDayData.dayOfCycle < 1) 1 else selectedDayData.dayOfCycle
    val daysUntilNextPeriod = cycleLength - adjustedDayOfCycle + 1
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 周期阶段进度指示器
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(32.dp)
                    .padding(bottom = 8.dp)
            ) {
                // 针织毛线进度条（渐变背景）
                // 使用调整后的进度值确保不为0
                val progress = adjustedDayOfCycle.toFloat() / cycleLength
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFFEEEEEE))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(progress)
                            .fillMaxHeight()
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFFF48FB1), // 粉色
                                        Color(0xFFFFAB91), // 橙色
                                        Color(0xFFFFF3E0)  // 米白色
                                    )
                                )
                            )
                    )
                    
                    // Get density in the composable context before drawing
                    val density = LocalDensity.current
                    val strokeWidthPx = with(density) { 1.dp.toPx() }
                    
                    // 针织毛线效果 - 使用Canvas代替CachedCanvas
                    Canvas(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 1.dp)
                    ) {
                        val width = size.width * progress
                        val height = size.height
                        
                        // 绘制针织线条效果
                        val pathEffect = PathEffect.dashPathEffect(floatArrayOf(3f, 3f), 0f)
                        for (y in 0..height.toInt() step 3) {
                            drawLine(
                                color = Color(0xFFEEEEEE),
                                start = Offset(0f, y.toFloat()),
                                end = Offset(width, y.toFloat()),
                                strokeWidth = strokeWidthPx,
                                pathEffect = pathEffect
                            )
                        }
                    }
                }
                
                // 当前周期文字
                Text(
                    text = phaseText,
                    color = Color(0xFF554A60),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.align(Alignment.TopEnd)
                )
            }
            
            // 预测提示
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
            ) {
                // 云朵雨伞图标（带浮动动画）
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .offset(y = floatOffset.dp)
                ) {
                    // 绘制云朵
                    Canvas(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        val cloudColor = CycleColors.CLOUD
                        val cloudShadowColor = Color(0xFF90CAF9)
                        
                        // 绘制主云朵
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.3f,
                            center = Offset(size.width * 0.3f, size.height * 0.5f)
                        )
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.25f,
                            center = Offset(size.width * 0.6f, size.height * 0.45f)
                        )
                        drawCircle(
                            color = cloudColor,
                            radius = size.width * 0.2f,
                            center = Offset(size.width * 0.75f, size.height * 0.55f)
                        )
                        
                        // 雨滴
                        val dropColor = CycleColors.DROP_DARK
                        drawCircle(
                            color = dropColor,
                            radius = size.width * 0.08f,
                            center = Offset(size.width * 0.3f, size.height * 0.8f)
                        )
                        drawCircle(
                            color = dropColor,
                            radius = size.width * 0.08f,
                            center = Offset(size.width * 0.6f, size.height * 0.85f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 预测文字
                Text(
                    text = "下次周期约在 $daysUntilNextPeriod 天后",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

/**
 * 月份选择器
 */
@Composable
fun MonthSelector(
    selectedMonth: YearMonth,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val formatter = DateTimeFormatter.ofPattern("yyyy年M月")
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowLeft,
                contentDescription = "上个月",
                tint = Color(0xFF554A60)
            )
        }
        
        Text(
            text = selectedMonth.format(formatter),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(horizontal = 20.dp)
        )
        
        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "下个月",
                tint = Color(0xFF554A60)
            )
        }
    }
}

/**
 * 周期视图
 */
@Composable
fun CycleView(
    selectedDayData: CycleDay,
    cycleLength: Int
) {
    val cycleRange = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("M月d日"))}-${selectedDayData.date.plusDays((cycleLength - selectedDayData.dayOfCycle).toLong()).format(DateTimeFormatter.ofPattern("M月d日"))}"
    
    // 添加density引用
    val density = LocalDensity.current
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = cycleRange,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 预先计算所有需要的px值
        val strokeWidthPx = with(density) { 30.dp.toPx() }
        val paddingPx = with(density) { 20.dp.toPx() }
        val dotRadius15Px = with(density) { 15.dp.toPx() }
        val dotRadius12Px = with(density) { 12.dp.toPx() }
        
        // 周期环形视图
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.size(280.dp)
        ) {
            // 环形背景
            Canvas(modifier = Modifier.fillMaxSize()) {
                // 使用预先计算的px值
                val circleRadius = size.minDimension / 2 - paddingPx
                
                // 绘制外圈灰色环
                drawCircle(
                    color = Color(0xFFEEEEEE),
                    radius = circleRadius,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 计算当前日期在周期中的角度
                val dayProgress = selectedDayData.dayOfCycle.toFloat() / cycleLength.toFloat()
                val sweepAngle = dayProgress * 360f
                
                // 绘制经期部分（红色，约占周期的前20%）
                drawArc(
                    color = Color(0xFFE91E63),
                    startAngle = -90f,
                    sweepAngle = 0.2f * 360f,
                    useCenter = false,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 绘制易孕期部分（蓝色，约占周期的中间30%）
                drawArc(
                    color = Color(0xFF2196F3),
                    startAngle = -90f + 0.5f * 360f,
                    sweepAngle = 0.3f * 360f,
                    useCenter = false,
                    style = Stroke(width = strokeWidthPx)
                )
                
                // 当前日期指示器
                val angle = -90f + dayProgress * 360f
                val x = center.x + cos(angle * PI.toFloat() / 180f) * circleRadius
                val y = center.y + sin(angle * PI.toFloat() / 180f) * circleRadius
                
                drawCircle(
                    color = Color.White,
                    radius = dotRadius15Px,
                    center = Offset(x, y)
                )
                
                drawCircle(
                    color = Color(0xFFE91E63),
                    radius = dotRadius12Px,
                    center = Offset(x, y)
                )
            }
            
            // 中心信息 - 移动到Canvas外部
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth(0.7f)
            ) {
                Text(
                    text = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("M月d日"))} ${getDayOfWeek(selectedDayData.date)}",
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
                
                val statusText = when (selectedDayData.type) {
                    CycleType.PERIOD -> "经期第 ${selectedDayData.dayOfCycle} 天"
                    CycleType.PREDICTED -> "预测经期第 ${selectedDayData.dayOfCycle - cycleLength} 天"
                    CycleType.FERTILE -> "易孕期"
                    else -> "周期第 ${selectedDayData.dayOfCycle} 天"
                }
                
                Text(
                    text = statusText,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF554A60),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Text(
                    text = "周期共 ${cycleLength} 天",
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

/**
 * 当日周期信息
 */
@Composable
fun CurrentDayInfo(
    selectedDayData: CycleDay
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 日期和星期
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .background(
                            color = Color(0xFFFCE4EC),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = selectedDayData.date.dayOfMonth.toString(),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFE91E63)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "${selectedDayData.date.format(DateTimeFormatter.ofPattern("yyyy年M月"))} ${getDayOfWeek(selectedDayData.date)}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF554A60)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 周期状态
            val statusText = when (selectedDayData.type) {
                CycleType.PERIOD -> "经期第 ${selectedDayData.dayOfCycle} 天"
                CycleType.PREDICTED -> "预测经期第 1 天"
                CycleType.FERTILE -> "易孕期"
                else -> "周期第 ${selectedDayData.dayOfCycle} 天"
            }
            
            val statusColor = when (selectedDayData.type) {
                CycleType.PERIOD -> Color(0xFFE91E63)
                CycleType.PREDICTED -> Color(0xFFE91E63).copy(alpha = 0.7f)
                CycleType.FERTILE -> Color(0xFF2196F3)
                else -> Color(0xFF554A60)
            }
            
            // 状态图标和文本
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                when (selectedDayData.type) {
                    CycleType.PERIOD -> {
                        BleedingCloudIcon(
                            modifier = Modifier.size(40.dp),
                            level = 2
                        )
                    }
                    CycleType.PREDICTED -> {
                        BleedingCloudIcon(
                            modifier = Modifier.size(40.dp),
                            level = 1,
                            isPredicted = true
                        )
                    }
                    CycleType.FERTILE -> {
                        // 使用另一种方式创建FlowerIcon
                        Box(
                            modifier = Modifier.size(40.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Filled.BlurOn,
                                contentDescription = null,
                                tint = Color(0xFF2196F3),
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                    else -> {
                        Box(
                            modifier = Modifier.size(40.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.CheckCircle,
                                contentDescription = null,
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = statusText,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = statusColor
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 周期提示信息
            val tipText = when (selectedDayData.type) {
                CycleType.PERIOD -> "经期期间注意保暖和休息，避免剧烈运动。"
                CycleType.PREDICTED -> "根据您的平均周期，这天可能是下次经期开始的日子。"
                CycleType.FERTILE -> "这是您的易孕期，此时排卵的可能性较高。"
                else -> "今天是您周期的正常日子。"
            }
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = when (selectedDayData.type) {
                        CycleType.PERIOD -> Color(0xFFFCE4EC)
                        CycleType.PREDICTED -> Color(0xFFFFF3E0)
                        CycleType.FERTILE -> Color(0xFFE3F2FD)
                        else -> Color(0xFFF1F8E9)
                    }
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Info,
                        contentDescription = null,
                        tint = when (selectedDayData.type) {
                            CycleType.PERIOD -> Color(0xFFE91E63)
                            CycleType.PREDICTED -> Color(0xFFFF9800)
                            CycleType.FERTILE -> CycleColors.FERTILE_TEXT
                            else -> Color(0xFF4CAF50)
                        },
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = tipText,
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                }
            }
        }
    }
}

/**
 * 血量云朵图标
 */
@Composable
fun BleedingCloudIcon(
    modifier: Modifier = Modifier,
    level: Int = 2, // 1-4级流量
    isPredicted: Boolean = false
) {
    // 避免无限循环动画导致的渲染问题
    val scale = if (isPredicted) 1f else 1.05f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        val cloudRadius = size.width * 0.35f
        val cloudColor = if (isPredicted) CycleColors.CLOUD else Color(0xFFFFCDD2)
        val dropColor = if (isPredicted) CycleColors.DROP_DARK else Color(0xFFE91E63).copy(alpha = 0.7f)
        
        // 云朵主体
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * scale,
            center = Offset(size.width * 0.3f, size.height * 0.4f)
        )
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * 0.8f * scale,
            center = Offset(size.width * 0.6f, size.height * 0.35f)
        )
        drawCircle(
            color = cloudColor,
            radius = cloudRadius * 0.7f * scale,
            center = Offset(size.width * 0.75f, size.height * 0.45f)
        )
        
        // 绘制滴血程度 - 根据级别调整数量
        val dropCount = level.coerceIn(1, 4)
        val dropRadius = size.width * 0.08f
        val dropSpacing = size.width * 0.2f
        
        for (i in 0 until dropCount) {
            val dropXOffset = size.width * 0.3f + dropSpacing * i
            drawCircle(
                color = dropColor,
                radius = dropRadius,
                center = Offset(dropXOffset.coerceAtMost(size.width * 0.8f), size.height * 0.8f)
            )
        }
        
        // 如果是预测模式，绘制虚线边框
        if (isPredicted) {
            drawCircle(
                color = Color(0xFFE91E63).copy(alpha = 0.3f),
                radius = size.width * 0.45f,
                center = Offset(size.width / 2, size.height / 2),
                style = Stroke(
                    width = size.width * 0.03f,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                )
            )
        }
        
        // 云朵眼睛 - 表情
        val eyeRadius = size.width * 0.05f
        val eyeDistance = size.width * 0.15f
        
        // 左眼
        drawCircle(
            color = Color.White,
            radius = eyeRadius,
            center = Offset(size.width * 0.4f - eyeDistance / 2, size.height * 0.4f)
        )
        drawCircle(
            color = Color.Black,
            radius = eyeRadius * 0.5f,
            center = Offset(size.width * 0.4f - eyeDistance / 2, size.height * 0.4f)
        )
        
        // 右眼
        drawCircle(
            color = Color.White,
            radius = eyeRadius,
            center = Offset(size.width * 0.4f + eyeDistance / 2, size.height * 0.4f)
        )
        drawCircle(
            color = Color.Black,
            radius = eyeRadius * 0.5f,
            center = Offset(size.width * 0.4f + eyeDistance / 2, size.height * 0.4f)
        )
    }
}

/**
 * 月亮枕头图标 - 表示经期开始/结束
 */
@Composable
fun MoonPillowIcon(
    modifier: Modifier = Modifier,
    contentColor: Color = Color(0xFFE91E63)
) {
    CachedCanvas(modifier = modifier) {
        // 枕头形状
        val pillowPath = Path().apply {
            val width = size.width
            val height = size.height
            
            moveTo(width * 0.2f, height * 0.3f)
            quadraticBezierTo(width * 0.1f, height * 0.5f, width * 0.2f, height * 0.7f)
            lineTo(width * 0.8f, height * 0.7f)
            quadraticBezierTo(width * 0.9f, height * 0.5f, width * 0.8f, height * 0.3f)
            close()
        }
        
        // 绘制枕头
        drawPath(
            path = pillowPath,
            color = contentColor.copy(alpha = 0.3f)
        )
        
        // 绘制枕头边缘
        drawPath(
            path = pillowPath,
            color = contentColor,
            style = Stroke(width = size.width * 0.05f)
        )
        
        // 绘制月亮
        val moonCenter = Offset(size.width * 0.6f, size.height * 0.4f)
        val moonRadius = size.width * 0.15f
        
        drawCircle(
            color = contentColor,
            radius = moonRadius,
            center = moonCenter
        )
        
        // 月亮阴影部分
        drawCircle(
            color = contentColor.copy(alpha = 0.3f),
            radius = moonRadius * 0.8f,
            center = Offset(moonCenter.x + moonRadius * 0.3f, moonCenter.y - moonRadius * 0.1f)
        )
    }
}

/**
 * 状态记录部分
 */
@Composable
fun StatusRecordSection() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // 标题
        Text(
            text = "今日记录",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF554A60),
            modifier = Modifier.padding(vertical = 12.dp)
        )
        
        // 经期开始状态
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    MoonPillowIcon(
                        modifier = Modifier.size(32.dp),
                        contentColor = Color(0xFFE91E63)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = stringResource(R.string.period_started),
                        fontSize = 16.sp,
                        color = Color(0xFF554A60)
                    )
                }
                
                Switch(
                    checked = false,
                    onCheckedChange = { /* 更新状态 */ },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = Color(0xFFE91E63),
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color(0xFFE0E0E0)
                    )
                )
            }
        }
        
        // 流血量记录
        SymptomRecordItem(
            icon = {
                BleedingCloudIcon(
                    modifier = Modifier.size(32.dp),
                    level = 2
                )
            },
            title = "流血量",
            tint = Color(0xFFE91E63)
        )
        
        // 腹痛记录
        SymptomRecordItem(
            icon = {
                StomachPainIcon(
                    modifier = Modifier.size(32.dp)
                )
            },
            title = "腹痛",
            tint = Color(0xFFFF9800)
        )
        
        // 疲倦记录
        SymptomRecordItem(
            icon = {
                TiredStarIcon(
                    modifier = Modifier.size(32.dp)
                )
            },
            title = "疲倦",
            tint = Color(0xFF9C27B0)
        )
    }
}

/**
 * 症状记录项
 */
@Composable
fun SymptomRecordItem(
    icon: @Composable () -> Unit,
    title: String,
    tint: Color
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable { /* 打开详情 */ },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Box(
                    modifier = Modifier.size(40.dp),
                    contentAlignment = Alignment.Center
                ) {
                    icon()
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = title,
                    fontSize = 16.sp,
                    color = Color(0xFF554A60)
                )
            }
            
            // 添加可选择的程度指示器
            Row {
                repeat(4) { index ->
                    val isSelected = index == 0
                    Box(
                        modifier = Modifier
                            .size(20.dp)
                            .padding(2.dp)
                            .clip(CircleShape)
                            .background(
                                if (isSelected) tint else Color(0xFFEEEEEE)
                            )
                            .clickable { /* 选择程度 */ }
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "查看详情",
                    tint = Color(0xFF999999)
                )
            }
        }
    }
}

/**
 * 腹痛图标
 */
@Composable
fun StomachPainIcon(
    modifier: Modifier = Modifier
) {
    // 避免动画导致的渲染问题
    val scale = 1.05f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        // 腹部
        val stomachColor = Color(0xFFFFCC80)
        val painColor = Color(0xFFFF9800)
        
        // 椭圆腹部
        drawOval(
            color = stomachColor,
            size = Size(size.width * 0.8f, size.height * 0.7f) * scale,
            topLeft = Offset(size.width * 0.1f, size.height * 0.15f)
        )
        
        // 痛苦表情
        val faceCenter = Offset(size.width * 0.5f, size.height * 0.4f)
        val eyeOffset = size.width * 0.15f
        val eyeSize = size.width * 0.1f
        
        // 眉毛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x - eyeOffset - eyeSize, faceCenter.y - eyeSize),
            end = Offset(faceCenter.x - eyeOffset + eyeSize, faceCenter.y - eyeSize * 0.5f),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x + eyeOffset - eyeSize, faceCenter.y - eyeSize * 0.5f),
            end = Offset(faceCenter.x + eyeOffset + eyeSize, faceCenter.y - eyeSize),
            strokeWidth = size.width * 0.03f
        )
        
        // 眼睛
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x - eyeOffset - eyeSize * 0.5f, faceCenter.y),
            end = Offset(faceCenter.x - eyeOffset + eyeSize * 0.5f, faceCenter.y),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF795548),
            start = Offset(faceCenter.x + eyeOffset - eyeSize * 0.5f, faceCenter.y),
            end = Offset(faceCenter.x + eyeOffset + eyeSize * 0.5f, faceCenter.y),
            strokeWidth = size.width * 0.03f
        )
        
        // 痛苦嘴巴
        val mouthPath = Path().apply {
            moveTo(faceCenter.x - eyeSize, faceCenter.y + eyeSize * 1.2f)
            cubicTo(
                faceCenter.x - eyeSize * 0.5f, faceCenter.y + eyeSize * 0.8f,
                faceCenter.x + eyeSize * 0.5f, faceCenter.y + eyeSize * 0.8f,
                faceCenter.x + eyeSize, faceCenter.y + eyeSize * 1.2f
            )
        }
        
        drawPath(
            path = mouthPath,
            color = Color(0xFF795548),
            style = Stroke(width = size.width * 0.03f)
        )
        
        // 疼痛标记
        drawCircle(
            color = CycleColors.PAIN_FILL,
            radius = size.width * 0.15f * scale,
            center = Offset(size.width * 0.7f, size.height * 0.55f)
        )
        
        drawCircle(
            color = CycleColors.PAIN_FILL,
            radius = size.width * 0.12f * (2 - scale),
            center = Offset(size.width * 0.3f, size.height * 0.6f)
        )
    }
}

/**
 * 疲倦星星图标
 */
@Composable
fun TiredStarIcon(
    modifier: Modifier = Modifier
) {
    // 避免动画导致的渲染问题
    val yawnSize = 0.85f  // 使用固定值替代动画
    
    CachedCanvas(modifier = modifier) {
        val starColor = Color(0xFFCE93D8)  // 浅紫色
        val starCenter = Offset(size.width / 2, size.height / 2)
        val outerRadius = size.width * 0.45f
        val innerRadius = size.width * 0.2f
        
        val path = Path()
        val angleStep = 36f
        
        // 创建星形路径
        for (i in 0 until 10) {
            val angle = i * angleStep * PI.toFloat() / 180
            val radius = if (i % 2 == 0) outerRadius else innerRadius
            val x = starCenter.x + cos(angle) * radius
            val y = starCenter.y + sin(angle) * radius
            
            if (i == 0) {
                path.moveTo(x, y)
            } else {
                path.lineTo(x, y)
            }
        }
        path.close()
        
        // 绘制星星
        drawPath(
            path = path,
            color = starColor
        )
        
        // 疲惫的眼睛 - 闭眼
        val eyeDistance = size.width * 0.2f
        val eyeY = starCenter.y - size.height * 0.05f
        
        // 左眼
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(starCenter.x - eyeDistance - size.width * 0.08f, eyeY),
            end = Offset(starCenter.x - eyeDistance + size.width * 0.08f, eyeY),
            strokeWidth = size.width * 0.03f
        )
        
        // 右眼
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(starCenter.x + eyeDistance - size.width * 0.08f, eyeY),
            end = Offset(starCenter.x + eyeDistance + size.width * 0.08f, eyeY),
            strokeWidth = size.width * 0.03f
        )
        
        // 打哈欠的嘴巴
        drawArc(
            color = Color(0xFF673AB7),
            startAngle = 180f,
            sweepAngle = 180f * yawnSize,
            useCenter = false,
            topLeft = Offset(
                starCenter.x - size.width * 0.15f,
                starCenter.y + size.height * 0.05f
            ),
            size = Size(
                size.width * 0.3f,
                size.height * 0.2f * yawnSize
            ),
            style = Stroke(width = size.width * 0.03f)
        )
        
        // 困倦的Z字符
        val zStartX = starCenter.x + size.width * 0.35f
        val zStartY = starCenter.y - size.height * 0.3f
        val zSize = size.width * 0.15f
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX, zStartY),
            end = Offset(zStartX + zSize, zStartY),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX + zSize, zStartY),
            end = Offset(zStartX, zStartY + zSize),
            strokeWidth = size.width * 0.03f
        )
        
        drawLine(
            color = Color(0xFF673AB7),
            start = Offset(zStartX, zStartY + zSize),
            end = Offset(zStartX + zSize, zStartY + zSize),
            strokeWidth = size.width * 0.03f
        )
    }
}

/**
 * 获取星期几的文本
 */
private fun getDayOfWeek(date: LocalDate): String {
    val dayOfWeek = date.dayOfWeek.value
    return when (dayOfWeek) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> ""
    }
} 

/**
 * 专业预测算法摘要卡片 - 已被TodayInsightCard替代
 * @deprecated 使用TodayInsightCard替代，功能已整合
 */
@Composable
fun ProfessionalPredictionCard(
    predictionResult: PeriodPredictionAlgorithm.PredictionResult?,
    cycleRegularity: PeriodPredictionAlgorithm.CycleRegularity,
    predictionConfidence: Double,
    healthRecommendations: List<String>
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = Color(0xFF7B68EE),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI 智能预测",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF554A60)
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 置信度指示器
                if (predictionResult != null) {
                    val confidencePercent = (predictionConfidence * 100).toInt()
                    Box(
                        modifier = Modifier
                            .background(
                                color = when {
                                    confidencePercent >= 80 -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                                    confidencePercent >= 60 -> Color(0xFFFF9800).copy(alpha = 0.1f)
                                    else -> Color(0xFFF44336).copy(alpha = 0.1f)
                                },
                                shape = RoundedCornerShape(12.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "$confidencePercent%",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = when {
                                confidencePercent >= 80 -> Color(0xFF4CAF50)
                                confidencePercent >= 60 -> Color(0xFFFF9800)
                                else -> Color(0xFFF44336)
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (predictionResult != null) {
                // 预测结果
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 下次经期预测
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "下次经期",
                            fontSize = 12.sp,
                            color = Color(0xFF888888),
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = predictionResult.nextPeriodStart.format(DateTimeFormatter.ofPattern("M月d日")),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFFF6B9D)
                        )
                    }
                    
                    // 排卵期预测
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "排卵期",
                            fontSize = 12.sp,
                            color = Color(0xFF888888),
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = predictionResult.ovulationDate.format(DateTimeFormatter.ofPattern("M月d日")),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF7B68EE)
                        )
                    }
                    
                    // 周期规律性
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "规律性",
                            fontSize = 12.sp,
                            color = Color(0xFF888888),
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = when (cycleRegularity) {
                                PeriodPredictionAlgorithm.CycleRegularity.VERY_REGULAR -> "非常规律"
                                PeriodPredictionAlgorithm.CycleRegularity.REGULAR -> "比较规律"
                                PeriodPredictionAlgorithm.CycleRegularity.SOMEWHAT_IRREGULAR -> "略不规律"
                                PeriodPredictionAlgorithm.CycleRegularity.IRREGULAR -> "不规律"
                                PeriodPredictionAlgorithm.CycleRegularity.INSUFFICIENT_DATA -> "数据不足"
                            },
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = when (cycleRegularity) {
                                PeriodPredictionAlgorithm.CycleRegularity.VERY_REGULAR,
                                PeriodPredictionAlgorithm.CycleRegularity.REGULAR -> Color(0xFF4CAF50)
                                PeriodPredictionAlgorithm.CycleRegularity.SOMEWHAT_IRREGULAR -> Color(0xFFFF9800)
                                else -> Color(0xFFF44336)
                            }
                        )
                    }
                }
                
                // 易孕期信息
                if (predictionResult.fertileWindowStart <= LocalDate.now() && 
                    predictionResult.fertileWindowEnd >= LocalDate.now()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = Color(0xFF4CAF50).copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            )
                            .padding(12.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Favorite,
                                contentDescription = null,
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "当前处于易孕期",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF4CAF50)
                            )
                        }
                    }
                }
                
                // 健康建议
                if (healthRecommendations.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "个性化建议",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF554A60)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    healthRecommendations.take(2).forEach { recommendation ->
                        Row(
                            modifier = Modifier.padding(vertical = 2.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(4.dp)
                                    .background(
                                        color = Color(0xFF7B68EE),
                                        shape = CircleShape
                                    )
                                    .align(Alignment.CenterVertically)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = recommendation,
                                fontSize = 13.sp,
                                color = Color(0xFF666666),
                                lineHeight = 18.sp
                            )
                        }
                    }
                }
            } else {
                // 无预测数据时的状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.ShowChart,
                        contentDescription = null,
                        tint = Color(0xFFBDBDBD),
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = "继续记录数据以获得AI智能预测",
                        fontSize = 14.sp,
                        color = Color(0xFF888888),
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "记录3个及以上周期可获得更准确的预测",
                        fontSize = 12.sp,
                        color = Color(0xFFBDBDBD),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
} 

/**
 * 医学提示卡片 - 已被TodayInsightCard替代
 * @deprecated 使用TodayInsightCard替代，功能已整合
 */
@Composable
fun MedicalInsightCard(
    selectedDayData: CycleDay
) {
    // 只在特定周期类型下显示
    if (selectedDayData.type == CycleType.NONE) {
        return
    }
    
    val cardBackgroundColor = when {
        selectedDayData.type == CycleType.PERIOD -> 
            CycleColors.PERIOD.copy(alpha = 0.1f)
        selectedDayData.type == CycleType.FERTILE -> 
            CycleColors.FERTILE.copy(alpha = 0.1f)
        selectedDayData.type == CycleType.OVULATION -> 
            CycleColors.OVULATION.copy(alpha = 0.1f)
        else -> Color.White
    }
    
    val accentColor = when {
        selectedDayData.type == CycleType.PERIOD -> 
            CycleColors.PERIOD_TEXT
        selectedDayData.type == CycleType.FERTILE -> 
            CycleColors.FERTILE_TEXT
        selectedDayData.type == CycleType.OVULATION -> 
            CycleColors.OVULATION_TEXT
        else -> Color.Gray
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 信息图标
            Icon(
                imageVector = Icons.Default.Info,
                contentDescription = null,
                tint = accentColor,
                modifier = Modifier
                    .size(24.dp)
                    .background(Color.White, CircleShape)
                    .padding(4.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 医学建议内容
            Column {
                Text(
                    text = when {
                        selectedDayData.type == CycleType.PERIOD -> 
                            "月经期健康管理"
                        selectedDayData.type == CycleType.FERTILE -> 
                            "排卵前期提示"
                        selectedDayData.type == CycleType.OVULATION -> 
                            "排卵期管理"
                        else -> 
                            "月经周期健康提示"
                    },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = accentColor
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = when {
                        selectedDayData.type == CycleType.PERIOD -> 
                            "月经期子宫内膜脱落出血，注意保暖避免寒冷刺激导致子宫收缩加剧。补充含铁丰富的食物如深绿色蔬菜、红肉等，有助于预防缺铁性贫血。"
                        selectedDayData.type == CycleType.FERTILE -> 
                            "排卵前期是生育窗口期的开始，宫颈粘液变得更加透明、拉丝，适合精子通过。每日测量基础体温(BBT)有助于精确判断排卵时间。"
                        selectedDayData.type == CycleType.OVULATION -> 
                            "排卵期可能出现轻微侧腹痛(中间痛)或少量排卵期出血，属于正常生理现象。排卵后基础体温会有0.3-0.5°C的明显上升，持续至下次月经。"
                        selectedDayData.dayOfCycle < 14 -> 
                            "卵泡期(月经结束至排卵前)是卵泡发育的阶段，雌激素水平逐渐上升。此阶段适合规律运动和均衡饮食，有助于促进卵泡健康发育。"
                        else -> 
                            "黄体期(排卵后至下次月经前)黄体分泌孕激素，使子宫内膜为可能的妊娠做准备。保持良好作息，减少压力有助于维持激素水平平衡。"
                    },
                    fontSize = 14.sp,
                    color = Color.DarkGray,
                    lineHeight = 20.sp
                )
                
            }
        }
    }
}



/**
 * 图表柱状条
 */
@Composable
fun ChartBar(
    height: Int,
    color: Color,
    label: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .width(24.dp)
                .height(height.dp)
                .background(color.copy(alpha = 0.7f), RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.DarkGray
        )
    }
}

// 判断是否为预测类型
fun isPredictedType(type: CycleType): Boolean {
    return type == CycleType.PREDICTED ||
           type == CycleType.PREDICTED_PERIOD ||
           type == CycleType.PREDICTED_FOLLICULAR ||
           type == CycleType.PREDICTED_OVULATION ||
           type == CycleType.PREDICTED_LUTEAL ||
           type == CycleType.PREDICTED_FERTILE
} 

/**
 * 日期编辑对话框 - 用于补录历史经期数据
 */
@Composable
fun CycleRecordEditDialog(
    date: LocalDate,
    viewModel: MenstrualCycleViewModel,
    onDismiss: () -> Unit
) {
    var selectedOperation by remember { mutableStateOf<String?>(null) }
    var startDate by remember { mutableStateOf(date) }
    var endDate by remember { mutableStateOf(date.plusDays(4)) } // 默认5天经期
    var showDateRangePicker by remember { mutableStateOf(false) }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }
    var showConfirmStartDialog by remember { mutableStateOf(false) }
    var showConfirmEndDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    
    // 获取平均经期长度
    val averagePeriodLength = remember { viewModel.getAveragePeriodLength() }
    
    // 标记是否可以编辑该日期
    val canEditDate = viewModel.isValidDateForHistory(date)
    
    if (!canEditDate) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("无法编辑此日期") },
            text = { 
                Column {
                    Text("只能编辑过去六个月内的日期，未来日期不可编辑。")
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示可编辑的日期范围
                    val today = LocalDate.now()
                    val sixMonthsAgo = today.minusMonths(6).withDayOfMonth(1)
                    Text(
                        text = "可编辑范围：${sixMonthsAgo.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 至 ${today.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}",
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT
                    )
                }
            },
            confirmButton = {
                TextButton(onClick = onDismiss) {
                    Text("确定", color = CycleColors.PERIOD_TEXT)
                }
            }
        )
        return
    }
    
    // 开始日期选择器
    if (showStartDatePicker) {
        CustomDatePickerDialog(
            title = "选择开始日期",
            initialDate = startDate,
            onDateSelected = { newDate -> 
                startDate = newDate
                // 确保结束日期不早于开始日期
                if (endDate.isBefore(startDate)) {
                    endDate = startDate.plusDays(averagePeriodLength.toLong() - 1)
                }
            },
            onDismiss = { showStartDatePicker = false }
        )
    }
    
    // 结束日期选择器
    if (showEndDatePicker) {
        CustomDatePickerDialog(
            title = "选择结束日期",
            initialDate = endDate,
            onDateSelected = { newDate -> 
                // 确保结束日期不早于开始日期
                if (!newDate.isBefore(startDate)) {
                    endDate = newDate
                } else {
                    Toast.makeText(context, "结束日期不能早于开始日期", Toast.LENGTH_SHORT).show()
                }
            },
            onDismiss = { showEndDatePicker = false }
        )
    }
    
    // 日期范围选择界面
    if (showDateRangePicker) {
        AlertDialog(
            onDismissRequest = { showDateRangePicker = false },
            title = { Text("选择日期范围") },
            text = { 
                Column {
                    Text("请选择月经期的起止日期")
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 开始日期选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "开始日期: ",
                            fontSize = 14.sp,
                            modifier = Modifier.width(80.dp)
                        )
                        Text(
                            text = startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 14.sp,
                            color = CycleColors.PERIOD_TEXT,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { showStartDatePicker = true }
                        )
                        IconButton(onClick = { showStartDatePicker = true }) {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择开始日期",
                                tint = CycleColors.PERIOD_TEXT
                            )
                        }
                    }
                    
                    // 结束日期选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "结束日期: ",
                            fontSize = 14.sp,
                            modifier = Modifier.width(80.dp)
                        )
                        Text(
                            text = endDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            fontSize = 14.sp,
                            color = CycleColors.PERIOD_TEXT,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .weight(1f)
                                .clickable { showEndDatePicker = true }
                        )
                        IconButton(onClick = { showEndDatePicker = true }) {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择结束日期",
                                tint = CycleColors.PERIOD_TEXT
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示经期长度
                    val daysCount = ChronoUnit.DAYS.between(startDate, endDate).toInt() + 1
                    Text(
                        text = "经期长度：$daysCount 天",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = CycleColors.PERIOD_TEXT
                    )
                    
                    // 医学知识提示
                    Spacer(modifier = Modifier.height(8.dp))
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "医学小知识",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "正常月经周期为21-35天，月经期通常持续3-7天，平均为5天。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showDateRangePicker = false
                        // 实际记录日期范围
                        viewModel.recordPeriodForDateRange(startDate, endDate)
                        
                        // 使用延迟刷新函数确保UI更新
                        viewModel.refreshCalendarViewWithDelay()
                        
                        Toast.makeText(context, "已记录从 ${startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 到 ${endDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 的经期", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确定", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDateRangePicker = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 确认开始经期对话框
    if (showConfirmStartDialog) {
        val predictedEndDate = date.plusDays(averagePeriodLength.toLong() - 1)
        
        AlertDialog(
            onDismissRequest = { showConfirmStartDialog = false },
            title = { Text("确认记录经期开始") },
            text = { 
                Column {
                    Text("您选择了 ${date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 作为经期开始日")
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "根据您的历史数据，系统将自动预设经期结束日为 ${predictedEndDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}（共 $averagePeriodLength 天）",
                        fontSize = 14.sp
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "您可以在经期结束时调整结束日期，或现在选择设置经期范围",
                        fontSize = 14.sp,
                        fontStyle = FontStyle.Italic
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 医学知识提示
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "月经小贴士",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "月经期间应保持良好卫生习惯，定时更换卫生用品，注意休息，避免剧烈运动，多喝温水，少食生冷辛辣食物。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showConfirmStartDialog = false
                        viewModel.recordPeriodStartForDate(date)
                        viewModel.refreshCalendarViewWithDelay()
                        Toast.makeText(context, "已记录经期开始，预计结束日期为 ${predictedEndDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确认", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                Row {
                    TextButton(
                        onClick = { 
                            showConfirmStartDialog = false
                            // 初始化日期范围选择器的日期
                            startDate = date
                            endDate = predictedEndDate
                            showDateRangePicker = true
                        }
                    ) {
                        Text("设置日期范围", color = CycleColors.PERIOD_TEXT)
                    }
                    TextButton(
                        onClick = { showConfirmStartDialog = false }
                    ) {
                        Text("取消")
                    }
                }
            }
        )
    }
    
    // 确认结束经期对话框
    if (showConfirmEndDialog) {
        AlertDialog(
            onDismissRequest = { showConfirmEndDialog = false },
            title = { Text("确认记录经期结束") },
            text = { 
                Column {
                    Text("您选择了 ${date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))} 作为经期结束日")
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    val latestCycle = viewModel.cycles.value.maxByOrNull { it.startDate }
                    if (latestCycle != null && latestCycle.endDate == null) {
                        val startDate = latestCycle.startDate
                        val periodLength = ChronoUnit.DAYS.between(startDate, date).toInt() + 1
                        
                        Text(
                            text = "您的本次经期从 ${startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} 开始，共持续 $periodLength 天",
                            fontSize = 14.sp
                        )
                        
                        if (periodLength < 3 || periodLength > 7) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFFFFF3CD)
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Row(
                                    modifier = Modifier.padding(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Warning,
                                        contentDescription = null,
                                        tint = Color(0xFFCC9900),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "您的经期长度为 $periodLength 天，超出了正常范围(3-7天)。如果经常出现此情况，建议咨询医生。",
                                        fontSize = 12.sp,
                                        color = Color.DarkGray
                                    )
                                }
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 医学知识提示
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = CycleColors.PERIOD.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = null,
                                    tint = CycleColors.PERIOD_TEXT,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "月经后调理小贴士",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = CycleColors.PERIOD_TEXT
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "月经结束后是身体恢复的重要时期，注意补充铁质食物，适量运动增强体质，保持良好的作息习惯，为下一个周期做好准备。",
                                fontSize = 12.sp,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { 
                        showConfirmEndDialog = false
                        viewModel.recordPeriodEndForDate(date)
                        viewModel.refreshCalendarViewWithDelay()
                        Toast.makeText(context, "已记录经期结束", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    }
                ) {
                    Text("确认", color = CycleColors.PERIOD_TEXT)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showConfirmEndDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 实际应用中，可以使用系统日期选择器
    // 这里简化处理
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Column {
                Text(
                    text = "经期记录编辑",
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.PERIOD_TEXT
                )
                
                Text(
                    text = date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = CycleColors.PERIOD_TEXT
                )
            }
        },
        text = {
            Column {
                // 显示当前日期周期状态
                val cycleType = viewModel.selectedDayData.value.type
                val cycleTypeText = when (cycleType) {
                    CycleType.PERIOD -> "月经期"
                    CycleType.FOLLICULAR -> "卵泡期"
                    CycleType.OVULATION -> "排卵期"
                    CycleType.LUTEAL -> "黄体期"
                    CycleType.FERTILE -> "排卵前期"
                    CycleType.PREDICTED, 
                    CycleType.PREDICTED_PERIOD -> "预测月经期"
                    CycleType.PREDICTED_FOLLICULAR -> "预测卵泡期"
                    CycleType.PREDICTED_OVULATION -> "预测排卵期"
                    CycleType.PREDICTED_LUTEAL -> "预测黄体期"
                    CycleType.PREDICTED_FERTILE -> "预测排卵前期"
                    CycleType.NONE -> "无特殊记录"
                }
                
                Text(
                    text = "当前状态: $cycleTypeText",
                    fontSize = 14.sp,
                    color = CycleColors.getTextColorForType(cycleType),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Divider(
                    color = Color.LightGray.copy(alpha = 0.5f),
                    thickness = 1.dp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                Text(
                    text = "请选择操作:",
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
                
                // 操作按钮 - 记录经期开始日
                OutlinedButton(
                    onClick = { showConfirmStartDialog = true },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = CycleColors.PERIOD_TEXT
                    ),
                    border = BorderStroke(1.dp, CycleColors.PERIOD_TEXT)
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = CycleColors.PERIOD_TEXT
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "记录经期开始日",
                            fontSize = 14.sp
                        )
                    }
                }
                
                // 操作按钮 - 删除记录
                OutlinedButton(
                    onClick = {
                        selectedOperation = "delete"
                        viewModel.deleteCycleForDate(date)

                        // 🔧 修复：确保数据完全刷新
                        viewModel.refreshData()

                        Toast.makeText(context, "已删除经期记录", Toast.LENGTH_SHORT).show()
                        onDismiss()
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.Red
                    ),
                    border = BorderStroke(1.dp, Color.Red)
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = Color.Red
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "删除此日期的经期记录",
                            fontSize = 14.sp,
                            color = Color.Red
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭", color = CycleColors.PERIOD_TEXT)
            }
        }
    )
}

/**
 * 打开日期上下文菜单（长按日期）- 简化版本，只支持记录开始
 */
private fun openDateContextMenu(
    date: LocalDate,
    onPeriodStart: () -> Unit,
    context: Context
) {
    // 使用系统的上下文菜单
    // 注意：此功能在实际应用中需要实现完整的上下文菜单
    // 这里只是示例逻辑
    
    // 例如可以使用PopupMenu或BottomSheetDialog
    // 显示经期记录选项:
    // 1. 记录经期开始
    // 2. 添加经期记录范围
    // 3. 删除经期记录
    
    // 简化版实现，直接显示提示并调用回调函数
    Toast.makeText(context, "长按功能: 请使用编辑对话框完成操作", Toast.LENGTH_SHORT).show()
    onPeriodStart()
}

// 在合适的位置添加日期选择器的实现
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CustomDatePickerDialog(
    title: String,
    initialDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    )
    
    val selectedDate = remember(datePickerState.selectedDateMillis) {
        datePickerState.selectedDateMillis?.let {
            Instant.ofEpochMilli(it).atZone(ZoneId.systemDefault()).toLocalDate()
        } ?: initialDate
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DatePicker(
                    state = datePickerState,
                    showModeToggle = false,
                    title = null,
                    headline = null,
                    colors = DatePickerDefaults.colors(
                        containerColor = Color.White,
                        titleContentColor = CycleColors.PERIOD_TEXT,
                        headlineContentColor = CycleColors.PERIOD_TEXT,
                        weekdayContentColor = Color.Gray,
                        subheadContentColor = Color.DarkGray,
                        yearContentColor = Color.DarkGray,
                        currentYearContentColor = CycleColors.PERIOD_TEXT,
                        selectedYearContainerColor = CycleColors.PERIOD.copy(alpha = 0.1f),
                        selectedYearContentColor = CycleColors.PERIOD_TEXT,
                        dayContentColor = Color.DarkGray,
                        selectedDayContainerColor = CycleColors.PERIOD,
                        selectedDayContentColor = Color.White,
                        todayContentColor = CycleColors.PERIOD_TEXT,
                        todayDateBorderColor = CycleColors.PERIOD
                    )
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    selectedDate?.let { onDateSelected(it) }
                    onDismiss()
                }
            ) {
                Text("确定", color = CycleColors.PERIOD_TEXT)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

// PeriodRecordCard已删除 - 简化界面，合并到顶部卡片中

// 已移除时间选择对话框 - 简化为只记录日期

/**
 * 经期结束日期选择对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PeriodEndDateDialog(
    initialDate: LocalDate,
    startDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onDismiss: () -> Unit
) {
    var selectedDate by remember { mutableStateOf(initialDate) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(
                onClick = { onDateSelected(selectedDate) }
            ) {
                Text("确定", color = CycleColors.LUTEAL_TEXT)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        title = {
            Text(
                text = "选择经期结束日期",
                fontWeight = FontWeight.Bold,
                color = CycleColors.LUTEAL_TEXT
            )
        },
        text = {
            Column {
                Text(
                    text = "经期开始: ${startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))}",
                    fontSize = 14.sp,
                    color = CycleColors.PERIOD_TEXT,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // 日期选择器
                val datePickerState = rememberDatePickerState(
                    initialSelectedDateMillis = selectedDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
                )
                
                DatePicker(
                    state = datePickerState,
                    showModeToggle = false,
                    colors = DatePickerDefaults.colors(
                        containerColor = Color.White,
                        titleContentColor = CycleColors.LUTEAL_TEXT,
                        headlineContentColor = CycleColors.LUTEAL_TEXT,
                        weekdayContentColor = Color.Gray,
                        subheadContentColor = Color.DarkGray,
                        selectedDayContainerColor = CycleColors.LUTEAL,
                        selectedDayContentColor = Color.White,
                        todayContentColor = CycleColors.LUTEAL_TEXT,
                        todayDateBorderColor = CycleColors.LUTEAL
                    )
                )
                
                // 更新选中日期
                LaunchedEffect(datePickerState.selectedDateMillis) {
                    datePickerState.selectedDateMillis?.let { millis ->
                        selectedDate = java.time.Instant.ofEpochMilli(millis)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDate()
                    }
                }
            }
        }
    )
}

/**
 * 四元组数据类 - 用于简化操作状态
 */
data class Tuple4<T1, T2, T3, T4>(
    val first: T1,
    val second: T2,
    val third: T3,
    val fourth: T4
)

/**
 * 简化的经期记录卡片 - 基于当前日期状态智能显示操作
 */
@Composable
fun SimplePeriodRecordCard(
    selectedDate: LocalDate,
    selectedDayData: CycleDay,
    viewModel: MenstrualCycleViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var showQuickRecordDialog by remember { mutableStateOf(false) }
    
    // 根据当前状态决定显示的操作
    val (actionText, actionIcon, actionColor, actionType) = when (selectedDayData.type) {
        CycleType.PERIOD -> {
            Tuple4("结束经期", Icons.Default.Stop, CycleColors.LUTEAL, "end")
        }
        else -> {
            Tuple4("开始经期", Icons.Default.PlayArrow, CycleColors.PERIOD, "start")
        }
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 智能标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "经期记录",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.PERIOD_TEXT
                    )
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("MM月dd日 EEEE")),
                        fontSize = 14.sp,
                        color = CycleColors.PERIOD_TEXT.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                // 状态指示器
                Surface(
                    color = when (selectedDayData.type) {
                        CycleType.PERIOD -> CycleColors.PERIOD.copy(alpha = 0.2f)
                        else -> Color.Gray.copy(alpha = 0.1f)
                    },
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text(
                        text = when (selectedDayData.type) {
                            CycleType.PERIOD -> "经期中"
                            else -> "非经期"
                        },
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = when (selectedDayData.type) {
                            CycleType.PERIOD -> CycleColors.PERIOD_TEXT
                            else -> Color.Gray
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 主要操作按钮
            Button(
                onClick = { showQuickRecordDialog = true },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(containerColor = actionColor),
                shape = RoundedCornerShape(12.dp),
                contentPadding = PaddingValues(vertical = 16.dp)
            ) {
                Icon(
                    imageVector = actionIcon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = actionText,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 当前周期信息
            if (selectedDayData.type == CycleType.PERIOD && selectedDayData.dayOfCycle > 0) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = CycleColors.PERIOD.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "当前经期第${selectedDayData.dayOfCycle}天",
                            fontSize = 13.sp,
                            color = CycleColors.PERIOD_TEXT
                        )
                    }
                }
            }
        }
    }
    
    // 快速记录确认对话框
    if (showQuickRecordDialog) {
        QuickRecordDialog(
            actionType = actionType,
            selectedDate = selectedDate,
            onConfirm = {
                try {
                    if (actionType == "start") {
                        viewModel.recordPeriodStartForDate(selectedDate)
                        Toast.makeText(context, "已记录经期开始", Toast.LENGTH_SHORT).show()
                    } else {
                        viewModel.recordPeriodEndForDate(selectedDate)
                        Toast.makeText(context, "已记录经期结束", Toast.LENGTH_SHORT).show()
                    }
                } catch (e: Exception) {
                    Toast.makeText(context, "记录失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
                showQuickRecordDialog = false
            },
            onDismiss = { showQuickRecordDialog = false }
        )
    }
}

/**
 * 快速记录确认对话框 - 简化版本，只确认日期
 */
@Composable
fun QuickRecordDialog(
    actionType: String,
    selectedDate: LocalDate,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    val (title, color, icon, message) = if (actionType == "start") {
        Tuple4(
            "确认经期开始",
            CycleColors.PERIOD,
            Icons.Default.PlayArrow,
            "确认在此日期开始经期？"
        )
    } else {
        Tuple4(
            "确认经期结束",
            CycleColors.LUTEAL,
            Icons.Default.Stop,
            "确认在此日期结束经期？"
        )
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text("确认", color = color)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        title = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
            }
        },
        text = {
            Column {
                Text(
                    text = selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 EEEE")),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.DarkGray,
                    modifier = Modifier.padding(bottom = 12.dp)
                )
                
                Text(
                    text = message,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
        }
    )
}

/**
 * 整合经期记录卡片 - 已被QuickActionsSection替代
 * @deprecated 使用QuickActionsSection替代，功能更简洁
 */
@Composable
fun IntegratedPeriodRecordCard(
    viewModel: MenstrualCycleViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val currentDate = remember { LocalDate.now() } // 🔧 避免重复计算当前日期
    val cycles by viewModel.cycles.collectAsState()
    var showHistoryDialog by remember { mutableStateOf(false) }
    
    // 🔧 使用derivedStateOf优化计算，只有cycles变化时才重新计算
    val currentCycle by remember(cycles) {
        derivedStateOf {
            cycles.find { cycle ->
                cycle.startDate <= currentDate && 
                (cycle.endDate == null || cycle.endDate!! >= currentDate)
            }
        }
    }
    
    // 🔧 缓存当前经期状态，避免重复计算
    val isCurrentlyInPeriod = remember(currentCycle) {
        currentCycle?.endDate == null && currentCycle != null
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Today,
                    contentDescription = null,
                    tint = CycleColors.PERIOD_TEXT,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "经期记录",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = CycleColors.PERIOD_TEXT
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 当前状态显示
            val statusText = currentCycle?.let { cycle ->
                if (cycle.endDate == null) {
                    "经期进行中（第${ChronoUnit.DAYS.between(cycle.startDate, currentDate) + 1}天）"
                } else {
                    "经期已结束"
                }
            } ?: "未在经期"
            
            Text(
                text = "当前状态：$statusText",
                fontSize = 14.sp,
                color = CycleColors.PERIOD_TEXT.copy(alpha = 0.7f),
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 今日操作按钮行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 记录今日经期开始按钮
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clickable(
                            indication = null, // 🔧 禁用Ripple效果减少性能消耗
                            interactionSource = remember { MutableInteractionSource() }
                        ) {
                            viewModel.recordPeriodStartForDate(currentDate)
                            Toast.makeText(
                                context,
                                "已记录今日经期开始",
                                Toast.LENGTH_SHORT
                            ).show()
                        },
                    color = CycleColors.PERIOD.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = null,
                            tint = CycleColors.PERIOD_TEXT,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "今日开始",
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = CycleColors.PERIOD_TEXT
                        )
                    }
                }

                // 记录今日经期结束按钮
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clickable(
                            indication = null, // 🔧 禁用Ripple效果减少性能消耗
                            interactionSource = remember { MutableInteractionSource() }
                        ) {
                            viewModel.recordPeriodEndForDate(currentDate)
                            Toast.makeText(
                                context,
                                "已记录今日经期结束",
                                Toast.LENGTH_SHORT
                            ).show()
                        },
                    color = CycleColors.LUTEAL.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Stop,
                            contentDescription = null,
                            tint = CycleColors.LUTEAL_TEXT,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "今日结束",
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = CycleColors.LUTEAL_TEXT
                        )
                    }
                }

                // 🆕 取消今日记录按钮
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clickable(
                            indication = null, // 🔧 禁用Ripple效果减少性能消耗
                            interactionSource = remember { MutableInteractionSource() }
                        ) {
                            viewModel.cancelTodayPeriodRecord()
                            Toast.makeText(
                                context,
                                "已取消今日经期记录",
                                Toast.LENGTH_SHORT
                            ).show()
                        },
                    color = Color.Gray.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Cancel,
                            contentDescription = null,
                            tint = Color.Gray,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "取消记录",
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Gray
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 分隔线
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(CycleColors.PERIOD_TEXT.copy(alpha = 0.1f))
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 历史记录按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = null,
                        tint = CycleColors.LUTEAL_TEXT,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "记录过去的经期",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = CycleColors.LUTEAL_TEXT
                    )
                }

                Surface(
                    modifier = Modifier
                        .clickable(
                            indication = null, // 🔧 禁用Ripple效果减少性能消耗
                            interactionSource = remember { MutableInteractionSource() }
                        ) { showHistoryDialog = true },
                    color = CycleColors.LUTEAL.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(18.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = CycleColors.LUTEAL,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "记录",
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = CycleColors.LUTEAL
                            )
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 提示文字
            Text(
                text = "最多可追溯2年的经期数据",
                fontSize = 12.sp,
                color = CycleColors.PERIOD_TEXT.copy(alpha = 0.5f),
                modifier = Modifier.fillMaxWidth()
            )
        }
    }

    // 历史记录对话框
    if (showHistoryDialog) {
        HistoryRecordDialog(
            onDismiss = { showHistoryDialog = false },
            onDateRangeSelected = { startDate, endDate ->
                viewModel.recordHistoryPeriod(startDate, endDate)
                showHistoryDialog = false
                // 显示成功提示
                Toast.makeText(
                    context,
                    "已成功记录${formatDateForDisplay(startDate)}至${formatDateForDisplay(endDate)}的经期数据",
                    Toast.LENGTH_SHORT
                ).show()
            }
        )
    }
}

// 已移除：CurrentPeriodRecordCard - 功能已整合到 IntegratedPeriodRecordCard

// 已移除：HistoryRecordCard - 功能已整合到 IntegratedPeriodRecordCard

/**
 * 经期记录对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryRecordDialog(
    onDismiss: () -> Unit,
    onDateRangeSelected: (LocalDate, LocalDate) -> Unit
) {
    var selectedStartDate by remember { mutableStateOf<LocalDate?>(null) }
    var selectedEndDate by remember { mutableStateOf<LocalDate?>(null) }
    var currentMonth by remember { mutableStateOf(YearMonth.now().minusMonths(1)) } // 默认显示上个月
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = Color.White,
            shadowElevation = 16.dp
        ) {
            Column(
                modifier = Modifier.padding(0.dp)
            ) {
                // 标题栏
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    CycleColors.LUTEAL.copy(alpha = 0.1f),
                                    Color.Transparent
                                )
                            )
                        )
                        .padding(horizontal = 20.dp, vertical = 16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "经期记录",
                            style = TextStyle(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = CycleColors.LUTEAL_TEXT
                            )
                        )

                        IconButton(
                            onClick = onDismiss,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = CycleColors.LUTEAL_TEXT.copy(alpha = 0.7f),
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }

                // 月份导航
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = { 
                            currentMonth = currentMonth.minusMonths(1)
                        },
                        enabled = currentMonth.isAfter(YearMonth.now().minusYears(2))
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "上个月",
                            tint = CycleColors.LUTEAL_TEXT
                        )
                    }

                    Text(
                        text = "${currentMonth.year}年${currentMonth.monthValue}月",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = CycleColors.LUTEAL_TEXT
                        )
                    )

                    IconButton(
                        onClick = { 
                            currentMonth = currentMonth.plusMonths(1)
                        },
                        enabled = currentMonth.isBefore(YearMonth.now())
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowForward,
                            contentDescription = "下个月",
                            tint = CycleColors.LUTEAL_TEXT
                        )
                    }
                }

                // 简化的日历网格
                HistoryCalendarGrid(
                    currentMonth = currentMonth,
                    selectedStartDate = selectedStartDate,
                    selectedEndDate = selectedEndDate,
                    onDateSelected = { date ->
                        when {
                            selectedStartDate == null -> {
                                selectedStartDate = date
                                selectedEndDate = null
                            }
                            selectedEndDate == null -> {
                                if (date.isBefore(selectedStartDate)) {
                                    selectedStartDate = date
                                    selectedEndDate = null
                                } else {
                                    selectedEndDate = date
                                }
                            }
                            else -> {
                                selectedStartDate = date
                                selectedEndDate = null
                            }
                        }
                    },
                    modifier = Modifier.padding(horizontal = 16.dp)
                )

                // 选择说明
                if (selectedStartDate != null) {
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 12.dp),
                        color = CycleColors.LUTEAL.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "已选择范围",
                                fontSize = 12.sp,
                                color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.7f)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            val rangeText = if (selectedEndDate != null) {
                                "${formatDateForDisplay(selectedStartDate!!)} 至 ${formatDateForDisplay(selectedEndDate!!)}"
                            } else {
                                "${formatDateForDisplay(selectedStartDate!!)} (请选择结束日期)"
                            }
                            Text(
                                text = rangeText,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = CycleColors.LUTEAL_TEXT
                            )
                        }
                    }
                }

                // 底部操作区
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFFAFBFC),
                    shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp, vertical = 16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // 取消按钮
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { onDismiss() },
                            color = Color.Transparent,
                            shape = RoundedCornerShape(12.dp),
                            border = BorderStroke(1.dp, CycleColors.LUTEAL.copy(alpha = 0.3f))
                        ) {
                            Box(
                                modifier = Modifier.padding(vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "取消",
                                    style = TextStyle(
                                        fontSize = 15.sp,
                                        fontWeight = FontWeight.Medium,
                                        color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.7f)
                                    )
                                )
                            }
                        }

                        // 确认按钮
                        Surface(
                            modifier = Modifier
                                .weight(1f)
                                .clickable {
                                    if (selectedStartDate != null && selectedEndDate != null) {
                                        onDateRangeSelected(selectedStartDate!!, selectedEndDate!!)
                                    }
                                },
                            color = if (selectedStartDate != null && selectedEndDate != null) {
                                CycleColors.LUTEAL
                            } else {
                                CycleColors.LUTEAL.copy(alpha = 0.3f)
                            },
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Box(
                                modifier = Modifier.padding(vertical = 12.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "确认记录",
                                    style = TextStyle(
                                        fontSize = 15.sp,
                                        fontWeight = FontWeight.SemiBold,
                                        color = Color.White
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 经期记录日历网格
 */
@Composable
fun HistoryCalendarGrid(
    currentMonth: YearMonth,
    selectedStartDate: LocalDate?,
    selectedEndDate: LocalDate?,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    val today = LocalDate.now()
    val firstDayOfMonth = currentMonth.atDay(1)
    val lastDayOfMonth = currentMonth.atEndOfMonth()
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 0=周日, 1=周一, ...
    
    Column(modifier = modifier) {
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                Text(
                    text = day,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.6f)
                    )
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 日期网格
        val weeks = mutableListOf<List<LocalDate?>>()
        var currentWeek = mutableListOf<LocalDate?>()
        
        // 添加月初的空白日期
        repeat(firstDayOfWeek) {
            currentWeek.add(null)
        }
        
        // 添加当月的所有日期
        for (day in 1..lastDayOfMonth.dayOfMonth) {
            currentWeek.add(currentMonth.atDay(day))
            if (currentWeek.size == 7) {
                weeks.add(currentWeek.toList())
                currentWeek.clear()
            }
        }
        
        // 添加月末的空白日期
        if (currentWeek.isNotEmpty()) {
            while (currentWeek.size < 7) {
                currentWeek.add(null)
            }
            weeks.add(currentWeek.toList())
        }
        
        weeks.forEach { week ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                week.forEach { date ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                            .padding(2.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        if (date != null) {
                            val isInRange = selectedStartDate != null && selectedEndDate != null &&
                                    !date.isBefore(selectedStartDate) && !date.isAfter(selectedEndDate)
                            val isStartDate = date == selectedStartDate
                            val isEndDate = date == selectedEndDate
                            val canSelect = !date.isAfter(today) && date.isAfter(today.minusYears(2))
                            
                            Surface(
                                modifier = Modifier
                                    .size(36.dp)
                                    .clickable(enabled = canSelect) { 
                                        if (canSelect) onDateSelected(date) 
                                    },
                                color = when {
                                    isStartDate || isEndDate -> CycleColors.LUTEAL
                                    isInRange -> CycleColors.LUTEAL.copy(alpha = 0.3f)
                                    else -> Color.Transparent
                                },
                                shape = CircleShape,
                                border = if (date == today) {
                                    BorderStroke(1.dp, CycleColors.LUTEAL.copy(alpha = 0.5f))
                                } else null
                            ) {
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    Text(
                                        text = date.dayOfMonth.toString(),
                                        style = TextStyle(
                                            fontSize = 14.sp,
                                            fontWeight = if (isStartDate || isEndDate) FontWeight.Bold else FontWeight.Normal,
                                            color = when {
                                                !canSelect -> CycleColors.LUTEAL_TEXT.copy(alpha = 0.3f)
                                                isStartDate || isEndDate -> Color.White
                                                isInRange -> CycleColors.LUTEAL_TEXT
                                                date == today -> CycleColors.LUTEAL
                                                else -> CycleColors.LUTEAL_TEXT.copy(alpha = 0.8f)
                                            }
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 格式化日期显示
 */
private fun formatDateForDisplay(date: LocalDate): String {
    val today = LocalDate.now()
    return when {
        date == today -> "今天"
        date == today.minusDays(1) -> "昨天"
        date.year == today.year -> "${date.monthValue}月${date.dayOfMonth}日"
        else -> "${date.year}年${date.monthValue}月${date.dayOfMonth}日"
    }
}

/**
 * 最近经期记录展示 - 功能已整合到CompactCycleOverviewCard
 * @deprecated 历史记录功能已整合到主要组件中
 */
@Composable
fun RecentHistoryRecordsCard(
    viewModel: MenstrualCycleViewModel,
    modifier: Modifier = Modifier
) {
    val cycles by viewModel.cycles.collectAsState()
    
    // 过滤出最近6个月内的历史记录（通过notes字段判断）
    val historyRecords = remember(cycles) {
        cycles.filter { cycle ->
            cycle.notes?.contains("历史记录") == true
        }.sortedByDescending { it.startDate }.take(5)
    }

    if (historyRecords.isNotEmpty()) {
        Card(
            modifier = modifier.fillMaxWidth(),
            shape = RoundedCornerShape(20.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp)
            ) {
                // 标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = null,
                        tint = CycleColors.LUTEAL_TEXT,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "最近历史记录",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = CycleColors.LUTEAL_TEXT
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Surface(
                        color = CycleColors.LUTEAL.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(18.dp)
                    ) {
                        Text(
                            text = "${historyRecords.size}条记录",
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = CycleColors.LUTEAL
                            )
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // 历史记录列表
                historyRecords.forEach { record ->
                    HistoryRecordItem(
                        record = record,
                        onDeleteClick = { 
                            viewModel.deleteCycleRecord(record.id)
                        }
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                if (historyRecords.size >= 5) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "显示最近5条历史记录",
                        fontSize = 12.sp,
                        color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.5f),
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 单个历史记录项
 */
@Composable
fun HistoryRecordItem(
    record: com.timeflow.app.data.entity.CycleRecord,
    onDeleteClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = CycleColors.LUTEAL.copy(alpha = 0.05f),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 日期图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        CycleColors.LUTEAL.copy(alpha = 0.2f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.CalendarToday,
                    contentDescription = null,
                    tint = CycleColors.LUTEAL_TEXT,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 记录信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                val dateRange = if (record.endDate != null) {
                    "${record.startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))} - ${record.endDate!!.format(DateTimeFormatter.ofPattern("MM月dd日"))}"
                } else {
                    record.startDate.format(DateTimeFormatter.ofPattern("MM月dd日"))
                }
                
                Text(
                    text = dateRange,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = CycleColors.LUTEAL_TEXT
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "经期${record.periodLength ?: 0}天",
                        fontSize = 12.sp,
                        color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.7f)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    // 显示记录时间
                    Text(
                        text = record.createdAt.format(DateTimeFormatter.ofPattern("MM-dd")),
                        fontSize = 10.sp,
                        color = CycleColors.LUTEAL_TEXT.copy(alpha = 0.5f)
                    )
                    
                    Spacer(modifier = Modifier.width(6.dp))
                    
                    Surface(
                        color = CycleColors.PERIOD.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = "历史",
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = CycleColors.PERIOD_TEXT
                        )
                    }
                }
            }
            
            // 删除按钮
            IconButton(
                onClick = onDeleteClick,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "删除历史记录",
                    tint = CycleColors.LUTEAL_TEXT.copy(alpha = 0.5f),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 美化的日期编辑对话框
 * 长按日历日期时显示，支持添加/删除经期记录，支持设置为开始日或结束日
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateEditDialog(
    date: LocalDate,
    viewModel: MenstrualCycleViewModel,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val cycleData by viewModel.cycleData.collectAsState()
    val historicalPeriods by viewModel.getHistoricalPeriods().collectAsState(initial = emptyList())

    // 检查当前日期的状态
    val currentDayData = cycleData.find { it.date == date }
    val hasPeriodRecord = currentDayData?.type == CycleType.PERIOD
    val hasOvulationRecord = currentDayData?.type == CycleType.OVULATION
    val isInCycle = currentDayData?.type != CycleType.NONE
    val isPredicted = currentDayData?.type == CycleType.PREDICTED_PERIOD

    // 查找是否有现有的经期记录包含这个日期
    val existingCycle = historicalPeriods.find { cycle ->
        !date.isBefore(cycle.startDate) &&
        (cycle.endDate == null || !date.isAfter(cycle.endDate))
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // 美化的标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = Color(0xFFE91E63).copy(alpha = 0.1f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = null,
                            tint = Color(0xFFE91E63),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column {
                        Text(
                            text = date.format(DateTimeFormatter.ofPattern("MM月dd日")),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1F2937)
                        )
                        Text(
                            text = date.format(DateTimeFormatter.ofPattern("EEEE", java.util.Locale.CHINESE)),
                            fontSize = 14.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))
                // 美化的状态显示
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = when {
                            hasPeriodRecord -> Color(0xFFFFEBEE)
                            hasOvulationRecord -> Color(0xFFF3E5F5)
                            isPredicted -> Color(0xFFFFF3E0)
                            isInCycle -> Color(0xFFE8F5E8)
                            else -> Color(0xFFF8FAFC)
                        }
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = when {
                                hasPeriodRecord -> Icons.Default.Favorite
                                hasOvulationRecord -> Icons.Default.Star
                                isPredicted -> Icons.Default.Schedule
                                isInCycle -> Icons.Default.Circle
                                else -> Icons.Default.RadioButtonUnchecked
                            },
                            contentDescription = null,
                            tint = when {
                                hasPeriodRecord -> Color(0xFFE91E63)
                                hasOvulationRecord -> Color(0xFF9C27B0)
                                isPredicted -> Color(0xFFFF9800)
                                isInCycle -> Color(0xFF4CAF50)
                                else -> Color(0xFF9CA3AF)
                            },
                            modifier = Modifier.size(24.dp)
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column {
                            Text(
                                text = when {
                                    hasPeriodRecord -> "经期记录"
                                    hasOvulationRecord -> "排卵期"
                                    isPredicted -> "预测经期"
                                    isInCycle -> "周期中"
                                    else -> "无记录"
                                },
                                fontSize = 16.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = Color(0xFF1F2937)
                            )

                            if (existingCycle != null) {
                                Text(
                                    text = "属于 ${existingCycle.startDate.format(DateTimeFormatter.ofPattern("MM/dd"))} 开始的周期",
                                    fontSize = 12.sp,
                                    color = Color(0xFF6B7280)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // 操作按钮区域
                Text(
                    text = "选择操作",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151),
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                // 经期相关操作
                if (!hasPeriodRecord && !isInCycle) {
                    // 设置为经期开始
                    ActionButton(
                        icon = Icons.Default.PlayArrow,
                        title = "设为经期开始",
                        subtitle = "将此日期标记为经期开始日",
                        color = Color(0xFFE91E63),
                        onClick = {
                            viewModel.recordPeriodStartForDate(date)
                            viewModel.refreshCalendarViewWithDelay()
                            Toast.makeText(context, "已设置为经期开始日", Toast.LENGTH_SHORT).show()
                            onDismiss()
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (existingCycle != null && !hasPeriodRecord) {
                    // 设置为经期结束
                    ActionButton(
                        icon = Icons.Default.Stop,
                        title = "设为经期结束",
                        subtitle = "将此日期标记为经期结束日",
                        color = Color(0xFF9C27B0),
                        onClick = {
                            viewModel.recordPeriodEndForDate(date)
                            viewModel.refreshCalendarViewWithDelay()
                            Toast.makeText(context, "已设置为经期结束日", Toast.LENGTH_SHORT).show()
                            onDismiss()
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (existingCycle != null) {
                    // 编辑记录
                    ActionButton(
                        icon = Icons.Default.Edit,
                        title = "编辑记录",
                        subtitle = "修改此周期的详细信息",
                        color = Color(0xFF3B82F6),
                        onClick = {
                            // 这里可以触发编辑对话框
                            Toast.makeText(context, "编辑功能开发中", Toast.LENGTH_SHORT).show()
                            onDismiss()
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (isInCycle) {
                    // 删除记录
                    ActionButton(
                        icon = Icons.Default.Delete,
                        title = "删除记录",
                        subtitle = "删除此日期的记录",
                        color = Color(0xFFEF4444),
                        onClick = {
                            viewModel.deleteCycleForDate(date)
                            // 🔧 修复：确保数据完全刷新
                            viewModel.refreshData()
                            Toast.makeText(context, "已删除记录", Toast.LENGTH_SHORT).show()
                            onDismiss()
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 取消按钮
                OutlinedButton(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF6B7280)
                    ),
                    border = BorderStroke(1.dp, Color(0xFFE5E7EB))
                ) {
                    Text(
                        text = "取消",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}


/**
 * 操作按钮组件
 */
@Composable
fun ActionButton(
    icon: ImageVector,
    title: String,
    subtitle: String,
    color: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.08f)
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        border = BorderStroke(1.dp, color.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = color.copy(alpha = 0.15f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.size(20.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1F2937)
                )
                Text(
                    text = subtitle,
                    fontSize = 13.sp,
                    color = Color(0xFF6B7280)
                )
            }

            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = Color(0xFF9CA3AF),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * Hilt EntryPoint for accessing UserPreferencesManager in Compose
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface UserPreferencesManagerEntryPoint {
    fun userPreferencesManager(): UserPreferencesManager
}