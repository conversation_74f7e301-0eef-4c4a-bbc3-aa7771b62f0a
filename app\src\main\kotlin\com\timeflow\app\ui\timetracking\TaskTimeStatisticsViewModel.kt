package com.timeflow.app.ui.timetracking

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.data.dao.TaskTimeStats
import com.timeflow.app.data.dao.TaskTimeStatsWithTags
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

/**
 * 任务时间统计ViewModel
 * 负责管理和提供任务时间统计数据
 */
@HiltViewModel
class TaskTimeStatisticsViewModel @Inject constructor(
    private val timeSessionRepository: TimeSessionRepository
) : ViewModel() {
    
    // 选择的时间范围
    private val _selectedTimeRange = MutableStateFlow("本周")
    val selectedTimeRange: StateFlow<String> = _selectedTimeRange.asStateFlow()
    
    // 月度热力图数据
    private val _monthlyHeatmapData = MutableStateFlow(Array(31) { IntArray(24) { 0 } })
    val monthlyHeatmapData: StateFlow<Array<IntArray>> = _monthlyHeatmapData.asStateFlow()
    
    // 实时监听任务统计数据
    val uiState: StateFlow<TaskTimeStatisticsUiState> = _selectedTimeRange
        .flatMapLatest { timeRange ->
            val (startDate, endDate) = getDateRangeForSelection(timeRange)
            
            // 同时获取基础统计和包含标签的统计
            combine(
                timeSessionRepository.getTaskTimeStats(startDate, endDate),
                timeSessionRepository.getTaskTimeStatsWithTags(startDate, endDate)
            ) { basicStats, statsWithTags ->
                // 如果是本月，生成热力图数据
                if (timeRange == "本月") {
                    generateMonthlyHeatmapData()
                }
                
                TaskTimeStatisticsUiState(
                    isLoading = false,
                    taskStats = basicStats,
                    taskStatsWithTags = statsWithTags,
                    error = null
                )
            }
                .catch { exception ->
                    emit(TaskTimeStatisticsUiState(
                        isLoading = false,
                        taskStats = emptyList(),
                        taskStatsWithTags = emptyList(),
                        error = exception.message ?: "加载统计数据失败"
                    ))
                }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = TaskTimeStatisticsUiState(isLoading = true)
        )
    
    /**
     * 设置时间范围
     */
    fun setTimeRange(range: String) {
        _selectedTimeRange.value = range
    }
    
    /**
     * 手动刷新数据（保留此方法供下拉刷新等场景使用）
     */
    fun loadStatistics() {
        // 触发数据重新加载，通过改变时间范围来刷新
        val currentRange = _selectedTimeRange.value
        _selectedTimeRange.value = ""
        _selectedTimeRange.value = currentRange
    }
    
    /**
     * 根据选择生成日期范围
     */
    private fun getDateRangeForSelection(selection: String): Pair<LocalDate, LocalDate> {
        val today = LocalDate.now()
        return when (selection) {
            "今日" -> today to today
            "本周" -> {
                val startOfWeek = today.minusDays(today.dayOfWeek.value - 1L)
                startOfWeek to startOfWeek.plusDays(6)
            }
            "本月" -> {
                val startOfMonth = today.withDayOfMonth(1)
                val endOfMonth = today.withDayOfMonth(today.lengthOfMonth())
                startOfMonth to endOfMonth
            }
            "季度" -> {
                val startOfQuarter = today.withDayOfYear(1).plusMonths((today.monthValue - 1) / 3 * 3L)
                val endOfQuarter = startOfQuarter.plusMonths(2).withDayOfMonth(startOfQuarter.plusMonths(2).lengthOfMonth())
                startOfQuarter to endOfQuarter
            }
            else -> today to today
        }
    }
    
    /**
     * 生成月度热力图数据
     */
    private suspend fun generateMonthlyHeatmapData() {
        try {
            val today = LocalDate.now()
            val startOfMonth = today.withDayOfMonth(1)
            val daysInMonth = today.lengthOfMonth()
            
            // 创建新的热力图数据数组
            val heatmapData = Array(31) { IntArray(24) { 0 } }
            
            // 为每一天生成模拟数据（实际应用中应该从数据库获取）
            for (day in 1..daysInMonth) {
                for (hour in 0 until 24) {
                    // 生成模拟的活动强度数据
                    // 工作时间（9-18）有更高的活动强度
                    val intensity = when (hour) {
                        in 9..18 -> (1..10).random()
                        in 19..22 -> (1..6).random()
                        else -> (0..2).random()
                    }
                    heatmapData[day - 1][hour] = intensity
                }
            }
            
            _monthlyHeatmapData.value = heatmapData
            
        } catch (e: Exception) {
            // 生成失败时使用空数据
            _monthlyHeatmapData.value = Array(31) { IntArray(24) { 0 } }
        }
    }
}

/**
 * 任务时间统计UI状态
 */
data class TaskTimeStatisticsUiState(
    val isLoading: Boolean = false,
    val taskStats: List<TaskTimeStats> = emptyList(),
    val taskStatsWithTags: List<TaskTimeStatsWithTags> = emptyList(),
    val error: String? = null
) 