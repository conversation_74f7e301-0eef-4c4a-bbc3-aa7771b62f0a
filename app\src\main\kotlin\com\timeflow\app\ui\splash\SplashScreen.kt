package com.timeflow.app.ui.splash

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.ui.theme.MistyPurple
import com.timeflow.app.ui.theme.MistyRose
import com.timeflow.app.ui.theme.VintageWhite
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

// 启动画面配色方案 - 更加精致的莫兰迪色系
private val SplashPrimary = Color(0xFFB49EC9) // 雾霾紫
private val SplashSecondary = Color(0xFFD9C2D4) // 玫瑰灰
private val SplashBackground = Color(0xFFFAF8F5) // 温暖的米白色
private val SplashAccent = Color(0xFFE8DFF1) // 淡紫色
private val SplashText = Color(0xFF2A2A2A) // 深灰色文字
private val SplashGold = Color(0xFFFFD700) // 金色点缀
private val SplashRose = Color(0xFFE8C5C5) // 雾玫瑰色
private val SplashMint = Color(0xFFC5E8D3) // 薄荷绿

/**
 * TimeFlow 启动画面
 * 
 * 设计理念：
 * - 时间旋律：体现时间管理的核心概念
 * - 优雅渐变：使用品牌色彩的渐变效果
 * - 呼吸动画：营造生动而不急躁的视觉体验
 * - 粒子效果：增加现代感和精致度
 */
@Composable
fun TimeFlowSplashScreen(
    onSplashFinished: () -> Unit
) {
    var animationPhase by remember { mutableStateOf(0) }
    var showBrandText by remember { mutableStateOf(false) }
    var showSubtitle by remember { mutableStateOf(false) }
    var showParticles by remember { mutableStateOf(false) }
    
    // 启动动画序列 - 优化时序，更加流畅
    LaunchedEffect(Unit) {
        delay(200) // 减少初始延迟
        animationPhase = 1 // 开始logo动画
        
        delay(600) // 缩短等待时间
        showBrandText = true // 显示品牌文字
        
        delay(400) // 缩短等待时间
        showSubtitle = true // 显示副标题
        
        delay(300) // 缩短等待时间
        showParticles = true // 显示粒子效果
        
        delay(1000) // 稍微缩短展示时间
        animationPhase = 2 // 退出动画
        
        delay(500) // 缩短退出动画时间
        onSplashFinished() // 完成启动
    }
    
    // 主容器背景渐变
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            SplashBackground,
            SplashAccent.copy(alpha = 0.1f),
            SplashBackground
        )
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundGradient),
        contentAlignment = Alignment.Center
    ) {
        // 背景粒子效果
        if (showParticles) {
            BackgroundParticles()
        }
        
        // 主要内容区域
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 核心Logo动画
            TimeFlowLogo(animationPhase = animationPhase)
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 品牌文字动画
            AnimatedVisibility(
                visible = showBrandText,
                enter = fadeIn(animationSpec = tween(800)) + 
                       slideInVertically(animationSpec = tween(800)) { it / 2 }
            ) {
                BrandTextSection()
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 副标题动画
            AnimatedVisibility(
                visible = showSubtitle,
                enter = fadeIn(animationSpec = tween(600, delayMillis = 200))
            ) {
                SubtitleSection()
            }
        }
        
        // 底部加载指示器
        if (animationPhase < 2) {
            BottomLoadingIndicator(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 60.dp)
            )
        }
    }
}

/**
 * TimeFlow 核心Logo
 */
@Composable
private fun TimeFlowLogo(animationPhase: Int) {
    val scale by animateFloatAsState(
        targetValue = when (animationPhase) {
            0 -> 0.3f
            1 -> 1f
            else -> 1.1f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "logo_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (animationPhase == 2) 0f else 1f,
        animationSpec = tween(600),
        label = "logo_alpha"
    )
    
    Box(
        modifier = Modifier
            .size(120.dp)
            .scale(scale)
            .alpha(alpha),
        contentAlignment = Alignment.Center
    ) {
        // 外围光环效果
        TimeRippleEffect()
        
        // 中心时钟图标
        CentralClockIcon()
        
        // 围绕的时间粒子
        OrbitingTimeParticles()
    }
}

/**
 * 时间波纹效果
 */
@Composable
private fun TimeRippleEffect() {
    val infiniteTransition = rememberInfiniteTransition(label = "ripple")
    
    repeat(3) { index ->
        val scale by infiniteTransition.animateFloat(
            initialValue = 0.5f,
            targetValue = 2f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = EaseOut),
                repeatMode = RepeatMode.Restart,
                initialStartOffset = StartOffset(index * 600)
            ),
            label = "ripple_scale_$index"
        )
        
        val alpha by infiniteTransition.animateFloat(
            initialValue = 0.6f,
            targetValue = 0f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = EaseOut),
                repeatMode = RepeatMode.Restart,
                initialStartOffset = StartOffset(index * 600)
            ),
            label = "ripple_alpha_$index"
        )
        
        Box(
            modifier = Modifier
                .size(60.dp)
                .scale(scale)
                .alpha(alpha)
                .border(
                    width = 2.dp,
                    color = SplashPrimary,
                    shape = CircleShape
                )
        )
    }
}

/**
 * 中心时钟图标
 */
@Composable
private fun CentralClockIcon() {
    val rotation by rememberInfiniteTransition(label = "clock_rotation").animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "clock_hand_rotation"
    )
    
    Box(
        modifier = Modifier
            .size(80.dp)
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        SplashPrimary,
                        SplashSecondary
                    )
                ),
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        // 时钟外圈
        Box(
            modifier = Modifier
                .size(70.dp)
                .border(3.dp, Color.White.copy(alpha = 0.3f), CircleShape)
        )
        
        // 时钟指针
        Box(
            modifier = Modifier
                .size(40.dp)
                .rotate(rotation),
            contentAlignment = Alignment.Center
        ) {
            // 时针
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(20.dp)
                    .background(Color.White, RoundedCornerShape(1.dp))
                    .offset(y = (-5).dp)
            )
            
            // 分针
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .height(25.dp)
                    .background(Color.White, RoundedCornerShape(0.5.dp))
                    .offset(y = (-7).dp)
                    .rotate(90f)
            )
        }
        
        // 中心点
        Box(
            modifier = Modifier
                .size(6.dp)
                .background(Color.White, CircleShape)
        )
    }
}

/**
 * 环绕的时间粒子
 */
@Composable
private fun OrbitingTimeParticles() {
    val infiniteTransition = rememberInfiniteTransition(label = "orbiting")
    
    repeat(6) { index ->
        val rotation by infiniteTransition.animateFloat(
            initialValue = index * 60f,
            targetValue = index * 60f + 360f,
            animationSpec = infiniteRepeatable(
                animation = tween(3000 + index * 200, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "particle_orbit_$index"
        )
        
        val radius = 50.dp
        val density = LocalDensity.current
        val radiusPx = with(density) { radius.toPx() }
        
        Box(
            modifier = Modifier
                .size(8.dp)
                .graphicsLayer {
                    val angle = rotation * PI / 180
                    translationX = cos(angle).toFloat() * radiusPx
                    translationY = sin(angle).toFloat() * radiusPx
                }
                .background(
                    SplashGold.copy(alpha = 0.8f),
                    CircleShape
                )
        )
    }
}

/**
 * 品牌文字区域
 */
@Composable
private fun BrandTextSection() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "TimeFlow",
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold,
            color = SplashText,
            letterSpacing = 2.sp,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 装饰线条
        Box(
            modifier = Modifier
                .width(60.dp)
                .height(2.dp)
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            SplashPrimary,
                            Color.Transparent
                        )
                    ),
                    shape = RoundedCornerShape(1.dp)
                )
        )
    }
}

/**
 * 副标题区域
 */
@Composable
private fun SubtitleSection() {
    Text(
        text = "时间旋律，生活韵律",
        fontSize = 16.sp,
        fontWeight = FontWeight.Medium,
        color = SplashText.copy(alpha = 0.7f),
        letterSpacing = 1.sp,
        textAlign = TextAlign.Center
    )
}

/**
 * 底部加载指示器
 */
@Composable
private fun BottomLoadingIndicator(
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "loading")
    
    val alpha1 by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "loading_alpha_1"
    )
    
    val alpha2 by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse,
            initialStartOffset = StartOffset(200)
        ),
        label = "loading_alpha_2"
    )
    
    val alpha3 by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse,
            initialStartOffset = StartOffset(400)
        ),
        label = "loading_alpha_3"
    )
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(3) { index ->
            val alpha = when (index) {
                0 -> alpha1
                1 -> alpha2
                else -> alpha3
            }
            
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .alpha(alpha)
                    .background(
                        SplashPrimary,
                        CircleShape
                    )
            )
        }
    }
}

/**
 * 背景粒子效果 - 增强版本，更多粒子和颜色变化
 */
@Composable
private fun BackgroundParticles() {
    val particles = remember {
        List(20) { index ->
            ParticleData(
                x = Random.nextFloat(),
                y = Random.nextFloat(),
                speed = Random.nextFloat() * 0.4f + 0.1f,
                size = Random.nextFloat() * 6f + 2f,
                alpha = Random.nextFloat() * 0.5f + 0.1f,
                colorIndex = index % 4 // 添加颜色索引
            )
        }
    }
    
    particles.forEach { particle ->
        val infiniteTransition = rememberInfiniteTransition(label = "particle_${particle.hashCode()}")
        
        val animatedY by infiniteTransition.animateFloat(
            initialValue = particle.y,
            targetValue = particle.y - 1.2f,
            animationSpec = infiniteRepeatable(
                animation = tween((8000 / particle.speed).toInt(), easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "particle_y_${particle.hashCode()}"
        )
        
        val animatedAlpha by infiniteTransition.animateFloat(
            initialValue = particle.alpha,
            targetValue = 0f,
            animationSpec = infiniteRepeatable(
                animation = tween((4000 / particle.speed).toInt(), easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "particle_alpha_${particle.hashCode()}"
        )
        
                    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
                val x = particle.x * constraints.maxWidth
                val y = (animatedY % 1.2f) * constraints.maxHeight
                
                // 根据颜色索引选择不同颜色
                val particleColor = when (particle.colorIndex) {
                    0 -> SplashPrimary
                    1 -> SplashSecondary
                    2 -> SplashRose
                    3 -> SplashMint
                    else -> SplashPrimary
                }
                
                Box(
                    modifier = Modifier
                        .offset(x = x.dp, y = y.dp)
                        .size(particle.size.dp)
                        .alpha(animatedAlpha)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    particleColor.copy(alpha = 0.8f),
                                    particleColor.copy(alpha = 0.2f)
                                )
                            ),
                            shape = CircleShape
                        )
                )
            }
    }
}

/**
 * 粒子数据类 - 增强版本，支持多种颜色
 */
private data class ParticleData(
    val x: Float,
    val y: Float,
    val speed: Float,
    val size: Float,
    val alpha: Float,
    val colorIndex: Int = 0 // 颜色索引
) 