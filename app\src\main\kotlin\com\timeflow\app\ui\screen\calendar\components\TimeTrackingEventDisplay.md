# 时间追踪事件显示逻辑

## 功能概述

在日历日视图的右侧区域显示时间追踪的实际用时，通过事件块的长度直观表示时间长度。

## 显示规则

### 1. 事件块长度
- 事件块的长度准确反映实际时间长度
- 最小可见宽度为3%，确保短时间事件也能看到

### 2. 文字显示规则

#### 短时间事件（< 60分钟）
- **< 10分钟**：不显示任何文字，只用颜色块表示
- **10-15分钟**：不显示文字，只用颜色块表示
- **15-20分钟**：显示任务名称（小字体）
- **≥ 20分钟**：显示任务名称（正常字体）

#### 长时间事件（≥ 60分钟）
- **跨小时显示**：只在持续时间最长的小时块中显示任务标题
- **标题位置**：选择跨越时间最长的小时块来显示标题（不显示总用时）
- **其他小时**：只显示颜色块，不重复显示标题

### 3. 颜色区分
- **普通计时**：蓝绿色 (#26A69A)
- **番茄钟**：绿色 (#4CAF50)
- **专注度评分**：影响透明度（1-5星对应50%-90%透明度）

## 示例场景

### 场景1：短时间任务（25分钟）
```
09:00-09:25 番茄钟学习
├─ 09:00-10:00 时间块：显示"番茄钟学习"
└─ 颜色：绿色，长度占25%
```

### 场景2：长时间任务（2小时15分钟）
```
09:30-11:45 深度工作
├─ 09:00-10:00 时间块：只显示颜色块（30分钟）
├─ 10:00-11:00 时间块：显示"深度工作"（60分钟，最长）
└─ 11:00-12:00 时间块：只显示颜色块（45分钟）
```

### 场景4：另一个长时间任务示例
```
14:10-16:20 编程开发
├─ 14:00-15:00 时间块：只显示颜色块（50分钟）
├─ 15:00-16:00 时间块：显示"编程开发"（60分钟，最长）
└─ 16:00-17:00 时间块：只显示颜色块（20分钟）
```

### 场景3：极短时间任务（5分钟）
```
14:10-14:15 快速回复邮件
└─ 14:00-15:00 时间块：只显示颜色块，无任何文字
```

## 技术实现

### 关键逻辑
```kotlin
val shouldShowTitle = if (event.duration >= 3600) { // 60分钟 = 3600秒
    // 长时间事件：在最长的时间块中显示标题
    val longestHourBlock = findLongestHourBlockForEvent(event)
    hour == longestHourBlock
} else {
    // 短时间事件：正常显示
    !isEventTooShort
}
```

### 最长时间块查找算法
```kotlin
private fun findLongestHourBlockForEvent(event: TimeTrackingEvent): Int {
    // 计算每个小时块的持续时间
    // 返回持续时间最长的小时块
    // 处理跨午夜的特殊情况
}
```

### 位置计算
```kotlin
val startOffset = startMinuteInHour / 60f
val actualWidthFraction = durationInHour / 60f
val widthFraction = if (actualWidthFraction < 0.03f) 0.03f else actualWidthFraction
```

## 用户体验

### 优势
1. **直观性**：事件块长度直接反映时间长度
2. **简洁性**：避免重复显示标题，减少视觉干扰
3. **一致性**：短时间和长时间事件有统一的显示逻辑

### 设计原则
1. **信息密度**：在有限空间内最大化信息展示
2. **视觉层次**：通过颜色和大小区分不同类型的事件
3. **可读性**：确保重要信息清晰可见，避免冗余

## 交互功能

### 点击查看详情
用户可以点击右侧的时间追踪事件块来查看详细信息：

#### 详情弹窗内容
- **任务名称**：显示完整的任务名称
- **计时类型**：普通计时 ⏱️ 或番茄钟 🍅
- **开始时间**：实际开始时间（HH:mm格式）
- **结束时间**：实际结束时间（HH:mm格式）
- **用时**：格式化的持续时间（如：2h15m）
- **专注度评分**：1-5星评分（如果有）
- **生产力评分**：1-5星评分（如果有）

#### 示例弹窗
```
时间追踪详情
─────────────────
任务名称        深度学习
计时类型        🍅 番茄钟
开始时间        14:30
结束时间        16:45
用时           2h15m
专注度         ★★★★☆ (4/5)
生产力         ★★★★★ (5/5)
```

### 交互设计
- **点击区域**：整个事件块都可以点击
- **视觉反馈**：点击时显示详情弹窗
- **关闭方式**：点击"确定"按钮或弹窗外部区域

## 数据流程

### 事件点击流程
1. 用户点击时间追踪事件块
2. 触发 `onTimeTrackingEventClick` 回调
3. 设置选中的事件和弹窗状态
4. 显示 `TimeTrackingEventDetailDialog`
5. 用户查看详情并关闭弹窗

### 数据传递
```kotlin
TimeTrackingEvent -> TimeTrackingEventCard -> onClick ->
ModernDayView -> showTimeTrackingDialog -> TimeTrackingEventDetailDialog
```
