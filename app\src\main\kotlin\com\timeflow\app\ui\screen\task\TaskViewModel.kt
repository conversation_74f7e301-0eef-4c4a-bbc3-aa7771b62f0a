package com.timeflow.app.ui.screen.task

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.UserPreferenceRepository
import androidx.work.WorkManager
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskType
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import com.timeflow.app.data.model.TaskTag
import java.util.UUID
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel
import com.timeflow.app.ui.screen.SharedFilterState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent
import kotlinx.coroutines.delay
import com.timeflow.app.ui.screen.task.model.FeedbackData
import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import com.timeflow.app.ui.screen.reflection.ReflectionCreatedEvent
import com.timeflow.app.data.model.MoodType
import java.time.Instant
import java.time.ZoneId
import androidx.compose.ui.graphics.Color
import java.time.ZoneOffset
import android.util.Log


/**
 * 任务详情ViewModel - 专注于单个任务的详情管理
 *
 * 此ViewModel负责处理单个任务的详细信息，包括：
 * - 加载和显示任务详情
 * - 管理任务的子任务列表
 * - 处理任务时间、优先级等属性的更新
 * - 与任务详情底部弹出层(TaskDetailBottomSheet)交互
 * 
 * 不同于TaskListViewModel，此ViewModel专注于单个任务的操作，
 * 而不是整个任务列表的管理。它被设计为轻量级，只包含单任务视图
 * 所需的逻辑和状态。
 *
 * @property taskRepository 任务数据仓库，用于获取和更新任务数据
 * @property reflectionRepository 反思数据仓库，用于处理任务相关的反思
 */
@HiltViewModel
class TaskViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val reflectionRepository: ReflectionRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "TaskViewModel"
        private const val DEFAULT_TTL_MS = 30_000L // 默认缓存30秒
    }
    
    private val _uiState = MutableStateFlow<TaskUiState>(TaskUiState.Loading)
    val uiState: StateFlow<TaskUiState> = _uiState.asStateFlow()

    private var currentTask: TaskData? = null

    // 添加任务刷新事件接收器
    private val _refreshTaskEvent = MutableSharedFlow<String>()
    val refreshTaskEvent: SharedFlow<String> = _refreshTaskEvent.asSharedFlow()

    init {
        // 监听任务刷新事件，添加去抖动逻辑
        viewModelScope.launch {
            var lastTaskId = ""
            var lastRefreshTime = 0L
            
            refreshTaskEvent.collect { taskId ->
                val currentTime = System.currentTimeMillis()
                // 只有当任务ID不同，或者与上次刷新间隔超过500毫秒时才刷新
                if (taskId != lastTaskId || currentTime - lastRefreshTime > 500) {
                    android.util.Log.d(TAG, "收到任务刷新事件: $taskId")
                    lastTaskId = taskId
                    lastRefreshTime = currentTime
                    loadTask(taskId)
                } else {
                    android.util.Log.d(TAG, "忽略过于频繁的刷新请求: $taskId")
                }
            }
        }
        
        // 订阅全局任务刷新事件，同样添加去抖动逻辑
        viewModelScope.launch {
            var lastTaskId = ""
            var lastRefreshTime = 0L
            
            NotificationCenter.events.collect { event ->
                if (event is TaskRefreshEvent) {
                    val taskId = event.taskId
                    val currentTime = System.currentTimeMillis()
                    // 只有当任务ID不同，或者与上次刷新间隔超过500毫秒时才刷新
                    if (taskId != lastTaskId || currentTime - lastRefreshTime > 500) {
                        android.util.Log.d(TAG, "收到全局任务刷新事件: $taskId")
                        lastTaskId = taskId
                        lastRefreshTime = currentTime
                        loadTask(taskId)
                    } else {
                        android.util.Log.d(TAG, "忽略过于频繁的全局刷新请求: $taskId")
                    }
                }
            }
        }
    }
    
    // 通过此方法接收来自AiAssistantViewModel的刷新请求
    fun refreshTask(taskId: String) {
        viewModelScope.launch {
            _refreshTaskEvent.emit(taskId)
        }
    }

    fun loadTask(taskId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = TaskUiState.Loading
                
                // 添加日志记录当前时间，用于调试子任务加载时序问题
                val startTime = System.currentTimeMillis()
                android.util.Log.d("TaskViewModel", "开始加载任务: $taskId (时间戳: $startTime)")
                
                // 获取当前任务的UI状态，用于子任务缓存比较
                val currentState = _uiState.value
                val cachedSubTasks = if (currentState is TaskUiState.Success) {
                    currentState.task.subTasks
                } else {
                    emptyList()
                }
                android.util.Log.d("TaskViewModel", "当前UI状态缓存的子任务数量: ${cachedSubTasks.size}")
                
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    android.util.Log.d("TaskViewModel", "加载任务成功: ${task.id}, 标题: ${task.title}, hasSubtasks=${task.hasSubtasks}, 任务类型=${task.type}, AI生成=${task.aiGenerated}")
                    
                    // 添加短暂延迟，确保所有子任务保存操作都已完成
                    delay(50)
                    
                    // 获取子任务并添加详细日志
                    var subTasks = taskRepository.getSubTasks(task.id)
                    val endTime = System.currentTimeMillis()
                    android.util.Log.d("TaskViewModel", "获取到 ${subTasks.size} 个子任务 (耗时: ${endTime - startTime}ms)")
                    
                    // 防御性代码：如果任务应该有子任务但返回为空，尝试二次查询
                    if (subTasks.isEmpty() && (task.hasSubtasks == true || task.type == TaskType.AI || task.aiGenerated == true)) {
                        android.util.Log.w("TaskViewModel", "任务应该有子任务但查询结果为空，尝试第二次查询")
                        // 增加延迟，等待可能的数据库操作完成
                        delay(100)
                        subTasks = taskRepository.getSubTasks(task.id)
                        android.util.Log.d("TaskViewModel", "第二次查询结果: ${subTasks.size} 个子任务")
                    }
                    
                    // 如果第二次查询仍然为空，但缓存中有子任务数据，使用缓存数据
                    if (subTasks.isEmpty() && cachedSubTasks.isNotEmpty() && task.hasSubtasks == true) {
                        android.util.Log.w("TaskViewModel", "数据库查询无子任务但缓存中有 ${cachedSubTasks.size} 个子任务，使用缓存数据")
                        
                        // 将UI子任务模型转回数据模型
                        subTasks = cachedSubTasks.map { subTask ->
                            Task(
                                id = subTask.id,
                                title = subTask.title,
                                description = subTask.note,
                                dueDate = subTask.dueDate,
                                completedAt = if (subTask.isCompleted) LocalDateTime.now() else null,
                                priority = subTask.priority,
                                parentTaskId = task.id,
                                createdAt = LocalDateTime.now(),
                                updatedAt = LocalDateTime.now()
                            )
                        }
                        
                        // 记录使用缓存数据
                        android.util.Log.d("TaskViewModel", "使用缓存数据: ${subTasks.size} 个子任务")
                    }
                    
                    // 强制设置任务的hasSubtasks标志
                    if (subTasks.isNotEmpty() && !task.hasSubtasks) {
                        android.util.Log.d("TaskViewModel", "检测到子任务但hasSubtasks标志为false，正在更新标志")
                        try {
                            val updatedTask = task.copy(hasSubtasks = true)
                            taskRepository.updateTask(updatedTask)
                            android.util.Log.d("TaskViewModel", "已更新任务hasSubtasks标志为true: ${updatedTask.id}")
                        } catch (e: Exception) {
                            android.util.Log.e("TaskViewModel", "更新hasSubtasks标志失败", e)
                        }
                    } else if (subTasks.isEmpty() && task.hasSubtasks) {
                        android.util.Log.w("TaskViewModel", "任务标记为hasSubtasks=true但未能加载到子任务，这可能是一个数据不一致问题")
                    }
                    
                    // 记录子任务详情
                    if (subTasks.isNotEmpty()) {
                        android.util.Log.d("TaskViewModel", "任务有${subTasks.size}个子任务，详细信息如下:")
                        subTasks.forEachIndexed { index, subTask ->
                            android.util.Log.d("TaskViewModel", "子任务${index + 1}: id=${subTask.id}, title=${subTask.title}, completed=${subTask.completedAt != null}")
                        }
                    }
                    
                    // 更新UI状态
                    _uiState.value = TaskUiState.Success(
                        TaskData(
                            id = task.id,
                            title = task.title,
                            description = task.description,
                            dueDate = task.dueDate,
                            priority = task.priority ?: Priority.MEDIUM,
                            status = task.status,
                            tags = task.tags?.map { it.name } ?: emptyList(),
                            daysLeft = calculateDaysLeft(task.dueDate),
                            subTasks = subTasks.map { subTask ->
                                SubTask(
                                    id = subTask.id,
                                    title = subTask.title,
                                    note = subTask.description,
                                    isCompleted = subTask.completedAt != null,
                                    parentTaskId = subTask.parentTaskId,
                                    priority = subTask.priority ?: Priority.MEDIUM,
                                    dueDate = subTask.dueDate
                                )
                            }
                        )
                    )
                } else {
                    android.util.Log.w("TaskViewModel", "未找到任务: $taskId")
                    _uiState.value = TaskUiState.Error("未找到任务")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "加载任务失败", e)
                _uiState.value = TaskUiState.Error(e.message ?: "加载任务失败")
            }
        }
    }
    
    fun toggleSubTaskCompletion(subTaskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                if (currentState is TaskUiState.Success) {
                    val task = currentState.task
                    val subTask = task.subTasks.find { it.id == subTaskId }
                    
                    if (subTask != null) {
                        // 更新子任务完成状态
                        val updatedSubTask = Task(
                            id = subTask.id,
                            title = subTask.title,
                            description = subTask.note,
                            dueDate = subTask.dueDate,
                            completedAt = if (isCompleted) LocalDateTime.now() else null,
                            priority = subTask.priority,
                            parentTaskId = task.id,
                            createdAt = LocalDateTime.now(),
                            updatedAt = LocalDateTime.now()
                        )
                        
                        taskRepository.updateTask(updatedSubTask)
                        
                        // 更新UI状态
                        val updatedSubTasks = task.subTasks.map { st ->
                            if (st.id == subTaskId) {
                                st.copy(isCompleted = isCompleted)
                            } else {
                                st
                            }
                        }
                        
                        _uiState.value = TaskUiState.Success(
                            task.copy(subTasks = updatedSubTasks)
                        )
                        
                        // 发送任务刷新事件
                        NotificationCenter.post(TaskRefreshEvent(task.id))
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "更新子任务状态失败", e)
            }
        }
    }

    /**
     * 获取指定ID的任务，通过回调返回结果
     * 用于支持UI组件直接调用获取任务数据
     *
     * @param taskId 任务ID
     * @param callback 获取到任务数据后的回调函数
     */
    fun getTaskById(taskId: String, callback: (Task?) -> Unit) {
        viewModelScope.launch {
            try {
                android.util.Log.d(TAG, "开始通过回调获取任务: $taskId")
                val task = taskRepository.getTaskById(taskId)
                callback(task)
                android.util.Log.d(TAG, "通过回调返回任务: ${task?.id}, 是否为空: ${task == null}")
            } catch (e: Exception) {
                android.util.Log.e(TAG, "获取任务失败: $taskId", e)
                callback(null)
            }
        }
    }

    private fun mapTaskToTaskData(task: Task): TaskData {
        return TaskData(
            id = task.id,
            title = task.title,
            description = task.description ?: "",
            dueDate = task.dueDate,
            priority = task.priority ?: Priority.MEDIUM,
            status = task.status ?: "待办",
            tags = task.tags?.map { it.name } ?: emptyList(),
            daysLeft = calculateDaysLeft(task.dueDate),
            subTasks = emptyList() // 暂时返回空列表，后续再处理子任务
        )
    }

    private fun mapTaskToSubTask(task: Task): SubTask {
        return SubTask(
            id = task.id,
            title = task.title,
            isCompleted = task.completedAt != null,
            priority = task.priority ?: Priority.MEDIUM,
            reminderTime = task.dueDate,
            note = task.description ?: "",
            parentTaskId = task.parentTaskId ?: "",
            dueDate = task.dueDate
        )
    }

    /**
     * 用于外部组件主动刷新子任务数据，不依赖于loadTask流程
     * 主要用于TaskDetailBottomSheet中刷新子任务列表
     */
    suspend fun getSubTasksForTask(taskId: String): List<SubTask>? {
        android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 开始获取任务的子任务: $taskId")
        return try {
            val startTime = System.currentTimeMillis()
            
            // 检查父任务hasSubTasks标志
            val parentTask = taskRepository.getTaskById(taskId)
            android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 父任务信息: hasSubtasks=${parentTask?.hasSubtasks}, type=${parentTask?.type}, aiGenerated=${parentTask?.aiGenerated}")
            
            // 从仓库获取子任务列表
            var subTasks = taskRepository.getSubTasks(taskId)
            val initialQueryTime = System.currentTimeMillis()
            android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 初次查询: ${subTasks.size}个子任务 (耗时: ${initialQueryTime - startTime}ms)")
            
            // 检查是否需要重试
            val needRetry = subTasks.isEmpty() && 
                            (parentTask?.hasSubtasks == true || 
                             parentTask?.type == TaskType.AI || 
                             parentTask?.aiGenerated == true)
            
            // 如果预期有子任务但查询为空，尝试延迟重试
            if (needRetry) {
                android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 预期有子任务但首次查询为空，延迟200ms后重试")
                delay(200) // 增加延迟，等待可能的数据库操作完成
                subTasks = taskRepository.getSubTasks(taskId)
                val retryTime = System.currentTimeMillis()
                android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 重试查询结果: ${subTasks.size}个子任务 (耗时: ${retryTime - initialQueryTime}ms)")
                
                // 二次重试，有些AI生成操作可能需要更长时间
                if (subTasks.isEmpty() && parentTask?.aiGenerated == true) {
                    android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] AI生成任务二次查询仍为空，延迟400ms后最终重试")
                    delay(400)
                    subTasks = taskRepository.getSubTasks(taskId)
                    android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 最终重试查询结果: ${subTasks.size}个子任务")
                }
            }
            
            // 记录详细日志
            if (subTasks.isNotEmpty()) {
                android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 子任务详情:")
                subTasks.forEachIndexed { index, task ->
                    android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 子任务[$index]: id=${task.id}, title=${task.title}, parentId=${task.parentTaskId}")
                }
                
                // 如果父任务hasSubtasks标志不正确，尝试修正
                if (parentTask != null && !parentTask.hasSubtasks) {
                    android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 修正父任务hasSubtasks标志")
                    try {
                        val updatedTask = parentTask.copy(hasSubtasks = true)
                        taskRepository.updateTask(updatedTask)
                    } catch (e: Exception) {
                        android.util.Log.e("TaskViewModel_Trace", "[getSubTasksForTask] 更新父任务标志失败", e)
                    }
                }
            } else {
                android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 警告: 未找到子任务")
            }
            
            // 映射子任务到UI模型
            val mappedSubTasks = subTasks.map { mapTaskToSubTask(it) }
            val endTime = System.currentTimeMillis()
            android.util.Log.d("TaskViewModel_Trace", "[getSubTasksForTask] 完成映射，返回 ${mappedSubTasks.size} 个子任务 (总耗时: ${endTime - startTime}ms)")
            
            mappedSubTasks
        } catch (e: Exception) {
            android.util.Log.e("TaskViewModel_Trace", "[getSubTasksForTask] 获取子任务失败", e)
            null
        }
    }

    private fun mapPriorityToUrgency(priority: Priority): TaskUrgency {
        return when (priority) {
            Priority.HIGH -> TaskUrgency.HIGH
            Priority.MEDIUM -> TaskUrgency.MEDIUM
            Priority.LOW -> TaskUrgency.LOW
            Priority.URGENT -> TaskUrgency.CRITICAL
        }
    }

    private fun mapUrgencyToPriority(urgency: TaskUrgency): Priority {
        return when (urgency) {
            TaskUrgency.HIGH -> Priority.HIGH
            TaskUrgency.MEDIUM -> Priority.MEDIUM
            TaskUrgency.LOW -> Priority.LOW
            TaskUrgency.CRITICAL -> Priority.URGENT
        }
    }

    private fun calculateDaysLeft(dueDate: LocalDateTime?): Int {
        if (dueDate == null) return 0
        return ChronoUnit.DAYS.between(LocalDateTime.now(), dueDate).toInt()
    }

    fun updateTaskPriority(taskId: String, priority: Priority) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val updatedTask = task.copy(priority = priority)
                    taskRepository.updateTask(updatedTask)
                    loadTask(taskId) // 重新加载任务以更新UI
                }
            } catch (e: Exception) {
                _uiState.value = TaskUiState.Error(e.message ?: "更新任务优先级失败")
            }
        }
    }

    fun markTaskCompleted(taskId: String) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val updatedTask = task.copy(
                        completedAt = LocalDateTime.now(),
                        status = "已完成"
                    )
                    taskRepository.updateTask(updatedTask)
                    loadTask(taskId) // 重新加载任务以更新UI
                }
            } catch (e: Exception) {
                _uiState.value = TaskUiState.Error(e.message ?: "更新任务状态失败")
            }
        }
    }

    fun updateTaskTime(taskId: String, startTime: LocalDateTime?, endTime: LocalDateTime?) {
        viewModelScope.launch {
            try {
                // 添加详细日志，记录任务ID和时间
                Log.d("TimeFormatting", "TaskViewModel更新任务时间: taskId=$taskId, startTime=$startTime, endTime=$endTime")
                
                // 特殊情况：如果startTime是MIN值，表示清除时间
                if (startTime != null && startTime == LocalDateTime.MIN) {
                    Log.d("TimeFormatting", "清除任务时间: taskId=$taskId")
                    val task = taskRepository.getTaskById(taskId)
                    if (task != null) {
                        val updatedTask = task.copy(
                            dueDate = null,
                            updatedAt = LocalDateTime.now()
                        )
                        taskRepository.updateTask(updatedTask)
                        
                        // 发送全局刷新事件
                        NotificationCenter.post(TaskRefreshEvent(taskId))
                        
                        // 重新加载任务以更新UI
                        loadTask(taskId)
                    }
                    return@launch
                }
                
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    // 记录任务的当前状态
                    Log.d("TimeFormatting", "当前任务状态: taskId=$taskId, 现有dueDate=${task.dueDate}")
                    
                    // 创建更新后的任务
                    val updatedTask = task.copy(
                        dueDate = startTime,
                        // 如果有需要，这里可以保存endTime到另一个字段
                        updatedAt = LocalDateTime.now()
                    )
                    
                    // 保存到数据库
                    taskRepository.updateTask(updatedTask)
                    Log.d("TimeFormatting", "任务时间已更新: taskId=$taskId, 新dueDate=$startTime")
                    
                    // 发送全局刷新事件，确保所有相关页面都能更新
                    NotificationCenter.post(TaskRefreshEvent(taskId))
                    
                    // 重新加载任务以更新UI
                    loadTask(taskId)
                } else {
                    Log.e("TimeFormatting", "更新任务时间失败，找不到任务: $taskId")
                    _uiState.value = TaskUiState.Error("找不到任务")
                }
            } catch (e: Exception) {
                Log.e("TimeFormatting", "更新任务时间异常: ${e.message}", e)
                _uiState.value = TaskUiState.Error(e.message ?: "更新任务时间失败")
            }
        }
    }

    fun updateTaskTags(taskId: String, tags: List<String>) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val updatedTask = task.copy(tags = tags.map { TaskTag(it, "#808080") })
                    taskRepository.updateTask(updatedTask)
                    loadTask(taskId) // 重新加载任务以更新UI
                }
            } catch (e: Exception) {
                _uiState.value = TaskUiState.Error(e.message ?: "更新任务标签失败")
            }
        }
    }

    fun addSubTask(parentId: String, subTask: TaskData) {
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskViewModel", "开始添加子任务到父任务: $parentId")
                
                // 1. 先检查是否已存在相同标题的子任务
                val existingSubTasks = taskRepository.getSubTasks(parentId)
                val hasDuplicate = existingSubTasks.any { 
                    it.title.equals(subTask.title, ignoreCase = true) 
                }
                
                if (hasDuplicate) {
                    android.util.Log.w("TaskViewModel", "发现重复子任务，标题: ${subTask.title}")
                    _uiState.value = TaskUiState.Error("子任务「${subTask.title}」已存在，不能重复添加")
                    return@launch
                }
                
                val parentTask = taskRepository.getTaskById(parentId)
                if (parentTask != null) {
                    android.util.Log.d("TaskViewModel", "找到父任务: ${parentTask.title}")
                    
                    // 创建新的子任务
                    val newTask = Task(
                        id = UUID.randomUUID().toString(),
                        title = subTask.title,
                        description = subTask.description,
                        dueDate = subTask.dueDate,
                        priority = subTask.priority,
                        status = subTask.status,
                        parentTaskId = parentId, // 确保正确设置父任务ID
                        depth = (parentTask.depth ?: 0) + 1,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    )
                    
                    // 保存子任务
                    taskRepository.saveTask(newTask)
                    android.util.Log.d("TaskViewModel", "子任务保存成功: ${newTask.id}")
                    
                    // 更新父任务的hasSubtasks标志
                    if (parentTask.hasSubtasks != true) {
                        val updatedParentTask = parentTask.copy(hasSubtasks = true)
                        taskRepository.updateTask(updatedParentTask)
                        android.util.Log.d("TaskViewModel", "已更新父任务hasSubtasks标志: ${updatedParentTask.id}")
                    }
                    
                    // 添加延迟，确保数据库操作完成
                    delay(100)
                    
                    // 发送全局刷新事件，确保所有使用该任务的页面都能更新
                    NotificationCenter.post(TaskRefreshEvent(parentId))
                    android.util.Log.d("TaskViewModel", "已发送全局刷新事件: taskId=$parentId")
                    
                    // 重新加载父任务以更新UI
                    loadTask(parentId)
                } else {
                    android.util.Log.e("TaskViewModel", "找不到父任务: $parentId")
                    _uiState.value = TaskUiState.Error("找不到父任务")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "添加子任务失败", e)
                _uiState.value = TaskUiState.Error(e.message ?: "添加子任务失败")
            }
        }
    }

    fun updateSubTask(parentId: String, subTask: TaskData) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(subTask.id)
                if (task != null) {
                    val updatedTask = task.copy(
                        title = subTask.title,
                        description = subTask.description,
                        dueDate = subTask.dueDate,
                        priority = subTask.priority,
                        status = subTask.status,
                        updatedAt = LocalDateTime.now()
                    )
                    taskRepository.updateTask(updatedTask)
                    
                    // 添加延迟，确保数据库操作完成
                    delay(100)
                    
                    // 发送全局刷新事件，确保所有使用该任务的页面都能更新
                    NotificationCenter.post(TaskRefreshEvent(parentId))
                    android.util.Log.d("TaskViewModel", "已发送更新子任务的全局刷新事件: taskId=$parentId")
                    
                    loadTask(parentId) // 重新加载父任务以更新UI
                }
            } catch (e: Exception) {
                _uiState.value = TaskUiState.Error(e.message ?: "更新子任务失败")
            }
        }
    }

    fun deleteSubTask(parentId: String, subTaskId: String) {
        viewModelScope.launch {
            try {
                // 使用正确的删除方法
                taskRepository.deleteTask(subTaskId)
                android.util.Log.d("TaskViewModel", "删除子任务: $subTaskId")
                
                // 添加延迟，确保数据库操作完成
                delay(100)
                
                // 发送全局刷新事件，确保所有使用该任务的页面都能更新
                NotificationCenter.post(TaskRefreshEvent(parentId))
                android.util.Log.d("TaskViewModel", "已发送删除子任务的全局刷新事件: taskId=$parentId")
                
                loadTask(parentId) // 重新加载父任务以更新UI
            } catch (e: Exception) {
                _uiState.value = TaskUiState.Error(e.message ?: "删除子任务失败")
            }
        }
    }

    /**
     * 添加简单子任务（只需提供标题）
     * @param parentId 父任务ID
     * @param title 子任务标题
     */
    fun addSimpleSubTask(parentId: String, title: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskViewModel", "开始添加简单子任务到父任务: $parentId, 标题: $title")
                
                // 1. 先检查是否已存在相同标题的子任务
                val existingSubTasks = taskRepository.getSubTasks(parentId)
                val hasDuplicate = existingSubTasks.any { 
                    it.title.equals(title, ignoreCase = true) 
                }
                
                if (hasDuplicate) {
                    android.util.Log.w("TaskViewModel", "发现重复子任务，标题: $title")
                    _uiState.value = TaskUiState.Error("子任务「$title」已存在，不能重复添加")
                    return@launch
                }
                
                val parentTask = taskRepository.getTaskById(parentId)
                if (parentTask != null) {
                    android.util.Log.d("TaskViewModel", "找到父任务: ${parentTask.title}")
                    
                    // 创建新的子任务
                    val newTask = Task(
                        id = UUID.randomUUID().toString(),
                        title = title,
                        description = "",
                        dueDate = parentTask.dueDate, // 继承父任务的截止日期
                        priority = parentTask.priority ?: Priority.MEDIUM, // 继承父任务的优先级
                        status = "待办",
                        parentTaskId = parentId, // 确保正确设置父任务ID
                        depth = (parentTask.depth ?: 0) + 1,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    )
                    
                    // 保存子任务
                    taskRepository.saveTask(newTask)
                    android.util.Log.d("TaskViewModel", "简单子任务保存成功: ${newTask.id}")
                    
                    // 更新父任务的hasSubtasks标志
                    if (parentTask.hasSubtasks != true) {
                        val updatedParentTask = parentTask.copy(hasSubtasks = true)
                        taskRepository.updateTask(updatedParentTask)
                        android.util.Log.d("TaskViewModel", "已更新父任务hasSubtasks标志: ${updatedParentTask.id}")
                    }
                    
                    // 添加延迟，确保数据库操作完成
                    delay(100)
                    
                    // 发送全局刷新事件，确保所有使用该任务的页面都能更新
                    NotificationCenter.post(TaskRefreshEvent(parentId))
                    android.util.Log.d("TaskViewModel", "已发送全局刷新事件: taskId=$parentId")
                    
                    // 重新加载父任务以更新UI
                    loadTask(parentId)
                } else {
                    android.util.Log.e("TaskViewModel", "找不到父任务: $parentId")
                    _uiState.value = TaskUiState.Error("找不到父任务")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "添加简单子任务失败", e)
                _uiState.value = TaskUiState.Error(e.message ?: "添加子任务失败")
            }
        }
    }

    /**
     * 更新任务的标题和描述
     * @param taskId 任务ID
     * @param title 新标题
     * @param description 新描述
     */
    fun updateTaskDetails(taskId: String, title: String, description: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskViewModel", "开始更新任务详情: $taskId, 标题: $title")
                
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val updatedTask = task.copy(
                        title = title.trim(),
                        description = description.trim(),
                        updatedAt = LocalDateTime.now()
                    )
                    
                    taskRepository.updateTask(updatedTask)
                    android.util.Log.d("TaskViewModel", "任务详情更新成功: ${updatedTask.id}")
                    
                    // 添加延迟，确保数据库操作完成
                    delay(100)
                    
                    // 发送全局刷新事件，确保所有使用该任务的页面都能更新
                    NotificationCenter.post(TaskRefreshEvent(taskId))
                    android.util.Log.d("TaskViewModel", "已发送任务详情更新的全局刷新事件: taskId=$taskId")
                    
                    // 重新加载任务以更新UI
                    loadTask(taskId)
                } else {
                    android.util.Log.e("TaskViewModel", "找不到任务: $taskId")
                    _uiState.value = TaskUiState.Error("找不到任务")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "更新任务详情失败", e)
                _uiState.value = TaskUiState.Error(e.message ?: "更新任务详情失败")
            }
        }
    }

    /**
     * 提交任务完成反馈并同步到感想页面
     * 
     * @param taskId 任务ID
     * @param feedback 用户提交的反馈数据
     */
    fun submitTaskFeedback(taskId: String, feedback: FeedbackData) {
        viewModelScope.launch {
            try {
                // 1. 获取任务信息
                val task = taskRepository.getTaskById(taskId)
                if (task == null) {
                    android.util.Log.e(TAG, "提交反馈失败，找不到任务: $taskId")
                    return@launch
                }
                
                android.util.Log.d(TAG, "任务反馈已收到: taskId=$taskId, 任务标题=${task.title}, emoji=${feedback.emotion}, comment=${feedback.comment}")
                
                // 2. 如果有任何反馈（表情或评论），创建感想数据
                if (feedback.emotion != null || (feedback.comment != null && feedback.comment.isNotBlank())) {
                    try {
                        // 将emoji映射为对应的MoodType
                        val moodType = mapEmotionToMood(feedback.emotion)
                        
                        // 创建感想内容：优先使用用户输入的评论，如果没有则使用表情作为内容
                        val reflectionContent = when {
                            feedback.comment != null && feedback.comment.isNotBlank() -> feedback.comment
                            feedback.emotion != null -> "任务完成时的心情：${feedback.emotion}"
                            else -> "任务已完成"
                        }
                        
                        // 🔧 修复：提取任务的真实标签
                        val taskTags = task.tags.map { it.name }
                        android.util.Log.d(TAG, "任务标签: ${taskTags.joinToString(", ")}")
                        
                        // 🔧 修复：组合标签 - 包含任务原有标签和完成标识
                        val combinedTags = mutableListOf<String>().apply {
                            add("任务完成")
                            add("反馈")
                            addAll(taskTags) // 添加任务的真实标签
                        }.distinct() // 去重
                        
                        android.util.Log.d(TAG, "合并后的标签: ${combinedTags.joinToString(", ")}")
                        
                        // 直接创建与ReflectionScreen兼容的UI层Reflection模型
                        val uiReflection = com.timeflow.app.ui.screen.reflection.Reflection(
                            id = UUID.randomUUID().toString(),
                            title = "✓ ${task.title}", // 🔧 修复：使用符号代替中文，避免编码问题
                            content = reflectionContent,
                            // 使用正确的ContentBlock构造方式
                            richContent = listOf(com.timeflow.app.ui.screen.reflection.ContentBlock("text", reflectionContent)),
                            // 确保日期格式正确
                            date = feedback.timestamp.toInstant(ZoneOffset.UTC),
                            rating = 5,
                            tags = combinedTags, // 🔧 修复：使用包含任务标签的合并标签
                            type = com.timeflow.app.ui.screen.reflection.ReflectionType.WORK,
                            mood = moodType,
                            // 🔧 修复：添加完整的任务相关元数据到metrics字段
                            metrics = mapOf(
                                "taskId" to taskId,
                                "taskTitle" to task.title, // 真实任务标题
                                "taskTags" to taskTags.joinToString(","), // 任务标签列表，逗号分隔
                                "taskDescription" to (task.description ?: ""),
                                "taskPriority" to (task.priority?.name ?: "未设置"),
                                "completedAt" to feedback.timestamp.toString(),
                                "emotion" to (feedback.emotion ?: "无"),
                                "hasComment" to (feedback.comment?.isNotBlank() == true).toString()
                            ),
                            plans = emptyList(),
                            backgroundImage = null
                        )
                        
                        // 保存UI层Reflection模型
                        val reflectionId = reflectionRepository.saveReflection(uiReflection)
                        android.util.Log.d(TAG, "感想已同步到感想页面: reflectionId=${uiReflection.id}, 标题=${uiReflection.title}, 标签=${uiReflection.tags.joinToString(",")}")
                        
                        // 发送感想创建事件，通知感想页面刷新数据
                        NotificationCenter.post(ReflectionCreatedEvent(reflectionId, uiReflection))
                        android.util.Log.d(TAG, "✓ 已发送感想创建事件，感想页面将自动刷新")
                    } catch (e: Exception) {
                        android.util.Log.e(TAG, "同步感想到感想页面失败", e)
                    }
                } else {
                    android.util.Log.d(TAG, "用户没有提供任何反馈（表情或评论），跳过感想保存")
                }
                
                // 3. 更新任务完成状态（如果尚未完成）
                if (task.completedAt == null) {
                    val updatedTask = task.copy(
                        completedAt = feedback.timestamp,
                        status = "已完成"
                    )
                    taskRepository.updateTask(updatedTask)
                    android.util.Log.d(TAG, "任务状态已更新为已完成: $taskId")

                    // 🔧 循环任务逻辑暂时移除，专注于目标拆解AI调用修复
                }
                
            } catch (e: Exception) {
                android.util.Log.e(TAG, "处理任务反馈失败", e)
            }
        }
    }
    
    /**
     * 将情绪emoji映射到感想系统的情绪类型
     */
    private fun mapEmotionToMood(emoji: String?): MoodType {
        return when (emoji) {
            "😊" -> MoodType.HAPPY
            "😐" -> MoodType.CALM
            "😢" -> MoodType.SAD
            "😡" -> MoodType.ANGRY
            "😰" -> MoodType.ANXIOUS
            else -> MoodType.CALM // 默认平静
        }
    }

    // 🔧 循环任务处理逻辑暂时移除，专注于目标拆解AI调用修复

    sealed class TaskUiState {
        object Loading : TaskUiState()
        data class Success(val task: TaskData) : TaskUiState()
        data class Error(val message: String) : TaskUiState()
    }
} 