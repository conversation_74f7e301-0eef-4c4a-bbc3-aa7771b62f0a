package com.timeflow.app.ui.screen.goal

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.data.model.Goal
import com.timeflow.app.ui.components.LoadingIndicator
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * UI状态
 */
sealed class GoalState {
    object Loading : GoalState()
    data class Success(val goal: Goal) : GoalState()
    data class Error(val message: String) : GoalState()
}

/**
 * 将目标保存为模板的屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SaveGoalAsTemplateScreen(
    navController: NavController,
    goalId: String,
    goalViewModel: GoalViewModel = hiltViewModel(),
    templateViewModel: GoalTemplateViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    val coroutineScope = rememberCoroutineScope()
    
    // 状态
    val uiState by templateViewModel.uiState.collectAsState()
    val goal = remember { mutableStateOf<Goal?>(null) }
    
    // 创建一个本地状态来跟踪目标加载状态
    var goalState by remember { mutableStateOf<GoalState>(GoalState.Loading) }
    
    // 表单状态
    var templateName by remember { mutableStateOf("") }
    var templateDescription by remember { mutableStateOf("") }
    var templateCategory by remember { mutableStateOf("") }
    
    // 类别选项
    val categoryOptions = remember { 
        listOf("学习", "工作", "健康", "个人成长", "财务", "心理健康", "生活习惯", "其他")
    }
    
    var showCategoryDialog by remember { mutableStateOf(false) }
    
    // 处理成功保存后的导航
    LaunchedEffect(uiState) {
        if (uiState is TemplateUiState.Success) {
            delay(1500) // 显示成功消息的时间
            navController.popBackStack()
        }
    }
    
    // 加载目标数据
    LaunchedEffect(goalId) {
        try {
            val loadedGoal = goalViewModel.getGoalById(goalId)
            if (loadedGoal != null) {
                goal.value = loadedGoal
                goalState = GoalState.Success(loadedGoal)
            } else {
                goalState = GoalState.Error("找不到该目标")
            }
        } catch (e: Exception) {
            goalState = GoalState.Error(e.message ?: "加载目标时发生错误")
        }
    }
    
    // 当目标数据加载后自动填充表单
    LaunchedEffect(goalState) {
        if (goalState is GoalState.Success) {
            val loadedGoal = (goalState as GoalState.Success).goal
            goal.value = loadedGoal
            
            // 自动填充表单
            if (templateName.isEmpty()) {
                templateName = "${loadedGoal.title} 模板"
            }
            
            if (templateDescription.isEmpty()) {
                templateDescription = "基于目标\"${loadedGoal.title}\"创建的模板"
            }
        }
    }
    
    // 状态栏设置
    SideEffect {
        activity?.let {
            SystemBarManager.setupDarkModeSystemBars(it)
        }
    }
    
    // 提供清理机制
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            val originalStatusBarColor = window.statusBarColor
            SystemBarManager.setupDarkModeSystemBars(act)
            
            onDispose {
                window.statusBarColor = originalStatusBarColor
            }
        }
    }
    
    // 保存模板方法
    val saveTemplate = {
        if (templateName.isNotBlank() && goal.value != null) {
            coroutineScope.launch {
                templateViewModel.createTemplateFromGoal(
                    goalId = goalId,
                    templateName = templateName,
                    category = templateCategory
                )
            }
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F9FA))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("保存为模板") },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = DustyLavender.copy(alpha = 0.85f),
                        titleContentColor = Color.White,
                        navigationIconContentColor = Color.White
                    )
                )
            },
            content = { paddingValues ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    when {
                        goalState is GoalState.Loading || uiState is TemplateUiState.Loading -> {
                            LoadingIndicator(isLoading = true)
                        }
                        goalState is GoalState.Error -> {
                            // 错误状态
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    modifier = Modifier.size(80.dp),
                                    tint = Color.Gray
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Text(
                                    text = "加载目标失败",
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.DarkGray
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = (goalState as GoalState.Error).message,
                                    fontSize = 16.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center
                                )
                                
                                Spacer(modifier = Modifier.height(24.dp))
                                
                                Button(
                                    onClick = { 
                                        goalState = GoalState.Loading
                                        coroutineScope.launch {
                                            try {
                                                val loadedGoal = goalViewModel.getGoalById(goalId)
                                                if (loadedGoal != null) {
                                                    goal.value = loadedGoal
                                                    goalState = GoalState.Success(loadedGoal)
                                                } else {
                                                    goalState = GoalState.Error("找不到该目标")
                                                }
                                            } catch (e: Exception) {
                                                goalState = GoalState.Error(e.message ?: "加载目标时发生错误")
                                            }
                                        }
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = DustyLavender
                                    )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Refresh,
                                        contentDescription = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("重试")
                                }
                            }
                        }
                        uiState is TemplateUiState.Success -> {
                            // 成功状态
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = null,
                                    modifier = Modifier.size(80.dp),
                                    tint = Color(0xFF4CAF50)
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Text(
                                    text = "模板创建成功！",
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.DarkGray
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = "已将目标保存为新的模板",
                                    fontSize = 16.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                        uiState is TemplateUiState.Error -> {
                            // 错误状态
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    modifier = Modifier.size(80.dp),
                                    tint = Color.Gray
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Text(
                                    text = "保存模板失败",
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = Color.DarkGray
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                Text(
                                    text = (uiState as TemplateUiState.Error).message,
                                    fontSize = 16.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center
                                )
                                
                                Spacer(modifier = Modifier.height(24.dp))
                                
                                Button(
                                    onClick = saveTemplate,
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = DustyLavender
                                    )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Refresh,
                                        contentDescription = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("重试")
                                }
                            }
                        }
                        else -> {
                            // 表单内容
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp)
                                    .verticalScroll(rememberScrollState())
                            ) {
                                // 目标信息
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(bottom = 16.dp),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                                    colors = CardDefaults.cardColors(containerColor = Color.White)
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp)
                                    ) {
                                        Text(
                                            text = "目标信息",
                                            fontSize = 18.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = Color.DarkGray
                                        )
                                        
                                        Spacer(modifier = Modifier.height(8.dp))
                                        
                                        goal.value?.let { currentGoal ->
                                            Text(
                                                text = "标题: ${currentGoal.title}",
                                                fontSize = 16.sp,
                                                color = Color.DarkGray
                                            )
                                            
                                            if (currentGoal.description.isNotEmpty()) {
                                                Spacer(modifier = Modifier.height(4.dp))
                                                Text(
                                                    text = "描述: ${currentGoal.description}",
                                                    fontSize = 14.sp,
                                                    color = Color.Gray
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 模板表单
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(bottom = 16.dp),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                                    colors = CardDefaults.cardColors(containerColor = Color.White)
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp)
                                    ) {
                                        Text(
                                            text = "模板信息",
                                            fontSize = 18.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = Color.DarkGray
                                        )
                                        
                                        Spacer(modifier = Modifier.height(16.dp))
                                        
                                        // 模板名称
                                        OutlinedTextField(
                                            value = templateName,
                                            onValueChange = { templateName = it },
                                            label = { Text("模板名称") },
                                            placeholder = { Text("输入模板名称") },
                                            modifier = Modifier.fillMaxWidth(),
                                            colors = OutlinedTextFieldDefaults.colors(
                                                focusedBorderColor = DustyLavender,
                                                unfocusedBorderColor = Color(0xFFE2E8F0)
                                            )
                                        )
                                        
                                        Spacer(modifier = Modifier.height(16.dp))
                                        
                                        // 模板描述
                                        OutlinedTextField(
                                            value = templateDescription,
                                            onValueChange = { templateDescription = it },
                                            label = { Text("模板描述") },
                                            placeholder = { Text("简要描述此模板的用途") },
                                            modifier = Modifier.fillMaxWidth(),
                                            colors = OutlinedTextFieldDefaults.colors(
                                                focusedBorderColor = DustyLavender,
                                                unfocusedBorderColor = Color(0xFFE2E8F0)
                                            ),
                                            minLines = 3
                                        )
                                        
                                        Spacer(modifier = Modifier.height(16.dp))
                                        
                                        // 分类选择
                                        OutlinedTextField(
                                            value = templateCategory,
                                            onValueChange = { },
                                            label = { Text("模板分类") },
                                            placeholder = { Text("选择一个分类") },
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clickable { showCategoryDialog = true },
                                            enabled = false,
                                            colors = OutlinedTextFieldDefaults.colors(
                                                focusedBorderColor = DustyLavender,
                                                unfocusedBorderColor = Color(0xFFE2E8F0)
                                            ),
                                            trailingIcon = {
                                                Icon(
                                                    imageVector = Icons.Default.KeyboardArrowDown,
                                                    contentDescription = "选择分类"
                                                )
                                            }
                                        )
                                        
                                        // 分类对话框
                                        if (showCategoryDialog) {
                                            AlertDialog(
                                                onDismissRequest = { showCategoryDialog = false },
                                                title = { Text("选择分类") },
                                                text = {
                                                    Column(
                                                        modifier = Modifier
                                                            .fillMaxWidth()
                                                            .padding(8.dp)
                                                    ) {
                                                        categoryOptions.forEach { category ->
                                                            Row(
                                                                modifier = Modifier
                                                                    .fillMaxWidth()
                                                                    .padding(vertical = 8.dp)
                                                                    .clickable {
                                                                        templateCategory = category
                                                                        showCategoryDialog = false
                                                                    },
                                                                verticalAlignment = Alignment.CenterVertically
                                                            ) {
                                                                RadioButton(
                                                                    selected = templateCategory == category,
                                                                    onClick = {
                                                                        templateCategory = category
                                                                        showCategoryDialog = false
                                                                    }
                                                                )
                                                                Text(
                                                                    text = category,
                                                                    modifier = Modifier.padding(start = 8.dp)
                                                                )
                                                            }
                                                        }
                                                    }
                                                },
                                                confirmButton = {
                                                    TextButton(onClick = { showCategoryDialog = false }) {
                                                        Text("取消")
                                                    }
                                                }
                                            )
                                        }
                                    }
                                }
                                
                                // 保存按钮
                                Button(
                                    onClick = saveTemplate,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp)
                                        .height(56.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = DustyLavender
                                    ),
                                    shape = RoundedCornerShape(8.dp),
                                    enabled = templateName.isNotBlank()
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Save,
                                        contentDescription = null
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("保存为模板")
                                }
                                
                                // 提示信息
                                Text(
                                    text = "创建模板后，您可以在目标添加页面快速使用此模板创建新目标",
                                    fontSize = 14.sp,
                                    color = Color.Gray,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 8.dp)
                                )
                            }
                        }
                    }
                }
            }
        )
    }
} 