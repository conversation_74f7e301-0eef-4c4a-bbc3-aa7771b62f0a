package com.timeflow.app.worker

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.timeflow.app.service.RecurringTaskManager
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject

/**
 * 循环任务Worker
 * 定期检查和生成循环任务实例
 */
@HiltWorker
class RecurringTaskWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val recurringTaskManager: RecurringTaskManager
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "RecurringTaskWorker"
    }

    override suspend fun doWork(): Result {
        return try {
            Log.d(TAG, "开始执行循环任务检查...")

            // 检查并生成到期的循环任务
            recurringTaskManager.checkAndGenerateRecurringTasks()

            Log.d(TAG, "✅ 循环任务检查完成")
            Result.success()

        } catch (e: Exception) {
            Log.e(TAG, "循环任务检查失败: ${e.message}", e)
            Result.failure()
        }
    }
}
