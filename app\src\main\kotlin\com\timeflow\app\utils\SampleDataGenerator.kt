package com.timeflow.app.utils

import android.util.Log
import com.timeflow.app.data.entity.KanbanBoard
import com.timeflow.app.data.entity.KanbanColumn
import com.timeflow.app.data.entity.Task as TaskEntity
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.TaskType
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.repository.KanbanBoardRepository
import com.timeflow.app.data.repository.KanbanColumnRepository
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.converter.TaskConverter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import javax.inject.Provider
import javax.inject.Singleton

/**
 * 示例数据生成器
 * 用于在应用首次启动时生成示例数据
 */
@Singleton
class SampleDataGenerator @Inject constructor(
    private val boardRepository: KanbanBoardRepository,
    private val columnRepository: KanbanColumnRepository,
    private val taskRepositoryProvider: Provider<TaskRepository>
) {
    
    private val taskRepository: TaskRepository by lazy { taskRepositoryProvider.get() }
    
    /**
     * 检查是否需要生成示例数据，如果没有任何看板，则生成示例数据
     */
    suspend fun generateSampleDataIfNeeded() = withContext(Dispatchers.IO) {
        try {
            // 检查是否已有看板数据
            val existingBoards = boardRepository.getAllBoards()
            if (existingBoards.isEmpty()) {
                Timber.d("未检测到看板数据，开始生成示例数据")
                generateSampleData()
                Timber.d("示例数据生成完成")
            } else {
                Timber.d("已存在${existingBoards.size}个看板，无需生成示例数据")
            }
        } catch (e: Exception) {
            Timber.e(e, "生成示例数据时发生错误")
            throw e
        }
    }
    
    /**
     * 生成示例数据
     */
    private suspend fun generateSampleData() = withContext(Dispatchers.IO) {
        // 1. 创建示例看板
        val board = KanbanBoard(
            id = UUID.randomUUID().toString(),
            title = "工作任务",
            description = "日常工作任务管理",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            color = "#4287f5",
            icon = "work",
            position = 0
        )
        boardRepository.insertBoard(board)
        
        // 2. 创建看板列
        val columns = listOf(
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = board.id,
                title = "待办",
                position = 0,
                color = "#E0E0E0",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            ),
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = board.id,
                title = "进行中",
                position = 1,
                color = "#FFCA28",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            ),
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = board.id,
                title = "已完成",
                position = 2,
                color = "#66BB6A",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )
        
        columns.forEach { columnRepository.insertColumn(it) }
        
        // 3. 创建示例任务
        val todoTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "完成工作报告",
                description = "准备月度工作总结报告，包括项目进展和下月计划",
                dueDate = LocalDateTime.now().plusDays(2),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = columns[0].id
            ),
            Task(
                id = UUID.randomUUID().toString(),
                title = "学习Kotlin协程",
                description = "学习Kotlin协程的基本概念和使用方法，完成相关练习",
                dueDate = LocalDateTime.now().plusDays(5),
                priority = Priority.MEDIUM,
                type = TaskType.NORMAL,
                columnId = columns[0].id
            ),
            Task(
                id = UUID.randomUUID().toString(),
                title = "购买日用品",
                description = "去超市购买厨房和浴室的日用品",
                dueDate = LocalDateTime.now().plusDays(1),
                priority = Priority.LOW,
                type = TaskType.NORMAL,
                columnId = columns[0].id
            )
        )
        
        val inProgressTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "设计新的APP界面",
                description = "根据需求设计新的APP界面，包括颜色方案和交互设计",
                dueDate = LocalDateTime.now().plusDays(3),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = columns[1].id
            )
        )
        
        val completedTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "安排团队会议",
                description = "安排团队会议讨论下周的工作计划",
                dueDate = LocalDateTime.now().minusDays(1),
                completedAt = LocalDateTime.now().minusHours(3),
                isCompleted = true,
                priority = Priority.MEDIUM,
                type = TaskType.NORMAL,
                columnId = columns[2].id
            )
        )
        
        // 保存所有任务
        (todoTasks + inProgressTasks + completedTasks).forEach { taskRepository.insertTask(it) }
        
        // 创建第二个看板：个人项目
        val personalBoard = KanbanBoard(
            id = UUID.randomUUID().toString(),
            title = "个人项目",
            description = "个人项目管理与追踪",
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            color = "#9c27b0",
            icon = "person",
            position = 1
        )
        boardRepository.insertBoard(personalBoard)
        
        // 个人项目看板列
        val personalColumns = listOf(
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = personalBoard.id,
                title = "规划中",
                position = 0,
                color = "#E1BEE7",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            ),
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = personalBoard.id,
                title = "开发中",
                position = 1,
                color = "#BA68C8",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            ),
            KanbanColumn(
                id = UUID.randomUUID().toString(),
                boardId = personalBoard.id,
                title = "已上线",
                position = 2,
                color = "#8E24AA",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )
        
        personalColumns.forEach { columnRepository.insertColumn(it) }
        
        // 个人项目示例任务
        val planningTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "个人网站设计",
                description = "设计个人作品展示网站的UI和功能",
                dueDate = LocalDateTime.now().plusDays(10),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = personalColumns[0].id
            ),
            Task(
                id = UUID.randomUUID().toString(),
                title = "学习新框架",
                description = "学习React Native框架基础知识",
                dueDate = LocalDateTime.now().plusDays(14),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = personalColumns[0].id
            )
        )
        
        val developingTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "开发天气应用",
                description = "使用新学到的框架开发一个简单的天气应用",
                dueDate = LocalDateTime.now().plusDays(7),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = personalColumns[1].id
            )
        )
        
        val launchedTasks = listOf(
            Task(
                id = UUID.randomUUID().toString(),
                title = "记账工具",
                description = "简单的个人记账管理工具",
                dueDate = LocalDateTime.now().minusDays(10),
                priority = Priority.HIGH,
                type = TaskType.NORMAL,
                columnId = personalColumns[2].id
            )
        )
        
        // 保存个人项目任务
        (planningTasks + developingTasks + launchedTasks).forEach { taskRepository.insertTask(it) }
    }
} 