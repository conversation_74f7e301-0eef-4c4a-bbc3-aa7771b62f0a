<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background_green"
    android:padding="20dp">

    <!-- 顶部标题区域 -->
    <LinearLayout
        android:id="@+id/widget_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="第22周"
                android:textSize="14sp"
                android:textColor="@color/widget_text_light"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/widget_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5.26 - 6.1"
                android:textSize="12sp"
                android:textColor="@color/widget_text_light"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📊"
            android:textSize="24sp" />

    </LinearLayout>

    <!-- 活动统计卡片 -->
    <LinearLayout
        android:id="@+id/widget_stats_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/widget_header"
        android:layout_marginTop="24dp"
        android:background="@drawable/widget_card_background"
        android:padding="16dp"
        android:elevation="2dp">

        <!-- 活动类型列表 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 睡眠 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🛌"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="睡眠"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/widget_sleep_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="23h 49m"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_secondary" />

            </LinearLayout>

            <!-- 工作 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="💼"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="工作"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/widget_work_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="23h"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_secondary" />

            </LinearLayout>

            <!-- 娱乐 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎮"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="娱乐"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/widget_entertainment_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="6h 38m"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_secondary" />

            </LinearLayout>

            <!-- 专注 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎯"
                    android:textSize="16sp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="专注"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/widget_focus_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3h 16m"
                    android:textSize="14sp"
                    android:textColor="@color/widget_text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 添加缺失的统计元素 -->
    <LinearLayout
        android:id="@+id/widget_stats_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_below="@id/widget_stats_container"
        android:layout_marginTop="16dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/widget_session_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="7次"
            android:textSize="14sp"
            android:textColor="@color/widget_text_primary"
            android:gravity="center" />

        <TextView
            android:id="@+id/widget_avg_session"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="50min"
            android:textSize="14sp"
            android:textColor="@color/widget_text_primary"
            android:gravity="center" />

        <TextView
            android:id="@+id/widget_task_stats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="4/6"
            android:textSize="14sp"
            android:textColor="@color/widget_text_primary"
            android:gravity="center" />

        <TextView
            android:id="@+id/widget_completion_rate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="67%"
            android:textSize="14sp"
            android:textColor="@color/widget_text_primary"
            android:gravity="center" />

    </LinearLayout>

    <!-- 添加缺失的进度条元素 -->
    <LinearLayout
        android:id="@+id/widget_progress_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_below="@id/widget_stats_bottom"
        android:layout_marginTop="16dp"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/widget_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_weight="1"
            android:progress="67"
            android:progressTint="@color/widget_accent_green" />

        <ProgressBar
            android:id="@+id/widget_morning_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_weight="1"
            android:progress="80"
            android:progressTint="@color/widget_accent_blue"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/widget_afternoon_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_weight="1"
            android:progress="60"
            android:progressTint="@color/widget_accent_orange"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/widget_evening_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="6dp"
            android:layout_weight="1"
            android:progress="40"
            android:progressTint="@color/widget_accent_purple"
            android:visibility="gone" />

    </LinearLayout>

</RelativeLayout>
