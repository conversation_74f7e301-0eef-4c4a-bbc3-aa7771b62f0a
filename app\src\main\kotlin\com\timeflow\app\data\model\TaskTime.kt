package com.timeflow.app.data.model

import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * 任务时间数据模型
 * 包含任务的完整时间信息和相关业务计算
 */
data class TaskTime(
    val taskId: String,
    val startTime: LocalDateTime? = null,
    val endTime: LocalDateTime? = null,
    val dueDate: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Long = 1L // 用于乐观锁
) {
    
    /**
     * 获取有效的时间（优先级：dueDate > endTime > startTime）
     */
    val effectiveTime: LocalDateTime?
        get() = dueDate ?: endTime ?: startTime
    
    /**
     * 计算距离截止时间的天数
     */
    val daysLeft: Int
        get() {
            val effective = effectiveTime ?: return Int.MAX_VALUE
            val today = LocalDateTime.now().toLocalDate()
            val targetDate = effective.toLocalDate()
            return ChronoUnit.DAYS.between(today, targetDate).toInt()
        }
    
    /**
     * 判断任务是否已过期
     */
    val isOverdue: Boolean
        get() {
            val effective = effectiveTime ?: return false
            return effective.isBefore(LocalDateTime.now())
        }
    
    /**
     * 判断任务是否在今天
     */
    val isToday: Boolean
        get() = daysLeft == 0
    
    /**
     * 判断任务是否在明天
     */
    val isTomorrow: Boolean
        get() = daysLeft == 1
    
    /**
     * 判断任务是否在本周内
     */
    val isThisWeek: Boolean
        get() = daysLeft in 0..6
    
    /**
     * 获取时间优先级（用于排序）
     * 返回值越小优先级越高
     */
    val timePriority: Int
        get() = when {
            isOverdue -> -1
            isToday -> 0
            isTomorrow -> 1
            isThisWeek -> 2
            daysLeft < 30 -> 3
            else -> 4
        }
    
    /**
     * 创建更新版本
     */
    fun withUpdatedTime(
        newStartTime: LocalDateTime? = this.startTime,
        newEndTime: LocalDateTime? = this.endTime,
        newDueDate: LocalDateTime? = this.dueDate
    ): TaskTime {
        return copy(
            startTime = newStartTime,
            endTime = newEndTime,
            dueDate = newDueDate,
            updatedAt = LocalDateTime.now(),
            version = version + 1
        )
    }
}

/**
 * 任务时间更新事件
 */
data class TaskTimeUpdateEvent(
    val taskId: String,
    val oldTime: TaskTime?,
    val newTime: TaskTime,
    val source: String = "Unknown",
    val timestamp: LocalDateTime = LocalDateTime.now()
)

/**
 * 任务时间冲突检测结果
 */
data class TaskTimeConflict(
    val conflictingTaskId: String,
    val conflictingTaskTitle: String,
    val conflictType: TimeConflictType,
    val conflictTime: LocalDateTime
)

enum class TimeConflictType {
    OVERLAP,        // 时间重叠
    BACK_TO_BACK,   // 紧接着
    SAME_TIME       // 完全相同时间
}