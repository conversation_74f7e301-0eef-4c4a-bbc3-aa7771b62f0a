package com.timeflow.app.utils

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.Window
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * 状态栏工具类
 * 提供对状态栏颜色的控制功能
 */
object StatusBarUtils {
    // 保存原始状态栏颜色
    private var originalStatusBarColor: Int = Color.Transparent.toArgb()
    private var isDarkContentOriginal: Boolean = true
    
    /**
     * 设置状态栏颜色
     * @param window 窗口对象
     * @param color 状态栏颜色
     * @param isDarkContent 是否使用深色内容（图标和文字）
     */
    fun setStatusBarColor(window: Window, color: Color, isDarkContent: Boolean? = null) {
        window.statusBarColor = color.toArgb()
        val shouldUseDarkContent = isDarkContent ?: (color.luminance() > 0.5f)
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = shouldUseDarkContent
        }
    }
    
    /**
     * 设置状态栏颜色
     * @param activity 当前活动
     * @param color 状态栏颜色
     * @param isDarkContent 是否使用深色内容（图标和文字）
     */
    fun setStatusBarColor(activity: Activity, color: Color, isDarkContent: Boolean? = null) {
        setStatusBarColor(activity.window, color, isDarkContent)
    }
    
    /**
     * 保存并设置状态栏颜色
     * @param activity 当前活动
     * @param color 状态栏颜色
     * @param isDarkContent 是否使用深色内容（图标和文字）
     */
    fun saveAndSetStatusBarColor(activity: Activity, color: Color, isDarkContent: Boolean? = null) {
        val window = activity.window
        originalStatusBarColor = window.statusBarColor
        isDarkContentOriginal = WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars
        setStatusBarColor(window, color, isDarkContent)
    }
    
    /**
     * 恢复原始状态栏颜色
     * @param activity 当前活动
     */
    fun resetStatusBarColor(activity: Activity) {
        val window = activity.window
        window.statusBarColor = originalStatusBarColor
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = isDarkContentOriginal
        }
    }
}

/**
 * 非Composable环境中设置状态栏颜色
 * @param color 状态栏颜色
 * @param isDarkContent 是否使用深色内容（图标和文字）
 */
fun setNonComposeStatusBarColor(color: Color, isDarkContent: Boolean? = null) {
    try {
        val context = ActivityContextProvider.getActivityContext()
        if (context != null) {
            StatusBarUtils.setStatusBarColor(context, color, isDarkContent)
        }
    } catch (e: Exception) {
        Log.e("StatusBarUtils", "设置状态栏颜色失败: ${e.message}")
    }
}

/**
 * 非Composable环境中保存并设置状态栏颜色
 * @param color 状态栏颜色
 * @param isDarkContent 是否使用深色内容（图标和文字）
 */
fun saveAndSetNonComposeStatusBarColor(color: Color, isDarkContent: Boolean? = null) {
    try {
        val context = ActivityContextProvider.getActivityContext()
        if (context != null) {
            StatusBarUtils.saveAndSetStatusBarColor(context, color, isDarkContent)
        }
    } catch (e: Exception) {
        Log.e("StatusBarUtils", "保存并设置状态栏颜色失败: ${e.message}")
    }
}

/**
 * 非Composable环境中重置状态栏颜色
 */
fun resetNonComposeStatusBarColor() {
    try {
        val context = ActivityContextProvider.getActivityContext()
        if (context != null) {
            StatusBarUtils.resetStatusBarColor(context)
        }
    } catch (e: Exception) {
        Log.e("StatusBarUtils", "重置状态栏颜色失败: ${e.message}")
    }
}

/**
 * Composable函数中设置状态栏颜色
 */
@Composable
fun ComposeStatusBarEffect(color: Color, isDarkContent: Boolean? = null) {
    val view = LocalView.current
    val context = LocalContext.current
    
    if (!view.isInEditMode) {
        LaunchedEffect(color, isDarkContent) {
            (context as? Activity)?.let {
                StatusBarUtils.setStatusBarColor(it, color, isDarkContent)
            }
        }
        
        DisposableEffect(Unit) {
            onDispose {
                (context as? Activity)?.let {
                    StatusBarUtils.resetStatusBarColor(it)
                }
            }
        }
    }
} 