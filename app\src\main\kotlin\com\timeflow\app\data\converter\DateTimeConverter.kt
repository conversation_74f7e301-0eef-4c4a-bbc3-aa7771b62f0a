package com.timeflow.app.data.converter

import androidx.room.TypeConverter
import java.time.LocalDateTime
import java.time.ZoneOffset

/**
 * LocalDateTime类型转换器
 * 用于在Room数据库中存储和读取LocalDateTime类型
 */
class DateTimeConverter {
    /**
     * 将LocalDateTime转换为Long（以秒为单位）
     */
    @TypeConverter
    fun fromLocalDateTime(dateTime: LocalDateTime?): Long? {
        return dateTime?.toEpochSecond(ZoneOffset.UTC)
    }
    
    /**
     * 将Long（以秒为单位）转换为LocalDateTime
     */
    @TypeConverter
    fun toLocalDateTime(epochSecond: Long?): LocalDateTime? {
        return epochSecond?.let { LocalDateTime.ofEpochSecond(it, 0, ZoneOffset.UTC) }
    }
} 