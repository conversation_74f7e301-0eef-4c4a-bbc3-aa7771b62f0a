package com.timeflow.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 主ViewModel，负责管理应用状态
 */
@HiltViewModel
class TimeFlowViewModel @Inject constructor() : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(TimeFlowUiState())
    val uiState: StateFlow<TimeFlowUiState> = _uiState.asStateFlow()
    
    // 初始化
    init {
        // 加载初始数据
    }
    
    // 更新主题颜色
    fun updateThemeColor(primaryColor: Int) {
        _uiState.value = _uiState.value.copy(
            primaryColor = primaryColor
        )
    }
}

/**
 * UI状态数据类
 */
data class TimeFlowUiState(
    val isLoading: Boolean = false,
    val primaryColor: Int = 0xFF7A3E89.toInt(), // 默认紫色
    val isDarkTheme: Boolean = false
) 