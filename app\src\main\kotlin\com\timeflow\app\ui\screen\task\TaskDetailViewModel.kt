package com.timeflow.app.ui.screen.task

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.TaskRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.timeflow.app.data.model.Task
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import java.time.LocalDateTime
import com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache

// 创建类型别名来引用UI层的SubTask，避免直接导入可能造成的循环依赖
typealias UISubTask = com.timeflow.app.ui.screen.task.SubTask

@HiltViewModel
class TaskDetailViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow<TaskDetailUiState>(TaskDetailUiState.Loading)
    val uiState: StateFlow<TaskDetailUiState> = _uiState.asStateFlow()

    fun loadTask(taskId: String) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    _uiState.value = TaskDetailUiState.Success(task)
                } else {
                    _uiState.value = TaskDetailUiState.Error("任务不存在")
                }
            } catch (e: Exception) {
                _uiState.value = TaskDetailUiState.Error(e.message ?: "加载任务失败")
            }
        }
    }

    /**
     * 获取任务并执行回调函数
     * @param taskId 任务ID
     * @param callback 获取任务后执行的回调函数，用于处理查询结果
     */
    fun getTaskById(taskId: String, callback: (Task?) -> Unit) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                callback(task)
            } catch (e: Exception) {
                callback(null)
            }
        }
    }

    /**
     * 同步获取任务数据
     * @param taskId 任务ID
     * @return 任务对象，如果不存在则返回null
     */
    suspend fun getTaskByIdSync(taskId: String): Task? {
        return try {
            // 这里我们需要添加一些逻辑来绕过可能的缓存
            android.util.Log.d("TaskDetailViewModel", "同步获取任务数据: $taskId")
            val task = taskRepository.getTaskById(taskId)
            android.util.Log.d("TaskDetailViewModel", "获取到任务: ${task?.title}, 子任务数量: ${task?.let { taskRepository.getSubTasks(it.id).size } ?: 0}")
            task
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "同步获取任务失败: ${e.message}", e)
            null
        }
    }

    /**
     * 获取任务的子任务列表
     * @param taskId 父任务ID
     * @return 子任务列表，如果没有子任务则返回空列表
     */
    suspend fun getSubTasksForTask(taskId: String): List<UISubTask> {
        return try {
            // 直接从数据库获取子任务列表
            val subTasks = taskRepository.getSubTasks(taskId)
            
            // 将数据库中的子任务转换为UI层的SubTask对象
            subTasks.map { subTaskEntity ->
                UISubTask(
                    id = subTaskEntity.id,
                    title = subTaskEntity.title,
                    isCompleted = subTaskEntity.status == "已完成",
                    parentTaskId = taskId,
                    note = subTaskEntity.description ?: "",
                    priority = subTaskEntity.priority ?: com.timeflow.app.data.model.Priority.MEDIUM,
                    dueDate = subTaskEntity.dueDate,
                    reminderTime = subTaskEntity.dueDate
                )
            }
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "获取子任务失败: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * 从缓存中获取子任务（如果可用且有效）
     * @param taskId 父任务ID
     * @return 缓存的子任务列表，如果缓存无效则返回空列表
     */
    suspend fun getCachedSubTasks(taskId: String): List<UISubTask> {
        return try {
            android.util.Log.d("TaskDetailViewModel", "尝试从缓存获取子任务: $taskId")
            
            // 如果是缓存层，先检查缓存有效性
            if (taskRepository is TaskRepositoryCache) {
                // 这里可以检查缓存的时效性，如果缓存还有效就直接返回
                // 目前简化处理，直接尝试获取
                val subTasks = taskRepository.getSubTasks(taskId)
                
                if (subTasks.isNotEmpty()) {
                    android.util.Log.d("TaskDetailViewModel", "缓存命中，获取到${subTasks.size}个子任务")
                    
                    // 将数据库中的子任务转换为UI层的SubTask对象
                    return subTasks.map { subTaskEntity ->
                        UISubTask(
                            id = subTaskEntity.id,
                            title = subTaskEntity.title,
                            isCompleted = subTaskEntity.status == "已完成",
                            parentTaskId = taskId,
                            note = subTaskEntity.description ?: "",
                            priority = subTaskEntity.priority ?: com.timeflow.app.data.model.Priority.MEDIUM,
                            dueDate = subTaskEntity.dueDate,
                            reminderTime = subTaskEntity.dueDate
                        )
                    }
                }
            }
            
            android.util.Log.d("TaskDetailViewModel", "缓存无效或为空")
            emptyList()
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "获取缓存子任务失败: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * 强制刷新子任务列表，绕过缓存
     * @param taskId 父任务ID
     * @return 子任务列表，如果没有子任务则返回空列表
     */
    suspend fun forceRefreshSubTasks(taskId: String): List<UISubTask> {
        return try {
            android.util.Log.d("TaskDetailViewModel", "强制刷新子任务: $taskId")
            
            // 如果taskRepository是缓存层，尝试强制清除缓存
            if (taskRepository is TaskRepositoryCache) {
                android.util.Log.d("TaskDetailViewModel", "检测到缓存层，强制清除父任务的子任务缓存")
                taskRepository.clearCache()
            }
            
            // 多次查询以确保获取最新数据，绕过可能的缓存
            var attempts = 0
            var subTasks: List<Task> = emptyList()
            
            while (attempts < 3 && subTasks.isEmpty()) {
                attempts++
                android.util.Log.d("TaskDetailViewModel", "强制刷新尝试 $attempts")
                
                // 直接从数据库获取子任务列表
                subTasks = taskRepository.getSubTasks(taskId)
                
                if (subTasks.isEmpty() && attempts < 3) {
                    // 短暂延迟后重试
                    delay(100)
                }
            }
            
            // 将数据库中的子任务转换为UI层的SubTask对象
            val result = subTasks.map { subTaskEntity ->
                UISubTask(
                    id = subTaskEntity.id,
                    title = subTaskEntity.title,
                    isCompleted = subTaskEntity.status == "已完成",
                    parentTaskId = taskId,
                    note = subTaskEntity.description ?: "",
                    priority = subTaskEntity.priority ?: com.timeflow.app.data.model.Priority.MEDIUM,
                    dueDate = subTaskEntity.dueDate,
                    reminderTime = subTaskEntity.dueDate
                )
            }
            
            android.util.Log.d("TaskDetailViewModel", "强制刷新获取到${result.size}个子任务")
            result
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "强制刷新子任务失败: ${e.message}", e)
            emptyList()
        }
    }
    
    /**
     * 真正绕过所有缓存的子任务获取方法
     * 直接访问底层数据库，完全绕过缓存层
     * @param taskId 父任务ID
     * @return 子任务列表
     */
    suspend fun getSubTasksDirectFromDatabase(taskId: String): List<UISubTask> {
        return try {
            android.util.Log.d("TaskDetailViewModel", "直接从数据库获取子任务: $taskId")
            
            // 尝试获取底层仓库实现（绕过缓存）
            val underlyingRepository = if (taskRepository is TaskRepositoryCache) {
                // 如果是缓存包装器，我们需要想办法访问底层实现
                // 这里暂时还是使用现有方法，但先清除缓存
                taskRepository.clearCache()
                taskRepository
            } else {
                taskRepository
            }
            
            // 延迟确保缓存清除完成
            delay(50)
            
            val subTasks = underlyingRepository.getSubTasks(taskId)
            
            // 将数据库中的子任务转换为UI层的SubTask对象
            val result = subTasks.map { subTaskEntity ->
                UISubTask(
                    id = subTaskEntity.id,
                    title = subTaskEntity.title,
                    isCompleted = subTaskEntity.status == "已完成",
                    parentTaskId = taskId,
                    note = subTaskEntity.description ?: "",
                    priority = subTaskEntity.priority ?: com.timeflow.app.data.model.Priority.MEDIUM,
                    dueDate = subTaskEntity.dueDate,
                    reminderTime = subTaskEntity.dueDate
                )
            }
            
            android.util.Log.d("TaskDetailViewModel", "直接从数据库获取到${result.size}个子任务")
            result
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "直接从数据库获取子任务失败: ${e.message}", e)
            emptyList()
        }
    }

    fun updateTask(taskId: String, title: String, description: String) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val updatedTask = task.copy(
                        title = title,
                        description = description
                    )
                    taskRepository.updateTask(updatedTask)
                    loadTask(taskId)
                } else {
                    _uiState.value = TaskDetailUiState.Error("任务不存在")
                }
            } catch (e: Exception) {
                _uiState.value = TaskDetailUiState.Error(e.message ?: "更新任务失败")
            }
        }
    }

    /**
     * 更新任务描述
     * @param taskId 任务ID
     * @param description 新的描述内容
     */
    suspend fun updateTaskDescription(taskId: String, description: String) {
        try {
            android.util.Log.d("TaskDetailViewModel", "更新任务描述: $taskId, 描述长度: ${description.length}")
            
            val task = taskRepository.getTaskById(taskId)
            if (task != null) {
                val updatedTask = task.copy(description = description)
                taskRepository.updateTask(updatedTask)
                android.util.Log.d("TaskDetailViewModel", "✓ 任务描述更新成功")
            } else {
                android.util.Log.e("TaskDetailViewModel", "✗ 任务不存在: $taskId")
                throw IllegalArgumentException("任务不存在")
            }
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "✗ 更新任务描述失败: ${e.message}", e)
            throw e
        }
    }

    fun deleteTask(taskId: String) {
        viewModelScope.launch {
            try {
                taskRepository.deleteTask(taskId)
                _uiState.value = TaskDetailUiState.Deleted
            } catch (e: Exception) {
                _uiState.value = TaskDetailUiState.Error(e.message ?: "删除任务失败")
            }
        }
    }

    /**
     * 强制清除特定任务的缓存
     * @param taskId 任务ID
     */
    suspend fun clearTaskCache(taskId: String) {
        try {
            android.util.Log.d("TaskDetailViewModel", "清除任务缓存: $taskId")
            
            // 如果taskRepository是缓存层，清除特定任务的缓存
            if (taskRepository is TaskRepositoryCache) {
                android.util.Log.d("TaskDetailViewModel", "检测到缓存层，清除任务和子任务缓存")
                // 清除整个缓存以确保获取最新数据
                taskRepository.clearCache()
            }
            
            android.util.Log.d("TaskDetailViewModel", "任务缓存清除完成: $taskId")
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "清除任务缓存失败: ${e.message}", e)
        }
    }
    
    /**
     * 清理被污染的标签数据
     * 调用Repository的数据清理方法
     */
    suspend fun cleanupCorruptedTagData() {
        try {
            android.util.Log.d("TaskDetailViewModel", "开始清理被污染的标签数据")
            
            // 如果repository是TaskRepositoryImpl类型，调用清理方法
            if (taskRepository is com.timeflow.app.data.repository.TaskRepositoryImpl) {
                (taskRepository as com.timeflow.app.data.repository.TaskRepositoryImpl).cleanupCorruptedTagData()
            } else {
                android.util.Log.w("TaskDetailViewModel", "Repository不是TaskRepositoryImpl类型，跳过数据清理")
            }
            
            android.util.Log.d("TaskDetailViewModel", "标签数据清理完成")
        } catch (e: Exception) {
            android.util.Log.e("TaskDetailViewModel", "清理被污染的标签数据失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取TaskDao实例，用于数据修复
     * @return TaskDao实例，如果无法获取则抛出异常
     */
    fun getTaskDao(): com.timeflow.app.data.dao.TaskDao {
        // 尝试从Repository中获取TaskDao
        return when (taskRepository) {
            is com.timeflow.app.data.repository.TaskRepositoryImpl -> {
                // 使用反射获取TaskDao实例
                try {
                    val field = taskRepository.javaClass.getDeclaredField("taskDao")
                    field.isAccessible = true
                    field.get(taskRepository) as com.timeflow.app.data.dao.TaskDao
                } catch (e: Exception) {
                    android.util.Log.e("TaskDetailViewModel", "无法通过反射获取TaskDao", e)
                    throw IllegalStateException("无法获取TaskDao实例: ${e.message}")
                }
            }
            is com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache -> {
                // 缓存包装器，尝试获取底层实现
                try {
                    val field = taskRepository.javaClass.getDeclaredField("taskRepository")
                    field.isAccessible = true
                    val underlyingRepo = field.get(taskRepository)
                    
                    if (underlyingRepo is com.timeflow.app.data.repository.TaskRepositoryImpl) {
                        val taskDaoField = underlyingRepo.javaClass.getDeclaredField("taskDao")
                        taskDaoField.isAccessible = true
                        taskDaoField.get(underlyingRepo) as com.timeflow.app.data.dao.TaskDao
                    } else {
                        throw IllegalStateException("底层Repository不是TaskRepositoryImpl类型")
                    }
                } catch (e: Exception) {
                    android.util.Log.e("TaskDetailViewModel", "无法从缓存包装器获取TaskDao", e)
                    throw IllegalStateException("无法获取TaskDao实例: ${e.message}")
                }
            }
            else -> {
                throw IllegalStateException("Repository类型不支持: ${taskRepository.javaClass.simpleName}")
            }
        }
    }
    
    /**
     * 获取TaskRepository实例，用于数据修复
     * @return TaskRepository实例
     */
    fun getTaskRepository(): TaskRepository {
        return when (taskRepository) {
            is com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache -> {
                // 如果是缓存包装器，返回底层实现
                try {
                    val field = taskRepository.javaClass.getDeclaredField("taskRepository")
                    field.isAccessible = true
                    field.get(taskRepository) as TaskRepository
                } catch (e: Exception) {
                    android.util.Log.w("TaskDetailViewModel", "无法获取底层Repository，返回包装器", e)
                    taskRepository
                }
            }
            else -> taskRepository
        }
    }
    
    /**
     * 标记任务日期为手动修改
     * 确保该任务不会被自动重新调度功能影响
     * @param taskId 任务ID
     */
    fun markTaskDateAsManuallyModified(taskId: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskDetailViewModel", "标记任务日期为手动修改: $taskId")
                taskRepository.updateTaskDateManuallyModified(taskId, true)
                android.util.Log.d("TaskDetailViewModel", "✓ 任务日期标记为手动修改成功")
            } catch (e: Exception) {
                android.util.Log.e("TaskDetailViewModel", "✗ 标记任务日期手动修改失败: ${e.message}", e)
                throw e
            }
        }
    }
}

sealed class TaskDetailUiState {
    object Loading : TaskDetailUiState()
    data class Success(val task: com.timeflow.app.data.model.Task) : TaskDetailUiState()
    data class Error(val message: String) : TaskDetailUiState()
    object Deleted : TaskDetailUiState()
} 