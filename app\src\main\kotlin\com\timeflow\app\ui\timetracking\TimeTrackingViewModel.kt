package com.timeflow.app.ui.timetracking

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.model.AppUsageData
import com.timeflow.app.data.repository.TimeAnalyticsRepository
import com.timeflow.app.service.TimeTrackingService
import com.timeflow.app.ui.timetracking.components.CalendarInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import androidx.compose.ui.graphics.Color
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.screen.task.model.TaskModel
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.isTaskForToday
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.TimeSessionRepository
import com.timeflow.app.data.model.TimeSession
import android.content.SharedPreferences
import java.time.Instant

/**
 * 时间追踪ViewModel
 * 负责管理时间追踪和分析功能
 * 参考Forest、RescueTime等知名时间管理应用的设计模式
 */
@HiltViewModel
class TimeTrackingViewModel @Inject constructor(
    private val timeAnalyticsRepository: TimeAnalyticsRepository,
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository,
    private val timeSessionRepository: TimeSessionRepository,
    private val sharedPreferences: SharedPreferences,
    private val focusTimerManager: com.timeflow.app.service.FocusTimerManager // 🔧 新增：专注计时管理器
) : ViewModel() {
    
    // TaskListViewModel应通过ViewModelProvider获取，而不是直接注入
// 例如：val taskListViewModel = ViewModelProvider(viewModelStoreOwner).get(TaskListViewModel::class.java)
    
    // 是否正在追踪时间
    private val _isTracking = MutableStateFlow(false)
    val isTracking: StateFlow<Boolean> = _isTracking
    
    // 是否在专注模式
    private val _isFocusSessionActive = MutableStateFlow(false)
    val isFocusSessionActive: StateFlow<Boolean> = _isFocusSessionActive
    
    // 专注会话时间（分钟）
    private val _focusSessionTimeMinutes = MutableStateFlow(0)
    val focusSessionTimeMinutes: StateFlow<Int> = _focusSessionTimeMinutes
    
    // 当日应用使用统计
    private val _todayAppUsage = MutableStateFlow<List<AppUsageData>>(emptyList())
    val todayAppUsage: StateFlow<List<AppUsageData>> = _todayAppUsage
    
    // 已连接的日历
    private val _connectedCalendars = MutableStateFlow<List<CalendarInfo>>(emptyList())
    val connectedCalendars: StateFlow<List<CalendarInfo>> = _connectedCalendars
    
    // AI洞察
    private val _aiInsights = MutableStateFlow<List<String>>(emptyList())
    val aiInsights: StateFlow<List<String>> = _aiInsights
    
    // 正在加载AI洞察
    private val _isLoadingInsights = MutableStateFlow(false)
    val isLoadingInsights: StateFlow<Boolean> = _isLoadingInsights
    
    // 应用类别时间统计
    val categoryStats: StateFlow<Map<String, Float>> = 
        timeAnalyticsRepository.getCategoryStatsByDate(LocalDate.now())
            .map { stats ->
                stats.mapValues { (_, duration) -> 
                    duration.inWholeMilliseconds.toFloat() / (1000 * 60 * 60) 
                }
            }
            .stateIn(
                viewModelScope,
                SharingStarted.Lazily,
                emptyMap()
            )
    
    // 生产力时间统计
    val productivityStats: StateFlow<Triple<Float, Float, Float>> =
        timeAnalyticsRepository.getProductivityStatsByDate(LocalDate.now())
            .map { (productiveTime, distractedTime) ->
                val productive = productiveTime.inWholeMilliseconds.toFloat() / (1000 * 60 * 60)
                val distracted = distractedTime.inWholeMilliseconds.toFloat() / (1000 * 60 * 60)
                
                // 假设总时间为8小时，计算中性时间
                val totalTrackedTime = productive + distracted
                val totalTimeBudget = 8f
                val neutral = if (totalTrackedTime < totalTimeBudget) {
                    totalTimeBudget - totalTrackedTime
                } else {
                    0f
                }
                
                Triple(productive, neutral, distracted)
            }
            .stateIn(
                viewModelScope,
                SharingStarted.Lazily,
                Triple(0f, 0f, 0f)
            )
    
    // 总使用时间
    val totalUsageHours: StateFlow<Float> = 
        productivityStats.map { (productive, neutral, distracted) ->
            productive + neutral + distracted
        }
        .stateIn(
            viewModelScope,
            SharingStarted.Lazily,
            0f
        )
    
    // 计时器状态
    private val _timerState = MutableStateFlow(TimerState.IDLE)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    // 当前任务
    private val _currentTask = MutableStateFlow<Task?>(Task("测试任务", "这是一个测试任务的描述", "1"))
    val currentTask: StateFlow<Task?> = _currentTask.asStateFlow()
    
    // 计时时间（秒）
    private val _elapsedTime = MutableStateFlow(0L)
    val elapsedTime: StateFlow<Long> = _elapsedTime.asStateFlow()
    
    // 专注模式
    private val _focusModeEnabled = MutableStateFlow(false)
    val focusModeEnabled: StateFlow<Boolean> = _focusModeEnabled.asStateFlow()
    
    // 任务选择器显示状态
    private val _showTaskSelector = MutableStateFlow(false)
    val showTaskSelector: StateFlow<Boolean> = _showTaskSelector.asStateFlow()
    
    // 计时器类型
    private val _timerType = MutableStateFlow(TimerType.NORMAL)
    val timerType: StateFlow<TimerType> = _timerType.asStateFlow()
    
    // 番茄钟计时
    private val _pomodoroCount = MutableStateFlow(0)
    val pomodoroCount: StateFlow<Int> = _pomodoroCount.asStateFlow()
    
    private val _pomodoroGoal = MutableStateFlow(4)
    val pomodoroGoal: StateFlow<Int> = _pomodoroGoal.asStateFlow()
    
    private val _pomodoroTimeRemaining = MutableStateFlow(25 * 60L) // 25分钟
    val pomodoroTimeRemaining: StateFlow<Long> = _pomodoroTimeRemaining.asStateFlow()
    
    private val _isBreakTime = MutableStateFlow(false)
    val isBreakTime: StateFlow<Boolean> = _isBreakTime.asStateFlow()
    
    // 计时器作业
    private var timerJob: Job? = null
    
    // 任务列表
    private val _taskList = MutableStateFlow<List<TaskModel>>(emptyList())
    val taskList: StateFlow<List<TaskModel>> = _taskList.asStateFlow()
    
    // 加载中状态
    private val _isLoadingTasks = MutableStateFlow(false)
    val isLoadingTasks: StateFlow<Boolean> = _isLoadingTasks.asStateFlow()
    
    // 当前任务的子任务
    private val _currentSubTasks = MutableStateFlow<List<TaskModel>>(emptyList())
    val currentSubTasks: StateFlow<List<TaskModel>> = _currentSubTasks.asStateFlow()
    
    // ===== 新增：持久化和后台计时功能 =====
    companion object {
        private const val TAG = "TimeTrackingViewModel"
        private const val PREF_TIMER_STATE = "timer_state"
        private const val PREF_TIMER_START_TIME = "timer_start_time"
        private const val PREF_ELAPSED_TIME = "elapsed_time"
        private const val PREF_CURRENT_TASK_ID = "current_task_id"
        private const val PREF_CURRENT_TASK_NAME = "current_task_name"
        private const val PREF_TIMER_TYPE = "timer_type"
        private const val PREF_POMODORO_COUNT = "pomodoro_count"
        private const val PREF_POMODORO_TIME_REMAINING = "pomodoro_time_remaining"
        private const val PREF_IS_BREAK_TIME = "is_break_time"
        private const val PREF_CURRENT_SESSION_ID = "current_session_id"
    }
    
    // 当前时间会话
    private val _currentTimeSession = MutableStateFlow<TimeSession?>(null)
    val currentTimeSession: StateFlow<TimeSession?> = _currentTimeSession.asStateFlow()
    
    // 计时开始时间戳
    private var timerStartTime: Long = 0L
    
    // 当前会话ID
    private var currentSessionId: String? = null
    
    // 🔧 新增：SharedPreferences监听器
    private val sharedPreferencesListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
        when (key) {
            PREF_TIMER_STATE, PREF_TIMER_START_TIME, PREF_ELAPSED_TIME -> {
                // 当计时器状态发生变化时，异步刷新状态
                viewModelScope.launch {
                    Log.d(TAG, "🔄 检测到SharedPreferences变化，刷新状态...")
                    refreshTimerState()
                }
            }
        }
    }
    
    init {
        // 初始化数据
        loadTodayAppUsage()
        loadConnectedCalendars()
        generateAIInsights()
        
        // 新增：恢复计时状态
        restoreTimerState()
        
        // 🔧 新增：注册SharedPreferences监听器
        sharedPreferences.registerOnSharedPreferenceChangeListener(sharedPreferencesListener)
    }
    
    override fun onCleared() {
        super.onCleared()
        // 🔧 新增：取消注册监听器
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(sharedPreferencesListener)
    }
    
    /**
     * 🔧 新增：手动刷新计时器状态
     * 用于页面重新进入时强制同步状态
     */
    fun refreshTimerState() {
        Log.d(TAG, "🔄 手动刷新计时器状态...")
        
        // 🔧 重新从持久化存储恢复状态
        val savedState = sharedPreferences.getString(PREF_TIMER_STATE, TimerState.IDLE.name)
        val savedStartTime = sharedPreferences.getLong(PREF_TIMER_START_TIME, 0L)
        val savedElapsedTime = sharedPreferences.getLong(PREF_ELAPSED_TIME, 0L)
        val savedTaskId = sharedPreferences.getString(PREF_CURRENT_TASK_ID, null)
        
        Log.d(TAG, "🔧 当前持久化状态: state=$savedState, startTime=$savedStartTime, elapsed=$savedElapsedTime, taskId=$savedTaskId")
        
        // 🔧 检查状态是否真的发生了变化
        val currentState = _timerState.value
        val newState = try {
            TimerState.valueOf(savedState ?: TimerState.IDLE.name)
        } catch (e: IllegalArgumentException) {
            TimerState.IDLE
        }
        
        when (newState) {
            TimerState.RUNNING -> {
                if (savedStartTime > 0 && savedTaskId != null) {
                    // 🔧 验证任务是否仍然存在
                    viewModelScope.launch {
                        try {
                            val task = taskRepository.getTaskById(savedTaskId)
                            if (task != null) {
                                // 任务存在，恢复运行状态
                                val currentTime = System.currentTimeMillis()
                                val calculatedElapsedTime = (currentTime - savedStartTime) / 1000
                                
                                // 🔧 状态变化处理
                                if (currentState == TimerState.PAUSED) {
                                    Log.d(TAG, "📈 从暂停状态恢复运行")
                                    // 重新计算开始时间
                                    timerStartTime = currentTime - (savedElapsedTime * 1000)
                                } else {
                                    timerStartTime = savedStartTime
                                }
                                
                                _timerState.value = TimerState.RUNNING
                                _elapsedTime.value = if (currentState == TimerState.PAUSED) savedElapsedTime else calculatedElapsedTime
                                
                                // 恢复任务信息
                                _currentTask.value = Task(
                                    name = task.title,
                                    description = task.description,
                                    id = task.id,
                                    color = getTaskColor(task.priority)
                                )
                                
                                // 🔧 如果计时器还没有运行或状态变化了，重新启动
                                if (timerJob?.isActive != true || currentState != TimerState.RUNNING) {
                                    resumeTimer()
                                }
                                
                                Log.d(TAG, "✅ 更新运行中计时器时间: ${_elapsedTime.value}秒")
                            } else {
                                Log.w(TAG, "⚠️ 刷新时发现任务已被删除，清理状态")
                                clearPersistedState()
                                _timerState.value = TimerState.IDLE
                                _elapsedTime.value = 0L
                                _currentTask.value = null
                                timerJob?.cancel()
                                Log.d(TAG, "✅ 无有效状态，保持空闲")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "刷新状态时验证任务失败", e)
                            clearPersistedState()
                            _timerState.value = TimerState.IDLE
                            _elapsedTime.value = 0L
                            _currentTask.value = null
                            timerJob?.cancel()
                        }
                    }
                } else {
                    Log.w(TAG, "⚠️ 运行状态但缺少必要信息，清理状态")
                    clearPersistedState()
                    _timerState.value = TimerState.IDLE
                    _elapsedTime.value = 0L
                    _currentTask.value = null
                    Log.d(TAG, "✅ 无有效状态，保持空闲")
                }
            }
            TimerState.PAUSED -> {
                if (savedTaskId != null) {
                    // 验证任务存在性后恢复暂停状态
                    viewModelScope.launch {
                        try {
                            val task = taskRepository.getTaskById(savedTaskId)
                            if (task != null) {
                                // 🔧 状态变化处理
                                if (currentState == TimerState.RUNNING) {
                                    Log.d(TAG, "⏸️ 从运行状态切换到暂停")
                                    // 停止计时器
                                    timerJob?.cancel()
                                }
                                
                                _timerState.value = TimerState.PAUSED
                                _elapsedTime.value = savedElapsedTime
                                _currentTask.value = Task(
                                    name = task.title,
                                    description = task.description,
                                    id = task.id,
                                    color = getTaskColor(task.priority)
                                )
                                Log.d(TAG, "✅ 更新暂停计时器时间: ${savedElapsedTime}秒")
                            } else {
                                Log.w(TAG, "⚠️ 刷新时发现任务已被删除，清理状态")
                                clearPersistedState()
                                _timerState.value = TimerState.IDLE
                                _elapsedTime.value = 0L
                                _currentTask.value = null
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "刷新状态时验证任务失败", e)
                            clearPersistedState()
                            _timerState.value = TimerState.IDLE
                            _elapsedTime.value = 0L
                            _currentTask.value = null
                        }
                    }
                } else {
                    Log.w(TAG, "⚠️ 暂停状态但无任务ID，清理状态")
                    clearPersistedState()
                    _timerState.value = TimerState.IDLE
                    _elapsedTime.value = 0L
                    _currentTask.value = null
                    Log.d(TAG, "✅ 无有效状态，保持空闲")
                }
            }
            else -> {
                // 空闲状态或无效状态
                if (currentState != TimerState.IDLE) {
                    Log.d(TAG, "⏹️ 切换到空闲状态")
                    timerJob?.cancel()
                }
                _timerState.value = TimerState.IDLE
                _elapsedTime.value = 0L
                _currentTask.value = null
                Log.d(TAG, "✅ 无有效状态，保持空闲")
            }
        }
        
        Log.d(TAG, "✅ 计时器状态刷新完成: state=${_timerState.value}, elapsed=${_elapsedTime.value}")
    }
    
    /**
     * 🔧 优化：恢复持久化的计时状态
     * 确保从小组件返回时不会重置计时器
     */
    private fun restoreTimerState() {
        try {
            val savedState = sharedPreferences.getString(PREF_TIMER_STATE, TimerState.IDLE.name)
            val savedStartTime = sharedPreferences.getLong(PREF_TIMER_START_TIME, 0L)
            val savedElapsedTime = sharedPreferences.getLong(PREF_ELAPSED_TIME, 0L)
            val savedTaskId = sharedPreferences.getString(PREF_CURRENT_TASK_ID, null)
            val savedTimerType = sharedPreferences.getString(PREF_TIMER_TYPE, TimerType.NORMAL.name)
            val savedSessionId = sharedPreferences.getString(PREF_CURRENT_SESSION_ID, null)
            
            Log.d(TAG, "🔧 恢复计时状态: state=$savedState, startTime=$savedStartTime, elapsed=$savedElapsedTime, taskId=$savedTaskId")
            
            when (savedState) {
                TimerState.RUNNING.name -> {
                    if (savedStartTime > 0) {
                        // 🔧 计算应有的经过时间
                        val currentTime = System.currentTimeMillis()
                        val calculatedElapsedTime = (currentTime - savedStartTime) / 1000
                        
                        Log.d(TAG, "📊 计时恢复计算: 当前时间=$currentTime, 开始时间=$savedStartTime, 计算时长=${calculatedElapsedTime}秒")
                        
                        // 🔧 恢复状态
                        _timerState.value = TimerState.RUNNING
                        _elapsedTime.value = calculatedElapsedTime
                        timerStartTime = savedStartTime
                        currentSessionId = savedSessionId
                        
                        // 恢复任务
                        if (savedTaskId != null) {
                            restoreCurrentTask(savedTaskId)
                        }
                        
                        // 恢复计时器类型
                        try {
                            _timerType.value = TimerType.valueOf(savedTimerType ?: TimerType.NORMAL.name)
                        } catch (e: IllegalArgumentException) {
                            _timerType.value = TimerType.NORMAL
                        }
                        
                        // 恢复番茄钟状态
                        if (_timerType.value == TimerType.POMODORO) {
                            _pomodoroCount.value = sharedPreferences.getInt(PREF_POMODORO_COUNT, 0)
                            _pomodoroTimeRemaining.value = sharedPreferences.getLong(PREF_POMODORO_TIME_REMAINING, 25 * 60L)
                            _isBreakTime.value = sharedPreferences.getBoolean(PREF_IS_BREAK_TIME, false)
                        }
                        
                        // 恢复当前会话
                        if (currentSessionId != null) {
                            restoreCurrentSession(currentSessionId!!)
                        }
                        
                        // 🔧 如果计时器还没有运行，重新启动
                        if (timerJob?.isActive != true) {
                            resumeTimer()
                        }
                        
                        Log.d(TAG, "✅ 成功恢复运行状态: ${calculatedElapsedTime}秒")
                    } else {
                        Log.w(TAG, "⚠️ 运行状态但无开始时间，清理状态")
                        clearPersistedState()
                    }
                }
                TimerState.PAUSED.name -> {
                    _timerState.value = TimerState.PAUSED
                    _elapsedTime.value = savedElapsedTime
                    timerStartTime = 0L
                    currentSessionId = savedSessionId
                    
                    // 恢复任务
                    if (savedTaskId != null) {
                        restoreCurrentTask(savedTaskId)
                    }
                    
                    // 恢复计时器类型
                    try {
                        _timerType.value = TimerType.valueOf(savedTimerType ?: TimerType.NORMAL.name)
                    } catch (e: IllegalArgumentException) {
                        _timerType.value = TimerType.NORMAL
                    }
                    
                    // 恢复番茄钟状态
                    if (_timerType.value == TimerType.POMODORO) {
                        _pomodoroCount.value = sharedPreferences.getInt(PREF_POMODORO_COUNT, 0)
                        _pomodoroTimeRemaining.value = sharedPreferences.getLong(PREF_POMODORO_TIME_REMAINING, 25 * 60L)
                        _isBreakTime.value = sharedPreferences.getBoolean(PREF_IS_BREAK_TIME, false)
                    }
                    
                    // 恢复当前会话
                    if (currentSessionId != null) {
                        restoreCurrentSession(currentSessionId!!)
                    }
                    
                    Log.d(TAG, "✅ 成功恢复暂停状态: ${savedElapsedTime}秒")
                }
                else -> {
                    // 清理过期状态
                    clearPersistedState()
                    Log.d(TAG, "✅ 无有效状态，保持空闲")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复计时状态失败", e)
            clearPersistedState()
        }
    }
    
    /**
     * 恢复当前任务
     */
    private fun restoreCurrentTask(taskId: String) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    val uiTask = Task(
                        name = task.title,
                        description = task.description,
                        id = task.id,
                        color = Color(0xFFCCAEC5) // 默认颜色
                    )
                    _currentTask.value = uiTask
                    Log.d(TAG, "✓ 成功恢复任务: ${task.title}")
                } else {
                    Log.w(TAG, "⚠️ 恢复任务失败：任务已被删除 (ID: $taskId)")
                    
                    // 🔧 如果任务已被删除，清理计时状态
                    if (_timerState.value == TimerState.RUNNING || _timerState.value == TimerState.PAUSED) {
                        Log.w(TAG, "🧹 任务已删除，清理计时状态")
                        clearPersistedState()
                        _timerState.value = TimerState.IDLE
                        _elapsedTime.value = 0L
                        _currentTask.value = null
                        timerJob?.cancel()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "恢复任务失败", e)
                
                // 🔧 出错时也清理状态，避免显示无效任务
                Log.w(TAG, "🧹 恢复任务出错，清理计时状态")
                clearPersistedState()
                _timerState.value = TimerState.IDLE
                _elapsedTime.value = 0L
                _currentTask.value = null
                timerJob?.cancel()
            }
        }
    }
    
    /**
     * 恢复当前时间会话
     */
    private fun restoreCurrentSession(sessionId: String) {
        viewModelScope.launch {
            try {
                val session = timeSessionRepository.getSessionById(sessionId)
                _currentTimeSession.value = session
                Log.d(TAG, "✓ 成功恢复时间会话: $sessionId")
            } catch (e: Exception) {
                Log.e(TAG, "恢复时间会话失败", e)
            }
        }
    }
    
    /**
     * 保存计时状态到持久化存储
     * 参考RescueTime的状态保存机制
     */
    private fun saveTimerState() {
        try {
            with(sharedPreferences.edit()) {
                putString(PREF_TIMER_STATE, _timerState.value.name)
                putLong(PREF_TIMER_START_TIME, timerStartTime)
                putLong(PREF_ELAPSED_TIME, _elapsedTime.value)
                putString(PREF_CURRENT_TASK_ID, _currentTask.value?.id)
                putString(PREF_CURRENT_TASK_NAME, _currentTask.value?.name ?: "专注任务")
                putString(PREF_TIMER_TYPE, _timerType.value.name)
                putInt(PREF_POMODORO_COUNT, _pomodoroCount.value)
                putLong(PREF_POMODORO_TIME_REMAINING, _pomodoroTimeRemaining.value)
                putBoolean(PREF_IS_BREAK_TIME, _isBreakTime.value)
                putString(PREF_CURRENT_SESSION_ID, currentSessionId)
                apply()
            }
            Log.d(TAG, "✓ 计时状态已保存，任务: ${_currentTask.value?.name}")

            // 🔧 新增：更新专注计时器小组件
            com.timeflow.app.widget.TimerWidgetUpdater.updateAllTimerWidgets(context)
        } catch (e: Exception) {
            Log.e(TAG, "保存计时状态失败", e)
        }
    }
    
    /**
     * 清理持久化状态
     */
    private fun clearPersistedState() {
        try {
            with(sharedPreferences.edit()) {
                remove(PREF_TIMER_STATE)
                remove(PREF_TIMER_START_TIME)
                remove(PREF_ELAPSED_TIME)
                remove(PREF_CURRENT_TASK_ID)
                remove(PREF_CURRENT_TASK_NAME)
                remove(PREF_TIMER_TYPE)
                remove(PREF_POMODORO_COUNT)
                remove(PREF_POMODORO_TIME_REMAINING)
                remove(PREF_IS_BREAK_TIME)
                remove(PREF_CURRENT_SESSION_ID)
                apply()
            }
            Log.d(TAG, "✓ 持久化状态已清理")

            // 🔧 新增：清理状态后更新小组件
            com.timeflow.app.widget.TimerWidgetUpdater.updateAllTimerWidgets(context)
        } catch (e: Exception) {
            Log.e(TAG, "清理持久化状态失败", e)
        }
    }
    
    /**
     * 创建新的时间会话
     * 参考Toggl的时间会话管理模式
     */
    private suspend fun createTimeSession(): String {
        return try {
            val session = TimeSession(
                id = generateSessionId(),
                taskId = _currentTask.value?.id ?: "",
                taskName = _currentTask.value?.name ?: "未命名任务",
                startTime = Instant.now(),
                endTime = null,
                duration = 0L,
                timerType = _timerType.value.name,
                isCompleted = false,
                notes = "",
                createdAt = Instant.now()
            )
            
            timeSessionRepository.insertSession(session)
            _currentTimeSession.value = session
            currentSessionId = session.id
            
            Log.d(TAG, "✓ 创建时间会话: ${session.id}")
            session.id
        } catch (e: Exception) {
            Log.e(TAG, "创建时间会话失败", e)
            ""
        }
    }
    
    /**
     * 更新时间会话
     */
    private suspend fun updateTimeSession() {
        try {
            val session = _currentTimeSession.value ?: return
            val updatedSession = session.copy(
                duration = _elapsedTime.value,
                endTime = if (_timerState.value == TimerState.IDLE) Instant.now() else null,
                isCompleted = _timerState.value == TimerState.IDLE
            )
            
            timeSessionRepository.updateSession(updatedSession)
            _currentTimeSession.value = updatedSession
            
            Log.d(TAG, "✓ 更新时间会话: ${session.id}, 时长: ${_elapsedTime.value}秒")
        } catch (e: Exception) {
            Log.e(TAG, "更新时间会话失败", e)
        }
    }
    
    /**
     * 生成唯一的会话ID
     */
    private fun generateSessionId(): String {
        return "session_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 恢复计时器（用于后台恢复）
     */
    private fun resumeTimer() {
        if (_timerState.value != TimerState.RUNNING) return
        
        timerJob?.cancel()
        timerJob = viewModelScope.launch {
            try {
                Log.d(TAG, "▶️ 恢复计时器运行")
                
                if (_timerType.value == TimerType.POMODORO) {
                    // 番茄钟计时逻辑
                    while (_timerState.value == TimerState.RUNNING) {
                        delay(1000)
                        
                        if (_isBreakTime.value) {
                            // 休息时间
                            _pomodoroTimeRemaining.value--
                            if (_pomodoroTimeRemaining.value <= 0) {
                                // 休息结束，开始新的工作周期
                                _isBreakTime.value = false
                                _pomodoroTimeRemaining.value = 25 * 60L
                                Log.d(TAG, "🍅 休息结束，开始新的工作周期")
                            }
                        } else {
                            // 工作时间
                            _pomodoroTimeRemaining.value--
                            _elapsedTime.value++
                            
                            if (_pomodoroTimeRemaining.value <= 0) {
                                // 一个番茄钟完成
                                _pomodoroCount.value++
                                _isBreakTime.value = true
                                _pomodoroTimeRemaining.value = if (_pomodoroCount.value % 4 == 0) 15 * 60L else 5 * 60L
                                
                                Log.d(TAG, "🍅 番茄钟完成！计数: ${_pomodoroCount.value}")
                                
                                // 检查是否完成所有番茄钟
                                if (_pomodoroCount.value >= _pomodoroGoal.value) {
                                    Log.d(TAG, "🎉 所有番茄钟完成！")
                                    stopTimer()
                                    break
                                }
                            }
                        }
                        
                        // 🔧 修改：更频繁地保存状态以同步小组件
                        if (_elapsedTime.value % 5 == 0L) { // 每5秒保存一次状态
                            saveTimerState()
                        }

                        // 每30秒更新会话
                        if (_elapsedTime.value % 30 == 0L) {
                            updateTimeSession()
                        }
                    }
                } else {
                    // 普通计时逻辑
                    while (_timerState.value == TimerState.RUNNING) {
                        delay(1000)
                        _elapsedTime.value++

                        // 🔧 修改：更频繁地保存状态以同步小组件
                        if (_elapsedTime.value % 3 == 0L) { // 每3秒保存一次状态
                            saveTimerState()
                        }

                        // 每30秒更新会话
                        if (_elapsedTime.value % 30 == 0L) {
                            updateTimeSession()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "计时器运行异常", e)
            }
        }
    }

    // 重写开始计时方法
    fun startTimer() {
        if (_timerState.value == TimerState.RUNNING) return
        
        viewModelScope.launch {
            try {
                Log.d(TAG, "🚀 开始计时")
                
                if (_timerState.value == TimerState.IDLE) {
                    // 全新开始
                    _elapsedTime.value = 0L
                    timerStartTime = System.currentTimeMillis()
                    
                    // 创建新的时间会话
                    currentSessionId = createTimeSession()
                    
                    // 重置番茄钟状态（如果需要）
                    if (_timerType.value == TimerType.POMODORO) {
                        _pomodoroCount.value = 0
                        _pomodoroTimeRemaining.value = 25 * 60L
                        _isBreakTime.value = false
                    }
                } else {
                    // 从暂停恢复 - 🔧 修复：重新计算开始时间，确保与小组件计算一致
                    timerStartTime = System.currentTimeMillis() - (_elapsedTime.value * 1000)
                    Log.d(TAG, "📈 从暂停恢复计时，重新计算开始时间: $timerStartTime")
                }
                
                _timerState.value = TimerState.RUNNING
                
                // 保存状态
                saveTimerState()
                
                // 🔧 新增：启动前台通知服务
                val taskName = _currentTask.value?.name ?: "专注任务"
                focusTimerManager.startFocusTimerService(taskName)
                
                // 启动计时器
                resumeTimer()
                
                Log.d(TAG, "✅ 计时器已启动")
            } catch (e: Exception) {
                Log.e(TAG, "启动计时器失败", e)
            }
        }
    }

    // 重写暂停计时方法
    fun pauseTimer() {
        if (_timerState.value != TimerState.RUNNING) return
        
        viewModelScope.launch {
            try {
                Log.d(TAG, "⏸️ 暂停计时")
                
                _timerState.value = TimerState.PAUSED
                timerJob?.cancel()
                
                // 保存状态
                saveTimerState()
                
                // 更新会话
                updateTimeSession()
                
                Log.d(TAG, "✅ 计时器已暂停，时长: ${_elapsedTime.value}秒")
            } catch (e: Exception) {
                Log.e(TAG, "暂停计时器失败", e)
            }
        }
    }

    // 重写停止计时方法
    fun stopTimer() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "⏹️ 停止计时")
                
                val finalElapsedTime = _elapsedTime.value
                
                _timerState.value = TimerState.IDLE
                timerJob?.cancel()
                
                // 完成当前会话
                updateTimeSession()
                
                // 清理状态
                _elapsedTime.value = 0L
                timerStartTime = 0L
                _currentTimeSession.value = null
                currentSessionId = null
                
                // 重置番茄钟状态
                if (_timerType.value == TimerType.POMODORO) {
                    _pomodoroCount.value = 0
                    _pomodoroTimeRemaining.value = 25 * 60L
                    _isBreakTime.value = false
                }
                
                // 清理持久化状态
                clearPersistedState()
                
                // 🔧 新增：停止前台通知服务
                focusTimerManager.stopFocusTimerService()
                
                Log.d(TAG, "✅ 计时器已停止，总时长: ${finalElapsedTime}秒")
                
                // 如果有任务，更新任务的累计时间
                _currentTask.value?.let { task ->
                    updateTaskTime(task.id, finalElapsedTime)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "停止计时器失败", e)
            }
        }
    }
    
    /**
     * 更新任务累计时间
     */
    private suspend fun updateTaskTime(taskId: String, additionalTime: Long) {
        try {
            // 这里可以调用TaskRepository更新任务的累计时间
            Log.d(TAG, "更新任务 $taskId 累计时间: +${additionalTime}秒")
        } catch (e: Exception) {
            Log.e(TAG, "更新任务时间失败", e)
        }
    }
    
    /**
     * 获取今日时间会话历史
     */
    fun getTodayTimeSessions(): Flow<List<TimeSession>> {
        return timeSessionRepository.getSessionsByDate(LocalDate.now())
    }
    
    /**
     * 获取任务的累计时间
     */
    fun getTaskTotalTime(taskId: String): Flow<Long> {
        return timeSessionRepository.getTaskTotalTime(taskId)
    }
    
    /**
     * 获取今日总计时时长
     */
    fun getTodayTotalTime(): Flow<Long> {
        return timeSessionRepository.getTodayTotalTime()
    }
    
    /**
     * 开始/停止时间追踪
     */
    fun toggleTimeTracking() {
        _isTracking.value = !_isTracking.value
        
        if (_isTracking.value) {
            // 开始追踪
            val intent = Intent(context, TimeTrackingService::class.java).apply {
                action = "START_TRACKING"
            }
            context.startService(intent)
        } else {
            // 停止追踪
            val intent = Intent(context, TimeTrackingService::class.java).apply {
                action = "STOP_TRACKING"
            }
            context.startService(intent)
        }
    }
    
    /**
     * 开始专注会话
     */
    fun startFocusSession() {
        _isFocusSessionActive.value = true
        _focusSessionTimeMinutes.value = 0
        
        // 开始计时
        viewModelScope.launch {
            while (_isFocusSessionActive.value) {
                kotlinx.coroutines.delay(60000) // 1分钟
                _focusSessionTimeMinutes.value += 1
            }
        }
    }
    
    /**
     * 结束专注会话
     */
    fun endFocusSession() {
        _isFocusSessionActive.value = false
    }
    
    /**
     * 加载今日应用使用情况
     */
    private fun loadTodayAppUsage() {
        viewModelScope.launch {
            timeAnalyticsRepository.getAppUsageByDate(LocalDate.now())
                .collect { appUsageList ->
                    _todayAppUsage.value = appUsageList
                }
        }
    }
    
    /**
     * 加载已连接的日历
     */
    private fun loadConnectedCalendars() {
        // 模拟数据
        _connectedCalendars.value = listOf(
            CalendarInfo(
                id = "1",
                name = "个人日历",
                accountName = "<EMAIL>",
                color = 0xFF4CAF50.toInt()
            )
        )
    }
    
    /**
     * 连接新日历
     */
    fun connectCalendar() {
        // 实际应用中，这里应该弹出日历选择器
        // 这里只是模拟添加一个新日历
        val currentCalendars = _connectedCalendars.value.toMutableList()
        currentCalendars.add(
            CalendarInfo(
                id = "2",
                name = "工作日历",
                accountName = "<EMAIL>",
                color = 0xFF2196F3.toInt()
            )
        )
        _connectedCalendars.value = currentCalendars
    }
    
    /**
     * 断开日历连接
     */
    fun disconnectCalendar(calendarId: String) {
        _connectedCalendars.value = _connectedCalendars.value.filter { it.id != calendarId }
    }
    
    /**
     * 生成AI洞察
     */
    fun generateAIInsights() {
        _isLoadingInsights.value = true
        
        viewModelScope.launch {
            kotlinx.coroutines.delay(1500) // 模拟网络请求
            
            // 模拟生成洞察
            _aiInsights.value = listOf(
                "今天您在社交应用上花费了比平时多30%的时间",
                "您的高效工作时段集中在上午9点到11点",
                "建议减少下午频繁切换应用，可提高工作效率约15%",
                "与昨天相比，您的生产力得分提高了8%"
            )
            
            _isLoadingInsights.value = false
        }
    }
    
    /**
     * 保存模拟数据（测试用）
     */
    fun saveTestData() {
        viewModelScope.launch {
            val today = LocalDate.now()
            
            val testData = listOf(
                AppUsageData("com.android.chrome", 2.5.hours, 15, "生产力", true),
                AppUsageData("com.google.android.gm", 1.2.hours, 8, "生产力", true),
                AppUsageData("com.android.calendar", 0.5.hours, 3, "生产力", true),
                AppUsageData("com.instagram.android", 1.8.hours, 12, "社交", false),
                AppUsageData("com.whatsapp", 1.0.hours, 20, "通讯", false),
                AppUsageData("com.spotify.music", 2.0.hours, 4, "娱乐", false),
                AppUsageData("com.android.vending", 0.3.hours, 2, "工具", true),
                AppUsageData("com.google.android.youtube", 1.5.hours, 6, "娱乐", false)
            )
            
            timeAnalyticsRepository.saveAppUsageBatch(testData, today)
        }
    }
    
    // 切换计时器类型
    fun toggleTimerType() {
        _timerType.value = when (_timerType.value) {
            TimerType.NORMAL -> TimerType.POMODORO
            TimerType.POMODORO -> TimerType.NORMAL
        }
        
        // 重置计时器
        stopTimer()
    }
    
    // 设置番茄钟目标
    fun setPomodoroGoal(goal: Int) {
        _pomodoroGoal.value = goal
    }
    
    // 跳过当前番茄钟/休息时间
    fun skipCurrentPomodoro() {
        if (_timerType.value == TimerType.POMODORO) {
            if (_isBreakTime.value) {
                // 跳过休息
                _isBreakTime.value = false
                _pomodoroTimeRemaining.value = 25 * 60
            } else {
                // 跳过工作时间
                _pomodoroCount.value++
                _isBreakTime.value = true
                _pomodoroTimeRemaining.value = if (_pomodoroCount.value % 4 == 0) 15 * 60 else 5 * 60
            }
        }
    }
    
    // 切换专注模式
    fun toggleFocusMode() {
        _focusModeEnabled.value = !_focusModeEnabled.value
    }
    
    // 切换任务选择器
    fun toggleTaskSelector() {
        _showTaskSelector.value = !_showTaskSelector.value
    }
    
    /**
     * 加载任务列表
     */
    fun loadTasks(taskListViewModel: TaskListViewModel) {
        viewModelScope.launch {
            _isLoadingTasks.value = true
            try {
                // 加载所有任务
                taskListViewModel.loadTasks()
                
                // 等待任务加载完成
                delay(100)
                
                // 获取任务列表状态
                val taskListState = taskListViewModel.taskListState.value
                
                // 直接使用TaskListFullScreen中相同的"今天"过滤逻辑
                val todayTasks = taskListState.tasks.filter { task ->
                    isTaskForToday(task)
                }
                
                // 转换为时间追踪模块的TaskModel格式
                _taskList.value = todayTasks.map { taskData ->
                    TaskModel(
                        id = taskData.id,
                        title = taskData.title,
                        description = taskData.description,
                        daysLeft = taskData.daysLeft,
                        urgency = taskData.urgency
                    )
                }
                
                Log.d("TimeTrackingViewModel", "加载今天的任务数量: ${_taskList.value.size}")
                
            } catch (e: Exception) {
                Log.e("TimeTrackingViewModel", "加载任务失败", e)
                _taskList.value = emptyList()
            } finally {
                _isLoadingTasks.value = false
            }
        }
    }
    
    /**
     * 判断任务是否为今天的任务 - 与TaskListFullScreen中的逻辑保持一致
     */
    private fun isTaskForToday(task: ModelTaskData): Boolean {
        val today = java.time.LocalDate.now()
        
        return when {
            // 如果有具体的dueDate日期，则直接与今天比较
            task.dueDate != null -> {
                task.dueDate.toLocalDate().isEqual(today)
            }
            // 通过daysLeft计算，如果正好是0天，则表示今天
            task.daysLeft == 0 -> true
            // 或者通过daysLeft计算出来的日期与今天相等
            task.daysLeft > 0 -> {
                val dueDate = today.plusDays(task.daysLeft.toLong())
                dueDate.isEqual(today)
            }
            else -> false
        }
    }
    
    /**
     * 解析任务描述字段中可能存在的颜色JSON信息
     * 返回Pair<String, Color?>，第一个为过滤后的描述文本，第二个为解析出的颜色（可能为null）
     */
    private fun parseColorFromDescription(description: String): Pair<String, Color?> {
        // 正则表达式匹配颜色JSON格式
        val colorPattern = """\{"color":(\d+)\}""".toRegex()
        val matchResult = colorPattern.find(description)
        
        return if (matchResult != null) {
            // 从描述中提取颜色值
            val colorValue = matchResult.groupValues[1].toLongOrNull()
            val color = colorValue?.let { androidx.compose.ui.graphics.Color(it.toInt()) }
            
            // 移除颜色JSON字符串，返回纯文本描述
            val cleanDescription = description.replace(colorPattern, "").trim()
            
            android.util.Log.d("TimeTrackingViewModel", "解析到颜色: $colorValue, 净化后描述: $cleanDescription")
            Pair(cleanDescription, color)
        } else {
            // 没有找到颜色信息，返回原始描述
            Pair(description, null)
        }
    }
    
    /**
     * 设置当前任务
     */
    fun setCurrentTask(task: TaskModel, taskListViewModel: TaskListViewModel) {
        // 解析描述中的颜色信息
        val (cleanDescription, taskColor) = parseColorFromDescription(task.description)
        
        _currentTask.value = Task(
            name = task.title,
            description = cleanDescription, // 使用清理后的描述
            id = task.id,
            color = taskColor ?: getTaskColorFromUrgency(task.urgency) // 使用urgency而不是priority
        )
        taskListViewModel.setCurrentTask(task.id)
        
        // 🔧 新增：如果计时器正在运行，更新前台服务的任务名称
        if (_timerState.value != TimerState.IDLE) {
            focusTimerManager.updateTaskName(task.title)
        }
    }
    
    /**
     * 设置当前任务（通过任务ID）
     */
    fun setCurrentTask(taskId: String) {
        viewModelScope.launch {
            try {
                // 从数据库中获取真实的任务信息
                val taskEntity = taskRepository.getTaskById(taskId)
                if (taskEntity != null) {
                    // 解析描述中的颜色信息
                    val originalDescription = taskEntity.description ?: "正在计时中"
                    val (cleanDescription, taskColor) = parseColorFromDescription(originalDescription)
                    
                    _currentTask.value = Task(
                        name = taskEntity.title,
                        description = cleanDescription, // 使用清理后的描述
                        id = taskEntity.id,
                        color = taskColor ?: getTaskColor(taskEntity.priority) // 优先使用解析出的颜色
                    )
                    
                    // 🔧 新增：如果计时器正在运行，更新前台服务的任务名称
                    if (_timerState.value != TimerState.IDLE) {
                        focusTimerManager.updateTaskName(taskEntity.title)
                    }
                } else {
                    // 如果找不到任务，使用默认信息
                    _currentTask.value = Task(
                        name = "任务 #" + taskId.takeLast(4),
                        description = "正在计时中",
                        id = taskId
                    )
                }
            } catch (e: Exception) {
                // 出错时使用默认信息
                _currentTask.value = Task(
                    name = "任务 #" + taskId.takeLast(4),
                    description = "正在计时中",
                    id = taskId
                )
            }
        }
    }
    
    /**
     * 根据任务优先级获取颜色
     */
    private fun getTaskColor(priority: com.timeflow.app.data.model.Priority?): Color {
        return when(priority) {
            com.timeflow.app.data.model.Priority.URGENT -> Color(0xFFFF4D4D)
            com.timeflow.app.data.model.Priority.HIGH -> Color(0xFFFF8FB3)
            com.timeflow.app.data.model.Priority.MEDIUM -> Color(0xFFB9A0FF)
            com.timeflow.app.data.model.Priority.LOW -> Color(0xFF64E1FF)
            null -> Color(0xFFCCAEC5)
        }
    }
    
    /**
     * 根据任务紧急度获取颜色
     */
    private fun getTaskColorFromUrgency(urgency: TaskUrgency): Color {
        return when(urgency) {
            TaskUrgency.CRITICAL -> Color(0xFFFF4D4D)
            TaskUrgency.HIGH -> Color(0xFFFF8FB3)
            TaskUrgency.MEDIUM -> Color(0xFFB9A0FF)
            TaskUrgency.LOW -> Color(0xFF64E1FF)
        }
    }
    
    /**
     * 创建新任务并自动开始计时
     * @param taskName 任务名称
     * @param taskDescription 任务描述
     * @param priority 任务优先级
     * @param taskListViewModel 任务列表ViewModel，用于保存任务
     */
    fun createTaskAndStartTimer(
        taskName: String,
        taskDescription: String = "",
        priority: com.timeflow.app.data.model.Priority = com.timeflow.app.data.model.Priority.MEDIUM,
        taskListViewModel: TaskListViewModel
    ) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "===== 开始创建任务并自动计时 =====")
                Log.d(TAG, "任务名称: $taskName")
                Log.d(TAG, "任务描述: $taskDescription")
                Log.d(TAG, "优先级: $priority")
                
                // 1. 创建新任务
                val newTask = com.timeflow.app.data.model.Task(
                    id = java.util.UUID.randomUUID().toString(),
                    title = taskName,
                    description = taskDescription,
                    priority = priority,
                    dueDate = null, // 快速创建不设置截止日期
                    createdAt = java.time.LocalDateTime.now(),
                    updatedAt = java.time.LocalDateTime.now()
                )
                
                Log.d(TAG, "Step 1: 创建任务对象，ID=${newTask.id}")
                
                // 2. 保存到数据库
                taskListViewModel.saveTask(newTask)
                Log.d(TAG, "Step 2: 任务已保存到数据库")
                
                // 3. 等待保存完成
                delay(200)
                
                // 4. 刷新任务列表
                taskListViewModel.refreshTasks()
                Log.d(TAG, "Step 3: 任务列表已刷新")
                
                // 5. 设置为当前任务
                // 解析描述中的颜色信息
                val (cleanDescription, taskColor) = parseColorFromDescription(newTask.description)
                
                _currentTask.value = Task(
                    name = newTask.title,
                    description = cleanDescription, // 使用清理后的描述
                    id = newTask.id,
                    color = taskColor ?: getTaskColor(newTask.priority) // 优先使用解析出的颜色
                )
                Log.d(TAG, "Step 4: 已设置为当前任务")
                
                // 6. 自动开始计时
                startTimer()
                Log.d(TAG, "Step 5: 已自动开始计时")
                
                Log.d(TAG, "✓ 任务创建并自动计时成功完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "创建任务并开始计时失败", e)
                throw e // 重新抛出异常，让调用方处理
            }
        }
    }
    
    /**
     * 快速设置任务并开始计时（用于外部创建的任务）
     * @param simpleTask 简化的任务对象
     */
    fun setTaskAndStartTimer(simpleTask: Task) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "===== 设置任务并开始计时 =====")
                Log.d(TAG, "任务: ${simpleTask.name} (ID: ${simpleTask.id})")
                
                // 1. 设置当前任务
                // 解析描述中的颜色信息
                val (cleanDescription, taskColor) = parseColorFromDescription(simpleTask.description)
                
                _currentTask.value = simpleTask.copy(
                    description = cleanDescription, // 使用清理后的描述
                    color = taskColor ?: simpleTask.color // 优先使用解析出的颜色
                )
                Log.d(TAG, "Step 1: 已设置当前任务")
                
                // 2. 自动开始计时
                startTimer()
                Log.d(TAG, "Step 2: 已自动开始计时")
                
                Log.d(TAG, "✓ 任务设置并自动计时成功")
                
            } catch (e: Exception) {
                Log.e(TAG, "设置任务并开始计时失败", e)
            }
        }
    }
    
    /**
     * 监听当前任务状态变化，当任务被删除或完成时自动处理
     * @param taskListViewModel 任务列表ViewModel，用于监听任务状态
     */
    fun monitorCurrentTaskStatus(taskListViewModel: TaskListViewModel) {
        viewModelScope.launch {
            // 🔧 添加防抖延迟，避免页面重新进入时的误判
            delay(1000) // 等待1秒，让任务列表完全加载
            
            var isFirstEmission = true // 标记是否是第一次接收状态
            
            taskListViewModel.taskListState.collect { taskListState ->
                val currentTaskId = _currentTask.value?.id
                if (currentTaskId != null) {
                    
                    // 🔧 新增：如果是第一次接收且任务列表为空，跳过处理
                    // 这是为了处理页面重新进入时任务列表还未加载完成的情况
                    if (isFirstEmission && taskListState.tasks.isEmpty()) {
                        Log.d(TAG, "🔄 首次状态更新，任务列表为空，跳过处理")
                        isFirstEmission = false
                        return@collect
                    }
                    
                    isFirstEmission = false
                    
                    val currentTaskData = taskListState.tasks.find { it.id == currentTaskId }
                    
                    when {
                        // 任务被删除 - 增加二次确认机制
                        currentTaskData == null -> {
                            Log.w(TAG, "⚠️ 当前任务(ID: $currentTaskId)在任务列表中未找到")
                            
                            // 🔧 增加二次确认：直接从数据库查询任务是否真的被删除
                            val taskFromDb = try {
                                taskRepository.getTaskById(currentTaskId)
                            } catch (e: Exception) {
                                Log.e(TAG, "查询任务失败", e)
                                null
                            }
                            
                            if (taskFromDb == null) {
                                // 确认任务真的被删除
                                Log.w(TAG, "❌ 确认任务已被删除，停止计时")
                                if (_timerState.value == TimerState.RUNNING || _timerState.value == TimerState.PAUSED) {
                                    stopTimer()
                                }
                                _currentTask.value = null
                            } else {
                                // 任务存在但不在列表中，可能是过滤问题
                                Log.d(TAG, "✅ 任务在数据库中存在，保持计时状态")
                                
                                // 更新当前任务信息
                                _currentTask.value = _currentTask.value?.copy(
                                    name = taskFromDb.title,
                                    description = taskFromDb.description
                                )
                            }
                        }
                        // 任务被标记为完成
                        currentTaskData.isCompleted -> {
                            Log.i(TAG, "✅ 当前任务(ID: $currentTaskId)已完成，停止计时")
                            if (_timerState.value == TimerState.RUNNING || _timerState.value == TimerState.PAUSED) {
                                stopTimer()
                            }
                            // 可选：保持任务信息但标记为完成状态，或清除当前任务
                            _currentTask.value = null
                        }
                        // 任务信息更新（标题、描述等）
                        else -> {
                            // 更新当前任务信息以反映最新变化
                            val updatedTask = _currentTask.value?.copy(
                                name = currentTaskData.title,
                                description = currentTaskData.description
                            )
                            if (updatedTask != _currentTask.value) {
                                _currentTask.value = updatedTask
                                Log.d(TAG, "📝 当前任务信息已更新: ${currentTaskData.title}")
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 清除当前任务（供外部调用）
     */
    fun clearCurrentTask() {
        _currentTask.value = null
        Log.d(TAG, "当前任务已清除")
    }
    
    /**
     * 检查任务是否有效（未删除且未完成）
     * @param taskId 任务ID
     * @param taskListViewModel 任务列表ViewModel
     * @return 任务是否有效
     */
    fun isTaskValid(taskId: String, taskListViewModel: TaskListViewModel): Boolean {
        val taskListState = taskListViewModel.taskListState.value
        val task = taskListState.tasks.find { it.id == taskId }
        return task != null && !task.isCompleted
    }


}

// 计时器状态
enum class TimerState {
    IDLE, RUNNING, PAUSED
}

// 任务数据类
data class Task(
    val name: String,
    val description: String = "",
    val id: String = "",
    val color: Color = Color(0xFF6571FF)
)

// 辅助函数：格式化时间
fun formatElapsedTime(timeInSeconds: Long): String {
    val hours = timeInSeconds / 3600
    val minutes = (timeInSeconds % 3600) / 60
    val seconds = timeInSeconds % 60
    return String.format("%02d:%02d:%02d", hours, minutes, seconds)
}

// 添加计时器类型枚举
enum class TimerType {
    NORMAL, POMODORO
}

// 格式化剩余时间
fun formatPomodoroTime(timeInSeconds: Long): String {
    val minutes = timeInSeconds / 60
    val seconds = timeInSeconds % 60
    return String.format("%02d:%02d", minutes, seconds)
} 