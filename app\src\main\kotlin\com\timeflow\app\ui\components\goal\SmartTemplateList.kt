package com.timeflow.app.ui.components.goal

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.AutoAwesome
import androidx.compose.material.icons.outlined.Category
import androidx.compose.material.icons.outlined.ErrorOutline
import androidx.compose.material.icons.outlined.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.timeflow.app.data.model.GoalTemplate
import com.timeflow.app.ui.theme.DustyLavender
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.PagedTemplateResult

/**
 * 智能模板列表组件
 * 展示分类的模板推荐，便于用户快速选择合适的目标模板
 */
@Composable
fun SmartTemplateList(
    categorizedTemplates: Map<String, List<GoalTemplate>>,
    onTemplateSelected: (String) -> Unit,
    onDismiss: () -> Unit,
    isLoading: Boolean = false,
    error: String? = null,
    onLoadMore: ((String, Int) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    // 获取状态栏高度
    val statusBarHeightDp = SystemBarManager.getFixedStatusBarHeight()
    
    // 保存每个分类的当前页码
    val categoryPages = remember { mutableStateMapOf<String, Int>() }
    
    // 保存分类的加载状态
    val categoryLoadingState = remember { mutableStateMapOf<String, Boolean>() }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
        ) {
            // 顶部状态栏空间
            Spacer(modifier = Modifier.height(height = statusBarHeightDp))
            
            // 标题区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp, bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Outlined.AutoAwesome,
                    contentDescription = null,
                    tint = DustyLavender,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "智能模板推荐",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Spacer(modifier = Modifier.weight(1f))
                TextButton(onClick = onDismiss) {
                    Text(
                        text = "关闭",
                        color = Color(0xFF666666)
                    )
                }
            }
            
            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
            ) {
                // 加载状态显示
                if (isLoading) {
                    LoadingState(modifier = Modifier.align(Alignment.Center))
                } 
                // 错误状态显示
                else if (error != null) {
                    ErrorState(error = error, modifier = Modifier.align(Alignment.Center))
                }
                // 空数据状态显示
                else if (categorizedTemplates.isEmpty() || categorizedTemplates.all { it.value.isEmpty() }) {
                    EmptyState(modifier = Modifier.align(Alignment.Center))
                }
                // 显示模板列表
                else {
                    // 分类模板列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(24.dp)
                    ) {
                        categorizedTemplates.forEach { (category, templates) ->
                            // 只显示有模板的类别
                            if (templates.isNotEmpty()) {
                                item(key = category) {
                                    CategoryTemplates(
                                        category = category,
                                        templates = templates,
                                        onTemplateSelected = onTemplateSelected,
                                        isLoadingMore = categoryLoadingState[category] ?: false,
                                        onLoadMore = if (onLoadMore != null) {
                                            {
                                                // 更新页码和加载状态
                                                val currentPage = categoryPages.getOrDefault(category, 1)
                                                val nextPage = currentPage + 1
                                                categoryPages[category] = nextPage
                                                categoryLoadingState[category] = true
                                                
                                                // 调用加载更多回调
                                                onLoadMore(category, nextPage)
                                                
                                                // 请求完成后重置加载状态
                                                // 理想情况下，这应该由调用者在数据加载完成后设置
                                                categoryLoadingState[category] = false
                                            }
                                        } else null
                                    )
                                }
                            }
                        }
                        
                        // 底部间距
                        item {
                            Spacer(modifier = Modifier.height(20.dp))
                        }
                    }
                }
            }
        }
    }
}

/**
 * 加载状态组件
 */
@Composable
private fun LoadingState(modifier: Modifier = Modifier) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = modifier.fillMaxWidth()
    ) {
        CircularProgressIndicator(
            color = DustyLavender,
            modifier = Modifier.size(40.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "正在加载模板...",
            fontSize = 16.sp,
            color = Color(0xFF666666),
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 错误状态组件
 */
@Composable
private fun ErrorState(error: String, modifier: Modifier = Modifier) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = modifier.fillMaxWidth().padding(32.dp)
    ) {
        Icon(
            imageVector = Icons.Outlined.ErrorOutline,
            contentDescription = null,
            tint = Color(0xFFE57373),
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "加载模板时出错",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = error,
            fontSize = 14.sp,
            color = Color(0xFF666666),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(24.dp))
        Button(
            onClick = { /* 重试逻辑 */ },
            colors = ButtonDefaults.buttonColors(
                containerColor = DustyLavender
            )
        ) {
            Icon(
                imageVector = Icons.Outlined.Refresh,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(text = "重试")
        }
    }
}

/**
 * 空状态组件
 */
@Composable
private fun EmptyState(modifier: Modifier = Modifier) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = modifier.fillMaxWidth().padding(32.dp)
    ) {
        Icon(
            imageVector = Icons.Outlined.Category,
            contentDescription = null,
            tint = DustyLavender.copy(alpha = 0.7f),
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "暂无可用模板",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF333333),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "我们正在准备精选的目标模板，敬请期待",
            fontSize = 14.sp,
            color = Color(0xFF666666),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 示例模板卡片
        Card(
            modifier = Modifier
                .width(280.dp)
                .padding(top = 8.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 2.dp
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(DustyLavender.copy(alpha = 0.2f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Outlined.AutoAwesome,
                        contentDescription = null,
                        tint = DustyLavender,
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "学习编程基础",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "每周学习3次，3个月内掌握编程基础知识",
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "示例模板",
                    fontSize = 12.sp,
                    color = Color(0xFF999999),
                    modifier = Modifier
                        .background(
                            color = Color(0xFFF5F5F5),
                            shape = RoundedCornerShape(12.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

/**
 * 单个类别的模板展示
 */
@Composable
private fun CategoryTemplates(
    category: String,
    templates: List<GoalTemplate>,
    onTemplateSelected: (String) -> Unit,
    isLoadingMore: Boolean = false,
    onLoadMore: (() -> Unit)? = null
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 类别标题
        Text(
            text = category,
            fontSize = 16.sp,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF333333),
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        // 横向滚动的模板列表
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(templates) { template ->
                TemplateCard(
                    template = template,
                    onClick = { onTemplateSelected(template.id) }
                )
            }
            
            // 加载更多按钮
            if (onLoadMore != null) {
                item {
                    LoadMoreCard(
                        onClick = onLoadMore,
                        isLoading = isLoadingMore
                    )
                }
            }
        }
    }
}

/**
 * 模板卡片组件
 */
@Composable
private fun TemplateCard(
    template: GoalTemplate,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .width(180.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 顶部彩条
            val color = try {
                Color(android.graphics.Color.parseColor(template.colorHex))
            } catch (e: Exception) {
                DustyLavender
            }
            
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .background(
                        Brush.horizontalGradient(
                            colors = listOf(
                                color,
                                color.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 模板内容
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 4.dp)
            ) {
                // 标题行
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 模板标题
                    Text(
                        text = template.name,
                        fontSize = 15.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF333333),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 使用次数指示器
                    if (template.usageCount > 0) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = null,
                                tint = Color(0xFFF59E0B),
                                modifier = Modifier.size(14.dp)
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            Text(
                                text = "${template.usageCount}",
                                fontSize = 12.sp,
                                color = Color(0xFF666666)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(6.dp))
                
                // 描述
                Text(
                    text = template.description,
                    fontSize = 13.sp,
                    color = Color(0xFF666666),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    lineHeight = 16.sp
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 添加按钮
                Button(
                    onClick = onClick,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(32.dp),
                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 0.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = color.copy(alpha = 0.1f),
                        contentColor = color
                    ),
                    shape = RoundedCornerShape(6.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "使用此模板",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

/**
 * 加载更多卡片
 */
@Composable
private fun LoadMoreCard(
    onClick: () -> Unit,
    isLoading: Boolean = false
) {
    Card(
        modifier = Modifier
            .width(100.dp)
            .clickable(onClick = onClick, enabled = !isLoading),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(180.dp),
            contentAlignment = Alignment.Center
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    color = DustyLavender,
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
            } else {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.ExpandMore,
                        contentDescription = "加载更多",
                        tint = DustyLavender,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "加载更多",
                        fontSize = 12.sp,
                        color = DustyLavender,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
} 