package com.timeflow.app.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.room.Room
import com.timeflow.app.data.db.AppDatabase
import kotlinx.coroutines.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 数据库备份管理器
 * 用于在版本更新时保护用户数据
 */
class DatabaseBackupManager(
    private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences("database_backup", Context.MODE_PRIVATE)
    private val backupDir = File(context.filesDir, "database_backups")
    
    companion object {
        private const val TAG = "DatabaseBackupManager"
        private const val PREF_LAST_BACKUP_VERSION = "last_backup_version"
        private const val PREF_CURRENT_DB_VERSION = "current_db_version"
        private const val PREF_LAST_BACKUP_TIME = "last_backup_time"
        private const val PREF_AUTO_BACKUP_ENABLED = "auto_backup_enabled"
        private const val PREF_AUTO_BACKUP_FREQUENCY = "auto_backup_frequency"
        private const val PREF_MAX_BACKUP_COUNT = "max_backup_count"
        private const val DEFAULT_MAX_BACKUP_COUNT = 5 // 默认最多保留5个备份文件
    }
    
    init {
        // 确保备份目录存在
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
    }
    
    /**
     * 检查是否需要备份数据库
     */
    fun shouldBackupDatabase(): Boolean {
        val currentVersion = getCurrentDatabaseVersion()
        val lastBackupVersion = prefs.getInt(PREF_LAST_BACKUP_VERSION, 0)
        
        Log.d(TAG, "当前数据库版本: $currentVersion, 上次备份版本: $lastBackupVersion")
        
        return currentVersion > lastBackupVersion
    }
    
    /**
     * 备份当前数据库
     */
    suspend fun backupDatabase(): Boolean = withContext(Dispatchers.IO) {
        try {
            val dbFile = context.getDatabasePath("timeflow_database")
            if (!dbFile.exists()) {
                Log.w(TAG, "数据库文件不存在，无需备份")
                return@withContext true
            }
            
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val currentVersion = getCurrentDatabaseVersion()
            val backupFile = File(backupDir, "timeflow_database_v${currentVersion}_$timestamp.db")
            
            // 执行文件复制
            dbFile.copyTo(backupFile, overwrite = true)
            
            val currentTime = System.currentTimeMillis()
            
            // 更新备份记录
            prefs.edit()
                .putInt(PREF_LAST_BACKUP_VERSION, currentVersion)
                .putLong("backup_time_$currentVersion", currentTime)
                .putLong(PREF_LAST_BACKUP_TIME, currentTime)
                .apply()
            
            // 清理旧备份文件
            cleanupOldBackups()
            
            Log.i(TAG, "数据库备份成功: ${backupFile.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "数据库备份失败", e)
            false
        }
    }
    
    /**
     * 恢复数据库备份（通过版本号）
     */
    suspend fun restoreDatabase(backupVersion: Int): Boolean = withContext(Dispatchers.IO) {
        try {
            val backupFiles = getBackupFiles().filter { it.name.contains("_v${backupVersion}_") }
            if (backupFiles.isEmpty()) {
                Log.w(TAG, "找不到版本 $backupVersion 的备份文件")
                return@withContext false
            }
            
            // 选择最新的备份文件
            val latestBackup = backupFiles.maxByOrNull { it.lastModified() }!!
            val dbFile = context.getDatabasePath("timeflow_database")
            
            // 创建当前数据库的紧急备份
            val emergencyBackup = File(backupDir, "emergency_backup_${System.currentTimeMillis()}.db")
            if (dbFile.exists()) {
                dbFile.copyTo(emergencyBackup, overwrite = true)
            }
            
            // 恢复备份
            latestBackup.copyTo(dbFile, overwrite = true)
            
            Log.i(TAG, "数据库恢复成功，从备份: ${latestBackup.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "数据库恢复失败", e)
            false
        }
    }
    
    /**
     * 恢复数据库备份（通过文件名）
     * 用于从UI直接恢复指定的备份文件
     */
    suspend fun restoreBackup(backupFileName: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val backupFile = File(backupDir, backupFileName)
            if (!backupFile.exists()) {
                Log.w(TAG, "找不到备份文件: $backupFileName")
                return@withContext false
            }
            
            val dbFile = context.getDatabasePath("timeflow_database")
            
            // 创建当前数据库的紧急备份
            val emergencyBackup = File(backupDir, "emergency_backup_${System.currentTimeMillis()}.db")
            if (dbFile.exists()) {
                dbFile.copyTo(emergencyBackup, overwrite = true)
            }
            
            // 恢复备份
            backupFile.copyTo(dbFile, overwrite = true)
            
            Log.i(TAG, "数据库恢复成功，从备份: ${backupFile.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "数据库恢复失败", e)
            false
        }
    }
    
    /**
     * 获取当前数据库版本
     */
    private fun getCurrentDatabaseVersion(): Int {
        return try {
            // 通过Room获取当前数据库版本
            17 // AppDatabase的当前版本
        } catch (e: Exception) {
            Log.w(TAG, "无法获取数据库版本，使用默认值", e)
            1
        }
    }
    
    /**
     * 检查是否需要自动备份
     * 根据上次备份时间和设置的备份频率决定
     */
    fun shouldAutoBackup(): Boolean {
        // 如果自动备份未启用，则不需要备份
        if (!isAutoBackupEnabled()) {
            return false
        }
        
        val lastBackupTime = prefs.getLong(PREF_LAST_BACKUP_TIME, 0)
        val currentTime = System.currentTimeMillis()
        
        // 如果从未备份过，则需要备份
        if (lastBackupTime == 0L) {
            return true
        }
        
        val frequency = getAutoBackupFrequency()
        val interval = when (frequency) {
            "daily" -> TimeUnit.DAYS.toMillis(1)
            "weekly" -> TimeUnit.DAYS.toMillis(7)
            "monthly" -> TimeUnit.DAYS.toMillis(30)
            else -> TimeUnit.DAYS.toMillis(7) // 默认每周
        }
        
        // 如果距离上次备份时间超过了设定的间隔，则需要备份
        return (currentTime - lastBackupTime) >= interval
    }
    
    /**
     * 获取所有备份文件
     */
    fun getBackupFiles(): List<File> {
        return backupDir.listFiles { file ->
            file.name.startsWith("timeflow_database_") && file.name.endsWith(".db")
        }?.toList() ?: emptyList()
    }
    
    /**
     * 删除指定的备份文件
     */
    suspend fun deleteBackup(backupFileName: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val backupFile = File(backupDir, backupFileName)
            if (!backupFile.exists()) {
                Log.w(TAG, "找不到备份文件: $backupFileName")
                return@withContext false
            }
            
            val result = backupFile.delete()
            if (result) {
                Log.i(TAG, "成功删除备份文件: $backupFileName")
            } else {
                Log.w(TAG, "删除备份文件失败: $backupFileName")
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "删除备份文件时出错", e)
            false
        }
    }
    
    /**
     * 清理旧的备份文件
     */
    private fun cleanupOldBackups() {
        val maxBackupCount = prefs.getInt(PREF_MAX_BACKUP_COUNT, DEFAULT_MAX_BACKUP_COUNT)
        val backupFiles = getBackupFiles().sortedByDescending { it.lastModified() }
        
        if (backupFiles.size > maxBackupCount) {
            val filesToDelete = backupFiles.drop(maxBackupCount)
            filesToDelete.forEach { file ->
                if (file.delete()) {
                    Log.d(TAG, "删除旧备份文件: ${file.name}")
                }
            }
        }
    }
    
    /**
     * 获取备份统计信息
     */
    fun getBackupInfo(): BackupInfo {
        val backupFiles = getBackupFiles()
        val totalSize = backupFiles.sumOf { it.length() }
        val lastBackupTime = backupFiles.maxOfOrNull { it.lastModified() } ?: 0L
        
        return BackupInfo(
            backupCount = backupFiles.size,
            totalSizeBytes = totalSize,
            lastBackupTime = lastBackupTime,
            backupFiles = backupFiles.map { 
                BackupFileInfo(
                    fileName = it.name,
                    version = extractVersionFromFileName(it.name),
                    sizeBytes = it.length(),
                    timestamp = it.lastModified()
                )
            }
        )
    }
    
    private fun extractVersionFromFileName(fileName: String): Int {
        return try {
            val regex = "_v(\\d+)_".toRegex()
            regex.find(fileName)?.groupValues?.get(1)?.toInt() ?: 0
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 数据备份信息
     */
    data class BackupInfo(
        val backupCount: Int,
        val totalSizeBytes: Long,
        val lastBackupTime: Long,
        val backupFiles: List<BackupFileInfo>
    )
    
    /**
     * 单个备份文件信息
     */
    data class BackupFileInfo(
        val fileName: String,
        val version: Int,
        val sizeBytes: Long,
        val timestamp: Long
    )
    
    /**
     * 备份设置信息
     */
    data class BackupSettings(
        val isAutoBackupEnabled: Boolean,
        val autoBackupFrequency: String,
        val maxBackupCount: Int
    )
    
    /**
     * 获取备份设置
     */
    fun getBackupSettings(): BackupSettings {
        return BackupSettings(
            isAutoBackupEnabled = isAutoBackupEnabled(),
            autoBackupFrequency = getAutoBackupFrequency(),
            maxBackupCount = getMaxBackupCount()
        )
    }
    
    /**
     * 更新备份设置
     */
    fun updateBackupSettings(
        isAutoBackupEnabled: Boolean,
        autoBackupFrequency: String,
        maxBackupCount: Int
    ) {
        prefs.edit()
            .putBoolean(PREF_AUTO_BACKUP_ENABLED, isAutoBackupEnabled)
            .putString(PREF_AUTO_BACKUP_FREQUENCY, autoBackupFrequency)
            .putInt(PREF_MAX_BACKUP_COUNT, maxBackupCount)
            .apply()
        
        // 如果更新了最大备份数量，则清理旧备份
        cleanupOldBackups()
    }
    
    /**
     * 检查自动备份是否启用
     */
    private fun isAutoBackupEnabled(): Boolean {
        return prefs.getBoolean(PREF_AUTO_BACKUP_ENABLED, false)
    }

    /**
     * 设置自动备份启用状态
     */
    fun setAutoBackupEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(PREF_AUTO_BACKUP_ENABLED, enabled).apply()
    }

    /**
     * 获取自动备份频率
     */
    private fun getAutoBackupFrequency(): String {
        return prefs.getString(PREF_AUTO_BACKUP_FREQUENCY, "weekly") ?: "weekly"
    }

    /**
     * 设置自动备份频率
     */
    fun setAutoBackupFrequency(frequency: String) {
        prefs.edit().putString(PREF_AUTO_BACKUP_FREQUENCY, frequency).apply()
    }
    
    /**
     * 获取最大备份数量
     */
    private fun getMaxBackupCount(): Int {
        return prefs.getInt(PREF_MAX_BACKUP_COUNT, DEFAULT_MAX_BACKUP_COUNT)
    }
}