package com.timeflow.app.ui.task.components.common.state

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 表示数据加载状态的类
 */
sealed class LoadState<out T> {
    /**
     * 初始状态
     */
    data object Initial : LoadState<Nothing>()
    
    /**
     * 加载中状态
     * @param isRefreshing 是否为刷新操作
     */
    data class Loading(val isRefreshing: Boolean = false) : LoadState<Nothing>()
    
    /**
     * 加载成功状态
     * @param data 加载的数据
     */
    data class Success<T>(val data: T) : LoadState<T>()
    
    /**
     * 加载错误状态
     * @param message 错误消息
     * @param error 错误对象
     */
    data class Error(val message: String, val error: Throwable? = null) : LoadState<Nothing>()
}

/**
 * 管理数据加载状态的类
 */
class ManagedState<T> {
    private val _state = MutableStateFlow<LoadState<T>>(LoadState.Initial)
    val state: StateFlow<LoadState<T>> = _state.asStateFlow()
    
    /**
     * 发出加载中状态
     * @param isRefreshing 是否为刷新操作
     */
    fun emitLoading(isRefreshing: Boolean = false) {
        _state.value = LoadState.Loading(isRefreshing)
    }
    
    /**
     * 发出成功状态
     * @param data 加载的数据
     */
    fun emitSuccess(data: T) {
        _state.value = LoadState.Success(data)
    }
    
    /**
     * 发出错误状态
     * @param message 错误消息
     * @param error 错误对象
     */
    fun emitError(message: String, error: Throwable? = null) {
        _state.value = LoadState.Error(message, error)
    }
    
    /**
     * 重置为初始状态
     */
    fun reset() {
        _state.value = LoadState.Initial
    }
    
    /**
     * 获取当前数据（如果处于成功状态）
     * @return 数据，如果不是成功状态则返回null
     */
    fun getCurrentData(): T? {
        return when (val currentState = state.value) {
            is LoadState.Success -> currentState.data
            else -> null
        }
    }
} 