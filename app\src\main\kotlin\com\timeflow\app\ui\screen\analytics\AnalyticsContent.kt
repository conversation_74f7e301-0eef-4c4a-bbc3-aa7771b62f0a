package com.timeflow.app.ui.screen.analytics

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.timeflow.app.ui.components.ChartCard
import com.timeflow.app.ui.components.LoadingContent
import com.timeflow.app.ui.components.StatsCard
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel.TimeRange

/**
 * 分析页面内容
 * 显示各种统计分析数据和图表
 */
@Composable
fun analyticsContent(
    viewModel: AnalyticsViewModel,
    onNavigateToSettings: () -> Unit
) {
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val selectedTimeRange by viewModel.selectedTimeRange.collectAsState()
    
    // 统计数据
    val productivityScore by viewModel.productivityScore.collectAsState()
    val completionRate by viewModel.completionRate.collectAsState()
    val interruptionCount by viewModel.interruptionCount.collectAsState()
    val comparisonData by viewModel.comparisonData.collectAsState()
    
    // 图表数据
    val timeDistribution by viewModel.timeDistribution.collectAsState()
    val efficiencyData by viewModel.efficiencyData.collectAsState()
    val taskTags by viewModel.taskTags.collectAsState()
    val heatmapData by viewModel.heatmapData.collectAsState()
    
    // 预加载数据
    LaunchedEffect(Unit) {
        viewModel.preloadData()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 时间范围选择器
        TimeRangeSelector(
            selectedTimeRange = selectedTimeRange,
            onTimeRangeSelected = { viewModel.setTimeRange(it) }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 内容区域
        if (isLoading) {
            LoadingContent()
        } else if (error != null) {
            ErrorContent(error = error!!)
        } else {
            // 统计卡片和图表
            Column(modifier = Modifier.fillMaxWidth()) {
                // 生产力统计卡片
                ProductivityStatsCard(
                    productivityScore = productivityScore,
                    completionRate = completionRate,
                    interruptionCount = interruptionCount,
                    comparisonData = comparisonData
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 时间分配饼图
                if (timeDistribution.isNotEmpty()) {
                    TimeDistributionChart(timeDistribution = timeDistribution)
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // 效率趋势图
                if (efficiencyData.isNotEmpty()) {
                    EfficiencyTrendChart(efficiencyData = efficiencyData)
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // 任务标签云
                if (taskTags.isNotEmpty()) {
                    TaskTagCloud(taskTags = taskTags)
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // 热力图
                if (heatmapData.isNotEmpty()) {
                    EfficiencyHeatmap(heatmapData = heatmapData)
                }
            }
        }
    }
}

/**
 * 时间范围选择器
 */
@Composable
private fun TimeRangeSelector(
    selectedTimeRange: TimeRange,
    onTimeRangeSelected: (TimeRange) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        TimeRange.values().forEach { timeRange ->
            FilterChip(
                selected = selectedTimeRange == timeRange,
                onClick = { onTimeRangeSelected(timeRange) },
                label = { Text(timeRange.name) }
            )
        }
    }
}

/**
 * 生产力统计卡片
 */
@Composable
private fun ProductivityStatsCard(
    productivityScore: Int,
    completionRate: Float,
    interruptionCount: Int,
    comparisonData: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "生产力概览",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 生产力得分
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = productivityScore.toString(),
                        style = MaterialTheme.typography.headlineMedium
                    )
                    Text(
                        text = "生产力得分",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = comparisonData,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                // 完成率
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = "${completionRate.toInt()}%",
                        style = MaterialTheme.typography.headlineMedium
                    )
                    Text(
                        text = "任务完成率",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                // 干扰次数
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = interruptionCount.toString(),
                        style = MaterialTheme.typography.headlineMedium
                    )
                    Text(
                        text = "干扰次数",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

/**
 * 时间分配图表
 * 在实际实现中，这里应该使用真正的饼图组件
 */
@Composable
private fun TimeDistributionChart(timeDistribution: Map<String, Float>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间分配",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 在实际应用中，这里应该显示饼图
            // 此处仅展示数据
            timeDistribution.forEach { (category, percentage) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(text = category)
                    Text(text = "$percentage%")
                }
            }
        }
    }
}

/**
 * 效率趋势图表
 * 在实际实现中，这里应该使用真正的线图组件
 */
@Composable
private fun EfficiencyTrendChart(efficiencyData: List<Pair<String, Float>>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "效率趋势",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 在实际应用中，这里应该显示线图或柱状图
            // 此处仅展示数据
            efficiencyData.forEach { (day, efficiency) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(text = day)
                    Text(text = "${efficiency.toInt()}%")
                }
            }
        }
    }
}

/**
 * 任务标签云
 * 在实际实现中，这里应该使用真正的标签云组件
 */
@Composable
private fun TaskTagCloud(taskTags: Map<String, Int>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "任务标签",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 在实际应用中，这里应该显示标签云
            // 此处仅展示数据
            taskTags.entries.sortedByDescending { it.value }.take(5).forEach { (tag, count) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(text = tag)
                    Text(text = count.toString())
                }
            }
        }
    }
}

/**
 * 效率热力图
 * 在实际实现中，这里应该使用真正的热力图组件
 */
@Composable
private fun EfficiencyHeatmap(heatmapData: List<List<Float>>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "时间效率热力图",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 在实际应用中，这里应该显示热力图
            Text(
                text = "热力图展示7天x24小时的效率数据",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

/**
 * 错误内容显示
 */
@Composable
private fun ErrorContent(error: String) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.titleLarge,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { /* 重试逻辑 */ }
        ) {
            Text("重试")
        }
    }
} 