package com.timeflow.app.ui.navigation

/**
 * 定义应用程序中的屏幕路由
 */
sealed class Screen(val route: String) {
    object Home : Screen("home")
    object TaskList : Screen("task_list")
    object TaskDetail : Screen("task_detail")
    object AddTask : Screen("add_task")
    object Calendar : Screen("calendar")
    object Settings : Screen("settings")
    object Profile : Screen("profile")
    object Analytics : Screen("analytics")
    object Discover : Screen("discover")
    object TimeTracking : Screen("time_tracking")
} 