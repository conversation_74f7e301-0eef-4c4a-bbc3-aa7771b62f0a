package com.timeflow.app.ui.screen.reflection

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowUpward
import androidx.compose.material.icons.filled.Category
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.Mood
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Tune
import androidx.compose.material.icons.rounded.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.material3.LocalContentColor
import androidx.compose.foundation.BorderStroke
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.ui.draw.rotate
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.TimeFlowNavigator
import com.timeflow.app.ui.navigation.asTimeFlowNavigator
import com.timeflow.app.utils.SystemBarManager
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
// 导入正确的数据模型
import com.timeflow.app.data.model.Reflection as DataReflection
import com.timeflow.app.ui.screen.reflection.TimeFilter
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.ReflectionType
import com.timeflow.app.ui.screen.reflection.model.TimeDimension
import com.timeflow.app.ui.screen.reflection.model.DayViewData
import com.timeflow.app.ui.screen.reflection.model.MonthViewData
import java.time.LocalDateTime
// 🆕 添加通知中心导入
import com.timeflow.app.util.NotificationCenter

// 🆕 添加感想创建事件类
data class ReflectionCreatedEvent(
    val reflectionId: String,
    val reflection: Reflection  // 使用UI模型的Reflection
)

/**
 * 感想页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReflectionScreen(
    navController: NavController,
    viewModel: ReflectionViewModel = hiltViewModel()
) {
    val navigator: TimeFlowNavigator = navController.asTimeFlowNavigator()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    val scope = rememberCoroutineScope()
    
    // 🎯 修复：优化页面生命周期管理，避免不必要的刷新
    val lifecycleOwner = LocalLifecycleOwner.current
    var hasInitialized by remember { mutableStateOf(false) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_CREATE -> {
                    if (!hasInitialized) {
                        Log.d("ReflectionScreen", "首次创建页面，初始化数据")
                        viewModel.loadInitialData()
                        hasInitialized = true
                    }
                }
                Lifecycle.Event.ON_RESUME -> {
                    // 只在特定情况下刷新，避免不必要的闪烁
                    if (hasInitialized && !uiState.showUndoSnackbar) {
                        Log.d("ReflectionScreen", "页面恢复，检查是否需要刷新")
                        viewModel.refreshIfNeeded()
                    }
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    // 🎯 新增：刷新状态管理
    var isRefreshing by remember { mutableStateOf(false) }
    
    // 刷新函数
    fun performRefresh() {
        Log.d("ReflectionScreen", "用户触发手动刷新")
        isRefreshing = true
        viewModel.forceRefresh()
        // 延迟重置刷新状态
        scope.launch {
            delay(1000)
            isRefreshing = false
        }
    }
    
    // 🎯 修复：优化事件监听，避免重复刷新
    LaunchedEffect(Unit) {
        NotificationCenter.events.collect { event ->
            if (event is ReflectionCreatedEvent) {
                Log.d("ReflectionScreen", "收到新感想创建事件: ${event.reflectionId}")
                try {
                    // 只在没有显示撤销删除时才刷新
                    if (!uiState.showUndoSnackbar) {
                        viewModel.addNewReflection(event.reflection)
                        Log.d("ReflectionScreen", "✓ 新感想已添加到列表")
                    }
                } catch (e: Exception) {
                    Log.e("ReflectionScreen", "添加新感想失败", e)
                }
            }
        }
    }
    
    // 控制搜索面板显示状态
    var showSearchPanel by remember { mutableStateOf(false) }
    
    // 日期选择相关状态
    val startDate by viewModel.startDate.collectAsState()
    
    // 日期选择对话框状态
    var showStartDatePicker by rememberSaveable { mutableStateOf(false) }
    
    // 日期格式化器
    val dateFormatter = remember { DateTimeFormatter.ofPattern("yyyy年MM月dd日") }
    
    // 格式化日期显示
    val formattedStartDate = remember(startDate) {
        startDate?.format(dateFormatter) ?: ""
    }
    
    // 获取当前时间维度
    val timeDimension = uiState.timeDimension
    
    // 获取当前时间范围标题
    val timeRangeTitle = viewModel.getCurrentTimeRangeTitle()
    
    // 添加与TimeStatisticsScreen相同的渐变背景
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFFFFFF),  // 纯白色 #ffffff
            Color(0xFFf9e9f3)   // 淡粉色 #ffdde1
        )
    )
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.setupStatisticsPageSystemBars(it)  // 使用与统计页面相同的状态栏设置
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            val originalNavigationBarColor = window.navigationBarColor
            
            // 应用状态栏设置
            SystemBarManager.setupStatisticsPageSystemBars(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                window.navigationBarColor = originalNavigationBarColor
                Log.d("ReflectionScreen", "ReflectionScreen disposed")
            }
        }
    }
    
    // 开始日期选择器对话框
    if (showStartDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = startDate?.atStartOfDay(ZoneId.systemDefault())?.toInstant()?.toEpochMilli()
                ?: System.currentTimeMillis()
        )
        
        Dialog(
            onDismissRequest = { showStartDatePicker = false },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface(
                modifier = Modifier
                    .width(320.dp) // 🎯 轻微增加宽度从300dp到320dp以确保完整显示七天
                    .wrapContentHeight(),
                shape = RoundedCornerShape(24.dp),
                color = Color.White,
                shadowElevation = 4.dp
            ) {
                Column(
                    modifier = Modifier.padding(12.dp) // 🎯 进一步减少外边距到12dp为日历腾出更多空间
                ) {
                    // 标题栏
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "选择日期",
                            style = MaterialTheme.typography.titleMedium, // 🎯 缩小字号从titleLarge到titleMedium
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF837A93) // 🎯 主色调改为 #837a93
                        )
                        
                        // 关闭按钮
                        IconButton(
                            onClick = { showStartDatePicker = false },
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp)) // 🎯 减少间距从12dp到8dp
                    
                    // 紧凑型自定义日历组件
                    CompactCalendar(
                        selectedDate = datePickerState.selectedDateMillis?.let { millis ->
                            Instant.ofEpochMilli(millis)
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate()
                        },
                        onDateSelected = { date ->
                            val millis = date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
                            datePickerState.selectedDateMillis = millis
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp)) // 🎯 减少间距
                    
                    // 底部按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        // 取消按钮
                        TextButton(
                            onClick = { showStartDatePicker = false },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        ) {
                            Text("取消")
                        }
                        
                        // 确认按钮
                        Button(
                            onClick = {
                                datePickerState.selectedDateMillis?.let { millis ->
                                    val selectedDate = Instant.ofEpochMilli(millis)
                                        .atZone(ZoneId.systemDefault())
                                        .toLocalDate()
                                    viewModel.updateStartDate(selectedDate)
                                }
                                showStartDatePicker = false
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF837A93) // 🎯 确认按钮使用主色调
                            )
                        ) {
                            Text("确定")
                        }
                    }
                }
            }
        }
    }

    
    // 主界面内容
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
    ) {
        Scaffold(
            modifier = Modifier.padding(top = SystemBarManager.getFixedStatusBarHeight()),
            topBar = {
                Column {
                    // 🎯 美化版顶部标题栏 - 参考Instagram/小红书设计
                    ModernTopAppBar(
                        title = "感想",
                        showSearchPanel = showSearchPanel,
                        searchQuery = uiState.searchQuery,
                        onSearchQueryChange = { query ->
                            viewModel.updateSearchQuery(query)
                        },
                        onSearchToggle = { 
                            showSearchPanel = !showSearchPanel
                            if (!showSearchPanel) {
                                viewModel.clearSearch()
                            }
                        },
                        onFilterClick = {
                            viewModel.toggleFilterPanel()
                        },
                        onBackClick = { navigator.navigateUp() },
                        hasActiveFilters = uiState.isFilterPanelVisible || uiState.searchQuery.isNotBlank()
                    )
                    
                    // 时间维度选择器
                    TimeDimensionSelector(
                        selectedDimension = timeDimension,
                        onDimensionSelected = { viewModel.setTimeDimension(it) },
                        onCalendarClick = { showStartDatePicker = true }
                    )
                    
                    // 时间导航控制栏
                    TimeNavigationBar(
                        title = timeRangeTitle,
                        onPrevious = { viewModel.navigatePrevious() },
                        onNext = { viewModel.navigateNext() },
                        onToday = { viewModel.navigateToToday() }
                    )
                    
                    // 🎯 专用筛选面板 - 专注于筛选条件，参考小红书筛选设计
                    AnimatedVisibility(
                        visible = uiState.isFilterPanelVisible,
                        enter = slideInVertically { -it / 3 } + fadeIn(),
                        exit = slideOutVertically { -it / 3 } + fadeOut()
                    ) {
                        PureFilterPanel(
                            selectedTimeFilter = uiState.timeFilter,
                            selectedMoodTypes = uiState.selectedMoodTypes,
                            selectedReflectionTypes = uiState.selectedReflectionTypes,
                            onTimeFilterChange = { viewModel.setTimeFilter(it) },
                            onMoodTypeToggle = { viewModel.toggleMoodFilter(it) },
                            onReflectionTypeToggle = { viewModel.toggleReflectionTypeFilter(it) },
                            onClearFilters = { viewModel.clearAllFilters() },
                            onApplyFilters = { viewModel.applyFilters() },
                            onDismiss = { viewModel.toggleFilterPanel() }
                        )
                    }
                }
            },
            floatingActionButton = {
                // 新增感想按钮
                FloatingActionButton(
                    onClick = { navigator.navigateToAddReflection() },
                    containerColor = Color(0xFF837A93) // 🎯 更改为 #837a93
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "新增感想",
                        tint = Color.White // 🎯 图标颜色为白色
                    )
                }
            }
        ) { paddingValues ->
            // 主要内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
            ) {
                when {
                    uiState.isLoading -> {
                        // 加载状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                    uiState.error != null -> {
                        // 错误状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "加载失败: ${uiState.error}",
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                    else -> {
                        // 根据当前时间维度显示不同的视图
                        when (timeDimension) {
                            TimeDimension.WEEK -> {
                                // 周视图 - 可折叠设计
                                CollapsibleWeekView(
                                    weekDays = uiState.weekDays,
                                    onDaySelected = { viewModel.selectDay(it) },
                                    selectedDay = uiState.selectedDay,
                                    reflections = uiState.reflections,
                                    onNavigateToDetail = { navigator.navigateToReflectionDetail(it) },
                                    onDeleteReflection = { viewModel.showDeleteDialog(it) }
                                )
                            }
                            TimeDimension.MONTH -> {
                                // 月视图 - 可折叠设计
                                CollapsibleMonthView(
                                    monthDays = uiState.monthDays,
                                    reflections = viewModel.getVisibleReflections(),
                                    onDaySelected = { 
                                        viewModel.selectDay(it)
                                        viewModel.setTimeDimension(TimeDimension.WEEK)
                                    },
                                    onNavigateToDetail = { navigator.navigateToReflectionDetail(it) },
                                    onDeleteReflection = { viewModel.showDeleteDialog(it) }
                                )
                            }
                            TimeDimension.YEAR -> {
                                // 年视图 - 改为支持上下滚动的LazyColumn结构
                                val yearReflections = viewModel.getVisibleReflections()
                                
                                LazyColumn(
                                    modifier = Modifier.fillMaxSize(),
                                    contentPadding = PaddingValues(bottom = 80.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    // 年度日历视图
                                    item {
                                        YearView(
                                            yearMonths = uiState.yearMonths,
                                            onMonthSelected = { viewModel.selectMonth(it) }
                                        )
                                    }
                                    
                                    // 分割线
                                    item {
                                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                                    }
                                    
                                    if (yearReflections.isNotEmpty()) {
                                        // 年度统计
                                        item {
                                            Column(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(16.dp)
                                            ) {
                                                Text(
                                                    text = "${uiState.currentYear}年度感想统计",
                                                    style = MaterialTheme.typography.titleMedium.copy(
                                                        fontWeight = FontWeight.Bold
                                                    ),
                                                    modifier = Modifier.padding(bottom = 16.dp)
                                                )
                                                
                                                // 统计卡片
                                                Card(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    colors = CardDefaults.cardColors(
                                                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)
                                                    ),
                                                    shape = RoundedCornerShape(16.dp),
                                                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                                                ) {
                                                    Column(
                                                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 20.dp)
                                                    ) {
                                                        // 总数统计
                                                        Row(
                                                            modifier = Modifier.fillMaxWidth(),
                                                            horizontalArrangement = Arrangement.SpaceBetween,
                                                            verticalAlignment = Alignment.CenterVertically
                                                        ) {
                                                            Text(
                                                                text = "总记录数",
                                                                style = MaterialTheme.typography.bodyMedium,
                                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                                            )
                                                            Text(
                                                                text = "${yearReflections.size}",
                                                                style = MaterialTheme.typography.bodyLarge,
                                                                fontWeight = FontWeight.Bold,
                                                                color = MaterialTheme.colorScheme.onSurface
                                                            )
                                                        }
                                                        
                                                        Spacer(modifier = Modifier.height(12.dp))
                                                        
                                                        // 月均统计
                                                        val monthlyAverage = yearReflections.size / 12f
                                                        Row(
                                                            modifier = Modifier.fillMaxWidth(),
                                                            horizontalArrangement = Arrangement.SpaceBetween,
                                                            verticalAlignment = Alignment.CenterVertically
                                                        ) {
                                                            Text(
                                                                text = "月均记录",
                                                                style = MaterialTheme.typography.bodyMedium,
                                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                                            )
                                                            Text(
                                                                text = String.format("%.1f", monthlyAverage),
                                                                style = MaterialTheme.typography.bodyLarge,
                                                                fontWeight = FontWeight.Bold,
                                                                color = MaterialTheme.colorScheme.onSurface
                                                            )
                                                        }
                                                        
                                                        Spacer(modifier = Modifier.height(12.dp))
                                                        
                                                        // 最活跃月份
                                                        val mostActiveMonth = uiState.yearMonths
                                                            .maxByOrNull { it.reflectionCount }
                                                        
                                                        mostActiveMonth?.let {
                                                            Row(
                                                                modifier = Modifier.fillMaxWidth(),
                                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                                verticalAlignment = Alignment.CenterVertically
                                                            ) {
                                                                Text(
                                                                    text = "最活跃月份",
                                                                    style = MaterialTheme.typography.bodyMedium,
                                                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                                                )
                                                                Text(
                                                                    text = "${it.month}月 (${it.reflectionCount}条)",
                                                                    style = MaterialTheme.typography.bodyLarge,
                                                                    fontWeight = FontWeight.Bold,
                                                                    color = MaterialTheme.colorScheme.onSurface
                                                                )
                                                            }
                                                        }
                                                    }
                                                }
                                                
                                                Spacer(modifier = Modifier.height(24.dp))
                                                
                                                // 收藏的感想
                                                val favoriteReflections = yearReflections.filter { 
                                                    it.metrics["isFavorite"] == "true"
                                                }
                                                
                                                if (favoriteReflections.isNotEmpty()) {
                                                    Row(
                                                        modifier = Modifier.fillMaxWidth(),
                                                        horizontalArrangement = Arrangement.SpaceBetween,
                                                        verticalAlignment = Alignment.CenterVertically
                                                    ) {
                                                        Text(
                                                            text = "⭐ 收藏的感想",
                                                            style = MaterialTheme.typography.titleSmall.copy(
                                                                fontWeight = FontWeight.SemiBold
                                                            ),
                                                            color = MaterialTheme.colorScheme.onSurface
                                                        )
                                                        
                                                        Text(
                                                            text = "${favoriteReflections.size}条",
                                                            style = MaterialTheme.typography.labelMedium,
                                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                                        )
                                                    }
                                                    
                                                    Spacer(modifier = Modifier.height(8.dp))
                                                    
                                                    // 收藏感想卡片容器
                                                    Surface(
                                                        modifier = Modifier.fillMaxWidth(),
                                                        color = Color(0xFFFFF9C4).copy(alpha = 0.5f), // 淡黄色背景
                                                        shape = RoundedCornerShape(16.dp)
                                                    ) {
                                                        Column(
                                                            modifier = Modifier
                                                                .padding(horizontal = 12.dp, vertical = 12.dp),
                                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                                        ) {
                                                            favoriteReflections.take(2).forEach { reflection ->
                                                                SuperEnhancedReflectionCard(
                                                                    reflection = reflection,
                                                                    onClick = { 
                                                                        navigator.navigateToReflectionDetail(reflection.id)
                                                                    },
                                                                    onLongClick = {
                                                                        viewModel.showDeleteDialog(reflection)
                                                                    }
                                                                )
                                                            }
                                                        }
                                                    }
                                                    
                                                    Spacer(modifier = Modifier.height(16.dp))
                                                }
                                                
                                                // 显示最近的几条感想
                                                Row(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    horizontalArrangement = Arrangement.SpaceBetween,
                                                    verticalAlignment = Alignment.CenterVertically
                                                ) {
                                                    Text(
                                                        text = "最近的感想",
                                                        style = MaterialTheme.typography.titleSmall.copy(
                                                            fontWeight = FontWeight.SemiBold
                                                        ),
                                                        color = MaterialTheme.colorScheme.onSurface
                                                    )
                                                    
                                                    if (yearReflections.size > 3) {
                                                        TextButton(
                                                            onClick = { 
                                                                viewModel.setTimeDimension(TimeDimension.MONTH)
                                                            },
                                                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 0.dp)
                                                        ) {
                                                            Text(
                                                                text = "查看更多",
                                                                style = MaterialTheme.typography.labelMedium
                                                            )
                                                            Icon(
                                                                imageVector = Icons.Default.ChevronRight,
                                                                contentDescription = null,
                                                                modifier = Modifier.size(16.dp)
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        
                                        // 年度感想列表
                                        item {
                                            Spacer(modifier = Modifier.height(16.dp))
                                        }
                                        
                                        items(yearReflections.take(10)) { reflection ->
                                            YearViewCompactReflectionCard(
                                                reflection = reflection,
                                                onClick = { 
                                                    navigator.navigateToReflectionDetail(reflection.id)
                                                },
                                                onLongClick = {
                                                    viewModel.showDeleteDialog(reflection)
                                                },
                                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                                            )
                                        }
                                    } else {
                                        // 空状态
                                        item {
                                            EmptyStateMessage(
                                                message = "本年度还没有记录感想",
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(32.dp)
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        

        
        // 删除确认对话框
        if (uiState.showDeleteDialog) {
            DeleteReflectionDialog(
                onConfirm = {
                    viewModel.confirmDeleteReflection()
                },
                onDismiss = {
                    viewModel.hideDeleteDialog()
                }
            )
        }
        
        // 撤销删除Snackbar
        if (uiState.showUndoSnackbar) {
            val deletedReflection = uiState.deletedReflection
            if (deletedReflection != null) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 16.dp),
                    contentAlignment = Alignment.BottomCenter
                ) {
                    UndoDeleteSnackbar(
                        onUndo = {
                            viewModel.undoDeleteReflection()
                        },
                        onDismiss = {
                            viewModel.hideUndoSnackbar()
                        },
                        reflectionTitle = deletedReflection.title
                    )
                }
            }
        }
    }
}

/**
 * 为测试添加TimeFlowNavigator的扩展方法
 */
fun TimeFlowNavigator.navigateToReflectionDetail(reflectionId: String = "") {
    // 在实际实现中，这里应该导航到详情页
}

/**
 * 空感想列表组件
 */
@Composable
fun EmptyReflectionList(
    modifier: Modifier = Modifier,
    onCreateNew: () -> Unit
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "暂无感想记录",
            style = MaterialTheme.typography.titleMedium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "点击右下角按钮创建新的感想",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 自定义过滤芯片组件
 */
@Composable
fun FilterChip(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        color = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant
    ) {
        Text(
            text = text,
            color = if (selected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

/**
 * 紧凑型日历组件 - 专为弹窗设计，缩小间距确保完整显示
 */
@Composable
fun CompactCalendar(
    selectedDate: LocalDate?,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    var currentMonth by remember(selectedDate) { 
        // 🎯 当selectedDate变化时，自动切换到相应的月份
        mutableStateOf(selectedDate ?: LocalDate.now())
    }
    
    // 🎯 监听selectedDate变化，自动切换到对应月份
    LaunchedEffect(selectedDate) {
        selectedDate?.let { date ->
            currentMonth = date.withDayOfMonth(1) // 确保切换到selectedDate所在的月份
        }
    }
    
    val today = LocalDate.now()
    val primaryColor = Color(0xFF837A93)
    
    Column(modifier = modifier) {
        // 月份导航
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { currentMonth = currentMonth.minusMonths(1) },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ChevronLeft,
                    contentDescription = "上个月",
                    tint = primaryColor
                )
            }
            
            Text(
                text = "${currentMonth.year}年${currentMonth.monthValue}月",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = primaryColor
            )
            
            IconButton(
                onClick = { currentMonth = currentMonth.plusMonths(1) },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "下个月",
                    tint = primaryColor
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("一", "二", "三", "四", "五", "六", "日").forEach { day ->
                Text(
                    text = day,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF666666),
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 日期网格
        val firstDayOfMonth = currentMonth.withDayOfMonth(1)
        val lastDayOfMonth = currentMonth.withDayOfMonth(currentMonth.lengthOfMonth())
        val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // 转换为周一=0的格式
        
        // 计算需要显示的所有日期
        val startDate = firstDayOfMonth.minusDays(firstDayOfWeek.toLong())
        val endDate = lastDayOfMonth.plusDays((6 - lastDayOfMonth.dayOfWeek.value % 7).toLong())
        
        val weeks = mutableListOf<List<LocalDate>>()
        var currentDate = startDate
        
        while (currentDate <= endDate) {
            val week = mutableListOf<LocalDate>()
            repeat(7) {
                week.add(currentDate)
                currentDate = currentDate.plusDays(1)
            }
            weeks.add(week)
        }
        
        weeks.forEach { week ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                week.forEach { date ->
                    val isSelected = date == selectedDate
                    val isToday = date == today
                    val isCurrentMonth = date.month == currentMonth.month
                    
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                            .padding(1.dp) // 🎯 极小的间距确保最大利用空间
                            .clip(CircleShape)
                            .background(
                                when {
                                    isSelected -> primaryColor
                                    isToday -> primaryColor.copy(alpha = 0.2f)
                                    else -> Color.Transparent
                                }
                            )
                            .clickable { 
                                if (isCurrentMonth) {
                                    onDateSelected(date)
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 12.sp, // 🎯 稍微缩小字体确保紧凑显示
                                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal
                            ),
                            color = when {
                                isSelected -> Color.White
                                isToday -> primaryColor
                                isCurrentMonth -> Color(0xFF333333)
                                else -> Color(0xFFCCCCCC)
                            },
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
            
            if (week != weeks.last()) {
                Spacer(modifier = Modifier.height(2.dp)) // 🎯 极小的行间距
            }
        }
    }
}

// 🎯 美化版顶部标题栏组件 - 参考微信朋友圈/小红书设计
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun ModernTopAppBar(
    title: String,
    showSearchPanel: Boolean,
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onSearchToggle: () -> Unit,
    onFilterClick: () -> Unit,
    onBackClick: () -> Unit,
    hasActiveFilters: Boolean,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column {
            // 主标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 返回按钮
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier.size(44.dp)
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF1C1B1F),
                        modifier = Modifier.size(22.dp)
                    )
                }

                // 搜索栏区域 - 参考微信朋友圈设计
                if (!showSearchPanel) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.SemiBold,
                            fontSize = 20.sp
                        ),
                        color = Color(0xFF1C1B1F),
                        modifier = Modifier.weight(1f)
                    )
                } else {
                    // 集成搜索和筛选的输入框
                    IntegratedSearchBar(
                        query = searchQuery,
                        onQueryChange = onSearchQueryChange,
                        onFilterClick = onFilterClick,
                        hasActiveFilters = hasActiveFilters,
                        modifier = Modifier.weight(1f)
                    )
                }

                // 右侧按钮组
                Row(
                    horizontalArrangement = Arrangement.spacedBy(2.dp)
                ) {
                    // 搜索/关闭按钮
                    ModernIconButton(
                        onClick = onSearchToggle,
                        isActive = showSearchPanel
                    ) {
                        AnimatedContent(
                            targetState = showSearchPanel,
                            transitionSpec = {
                                slideInHorizontally { it } + fadeIn() with
                                slideOutHorizontally { -it } + fadeOut()
                            }
                        ) { isSearchMode ->
                            Icon(
                                imageVector = if (isSearchMode) Icons.Default.Close else Icons.Default.Search,
                                contentDescription = if (isSearchMode) "关闭搜索" else "搜索",
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }

                    // 当没有搜索模式时显示单独的筛选按钮
                    if (!showSearchPanel) {
                        ModernIconButton(
                            onClick = onFilterClick,
                            isActive = hasActiveFilters
                        ) {
                            Box {
                                Icon(
                                    imageVector = Icons.Default.Tune,
                                    contentDescription = "筛选",
                                    modifier = Modifier.size(20.dp)
                                )
                                
                                // 活跃筛选指示器
                                if (hasActiveFilters) {
                                    Box(
                                        modifier = Modifier
                                            .size(6.dp)
                                            .background(Color(0xFFFF4757), CircleShape)
                                            .offset(x = 6.dp, y = (-6).dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 🎯 简洁版搜索栏组件 - 参考微信朋友圈设计，无重复搜索功能
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun IntegratedSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onFilterClick: () -> Unit,
    hasActiveFilters: Boolean,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    
    // 主搜索栏 - 简洁版，无额外建议面板
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(42.dp),
        shape = RoundedCornerShape(21.dp),
        color = if (isFocused) Color.White else Color(0xFFF5F5F5),
        border = BorderStroke(
            width = if (isFocused) 1.5.dp else 1.dp,
            color = when {
                isFocused -> Color(0xFF837A93)
                hasActiveFilters -> Color(0xFF837A93).copy(alpha = 0.6f)
                else -> Color(0xFFE0E0E0)
            }
        ),
        shadowElevation = if (isFocused) 4.dp else 0.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 搜索图标 - 动态颜色
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = null,
                tint = if (isFocused) Color(0xFF837A93) else Color(0xFF6C7B7F),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(10.dp))
            
            // 简洁版搜索输入框
            BasicTextField(
                value = query,
                onValueChange = onQueryChange,
                modifier = Modifier
                    .weight(1f)
                    .onFocusChanged { focusState ->
                        isFocused = focusState.isFocused
                    },
                textStyle = MaterialTheme.typography.bodyMedium.copy(
                    color = Color(0xFF1C1B1F),
                    fontSize = 15.sp,
                    fontWeight = FontWeight.Normal
                ),
                singleLine = true,
                decorationBox = { innerTextField ->
                    Box {
                        if (query.isEmpty()) {
                            Text(
                                text = if (isFocused) "搜索感想内容、标签、心情..." else "搜索感想...",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    color = Color(0xFF9E9E9E),
                                    fontSize = 15.sp
                                )
                            )
                        }
                        innerTextField()
                    }
                }
            )
            
            // 右侧按钮组
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 清空按钮 - 仅在有内容时显示
                AnimatedVisibility(
                    visible = query.isNotEmpty(),
                    enter = fadeIn() + slideInHorizontally { it },
                    exit = fadeOut() + slideOutHorizontally { it }
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF6C7B7F).copy(alpha = 0.1f))
                            .clickable { onQueryChange("") },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "清空搜索",
                            tint = Color(0xFF6C7B7F),
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
                
                // 分隔线
                if (query.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .width(1.dp)
                            .height(16.dp)
                            .background(Color(0xFFE0E0E0))
                    )
                }
                
                // 筛选按钮 - 增强版
                EnhancedFilterButton(
                    onClick = onFilterClick,
                    hasActiveFilters = hasActiveFilters,
                    isCompact = true
                )
            }
        }
    }
}

// 🎯 增强版筛选按钮
@Composable
fun EnhancedFilterButton(
    onClick: () -> Unit,
    hasActiveFilters: Boolean,
    isCompact: Boolean = false,
    modifier: Modifier = Modifier
) {
    val buttonSize = if (isCompact) 28.dp else 32.dp
    val iconSize = if (isCompact) 14.dp else 16.dp
    
    Box(
        modifier = modifier
            .size(buttonSize)
            .clip(CircleShape)
            .background(
                when {
                    hasActiveFilters -> Color(0xFF837A93).copy(alpha = 0.12f)
                    else -> Color.Transparent
                }
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Box {
            Icon(
                imageVector = Icons.Default.Tune,
                contentDescription = "筛选",
                tint = if (hasActiveFilters) Color(0xFF837A93) else Color(0xFF6C7B7F),
                modifier = Modifier.size(iconSize)
            )
            
            // 活跃筛选指示器 - 更精致的设计
            if (hasActiveFilters) {
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .background(
                            Color(0xFF837A93),
                            CircleShape
                        )
                        .offset(x = 8.dp, y = (-8).dp)
                ) {
                    // 内部白点
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(1.dp)
                            .background(Color.White, CircleShape)
                    )
                }
            }
        }
    }
}



// 🎯 现代化图标按钮
@Composable
fun ModernIconButton(
    onClick: () -> Unit,
    isActive: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                if (isActive) Color(0xFF837A93).copy(alpha = 0.12f) 
                else Color.Transparent
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        CompositionLocalProvider(
            LocalContentColor provides if (isActive) Color(0xFF837A93) else Color(0xFF6C7B7F)
        ) {
            content()
        }
    }
}

// 🎯 纯筛选面板 - 专注于筛选条件，参考小红书/抖音设计
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun PureFilterPanel(
    selectedTimeFilter: TimeFilter?,
    selectedMoodTypes: Set<MoodType>,
    selectedReflectionTypes: Set<ReflectionType>,
    onTimeFilterChange: (TimeFilter) -> Unit,
    onMoodTypeToggle: (MoodType) -> Unit,
    onReflectionTypeToggle: (ReflectionType) -> Unit,
    onClearFilters: () -> Unit,
    onApplyFilters: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shadowElevation = 12.dp,
        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
    ) {
        Column(
            modifier = Modifier.padding(0.dp)
        ) {
            // 顶部把手指示器 - 参考抖音设计
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .width(40.dp)
                        .height(4.dp)
                        .background(
                            Color(0xFFE0E0E0),
                            RoundedCornerShape(2.dp)
                        )
                )
            }
            
            // 标题栏 - 参考小红书设计
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "筛选",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    ),
                    color = Color(0xFF1C1B1F)
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 清空按钮
                    TextButton(
                        onClick = onClearFilters,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF6C7B7F)
                        )
                    ) {
                        Text(
                            text = "清空",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontSize = 14.sp
                            )
                        )
                    }
                    
                    // 关闭按钮
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭筛选",
                            tint = Color(0xFF6C7B7F),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
            
            // 分割线
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color(0xFFF0F0F0))
            )
            
            // 筛选内容区域
            Column(
                modifier = Modifier.padding(horizontal = 20.dp, vertical = 16.dp)
            ) {
                // 时间筛选
                CompactFilterSection(
                    title = "时间范围",
                    icon = Icons.Default.Schedule
                ) {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(10.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        TimeFilter.values().forEach { filter ->
                            CompactFilterChip(
                                text = filter.displayName,
                                isSelected = selectedTimeFilter == filter,
                                onClick = { onTimeFilterChange(filter) }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 心情筛选
                CompactFilterSection(
                    title = "心情状态",
                    icon = Icons.Default.Mood
                ) {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(10.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        MoodType.values().forEach { mood ->
                            CompactMoodChip(
                                mood = mood,
                                isSelected = selectedMoodTypes.contains(mood),
                                onClick = { onMoodTypeToggle(mood) }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 类型筛选
                CompactFilterSection(
                    title = "感想类型",
                    icon = Icons.Default.Category
                ) {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(10.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        ReflectionType.values().forEach { type ->
                            CompactFilterChip(
                                text = type.displayName,
                                isSelected = selectedReflectionTypes.contains(type),
                                onClick = { onReflectionTypeToggle(type) }
                            )
                        }
                    }
                }
            }
            
            // 底部操作栏
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = Color(0xFFFAFAFA),
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 重置按钮
                    OutlinedButton(
                        onClick = onClearFilters,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp),
                        border = BorderStroke(1.dp, Color(0xFFE0E0E0)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF6C7B7F)
                        )
                    ) {
                        Text(
                            text = "重置",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                    }
                    
                    // 确认按钮
                    Button(
                        onClick = {
                            onApplyFilters()
                            onDismiss()
                        },
                        modifier = Modifier.weight(2f),
                        shape = RoundedCornerShape(12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF837A93)
                        )
                    ) {
                        Text(
                            text = "确认筛选",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = Color.White,
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

// 🎯 紧凑型筛选区域标题组件
@Composable
fun CompactFilterSection(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 12.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = Color(0xFF837A93),
                modifier = Modifier.size(18.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 15.sp
                ),
                color = Color(0xFF1C1B1F)
            )
        }
        
        content()
    }
}

// 🎯 现代化筛选芯片
@Composable
fun ModernFilterChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        color = if (isSelected) Color(0xFF837A93) else Color(0xFFF5F5F5),
        border = BorderStroke(
            width = 1.dp,
            color = if (isSelected) Color(0xFF837A93) else Color(0xFFE0E0E0)
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                fontSize = 13.sp
            ),
            color = if (isSelected) Color.White else Color(0xFF6C7B7F),
            modifier = Modifier.padding(horizontal = 14.dp, vertical = 6.dp)
        )
    }
}

// 🎯 心情芯片组件
@Composable
fun ModernMoodChip(
    mood: MoodType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        color = if (isSelected) Color(0xFF837A93).copy(alpha = 0.12f) else Color(0xFFF5F5F5),
        border = BorderStroke(
            width = 1.dp,
            color = if (isSelected) Color(0xFF837A93) else Color(0xFFE0E0E0)
        )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
        ) {
            Text(
                text = mood.emoji,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(end = 4.dp)
            )
            
            Text(
                text = mood.displayName,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                    fontSize = 12.sp
                ),
                color = if (isSelected) Color(0xFF837A93) else Color(0xFF6C7B7F)
            )
        }
    }
}

// 🎯 紧凑型筛选芯片组件
@Composable
fun CompactFilterChip(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(18.dp),
        color = if (isSelected) Color(0xFF837A93) else Color(0xFFF8F8F8),
        border = BorderStroke(
            width = if (isSelected) 0.dp else 1.dp,
            color = if (isSelected) Color.Transparent else Color(0xFFE8E8E8)
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                fontSize = 13.sp
            ),
            color = if (isSelected) Color.White else Color(0xFF4A4A4A),
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )
    }
}

// 🎯 紧凑型心情芯片组件
@Composable
fun CompactMoodChip(
    mood: MoodType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(18.dp),
        color = if (isSelected) Color(0xFF837A93) else Color(0xFFF8F8F8),
        border = BorderStroke(
            width = if (isSelected) 0.dp else 1.dp,
            color = if (isSelected) Color.Transparent else Color(0xFFE8E8E8)
        )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 14.dp, vertical = 8.dp)
        ) {
            Text(
                text = mood.emoji,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                modifier = Modifier.padding(end = 6.dp)
            )
            
            Text(
                text = mood.displayName,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                    fontSize = 12.sp
                ),
                color = if (isSelected) Color.White else Color(0xFF4A4A4A)
            )
        }
    }
}