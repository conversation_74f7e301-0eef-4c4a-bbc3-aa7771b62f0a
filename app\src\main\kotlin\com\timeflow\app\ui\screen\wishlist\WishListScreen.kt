package com.timeflow.app.ui.screen.wishlist

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import coil.compose.AsyncImage
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.timeflow.app.data.model.*
import com.timeflow.app.ui.viewmodel.WishListViewModel
import com.timeflow.app.ui.theme.*
import java.time.format.DateTimeFormatter

// 愿望池主题配色 - 优雅紫灰色调
private val WishPoolPrimary = Color(0xFF9c7d8e) // 优雅紫灰色 
private val WishPoolSecondary = Color(0xFFb899a7) // 浅紫灰色
private val WishPoolAccent = Color(0xFFFFD93D) // 金黄色（星星）
private val WishPoolBackground = Color(0xFFFFFBF7) // 温暖白
private val WishPoolSurface = Color(0xFFFFF8F0) // 奶油白
private val WishPoolText = Color(0xFF2D1B2E) // 深紫灰
private val WishPoolTextSecondary = Color(0xFF6B4C6D) // 中紫灰

/**
 * 愿望池主页 - 小红书风格设计
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WishListScreen(
    navController: NavController,
    viewModel: com.timeflow.app.ui.viewmodel.WishListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // 动画状态
    var isVisible by remember { mutableStateOf(false) }
    val contentAlpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(600, easing = EaseOutCubic),
        label = "content_alpha"
    )
    
    // 添加愿望对话框状态
    var showAddWishDialog by remember { mutableStateOf(false) }
    
    // 处理系统栏颜色
    val activity = LocalContext.current as? android.app.Activity
    LaunchedEffect(Unit) {
        activity?.let { 
            com.timeflow.app.utils.SystemBarManager.setupLightModeSystemBars(it)
        }
        delay(100)
        isVisible = true
    }

    // 主背景渐变
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        WishPoolBackground,
                        WishPoolSurface
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .alpha(contentAlpha)
                .padding(top = com.timeflow.app.utils.SystemBarManager.getFixedStatusBarHeight())
        ) {
            // 🎨 精美顶部栏 - Instagram风格
            WishPoolTopBar(
                onBackClick = { navController.popBackStack() },
                onSearchClick = { /* 搜索功能 */ },
                onStatisticsClick = { 
                    navController.navigate(com.timeflow.app.navigation.AppDestinations.WISH_STATISTICS_ROUTE)
                }
            )
            
            // 📊 愿望统计卡片 - Notion风格
            WishStatisticsCard(
                totalWishes = uiState.allWishes.size,
                activeWishes = uiState.allWishes.count { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE },
                achievedWishes = uiState.allWishes.count {
                    it.status == com.timeflow.app.data.model.WishStatus.ACHIEVED ||
                    (it.status == com.timeflow.app.data.model.WishStatus.ARCHIVED && it.achievedAt != null)
                },
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
            
            // 🌟 愿望列表 - 小红书瀑布流风格
            if (uiState.wishes.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(
                        start = 16.dp, 
                        end = 16.dp, 
                        bottom = 100.dp
                    ),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(
                        items = uiState.wishes,
                        key = { it.id }
                    ) { wish ->
                        EnhancedWishCard(
                            wish = wish,
                            onSetAsGoal = { 
                                scope.launch {
                                    android.widget.Toast.makeText(context, "正在将愿望「${wish.title}」转化为目标...", android.widget.Toast.LENGTH_SHORT).show()
                                    // 🎬 播放转化动效
                                    playWishToGoalAnimation(wish)
                                    // 转为目标逻辑
                                    convertWishToGoal(wish, viewModel, navController)
                                }
                            },
                            onEdit = { 
                                android.widget.Toast.makeText(context, "正在编辑愿望「${wish.title}」", android.widget.Toast.LENGTH_SHORT).show()
                                viewModel.editWish(wish) // ViewModel中已经处理showAddDialog状态
                            },
                            onArchive = { 
                                android.widget.Toast.makeText(context, "愿望「${wish.title}」已归档", android.widget.Toast.LENGTH_SHORT).show()
                                viewModel.archiveWish(wish.id) 
                            },
                            onShare = { 
                                android.widget.Toast.makeText(context, "正在分享愿望「${wish.title}」", android.widget.Toast.LENGTH_SHORT).show()
                                shareWish(wish, context) 
                            },
                            onDelete = { 
                                android.widget.Toast.makeText(context, "愿望「${wish.title}」已删除", android.widget.Toast.LENGTH_SHORT).show()
                                viewModel.deleteWish(wish.id) 
                            },
                            onMarkAsAchieved = { 
                                if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) {
                                    android.widget.Toast.makeText(context, "愿望「${wish.title}」已标记为未完成", android.widget.Toast.LENGTH_SHORT).show()
                                    viewModel.markWishAsActive(wish.id)
                                } else {
                                    android.widget.Toast.makeText(context, "🎉 恭喜！愿望「${wish.title}」已实现", android.widget.Toast.LENGTH_SHORT).show()
                                    viewModel.markWishAsAchieved(wish.id)
                                }
                            },
                            modifier = Modifier.animateItemPlacement(
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessLow
                                )
                            )
                        )
                    }
                }
            } else {
                // 🎭 精美空状态 - Apple风格
                WishPoolEmptyState(
                    onAddWish = { showAddWishDialog = true },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
        
        // 🌸 浮动添加按钮 - 手绘风格WISH按钮
        FloatingActionButton(
            onClick = { showAddWishDialog = true },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 32.dp, end = 16.dp) // 减少底部边距，因为已隐藏底部导航栏
                .shadow(
                    elevation = 12.dp,
                    shape = RoundedCornerShape(16.dp),
                    spotColor = WishPoolPrimary.copy(alpha = 0.3f)
                ),
            containerColor = WishPoolPrimary,
            contentColor = Color.White,
            shape = RoundedCornerShape(16.dp)
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.padding(horizontal = 20.dp)
            ) {
                Text(
                    text = "WISH",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Serif, // 使用衬线字体营造手写感
                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic, // 添加斜体效果
                        letterSpacing = 1.sp, // 增加字母间距
                        shadow = androidx.compose.ui.graphics.Shadow(
                            color = Color.Black.copy(alpha = 0.1f),
                            offset = androidx.compose.ui.geometry.Offset(1f, 1f),
                            blurRadius = 2f
                        )
                    )
                )
            }
        }
    }
    
    // 💫 温暖的成功提示
    uiState.successMessage?.let { message ->
        LaunchedEffect(message) {
            android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
            delay(3000)
            viewModel.clearSuccessMessage()
        }
    }
    
    // ⚠️ 错误提示
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            android.widget.Toast.makeText(context, error, android.widget.Toast.LENGTH_LONG).show()
            delay(3000)
            viewModel.clearError()
        }
    }
    
    // 📝 添加/编辑愿望对话框 - 统一使用ViewModel状态管理
    if (showAddWishDialog || uiState.showAddDialog) {
        val editingWish by viewModel.editingWish.collectAsStateWithLifecycle()
        
        EnhancedAddWishDialog(
            onDismiss = { 
                if (editingWish != null) {
                    viewModel.cancelEdit()
                } else {
                    showAddWishDialog = false
                    viewModel.hideAddDialog()
                }
            },
            onAddWish = { wish ->
                if (editingWish != null) {
                    // 编辑模式 - 更新愿望
                    viewModel.updateWish(wish)
                } else {
                    // 新建模式 - 创建愿望
                    viewModel.createWish(wish)
                    showAddWishDialog = false
                    viewModel.hideAddDialog()
                }
            },
            editingWish = editingWish // 传递当前编辑的愿望数据
        )
    }
}

/**
 * 🎨 精美顶部栏 - Instagram风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WishPoolTopBar(
    onBackClick: () -> Unit,
    onSearchClick: () -> Unit,
    onStatisticsClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.Transparent
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        Color.White.copy(alpha = 0.9f),
                        RoundedCornerShape(20.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = WishPoolText
                )
            }
            
            // 标题区域
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = WishPoolAccent,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "我的愿望池",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = WishPoolText
                    )
                }
                Text(
                    text = "Dreams come true ✨",
                    fontSize = 10.sp,
                    color = WishPoolTextSecondary.copy(alpha = 0.8f),
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
            }
            
            // 操作按钮
            Row {
                // 愿望宇宙统计按钮 - 金色特殊标识
                IconButton(
                    onClick = onStatisticsClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFFFFD700).copy(alpha = 0.2f),
                                    Color(0xFFFFD700).copy(alpha = 0.1f)
                                )
                            ),
                            shape = RoundedCornerShape(20.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Analytics,
                        contentDescription = "愿望宇宙统计",
                        tint = Color(0xFFFFD700),
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                IconButton(
                    onClick = onSearchClick,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color.White.copy(alpha = 0.9f),
                            RoundedCornerShape(20.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Search,
                        contentDescription = "搜索",
                        tint = WishPoolText
                    )
                }
                

            }
        }
    }
}

/**
 * 📊 愿望统计卡片 - Notion风格
 */
@Composable
private fun WishStatisticsCard(
    totalWishes: Int,
    activeWishes: Int,
    achievedWishes: Int,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = WishPoolPrimary.copy(alpha = 0.1f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = Color.White
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(
                count = totalWishes,
                label = "总愿望",
                icon = Icons.Default.FavoriteBorder,
                color = WishPoolPrimary
            )
            
            VerticalDivider(
                modifier = Modifier.height(40.dp),
                thickness = 1.dp,
                color = Color.Gray.copy(alpha = 0.2f)
            )
            
            StatItem(
                count = activeWishes,
                label = "进行中",
                icon = Icons.Default.LocalFireDepartment,
                color = Color(0xFFFF8A65)
            )
            
            VerticalDivider(
                modifier = Modifier.height(40.dp),
                thickness = 1.dp,
                color = Color.Gray.copy(alpha = 0.2f)
            )
            
            StatItem(
                count = achievedWishes,
                label = "已实现",
                icon = Icons.Default.EmojiEvents,
                color = WishPoolAccent
            )
        }
    }
}

@Composable
private fun StatItem(
    count: Int,
    label: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = count.toString(),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
        Text(
            text = label,
            fontSize = 10.sp,
            color = WishPoolTextSecondary,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 🌟 增强愿望卡片 - 小红书风格
 */
@Composable
private fun EnhancedWishCard(
    wish: com.timeflow.app.data.model.WishModel,
    onSetAsGoal: () -> Unit,
    onEdit: () -> Unit,
    onArchive: () -> Unit,
    onShare: () -> Unit,
    onDelete: () -> Unit,
    onMarkAsAchieved: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    val cardElevation by animateDpAsState(
        targetValue = if (isExpanded) 16.dp else 8.dp,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "card_elevation"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = cardElevation,
                shape = RoundedCornerShape(24.dp),
                spotColor = WishPoolPrimary.copy(alpha = 0.15f)
            )
            .clickable { isExpanded = !isExpanded },
        shape = RoundedCornerShape(24.dp),
        color = Color.White
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 📱 卡片头部 - 重新设计布局
            Column {
                // 第一行：复选框 + 类别标签 + 星标
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    // 左侧：复选框 + 类别标签
                    Row(
                        verticalAlignment = Alignment.Top
                    ) {
                        // 完成复选框
                        Surface(
                            modifier = Modifier
                                .padding(top = 4.dp)
                                .clickable { onMarkAsAchieved() },
                            color = if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) 
                                WishPoolPrimary else Color.Transparent,
                            shape = RoundedCornerShape(6.dp),
                            border = BorderStroke(
                                2.dp, 
                                if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) 
                                    WishPoolPrimary else WishPoolPrimary.copy(alpha = 0.4f)
                            )
                        ) {
                            Box(
                                modifier = Modifier.size(20.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已完成",
                                        tint = Color.White,
                                        modifier = Modifier.size(14.dp)
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        // 类别标签 - 更小尺寸
                        Surface(
                            color = getCategoryColor(wish.category).copy(alpha = 0.15f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = wish.category.emoji,
                                    fontSize = 10.sp  // 从10sp减小到9sp
                                )
                                Spacer(modifier = Modifier.width(3.dp))
                                Text(
                                    text = wish.category.displayName,
                                    fontSize = 9.sp,  // 从9sp减小到8sp
                                    fontWeight = FontWeight.Medium,
                                    color = getCategoryColor(wish.category)
                                )
                            }
                        }
                    }
                    
                    // 右侧：优先级星星
                    PriorityStars(
                        priority = wish.priority,
                        color = WishPoolAccent
                    )
                }
                
                Spacer(modifier = Modifier.height(10.dp))
                
                // 第二行：标题 + 标签（标签在卡片最右侧）
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 愿望标题 - 占据主要空间
                    Text(
                        text = if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) "✨ ${wish.title}" else wish.title,
                        fontSize = 15.sp,  // 从16sp减小到15sp
                        fontWeight = FontWeight.Bold,
                        color = if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED) 
                            WishPoolPrimary else WishPoolText,
                        lineHeight = 18.sp,  // 从20sp减小到18sp
                        maxLines = 1,  // 强制单行显示
                        overflow = TextOverflow.Ellipsis,  // 超长显示省略号
                        softWrap = false,  // 不换行
                        textDecoration = if (wish.status == com.timeflow.app.data.model.WishStatus.ACHIEVED)
                            androidx.compose.ui.text.style.TextDecoration.LineThrough else null,
                        modifier = Modifier.weight(1f)  // 占据主要空间
                    )
                    
                    // 标签 - 真正的最右侧显示
                    if (wish.tags.isNotEmpty()) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(3.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            wish.tags.take(2).forEach { tag ->  // 只显示前2个标签
                                Surface(
                                    color = WishPoolPrimary.copy(alpha = 0.15f),
                                    shape = RoundedCornerShape(6.dp)
                                ) {
                                    Text(
                                        text = "#$tag",
                                        fontSize = 8.sp,  // 从9sp减小到8sp
                                        fontWeight = FontWeight.Medium,
                                        color = WishPoolPrimary,
                                        modifier = Modifier.padding(horizontal = 5.dp, vertical = 2.dp)
                                    )
                                }
                            }
                            // 如果有更多标签，显示省略指示
                            if (wish.tags.size > 2) {
                                Text(
                                    text = "...",
                                    fontSize = 8.sp,  // 从9sp减小到8sp
                                    color = WishPoolPrimary.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                }
                
                // 描述文本
                if (wish.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = wish.description,
                        fontSize = 11.sp,  // 从12sp减小到11sp
                        color = WishPoolTextSecondary,
                        lineHeight = 15.sp,  // 从16sp减小到15sp
                        maxLines = if (isExpanded) Int.MAX_VALUE else 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 🖼️ 愿望图片展示
            if (wish.imageUris.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                WishImageGallery(
                    imageUris = wish.imageUris,
                    isExpanded = isExpanded
                )
            }
            
            // 📸 灵感库展示
            if (wish.inspirationItems.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                InspirationGallery(
                    inspirationItems = wish.inspirationItems,
                    isExpanded = isExpanded
                )
            }
            

            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 🎮 操作按钮组
            WishActionButtons(
                onSetAsGoal = onSetAsGoal,
                onEdit = onEdit,
                onArchive = onArchive,
                onShare = onShare,
                onDelete = { showDeleteConfirmDialog = true }, // 显示确认对话框
                isExpanded = isExpanded
            )
        }
    }
    
    // 🗑️ 删除确认对话框
    if (showDeleteConfirmDialog) {
        WishDeleteConfirmDialog(
            wishTitle = wish.title,
            onConfirm = {
                showDeleteConfirmDialog = false
                onDelete()
            },
            onDismiss = {
                showDeleteConfirmDialog = false
            }
        )
    }
}

/**
 * ⭐ 优先级星星组件
 */
@Composable
private fun PriorityStars(
    priority: Int,
    color: Color = WishPoolAccent
) {
    Row {
        repeat(5) { index ->
            Icon(
                imageVector = if (index < priority) Icons.Default.Star else Icons.Outlined.StarBorder,
                contentDescription = null,
                tint = if (index < priority) color else color.copy(alpha = 0.3f),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 🖼️ 愿望图片画廊 - 简化版
 */
@Composable
private fun WishImageGallery(
    imageUris: List<String>,
    isExpanded: Boolean
) {
    // 图片展示 - 简化版，去掉标题区域
    val visibleImages = if (isExpanded) imageUris else imageUris.take(1) // 未展开时只显示1张主图
    if (visibleImages.isNotEmpty()) {
        if (!isExpanded && imageUris.size == 1) {
            // 单张图片 - 缩小尺寸展示
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp), // 从160dp减小到120dp
                shape = RoundedCornerShape(12.dp),
                color = Color.Gray.copy(alpha = 0.1f)
            ) {
                AsyncImage(
                    model = visibleImages[0],
                    contentDescription = "愿望配图",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            }
        } else {
            // 多张图片 - 缩小尺寸网格展示
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(6.dp), // 从8dp减少到6dp
                contentPadding = PaddingValues(horizontal = 2.dp) // 从4dp减少到2dp
            ) {
                items(visibleImages.size) { index ->
                    val imageUri = visibleImages[index]
                    Surface(
                        modifier = Modifier
                            .size(64.dp), // 从80dp减小到64dp
                        shape = RoundedCornerShape(8.dp),
                        color = Color.Gray.copy(alpha = 0.1f)
                    ) {
                        AsyncImage(
                            model = imageUri,
                            contentDescription = "愿望配图 ${index + 1}",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                    }
                }
            }
        }
        
        if (!isExpanded && imageUris.size > 1) {
            Text(
                text = "还有 ${imageUris.size - 1} 张图片...",
                fontSize = 9.sp, // 从10sp减小到9sp
                color = WishPoolPrimary.copy(alpha = 0.7f),
                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                modifier = Modifier.padding(start = 2.dp, top = 4.dp) // 减少左边距
            )
        }
    }
}

/**
 * 📸 灵感库画廊
 */
@Composable
private fun InspirationGallery(
    inspirationItems: List<String>,
    isExpanded: Boolean
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.PhotoLibrary,
                contentDescription = null,
                tint = WishPoolPrimary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "灵感库",
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                color = WishPoolText
            )
            Spacer(modifier = Modifier.width(8.dp))
            Surface(
                color = WishPoolPrimary.copy(alpha = 0.2f),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = "${inspirationItems.size}",
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold,
                    color = WishPoolPrimary,
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 显示灵感项目
        val visibleItems = if (isExpanded) inspirationItems else inspirationItems.take(2)
        visibleItems.forEach { item ->
            Row(
                modifier = Modifier.padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Circle,
                    contentDescription = null,
                    tint = WishPoolSecondary,
                    modifier = Modifier.size(6.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = item,
                    fontSize = 11.sp,
                    color = WishPoolTextSecondary
                )
            }
        }
        
        if (!isExpanded && inspirationItems.size > 2) {
            Text(
                text = "还有 ${inspirationItems.size - 2} 项...",
                fontSize = 10.sp,
                color = WishPoolPrimary,
                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                modifier = Modifier.padding(start = 14.dp, top = 4.dp)
            )
        }
    }
}

/**
 * 🏷️ 标签行组件
 */
@Composable
private fun TagsRow(
    tags: List<String>,
    maxVisible: Int = 3
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        val visibleTags = tags.take(maxVisible)
        visibleTags.forEach { tag ->
            Surface(
                color = WishPoolSecondary.copy(alpha = 0.3f),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "#$tag",
                    fontSize = 9.sp,
                    fontWeight = FontWeight.Medium,
                    color = WishPoolText,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
        
        if (tags.size > maxVisible) {
            Surface(
                color = Color.Gray.copy(alpha = 0.2f),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "+${tags.size - maxVisible}",
                    fontSize = 11.sp,
                    fontWeight = FontWeight.Medium,
                    color = WishPoolTextSecondary,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

/**
 * 🎮 愿望操作按钮组
 */
@Composable
private fun WishActionButtons(
    onSetAsGoal: () -> Unit,
    onEdit: () -> Unit,
    onArchive: () -> Unit,
    onShare: () -> Unit,
    onDelete: () -> Unit,
    isExpanded: Boolean
) {
    AnimatedVisibility(
        visible = isExpanded,
        enter = fadeIn() + slideInVertically(),
        exit = fadeOut() + slideOutVertically()
    ) {
        Column {
            // 主要操作 - 设为目标
            Button(
                onClick = onSetAsGoal,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = WishPoolPrimary
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Rocket,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "设为目标",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 次要操作 - 第一行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SecondaryActionButton(
                    text = "编辑",
                    icon = Icons.Default.Edit,
                    onClick = onEdit,
                    modifier = Modifier.weight(1f)
                )
                
                SecondaryActionButton(
                    text = "分享",
                    icon = Icons.Default.Share,
                    onClick = onShare,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 次要操作 - 第二行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SecondaryActionButton(
                    text = "归档",
                    icon = Icons.Default.Archive,
                    onClick = onArchive,
                    modifier = Modifier.weight(1f)
                )
                
                SecondaryActionButton(
                    text = "删除",
                    icon = Icons.Default.Delete,
                    onClick = onDelete,
                    modifier = Modifier.weight(1f),
                    isDestructive = true
                )
            }
        }
    }
}

@Composable
private fun SecondaryActionButton(
    text: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isDestructive: Boolean = false
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier,
        border = BorderStroke(
            1.dp, 
            if (isDestructive) Color(0xFFE53E3E).copy(alpha = 0.3f) 
            else WishPoolPrimary.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = if (isDestructive) Color(0xFFE53E3E) else WishPoolPrimary
        )
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 🎭 精美空状态 - Apple风格
 */
@Composable
private fun WishPoolEmptyState(
    onAddWish: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 动画图标
        val infiniteTransition = rememberInfiniteTransition(label = "star_animation")
        val starScale by infiniteTransition.animateFloat(
            initialValue = 1f,
            targetValue = 1.2f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = EaseInOutSine),
                repeatMode = RepeatMode.Reverse
            ),
            label = "star_scale"
        )
        
        Icon(
            imageVector = Icons.Default.AutoAwesome,
            contentDescription = null,
            tint = WishPoolPrimary,
            modifier = Modifier
                .size(80.dp)
                .graphicsLayer(scaleX = starScale, scaleY = starScale)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "开始你的愿望之旅",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = WishPoolText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Text(
            text = "记录心中的梦想，让愿望成为目标\n每一个愿望都值得被实现 ✨",
            fontSize = 16.sp,
            color = WishPoolTextSecondary,
            textAlign = TextAlign.Center,
            lineHeight = 24.sp
        )
        
        Spacer(modifier = Modifier.height(32.dp))
    }
}

// 🎨 辅助函数
private fun getCategoryColor(category: com.timeflow.app.data.model.WishCategory): Color {
    return when (category) {
        com.timeflow.app.data.model.WishCategory.TRAVEL -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.SHOPPING -> Color(0xFFFF9800)
        com.timeflow.app.data.model.WishCategory.LEARNING -> Color(0xFF2196F3)
        com.timeflow.app.data.model.WishCategory.CAREER -> Color(0xFF9C27B0)
        com.timeflow.app.data.model.WishCategory.LIFESTYLE -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.HEALTH -> Color(0xFF4CAF50)
        com.timeflow.app.data.model.WishCategory.HOBBY -> Color(0xFFFF5722)
        com.timeflow.app.data.model.WishCategory.RELATIONSHIP -> Color(0xFFE91E63)
        com.timeflow.app.data.model.WishCategory.OTHER -> Color(0xFF607D8B)
    }
}

// 🚀 转化动效函数
private suspend fun playWishToGoalAnimation(wish: com.timeflow.app.data.model.WishModel) {
    // TODO: 实现樱花飘落动效
    delay(300)
}

// 🎯 愿望转目标逻辑
private suspend fun convertWishToGoal(
    wish: com.timeflow.app.data.model.WishModel,
    viewModel: com.timeflow.app.ui.viewmodel.WishListViewModel,
    navController: NavController
) {
    viewModel.convertWishToGoal(wish.id)
}

// 📤 分享愿望功能
private fun shareWish(wish: com.timeflow.app.data.model.WishModel, context: android.content.Context) {
    val shareIntent = android.content.Intent().apply {
        action = android.content.Intent.ACTION_SEND
        type = "text/plain"
        putExtra(android.content.Intent.EXTRA_TEXT, generateShareText(wish))
        putExtra(android.content.Intent.EXTRA_SUBJECT, "我的愿望：${wish.title}")
    }
    context.startActivity(android.content.Intent.createChooser(shareIntent, "分享愿望"))
}

// 📝 生成分享文案
private fun generateShareText(wish: com.timeflow.app.data.model.WishModel): String {
    val categoryEmoji = wish.category.emoji
    val priorityStars = "⭐".repeat(wish.priority)
    
    return buildString {
        appendLine("🌟 我的小愿望 🌟")
        appendLine()
        appendLine("$categoryEmoji ${wish.title}")
        appendLine()
        if (wish.description.isNotBlank()) {
            appendLine("💭 ${wish.description}")
            appendLine()
        }
        appendLine("✨ 期待值：$priorityStars")
        if (wish.tags.isNotEmpty()) {
            appendLine("🏷️ ${wish.tags.joinToString(" #", "#")}")
        }
        appendLine()
        appendLine("记录生活中的美好憧憬，让愿望成为前进的光✨")
        appendLine("——来自TimeFlow愿望池")
    }
}

/**
 * 🗑️ 删除确认对话框 - 优雅的确认提示
 */
@Composable
private fun WishDeleteConfirmDialog(
    wishTitle: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        shape = RoundedCornerShape(20.dp),
        title = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 警告图标
                Surface(
                    color = Color(0xFFFFEBEE),
                    shape = CircleShape,
                    modifier = Modifier.size(56.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.DeleteOutline,
                            contentDescription = null,
                            tint = Color(0xFFE53E3E),
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "确定删除愿望？",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = WishPoolText,
                    textAlign = TextAlign.Center
                )
            }
        },
        text = {
            Column {
                Text(
                    text = "即将删除愿望：",
                    fontSize = 14.sp,
                    color = WishPoolTextSecondary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Surface(
                    color = WishPoolPrimary.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "「$wishTitle」",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = WishPoolPrimary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(12.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "删除后无法恢复，请谨慎操作",
                    fontSize = 12.sp,
                    color = Color(0xFFE53E3E).copy(alpha = 0.8f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE53E3E)
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.fillMaxWidth(0.48f)
            ) {
                Text(
                    text = "确定删除",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        },
        dismissButton = {
            OutlinedButton(
                onClick = onDismiss,
                border = BorderStroke(1.dp, WishPoolPrimary.copy(alpha = 0.3f)),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.fillMaxWidth(0.48f)
            ) {
                Text(
                    text = "取消",
                    color = WishPoolPrimary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    )
} 