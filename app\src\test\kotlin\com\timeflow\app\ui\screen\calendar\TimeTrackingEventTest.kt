package com.timeflow.app.ui.screen.calendar

import com.timeflow.app.ui.screen.calendar.components.TimeTrackingEvent
import org.junit.Test
import org.junit.Assert.*
import java.time.LocalDateTime

/**
 * 时间追踪事件数据模型测试
 */
class TimeTrackingEventTest {

    @Test
    fun `test duration calculation`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 0)
        val endTime = LocalDateTime.of(2024, 1, 1, 10, 30)
        
        val event = TimeTrackingEvent(
            id = "test-1",
            taskName = "测试任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 5400, // 90分钟 = 5400秒
            taskId = "task-1",
            sessionId = "session-1",
            timerType = "NORMAL"
        )
        
        assertEquals(1, event.durationHours)
        assertEquals(30, event.durationMinutes)
        assertEquals("1h30m", event.formattedDuration)
    }

    @Test
    fun `test short duration formatting`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 0)
        val endTime = LocalDateTime.of(2024, 1, 1, 9, 25)
        
        val event = TimeTrackingEvent(
            id = "test-2",
            taskName = "短任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 1500, // 25分钟 = 1500秒
            taskId = "task-2",
            sessionId = "session-2",
            timerType = "POMODORO"
        )
        
        assertEquals(0, event.durationHours)
        assertEquals(25, event.durationMinutes)
        assertEquals("25m", event.formattedDuration)
    }

    @Test
    fun `test very short duration formatting`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 0)
        val endTime = LocalDateTime.of(2024, 1, 1, 9, 0, 45)
        
        val event = TimeTrackingEvent(
            id = "test-3",
            taskName = "极短任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 45, // 45秒
            taskId = "task-3",
            sessionId = "session-3",
            timerType = "NORMAL"
        )
        
        assertEquals(0, event.durationHours)
        assertEquals(0, event.durationMinutes)
        assertEquals("45s", event.formattedDuration)
    }

    @Test
    fun `test long duration formatting`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 0)
        val endTime = LocalDateTime.of(2024, 1, 1, 12, 15)

        val event = TimeTrackingEvent(
            id = "test-4",
            taskName = "长任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 11700, // 3小时15分钟 = 11700秒
            taskId = "task-4",
            sessionId = "session-4",
            timerType = "NORMAL",
            focusRating = 5
        )

        assertEquals(3, event.durationHours)
        assertEquals(15, event.durationMinutes)
        assertEquals("3h15m", event.formattedDuration)
        assertEquals(5, event.focusRating)
    }

    @Test
    fun `test cross-hour event duration`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 30)
        val endTime = LocalDateTime.of(2024, 1, 1, 11, 45)

        val event = TimeTrackingEvent(
            id = "test-5",
            taskName = "跨小时任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 8100, // 2小时15分钟 = 8100秒
            taskId = "task-5",
            sessionId = "session-5",
            timerType = "POMODORO"
        )

        assertEquals(2, event.durationHours)
        assertEquals(15, event.durationMinutes)
        assertEquals("2h15m", event.formattedDuration)

        // 验证是否超过60分钟（应该只在开始小时显示标题）
        assertTrue("跨小时事件应该超过60分钟", event.duration >= 3600)
    }

    @Test
    fun `test title display logic for cross-hour events`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 9, 30)
        val endTime = LocalDateTime.of(2024, 1, 1, 11, 45)

        val longEvent = TimeTrackingEvent(
            id = "test-6",
            taskName = "长时间任务",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 8100, // 2小时15分钟 = 8100秒
            taskId = "task-6",
            sessionId = "session-6",
            timerType = "NORMAL"
        )

        // 验证长时间事件的显示逻辑
        assertTrue("长时间事件应该超过60分钟", longEvent.duration >= 3600)
        assertEquals("开始小时应该是9", 9, longEvent.actualStartTime.hour)
        assertEquals("结束小时应该是11", 11, longEvent.actualEndTime.hour)

        // 短时间事件测试
        val shortEvent = TimeTrackingEvent(
            id = "test-7",
            taskName = "短时间任务",
            actualStartTime = LocalDateTime.of(2024, 1, 1, 14, 10),
            actualEndTime = LocalDateTime.of(2024, 1, 1, 14, 35),
            duration = 1500, // 25分钟 = 1500秒
            taskId = "task-7",
            sessionId = "session-7",
            timerType = "POMODORO"
        )

        assertFalse("短时间事件应该少于60分钟", shortEvent.duration >= 3600)
    }

    @Test
    fun `test event detail information`() {
        val startTime = LocalDateTime.of(2024, 1, 1, 14, 30)
        val endTime = LocalDateTime.of(2024, 1, 1, 16, 45)

        val event = TimeTrackingEvent(
            id = "test-8",
            taskName = "深度学习",
            actualStartTime = startTime,
            actualEndTime = endTime,
            duration = 8100, // 2小时15分钟 = 8100秒
            taskId = "task-8",
            sessionId = "session-8",
            timerType = "POMODORO",
            focusRating = 4,
            productivityRating = 5
        )

        // 验证详情信息
        assertEquals("深度学习", event.taskName)
        assertEquals("POMODORO", event.timerType)
        assertEquals(4, event.focusRating)
        assertEquals(5, event.productivityRating)
        assertEquals("2h15m", event.formattedDuration)

        // 验证时间格式
        assertEquals(14, event.actualStartTime.hour)
        assertEquals(30, event.actualStartTime.minute)
        assertEquals(16, event.actualEndTime.hour)
        assertEquals(45, event.actualEndTime.minute)
    }

    @Test
    fun `test longest hour block calculation`() {
        // 测试跨小时事件的最长时间块计算

        // 场景1：09:30-11:45，应该在10:00-11:00显示标题（60分钟最长）
        val event1 = TimeTrackingEvent(
            id = "test-9",
            taskName = "跨小时任务1",
            actualStartTime = LocalDateTime.of(2024, 1, 1, 9, 30),
            actualEndTime = LocalDateTime.of(2024, 1, 1, 11, 45),
            duration = 8100, // 2小时15分钟
            taskId = "task-9",
            sessionId = "session-9",
            timerType = "NORMAL"
        )

        // 09:30-10:00 = 30分钟
        // 10:00-11:00 = 60分钟（最长）
        // 11:00-11:45 = 45分钟
        // 预期最长块在10点

        // 场景2：14:10-16:20，应该在15:00-16:00显示标题（60分钟最长）
        val event2 = TimeTrackingEvent(
            id = "test-10",
            taskName = "跨小时任务2",
            actualStartTime = LocalDateTime.of(2024, 1, 1, 14, 10),
            actualEndTime = LocalDateTime.of(2024, 1, 1, 16, 20),
            duration = 7800, // 2小时10分钟
            taskId = "task-10",
            sessionId = "session-10",
            timerType = "POMODORO"
        )

        // 14:10-15:00 = 50分钟
        // 15:00-16:00 = 60分钟（最长）
        // 16:00-16:20 = 20分钟
        // 预期最长块在15点

        // 验证跨小时事件的特征
        assertTrue("事件1应该超过60分钟", event1.duration >= 3600)
        assertTrue("事件2应该超过60分钟", event2.duration >= 3600)
        assertNotEquals("事件1应该跨小时", event1.actualStartTime.hour, event1.actualEndTime.hour)
        assertNotEquals("事件2应该跨小时", event2.actualStartTime.hour, event2.actualEndTime.hour)
    }
}
