package com.timeflow.app.ui.screen.account

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.PhotoCamera
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import com.timeflow.app.ui.screen.settings.SettingsViewModel
import com.timeflow.app.ui.components.payment.PaymentDialog
import com.timeflow.app.data.model.MembershipPlan
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import android.util.Log

/**
 * 账户页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountScreen(
    navController: NavController,
    settingsViewModel: SettingsViewModel = hiltViewModel()
) {
    // 获取当前活动实例
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 获取用户信息状态
    val userInfo by settingsViewModel.userInfo.collectAsState()
    val avatarUri by settingsViewModel.avatarUri.collectAsState()
    
    // 编辑状态
    var isEditingNickname by remember { mutableStateOf(false) }
    var tempNickname by remember { mutableStateOf(userInfo.nickname) }
    var isEditingEmail by remember { mutableStateOf(false) }
    var tempEmail by remember { mutableStateOf(userInfo.email) }
    
    // 更新临时值当用户信息变化时
    LaunchedEffect(userInfo.nickname) {
        tempNickname = userInfo.nickname
    }
    
    LaunchedEffect(userInfo.email) {
        tempEmail = userInfo.email
    }
    
    // 焦点请求器
    val nicknameFocusRequester = remember { FocusRequester() }
    val emailFocusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // 支付相关状态
    val showPaymentDialog by settingsViewModel.showPaymentDialog.collectAsState()
    val selectedMembershipPlan by settingsViewModel.selectedMembershipPlan.collectAsState()
    val paymentState by settingsViewModel.paymentState.collectAsState()
    
    // 相册选择器
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            settingsViewModel.saveAvatarUri(it)
            Log.d("AccountScreen", "头像已选择: $it")
        }
    }
    
    // 权限请求
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            galleryLauncher.launch("image/*")
        } else {
            Log.d("AccountScreen", "存储权限被拒绝")
        }
    }
    
    // 选择头像函数
    val selectAvatar = {
        when {
            // Android 10+不再需要外部存储权限来访问媒体
            android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q -> {
                galleryLauncher.launch("image/*")
            }
            // 低版本Android需要请求权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED -> {
                galleryLauncher.launch("image/*")
            }
            else -> {
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明黑色状态栏
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("AccountScreen", "AccountScreen disposed")
            }
        }
    }

    // 页面内容
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    "账户",
                    fontWeight = FontWeight.SemiBold,
                    color = TextPrimary
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = TextPrimary
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFFF8F8F8)
            )
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 头像区域卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "头像",
                    style = MaterialTheme.typography.bodyLarge,
                    color = TextSecondary,
                    modifier = Modifier.align(Alignment.Start)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 头像容器
                Box(
                    modifier = Modifier
                        .size(100.dp)
                        .clickable { selectAvatar() }
                ) {
                    // 头像背景
                    Box(
                        modifier = Modifier
                            .size(100.dp)
                        .clip(CircleShape)
                            .background(LavenderAsh.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.Center
                    ) {
                        if (avatarUri != null) {
                            // 显示用户选择的头像
                            var isLoading by remember { mutableStateOf(true) }
                            
                            Image(
                                painter = rememberAsyncImagePainter(
                                    model = avatarUri,
                                    onState = { state ->
                                        isLoading = state is AsyncImagePainter.State.Loading
                                    }
                                ),
                                contentDescription = "用户头像",
                                modifier = Modifier
                                    .size(100.dp)
                                    .clip(CircleShape),
                                contentScale = ContentScale.Crop
                            )
                            
                            // 加载进度指示器
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(30.dp),
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        } else {
                            // 显示默认头像图标
                    Icon(
                        imageVector = Icons.Outlined.Person,
                                contentDescription = "默认头像",
                        tint = DustyLavender,
                                modifier = Modifier.size(50.dp)
                            )
                        }
                    }
                    
                    // 编辑图标
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.primary)
                            .align(Alignment.BottomEnd),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.PhotoCamera,
                            contentDescription = "更换头像",
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "点击更换头像",
                    style = MaterialTheme.typography.bodySmall,
                    color = TextSecondary,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 个人信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp
            )
        ) {
            Column {
            // 昵称
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                        .clickable { 
                            isEditingNickname = true 
                        }
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "昵称",
                    style = MaterialTheme.typography.bodyLarge,
                    color = TextSecondary,
                    modifier = Modifier.weight(1f)
                )
                
                    if (isEditingNickname) {
                        OutlinedTextField(
                            value = tempNickname,
                            onValueChange = { tempNickname = it },
                            modifier = Modifier
                                .width(160.dp)
                                .focusRequester(nicknameFocusRequester),
                            keyboardOptions = KeyboardOptions(
                                imeAction = ImeAction.Done
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    settingsViewModel.updateNickname(tempNickname)
                                    isEditingNickname = false
                                    keyboardController?.hide()
                                }
                            ),
                            singleLine = true,
                            textStyle = MaterialTheme.typography.bodyMedium // 缩小字体
                        )
                        
                        LaunchedEffect(Unit) {
                            nicknameFocusRequester.requestFocus()
                        }
                    } else {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 显示金色王冠图标（终身会员）
                            if (userInfo.membershipType == com.timeflow.app.ui.screen.settings.MembershipType.LIFETIME) {
                                Text(
                                    text = "👑",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(end = 4.dp)
                                )
                            }
                                                        Text(
                                 text = userInfo.nickname,
                                 style = MaterialTheme.typography.bodyMedium, // 缩小字体
                                 color = TextPrimary
                             )
                         }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "编辑昵称",
                    tint = TextSecondary.copy(alpha = 0.6f),
                    modifier = Modifier.size(16.dp)
                )
                    }
                }
                
                HorizontalDivider(
                    color = LavenderAsh.copy(alpha = 0.3f),
                    thickness = 0.5.dp
                )
                
                            // 邮箱
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                        .clickable { 
                            isEditingEmail = true 
                        }
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "邮箱",
                    style = MaterialTheme.typography.bodyMedium, // 缩小字体
                    color = TextSecondary,
                    modifier = Modifier.weight(1f)
                )
                    
                    if (isEditingEmail) {
                        OutlinedTextField(
                            value = tempEmail,
                            onValueChange = { tempEmail = it },
                            modifier = Modifier
                                .width(200.dp)
                                .focusRequester(emailFocusRequester),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Email,
                                imeAction = ImeAction.Done
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    settingsViewModel.updateEmail(tempEmail)
                                    isEditingEmail = false
                                    keyboardController?.hide()
                                }
                            ),
                            singleLine = true,
                            placeholder = { Text("输入邮箱地址", style = MaterialTheme.typography.bodySmall) },
                            textStyle = MaterialTheme.typography.bodyMedium // 缩小字体
                        )
                        
                        LaunchedEffect(Unit) {
                            emailFocusRequester.requestFocus()
                        }
                    } else {
                        Column(
                            horizontalAlignment = Alignment.End
                        ) {
                            Text(
                                text = if (userInfo.email.isEmpty()) "未设置" else userInfo.email,
                                style = MaterialTheme.typography.bodyMedium, // 缩小字体
                                color = if (userInfo.email.isEmpty()) Color.Gray else TextPrimary
                            )
                            
                            if (userInfo.email.isNotEmpty()) {
                                Text(
                                    text = if (userInfo.isEmailVerified) "已验证" else "未验证",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (userInfo.isEmailVerified) Color(0xFF4CAF50) else Color(0xFFFF9800)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "编辑邮箱",
                            tint = TextSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 账户绑定卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp
            )
        ) {
            Column {
                // 微信
                AccountBindItem(
                    title = "微信",
                    value = if (userInfo.wechatBound) "已绑定" else "未绑定",
                    subtitle = if (userInfo.wechatBound) userInfo.wechatNickname else null,
                    onClick = { 
                        if (userInfo.wechatBound) {
                            settingsViewModel.unbindWechat()
                        } else {
                            settingsViewModel.bindWechat("微信用户")
                        }
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 会员服务卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 0.dp
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "会员服务",
                    style = MaterialTheme.typography.titleMedium,
                    color = TextPrimary,
                    fontWeight = FontWeight.SemiBold
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 当前会员状态
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "当前状态",
                        style = MaterialTheme.typography.bodyMedium,
                        color = TextSecondary
                    )
                    Text(
                        text = settingsViewModel.getMembershipStatus(),
                        style = MaterialTheme.typography.bodyMedium,
                        color = when (userInfo.membershipType) {
                            com.timeflow.app.ui.screen.settings.MembershipType.LIFETIME -> Color(0xFFFFD700) // 金色
                            com.timeflow.app.ui.screen.settings.MembershipType.MONTHLY -> Color(0xFF4CAF50) // 绿色
                            else -> TextSecondary
                        },
                        fontWeight = FontWeight.Medium
                    )
                }
                
                if (userInfo.membershipType == com.timeflow.app.ui.screen.settings.MembershipType.NONE) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 月度会员购买按钮
                    Button(
                        onClick = { settingsViewModel.purchaseMonthlyMembership() },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4CAF50)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = "购买月度会员 ¥9.90/月",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.White
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 终身会员购买按钮
                    Button(
                        onClick = { settingsViewModel.purchaseLifetimeMembership() },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFFD700)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "👑",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "购买终身会员 ¥29.90",
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.Black,
                                fontWeight = FontWeight.SemiBold
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 退出登录按钮
        Button(
            onClick = { 
                // 退出登录逻辑
                settingsViewModel.clearAllUserData()
                Log.d("AccountScreen", "用户退出登录，数据已清理")
                // 这里可以添加导航到登录页面的逻辑
                navController.popBackStack()
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFE57373)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = "退出登录",
                style = MaterialTheme.typography.bodyMedium, // 缩小字体
                color = Color.White,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
    }
    
    // 支付对话框
    selectedMembershipPlan?.let { plan ->
        PaymentDialog(
            isVisible = showPaymentDialog,
            membershipPlan = plan,
            onPaymentMethodSelected = { paymentMethod ->
                activity?.let { act ->
                    settingsViewModel.onPaymentMethodSelected(act, paymentMethod)
                }
            },
            onDismiss = {
                settingsViewModel.dismissPaymentDialog()
            },
            paymentState = paymentState
        )
    }
}

/**
 * 账户绑定项组件
 */
@Composable
fun AccountBindItem(
    title: String,
    value: String,
    subtitle: String? = null,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium, // 缩小字体
            color = TextSecondary,
            modifier = Modifier.weight(1f)
        )
        
        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium, // 缩小字体
                color = if (value == "已绑定") TextPrimary else Color.Gray
            )
            
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = TextSecondary
                )
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Icon(
            imageVector = Icons.Filled.ChevronRight,
            contentDescription = null,
            tint = TextSecondary.copy(alpha = 0.6f),
            modifier = Modifier.size(16.dp)
        )
    }
} 