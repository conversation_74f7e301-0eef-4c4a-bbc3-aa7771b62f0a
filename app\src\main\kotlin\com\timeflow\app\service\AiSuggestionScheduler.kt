package com.timeflow.app.service

import android.content.Context
import android.util.Log
import androidx.work.Data
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import com.timeflow.app.worker.AiSuggestionWorker
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI建议通知调度服务
 * 负责管理周期性AI建议通知的调度
 */
@Singleton
class AiSuggestionScheduler @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "AiSuggestionScheduler"
        private const val DAILY_WORK_NAME = "ai_suggestion_daily"
        private const val WEEKLY_WORK_NAME = "ai_suggestion_weekly"
        private const val MONTHLY_WORK_NAME = "ai_suggestion_monthly"
    }
    
    /**
     * 启用周期性AI建议通知
     */
    fun enablePeriodicAiSuggestions() {
        try {
            Log.d(TAG, "🤖 启用周期性AI建议通知")
            
            // 调度每日总结（每天21:00发送）
            scheduleDailySummary()
            
            // 调度周报（每周日21:00发送）
            scheduleWeeklyReport()
            
            // 调度月报（每月最后一天21:00发送）
            scheduleMonthlyReport()
            
            Log.d(TAG, "✅ 周期性AI建议通知已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用周期性AI建议通知失败", e)
        }
    }
    
    /**
     * 禁用周期性AI建议通知
     */
    fun disablePeriodicAiSuggestions() {
        try {
            val workManager = WorkManager.getInstance(context)
            
            // 取消所有AI建议相关的工作
            workManager.cancelUniqueWork(DAILY_WORK_NAME)
            workManager.cancelUniqueWork(WEEKLY_WORK_NAME)
            workManager.cancelUniqueWork(MONTHLY_WORK_NAME)
            
            Log.d(TAG, "❌ 周期性AI建议通知已禁用")
        } catch (e: Exception) {
            Log.e(TAG, "禁用周期性AI建议通知失败", e)
        }
    }
    
    /**
     * 调度每日工作效率总结
     */
    private fun scheduleDailySummary() {
        try {
            // 创建每日总结工作请求
            val dailyWorkRequest = PeriodicWorkRequestBuilder<AiSuggestionWorker>(
                1, TimeUnit.DAYS  // 每天执行一次
            )
                .setInputData(
                    Data.Builder()
                        .putString("notification_type", "daily")
                        .build()
                )
                .setInitialDelay(calculateInitialDelayForDaily(), TimeUnit.MILLISECONDS)
                .build()
            
            // 调度工作（替换已存在的）
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                DAILY_WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                dailyWorkRequest
            )
            
            Log.d(TAG, "📈 每日效率总结通知已调度")
        } catch (e: Exception) {
            Log.e(TAG, "调度每日效率总结通知失败", e)
        }
    }

    /**
     * 调度每周AI建议通知
     */
    private fun scheduleWeeklyReport() {
        try {
            // 创建周报工作请求
            val weeklyWorkRequest = PeriodicWorkRequestBuilder<AiSuggestionWorker>(
                7, TimeUnit.DAYS  // 每7天执行一次
            )
                .setInputData(
                    Data.Builder()
                        .putString("notification_type", "weekly")
                        .build()
                )
                .setInitialDelay(calculateInitialDelayForWeekly(), TimeUnit.MILLISECONDS)
                .build()
            
            // 调度工作（替换已存在的）
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WEEKLY_WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                weeklyWorkRequest
            )
            
            Log.d(TAG, "📊 每周AI建议通知已调度")
        } catch (e: Exception) {
            Log.e(TAG, "调度每周AI建议通知失败", e)
        }
    }
    
    /**
     * 调度每月AI建议通知
     */
    private fun scheduleMonthlyReport() {
        try {
            // 创建月报工作请求
            val monthlyWorkRequest = PeriodicWorkRequestBuilder<AiSuggestionWorker>(
                30, TimeUnit.DAYS  // 每30天执行一次
            )
                .setInputData(
                    Data.Builder()
                        .putString("notification_type", "monthly")
                        .build()
                )
                .setInitialDelay(calculateInitialDelayForMonthly(), TimeUnit.MILLISECONDS)
                .build()
            
            // 调度工作（替换已存在的）
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                MONTHLY_WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                monthlyWorkRequest
            )
            
            Log.d(TAG, "📅 每月AI建议通知已调度")
        } catch (e: Exception) {
            Log.e(TAG, "调度每月AI建议通知失败", e)
        }
    }
    
    /**
     * 计算到今天21:00的延迟时间（如果已过，则计算到明天21:00）
     */
    private fun calculateInitialDelayForDaily(): Long {
        val now = java.time.LocalDateTime.now()
        val today21 = now.toLocalDate()
            .atTime(21, 0)
            .withSecond(0)
            .withNano(0)
        
        // 如果今天21:00已过，则调度到明天21:00
        val targetTime = if (today21.isBefore(now) || today21.isEqual(now)) {
            today21.plusDays(1)
        } else {
            today21
        }
        
        val delayMillis = java.time.Duration.between(now, targetTime).toMillis()
        
        Log.d(TAG, "📈 下次每日总结时间: $targetTime, 延迟: ${delayMillis / 1000}秒")
        return delayMillis
    }

    /**
     * 计算到下次周日21:00的延迟时间
     */
    private fun calculateInitialDelayForWeekly(): Long {
        val now = java.time.LocalDateTime.now()
        val nextSunday = now.with(java.time.DayOfWeek.SUNDAY)
            .withHour(21)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)
        
        // 如果本周日已过，则计算下周日
        val targetTime = if (nextSunday.isBefore(now) || nextSunday.isEqual(now)) {
            nextSunday.plusWeeks(1)
        } else {
            nextSunday
        }
        
        val delayMillis = java.time.Duration.between(now, targetTime).toMillis()
        
        Log.d(TAG, "📊 下次周报时间: $targetTime, 延迟: ${delayMillis / 1000}秒")
        return delayMillis
    }
    
    /**
     * 计算到下月最后一天21:00的延迟时间
     */
    private fun calculateInitialDelayForMonthly(): Long {
        val now = java.time.LocalDateTime.now()
        val lastDayOfMonth = now.with(java.time.temporal.TemporalAdjusters.lastDayOfMonth())
            .withHour(21)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)
        
        // 如果本月最后一天已过，则计算下月最后一天
        val targetTime = if (lastDayOfMonth.isBefore(now) || lastDayOfMonth.isEqual(now)) {
            now.plusMonths(1).with(java.time.temporal.TemporalAdjusters.lastDayOfMonth())
                .withHour(21)
                .withMinute(0)
                .withSecond(0)
                .withNano(0)
        } else {
            lastDayOfMonth
        }
        
        val delayMillis = java.time.Duration.between(now, targetTime).toMillis()
        
        Log.d(TAG, "📅 下次月报时间: $targetTime, 延迟: ${delayMillis / 1000}秒")
        return delayMillis
    }
    
    /**
     * 立即发送测试AI建议通知
     */
    fun sendTestAiSuggestion(type: String = "daily") {
        try {
            val testWorkRequest = OneTimeWorkRequestBuilder<AiSuggestionWorker>()
                .setInputData(
                    Data.Builder()
                        .putString("notification_type", type)
                        .build()
                )
                .build()
            
            WorkManager.getInstance(context).enqueue(testWorkRequest)
            
            Log.d(TAG, "🧪 测试AI建议通知已发送: $type")
        } catch (e: Exception) {
            Log.e(TAG, "发送测试AI建议通知失败", e)
        }
    }
    
    /**
     * 获取当前调度状态
     */
    fun getScheduleStatus(): String {
        return try {
            val workManager = WorkManager.getInstance(context)
            val dailyWorkInfo = workManager.getWorkInfosForUniqueWork(DAILY_WORK_NAME).get()
            val weeklyWorkInfo = workManager.getWorkInfosForUniqueWork(WEEKLY_WORK_NAME).get()
            val monthlyWorkInfo = workManager.getWorkInfosForUniqueWork(MONTHLY_WORK_NAME).get()
            
            buildString {
                appendLine("🤖 AI建议通知调度状态:")
                appendLine("━━━━━━━━━━━━━━━━━━━━")
                
                appendLine("📈 日报状态:")
                if (dailyWorkInfo.isNotEmpty()) {
                    val workInfo = dailyWorkInfo.first()
                    appendLine("  • 状态: ${workInfo.state}")
                    appendLine("  • 执行次数: ${workInfo.runAttemptCount}")
                    appendLine("  • 下次执行: 每天21:00")
                } else {
                    appendLine("  • 状态: 未调度")
                }
                
                appendLine()
                appendLine("📊 周报状态:")
                if (weeklyWorkInfo.isNotEmpty()) {
                    val workInfo = weeklyWorkInfo.first()
                    appendLine("  • 状态: ${workInfo.state}")
                    appendLine("  • 执行次数: ${workInfo.runAttemptCount}")
                    appendLine("  • 下次执行: 每周日21:00")
                } else {
                    appendLine("  • 状态: 未调度")
                }
                
                appendLine()
                appendLine("📅 月报状态:")
                if (monthlyWorkInfo.isNotEmpty()) {
                    val workInfo = monthlyWorkInfo.first()
                    appendLine("  • 状态: ${workInfo.state}")
                    appendLine("  • 执行次数: ${workInfo.runAttemptCount}")
                    appendLine("  • 下次执行: 每月最后一天21:00")
                } else {
                    appendLine("  • 状态: 未调度")
                }
                
                appendLine()
                appendLine("💡 说明:")
                appendLine("  • 日报：每天21:00发送当日效率总结")
                appendLine("  • 周报：每周日21:00发送工作效率总结")
                appendLine("  • 月报：每月最后一天21:00发送深度分析")
                appendLine("  • 通知会根据用户设置决定是否发送")
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取调度状态失败", e)
            "❌ 获取调度状态失败: ${e.message}"
        }
    }
} 