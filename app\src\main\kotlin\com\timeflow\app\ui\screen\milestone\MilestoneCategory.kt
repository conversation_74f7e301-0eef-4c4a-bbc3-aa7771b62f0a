package com.timeflow.app.ui.screen.milestone

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Work
import androidx.compose.material.icons.filled.School
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.People
import androidx.compose.material.icons.filled.HealthAndSafety
import androidx.compose.material.icons.filled.Flight
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.More
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 里程碑类别枚举
 */
enum class MilestoneCategory(
    val displayName: String,
    val icon: ImageVector,
    val color: Color,
    val ratingName: String = "重要性"
) {
    CAREER("职业", Icons.Default.Work, Color(0xFFAAB1BB), "成就感"),
    EDUCATION("教育", Icons.Default.School, Color(0xFFB3C2A7), "学习收获"),
    <PERSON>O<PERSON>("爱情", Icons.Default.Favorite, Color(0xFFE91E63), "幸福感"),
    FAMILY("家庭", Icons.Default.People, Color(0xFFFFC107), "温馨感"),
    HEALTH("健康", Icons.Default.HealthAndSafety, Color(0xFF9C27B0), "成长度"),
    TRAVEL("旅行", Icons.Default.Flight, Color(0xFF6B7967), "探索度"),
    FINANCE("财务", Icons.Default.AccountBalance, Color(0xFF3F51B5), "满意度"),
    SKILL("技能", Icons.Default.Star, Color(0xFFFF5722), "精通度"),
    LIFE("生活", Icons.Default.Home, Color(0xFF795548), "充实感"),
    OTHER("其他", Icons.Default.More, Color(0xFF607D8B), "评分")
} 