package com.timeflow.app.ui.screen.health

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin

/**
 * 症状小部件 - 在主页面上显示简化版的症状卡片
 */
@Composable
fun SymptomWidget(
    onNavigateToSymptomDetail: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable(onClick = onNavigateToSymptomDetail),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "今日症状",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF880E4F),
                    modifier = Modifier.weight(1f)
                )
                
                // 添加按钮
                IconButton(
                    onClick = onNavigateToSymptomDetail,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加症状",
                        tint = Color(0xFF4CAF50)
                    )
                }
            }
            
            // 症状图标区域
            Row(
                modifier = Modifier.fillMaxWidth().padding(top = 8.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 流量
                SymptomIconSimple(
                    backgroundColor = Color(0xFFFFEBEE),
                    icon = {
                        FlowIcon(
                            modifier = Modifier.size(28.dp)
                        )
                    },
                    text = "流量"
                )
                
                // 痛感
                SymptomIconSimple(
                    backgroundColor = Color(0xFFE3F2FD),
                    icon = {
                        PainIcon(
                            modifier = Modifier.size(28.dp)
                        )
                    },
                    text = "痛感"
                )
                
                // 情绪
                SymptomIconSimple(
                    backgroundColor = Color(0xFFFFF8E1),
                    icon = {
                        MoodIcon(
                            modifier = Modifier.size(28.dp)
                        )
                    },
                    text = "情绪"
                )
                
                // 睡眠
                SymptomIconSimple(
                    backgroundColor = Color(0xFFE8F5E9),
                    icon = {
                        SleepIcon(
                            modifier = Modifier.size(28.dp)
                        )
                    },
                    text = "睡眠"
                )
            }
        }
    }
}

/**
 * 简化版症状图标组件
 */
@Composable
fun SymptomIconSimple(
    backgroundColor: Color,
    icon: @Composable () -> Unit,
    text: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            icon()
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = text,
            fontSize = 12.sp,
            color = Color.DarkGray
        )
    }
} 