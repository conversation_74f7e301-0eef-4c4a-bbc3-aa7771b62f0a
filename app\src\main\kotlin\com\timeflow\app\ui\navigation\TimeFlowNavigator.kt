package com.timeflow.app.ui.navigation

import androidx.navigation.NavController
import com.timeflow.app.utils.NavigationOptimizer

/**
 * 应用导航接口
 */
interface TimeFlowNavigator {
    /**
     * 导航返回
     */
    fun navigateBack()
    
    /**
     * 导航向上返回
     */
    fun navigateUp()
    
    /**
     * 导航到感想主页
     */
    fun navigateToReflections()
    
    /**
     * 导航到感想详情页面
     */
    fun navigateToReflectionDetail(reflectionId: String)
    
    /**
     * 添加感想页面
     */
    fun navigateToAddReflection()
}

/**
 * TimeFlowNavigator的默认实现
 */
class TimeFlowNavigatorImpl(
    private val navController: NavController
) : TimeFlowNavigator {
    override fun navigateBack() {
        navController.popBackStack()
    }
    
    override fun navigateUp() {
        navController.navigateUp()
    }
    
    override fun navigateToReflections() {
        NavigationOptimizer.safeNavigate(
            navController,
            AppDestinations.REFLECTION_ROUTE
        )
    }
    
    override fun navigateToReflectionDetail(reflectionId: String) {
        // 当reflectionId为空时，导航到新建感想页面
        val route = if (reflectionId.isBlank()) {
            AppDestinations.ADD_REFLECTION_ROUTE
        } else {
            AppDestinations.reflectionDetailRoute(reflectionId)
        }
        
        NavigationOptimizer.safeNavigate(
            navController,
            route
        )
    }
    
    override fun navigateToAddReflection() {
        NavigationOptimizer.safeNavigate(
            navController,
            AppDestinations.ADD_REFLECTION_ROUTE
        )
    }
}

/**
 * 创建TimeFlowNavigator实例
 */
fun NavController.asTimeFlowNavigator(): TimeFlowNavigator {
    return TimeFlowNavigatorImpl(this)
} 