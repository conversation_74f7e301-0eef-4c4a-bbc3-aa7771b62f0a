package com.timeflow.app.ui.screen.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.repository.EmotionRecordRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

/**
 * 个人资料页面ViewModel
 * 管理情绪记录的持久化和状态
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val emotionRecordRepository: EmotionRecordRepository
) : ViewModel() {
    
    private val _emotionRecords = MutableStateFlow<List<EmotionRecord>>(emptyList())
    val emotionRecords: StateFlow<List<EmotionRecord>> = _emotionRecords.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    init {
        android.util.Log.d("ProfileViewModel", "🚀 ProfileViewModel初始化，开始加载情绪记录...")
        checkDatabaseStatus()
        loadEmotionRecords()
    }

    /**
     * 检查数据库状态
     */
    private fun checkDatabaseStatus() {
        viewModelScope.launch {
            try {
                val count = emotionRecordRepository.getEmotionRecordCount()
                android.util.Log.d("ProfileViewModel", "📊 数据库中现有情绪记录数量: $count")
            } catch (e: Exception) {
                android.util.Log.e("ProfileViewModel", "❌ 检查数据库状态失败", e)
            }
        }
    }
    
    /**
     * 加载所有情绪记录
     */
    private fun loadEmotionRecords() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                android.util.Log.d("ProfileViewModel", "开始加载情绪记录...")

                // 🔧 修复：在collect之前就设置loading为false，因为collect是持续的流
                emotionRecordRepository.getAllEmotionRecords().collect { records ->
                    _emotionRecords.value = records
                    _isLoading.value = false // 🔧 在收到第一批数据后立即设置为false
                    android.util.Log.d("ProfileViewModel", "✅ 加载了 ${records.size} 条情绪记录")
                }
            } catch (e: Exception) {
                _error.value = "加载情绪记录失败: ${e.message}"
                _isLoading.value = false // 🔧 确保在错误时也设置为false
                android.util.Log.e("ProfileViewModel", "❌ 加载情绪记录失败", e)
            }
        }
    }
    
    /**
     * 🔧 保存情绪记录（支持详细模式）
     */
    fun saveEmotionRecord(record: EmotionRecord) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val recordId = emotionRecordRepository.saveEmotionRecord(record)
                
                // 更新本地状态
                val updatedRecords = _emotionRecords.value.toMutableList()
                // 移除同日期的旧记录（如果存在）
                updatedRecords.removeAll { it.date == record.date }
                // 添加新记录
                updatedRecords.add(record)
                // 按日期排序
                updatedRecords.sortByDescending { it.date }
                _emotionRecords.value = updatedRecords
                
                android.util.Log.d("ProfileViewModel", "✅ 情绪记录保存成功: $recordId, 详细模式: ${record.isDetailed}")
                _error.value = null
            } catch (e: Exception) {
                _error.value = "保存情绪记录失败: ${e.message}"
                android.util.Log.e("ProfileViewModel", "保存情绪记录失败", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新情绪记录
     */
    fun updateEmotionRecord(record: EmotionRecord) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                emotionRecordRepository.updateEmotionRecord(record)
                
                // 更新本地状态
                val updatedRecords = _emotionRecords.value.map { 
                    if (it.date == record.date) record else it 
                }
                _emotionRecords.value = updatedRecords
                
                android.util.Log.d("ProfileViewModel", "✅ 情绪记录更新成功")
                _error.value = null
            } catch (e: Exception) {
                _error.value = "更新情绪记录失败: ${e.message}"
                android.util.Log.e("ProfileViewModel", "更新情绪记录失败", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 根据日期获取情绪记录
     */
    fun getEmotionRecordByDate(date: LocalDate): EmotionRecord? {
        return _emotionRecords.value.find { it.date == date }
    }
    
    /**
     * 获取最近的情绪记录
     */
    fun getRecentEmotionRecords(limit: Int = 7): List<EmotionRecord> {
        return _emotionRecords.value.take(limit)
    }
    
    /**
     * 🔧 安全的数据迁移方法 - 从内存状态迁移到数据库
     */
    fun migrateExistingEmotionRecords(existingRecords: List<EmotionRecord>) {
        viewModelScope.launch {
            try {
                if (existingRecords.isNotEmpty()) {
                    android.util.Log.d("ProfileViewModel", "开始迁移 ${existingRecords.size} 条现有情绪记录")
                    emotionRecordRepository.migrateExistingData(existingRecords)
                    
                    // 迁移完成后重新加载数据
                    loadEmotionRecords()
                }
            } catch (e: Exception) {
                android.util.Log.e("ProfileViewModel", "迁移情绪记录失败", e)
                _error.value = "数据迁移失败: ${e.message}"
            }
        }
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 添加新的情绪记录到内存状态（用于向后兼容）
     */
    fun addEmotionRecordToMemory(record: EmotionRecord) {
        val updatedRecords = _emotionRecords.value.toMutableList()
        // 移除同日期的旧记录
        updatedRecords.removeAll { it.date == record.date }
        // 添加新记录
        updatedRecords.add(record)
        // 按日期排序
        updatedRecords.sortByDescending { it.date }
        _emotionRecords.value = updatedRecords
        
        // 同时保存到数据库
        saveEmotionRecord(record)
    }
}
