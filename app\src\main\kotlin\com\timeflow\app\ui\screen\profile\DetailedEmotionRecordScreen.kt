package com.timeflow.app.ui.screen.profile

import android.app.Activity
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.runtime.derivedStateOf
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding

/**
 * 预设的情绪触发因素
 */
data class EmotionTrigger(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val color: Color
)

/**
 * 详细情绪记录页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class)
@Composable
fun DetailedEmotionRecordScreen(
    navController: NavController,
    selectedEmotion: EmotionType = EmotionType.JOY, // 默认值，实际应通过参数传递
    onRecordEmotion: (EmotionRecord) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 获取传递的日期参数，如果没有则使用今天
    val savedStateHandle = navController.previousBackStackEntry?.savedStateHandle
    val emotionParam = savedStateHandle?.get<EmotionType>("selectedEmotion") ?: selectedEmotion
    val dateParam = savedStateHandle?.get<LocalDate>("selectedDate")
    val recordDate = remember { dateParam ?: LocalDate.now() }
    val dateFormatter = remember { DateTimeFormatter.ofPattern("yyyy年M月d日") }
    
    // 状态
    val selectedTriggers = remember { mutableStateListOf<EmotionTrigger>() }
    var mindfulnessNote by remember { mutableStateOf("") }
    var customTrigger by remember { mutableStateOf("") }
    var imageUri by remember { mutableStateOf<Uri?>(null) }
    // 🔧 删除语音相关状态
    
    // 预设触发因素列表
    val predefinedTriggers = remember {
        listOf(
            EmotionTrigger("work", "工作", Icons.Outlined.Work, Color(0xFF42A5F5)),
            EmotionTrigger("relationships", "人际关系", Icons.Outlined.Group, Color(0xFFEC407A)),
            EmotionTrigger("family", "家庭", Icons.Outlined.Home, Color(0xFF66BB6A)),
            EmotionTrigger("health", "健康", Icons.Outlined.HealthAndSafety, Color(0xFFAB47BC)),
            EmotionTrigger("finances", "财务", Icons.Outlined.AccountBalance, Color(0xFFFFB74D)),
            EmotionTrigger("environment", "环境", Icons.Outlined.LocationCity, Color(0xFF26A69A)),
            EmotionTrigger("events", "事件", Icons.Outlined.Event, Color(0xFF7E57C2)),
            EmotionTrigger("self", "自我", Icons.Outlined.Person, Color(0xFF78909C))
        )
    }
    
    // 自定义触发因素列表
    val customTriggers = remember { mutableStateListOf<EmotionTrigger>() }
    
    // 键盘控制
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { imageUri = it }
    }
    
    // 使用系统状态栏管理
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
            }
        }
    }
    
    // 🔧 添加滚动状态和输入法适配
    val scrollState = rememberScrollState()

    // 🔧 使用更强的输入法适配方案
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF8F6F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
            .imePadding() // 🔧 使用imePadding确保内容不被输入法遮挡
            .navigationBarsPadding() // 🔧 添加导航栏适配
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { 
                Text(
                    "详细情绪记录",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF554A60)
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF554A60)
                    )
                }
            },
            actions = {
                // 保存按钮
                TextButton(
                    onClick = {
                        try {
                            // 创建新情绪记录
                            val newRecord = EmotionRecord(
                                date = recordDate, // 使用传入的日期或今天
                                emotion = emotionParam, // 使用传入的情绪类型
                                triggers = selectedTriggers.map { it.name } + customTriggers.map { it.name },
                                mindfulnessNote = mindfulnessNote,
                                imageUri = imageUri?.toString(),
                                audioUri = null, // 🔧 删除语音功能，设置为null
                                isDetailed = true
                            )
                            
                            // 回调通知记录创建
                            onRecordEmotion(newRecord)
                            
                            // 返回上一页
                            navController.navigateUp()
                        } catch (e: Exception) {
                            // 记录错误
                            android.util.Log.e("TimeFlow", "创建情绪记录失败", e)
                            
                            // 简单记录，确保至少记录情绪类型和日期
                            val simpleRecord = EmotionRecord(
                                date = recordDate,
                                emotion = emotionParam,
                                isDetailed = true
                            )
                            onRecordEmotion(simpleRecord)
                            
                            // 返回上一页
                            navController.navigateUp()
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color(0xFF5b7da9)
                    )
                ) {
                    Text("保存", fontSize = 16.sp, fontWeight = FontWeight.Medium)
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color(0xFFF8F6F8),
                titleContentColor = Color(0xFF554A60)
            )
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(scrollState) // 🔧 使用统一的滚动状态
        ) {
            // 日期和情绪显示
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 20.dp)
            ) {
                // 日期显示
                Text(
                    text = recordDate.format(dateFormatter),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF554A60)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 情绪显示
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .clip(RoundedCornerShape(20.dp))
                        .background(emotionParam.color.copy(alpha = 0.1f))
                        .padding(horizontal = 12.dp, vertical = 6.dp)
                ) {
                    Icon(
                        imageVector = emotionParam.icon,
                        contentDescription = emotionParam.title,
                        tint = emotionParam.color,
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = emotionParam.title,
                        fontSize = 14.sp,
                        color = emotionParam.color,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 触发因素标记系统
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 标题行 - 水平排列标题和提示文字
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 左侧标题
                        Text(
                            text = "触发因素",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF554A60)
                        )
                        
                        // 右侧提示文字
                        Text(
                            text = "选择触发因素（可多选）",
                            fontSize = 14.sp,
                            color = Color(0xFF666666)
                        )
                    }
                    
                    // 预设触发因素网格
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(180.dp)
                    ) {
                        items(predefinedTriggers) { trigger ->
                            // 检查是否已选中
                            val isSelected = selectedTriggers.any { it.id == trigger.id }
                            
                            // 触发因素项
                            TriggerItem(
                                trigger = trigger,
                                isSelected = isSelected,
                                onClick = {
                                    if (isSelected) {
                                        // 如果已选中，则移除
                                        selectedTriggers.removeAll { it.id == trigger.id }
                                    } else {
                                        // 如果未选中，则添加
                                        selectedTriggers.add(trigger)
                                    }
                                }
                            )
                        }
                    }
                    
                    // 自定义触发因素
                    if (customTriggers.isNotEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "自定义触发因素",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF666666)
                            )
                            
                            // 右侧可以添加说明文字或操作按钮
                            Text(
                                text = "点击可移除",
                                fontSize = 12.sp,
                                color = Color(0xFF999999)
                            )
                        }
                        
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            items(customTriggers) { trigger ->
                                AssistChip(
                                    onClick = { 
                                        // 移除自定义触发因素
                                        customTriggers.remove(trigger)
                                    },
                                    label = { Text(trigger.name) },
                                    colors = AssistChipDefaults.assistChipColors(
                                        containerColor = trigger.color.copy(alpha = 0.1f),
                                        labelColor = trigger.color
                                    ),
                                    border = null,
                                    trailingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = "移除",
                                            tint = trigger.color
                                        )
                                    }
                                )
                            }
                        }
                    }
                    
                    // 添加自定义触发因素
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp)
                    ) {
                        OutlinedTextField(
                            value = customTrigger,
                            onValueChange = { customTrigger = it },
                            label = { Text("添加自定义触发因素") },
                            singleLine = true,
                            modifier = Modifier.weight(1f),
                            placeholder = { Text("例如：工作压力、人际关系等", color = Color(0xFF999999)) }, // 🔧 添加占位符提示
                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    if (customTrigger.isNotEmpty()) {
                                        // 添加自定义触发因素
                                        val customColor = Color(0xFF8D6E63) // 棕色作为默认颜色
                                        val newTrigger = EmotionTrigger(
                                            id = "custom_${System.currentTimeMillis()}",
                                            name = customTrigger,
                                            icon = Icons.Outlined.Label,
                                            color = customColor
                                        )
                                        customTriggers.add(newTrigger)
                                        customTrigger = ""
                                        keyboardController?.hide()
                                    }
                                }
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFF9575CD),
                                unfocusedBorderColor = Color(0xFFDDDDDD)
                            )
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 添加按钮
                        IconButton(
                            onClick = {
                                if (customTrigger.isNotEmpty()) {
                                    // 添加自定义触发因素
                                    val customColor = Color(0xFF8D6E63) // 棕色作为默认颜色
                                    val newTrigger = EmotionTrigger(
                                        id = "custom_${System.currentTimeMillis()}",
                                        name = customTrigger,
                                        icon = Icons.Outlined.Label,
                                        color = customColor
                                    )
                                    customTriggers.add(newTrigger)
                                    customTrigger = ""
                                    keyboardController?.hide()
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "添加",
                                tint = Color(0xFF9575CD)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 正念记录引导
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 标题行 - 水平排列
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 左侧标题
                        Text(
                            text = "正念记录",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF554A60)
                        )
                        
                        // 右侧提示
                        Text(
                            text = "记录情绪体验",
                            fontSize = 14.sp,
                            color = Color(0xFF666666)
                        )
                    }
                    
                    // 引导性问题
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFF1F3F9)
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp)
                        ) {
                            // 正念引导提示
                            Text(
                                text = "正念反思提示",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF5b7da9),
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                            
                            // 提示性问题
                            Text(
                                text = "• 当你开始注意到这种情绪时，你在做什么？\n• 身体有什么感受？\n• 当时你的想法是什么？\n• 你如何回应这种情绪？\n• 这种情绪如何影响了你的日常活动？",
                                fontSize = 13.sp,
                                color = Color(0xFF666666),
                                lineHeight = 20.sp
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 🔧 正念记录文本区域 - 确保在输入法上方可见
                    var isFocused by remember { mutableStateOf(false) }

                    // 🔧 监听焦点状态变化，自动滚动确保输入框可见
                    LaunchedEffect(isFocused) {
                        if (isFocused) {
                            // 当输入框获得焦点时，分阶段滚动确保输入框完全可见
                            kotlinx.coroutines.delay(100) // 短暂延迟等待焦点稳定
                            scrollState.animateScrollTo(scrollState.maxValue)

                            kotlinx.coroutines.delay(400) // 等待输入法完全弹出
                            scrollState.animateScrollTo(scrollState.maxValue) // 再次滚动确保完全可见
                        }
                    }

                    OutlinedTextField(
                        value = mindfulnessNote,
                        onValueChange = { mindfulnessNote = it },
                        label = { Text("记录你的感受和想法...") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(min = 150.dp, max = 200.dp) // 🔧 使用heightIn提供更灵活的高度
                            .onFocusChanged { focusState ->
                                isFocused = focusState.isFocused
                            },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF9575CD),
                            unfocusedBorderColor = Color(0xFFDDDDDD)
                        ),
                        maxLines = 6, // 🔧 限制最大行数，提高输入体验
                        placeholder = {
                            Text(
                                "在这里记录你的感受、想法或任何想要记住的内容...",
                                color = Color(0xFF999999)
                            )
                        },
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Default
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                focusManager.clearFocus()
                                keyboardController?.hide()
                            }
                        )
                    )

                    // 🔧 在输入框后添加额外间距，确保输入时有足够空间
                    Spacer(modifier = Modifier.height(if (isFocused) 200.dp else 16.dp))
                    
                    // 🔧 添加图片功能（删除语音功能后简化布局）
                    OutlinedButton(
                        onClick = {
                            imagePickerLauncher.launch("image/*")
                        },
                        shape = RoundedCornerShape(24.dp),
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF9575CD)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Image,
                            contentDescription = "添加图片",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = if (imageUri != null) "更换图片" else "添加图片",
                            fontSize = 14.sp
                        )
                    }
                    
                    // 显示已添加的图片预览
                    if (imageUri != null) {
                        Box(
                            modifier = Modifier
                                .padding(top = 16.dp)
                                .fillMaxWidth()
                                .height(150.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(Color(0xFFEEEEEE)),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "图片已添加",
                                fontSize = 14.sp,
                                color = Color(0xFF666666)
                            )
                        }
                    }
                    
                    // 🔧 删除语音预览显示
                }
            }
            
            // 🔧 增加底部间距，确保内容不被输入法遮挡
            Spacer(modifier = Modifier.height(120.dp))
        } // 内容区域Column结束
    } // 外层Column结束
} // DetailedEmotionRecordScreen函数结束

/**
 * 触发因素项
 */
@Composable
fun TriggerItem(
    trigger: EmotionTrigger,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(onClick = onClick)
    ) {
        // 触发因素图标
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(50.dp)
                .clip(CircleShape)
                .background(
                    if (isSelected) trigger.color.copy(alpha = 0.2f)
                    else Color(0xFFF5F5F5)
                )
                .border(
                    width = if (isSelected) 2.dp else 0.dp,
                    color = if (isSelected) trigger.color else Color.Transparent,
                    shape = CircleShape
                )
                .padding(10.dp)
        ) {
            Icon(
                imageVector = trigger.icon,
                contentDescription = trigger.name,
                tint = if (isSelected) trigger.color else Color.Gray,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 触发因素名称
        Text(
            text = trigger.name,
            fontSize = 12.sp,
            color = if (isSelected) trigger.color else Color(0xFF666666),
            modifier = Modifier.padding(top = 4.dp),
            textAlign = TextAlign.Center
        )
    }
} 