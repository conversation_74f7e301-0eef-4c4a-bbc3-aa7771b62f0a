package com.timeflow.app.ui.screen.calendar

import android.app.Activity
import android.os.Build
import android.text.format.DateFormat
import android.util.Log
import android.widget.Toast
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures

import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.geometry.Rect as ComposeRect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons as MaterialIcons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.snapshots.SnapshotStateMap
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.ui.input.pointer.consumeDownChange
import androidx.compose.ui.input.pointer.consumePositionChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.screen.calendar.DimensionUtils
import com.timeflow.app.ui.screen.calendar.components.ModernDayView
import com.timeflow.app.ui.screen.calendar.components.ModernCalendarEvent
import com.timeflow.app.ui.screen.health.HabitData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.theme.AppTheme
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.enableHardwareAcceleration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.*
import kotlin.math.abs
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import com.timeflow.app.ui.screen.calendar.components.FloatingTasksSection
import com.timeflow.app.ui.screen.calendar.components.EnhancedFloatingTasksSection
import com.timeflow.app.ui.screen.calendar.components.EnhancedFloatingTaskItem
import com.timeflow.app.ui.screen.calendar.components.EnhancedTodayTasksSection
import com.timeflow.app.ui.screen.calendar.components.EnhancedDailyHabitsSection
import com.timeflow.app.ui.screen.calendar.components.WeekDateDropTarget
import com.timeflow.app.ui.screen.calendar.components.DragTargetArea
import com.timeflow.app.ui.screen.calendar.components.CreateFloatingTaskDialog
import com.timeflow.app.data.model.Task
import androidx.compose.ui.zIndex
import androidx.compose.ui.unit.IntOffset
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.theme.DustyLavender
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.style.TextDecoration
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.task.components.common.event.AppEvent

// 日历专用配色方案，使用Material3的darkColorScheme
val CalendarColors = darkColorScheme(
    primary = Color(0xFF554A60),
    onPrimary = Color(0xFF554A60),
    primaryContainer = Color(0xFFFFFFFF),
    onPrimaryContainer = Color(0xFF554A60),
    inversePrimary = Color(0xFFB1A5BD),
    secondary = Color(0xFFB1A5BD),
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFFFFFFF),
    onSecondaryContainer = Color(0xFF554A60),
    tertiary = Color(0xFF554A60),
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFFFFFFF),
    onTertiaryContainer = Color(0xFF554A60),
    background = Color(0xFFFFFFFF),
    onBackground = Color(0xFF554A60),
    surface = Color(0xFFFFFFFF),
    onSurface = Color(0xFF554A60).copy(alpha = 0.6f),
    surfaceVariant = Color(0xFFFFFFFF),
    onSurfaceVariant = Color(0xFF554A60).copy(alpha = 0.7f),
    surfaceTint = Color(0xFFFFFFFF),
    inverseSurface = Color(0xFF554A60),
    inverseOnSurface = Color(0xFFFFFFFF),
    error = Color(0xFFB00020),
    onError = Color.White,
    errorContainer = Color(0xFFFDE7E9),
    onErrorContainer = Color(0xFFB00020),
    outline = Color(0xFF554A60).copy(alpha = 0.2f),
    outlineVariant = Color(0xFF554A60).copy(alpha = 0.1f),
    scrim = Color(0xFF000000).copy(alpha = 0.3f)
)

// 更新任务颜色，确保所有颜色都有80%透明度
val TaskColors = listOf(
    Color(0xCCFFF0C8), // 黄色 (80%不透明度，CC=204/255≈0.8)
    Color(0xCCE8E4F3), // 淡紫色 (80%不透明度)
    Color(0xCCFFD6DB), // 粉红色 (80%不透明度)
    Color(0xCCD8F3E7)  // 薄荷绿 (80%不透明度)
)

/**
 * 日历视图类型
 */
enum class CalendarViewType {
    MONTH,      // 月视图
    WEEK,       // 周视图（简洁版）
    WEEK_DETAIL,// 周视图（详细版）
    DAY         // 日视图
}

// 优化 NoRippleInteractionSource 实现
private object NoRippleInteractionSource : MutableInteractionSource {
    override val interactions: Flow<Interaction> = emptyFlow()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction): Boolean = false
}

// 提供一个函数，返回记住的无涟漪交互源
@Composable
private fun rememberNoRippleInteractionSource(): MutableInteractionSource {
    return remember { NoRippleInteractionSource }
}

/**
 * 日历屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalendarScreen(
    navController: NavController,
    viewModel: CalendarViewModel = hiltViewModel(),
    habitViewModel: HabitViewModel = hiltViewModel()
) {
    // 获取状态栏高度
    val statusBarHeight = rememberStatusBarHeight()
    
    // 提供状态栏高度的CompositionLocal
    CompositionLocalProvider(
        LocalStatusBarHeight provides statusBarHeight
    ) {
        // 当前日历视图类型
        var calendarViewType by remember { mutableStateOf(CalendarViewType.DAY) }
    
        // 当前选中日期
        // 🔧 修复：使用CalendarViewModel的selectedDate状态，而不是局部状态
    val selectedDate by viewModel.selectedDate.collectAsState()

        // 当前年月
        val currentYearMonth by viewModel.currentYearMonth.collectAsState()
        
        // 当前周
        var currentWeekStart by remember { 
            mutableStateOf(
                selectedDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
            )
        }
    var currentWeek by remember { 
        mutableStateOf(
                Pair(currentWeekStart, currentWeekStart.plusDays(6))
            )
    }
    
    // 滚动到当前时间的状态
    var scrollToCurrentTime by remember { mutableStateOf(false) }
    
        // 创建事件对话框状态
        var showCreateEventDialog by remember { mutableStateOf(false) }
        var newEventStartTime by remember { mutableStateOf<LocalDateTime?>(null) }
        var newEventEndTime by remember { mutableStateOf<LocalDateTime?>(null) }

    // 🔧 新增：时间追踪事件状态
    var timeTrackingEvents by remember { mutableStateOf<List<com.timeflow.app.ui.screen.calendar.components.TimeTrackingEvent>>(emptyList()) }

        // 处理日期选择
        val handleDateSelection: (LocalDate) -> Unit = { date ->
            viewModel.selectDate(date)
            
            // 根据视图类型，更新当前年月或周
            when (calendarViewType) {
                CalendarViewType.MONTH -> {
                    if (date.month != currentYearMonth.month) {
                        viewModel.selectYearMonth(YearMonth.from(date))
                    }
                }
                CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                    // 检查是否在当前周内
                    if (date.isBefore(currentWeek.first) || date.isAfter(currentWeek.second)) {
                        val newWeekStart = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                        currentWeekStart = newWeekStart
                        currentWeek = Pair(newWeekStart, newWeekStart.plusDays(6))
                    }
                }
                else -> { /* 其他视图类型处理 */ }
            }
        }
        
        // 获取应用背景颜色
        val calendarBackgroundColor = Color(0xFFFAFAFA)  // 浅灰色背景
        
        // 提供 CalendarViewTypeContext
        CompositionLocalProvider(
            LocalCalendarViewType provides CalendarViewTypeContext(
                calendarViewType = calendarViewType,
                onCalendarViewTypeChange = { newType -> calendarViewType = newType }
            )
        ) {
    // 🔧 新增：监听选中日期变化，获取时间追踪事件
    LaunchedEffect(selectedDate) {
        try {
            timeTrackingEvents = viewModel.getTimeTrackingEventsForDate(selectedDate)
            Log.d("CalendarScreen", "获取到 ${timeTrackingEvents.size} 个时间追踪事件")
        } catch (e: Exception) {
            Log.e("CalendarScreen", "获取时间追踪事件失败", e)
            timeTrackingEvents = emptyList()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(calendarBackgroundColor)
    ) {
        Scaffold(
            containerColor = calendarBackgroundColor,
            topBar = {
                CalendarTopBar(
                    statusBarHeight = statusBarHeight,
                    calendarViewType = calendarViewType,
                    onCalendarViewTypeChange = { newType -> calendarViewType = newType },
                    selectedDate = selectedDate,
                    currentYearMonth = currentYearMonth,
                    currentWeek = currentWeek,
                    onDateSelected = handleDateSelection,
                    onYearMonthChanged = { newYM -> 
                        viewModel.selectYearMonth(newYM)
                    },
                    onWeekStartChanged = { newStart ->
                        val weekEnd = newStart.plusDays(6)
                        currentWeek = Pair(newStart, weekEnd)
                        currentWeekStart = newStart
                    },
                    onScrollToCurrentTime = { 
                        // 🎯 聚焦到当日 - 设置选中日期为今天
                        val today = LocalDate.now()
                        viewModel.selectDate(today)
                        
                        // 根据当前视图类型调整显示
                        when (calendarViewType) {
                            CalendarViewType.MONTH -> {
                                // 月视图：切换到今天所在的月份
                                val todayYearMonth = YearMonth.from(today)
                                viewModel.selectYearMonth(todayYearMonth)
                            }
                            CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                                // 周视图：切换到今天所在的周
                                val todayWeekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                                currentWeekStart = todayWeekStart
                                currentWeek = Pair(todayWeekStart, todayWeekStart.plusDays(6))
                            }
                            CalendarViewType.DAY -> {
                                // 日视图：已经设置了selectedDate，会自动显示今天
                                scrollToCurrentTime = true // 同时滚动到当前时间
                            }
                        }
                        
                        Log.d("CalendarScreen", "聚焦到当日: $today, 当前视图: $calendarViewType")
                    },
                    backgroundColor = calendarBackgroundColor // 传递背景颜色
                )
            }
        ) { innerPadding ->
            // 使用 key 优化重组控制
            key(calendarViewType) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding)
                ) {
                    when (calendarViewType) {
                        CalendarViewType.MONTH -> {
                            // 使用 LazyVerticalGrid 优化月视图性能
                            MonthCalendarView(
                                yearMonth = currentYearMonth,
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                backgroundColor = calendarBackgroundColor,
                                viewModel = viewModel,
                                habitViewModel = habitViewModel
                            )
                        }
                        CalendarViewType.WEEK -> {
                            WeekCalendarView(
                                weekRange = Pair(currentWeekStart, currentWeekStart.plusDays(6)),
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                viewModel = viewModel,
                                habitViewModel = habitViewModel,
                                backgroundColor = calendarBackgroundColor
                            )
                        }
                        CalendarViewType.WEEK_DETAIL -> {
                            // TODO: DetailedWeekCalendarView未实现，暂时使用普通WeekCalendarView替代
                            WeekCalendarView(
                                weekRange = Pair(currentWeekStart, currentWeekStart.plusDays(6)),
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                viewModel = viewModel,
                                backgroundColor = calendarBackgroundColor
                            )
                            // 原代码暂时注释掉，等实现完成后再启用
                            /*
                            DetailedWeekCalendarView(
                                startDate = currentWeekStart,
                                selectedDate = selectedDate,
                                onDateSelected = handleDateSelection,
                                events = viewModel.calendarState.collectAsState().value.events,
                                modifier = Modifier.fillMaxSize()
                            )
                            */
                        }
                        CalendarViewType.DAY -> {
                            // 🔧 修复日视图不显示任务的问题
                            LaunchedEffect(Unit) {
                                Log.d("DayView", "=== 日视图初始化，触发数据加载 ===")
                                viewModel.loadEvents()
                            }
                            
                            // 🔧 添加事件监听，确保任务添加后实时同步
                            LaunchedEffect(Unit) {
                                EventBus.events.collect { event ->
                                    when (event) {
                                        is AppEvent.TaskCreated,
                                        is AppEvent.TaskUpdated,
                                        is AppEvent.TaskDeleted,
                                        is AppEvent.TaskCompletionChanged,
                                        is AppEvent.DatabaseRefreshed -> {
                                            Log.d("DayView", "收到任务变更事件: ${event.javaClass.simpleName}，重新加载日视图数据")
                                            // 短暂延迟确保数据库操作完成
                                            delay(150)
                                            viewModel.loadEvents()
                                        }
                                    }
                                }
                            }
                            
                            // 获取当前日期的事件并转换为ModernCalendarEvent格式
                            val calendarState by viewModel.calendarState.collectAsState()
                            val dayEvents = remember(selectedDate, calendarState.events) {
                                Log.d("DayView", "日视图事件更新: 选中日期=$selectedDate, 总事件=${calendarState.events.size}个")
                                calendarState.events.filter { event ->
                                    val eventDate = event.start.toLocalDate()
                                    val isFloatingTask = event.id.startsWith("floating_") || 
                                                       event.title.contains("[浮动]") ||
                                                       event.title.contains("本周完成")
                                    
                                    val shouldInclude = eventDate == selectedDate && !isFloatingTask
                                    Log.d("DayView", "事件过滤: ${event.title}, 日期匹配=${eventDate == selectedDate}, 非浮动=${!isFloatingTask}, 包含=$shouldInclude")
                                    shouldInclude
                                }.map { event ->
                                    com.timeflow.app.ui.screen.calendar.components.ModernCalendarEvent(
                                        id = event.id,
                                        title = event.title,
                                        description = "",
                                        start = event.start,
                                        end = event.end ?: event.start.plusMinutes(15),
                                        color = event.color,
                                        urgency = event.urgency,
                                        taskId = event.taskId,
                                        isAllDay = event.isAllDay
                                    )
                                }
                            }
                            
                            Log.d("DayView", "日视图最终显示事件数: ${dayEvents.size}")
                            
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = 8.dp)
                            ) {
                                ModernDayView(
                                    selectedDate = selectedDate,
                                    events = dayEvents,
                                    timeTrackingEvents = timeTrackingEvents, // 🔧 新增：传递时间追踪事件
                                    viewModel = viewModel,
                                    scrollToCurrentTime = scrollToCurrentTime,
                                    onAddEvent = { date, time ->
                                        // 🔧 修复：设置新事件的开始时间，默认1小时持续时间
                                        Log.d("时间调试", "=== onAddEvent 被调用 ===")
                                        Log.d("时间调试", "点击日期: $date")
                                        Log.d("时间调试", "点击时间: $time")
                                        newEventStartTime = date.atTime(time)
                                        newEventEndTime = date.atTime(time.plusMinutes(60)) // 改为1小时持续时间
                                        Log.d("时间调试", "设置开始时间: $newEventStartTime")
                                        Log.d("时间调试", "设置结束时间: $newEventEndTime")
                                        showCreateEventDialog = true
                                    },
                                    // 🔧 新增：任务完成状态切换回调
                                    onCompleteTask = { taskId, isCompleted ->
                                        Log.d("CalendarScreen", "切换任务完成状态: taskId=$taskId, isCompleted=$isCompleted")
                                        viewModel.toggleTaskCompletion(taskId, isCompleted)
                                    },
                                    // 🔧 新增：时间追踪事件点击回调
                                    onTimeTrackingEventClick = { event ->
                                        Log.d("CalendarScreen", "点击时间追踪事件: ${event.taskName}, 用时: ${event.formattedDuration}")
                                        // 这里可以添加额外的处理逻辑，比如导航到详情页面
                                    }
                                )
                            }
                        }
                    }
                    
                    // 创建事件对话框
                    if (showCreateEventDialog && newEventStartTime != null) {
                        CreateEventDialog(
                            startTime = newEventStartTime!!,
                            endTime = newEventEndTime!!,
                            onDismiss = { showCreateEventDialog = false },
                            onCreateEvent = { title, description, startTime, endTime ->
                                viewModel.createEvent(
                                    title = title,
                                    start = startTime,
                                    end = endTime,
                                    color = null // 暂时不设置颜色
                                )
                                showCreateEventDialog = false
                            }
                        )
                    }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 日历顶部栏，包含导航和操作按钮
 */
@Composable
fun CalendarTopBar(
    statusBarHeight: Dp,
    calendarViewType: CalendarViewType,
    onCalendarViewTypeChange: (CalendarViewType) -> Unit,
    selectedDate: LocalDate,
    currentYearMonth: YearMonth,
    currentWeek: Pair<LocalDate, LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onYearMonthChanged: (YearMonth) -> Unit,
    onWeekStartChanged: (LocalDate) -> Unit,
    onScrollToCurrentTime: () -> Unit,
    backgroundColor: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = statusBarHeight)
            .background(backgroundColor)
            .padding(vertical = 2.dp) // 进一步减少到2.dp，最小化空白区域
    ) {
        // 日历操作按钮
        CalendarActions(
            calendarViewType = calendarViewType,
            onCalendarViewTypeChange = onCalendarViewTypeChange,
            onScrollToCurrentTime = onScrollToCurrentTime,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 2.dp) // 减少水平间距和垂直间距
        )
        
        // 日历导航
        CalendarNavigation(
            calendarViewType = calendarViewType,
            selectedDate = selectedDate,
            currentYearMonth = currentYearMonth,
            currentWeek = currentWeek,
            onYearMonthChanged = onYearMonthChanged,
            onWeekStartChanged = onWeekStartChanged,
            onDateSelected = onDateSelected,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 2.dp) // 减少水平间距和垂直间距
        )
    }
}

/**
 * 日历操作按钮
 */
@Composable
fun CalendarActions(
    calendarViewType: CalendarViewType,
    onCalendarViewTypeChange: (CalendarViewType) -> Unit,
    onScrollToCurrentTime: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 🎯 左侧1/3：空白区域
        Spacer(modifier = Modifier.weight(1f))
        
        // 🎯 右侧2/3：三个按钮，均匀分布
        Row(
            modifier = Modifier.weight(2f),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 日视图按钮 - 现在位于中间位置
            Box(
                modifier = Modifier
                    .size(32.dp) // 从40dp减小到32dp
                    .clip(RoundedCornerShape(6.dp)) // 圆角也相应减小
                    .background(
                        if (calendarViewType == CalendarViewType.DAY)
                        Color(0xFFbecabb) else Color(0xFFF5F5F5)
                    )
                    .clickable(
                        interactionSource = rememberNoRippleInteractionSource(),
                        indication = null
                    ) {
                        onCalendarViewTypeChange(CalendarViewType.DAY)
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = MaterialIcons.Default.CalendarViewDay,
                    contentDescription = "日视图",
                    tint = if (calendarViewType == CalendarViewType.DAY)
                        Color.White else Color(0xFF757575),
                    modifier = Modifier.size(18.dp) // 从24dp减小到18dp
                )
            }
            
            // 周/月视图切换按钮 - 保持在中间位置
            Box(
                modifier = Modifier
                    .size(32.dp) // 从40dp减小到32dp
                    .clip(RoundedCornerShape(6.dp)) // 圆角也相应减小
                    .background(
                        if (calendarViewType == CalendarViewType.WEEK || calendarViewType == CalendarViewType.WEEK_DETAIL) 
                        Color(0xFFbecabb) else Color(0xFFF5F5F5)
                    )
                    .clickable(
                        interactionSource = rememberNoRippleInteractionSource(),
                        indication = null
                    ) {
                        onCalendarViewTypeChange(
                            if (calendarViewType == CalendarViewType.MONTH) CalendarViewType.WEEK 
                            else CalendarViewType.MONTH
                        )
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = if (calendarViewType == CalendarViewType.WEEK || calendarViewType == CalendarViewType.WEEK_DETAIL) 
                        MaterialIcons.Default.CalendarViewWeek else MaterialIcons.Default.CalendarViewMonth,
                    contentDescription = "周/月视图",
                    tint = if (calendarViewType == CalendarViewType.WEEK || calendarViewType == CalendarViewType.WEEK_DETAIL) 
                        Color.White else Color(0xFF757575),
                    modifier = Modifier.size(18.dp) // 从24dp减小到18dp
                )
            }
            
            // Today button - 保持在最右侧
            Box(
                modifier = Modifier
                    .size(32.dp) // 从40dp减小到32dp
                    .clip(RoundedCornerShape(6.dp)) // 圆角也相应减小
                    .background(Color(0xFFb2a9b1)) // 使用灰紫色背景突出这个重要功能
                    .clickable(
                        interactionSource = rememberNoRippleInteractionSource(),
                        indication = null
                    ) {
                        onScrollToCurrentTime()
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = MaterialIcons.Default.Today,
                    contentDescription = "回到今天",
                    tint = Color.White, // 白色图标在灰紫色背景上更醒目
                    modifier = Modifier.size(18.dp) // 从24dp减小到18dp
                )
            }
        }
    }
}

// Add CalendarNavigation composable
@Composable
private fun CalendarNavigation(
    calendarViewType: CalendarViewType,
    selectedDate: LocalDate,
    currentYearMonth: YearMonth,
    currentWeek: Pair<LocalDate, LocalDate>,
    onYearMonthChanged: (YearMonth) -> Unit,
    onWeekStartChanged: (LocalDate) -> Unit,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(CalendarColors.background) // 应用背景颜色
            .padding(horizontal = 16.dp, vertical = 4.dp), // 进一步减少padding
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Previous button
        IconButton(
            onClick = {
                when (calendarViewType) {
                    CalendarViewType.MONTH -> onYearMonthChanged(currentYearMonth.minusMonths(1))
                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                        val newStartDate = currentWeek.first.minusWeeks(1)
                        onWeekStartChanged(newStartDate)
                        // 选择周视图中的第一天来确保事件正确显示
                        onDateSelected(newStartDate)
                    }
                    else -> onDateSelected(selectedDate.minusDays(1))
                }
            },
            modifier = Modifier
                .size(28.dp) // 从32dp减小到28dp
                .clip(CircleShape)
                .background(Color(0xFFFBFCFC))
        ) {
            Icon(
                imageVector = MaterialIcons.Default.ArrowBack,
                contentDescription = "上一个",
                tint = Color(0xFF554A60),
                modifier = Modifier.size(16.dp) // 从18dp减小到16dp
            )
        }

        // Date range text - 进一步缩小字体和间距
        Text(
            text = when(calendarViewType) {
                CalendarViewType.MONTH -> currentYearMonth.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
                CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL ->
                    "${currentWeek.first.format(DateTimeFormatter.ofPattern("MM月dd日"))} - ${currentWeek.second.format(DateTimeFormatter.ofPattern("MM月dd日"))}"
                CalendarViewType.DAY -> selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
            },
            fontSize = 13.sp, // 从14.sp进一步缩小到13.sp
            fontWeight = FontWeight.Medium,
            color = Color(0xFF3C3C3C),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 4.dp) // 从6.dp减少到4.dp
        )

        // Next button
        IconButton(
            onClick = {
                when (calendarViewType) {
                    CalendarViewType.MONTH -> onYearMonthChanged(currentYearMonth.plusMonths(1))
                    CalendarViewType.WEEK, CalendarViewType.WEEK_DETAIL -> {
                        val newStartDate = currentWeek.first.plusWeeks(1)
                        onWeekStartChanged(newStartDate)
                        // 选择周视图中的第一天来确保事件正确显示
                        onDateSelected(newStartDate)
                    }
                    else -> onDateSelected(selectedDate.plusDays(1))
                }
            },
            modifier = Modifier
                .size(28.dp) // 从32dp减小到28dp
                .clip(CircleShape)
                .background(Color(0xFFFBFCFC))
        ) {
            Icon(
                imageVector = MaterialIcons.Default.ArrowForward,
                contentDescription = "下一个",
                tint = Color(0xFF554A60),
                modifier = Modifier.size(16.dp) // 从18dp减小到16dp
            )
        }
    }
}

// 辅助函数：获取周范围
private fun getWeekRange(date: LocalDate): Pair<LocalDate, LocalDate> {
    // 获取一周的开始（周一）和结束（周日）
    var start = date
    while (start.dayOfWeek != DayOfWeek.MONDAY) {
        start = start.minusDays(1)
    }
    
    var end = date
    while (end.dayOfWeek != DayOfWeek.SUNDAY) {
        end = end.plusDays(1)
    }
    
    return Pair(start, end)
}

/**
 * 高级美化月视图日历 - 参考Apple Calendar & Google Calendar设计
 * 设计特点：极简主义、现代质感、无阴影、优雅间距
 */
@Composable
fun MonthCalendarView(
    yearMonth: YearMonth,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    backgroundColor: Color = Color(0xFFFBFBFB), // 更温暖的背景色
    viewModel: CalendarViewModel = hiltViewModel(),
    habitViewModel: HabitViewModel = hiltViewModel()
) {
    val daysInMonth = yearMonth.lengthOfMonth()
    val firstDayOfMonth = yearMonth.atDay(1)
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7
    
    // 构建显示的日期列表
    val datesList = buildList {
        // 前一个月的填充日期
        for (i in 0 until firstDayOfWeek) {
            add(firstDayOfMonth.minusDays((firstDayOfWeek - i).toLong()))
        }
        
        // 当前月的日期
        for (i in 1..daysInMonth) {
            add(yearMonth.atDay(i))
        }
        
        // 计算需要显示的行数（通常是6行）
        val totalCells = 42 // 6行 x 7列
        
        // 后一个月的填充日期
        val daysFromNextMonth = totalCells - (firstDayOfWeek + daysInMonth)
        val lastDayOfMonth = yearMonth.atDay(daysInMonth)
        for (i in 1..daysFromNextMonth) {
            add(lastDayOfMonth.plusDays(i.toLong()))
        }
    }
    
    // 🔧 修复月视图不显示任务的问题
    LaunchedEffect(Unit) {
        Log.d("MonthCalendarView", "=== 月视图初始化，触发数据加载 ===")
        viewModel.loadEvents()
    }
    
    // 🔧 监听日历状态变化，确保数据加载完成后重新组合
    val calendarState by viewModel.calendarState.collectAsState()
    
    // 🔧 当前选择的日期对应的事件 - 使用响应式状态
    val eventsForSelectedDate = remember(selectedDate, calendarState.events) {
        Log.d("MonthCalendarView", "月视图事件更新: 选中日期=$selectedDate, 总事件=${calendarState.events.size}个")
        calendarState.events.filter { event ->
            val eventDate = event.start.toLocalDate()
            val isFloatingTask = event.id.startsWith("floating_") || 
                               event.title.contains("[浮动]") ||
                               event.title.contains("本周完成")
            
            val shouldInclude = eventDate == selectedDate && !isFloatingTask
            Log.d("MonthCalendarView", "事件过滤: ${event.title}, 日期匹配=${eventDate == selectedDate}, 非浮动=${!isFloatingTask}, 包含=$shouldInclude")
            shouldInclude
        }
    }
    
    // 显示日志
    LaunchedEffect(eventsForSelectedDate) {
        Log.d("MonthCalendarView", "选择日期为 $selectedDate，总共有 ${eventsForSelectedDate.size} 个事件")
    }
    
    // 使用BoxWithConstraints解决高度约束问题
    BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
        val maxHeight = this.maxHeight
        val scrollState = rememberScrollState()
        var isDragging by remember { mutableStateOf(false) }
        var calendarVisible by remember { mutableStateOf(true) }
        var dragStartY by remember { mutableStateOf(0f) }
        val coroutineScope = rememberCoroutineScope()
        
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(maxHeight)
                .background(backgroundColor)
                .verticalScroll(
                    scrollState,
                    enabled = true // 启用垂直滚动
                )
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            dragStartY = offset.y
                            isDragging = true
                        },
                        onDragEnd = {
                            isDragging = false
                        },
                        onDragCancel = {
                            isDragging = false
                        },
                        onDrag = { change, dragAmount ->
                            change.consume()
        
                            // 限制性滑动：只允许少量上下滑动
                            if (abs(dragAmount.y) > 5) { // 降低触发阈值
                                if (dragAmount.y < 0 && calendarVisible && scrollState.value < 100) { // 向上滑动，限制最大滚动距离
                                    calendarVisible = false
                                } else if (dragAmount.y > 0 && !calendarVisible) { // 向下滑动
                                    calendarVisible = true
                                }
                            }
                            
                            // 限制滚动范围，支持有限的上下滑动
                            coroutineScope.launch {
                                val maxScrollDistance = 200 // 限制最大滚动距离为200dp
                                val newScrollPosition = (scrollState.value - dragAmount.y.toInt()).coerceIn(0, maxScrollDistance)
                                scrollState.scrollTo(newScrollPosition)
                            }
                        }
                    )
                }
        ) {
            // 高级美化日历部分
            AnimatedVisibility(
                visible = calendarVisible,
                enter = fadeIn(animationSpec = tween(300)) + expandVertically(animationSpec = tween(300)),
                exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(animationSpec = tween(200))
            ) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 4.dp, vertical = 8.dp), // 从16dp减少到4dp
                    color = Color.White,
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp, vertical = 20.dp) // 水平内边距从20dp减少到12dp
                    ) {
                        // 🎨 高级周标题设计 - 参考Apple Calendar
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            val daysOfWeek = listOf("日", "一", "二", "三", "四", "五", "六")
                            daysOfWeek.forEachIndexed { index, dayOfWeek ->
                                Box(
                                    modifier = Modifier.weight(1f),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = dayOfWeek,
                                        fontSize = 11.sp, // 从13.sp减少到11.sp
                                        fontWeight = FontWeight.Medium,
                                        color = when {
                                            dayOfWeek == "日" -> Color(0xFFE53E3E) // 温和的红色
                                            dayOfWeek == "六" -> Color(0xFF3182CE) // 优雅的蓝色
                                            else -> Color(0xFF718096) // 中性灰色
                                        },
                                        letterSpacing = 0.3.sp // 从0.5.sp减少到0.3.sp
                                    )
                                }
                            }
                        }
                        
                        // 🎨 高级日历网格 - 无边框，充分利用空间
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(7),
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 380.dp), // 略微增加高度
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            items(datesList) { date ->
                                // 🔧 从状态中获取事件，确保响应式更新
                                val dateEvents = remember(date, calendarState.events) {
                                    calendarState.events.filter { event ->
                                        val eventDate = event.start.toLocalDate()
                                        val isFloatingTask = event.id.startsWith("floating_") || 
                                                           event.title.contains("[浮动]") ||
                                                           event.title.contains("本周完成")
                                        eventDate == date && !isFloatingTask
                                    }
                                }
                                val isCurrentMonth = date.month == yearMonth.month
                                val isSelected = date == selectedDate
                                val isToday = date == LocalDate.now()
                                
                                val habits = remember(date) { 
                                    mutableStateOf(habitViewModel.getHabitsForDate(date))
                                }
                                
                                EnhancedMonthCalendarDay(
                                    date = date,
                                    isCurrentMonth = isCurrentMonth,
                                    isSelected = isSelected,
                                    isToday = isToday,
                                    events = dateEvents,
                                    habits = habits.value,
                                    onDateClick = {
                                        onDateSelected(it)
                                        Log.d("MonthCalendarView", "点击日期: $it, 当前选中: $selectedDate")
                                    }
                                )
                            }
                        }
                    }
                }
            }
            
            // 日期详情面板 - 显示所选日期的事件和习惯
            DateDetailsPanel(
                selectedDate = selectedDate,
                events = eventsForSelectedDate, // 使用已加载的事件
                habits = remember(selectedDate) { 
                    habitViewModel.getHabitsForDate(selectedDate) 
                },
                viewModel = viewModel,
                habitViewModel = habitViewModel,
                modifier = Modifier
                    .fillMaxWidth()
                    .animateContentSize()
            )
            
            // 底部空白空间 - 支持有限滑动
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp) // 增加120dp的底部空白空间
                    .background(backgroundColor)
            )
        }
    }
}

/**
 * 🎨 高级美化月视图日期单元格 - 参考Apple Calendar & Notion Calendar设计
 * 设计特点：
 * - 极简主义：去除不必要的装饰
 * - 现代质感：微妙的颜色过渡和精致的排版
 * - 优雅交互：平滑的动画和视觉反馈
 * - 信息层次：清晰的视觉层级和内容组织
 */
@Composable
fun EnhancedMonthCalendarDay(
    date: LocalDate,
    isCurrentMonth: Boolean,
    isSelected: Boolean,
    isToday: Boolean,
    events: List<CalendarEvent>,
    habits: List<HabitData>,
    onDateClick: (LocalDate) -> Unit
) {
    // 🎨 动态计算颜色系统
    val containerColor = remember(isSelected, isToday, isCurrentMonth) {
        when {
                                isSelected -> Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
            isToday -> Color(0xFFF7FAFC) // 极淡的背景色
            else -> Color.Transparent
        }
    }
    
    val textColor = remember(isSelected, isToday, isCurrentMonth) {
        when {
            isSelected -> Color.White
            isToday -> Color(0xFF1A202C) // 深色文字，提升对比度
            !isCurrentMonth -> Color(0xFFCBD5E0) // 柔和的灰色
            else -> Color(0xFF2D3748) // 深灰色，确保可读性
        }
    }
    
    val borderColor = remember(isToday) {
                            if (isToday) Color(0xFFC4B5D4).copy(alpha = 0.3f) else Color.Transparent
    }
    
    // 🎨 动画状态
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.0f else 1.0f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "scale_animation"
    )
    
    val animatedContainerColor by animateColorAsState(
        targetValue = containerColor,
        animationSpec = tween(200),
        label = "container_color"
    )
    
    // 🎨 主容器
    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale
            )
            .clip(RoundedCornerShape(12.dp)) // 更大的圆角，现代感十足
            .background(animatedContainerColor)
            .border(
                width = if (isToday) 1.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                onDateClick(date)
            }
            .padding(6.dp), // 增加内边距，提升呼吸感
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            // 🎨 日期数字 - 现代化排版（缩小字体）
            Text(
                text = date.dayOfMonth.toString(),
                color = textColor,
                fontSize = if (isSelected || isToday) 14.sp else 13.sp, // 从16.sp/15.sp减少到14.sp/13.sp
                fontWeight = when {
                    isSelected -> FontWeight.SemiBold
                    isToday -> FontWeight.Medium
                    else -> FontWeight.Normal
                },
                letterSpacing = 0.3.sp, // 从0.5.sp减少到0.3.sp
                overflow = TextOverflow.Clip,
                maxLines = 1
            )
            
            // 🎨 事件指示器 - 极简设计
            if ((habits.isNotEmpty() || events.isNotEmpty()) && isCurrentMonth) {
                Spacer(modifier = Modifier.height(4.dp))
                
                EnhancedEventIndicators(
                    events = events,
                    habits = habits,
                    isSelected = isSelected,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 🎨 高级事件指示器组件 - 现代极简设计
 */
@Composable
private fun EnhancedEventIndicators(
    events: List<CalendarEvent>,
    habits: List<HabitData>,
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    val totalItems = events.size + habits.size
    val maxVisibleDots = 3
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 🎨 习惯指示器 - 绿色系
        val visibleHabits = minOf(habits.size, maxVisibleDots)
        repeat(visibleHabits) { index ->
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .clip(CircleShape)
                    .background(
                        if (isSelected) Color.White.copy(alpha = 0.9f) 
                        else Color(0xFF10B981) // 现代绿色
                    )
            )
            if (index < visibleHabits - 1 || events.isNotEmpty()) {
                Spacer(modifier = Modifier.width(3.dp))
            }
        }
        
        // 🎨 事件指示器 - 蓝色系
        val remainingSlots = maxVisibleDots - visibleHabits
        val visibleEvents = minOf(events.size, remainingSlots)
        repeat(visibleEvents) { index ->
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .clip(CircleShape)
                    .background(
                        if (isSelected) Color.White.copy(alpha = 0.7f)
                        else Color(0xFF3B82F6) // 现代蓝色
                    )
            )
            if (index < visibleEvents - 1) {
                Spacer(modifier = Modifier.width(3.dp))
            }
        }
        
        // 🎨 更多指示器 - 当有超过3个项目时
        if (totalItems > maxVisibleDots) {
            Spacer(modifier = Modifier.width(2.dp))
            Text(
                text = "+",
                fontSize = 8.sp,
                fontWeight = FontWeight.Medium,
                color = if (isSelected) Color.White.copy(alpha = 0.8f) 
                       else Color(0xFF6B7280),
                letterSpacing = 0.sp
            )
        }
    }
}

/**
 * 月视图的单个日期单元格，增强显示事件和习惯
 */
@Composable
fun MonthCalendarDay(
    date: LocalDate,
    isCurrentMonth: Boolean,
    isSelected: Boolean,
    isToday: Boolean,
    events: List<CalendarEvent>,
    habits: List<HabitData>,
    onDateClick: (LocalDate) -> Unit
) {
    // 添加调试日志
    LaunchedEffect(date, events) {
        Log.d("MonthCalendarDay", "日期: $date, 事件数量: ${events.size}")
    }

    // 缓存颜色和修饰符
    val backgroundColor = remember(isSelected, isToday) {
        when {
            isSelected -> Color(0xE6E8E4EC)
            isToday -> CalendarColors.primary.copy(alpha = 0.1f)
            else -> Color.Transparent
        }
    }
    
    val textColor = remember(isSelected, isToday, isCurrentMonth) {
        when {
            isSelected -> CalendarColors.onPrimary
            isToday -> CalendarColors.primary
            !isCurrentMonth -> CalendarColors.onSurface.copy(alpha = 0.4f)
            else -> CalendarColors.onSurface
        }
    }
    
    val fontWeight = remember(isToday, isSelected) {
        if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal
    }

    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable(
                interactionSource = rememberNoRippleInteractionSource(),
                indication = null
            ) {
                onDateClick(date)
            }
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(1.dp)
        ) {
            // 日期数字
            Text(
                text = date.dayOfMonth.toString(),
                color = textColor,
                fontSize = 14.sp,
                fontWeight = fontWeight,
                overflow = TextOverflow.Clip,
                maxLines = 1
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            // 事件和习惯指示 - 改进指示器显示方式
                if ((habits.isNotEmpty() || events.isNotEmpty()) && isCurrentMonth) {
                // 使用固定位置的Box而不是Row
                Box(
                        modifier = Modifier
                            .fillMaxWidth()
                        .height(8.dp)
                        .padding(top = 2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    // 计算需要的指示点总数和位置
                    val totalCount = minOf(3, habits.size + events.size)
                    val spacing = 6.dp // 点之间的间距
                    
                    // 根据总数确定起始X位置
                    val startX = when(totalCount) {
                        1 -> 0.dp
                        2 -> -spacing/2
                        else -> -spacing
                    }
                    
                    // 绘制指示点
                    Row(
                        modifier = Modifier.width(IntrinsicSize.Min),
                        horizontalArrangement = Arrangement.spacedBy(2.dp)
                    ) {
                        // 先显示习惯指示点
                        repeat(minOf(habits.size, 2)) { index ->
                            Box(
                                modifier = Modifier
                                    .size(4.dp)
                                    .border(0.5.dp, Color.White, CircleShape)
                                    .clip(CircleShape)
                                    .background(Color(0xFF81C784))
                            )
                        }
                        
                        // 再显示事件指示点
                        repeat(minOf(events.size, if (habits.isEmpty()) 3 else (3 - minOf(habits.size, 2)))) { index ->
                            Box(
                                modifier = Modifier
                                    .size(4.dp)
                                    .border(0.5.dp, Color.White, CircleShape)
                                    .clip(CircleShape)
                                    .background(Color(0xFFAB47BC))
                            )
                        }
                        
                        // 如果有更多未显示的项目，显示+数字
                        if (habits.size + events.size > 3) {
                            Text(
                                text = "+${habits.size + events.size - 3}",
                                fontSize = 6.sp, // 从7.sp减少到6.sp
                                color = textColor.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
            }
        }
    }
}

// DayCalendarView 已移动到 ModernDayView.kt 中，使用现代化的设计

/**
 * 增强版日视图垂直时间轴
 */
@Composable
fun EnhancedDayTimelineView(
    viewModel: CalendarViewModel,
    modifier: Modifier = Modifier,
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    hourHeightDp: Dp = 60.dp,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    onEventClick: (CalendarEvent) -> Unit = {},
    scrollToCurrentTime: Boolean = false,
    backgroundColor: Color = CalendarColors.background // 添加背景色参数
) {
    val density = LocalDensity.current
    val hourHeightPx = with(density) { hourHeightDp.toPx() }
    val state = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val dayStartHour = 0
    val dayEndHour = 24
    
    // 状态变量
    var isDragging by remember { mutableStateOf(false) }
    var draggedEventId by remember { mutableStateOf<String?>(null) }
    var dragStartY by remember { mutableStateOf(0f) }
    var dragCurrentY by remember { mutableStateOf(0f) }
    
    // 创建事件对话框状态
    var showCreateEventDialog by remember { mutableStateOf(false) }
    var newEventStartTime by remember { mutableStateOf<LocalTime?>(null) }
    var newEventTitle by remember { mutableStateOf("") }
    var newEventDuration by remember { mutableStateOf(60) } // 默认1小时
    
    // 颜色选择器状态
    var showColorPicker by remember { mutableStateOf(false) }
    var selectedColor by remember { mutableStateOf<Color?>(null) }
    var currentRed by remember { mutableStateOf(205f) }   // 默认R值
    var currentGreen by remember { mutableStateOf(86f) }  // 默认G值
    var currentBlue by remember { mutableStateOf(84f) }   // 默认B值
    var currentAlpha by remember { mutableStateOf(0.2f) } // 默认透明度20%
    
    // 编辑模式状态
    var isEditMode by remember { mutableStateOf(false) }
    var editingEventId by remember { mutableStateOf<String?>(null) }
    
    // 获取 context 并在 LaunchedEffect 外存储
    val context = LocalContext.current
    
    // 监听UI事件
    LaunchedEffect(viewModel) {
        viewModel.eventFlow.collect { event ->
            when (event) {
                is CalendarViewModel.CalendarUiEvent.EventsUpdated -> {
                    Log.d("EnhancedDayTimelineView", "事件更新通知已接收，刷新UI")
                    // 通知重组
                    coroutineScope.launch {
                        // 强制重新计算事件布局
                        val updatedEvents = viewModel.calendarState.value.events.filter { 
                            it.start.toLocalDate() == selectedDate 
                        }
                        // 此处不需要实际操作，Compose会检测到events集合变化自动重组
                    }
                }
                is CalendarViewModel.CalendarUiEvent.Error -> {
                    Log.e("EnhancedDayTimelineView", "事件错误: ${event.message}")
                    // 使用上面获取的 context，而不是直接调用 LocalContext.current
                    Toast.makeText(
                        context,
                        "操作失败: ${event.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }
    }
    
    // 对事件进行时间重叠分析，获取每个事件的布局信息
    val eventsLayoutInfo = remember(events) {
        Log.d("EnhancedDayTimelineView", "计算事件布局, 事件数量: ${events.size}")
        calculateEventsLayout(events)
    }
    
    // 滚动到当前时间的函数
    val scrollToCurrentTimePosition = {
        val currentHour = LocalTime.now().hour
        val currentMinute = LocalTime.now().minute
        
        // 计算滚动位置，考虑小时和分钟，更精确地滚动到当前时间点
        // 如果想要滚动到稍微靠上的位置，可以减去一个偏移量
        val scrollPosition = (currentHour - 0.5f).coerceAtLeast(0f)
        
        coroutineScope.launch {
            // 使用带有偏移量的animateScrollToItem可以提供更平滑的滚动体验
            state.animateScrollToItem(currentHour.coerceAtLeast(0), 
                (currentMinute.toFloat() / 60f * hourHeightPx).toInt())
        }
    }
    
    // 默认滚动到当前时间附近
    LaunchedEffect(Unit) {
        scrollToCurrentTimePosition()
    }
    
    // 响应scrollToCurrentTime状态的变化
    LaunchedEffect(scrollToCurrentTime) {
        if (scrollToCurrentTime) {
            scrollToCurrentTimePosition()
        }
    }
    
    Box(modifier = modifier.fillMaxSize().background(backgroundColor)) { // 使用传入的背景色
        LazyColumn(
            state = state,
            contentPadding = contentPadding,
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor) // 使用传入的背景色
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { offset ->
                            // 计算点击位置对应的时间
                            val hourIndex = (offset.y / hourHeightPx).toInt()
                            val minute = ((offset.y % hourHeightPx) / hourHeightPx * 60).toInt()
                            val clickTime = LocalTime.of(
                                (dayStartHour + hourIndex).coerceIn(0, 23), 
                                (minute / 5 * 5).coerceIn(0, 59)
                            )
                            
                            // 显示创建事件对话框
                            newEventStartTime = clickTime
                            showCreateEventDialog = true
                            
                            // 重置颜色选择器，使用默认颜色
                            currentRed = 205f
                            currentGreen = 86f
                            currentBlue = 84f
                            selectedColor = null
                        }
                    )
                }
        ) {
            items(24) { hour ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(hourHeightDp)
                ) {
                    // 时间轴的小时标记和网格
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight()
                    ) {
                        // 时间标签
                        Box(
                            modifier = Modifier
                                .width(50.dp)
                                .fillMaxHeight(),
                            contentAlignment = Alignment.TopEnd
                        ) {
                            Text(
                                text = String.format("%02d:00", hour),
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray,
                                modifier = Modifier.padding(top = 4.dp, end = 8.dp)
                            )
                        }
                        
                        // 分隔线
                        Box(
                            modifier = Modifier
                                .width(1.dp)
                                .fillMaxHeight()
                                .background(Color.LightGray.copy(alpha = 0.5f))
                        )
                        
                        // 时间块内容区
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        ) {
                            // 渲染当前小时的事件
                            eventsLayoutInfo.forEach { (event, layout) ->
                                // 当前事件的开始时间和结束时间
                                val eventStartHour = event.start.hour
                                val eventEndHour = event.end?.hour ?: (event.start.hour + 1)
                                
                                // 🔧 修复：只处理真正跨越当前小时的事件，排除整点边界重复显示
                                // 对于整点结束的事件，只在开始小时到结束前一小时显示
                                val shouldProcessEvent = if (event.end?.minute == 0) {
                                    // 整点结束的事件：只在开始小时到结束前一小时显示
                                    hour in eventStartHour until eventEndHour
                                } else {
                                    // 非整点结束的事件：正常范围显示
                                    hour in eventStartHour..eventEndHour
                                }
                                
                                if (shouldProcessEvent) {
                                    
                                    // 计算在当前小时内的起始位置
                                    val startMinute = if (eventStartHour == hour) event.start.minute else 0
                                    
                                    // 🔧 修复：重新计算结束位置，避免整点事件重复显示
                                    val endMinute = when {
                                        // 如果在当前小时结束，使用精确的结束分钟数
                                        eventEndHour == hour -> event.end?.minute ?: 60
                                        // 如果事件跨越到下一小时且非整点结束，占满当前小时
                                        eventEndHour > hour && event.end?.minute != 0 -> 60
                                        // 如果事件跨越到下一小时且整点结束，占满当前小时（但此时不会执行到这里）
                                        eventEndHour > hour && event.end?.minute == 0 -> 60
                                        // 其他情况占满整个小时
                                        else -> 60
                                    }
                                    
                                    // 计算位置和高度比例
                                    val topOffsetRatio = startMinute.toFloat() / 60f
                                    val heightRatio = (endMinute - startMinute).toFloat() / 60f
                                    
                                    // 转换为dp值，确保最小高度
                                    val topOffset = hourHeightDp * topOffsetRatio
                                    val height = (hourHeightDp * heightRatio).coerceAtLeast(45.dp)
                                    
                                    // 格式化时间显示，只在事件开始的小时显示
                                    val startTimeStr = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                                    val endTimeStr = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: 
                                        event.start.plusMinutes(15).format(DateTimeFormatter.ofPattern("HH:mm"))
                                    
                                    // 只在事件开始的小时显示完整事件，避免在每个跨越的小时重复显示
                                    if (event.start.hour == hour) {
                                        // 判断是否正在拖拽当前事件
                                        val isDraggingThis = isDragging && draggedEventId == event.id
                                        
                                        // 事件块颜色 - 使用事件颜色或默认颜色，确保透明度正确
                                        val bgColor = event.color?.let { 
                                            // 从Long值中提取颜色通道值，包括透明度
                                            val a = (it shr 24 and 0xFFL).toFloat() / 255f
                                            val r = (it shr 16 and 0xFFL).toFloat() / 255f
                                            val g = (it shr 8 and 0xFFL).toFloat() / 255f
                                            val b = (it and 0xFFL).toFloat() / 255f
                                            
                                            Log.d("DayTimelineView", "事件颜色: ID=${event.id}, a=$a, r=$r, g=$g, b=$b, color=${it.toString(16)}")
                                            // 降低不透明度到0.1f，确保与周视图一致
                                            Color(r, g, b, 0.1f)
                                        } ?: getEventColorByUrgency(event.urgency).copy(alpha = 0.1f)
                                        
                                        // 事件块 - 使用计算的水平位置和宽度
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth(layout.widthFraction) // 使用计算的宽度比例
                                                .height(height)
                                                .offset(
                                                    x = layout.xOffsetFraction.times(LocalDensity.current.density).dp, // 横向偏移
                                                    y = topOffset
                                                )
                                                .padding(start = 4.dp, end = 4.dp, bottom = 2.dp)
                                                .clip(RoundedCornerShape(8.dp))
                                                .background(bgColor)
                                                .clickable { 
                                                    // 打开编辑对话框而不是导航
                                                    isEditMode = true
                                                    editingEventId = event.id
                                                    
                                                    Log.d("EnhancedDayTimelineView", "点击事件进入编辑模式: ID=${event.id}, taskId=${event.taskId}, 当前颜色=${event.color?.toString(16) ?: "默认颜色"}")
                                                    
                                                    // 预填充事件信息
                                                    newEventStartTime = event.start.toLocalTime()
                                                    newEventTitle = event.title
                                                    
                                                    // 计算事件持续时间（分钟）
                                                    val durationMinutes = if (event.end != null) {
                                                        ChronoUnit.MINUTES.between(event.start, event.end).toInt()
                                                    } else {
                                                        60 // 默认1小时
                                                    }
                                                    newEventDuration = durationMinutes
                                                    
                                                    // 设置颜色
                                                    event.color?.let { colorLong ->
                                                        val a = (colorLong shr 24 and 0xFFL).toFloat() / 255f
                                                        val r = (colorLong shr 16 and 0xFFL).toFloat() / 255f
                                                        val g = (colorLong shr 8 and 0xFFL).toFloat() / 255f
                                                        val b = (colorLong and 0xFFL).toFloat() / 255f
                                                        
                                                        selectedColor = Color(r, g, b, a)
                                                        currentRed = r * 255f
                                                        currentGreen = g * 255f
                                                        currentBlue = b * 255f
                                                        currentAlpha = a
                                                    }
                                                    
                                                    // 显示对话框
                                                    showCreateEventDialog = true
                                                }
                                                .pointerInput(event.id) {
                                                    detectDragGestures(
                                                        onDragStart = { offset ->
                                                            isDragging = true
                                                            draggedEventId = event.id
                                                            dragStartY = offset.y
                                                            dragCurrentY = offset.y
                                                        },
                                                        onDrag = { change, dragAmount ->
                                                            change.consumePositionChange()
                                                            dragCurrentY += dragAmount.y
                                                        },
                                                        onDragEnd = {
                                                            // 计算拖拽的时间偏移
                                                            val dragDelta = dragCurrentY - dragStartY
                                                            val minutesDelta = ((dragDelta / hourHeightPx) * 60).toInt()
                                                            
                                                            // 如果拖拽超过5分钟，更新事件时间
                                                            if (abs(minutesDelta) >= 5) {
                                                                val newStartTime = event.start.plusMinutes(minutesDelta.toLong())
                                                                val newEndTime = event.end?.plusMinutes(minutesDelta.toLong())
                                                                viewModel.updateTaskTime(event.id, newStartTime, newEndTime ?: newStartTime.plusMinutes(15))
                                                            }
                                                            
                                                            isDragging = false
                                                            draggedEventId = null
                                                        },
                                                        onDragCancel = {
                                                            isDragging = false
                                                            draggedEventId = null
                                                        }
                                                    )
                                                }
                                                .then(
                                                    if (isDraggingThis) {
                                                        Modifier.shadow(8.dp, RoundedCornerShape(8.dp))
                                                    } else {
                                                        Modifier
                                                    }
                                                ),
                                            contentAlignment = Alignment.CenterStart
                                        ) {
                                            Column(
                                                modifier = Modifier
                                                    .fillMaxSize()
                                                    .padding(horizontal = 6.dp, vertical = 6.dp) // 增加垂直内边距
                                            ) {
                                                Text(
                                                    text = event.title,
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    fontWeight = FontWeight.Medium,
                                                    fontSize = 13.sp, // 增加字体大小
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                                
                                                // 增加间距
                                                Spacer(modifier = Modifier.height(4.dp))
                                                
                                                // 确保时间信息始终可见
                                                Text(
                                                    text = "$startTimeStr - $endTimeStr",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    fontSize = 11.sp, // 增加字体大小
                                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                    maxLines = 1
                                                )
                                            }
                                        }
                                    }
                                    // 对于跨越多个小时的事件，在非起始小时显示简化版块
                                    else if (eventStartHour != hour) {
                                        // 只有当高度值明显可见时才显示（避免边界像素问题）
                                        if (heightRatio > 0.05f) { // 只要高度大于5%（3分钟）就显示
                                            val bgColor = event.color?.let { 
                                                // 从Long值中提取颜色通道值，包括透明度
                                                val a = (it shr 24 and 0xFFL).toFloat() / 255f
                                                val r = (it shr 16 and 0xFFL).toFloat() / 255f
                                                val g = (it shr 8 and 0xFFL).toFloat() / 255f
                                                val b = (it and 0xFFL).toFloat() / 255f
                                                
                                                // 降低不透明度到0.1f，确保与周视图一致
                                                Color(r, g, b, 0.1f)
                                            } ?: getEventColorByUrgency(event.urgency).copy(alpha = 0.1f)
                                            
                                            // 跨小时的事件块 - 简化内容，确保连续性
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxWidth(layout.widthFraction)
                                                    .height(height)
                                                    .offset(
                                                        x = layout.xOffsetFraction.times(LocalDensity.current.density).dp,
                                                        y = topOffset
                                                    )
                                                    .padding(start = 4.dp, end = 4.dp, bottom = 2.dp)
                                                    .clip(RoundedCornerShape(8.dp))
                                                    .background(bgColor)
                                                    .clickable {
                                                        // 使用相同的编辑功能
                                                        isEditMode = true
                                                        editingEventId = event.id
                                                        newEventStartTime = event.start.toLocalTime()
                                                        newEventTitle = event.title
                                                        
                                                        val durationMinutes = if (event.end != null) {
                                                            ChronoUnit.MINUTES.between(event.start, event.end).toInt()
                                                        } else {
                                                            60
                                                        }
                                                        newEventDuration = durationMinutes
                                                        
                                                        event.color?.let { colorLong ->
                                                            val a = (colorLong shr 24 and 0xFFL).toFloat() / 255f
                                                            val r = (colorLong shr 16 and 0xFFL).toFloat() / 255f
                                                            val g = (colorLong shr 8 and 0xFFL).toFloat() / 255f
                                                            val b = (colorLong and 0xFFL).toFloat() / 255f
                                                            
                                                            selectedColor = Color(r, g, b, a)
                                                            currentRed = r * 255f
                                                            currentGreen = g * 255f
                                                            currentBlue = b * 255f
                                                            currentAlpha = a
                                                        }
                                                        
                                                        showCreateEventDialog = true
                                                    }
                                            ) {
                                                // 跨小时段的事件块内只显示标题，不显示时间
                                                if (heightRatio > 0.25f) { // 只有高度足够(15分钟以上)才显示文本
                                                    Text(
                                                        text = event.title,
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        fontWeight = FontWeight.Medium,
                                                        fontSize = 13.sp,
                                                        maxLines = 1,
                                                        overflow = TextOverflow.Ellipsis,
                                                        modifier = Modifier.padding(6.dp)
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 小时分隔线
                if (hour < 23) {
                    Divider(
                        color = Color.LightGray.copy(alpha = 0.3f),
                        modifier = Modifier.padding(start = 50.dp)
                    )
                }
            }
        }
        
        // 当前时间指示线
        val currentTime = LocalTime.now()
        if (selectedDate.isEqual(LocalDate.now())) {
            val minutesFromMidnight = currentTime.hour * 60 + currentTime.minute
            val offsetY = with(density) { (minutesFromMidnight / 60f) * hourHeightDp.toPx() }
            
            Canvas(modifier = Modifier.fillMaxWidth()) {
                drawLine(
                    color = Color.Red,
                    start = Offset(50f, offsetY),
                    end = Offset(size.width, offsetY),
                    strokeWidth = 2f
                )
                
                // 时间指示点
                drawCircle(
                    color = Color.Red,
                    radius = 6f,
                    center = Offset(50f, offsetY)
                )
            }
        }
    }
    
    // 颜色选择器对话框
    if (showColorPicker) {
        AlertDialog(
            onDismissRequest = { showColorPicker = false },
            title = { Text("选择颜色") },
            text = {
                Column(
                        modifier = Modifier
                            .fillMaxWidth()
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 颜色预览
                    val previewColor = Color(
                        red = currentRed / 255f,
                        green = currentGreen / 255f,
                        blue = currentBlue / 255f,
                        alpha = currentAlpha  // 使用自定义透明度
                    )
                    
                    // 显示颜色预览和十六进制值
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(48.dp)
                                .background(previewColor, RoundedCornerShape(8.dp))
                                .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp))
                        )
                        
                        // 显示颜色的十六进制值和透明度百分比
                        val hexValue = String.format(
                            "#%02X%02X%02X (%d%%)",
                            currentRed.toInt(),
                            currentGreen.toInt(),
                            currentBlue.toInt(),
                            (currentAlpha * 100).toInt()
                        )
                        Text(text = hexValue, style = MaterialTheme.typography.bodyMedium)
                    }
                    
                    // R值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("R: ${currentRed.toInt()}")
                        }
                        Slider(
                            value = currentRed,
                            onValueChange = { currentRed = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Red,
                                activeTrackColor = Color.Red.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // G值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("G: ${currentGreen.toInt()}")
                        }
                        Slider(
                            value = currentGreen,
                            onValueChange = { currentGreen = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Green,
                                activeTrackColor = Color.Green.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // B值滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("B: ${currentBlue.toInt()}")
                        }
                        Slider(
                            value = currentBlue,
                            onValueChange = { currentBlue = it },
                            valueRange = 0f..255f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Blue,
                                activeTrackColor = Color.Blue.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // 透明度滑块
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text("透明度: ${(currentAlpha * 100).toInt()}%")
                        }
                        Slider(
                            value = currentAlpha,
                            onValueChange = { currentAlpha = it },
                            valueRange = 0.1f..1.0f,
                            colors = SliderDefaults.colors(
                                thumbColor = Color.DarkGray,
                                activeTrackColor = Color.DarkGray.copy(alpha = 0.5f)
                            )
                        )
                    }
                    
                    // 透明度说明
                    Row(
                        modifier = Modifier
            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = MaterialIcons.Default.Info,
                            contentDescription = "透明度信息",
                            tint = Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            "透明度可调整，数值越小越透明",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                    
                    // 颜色推荐
                    Text("推荐颜色", modifier = Modifier.padding(top = 8.dp))
                    
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(getRecommendedColors()) { color ->
        Box(
            modifier = Modifier
                                    .size(36.dp)
                                    .background(color, RoundedCornerShape(8.dp))
                                    .border(
                                        width = if (isColorSimilar(color, previewColor)) 2.dp else 1.dp,
                                        color = if (isColorSimilar(color, previewColor)) Color.Black else Color.LightGray,
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .clickable {
                                        // 设置当前RGB值为推荐颜色的值，包括透明度
                                        currentRed = color.red * 255f
                                        currentGreen = color.green * 255f
                                        currentBlue = color.blue * 255f
                                        currentAlpha = color.alpha
                                    }
                            )
                        }
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 使用当前RGB值和用户自定义透明度创建新的Color对象
                        val newColor = Color(
                            red = currentRed / 255f,
                            green = currentGreen / 255f,
                            blue = currentBlue / 255f,
                            alpha = currentAlpha  // 使用自定义透明度
                        )
                        
                        selectedColor = newColor
                        showColorPicker = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showColorPicker = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 创建事件对话框
    if (showCreateEventDialog && newEventStartTime != null) {
        AlertDialog(
            onDismissRequest = { 
                showCreateEventDialog = false
                isEditMode = false
                editingEventId = null
            },
            title = {
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = if (isEditMode) "编辑事件" else "创建新事件",
                        modifier = Modifier.align(Alignment.CenterStart)
                    )
                    
                    // 只在编辑模式下显示删除按钮
                    if (isEditMode && editingEventId != null) {
                        IconButton(
                            onClick = {
                                // 调用删除方法
                                editingEventId?.let { eventId ->
                                    // 处理"task_"前缀
                                    val taskId = if (eventId.startsWith("task_")) {
                                        eventId
                                    } else {
                                        "task_$eventId"
                                    }
                                    viewModel.deleteTask(taskId)
                                    
                                    // 关闭对话框
                                    showCreateEventDialog = false
                                    isEditMode = false
                                    editingEventId = null
                                    
                                    // 显示成功提示
                                    Toast.makeText(context, "事件已删除", Toast.LENGTH_SHORT).show()
                                }
                            },
                            modifier = Modifier
                                .size(48.dp)
                                .align(Alignment.CenterEnd)
                                .padding(end = 8.dp)
                        ) {
                            Icon(
                                imageVector = MaterialIcons.Default.Delete,
                                contentDescription = "删除事件",
                                tint = Color.Red.copy(alpha = 0.8f)
                            )
                        }
                    }
                }
            },
            text = {
        Column(
                    modifier = Modifier.padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text("时间: ${newEventStartTime?.format(DateTimeFormatter.ofPattern("HH:mm"))}")
                    
                    OutlinedTextField(
                        value = newEventTitle,
                        onValueChange = { newEventTitle = it },
                        label = { Text("事件标题") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("持续时间: ")
                        Slider(
                            value = newEventDuration.toFloat(),
                            onValueChange = { newEventDuration = it.toInt() },
                            valueRange = 15f..180f,
                            steps = 11,
                    modifier = Modifier.weight(1f)
                )
                        Text("${newEventDuration}分钟")
                    }
                    
                    // 颜色选择
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("事件颜色: ")
                        
                        // 显示当前选中的颜色
                        Box(
                            modifier = Modifier
                                .size(36.dp)
                                .background(
                                    selectedColor ?: Color(0xCCE57373).copy(alpha = 0.2f), // 默认红色，20%透明度
                                    RoundedCornerShape(8.dp)
                                )
                                .border(1.dp, Color.LightGray, RoundedCornerShape(8.dp))
                                .clickable { showColorPicker = true }
                        )
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 计算实际的开始时间
                        val startDateTime = LocalDateTime.of(selectedDate, LocalTime.of(9, 0))
                        
                        // 确保最小持续时间为15分钟
                        val safeDuration = Math.max(newEventDuration, 15)
                        
                        // 计算结束时间
                        val endDateTime = startDateTime.plusMinutes(safeDuration.toLong())
                        
                        // 将选中的颜色转换为Long类型存储
                        val colorValue = selectedColor?.let { color ->
                            try {
                                val colorInt = color.toArgb()
                                val r = (colorInt shr 16) and 0xFF
                                val g = (colorInt shr 8) and 0xFF
                                val b = colorInt and 0xFF
                                val a = (colorInt shr 24) and 0xFF
                                
                                ((a.toLong() shl 24) or (r.toLong() shl 16) or (g.toLong() shl 8) or b.toLong())
                            } catch (e: Exception) {
                                null // 如果处理颜色出错，返回null
                            }
                        }
                        
                        // 创建新事件
                        viewModel.createEvent(
                            title = newEventTitle,
                            start = startDateTime,
                            end = endDateTime,
                            color = colorValue
                        )
                        
                        // 重置并关闭对话框
                        showCreateEventDialog = false
                        newEventTitle = ""
                        selectedColor = null
                    },
                    enabled = newEventTitle.isNotBlank()
                ) {
                    Text("创建")
                }
            },
            dismissButton = {
                TextButton(onClick = { 
                    showCreateEventDialog = false
                    newEventTitle = ""
                    selectedColor = null
                    isEditMode = false
                    editingEventId = null
                }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 判断两个颜色是否相似（忽略alpha值）
 */
private fun isColorSimilar(color1: Color, color2: Color): Boolean {
    val threshold = 0.05f  // 颜色相似度阈值
    val color1Int = color1.toArgb()
    val color2Int = color2.toArgb()
    
    val r1 = (color1Int shr 16) and 0xFF
    val g1 = (color1Int shr 8) and 0xFF
    val b1 = color1Int and 0xFF
    
    val r2 = (color2Int shr 16) and 0xFF
    val g2 = (color2Int shr 8) and 0xFF
    val b2 = color2Int and 0xFF
    
    return abs(r1 - r2) < (threshold * 255) &&
           abs(g1 - g2) < (threshold * 255) &&
           abs(b1 - b2) < (threshold * 255)
}

/**
 * 根据事件紧急程度获取颜色（带透明度）
 */
private fun getEventColorByUrgency(urgency: TaskUrgency): Color {
    val baseColor = when (urgency) {
        TaskUrgency.CRITICAL -> Color(0xFFE57373) // 红色
        TaskUrgency.HIGH -> Color(0xFFFFB74D)   // 橙色
        TaskUrgency.MEDIUM -> Color(0xFF4FC3F7) // 蓝色
        TaskUrgency.LOW -> Color(0xFF81C784)    // 绿色
        else -> Color(0xFF9E9E9E) // 默认灰色
    }
    
    // 应用10%透明度
    return baseColor.copy(alpha = 0.1f)
}

/**
 * 根据事件紧急程度获取推荐的事件颜色列表，供用户选择
 */
private fun getRecommendedColors(): List<Color> {
    return listOf(
        // 不同颜色，透明度固定为10%
        Color(0xFFE57373).copy(alpha = 0.1f), // 红色带10%透明度  
        Color(0xFFFFB74D).copy(alpha = 0.1f), // 橙色带10%透明度
        Color(0xFF4FC3F7).copy(alpha = 0.1f), // 蓝色带10%透明度
        Color(0xFF81C784).copy(alpha = 0.1f), // 绿色带10%透明度
        
        // 相同颜色，不同透明度
        Color(0xFFAB47BC).copy(alpha = 0.1f),  // 紫色带10%透明度
        Color(0xFFAB47BC).copy(alpha = 0.075f), // 紫色带7.5%透明度
        Color(0xFFAB47BC).copy(alpha = 0.05f),  // 紫色带5%透明度
        Color(0xFFAB47BC).copy(alpha = 0.025f)  // 紫色带2.5%透明度
    )
}

/**
 * 事件布局信息，包含水平位置偏移和宽度比例
 */
data class EventLayoutInfo(
    val widthFraction: Float,  // 事件宽度占比
    val xOffsetFraction: Float // 横向偏移占比
)

/**
 * 计算事件的布局信息，解决重叠问题
 * 实现并排显示同一时间段内的事件
 */
private fun calculateEventsLayout(events: List<CalendarEvent>): Map<CalendarEvent, EventLayoutInfo> {
    // 如果没有事件或只有一个事件，直接返回简单布局
    if (events.isEmpty()) {
        return emptyMap()
    }
    
    if (events.size == 1) {
        return mapOf(events.first() to EventLayoutInfo(0.95f, 0f))
    }
    
    // 创建结果Map
    val result = mutableMapOf<CalendarEvent, EventLayoutInfo>()
    
    // 预处理事件数据 - 处理无效的时间范围
    val normalizedEvents = events.map { event ->
        // 处理开始时间等于结束时间的情况，确保至少有15分钟的持续时间
        if (event.end != null && event.start == event.end) {
            event.copy(end = event.start.plusMinutes(15))
        } 
        // 处理缺少结束时间的情况
        else if (event.end == null) {
                                    event.copy(end = event.start.plusMinutes(15))
        }
        else {
            event
        }
    }
    
    // 创建时间段冲突图
    val overlapGroups = mutableListOf<MutableSet<CalendarEvent>>()
    
    // 寻找重叠的事件组
    for (event in normalizedEvents) {
        val eventStart = event.start
                        val eventEnd = event.end ?: event.start.plusMinutes(15)
        
        // 查找此事件是否与已有分组重叠
        var foundGroup = false
        for (group in overlapGroups) {
            // 检查是否与组内任何事件重叠
            val overlapsWithGroup = group.any { existingEvent ->
                val existingStart = existingEvent.start
                val existingEnd = existingEvent.end ?: existingStart.plusMinutes(15)
                
                // 检测时间重叠，包括边界情况
                (eventStart.isBefore(existingEnd) && eventEnd.isAfter(existingStart)) ||
                        eventStart == existingStart || eventEnd == existingEnd
            }
            
            if (overlapsWithGroup) {
                group.add(event)
                foundGroup = true
                break
            }
        }
        
        // 如果没找到重叠的组，创建新组
        if (!foundGroup) {
            overlapGroups.add(mutableSetOf(event))
        }
    }
    
    // 合并可能有交叉的组
    var i = 0
    while (i < overlapGroups.size - 1) {
        var j = i + 1
        var merged = false
        
        while (j < overlapGroups.size) {
            // 检查两个组是否有共同事件或时间重叠
            val hasCommonEvents = overlapGroups[i].intersect(overlapGroups[j]).isNotEmpty()
            
            // 检查组间事件是否有时间重叠
            val hasTimeOverlap = if (!hasCommonEvents) {
                val eventsInGroup1 = overlapGroups[i].toList()
                val eventsInGroup2 = overlapGroups[j].toList()
                
                eventsInGroup1.any { event1 ->
                    val event1Start = event1.start
                    val event1End = event1.end ?: event1Start.plusMinutes(15)
                    
                    eventsInGroup2.any { event2 ->
                        val event2Start = event2.start
                        val event2End = event2.end ?: event2Start.plusMinutes(15)
                        
                        // 检测时间重叠
                        (event1Start.isBefore(event2End) && event1End.isAfter(event2Start)) ||
                                event1Start == event2Start || event1End == event2End
                    }
                }
            } else {
                false // 已经有共同事件，不需要再检查时间重叠
            }
            
            if (hasCommonEvents || hasTimeOverlap) {
                // 合并组
                overlapGroups[i].addAll(overlapGroups[j])
                overlapGroups.removeAt(j)
                merged = true
            } else {
                j++
            }
        }
        
        if (!merged) {
            i++
        }
    }
    
    // 对每个重叠组计算布局
    for (group in overlapGroups) {
        val eventsInGroup = group.toList()
        val columnCount = eventsInGroup.size // 该组中的事件数量决定每个事件的宽度
        
        // 按开始时间排序
        val sortedEvents = eventsInGroup.sortedBy { it.start }
        
        // 计算每个事件的宽度和位置
        val width = 0.95f / columnCount // 95%的宽度平均分配
        
        sortedEvents.forEachIndexed { index, event ->
            val xOffset = index * width // 水平偏移
            result[event] = EventLayoutInfo(width, xOffset)
        }
    }
    
    return result
}

// 简洁周视图 (类似图1)
@Composable
fun WeekCalendarView(
    weekRange: Pair<LocalDate, LocalDate>,
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    viewModel: CalendarViewModel = hiltViewModel(),
    habitViewModel: HabitViewModel = hiltViewModel(),
    backgroundColor: Color = CalendarColors.background
) {
    // 🔧 修复首次加载不显示任务的问题
    // 确保进入组件时触发数据加载
    LaunchedEffect(Unit) {
        Log.d("WeekCalendarView", "=== 组件初始化，触发数据加载 ===")
        viewModel.loadEvents()
    }
    
    // 🔧 监听日历状态变化，确保数据加载完成后重新组合
    val calendarState by viewModel.calendarState.collectAsState()
    
    // 创建当前周的日期列表
    val datesList = mutableListOf<LocalDate>()
    var currentDate = weekRange.first
    while (!currentDate.isAfter(weekRange.second)) {
        datesList.add(currentDate)
        currentDate = currentDate.plusDays(1)
    }
    
    // 🔧 从状态中获取事件而不是直接调用方法，确保响应式更新
    val weekEvents = remember(calendarState.events, weekRange.first, weekRange.second) {
        calendarState.events.filter { event ->
            val eventDate = event.start.toLocalDate()
            (eventDate.isEqual(weekRange.first) || eventDate.isAfter(weekRange.first)) &&
                    (eventDate.isEqual(weekRange.second) || eventDate.isBefore(weekRange.second))
        }
    }
    
    Log.d("WeekCalendarView", "周视图事件更新: 总事件${calendarState.events.size}个, 本周事件${weekEvents.size}个")
    Log.d("WeekCalendarView", "数据加载状态: isLoading=${calendarState.isLoading}")
    
    // 获取习惯数据
    val habits = habitViewModel.habits.collectAsState().value
    
    // 计算每个日期的习惯完成情况
    val dateHabitsMap = remember(habits, datesList) {
        habits.flatMap { habit ->
            habit.completedDates.filter { date -> 
                date >= weekRange.first && date <= weekRange.second 
            }.map { date -> date to habit }
        }.groupBy({ it.first }, { it.second })
    }
    
    // 🔧 获取选中日期的事件 - 使用响应式状态
    val selectedDateEvents = remember(calendarState.events, selectedDate) {
        calendarState.events.filter { event ->
            val eventDate = event.start.toLocalDate()
            val isFloatingTask = event.id.startsWith("floating_") || 
                               event.title.contains("[浮动]") ||
                               event.title.contains("本周完成")
            
            eventDate == selectedDate && !isFloatingTask
        }
    }
    
    // 获取选中日期的习惯
    val selectedDateHabits = habits.filter { habit ->
        // 对于选中日期，显示所有需要完成的习惯，而不仅是已完成的
        habit.frequency.contains(selectedDate.dayOfWeek)
    }
    
    // 定义习惯区域的可见性状态
    var habitsVisible by remember { mutableStateOf(true) }
    
    // 🔧 优化滚动实现 - 使用BoxWithConstraints限制滚动范围
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        val maxHeight = this.maxHeight
        val scrollState = rememberScrollState()
        
        // 计算实际内容高度和最大滚动范围
        val contentHeight = 1000.dp // 预估内容高度
        val maxScrollRange = (contentHeight - maxHeight).coerceAtLeast(0.dp)
        
        // 🔧 限制滚动范围的Column
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(
                    state = scrollState,
                    enabled = contentHeight > maxHeight // 仅在内容超出屏幕时启用滚动
                )
                .padding(horizontal = 4.dp)
        ) {
            // 显示日期数字行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                datesList.forEach { date ->
                    val isToday = date == LocalDate.now()
                    val isSelected = date == selectedDate
                    val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
                    
                    // 当天的事件和习惯
                    val eventsForDay = weekEvents.filter { it.start.toLocalDate() == date }
                    val habitsForDay = habits.filter { habit -> 
                        habit.frequency.contains(date.dayOfWeek)
                    }
                    val completedHabitsForDay = dateHabitsMap[date] ?: emptyList()
                    val hasEvents = eventsForDay.isNotEmpty()
                    val hasHabits = habitsForDay.isNotEmpty()
                    val hasCompletedHabits = completedHabitsForDay.isNotEmpty()
                    
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 4.dp)
                    ) {
                        // 日期数字
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(
                                    when {
                                        isSelected -> Color(0xE6E8E4EC)
                                        isToday -> CalendarColors.primary.copy(alpha = 0.1f)
                                        else -> Color.Transparent
                                    }
                                )
                                .clickable(
                                    interactionSource = rememberNoRippleInteractionSource(),
                                    indication = null
                                ) {
                                    onDateSelected(date)
                                }
                                .padding(2.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = date.dayOfMonth.toString(),
                                fontSize = 13.sp,
                                fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
                                color = when {
                                    isSelected -> CalendarColors.primary
                                    isToday -> CalendarColors.primary
                                    isWeekend -> Color(0xFFE57373)
                                    else -> CalendarColors.onPrimary
                                }
                            )
                        }
                        
                        // 事件和习惯指示器
                        Row(
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier.padding(top = 2.dp)
                        ) {
                            if (hasHabits) {
                                Box(
                                    modifier = Modifier
                                        .size(3.dp)
                                        .clip(CircleShape)
                                        .background(
                                            if (hasCompletedHabits) {
                                                if (completedHabitsForDay.size == habitsForDay.size) {
                                                    Color(0xFF4CAF50).copy(alpha = 0.8f)
                                                } else {
                                                    Color(0xFFFFC107).copy(alpha = 0.8f)
                                                }
                                            } else {
                                                Color(0xFF9E9E9E).copy(alpha = 0.5f)
                                            }
                                        )
                                )
                                Spacer(modifier = Modifier.width(1.dp))
                            }
                            
                            if (hasEvents) {
                                Box(
                                    modifier = Modifier
                                        .size(3.dp)
                                        .clip(CircleShape)
                                        .background(CalendarColors.primary.copy(alpha = 0.7f))
                                )
                            }
                        }
                    }
                }
            }
            
            // 星期几标题行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                datesList.forEach { date ->
                    val dayText = when (date.dayOfWeek) {
                        DayOfWeek.MONDAY -> "周一"
                        DayOfWeek.TUESDAY -> "周二"
                        DayOfWeek.WEDNESDAY -> "周三"
                        DayOfWeek.THURSDAY -> "周四"
                        DayOfWeek.FRIDAY -> "周五"
                        DayOfWeek.SATURDAY -> "周六"
                        DayOfWeek.SUNDAY -> "周日"
                    }
                    val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
                    
                    Text(
                        text = dayText,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center,
                        color = if (isWeekend) Color(0xFFE57373).copy(alpha = 0.8f) else CalendarColors.onSurface.copy(alpha = 0.6f),
                        fontSize = 11.sp
                    )
                }
            }
            
            // 任务详情显示区域
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = selectedDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 EEEE", Locale.CHINA)),
                fontSize = 13.sp,
                fontWeight = FontWeight.Medium,
                color = CalendarColors.primary,
                modifier = Modifier.padding(start = 8.dp, bottom = 8.dp)
            )

            // 🆕 浮动任务区域 - 支持限制滚动
            val weekStart = weekRange.first.atStartOfDay()
            val weekEnd = weekRange.second.atTime(23, 59, 59)
            
                          val floatingTasks by viewModel.getFloatingTasksFlow(weekStart, weekEnd).collectAsState(initial = emptyList())
              var showCreateDialog by remember { mutableStateOf(false) }
              
                            // 🔧 调试日志：追踪对话框状态
              Log.d("CalendarScreen", "📱 showCreateDialog状态: $showCreateDialog")
              Log.d("CalendarScreen", "📊 浮动任务数量: ${floatingTasks.size}")
            
            // 🔧 修复拖拽层级问题：将浮动任务和今日任务放在统一的拖拽容器中
            DragAndDropContainer(
                floatingTasks = floatingTasks,
                selectedDateEvents = selectedDateEvents,
                selectedDate = selectedDate,
                weekDates = datesList,
                viewModel = viewModel,
                onCreateFloatingTask = { showCreateDialog = true }
            )
            
            // 创建浮动任务对话框
            if (showCreateDialog) {
                CreateFloatingTaskDialog(
                    weekStart = weekStart,
                    weekEnd = weekEnd,
                    onDismiss = { showCreateDialog = false },
                    onCreateTask = { title, description, priority, estimatedMinutes ->
                        Log.d("CalendarScreen", "🔧 开始创建浮动任务: $title")
                        try {
                            viewModel.createFloatingTask(
                                title = title,
                                description = description,
                                priority = priority,
                                weekStart = weekStart,
                                weekEnd = weekEnd,
                                estimatedMinutes = estimatedMinutes
                            )
                            Log.d("CalendarScreen", "✅ 浮动任务创建成功，关闭对话框")
                        } catch (e: Exception) {
                            Log.e("CalendarScreen", "❌ 浮动任务创建失败", e)
                        } finally {
                            showCreateDialog = false
                            Log.d("CalendarScreen", "📱 对话框状态设置为: false")
                        }
                    }
                )
            }
            
            // 底部安全区域
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * 周视图中的今日习惯区域
 */
@Composable
private fun DailyHabitsSection(
    selectedDate: LocalDate,
    habits: List<HabitData>,
    habitViewModel: HabitViewModel,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    if (habits.isEmpty()) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .padding(8.dp), // 🔧 减少内边距：16dp -> 8dp
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "今日无习惯",
                fontSize = 12.sp, // 🔧 减小字体：14sp -> 12sp
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
        return
    }
    
    // 习惯布局外部包装一个Box，设置固定高度约束
    Box(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(max = 500.dp)
    ) {
    // 🔧 修复：使用FlowRow替代LazyVerticalGrid，避免可滚动容器嵌套
    // 创建分组显示的习惯
    val chunkedHabits = habits.chunked(3) // 每行3个习惯
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = 400.dp) // 🔧 减小最大高度：500dp -> 400dp
            .padding(horizontal = 8.dp), // 🔧 减少水平内边距：16dp -> 8dp
        verticalArrangement = Arrangement.spacedBy(4.dp) // 🔧 减少垂直间距：8dp -> 4dp
    ) {
        chunkedHabits.forEach { rowHabits ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp) // 🔧 减少水平间距：8dp -> 4dp
            ) {
                rowHabits.forEach { habit ->
                    val isCompleted = habit.completedDates.contains(selectedDate)
                    
                    // 习惯项容器，使用weight平均分配宽度
                    Box(
                        modifier = Modifier
                            .weight(1f) // 使用weight替代fillMaxWidth实现平均分配
                            .clip(RoundedCornerShape(16.dp))
                            .background(habit.color.copy(alpha = 0.1f)) // 降低背景色不透明度
                            .clickable {
                                if (isCompleted) {
                                    habitViewModel.removeCompletedDate(habit.id, selectedDate)
                                    Toast.makeText(context, "已取消「${habit.name}」打卡", Toast.LENGTH_SHORT).show()
                                } else {
                                    habitViewModel.addCompletedDate(habit.id, selectedDate)
                                    Toast.makeText(context, "「${habit.name}」打卡成功", Toast.LENGTH_SHORT).show()
                                }
                            }
                            .padding(horizontal = 4.dp, vertical = 4.dp), // 🔧 减少内边距：8dp -> 4dp
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(2.dp), // 🔧 减少间距：4dp -> 2dp
                            modifier = Modifier.fillMaxWidth() // 确保行内容填充整个Box
                        ) {
                            // 显示完成状态
                            if (isCompleted) {
                                Text(
                                    text = "✓",
                                    color = Color(0xFF4CAF50),
                                    fontSize = 12.sp, // 🔧 减小字体：16sp -> 12sp
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            // 习惯图标或emoji
                            if (habit.customEmoji.isNotEmpty()) {
                                Text(
                                    text = habit.customEmoji,
                                    fontSize = 14.sp // 🔧 减小字体：18sp -> 14sp
                                )
                            } else {
                                Text(
                                    text = habit.icon, // 现在是emoji字符串
                                    fontSize = 14.sp,
                                    modifier = Modifier.size(14.dp) // 🔧 减小图标：18dp -> 14dp
                                )
                            }
                            
                            // 习惯名称 - 使用Modifier.weight确保在空间不足时能够适当缩小
                            Text(
                                text = habit.name,
                                fontSize = 11.sp, // 🔧 减小字体：14sp -> 11sp
                                color = Color.Black,
                                overflow = TextOverflow.Ellipsis, // 文本溢出时显示省略号
                                maxLines = 1, // 限制为一行
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                // 如果行中的习惯少于3个，添加空白占位符
                repeat(3 - rowHabits.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
        }
    }
}
}

/**
 * 周视图中的事件项 - 现代简约设计
 */
@Composable
private fun WeekCalendarEventItem(
    event: CalendarEvent,
    viewModel: CalendarViewModel
) {
    val eventColor = event.color?.let { Color(it) } ?: 
        TaskColors[event.urgency.ordinal % TaskColors.size]
    
    // 🎨 现代简约设计 - 完全无阴影的平面设计
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 1.dp) // 更小的间距
            .clip(RoundedCornerShape(8.dp)) // 适中的圆角
            .background(
                color = Color.White.copy(alpha = 0.6f) // 极淡的白色背景
            )
            .border(
                width = 0.5.dp,
                color = eventColor.copy(alpha = 0.12f), // 更淡的边框
                shape = RoundedCornerShape(8.dp)
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            eventColor.copy(alpha = 0.02f),
                            Color.Transparent
                        )
                    )
                ) // 极淡的渐变背景
                .padding(horizontal = 16.dp, vertical = 12.dp), // 更大的内边距提升质感
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🎯 简约状态指示器 - 替代传统复选框
            Surface(
                modifier = Modifier
                    .size(20.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        viewModel.toggleEventCompletion(event.id, !event.isCompleted)
                    },
                color = if (event.isCompleted) 
                    eventColor.copy(alpha = 0.1f) 
                else 
                    Color.Transparent,
                shape = CircleShape,
                border = BorderStroke(
                    width = 1.5.dp,
                    color = if (event.isCompleted) 
                        eventColor 
                    else 
                        eventColor.copy(alpha = 0.3f)
                )
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    if (event.isCompleted) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已完成",
                            tint = eventColor,
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 🎨 优雅的左侧颜色条
            Box(
                modifier = Modifier
                    .width(3.dp)
                    .height(24.dp)
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                eventColor.copy(alpha = 0.8f),
                                eventColor.copy(alpha = 0.3f)
                            )
                        ),
                        shape = RoundedCornerShape(1.5.dp)
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 📝 内容区域
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = event.title,
                    fontWeight = FontWeight.SemiBold, // 更强的字重
                    fontSize = 14.sp,
                    color = Color(0xFF1A1A1A), // 更深的黑色
                    letterSpacing = 0.2.sp, // 字母间距提升可读性
                    lineHeight = 18.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 时间和状态信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 🎯 浮动任务显示时间范围，具体任务显示精确时间
                    val timeText = if (event.id.startsWith("floating_") || event.title.contains("[浮动]")) {
                        "本周完成" // 浮动任务显示时间范围
                    } else if (event.isAllDay == true) {
                        "全天"
                    } else {
                        val startTime = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                        val endTime = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: 
                            event.start.plusMinutes(15).format(DateTimeFormatter.ofPattern("HH:mm"))
                        "$startTime - $endTime"
                    }
                    
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = Color(0xFF8E8E93), // iOS风格的灰色
                        modifier = Modifier.size(12.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = timeText,
                        fontSize = 11.sp,
                        color = Color(0xFF8E8E93),
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.1.sp
                    )
                    
                    // 优先级标签
                    if (event.urgency != TaskUrgency.MEDIUM) {
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Surface(
                            color = when(event.urgency) {
                                TaskUrgency.CRITICAL -> Color(0xFFFF3B30).copy(alpha = 0.1f)
                                TaskUrgency.HIGH -> Color(0xFFFF9500).copy(alpha = 0.1f)
                                else -> Color.Transparent
                            },
                            shape = RoundedCornerShape(6.dp)
                        ) {
                            Text(
                                text = when(event.urgency) {
                                    TaskUrgency.CRITICAL -> "紧急"
                                    TaskUrgency.HIGH -> "重要"
                                    else -> ""
                                },
                                fontSize = 9.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = when(event.urgency) {
                                    TaskUrgency.CRITICAL -> Color(0xFFFF3B30)
                                    TaskUrgency.HIGH -> Color(0xFFFF9500)
                                    else -> Color.Transparent
                                },
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
            }
            
            // 🎯 极简操作指示器
            if (!event.isCompleted) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "查看详情",
                    tint = Color(0xFFD1D1D6), // 极淡的箭头
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
private fun CalendarEventList(
    events: List<CalendarEvent>,
    onEventClick: (CalendarEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    // 使用key保证列表状态稳定性，减少重组
    val listState = rememberLazyListState()
    
    LazyColumn(
        state = listState,
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = events,
            key = { it.id } // 使用稳定的键以提高性能
        ) { event ->
            CalendarEventItem(
                event = event,
                onClick = { onEventClick(event) },
                modifier = Modifier.animateItemPlacement() // 平滑动画
            )
        }
        
        // 如果没有事件，显示空状态
        if (events.isEmpty()) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "没有事件",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
private fun CalendarEventItem(
    event: CalendarEvent,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 计算背景颜色和文字颜色 - 避免在remember中使用Composable函数
    val color = event.color
    val backgroundColor = if (color != null) {
        Color(color)
    } else {
        MaterialTheme.colorScheme.surface
    }
    
    // 使用Surface避免多层嵌套提高性能
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        color = backgroundColor.copy(alpha = 0.1f), // 降低不透明度从0.12f到0.1f
        shape = RoundedCornerShape(8.dp),
        // 移除边框
    ) {
        Row(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 优化文本布局避免重组
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = event.title,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                // 避免在remember中使用Composable函数
                val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
                val startTime = event.start.format(timeFormatter)
                val endTime = event.end?.format(timeFormatter) ?: "?"
                val formattedTime = "$startTime - $endTime"
                
                Text(
                    text = formattedTime,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            // 完成状态指示器
            if (event.isCompleted) {
                Icon(
                    imageVector = MaterialIcons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = Color(0xFF5d733a).copy(alpha = 0.8f),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun CalendarTopBar(
    currentYearMonth: YearMonth,
    onPreviousClick: () -> Unit,
    onNextClick: () -> Unit,
    onMonthClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(CalendarColors.surface)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onPreviousClick) {
                Icon(
                    imageVector = MaterialIcons.Default.ChevronLeft,
                    contentDescription = "上个月",
                    tint = CalendarColors.onSurface
                )
            }
            
            Row(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onMonthClick() },
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = currentYearMonth.format(DateTimeFormatter.ofPattern("yyyy年MM月")),
                    style = MaterialTheme.typography.titleLarge,
                    color = CalendarColors.onSurface,
                    fontWeight = FontWeight.Bold
                )
                Icon(
                    imageVector = MaterialIcons.Default.ArrowDropDown,
                    contentDescription = "选择月份",
                    tint = CalendarColors.onSurface
                )
            }
            
            IconButton(onClick = onNextClick) {
                Icon(
                    imageVector = MaterialIcons.Default.ChevronRight,
                    contentDescription = "下个月",
                    tint = CalendarColors.onSurface
                )
            }
        }
    }
}

/**
 * 🎨 高级美化日期详情面板 - 参考Apple Calendar & Linear App设计
 * 设计特点：卡片式布局、现代排版、优雅的信息层次
 */
@Composable
fun DateDetailsPanel(
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    habits: List<HabitData>,
    viewModel: CalendarViewModel,
    habitViewModel: HabitViewModel,
    calendarViewContext: CalendarViewTypeContext = LocalCalendarViewType.current,
    modifier: Modifier = Modifier
) {
    val selectedDateEvents = events.toMutableList()
    val selectedDateHabits = habits
    val context = LocalContext.current
    
    var habitsVisible by remember { mutableStateOf(true) }
    var showCreateEventDialog by remember { mutableStateOf(false) }
    var newEventTitle by remember { mutableStateOf("") }
    var selectedColor by remember { mutableStateOf<Color?>(null) }
    var newEventDuration by remember { mutableStateOf(60) }
    
    // 🎨 主容器 - 现代卡片式设计
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 8.dp), // 从16dp减少到4dp
        color = Color.White,
        shape = RoundedCornerShape(24.dp),
        tonalElevation = 0.dp // 无阴影，现代极简
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp) // 水平边距从24dp减少到16dp
        ) {
            // 🎨 优雅的日期标题
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 20.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("M月d日", Locale.CHINA)),
                        fontSize = 20.sp, // 从24.sp减少到20.sp
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A202C),
                        letterSpacing = 0.3.sp // 从0.5.sp减少到0.3.sp
                    )
                    
                    Text(
                        text = selectedDate.format(DateTimeFormatter.ofPattern("EEEE", Locale.CHINA)),
                        fontSize = 12.sp, // 从14.sp减少到12.sp
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF718096),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                // 🎨 状态徽章
                Surface(
                                            color = Color(0xFFC4B5D4).copy(alpha = 0.1f),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text(
                        text = "${selectedDateEvents.size + selectedDateHabits.size}项",
                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp), // 减少padding
                        fontSize = 10.sp, // 从12.sp减少到10.sp
                        fontWeight = FontWeight.Medium,
                                                    color = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                    )
                }
            }
            
            // 🎨 习惯区域 - 现代折叠设计
            if (selectedDateHabits.isNotEmpty()) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(16.dp))
                        .clickable { habitsVisible = !habitsVisible },
                    color = Color(0xFFF7FAFC),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(8.dp)
                                        .clip(CircleShape)
                                        .background(Color(0xFF10B981))
                                )
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Text(
                                    text = "今日习惯",
                                    fontSize = 14.sp, // 从16.sp减少到14.sp
                                    fontWeight = FontWeight.SemiBold,
                                    color = Color(0xFF1A202C)
                                )
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                Text(
                                    text = "${selectedDateHabits.size}",
                                    fontSize = 12.sp, // 从14.sp减少到12.sp
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF718096)
                                )
                            }
                            
                            Icon(
                                imageVector = if (habitsVisible) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                contentDescription = null,
                                tint = Color(0xFF718096),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                        
                        AnimatedVisibility(
                            visible = habitsVisible,
                            enter = fadeIn(animationSpec = tween(200)) + expandVertically(animationSpec = tween(200)),
                            exit = fadeOut(animationSpec = tween(150)) + shrinkVertically(animationSpec = tween(150))
                        ) {
                            DailyHabitsSection(
                                selectedDate = selectedDate,
                                habits = selectedDateHabits,
                                habitViewModel = habitViewModel,
                                modifier = Modifier.padding(top = 12.dp)
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // 🎨 任务区域 - 现代列表设计
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF3B82F6))
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "今日任务",
                        fontSize = 14.sp, // 从16.sp减少到14.sp
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF1A202C)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "${selectedDateEvents.size}",
                        fontSize = 12.sp, // 从14.sp减少到12.sp
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF718096)
                    )
                }
            }
            
            // 🎨 事件列表或空状态
            if (selectedDateEvents.isEmpty()) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFF7FAFC),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.EventNote,
                            contentDescription = null,
                            tint = Color(0xFFCBD5E0),
                            modifier = Modifier.size(40.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "暂无安排",
                            fontSize = 12.sp, // 从14.sp减少到12.sp
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF718096)
                        )
                        
                        Text(
                            text = "今天还没有添加任务",
                            fontSize = 10.sp, // 从12.sp减少到10.sp
                            color = Color(0xFFA0AEC0),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = selectedDateEvents,
                        key = { event -> event.id }
                    ) { event ->
                        val eventColor = event.color?.let { colorLong -> 
                            try {
                                Color(colorLong)
                            } catch (e: Exception) {
                                Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                            }
                        } ?: Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                        
                        EnhancedEventItemCard(
                            event = event,
                            eventColor = eventColor,
                            onToggleCompletion = { isCompleted ->
                                viewModel.toggleEventCompletion(event.id, isCompleted)
                            },
                            onClick = {
                                // TODO: 实现编辑事件逻辑
                            }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 🎨 现代操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 添加任务按钮
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { showCreateEventDialog = true },
                    color = Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                    shape = RoundedCornerShape(14.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(vertical = 14.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(6.dp))
                        
                        Text(
                            text = "添加任务",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color.White
                        )
                    }
                }
                
                // 日视图按钮
                Surface(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { 
                            calendarViewContext.onCalendarViewTypeChange(CalendarViewType.DAY)
                        },
                    color = Color(0xFFF7FAFC),
                    shape = RoundedCornerShape(14.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(vertical = 14.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ViewDay,
                            contentDescription = null,
                            tint = Color(0xFFC4B5D4), // 改为莫兰迪薰衣草紫
                            modifier = Modifier.size(18.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(6.dp))
                        
                        Text(
                            text = "详细视图",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                        )
                    }
                }
            }
        }
    }
    
    // 创建事件对话框保持原样
    if (showCreateEventDialog) {
        AlertDialog(
            onDismissRequest = { 
                showCreateEventDialog = false 
                newEventTitle = ""
                selectedColor = null
            },
            title = { 
                Text(
                    text = "添加事件",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold
                ) 
            },
            text = {
                Column {
                    OutlinedTextField(
                        value = newEventTitle,
                        onValueChange = { newEventTitle = it },
                        label = { Text("事件标题") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = "持续时间: ${newEventDuration}分钟",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                    
                    Slider(
                        value = newEventDuration.toFloat(),
                        onValueChange = { newEventDuration = it.toInt() },
                        valueRange = 15f..240f,
                        steps = 14
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (newEventTitle.isNotBlank()) {
                            val eventColor = selectedColor ?: Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                            val startTime = LocalTime.of(9, 0)
                            val endTime = startTime.plusMinutes(newEventDuration.toLong())
                            
                            viewModel.createEvent(
                                title = newEventTitle,
                                start = LocalDateTime.of(selectedDate, startTime),
                                end = LocalDateTime.of(selectedDate, endTime),
                                color = eventColor.value.toLong()
                            )
                            
                            showCreateEventDialog = false
                            newEventTitle = ""
                            selectedColor = null
                        }
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showCreateEventDialog = false
                        newEventTitle = ""
                        selectedColor = null
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * Calendar View 类型上下文，用于在组件间共享日历视图类型状态与变更函数
 */
data class CalendarViewTypeContext(
    val calendarViewType: CalendarViewType,
    val onCalendarViewTypeChange: (CalendarViewType) -> Unit
)

/**
 * 提供 Calendar View 类型的 CompositionLocal
 */
val LocalCalendarViewType = compositionLocalOf {
    CalendarViewTypeContext(
        calendarViewType = CalendarViewType.MONTH,
        onCalendarViewTypeChange = { }
    )
}

// 状态栏高度的CompositionLocal
val LocalStatusBarHeight = compositionLocalOf { 24.dp }

// 获取状态栏高度的函数
@Composable
fun rememberStatusBarHeight(): Dp {
    val context = LocalContext.current
    val resources = context.resources
    val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
    return if (resourceId > 0) {
        with(LocalDensity.current) {
            resources.getDimensionPixelSize(resourceId).toDp()
        }
    } else {
        24.dp // 默认状态栏高度
    }
}

// 添加事件卡片组件
@Composable
private fun EventItemCard(
    event: CalendarEvent,
    eventColor: Color,
    onToggleCompletion: (Boolean) -> Unit,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = eventColor.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 事件完成状态
            Checkbox(
                checked = event.isCompleted,
                onCheckedChange = onToggleCompletion,
                colors = CheckboxDefaults.colors(
                    checkedColor = eventColor,
                    uncheckedColor = eventColor.copy(alpha = 0.5f)
                ),
                modifier = Modifier.padding(end = 8.dp)
            )
            
            Column(modifier = Modifier.weight(1f)) {
                // 事件标题
                Text(
                    text = event.title,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp, // 从15.sp减少到13.sp
                    color = Color.Black,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 时间显示
                val timeText = if (event.isAllDay == true) {
                    "全天"
                } else {
                    val startTime = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                    val endTime = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: 
                        event.start.plusMinutes(15).format(DateTimeFormatter.ofPattern("HH:mm"))
                    "$startTime - $endTime"
                }
                
                Text(
                    text = timeText,
                    fontSize = 13.sp,
                    color = Color.DarkGray,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // 紧急程度指示器
            if (event.urgency != TaskUrgency.MEDIUM) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .border(0.5.dp, Color.White, CircleShape)
                        .clip(CircleShape)
                        .background(
                            when(event.urgency) {
                                TaskUrgency.CRITICAL -> Color.Red
                                TaskUrgency.HIGH -> Color(0xFFF57C00)
                                TaskUrgency.MEDIUM -> Color(0xFFFBC02D)
                                else -> Color.Transparent
                            }
                        )
                )
            }
        }
    }
}

/**
 * 创建事件对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CreateEventDialog(
    startTime: LocalDateTime,
    endTime: LocalDateTime,
    onDismiss: () -> Unit,
    onCreateEvent: (String, String, LocalDateTime, LocalDateTime) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var eventStartTime by remember { mutableStateOf(startTime) }
    var eventEndTime by remember { mutableStateOf(endTime) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Text(
                    text = "添加新事件",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 事件标题输入
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("事件标题") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 事件描述输入
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("备注（可选）") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 时间显示
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFF5F5F5),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "时间",
                            style = MaterialTheme.typography.labelMedium,
                            color = Color.Gray
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "${eventStartTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))} - ${eventEndTime.format(DateTimeFormatter.ofPattern("HH:mm"))}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Black
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("取消", color = Color.Gray)
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                onCreateEvent(title, description, eventStartTime, eventEndTime)
                            }
                        },
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                        )
                    ) {
                        Text("创建", color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 简化的拖拽状态管理 - 添加调试功能
 */
@Composable
fun rememberDragDropState(): DragDropState {
    return remember { DragDropState() }
}

class DragDropState {
    var isDragging = mutableStateOf(false)
    var draggedTask = mutableStateOf<Task?>(null)
    var isOverDropZone = mutableStateOf(false)
    
    fun startDrag(task: Task) {
        Log.d("DragState", "开始拖拽任务: ${task.title}")
        isDragging.value = true
        draggedTask.value = task
    }
    
    fun setOverDropZone(isOver: Boolean) {
        Log.d("DragState", "设置投放区域状态: $isOver")
        isOverDropZone.value = isOver
    }
    
    fun completeDrop(): Task? {
        val task = draggedTask.value
        Log.d("DragState", "完成投放: ${task?.title}")
        clearDrag()
        return task
    }
    
    fun clearDrag() {
        Log.d("DragState", "清除拖拽状态")
        isDragging.value = false
        draggedTask.value = null
        isOverDropZone.value = false
    }
}

/**
 * 统一的任务容器 - 解决拖拽问题
 * 特点：
 * 1. 本周任务和今日任务在同一个拖拽容器中
 * 2. 支持跨区域拖拽
 * 3. 精细化交互：左侧图标拖拽，中心区域删除
 * 4. 流畅的视觉反馈
 */
@Composable
private fun DragAndDropContainer(
    floatingTasks: List<Task>,
    selectedDateEvents: List<CalendarEvent>,
    selectedDate: LocalDate,
    weekDates: List<LocalDate>,
    viewModel: CalendarViewModel,
    onCreateFloatingTask: () -> Unit
) {
    val dragDropState = rememberDragDropState()
    
    // 🎨 统一任务容器
    UnifiedTaskContainer(
        floatingTasks = floatingTasks,
        todayEvents = selectedDateEvents,
        selectedDate = selectedDate,
        dragDropState = dragDropState,
        onCreateFloatingTask = onCreateFloatingTask,
        onDeleteFloatingTask = { task ->
            viewModel.deleteFloatingTask(task.id)
        },
        onDeleteTodayTask = { event ->
            // 🔧 修复删除逻辑：从事件ID中提取真实任务ID
            val taskId = if (event.id.startsWith("task_")) {
                event.id.removePrefix("task_")
            } else {
                event.taskId ?: event.id
            }
            Log.d("TaskDelete", "🗑️ 删除今日任务: eventId=${event.id}, taskId=$taskId")
            viewModel.deleteTask(taskId)
        },
        onTaskDropped = { task ->
            // 从周任务拖拽到今日任务
            Log.d("TaskDrop", "🚀 开始处理任务拖拽: ${task.title}")
            val scheduledDateTime = selectedDate.atTime(9, 0)
            
            // 立即调用ViewModel的方法
            viewModel.scheduleFloatingTask(task.id, scheduledDateTime)
            
            // 强制刷新数据
            viewModel.refreshCalendarData()
            
            Log.d("TaskDrop", "✅ 任务拖拽处理完成，已触发刷新")
        }
    )
}

/**
 * 统一任务容器 - 简化的拖拽实现，取消阴影效果
 */
@Composable
private fun UnifiedTaskContainer(
    floatingTasks: List<Task>,
    todayEvents: List<CalendarEvent>,
    selectedDate: LocalDate,
    dragDropState: DragDropState,
    onCreateFloatingTask: () -> Unit,
    onDeleteFloatingTask: (Task) -> Unit,
    onDeleteTodayTask: (CalendarEvent) -> Unit,
    onTaskDropped: (Task) -> Unit
) {
    // 🔧 删除对话框状态
    var showDeleteFloatingTaskDialog by remember { mutableStateOf(false) }
    var showDeleteTodayTaskDialog by remember { mutableStateOf(false) }
    var floatingTaskToDelete by remember { mutableStateOf<Task?>(null) }
    var todayTaskToDelete by remember { mutableStateOf<CalendarEvent?>(null) }
    
    // 🔄 本地刷新状态跟踪
    var refreshTrigger by remember { mutableStateOf(0) }
    var localTodayEvents by remember { mutableStateOf(todayEvents) }
    var localFloatingTasks by remember { mutableStateOf(floatingTasks) }
    
    // 🔧 删除任务黑名单：记录已被用户删除的任务ID，防止重新出现
    var deletedTaskIds by remember { mutableStateOf(setOf<String>()) }
    
    // 🔧 新增任务白名单：记录用户拖拽新增的任务ID，确保立即显示
    var addedTaskIds by remember { mutableStateOf(setOf<String>()) }
    
    // 🔧 智能监听外部数据变化 - 避免覆盖用户操作的即时反馈
    LaunchedEffect(todayEvents, floatingTasks, refreshTrigger) {
        // 只有在初始化或者确认数据库同步完成后才更新本地状态
        // 避免覆盖用户拖拽操作的即时反馈
        if (refreshTrigger == 0 || refreshTrigger % 2 == 0) {
            // 🔧 数据完整性检查：智能处理新增和删除操作
            val shouldUpdate = when {
                refreshTrigger == 0 -> true // 初始化时总是同步
                deletedTaskIds.isNotEmpty() || addedTaskIds.isNotEmpty() -> {
                    // 有操作时，需要智能合并本地状态和外部数据
                    Log.d("TaskContainer", "🔄 检测到用户操作，进行智能同步 - 删除: ${deletedTaskIds.size}, 新增: ${addedTaskIds.size}")
                    true
                }
                todayEvents.size >= localTodayEvents.size -> true // 外部数据更完整
                else -> {
                    Log.d("TaskContainer", "⚠️ 外部数据不完整，保持本地状态 - 外部: ${todayEvents.size}, 本地: ${localTodayEvents.size}")
                    false
                }
            }
            
            if (shouldUpdate) {
                // 🔧 详细记录同步前后的数据变化
                Log.d("TaskContainer", "📊 同步前本地状态:")
                localTodayEvents.forEachIndexed { index, event ->
                    Log.d("TaskContainer", "  本地[$index] ${event.title} (ID: ${event.id})")
                }
                Log.d("TaskContainer", "📊 外部数据状态:")
                todayEvents.forEachIndexed { index, event ->
                    Log.d("TaskContainer", "  外部[$index] ${event.title} (ID: ${event.id})")
                }
                
                // 🔧 智能合并：保留新增任务，过滤删除任务
                val smartMergedEvents = mutableListOf<CalendarEvent>()
                
                // 1. 添加白名单中的新增任务（从本地状态保留）
                localTodayEvents.forEach { event ->
                    val taskId = event.taskId ?: event.id.removePrefix("task_")
                    if (addedTaskIds.contains(taskId) || addedTaskIds.contains(event.id)) {
                        smartMergedEvents.add(event)
                        Log.d("TaskContainer", "✅ 保留新增任务: ${event.title} (ID: ${event.id})")
                    }
                }
                
                // 2. 添加外部数据中的其他任务（过滤掉删除的和重复的）
                todayEvents.forEach { event ->
                    val taskId = event.taskId ?: event.id.removePrefix("task_")
                    val isDeleted = deletedTaskIds.contains(taskId) || deletedTaskIds.contains(event.id)
                    val isAlreadyAdded = smartMergedEvents.any { 
                        it.id == event.id || 
                        (it.taskId == event.taskId && event.taskId != null) 
                    }
                    
                    when {
                        isDeleted -> {
                            Log.d("TaskContainer", "🚫 过滤已删除任务: ${event.title} (ID: ${event.id})")
                        }
                        isAlreadyAdded -> {
                            Log.d("TaskContainer", "⏭️ 跳过重复任务: ${event.title} (ID: ${event.id})")
                        }
                        else -> {
                            smartMergedEvents.add(event)
                            Log.d("TaskContainer", "📥 添加外部任务: ${event.title} (ID: ${event.id})")
                        }
                    }
                }
                
                // 3. 过滤浮动任务
                val filteredFloatingTasks = floatingTasks.filter { task ->
                    val isDeleted = deletedTaskIds.contains(task.id)
                    if (isDeleted) {
                        Log.d("TaskContainer", "🚫 过滤已删除浮动任务: ${task.title} (ID: ${task.id})")
                    }
                    !isDeleted
                }
                
                localTodayEvents = smartMergedEvents
                localFloatingTasks = filteredFloatingTasks
                Log.d("TaskContainer", "🔄 智能合并完成 - 今日事件: ${smartMergedEvents.size}, 浮动任务: ${filteredFloatingTasks.size}")
                
                // 同步后再次确认状态
                Log.d("TaskContainer", "📊 同步后最终状态:")
                localTodayEvents.forEachIndexed { index, event ->
                    Log.d("TaskContainer", "  最终[$index] ${event.title} (ID: ${event.id})")
                }
            }
        } else {
            Log.d("TaskContainer", "🚫 跳过外部数据同步，保护用户操作 - refreshTrigger: $refreshTrigger")
        }
    }
    
    val isDragging by dragDropState.isDragging
    val draggedTask by dragDropState.draggedTask
    val isOverDropZone by dragDropState.isOverDropZone
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        // 📋 本周任务区域
        TaskGroupSection(
            title = "本周任务 (${localFloatingTasks.size})",
            onAddClick = onCreateFloatingTask
        ) {
            if (localFloatingTasks.isEmpty()) {
                EmptyTaskPrompt(
                    text = "点击添加本周计划",
                    onClick = onCreateFloatingTask
                )
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 200.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    contentPadding = PaddingValues(vertical = 4.dp)
                ) {
                    itemsIndexed(
                        items = localFloatingTasks,
                        key = { _, task -> "floating_${task.id}_$refreshTrigger" }
                    ) { index, task ->
                        SimpleDraggableTaskCard(
                            task = task,
                            onStartDrag = { dragDropState.startDrag(task) },
                            onLongPressCenter = { selectedTask ->
                                floatingTaskToDelete = selectedTask
                                showDeleteFloatingTaskDialog = true
                            }
                        )
                    }
                }
            }
        }
        
        // 分隔线
        HorizontalDivider(
            modifier = Modifier.padding(vertical = 8.dp),
            color = Color.LightGray.copy(alpha = 0.3f)
        )
        
        // 📋 今日任务区域 - TickTick风格的拖拽目标区域
        TaskGroupSection(
            title = "今日任务 (${localTodayEvents.size})",
            showAddButton = false
        ) {
            TickTickStyleDropZone(
                events = localTodayEvents, // 🔧 使用本地状态，确保立即更新
                selectedDate = selectedDate,
                draggedTask = draggedTask,
                onTaskDrop = { task ->
                    // 处理拖拽完成
                    Log.d("TaskContainer", "🎯 任务投放: ${task.title}")
                    
                    // 🔧 立即进行本地状态更新，提供即时反馈
                    val droppedTaskEvent = CalendarEvent(
                        id = "task_${task.id}",
                        title = task.title,
                        start = selectedDate.atTime(9, 0),
                        end = selectedDate.atTime(18, 0),
                        color = when (task.priority) {
                            Priority.URGENT -> Color(0xFFFF4444).value.toLong()
                            Priority.HIGH -> Color(0xFFFF8800).value.toLong()
                            Priority.MEDIUM -> Color(0xFFFFBB33).value.toLong()
                            Priority.LOW -> Color(0xFF00C851).value.toLong()
                            null -> Color(0xFF776993).value.toLong()
                        },
                        isCompleted = false,
                        taskId = task.id
                    )
                    
                    // ✅ 立即更新本地状态，确保UI即时反馈
                    localTodayEvents = localTodayEvents + droppedTaskEvent
                    localFloatingTasks = localFloatingTasks.filter { it.id != task.id }
                    
                    // 🔧 将新拖入的任务添加到白名单，确保不被外部数据同步时删除
                    addedTaskIds = addedTaskIds + task.id + droppedTaskEvent.id
                    
                    refreshTrigger += 1 // 奇数标记：立即更新
                    
                    Log.d("TaskContainer", "🔄 立即更新本地UI状态: 今日事件+1(${localTodayEvents.size}), 浮动任务-1(${localFloatingTasks.size})")
                    Log.d("TaskContainer", "✅ 新增任务事件ID: ${droppedTaskEvent.id}, 任务ID: ${task.id}")
                    Log.d("TaskContainer", "🔧 任务已添加到白名单: $addedTaskIds")
                    Log.d("TaskContainer", "🎯 今日任务区域应立即显示新任务: ${task.title}")
                    Log.d("TaskContainer", "📊 本地今日事件列表:")
                    localTodayEvents.forEachIndexed { index, event ->
                        Log.d("TaskContainer", "  [$index] ${event.title} (ID: ${event.id})")
                    }
                    
                    // 完成拖拽状态
                    dragDropState.completeDrop()
                    
                    // 后台执行数据库更新
                    onTaskDropped(task)
                    
                    // 🔧 多重验证确保数据库同步完成
                    kotlinx.coroutines.GlobalScope.launch {
                        var verificationAttempts = 0
                        val maxAttempts = 4
                        val expectedTaskCount = localTodayEvents.size
                        
                        Log.d("TaskContainer", "🔍 开始验证数据库同步 - 期望任务数: $expectedTaskCount")
                        
                        while (verificationAttempts < maxAttempts) {
                            kotlinx.coroutines.delay(400) // 每400ms检查一次
                            verificationAttempts++
                            
                            Log.d("TaskContainer", "🔍 数据库同步验证 - 尝试 $verificationAttempts/$maxAttempts")
                            
                            // 经过足够的等待时间后，允许外部数据同步
                            if (verificationAttempts >= 3) {
                                refreshTrigger += 1 // 偶数标记：允许外部数据同步
                                
                                // 🔧 清理白名单：拖拽任务已稳定，不再需要特殊保护
                                kotlinx.coroutines.delay(1000) // 再等1秒确保UI稳定
                                addedTaskIds = emptySet()
                                
                                Log.d("TaskContainer", "✅ 数据库同步验证完成，允许外部数据更新")
                                Log.d("TaskContainer", "🧹 已清理新增任务白名单")
                                break
                            }
                        }
                        
                        if (verificationAttempts >= maxAttempts) {
                            // 超时保护：避免永久阻塞
                            refreshTrigger += 1
                            addedTaskIds = emptySet() // 清理白名单
                            Log.d("TaskContainer", "⏰ 同步验证超时，强制允许外部数据更新")
                            Log.d("TaskContainer", "🧹 已清理新增任务白名单（超时）")
                        }
                    }
                },
                onEventLongPress = { event ->
                    todayTaskToDelete = event
                    showDeleteTodayTaskDialog = true
                }
            )
        }
    }
    
    // 删除对话框
    if (showDeleteFloatingTaskDialog && floatingTaskToDelete != null) {
        TaskDeleteConfirmDialog(
            taskTitle = floatingTaskToDelete!!.title,
            onConfirm = {
                val taskToDelete = floatingTaskToDelete!!
                
                // 🔧 加入删除黑名单，防止重新出现
                deletedTaskIds = deletedTaskIds + taskToDelete.id
                
                // 🔧 立即从本地状态中移除任务，提供即时反馈
                localFloatingTasks = localFloatingTasks.filter { it.id != taskToDelete.id }
                refreshTrigger += 1 // 奇数标记：立即更新
                
                Log.d("TaskContainer", "🗑️ 浮动任务已从UI中移除并加入黑名单: ${taskToDelete.title} (ID: ${taskToDelete.id})")
                Log.d("TaskContainer", "📋 当前删除黑名单: $deletedTaskIds")
                
                // 然后执行数据库删除
                onDeleteFloatingTask(taskToDelete)
                showDeleteFloatingTaskDialog = false
                floatingTaskToDelete = null
                
                // 确保数据库删除完成后允许外部数据同步
                kotlinx.coroutines.GlobalScope.launch {
                    kotlinx.coroutines.delay(1000) // 增加延迟时间到1秒
                    refreshTrigger += 1 // 偶数标记：允许外部数据同步
                    Log.d("TaskContainer", "✅ 浮动任务删除完成，允许外部数据同步")
                }
            },
            onDismiss = {
                showDeleteFloatingTaskDialog = false
                floatingTaskToDelete = null
            }
        )
    }
    
    if (showDeleteTodayTaskDialog && todayTaskToDelete != null) {
        EventDeleteConfirmDialog(
            eventTitle = todayTaskToDelete!!.title,
            onConfirm = {
                val eventToDelete = todayTaskToDelete!!
                
                // 🔧 加入删除黑名单，防止重新出现
                val taskId = eventToDelete.taskId ?: eventToDelete.id.removePrefix("task_")
                deletedTaskIds = deletedTaskIds + taskId + eventToDelete.id
                
                // 🔧 立即从本地状态中移除事件，提供即时反馈
                localTodayEvents = localTodayEvents.filter { it.id != eventToDelete.id }
                refreshTrigger += 1 // 奇数标记：立即更新
                
                Log.d("TaskContainer", "🗑️ 今日任务从本地状态移除并加入黑名单: ${eventToDelete.title}")
                Log.d("TaskContainer", "📊 移除后本地事件数: ${localTodayEvents.size}")
                Log.d("TaskContainer", "📋 当前删除黑名单: $deletedTaskIds")
                
                // 然后执行数据库删除
                onDeleteTodayTask(eventToDelete)
                showDeleteTodayTaskDialog = false
                todayTaskToDelete = null
                
                // 确保数据库删除完成后允许外部数据同步
                kotlinx.coroutines.GlobalScope.launch {
                    kotlinx.coroutines.delay(1000) // 增加延迟时间到1秒
                    refreshTrigger += 1 // 偶数标记：允许外部数据同步
                    Log.d("TaskContainer", "✅ 今日任务删除完成，允许外部数据同步")
                }
            },
            onDismiss = {
                showDeleteTodayTaskDialog = false
                todayTaskToDelete = null
            }
        )
    }
}

/**
 * 任务分组区域标题
 */
@Composable
private fun TaskGroupSection(
    title: String,
    showAddButton: Boolean = true,
    onAddClick: (() -> Unit)? = null,
    content: @Composable () -> Unit
) {
    Column {
        // 📋 标题栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = CalendarColors.primary
            )
            
            if (showAddButton && onAddClick != null) {
                // ➕ 添加任务按钮
                Surface(
                    onClick = onAddClick,
                    shape = CircleShape,
                    color = CalendarColors.primary.copy(alpha = 0.1f),
                    modifier = Modifier.size(20.dp)
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加任务",
                            tint = CalendarColors.primary,
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
            }
        }
        
        // 内容区域
        content()
    }
}

/**
 * 空任务提示
 */
@Composable
private fun EmptyTaskPrompt(
    text: String,
    onClick: (() -> Unit)?
) {
    Surface(
        onClick = onClick ?: {},
        shape = RoundedCornerShape(8.dp),
        color = Color.Gray.copy(alpha = 0.05f),
        border = BorderStroke(1.dp, Color.Gray.copy(alpha = 0.2f)),
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
    ) {
        Box(contentAlignment = Alignment.Center) {
            Text(
                text = text,
                fontSize = 11.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 拖拽提示卡片
 */
@Composable
private fun DropHintCard(
    text: String,
    isActive: Boolean
) {
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = if (isActive) {
            CalendarColors.primary.copy(alpha = 0.2f)
        } else {
            CalendarColors.primary.copy(alpha = 0.1f)
        },
        border = BorderStroke(
            width = 2.dp,
            color = if (isActive) {
                CalendarColors.primary
            } else {
                CalendarColors.primary.copy(alpha = 0.3f)
            }
        ),
        modifier = Modifier
            .fillMaxWidth()
            .height(50.dp)
    ) {
        Box(contentAlignment = Alignment.Center) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    tint = CalendarColors.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = text,
                    fontSize = 12.sp,
                    color = CalendarColors.primary,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 增强版可拖拽卡片 - 精细化交互
 */
@Composable
private fun EnhancedDraggableCard(
    task: Task,
    dragDropState: DragDropState,
    onDragFromIcon: (Task) -> Unit,
    onLongPressCenter: (Task) -> Unit = {}
) {
    val isDragging by dragDropState.isDragging
    val currentDraggedTask by dragDropState.draggedTask
    val isCurrentlyDragging = isDragging && currentDraggedTask?.id == task.id
    val haptic = LocalHapticFeedback.current
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer(
                scaleX = if (isCurrentlyDragging) 0.95f else 1f,
                scaleY = if (isCurrentlyDragging) 0.95f else 1f,
                alpha = if (isCurrentlyDragging) 0.8f else 1f
            ),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
        shadowElevation = if (isCurrentlyDragging) 8.dp else 2.dp,
        border = BorderStroke(0.5.dp, Color.Gray.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .pointerInput(task.id) {
                    detectTapGestures(
                        onLongPress = { offset ->
                            // 检查长按位置是否在左侧拖拽图标区域（左侧48dp）
                            if (offset.x <= 48.dp.toPx()) {
                                // 长按左侧图标 - 开始拖拽
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                dragDropState.startDrag(task)
                                onDragFromIcon(task)
                            } else {
                                // 长按中心区域 - 删除
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                onLongPressCenter(task)
                            }
                        }
                    )
                }
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🎯 左侧拖拽图标区域
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(
                        Color.Gray.copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.DragHandle,
                    contentDescription = "拖拽任务",
                    tint = Color.Gray.copy(alpha = 0.6f),
                    modifier = Modifier.size(16.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 📝 任务内容区域（中心区域）
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black.copy(alpha = 0.8f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (task.description?.isNotBlank() == true) {
                    Text(
                        text = task.description!!,
                        fontSize = 11.sp,
                        color = Color.Gray,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 🏷️ 优先级标识
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = when (task.priority) {
                            Priority.URGENT -> Color(0xFFFF4444)
                            Priority.HIGH -> Color(0xFFFF8800)
                            Priority.MEDIUM -> Color(0xFFFFBB33)
                            Priority.LOW -> Color(0xFF00C851)
                            null -> Color(0xFF9CA3AF) // 默认灰色
                        },
                        shape = CircleShape
                    )
            )
        }
    }
}

// 废弃的组件已被UnifiedTaskContainer替代

/**
 * 事件删除确认对话框
 */
@Composable
private fun EventDeleteConfirmDialog(
    event: CalendarEvent,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = Color(0xFFFF9500),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "确认删除",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        },
        text = {
            Column {
                Text(
                    text = "您确定要删除这个任务吗？",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
                Spacer(modifier = Modifier.height(8.dp))
                
                // 显示任务信息
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    color = Color.Gray.copy(alpha = 0.1f)
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = event.title,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        // 显示时间信息
                        Text(
                            text = if (event.isAllDay == true) {
                                "全天任务"
                            } else {
                                val startTime = event.start.format(DateTimeFormatter.ofPattern("HH:mm"))
                                val endTime = event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: "?"
                                "$startTime - $endTime"
                            },
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "删除后无法恢复，请谨慎操作。",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Red.copy(alpha = 0.7f)
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Red
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("删除", color = Color.White)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消", color = Color.Gray)
            }
        },
        containerColor = Color.White,
        shape = RoundedCornerShape(16.dp)
    )
}

/**
 * 任务删除确认对话框
 */
@Composable
private fun TaskDeleteConfirmDialog(
    taskTitle: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("删除任务")
        },
        text = {
            Text("确定要删除任务「$taskTitle」吗？此操作无法撤销。")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(contentColor = Color.Red)
            ) {
                Text("删除")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        containerColor = Color(0xFFFAFAFA) // 🎨 修改为用户指定的背景色 #fafafa
    )
}

/**
 * TickTick风格的任务卡片 - 支持直接拖拽
 */
@Composable
private fun TickTickStyleTaskCard(
    task: Task,
    index: Int,
    section: String,
    dragState: Any,
    isCompleted: Boolean = false,
    modifier: Modifier = Modifier
) {
    var isDragging by remember { mutableStateOf(false) }
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                // 🎨 拖拽时的视觉效果
                scaleX = if (isDragging) 1.02f else 1f,
                scaleY = if (isDragging) 1.02f else 1f,
                alpha = if (isDragging) 0.9f else 1f
                // 移除shadowElevation，因为graphicsLayer不支持这个参数
            )
            .pointerInput(task.id) {
                detectDragGestures(
                    onDragStart = { 
                        isDragging = true
                        // dragState.startDrag(task, section, index)
                    },
                    onDragEnd = { 
                        isDragging = false
                        // dragState.endDrag()
                    }
                ) { _, _ ->
                    // 拖拽过程中的处理
                }
            },
        color = if (isDragging) 
            Color(0xFFE3F2FD)
        else 
            Color.White,
        shadowElevation = if (isDragging) 4.dp else 0.dp,
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(
            width = if (isDragging) 2.dp else 1.dp,
            color = if (isDragging) 
                Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
            else 
                Color(0xFFE0E0E0).copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 拖拽手柄
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "拖拽",
                tint = Color(0xFF9CA3AF),
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 完成状态
            Checkbox(
                checked = isCompleted,
                onCheckedChange = { /* TODO: 处理完成状态变更 */ },
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任务内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (isCompleted) 
                        Color(0xFF6B7280) 
                    else 
                        Color(0xFF1F2937),
                    textDecoration = if (isCompleted) 
                        TextDecoration.LineThrough 
                    else 
                        TextDecoration.None
                )
                
                if (task.description.isNotEmpty()) {
                    Text(
                        text = task.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF6B7280),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        when (task.priority) {
                            Priority.URGENT -> Color(0xFFE53E3E)
                            Priority.HIGH -> Color(0xFFFF8247)
                            Priority.MEDIUM -> Color(0xFFd9b335)
                            Priority.LOW -> Color(0xFFA8C986)
                            else -> Color(0xFF9CA3AF)
                        },
                        CircleShape
                    )
            )
        }
    }
}

/**
 * 拖拽目标区域
 */
@Composable
private fun DropTargetArea(
    section: String,
    index: Int = -1,
    dragState: Any,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isHovered by remember { mutableStateOf(false) }
    
    Surface(
        modifier = modifier
            .background(
                color = if (isHovered) 
                    Color(0xFFC4B5D4).copy(alpha = 0.1f) // 莫兰迪薰衣草紫
                else 
                    Color.Transparent
            )
            .border(
                width = if (isHovered) 2.dp else 1.dp,
                color = if (isHovered) 
                    Color(0xFFC4B5D4) // 莫兰迪薰衣草紫
                else 
                    Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            ),
        color = Color.Transparent,
        shape = RoundedCornerShape(8.dp)
    ) {
        content()
    }
}

/**
 * TickTick风格任务卡片
 * 特点：
 * 1. 直接拖拽卡片本身，无额外预览
 * 2. 拖拽时提供视觉反馈（缩放、透明度）
 * 3. 清晰的拖拽手柄
 * 4. 任务完成状态切换
 */
@Composable
private fun TickTickStyleTaskCard(
    task: Task,
    isFloating: Boolean,
    isDragging: Boolean,
    onDragStart: () -> Unit,
    onDragEnd: () -> Unit,
    onTaskToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isDragging) 0.95f else 1f,
        animationSpec = tween(150),
        label = "scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.8f else 1f,
        animationSpec = tween(150),
        label = "alpha"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
                this.alpha = alpha
            }
            .pointerInput(task.id) {
                detectDragGestures(
                    onDragStart = { onDragStart() },
                    onDragEnd = { onDragEnd() },
                    onDrag = { _, _ -> /* 拖拽过程中的处理 */ }
                )
            },
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
        shadowElevation = if (isDragging) 8.dp else 2.dp,
        border = BorderStroke(0.5.dp, Color.Gray.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🎯 拖拽手柄
            DragHandle(
                modifier = Modifier.padding(end = 8.dp),
                isActive = !isDragging
            )
            
            // ✅ 完成状态复选框
            Checkbox(
                checked = task.isCompleted ?: false,
                onCheckedChange = onTaskToggle,
                modifier = Modifier.size(18.dp),
                colors = CheckboxDefaults.colors(
                    checkedColor = CalendarColors.primary,
                    uncheckedColor = Color.Gray.copy(alpha = 0.6f)
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 📝 任务内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (task.isCompleted == true) 
                        Color.Gray.copy(alpha = 0.6f) 
                    else 
                        Color.Black,
                    textDecoration = if (task.isCompleted == true) 
                        TextDecoration.LineThrough 
                    else 
                        TextDecoration.None,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (task.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = task.description,
                        fontSize = 11.sp,
                        color = Color.Gray,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 时间显示
                if (isFloating) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "本周完成",
                        fontSize = 10.sp,
                        color = CalendarColors.primary.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 优先级指示器
            if (task.priority != Priority.MEDIUM) {
                Surface(
                    shape = CircleShape,
                    color = when (task.priority) {
                        Priority.LOW -> Color(0xFF10B981).copy(alpha = 0.1f)
                        Priority.HIGH -> Color(0xFFEF4444).copy(alpha = 0.1f)
                        Priority.URGENT -> Color(0xFFDC2626).copy(alpha = 0.2f)
                        else -> Color.Transparent
                    },
                    modifier = Modifier.size(6.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                when (task.priority) {
                                    Priority.LOW -> Color(0xFF10B981)
                                    Priority.HIGH -> Color(0xFFEF4444)
                                    Priority.URGENT -> Color(0xFFDC2626)
                                    else -> Color.Transparent
                                },
                                CircleShape
                            )
                    )
                }
            }
        }
    }
}

/**
 * 拖拽手柄组件
 */
@Composable
private fun DragHandle(
    modifier: Modifier = Modifier,
    isActive: Boolean = true
) {
    Icon(
        imageVector = Icons.Default.DragHandle,
        contentDescription = "拖拽",
        tint = if (isActive) Color.Gray.copy(alpha = 0.6f) else Color.Gray.copy(alpha = 0.3f),
        modifier = modifier.size(16.dp)
    )
}

/**
 * 新的拖拽目标区域
 */
@Composable
private fun DropTargetArea(
    isActive: Boolean,
    onDrop: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit = {}
) {
    var isHovered by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier
            .pointerInput(isActive) {
                if (isActive) {
                    detectDragGestures(
                        onDragStart = { isHovered = true },
                        onDragEnd = { 
                            if (isHovered) {
                                onDrop()
                            }
                            isHovered = false
                        },
                                                 onDrag = { _, _ -> /* 检测是否在目标区域内 */ }
                    )
                }
            }
            .background(
                if (isActive && isHovered) 
                    CalendarColors.primary.copy(alpha = 0.1f) 
                else 
                    Color.Transparent,
                RoundedCornerShape(8.dp)
            )
            .border(
                width = if (isActive && isHovered) 2.dp else 0.dp,
                color = CalendarColors.primary.copy(alpha = 0.5f),
                shape = RoundedCornerShape(8.dp)
            )
    ) {
        content()
        
        // 插入位置指示器
        if (isActive && isHovered) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(2.dp)
                    .background(CalendarColors.primary)
                    .align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 可拖拽的任务卡片 - 核心拖拽组件
 * 特点：
 * 1. 长按启动拖拽
 * 2. 拖拽时显示半透明状态
 * 3. 支持跨区域拖拽
 * 4. 实时触觉反馈
 * 5. 长按删除功能
 */
@Composable
private fun SimpleDraggableCard(
    task: Task,
    dragDropState: DragDropState,
    onLongPress: (Task) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val isDragging by dragDropState.isDragging
    val currentDraggedTask by dragDropState.draggedTask
    val isCurrentlyDragging = isDragging && currentDraggedTask?.id == task.id
    
    // 🔧 添加长按状态管理
    var isLongPressed by remember { mutableStateOf(false) }
    val haptic = LocalHapticFeedback.current
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                scaleX = if (isCurrentlyDragging) 0.95f else if (isLongPressed) 0.98f else 1f,
                scaleY = if (isCurrentlyDragging) 0.95f else if (isLongPressed) 0.98f else 1f,
                alpha = if (isCurrentlyDragging) 0.8f else 1f
            )
            .pointerInput(task.id) {
                detectDragGestures(
                    onDragStart = { 
                        dragDropState.startDrag(task)
                    },
                    onDragEnd = {
                        // 拖拽结束时，状态在目标区域处理
                    }
                ) { _, _ -> 
                    // 拖拽中
                }
            }
            .pointerInput(task.id) {
                // 🔧 添加长按检测
                detectTapGestures(
                    onLongPress = {
                        isLongPressed = true
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPress(task)
                        isLongPressed = false
                    }
                )
            },
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
        shadowElevation = if (isCurrentlyDragging) 8.dp else 2.dp,
        border = BorderStroke(
            width = if (isCurrentlyDragging) 2.dp else 0.5.dp,
            color = if (isCurrentlyDragging) CalendarColors.primary else Color.Gray.copy(alpha = 0.2f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🏃 拖拽手柄
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "拖拽",
                tint = Color.Gray.copy(alpha = 0.5f),
                modifier = Modifier.size(16.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        when (task.priority) {
                            Priority.URGENT -> Color(0xFFE53E3E)    // 红色 - 紧急
                            Priority.HIGH -> Color(0xFFFF8247)      // 橙色 - 高优先级
                            Priority.MEDIUM -> Color(0xFFFFB800)    // 黄色 - 中优先级
                            Priority.LOW -> Color(0xFF48BB78)       // 绿色 - 低优先级
                            null -> Color(0xFF9CA3AF)               // 灰色 - 无优先级
                        },
                        CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(2.dp))
                
                Text(
                    text = "本周完成",
                    fontSize = 11.sp,
                    color = Color.Gray
                )
            }
            
            // 完成状态复选框
            Checkbox(
                checked = task.isCompleted,
                onCheckedChange = { /* 处理完成状态 */ },
                modifier = Modifier.size(18.dp)
            )
        }
    }
}



/**
 * 简化的拖拽提示
 */
@Composable
private fun SimpleDropHint() {
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = CalendarColors.primary.copy(alpha = 0.1f),
        border = BorderStroke(1.dp, CalendarColors.primary.copy(alpha = 0.3f)),
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
    ) {
        Box(contentAlignment = Alignment.Center) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = null,
                    tint = CalendarColors.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "松开以安排到今日",
                    fontSize = 12.sp,
                    color = CalendarColors.primary
                )
            }
        }
    }
}

/**
 * TickTick风格的拖拽目标区域
 * 支持自动重排序和视觉反馈
 */
@Composable
private fun TickTickStyleDropZone(
    isDragOver: Boolean,
    onDrop: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = {
                        if (isDragOver) {
                            onDrop()
                        }
                    }
                )
            }
            .background(
                color = if (isDragOver) {
                    CalendarColors.primary.copy(alpha = 0.08f)
                } else {
                    Color.Transparent
                },
                shape = RoundedCornerShape(12.dp)
            )
            .border(
                width = if (isDragOver) 2.dp else 0.dp,
                color = if (isDragOver) {
                    CalendarColors.primary.copy(alpha = 0.4f)
                } else {
                    Color.Transparent
                },
                shape = RoundedCornerShape(12.dp)
            )
            .padding(8.dp)
    ) {
        content()
        
        // 拖拽时的视觉提示
        if (isDragOver) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
                    .padding(8.dp)
            ) {
                Surface(
                    shape = RoundedCornerShape(20.dp),
                    color = CalendarColors.primary.copy(alpha = 0.9f),
                    modifier = Modifier.align(Alignment.Center)
                ) {
                    Row(
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "松开添加到今日",
                            fontSize = 12.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 增强的今日任务卡片，支持位置排序
 */
@Composable
private fun TodayTaskCard(
    event: CalendarEvent,
    onLongPressCenter: (CalendarEvent) -> Unit
) {
    val haptic = LocalHapticFeedback.current
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .pointerInput("center_${event.id}") {
                detectTapGestures(
                    onLongPress = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPressCenter(event)
                    }
                )
            },
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
        shadowElevation = 0.dp,
        border = BorderStroke(0.5.dp, Color.Gray.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 📅 时间信息
            Column {
                Text(
                    text = event.start.format(DateTimeFormatter.ofPattern("HH:mm")),
                    fontSize = 11.sp,
                    color = CalendarColors.primary,
                    fontWeight = FontWeight.Medium
                )
                if (event.end != null && event.start != event.end) {
                    Text(
                        text = event.end.format(DateTimeFormatter.ofPattern("HH:mm")),
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 📝 任务内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = event.title,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black.copy(alpha = 0.8f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // CalendarEvent 没有 description 属性，这里可以显示其他信息
                if (event.isCompleted) {
                    Text(
                        text = "已完成",
                        fontSize = 11.sp,
                        color = Color(0xFF5d733a),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // ✅ 完成状态指示
            if (event.isCompleted) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已完成",
                    tint = Color(0xFF5d733a),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * TickTick风格的投放区域 - 带刷新动画
 * 特点：
 * 1. 简单的投放检测
 * 2. 拖拽成功后的微动画
 * 3. 动画结束后刷新显示
 */
@Composable
private fun TickTickStyleDropZone(
    events: List<CalendarEvent>,
    selectedDate: LocalDate,
    draggedTask: Task?,
    onTaskDrop: (Task) -> Unit,
    onEventLongPress: (CalendarEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    val isDragOver = draggedTask != null
    var isDropAnimating by remember { mutableStateOf(false) }
    var justDroppedTask by remember { mutableStateOf<Task?>(null) }
    
    // 刷新动画状态 - 加快动画速度
    val refreshRotation by animateFloatAsState(
        targetValue = if (isDropAnimating) 360f else 0f,
        animationSpec = tween(durationMillis = 800, easing = EaseInOutCubic),
        finishedListener = {
            if (isDropAnimating) {
                Log.d("TickTickDrop", "🎬 成功动画完成")
                // 立即重置动画状态，任务列表继续显示
                isDropAnimating = false
                // 延迟重置投放任务信息，让用户看到成功反馈
                kotlinx.coroutines.GlobalScope.launch {
                    kotlinx.coroutines.delay(1000) // 延长成功提示显示时间
                    justDroppedTask = null
                    Log.d("TickTickDrop", "✨ 成功提示已清除，任务已显示在今日任务区域")
                }
            }
        },
        label = "refresh_rotation"
    )
    
    val scaleAnimation by animateFloatAsState(
        targetValue = if (isDropAnimating) 1.1f else 1f,
        animationSpec = tween(durationMillis = 300),
        label = "scale_animation"
    )
    
    Log.d("TickTickDrop", "渲染投放区域，拖拽: $isDragOver, 动画: $isDropAnimating, 事件数: ${events.size}")
    if (events.isNotEmpty()) {
        events.forEach { event ->
            Log.d("TickTickDrop", "  事件: ${event.title} (ID: ${event.id}, TaskID: ${event.taskId})")
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                scaleX = scaleAnimation,
                scaleY = scaleAnimation
            )
            .background(
                color = when {
                    isDropAnimating -> Color(0xFFE8F5E8).copy(alpha = 0.6f)
                    isDragOver -> Color(0xFFE3F2FD).copy(alpha = 0.3f)
                    else -> Color.Transparent
                },
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = when {
                    isDropAnimating -> 2.dp
                    isDragOver -> 2.dp
                    else -> 1.dp
                },
                color = when {
                    isDropAnimating -> Color(0xFF4CAF50)
                    isDragOver -> Color(0xFF776993) // 使用紫色主题
                    else -> Color.Gray.copy(alpha = 0.2f)
                },
                shape = RoundedCornerShape(8.dp)
            )
            .padding(8.dp)
    ) {
        // 标题行 - 带刷新图标
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Today,
                contentDescription = null,
                tint = Color(0xFF776993), // 紫色主题
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "今日任务",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1F2937)
            )
            
            // 显示状态提示
            when {
                isDropAnimating -> {
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新中",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier
                            .size(16.dp)
                            .graphicsLayer(rotationZ = refreshRotation)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "添加成功",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50),
                        fontWeight = FontWeight.Medium
                    )
                }
                isDragOver -> {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "点击添加",
                        fontSize = 12.sp,
                        color = Color(0xFF776993),
                        fontWeight = FontWeight.Medium
                    )
                }
                justDroppedTask != null -> {
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "「${justDroppedTask!!.title}」已添加",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 投放检测区域 - 带动画反馈
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 100.dp)
                .clickable(enabled = isDragOver && !isDropAnimating) {
                    // 如果有拖拽任务，点击完成投放
                    draggedTask?.let { task ->
                        Log.d("TickTickDrop", "🎯 点击投放: ${task.title}")
                        justDroppedTask = task
                        
                        // 🔧 先执行投放操作，让任务立即添加到UI
                        onTaskDrop(task)
                        
                        // 然后启动成功动画
                        isDropAnimating = true
                        
                        Log.d("TickTickDrop", "✅ 投放操作完成，动画开始")
                    }
                }
        ) {
            if (isDragOver && !isDropAnimating) {
                // 拖拽提示界面
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = null,
                        tint = Color(0xFF776993), // 使用紫色主题
                        modifier = Modifier.size(48.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Text(
                        text = "点击这里添加到今日",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF776993) // 使用紫色主题
                    )
                    
                    Text(
                        text = "任务将自动排序到今天的计划中",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // 🔧 始终显示任务列表或空状态，不被动画遮盖
                if (events.isEmpty()) {
                    // 空状态
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = null,
                            tint = Color.Gray.copy(alpha = 0.4f),
                            modifier = Modifier.size(32.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "今日暂无安排",
                            fontSize = 14.sp,
                            color = Color.Gray.copy(alpha = 0.6f)
                        )
                        
                        Text(
                            text = "长按本周任务拖拽到这里",
                            fontSize = 12.sp,
                            color = Color.Gray.copy(alpha = 0.5f)
                        )
                    }
                } else {
                    // 今日任务列表 - 始终显示
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 🔧 确保任务列表始终可见
                        Log.d("TickTickDrop", "📋 渲染任务列表: ${events.size} 个任务, 动画中: $isDropAnimating")
                        Log.d("TickTickDrop", "📋 任务列表详情:")
                        events.forEachIndexed { index, event ->
                            Log.d("TickTickDrop", "  [$index] ${event.title} (ID: ${event.id})")
                            TodayTaskCard(
                                event = event,
                                onLongPressCenter = onEventLongPress
                            )
                        }
                        
                        // 🎉 如果正在动画中且刚投放了任务，在任务列表下方显示成功提示
                        if (isDropAnimating && justDroppedTask != null) {
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp)
                                    .graphicsLayer(
                                        scaleX = scaleAnimation,
                                        scaleY = scaleAnimation
                                    ),
                                shape = RoundedCornerShape(12.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFF4CAF50).copy(alpha = 0.15f)
                                ),
                                border = BorderStroke(2.dp, Color(0xFF4CAF50).copy(alpha = 0.4f))
                            ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.CheckCircle,
                                            contentDescription = null,
                                            tint = Color(0xFF4CAF50),
                                            modifier = Modifier
                                                .size(20.dp)
                                                .graphicsLayer(rotationZ = refreshRotation)
                                        )
                                        Spacer(modifier = Modifier.width(12.dp))
                                        Text(
                                            text = "✨ 「${justDroppedTask!!.title}」已成功添加到今日计划！",
                                            fontSize = 14.sp,
                                            color = Color(0xFF4CAF50),
                                            fontWeight = FontWeight.SemiBold
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * TickTick风格的可拖拽任务卡片 - 最简化版本
 * 特点：
 * 1. 长按卡片开始拖拽
 * 2. 简单的拖拽反馈
 * 3. 右侧删除按钮
 */
@Composable
private fun SimpleDraggableTaskCard(
    task: Task,
    onStartDrag: () -> Unit,
    onLongPressCenter: (Task) -> Unit,
    modifier: Modifier = Modifier
) {
    val haptic = LocalHapticFeedback.current
    var isDragging by remember { mutableStateOf(false) }
    
    Log.d("TickTickDrag", "🎯 渲染任务卡片: ${task.title}")
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(task.id) {
                Log.d("TickTickDrag", "🎯 设置长按检测: ${task.title}")
                detectTapGestures(
                    onLongPress = { offset ->
                        Log.d("TickTickDrag", "🚀 长按触发拖拽: ${task.title}")
                        isDragging = true
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onStartDrag()
                        
                        // 模拟拖拽过程，一段时间后重置状态
                        kotlinx.coroutines.GlobalScope.launch {
                            kotlinx.coroutines.delay(2000)
                            isDragging = false
                        }
                    }
                )
            },
        shape = RoundedCornerShape(8.dp),
        color = if (isDragging) Color(0xFFE8F5E8) else Color.White,
        shadowElevation = 0.dp,
        border = BorderStroke(
            width = if (isDragging) 2.dp else 0.5.dp,
            color = if (isDragging) Color(0xFF4CAF50) else Color.Gray.copy(alpha = 0.2f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 拖拽图标
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "长按拖拽",
                tint = if (isDragging) Color(0xFF4CAF50) else Color.Gray,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        when (task.priority) {
                            Priority.URGENT -> Color(0xFFE53E3E)
                            Priority.HIGH -> Color(0xFFFF8247)
                            Priority.MEDIUM -> Color(0xFFd9b335)
                            Priority.LOW -> Color(0xFFA8C986)
                            else -> Color(0xFF9CA3AF)
                        },
                        CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1F2937),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (task.description.isNotEmpty()) {
                    Text(
                        text = task.description,
                        fontSize = 12.sp,
                        color = Color(0xFF6B7280),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Text(
                    text = if (isDragging) "拖拽中..." else "本周完成",
                    fontSize = 11.sp,
                    color = if (isDragging) Color(0xFF4CAF50) else Color(0xFFC4B5D4), // 莫兰迪薰衣草紫
                    fontWeight = FontWeight.Medium
                )
            }
            
            // 删除按钮 - 改为长按删除，避免误触
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .pointerInput(task.id) {
                        detectTapGestures(
                            onLongPress = {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                Log.d("TickTickDrag", "🗑️ 长按删除: ${task.title}")
                                onLongPressCenter(task)
                            }
                        )
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "长按删除任务",
                    tint = Color.Gray,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 今日任务卡片
 * 特点：
 * 1. 显示具体时间
 * 2. 完成状态切换
 * 3. 长按删除
 */
@Composable
private fun TodayTaskCard(
    event: CalendarEvent,
    onLongPressCenter: (CalendarEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    val haptic = LocalHapticFeedback.current
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(event.id) {
                detectTapGestures(
                    onLongPress = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPressCenter(event)
                    }
                )
            },
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
        shadowElevation = 0.dp,
        border = BorderStroke(0.5.dp, Color.Gray.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 完成状态复选框
            Checkbox(
                checked = event.isCompleted ?: false,
                onCheckedChange = { /* 处理完成状态切换 */ },
                modifier = Modifier.size(20.dp),
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFF10B981),
                    uncheckedColor = Color.Gray.copy(alpha = 0.5f)
                )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 时间显示
            Column {
                Text(
                    text = event.start?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: "全天",
                    fontSize = 11.sp,
                    color = Color.Gray,
                    fontWeight = FontWeight.Medium
                )
                if (event.end != null && event.end != event.start) {
                    Text(
                        text = "- ${event.end!!.format(DateTimeFormatter.ofPattern("HH:mm"))}",
                        fontSize = 11.sp,
                        color = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 任务内容
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = event.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (event.isCompleted == true) 
                        Color.Gray.copy(alpha = 0.6f) 
                    else 
                        Color(0xFF1F2937),
                    textDecoration = if (event.isCompleted == true) 
                        TextDecoration.LineThrough 
                    else 
                        TextDecoration.None,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // CalendarEvent没有description字段，这里移除description显示
            }
        }
    }
}

/**
 * 事件删除确认对话框
 */
@Composable
private fun EventDeleteConfirmDialog(
    eventTitle: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("删除事件")
        },
        text = {
            Text("确定要删除事件「$eventTitle」吗？此操作无法撤销。")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(contentColor = Color.Red)
            ) {
                Text("删除")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        containerColor = Color(0xFFFAFAFA) // 🎨 修改为用户指定的背景色 #fafafa
    )
}

/**
 * 🎨 高级美化事件卡片 - 参考Notion & Linear App设计
 * 特点：清晰的层次结构、优雅的动画、现代的视觉语言
 */
@Composable
private fun EnhancedEventItemCard(
    event: CalendarEvent,
    eventColor: Color,
    onToggleCompletion: (Boolean) -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "card_scale"
    )
    
    val cardColor by animateColorAsState(
        targetValue = if (event.isCompleted) Color(0xFFF7FAFC) else Color.White,
        animationSpec = tween(200),
        label = "card_color"
    )
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale
            )
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onClick() }
                )
            },
        color = cardColor,
        shape = RoundedCornerShape(16.dp),
        border = BorderStroke(
            width = 1.dp,
            color = if (event.isCompleted) 
                Color(0xFFE2E8F0) 
            else 
                eventColor.copy(alpha = 0.2f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 🎨 完成状态指示器
            Surface(
                modifier = Modifier
                    .size(24.dp)
                    .clickable { onToggleCompletion(!event.isCompleted) },
                color = if (event.isCompleted) eventColor else Color.Transparent,
                shape = CircleShape,
                border = BorderStroke(
                    width = 2.dp,
                    color = if (event.isCompleted) eventColor else eventColor.copy(alpha = 0.6f)
                )
            ) {
                AnimatedVisibility(
                    visible = event.isCompleted,
                    enter = scaleIn(animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)) + fadeIn(),
                    exit = scaleOut() + fadeOut()
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 🎨 事件内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = event.title,
                    fontSize = 14.sp, // 从16.sp减少到14.sp
                    fontWeight = FontWeight.SemiBold,
                    color = if (event.isCompleted) 
                        Color(0xFF718096) 
                    else 
                        Color(0xFF1A202C),
                    textDecoration = if (event.isCompleted) 
                        TextDecoration.LineThrough 
                    else 
                        TextDecoration.None,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 暂时不显示描述，因为CalendarEvent模型暂时没有description字段
                // 可以考虑未来从关联的任务中获取description
                // if (event.description?.isNotBlank() == true) {
                //     Spacer(modifier = Modifier.height(4.dp))
                //     Text(
                //         text = event.description,
                //         fontSize = 14.sp,
                //         color = Color(0xFF718096),
                //         maxLines = 1,
                //         overflow = TextOverflow.Ellipsis
                //     )
                // }
                
                // 时间信息
                Spacer(modifier = Modifier.height(6.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = null,
                        tint = Color(0xFFA0AEC0),
                        modifier = Modifier.size(14.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = "${event.start.format(DateTimeFormatter.ofPattern("HH:mm"))} - ${event.end?.format(DateTimeFormatter.ofPattern("HH:mm")) ?: "待定"}",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFFA0AEC0)
                    )
                }
            }
            
            // 🎨 优先级指示器或状态
            if (!event.isCompleted) {
                Surface(
                    color = eventColor.copy(alpha = 0.15f),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .width(4.dp)
                            .height(32.dp)
                            .background(eventColor, RoundedCornerShape(8.dp))
                    )
                }
            }
        }
    }
}
