package com.timeflow.app.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import android.graphics.Color as AndroidColor

/**
 * 颜色处理工具类
 */
object ColorUtils {
    /**
     * 将十六进制颜色字符串转换为Compose Color对象
     * @param hex 十六进制颜色字符串，如 "#FF5733"
     * @return Color对象，如果转换失败则返回null
     */
    fun getColorFromHex(hex: String): Color? {
        return try {
            val colorStr = if (hex.startsWith("#")) hex.substring(1) else hex
            val r = colorStr.substring(0, 2).toInt(16)
            val g = colorStr.substring(2, 4).toInt(16)
            val b = colorStr.substring(4, 6).toInt(16)
            val a = if (colorStr.length == 8) colorStr.substring(6, 8).toInt(16) else 255
            Color(r, g, b, a)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 十六进制颜色转换（带默认值）
     * @param hex 十六进制颜色字符串
     * @param defaultColor 默认颜色
     * @return Color对象
     */
    fun getColorFromHex(hex: String, defaultColor: Color): Color {
        return getColorFromHex(hex) ?: defaultColor
    }
    
    /**
     * 安全地将十六进制颜色字符串转换为Android Color整数值
     * 替代Color.parseColor的安全实现
     * @param hexColor 十六进制颜色字符串，如 "#FF5733"
     * @param defaultColor 默认颜色值，如果解析失败则返回此值
     * @return 对应的Android Color整数值
     */
    fun safeParseColor(hexColor: String, defaultColor: Int = AndroidColor.TRANSPARENT): Int {
        return try {
            AndroidColor.parseColor(hexColor)
        } catch (e: Exception) {
            // 解析失败时返回默认颜色
            defaultColor
        }
    }
    
    /**
     * 将Compose Color对象转换为Android Color整数值
     * @param color Compose Color对象
     * @return 对应的Android Color整数值
     */
    fun composeColorToAndroid(color: Color): Int {
        return color.toArgb()
    }
    
    /**
     * 将Android Color整数值转换为Compose Color对象
     * @param color Android Color整数值
     * @return 对应的Compose Color对象
     */
    fun androidColorToCompose(color: Int): Color {
        return Color(color)
    }
    
    /**
     * 综合颜色转换函数，可处理各种颜色输入格式并统一转换为Compose Color
     * @param colorInput 可以是Hex字符串、Android Color整数或Compose Color对象
     * @param defaultColor 默认颜色，如果转换失败则返回此值
     * @return Compose Color对象
     */
    fun toComposeColor(colorInput: Any?, defaultColor: Color = Color.Black): Color {
        return when(colorInput) {
            is String -> getColorFromHex(colorInput, defaultColor)
            is Int -> Color(colorInput)
            is Color -> colorInput
            else -> defaultColor
        }
    }
    
    // 预定义颜色
    val DustyLavender = Color(0xFF9370DB)
    val SoftMint = Color(0xFF98D4BB)
    val WarmSand = Color(0xFFE6D2AA)
    val MellowRose = Color(0xFFE29587)
    val CalmSky = Color(0xFF87CEEB)
    val QuietPlum = Color(0xFF673147)
    val EarthyTaupe = Color(0xFFB99470)
    val SageTeal = Color(0xFF5F9EA0)
}

// 顶层函数，使用时更简洁
fun getColorFromHex(hex: String): Color? = ColorUtils.getColorFromHex(hex)
fun getColorFromHex(hex: String, defaultColor: Color): Color = ColorUtils.getColorFromHex(hex, defaultColor) 

/**
 * 安全的Color.parseColor替代函数，避免因格式错误导致的崩溃
 * 顶层函数，易于调用
 * @param hexColor 十六进制颜色字符串
 * @param defaultColor 默认颜色值，如果解析失败返回此值
 * @return 对应的Android Color整数值
 */
fun safeParseColor(hexColor: String, defaultColor: Int = AndroidColor.TRANSPARENT): Int = 
    ColorUtils.safeParseColor(hexColor, defaultColor) 