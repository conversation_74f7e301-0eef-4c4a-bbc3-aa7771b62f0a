package com.timeflow.app.di

import android.content.Context
import android.util.Log
import com.timeflow.app.data.repository.DefaultTemplateInitializer
import com.timeflow.app.utils.SampleDataGenerator
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用初始化器 - 负责应用启动时的初始化操作
 */
@Singleton
class AppInitializer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val sampleDataGenerator: SampleDataGenerator,
    private val templateInitializer: DefaultTemplateInitializer
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    /**
     * 初始化应用
     */
    fun initialize() {
        Log.d("AppInitializer", "开始应用初始化")
        
        // 生成示例数据
        scope.launch {
            try {
                Log.d("AppInitializer", "开始检查并生成示例数据")
                sampleDataGenerator.generateSampleDataIfNeeded()
                Log.d("AppInitializer", "示例数据检查/生成完成")
            } catch (e: Exception) {
                Log.e("AppInitializer", "生成示例数据失败", e)
            }
        }
        
        // 初始化默认目标模板
        scope.launch {
            try {
                Log.d("AppInitializer", "开始初始化默认目标模板")
                templateInitializer.initializeDefaultTemplates()
                Log.d("AppInitializer", "默认目标模板初始化完成")
            } catch (e: Exception) {
                Log.e("AppInitializer", "初始化默认目标模板失败", e)
            }
        }
    }
} 