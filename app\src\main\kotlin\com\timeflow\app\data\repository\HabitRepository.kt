package com.timeflow.app.data.repository

import com.timeflow.app.data.model.HabitModel
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

/**
 * 习惯管理仓库接口
 * 定义与习惯相关的所有数据操作
 */
interface HabitRepository {
    /**
     * 获取所有习惯列表
     */
    suspend fun getAllHabits(): List<HabitModel>
    
    /**
     * 获取所有习惯列表流
     */
    fun getAllHabitsFlow(): Flow<List<HabitModel>>
    
    /**
     * 根据ID获取习惯详情
     */
    suspend fun getHabitById(habitId: String): HabitModel?
    
    /**
     * 添加一个新习惯
     */
    suspend fun addHabit(habit: HabitModel): Boolean
    
    /**
     * 更新习惯信息
     */
    suspend fun updateHabit(habit: HabitModel): Boolean
    
    /**
     * 删除习惯
     */
    suspend fun deleteHabit(habitId: String): Boolean
    
    /**
     * 恢复习惯（撤销删除/归档操作）
     */
    suspend fun restoreHabit(habitId: String): Boolean
    
    /**
     * 标记习惯为已完成
     */
    suspend fun markHabitAsCompleted(habitId: String, date: LocalDate, completed: Boolean): Boolean
    
    /**
     * 计算当前连续天数
     */
    suspend fun calculateStreak(habitId: String): Int
    
    /**
     * 设置习惯提醒
     */
    suspend fun scheduleHabitReminder(habitId: String, time: String): Boolean
    
    /**
     * 获取与指定目标关联的习惯列表
     */
    suspend fun getHabitsByGoalId(goalId: String): List<HabitModel>
    
    /**
     * 获取与指定目标关联的习惯列表流
     */
    fun getHabitsByGoalIdFlow(goalId: String): Flow<List<HabitModel>>

    /**
     * 获取习惯统计数据
     */
    suspend fun getHabitStatistics(startDate: LocalDate, endDate: LocalDate): HabitAnalyticsData

    /**
     * 获取习惯完成率统计
     */
    suspend fun getHabitCompletionRates(startDate: LocalDate, endDate: LocalDate): Map<String, Float>

    /**
     * 获取习惯连续天数排行
     */
    suspend fun getHabitStreakRanking(): List<HabitStreakData>

    /**
     * 获取每日习惯完成统计
     */
    suspend fun getDailyHabitCompletionStats(startDate: LocalDate, endDate: LocalDate): Map<LocalDate, Int>

    /**
     * 获取习惯分类统计
     */
    suspend fun getHabitCategoryStatistics(): Map<String, Int>
}

/**
 * 习惯分析数据
 */
data class HabitAnalyticsData(
    val totalHabits: Int,
    val activeHabits: Int,
    val completedToday: Int,
    val averageCompletionRate: Float,
    val averageStreak: Float,
    val longestStreak: Int,
    val habitsByCategory: Map<String, Int>,
    val topPerformingHabits: List<HabitPerformanceData>
)

/**
 * 习惯表现数据
 */
data class HabitPerformanceData(
    val habitId: String,
    val habitName: String,
    val completionRate: Float,
    val currentStreak: Int,
    val longestStreak: Int
)

/**
 * 习惯连续天数数据
 */
data class HabitStreakData(
    val habitId: String,
    val habitName: String,
    val currentStreak: Int,
    val longestStreak: Int
)