﻿package com.timeflow.app.ui.task

import androidx.lifecycle.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import javax.inject.Inject
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import android.util.Log
import timber.log.Timber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.delay
import com.timeflow.app.data.converter.TaskConverter
import com.timeflow.app.data.entity.Task
import com.timeflow.app.data.model.TaskGroup
import com.timeflow.app.data.model.TaskGroupType
import com.timeflow.app.data.model.Task as ModelTask
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.model.DataLoadingState
import com.timeflow.app.data.model.KanbanColumn
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.task.model.ViewMode
import com.timeflow.app.ui.task.model.SortOption
import com.timeflow.app.ui.task.components.common.state.ManagedState
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.data.repository.UserPreferenceRepository
import com.timeflow.app.data.repository.TransactionHelper
import android.content.Context
import androidx.work.WorkManager
import dagger.hilt.android.qualifiers.ApplicationContext
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.getSampleTasks
import java.time.temporal.ChronoUnit
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.data.repository.SharedFilterState
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancelChildren
import com.timeflow.app.util.NotificationCenter
import com.timeflow.app.util.TaskRefreshEvent
import com.timeflow.app.ui.screen.task.model.FeedbackData
import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import com.timeflow.app.data.model.Reflection as DataReflection  // ???????Reflection
import com.timeflow.app.ui.screen.reflection.ReflectionType
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.ui.screen.reflection.ContentBlock
import java.time.Instant
import java.time.ZoneId
import kotlinx.coroutines.runBlocking
import com.timeflow.app.ui.screen.reflection.ReflectionCreatedEvent
import com.timeflow.app.ui.screen.reflection.Reflection  // ??UI???Reflection
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import com.timeflow.app.ui.screen.task.model.TaskDataModel
import com.timeflow.app.data.repository.SharedPendingDeletionState

// ????????
private const val DEFAULT_BOARD_ID = "default_board"
private const val ALL_TASKS_CACHE_KEY = "all_tasks"
private const val TASK_GROUPS_CACHE_KEY = "task_groups"

/**
 * ??????
 */
data class TaskListState(
    val tasks: List<ModelTaskData> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val lastUpdateTime: Long = 0L // ????????,????UI??
)

/**
 * 🔧 删除消息数据类
 */
data class DeleteMessage(
    val taskId: String,
    val taskTitle: String,
    val message: String = "任务已删除",
    val actionLabel: String = "撤销"
)

/**
 * ????ViewModel - ???????????
 *
 * ?ViewModel??????????????????,??:
 * - ????????????
 * - ???????????
 * - ?????????????
 * - ?TaskListFullScreen?UnifiedHomeScreen???????????
 * - ???????????
 *
 * ?TaskDetailViewModel??,?ViewModel????????????????,
 * ???????????????????????????????,
 * ?????????????????
 *
 * @property taskRepository ??????,???????????
 * @property goalRepository ??????,???????????
 * @property userPreferenceRepository ??????,????????
 * @property workManager WorkManager??,????????
 * @property context ?????
 * @property sharedFilterState ??????,????????????
 * @property reflectionRepository ??????,???????????
 */
@HiltViewModel
class TaskListViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val goalRepository: GoalRepository,
    private val userPreferenceRepository: UserPreferenceRepository,
    private val workManager: WorkManager,
    @ApplicationContext private val context: Context,
    private val sharedFilterState: SharedFilterState,
    private val reflectionRepository: ReflectionRepository,
    private val sharedPendingDeletionState: SharedPendingDeletionState
) : ViewModel() {
    
    companion object {
        private const val TAG = "TaskListViewModel"
        private const val DEFAULT_TTL_MS = 30_000L // ????30?
    }
    
    // ????????????,??SupervisorJob,??????????????????
    private val eventScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    // ????Job??,??????????
    private var eventListenerJob: Job? = null
    
    // ????
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    // ????
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    // ????
    private val _tasks = MutableStateFlow<List<Task>>(emptyList())
    val tasks: StateFlow<List<Task>> = _tasks
    
    // ????(?????)
    private val _allTasks = MutableStateFlow<List<Task>>(emptyList())
    val allTasks: StateFlow<List<Task>> = _allTasks.asStateFlow()
    
    // ?????ID
    private val _expandedTaskIds = MutableStateFlow<Set<String>>(setOf())
    val expandedTaskIds: StateFlow<Set<String>> = _expandedTaskIds.asStateFlow()
    
    // ?????
    private val _selectedTasks = MutableStateFlow<Set<String>>(setOf())
    val selectedTasks: StateFlow<Set<String>> = _selectedTasks.asStateFlow()
    
    // ?????(????????)
    private val _rootTasks = MutableStateFlow<List<Task>>(emptyList())
    val rootTasks: StateFlow<List<Task>> = _rootTasks.asStateFlow()
    
    // ???????
    private val _selectedTask = MutableStateFlow<Task?>(null)
    val selectedTask: StateFlow<Task?> = _selectedTask.asStateFlow()
    
    // ???????
    private val _draggedTask = MutableStateFlow<Task?>(null)
    val draggedTask: StateFlow<Task?> = _draggedTask.asStateFlow()
    
    // ????
    private val _viewMode = MutableStateFlow(ViewMode.LIST)
    val viewMode: StateFlow<ViewMode> = _viewMode.asStateFlow()
    
    // ???
    private val _selectedTags = MutableStateFlow<List<String>>(emptyList())
    val selectedTags: StateFlow<List<String>> = _selectedTags.asStateFlow()
    
    private val _priorityFilter = MutableStateFlow<Int?>(null)
    val priorityFilter: StateFlow<Int?> = _priorityFilter.asStateFlow()
    
    private val _showCompleted = MutableStateFlow(true)
    val showCompleted: StateFlow<Boolean> = _showCompleted.asStateFlow()
    
    // ????
    private val _taskGroups = MutableStateFlow<Map<String, List<com.timeflow.app.data.entity.Task>>>(emptyMap())
    val taskGroups: StateFlow<Map<String, List<com.timeflow.app.data.entity.Task>>> = _taskGroups.asStateFlow()
    
    // ?????
    private val _customGroups = MutableStateFlow<List<TaskGroup>>(emptyList())
    val customGroups: StateFlow<List<TaskGroup>> = _customGroups.asStateFlow()
    
    // ??????
    private val _isInBatchEditMode = MutableStateFlow(false)
    val isInBatchEditMode: StateFlow<Boolean> = _isInBatchEditMode.asStateFlow()
    
    // ?????ID??
    private val _selectedTaskIds = MutableStateFlow<Set<String>>(emptySet())
    val selectedTaskIds: StateFlow<Set<String>> = _selectedTaskIds.asStateFlow()
    
    // ????
    private val _draggedTaskId = MutableStateFlow<String?>(null)
    val draggedTaskId: StateFlow<String?> = _draggedTaskId.asStateFlow()
    
    private val _dragTargetGroupId = MutableStateFlow<String?>(null)
    val dragTargetGroupId: StateFlow<String?> = _dragTargetGroupId.asStateFlow()
    
    // ?????
    private val _subTasks = MutableStateFlow<List<com.timeflow.app.data.model.Task>>(emptyList())
    val subTasks: StateFlow<List<com.timeflow.app.data.model.Task>> = _subTasks
    
    private val _sortOption = MutableStateFlow<SortOption>(SortOption.CREATE_TIME)
    val sortOption: StateFlow<SortOption> = _sortOption.asStateFlow()
    
    private val _uiState = MutableStateFlow(TaskUiState())
    val uiState: StateFlow<TaskUiState> = _uiState.asStateFlow()
    
    // ?????,??????UI
    private val _refreshTrigger = MutableStateFlow(0)
    val refreshTrigger = _refreshTrigger.asStateFlow()
    
    private var lastRefreshTime: Long = 0
    private val REFRESH_INTERVAL = 30_000 // 30?????
    
    // ?????
    private val subTaskCache = mutableMapOf<String, Pair<List<Task>, Long>>() // ??ID -> (?????, ????)
    private val SUB_TASK_CACHE_TTL = 5000L // 5?????????
    private val loadingParents = mutableSetOf<String>() // ??????????ID
    
    // ???????
    private val _subTasksState = ManagedState<List<ModelTask>>()
    val subTasksState = _subTasksState.state
    
    // ?????ID
    private val _currentParentTaskId = MutableStateFlow<String?>(null)
    val currentParentTaskId = _currentParentTaskId.asStateFlow()
    
    // ??mutex?????????
    private val mutex = Mutex()
    
    // UI??????
    private val _isOptimizingUi = MutableStateFlow(false)
    val isOptimizingUi: StateFlow<Boolean> = _isOptimizingUi
    
    // ?????????
    private val _dataLoadingState = MutableStateFlow(DataLoadingState())
    val dataLoadingState: StateFlow<DataLoadingState> = _dataLoadingState.asStateFlow()
    
    // ???????
    private val _taskConflicts = MutableStateFlow<List<Task>>(emptyList())
    val taskConflicts: StateFlow<List<Task>> = _taskConflicts.asStateFlow()
    
    // ???????
    private val _lastEditedTask = MutableStateFlow<Task?>(null)
    val lastEditedTask: StateFlow<Task?> = _lastEditedTask.asStateFlow()
    
    // ??????
    private val _conflictTasks = MutableStateFlow<List<Task>>(emptyList())
    val conflictTasks: StateFlow<List<Task>> = _conflictTasks.asStateFlow()
    
    // ??_tagFilter?????????????
    private val _tagFilter = MutableStateFlow<String?>(null)
    val tagFilter: StateFlow<String?> = _tagFilter
    
    // ??????
    private val _taskListState = MutableStateFlow(TaskListState())
    val taskListState: StateFlow<TaskListState> = _taskListState.asStateFlow()
    
    // 🔧 添加删除消息状态
    private val _deleteMessage = MutableSharedFlow<DeleteMessage>()
    val deleteMessage: SharedFlow<DeleteMessage> = _deleteMessage.asSharedFlow()
    
    // ?????????
    private val _filterState = MutableStateFlow("??")
    
    // ??????
    val filterState: StateFlow<String> = sharedFilterState.currentFilter
    
    // 🔧 修复：添加日志记录过滤状态
    val filteredTasksState: StateFlow<List<ModelTaskData>> = 
        combine(_taskListState, filterState, sharedPendingDeletionState.pendingDeletions) { state, filter, pendingDeletions ->
            // 🔧 修复：添加日志记录过滤状态
            Log.d(TAG, "[filteredTasksState] 当前过滤条件: $filter, 任务总数: ${state.tasks.size}, 待删除数: ${pendingDeletions.size}")
            
            // 🔧 第一步：排除待删除的任务
            val tasksAfterDeletionFilter = state.tasks.filter { task ->
                val isPendingDeletion = pendingDeletions.containsKey(task.id)
                if (isPendingDeletion) {
                    Log.d(TAG, "[filteredTasksState] 🗑️ 任务 ${task.id} (${task.title}) 被待删除状态过滤掉")
                }
                !isPendingDeletion
            }
            Log.d(TAG, "[filteredTasksState] 待删除过滤后任务数: ${tasksAfterDeletionFilter.size}")
            
            // 🔧 第二步：根据过滤条件进一步过滤（保留所有任务，包括父任务和子任务）
            val filteredTasks = when (filter) {
                "今天" -> tasksAfterDeletionFilter.filter { it.daysLeft == 0 }
                "未完成" -> tasksAfterDeletionFilter.filter { !it.isCompleted }
                "已完成" -> tasksAfterDeletionFilter.filter { it.isCompleted }
                "未定期" -> tasksAfterDeletionFilter.filter { it.daysLeft == Int.MAX_VALUE }
                else -> tasksAfterDeletionFilter // 全部
            }
            
            // 🔧 第三步：统计父任务和子任务数量
            val parentTaskCount = filteredTasks.count { task ->
                task.parentTaskId == null || task.parentTaskId.isBlank()
            }
            val subTaskCount = filteredTasks.size - parentTaskCount
            
            Log.d(TAG, "[filteredTasksState] 最终过滤后任务数: ${filteredTasks.size} (父任务: $parentTaskCount, 子任务: $subTaskCount)")
            
            // 🔧 注意：这里返回所有过滤后的任务，父任务/子任务的进一步过滤由UI层决定
            filteredTasks
        }
        .flowOn(Dispatchers.Default) // ?????????????
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000), // 5??????,??UI????
            initialValue = emptyList() // ???
        )
    
    /**
     * ????????
     */
    fun saveTask(task: Any) {
        viewModelScope.launch {
            try {
                // ????? IO ?????????
                withContext(Dispatchers.IO) {
                    val transactionHelper = TransactionHelper(taskRepository)
                    
                    when (task) {
                        is com.timeflow.app.data.entity.Task -> {
                            val modelTask = TaskConverter.toModelTask(task)
                            // ???????????,??????
                            val success = transactionHelper.saveTaskWithForeignKeyCheck(modelTask as com.timeflow.app.data.model.Task)
                            if (success) {
                                Timber.d("???????: ${task.id}")
                            } else {
                                Timber.e("????????,??????: ${task.id}")
                                // ?????? UI ??
                                withContext(Dispatchers.Main) {
                                    _error.value = "??????:??????"
                                }
                                // ??????? IO ??
                                fixTaskReferences(task.id) // fixTaskReferences ?????????
                            }
                        }
                        is com.timeflow.app.data.model.Task -> {
                            // ???????????,??????
                            val success = transactionHelper.saveTaskWithForeignKeyCheck(task)
                            if (success) {
                                Timber.d("???????: ${task.id}")
                            } else {
                                Timber.e("????????,??????: ${task.id}")
                                // ?????? UI ??
                                withContext(Dispatchers.Main) {
                                    _error.value = "??????:??????"
                                }
                                // ??????? IO ??
                                fixTaskReferences(task.id) // fixTaskReferences ?????????
                            }
                        }
                        else -> {
                            Timber.e("???????????: ${task.javaClass.name}")
                            throw IllegalArgumentException("???????????")
                        }
                    }
                }
                // ???????????????????
                refreshTasks()
            } catch (e: Exception) {
                Timber.e(e, "??????")
                // ?????? UI ??
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.localizedMessage}"
                }
            }
        }
    }

    /**
     * ??????
     */
    private fun observeTaskConflicts() {
        viewModelScope.launch {
            try {
                // ????? IO ?????????
                val conflictTasksList = withContext(Dispatchers.IO) {
                    taskRepository.getConflictTasks()
                }
                if (conflictTasksList.isNotEmpty()) {
                    val entityTasks = conflictTasksList.map { task ->
                        TaskConverter.toEntityTask(task)
                    }
                    // ?????? UI ??
                    withContext(Dispatchers.Main) {
                        _taskConflicts.value = entityTasks
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????", e)
            }
        }
    }
    
    // 🚀 性能优化：使用Flow自动监听数据变化，无需手动刷新
    init {
        Log.d(TAG, "TaskListViewModel 初始化开始 - 使用响应式数据监听")
        // 🚀 使用Room Flow自动监听数据变化
        setupReactiveDataFlow()
    }
    
    /**
     * 设置响应式数据流，自动监听数据库变化
     */
    private fun setupReactiveDataFlow() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "设置响应式数据流...")
                // 使用Room的observeAllTasks Flow，数据变化时自动更新UI
                taskRepository.observeAllTasks()
                    .catch { e ->
                        Log.e(TAG, "数据流异常", e)
                        _taskListState.value = _taskListState.value.copy(
                            isLoading = false,
                            error = "数据加载失败: ${e.message}"
                        )
                    }
                    .collect { modelTasks ->
                        Log.d(TAG, "🔄 响应式数据更新，收到 ${modelTasks.size} 个任务")
                        
                        // 在IO线程进行数据转换
                        withContext(Dispatchers.IO) {
                            try {
                                val taskDataList = modelTasks.map { modelTask ->
                                    convertModelTaskToTaskData(modelTask)
                                }
                                
                                val entityTasks = modelTasks.map { TaskConverter.toEntityTask(it) }
                                val groupsMap = createTaskGroups(entityTasks)
                                
                                // 在主线程更新UI
                                withContext(Dispatchers.Main) {
                                    _taskListState.value = _taskListState.value.copy(
                                        tasks = taskDataList,
                                        isLoading = false,
                                        error = null
                                    )
                                    _taskGroups.value = groupsMap
                                    _refreshTrigger.value = _refreshTrigger.value + 1
                                    
                                    Log.d(TAG, "✓ UI已更新，显示 ${taskDataList.size} 个任务")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "数据转换失败", e)
                                withContext(Dispatchers.Main) {
                                    _taskListState.value = _taskListState.value.copy(
                                        isLoading = false,
                                        error = "数据处理失败: ${e.message}"
                                    )
                                }
                            }
                        }
                    }
            } catch (e: Exception) {
                Log.e(TAG, "响应式数据流设置失败", e)
                _taskListState.value = _taskListState.value.copy(
                    isLoading = false,
                    error = "初始化失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * ????????
     */
    private suspend fun processEvent(event: AppEvent.BaseEvent) {
        // ?????????????
        when (event) {
            is AppEvent.TaskCompletionChanged -> {
                refreshTask(event.taskId)
            }
            is AppEvent.TaskUpdated -> {
                refreshTask(event.taskId)
            }
            is AppEvent.TaskDeleted -> {
                refreshTasks()
            }
            is AppEvent.TaskCreated -> {
                refreshTasks()
            }
            // ??????...
            else -> {
                // ????????????,????????
            }
        }
    }
    
    override fun onCleared() {
        // ???????
        eventListenerJob?.cancel()
        // ???????
        eventScope.coroutineContext.cancelChildren()
        super.onCleared()
    }
    
    /**
     * ??????
     * ????????????UI??
     */
    private fun refreshTask(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "????: $taskId")
                // ?????????ID
                val selectedId = _selectedTask.value?.id
                
                // ?????????????,??????
                if (taskId == selectedId) {
                    loadTask(taskId)
                }
                
                // ??????
                smartRefresh()
            } catch (e: Exception) {
                Log.e(TAG, "??????: $taskId", e)
            }
        }
    }
    
    /**
     * ???? - ??????
     */
    fun smartRefresh() {
        viewModelScope.launch {
            try {
                // ??????????????,????
                withContext(Dispatchers.Main) {
                    _taskListState.value = _taskListState.value.copy(isLoading = true)
                }
                
                // ????????????
                val shouldFullReload = _taskListState.value.tasks.isEmpty()
                
                if (shouldFullReload) {
                    // ??????,??????
                    loadTasks() // loadTasks ??????
                } else {
                    // ???? - ?????????
                    val currentTasks = _taskListState.value.tasks.toMutableList()
                    // ????? IO ?????????
                    val updatedModelTasks = withContext(Dispatchers.IO) {
                        taskRepository.getRecentlyUpdatedTasks()
                    }
                    
                    // ???????????,?????????? Default
                    // ????? Default ??
                    val updatedTasks = withContext(Dispatchers.Default) {
                        updatedModelTasks.map { modelTask ->
                            convertModelTaskToTaskData(modelTask)
                        }
                    }
                    
                    // ????????
                    updatedTasks.forEach { updatedTask ->
                        val index = currentTasks.indexOfFirst { it.id == updatedTask.id }
                        if (index >= 0) {
                            // ??????
                            currentTasks[index] = updatedTask
                        } else {
                            // ?????
                            currentTasks.add(updatedTask)
                        }
                    }
                    
                    // ????????,??????????
                    withContext(Dispatchers.Main) {
                        _taskListState.value = _taskListState.value.copy(
                            tasks = currentTasks,
                            isLoading = false
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "??????: ${e.message}", e)
                // ?????? UI ??
                withContext(Dispatchers.Main) {
                    _taskListState.value = _taskListState.value.copy(
                        isLoading = false,
                        error = e.message
                    )
                }
            }
        }
    }
    
    /**
     * ??????????
     * ???????????30??????
     */
    suspend fun checkAndRefreshIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRefreshTime > REFRESH_INTERVAL) {
            refreshTasks()
        }
    }
    
    /**
     * ??????
     */
    fun refreshTasks() {
        viewModelScope.launch { // ????,??????????
            try {
                // ??????
                withContext(Dispatchers.Main) {
                    _taskListState.value = _taskListState.value.copy(isLoading = true, error = null)
                    Log.d(TAG, "????????...")
                }
                
                // 🚀 新增：在加载任务前先自动顺延过期任务
                withContext(Dispatchers.IO) {
                    try {
                        Log.d(TAG, "===== 开始自动顺延过期任务 =====")
                        val rescheduledCount = taskRepository.autoRescheduleOverdueTasks()
                        if (rescheduledCount > 0) {
                            Log.d(TAG, "✓ 自动顺延了 $rescheduledCount 个过期任务")
                        } else {
                            Log.d(TAG, "没有需要顺延的过期任务")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "自动顺延过期任务失败", e)
                    }
                }
                
                // ???????,??? IO ??
                val modelTasks = withContext(Dispatchers.IO) {
                    Log.d(TAG, "??????????...")
                    val tasks = taskRepository.getAllTasks()
                    Log.d(TAG, "???? ${tasks.size} ???")
                    tasks
                }
                
                // ?????? - ???Default?????????
                val taskDataList = withContext(Dispatchers.Default) {
                    Log.d(TAG, "??????????...")
                    val convertedTasks = modelTasks.map { convertModelTaskToTaskData(it) }
                    Log.d(TAG, "????,?? ${convertedTasks.size} ?UI??")
                    convertedTasks
                }
                
                // ??????UI??
                withContext(Dispatchers.Main) {
                    Log.d(TAG, "??UI??...")
                    _taskListState.value = _taskListState.value.copy(
                        tasks = taskDataList,
                        isLoading = false
                    )
                    Log.d(TAG, "UI??????,??????: ${taskDataList.size}")
                }
                
                // ??????
                val entityTasks = modelTasks.map { TaskConverter.toEntityTask(it) }
                withContext(Dispatchers.Default) {
                    updateTaskGroups(entityTasks)
                }
                
                // ??????
                lastRefreshTime = System.currentTimeMillis()
                
            } catch (e: Exception) {
                Timber.e(e, "??????")
                Log.e(TAG, "??????: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    _taskListState.value = _taskListState.value.copy(
                        isLoading = false,
                        error = "??????:${e.message}"
                    )
                }
            } finally {
                // ???????????????
                withContext(Dispatchers.Main) {
                    if (_taskListState.value.isLoading) {
                        _taskListState.value = _taskListState.value.copy(isLoading = false)
                    }
                }
            }
        } // ????,??viewModelScope.launch
    }
    
    /**
     * ???????(????????)
     */
    private fun updateRootTasks(allTasks: List<com.timeflow.app.data.entity.Task>) {
        try {
            // ??????(????????)
            val rootTasks = allTasks.filter { it.parentTaskId == null }
            _rootTasks.value = rootTasks
            Timber.d("????????,? ${rootTasks.size} ????")
        } catch (e: Exception) {
            Timber.e(e, "?????????")
            // ????,???????????null
            if (_rootTasks.value.isEmpty()) {
                _rootTasks.value = emptyList()
            }
        }
    }
    
    // ??????ModelTask????entity.Task
    private suspend fun getModelTaskById(taskId: String): ModelTask? {
        // ????? IO ?????????
        return withContext(Dispatchers.IO) {
             taskRepository.getTaskById(taskId)
        }
    }
    
    /**
     * ??ModelTask - ?UI????
     */
    fun getModelTaskById(taskId: String, callback: (ModelTask?) -> Unit) {
        viewModelScope.launch {
            val task = getModelTaskById(taskId)
            withContext(Dispatchers.Main) {
                callback(task)
            }
        }
    }
    
    /**
     * ???????????
     */
    fun loadTask(taskId: String) {
        viewModelScope.launch {
            try {
                // ?????? - UI??????
                withContext(Dispatchers.Main) {
                    _isLoading.value = true
                    _error.value = null
                }
                
                // ??????IO??
                val task = withContext(Dispatchers.IO) {
                    taskRepository.getTaskById(taskId)
                }
                
                if (task != null) {
                    // ?????? - ??Default????,??????
                    val entityTask = withContext(Dispatchers.Default) {
                        TaskConverter.toEntityTask(task)
                    }
                    
                    // ??UI?? - ?????
                    withContext(Dispatchers.Main) {
                        _selectedTask.value = entityTask
                    }
                    
                    // ????????,?????,?????????
                    if (task.hasSubtasks) {
                        loadSubTasks(taskId)
                    }
                } else {
                    // ?????????? - UI??????
                    withContext(Dispatchers.Main) {
                        _error.value = "?????:$taskId"
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "??????: $taskId", e)
                // ???? - UI??????
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.message}"
                }
            } finally {
                // ?????? - UI??????
                withContext(Dispatchers.Main) {
                    _isLoading.value = false
                }
            }
        }
    }
    
    /**
     * ?????
     * ?????????????????,???UI??
     */
    fun loadSubTasks(parentId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "???????,???ID: $parentId")
                
                // ?????????????????????
                mutex.withLock {
                    if (loadingParents.contains(parentId)) {
                        Log.d(TAG, "????????,??: $parentId")
                        return@withLock
                    }
                    loadingParents.add(parentId)
                }
                
                // ?????? - UI??????
                withContext(Dispatchers.Main) {
                    _isLoading.value = true
                    _error.value = null
                }
                
                try {
                    // ???? - ?????Default??????
                    val cachedSubTasks = withContext(Dispatchers.Default) {
                        val cachedData = subTaskCache[parentId]
                        if (cachedData != null) {
                            val (tasks, timestamp) = cachedData
                            val now = System.currentTimeMillis()
                            // ??????,??????
                            if (now - timestamp < SUB_TASK_CACHE_TTL) {
                                Log.d(TAG, "????????,???ID: $parentId")
                                return@withContext tasks
                            }
                        }
                        null
                    }
                    
                    if (cachedSubTasks != null) {
                        // ????????UI?? - UI??????
                        withContext(Dispatchers.Main) {
                            _subTasks.value = cachedSubTasks.map { 
                                TaskConverter.toModelTask(it) 
                            }
                            _currentParentTaskId.value = parentId
                            Log.d(TAG, "??????????,? ${cachedSubTasks.size} ????")
                        }
                    } else {
                        // ????????? - ??????IO??
                        val subTasks = withContext(Dispatchers.IO) {
                            Log.d(TAG, "?????????,???ID: $parentId")
                            taskRepository.getSubTasks(parentId)
                        }
                        
                        // ??????
                        Log.d(TAG, "???? ${subTasks.size} ????")
                        
                        // ???? - UI??????
                        withContext(Dispatchers.Main) {
                            _subTasks.value = subTasks
                            _currentParentTaskId.value = parentId
                            Log.d(TAG, "????????,? ${subTasks.size} ????")
                        }
                        
                        // ???? - ?????Default??
                        withContext(Dispatchers.Default) {
                            // ????????,???EntityTask
                            val entitySubTasks = subTasks.map { TaskConverter.toEntityTask(it) }
                            subTaskCache[parentId] = Pair(entitySubTasks, System.currentTimeMillis())
                            Log.d(TAG, "??????,???ID: $parentId")
                        }
                    }
                } finally {
                    // ??????
                    mutex.withLock {
                        loadingParents.remove(parentId)
                    }
                    
                    // ?????? - UI??????
                    withContext(Dispatchers.Main) {
                        _isLoading.value = false
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "???????: $parentId", e)
                // ?????? - UI??????
                withContext(Dispatchers.Main) {
                    _error.value = "???????: ${e.message}"
                    _subTasks.value = emptyList()
                }
                
                // ??????
                mutex.withLock {
                    loadingParents.remove(parentId)
                }
                
                // ?????? - UI??????
                withContext(Dispatchers.Main) {
                    _isLoading.value = false
                }
            }
        }
    }
    
    /**
     * ?????
     */
    fun updateSubTasks(parentId: String, subtaskTitles: List<String>) {
        viewModelScope.launch {
            try {
                // ?????,??? IO ??
                val parentTask = withContext(Dispatchers.IO) {
                    getModelTaskById(parentId) // getModelTaskById ???????
                }
                if (parentTask == null) {
                    Timber.e("??????: $parentId")
                    return@launch
                }
                
                // ?????,???????
                val newSubTasks = subtaskTitles.map { title ->
                    com.timeflow.app.data.model.Task(
                            id = UUID.randomUUID().toString(),
                            title = title,
                        description = "",
                        parentTaskId = parentId,
                        depth = parentTask.depth + 1,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now(),
                        status = "??",
                        columnId = parentTask.columnId
                    )
                }
                
                // ?????,??? IO ??
                withContext(Dispatchers.IO) {
                    newSubTasks.forEach { subTask ->
                        taskRepository.saveTask(subTask)
                    }
                }
                
                // ??????hasSubtasks??,??? IO ??
                if (!parentTask.hasSubtasks && newSubTasks.isNotEmpty()) {
                    val updatedParent = parentTask.copy(hasSubtasks = true)
                    // updateTask ??????
                    updateTask(updatedParent)
                }
                
                // ???????,??????
                loadSubTasks(parentId)
                
                Timber.d("???????: ???ID = $parentId")
            } catch (e: Exception) {
                Timber.e(e, "???????")
                withContext(Dispatchers.Main) {
                    _error.value = "???????: ${e.localizedMessage}"
                }
            }
        }
    }
    
    /**
     * ???????
     */
    private fun updateParentTaskProgress(parentTaskId: String) {
        viewModelScope.launch {
            try {
                // ?????,??? IO ??
                val parentTask = withContext(Dispatchers.IO) {
                    taskRepository.getTaskById(parentTaskId)
                } ?: return@launch
                
                // ?????,??? IO ??
                val subTasks = withContext(Dispatchers.IO) {
                    taskRepository.getSubTasks(parentTaskId)
                }
            
                // ??????,??? Default ??
                if (subTasks.isNotEmpty()) {
                    val totalSubtasks = subTasks.size
                    val completedSubtasks = subTasks.count { it.completedAt != null }
                    
                    // ???????
                    val progress = if (totalSubtasks > 0) {
                        completedSubtasks.toFloat() / totalSubtasks.toFloat()
                    } else {
                        0f
                    }
                    
                    // ?????
                    // ?????????????,??Task?????????
                    // ????,????????
                    val updatedParent = parentTask.copy(
                        updatedAt = LocalDateTime.now()
                        // ?????????,?????????
                        // progress = progress,
                        // childTasksCount = totalSubtasks,
                        // completedChildTasksCount = completedSubtasks
                    )
                
                    // ????????,??? IO ??
                    withContext(Dispatchers.IO) {
                        taskRepository.updateTask(updatedParent)
                    }
                
                    // ???????,????
                    withContext(Dispatchers.Main) {
                         // ?????????????????
                         if (_selectedTask.value?.id == parentTaskId) {
                             _selectedTask.value = TaskConverter.toEntityTask(updatedParent)
                         }
                    }
                    
                    Log.d(TAG, "????????: $parentTaskId, ????: $totalSubtasks, ???: $completedSubtasks, ??: ${progress * 100}%")
                }
            } catch (e: Exception) {
                Timber.e(e, "?????????: $parentTaskId")
            }
        }
    }
    
    /**
     * ??UI?? - ??UI????,????
     */
    private inline fun batchUiUpdates(crossinline updates: suspend () -> Unit) {
        viewModelScope.launch {
            _isOptimizingUi.value = true
            try {
                updates()
            } finally {
                _isOptimizingUi.value = false
            }
        }
    }
    
    /**
     * ????????
     */
    private suspend fun clearTaskRelatedCaches() {
        mutex.withLock {
            // ???????
            subTaskCache.clear()
            
            Timber.d("?????????")
        }
    }

    // UI?????
    data class TaskUiState(
        val tasks: List<Task> = emptyList(),
        val isLoading: Boolean = false,
        val error: String? = null,
        val searchQuery: String = "",
        val sortOption: SortOption = SortOption.CREATE_TIME,
        val viewMode: ViewMode = ViewMode.LIST,
        val showCompleted: Boolean = true
    )
    
    // ??????
    data class DataLoadingState(
        val isLoading: Boolean = false,
        val error: String? = null
    )

    /**
     * ??????
     * ??????????????,???UI??
     */
    private fun updateTaskGroups(tasks: List<com.timeflow.app.data.entity.Task>) {
        viewModelScope.launch {
            try {
                // ?Default??????????????
                val timeGroups = withContext(Dispatchers.Default) {
                    // ?????
                    tasks
                        .groupBy { task ->
                            when {
                                task.dueDate == null -> "???"
                                task.dueDate.toLocalDate().isBefore(LocalDate.now()) -> "???"
                                task.dueDate.toLocalDate().isEqual(LocalDate.now()) -> "??"
                                task.dueDate.toLocalDate().isEqual(LocalDate.now().plusDays(1)) -> "??"
                                task.dueDate.toLocalDate().isEqual(LocalDate.now().plusDays(2)) -> "??"
                                task.dueDate.toLocalDate().isAfter(LocalDate.now().plusDays(2)) && 
                                task.dueDate.toLocalDate().isBefore(LocalDate.now().plusDays(8)) -> "??"
                                else -> "??"
                            }
                        }
                        .toSortedMap(
                            compareBy<String> { 
                                when(it) {
                                    "???" -> 0
                                    "??" -> 1
                                    "??" -> 2
                                    "??" -> 3
                                    "??" -> 4
                                    "??" -> 5
                                    "???" -> 6
                                    else -> 7
                                }
                            }
                        )
                }
                
                // ???????UI??
                withContext(Dispatchers.Main) {
                    // ????????
                    val currentValue = _taskGroups.value
                    if (currentValue != timeGroups) {
                        _taskGroups.value = timeGroups
                        Log.d(TAG, "???????,? ${timeGroups.size} ???")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????", e)
            }
        }
    }

    /**
     * ????
     */
    fun addTask(task: com.timeflow.app.data.entity.Task) {
        viewModelScope.launch {
            try {
                val modelTask = TaskConverter.toModelTask(task)
                // ??? IO ????
                withContext(Dispatchers.IO) {
                    taskRepository.saveTask(modelTask)
                }
                // refreshTasks ??????
                refreshTasks()
                // ????????
                // EventBus.publish(AppEvent.TaskCreated(task.id))
            } catch (e: Exception) {
                Timber.e(e, "??????")
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.localizedMessage}"
                }
            }
        }
    }

    /**
     * ????
     * ???TaskEditScreen????ModelTask??
     */
    suspend fun saveTask(task: ModelTask) {
        // ?????????????,????????????
        // ?????,???? IO ????
        withContext(Dispatchers.IO) {
            try {
                Timber.d("????: ${task.id}")
                taskRepository.saveTask(task)
                // ??????
                Timber.d("??????: ${task.id}")
            } catch (e: Exception) {
                Timber.e(e, "??????: ${e.message}")
                throw e
            }
        }
        // ?????????????,????????????????
        // ????????????? refreshTasks() ? smartRefresh()
    }

    /**
     * ?????
     */
    fun addSubTask(parentTaskId: String, title: String) {
        viewModelScope.launch {
            try {
                val subTaskId = withContext(Dispatchers.IO) {
                    // ????Task?????????
                    val subTask = com.timeflow.app.data.model.Task(
                        id = UUID.randomUUID().toString(),
                        title = title,
                        description = "",
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now(),
                        startDate = null,
                        dueDate = null,
                        completedAt = null,
                        // ??????????
                        priority = com.timeflow.app.data.model.Priority.MEDIUM,
                        parentTaskId = parentTaskId
                    )
                    
                    // ??????????runInTransaction??,??????
                    taskRepository.insertTask(subTask)
                    val parentTask = taskRepository.getTaskById(parentTaskId)
                    if (parentTask != null) {
                        val updatedParentTask = parentTask.copy(
                            hasSubtasks = true,
                            updatedAt = LocalDateTime.now()
                        )
                        taskRepository.updateTask(updatedParentTask)
                    }
                    
                    subTask.id
                }
                
                Timber.d("???????,??ID: $subTaskId")
                
                // ?????????
                EventBus.tryEmit(AppEvent.SubTaskAdded(parentTaskId, subTaskId))
                // ?????????
                EventBus.tryEmit(AppEvent.TaskUpdated(parentTaskId))
                
                // ???????,????????reloadSubtasks
                reloadSubtasks(parentTaskId)
            } catch (e: Exception) {
                Timber.e(e, "???????")
                withContext(Dispatchers.Main) {
                    _error.value = e.message ?: "???????"
                }
            }
        }
    }
    
    /**
     * ?????????
     */
    fun reloadSubtasks(parentId: String) {
        viewModelScope.launch {
            try {
                // ?????,?????????
                loadSubTasks(parentId)
                
                // ???????,?????????
                updateParentTaskProgress(parentId)
                
                Timber.d("????????: parentId=$parentId")
            } catch (e: Exception) {
                Timber.e(e, "?????????: $parentId")
            }
        }
    }
    
    /**
     * ????(??????Task??)
     */
    fun updateTask(task: com.timeflow.app.data.model.Task) {
        viewModelScope.launch {
            try {
                // ??? IO ???????
                withContext(Dispatchers.IO) {
                    taskRepository.updateTask(task)
                }
                
                // ??????????,??????,????
                if (_selectedTask.value?.id == task.id) {
                    withContext(Dispatchers.Main) {
                        _selectedTask.value = TaskConverter.toEntityTask(task)
                    }
                }
                
                // ??????,??????
                refreshTasks()
                
                // ????????
                Timber.d("?????: ${task.id}")
                EventBus.tryEmit(AppEvent.TaskUpdated(task.id))
            } catch (e: Exception) {
                Timber.e(e, "??????")
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.message}"
                }
            }
        }
    }
    
    /**
     * ????????(????)
     */
    fun updateTask(taskId: String, title: String, description: String, priority: Priority, dueDate: LocalDateTime?, parentId: String? = null) {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val task = taskRepository.getTaskById(taskId)
                    if (task != null) {
                        val updatedTask = task.copy(
                            title = title,
                            description = description,
                            priority = priority,
                            dueDate = dueDate,
                            updatedAt = LocalDateTime.now()
                        )
                        taskRepository.updateTask(updatedTask)
                    }
                }
                
                // ???????? - ??????,???????????
                EventBus.tryEmit(AppEvent.TaskUpdated(taskId))
                if (parentId != null) {
                    EventBus.tryEmit(AppEvent.SubTaskUpdated(parentId, taskId))
                }
                
                // ????????
                loadTasks()
            } catch (e: Exception) {
                Timber.e(e, "??????")
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.message}"
                }
            }
        }
    }
    
    /**
     * 更新任务状态（优化版：减少延迟，提高响应速度）
     */
    fun updateTaskStatus(taskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "[updateTaskStatus] ===== 开始更新任务状态 =====")
                Log.d(TAG, "[updateTaskStatus] 任务ID: $taskId, 新状态: $isCompleted")
                
                // 🚀 优化：直接在IO线程进行数据库更新，减少锁竞争
                withContext(Dispatchers.IO) {
                    taskRepository.updateTaskCompletionStatus(taskId, isCompleted)
                    Log.d(TAG, "[updateTaskStatus] ✓ 数据库更新完成")
                }
                
                // 🚀 优化：发送全局刷新事件，通知其他组件
                try {
                    NotificationCenter.post(TaskRefreshEvent(taskId))
                    Log.d(TAG, "[updateTaskStatus] ✓ 全局刷新事件已发送")
                } catch (e: Exception) {
                    Log.w(TAG, "[updateTaskStatus] 全局事件发送失败，继续执行", e)
                }
                
                Log.d(TAG, "[updateTaskStatus] ===== 任务状态更新完成 =====")
                    
            } catch (e: Exception) {
                Log.e(TAG, "[updateTaskStatus] ✗ 任务状态更新失败: $taskId", e)
                
                // 抛出异常，让调用方处理UI回滚
                throw e
            }
        }
    }

    /**
     * ????
     */
    fun deleteTask(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "??????: $taskId")
                withContext(Dispatchers.IO) {
                    // ????(???????????)
                    taskRepository.deleteTask(taskId)
                }
                
                // ??????????
                val currentTasks = _taskListState.value.tasks
                val updatedTasks = currentTasks.filter { it.id != taskId }
                
                _taskListState.update { state ->
                    state.copy(tasks = updatedTasks)
                }
                
                // ??????
                EventBus.tryEmit(AppEvent.TaskDeleted(taskId))
                
                Log.d(TAG, "??????: $taskId")
            } catch (e: Exception) {
                Log.e(TAG, "??????: $taskId", e)
                _error.value = "??????: ${e.message}"
            }
        }
    }

    /**
     * ??????
     * @param taskId ??ID
     * @param tags ??????
     */
    fun updateTaskTags(taskId: String, tags: List<String>) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "????????: taskId=$taskId, tags=$tags")
                
                withContext(Dispatchers.IO) {
                    val task = taskRepository.getTaskById(taskId)
                    if (task != null) {
                        Log.d(TAG, "????: ${task.title}, ???: ${task.tags.map { it.name }}")
                        
                        // ?????????TaskTag??
                        val taskTags = tags.map { tagName ->
                            com.timeflow.app.data.model.TaskTag(
                                name = tagName,
                                color = androidx.compose.ui.graphics.Color(0xFF808080) // ??Color????????
                            )
                        }
                        
                        val updatedTask = task.copy(
                            tags = taskTags,
                            updatedAt = LocalDateTime.now()
                        )
                        
                        Log.d(TAG, "??????????,???: ${updatedTask.tags.map { it.name }}")
                        taskRepository.updateTask(updatedTask)
                        Log.d(TAG, "????????: $taskId")
                        
                        // ??????
                        delay(50)
                        val verifyTask = taskRepository.getTaskById(taskId)
                        Log.d(TAG, "??????: ${verifyTask?.tags?.map { it.name } ?: "?????"}")
                        
                    } else {
                        Log.e(TAG, "?????: $taskId")
                        throw IllegalArgumentException("?????: $taskId")
                    }
                }
                
                // ????????????????
                delay(100)
                
                // ?????????????????
                refreshTasks()
                
                // ????????
                NotificationCenter.post(TaskRefreshEvent(taskId))
                
                Log.d(TAG, "????????,???????")
                
            } catch (e: Exception) {
                Log.e(TAG, "????????: taskId=$taskId", e)
                _error.value = "????????: ${e.localizedMessage ?: "????"}"
            }
        }
    }

    /**
     * ?????????
     * @param taskId ??ID
     * @param goalId ??ID,null??????
     * @param goalTitle ????,????
     */
    fun updateTaskGoalAssociation(taskId: String, goalId: String?, goalTitle: String?) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "🎯 开始目标关联更新: taskId=$taskId, goalId=$goalId, goalTitle=$goalTitle")
                
                withContext(Dispatchers.IO) {
                    val task = taskRepository.getTaskById(taskId)
                    if (task != null) {
                        Log.d(TAG, "📝 找到任务: ${task.title}, 当前goalId: ${task.goalId}")
                        
                        val updatedTask = task.copy(
                            goalId = goalId,
                            updatedAt = LocalDateTime.now()
                        )
                        
                        Log.d(TAG, "🔄 准备更新任务，新goalId: ${updatedTask.goalId}")
                        taskRepository.updateTask(updatedTask)
                        Log.d(TAG, "💾 数据库更新完成: $taskId")
                        
                        // 验证更新结果
                        delay(50)
                        val verifyTask = taskRepository.getTaskById(taskId)
                        Log.d(TAG, "✅ 验证结果: goalId=${verifyTask?.goalId ?: "读取失败"}")
                        
                        if (verifyTask?.goalId == goalId) {
                            Log.d(TAG, "🎉 目标关联更新验证成功！")
                        } else {
                            Log.e(TAG, "❌ 目标关联更新验证失败！期望=$goalId, 实际=${verifyTask?.goalId}")
                        }
                        
                    } else {
                        Log.e(TAG, "❌ 任务不存在: $taskId")
                        throw IllegalArgumentException("任务不存在: $taskId")
                    }
                }
                
                // ????????????????
                delay(100)
                
                // ???????????????????
                refreshTasks()
                
                // ????????
                NotificationCenter.post(TaskRefreshEvent(taskId))
                
                Log.d(TAG, "??????????,???????")
                
            } catch (e: Exception) {
                Log.e(TAG, "??????????: taskId=$taskId", e)
                _error.value = "??????????: ${e.localizedMessage ?: "????"}"
            }
        }
    }

    /**
     * ????????
     */
    fun moveTask(taskId: String, newStatus: String) {
        viewModelScope.launch {
            try {
                // ??????,??????updateTaskCompletion
                updateTaskStatus(taskId, newStatus == "???")
            } catch (e: Exception) {
                Log.e(TAG, "??????", e)
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.localizedMessage}"
                }
            }
        }
    }
    
    /**
     * ??????
     */
    fun setViewMode(mode: ViewMode) {
            _viewMode.value = mode
        // ??????,??? IO ??
        saveUserPreference("view_mode", mode.name)
    }

    /**
     * ????????
     */
    fun setPriorityFilter(priority: Int?) {
        _priorityFilter.value = priority
        refreshTasksWithRetry() // ??????
    }

    /**
     * ???????
     */
    fun setTagFilter(tagId: String?) {
        _tagFilter.value = tagId
        refreshTasksWithRetry() // ??????
    }

    /**
     * ???????????
     */
    fun setShowCompleted(show: Boolean) {
        _showCompleted.value = show
        refreshTasksWithRetry() // ??????
    }

    /**
     * ??????,?????
     */
    fun refreshTasksWithRetry() {
        viewModelScope.launch {
            withContext(Dispatchers.Main) {
                 _isLoading.value = true
            }
            try {
                refreshTasks() // ??????
            } catch (e: Exception) {
                Log.e(TAG, "??????", e)
                withContext(Dispatchers.Main) {
                    _error.value = "??????: ${e.localizedMessage}"
                }
                
                // ????
                delay(1000)
                try {
                    refreshTasks() // ??????
                } catch (e: Exception) {
                    Log.e(TAG, "????????", e)
                    withContext(Dispatchers.Main) {
                        _error.value = "????????: ${e.localizedMessage}"
                    }
                }
            } finally {
                withContext(Dispatchers.Main) {
                    _isLoading.value = false
                }
            }
        }
    }

    /**
     * ????
     */
    private fun invalidateCache(taskId: String? = null) {
        if (taskId == null) {
            // ??????
            subTaskCache.clear()
        } else {
            // ?????????
            subTaskCache.remove(taskId)
        }
    }

    /**
     * ??????????
     * 
     * @param visibleCount ??????
     * @param totalCount ?????
     * @param renderTimeMs ????(??)
     */
    fun reportTaskListMetrics(
        visibleCount: Int,
        totalCount: Int,
        renderTimeMs: Long
    ) {
        Log.d(TAG, "TaskList????: ????=$visibleCount, ???=$totalCount, ????=${renderTimeMs}ms")
        
        // ???????????????
        if (totalCount > 50 || renderTimeMs > 100) {
            if (!_isOptimizingUi.value) {
                Log.d(TAG, "????UI???? (???=$totalCount, ????=${renderTimeMs}ms)")
                _isOptimizingUi.value = true
            }
        } else if (totalCount < 20 && renderTimeMs < 50) {
            if (_isOptimizingUi.value) {
                Log.d(TAG, "????UI???? (???=$totalCount, ????=${renderTimeMs}ms)")
                _isOptimizingUi.value = false
            }
        }
    }

    /**
     * ??????
     * ??????????,???????UI??
     */
    fun loadTasks() {
        viewModelScope.launch {
            try {
                // ?????? - UI??????
                _taskListState.value = _taskListState.value.copy(isLoading = true, error = null)
                _isLoading.value = true
                
                // ????????????IO???????
                withContext(Dispatchers.IO) {
                    Log.d(TAG, "????????...")
                    val modelTasks = taskRepository.getAllTasks()
                    Log.d(TAG, "???? ${modelTasks.size} ???")
                    
                    // ????????,???????????
                    val batchSize = 20 // ????20???
                    val totalBatches = (modelTasks.size + batchSize - 1) / batchSize
                    
                    val allConvertedTasks = mutableListOf<ModelTaskData>()
                    
                    for (batchIndex in 0 until totalBatches) {
                        val startIndex = batchIndex * batchSize
                        val endIndex = minOf(startIndex + batchSize, modelTasks.size)
                        val taskBatch = modelTasks.subList(startIndex, endIndex)
                        
                        // ?? ?????????,??????????
                        val convertedTasks = mutableListOf<ModelTaskData>()
                        taskBatch.forEach { modelTask ->
                            val taskData = convertTaskToModelTaskDataWithGoalInfo(modelTask)
                            convertedTasks.add(taskData)
                        }
                        
                        allConvertedTasks.addAll(convertedTasks)
                        
                        // ?????????UI,?????????
                        if (batchIndex < totalBatches - 1) {
                            withContext(Dispatchers.Main) {
                                _taskListState.value = _taskListState.value.copy(
                                    tasks = allConvertedTasks.toList(),
                                    isLoading = true // ?????
                                )
                            }
                        }
                    }
                    
                    Log.d(TAG, "????????,? ${allConvertedTasks.size} ???")
                    
                    // ?IO?????????
                    val entityTasks = modelTasks.map { TaskConverter.toEntityTask(it) }
                    val groupsMap = createTaskGroups(entityTasks)
                    
                    // ?????????UI??
                    withContext(Dispatchers.Main) {
                        _taskListState.value = _taskListState.value.copy(
                            tasks = allConvertedTasks,
                            isLoading = false
                        )
                        
                        // ??????
                        _taskGroups.value = groupsMap
                        
                        // ??????
                        lastRefreshTime = System.currentTimeMillis()
                        
                        // ????
                        _refreshTrigger.value = _refreshTrigger.value + 1
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????: ${e.message}", e)
                
                // ?????? - UI??????
                _taskListState.value = _taskListState.value.copy(
                    isLoading = false,
                    error = "??????: ${e.message}"
                )
                _error.value = "??????: ${e.message}"
            } finally {
                // ??????????????
                _isLoading.value = false
            }
        }
    }
    
    /**
     * ?IO?????????
     */
    private fun createTaskGroups(tasks: List<com.timeflow.app.data.entity.Task>): Map<String, List<com.timeflow.app.data.entity.Task>> {
        // ?????,??updateTaskGroups??????
        return tasks
            .groupBy { task ->
                when {
                    task.dueDate == null -> "???"
                    task.dueDate.toLocalDate().isBefore(LocalDate.now()) -> "???"
                    task.dueDate.toLocalDate().isEqual(LocalDate.now()) -> "??"
                    task.dueDate.toLocalDate().isEqual(LocalDate.now().plusDays(1)) -> "??"
                    task.dueDate.toLocalDate().isEqual(LocalDate.now().plusDays(2)) -> "??"
                    task.dueDate.toLocalDate().isAfter(LocalDate.now().plusDays(2)) && 
                    task.dueDate.toLocalDate().isBefore(LocalDate.now().plusDays(8)) -> "??"
                    else -> "??"
                }
            }
            .toSortedMap(
                compareBy<String> { 
                    when(it) {
                        "???" -> 0
                        "??" -> 1
                        "??" -> 2
                        "??" -> 3
                        "??" -> 4
                        "??" -> 5
                        "???" -> 6
                        else -> 7
                    }
                }
            )
    }
    
    // ?ModelTask???ModelTaskData - ?IO?????
    private fun convertModelTaskToTaskData(modelTask: com.timeflow.app.data.model.Task): ModelTaskData {
        // 计算剩余天数
        val daysLeft = calculateDaysLeft(modelTask.dueDate)
        
        // 🔧 修复完成状态判断：使用多重判断确保数据一致性
        val isCompleted = when {
            // 优先级1：completedAt字段不为空则认为已完成
            modelTask.completedAt != null -> true
            // 优先级2：status字段为"已完成"则认为已完成
            modelTask.status == "已完成" -> true
            // 优先级3：isCompleted字段为true则认为已完成
            modelTask.isCompleted -> true
            // 其他情况均认为未完成
            else -> false
        }
        
        Log.d("TaskConversion", "任务 ${modelTask.id} 完成状态判断: completedAt=${modelTask.completedAt}, status=${modelTask.status}, isCompleted=${modelTask.isCompleted} -> 最终结果=$isCompleted")
        
        // 如果存在状态不一致，记录警告日志
        if ((modelTask.completedAt != null) != modelTask.isCompleted || 
            (modelTask.status == "已完成") != modelTask.isCompleted) {
            Log.w("TaskConversion", "发现任务 ${modelTask.id} 完成状态不一致: completedAt=${modelTask.completedAt}, status=${modelTask.status}, isCompleted=${modelTask.isCompleted}")
        }
        
        // 根据完成状态和其他信息确定紧急程度，不对已完成任务进行干预
        val urgency = determineUrgency(modelTask, isCompleted)
        
        // 标签转换：清理无效标签，保留有效标签
        val tag = null // 暂时不使用tag字段
        val customTags = modelTask.tags.mapNotNull { taskTag ->
            val tagName = taskTag.name
            when {
                // 跳过name中包含格式错误的标签
                tagName.contains("TaskTag(") -> {
                    Log.w("TagConversion", "Invalid tag name format, skipping: $tagName")
                    null
                }
                tagName.isNotBlank() && !tagName.contains("(") -> tagName
                else -> {
                    Log.w("TagConversion", "Empty or invalid tag name, skipping: $tagName")
                    null
                }
            }
        }
        
        // Log tag conversion result
        if (modelTask.tags.isNotEmpty()) {
            Log.d("TagConversion", "Task ${modelTask.id} tags: ${modelTask.tags.size} original -> ${customTags.size} converted: $customTags")
        }
        
        // 🔧 关键修复：将LocalDateTime的completedAt转换为Long时间戳
        val completedAtTimestamp = modelTask.completedAt?.let { localDateTime ->
            localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
        }
        
        return ModelTaskData(
            id = modelTask.id,
            title = modelTask.title,
            description = modelTask.description,
            daysLeft = daysLeft,
            isCompleted = isCompleted,
            urgency = urgency,
            tag = tag,
            customTags = customTags,
            parentTaskId = modelTask.parentTaskId,
            // 显示设置：只有父任务（非子任务）才在任务列表中显示
            displayInTaskList = modelTask.parentTaskId == null,
            dueDate = modelTask.startDate ?: modelTask.dueDate, // 🔧 修复：优先使用startDate作为显示时间，fallback到dueDate
            completedAt = completedAtTimestamp // 🔧 关键修复：正确设置完成时间戳
        )
    }
    
    /**
     * ????????
     */
    private fun calculateDaysLeft(dueDate: LocalDateTime?): Int {
        if (dueDate == null) return Int.MAX_VALUE
        val now = LocalDateTime.now()
        
        // ???????????(??????)
        val isSameDay = now.toLocalDate().isEqual(dueDate.toLocalDate())
        
        // ??????,??0??"??"
        if (isSameDay) {
            return 0
        }
        
        // ???????????
        return ChronoUnit.DAYS.between(now.toLocalDate(), dueDate.toLocalDate()).toInt()
    }
    
    /**
     * ???????????? - ?? ????:????????????
     */
    private fun determineUrgency(task: ModelTask, isCompleted: Boolean? = null): TaskUrgency {
        Log.d("UrgencyConversion", "[determineUrgency] Processing task ${task.id}")
        Log.d("UrgencyConversion", "[determineUrgency] Priority: ${task.priority}")
        
        val taskCompleted = isCompleted ?: (task.completedAt != null)
        Log.d("UrgencyConversion", "[determineUrgency] Completed: $taskCompleted")
        
        val priority = task.priority ?: Priority.MEDIUM
        
        val urgency = when(priority) {
            Priority.URGENT -> TaskUrgency.CRITICAL
            Priority.HIGH -> TaskUrgency.HIGH  
            Priority.MEDIUM -> TaskUrgency.MEDIUM
            Priority.LOW -> TaskUrgency.LOW
        }
        
        Log.d("UrgencyConversion", "[determineUrgency] Priority mapping: $priority -> $urgency")
        Log.d("UrgencyConversion", "[determineUrgency] Final urgency: $urgency")
        Log.d("UrgencyConversion", "[determineUrgency] ===== Conversion complete =====")
        
        return urgency
    }

    /**
     * ????????
     */
    private fun saveUserPreference(key: String, value: String) {
        viewModelScope.launch {
            try {
                // ??? IO ????????
                withContext(Dispatchers.IO) {
                    userPreferenceRepository.savePreference(key, value)
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????: $key", e)
            }
        }
    }
    
    /**
     * ????????
     */
    private suspend fun getUserPreference(key: String, defaultValue: String): String {
        return try {
            // ??? IO ??????
            withContext(Dispatchers.IO) {
                 userPreferenceRepository.getStringPreference(key, defaultValue)
            }
            } catch (e: Exception) {
            Log.e(TAG, "????????: $key", e)
            defaultValue
        }
    }

    /**
     * ????????
     * ?????????????
     */
    fun fixTaskReferences(taskId: String) {
        viewModelScope.launch {
            try {
                Timber.d("??????????: $taskId")
                // ??? IO ?????????
                val success = withContext(Dispatchers.IO) {
                    val transactionHelper = TransactionHelper(taskRepository)
                    val result = transactionHelper.fixTaskReferences(taskId)
                    
                    if (result) {
                        // ????????
                        val task = taskRepository.getTaskById(taskId) // ?? IO ??
                        if (task != null) {
                            transactionHelper.saveTaskWithForeignKeyCheck(task) // ?? IO ??
                        }
                    }
                    result // ??????
                }
                
                if (success) {
                    Timber.d("??????????: $taskId")
                } else {
                    Timber.e("??????????: $taskId")
                    withContext(Dispatchers.Main) {
                        _error.value = "????????"
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "???????????: $taskId")
                withContext(Dispatchers.Main) {
                    _error.value = "?????????: ${e.message}"
                }
            }
        }
    }

    // ??????????
    private fun handleLargeTaskList(tasks: List<ModelTask>): List<ModelTask> {
        if (tasks.size > 500) {
            Timber.d("??????? (${tasks.size}),??????")
            // ?????????,?????????????
            return tasks.sortedByDescending { it.updatedAt }.take(100)
        }
        return tasks
    }

    /**
     * ????????,??????????
     */
    private val taskCacheManager = object {
        private val cache = mutableMapOf<String, Any>()
        private val expirations = mutableMapOf<String, Long>()
        
        fun <T> getOrCompute(key: String, ttl: Long = DEFAULT_TTL_MS, compute: () -> T): T {
            val now = System.currentTimeMillis()
            val expiration = expirations[key] ?: 0L
            
            @Suppress("UNCHECKED_CAST")
            return if (now < expiration && cache.containsKey(key)) {
                cache[key] as T
            } else {
                val result = compute()
                cache[key] = result as Any
                expirations[key] = now + ttl
                result
            }
        }
        
        fun invalidate(key: String) {
            cache.remove(key)
            expirations.remove(key)
        }
        
        fun invalidateAll() {
            cache.clear()
            expirations.clear()
        }
    }

    /**
     * ????????
     */
    fun updateSharedFilterState(filter: String) {
        viewModelScope.launch {
            sharedFilterState.updateFilter(filter)
        }
    }

    /**
     * ???????????
     */
    fun isTaskWithoutDueDate(task: ModelTaskData): Boolean {
        return task.daysLeft == Int.MAX_VALUE
    }

    /**
     * ??????
     */
    fun updateTaskTime(taskId: String, newDateTime: LocalDateTime) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "????[$taskId]??: $newDateTime")
                
                // ??????????,??????????
                val currentTask = withContext(Dispatchers.IO) {
                    taskRepository.getTaskById(taskId)
                }
                
                if (currentTask == null) {
                    Log.e(TAG, "?????,??????: $taskId")
                    return@launch
                }
                
                Log.d(TAG, "????????: $taskId, ???=${currentTask.dueDate}, ???=$newDateTime")
                
                // ??Repository??????,??? IO ??
                withContext(Dispatchers.IO) {
                    // 🔧 修复：同时更新startDate和dueDate，确保日历时间块能正确移动
                    val updatedTask = currentTask.copy(
                        startDate = newDateTime,  // 新增：同时更新开始时间
                        dueDate = newDateTime,
                        // ??????
                        updatedAt = LocalDateTime.now()
                    )
                    
                    // ?????????
                    taskRepository.updateTask(updatedTask)
                    Log.d(TAG, "任务时间已更新: taskId=$taskId, startDate=$newDateTime, dueDate=$newDateTime")
                }
                
                // ?????????UI??
                refreshTasks() // ??????,???????,????????
                
                // ????????
                EventBus.tryEmit(AppEvent.TaskUpdated(taskId))
                
                // ????NotificationCenter??????
                try {
                    // ??TaskRefreshEvent??
                    com.timeflow.app.util.NotificationCenter.post(com.timeflow.app.util.TaskRefreshEvent(taskId))
                    Log.d(TAG, "???TaskRefreshEvent????????")
                } catch (e: Exception) {
                    Log.e(TAG, "??TaskRefreshEvent??,?????????", e)
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    _error.value = "????????: ${e.message}"
                }
            }
        }
    }
    
    /**
     * ??????????
     */
    private fun calculateDaysLeftFromDate(dueDate: LocalDateTime): Int {
        if (dueDate == null) return Int.MAX_VALUE
        
        val today = LocalDate.now()
        val dueLocalDate = dueDate.toLocalDate()
        
        return ChronoUnit.DAYS.between(today, dueLocalDate).toInt()
    }

    /**
     * ?????
     */
    fun deleteSubTask(parentId: String, subTaskId: String) {
        viewModelScope.launch {
            try {
                // ??? IO ????
                withContext(Dispatchers.IO) {
                    taskRepository.deleteTask(subTaskId)
                }
                // ?????????
                EventBus.tryEmit(AppEvent.SubTaskDeleted(parentId, subTaskId))
                // ??????????UI??
                EventBus.tryEmit(AppEvent.TaskUpdated(parentId))
                // ??????
                loadTasks() // ??????
            } catch (e: Exception) {
                Timber.e(e, "???????: $subTaskId")
                withContext(Dispatchers.Main) {
                    _error.value = e.message ?: "???????"
                }
            }
        }
    }
    
    /**
     * ?????? - ?????????????????
     */
    fun updateTaskTitle(taskId: String, newTitle: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "????????: $taskId, ???=$newTitle")
                
                // ??????
                val task = withContext(Dispatchers.IO) {
                    taskRepository.getTaskById(taskId)
                }
                
                if (task != null) {
                    // ???????
                    val updatedTask = task.copy(
                        title = newTitle,
                        updatedAt = LocalDateTime.now()
                    )
                    
                    // ??????
                    withContext(Dispatchers.IO) {
                        taskRepository.updateTask(updatedTask)
                    }
                    
                    // ?????????UI??
                    refreshTasks()
                    
                    // ??????????
                    EventBus.tryEmit(AppEvent.TaskUpdated(taskId))
                    
                    Log.d(TAG, "???????: $taskId")
                } else {
                    Log.e(TAG, "????????: ????? $taskId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????: $taskId, ${e.message}", e)
                withContext(Dispatchers.Main) {
                    _error.value = "????????: ${e.message}"
                }
            }
        }
    }

    /**
     * ????ID???
     * 
     * @param taskId ??ID
     * @return ????,????????null
     */
    suspend fun getTaskById(taskId: String): com.timeflow.app.data.model.Task? {
        return try {
            Log.d(TAG, "??ID????: $taskId")
            withContext(Dispatchers.IO) {
                taskRepository.getTaskById(taskId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "??????", e)
            null
        }
    }

    /**
     * ??????
     */
    fun setCurrentTask(taskId: String) {
        viewModelScope.launch {
            try {
                val task = taskRepository.getTaskById(taskId)
                if (task != null) {
                    _selectedTask.value = TaskConverter.toEntityTask(task)
                    
                    // ?????
                    loadSubTasksForTask(taskId)
                }
            } catch (e: Exception) {
                Log.e(TAG, "????????: $taskId", e)
            }
        }
    }

    /**
     * ????????????????
     * 
     * @param taskId ??ID
     * @param feedback ?????????
     */
    fun submitTaskFeedback(taskId: String, feedback: com.timeflow.app.ui.screen.task.model.FeedbackData) {
        viewModelScope.launch {
            try {
                android.util.Log.d(TAG, "===== START TASK FEEDBACK SUBMISSION =====")
                android.util.Log.d(TAG, "Task ID: $taskId")
                android.util.Log.d(TAG, "Emotion: ${feedback.emotion}")
                android.util.Log.d(TAG, "Comment: ${feedback.comment}")
                android.util.Log.d(TAG, "Image paths: ${feedback.imagePaths}")
                android.util.Log.d(TAG, "Timestamp: ${feedback.timestamp}")
                
                // 1. Get task
                val entityTask = taskRepository.getTaskById(taskId)
                if (entityTask == null) {
                    android.util.Log.e(TAG, "Task not found, taskId: $taskId")
                    return@launch
                }
                
                android.util.Log.d(TAG, "Found task: ${entityTask.title}")
                
                // 2. Create reflection if user provided feedback
                if (feedback.emotion != null || (feedback.comment != null && feedback.comment.isNotBlank()) || feedback.imagePaths.isNotEmpty()) {
                    try {
                        android.util.Log.d(TAG, "Creating reflection...")
                        
                        // Map emoji to MoodType
                        val moodType = mapEmotionToMood(feedback.emotion)
                        android.util.Log.d(TAG, "Emotion mapping: ${feedback.emotion} -> $moodType")
                        
                        // Build reflection content
                        val reflectionContent = when {
                            feedback.comment != null && feedback.comment.isNotBlank() -> {
                                android.util.Log.d(TAG, "Using user comment as content")
                                feedback.comment.trim()
                            }
                            feedback.emotion != null -> {
                                android.util.Log.d(TAG, "Using emotion as content")
                                "Task completed with feeling: ${feedback.emotion}"
                            }
                            feedback.imagePaths.isNotEmpty() -> {
                                android.util.Log.d(TAG, "Using image description as content")
                                "Task completed with ${feedback.imagePaths.size} image(s)"
                            }
                            else -> {
                                android.util.Log.d(TAG, "Using default content")
                                "Task completed"
                            }
                        }
                        
                        // Build rich content blocks including images
                        val richContentBlocks = mutableListOf<ContentBlock>()
                        
                        // Add text content
                        if (reflectionContent.isNotBlank()) {
                            richContentBlocks.add(ContentBlock("text", reflectionContent))
                        }
                        
                        // Add image blocks
                        feedback.imagePaths.forEach { imagePath ->
                            richContentBlocks.add(ContentBlock("image", imagePath))
                            android.util.Log.d(TAG, "Added image block: $imagePath")
                        }
                        
                        android.util.Log.d(TAG, "Reflection content: $reflectionContent")
                        
                        val reflectionId = UUID.randomUUID().toString()
                        val reflection = Reflection(
                            id = reflectionId,
                            title = "✓ ${entityTask.title}",
                            content = reflectionContent,
                            richContent = richContentBlocks,
                            date = feedback.timestamp.atZone(ZoneId.systemDefault()).toInstant(),
                            rating = 5, // ?????
                            tags = listOf("Task Completion", "Work"),
                            type = ReflectionType.WORK,
                            mood = moodType, // User selected mood
                            plans = emptyList(),
                            backgroundImage = null,
                            // ??????????metrics??
                            metrics = mapOf(
                                "taskId" to taskId,
                                "taskTitle" to entityTask.title,
                                "completedAt" to feedback.timestamp.toString(),
                                "emotion" to (feedback.emotion ?: "none"),
                                "hasComment" to (feedback.comment?.isNotBlank() == true).toString(),
                                "hasImages" to feedback.imagePaths.isNotEmpty().toString(),
                                "imageCount" to feedback.imagePaths.size.toString()
                            )
                        )
                        
                        android.util.Log.d(TAG, "Reflection created: id=$reflectionId, mood=$moodType")
                        
                        // Save reflection
                        android.util.Log.d(TAG, "Saving reflection...")
                        reflectionRepository.saveReflection(reflection)
                        android.util.Log.d(TAG, "Reflection saved: reflectionId=${reflection.id}")
                        android.util.Log.d(TAG, "Content: ${reflectionContent.take(50)}")
                        android.util.Log.d(TAG, "Mood: ${reflection.mood}")
                        
                        // Post events
                        try {
                            android.util.Log.d(TAG, "Posting reflection event...")
                            NotificationCenter.post(ReflectionCreatedEvent(reflectionId, reflection))
                            android.util.Log.d(TAG, "Reflection event posted")
                            
                            android.util.Log.d(TAG, "Posting task refresh event...")
                            NotificationCenter.post(TaskRefreshEvent(taskId))
                            android.util.Log.d(TAG, "Task refresh event posted")
                        } catch (e: Exception) {
                            android.util.Log.e(TAG, "Error posting events", e)
                        }
                        
                        android.util.Log.d(TAG, "Reflection processing completed")
                        
                    } catch (e: Exception) {
                        android.util.Log.e(TAG, "Error creating reflection", e)
                    }
                } else {
                    android.util.Log.d(TAG, "No feedback provided, skipping reflection creation")
                }
                
                // 3. Update task completion status
                android.util.Log.d(TAG, "Updating task status...")
                if (entityTask.completedAt == null) {
                    android.util.Log.d(TAG, "Marking task as completed...")
                    val updatedTask = entityTask.copy(
                        completedAt = feedback.timestamp,
                        status = "completed"
                    )
                    taskRepository.updateTask(updatedTask)
                    android.util.Log.d(TAG, "Task completion updated: $taskId")
                } else {
                    android.util.Log.d(TAG, "Task already completed, skipping update")
                }
                
                // 4. Refresh task list
                android.util.Log.d(TAG, "Refreshing task list...")
                loadTasks()
                android.util.Log.d(TAG, "Task list refreshed")
                
                android.util.Log.d(TAG, "===== TASK FEEDBACK SUBMISSION COMPLETED =====")
                
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error in feedback submission", e)
            }
        }
    }
    
    /**
     * ?????????,???????????
     */
    private fun buildReflectionContent(feedback: com.timeflow.app.ui.screen.task.model.FeedbackData): String {
        val parts = mutableListOf<String>()
        
        // ???????,??????
        if (feedback.emotion != null) {
            parts.add("??:${feedback.emotion}")
        }
        
        // ???????,??????
        if (feedback.comment != null && feedback.comment.isNotBlank()) {
            parts.add("??:${feedback.comment}")
        }
        
        // ?????,??????
        if (parts.isEmpty()) {
            return "?????"
        }
        
        // ??????????
        return parts.joinToString("\n")
    }
    
    /**
     * ???emoji????????????
     */
    private fun mapEmotionToMood(emoji: String?): MoodType {
        return when (emoji) {
            "??" -> MoodType.HAPPY
            "??" -> MoodType.CALM
            "??" -> MoodType.SAD
            "??" -> MoodType.ANGRY
            "??" -> MoodType.ANXIOUS
            else -> MoodType.CALM // ????
        }
    }

    /**
     * ??????????
     */
    fun loadSubTasksForTask(taskId: String) {
        viewModelScope.launch {
            try {
                val subTasks = withContext(Dispatchers.IO) {
                    taskRepository.getSubTasks(taskId)
                }
                
                _subTasks.value = subTasks
                Log.d(TAG, "???${subTasks.size}????, ???ID: $taskId")
            } catch (e: Exception) {
                Log.e(TAG, "???????", e)
            }
        }
    }

    /**
     * ????ID???(???????,???Composable?????)
     * ?????ViewModel?????????
     * 
     * @param taskId ??ID
     * @return ????????null
     */
    fun getTaskByIdSync(taskId: String): com.timeflow.app.data.model.Task? {
        var result: com.timeflow.app.data.model.Task? = null
        runBlocking {
            result = taskRepository.getTaskById(taskId)
        }
        
        return result
    }
    
    /**
     * ???????????????
     * @param taskId ???ID
     * @param callback ????,????????????
     */
    fun loadSubTasksForTask(taskId: String, callback: (List<com.timeflow.app.data.model.Task>) -> Unit) {
        viewModelScope.launch {
            try {
                val subTasks = withContext(Dispatchers.IO) {
                    taskRepository.getSubTasks(taskId)
                }
                
                withContext(Dispatchers.Main) {
                    callback(subTasks)
                    Log.d(TAG, "???${subTasks.size}????, ???ID: $taskId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "???????", e)
                withContext(Dispatchers.Main) {
                    callback(emptyList())
                }
            }
        }
    }

    /**
     * ???????????
     * @param taskId ??ID
     */
    suspend fun clearTaskCache(taskId: String) {
        try {
            Log.d(TAG, "??????: $taskId")
            
            // ??taskRepository????,?????????
            if (taskRepository is com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache) {
                Log.d(TAG, "??????,??????????")
                // ???????????????
                taskRepository.clearCache()
            }
            
            Log.d(TAG, "????????: $taskId")
        } catch (e: Exception) {
            Log.e(TAG, "????????: ${e.message}", e)
        }
    }

    /**
     * ??????? - ????,?????????
     * @param taskId ??ID
     * @param priority ?????
     */
    fun updateTaskPriority(taskId: String, priority: Priority) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "[updateTaskPriority] ===== ????????? =====")
                Log.d(TAG, "[updateTaskPriority] ??ID: $taskId")
                Log.d(TAG, "[updateTaskPriority] ????: $priority")
                
                // ??0: ??????????
                Log.d(TAG, "[updateTaskPriority] ??0: ??????...")
                withContext(Dispatchers.IO) {
                    try {
                        clearTaskCache(taskId)
                        // ??????
                        if (taskRepository is com.timeflow.app.ui.task.components.common.cache.TaskRepositoryCache) {
                            taskRepository.clearCache()
                        }
                        Log.d(TAG, "[updateTaskPriority] ? ??????")
                    } catch (e: Exception) {
                        Log.w(TAG, "[updateTaskPriority] ??????,????: ${e.message}")
                    }
                }
                
                withContext(Dispatchers.IO) {
                    // 1. ??????,?????
                    Log.d(TAG, "[updateTaskPriority] ??1: ????????...")
                    var task: ModelTask? = null
                    var retryCount = 0
                    while (task == null && retryCount < 3) {
                        task = taskRepository.getTaskById(taskId)
                        if (task == null) {
                            retryCount++
                            Log.w(TAG, "[updateTaskPriority] ??????,?? $retryCount/3")
                            delay((100 * retryCount).toLong()) // ????,???Long
                        }
                    }
                    
                    if (task != null) {
                        Log.d(TAG, "[updateTaskPriority] ? ????: ${task.title}")
                        Log.d(TAG, "[updateTaskPriority] ????: ${task.priority}")
                        
                        // 2. ????????
                        val updatedTask = task.copy(
                            priority = priority,
                            updatedAt = LocalDateTime.now()
                        )
                        Log.d(TAG, "[updateTaskPriority] ??2: ??????,????: ${updatedTask.priority}")
                        
                        // 3. ??????,?????
                        Log.d(TAG, "[updateTaskPriority] ??3: ????????...")
                        var saveSuccess = false
                        var saveRetryCount = 0
                        while (!saveSuccess && saveRetryCount < 3) {
                            try {
                                taskRepository.updateTask(updatedTask)
                                saveSuccess = true
                                Log.d(TAG, "[updateTaskPriority] ? ???????")
                            } catch (e: Exception) {
                                saveRetryCount++
                                Log.w(TAG, "[updateTaskPriority] ???????,?? $saveRetryCount/3: ${e.message}")
                                if (saveRetryCount < 3) {
                                    delay((200 * saveRetryCount).toLong())
                                } else {
                                    throw e
                                }
                            }
                        }
                        
                        // 4. ??????,?????
                        Log.d(TAG, "[updateTaskPriority] ??4: ??????...")
                        var verifySuccess = false
                        var verifyRetryCount = 0
                        while (!verifySuccess && verifyRetryCount < 5) {
                            delay((100 + (verifyRetryCount * 50)).toLong()) // ??????????,???Long
                            val verifyTask = taskRepository.getTaskById(taskId)
                            if (verifyTask != null) {
                                Log.d(TAG, "[updateTaskPriority] ???? - ???: ${verifyTask.priority}")
                                if (verifyTask.priority == priority) {
                                    Log.d(TAG, "[updateTaskPriority] ? ?????????")
                                    verifySuccess = true
                                } else {
                                    verifyRetryCount++
                                    Log.w(TAG, "[updateTaskPriority] ????,?? $verifyRetryCount/5: ??=$priority, ??=${verifyTask.priority}")
                                }
                            } else {
                                verifyRetryCount++
                                Log.w(TAG, "[updateTaskPriority] ????,??????,?? $verifyRetryCount/5")
                            }
                        }
                        
                        if (!verifySuccess) {
                            Log.e(TAG, "[updateTaskPriority] ? ???????????")
                            throw RuntimeException("????????????")
                        }
                        
                    } else {
                        val errorMsg = "?????: $taskId"
                        Log.e(TAG, "[updateTaskPriority] ? $errorMsg")
                        throw IllegalArgumentException(errorMsg)
                    }
                }
                
                // 5. ????????UI??
                Log.d(TAG, "[updateTaskPriority] ??5: ????????...")
                
                // 5a. ????????(???)
                Log.d(TAG, "[updateTaskPriority] 5a: ?????????...")
                refreshTasks()
                
                // 5b. ????????(???,??????)
                delay(300)
                Log.d(TAG, "[updateTaskPriority] 5b: ?????????...")
                refreshTasks()
                
                // 5c. ????UI??
                Log.d(TAG, "[updateTaskPriority] 5c: ????UI??...")
                withContext(Dispatchers.Main) {
                    // ????UI??
                    val currentState = _taskListState.value
                    _taskListState.value = currentState.copy(
                        lastUpdateTime = System.currentTimeMillis()
                    )
                    Log.d(TAG, "[updateTaskPriority] ? UI????????")
                }
                
                // 6. ????????(????????)
                Log.d(TAG, "[updateTaskPriority] ??6: ????????...")
                try {
                    NotificationCenter.post(TaskRefreshEvent(taskId))
                    delay(100)
                    NotificationCenter.post(TaskRefreshEvent(taskId)) // ????????
                    Log.d(TAG, "[updateTaskPriority] ? ?????????")
                } catch (e: Exception) {
                    Log.w(TAG, "[updateTaskPriority] ????????: ${e.message}")
                }
                
                // 7. ????????
                delay(200)
                Log.d(TAG, "[updateTaskPriority] ??7: ????????...")
                val finalTasks = _taskListState.value.tasks
                val targetTask = finalTasks.find { it.id == taskId }
                if (targetTask != null) {
                    // ?????UI????
                    val expectedUrgency = when(priority) {
                        Priority.URGENT -> TaskUrgency.CRITICAL
                        Priority.HIGH -> TaskUrgency.HIGH  
                        Priority.MEDIUM -> TaskUrgency.MEDIUM
                        Priority.LOW -> TaskUrgency.LOW
                    }
                    Log.d(TAG, "[updateTaskPriority] ???? - ??UI????: ${targetTask.urgency}, ??: $expectedUrgency")
                    if (targetTask.urgency == expectedUrgency) {
                        Log.d(TAG, "[updateTaskPriority] ? UI??????")
                    } else {
                        Log.w(TAG, "[updateTaskPriority] ? UI??????,???????")
                    }
                } else {
                    Log.w(TAG, "[updateTaskPriority] ? ????:?UI????????????")
                }
                
                Log.d(TAG, "[updateTaskPriority] ===== ????????? =====")
                
            } catch (e: Exception) {
                Log.e(TAG, "[updateTaskPriority] ? ?????????", e)
                withContext(Dispatchers.Main) {
                    _error.value = "?????????: ${e.localizedMessage ?: "????"}"
                }
                
                // ?????????UI,??????
                try {
                    refreshTasks()
                } catch (refreshError: Exception) {
                    Log.e(TAG, "[updateTaskPriority] ????????", refreshError)
                }
                
                throw e // ??????,?????????
            }
        }
    }

    /**
     * 乐观更新任务状态 - 立即更新UI，异步更新数据库
     * 修复复选框立即响应问题
     */
    fun updateTaskStatusOptimistic(taskId: String, isCompleted: Boolean) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始乐观更新任务状态: $taskId -> $isCompleted")
                
                // 步骤1：立即更新UI状态（乐观更新）
                mutex.withLock {
                    val currentState = _taskListState.value
                    val updatedTasks = currentState.tasks.map { task ->
                        if (task.id == taskId) {
                            // 立即更新：复选框状态，确保UI立即响应
                            task.copy(
                                isCompleted = isCompleted
                                // 其他字段保持不变，避免数据丢失
                            )
                        } else {
                            task
                        }
                    }
                    
                    // 立即触发UI重组
                    _taskListState.update { state ->
                        state.copy(
                            tasks = updatedTasks,
                            lastUpdateTime = System.currentTimeMillis() // 触发UI重组
                        )
                    }
                    
                    Log.d(TAG, "任务 $taskId UI状态已立即更新为 $isCompleted，等待数据库同步")
                }
                
                // 步骤2：异步更新数据库
                launch {
                    try {
                        Log.d(TAG, "开始异步更新数据库: $taskId")
                        
                        // 关键修复：使用完整的任务更新，包含completedAt时间戳
                        withContext(Dispatchers.IO) {
                            val currentTask = taskRepository.getTaskById(taskId)
                            if (currentTask != null) {
                                val updatedTask = currentTask.copy(
                                    isCompleted = isCompleted,
                                    completedAt = if (isCompleted) LocalDateTime.now() else null,
                                    updatedAt = LocalDateTime.now()
                                )
                                taskRepository.updateTask(updatedTask)
                                Log.d(TAG, "任务 $taskId 数据库更新成功:completedAt=${updatedTask.completedAt}")
                            } else {
                                // 如果任务不存在，使用简单更新
                                taskRepository.updateTaskCompletionStatus(taskId, isCompleted)
                                Log.d(TAG, "任务 $taskId 简单状态更新完成")
                            }
                        }
                        
                        // 触发刷新事件
                        _refreshTrigger.value = _refreshTrigger.value + 1
                        
                        // 发送全局事件
                        NotificationCenter.post(TaskRefreshEvent(taskId))
                        
                        Log.d(TAG, "任务 $taskId 状态更新完全成功")
                        
                    } catch (e: Exception) {
                        if (e is CancellationException) throw e
                        
                        Log.e(TAG, "任务 $taskId 数据库更新失败，回滚UI状态", e)
                        
                        // 数据库更新失败，回滚UI状态
                        mutex.withLock {
                            val currentState = _taskListState.value
                            val revertedTasks = currentState.tasks.map { task ->
                                if (task.id == taskId) {
                                    task.copy(
                                        isCompleted = !isCompleted // 回滚到原始状态
                                        // 其他字段保持不变，避免数据丢失
                                    )
                                } else {
                                    task
                                }
                            }
                            
                            _taskListState.update { state ->
                                state.copy(
                                    tasks = revertedTasks,
                                    lastUpdateTime = System.currentTimeMillis()
                                )
                            }
                            
                            Log.d(TAG, "任务 $taskId UI状态已回滚")
                        }
                        
                        _error.value = "状态更新失败: ${e.message}"
                    }
                }
                
            } catch (e: Exception) {
                if (e is CancellationException) throw e
                
                Log.e(TAG, "乐观更新初始化失败: ${e.message}", e)
                _error.value = "状态更新失败: ${e.message}"
            }
        }
    }

    // === 🗑️ 任务删除功能 ===
    
    // 🌐 使用全局待删除状态管理器
    val pendingDeletions = sharedPendingDeletionState.pendingDeletions
    
    // 撤销删除事件
    private val _undoDeleteEvent = MutableSharedFlow<String>()
    val undoDeleteEvent = _undoDeleteEvent.asSharedFlow()
    
    // 软删除任务(可撤销)
    fun deleteTaskWithUndo(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始软删除任务(可撤销): $taskId")
                
                // 1. 获取任务实体
                val taskEntity = withContext(Dispatchers.IO) {
                    taskRepository.getTaskById(taskId)
                }
                
                if (taskEntity == null) {
                    Log.e(TAG, "找不到指定任务: $taskId")
                    _error.value = "找不到指定任务"
                    return@launch
                }
                
                // 2. 🌐 使用全局状态管理器标记为待删除
                sharedPendingDeletionState.markForDeletion(taskId, taskEntity.title)
                
                // 3. 🔧 发送删除消息，通知UI显示Snackbar
                _deleteMessage.emit(DeleteMessage(
                    taskId = taskId,
                    taskTitle = taskEntity.title
                ))
                
                // 4. 📢 发送删除事件，通知其他页面同步删除
                EventBus.tryEmit(AppEvent.TaskDeleted(taskId))
                Log.d(TAG, "📢 已发送TaskDeleted事件，通知其他页面: $taskId")
                
                // 5. 🔄 立即触发UI刷新，确保删除效果立即可见
                delay(100) // 短暂延迟确保状态已更新
                refreshTasks()
                
                Log.d(TAG, "✅ 任务已标记为待删除（60秒后永久删除）: $taskId")
                Log.d(TAG, "🔄 已触发UI刷新以反映删除状态")
                
            } catch (e: Exception) {
                Log.e(TAG, "软删除任务失败: $taskId", e)
                _error.value = "删除任务失败: ${e.message}"
            }
        }
    }
    
    // 撤销删除任务
    fun undoDeleteTask(taskId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "🔄 撤销删除任务: $taskId")
                
                // 🌐 使用全局状态管理器撤销删除
                val success = sharedPendingDeletionState.undoDeletion(taskId)
                
                if (success) {
                    // 发送撤销删除事件
                    _undoDeleteEvent.emit(taskId)
                    
                    // 📢 发送任务恢复事件，通知其他页面同步恢复
                    EventBus.tryEmit(AppEvent.TaskUpdated(taskId))
                    Log.d(TAG, "📢 已发送TaskUpdated事件，通知其他页面恢复任务: $taskId")
                    
                    // 刷新任务列表
                    refreshTasks()
                    
                    Log.d(TAG, "✅ 成功撤销删除任务: $taskId")
                } else {
                    Log.e(TAG, "❌ 撤销删除失败，任务可能已过期: $taskId")
                    _error.value = "撤销删除失败，任务可能已过期"
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "撤销删除任务失败: $taskId", e)
                _error.value = "撤销删除失败: ${e.message}"
            }
        }
    }
    
    // 清理过期的待删除任务
    fun cleanupExpiredPendingDeletions() {
        viewModelScope.launch {
            Log.d(TAG, "🧹 开始清理过期的待删除任务...")
            
            // 🌐 使用全局状态管理器清理过期任务
            sharedPendingDeletionState.cleanupExpiredDeletions()
            
            // 刷新任务列表以反映清理结果
            refreshTasks()
            
            Log.d(TAG, "🧹 过期待删除任务清理完成")
        }
    }
    
    // 清空所有待删除任务(用于测试或重置)
    fun clearAllPendingDeletions() {
        viewModelScope.launch {
            Log.d(TAG, "🗑️ 清空所有待删除任务")
            
            // 🌐 使用全局状态管理器清空所有待删除任务
            sharedPendingDeletionState.clearAllPendingDeletions()
            
            // 刷新任务列表
            refreshTasks()
            
            Log.d(TAG, "🗑️ 所有待删除任务已清空")
        }
    }
    
    /**
     * 🔧 修复数据库中的任务完成状态不一致问题
     * 将所有status为"已完成"但completedAt为null的任务进行统一
     */
    fun fixTaskCompletionStatus() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "===== 开始修复任务完成状态不一致问题 =====")
                
                withContext(Dispatchers.IO) {
                    // 获取所有任务
                    val allTasks = taskRepository.getAllTasks()
                    var fixedCount = 0
                    
                    Log.d(TAG, "总任务数: ${allTasks.size}")
                    
                    allTasks.forEach { task ->
                        var needsUpdate = false
                        var updatedTask = task
                        
                        // 情况1：status为"已完成"但completedAt为null
                        if (task.status == "已完成" && task.completedAt == null) {
                            Log.d(TAG, "修复任务 ${task.id}: status=已完成但completedAt=null")
                            updatedTask = task.copy(
                                completedAt = LocalDateTime.now(),
                                updatedAt = LocalDateTime.now()
                            )
                            needsUpdate = true
                        }
                        // 情况2：completedAt不为null但status不为"已完成"
                        else if (task.completedAt != null && task.status != "已完成") {
                            Log.d(TAG, "修复任务 ${task.id}: completedAt存在但status=${task.status}")
                            updatedTask = task.copy(
                                status = "已完成",
                                updatedAt = LocalDateTime.now()
                            )
                            needsUpdate = true
                        }
                        // 情况3：status为"待办"或其他，但completedAt不为null
                        else if (task.status != "已完成" && task.completedAt != null) {
                            Log.d(TAG, "修复任务 ${task.id}: status=${task.status}但completedAt存在")
                            updatedTask = task.copy(
                                completedAt = null,
                                updatedAt = LocalDateTime.now()
                            )
                            needsUpdate = true
                        }
                        
                        if (needsUpdate) {
                            taskRepository.updateTask(updatedTask)
                            fixedCount++
                            Log.d(TAG, "✓ 已修复任务 ${task.id}")
                        }
                    }
                    
                    Log.d(TAG, "===== 任务完成状态修复完成，共修复 $fixedCount 个任务 =====")
                }
                
                // 修复完成后刷新任务列表
                refreshTasks()
                
            } catch (e: Exception) {
                Log.e(TAG, "修复任务完成状态时发生错误", e)
                _error.value = "修复任务状态失败: ${e.message}"
            }
        }
    }
    
    /**
     * 立即更新任务列表状态（仅UI层面，不触发数据库操作）
     * 用于复选框等UI组件的即时响应
     */
    fun updateTaskListStateImmediate(updatedTasks: List<ModelTaskData>) {
        Log.d(TAG, "[updateTaskListStateImmediate] ===== 开始立即更新UI状态 =====")
        Log.d(TAG, "[updateTaskListStateImmediate] 更新任务数量: ${updatedTasks.size}")
        
        try {
            // 立即更新UI状态，确保复选框等组件即时响应
            _taskListState.update { currentState ->
                currentState.copy(
                    tasks = updatedTasks,
                    lastUpdateTime = System.currentTimeMillis(), // 触发UI重组
                    isLoading = false // 确保加载状态为false
                )
            }
            
            Log.d(TAG, "[updateTaskListStateImmediate] ✓ UI状态更新完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "[updateTaskListStateImmediate] ✗ UI状态更新失败", e)
        }
        
        Log.d(TAG, "[updateTaskListStateImmediate] ===== UI状态更新处理完成 =====")
    }
    
    /**
     * 增强的任务转换方法，能够正确填充目标标题
     * @param task 数据库中的任务实体
     * @return 包含完整目标信息的ModelTaskData
     */
    private suspend fun convertTaskToModelTaskDataWithGoalInfo(task: com.timeflow.app.data.model.Task): ModelTaskData {
        return withContext(Dispatchers.IO) {
            // 基础转换 - 使用TaskAdapters中的转换方法
            val baseTaskData = com.timeflow.app.ui.screen.task.convertTaskToModelTaskData(task)
            
            // 如果有目标ID，获取目标标题
            val goalTitle = if (task.goalId != null) {
                try {
                    Log.d(TAG, "查询目标标题: goalId=${task.goalId}")
                    val goal = goalRepository.getGoalById(task.goalId)
                    val title = goal?.title
                    Log.d(TAG, "获取到目标标题: goalId=${task.goalId}, title=$title")
                    title
                } catch (e: Exception) {
                    Log.w(TAG, "获取目标标题失败: goalId=${task.goalId}", e)
                    null
                }
            } else {
                null
            }
            
            // 返回包含目标信息的任务数据
            baseTaskData.copy(goalTitle = goalTitle)
        }
    }
    
    /**
     * 根据目标ID获取关联的任务
     */
    fun getTasksByGoalId(goalId: String): kotlinx.coroutines.flow.Flow<List<com.timeflow.app.data.model.Task>> {
        return kotlinx.coroutines.flow.flow {
            try {
                Log.d(TAG, "获取目标关联任务: goalId=$goalId")
                // 从数据库获取与目标关联的任务
                val relatedTasks = taskRepository.getTasksByGoalId(goalId)
                Log.d(TAG, "找到 ${relatedTasks.size} 个关联任务")
                emit(relatedTasks)
            } catch (e: Exception) {
                Log.e(TAG, "获取目标关联任务失败: goalId=$goalId", e)
                // 不要在catch块中emit，这会导致Flow异常
                // 直接抛出异常让上层处理
                throw e
            }
        }.catch { throwable ->
            // 使用Flow.catch操作符正确处理异常
            Log.e(TAG, "Flow异常处理: goalId=$goalId", throwable)
            emit(emptyList())
        }
    }
    
}
