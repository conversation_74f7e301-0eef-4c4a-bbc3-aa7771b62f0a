package com.timeflow.app.utils

import android.app.Activity
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Choreographer
import android.view.SurfaceControl
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.annotation.IntRange
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import android.os.SystemClock
import android.content.Context
import android.app.ActivityManager
import kotlin.math.roundToInt
import android.graphics.drawable.RippleDrawable

/**
 * 渲染优化器
 * 用于解决日志中显示的HwcComposer LUTs相关问题和渲染崩溃
 */
object RenderOptimizer {
    // 公开给内联函数使用
    const val TAG = "RenderOptimizer"
    
    // 性能监控相关常量
    private const val FRAME_THRESHOLD_MS = 16 // 60FPS的帧时间
    private const val SEVERE_FRAME_THRESHOLD_MS = 32 // 30FPS的帧时间
    private const val MEMORY_CHECK_INTERVAL_MS = 5000L // 5秒检查一次内存
    private const val MEMORY_WARNING_THRESHOLD = 0.8 // 内存使用率警告阈值
    
    // 性能数据统计
    private var frameCount = 0
    private var jankCount = 0
    private var lastFrameTime = 0L
    private var lastMemoryCheck = 0L
    private var totalFrameTime = 0L
    private var lastFrameTimeNanos = 0L
    
    // 性能监控状态
    private var isMonitoring = false
    private var monitoringThread: Thread? = null
    
    // 配置
    private var isGpuOptimizationEnabled = true
    private var isBinderOptimizationEnabled = true
    private var isMemoryTrackingEnabled = true
    
    // 硬件渲染相关常量
    private const val MAX_SAFE_REFRESH_RATE = 60
    private const val HWC_FALLBACK_ENABLED = true
    
    // 用于检测HWC渲染问题的状态
    private var hwcErrorCount = 0
    private var isHwcFallbackActive = false
    
    // 安全渲染相关参数
    private const val MAX_RETRY_COUNT = 3
    private const val FORCE_SOFTWARE_RENDER_THRESHOLD = 5
    
    // 状态跟踪
    private var isHardwareRenderingEnabled = true
    
    // 黑边修复相关
    private var blackBorderFixEnabled = true
    private var safeRenderModeEnabled = true
    
    // 性能监控
    private var lastPerformanceCheck = 0L
    private const val PERFORMANCE_CHECK_INTERVAL = 5000L // 5秒检查一次
    
    // RippleDrawable优化相关常量
    private const val MAX_RIPPLE_DURATION = 200L // 最大水波纹动画持续时间
    private const val RIPPLE_ALPHA = 0.5f // 水波纹透明度
    private const val RIPPLE_RADIUS = 20f // 水波纹半径
    
    // 帧回调处理状态
    private var frameCallbackPosted = false
    private var frameCallback: Choreographer.FrameCallback? = null
    
    /**
     * 为Activity设置渲染优化
     */
    fun setupWindowOptimizations(activity: Activity) {
        try {
            val window = activity.window
            
            // 立即应用关键优化
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 设置窗口格式为RGBA_8888
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 设置窗口背景为透明度较低的黑色，减少渲染负担
            window.decorView.setBackgroundColor(android.graphics.Color.BLACK)
            
            // 为根视图启用硬件加速
            window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            
            // 延迟应用其他优化，确保主线程不会被阻塞
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    // 优化RippleDrawable
                    optimizeRippleDrawables(window.decorView)
                    
                    // 设置帧回调监控
                    setupFrameCallback(window)
                    
                    Log.d(TAG, "已应用窗口渲染优化")
                } catch (e: Exception) {
                    Log.e(TAG, "应用窗口优化失败: ${e.message}")
                }
            }, 100)
            
        } catch (e: Exception) {
            Log.e(TAG, "设置窗口优化失败: ${e.message}")
        }
    }
    
    /**
     * 优化RippleDrawable - 减少反射操作，直接设置硬件加速
     */
    private fun optimizeRippleDrawables(view: View) {
        try {
            // 使用常量限制处理深度，避免过深递归
            optimizeRippleDrawablesInternal(view, 0, 5)
        } catch (e: Exception) {
            Log.e(TAG, "处理RippleDrawable时出错: ${e.message}")
        }
    }
    
    /**
     * 内部RippleDrawable优化实现，添加深度限制
     */
    private fun optimizeRippleDrawablesInternal(view: View, currentDepth: Int, maxDepth: Int) {
        // 超过最大深度则返回，避免过深递归
        if (currentDepth > maxDepth) return
        
        // 为每个可点击的视图启用硬件加速，但仅处理真正需要的视图
        if (view.isClickable || view is android.widget.Button || view is androidx.appcompat.widget.AppCompatButton) {
            // 如果已经是硬件加速，则不重复设置
            if (view.layerType != View.LAYER_TYPE_HARDWARE) {
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
        }
        
        // 处理背景为RippleDrawable的情况
        if (view.background is RippleDrawable) {
            // 强制为所有RippleDrawable所在视图启用硬件加速
            if (view.layerType != View.LAYER_TYPE_HARDWARE) {
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
            
            // 设置透明度以减轻渲染压力，不使用反射
            (view.background as RippleDrawable).alpha = 200 // 约78%透明度
        }
        
        // 递归处理子视图
        if (view is ViewGroup) {
            // 限制每次处理的子视图数量
            val childCount = view.childCount
            
            // 小优化：对于大型ViewGroup，随机抽样处理子视图，避免处理所有子视图
            val shouldProcessAllChildren = childCount <= 20 || currentDepth <= 1
            val batchSize = if (shouldProcessAllChildren) childCount else minOf(10, childCount)
            
            // 使用Handler在空闲时处理子视图，避免阻塞主线程
            val handler = Handler(Looper.getMainLooper())
            
            for (i in 0 until batchSize) {
                val childIndex = if (shouldProcessAllChildren) i else (Math.random() * childCount).toInt()
                if (childIndex < childCount) {
                    val child = view.getChildAt(childIndex)
                    
                    // 对于同一层次的视图使用Handler延迟处理，不同层次直接处理
                    if (currentDepth == 0 && childCount > 10) {
                        // 使用Handler错开处理时间，避免主线程阻塞
                        handler.postDelayed({
                            optimizeRippleDrawablesInternal(child, currentDepth + 1, maxDepth)
                        }, i * 5L) // 5ms间隔，错开处理时间
                    } else {
                        // 直接处理子视图
                        optimizeRippleDrawablesInternal(child, currentDepth + 1, maxDepth)
                    }
                }
            }
        }
    }
    
    /**
     * 应用防止黑边的高级安全渲染修饰符
     * 结合了用户提供的渲染优化技术来防止HwcComposer getLuts错误造成的黑边
     */
    fun Modifier.preventBlackBorders() = composed {
        if (!blackBorderFixEnabled) return@composed this
        
        // 本地状态
        val view = LocalView.current
        var isRenderProblemDetected by remember { mutableStateOf(false) }
        var isUnsupported8Detected by remember { mutableStateOf(false) }
        
        // 检测渲染问题
        LaunchedEffect(Unit) {
            // 检查是否需要特殊处理
            val activity = view.context as? Activity
            if (activity != null) {
                // 检查已知的渲染问题
                isRenderProblemDetected = checkHwcCapability(activity) || hwcErrorCount > 0
                
                // 使用反射检查是否存在UNSUPPORTED (8)错误的标志
                try {
                    val hwcFixerClass = Class.forName("com.timeflow.app.utils.HwcLutsErrorFixer")
                    val field = hwcFixerClass.getDeclaredField("isUnsupported8ErrorDetected")
                    field.isAccessible = true
                    val value = field.get(null)
                    if (value is java.util.concurrent.atomic.AtomicBoolean) {
                        isUnsupported8Detected = value.get()
                        if (isUnsupported8Detected) {
                            Log.w(TAG, "检测到UNSUPPORTED (8)错误的标志，启用深度优化")
                        }
                    }
                } catch (e: Exception) {
                    // 忽略反射异常
                }
            }
        }
        
        // 应用渲染修复 - 使用不同的策略处理不同级别的问题
        return@composed when {
            // 针对getLuts UNSUPPORTED (8)的深度优化
            isUnsupported8Detected -> {
                this
                    // 强制使用软件渲染模式 - 完全避免使用硬件渲染
                    .drawWithCache {
                        onDrawWithContent {
                            // 首先绘制一个统一的白色背景
                            drawRect(Color.White)
                            // 然后绘制实际内容
                            drawContent()
                        }
                    }
                    // 所有图层配置尽可能简单，避免触发HWC处理
                    .graphicsLayer(
                        alpha = 1.0f,  // 完全不透明
                        shadowElevation = 0f,  // 禁用阴影
                        clip = false,  // 禁用裁剪
                        renderEffect = null,  // 禁用特效
                        rotationX = 0f,
                        rotationY = 0f,
                        rotationZ = 0f,
                        scaleX = 1f,
                        scaleY = 1f,
                        translationX = 0f,
                        translationY = 0f
                    )
                    // 添加空白填充确保内容不会紧贴屏幕边缘
                    .padding(1.dp)
            }
            
            // 普通渲染问题处理
            isRenderProblemDetected -> {
                this
                    // 避免透明度引起的渲染问题
                    .graphicsLayer(alpha = 0.99f)
                    // 使用双层绘制确保内容被完全渲染
                    .drawWithCache {
                        onDrawWithContent {
                            // 先绘制一层不透明背景确保整个区域都被渲染
                            drawRect(Color.Black)
                            // 再绘制前景为白色，确保UI正确渲染
                            drawRect(Color.White)
                            // 最后绘制实际内容
                            drawContent()
                        }
                    }
            }
            
            // 正常情况，轻度优化以预防问题
            else -> this.drawWithCache {
                onDrawWithContent {
                    // 只需正常绘制内容
                    drawContent()
                }
            }
        }
    }
    
    /**
     * 检测设备HWC能力
     */
    private fun checkHwcCapability(context: Activity): Boolean {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val displayManager = context.getSystemService(android.content.Context.DISPLAY_SERVICE) as android.hardware.display.DisplayManager
                val display = displayManager.getDisplay(android.view.Display.DEFAULT_DISPLAY)
                
                // 检查是否支持HDR，这通常与HWC复杂渲染能力相关
                if (display != null) {
                    val hdrCapabilities = display.hdrCapabilities
                    if (hdrCapabilities != null) {
                        // 使用新的API检查HDR支持
                        val hdrTypes = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            hdrCapabilities.supportedHdrTypes
                        } else {
                            @Suppress("DEPRECATION")
                            hdrCapabilities.supportedHdrTypes
                        }
                        
                        if (hdrTypes.isEmpty()) {
                            Log.w(TAG, "HWC可能不支持复杂渲染，启用软件渲染回退")
                            return true // 需要特殊处理
                        }
                    }
                }
            }
            
            // 尝试检测HWC版本
            try {
                val clazz = Class.forName("android.os.SystemProperties")
                val method = clazz.getMethod("get", String::class.java)
                val hwcVer = method.invoke(null, "ro.hardware.hwcomposer") as String
                Log.d(TAG, "HWC版本: $hwcVer")
                
                // 某些已知有问题的HWC版本
                if (hwcVer.contains("qcom") && Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                    Log.w(TAG, "检测到已知有问题的HWC版本，启用软件渲染回退")
                    return true
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取HWC版本失败", e)
            }
            
            // 主动检查是否有HwcComposer getLuts错误
            checkForHwcLutsErrors()
            
            return hwcErrorCount > 0 // 如果已经检测到错误，需要特殊处理
            
        } catch (e: Exception) {
            Log.e(TAG, "检查HWC能力失败: ${e.message}")
            return false
        }
    }
    
    /**
     * 主动检查是否有HwcComposer getLuts错误
     */
    private fun checkForHwcLutsErrors() {
        try {
            // 尝试通过读取系统日志检测错误
            val process = Runtime.getRuntime().exec("logcat -d -t 100 HwcComposer:E HWComposer:E *:S")
            val output = process.inputStream.bufferedReader().use { it.readText() }
            
            // 分析日志查找特定错误
            val lines = output.split("\n")
            for (line in lines) {
                if (line.contains("getLuts failed") || (line.contains("HWComposer") && line.contains("UNSUPPORTED"))) {
                    hwcErrorCount++
                    if (line.contains("UNSUPPORTED (8)")) {
                        // 检测到特定的UNSUPPORTED (8)错误
                        try {
                            // 通知HwcLutsErrorFixer
                            val hwcFixerClass = Class.forName("com.timeflow.app.utils.HwcLutsErrorFixer")
                            val method = hwcFixerClass.getMethod("reportHwcError")
                            method.invoke(null)
                            Log.w(TAG, "检测到getLuts UNSUPPORTED (8)错误，已通知HwcLutsErrorFixer")
                        } catch (e: Exception) {
                            // 忽略反射异常
                        }
                    }
                }
            }
            
            if (hwcErrorCount > 0) {
                Log.w(TAG, "检测到$hwcErrorCount 个HwcComposer相关错误")
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查HwcLuts错误失败: ${e.message}")
        }
    }
    
    /**
     * 应用HWC优化，解决HwcComposer getLuts相关错误
     */
    fun handleHwcLutsError(window: Window) {
        try {
            Log.d(TAG, "应用HwcComposer getLuts错误修复")
            
            // 将窗口颜色模式重置为默认值
            window.colorMode = 0
            
            // 降低渲染复杂度
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 禁用可能触发HWC错误的高级视觉效果
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(true)
            }
            
            // 重置渲染属性
            val attrs = window.attributes
            
            // 根据日夜模式选择适合的状态栏颜色
            val context = window.context
            val isNightMode = (context.resources.configuration.uiMode and 
                android.content.res.Configuration.UI_MODE_NIGHT_MASK) == 
                android.content.res.Configuration.UI_MODE_NIGHT_YES
                
            if (isNightMode) {
                // 夜间模式：不透明深色状态栏
                window.statusBarColor = 0xFF212121.toInt() // 深灰色
                window.navigationBarColor = 0xFF212121.toInt()
            } else {
                // 日间模式：不透明浅色状态栏
                window.statusBarColor = 0xFFF5F5F5.toInt() // 浅灰色
                window.navigationBarColor = 0xFFF5F5F5.toInt()
                
                // 为浅色状态栏设置深色图标
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 使用固定值替代未解析的常量 (8是APPEARANCE_LIGHT_STATUS_BARS的值)
                    window.insetsController?.setSystemBarsAppearance(
                        8, // APPEARANCE_LIGHT_STATUS_BARS
                        8  // APPEARANCE_LIGHT_STATUS_BARS
                    )
                } else {
                    @Suppress("DEPRECATION")
                    window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                            View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                }
            }
            
            window.attributes = attrs
            
            // 报告错误已处理
            hwcErrorCount++
            Log.d(TAG, "已应用HwcComposer getLuts错误修复措施")
        } catch (e: Exception) {
            Log.e(TAG, "应用HwcComposer getLuts错误修复失败: ${e.message}")
        }
    }
    
    /**
     * 解决Binder传输过大问题的修饰符
     * 用于防止绘制大量复杂内容导致的Binder错误
     */
    fun Modifier.binderSafe(): Modifier = composed {
        // 使用drawWithCache提供更好的性能
        drawWithCache {
            // 检测绘制区域大小
            val isLargeDrawingArea = size.width > 2000 || size.height > 2000
            
            // 如果是大型绘制区域，提供简化的绘制操作
            if (isLargeDrawingArea) {
                Log.d(TAG, "检测到大型绘制区域 ${size.width}x${size.height}，应用简化渲染")
            }
            
            onDrawBehind {
                if (isLargeDrawingArea) {
                    // 最小化的绘制操作，避免复杂渲染路径
                    drawRect(Color.White.copy(alpha = 0.01f)) // 几乎透明的背景
                } else {
                    // 正常绘制
                    drawRect(Color.Transparent)
                }
            }
        }
    }
    
    /**
     * 设置帧回调来监控渲染性能
     */
    private fun setupFrameCallback(window: Window) {
        // 如果回调已经设置，则不重复设置
        if (frameCallbackPosted) return
        
        // 帧率监控计数器
        var consecutiveSevereJankCount = 0 // 只在严重卡顿时计数
        var lastRenderModeSwitch = 0L
        val MIN_RENDER_MODE_SWITCH_INTERVAL = 5000L // 增加切换间隔到5秒
        val HW_RENDER_STABILITY_FRAMES = 300 // 需要稳定300帧（约5秒）才切回硬件渲染
        
        frameCallback = object : Choreographer.FrameCallback {
            override fun doFrame(frameTimeNanos: Long) {
                try {
                    // 计算帧间隔
                    if (lastFrameTimeNanos > 0) {
                        val frameTime = TimeUnit.NANOSECONDS.toMillis(frameTimeNanos - lastFrameTimeNanos)
                        
                        // 累计统计数据
                        frameCount++
                        totalFrameTime += frameTime
                        
                        // 记录帧间延迟超过阈值的情况
                        val isSevereJank = frameTime > SEVERE_FRAME_THRESHOLD_MS * 2 // 定义更严格的严重卡顿标准
                        if (isSevereJank) {
                            jankCount++
                            consecutiveSevereJankCount++
                            
                            // 避免频繁切换渲染模式，添加最小间隔检查
                            val currentTime = SystemClock.elapsedRealtime()
                            if (consecutiveSevereJankCount >= 5 && // 需要连续5次严重卡顿才切换
                                currentTime - lastRenderModeSwitch > MIN_RENDER_MODE_SWITCH_INTERVAL) {
                                
                                Log.w(TAG, "检测到连续严重卡顿 ($consecutiveSevereJankCount 次, ${frameTime}ms)，切换到软件渲染")
                                
                                // 切换到软件渲染模式
                                if (isHardwareRenderingEnabled) {
                                    window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                                    isHardwareRenderingEnabled = false
                                    lastRenderModeSwitch = currentTime
                                    consecutiveSevereJankCount = 0 // 重置计数器
                                }
                            }
                        } else {
                            // 如果不是严重卡顿，重置严重卡顿计数器
                            consecutiveSevereJankCount = 0
                            
                            // 如果帧率恢复正常且当前是软件渲染，考虑切回硬件渲染
                            val currentTime = SystemClock.elapsedRealtime()
                            if (!isHardwareRenderingEnabled && 
                                currentTime - lastRenderModeSwitch > MIN_RENDER_MODE_SWITCH_INTERVAL && 
                                frameCount >= HW_RENDER_STABILITY_FRAMES && // 确保稳定运行足够长时间
                                jankCount == 0) { // 并且期间没有发生卡顿
                                
                                window.decorView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                                isHardwareRenderingEnabled = true
                                lastRenderModeSwitch = currentTime
                                Log.i(TAG, "帧率稳定恢复 ($HW_RENDER_STABILITY_FRAMES 帧)，切换回硬件渲染")
                                // 重置帧计数器以重新开始稳定期计算
                                frameCount = 0
                                jankCount = 0
                                totalFrameTime = 0
                            }
                        }
                        
                        // 每300帧计算一次性能指标
                        if (isHardwareRenderingEnabled && frameCount > 0 && frameCount % 300 == 0) {
                            val jankRate = (jankCount * 100.0) / frameCount
                            val avgFrameTime = totalFrameTime / frameCount.toDouble()
                            
                            if (jankRate > 15.0) {
                                Log.w(TAG, "性能警告: 丢帧率 ${String.format("%.1f", jankRate)}% ($jankCount/$frameCount), 平均帧耗时 ${String.format("%.1f", avgFrameTime)}ms")
                            }
                        }
                    }
                    
                    // 更新上一帧时间
                    lastFrameTimeNanos = frameTimeNanos
                    
                    // 继续监控下一帧
                    Choreographer.getInstance().postFrameCallback(this)
                } catch (e: Exception) {
                    Log.e(TAG, "帧回调处理失败: ${e.message}")
                    // 出错时仍然继续监控
                    Choreographer.getInstance().postFrameCallback(this)
                }
            }
        }
        
        // 开始监控
        Choreographer.getInstance().postFrameCallback(frameCallback)
        frameCallbackPosted = true
        
        // 添加内存检查定时器
        addMemoryCheck(window.context)
    }
    
    /**
     * 添加低内存检查，在内存压力大时主动释放资源
     */
    private fun addMemoryCheck(context: Context) {
        val handler = Handler(Looper.getMainLooper())
        
        val memoryCheckRunnable = object : Runnable {
            override fun run() {
                try {
                    val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                    val memoryInfo = ActivityManager.MemoryInfo()
                    activityManager.getMemoryInfo(memoryInfo)
                    
                    val availableMem = memoryInfo.availMem
                    val totalMem = memoryInfo.totalMem
                    val percentUsed = 1 - (availableMem.toDouble() / totalMem.toDouble())
                    
                    // 当内存使用率超过阈值时，主动触发垃圾回收
                    if (percentUsed > MEMORY_WARNING_THRESHOLD) {
                        Log.w(TAG, "内存使用率高: ${(percentUsed * 100).roundToInt()}%, 主动释放资源")
                        
                        // 清理图片缓存
                        System.gc()
                        
                        // 通知应用需要释放内存
                        RenderOptimizer.onLowMemoryWarning()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "内存检查失败: ${e.message}")
                }
                
                // 继续定期检查
                handler.postDelayed(this, MEMORY_CHECK_INTERVAL_MS)
            }
        }
        
        // 开始定期内存检查
        handler.postDelayed(memoryCheckRunnable, MEMORY_CHECK_INTERVAL_MS)
    }
    
    /**
     * 低内存警告处理
     * 应用可以监听此方法释放非必要资源
     */
    fun onLowMemoryWarning() {
        // 暂停性能监控
        isMonitoring = false
        
        // 释放一些内部缓存
        hwcErrorCount = 0
        
        // 在这里可以添加释放其他内部缓存的代码
    }
    
    /**
     * 检查RippleDrawable动画 - 不再使用反射，简化处理
     */
    private fun checkRippleAnimations(view: View) {
        try {
            // 简化为只为RippleDrawable启用硬件加速
            if (view.background is RippleDrawable) {
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
            
            // 递归检查子视图，使用更少的递归
            if (view is ViewGroup && view.childCount < 50) { // 限制只处理合理数量的子视图
                for (i in 0 until view.childCount) {
                    val child = view.getChildAt(i)
                    // 只处理可见的直接子视图，避免过深递归
                    if (child.visibility == View.VISIBLE && child.background is RippleDrawable) {
                        child.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    }
                }
            }
        } catch (e: Exception) {
            // 只记录错误，不影响主线程
            Log.e(TAG, "检查RippleDrawable时出错: ${e.message}")
        }
    }
    
    /**
     * 启动性能监控
     */
    private fun startPerformanceMonitoring(activity: Activity) {
        if (isMonitoring) return
        isMonitoring = true
        
        monitoringThread = Thread {
            while (isMonitoring) {
                try {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastMemoryCheck >= MEMORY_CHECK_INTERVAL_MS) {
                        val runtime = Runtime.getRuntime()
                        val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
                        val maxMemory = runtime.maxMemory() / 1024 / 1024
                        val memoryUsage = usedMemory.toFloat() / maxMemory
                        
                        if (memoryUsage > MEMORY_WARNING_THRESHOLD) {
                            // 执行GC
                            // System.gc() // 注释掉GC调用
                            // 如果内存使用仍然过高，切换到软件渲染
                            if (memoryUsage > 0.9) {
                                activity.window.decorView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                            }
                        }
                        
                        lastMemoryCheck = currentTime
                    }
                    
                    Thread.sleep(1000)
                } catch (e: Exception) {
                    Log.e(TAG, "性能监控失败: ${e.message}")
                }
            }
        }.apply {
            name = "performance-monitor"
            priority = Thread.MIN_PRIORITY
            start()
        }
    }
    
    /**
     * 重置优化器状态
     */
    fun resetOptimizerState() {
        // 移除现有的帧回调
        if (frameCallbackPosted && frameCallback != null) {
            try {
                Choreographer.getInstance().removeFrameCallback(frameCallback)
            } catch (e: Exception) {
                Log.e(TAG, "移除帧回调失败: ${e.message}")
            }
            frameCallbackPosted = false
            frameCallback = null
        }
        
        isMonitoring = false
        monitoringThread?.interrupt() // 使用interrupt而不是join，避免阻塞
        monitoringThread = null
        
        frameCount = 0
        jankCount = 0
        lastFrameTime = 0L
        lastMemoryCheck = 0L
        totalFrameTime = 0L
        lastFrameTimeNanos = 0L
        hwcErrorCount = 0
        isHwcFallbackActive = false
        isHardwareRenderingEnabled = true
    }
    
    /**
     * 检查渲染状态
     */
    fun isRenderingHealthy(): Boolean {
        return hwcErrorCount < FORCE_SOFTWARE_RENDER_THRESHOLD
    }
    
    /**
     * 应用全局渲染优化
     */
    fun applyGlobalRenderingOptimization(window: Window) {
        try {
            // 1. 设置优化的窗口格式
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 2. 检查内存状态
            val activityManager = window.context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager?.getMemoryInfo(memoryInfo)
            
            val isLowMemory = memoryInfo.lowMemory || memoryInfo.availMem < 100 * 1024 * 1024
            
            // 3. 根据内存状态选择渲染模式
            window.decorView.setLayerType(
                if (isLowMemory) View.LAYER_TYPE_SOFTWARE else View.LAYER_TYPE_HARDWARE,
                null
            )
            
            // 4. 优化堆内存使用
            try {
                val vmRuntimeClass = Class.forName("dalvik.system.VMRuntime")
                val getRuntime = vmRuntimeClass.getDeclaredMethod("getRuntime")
                val runtime = getRuntime.invoke(null)
                
                val setTargetHeapUtilization = vmRuntimeClass.getDeclaredMethod(
                    "setTargetHeapUtilization", Float::class.java)
                setTargetHeapUtilization.invoke(runtime, 0.75f)
            } catch (e: Exception) {
                // 忽略反射错误
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "应用全局渲染优化失败: ${e.message}")
        }
    }

    /**
     * 应用性能优化的修饰符，减少绘制复杂度
     */
    fun Modifier.performanceOptimized(): Modifier {
        return this
            // 使用硬件加速层，但减少不必要的参数
            .graphicsLayer(
                renderEffect = null,
                clip = false,
                shadowElevation = 0f
            )
    }
    
    /**
     * 可组合元素的惰性加载包装器
     */
    @Composable
    fun <T> lazyInitialize(key: T, initialize: @Composable () -> Unit) {
        val remembered = remember(key) { true }
        if (remembered) {
            initialize()
        }
    }
    
    /**
     * 对过渡动画进行性能优化的包装器
     */
    @Composable
    fun OptimizedAnimatedContainer(
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit
    ) {
        Box(
            modifier = modifier
                .graphicsLayer(
                    renderEffect = null,
                    clip = false,
                    shadowElevation = 0f,
                    cameraDistance = with(LocalDensity.current) { 16.dp.toPx() }
                )
        ) {
            content()
        }
    }
    
    /**
     * 四舍五入DP值到整数像素，避免子像素渲染
     */
    @Composable
    fun alignToPixel(dp: Float): Float {
        val density = LocalDensity.current.density
        return (dp * density).roundToInt() / density
    }

    /**
     * 修复timerslack相关错误
     * 用于解决"set_timerslack_ns write failed: Operation not permitted"错误
     */
    fun fixTimerSlackIssues() {
        try {
            Log.d(TAG, "正在应用timerslack错误修复")
            
            // 1. 尝试使用反射修改线程优先级设置方式
            try {
                val threadClass = Thread::class.java
                val currentThreadMethod = threadClass.getDeclaredMethod("currentThread")
                val currentThread = currentThreadMethod.invoke(null)
                
                // 找到setPriority方法
                val setPriorityMethod = threadClass.getDeclaredMethod("setPriority", Int::class.java)
                
                // 设置UI线程为较高优先级，但不使用MAX_PRIORITY以避免权限问题
                setPriorityMethod.invoke(currentThread, Thread.NORM_PRIORITY + 2)
                
                Log.d(TAG, "成功设置线程优先级")
            } catch (e: Exception) {
                Log.w(TAG, "通过反射设置线程优先级失败: ${e.message}")
            }
            
            // 2. 使用Process API的替代方案
            try {
                // 使用早期API设置线程组优先级，避免使用timerslack_ns
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_DISPLAY)
                Log.d(TAG, "成功设置线程优先级为THREAD_PRIORITY_DISPLAY")
            } catch (e: Exception) {
                Log.w(TAG, "设置线程优先级失败: ${e.message}")
            }
            
            // 3. 配置线程调度策略
            try {
                // 为了避免使用可能需要特殊权限的timerslack_ns，可以尝试使用其他调度政策
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    val threadId = android.os.Process.myTid()
                    
                    // 使用反射获取ProcessDelegate类
                    val processDelegateClass = Class.forName("android.os.ProcessDelegate")
                    val getProcessDelegateMethod = processDelegateClass.getDeclaredMethod("getDelegate")
                    getProcessDelegateMethod.isAccessible = true
                    val delegate = getProcessDelegateMethod.invoke(null)
                    
                    // 调用setSchedulerPolicy方法
                    val setSchedulerPolicyMethod = processDelegateClass.getDeclaredMethod(
                        "setSchedulerPolicy", Int::class.java, Int::class.java)
                    setSchedulerPolicyMethod.isAccessible = true
                    
                    // 使用SCHED_OTHER策略(值为0)，这通常不需要特殊权限
                    setSchedulerPolicyMethod.invoke(delegate, threadId, 0)
                    
                    Log.d(TAG, "成功设置线程调度策略")
                }
            } catch (e: Exception) {
                Log.w(TAG, "设置线程调度策略失败: ${e.message}")
                // 这是一个预期中可能的失败，因为并非所有设备都支持自定义调度策略
            }
            
            // 4. 降低线程优先级策略，可能会降低一些UI响应能力，但能避免权限问题
            try {
                // 在主线程上执行
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    try {
                        // 使用低于默认值的优先级
                        android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_DEFAULT)
                        Log.d(TAG, "已将线程优先级设置为默认级别")
                    } catch (e: Exception) {
                        Log.w(TAG, "设置默认线程优先级失败: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "在主线程上设置优先级失败: ${e.message}")
            }
            
            Log.d(TAG, "已完成timerslack错误修复尝试")
        } catch (e: Exception) {
            Log.e(TAG, "修复timerslack错误时出现严重错误: ${e.message}")
        }
    }
} 