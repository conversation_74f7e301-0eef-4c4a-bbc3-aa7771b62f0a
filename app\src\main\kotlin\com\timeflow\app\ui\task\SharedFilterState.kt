package com.timeflow.app.ui.task

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 管理任务筛选状态的共享类
 */
@Singleton
class SharedFilterState @Inject constructor() {
    private val _currentFilter = MutableStateFlow("全部")
    val currentFilter: StateFlow<String> = _currentFilter.asStateFlow()

    /**
     * 更新当前筛选条件
     */
    fun updateFilter(filter: String) {
        _currentFilter.value = filter
    }
} 