package com.timeflow.app.widget

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 小组件更新管理器
 * 统一管理所有小组件的更新逻辑
 */
class WidgetUpdateManager(private val context: Context) {
    
    /**
     * 更新所有小组件
     */
    fun updateAllWidgets() {
        CoroutineScope(Dispatchers.IO).launch {
            updateTimeInsightWidgets()
            updateTodayTasksWidgets()
            updateWeeklyStatsWidgets()
            updateGoalProgressWidgets()
        }
    }
    
    /**
     * 更新时间洞察小组件
     */
    fun updateTimeInsightWidgets() {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        val componentName = ComponentName(context, TimeInsightWidget::class.java)
        val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
        
        if (widgetIds.isNotEmpty()) {
            val intent = Intent(context, TimeInsightWidget::class.java).apply {
                action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
            }
            context.sendBroadcast(intent)
        }
    }
    
    /**
     * 更新今日待办小组件
     */
    fun updateTodayTasksWidgets() {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        val componentName = ComponentName(context, TodayTasksWidget::class.java)
        val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
        
        if (widgetIds.isNotEmpty()) {
            val intent = Intent(context, TodayTasksWidget::class.java).apply {
                action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
            }
            context.sendBroadcast(intent)
        }
    }
    
    /**
     * 更新周统计小组件
     */
    fun updateWeeklyStatsWidgets() {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        val componentName = ComponentName(context, WeeklyStatsWidget::class.java)
        val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
        
        if (widgetIds.isNotEmpty()) {
            val intent = Intent(context, WeeklyStatsWidget::class.java).apply {
                action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
            }
            context.sendBroadcast(intent)
        }
    }
    
    /**
     * 更新目标进度小组件
     */
    fun updateGoalProgressWidgets() {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        val componentName = ComponentName(context, GoalProgressWidget::class.java)
        val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
        
        if (widgetIds.isNotEmpty()) {
            val intent = Intent(context, GoalProgressWidget::class.java).apply {
                action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
            }
            context.sendBroadcast(intent)
        }
    }
    
    /**
     * 当任务状态改变时更新相关小组件
     */
    fun onTaskStatusChanged() {
        updateTodayTasksWidgets()
        updateTimeInsightWidgets()
    }
    
    /**
     * 当专注会话结束时更新相关小组件
     */
    fun onFocusSessionCompleted() {
        updateTimeInsightWidgets()
        updateWeeklyStatsWidgets()
    }
    
    /**
     * 当习惯状态改变时更新相关小组件
     */
    fun onHabitStatusChanged() {
        updateGoalProgressWidgets()
    }
    
    /**
     * 定时更新所有小组件（每小时）
     */
    fun schedulePeriodicUpdates() {
        // 这里可以使用WorkManager来定期更新小组件
        // 或者在应用启动时设置定时器
    }
    
    /**
     * 检查是否有小组件被添加到桌面
     */
    fun hasWidgetsOnHomeScreen(): Boolean {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        
        val timeInsightIds = appWidgetManager.getAppWidgetIds(
            ComponentName(context, TimeInsightWidget::class.java)
        )
        val todayTasksIds = appWidgetManager.getAppWidgetIds(
            ComponentName(context, TodayTasksWidget::class.java)
        )
        val quickTimerIds = appWidgetManager.getAppWidgetIds(
            ComponentName(context, QuickTimerWidget::class.java)
        )
        val weeklyStatsIds = appWidgetManager.getAppWidgetIds(
            ComponentName(context, WeeklyStatsWidget::class.java)
        )
        val goalProgressIds = appWidgetManager.getAppWidgetIds(
            ComponentName(context, GoalProgressWidget::class.java)
        )
        
        return timeInsightIds.isNotEmpty() || 
               todayTasksIds.isNotEmpty() || 
               quickTimerIds.isNotEmpty() ||
               weeklyStatsIds.isNotEmpty() ||
               goalProgressIds.isNotEmpty()
    }
    
    /**
     * 获取桌面上的小组件数量
     */
    fun getWidgetCount(): Int {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        
        val timeInsightCount = appWidgetManager.getAppWidgetIds(
            ComponentName(context, TimeInsightWidget::class.java)
        ).size
        val todayTasksCount = appWidgetManager.getAppWidgetIds(
            ComponentName(context, TodayTasksWidget::class.java)
        ).size
        val quickTimerCount = appWidgetManager.getAppWidgetIds(
            ComponentName(context, QuickTimerWidget::class.java)
        ).size
        val weeklyStatsCount = appWidgetManager.getAppWidgetIds(
            ComponentName(context, WeeklyStatsWidget::class.java)
        ).size
        val goalProgressCount = appWidgetManager.getAppWidgetIds(
            ComponentName(context, GoalProgressWidget::class.java)
        ).size
        
        return timeInsightCount + todayTasksCount + quickTimerCount + weeklyStatsCount + goalProgressCount
    }
}
