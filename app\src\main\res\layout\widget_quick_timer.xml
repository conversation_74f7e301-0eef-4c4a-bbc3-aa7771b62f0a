<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background_blue"
    android:padding="20dp">

    <!-- 顶部标题区域 -->
    <LinearLayout
        android:id="@+id/widget_main_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_alignParentTop="true">

        <TextView
            android:id="@+id/widget_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="专注计时"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_light"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⏱️"
            android:textSize="24sp" />

    </LinearLayout>

    <!-- 计时器按钮网格 -->
    <LinearLayout
        android:id="@+id/widget_timer_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/widget_header"
        android:layout_marginTop="24dp">

        <!-- 第一行：25分钟和45分钟 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <!-- 25分钟专注 -->
            <LinearLayout
                android:id="@+id/widget_timer_25"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/widget_card_background"
                android:layout_marginEnd="8dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="25"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="分钟专注"
                    android:textSize="12sp"
                    android:textColor="@color/widget_text_secondary"
                    android:layout_marginTop="4dp"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- 45分钟专注 -->
            <LinearLayout
                android:id="@+id/widget_timer_45"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/widget_card_background"
                android:layout_marginStart="8dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="45"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="深度工作"
                    android:textSize="12sp"
                    android:textColor="@color/widget_text_secondary"
                    android:layout_marginTop="4dp"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </LinearLayout>

        <!-- 第二行：60分钟和自定义 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 60分钟专注 -->
            <LinearLayout
                android:id="@+id/widget_timer_60"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/widget_card_background"
                android:layout_marginEnd="8dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="60"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    android:textColor="@color/widget_text_primary"
                    android:fontFamily="sans-serif-light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="心流状态"
                    android:textSize="12sp"
                    android:textColor="@color/widget_text_secondary"
                    android:layout_marginTop="4dp"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- 自定义时间 -->
            <LinearLayout
                android:id="@+id/widget_timer_custom"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/widget_card_background"
                android:layout_marginStart="8dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⚙️"
                    android:textSize="24sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="自定义"
                    android:textSize="12sp"
                    android:textColor="@color/widget_text_secondary"
                    android:layout_marginTop="4dp"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 底部提示 -->
    <TextView
        android:id="@+id/widget_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="轻触开始专注时光 ✨"
        android:textSize="14sp"
        android:textColor="@color/widget_text_light"
        android:gravity="center"
        android:layout_below="@id/widget_timer_grid"
        android:layout_marginTop="24dp"
        android:fontFamily="sans-serif-medium" />

</RelativeLayout>
