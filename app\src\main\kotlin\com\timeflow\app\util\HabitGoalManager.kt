package com.timeflow.app.util

import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.HabitModel
import com.timeflow.app.data.repository.GoalRepository
import com.timeflow.app.data.repository.HabitRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 习惯和目标关联关系管理器
 * 负责管理习惯与目标之间的逻辑关联
 */
@Singleton
class HabitGoalManager @Inject constructor(
    private val habitRepository: HabitRepository,
    private val goalRepository: GoalRepository
) {
    
    /**
     * 获取指定目标关联的所有习惯
     */
    suspend fun getHabitsForGoal(goalId: String): List<HabitModel> {
        return habitRepository.getHabitsByGoalId(goalId)
    }
    
    /**
     * 获取指定习惯关联的目标信息
     */
    suspend fun getGoalForHabit(habitId: String): Goal? {
        val habit = habitRepository.getHabitById(habitId) ?: return null
        return habit.relatedGoalId?.let { goalId ->
            goalRepository.getGoalById(goalId)
        }
    }
    
    /**
     * 关联习惯到目标
     */
    suspend fun associateHabitWithGoal(habitId: String, goalId: String) {
        val habit = habitRepository.getHabitById(habitId) ?: return
        val goal = goalRepository.getGoalById(goalId) ?: return
        
        val updatedHabit = habit.copy(
            relatedGoalId = goalId,
            relatedGoalTitle = goal.title
        )
        
        habitRepository.updateHabit(updatedHabit)
    }
    
    /**
     * 取消习惯与目标的关联
     */
    suspend fun dissociateHabitFromGoal(habitId: String) {
        val habit = habitRepository.getHabitById(habitId) ?: return
        
        val updatedHabit = habit.copy(
            relatedGoalId = null,
            relatedGoalTitle = null
        )
        
        habitRepository.updateHabit(updatedHabit)
    }
    
    /**
     * 获取目标完成进度（基于关联习惯的完成情况）
     */
    suspend fun calculateGoalProgressFromHabits(goalId: String): Float {
        val habits = getHabitsForGoal(goalId)
        if (habits.isEmpty()) return 0f
        
        // 计算所有关联习惯的平均完成率
        val totalCompletionRate = habits.sumOf { habit ->
            habit.getCompletionRate(30).toDouble() // 最近30天的完成率
        }
        
        return (totalCompletionRate / habits.size).toFloat()
    }
    
    /**
     * 获取目标与习惯的关联统计信息
     */
    suspend fun getAssociationStats(): HabitGoalAssociationStats {
        val allHabits = habitRepository.getAllHabits()
        val allGoals = goalRepository.getAllGoalsList()
        
        val totalHabits = allHabits.size
        val associatedHabits = allHabits.count { it.relatedGoalId != null }
        val totalGoals = allGoals.size
        val goalsWithHabits = allGoals.count { goal ->
            getHabitsForGoal(goal.id).isNotEmpty()
        }
        
        return HabitGoalAssociationStats(
            totalHabits = totalHabits,
            associatedHabits = associatedHabits,
            totalGoals = totalGoals,
            goalsWithHabits = goalsWithHabits,
            associationRate = if (totalHabits > 0) associatedHabits.toFloat() / totalHabits else 0f
        )
    }
    
    /**
     * 获取习惯-目标关联的数据流
     */
    fun getHabitGoalAssociationsFlow(): Flow<List<HabitGoalAssociation>> {
        return combine(
            habitRepository.getAllHabitsFlow(),
            goalRepository.getAllGoals()
        ) { habits, goals ->
            habits.mapNotNull { habit ->
                habit.relatedGoalId?.let { goalId ->
                    val goal = goals.find { it.id == goalId }
                    goal?.let {
                        HabitGoalAssociation(
                            habitId = habit.id,
                            habitName = habit.name,
                            goalId = goal.id,
                            goalTitle = goal.title,
                            habitCompletionRate = habit.getCompletionRate(30),
                            habitStreak = habit.currentStreak
                        )
                    }
                }
            }
        }
    }
}

/**
 * 习惯-目标关联统计信息
 */
data class HabitGoalAssociationStats(
    val totalHabits: Int,
    val associatedHabits: Int,
    val totalGoals: Int,
    val goalsWithHabits: Int,
    val associationRate: Float // 关联率 (0-1)
)

/**
 * 习惯-目标关联关系数据
 */
data class HabitGoalAssociation(
    val habitId: String,
    val habitName: String,
    val goalId: String,
    val goalTitle: String,
    val habitCompletionRate: Float,
    val habitStreak: Int
) 