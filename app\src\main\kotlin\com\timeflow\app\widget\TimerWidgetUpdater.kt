package com.timeflow.app.widget

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * 专注计时器小组件更新工具
 * 负责统一更新所有计时器相关的小组件
 */
object TimerWidgetUpdater {
    
    private const val TAG = "TimerWidgetUpdater"
    
    /**
     * 更新所有专注计时器小组件
     * @param context 上下文
     */
    fun updateAllTimerWidgets(context: Context) {
        try {
            Log.d(TAG, "🔄 开始更新所有专注计时器小组件")
            
            // 更新QuickTimerWidget
            updateQuickTimerWidgets(context)
            
            // 更新FocusTimerWidget
            updateFocusTimerWidgets(context)
            
            Log.d(TAG, "✅ 所有专注计时器小组件更新完成")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 更新专注计时器小组件失败", e)
        }
    }
    
    /**
     * 更新快速计时器小组件
     */
    private fun updateQuickTimerWidgets(context: Context) {
        try {
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val componentName = ComponentName(context, QuickTimerWidget::class.java)
            val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
            
            if (widgetIds.isNotEmpty()) {
                val intent = Intent(context, QuickTimerWidget::class.java).apply {
                    action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
                }
                context.sendBroadcast(intent)
                Log.d(TAG, "✓ 已更新 ${widgetIds.size} 个快速计时器小组件")
            } else {
                Log.d(TAG, "ℹ️ 没有找到快速计时器小组件")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 更新快速计时器小组件失败", e)
        }
    }
    
    /**
     * 更新专注计时器小组件
     */
    private fun updateFocusTimerWidgets(context: Context) {
        try {
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val componentName = ComponentName(context, FocusTimerWidget::class.java)
            val widgetIds = appWidgetManager.getAppWidgetIds(componentName)
            
            if (widgetIds.isNotEmpty()) {
                val intent = Intent(context, FocusTimerWidget::class.java).apply {
                    action = AppWidgetManager.ACTION_APPWIDGET_UPDATE
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_IDS, widgetIds)
                }
                context.sendBroadcast(intent)
                Log.d(TAG, "✓ 已更新 ${widgetIds.size} 个专注计时器小组件")
            } else {
                Log.d(TAG, "ℹ️ 没有找到专注计时器小组件")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 更新专注计时器小组件失败", e)
        }
    }
    
    /**
     * 强制刷新所有小组件（用于调试）
     */
    fun forceRefreshAllWidgets(context: Context) {
        Log.d(TAG, "🔄 强制刷新所有小组件")
        updateAllTimerWidgets(context)
    }
}
