package com.timeflow.app.service

import android.content.Context
import android.util.Log
import androidx.work.*
import com.timeflow.app.data.model.RecurrenceSettings
import com.timeflow.app.data.model.RecurrenceType
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.worker.RecurringTaskWorker
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 循环任务管理服务
 * 负责循环任务的生成、调度和管理
 */
@Singleton
class RecurringTaskManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val taskRepository: TaskRepository,
    private val recurrenceCalculator: RecurrenceCalculator
) {
    companion object {
        private const val TAG = "RecurringTaskManager"
        private const val RECURRING_TASK_WORK_NAME = "recurring_task_check"
        private const val CHECK_INTERVAL_HOURS = 6L // 每6小时检查一次
    }

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 初始化循环任务管理器
     */
    fun initialize() {
        Log.d(TAG, "初始化循环任务管理器")
        scheduleRecurringTaskCheck()
    }

    /**
     * 调度循环任务检查
     */
    private fun scheduleRecurringTaskCheck() {
        try {
            // 创建周期性工作请求
            val workRequest = PeriodicWorkRequestBuilder<RecurringTaskWorker>(
                CHECK_INTERVAL_HOURS, TimeUnit.HOURS
            )
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                        .setRequiresBatteryNotLow(false)
                        .build()
                )
                .build()

            // 调度工作
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                RECURRING_TASK_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )

            Log.d(TAG, "✅ 循环任务检查已调度，每${CHECK_INTERVAL_HOURS}小时执行一次")
        } catch (e: Exception) {
            Log.e(TAG, "调度循环任务检查失败", e)
        }
    }

    /**
     * 检查并生成到期的循环任务
     */
    suspend fun checkAndGenerateRecurringTasks() {
        try {
            Log.d(TAG, "开始检查循环任务...")

            // 获取所有循环任务
            val recurringTasks = taskRepository.getAllTasks().filter { it.isRecurring }
            Log.d(TAG, "找到 ${recurringTasks.size} 个循环任务")

            val now = LocalDateTime.now()
            var generatedCount = 0

            for (task in recurringTasks) {
                try {
                    val generated = generateNextInstanceIfNeeded(task, now)
                    if (generated) generatedCount++
                } catch (e: Exception) {
                    Log.e(TAG, "处理循环任务失败: ${task.title}", e)
                }
            }

            Log.d(TAG, "✅ 循环任务检查完成，生成了 $generatedCount 个新任务实例")
        } catch (e: Exception) {
            Log.e(TAG, "检查循环任务失败", e)
        }
    }

    /**
     * 如果需要，生成下一个任务实例
     */
    private suspend fun generateNextInstanceIfNeeded(task: Task, currentTime: LocalDateTime): Boolean {
        try {
            // 解析循环设置
            val recurrenceSettings = parseRecurrenceSettings(task.recurringPattern)
                ?: return false

            // 检查是否需要生成下一个实例
            if (!shouldGenerateNextInstance(task, recurrenceSettings, currentTime)) {
                return false
            }

            // 计算下一个实例的时间
            val nextDateTime = recurrenceCalculator.calculateNextOccurrence(
                task.dueDate ?: task.createdAt,
                recurrenceSettings
            ) ?: return false

            // 检查是否已经存在该时间的实例
            if (hasInstanceForDateTime(task, nextDateTime)) {
                return false
            }

            // 生成新的任务实例
            val nextInstance = createNextTaskInstance(task, nextDateTime, recurrenceSettings)
            taskRepository.insertTask(nextInstance)

            Log.d(TAG, "✅ 生成循环任务实例: ${task.title} -> ${nextDateTime}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "生成循环任务实例失败: ${task.title}", e)
            return false
        }
    }

    /**
     * 解析循环设置
     */
    private fun parseRecurrenceSettings(recurringPattern: String?): RecurrenceSettings? {
        return try {
            if (recurringPattern.isNullOrBlank()) return null
            Json.decodeFromString<RecurrenceSettings>(recurringPattern)
        } catch (e: Exception) {
            Log.e(TAG, "解析循环设置失败: $recurringPattern", e)
            null
        }
    }

    /**
     * 检查是否应该生成下一个实例
     */
    private suspend fun shouldGenerateNextInstance(
        task: Task,
        settings: RecurrenceSettings,
        currentTime: LocalDateTime
    ): Boolean {
        // 检查循环是否已结束
        if (isRecurrenceEnded(settings, currentTime)) {
            return false
        }

        // 检查任务是否已完成（对于某些循环类型）
        if (settings.type == "DAILY" || settings.type == "WEEKLY") {
            // 每日/每周任务需要等待当前实例完成
            return task.isCompleted
        }

        return true
    }

    /**
     * 检查循环是否已结束
     */
    private fun isRecurrenceEnded(settings: RecurrenceSettings, currentTime: LocalDateTime): Boolean {
        return when (settings.endType) {
            "DATE" -> {
                val endDate = settings.endDate?.let { LocalDateTime.parse(it) }
                endDate != null && currentTime.isAfter(endDate)
            }
            "COUNT" -> {
                // TODO: 实现基于次数的结束检查
                false
            }
            else -> false // NEVER
        }
    }

    /**
     * 检查是否已存在指定时间的任务实例
     */
    private suspend fun hasInstanceForDateTime(task: Task, dateTime: LocalDateTime): Boolean {
        // 查找同一天的相同循环任务
        val existingTasks = taskRepository.getTasksByDateRange(
            dateTime.toLocalDate().atStartOfDay(),
            dateTime.toLocalDate().atTime(23, 59, 59)
        )

        return existingTasks.any { existingTask ->
            existingTask.title == task.title &&
            existingTask.isRecurring &&
            existingTask.id != task.id
        }
    }

    /**
     * 创建下一个任务实例
     */
    private fun createNextTaskInstance(
        originalTask: Task,
        nextDateTime: LocalDateTime,
        settings: RecurrenceSettings
    ): Task {
        return originalTask.copy(
            id = generateTaskId(),
            dueDate = nextDateTime,
            startDate = nextDateTime.minusHours(1), // 默认提前1小时开始
            isCompleted = false,
            progress = 0f,
            completedAt = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            // 保持循环设置
            isRecurring = true,
            recurringPattern = originalTask.recurringPattern
        )
    }

    /**
     * 生成任务ID
     */
    private fun generateTaskId(): String {
        return "task_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    /**
     * 当任务完成时处理循环逻辑
     */
    suspend fun onTaskCompleted(task: Task) {
        if (!task.isRecurring) return

        try {
            Log.d(TAG, "处理循环任务完成: ${task.title}")

            val recurrenceSettings = parseRecurrenceSettings(task.recurringPattern)
                ?: return

            // 对于某些循环类型，立即生成下一个实例
            if (shouldGenerateImmediatelyOnCompletion(recurrenceSettings)) {
                generateNextInstanceIfNeeded(task, LocalDateTime.now())
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理循环任务完成失败: ${task.title}", e)
        }
    }

    /**
     * 检查是否应该在完成时立即生成下一个实例
     */
    private fun shouldGenerateImmediatelyOnCompletion(settings: RecurrenceSettings): Boolean {
        return when (settings.type) {
            "DAILY", "WEEKLY" -> true
            else -> false
        }
    }

    /**
     * 取消循环任务检查
     */
    fun cancelRecurringTaskCheck() {
        WorkManager.getInstance(context).cancelUniqueWork(RECURRING_TASK_WORK_NAME)
        Log.d(TAG, "已取消循环任务检查")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        cancelRecurringTaskCheck()
        Log.d(TAG, "循环任务管理器资源已清理")
    }
}
