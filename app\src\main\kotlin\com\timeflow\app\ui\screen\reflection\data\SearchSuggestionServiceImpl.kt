package com.timeflow.app.ui.screen.reflection.data

import com.timeflow.app.ui.screen.reflection.SearchSuggestionService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索建议服务实现类
 */
@Singleton
class SearchSuggestionServiceImpl @Inject constructor() : SearchSuggestionService {

    // 模拟的搜索建议数据
    private val commonSuggestions = mapOf(
        "运" to listOf("运动", "运动效果", "运动反思", "运动计划"),
        "工" to listOf("工作", "工作压力", "工作进展", "工作反思", "工作成果"),
        "学" to listOf("学习", "学习方法", "学习笔记", "学习进度", "学习计划"),
        "读" to listOf("读书", "读书笔记", "读书感悟", "读书计划"),
        "冥" to listOf("冥想", "冥想体验", "冥想效果", "冥想方法")
    )

    private val extendedSuggestions = mapOf(
        "运动" to listOf("晨跑", "健身", "游泳", "篮球", "跑步", "瑜伽"),
        "工作" to listOf("会议", "项目", "任务", "团队", "效率", "压力"),
        "学习" to listOf("课程", "笔记", "考试", "复习", "理解", "掌握"),
        "读书" to listOf("小说", "自助", "历史", "科学", "心理", "哲学"),
        "冥想" to listOf("呼吸", "专注", "放松", "内观", "清晰")
    )

    override suspend fun getContextualSuggestions(query: String): List<String> {
        if (query.isEmpty()) return emptyList()
        
        // 查找匹配的前缀建议
        val suggestions = commonSuggestions.entries
            .filter { it.key.startsWith(query) || query.startsWith(it.key) }
            .flatMap { it.value }
            .filter { it.contains(query, ignoreCase = true) }
            .toMutableList()
        
        // 如果找不到前缀匹配，尝试部分匹配
        if (suggestions.isEmpty()) {
            return commonSuggestions.values
                .flatten()
                .filter { it.contains(query, ignoreCase = true) }
                .take(5) // 限制数量
        }
        
        return suggestions.take(5) // 限制数量
    }

    override suspend fun getExtendedSuggestions(query: String): List<String> {
        if (query.isEmpty()) return emptyList()
        
        // 为每个上下文关键词找到扩展建议
        val extendedResults = mutableListOf<String>()
        
        // 找到匹配query的关键词
        commonSuggestions.values
            .flatten()
            .filter { it.contains(query, ignoreCase = true) }
            .forEach { keyword ->
                // 获取该关键词的扩展建议
                val extensions = extendedSuggestions[keyword] ?: emptyList()
                extendedResults.addAll(extensions)
            }
        
        // 如果找不到任何扩展，尝试直接匹配query
        if (extendedResults.isEmpty()) {
            return extendedSuggestions.values
                .flatten()
                .filter { it.contains(query, ignoreCase = true) }
                .take(8) // 限制数量
        }
        
        return extendedResults.distinct().take(8) // 去重并限制数量
    }
} 