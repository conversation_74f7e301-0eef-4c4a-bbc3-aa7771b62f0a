package com.timeflow.app.data.repository

import com.timeflow.app.data.model.WishModel
import kotlinx.coroutines.flow.Flow

interface WishRepository {

    fun getAllActiveWishes(): Flow<List<WishModel>>

    fun getAllArchivedWishes(): Flow<List<WishModel>>

    fun getAllWishes(): Flow<List<WishModel>>
    
    suspend fun getWishById(wishId: String): WishModel?
    
    fun getWishesByCategory(category: String): Flow<List<WishModel>>
    
    fun getWishesByPriority(minPriority: Int): Flow<List<WishModel>>
    
    fun getWishesByStatus(status: String): Flow<List<WishModel>>
    
    suspend fun getAllCategories(): List<String>
    
    suspend fun getActiveWishCount(): Int
    
    suspend fun getConvertedWishCount(): Int
    
    suspend fun insertWish(wish: WishModel)
    
    suspend fun insertWishes(wishes: List<WishModel>)
    
    suspend fun updateWish(wish: WishModel)
    
    suspend fun deleteWish(wish: WishModel)
    
    suspend fun deleteWishById(wishId: String)
    
    suspend fun archiveWish(wishId: String)
    
    suspend fun unarchiveWish(wishId: String)
    
    suspend fun convertWishToGoal(wishId: String, goalId: String)
    
    suspend fun markWishAsAchieved(wishId: String)
    
    fun searchWishes(searchQuery: String): Flow<List<WishModel>>
} 