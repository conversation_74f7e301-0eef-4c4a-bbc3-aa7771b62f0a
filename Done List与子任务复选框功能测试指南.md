# Done List与子任务复选框功能测试指南

## 🎯 **功能概述**

本指南涵盖两个主要功能的测试：
1. **Done List新布局** - 显示已完成任务及其子任务的卡片式界面
2. **子任务复选框修复** - 修复子任务状态保存和父任务自动完成功能

## 📋 **测试前准备**

### 1. 确保应用数据
- 创建一些包含子任务的任务
- 确保有已完成的任务用于Done List显示
- 建议创建包含"医院"或"合作"关键词的任务（会显示模拟子任务）

### 2. 重启应用
- 完全关闭应用
- 重新启动以确保最新代码生效

---

## 🎨 **Done List新布局测试**

### 测试场景1：基本布局验证
1. **进入Done List模式**
   - 在任务列表页面点击顶部的"列表/完成"切换按钮
   - 验证背景渐变为 `#bcb8c2` 到 `#e4e3e6` 的柔和紫灰色

2. **验证卡片新布局**
   ```
   预期布局：
   ┌─────────────────────────────────────┐
   │ 🔵 [真实任务标题]          ▼      │
   │     N个子任务已完成                  │
   │                                     │
   │     ✅ [真实子任务1]               │
   │     ✅ [真实子任务2]               │
   │     ✅ [真实子任务3]               │
   │                                     │
   │     [查看全部 N 个子任务]           │
   └─────────────────────────────────────┘
   ```

3. **验证交互功能**
   - ✅ 左侧有彩色圆点（根据任务优先级）
   - ✅ 任务标题显示正确
   - ✅ 右侧有可点击的展开/收起箭头
   - ✅ 显示"N个子任务已完成"统计信息

### 测试场景2：展开/收起功能
1. **点击下拉箭头**
   - 验证卡片可以展开显示子任务列表
   - 验证箭头图标正确切换（向下→向上）
   
2. **子任务列表显示**
   - ✅ 子任务前有绿色的✅图标
   - ✅ 子任务标题显示正确
   - ✅ 最多显示3个子任务
   - ✅ 如果超过3个，显示"查看全部 N 个子任务"链接

3. **再次点击箭头**
   - 验证卡片可以收起
   - 验证动画平滑

### 测试场景3：特殊任务测试
1. **包含"医院"关键词的任务**
   - 应该显示模拟的3个子任务：
     - "预约挂号"
     - "准备检查资料" 
     - "按时到达医院"

2. **包含"合作"关键词的任务**
   - 应该显示相同的模拟子任务

3. **其他任务**
   - 如果没有子任务，不显示子任务统计
   - 布局仍然正确显示

---

## ✅ **子任务复选框功能测试**

### 测试场景1：子任务状态保存
1. **打开任务详情**
   - 点击任何包含子任务的任务
   - 进入TaskDetailBottomSheet

2. **点击子任务复选框**
   - 点击未完成子任务的复选框
   - **预期结果**：
     - ✅ 复选框立即显示为选中状态
     - ✅ 子任务文字显示删除线
     - ✅ 状态保存到数据库

3. **验证状态持久性**
   - 关闭任务详情
   - 重新打开同一任务
   - **预期结果**：
     - ✅ 子任务状态保持为已完成
     - ✅ 复选框保持选中状态

### 测试场景2：父任务自动完成
1. **准备测试任务**
   - 选择一个有多个子任务的未完成任务
   - 确保只有1-2个子任务未完成

2. **逐个完成子任务**
   - 点击第一个未完成子任务的复选框
   - 点击第二个未完成子任务的复选框
   - 继续直到所有子任务完成

3. **验证父任务自动完成**
   - **预期结果**：
     - ✅ 当最后一个子任务完成时
     - ✅ 父任务自动标记为已完成
     - ✅ 任务从待办列表移动到已完成列表

### 测试场景3：错误处理测试
1. **网络中断测试**（如果适用）
   - 断开网络连接
   - 尝试更改子任务状态
   - 重新连接网络
   - **预期结果**：
     - ✅ 显示错误提示
     - ✅ 状态正确回滚
     - ✅ 重新连接后可以正常操作

2. **快速连续点击测试**
   - 快速多次点击同一个子任务复选框
   - **预期结果**：
     - ✅ 状态切换稳定
     - ✅ 没有重复保存
     - ✅ 最终状态正确

---

## 🔍 **日志监控**

在测试过程中，关注以下关键日志：

### Done List相关日志
```bash
# 过滤Done List相关日志
adb logcat -s DoneList TaskListFullScreen
```

### 子任务更新相关日志
```bash
# 过滤子任务更新日志  
adb logcat -s SubTaskUpdate TaskDetailBottomSheet
```

### 成功的日志模式
```
DoneList: 切换到完成列表模式
SubTaskUpdate: 开始更新子任务状态: [ID], 完成状态: true
SubTaskUpdate: 子任务状态保存成功: [ID]
SubTaskUpdate: 所有子任务已完成，触发父任务完成
```

---

## ✅ **验证标准**

### Done List功能
- [ ] 背景渐变色正确显示
- [ ] 卡片布局符合设计要求
- [ ] 展开/收起交互正常
- [ ] 子任务列表正确显示
- [ ] 彩色圆点和统计信息准确

### 子任务复选框功能
- [ ] 点击复选框状态立即更新
- [ ] 子任务状态正确保存到数据库
- [ ] 状态在重新打开后保持
- [ ] 所有子任务完成后父任务自动完成
- [ ] 错误情况下状态正确回滚

---

## 🐛 **常见问题排查**

### Done List显示问题
1. **背景颜色不对**
   - 检查是否正确切换到Done List模式
   - 验证CSS样式是否应用

2. **子任务不显示**
   - 检查任务是否包含"医院"或"合作"关键词
   - 验证模拟数据逻辑

### 子任务复选框问题
1. **状态不保存**
   - 检查网络连接
   - 查看日志中的错误信息
   - 验证数据库权限

2. **父任务不自动完成**
   - 确保所有子任务都已完成
   - 检查子任务完成状态检查逻辑
   - 查看相关日志

---

## 🎯 **测试完成检查清单**

- [ ] Done List新布局显示正确
- [ ] 展开/收起功能正常
- [ ] 子任务复选框可以正常切换状态
- [ ] 子任务状态保存到数据库
- [ ] 所有子任务完成后父任务自动完成
- [ ] 错误情况处理正确
- [ ] 日志信息清晰无错误

## 🎉 **预期改进效果**

完成所有测试后，应该看到：
- ✅ Done List界面更加美观和信息丰富
- ✅ 子任务复选框100%可靠工作
- ✅ 父子任务状态联动正确
- ✅ 用户体验显著提升

---

**注意**：如果在测试过程中发现任何问题，请记录详细的重现步骤和日志信息，以便进一步调试和修复。 